from langchain.tools import tool

import datetime


@tool()
def get_current_date():
    """获取今天的日期"""
    return datetime.datetime.today().strftime("%Y-%m-%d")
#  这里的工具函数的注解是非常重要的，这个就是告诉大模型工具的能力

from langchain_community.chat_models import ChatTongyi
from langchain_core.messages import HumanMessage
from langchain_text_splitters import Language

from load_key import load_key

llm = ChatTongyi(
    # model="deepseek-v3",
    model="qwen-plus",
    api_key=load_key("DASHSCOPE_API_KEY"),
    temperature=0.5,
)

llm_with_tools = llm.bind_tools([get_current_date])
all_tools = {"get_current_date": get_current_date,"get_current_time":get_current_time}
query = "今天的日期是？"
messages = [query]
# 询问大模型，大模型会判断需要调用的工具,并返回一个工具调用请求
# 返回的content是空的，说明还没有和llm进行交互
ai_message = llm_with_tools.invoke(messages)
print("111",ai_message )
messages.append(ai_message)
print(ai_message.tool_calls)

for tc in ai_message.tool_calls:
    select_tool = all_tools[tc["name"].lower()]
    tool_msg = select_tool.invoke(tc)
    messages.append(tool_msg)
res = llm_with_tools.invoke(messages)
print("222",res)

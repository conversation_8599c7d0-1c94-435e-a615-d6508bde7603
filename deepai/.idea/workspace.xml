<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3186ad2b-7bea-4703-b827-47bbe41f0577" name="Changes" comment="langchain">
      <change afterPath="$PROJECT_DIR$/langchain/lcel/lcel01.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/langchain/Keys.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/langchain/config/Keys.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/langchain/start01.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/langchain/start02.py" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file:///opt/anaconda3/envs/aideep/lib/python3.9/site-packages/torch/nn/modules/module.py" root0="SKIP_INSPECTION" />
    <setting file="file:///opt/anaconda3/envs/aideep/lib/python3.9/site-packages/torch/utils/data/dataloader.py" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2wDpo4C6wrrBRBn0xUlROyiXJlT" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="compactDirectories" value="true" />
    <option name="flattenModules" value="true" />
    <option name="flattenPackages" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.MultiHeadAttention.executor": "Debug",
    "Python.bahdanau.executor": "Debug",
    "Python.bert.executor": "Run",
    "Python.concise.executor": "Debug",
    "Python.dropout1.executor": "Debug",
    "Python.encode-decode.executor": "Debug",
    "Python.language-model.executor": "Debug",
    "Python.lenet.executor": "Run",
    "Python.linear-regresion.executor": "Debug",
    "Python.load_key.executor": "Debug",
    "Python.lstm.executor": "Run",
    "Python.pkg.executor": "Run",
    "Python.regresion2.executor": "Run",
    "Python.rnn-concise.executor": "Run",
    "Python.sequence.executor": "Debug",
    "Python.start01.executor": "Debug",
    "Python.start03.executor": "Run",
    "Python.start04.executor": "Debug",
    "Python.text-sequence.executor": "Run",
    "Python.torch.executor": "Debug",
    "Python.transformer.executor": "Debug",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "/Users/<USER>/work/python/deepai",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$" />
      <recent name="$PROJECT_DIR$/liner" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$" />
    </key>
  </component>
  <component name="RunManager" selected="Python.start04">
    <configuration name="bert" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="deepai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/attention" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/attention/bert.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="load_key" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="deepai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/langchain/config" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/langchain/config/load_key.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="deepai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="start01" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="deepai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/langchain" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/langchain/start01.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="start03" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="deepai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/langchain" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/langchain/start03.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="start04" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="deepai" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/langchain/start" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/langchain/start/start04.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.start04" />
        <item itemvalue="Python.start03" />
        <item itemvalue="Python.start01" />
        <item itemvalue="Python.load_key" />
        <item itemvalue="Python.bert" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-PY-243.26053.29" />
        <option value="bundled-python-sdk-b1dbf8ef85a6-4df51de95216-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.26053.29" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="3186ad2b-7bea-4703-b827-47bbe41f0577" name="Changes" comment="" />
      <created>1745586171414</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745586171414</updated>
      <workItem from="1745586173219" duration="5899000" />
      <workItem from="1745629558473" duration="5104000" />
      <workItem from="1745656884288" duration="6318000" />
      <workItem from="1745844684941" duration="23000" />
      <workItem from="1746516224096" duration="10844000" />
      <workItem from="1746579816839" duration="3773000" />
      <workItem from="1746605028395" duration="4578000" />
      <workItem from="1746622081401" duration="4187000" />
      <workItem from="1746673665121" duration="17471000" />
      <workItem from="1746793222310" duration="5944000" />
      <workItem from="1746832716718" duration="4468000" />
      <workItem from="1746925345710" duration="22148000" />
      <workItem from="1747053833941" duration="5479000" />
      <workItem from="1747141307464" duration="668000" />
      <workItem from="1747141983296" duration="294000" />
      <workItem from="1747142295352" duration="640000" />
      <workItem from="1747143098956" duration="4237000" />
      <workItem from="1747184715357" duration="694000" />
      <workItem from="1747185672863" duration="231000" />
      <workItem from="1747185909513" duration="15972000" />
      <workItem from="1747273587134" duration="12856000" />
      <workItem from="1747308980543" duration="598000" />
      <workItem from="1747314713111" duration="2716000" />
      <workItem from="1747317673432" duration="3467000" />
      <workItem from="1747400604662" duration="4275000" />
    </task>
    <task id="LOCAL-00001" summary="开始动手实现">
      <option name="closed" value="true" />
      <created>1745630665536</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1745630665536</updated>
    </task>
    <task id="LOCAL-00002" summary="encode-decode">
      <option name="closed" value="true" />
      <created>1746949713637</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1746949713637</updated>
    </task>
    <task id="LOCAL-00003" summary="transfomer">
      <option name="closed" value="true" />
      <created>1747060302682</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1747060302682</updated>
    </task>
    <task id="LOCAL-00004" summary="transfomer">
      <option name="closed" value="true" />
      <created>1747210573694</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1747210573694</updated>
    </task>
    <task id="LOCAL-00005" summary="bert">
      <option name="closed" value="true" />
      <created>1747299364129</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1747299364129</updated>
    </task>
    <task id="LOCAL-00006" summary="langchain">
      <option name="closed" value="true" />
      <created>1747319721203</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1747319721203</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="开始动手实现" />
    <MESSAGE value="encode-decode" />
    <MESSAGE value="transfomer" />
    <MESSAGE value="bert" />
    <MESSAGE value="langchain" />
    <option name="LAST_COMMIT_MESSAGE" value="langchain" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/MLP/concise.py</url>
          <line>36</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/rnn/text-sequence.py</url>
          <line>19</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/attention/MultiHeadAttention.py</url>
          <line>44</line>
          <option name="timeStamp" value="42" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/attention/MultiHeadAttention.py</url>
          <line>36</line>
          <option name="timeStamp" value="43" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/rnn/encode-decode.py</url>
          <line>236</line>
          <option name="timeStamp" value="44" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/d2l/torch.py</url>
          <line>549</line>
          <option name="timeStamp" value="50" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/deepai$bert.coverage" NAME="bert Coverage Results" MODIFIED="1747279573129" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/attention" />
    <SUITE FILE_PATH="coverage/deepai$pkg.coverage" NAME="pkg Coverage Results" MODIFIED="1745590855883" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pkg" />
    <SUITE FILE_PATH="coverage/deepai$lenet.coverage" NAME="lenet Coverage Results" MODIFIED="1746541563492" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/cnn" />
    <SUITE FILE_PATH="coverage/deepai$start03.coverage" NAME="start03 Coverage Results" MODIFIED="1747401948589" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langchain" />
    <SUITE FILE_PATH="coverage/deepai$linear_regresion.coverage" NAME="linear-regresion Coverage Results" MODIFIED="1745658354699" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/liner" />
    <SUITE FILE_PATH="coverage/deepai$start01.coverage" NAME="start01 Coverage Results" MODIFIED="1747320610019" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langchain" />
    <SUITE FILE_PATH="coverage/deepai$lstm.coverage" NAME="lstm Coverage Results" MODIFIED="1746716134601" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/rnn" />
    <SUITE FILE_PATH="coverage/deepai$regresion2.coverage" NAME="regresion2 Coverage Results" MODIFIED="1745670002680" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/liner" />
    <SUITE FILE_PATH="coverage/deepai$encode_decode.coverage" NAME="encode-decode Coverage Results" MODIFIED="1747054253515" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/rnn" />
    <SUITE FILE_PATH="coverage/deepai$MultiHeadAttention.coverage" NAME="MultiHeadAttention Coverage Results" MODIFIED="1746971373285" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/attention" />
    <SUITE FILE_PATH="coverage/deepai$dropout1.coverage" NAME="dropout1 Coverage Results" MODIFIED="1746538657202" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/MLP" />
    <SUITE FILE_PATH="coverage/deepai$rnn_concise.coverage" NAME="rnn-concise Coverage Results" MODIFIED="1746711161319" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/rnn" />
    <SUITE FILE_PATH="coverage/deepai$language_model.coverage" NAME="language-model Coverage Results" MODIFIED="1746714597435" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/rnn" />
    <SUITE FILE_PATH="coverage/deepai$transformer.coverage" NAME="transformer Coverage Results" MODIFIED="1747209737575" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/attention" />
    <SUITE FILE_PATH="coverage/deepai$concise.coverage" NAME="concise Coverage Results" MODIFIED="1746536514303" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/MLP" />
    <SUITE FILE_PATH="coverage/deepai$text_sequence.coverage" NAME="text-sequence Coverage Results" MODIFIED="1746687918949" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/rnn" />
    <SUITE FILE_PATH="coverage/deepai$torch.coverage" NAME="torch Coverage Results" MODIFIED="1746936540231" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/d2l" />
    <SUITE FILE_PATH="coverage/deepai$load_key.coverage" NAME="load_key Coverage Results" MODIFIED="1747317693716" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langchain/config" />
    <SUITE FILE_PATH="coverage/deepai$bahdanau.coverage" NAME="bahdanau Coverage Results" MODIFIED="1746969295601" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/attention" />
    <SUITE FILE_PATH="coverage/deepai$start04.coverage" NAME="start04 Coverage Results" MODIFIED="1747405442198" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/langchain/start" />
    <SUITE FILE_PATH="coverage/deepai$sequence.coverage" NAME="sequence Coverage Results" MODIFIED="1746624922188" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/rnn" />
  </component>
</project>
import torch
import logging
from transformers import BertTokenizer, BertForSequenceClassification, Trainer, TrainingArguments
from datasets import load_dataset
from sklearn.metrics import accuracy_score, f1_score
from typing import Dict, Any
import numpy as np

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sentiment_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SentimentAnalysisPipeline:
    def __init__(self,  model_name: str = "bert-base-chinese", dataset_name: str = "lansinuote/ChnSentiCorp"):
        """初始化情感分析管道"""
        self.model_name = model_name
        self.dataset_name = dataset_name
        self.tokenizer = None
        self.model = None
        self.train_dataset = None
        self.eval_dataset = None
        
    def load_dataset(self) -> None:
        """下载并加载数据集"""
        logger.info(f"开始下载并加载数据集: {self.dataset_name}")
        try:
            self.dataset = load_dataset(self.dataset_name)
            logger.info("数据集加载成功")
            logger.info(f"数据集结构: {self.dataset}")
        except Exception as e:
            logger.error(f"数据集加载失败: {str(e)}")
            raise

    def load_model(self) -> None:
        """下载并加载预训练模型和tokenizer"""
        logger.info(f"开始加载模型和tokenizer: {self.model_name}")
        try:
            self.tokenizer = BertTokenizer.from_pretrained(self.model_name)
            self.model = BertForSequenceClassification.from_pretrained(self.model_name, num_labels=2)
            logger.info("模型和tokenizer加载成功")
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise

    def preprocess_data(self, max_length: int = 128) -> None:
        """预处理数据集"""
        logger.info("开始预处理数据")
        
        def tokenize_function(examples):
            return self.tokenizer(
                examples["text"], 
                padding="max_length", 
                truncation=True, 
                max_length=max_length
            )
        
        try:
            # Tokenize处理
            tokenized_datasets = self.dataset.map(tokenize_function, batched=True)
            # 移除不需要的列
            tokenized_datasets = tokenized_datasets.remove_columns(["text"])
            # 重命名标签列
            tokenized_datasets = tokenized_datasets.rename_column("label", "labels")
            # 设置PyTorch格式
            tokenized_datasets.set_format("torch")
            
            # 分割数据集
            self.train_dataset = tokenized_datasets["train"]
            self.eval_dataset = tokenized_datasets["test"]
            
            logger.info("数据预处理完成")
            logger.info(f"训练集样本数: {len(self.train_dataset)}")
            logger.info(f"测试集样本数: {len(self.eval_dataset)}")
        except Exception as e:
            logger.error(f"数据预处理失败: {str(e)}")
            raise

    def compute_metrics(self, eval_pred) -> Dict[str, float]:
        """计算评估指标"""
        predictions, labels = eval_pred
        predictions = np.argmax(predictions, axis=1)
        return {
            "accuracy": accuracy_score(labels, predictions),
            "f1": f1_score(labels, predictions, average="binary")
        }

    def train_model(
        self,
        output_dir: str = "./results",
        learning_rate: float = 2e-5,
        per_device_train_batch_size: int = 16,
        num_train_epochs: int = 3,
        eval_strategy: str = "epoch",
        save_strategy: str = "epoch"
    ) -> None:
        """训练模型"""
        logger.info("开始配置训练参数")
        
        training_args = TrainingArguments(
            output_dir=output_dir,
            eval_strategy=eval_strategy,
            learning_rate=learning_rate,
            per_device_train_batch_size=per_device_train_batch_size,
            per_device_eval_batch_size=16,
            num_train_epochs=num_train_epochs,
            weight_decay=0.01,
            save_strategy=save_strategy,
            load_best_model_at_end=True,
            logging_dir='./logs',
            logging_steps=10,
        )

        logger.info("初始化Trainer")
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=self.train_dataset,
            eval_dataset=self.eval_dataset,
            compute_metrics=self.compute_metrics,
        )

        logger.info("开始训练模型...")
        try:
            train_result = trainer.train()
            logger.info("训练完成")
            logger.info(f"训练损失: {train_result.training_loss:.4f}")
            
            # 评估模型
            logger.info("在测试集上评估模型")
            eval_results = trainer.evaluate()
            logger.info(f"评估结果 - 准确率: {eval_results['eval_accuracy']:.4f}")
            logger.info(f"评估结果 - F1分数: {eval_results['eval_f1']:.4f}")
            
        except Exception as e:
            logger.error(f"训练过程中出错: {str(e)}")
            raise

    def save_model(self, save_path: str = "./fine_tuned_bert") -> None:
        """保存模型"""
        logger.info(f"正在保存模型到 {save_path}")
        try:
            self.model.save_pretrained(save_path)
            self.tokenizer.save_pretrained(save_path)
            logger.info("模型保存成功")
        except Exception as e:
            logger.error(f"模型保存失败: {str(e)}")
            raise

    def load_saved_model(self, model_path: str = "./fine_tuned_bert") -> None:
        """加载已保存的模型"""
        logger.info(f"从 {model_path} 加载模型")
        try:
            self.model = BertForSequenceClassification.from_pretrained(model_path)
            logger.info("模型加载成功")
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise

    def predict_sentiment(self, text: str) -> Dict[str, Any]:
        """预测单个文本的情感"""
        logger.info(f"预测文本: '{text}'")
        try:
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=128
            )
            
            with torch.no_grad():
                outputs = self.model(**inputs)
                prediction = torch.argmax(outputs.logits, dim=-1).item()
                
            sentiment = "positive" if prediction == 1 else "negative"
            logger.info(f"预测结果: {sentiment} (标签: {prediction})")
            
            return {
                "text": text,
                "sentiment": sentiment,
                "label": prediction,
                "confidence": torch.softmax(outputs.logits, dim=-1).tolist()[0]
            }
            
        except Exception as e:
            logger.error(f"预测过程中出错: {str(e)}")
            raise

def main():
    """主执行函数"""
    try:
        # 初始化管道
        pipeline = SentimentAnalysisPipeline()
        
        # 1. 加载数据集
        pipeline.load_dataset()
        
        # 2. 加载模型
        pipeline.load_model()
        
        # 3. 预处理数据
        pipeline.preprocess_data()
        
        # 4. 训练模型
        pipeline.train_model()
        
        # 5. 保存模型
        pipeline.save_model()
        
        # 6. 测试模型
        pipeline.load_saved_model()
        
        # 测试样本
        test_samples = [
            "这部电影太棒了，演员表演出色！",
            "服务很差，再也不会来了。",
            "产品一般，没什么特别之处。"
        ]
        
        for sample in test_samples:
            result = pipeline.predict_sentiment(sample)
            logger.info(f"测试结果: {result}")
            
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main()
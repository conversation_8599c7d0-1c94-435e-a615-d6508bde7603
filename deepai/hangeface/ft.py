import torch
import logging
from transformers import BertTokenizer, BertForSequenceClassification, AdamW
from datasets import load_dataset
from torch.utils.data import DataLoader
from sklearn.metrics import accuracy_score
import numpy as np
from tqdm import tqdm

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sentiment_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ChineseSentimentAnalyzer:
    def __init__(self):
        self.device = self._get_device()
        logger.info(f"使用设备: {self.device}")
        self.tokenizer = None
        self.model = None
        self.train_loader = None
        self.test_loader = None

    def _get_device(self):
        """自动检测最佳可用设备"""
        if torch.backends.mps.is_available():
            return torch.device("mps")
        elif torch.cuda.is_available():
            return torch.device("cuda")
        else:
            return torch.device("cpu")

    def load_dataset(self):
        """下载并加载数据集"""
        logger.info("开始下载并加载数据集 lansinuote/ChnSentiCorp")
        try:
            dataset = load_dataset("lansinuote/ChnSentiCorp")
            logger.info("数据集加载成功")
            logger.info(f"训练集样本数: {len(dataset['train'])}")
            logger.info(f"测试集样本数: {len(dataset['test'])}")
            return dataset
        except Exception as e:
            logger.error(f"数据集加载失败: {str(e)}")
            raise

    def load_model(self):
        """加载预训练模型和tokenizer"""
        logger.info("开始加载模型 google-bert/bert-base-chinese")
        try:
            self.tokenizer = BertTokenizer.from_pretrained("bert-base-chinese")
            self.model = BertForSequenceClassification.from_pretrained(
                "bert-base-chinese",
                num_labels=2
            ).to(self.device)
            logger.info("模型和tokenizer加载成功")
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise

    def preprocess_data(self, dataset, batch_size=16, max_length=128):
        """预处理数据并创建DataLoader"""
        logger.info("开始预处理数据")

        def tokenize_function(examples):
            return self.tokenizer(
                examples["text"],
                padding="max_length",
                truncation=True,
                max_length=max_length,
                return_tensors="pt"
            )

        try:
            # Tokenize处理
            tokenized_train = dataset["train"].map(tokenize_function, batched=True)
            tokenized_test = dataset["test"].map(tokenize_function, batched=True)

            # 设置格式
            tokenized_train.set_format(type='torch', columns=['input_ids', 'attention_mask', 'label'])
            tokenized_test.set_format(type='torch', columns=['input_ids', 'attention_mask', 'label'])

            # 创建DataLoader
            self.train_loader = DataLoader(tokenized_train, batch_size=batch_size, shuffle=True)
            self.test_loader = DataLoader(tokenized_test, batch_size=batch_size)

            logger.info("数据预处理完成")
        except Exception as e:
            logger.error(f"数据预处理失败: {str(e)}")
            raise

    def train(self, epochs=3, learning_rate=2e-5):
        """训练模型"""
        if not self.model or not self.train_loader:
            logger.error("模型或数据未初始化")
            raise RuntimeError("请先加载模型和数据")

        logger.info("开始训练模型")
        self.model.train()
        optimizer = AdamW(self.model.parameters(), lr=learning_rate)
        loss_fn = torch.nn.CrossEntropyLoss()

        for epoch in range(epochs):
            total_loss = 0
            progress_bar = tqdm(self.train_loader, desc=f"Epoch {epoch + 1}")
            
            for batch in progress_bar:
                optimizer.zero_grad()
                
                inputs = {
                    'input_ids': batch['input_ids'].to(self.device),
                    'attention_mask': batch['attention_mask'].to(self.device)
                }
                labels = batch['label'].to(self.device)
                
                outputs = self.model(**inputs)
                loss = loss_fn(outputs.logits, labels)
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
                progress_bar.set_postfix({'loss': loss.item()})
            
            avg_loss = total_loss / len(self.train_loader)
            logger.info(f"Epoch {epoch + 1} 完成, 平均损失: {avg_loss:.4f}")

    def evaluate(self):
        """评估模型"""
        if not self.model or not self.test_loader:
            logger.error("模型或数据未初始化")
            raise RuntimeError("请先加载模型和数据")

        logger.info("开始在测试集上评估模型")
        self.model.eval()
        predictions, true_labels = [], []

        with torch.no_grad():
            for batch in tqdm(self.test_loader, desc="评估"):
                inputs = {
                    'input_ids': batch['input_ids'].to(self.device),
                    'attention_mask': batch['attention_mask'].to(self.device)
                }
                labels = batch['label'].to(self.device)
                
                outputs = self.model(**inputs)
                preds = torch.argmax(outputs.logits, dim=1)
                
                predictions.extend(preds.cpu().numpy())
                true_labels.extend(labels.cpu().numpy())

        accuracy = accuracy_score(true_labels, predictions)
        logger.info(f"测试集准确率: {accuracy:.4f}")
        return accuracy

    def save_model(self, save_path="./fine_tuned_bert"):
        """保存模型"""
        logger.info(f"保存模型到 {save_path}")
        try:
            self.model.save_pretrained(save_path)
            self.tokenizer.save_pretrained(save_path)
            logger.info("模型保存成功")
        except Exception as e:
            logger.error(f"模型保存失败: {str(e)}")
            raise

    def predict(self, text):
        """预测单个文本的情感"""
        if not self.model or not self.tokenizer:
            logger.error("模型或tokenizer未初始化")
            raise RuntimeError("请先加载模型")

        logger.info(f"预测文本: '{text}'")
        try:
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=128
            ).to(self.device)

            with torch.no_grad():
                outputs = self.model(**inputs)
                prediction = torch.argmax(outputs.logits, dim=1).item()

            sentiment = "正面" if prediction == 1 else "负面"
            logger.info(f"预测结果: {sentiment} (标签: {prediction})")
            return {"text": text, "sentiment": sentiment, "label": prediction}
        except Exception as e:
            logger.error(f"预测失败: {str(e)}")
            raise

def main():
    try:
        # 初始化分析器
        analyzer = ChineseSentimentAnalyzer()
        
        # 1. 加载数据集
        dataset = analyzer.load_dataset()
        
        # 2. 加载预训练模型
        analyzer.load_model()
        
        # 3. 预处理数据
        analyzer.preprocess_data(dataset)
        
        # 4. 微调模型
        analyzer.train(epochs=3)
        
        # 5. 评估模型
        analyzer.evaluate()
        
        # 6. 保存模型
        analyzer.save_model()
        
        # 7. 测试模型
        test_samples = [
            "这部电影太棒了，演员表演出色！",
            "服务很差，再也不会来了。",
            "产品一般，没什么特别之处。"
        ]
        
        for sample in test_samples:
            result = analyzer.predict(sample)
            logger.info(f"测试结果: {result}")
            
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main()
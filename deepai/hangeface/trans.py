from transformers import AutoTokenizer, AutoModelForMaskedLM
from transformers import AutoTokenizer, AutoModelForCausalLM
from transformers import pipeline

cache_dir = "/Users/<USER>/.cache/huggingface/hub"
model_name = "uer/gpt2-chinese-cluecorpussmall"
tokenizer = AutoTokenizer.from_pretrained(
    model_name, cache_dir=cache_dir, local_files_only=True
)

model = AutoModelForCausalLM.from_pretrained(
    model_name, cache_dir=cache_dir, local_files_only=True
)
# local_files_only 是否仅使用本地文件（不联网检查更新）

print("down done")

# 本地调用


# Use a pipeline as a high-level helper

# 一定是绝对路径
model_dir = "/Users/<USER>/.cache/huggingface/hub/models--uer--gpt2-chinese-cluecorpussmall/snapshots/c2c0249d8a2731f269414cc3b22dff021f8e07a3"

model = AutoModelForCausalLM.from_pretrained(model_dir)
tokenizer = AutoTokenizer.from_pretrained(model_dir)

pipe = pipeline("text-generation", model=model, tokenizer=tokenizer)

output = pipe("你好，我是一个语言模型", max_length=50, num_return_sequences=1)

print(output)


from transformers import BertTokenizer, GPT2LMHeadModel, TextGenerationPipeline

# tokenizer = BertTokenizer.from_pretrained("uer/gpt2-distil-chinese-cluecorpussmall")
# model = GPT2LMHeadModel.from_pretrained("uer/gpt2-distil-chinese-cluecorpussmall")
text_generator = TextGenerationPipeline(
    model=model, tokenizer=tokenizer, device="mps:0"
)
# 增加了参数之后，生成的质量就会好些
output = text_generator(
    "这是很久之前的事情了",
    max_length=100,
    do_sample=True,
    num_return_sequences=1,
    truncation=True,
    temperature=0.7,
    top_p=0.95,
    top_k=50,
    clean_up_tokenization_spaces=True,
)
print(output)

from transformers import AutoModelForQuestionAnswering, AutoTokenizer, pipeline

cache_dir = "/Users/<USER>/.cache/huggingface/hub"
model_name = "models--uer--roberta-base-chinese-extractive-qa/snapshots/9b02143727b9c4655d18b43a69fc39d5eb3ddd53"

model = AutoModelForQuestionAnswering.from_pretrained(
    cache_dir + "/" + model_name,
    cache_dir=cache_dir,
)
tokenizer = AutoTokenizer.from_pretrained(
    cache_dir + "/" + model_name, cache_dir=cache_dir
)

QA = pipeline("question-answering", model=model, tokenizer=tokenizer)
#  这里的参数应该怎么传，应该怎么查接口呢？不重要了，知道是怎么传就行了
QA_input = {
    "question": "著名诗歌《假如生活欺骗了你》的作者是",
    "context": "普希金从那里学习人民的语言，吸取了许多有益的养料，这一切对普希金后来的创作产生了很大的影响。这两年里，普希金创作了不少优秀的作品，如《囚徒》、《致大海》、《致凯恩》和《假如生活欺骗了你》等几十首抒情诗，叙事诗《努林伯爵》，历史剧《鲍里斯·戈都诺夫》，以及《叶甫盖尼·奥涅金》前六章。",
}
out = QA(QA_input)
print(out)

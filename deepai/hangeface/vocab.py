from transformers import BertTokenizer, GPT2LMHeadModel, TextGenerationPipeline

model_name = "bert-base-chinese"

model_dir = "/Users/<USER>/.cache/huggingface/hub/models--bert-base-chinese/snapshots/c30a6ed22ab4564dc1e3b2ecbf6e766b0611a33f"


token = BertTokenizer.from_pretrained(model_dir)

print(token)

vac = token.get_vocab()
# print(vac)

print(len(vac))
print("yang" in vac)
print("阳光" in vac)

token.add_tokens(["阳光", "大地"])
token.add_special_tokens({"eos_token": "[EOS]"})
vac = token.get_vocab()
# save to new token to local


# print(vac)
print(len(vac))
print("阳光" in vac)

out = token.encode(
    text="阳光照在大地上[EOS]",
    text_pair=None,
    truncation=True,
    padding="max_length",
    max_length=10,
    # return_tensors="pt",
    add_special_tokens=True,
)
print(out)

print(token.decode(out))


token.save_pretrained("./models/bert-base-chinese")

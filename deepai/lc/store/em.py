#  向量数据持久化保存
import os
from load_key import load_key
from langchain_community.embeddings import DashScopeEmbeddings
from langchain_redis import RedisVectorStore, RedisConfig
import redis
from langchain_core.prompts import ChatPromptTemplate


redis_url = "redis://localhost:6379"

redis_client = redis.from_url(redis_url)

print(redis_client.ping())


config = RedisConfig(index_name="fruit", redis_url=redis_url)

embedding_model = DashScopeEmbeddings(
    model="text-embedding-v3", dashscope_api_key=load_key("DASHSCOPE_API_KEY")
)
vector_store = RedisVectorStore(embeddings=embedding_model, config=config)
vector_store.add_texts(["香蕉很长", "苹果很甜", "西瓜又大又圆"])
scored_results = vector_store.similarity_search_with_score("又大又圆的水果是什么", k=3)
for doc, score in scored_results:
    print(doc.page_content, score)

retriever = vector_store.as_retriever(search_type="similarity", search_kwargs={"k": 3})
res = retriever.invoke("长长的水果是什么")
print(res)


prompt = ChatPromptTemplate.from_messages(
    [
        ("human", "{question}"),
    ]
)


def format_prompt_value(prompt_value):
    return prompt_value.to_string()


chain = prompt | format_prompt_value | retriever

# 链式调用并传入用户的问题
document = chain.invoke({"question": "又长又甜的水果是什么"})

for doc in document:
    print(doc.page_content)

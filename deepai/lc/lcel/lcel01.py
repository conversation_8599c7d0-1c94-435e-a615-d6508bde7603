from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_core.prompts import ChatPromptTemplate

from langchain_openai import ChatOpenAI

from load_key import load_key


prompt_template = ChatPromptTemplate.from_messages(
    [
        ("system", "Translate the following from English into {language}"),
        ("user", "{text}"),
    ]
)

llm = ChatOpenAI(
    model="deepseek-v3",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    openai_api_key=load_key("DASHSCOPE_API_KEY"),
)


parse = StrOutputParser()

chain = prompt_template | llm | parse

res = chain.invoke({"text": "nice to me you ", "language": "Chinese"})
print(res)

analysis_prompt = ChatPromptTemplate.from_messages(
    [
        ("system", "我应该怎么回呢?{text}。给我一个五个字的示例"),
    ]
)

chain2 = {"text": chain} | analysis_prompt | llm | parse
chain2.get_graph().print_ascii()
# res2 = chain2.invoke({"text": "nice to me you ", "language": "Chinese"})

# print(res2)

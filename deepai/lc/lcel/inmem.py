from langchain_core.chat_history import InMemoryChatMessageHistory
from langchain_openai import ChatOpenAI

from load_key import load_key

llm = ChatOpenAI(
    model="deepseek-v3",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    openai_api_key=load_key("DASHSCOPE_API_KEY"),
)


history = InMemoryChatMessageHistory()

history.add_user_message("who are you")

aimessage = llm.invoke(history.messages)
history.add_message(aimessage)

#  second  communication

history.add_user_message("please repeat")
aimessage2 = llm.invoke(history.messages)
history.add_message(aimessage2)

print("char history:")
for msg in history.messages:
    print(f"{type(msg).__name__}: {msg}")

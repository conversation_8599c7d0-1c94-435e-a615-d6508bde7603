# from langchain_redis import RedisChatMessageHistory
from langchain_openai import ChatOpenAI
from langchain_redis.chat_message_history import RedisChatMessageHistory
from load_key import load_key
import os


llm = ChatOpenAI(
    model="deepseek-v3",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    openai_api_key=load_key("DASHSCOPE_API_KEY"),
)

# Use the environment variable if set, otherwise default to localhost
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
print(f"Connecting to Redis at: {REDIS_URL}")

# Initialize RedisChatMessageHistory

history = RedisChatMessageHistory(session_id="user_123", redis_url=REDIS_URL)

# Add messages to the history
# history.add_user_message("你是谁")
# aimessage = llm.invoke(history.messages)
# history.add_message(aimessage)
history.add_user_message("Please repeat")
aimessage2 = llm.invoke(history.messages)
history.add_message(aimessage2)

# Retrieve messages
print("Chat History:")
for message in history.messages:
    print(f"{type(message).__name__}: {message.content}")


#  把history加入chain中

from langchain_core.runnables.history import RunnableWithMessageHistory

runable = RunnableWithMessageHistory(llm, get_session_history=history)
history.clear()
runable.invoke({"text": "你是谁"})

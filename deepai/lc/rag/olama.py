from langchain_community.chat_models import ChatOllama

llm = ChatOllama(model="deepseek-r1:1.5b", base_url="http://localhost:11434")

print(llm.invoke("你是谁"))


# 兼容openapi
from langchain_openai import ChatOpenAI

llm = ChatOpenAI(
    model="deepseek-r1:1.5b",
    base_url="http://localhost:11434/v1",  # 注意，这里和上面不一样，有v1
    openai_api_key="none",
)


print(llm.invoke("你是谁"))


#  embedding

from langchain_ollama import OllamaEmbeddings

embedding_model = OllamaEmbeddings(model="nomic-embed-text", base_url="http://localhost:11434")

text = "this is a test query"
query_result = embedding_model.embed_query(text)
print(query_result)
print(len(query_result))



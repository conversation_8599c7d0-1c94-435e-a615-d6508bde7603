# 加载并解析文档

from langchain_community.document_loaders import TextLoader

loader = TextLoader("doc.txt")
docs = loader.load()
print(docs[0].page_content)

# 加载目录下的所有txt文件
from langchain_community.document_loaders import DirectoryLoader

directLoader = DirectoryLoader(
    "./docs", glob="**/*.txt", loader_cls=TextLoader, show_progress=True
)

docs = directLoader.load()
print(docs[0].page_content)

# 切分文档
from langchain_text_splitters import CharacterTextSplitter

text_splitter = CharacterTextSplitter(
    chunk_size=500,
    chunk_overlap=0,
    separator="\n\n",
    keep_separator=True,
)

segments = text_splitter.split_documents(docs)

print(len(segments))

for seg in segments:
    print(seg.page_content)
    print("---")

#  文本向量化
import os
from langchain_community.embeddings import DashScopeEmbeddings
from load_key import load_key
from langchain_redis import RedisVectorStore, RedisConfig

redis_url = "redis://localhost:6379"

config = RedisConfig(index_name="meituan-index", redis_url=redis_url)

embedding_model = DashScopeEmbeddings(
    model="text-embedding-v3", dashscope_api_key=load_key("DASHSCOPE_API_KEY")
)
vector_store = RedisVectorStore(embeddings=embedding_model, config=config)
vector_store.add_documents(segments)

# 检索增加
scored_results = vector_store.similarity_search_with_score("如何退款", k=3)


# 1 检索相关信息

query = "在线支付取消订单后钱怎么返还"

retriever = vector_store.as_retriever()
relative_segments = retriever.invoke(query, k=1)

for seg in relative_segments:
    print(seg)
    print("---")

# 2.构建提示词

from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate.from_messages(
    [
        (
            "user",
            """你是一个客服代表，根据下面的背景信息，回答用户的问题,
            已知信息:{context}
            用户问题：{question}
            如果已知信息无法回答用户问题，回答“我需要进一步了解”,请不要输回去已知信息中没有的内容,请用中文。
         """,
        ),
    ]
)
text = []
for seg in relative_segments:
    text.append(seg.page_content)
real_prompt = prompt.invoke({"context": text, "question": query})
print(real_prompt.to_string())


#  调用llm
from langchain_community.chat_models import ChatTongyi
from langchain_core.messages import HumanMessage
from langchain_text_splitters import Language

from load_key import load_key

llm = ChatTongyi(
    # model="deepseek-v3",
    model="qwen-plus",
    api_key=load_key("DASHSCOPE_API_KEY"),
    temperature=0.5,
)

response = llm.invoke(real_prompt)
print("response:", response.content)

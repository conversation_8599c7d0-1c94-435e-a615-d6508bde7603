import getpass
import json
import os.path
import os

"""
实现一个方法：
1，根据传入的keyname,转成大写，去环境变量找对应的值，如果再到就返回
2:如果没有找到，就到当前的keys.json这个文件中去找，如果找到就返回
3:如果没有找到，就让用户输入，然后保存到keys.json中,并返回用户输入的值
"""


def load_key(keyname: str) -> str:
    """
    加载API密钥，按照以下顺序查找：
    1. 环境变量中查找大写的keyname
    2. Keys.json文件中查找
    3. 用户输入并保存到Keys.json

    Args:
        keyname: 密钥名称

    Returns:
        object: 找到的密钥值
    """
    keyname = keyname.strip()
    file_name = "Keys.json"

    # 1. 从环境变量中查找
    env_key = os.getenv(keyname.upper())
    if env_key:
        return env_key

    # 2. 从Keys.json文件中查找
    if os.path.isfile(file_name):
        with open(file_name, "r") as f:
            keys = json.load(f)
        if keys is not None and keyname in keys and keys[keyname]:
            return keys[keyname]

    # 3. 让用户输入并保存
    user_input = getpass.getpass(f"请输入{keyname}的值: ").strip()

    # 创建或更新Keys.json
    keys = {}
    if os.path.isfile(file_name):
        with open(file_name, "r") as f:
            keys = json.load(f)

    keys[keyname] = user_input

    with open(file_name, "w") as f:
        json.dump(keys, f, indent=4)

    return user_input


if __name__ == "__main__":
    key = load_key("OPENAI_API_KEY")
    print(key)
    key = load_key("DEEPSEAK_API_KEY")
    print(key)

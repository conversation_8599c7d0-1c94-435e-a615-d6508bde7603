from langchain.tools import tool

import datetime


@tool(description="获取今天的日期")
def get_current_date():
    """获取今天的日期"""
    return datetime.datetime.today().strftime("%Y-%m-%d")


#  这里的工具函数的注解是非常重要的，这个就是告诉大模型工具的能力


@tool(description="获取某个城市的天气情况")
def get_city_weather(city: str):
    """
    获取某个城市的天气情况。

    Args:
        city: 城市名称
    """
    return "城市：" + city + " 的天气不错"


def bad_city_weather(city: str):
    """
    获取城市的天气情况。
    Args:
        city: 城市名称
    """
    return "城市：" + city + " 的天气很差"


from langchain_community.chat_models import ChatTongyi
from langchain_core.messages import HumanMessage
from langchain_text_splitters import Language
from langchain_core.tools import StructuredTool


from load_key import load_key

llm = ChatTongyi(
    # model="deepseek-v3",
    model="qwen-plus",
    api_key=load_key("DASHSCOPE_API_KEY"),
)
bad_city_weather = StructuredTool.from_function(
    func=bad_city_weather, description="获取城市的天气情况", name="bad_city_weather"
)

llm_with_tools = llm.bind_tools([get_current_date, get_city_weather, bad_city_weather])

all_tools = {
    "get_current_date": get_current_date,
    "get_city_weather": get_city_weather,
    "bad_city_weather": bad_city_weather,
}

query1 = "今天的日期是？"
query2 = "北京的天气如何？"

messages = [query1, query2]
# 询问大模型，大模型会判断需要调用的工具,并返回一个工具调用请求
# 返回的content是空的，说明还没有和llm进行交互
ai_message = llm_with_tools.invoke(messages)
print("111", ai_message)
messages.append(ai_message)
print(ai_message.tool_calls)

for tc in ai_message.tool_calls:
    select_tool = all_tools[tc["name"].lower()]
    tool_msg = select_tool.invoke(tc)
    messages.append(tool_msg)
res = llm_with_tools.invoke(messages)
print("222", res)

from langchain.agents import initialize_agent, AgentType

from langgraph.prebuilt import create_react_agent

agent = create_react_agent(
    tools=[get_city_weather],
    model=llm,
    # agent=AgentType.OPENAI_FUNCTIONS,
    # verbose=True,
)
res = agent.invoke([HumanMessage(query2)])
print("\n", res)

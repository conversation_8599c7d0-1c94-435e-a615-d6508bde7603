import os
from load_key import load_key
from langchain_community.embeddings import DashScopeEmbeddings

embedding_model = DashScopeEmbeddings(
    model="text-embedding-v3", dashscope_api_key=load_key("DASHSCOPE_API_KEY")
)
# text = "this is a test query"
text = "我是谁"
query_result = embedding_model.embed_query(text)
# print(query_result)
print(len(query_result))


from langchain.embeddings import OpenAIEmbeddings
from langchain.evaluation import load_evaluator
from langchain.evaluation.schema import EvaluatorType, LLMEvalChain, StringEvaluator
from langchain_community.chat_models import ChatTongyi




from langchain.embeddings import DashScopeEmbeddings
import numpy as np


# 2. 定义待比较的句子
text1 = "深度学习是人工智能的重要分支"
text2 = "神经网络在AI领域应用广泛"

# 3. 生成嵌入向量
vec1 = np.array(embedding_model.embed_query(text1))  # 向量维度：1536
vec2 = np.array(embedding_model.embed_query(text2))

# 4. 计算余弦相似度
def cosine_similarity(a, b):
    dot_product = np.dot(a, b)
    norm_a = np.linalg.norm(a)
    norm_b = np.linalg.norm(b)
    return dot_product / (norm_a * norm_b)

similarity = cosine_similarity(vec1, vec2)
print(f"Cosine Similarity: {similarity:.4f}")  
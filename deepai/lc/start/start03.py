from langchain_openai import ChatOpenAI
from langchain_core.messages.human import HumanMessage

from lc.config.load_key import load_key

llm = ChatOpenAI(
    model="deepseek-v3",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    openai_api_key=load_key("DASHSCOPE_API_KEY"),
)
# response = llm.invoke([HumanMessage("你是那个？你是搞啥的")])
# print(response)


from langchain_community.chat_models import ChatTongyi

llm = ChatTongyi(
    # model="deepseek-v3",
    model="qwen-plus",
    api_key=load_key("DASHSCOPE_API_KEY"),
)

response = llm.invoke([HumanMessage("你是那个？你是搞啥的")])
print(response)

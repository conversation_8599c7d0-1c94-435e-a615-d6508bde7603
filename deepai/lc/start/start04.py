from langchain_community.chat_models import Cha<PERSON><PERSON><PERSON>yi
from langchain_core.messages import HumanMessage
from langchain_text_splitters import Language

from load_key import load_key

llm = ChatTongyi(
    # model="deepseek-v3",
    model="qwen-plus",
    api_key=load_key("DASHSCOPE_API_KEY"),
    temperature=0.5,
)
# stream = llm.stream([HumanMessage("你是那个？你是搞啥的")])
# # 流式输出
# for ch in stream:
#     print(ch.text(), end="\n")


# 提示词模板
# 简单的智能体
from langchain_core.prompts import ChatPromptTemplate

template = ChatPromptTemplate.from_messages(
    [
        ("system", "Translate the following from English into {language}"),
        ("user", "{text}"),
    ]
)

prompt = template.invoke({"language": "中文", "text": "Hello world"})

response = llm.invoke(prompt)
print(response)

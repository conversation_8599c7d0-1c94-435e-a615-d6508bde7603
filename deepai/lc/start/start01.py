# 安装必要库（如果尚未安装）
# pip install langchain-core langchain-openai
import os

from langchain_core.language_models import LanguageModelInput
from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from config.load_key import load_key


# 使用兼容OpenAI格式的API配置（DeepSeek的API地址和Key）
llm = ChatOpenAI(
    base_url="https://api.deepseek.com/v1",  # 请替换为DeepSeek的实际API地址
    api_key=load_key("DEEPSEEK_API_KEY"),  # 替换为你的DeepSeek API Key
    model="deepseek-chat",  # 根据需求选择模型
)

# 创建提示模板
prompt = ChatPromptTemplate.from_messages(
    [("user", "你好，请帮我写一首关于{theme}的诗")]
)

# 创建调用链
chain = prompt | llm

# 调用模型
# response = chain.invoke({"theme": "春天"})

# 输出结果
# print(response.content)


from langchain_deepseek import ChatDeepSeek

os.environ["DEEPSEEK_API_KEY"] = load_key("DEEPSEEK_API_KEY")
llm = ChatDeepSeek(
    model="deepseek-chat",
)
response = llm.invoke([HumanMessage("你好，请帮我写一首关于{theme}的诗")])
print(response.content)

import math
import time
import numpy as np
import torch
from torch import nn
from torch import optim
from d2l import torch as d2l


class SGD(object):

    def __init__(self, params, lr=0.01, momentum=0.9):
        self.params = params
        self.lr = lr
        self.momentum = momentum

    def step(self):
        for param in self.params:
            param -= self.lr * param.grad

    def zero_grad(self):
        for param in self.params:
            if param.grad is not None:
                param.grad.zero_()


class LinearRegressionScratch(d2l.Module):
    def __init__(self, num_inputs: int, lr: float = 0.03, sigma: float = 0.01):
        self.save_hyperparameters()
        super().__init__()
        self.lr = lr
        self.w = torch.normal(0, sigma, (num_inputs, 1), requires_grad=True)
        self.b = torch.zeros(1, requires_grad=True)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        res = torch.matmul(x, self.w) + self.b
        return res

    def loss(self, y_hot: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
        l = (y_hot - y.reshape(y_hot.shape)).pow(2) / 2
        res = l.mean()
        return res

    def configure_optimizers(self) -> SGD:
        opt = SGD([self.w, self.b], self.lr)
        return opt


@d2l.add_to_class(d2l.Trainer)  # @save
def prepare_batch(self, batch):
    return batch


if __name__ == "__main__":
    model = LinearRegressionScratch(2, lr=0.03)
    w = torch.tensor([2.0, -3.4])
    data = d2l.SyntheticRegressionData(w, b=4.2)
    trainer = d2l.Trainer(max_epochs=3)
    trainer.fit(model, data)
    print("w", model.w)

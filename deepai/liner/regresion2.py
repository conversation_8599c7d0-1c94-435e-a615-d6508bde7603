import numpy as np
import torch
from torch import nn
from d2l import torch as d2l


class LinearRegression(d2l.Module):
    def __init__(self, lr):
        super().__init__()
        self.lr = lr
        self.save_hyperparameters()
        self.net = nn.LazyLinear(1)
        self.net.weight.data.normal_(mean=0.0, std=0.01)
        self.net.bias.data.fill_(0)

    def forward(self, x):
        return self.net(x)

    def loss(self, x, y_hot):
        fn = nn.MSELoss()
        res = fn(x, y_hot)
        return res

    def configure_optimizers(self):
        opt = torch.optim.SGD(self.parameters(), lr=self.lr)
        return opt

    def get_w_b(self):
        w = self.net.weight.data
        b = self.net.bias.data
        return w, b


if __name__ == "__main__":
    model = LinearRegression(lr=0.03)
    data = d2l.SyntheticRegressionData(w=torch.tensor([2, -3.4]), b=4.2)
    trainer = d2l.Trainer(max_epochs=3)
    trainer.fit(model, data)
    print(model.get_w_b())

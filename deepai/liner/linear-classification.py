import time
import math
import time
import numpy as np
import torch
import torchvision
from torchvision import datasets, transforms
from d2l import torch as d2l

d2l.use_svg_display()


class FashionMNIST(d2l.DataModule):
    def __init__(self, batch_size=64, resize=(28, 28)):
        super().__init__()
        self.save_hyperparameters()
        trans = transforms.Compose([transforms.Resize(resize), transforms.ToTensor()])

        self.train = torchvision.datasets.FashionMNIST(
            root=self.root, train=True, transform=trans, download=True
        )
        self.val = torchvision.datasets.FashionMNIST(
            root=self.root, train=False, transform=trans, download=True
        )

    def get_dataloader(self, train):
        data = self.train
        if self.train is None:
            data = self.val
        res = torch.utils.data.DataLoader(
            data, self.batch_size, shuffle=train, num_workers=self.num_workers
        )
        return res

    def visualize(self, batch, nrows=1, ncols=8, labels=[]):
        X, y = batch
        if not labels:
            labels = self.text_labels(y)
        d2l.show_images(X.squeeze(1), nrows, ncols, titles=labels)


class Classifier(d2l.Module):
    def validation_step(self, batch):
        y_hat = self(*batch[:-1])
        self.plot("loss", self.loss(y_hat, batch[-1]), train=False)
        self.plot("acc", self.accuracy(y_hat, batch[-1]), train=False)

    def configure_optimizers(self):
        opt = torch.optim.SGD(self.parameters(), lr=self.lr)
        return opt

    def accuracy(self, Y_hat, Y, average=True):
        Y_hat = Y_hat.reshape((-1, Y_hat.shape[-1]))
        preds = Y_hat.argmax(axis=1).type(Y.dtype)

        compare = (preds == Y.reshape(-1)).type(torch.float32)
        if average:
            compare = compare.mean()
        return compare

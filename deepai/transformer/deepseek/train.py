# train.py
import torch
import logging
from torch.utils.data import DataLoader
from config import ModelConfig, TrainingConfig
from model import Transformer
from data_utils import load_data
from torch import nn


logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

def create_masks(src, tgt, pad_idx):
    src_mask = (src != pad_idx).unsqueeze(1).unsqueeze(2)
    tgt_mask = (tgt != pad_idx).unsqueeze(1).unsqueeze(2)
    seq_len = tgt.size(1)
    nopeak_mask = (1 - torch.triu(torch.ones(1, seq_len, seq_len), diagonal=1)).bool()
    tgt_mask = tgt_mask & nopeak_mask.to(tgt.device)
    return src_mask, tgt_mask

def train():
    model_config = ModelConfig()
    train_config = TrainingConfig()
    
    # 加载数据
    train_dataset, valid_dataset = load_data()
    train_loader = DataLoader(train_dataset, batch_size=train_config.batch_size, shuffle=True)
    valid_loader = DataLoader(valid_dataset, batch_size=train_config.batch_size)
    
    # 初始化模型
    model = Transformer(model_config).to(train_config.device)
    criterion = nn.CrossEntropyLoss(ignore_index=model_config.pad_idx)
    optimizer = torch.optim.Adam(model.parameters(), lr=train_config.lr)
    
    # 训练循环
    for epoch in range(train_config.epochs):
        model.train()
        total_loss = 0
        
        for batch_idx, (src, tgt) in enumerate(train_loader):
            src = src.to(train_config.device)
            tgt = tgt.to(train_config.device)
            
            src_mask, tgt_mask = create_masks(src, tgt, model_config.pad_idx)
            output = model(src, tgt[:, :-1], src_mask, tgt_mask[:, :-1, :-1])
            
            loss = criterion(output.contiguous().view(-1, output.size(-1)), 
                            tgt[:, 1:].contiguous().view(-1))
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            
            if batch_idx % train_config.log_interval == 0:
                avg_loss = total_loss / (batch_idx + 1)
                logging.info(f"Epoch {epoch} | Batch {batch_idx} | Loss: {avg_loss:.4f}")
        
        # 验证
        model.eval()
        val_loss = 0
        with torch.no_grad():
            for src, tgt in valid_loader:
                src = src.to(train_config.device)
                tgt = tgt.to(train_config.device)
                output = model(src, tgt[:, :-1], src_mask, tgt_mask[:, :-1, :-1])
                loss = criterion(output.contiguous().view(-1, output.size(-1)), 
                                tgt[:, 1:].contiguous().view(-1))
                val_loss += loss.item()
        
        logging.info(f"Epoch {epoch} | Train Loss: {total_loss/len(train_loader):.4f} | Val Loss: {val_loss/len(valid_loader):.4f}")
    
    # 保存模型
    torch.save(model.state_dict(), train_config.save_path)
    logging.info(f"Model saved to {train_config.save_path}")

if __name__ == "__main__":
    train()
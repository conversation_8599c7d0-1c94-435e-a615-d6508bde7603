"""
AddNorm 模块使用示例
展示如何在 Transformer 架构中使用 AddNorm 模块
"""

import torch
import torch.nn as nn
from test_addnorm import AddNorm, MultiHeadAttention, FeedForward


class TransformerLayerWithAddNorm(nn.Module):
    """
    使用 AddNorm 模块的 Transformer 层示例
    """

    def __init__(self, d_model: int, n_heads: int, d_ff: int, dropout: float = 0.1):
        super().__init__()

        # 子层
        self.self_attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout)

        # AddNorm 模块
        self.add_norm1 = AddNorm(d_model, dropout)
        self.add_norm2 = AddNorm(d_model, dropout)

    def forward(self, x: torch.Tensor, mask=None) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量 [batch_size, seq_len, d_model]
            mask: 注意力掩码

        Returns:
            输出张量 [batch_size, seq_len, d_model]
        """
        # 第一个子层：自注意力 + AddNorm
        attn_output = self.self_attention(x, x, x, mask)
        x = self.add_norm1(x, attn_output)

        # 第二个子层：前馈网络 + AddNorm
        ff_output = self.feed_forward(x)
        x = self.add_norm2(x, ff_output)

        return x


def demonstrate_addnorm_usage():
    """演示 AddNorm 的使用"""
    print("=== AddNorm 模块使用演示 ===\n")

    # 设置参数
    batch_size, seq_len, d_model = 2, 8, 512
    n_heads, d_ff = 8, 2048
    dropout = 0.1

    # 创建测试数据
    x = torch.randn(batch_size, seq_len, d_model)
    print(f"输入数据形状: {x.shape}")
    print(f"输入数据均值: {x.mean().item():.6f}")
    print(f"输入数据标准差: {x.std().item():.6f}\n")

    # 使用 AddNorm 的 Transformer 层
    print("使用 AddNorm 的 Transformer 层:")
    transformer_layer = TransformerLayerWithAddNorm(d_model, n_heads, d_ff, dropout)
    output = transformer_layer(x)
    print(f"   输出形状: {output.shape}")
    print(f"   输出均值: {output.mean().item():.6f}")
    print(f"   输出标准差: {output.std().item():.6f}\n")


def demonstrate_residual_effect():
    """演示残差连接的效果"""
    print("=== 残差连接效果演示 ===\n")

    batch_size, seq_len, d_model = 2, 8, 512

    # 创建 AddNorm 模块
    add_norm = AddNorm(d_model, dropout=0.0)  # 关闭 dropout

    # 创建输入
    x = torch.randn(batch_size, seq_len, d_model)

    # 情况1：子层输出为零（恒等映射）
    zero_output = torch.zeros_like(x)
    result1 = add_norm(x, zero_output)

    # 情况2：子层输出很小
    small_output = 0.01 * torch.randn_like(x)
    result2 = add_norm(x, small_output)

    # 情况3：子层输出较大
    large_output = torch.randn_like(x)
    result3 = add_norm(x, large_output)

    print("残差连接效果对比:")
    print(f"原始输入均值: {x.mean().item():.6f}, 标准差: {x.std().item():.6f}")
    print(f"零输出结果均值: {result1.mean().item():.6f}, 标准差: {result1.std().item():.6f}")
    print(f"小输出结果均值: {result2.mean().item():.6f}, 标准差: {result2.std().item():.6f}")
    print(f"大输出结果均值: {result3.mean().item():.6f}, 标准差: {result3.std().item():.6f}")

    # 验证残差连接保持了信息
    print(f"\n残差连接保持信息的验证:")
    print(f"零输出时与原输入的相似度: {torch.cosine_similarity(x.flatten(), result1.flatten(), dim=0).item():.6f}")
    print(f"小输出时与原输入的相似度: {torch.cosine_similarity(x.flatten(), result2.flatten(), dim=0).item():.6f}")
    print(f"大输出时与原输入的相似度: {torch.cosine_similarity(x.flatten(), result3.flatten(), dim=0).item():.6f}")


def demonstrate_gradient_flow():
    """演示梯度流动的改善"""
    print("\n=== 梯度流动演示 ===\n")

    batch_size, seq_len, d_model = 2, 8, 512

    # 创建一个简单的网络来比较有无残差连接的梯度
    class WithoutResidual(nn.Module):
        def __init__(self):
            super().__init__()
            self.layers = nn.ModuleList([
                nn.Linear(d_model, d_model) for _ in range(5)
            ])
            self.layer_norms = nn.ModuleList([
                nn.LayerNorm(d_model) for _ in range(5)
            ])

        def forward(self, x):
            for layer, norm in zip(self.layers, self.layer_norms):
                x = norm(torch.relu(layer(x)))
            return x.mean()

    class WithResidual(nn.Module):
        def __init__(self):
            super().__init__()
            self.layers = nn.ModuleList([
                nn.Linear(d_model, d_model) for _ in range(5)
            ])
            self.add_norms = nn.ModuleList([
                AddNorm(d_model, dropout=0.0) for _ in range(5)
            ])

        def forward(self, x):
            for layer, add_norm in zip(self.layers, self.add_norms):
                layer_output = torch.relu(layer(x))
                x = add_norm(x, layer_output)
            return x.mean()

    # 创建网络和输入
    net_without = WithoutResidual()
    net_with = WithResidual()

    # 测试无残差连接的网络
    x1 = torch.randn(batch_size, seq_len, d_model, requires_grad=True)
    loss_without = net_without(x1)
    loss_without.backward()
    grad_without = x1.grad.clone() if x1.grad is not None else torch.zeros_like(x1)

    # 测试有残差连接的网络
    x2 = torch.randn(batch_size, seq_len, d_model, requires_grad=True)
    loss_with = net_with(x2)
    loss_with.backward()
    grad_with = x2.grad.clone() if x2.grad is not None else torch.zeros_like(x2)

    print("梯度流动比较:")
    print(f"无残差连接的梯度范数: {grad_without.norm().item():.6f}")
    print(f"有残差连接的梯度范数: {grad_with.norm().item():.6f}")

    if grad_without.norm().item() > 0:
        print(f"梯度改善比例: {(grad_with.norm() / grad_without.norm()).item():.2f}x")
    else:
        print("无残差连接的梯度为零，无法计算改善比例")


if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)

    # 运行演示
    demonstrate_addnorm_usage()
    demonstrate_residual_effect()
    demonstrate_gradient_flow()

    print("\n演示完成！")
    print("\n总结:")
    print("1. AddNorm 模块成功实现了残差连接 + 层归一化")
    print("2. Post-LN 和 Pre-LN 有不同的特性和用途")
    print("3. 残差连接有效保持了输入信息并改善了梯度流动")
    print("4. 在深层网络中，残差连接对训练稳定性至关重要")

import torch
from torch import nn
from d2l import torch as d2l


def init_cnn(module):
    if type(module) == nn.Linear or type(module) == nn.Conv2d:
        nn.init.xavier_uniform_(module.weight)


class LeNet(d2l.Classifier):
    def __init__(self, lr=0.1, num_classes=10):
        super(LeNet, self).__init__()
        self.save_hyperparameters()
        self.net = nn.Sequential(
            nn.LazyConv2d(6, kernel_size=5, padding=2),
            nn.<PERSON>g<PERSON><PERSON>(),
            nn.AvgPool2d(kernel_size=2, stride=2),
            nn.LazyConv2d(16, kernel_size=5),
            nn.<PERSON>g<PERSON><PERSON>(),
            nn.AvgPool2d(kernel_size=2, stride=2),
            nn.<PERSON><PERSON>(),
            nn.LazyLinear(120),
            nn.Sigmoid(),
            nn.LazyLinear(84),
            nn.<PERSON>g<PERSON><PERSON>(),
            nn.<PERSON>zy<PERSON>inear(num_classes),
        )

    def layer_summary(self, X_shape):
        X = torch.randn(*X_shape)
        for layer in self.net:
            X = layer(X)
            print(layer.__class__.__name__, "output shape:\t", X.shape)


if __name__ == "__main__":
    model = LeNet()
    model.layer_summary((1, 1, 28, 28))

    trainer = d2l.Trainer(max_epochs=10, num_gpus=1)
    data = d2l.FashionMNIST(batch_size=128)
    model = LeNet(lr=0.1)

    train_loader = data.get_dataloader(True)
    # 获取训练数据加载器的迭代器
    train_iterator = iter(train_loader)
    # 获取一个批次的数据
    batch = next(train_iterator)
    # 提取输入特征，忽略标签
    X = batch[0]
    model.apply_init([X], init_cnn)
    trainer.fit(model, data)


"""
nn.LazyConv2d 是 PyTorch 1.8+ 引入的延迟初始化卷积层，其特殊之处在于：
不需要预先声明输入通道数 in_channels
会在第一次前向传播时自动推断 in_channels
适用于网络结构动态变化的场景
"""

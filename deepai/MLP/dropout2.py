import torch
from torch import nn
from d2l import torch as d2l


class DropoutMLP(d2l.Classifier):
    def __init__(
        self, num_output, num_hidden_1, num_hidden_2, dropout_1, dropout_2, lr
    ):
        super().__init__()
        self.save_hyperparameters()
        self.net = nn.Sequential(
            nn.<PERSON><PERSON>(),
            nn.<PERSON>zyLinear(num_hidden_1),
            nn.ReLU(),
            nn.Dropout(dropout_1),
            nn.<PERSON>zyLinear(num_hidden_2),
            nn.ReLU(),
            nn.Dropout(dropout_2),
            nn.LazyLinear(num_output),
        )


if __name__ == "__main__":
    model = DropoutMLP(
        num_output=10,
        num_hidden_1=256,
        num_hidden_2=256,
        dropout_1=0.5,
        dropout_2=0.5,
        lr=0.1,
    )
    trainer = d2l.Trainer(max_epochs=10)
    data = d2l.FashionMNIST(batch_size=256)
    trainer.fit(model, data)

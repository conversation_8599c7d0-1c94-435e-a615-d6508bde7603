import torch
from torch import nn
from d2l import torch as d2l


def dropout_layer(X, dropout):
    """
    dropout 正则化
    :param X:
    :param dropout:
    :return:
    """

    assert 0 <= dropout <= 1
    if dropout == 1:
        return torch.zeros_like(X)
    if dropout == 0:
        return X

    mask = (torch.rand(X.shape) > dropout).float()
    res = mask * X / (1.0 - dropout)

    return res


class DropoutMLPScratch(d2l.Classifier):
    def __init__(
        self, num_output, num_hidden_1, num_hidden_2, dropout_1, dropout_2, lr
    ):
        super().__init__()
        self.save_hyperparameters()
        self.lin1 = nn.LazyLinear(num_hidden_1)
        self.lin2 = nn.LazyLinear(num_hidden_2)
        self.lin3 = nn.LazyLinear(num_output)
        self.relu = nn.ReLU()

    def forward(self, X):
        Y = X.reshape((X.shape[0], -1))
        lin1 = self.relu(self.lin1(Y))
        # 先做relu，然后再dropout
        if self.training:
            lin1 = dropout_layer(lin1, self.dropout_1)

        lin2 = self.relu(self.lin2(lin1))
        if self.training:
            lin2 = dropout_layer(lin2, self.dropout_2)
        res = self.lin3(lin2)

        return res


if __name__ == "__main__":
    model = DropoutMLPScratch(
        num_output=10,
        num_hidden_1=256,
        num_hidden_2=256,
        dropout_1=0.5,
        dropout_2=0.5,
        lr=0.1,
    )

    data = d2l.FashionMNIST(batch_size=256)
    trainer = d2l.Trainer(max_epochs=10)
    trainer.fit(model, data)

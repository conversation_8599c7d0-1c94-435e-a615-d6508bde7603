import torch
from torch import nn
from d2l import torch as d2l


def relu(X):
    a = torch.zeros_like(X)
    return torch.max(X, a)


class MLPScratch(d2l.Classifier):
    def __init__(self, num_input, num_output, num_hidden, lr, sigma=0.01):
        super().__init__()
        self.save_hyperparameters()
        self.W1 = nn.Parameter(torch.randn(num_input, num_hidden) * sigma)
        self.B1 = nn.Parameter(torch.zeros(num_hidden))

        self.W2 = nn.Parameter(torch.randn(num_hidden, num_output) * sigma)
        self.B2 = nn.Parameter(torch.zeros(num_output))

    def forward(self, X):
        Y = X.reshape((-1, self.num_input))
        out1 = torch.mm(Y, self.W1) + self.B1
        out2 = relu(out1)
        res = torch.mm(out2, self.W2) + self.B2
        return res


if __name__ == "__main__":
    model = MLPScratch(num_input=784, num_output=10, num_hidden=256, lr=0.1)
    data = d2l.FashionMNIST(batch_size=256)
    trainer = d2l.Trainer(max_epochs=10)
    trainer.fit(model, data)

import torch
from torch import nn
from d2l import torch as d2l


class MLP1(d2l.Classifier):
    def __init__(self, num_input, num_outputs, num_hiddens, lr):
        super().__init__()
        self.save_hyperparameters()
        # 手工指定输入及输出的维度
        self.net = nn.Sequential(
            nn.<PERSON>ten(),
            nn.Linear(num_input, num_hiddens),
            nn.ReLU(),
            nn.Linear(num_hiddens, num_outputs),
        )


class MLP2(d2l.Classifier):
    def __init__(self, num_outputs, num_hiddens, lr):
        super().__init__()
        self.save_hyperparameters()
        # 手工指定输入及输出的维度
        self.net = nn.Sequential(
            nn.Flatten(),
            nn.LazyLinear(num_hiddens),
            nn.ReLU(),
            nn.LazyLinear(num_outputs),
        )


if __name__ == "__main__":
    # model = MLP1(num_input=784, num_outputs=10, num_hiddens=256, lr=0.1)
    model = MLP2(num_outputs=10, num_hiddens=256, lr=0.1)
    trainer = d2l.Trainer(max_epochs=10)
    data = d2l.FashionMNIST(batch_size=256)
    trainer.fit(model, data)

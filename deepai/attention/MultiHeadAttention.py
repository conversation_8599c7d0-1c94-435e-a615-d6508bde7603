import torch
import torch.nn as nn


class MultiHeadAttention(nn.Module):
    def __init__(self, num_hiddens=512, h=8):
        super().__init__()
        self.d_k = num_hiddens // h
        self.h = h
        self.W_Q = nn.Linear(num_hiddens, num_hiddens)
        self.W_K = nn.Linear(num_hiddens, num_hiddens)
        self.W_V = nn.Linear(num_hiddens, num_hiddens)
        self.W_O = nn.Linear(num_hiddens, num_hiddens)

    def forward(self, X):
        batch_size = X.size(0)

        # 生成Q/K/V并分头
        Q = (
            self.W_Q(X).view(batch_size, -1, self.h, self.d_k).transpose(1, 2)
        )  # [batch, h, seq_len, d_k]
        K = self.W_K(X).view(batch_size, -1, self.h, self.d_k).transpose(1, 2)
        V = self.W_V(X).view(batch_size, -1, self.h, self.d_k).transpose(1, 2)

        # 缩放点积注意力
        scores = torch.matmul(Q, K.transpose(-2, -1)) / torch.sqrt(
            torch.tensor(self.d_k, dtype=torch.float32)
        )
        weights = torch.softmax(scores, dim=-1)
        output = torch.matmul(weights, V)  # [batch, h, seq_len, d_k]

        # 合并多头
        output = (
            output.transpose(1, 2).contiguous().view(batch_size, -1, self.h * self.d_k)
        )
        res = self.W_O(output)
        return res


if __name__ == "__main__":
    # 测试
    X = torch.randn(2, 10, 512)  # [batch_size=2, seq_len=10, num_hiddens=512]
    mha = MultiHeadAttention()
    output = mha(X)  # 输出形状: [2, 10, 512]
    pass

import torch
from torch import nn
from d2l import torch as d2l
import math


class PositionWiseFFN(nn.Module):
    def __init__(self, ffn_num_hiddens, ffn_num_outputs, **kwargs):
        super().__init__(**kwargs)
        self.dense1 = nn.LazyLinear(ffn_num_hiddens)
        self.relu = nn.ReLU()
        self.dense2 = nn.LazyLinear(ffn_num_outputs)

    def forward(self, X):
        res = self.dense2(self.relu(self.dense1(X)))
        return res


class AddNorm(nn.Module):
    """The residual connection followed by layer normalization."""

    def __init__(self, normalized_shape, dropout, **kwargs):
        super().__init__(**kwargs)
        self.dropout = nn.Dropout(dropout)
        self.ln = nn.LayerNorm(normalized_shape)

    def forward(self, X, Y):
        #    残差连接后进行层规范化
        # - X ：残差连接的 原始输入 （即“跳跃连接”的输入）。它保留了当前层之前的信息，用于缓解深度网络中的梯度消失问题。
        # - Y ：当前层的 变换输出 （例如注意力层或前馈网络的输出）。它会先经过 Dropout 正则化，再与原始输入 X 相加
        res = self.ln(self.dropout(Y) + X)
        return res


class TransformerEncoderBlock(nn.Module):
    def __init__(
        self, num_hiddens, ffn_num_hiddens, num_heads, dropout, use_bias=False, **kwargs
    ):
        super().__init__(**kwargs)
        self.attention = d2l.MultiHeadAttention(
            num_hiddens, num_heads, dropout, use_bias
        )
        self.addnorm1 = AddNorm(num_hiddens, dropout)
        self.ffn = PositionWiseFFN(ffn_num_hiddens, num_hiddens)
        self.addnorm2 = AddNorm(num_hiddens, dropout)

    def forward(self, X, valid_lens):
        a = self.attention(X, X, X, valid_lens)
        b = self.addnorm1(X, a)
        c = self.ffn(b)
        d = self.addnorm2(b, c)
        return d


class TransformerEncoder(d2l.Encoder):
    def __init__(
        self,
        vocab_size,
        num_hiddens,
        ffn_num_hiddens,
        num_heads,
        num_blks,
        dropout,
        use_bias=False,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.num_hiddens = num_hiddens
        self.embedding = nn.Embedding(vocab_size, num_hiddens)
        self.pos_encoding = d2l.PositionalEncoding(num_hiddens, dropout)
        self.blks = nn.Sequential()
        for i in range(num_blks):
            enc = TransformerEncoderBlock(
                num_hiddens, ffn_num_hiddens, num_heads, dropout, use_bias
            )
            self.blks.add_module(f"encode block{i}", enc)

    def forward(self, X, valid_lens, *args):
        # 进入位置编码前进行缩放
        a = self.embedding(X) * math.sqrt(self.num_hiddens)
        b = self.pos_encoding(a)
        self.attention_weights = [None] * len(self.blks)
        for i, blk in enumerate(self.blks):
            # 注意这里的 encoder 不是使用的 rnn,是使用的自注意力机制
            c = blk(b, valid_lens)
            b = c
            self.attention_weights[i] = blk.attention.attention.attention_weights
        return b


class TransformerDecoderBlock(nn.Module):
    def __init__(self, num_hiddens, ffn_num_hiddens, num_heads, dropout, i, **kwargs):
        super().__init__(**kwargs)
        self.i = i
        self.attention1 = d2l.MultiHeadAttention(num_hiddens, num_heads, dropout)
        self.addnorm1 = AddNorm(num_hiddens, dropout)
        self.attention2 = d2l.MultiHeadAttention(num_hiddens, num_heads, dropout)
        self.addnorm2 = AddNorm(num_hiddens, dropout)
        self.ffn = PositionWiseFFN(ffn_num_hiddens, num_hiddens)
        self.addnorm3 = AddNorm(num_hiddens, dropout)

    def forward(self, X, state):
        enc_outputs, enc_valid_lens = state[0], state[1]

        if state[2][self.i] is not None:
            # 只有在预测阶段才会有,会保存之前一步的状态，在训练阶段是没有的,
            pass
        if state[2][self.i] is None:
            key_values = X
        else:
            # 在num_steps维度上叠加
            key_values = torch.cat((state[2][self.i], X), axis=1)
        state[2][self.i] = key_values
        if self.training:
            batch_size, num_steps, _ = X.shape
            # 解码器使用完整的目标序列（teacher forcing），需要生成有效长度掩码，避免模型注意到未来的token（因果掩码）。
            # dec_valid_lens 是形状为 (batch_size, num_steps) 的张量，每个元素表示对应位置的有效长度。
            # 例如，对于 num_steps=5 ，每个样本的有效长度为 [1,2,3,4,5] ，确保第 t 步只能看到前 t 步的信息。
            dec_valid_lens = torch.arange(1, num_steps + 1, device=X.device).repeat(
                batch_size, 1
            )
        else:
            # 解码器逐个生成token（自回归），无需显式有效长度掩码（因果关系由缓存的键值对自然保证），因此设为 None 。
            dec_valid_lens = None
        a = self.attention1(X, key_values, key_values, dec_valid_lens)  # 自注意力
        b = self.addnorm1(X, a)  # 残差连接
        c = self.attention2(
            b, enc_outputs, enc_outputs, enc_valid_lens
        )  # 编码器-解码器注意力
        d = self.addnorm2(b, c)  # 残差连接
        e = self.ffn(d)  # 基于位置的前馈神经网络
        f = self.addnorm3(d, e)  # 残差连接
        return f, state


class TransformerDecoder(d2l.AttentionDecoder):
    def __init__(
        self,
        vocab_size,
        num_hiddens,
        ffn_num_hiddens,
        num_heads,
        num_blks,
        dropout,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.num_hiddens = num_hiddens
        self.num_blks = num_blks
        self.embedding = nn.Embedding(vocab_size, num_hiddens)
        self.pos_encoding = d2l.PositionalEncoding(num_hiddens, dropout)
        self.blks = nn.Sequential()  # 解码器块
        for i in range(num_blks):
            blk = TransformerDecoderBlock(
                num_hiddens, ffn_num_hiddens, num_heads, dropout, i
            )
            self.blks.add_module(f"block{i}", blk)
        self.dense = nn.LazyLinear(vocab_size)

    def init_state(self, enc_outputs, enc_valid_lens, *args):
        return [enc_outputs, enc_valid_lens, [None] * self.num_blks]

    def forward(self, X, state):
        a = self.embedding(X) * math.sqrt(self.num_hiddens)
        self._attention_weights = [None] * len(self.blks)
        b = self.pos_encoding(a)
        for i, blk in enumerate(self.blks):
            c, state = blk(b, state)
            b = c
            self._attention_weights[i] = blk.attention2.attention.attention_weights
        d = self.dense(b)
        return d, state

    @property
    def attention_weights(self):
        return self._attention_weights


if __name__ == "__main__":
    data = d2l.MTFraEng(batch_size=128)
    num_hiddens = 256
    ffn_num_hiddens = 64
    num_heads = 4
    dropout = 0.2
    num_blks = 2

    encoder = TransformerEncoder(
        len(data.src_vocab),
        num_hiddens,
        ffn_num_hiddens,
        num_heads,
        num_blks,
        dropout,
    )

    decoder = TransformerDecoder(
        len(data.tgt_vocab),
        num_hiddens,
        ffn_num_hiddens,
        num_heads,
        num_blks,
        dropout,
    )
    model = d2l.Seq2Seq(encoder, decoder, tgt_pad=data.tgt_vocab["<pad>"], lr=0.001)
    trainer = d2l.Trainer(max_epochs=3, gradient_clip_val=1, num_gpus=1)
    trainer.fit(model, data)

    """
        def forward(self, enc_X, dec_X, *args):
        enc_all_outputs = self.encoder(enc_X, *args)
        dec_state = self.decoder.init_state(enc_all_outputs, *args)
        res = self.decoder(dec_X, dec_state)
        # just return decoder output,do not return last state
        return res[0]
    """

    engs = ["go .", "i lost .", "he's calm .", "i'm home ."]
    fras = ["va !", "j'ai perdu .", "il est calme .", "je suis chez moi ."]
    data_build = data.build(engs, fras)
    preds, _ = model.predict_step(data_build, d2l.try_gpu(), data.num_steps)
    for en, fr, p in zip(engs, fras, preds):
        translation = []
        for token in data.tgt_vocab.to_tokens(p):
            if token == "<eos>":
                break
            translation.append(token)
        print(
            f"{en} => {translation}, bleu,"
            f'{d2l.bleu(" ".join(translation), fr, k=2):.3f}'
        )

    _, dec_attention_weights = model.predict_step(
        data.build([engs[-1]], [fras[-1]]), d2l.try_gpu(), data.num_steps, True
    )
    enc_attention_weights = torch.cat(model.encoder.attention_weights, 0)
    shape = (num_blks, num_heads, -1, data.num_steps)
    enc_attention_weights = enc_attention_weights.reshape(shape)
    d2l.check_shape(
        enc_attention_weights, (num_blks, num_heads, data.num_steps, data.num_steps)
    )

import torch
from torch import nn
from d2l import torch as d2l


class PositionalEncoding(nn.Module):
    def __init__(self, num_hiddens, dropout, max_len=1000):
        super().__init__()
        self.dropout = nn.Dropout(dropout)
        # 创建一个足够长的P
        self.P = torch.zeros((1, max_len, num_hiddens))

        X = torch.arange(max_len, dtype=torch.float32).reshape(-1, 1)
        Y = torch.pow(10000, torch.arange(0, num_hiddens, 2, dtype=torch.float32))
        X = X / Y

        self.P[:, :, 1::2] = torch.sin(X)
        self.P[:, :, 1::2] = torch.cos(X)

        def forward(self, X):
            X = X + self.P[:, : X.shape[1], :].to(X.device)
            X = self.dropout(X)
            return X


if __name__ == "__main__":
    po = PositionalEncoding(num_hiddens=512, dropout=0.1)

import torch
from torch import nn
from d2l import torch as d2l


class GRUScratch(d2l.Module):
    def __init__(self, num_inputs, num_hiddens, sigma=0.01):
        super().__init__()
        self.save_hyperparameters()

        def init_weight(*shape):
            return nn.Parameter(torch.randn(*shape) * sigma)

        def triple():
            return (
                init_weight(num_inputs, num_hiddens),
                init_weight(num_hiddens, num_hiddens),
                nn.Parameter(torch.zeros(num_hiddens)),
            )

        self.W_xz, self.W_hz, self.b_z = triple()  # Update Gate
        self.W_xr, self.W_hr, self.b_r = triple()  # Reset Gate
        self.W_xh, self.W_hh, self.b_h = triple()  # Candidate Hidden State

    def forward(self, inputs, H=None):
        if H is None:
            H = torch.zeros((inputs.shape[1], self.num_hiddens), device=inputs.device)
        outputs = []
        for X in inputs:
            Z = torch.sigmoid((X @ self.W_xz) + (H @ self.W_hz) + self.b_z)
            R = torch.sigmoid((X @ self.W_xr) + (H @ self.W_hr) + self.b_r)
            H_tilda = torch.tanh((X @ self.W_xh) + ((R * H) @ self.W_hh) + self.b_h)
            H = Z * H + (1 - Z) * H_tilda
            outputs.append(H)
        return outputs, H


if __name__ == "__main__":
    data = d2l.TimeMachine(batch_size=1024, num_steps=32)
    gru = GRUScratch(num_inputs=len(data.vocab), num_hiddens=32)
    model = d2l.RNNLMScratch(gru, vocab_size=len(data.vocab), lr=4)
    pre = model.predict("it has", 20, data.vocab)
    print(pre)
    trainer = d2l.Trainer(max_epochs=1, gradient_clip_val=1, num_gpus=1)
    trainer.fit(model, data)
    pre = model.predict("it has", 20, data.vocab, d2l.try_gpu())
    print(pre)

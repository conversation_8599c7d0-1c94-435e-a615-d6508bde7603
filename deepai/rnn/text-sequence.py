import torch
from torch import nn
from d2l import torch as d2l
import re
import collections


class TimeMachine(d2l.DataModule):
    def _download(self):
        fname = d2l.download(
            d2l.DATA_URL + "timemachine.txt",
            self.root,
            "090b5e7e70c295757f55df93cb0a180b9691891a",
        )
        with open(fname) as f:
            return f.read()

    def _preprocess(self, text):
        after = re.sub("[^A-Za-z]+", " ", text).lower().strip()
        return after

    def _tokenize(self, text):
        data = list(text)
        return data

    def build(self, raw_text, vocab=None):
        tokens = self._tokenize(raw_text)
        if vocab is None:
            vocab = Vocab(tokens)
        corpus = [vocab[token] for token in tokens]
        return corpus, vocab


class Vocab(object):
    def __init__(self, tokens=[], min_freq=0, reserved_tokens=[]):

        if tokens and isinstance(tokens[0], list):
            tokens = [token for line in tokens for token in line]
        counter = collections.Counter(tokens)
        token_preqs = sorted(counter.items(), key=lambda x: x[1], reverse=True)
        self.token_freqs = token_preqs

        idx_to_token = list(
            sorted(
                set(
                    ["<unk>"]
                    + reserved_tokens
                    + [token for token, freq in self.token_freqs if freq >= min_freq]
                )
            )
        )
        self.idx_to_token = idx_to_token

        token_to_idx = {token: idx for idx, token in enumerate(self.idx_to_token)}
        self.token_to_idx = token_to_idx

    def __len__(self):
        return len(self.idx_to_token)

    def __getitem__(self, tokens):
        if not isinstance(tokens, (list, tuple)):
            return self.token_to_idx.get(tokens, self.unk)
        return [self.__getitem__(token) for token in tokens]

    def to_tokens(self, indices):
        if hasattr(indices, "__len__") and len(indices) > 1:
            return [self.idx_to_token[int(index)] for index in indices]
        return self.idx_to_token[indices]

    @property
    def unk(self):
        return self.token_to_idx["<unk>"]


if __name__ == "__main__":
    data = TimeMachine()
    raw_text = data._download()
    text = data._preprocess(raw_text)
    print(text[:60])
    tokens = data._tokenize(text)
    print(tokens[:60])

    vocab = Vocab(tokens)
    indices = vocab[tokens[:10]]
    print(indices)
    print(vocab.to_tokens(indices))
    print(vocab.token_freqs[:10])

    corpus, vocab = data.build(text, vocab)
    print(len(corpus))
    print(len(vocab))

    words = text.split()

    vocab = Vocab(words)
    print(vocab.token_freqs[:10])

import torch
from torch import nn
from torch.nn import functional as F
from d2l import torch as d2l


class RNN(d2l.Module):
    def __init__(self, num_inputs, num_hiddens):
        super().__init__()
        self.save_hyperparameters()
        self.rnn = nn.RNN(num_inputs, num_hiddens)

    def forward(self, inputs, state=None):
        res = self.rnn(inputs, state)
        return res


class RNNLM(d2l.RNNLMScratch):
    def init_params(self):
        self.linear = nn.LazyLinear(self.vocab_size)

    def output_layer(self, hiddens):
        res = self.linear(hiddens).swapaxes(0, 1)
        return res


if __name__ == "__main__":
    data = d2l.TimeMachine(batch_size=1024, num_steps=32)
    rnn = RNN(num_inputs=len(data.vocab), num_hiddens=32)
    model = RNNLM(rnn, vocab_size=len(data.vocab), lr=1)
    pre = model.predict("it has", 20, data.vocab)
    print(pre)

    trainer = d2l.Trainer(max_epochs=100, gradient_clip_val=1, num_gpus=1)
    trainer.fit(model, data)

    pre = model.predict("it has", 20, data.vocab, d2l.try_gpu())
    print(pre)

import torch
from torch import nn
from d2l import torch as d2l


class Data(d2l.DataModule):
    def __init__(self, batch_size=16, T=1000, num_train=600, tau=4):
        super().__init__()
        self.save_hyperparameters()
        self.time = torch.arange(1, T + 1, dtype=torch.float32)
        self.x = torch.sin(0.01 * self.time) + torch.randn(T) * 0.2

    def get_dataloader(self, train):
        features = [self.x[i : self.T - self.tau + i] for i in range(self.tau)]
        self.features = torch.stack(features, 1)
        self.labels = self.x[self.tau :].reshape((-1, 1))
        i = slice(0, self.num_train) if train else slice(self.num_train, None)
        data = self.get_tensorloader([self.features, self.labels], train, i)
        return data


data = Data()
d2l.plot(data.time, [data.x], "time", "x", xlim=[1, 1000], figsize=(6, 3))

model = d2l.LinearRegression(lr=0.01)
trainer = d2l.Trainer(max_epochs=5)
trainer.fit(model, data)


onestep_preds = model(data.features).detach().numpy()
print("one step")

d2l.plot(
    data.time[data.tau :],
    [data.labels, onestep_preds],
    "time",
    "x",
    legend=["labels", "1-step"],
    figsize=(6, 3),
)


multistep_preds = torch.zeros(data.T)
multistep_preds[:] = data.x
for i in range(data.num_train + data.tau, data.T):
    pre = multistep_preds[i - data.tau : i].reshape((1, -1))
    # pre shape is (1,4)
    multistep_preds[i] = model(pre)

multistep_preds = multistep_preds.detach().numpy()

d2l.plot(
    [data.time[data.tau :], data.time[data.num_train + data.tau :]],
    [onestep_preds, multistep_preds[data.num_train + data.tau :]],
    "time",
    "x",
    legend=["1-step", "mul-step"],
    figsize=(6, 3),
)


在 seq2seq 模型中，源序列（src）和目标序列（tgt）的  num_steps 不必相同，这也是这类模型的主要优势之一。

rnn
# src shape (batch_size,num_steps)
# tgt shape (batch_size,num_steps)
# src shape 中的 num_steps维表示源序列的长度，即输入序列的时间步数,和tgt的num_steps维有什么关系呢
src, tgt, src_valid_len, _ = batch
关于 src 和 tgt 的 num_steps 关系：

不需要相同长度：
源序列和目标序列的长度可以完全不同
例如，英语句子"I am a student"（4个词）可以翻译为法语"Je suis étudiant"（3个词）
数据处理中的长度处理：
在批处理中，每个批次内的序列通常被填充到相同长度
但源语言批次和目标语言批次的长度可以不同
在  MTFraEng 类中，源序列和目标序列都被填充/截断到  num_steps 长度，但这只是为了批处理的便利
模型架构支持不同长度：
编码器处理完整的源序列，生成上下文向量
解码器可以生成任意长度的目标序列，不受源序列长度限制
注意代码中的 context = enc_outputs[-1].repeat(embs.shape[0], 1, 1) 将编码器的最终状态扩展到解码器的时间步数
预测时的长度差异：
在  predict_step 方法中，生成的序列长度由参数  num_steps 控制
这个长度可以与源序列长度无关，取决于模型需要生成多长的翻译
这种灵活性是 seq2seq 模型的核心优势，使其能够处理不同长度的序列间的转换，如机器翻译、文本摘要等任务。

在实际应用中，目标序列的长度通常由模型自行决定（通过生成特殊的结束标记 <eos>），而不是预先固定的。
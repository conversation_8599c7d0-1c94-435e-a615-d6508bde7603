import torch
import torch.nn as nn
from d2l import torch as d2l
from torch.nn import functional as F


def check_len(a, n):  # @sav
    """Check the length of a list."""
    assert len(a) == n, f"list's length {len(a)} != expected length {n}"


def check_shape(a, shape):  # @save
    """Check the shape of a tensor."""
    assert a.shape == shape, f"tensor's shape {a.shape} != expected shape {shape}"


class TimeMachine(d2l.DataModule):
    """The Time Machine dataset.

    Defined in :numref:`sec_text-sequence`"""

    def _download(self):
        fname = d2l.download(
            d2l.DATA_URL + "timemachine.txt",
            self.root,
            "090b5e7e70c295757f55df93cb0a180b9691891a",
        )
        with open(fname) as f:
            return f.read()

    def _preprocess(self, text):
        """Defined in :numref:`sec_text-sequence`"""
        return re.sub("[^A-Za-z]+", " ", text).lower()

    def _tokenize(self, text):
        """Defined in :numref:`sec_text-sequence`"""
        return list(text)

    def build(self, raw_text, vocab=None):
        """Defined in :numref:`sec_text-sequence`"""
        tokens = self._tokenize(self._preprocess(raw_text))
        if vocab is None:
            vocab = Vocab(tokens)
        corpus = [vocab[token] for token in tokens]
        return corpus, vocab

    def __init__(self, batch_size, num_steps, num_train=10000, num_val=5000):
        """Defined in :numref:`sec_language-model`"""
        super(d2l.TimeMachine, self).__init__()
        self.save_hyperparameters()
        corpus, self.vocab = self.build(self._download())
        array = d2l.tensor(
            [corpus[i : i + num_steps + 1] for i in range(len(corpus) - num_steps)]
        )
        # - self.X : (num_samples, num_steps) 每个样本包含 num_steps 个连续的token
        # - self.Y : (num_samples, num_steps) 对应 self.X 向右移动1位的序列
        # - self.X 作为输入序列
        # - self.Y 是对应的目标序列（预测下一个token）
        # X: [token1, token2, token3]
        # Y: [token2, token3, token4]
        self.X, self.Y = array[:, :-1], array[:, 1:]

    def get_dataloader(self, train):
        """Defined in :numref:`subsec_partitioning-seqs`"""
        idx = (
            slice(0, self.num_train)
            if train
            else slice(self.num_train, self.num_train + self.num_val)
        )
        return self.get_tensorloader([self.X, self.Y], train, idx)


class RNNScratch(d2l.Module):
    def __init__(self, num_inputs, num_hiddens, sigma=0.01):
        super().__init__()
        self.save_hyperparameters()
        self.W_xh = nn.Parameter(torch.randn(num_inputs, num_hiddens) * sigma)
        self.W_hh = nn.Parameter(torch.randn(num_hiddens, num_hiddens) * sigma)
        self.b_h = nn.Parameter(torch.zeros(num_hiddens))

    def forward(self, inputs, state=None):
        if state is None:
            state = torch.zeros(inputs.shape[1], self.num_hiddens, device=inputs.device)
        else:
            (state,) = state
        #  state shape (batch_size,num_hiddens)
        output = []
        for X in inputs:
            #  这里的state是循环更新的，所以也就叫循环神经网络
            a = torch.matmul(X, self.W_xh) + torch.matmul(state, self.W_hh) + self.b_h
            state = torch.tanh(a)
            output.append(state)
        return output, state


class RNNLMScratch(d2l.Classifier):
    def __init__(self, rnn, vocab_size, lr=0.01):
        super().__init__()
        self.save_hyperparameters()
        self.init_params()

    def init_params(self):
        self.W_hq = nn.Parameter(
            torch.randn(self.rnn.num_hiddens, self.vocab_size) * self.rnn.sigma
        )
        self.b_q = nn.Parameter(torch.zeros(self.vocab_size))

    def training_step(self, batch):
        y = batch[:-1]
        y_hat = batch[-1]
        l = self.loss(self(*y), y_hat)
        ppl = torch.exp(l)
        self.plot("ppl", ppl, train=True)
        return l

    def validation_step(self, batch):
        l = self.loss(self(*batch[:-1]), batch[-1])
        ppl = torch.exp(l)
        self.plot("ppl", ppl, train=False)

    def one_hot(self, X):
        code = F.one_hot(X.T, self.vocab_size).type(torch.float32)
        return code

    def output_layer(self, runn_outputs):
        outputs = [torch.matmul(H, self.W_hq) + self.b_q for H in runn_outputs]
        res = torch.stack(outputs, dim=1)
        return res

    def forward(self, inputs, state=None):
        # input shape (batch_size,num_steps)
        embs = self.one_hot(inputs)
        # embs shape: (num_steps, batch_size, vocab_size)
        outputs, state = self.rnn(embs, state)
        # outputs shape (num_steps,batch_size,num_hiddens)
        # state shape (batch_size,num_hiddens)
        res = self.output_layer(outputs)
        # res shape: (batch_size,num_steps, vocab_size)
        return res

    def predict(self, prefix, num_preds, vocab, device=None):
        state, outputs = None, [vocab[prefix[0]]]
        for i in range(len(prefix) + num_preds - 1):
            X = torch.tensor([[outputs[-1]]], device=device)
            embs = self.one_hot(X)
            runn_outputs, state = self.rnn(embs, state)
            if i < len(prefix) - 1:
                outputs.append(vocab[prefix[i + 1]])
            else:
                Y = self.output_layer(runn_outputs)
                outputs.append(int(Y.argmax(axis=2).reshape(1)))
        return "".join([vocab.idx_to_token[i] for i in outputs])


@d2l.add_to_class(d2l.Trainer)
def clip_grad(self, grad_clip, params):
    """Defined in :numref:`sec_rnn-concise`"""
    params = [p for p in params if p.requires_grad]
    norm = torch.sqrt(sum(torch.sum((p.grad**2)) for p in params))
    if norm > grad_clip:
        for param in params:
            param.grad[:] *= grad_clip / norm


if __name__ == "__main__":
    # batch_size, num_inputs, num_hiddens, num_steps = 2, 16, 32, 100
    # rnn = RNNScratch(num_inputs, num_hiddens)
    # X = torch.ones((num_steps, batch_size, num_inputs))
    # outputs, state = rnn(X)
    # check_len(outputs, num_steps)
    # check_shape(outputs[0], (batch_size, num_hiddens))
    # check_shape(state, (batch_size, num_hiddens))
    #
    # model = RNNLMScratch(rnn, num_inputs)
    # outputs = model(torch.ones((batch_size, num_steps), dtype=torch.int64))
    # check_shape(outputs, (batch_size, num_steps, num_inputs))

    data = d2l.TimeMachine(batch_size=1024, num_steps=32)
    rnn = RNNScratch(num_inputs=len(data.vocab), num_hiddens=24)
    model = RNNLMScratch(rnn, vocab_size=len(data.vocab), lr=1)
    trainer = d2l.Trainer(max_epochs=100, gradient_clip_val=1, num_gpus=1)
    trainer.fit(model, data)

    pre = model.predict("it has", 20, data.vocab, device=d2l.try_gpu())
    print(pre)

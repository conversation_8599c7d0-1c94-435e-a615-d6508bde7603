import math
import torch
from torch import nn
from d2l import torch as d2l
from torch.nn import functional as F
import collections


class Encoder(nn.Module):
    def __init__(self):
        super().__init__()

    # Later there can be additional arguments (e.g., length excluding padding)
    def forward(self, X, *args):
        raise NotImplementedError


class Decoder(nn.Module):
    def __init__(self):
        super().__init__()

    # Later there can be additional arguments (e.g., length excluding padding)
    def init_state(self, enc_all_outputs, *args):
        raise NotImplementedError

    def forward(self, X, state):
        raise NotImplementedError


class EncoderDecoder(d2l.Classifier):
    def __init__(self, encoder, decoder):
        super().__init__()
        self.encoder = encoder
        self.decoder = decoder

    def forward(self, enc_X, dec_X, *args):
        enc_all_outputs = self.encoder(enc_X, *args)
        dec_state = self.decoder.init_state(enc_all_outputs, *args)
        res = self.decoder(dec_X, dec_state)
        # just return decoder output,do not return last state
        return res[0]


def init_seq2seq(module):
    if type(module) == nn.Linear:
        nn.init.xavier_uniform_(module.weight)
    if type(module) == nn.GRU:
        for param in module._flat_weights_names:
            if "weight" in param:
                nn.init.xavier_uniform_(module._parameters[param])


class Seq2SeqEncoder(Encoder):
    def __init__(self, vocab_size, embed_size, num_hiddens, num_layers, dropout=0):
        super().__init__()
        self.embedding = nn.Embedding(vocab_size, embed_size)
        self.rnn = d2l.GRU(embed_size, num_hiddens, num_layers, dropout)
        self.apply(init_seq2seq)

    def forward(self, X, *args):
        # - X 的含义 ：
        # - 代表输入序列的整数索引矩阵（词表索引）
        # - 例如在机器翻译中，可以是源语言句子的词索引
        # - 形状 (batch_size, num_steps) 的成因 ：
        # - batch_size ：当前批次的句子数量
        # - num_steps ：每个句子的时间步数（即句子长度）
        # - 例如：4个句子，每个句子9个词 → (4, 9)

        # X shape ( batch_size,num_steps)
        # 这里传入 X.t() 是传入X的转置，但并不是一定只能传入转置，这里只是为了让embs的第一维是num_steps，方便后面的rnn处理
        # 也可以直接传入X，然后再转换embs
        # embs = self.embedding(x)
        # embs = embs.permute(1, 0, 2)
        embs = self.embedding(X.t().type(torch.int64))

        # embs shape (num_steps, batch_size, embed_size)
        # PyTorch 的 RNN 层默认要求输入形状为 (num_steps, batch_size, input_size)
        outputs, state = self.rnn(embs)

        # outputs shape (num_steps, batch_size, num_hiddens)
        # state shape (num_layers, batch_size, num_hiddens)
        return outputs, state


# vocab_size, embed_size, num_hiddens, num_layers = 10, 8, 16, 2
# batch_size, num_steps = 4, 9
# encoder = Seq2SeqEncoder(vocab_size, embed_size, num_hiddens, num_layers)
# X = torch.zeros((batch_size, num_steps))
# enc_outputs, enc_state = encoder(X)
# d2l.check_shape(enc_outputs, (num_steps, batch_size, num_hiddens))
#
# d2l.check_shape(enc_state, (num_layers, batch_size, num_hiddens))


class Seq2SeqDecoder(Decoder):
    def __init__(self, vocab_size, embed_size, num_hiddens, num_layers, dropout=0):
        super().__init__()
        self.embedding = nn.Embedding(vocab_size, embed_size)
        self.rnn = d2l.GRU(embed_size + num_hiddens, num_hiddens, num_layers, dropout)
        self.dense = nn.LazyLinear(vocab_size)
        self.apply(init_seq2seq)

    def init_state(self, enc_outputs, *args):
        return enc_outputs

    def forward(self, X, state):
        #  X shap (batch_size,num_steps)
        # embs shape (num_steps, batch_size, embed_size)
        embs = self.embedding(X.t().type(torch.int32))

        # enc_outputs shape (num_steps, batch_size, num_hiddens)
        # 注意，X 中的num_steps和enc_outputs的num_steps可能是不一样的,为什么会不一样呢？
        # hidden_state shape (num_layers,  batch_size, num_hiddens)
        enc_outputs, hidden_state = state

        # enc_outputs[-1]：提取编码器最后一个时间步的输出（形状 (batch_size, num_hiddens)）
        # .repeat(embs.shape[0], 1, 1)：沿时间步维度复制 num_steps 次（与解码器输入对齐），得到形状 (num_steps, batch_size, num_hiddens)
        # 作用：将编码器的最终状态扩展为与解码器输入相同的时间步维度
        # context shape (num_steps,batch_size, num_hiddens)
        context = enc_outputs[-1].repeat(embs.shape[0], 1, 1)

        # embs：解码器输入经过词嵌入后的张量（形状 (num_steps, batch_size, embed_size)）
        # torch.cat(..., -1)：在特征维度拼接词嵌入和上下文（embed_size + num_hiddens）
        # 作用：将当前解码输入与编码上下文结合，增强解码器对源序列信息的利用
        # embs_and_context shape (num_steps, batch_size, embed_size + num_hiddens)
        embs_and_context = torch.cat((embs, context), -1)

        # outputs1 shape (num_steps, batch_size, num_hiddens)
        # hidden_state shape (num_layers, batch_size, num_hiddens)
        outputs1, hidden_state = self.rnn(embs_and_context, hidden_state)

        # outputs shape (batch_size, num_steps, vocab_size)
        outputs2 = self.dense(outputs1).swapaxes(0, 1)
        return outputs2, [enc_outputs, hidden_state]


def bleu(pred_seq, label_seq, k):
    pred_tokens, label_tokens = pred_seq.split(" "), label_seq.split(" ")
    len_pred, len_label = len(pred_tokens), len(label_tokens)
    score = math.exp(min(0, 1 - len_label / len_pred))
    for n in range(1, k + 1):
        num_matches, label_subs = 0, collections.defaultdict(int)
        for i in range(len_label - n + 1):
            label_subs[" ".join(label_tokens[i : i + n])] += 1
        for i in range(len_pred - n + 1):
            if label_subs[" ".join(pred_tokens[i : i + n])] > 0:
                num_matches += 1
                label_subs[" ".join(pred_tokens[i : i + n])] -= 1
        score *= math.pow(num_matches / (len_pred - n + 1), math.pow(0.5, n))
    return score


# decoder = Seq2SeqDecoder(vocab_size, embed_size, num_hiddens, num_layers)
# state = decoder.init_state(encoder(X))
# dec_outputs, state = decoder(X, state)
# d2l.check_shape(dec_outputs, (batch_size, num_steps, vocab_size))
# d2l.check_shape(state[1], (num_layers, batch_size, num_hiddens))


class Seq2Seq(EncoderDecoder):
    def __init__(self, encoder, decoder, tgt_pad, lr):
        super().__init__(encoder, decoder)
        self.save_hyperparameters()

    def validation_step(self, batch):
        # 这里的 batch 的shape 是啥
        # batch[:-1] 部分包含输入序列和相关信息：
        # - 第一个元素是源语言序列(src)，shape为 (batch_size, num_steps)
        # - 第二个元素是目标语言序列(tgt)，shape为 (batch_size, num_steps)
        # - 第三个元素是源序列有效长度(src_valid_len)，shape为 (batch_size,)

        y_hat = self(*batch[:-1])
        # batch[-1] 是目标序列的标签，shape为 (batch_size, num_steps)
        loss = self.loss(y_hat, batch[-1])
        self.plot("loss", loss, train=False)

    def configure_optimizers(self):
        return torch.optim.Adam(self.parameters(), lr=self.lr)

    def loss(self, y_hat, y):
        l = super().loss(y_hat, y, averaged=False)
        mask = (y.reshape(-1) != self.tgt_pad).type(torch.float32)
        los = (l * mask).sum() / mask.sum()
        return los

    def predict_step(self, batch, device, tgt_max_len, save_attention_wight=False):
        # 参数 tgt_max_len 表示 预测时需要生成的目标序列的时间步数（即生成的序列长度)
        batch = [a.to(device) for a in batch]

        # src shape (batch_size,num_steps)
        # tgt shape (batch_size,num_steps)
        # src shape 中的 num_steps维表示源序列的长度，即输入序列的时间步数,和tgt的num_steps维有什么关系呢
        src, tgt, src_valid_len, _ = batch
        # enc_all_output shape: [(num_steps,batch_size,num_hiddens),(num_layer,batch_size,num_hiddens) ]
        enc_all_outputs = self.encoder(src, src_valid_len)
        # dec_state = tuple(enc_all_outputs,src)
        dec_state = self.decoder.init_state(enc_all_outputs, src)
        # # 假设 tgt 是目标序列的张量
        # tgt = torch.tensor([[1, 2, 3, 4, 5],
        # [6, 7, 8, 9, 10],
        # [11, 12, 13, 14, 15]])
        # 在这个示例中， tgt[:, 0] 会提取每个样本的第一个时间步的标记 [1, 6, 11] ， unsqueeze(-1) 会将其转换为形状为 (3, 1)
        # 的张量 [[1], [6], [11]] ，最后将这个张量放入列表 outputs 中。
        outputs = [tgt[:, 0].unsqueeze(-1)]

        attention_wights = []
        for _ in range(tgt_max_len):
            # Y shape  (batch_size, num_steps, vocab_size)
            Y, dec_state = self.decoder(outputs[-1], dec_state)
            outputs.append(Y.argmax(dim=2))
            if save_attention_wight:
                attention_wights.append(self.decoder.attention_wight)
        pred = torch.cat(outputs[1:], dim=1)
        return pred, attention_wights


if __name__ == "__main__":
    # vocab_size, embed_size, num_hiddens, num_layers = 10, 8, 16, 2
    vocab_size = 10
    num_hiddens = 9
    data = d2l.MTFraEng(batch_size=128)
    embed_size, num_hiddens, num_layers, dropout = 125, 256, 2, 0.2
    encoder = Seq2SeqEncoder(
        len(data.src_vocab), embed_size, num_hiddens, num_layers, dropout
    )
    decoder = Seq2SeqDecoder(
        len(data.tgt_vocab), embed_size, num_hiddens, num_layers, dropout
    )
    model = Seq2Seq(encoder, decoder, tgt_pad=data.tgt_vocab["<pad>"], lr=0.005)
    trainer = d2l.Trainer(max_epochs=1, gradient_clip_val=1, num_gpus=1)
    trainer.fit(model, data)

    engs = ["go .", "i lost .", "he's calm .", "i'm home .", "love"]
    fras = ["va !", "j'ai perdu .", "il est calme .", "je suis chez moi .", "just love"]
    build_data = data.build(engs, fras)
    # data.build(engs, fras) 之后的 batch_size 是多少,batch_size就len(engs)
    preds, _ = model.predict_step(build_data, d2l.try_gpu(), tgt_max_len=data.num_steps)
    for en, fr, p in zip(engs, fras, preds):
        translation = []
        for token in data.tgt_vocab.to_tokens(p):
            if token == "<eos>":
                break
            translation.append(token)
        print(
            f"{en} => {translation}, bleu,"
            f'{bleu(" ".join(translation), fr, k=2):.3f}'
        )

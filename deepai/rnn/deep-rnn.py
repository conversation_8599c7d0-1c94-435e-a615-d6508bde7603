from ast import Num
from sympy import N
import torch
from torch import nn
from d2l import torch as d2l


class StackedRNNScratch(nn.Module):
    def __init__(self, num_inputs, num_hiddens, num_layers, sigma=0.01):
        super().__init__()
        self.save_hyperparameters()

        rnns = []
        for i in range(num_layers):
            if i == 0:
                rnns.append(d2l.RNNScratch(num_inputs, num_hiddens, sigma))
            else:
                rnns.append(d2l.RNNScratch(num_hiddens, num_hiddens, sigma))
        self.rnns = nn.Sequential(*rnns)

    def forward(self, inputs, Hs=None):
        outputs = inputs
        if Hs is None:
            Hs = [None] * self.num_layers

        for i in range(self.num_layers):
            rnn = self.rnns[i]
            outputs, Hs[i] = rnn(outputs, Hs[i])
        return outputs, Hs


class GRU(d2l.RNN):
    def __init__(self, num_inputs, num_hiddens, num_layers, dropout=0):
        super().__init__()
        d2l.Module.__init__(self)
        self.save_hyperparameters()
        self.rnn = nn.GRU(num_inputs, num_hiddens, num_layers, dropout=dropout)


if __name__ == "__main__":
    data = d2l.TimeMachine(batch_size=1024, num_steps=32)
    rnn_block = StackedRNNScratch(
        num_inputs=len(data.vocab), num_hiddens=32, num_layers=2
    )
    model = d2l.RNNLMScratch(rnn_block, vocab_size=len(data.vocab), lr=2)
    trainer = d2l.Trainer(max_epochs=10, gradient_clip_val=1, num_gpus=1)
    trainer.fit(model, data)
    pre = model.predict("it has", 20, data.vocab, d2l.try_gpu())
    print(pre)

    gru = GRU(num_inputs=len(data.vocab), num_hiddens=32, num_layers=2)
    model = d2l.RNNLM(gru, vocab_size=len(data.vocab), lr=2)
    trainer.fit(model, data)
    pre = model.predict("it has", 20, data.vocab, d2l.try_gpu())
    print(pre)

import torch
from d2l import torch as d2l
from torch import nn


class LSTMScratch(d2l.Module):
    def __init__(self, num_inputs, num_hiddens, sigma=0.01):
        super().__init__()
        self.save_hyperparameters()
        
        def init_weight(*shape):
            return nn.Parameter(torch.randn(*shape) * sigma)
            
        def triple():
            return (
                init_weight(num_inputs, num_hiddens),
                init_weight(num_hiddens, num_hiddens),
                nn.Parameter(torch.zeros(num_hiddens)),
            )

        self.W_xi, self.W_hi, self.b_i = triple()
        self.W_xf, self.W_hf, self.b_f = triple()
        self.W_xo, self.W_ho, self.b_o = triple()
        self.W_xc, self.W_hc, self.b_c = triple()

    def forward(self, inputs, H_C=None):
        # H (Hidden State) 表示LSTM的隐藏状态（短期记忆）
        # C (Cell State) : 表示LSTM的单元状态（长期记忆）
        if H_C is None:
            H = torch.zeros((inputs.shape[1], self.num_hiddens), device=inputs.device)
            C = torch.zeros((inputs.shape[1], self.num_hiddens), device=inputs.device)
        else:
            H, C = H_C
        outputs = []
        for X in inputs:
            I = torch.sigmoid((X @ self.W_xi) + (H @ self.W_hi) + self.b_i)
            F = torch.sigmoid((X @ self.W_xf) + (H @ self.W_hf) + self.b_f)
            O = torch.sigmoid((X @ self.W_xo) + (H @ self.W_ho) + self.b_o)
            # C_tilda 候选记忆元
            C_tilda = torch.tanh((X @ self.W_xc) + (H @ self.W_hc) + self.b_c)
            # C 记忆元
            # 输入站 I 控制采用多少个来自 C_tilda的新数据，，遗忘门 F 控制保留多少个过去的记忆元
            C = F * C + I * C_tilda 
            H = O * torch.tanh(C)
            outputs.append(H)
        return outputs, (H, C)


if __name__ == "__main__":
    data = d2l.TimeMachine(batch_size=1024, num_steps=32)
    lstm = LSTMScratch(num_inputs=len(data.vocab), num_hiddens=32)
    model = d2l.RNNLMScratch(lstm, vocab_size=len(data.vocab), lr=1)
    pre = model.predict("it has", 20, data.vocab)
    print(pre)
    trainer = d2l.Trainer(max_epochs=10, gradient_clip_val=1, num_gpus=1)
    trainer.fit(model, data)
    pre = model.predict("it has", 20, data.vocab, d2l.try_gpu())
    print(pre)

{"cells": [{"cell_type": "markdown", "id": "5231073b", "metadata": {"origin_pos": 0}, "source": ["# 卷积神经网络\n", ":label:`chap_cnn`\n", "\n", "在前面的章节中，我们遇到过图像数据。\n", "这种数据的每个样本都由一个二维像素网格组成，\n", "每个像素可能是一个或者多个数值，取决于是黑白还是彩色图像。\n", "到目前为止，我们处理这类结构丰富的数据的方式还不够有效。\n", "我们仅仅通过将图像数据展平成一维向量而忽略了每个图像的空间结构信息，再将数据送入一个全连接的多层感知机中。\n", "因为这些网络特征元素的顺序是不变的，因此最优的结果是利用先验知识，即利用相近像素之间的相互关联性，从图像数据中学习得到有效的模型。\n", "\n", "本章介绍的*卷积神经网络*（convolutional neural network，CNN）是一类强大的、为处理图像数据而设计的神经网络。\n", "基于卷积神经网络架构的模型在计算机视觉领域中已经占主导地位，当今几乎所有的图像识别、目标检测或语义分割相关的学术竞赛和商业应用都以这种方法为基础。\n", "\n", "现代卷积神经网络的设计得益于生物学、群论和一系列的补充实验。\n", "卷积神经网络需要的参数少于全连接架构的网络，而且卷积也很容易用GPU并行计算。\n", "因此卷积神经网络除了能够高效地采样从而获得精确的模型，还能够高效地计算。\n", "久而久之，从业人员越来越多地使用卷积神经网络。即使在通常使用循环神经网络的一维序列结构任务上（例如音频、文本和时间序列分析），卷积神经网络也越来越受欢迎。\n", "通过对卷积神经网络一些巧妙的调整，也使它们在图结构数据和推荐系统中发挥作用。\n", "\n", "在本章的开始，我们将介绍构成所有卷积网络主干的基本元素。\n", "这包括卷积层本身、填充（padding）和步幅（stride）的基本细节、用于在相邻区域汇聚信息的汇聚层（pooling）、在每一层中多通道（channel）的使用，以及有关现代卷积网络架构的仔细讨论。\n", "在本章的最后，我们将介绍一个完整的、可运行的LeNet模型：这是第一个成功应用的卷积神经网络，比现代深度学习兴起时间还要早。\n", "在下一章中，我们将深入研究一些流行的、相对较新的卷积神经网络架构的完整实现，这些网络架构涵盖了现代从业者通常使用的大多数经典技术。\n", "\n", ":begin_tab:toc\n", " - [why-conv](why-conv.ipynb)\n", " - [conv-layer](conv-layer.ipynb)\n", " - [padding-and-strides](padding-and-strides.ipynb)\n", " - [channels](channels.ipynb)\n", " - [pooling](pooling.ipynb)\n", " - [lenet](lenet.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
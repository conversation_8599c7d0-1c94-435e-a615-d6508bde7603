{"cells": [{"cell_type": "markdown", "id": "92354008", "metadata": {"origin_pos": 0}, "source": ["# softmax回归的从零开始实现\n", ":label:`sec_softmax_scratch`\n", "\n", "(**就像我们从零开始实现线性回归一样，**)\n", "我们认为softmax回归也是重要的基础，因此(**应该知道实现softmax回归的细节**)。\n", "本节我们将使用刚刚在 :numref:`sec_fashion_mnist`中引入的Fashion-MNIST数据集，\n", "并设置数据迭代器的批量大小为256。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "d5454103", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:34.568185Z", "iopub.status.busy": "2023-08-18T07:05:34.567550Z", "iopub.status.idle": "2023-08-18T07:05:36.481085Z", "shell.execute_reply": "2023-08-18T07:05:36.480189Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from IPython import display\n", "from d2l import torch as d2l"]}, {"cell_type": "code", "execution_count": 2, "id": "b8bd138c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.486713Z", "iopub.status.busy": "2023-08-18T07:05:36.486051Z", "iopub.status.idle": "2023-08-18T07:05:36.589161Z", "shell.execute_reply": "2023-08-18T07:05:36.588107Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [], "source": ["batch_size = 256\n", "train_iter, test_iter = d2l.load_data_fashion_mnist(batch_size)"]}, {"cell_type": "markdown", "id": "0c00d722", "metadata": {"origin_pos": 6}, "source": ["## 初始化模型参数\n", "\n", "和之前线性回归的例子一样，这里的每个样本都将用固定长度的向量表示。\n", "原始数据集中的每个样本都是$28 \\times 28$的图像。\n", "本节[**将展平每个图像，把它们看作长度为784的向量。**]\n", "在后面的章节中，我们将讨论能够利用图像空间结构的特征，\n", "但现在我们暂时只把每个像素位置看作一个特征。\n", "\n", "回想一下，在softmax回归中，我们的输出与类别一样多。\n", "(**因为我们的数据集有10个类别，所以网络输出维度为10**)。\n", "因此，权重将构成一个$784 \\times 10$的矩阵，\n", "偏置将构成一个$1 \\times 10$的行向量。\n", "与线性回归一样，我们将使用正态分布初始化我们的权重`W`，偏置初始化为0。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "4016fe6d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.594606Z", "iopub.status.busy": "2023-08-18T07:05:36.594134Z", "iopub.status.idle": "2023-08-18T07:05:36.599637Z", "shell.execute_reply": "2023-08-18T07:05:36.598552Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["num_inputs = 784\n", "num_outputs = 10\n", "\n", "W = torch.normal(0, 0.01, size=(num_inputs, num_outputs), requires_grad=True)\n", "b = torch.zeros(num_outputs, requires_grad=True)"]}, {"cell_type": "markdown", "id": "bcc89948", "metadata": {"origin_pos": 11}, "source": ["## 定义softmax操作\n", "\n", "在实现softmax回归模型之前，我们简要回顾一下`sum`运算符如何沿着张量中的特定维度工作。\n", "如 :numref:`subseq_lin-alg-reduction`和\n", " :numref:`subseq_lin-alg-non-reduction`所述，\n", " [**给定一个矩阵`X`，我们可以对所有元素求和**]（默认情况下）。\n", " 也可以只求同一个轴上的元素，即同一列（轴0）或同一行（轴1）。\n", " 如果`X`是一个形状为`(2, 3)`的张量，我们对列进行求和，\n", " 则结果将是一个具有形状`(3,)`的向量。\n", " 当调用`sum`运算符时，我们可以指定保持在原始张量的轴数，而不折叠求和的维度。\n", " 这将产生一个具有形状`(1, 3)`的二维张量。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c0df140e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.604982Z", "iopub.status.busy": "2023-08-18T07:05:36.604096Z", "iopub.status.idle": "2023-08-18T07:05:36.615513Z", "shell.execute_reply": "2023-08-18T07:05:36.614620Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(tensor([[5., 7., 9.]]),\n", " tensor([[ 6.],\n", "         [15.]]))"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.tensor([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])\n", "X.sum(0, keepdim=True), X.sum(1, keepdim=True)"]}, {"cell_type": "markdown", "id": "3b78565c", "metadata": {"origin_pos": 14}, "source": ["回想一下，[**实现softmax**]由三个步骤组成：\n", "\n", "1. 对每个项求幂（使用`exp`）；\n", "1. 对每一行求和（小批量中每个样本是一行），得到每个样本的规范化常数；\n", "1. 将每一行除以其规范化常数，确保结果的和为1。\n", "\n", "在查看代码之前，我们回顾一下这个表达式：\n", "\n", "(**\n", "$$\n", "\\mathrm{softmax}(\\mathbf{X})_{ij} = \\frac{\\exp(\\mathbf{X}_{ij})}{\\sum_k \\exp(\\mathbf{X}_{ik})}.\n", "$$\n", "**)\n", "\n", "分母或规范化常数，有时也称为*配分函数*（其对数称为对数-配分函数）。\n", "该名称来自[统计物理学](https://en.wikipedia.org/wiki/Partition_function_(statistical_mechanics))中一个模拟粒子群分布的方程。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "4c0be245", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.620749Z", "iopub.status.busy": "2023-08-18T07:05:36.620003Z", "iopub.status.idle": "2023-08-18T07:05:36.624603Z", "shell.execute_reply": "2023-08-18T07:05:36.623701Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["def softmax(X):\n", "    X_exp = torch.exp(X)\n", "    partition = X_exp.sum(1, keepdim=True)\n", "    return X_exp / partition  # 这里应用了广播机制"]}, {"cell_type": "markdown", "id": "b641b9eb", "metadata": {"origin_pos": 17}, "source": ["正如上述代码，对于任何随机输入，[**我们将每个元素变成一个非负数。\n", "此外，依据概率原理，每行总和为1**]。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "a357bb20", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.629240Z", "iopub.status.busy": "2023-08-18T07:05:36.628878Z", "iopub.status.idle": "2023-08-18T07:05:36.640613Z", "shell.execute_reply": "2023-08-18T07:05:36.639677Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(tensor([[0.1686, 0.4055, 0.0849, 0.1064, 0.2347],\n", "         [0.0217, 0.2652, 0.6354, 0.0457, 0.0321]]),\n", " tensor([1.0000, 1.0000]))"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.normal(0, 1, (2, 5))\n", "X_prob = softmax(X)\n", "X_prob, X_prob.sum(1)"]}, {"cell_type": "markdown", "id": "b5943861", "metadata": {"origin_pos": 20}, "source": ["注意，虽然这在数学上看起来是正确的，但我们在代码实现中有点草率。\n", "矩阵中的非常大或非常小的元素可能造成数值上溢或下溢，但我们没有采取措施来防止这点。\n", "\n", "## 定义模型\n", "\n", "定义softmax操作后，我们可以[**实现softmax回归模型**]。\n", "下面的代码定义了输入如何通过网络映射到输出。\n", "注意，将数据传递到模型之前，我们使用`reshape`函数将每张原始图像展平为向量。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "098246b8", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.644224Z", "iopub.status.busy": "2023-08-18T07:05:36.643949Z", "iopub.status.idle": "2023-08-18T07:05:36.648644Z", "shell.execute_reply": "2023-08-18T07:05:36.647745Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [], "source": ["def net(X):\n", "    return softmax(torch.matmul(<PERSON><PERSON>reshape((-1, W.shape[0])), W) + b)"]}, {"cell_type": "markdown", "id": "e46f8133", "metadata": {"origin_pos": 22}, "source": ["## 定义损失函数\n", "\n", "接下来，我们实现 :numref:`sec_softmax`中引入的交叉熵损失函数。\n", "这可能是深度学习中最常见的损失函数，因为目前分类问题的数量远远超过回归问题的数量。\n", "\n", "回顾一下，交叉熵采用真实标签的预测概率的负对数似然。\n", "这里我们不使用Python的for循环迭代预测（这往往是低效的），\n", "而是通过一个运算符选择所有元素。\n", "下面，我们[**创建一个数据样本`y_hat`，其中包含2个样本在3个类别的预测概率，\n", "以及它们对应的标签`y`。**]\n", "有了`y`，我们知道在第一个样本中，第一类是正确的预测；\n", "而在第二个样本中，第三类是正确的预测。\n", "然后(**使用`y`作为`y_hat`中概率的索引**)，\n", "我们选择第一个样本中第一个类的概率和第二个样本中第三个类的概率。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "d7196ba4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.652705Z", "iopub.status.busy": "2023-08-18T07:05:36.652434Z", "iopub.status.idle": "2023-08-18T07:05:36.660790Z", "shell.execute_reply": "2023-08-18T07:05:36.659617Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0.1000, 0.5000])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["y = torch.tensor([0, 2])\n", "y_hat = torch.tensor([[0.1, 0.3, 0.6], [0.3, 0.2, 0.5]])\n", "y_hat[[0, 1], y]"]}, {"cell_type": "markdown", "id": "f60bb6e4", "metadata": {"origin_pos": 25}, "source": ["现在我们只需一行代码就可以[**实现交叉熵损失函数**]。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "8a2ec204", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.665898Z", "iopub.status.busy": "2023-08-18T07:05:36.665109Z", "iopub.status.idle": "2023-08-18T07:05:36.672113Z", "shell.execute_reply": "2023-08-18T07:05:36.671215Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([2.3026, 0.6931])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["def cross_entropy(y_hat, y):\n", "    return - torch.log(y_hat[range(len(y_hat)), y])\n", "\n", "cross_entropy(y_hat, y)"]}, {"cell_type": "markdown", "id": "889a4000", "metadata": {"origin_pos": 29}, "source": ["## 分类精度\n", "\n", "给定预测概率分布`y_hat`，当我们必须输出硬预测（hard prediction）时，\n", "我们通常选择预测概率最高的类。\n", "许多应用都要求我们做出选择。如Gmail必须将电子邮件分类为“Primary（主要邮件）”、\n", "“Social（社交邮件）”“Updates（更新邮件）”或“Forums（论坛邮件）”。\n", "Gmail做分类时可能在内部估计概率，但最终它必须在类中选择一个。\n", "\n", "当预测与标签分类`y`一致时，即是正确的。\n", "分类精度即正确预测数量与总预测数量之比。\n", "虽然直接优化精度可能很困难（因为精度的计算不可导），\n", "但精度通常是我们最关心的性能衡量标准，我们在训练分类器时几乎总会关注它。\n", "\n", "为了计算精度，我们执行以下操作。\n", "首先，如果`y_hat`是矩阵，那么假定第二个维度存储每个类的预测分数。\n", "我们使用`argmax`获得每行中最大元素的索引来获得预测类别。\n", "然后我们[**将预测类别与真实`y`元素进行比较**]。\n", "由于等式运算符“`==`”对数据类型很敏感，\n", "因此我们将`y_hat`的数据类型转换为与`y`的数据类型一致。\n", "结果是一个包含0（错）和1（对）的张量。\n", "最后，我们求和会得到正确预测的数量。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "2038b97d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.676633Z", "iopub.status.busy": "2023-08-18T07:05:36.676080Z", "iopub.status.idle": "2023-08-18T07:05:36.681962Z", "shell.execute_reply": "2023-08-18T07:05:36.680997Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [], "source": ["def accuracy(y_hat, y):  #@save\n", "    \"\"\"计算预测正确的数量\"\"\"\n", "    if len(y_hat.shape) > 1 and y_hat.shape[1] > 1:\n", "        y_hat = y_hat.argmax(axis=1)\n", "    cmp = y_hat.type(y.dtype) == y\n", "    return float(cmp.type(y.dtype).sum())"]}, {"cell_type": "markdown", "id": "51a65a85", "metadata": {"origin_pos": 32}, "source": ["我们将继续使用之前定义的变量`y_hat`和`y`分别作为预测的概率分布和标签。\n", "可以看到，第一个样本的预测类别是2（该行的最大元素为0.6，索引为2），这与实际标签0不一致。\n", "第二个样本的预测类别是2（该行的最大元素为0.5，索引为2），这与实际标签2一致。\n", "因此，这两个样本的分类精度率为0.5。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "6337adf4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.686076Z", "iopub.status.busy": "2023-08-18T07:05:36.685804Z", "iopub.status.idle": "2023-08-18T07:05:36.692192Z", "shell.execute_reply": "2023-08-18T07:05:36.691298Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["0.5"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["accuracy(y_hat, y) / len(y)"]}, {"cell_type": "markdown", "id": "f553b37b", "metadata": {"origin_pos": 34}, "source": ["同样，对于任意数据迭代器`data_iter`可访问的数据集，\n", "[**我们可以评估在任意模型`net`的精度**]。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "41ea8ca1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.696515Z", "iopub.status.busy": "2023-08-18T07:05:36.696074Z", "iopub.status.idle": "2023-08-18T07:05:36.702503Z", "shell.execute_reply": "2023-08-18T07:05:36.701545Z"}, "origin_pos": 36, "tab": ["pytorch"]}, "outputs": [], "source": ["def evaluate_accuracy(net, data_iter):  #@save\n", "    \"\"\"计算在指定数据集上模型的精度\"\"\"\n", "    if isinstance(net, torch.nn.Module):\n", "        net.eval()  # 将模型设置为评估模式\n", "    metric = Accumulator(2)  # 正确预测数、预测总数\n", "    with torch.no_grad():\n", "        for X, y in data_iter:\n", "            metric.add(accuracy(net(X), y), y.numel())\n", "    return metric[0] / metric[1]"]}, {"cell_type": "markdown", "id": "eb10ad98", "metadata": {"origin_pos": 38}, "source": ["这里定义一个实用程序类`Accumulator`，用于对多个变量进行累加。\n", "在上面的`evaluate_accuracy`函数中，\n", "我们在(**`Accumulator`实例中创建了2个变量，\n", "分别用于存储正确预测的数量和预测的总数量**)。\n", "当我们遍历数据集时，两者都将随着时间的推移而累加。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "381e6f11", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.707084Z", "iopub.status.busy": "2023-08-18T07:05:36.706353Z", "iopub.status.idle": "2023-08-18T07:05:36.712280Z", "shell.execute_reply": "2023-08-18T07:05:36.711359Z"}, "origin_pos": 39, "tab": ["pytorch"]}, "outputs": [], "source": ["class Accumulator:  #@save\n", "    \"\"\"在n个变量上累加\"\"\"\n", "    def __init__(self, n):\n", "        self.data = [0.0] * n\n", "\n", "    def add(self, *args):\n", "        self.data = [a + float(b) for a, b in zip(self.data, args)]\n", "\n", "    def reset(self):\n", "        self.data = [0.0] * len(self.data)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.data[idx]"]}, {"cell_type": "markdown", "id": "cd7411c0", "metadata": {"origin_pos": 40}, "source": ["由于我们使用随机权重初始化`net`模型，\n", "因此该模型的精度应接近于随机猜测。\n", "例如在有10个类别情况下的精度为0.1。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "77706f95", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:36.716926Z", "iopub.status.busy": "2023-08-18T07:05:36.716179Z", "iopub.status.idle": "2023-08-18T07:05:37.338754Z", "shell.execute_reply": "2023-08-18T07:05:37.337496Z"}, "origin_pos": 41, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["0.0625"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["evaluate_accuracy(net, test_iter)"]}, {"cell_type": "markdown", "id": "7eba262d", "metadata": {"origin_pos": 42}, "source": ["## 训练\n", "\n", "在我们看过 :numref:`sec_linear_scratch`中的线性回归实现，\n", "[**softmax回归的训练**]过程代码应该看起来非常眼熟。\n", "在这里，我们重构训练过程的实现以使其可重复使用。\n", "首先，我们定义一个函数来训练一个迭代周期。\n", "请注意，`updater`是更新模型参数的常用函数，它接受批量大小作为参数。\n", "它可以是`d2l.sgd`函数，也可以是框架的内置优化函数。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "a2e8f2ba", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:37.344329Z", "iopub.status.busy": "2023-08-18T07:05:37.343921Z", "iopub.status.idle": "2023-08-18T07:05:37.354464Z", "shell.execute_reply": "2023-08-18T07:05:37.353391Z"}, "origin_pos": 44, "tab": ["pytorch"]}, "outputs": [], "source": ["def train_epoch_ch3(net, train_iter, loss, updater):  #@save\n", "    \"\"\"训练模型一个迭代周期（定义见第3章）\"\"\"\n", "    # 将模型设置为训练模式\n", "    if isinstance(net, torch.nn.Module):\n", "        net.train()\n", "    # 训练损失总和、训练准确度总和、样本数\n", "    metric = Accumulator(3)\n", "    for X, y in train_iter:\n", "        # 计算梯度并更新参数\n", "        y_hat = net(X)\n", "        l = loss(y_hat, y)\n", "        if isinstance(updater, torch.optim.Optimizer):\n", "            # 使用PyTorch内置的优化器和损失函数\n", "            updater.zero_grad()\n", "            l.mean().backward()\n", "            updater.step()\n", "        else:\n", "            # 使用定制的优化器和损失函数\n", "            l.sum().backward()\n", "            updater(X.shape[0])\n", "        metric.add(float(l.sum()), accuracy(y_hat, y), y.numel())\n", "    # 返回训练损失和训练精度\n", "    return metric[0] / metric[2], metric[1] / metric[2]"]}, {"cell_type": "markdown", "id": "041a8166", "metadata": {"origin_pos": 47}, "source": ["在展示训练函数的实现之前，我们[**定义一个在动画中绘制数据的实用程序类**]`Animator`，\n", "它能够简化本书其余部分的代码。\n"]}, {"cell_type": "code", "execution_count": 16, "id": "9d3bab29", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:37.360211Z", "iopub.status.busy": "2023-08-18T07:05:37.359378Z", "iopub.status.idle": "2023-08-18T07:05:37.375759Z", "shell.execute_reply": "2023-08-18T07:05:37.374685Z"}, "origin_pos": 48, "tab": ["pytorch"]}, "outputs": [], "source": ["class Animator:  #@save\n", "    \"\"\"在动画中绘制数据\"\"\"\n", "    def __init__(self, xlabel=None, ylabel=None, legend=None, xlim=None,\n", "                 ylim=None, xscale='linear', yscale='linear',\n", "                 fmts=('-', 'm--', 'g-.', 'r:'), nrows=1, ncols=1,\n", "                 figsize=(3.5, 2.5)):\n", "        # 增量地绘制多条线\n", "        if legend is None:\n", "            legend = []\n", "        d2l.use_svg_display()\n", "        self.fig, self.axes = d2l.plt.subplots(nrows, ncols, figsize=figsize)\n", "        if nrows * ncols == 1:\n", "            self.axes = [self.axes, ]\n", "        # 使用lambda函数捕获参数\n", "        self.config_axes = lambda: d2l.set_axes(\n", "            self.axes[0], xlabel, ylabel, xlim, ylim, xscale, yscale, legend)\n", "        self.X, self.Y, self.fmts = None, None, fmts\n", "\n", "    def add(self, x, y):\n", "        # 向图表中添加多个数据点\n", "        if not hasattr(y, \"__len__\"):\n", "            y = [y]\n", "        n = len(y)\n", "        if not hasattr(x, \"__len__\"):\n", "            x = [x] * n\n", "        if not self.X:\n", "            self.X = [[] for _ in range(n)]\n", "        if not self.Y:\n", "            self.Y = [[] for _ in range(n)]\n", "        for i, (a, b) in enumerate(zip(x, y)):\n", "            if a is not None and b is not None:\n", "                self.X[i].append(a)\n", "                self.Y[i].append(b)\n", "        self.axes[0].cla()\n", "        for x, y, fmt in zip(self.X, self.Y, self.fmts):\n", "            self.axes[0].plot(x, y, fmt)\n", "        self.config_axes()\n", "        display.display(self.fig)\n", "        display.clear_output(wait=True)"]}, {"cell_type": "markdown", "id": "7c09ec7c", "metadata": {"origin_pos": 49}, "source": ["接下来我们实现一个[**训练函数**]，\n", "它会在`train_iter`访问到的训练数据集上训练一个模型`net`。\n", "该训练函数将会运行多个迭代周期（由`num_epochs`指定）。\n", "在每个迭代周期结束时，利用`test_iter`访问到的测试数据集对模型进行评估。\n", "我们将利用`Animator`类来可视化训练进度。\n"]}, {"cell_type": "code", "execution_count": 17, "id": "7ff0a317", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:37.381304Z", "iopub.status.busy": "2023-08-18T07:05:37.380550Z", "iopub.status.idle": "2023-08-18T07:05:37.389072Z", "shell.execute_reply": "2023-08-18T07:05:37.387971Z"}, "origin_pos": 50, "tab": ["pytorch"]}, "outputs": [], "source": ["def train_ch3(net, train_iter, test_iter, loss, num_epochs, updater):  #@save\n", "    \"\"\"训练模型（定义见第3章）\"\"\"\n", "    animator = Animator(xlabel='epoch', xlim=[1, num_epochs], ylim=[0.3, 0.9],\n", "                        legend=['train loss', 'train acc', 'test acc'])\n", "    for epoch in range(num_epochs):\n", "        train_metrics = train_epoch_ch3(net, train_iter, loss, updater)\n", "        test_acc = evaluate_accuracy(net, test_iter)\n", "        animator.add(epoch + 1, train_metrics + (test_acc,))\n", "    train_loss, train_acc = train_metrics\n", "    assert train_loss < 0.5, train_loss\n", "    assert train_acc <= 1 and train_acc > 0.7, train_acc\n", "    assert test_acc <= 1 and test_acc > 0.7, test_acc"]}, {"cell_type": "markdown", "id": "a5add373", "metadata": {"origin_pos": 51}, "source": ["作为一个从零开始的实现，我们使用 :numref:`sec_linear_scratch`中定义的\n", "[**小批量随机梯度下降来优化模型的损失函数**]，设置学习率为0.1。\n"]}, {"cell_type": "code", "execution_count": 18, "id": "44cfab15", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:37.393966Z", "iopub.status.busy": "2023-08-18T07:05:37.393127Z", "iopub.status.idle": "2023-08-18T07:05:37.398492Z", "shell.execute_reply": "2023-08-18T07:05:37.397420Z"}, "origin_pos": 52, "tab": ["pytorch"]}, "outputs": [], "source": ["lr = 0.1\n", "\n", "def updater(batch_size):\n", "    return d2l.sgd([W, b], lr, batch_size)"]}, {"cell_type": "markdown", "id": "0291691f", "metadata": {"origin_pos": 54}, "source": ["现在，我们[**训练模型10个迭代周期**]。\n", "请注意，迭代周期（`num_epochs`）和学习率（`lr`）都是可调节的超参数。\n", "通过更改它们的值，我们可以提高模型的分类精度。\n"]}, {"cell_type": "code", "execution_count": 19, "id": "fb9c12f8", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:37.403054Z", "iopub.status.busy": "2023-08-18T07:05:37.402682Z", "iopub.status.idle": "2023-08-18T07:06:16.273679Z", "shell.execute_reply": "2023-08-18T07:06:16.272655Z"}, "origin_pos": 55, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"180.65625pt\" viewBox=\"0 0 238.**********.65625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:06:16.233360</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 180.65625 \n", "L 238.**********.65625 \n", "L 238.965625 0 \n", "L 0 0 \n", "L 0 180.65625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 51.803125 143.1 \n", "L 51.803125 7.2 \n", "\" clip-path=\"url(#p770bf4bf54)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"med7e1813da\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#med7e1813da\" x=\"51.803125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(48.621875 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 95.203125 143.1 \n", "L 95.203125 7.2 \n", "\" clip-path=\"url(#p770bf4bf54)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#med7e1813da\" x=\"95.203125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(92.021875 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 138.603125 143.1 \n", "L 138.603125 7.2 \n", "\" clip-path=\"url(#p770bf4bf54)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#med7e1813da\" x=\"138.603125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(135.421875 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 182.003125 143.1 \n", "L 182.003125 7.2 \n", "\" clip-path=\"url(#p770bf4bf54)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#med7e1813da\" x=\"182.003125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(178.821875 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.1 \n", "L 225.403125 7.2 \n", "\" clip-path=\"url(#p770bf4bf54)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#med7e1813da\" x=\"225.403125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 171.376563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.103125 120.45 \n", "L 225.403125 120.45 \n", "\" clip-path=\"url(#p770bf4bf54)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mb5367e516c\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb5367e516c\" x=\"30.103125\" y=\"120.45\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 124.249219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 75.15 \n", "L 225.403125 75.15 \n", "\" clip-path=\"url(#p770bf4bf54)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mb5367e516c\" x=\"30.103125\" y=\"75.15\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 78.949219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 29.85 \n", "L 225.403125 29.85 \n", "\" clip-path=\"url(#p770bf4bf54)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mb5367e516c\" x=\"30.103125\" y=\"29.85\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 33.649219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 30.103125 32.881313 \n", "L 51.803125 81.601912 \n", "L 73.503125 92.009472 \n", "L 95.203125 97.511563 \n", "L 116.903125 101.190635 \n", "L 138.603125 103.866437 \n", "L 160.303125 105.580544 \n", "L 182.003125 107.456001 \n", "L 203.703125 108.630909 \n", "L 225.403125 109.701656 \n", "\" clip-path=\"url(#p770bf4bf54)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 30.103125 41.5676 \n", "L 51.803125 26.8904 \n", "L 73.503125 23.9761 \n", "L 95.203125 22.6171 \n", "L 116.903125 21.34115 \n", "L 138.603125 20.57105 \n", "L 160.303125 20.2615 \n", "L 182.003125 19.5216 \n", "L 203.703125 19.129 \n", "L 225.403125 19.064825 \n", "\" clip-path=\"url(#p770bf4bf54)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 30.103125 32.2962 \n", "L 51.803125 28.3098 \n", "L 73.503125 26.1354 \n", "L 95.203125 25.93155 \n", "L 116.903125 23.6439 \n", "L 138.603125 24.6405 \n", "L 160.303125 23.1456 \n", "L 182.003125 24.39135 \n", "L 203.703125 24.21015 \n", "L 225.403125 21.696 \n", "\" clip-path=\"url(#p770bf4bf54)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.1 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.1 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 98.667187 \n", "L 218.403125 98.667187 \n", "Q 220.403125 98.667187 220.403125 96.667187 \n", "L 220.403125 53.632812 \n", "Q 220.403125 51.632812 218.403125 51.632812 \n", "L 140.634375 51.632812 \n", "Q 138.634375 51.632812 138.634375 53.632812 \n", "L 138.634375 96.667187 \n", "Q 138.634375 98.667187 140.634375 98.667187 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_20\">\n", "     <path d=\"M 142.634375 59.73125 \n", "L 152.634375 59.73125 \n", "L 162.634375 59.73125 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 63.23125)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_21\">\n", "     <path d=\"M 142.634375 74.409375 \n", "L 152.634375 74.409375 \n", "L 162.634375 74.409375 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 77.909375)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_22\">\n", "     <path d=\"M 142.634375 89.0875 \n", "L 152.634375 89.0875 \n", "L 162.634375 89.0875 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(170.634375 92.5875)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p770bf4bf54\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["num_epochs = 10\n", "train_ch3(net, train_iter, test_iter, cross_entropy, num_epochs, updater)"]}, {"cell_type": "markdown", "id": "a8121f40", "metadata": {"origin_pos": 56}, "source": ["## 预测\n", "\n", "现在训练已经完成，我们的模型已经准备好[**对图像进行分类预测**]。\n", "给定一系列图像，我们将比较它们的实际标签（文本输出的第一行）和模型预测（文本输出的第二行）。\n"]}, {"cell_type": "code", "execution_count": 20, "id": "74ba2d12", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:06:16.277808Z", "iopub.status.busy": "2023-08-18T07:06:16.277179Z", "iopub.status.idle": "2023-08-18T07:06:16.734243Z", "shell.execute_reply": "2023-08-18T07:06:16.733343Z"}, "origin_pos": 57, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"520.1pt\" height=\"118.198357pt\" viewBox=\"0 0 520.1 118.198357\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:06:16.683770</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 118.198357 \n", "L 520.1 118.198357 \n", "L 520.1 0 \n", "L 0 0 \n", "L 0 118.198357 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 10.7 107.498357 \n", "L 82.**********.498357 \n", "L 82.442857 35.7555 \n", "L 10.7 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pffb999be85)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagee4f714b21f\" transform=\"scale(1 -1)translate(0 -72)\" x=\"10.7\" y=\"-35.498357\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 10.7 107.498357 \n", "L 10.7 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 82.**********.498357 \n", "L 82.442857 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 10.7 107.498357 \n", "L 82.**********.498357 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 10.7 35.7555 \n", "L 82.442857 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_1\">\n", "    <!-- ankle boot -->\n", "    <g transform=\"translate(14.848304 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"61.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"182.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"210.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"271.875\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"303.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"367.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"428.320312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"489.501953\"/>\n", "    </g>\n", "    <!-- ankle boot -->\n", "    <g transform=\"translate(14.848304 29.7555)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"61.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"182.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"210.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"271.875\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"303.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"367.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"428.320312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"489.501953\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 96.791429 107.498357 \n", "L 168.534286 107.498357 \n", "L 168.534286 35.7555 \n", "L 96.791429 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p5fdd32ce50)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image1ce9c2aff6\" transform=\"scale(1 -1)translate(0 -72)\" x=\"96.791429\" y=\"-35.498357\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 96.791429 107.498357 \n", "L 96.791429 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 168.534286 107.498357 \n", "L 168.534286 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 96.791429 107.498357 \n", "L 168.534286 107.498357 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 96.791429 35.7555 \n", "L 168.534286 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_2\">\n", "    <!-- pullover -->\n", "    <g transform=\"translate(108.336607 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-70\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"126.855469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"182.421875\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"243.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.783203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"364.306641\"/>\n", "    </g>\n", "    <!-- pullover -->\n", "    <g transform=\"translate(108.336607 29.7555)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-70\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"126.855469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"182.421875\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"243.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.783203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"364.306641\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 182.882857 107.498357 \n", "L 254.625714 107.498357 \n", "L 254.625714 35.7555 \n", "L 182.882857 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pac627b8f50)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image5bce7b1f97\" transform=\"scale(1 -1)translate(0 -72)\" x=\"182.882857\" y=\"-35.498357\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 182.882857 107.498357 \n", "L 182.882857 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 254.625714 107.498357 \n", "L 254.625714 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 182.882857 107.498357 \n", "L 254.625714 107.498357 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 182.882857 35.7555 \n", "L 254.625714 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_3\">\n", "    <!-- trouser -->\n", "    <g transform=\"translate(197.312723 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "    <!-- trouser -->\n", "    <g transform=\"translate(197.312723 29.7555)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 268.974286 107.498357 \n", "L 340.717143 107.498357 \n", "L 340.717143 35.7555 \n", "L 268.974286 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p32bf0421e4)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagef781f29e82\" transform=\"scale(1 -1)translate(0 -72)\" x=\"268.974286\" y=\"-35.498357\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 268.974286 107.498357 \n", "L 268.974286 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 340.717143 107.498357 \n", "L 340.717143 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 268.974286 107.498357 \n", "L 340.717143 107.498357 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 268.974286 35.7555 \n", "L 340.717143 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- trouser -->\n", "    <g transform=\"translate(283.404152 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "    <!-- trouser -->\n", "    <g transform=\"translate(283.404152 29.7555)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 355.065714 107.498357 \n", "L 426.808571 107.498357 \n", "L 426.808571 35.7555 \n", "L 355.065714 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pcc7b54fdde)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image555f5e45db\" transform=\"scale(1 -1)translate(0 -72)\" x=\"355.065714\" y=\"-35.498357\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 355.065714 107.498357 \n", "L 355.065714 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 426.808571 107.498357 \n", "L 426.808571 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 355.065714 107.498357 \n", "L 426.808571 107.498357 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 355.065714 35.7555 \n", "L 426.808571 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- shirt -->\n", "    <g transform=\"translate(377.523393 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"143.261719\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"184.375\"/>\n", "    </g>\n", "    <!-- shirt -->\n", "    <g transform=\"translate(377.523393 29.7555)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"143.261719\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"184.375\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 441.157143 107.498357 \n", "L 512.9 107.498357 \n", "L 512.9 35.7555 \n", "L 441.157143 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pc4c8f3bfc9)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image481014d4e0\" transform=\"scale(1 -1)translate(0 -72)\" x=\"441.157143\" y=\"-35.498357\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 441.157143 107.498357 \n", "L 441.157143 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 512.9 107.498357 \n", "L 512.9 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 441.157143 107.498357 \n", "L 512.9 107.498357 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 441.157143 35.7555 \n", "L 512.9 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- trouser -->\n", "    <g transform=\"translate(455.587009 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "    <!-- trouser -->\n", "    <g transform=\"translate(455.587009 29.7555)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pffb999be85\">\n", "   <rect x=\"10.7\" y=\"35.7555\" width=\"71.742857\" height=\"71.742857\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p5fdd32ce50\">\n", "   <rect x=\"96.791429\" y=\"35.7555\" width=\"71.742857\" height=\"71.742857\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pac627b8f50\">\n", "   <rect x=\"182.882857\" y=\"35.7555\" width=\"71.742857\" height=\"71.742857\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p32bf0421e4\">\n", "   <rect x=\"268.974286\" y=\"35.7555\" width=\"71.742857\" height=\"71.742857\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pcc7b54fdde\">\n", "   <rect x=\"355.065714\" y=\"35.7555\" width=\"71.742857\" height=\"71.742857\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pc4c8f3bfc9\">\n", "   <rect x=\"441.157143\" y=\"35.7555\" width=\"71.742857\" height=\"71.742857\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 648x108 with 6 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["def predict_ch3(net, test_iter, n=6):  #@save\n", "    \"\"\"预测标签（定义见第3章）\"\"\"\n", "    for X, y in test_iter:\n", "        break\n", "    trues = d2l.get_fashion_mnist_labels(y)\n", "    preds = d2l.get_fashion_mnist_labels(net(X).argmax(axis=1))\n", "    titles = [true +'\\n' + pred for true, pred in zip(trues, preds)]\n", "    d2l.show_images(\n", "        X[0:n].reshape((n, 28, 28)), 1, n, titles=titles[0:n])\n", "\n", "predict_ch3(net, test_iter)"]}, {"cell_type": "markdown", "id": "fd6f7fb0", "metadata": {"origin_pos": 58}, "source": ["## 小结\n", "\n", "* 借助softmax回归，我们可以训练多分类的模型。\n", "* 训练softmax回归循环模型与训练线性回归模型非常相似：先读取数据，再定义模型和损失函数，然后使用优化算法训练模型。大多数常见的深度学习模型都有类似的训练过程。\n", "\n", "## 练习\n", "\n", "1. 本节直接实现了基于数学定义softmax运算的`softmax`函数。这可能会导致什么问题？提示：尝试计算$\\exp(50)$的大小。\n", "1. 本节中的函数`cross_entropy`是根据交叉熵损失函数的定义实现的。它可能有什么问题？提示：考虑对数的定义域。\n", "1. 请想一个解决方案来解决上述两个问题。\n", "1. 返回概率最大的分类标签总是最优解吗？例如，医疗诊断场景下可以这样做吗？\n", "1. 假设我们使用softmax回归来预测下一个单词，可选取的单词数目过多可能会带来哪些问题?\n"]}, {"cell_type": "markdown", "id": "ed8131a1", "metadata": {"origin_pos": 60, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1789)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
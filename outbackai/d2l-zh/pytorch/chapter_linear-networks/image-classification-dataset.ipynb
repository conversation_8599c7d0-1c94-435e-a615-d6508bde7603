{"cells": [{"cell_type": "markdown", "id": "8ae526de", "metadata": {"origin_pos": 0}, "source": ["# 图像分类数据集\n", ":label:`sec_fashion_mnist`\n", "\n", "(**MNIST数据集**) :cite:`LeCun.Bottou.Bengio.ea.1998`\n", "(**是图像分类中广泛使用的数据集之一，但作为基准数据集过于简单。\n", "我们将使用类似但更复杂的Fashion-MNIST数据集**) :cite:`<PERSON><PERSON>Rasul.Vollgraf.2017`。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "716c9e45", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:32.130767Z", "iopub.status.busy": "2023-08-18T07:00:32.129861Z", "iopub.status.idle": "2023-08-18T07:00:34.258162Z", "shell.execute_reply": "2023-08-18T07:00:34.257055Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "import torchvision\n", "from torch.utils import data\n", "from torchvision import transforms\n", "from d2l import torch as d2l\n", "\n", "d2l.use_svg_display()"]}, {"cell_type": "markdown", "id": "601c08d4", "metadata": {"origin_pos": 5}, "source": ["## 读取数据集\n", "\n", "我们可以[**通过框架中的内置函数将Fashion-MNIST数据集下载并读取到内存中**]。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "d8593555", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:34.264466Z", "iopub.status.busy": "2023-08-18T07:00:34.263710Z", "iopub.status.idle": "2023-08-18T07:00:34.378988Z", "shell.execute_reply": "2023-08-18T07:00:34.377831Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["# 通过ToTensor实例将图像数据从PIL类型变换成32位浮点数格式，\n", "# 并除以255使得所有像素的数值均在0～1之间\n", "trans = transforms.ToTensor()\n", "mnist_train = torchvision.datasets.FashionMNIST(\n", "    root=\"../data\", train=True, transform=trans, download=True)\n", "mnist_test = torchvision.datasets.FashionMNIST(\n", "    root=\"../data\", train=False, transform=trans, download=True)"]}, {"cell_type": "markdown", "id": "3d25caa7", "metadata": {"origin_pos": 10}, "source": ["Fashion-MNIST由10个类别的图像组成，\n", "每个类别由*训练数据集*（train dataset）中的6000张图像\n", "和*测试数据集*（test dataset）中的1000张图像组成。\n", "因此，训练集和测试集分别包含60000和10000张图像。\n", "测试数据集不会用于训练，只用于评估模型性能。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6db7fb8c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:34.384171Z", "iopub.status.busy": "2023-08-18T07:00:34.383782Z", "iopub.status.idle": "2023-08-18T07:00:34.391174Z", "shell.execute_reply": "2023-08-18T07:00:34.390176Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(60000, 10000)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["len(mnist_train), len(mnist_test)"]}, {"cell_type": "markdown", "id": "534d543c", "metadata": {"origin_pos": 13}, "source": ["每个输入图像的高度和宽度均为28像素。\n", "数据集由灰度图像组成，其通道数为1。\n", "为了简洁起见，本书将高度$h$像素、宽度$w$像素图像的形状记为$h \\times w$或（$h$,$w$）。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "3c69c2c8", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:34.396338Z", "iopub.status.busy": "2023-08-18T07:00:34.395813Z", "iopub.status.idle": "2023-08-18T07:00:34.403276Z", "shell.execute_reply": "2023-08-18T07:00:34.402307Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([1, 28, 28])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["mnist_train[0][0].shape"]}, {"cell_type": "markdown", "id": "4eb34556", "metadata": {"origin_pos": 15}, "source": ["[~~两个可视化数据集的函数~~]\n", "\n", "Fashion-MNIST中包含的10个类别，分别为t-shirt（T恤）、trouser（裤子）、pullover（套衫）、dress（连衣裙）、coat（外套）、sandal（凉鞋）、shirt（衬衫）、sneaker（运动鞋）、bag（包）和ankle boot（短靴）。\n", "以下函数用于在数字标签索引及其文本名称之间进行转换。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "fe9f8cfe", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:34.407798Z", "iopub.status.busy": "2023-08-18T07:00:34.407292Z", "iopub.status.idle": "2023-08-18T07:00:34.413948Z", "shell.execute_reply": "2023-08-18T07:00:34.412905Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_fashion_mnist_labels(labels):  #@save\n", "    \"\"\"返回Fashion-MNIST数据集的文本标签\"\"\"\n", "    text_labels = ['t-shirt', 'trouser', 'pullover', 'dress', 'coat',\n", "                   'sandal', 'shirt', 'sneaker', 'bag', 'ankle boot']\n", "    return [text_labels[int(i)] for i in labels]"]}, {"cell_type": "markdown", "id": "1af6b85c", "metadata": {"origin_pos": 17}, "source": ["我们现在可以创建一个函数来可视化这些样本。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "12d8707e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:34.421351Z", "iopub.status.busy": "2023-08-18T07:00:34.420405Z", "iopub.status.idle": "2023-08-18T07:00:34.429911Z", "shell.execute_reply": "2023-08-18T07:00:34.428770Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [], "source": ["def show_images(imgs, num_rows, num_cols, titles=None, scale=1.5):  #@save\n", "    \"\"\"绘制图像列表\"\"\"\n", "    figsize = (num_cols * scale, num_rows * scale)\n", "    _, axes = d2l.plt.subplots(num_rows, num_cols, figsize=figsize)\n", "    axes = axes.flatten()\n", "    for i, (ax, img) in enumerate(zip(axes, imgs)):\n", "        if torch.is_tensor(img):\n", "            # 图片张量\n", "            ax.imshow(img.numpy())\n", "        else:\n", "            # PIL图片\n", "            ax.imshow(img)\n", "        ax.axes.get_xaxis().set_visible(False)\n", "        ax.axes.get_yaxis().set_visible(False)\n", "        if titles:\n", "            ax.set_title(titles[i])\n", "    return axes"]}, {"cell_type": "markdown", "id": "aea8d92e", "metadata": {"origin_pos": 21}, "source": ["以下是训练数据集中前[**几个样本的图像及其相应的标签**]。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "e7d37edd", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:34.435295Z", "iopub.status.busy": "2023-08-18T07:00:34.434562Z", "iopub.status.idle": "2023-08-18T07:00:35.484726Z", "shell.execute_reply": "2023-08-18T07:00:35.483779Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"771.2pt\" height=\"193.03689pt\" viewBox=\"0 0 771.2 193.03689\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:00:35.357570</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 193.03689 \n", "L 771.2 193.03689 \n", "L 771.2 -0 \n", "L 0 -0 \n", "L 0 193.03689 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 10.7 93.384163 \n", "L 81.766038 93.384163 \n", "L 81.766038 22.318125 \n", "L 10.7 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p8c1b972a22)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image4118829713\" transform=\"scale(1 -1)translate(0 -72)\" x=\"10.7\" y=\"-21.384163\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 10.7 93.384163 \n", "L 10.7 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 81.766038 93.384163 \n", "L 81.766038 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 10.7 93.384163 \n", "L 81.766038 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 10.7 22.318125 \n", "L 81.766038 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_1\">\n", "    <!-- ankle boot -->\n", "    <g transform=\"translate(14.509894 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"61.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"182.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"210.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"271.875\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"303.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"367.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"428.320312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"489.501953\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 95.979245 93.384163 \n", "L 167.045283 93.384163 \n", "L 167.045283 22.318125 \n", "L 95.979245 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p80dd918746)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagef78f399944\" transform=\"scale(1 -1)translate(0 -72)\" x=\"95.979245\" y=\"-21.384163\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 95.979245 93.384163 \n", "L 95.979245 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 167.045283 93.384163 \n", "L 167.045283 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 95.979245 93.384163 \n", "L 167.045283 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 95.979245 22.318125 \n", "L 167.045283 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_2\">\n", "    <!-- t-shirt -->\n", "    <g transform=\"translate(113.581639 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"75.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"127.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"190.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"218.554688\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"259.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 181.258491 93.384163 \n", "L 252.324528 93.384163 \n", "L 252.324528 22.318125 \n", "L 181.258491 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p5b33e84a7a)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAALE0lEQVR4nNWcy47cxhWGv1NFNruney6akWRJkQNDcJBFnGSRVwjgF8h7ZplFkGWWWRlwoCyCOHAsWxrJc+npC1lVJ4virTk9o9EMaY8OQPSFZHXz5zl/nVtR/ih/Un4GkSzD7O0hoxTdn+FnGZpa3DQhWEHKf2WKQHqywqwccrFE352gzhFWawh+8P9pBv+FSkQ2NhEB0/rOCCrN4dV7lXiuto5DBLE2fh5YksFGFgExiBHMwT48OkStRTMbNSUx+J2oLX5i8KMIUEgENdQaJEFZP0gwHux6F7t8iAQlmeeYlcMsVoQf3qB5gXrfu1YNBxAgaRLv9OMjzn/9AJ8JxY7gswhEGIGaZhMFCZdGqbVJgkUCiIfRaUa6VMbHBZPlijC/gNUa/SgAEkGSNHLMOMPtjXFjwY8EP4KQRi1RW5lQ5/w2K0rzqi1C8Fn8MplZ9MEeZpSip2f4Mw8aQPuh1t4BkiRBkgRzsM/i95+yfJjgM8FNpL5ACYCCWRMBqMxJm31QvrbBa/GSnwhuCsUsYXl4hF0rB/86wL78plcS71+DxECawmTM4nHKxbNyRio3U0QTEY2v0OwT1Rog2aIAWmmREfy41KIxFLuCccLkOGOSZYgRWK17uZzeATJ7Mzh6gN/fIaQNEOKJAHmg4pmaW/SSWWlHs9oiqphcGg2TOH4YGTjcR1ZrZLlC1/dNg0Tg8IDli0Pcjol3uATIrhRRNqby2nwq05IOJ1XAhA5ICslKYRl5zI/iCcWOIX+2R3K2xvx4il/fXYv6N7HE4jPBp4IaacynukjTAukKDamlpUUbX7e5SpqZTw2E1KCpjf5SH5fTyyhtMSb6MpYGmEo7YJNf2o5h22XtgKJm86tKy6Q81hRajxlSISQGK/34wL0DpCKorZw9RYJsaEj7ff12m1m1ReK4oi2uqihIQVxzXkgFTcz91SDxHrsOgNl0+q6YmWreKY+5ctyuX7PFVwqJ4EcmknVPYUi/AKkiyzXZuwI3TVjvlsNX4LT8mspMbgLOVftq0i/H8FnUIJsbsPauVwMMwUE+IK7cBsoTdMetQ5EScK2C4R6kf4BCQHzA+DbxlCT8vlnrPXKliZrIT3YFo4vA6NShRXG7H+nIYADVzl91V0uApMWzepubvA1kA6pCsvJkPxYk5zkU7k6X0Rp6AAk03jJcDkav+u4Woi3nMs5oiriA3tdgVUNACh+1qPsfW9HBrbSn/TvdKL/0uczaIblDw6W8ya2kfw0K2vgrbZ+nRZp3BeeStGI3CQr+Hqc70FDzUPxM88fvOnRrSq+0U00rAyn0BkwlwyTMQnT9pUWolR+khusTZNdJ7UNJDUSVeCMMoJkMYWKqra330TdMdfP78k2gVy0aYJpXxPnSUYyxWJMQ62B2h+vQMtaqNVLBeMXkDinuM0lX+eC2Bl0RrN5ZtplqiBzYlwzjB7VUvE2sG3GXssFT75WanK8+wThFVjnkBer7AWnQsg9CvAUVSF1N0uawtl+zTep0BxGkDS4qgTaFoosl5EXU5B5kOA1qXcTdncIbDKBA0Lh5j/bgVsBAJB23yhw+AJ0rDu2aVZzmq3005hoA53oLM2AADdJSe67jiq1yQxy7plUn/KsbogF8f9XVYUn6jumNW0llWveag+ASSN1k/SVe6sRu7xMptbRbDlLVj2Ca7/EP/twyQMrVo85D4a5MjbYrE12pa1xX5JAuEzat6qpCUaDO3d9oXr1HihxxfiOjuK0I2AXhSt+oPoHNWlp3+ldFnYt9Qj3JQCZ2h7v3IV5BFdEP2Gg2SMJsG1FeKvNskY0sYct0th/MhqlWlVn1/p5H81CDs8FBcrNJ6lbacB2Qd5RhADItxRyqNlY2Q7TLSUPIsF2u2urq6OMipCFmCbEm30cq9zrpHyBTVjW7gWofJtDBQrocZAV66uqopHeARARGKZomhAQ0gXCTMvk2ALuB6JZ9CPhRrMv7zGLGGZKOemteGECDysYBY+qKQ20a7/vP181YVAk2bb2P42p9IwSsRWx/l9V/NO89LFdIXmz0HsqWJP6NM4pbXAQ1pWYKsQfSxZx07Un3JP170us1Ps+xkzHG0XSYdUQ6fsy1Y27Zr61Mpbgyo5gHQl702m0/zDSvMas3SJqjTczV2/ImiNfe0hyVDJuT5vIUf63mdEzuylitnLWqKd+uwDgwRf+rfwb2g7YUD98DwvvyQnWFVppGUZtrbDN2/adZhl8OVd9xqYt9bZBqsD6kBASNFg18BcOu9qmnYtAUqk57qerocvm4qwdjQ7PUxqkdD/Q3aV2Sn3BBHZem6q4WfSipD5nmqGQ4DSoXpkjsCG54ufKO4yF1M3jdNV+dTofDqxR3twWm5W333foCQ89iSuy2qMxjC2FXrxuNDdL5fM347daaIWRYDgqK8Uqg1eTUBmNLGnbj/Cv2KbS0puWMDlArGA6goJhcsWuQJBqMqMawoNIqqHlp6yqfbaIxDpWyiaRKexinSAi9K9KAdbHYClx7uWVcdmndRvXdTQh3mzm1Nehj46Bt3NDWlmoWMuVilHZA284EVEHpdb/z8ZlYKfGiZdOkTHt/NI9qSVO1Diwk2iz+rbxmqGfAaiyg1NRb9APcQAYHqCsSymWZlbQ1hiYhWINYOpfta/8p/J9KBgVIjRCsbFysXWtcTknjPbux4KZRS9w4Hp8slXQROSxZhLgmNRVc1qDTPJ3hBsm4W8qwAHVqW1Jqg8215hwVKdfURwCKGaVLICTLeIxdRxP0KvjUNEs9YQOYGzVafaAM+GgKg1ohlL9g8ghQSCCfGYqpsHwkhAyKqeKnHqwiE48YZXmRYM8tphBGJwnJMi4MHp0rtlC8hxBkYxL4uDjICCEVfCbYdTQXgGInPpJi/ik8+cMrns9OeJjNOUovSMWza1cYCRwXu7zJd3mX7/DV66ecnY9J/5tx9BXYQikmUrsPUnXVDiDDN3FC4z0L8SkMU3Azz/PZCS+mx3ySnvEoOSMVz9SsMQSO7JzD5ILj0Yw3qxmvRZlPR7ixNKnWdjp3oCfBDB5qSLmwLtgIztmvAqPnF/xy/5wvdr/jk/SUT9O3PEnOSQlMTcAAx37OoZ3zLJ2QPXK8PZjy99EL3vCQZCFMfhCykzi2GiEkitqPiYNCE1aIgtpIxPbpgi9f/JOH6ZzPx98zNWt+kZzxzCqpGCYyxophKgsyOaXgnCM750JHAPw1T5mfTRidjEmWAbVxZgtWNkvePckAz+4oH340Sss17EK+B24GbqIc7S1IxVOo5bviAWMpuAgZ39s5FiUVBwQuwj4LzfBquAgjCk0wojzdPefYes6PMhYXFuMVcWD7WYF5SQZ4+kuKjDNkUj4SZwIXn3m++O03PMrmHKQLMuOYu4xvlw8o1BBKRymokJdl2LF1TGyBkUAqASOBmV3z5Sdf885N+XP+O053ZqTnhp1XgoSPxcSMxMpm1cusoGngs+lbHo/OOXUTToodzlzG6+UuRbAU3lIEg6qQuwhQljp20oJEAlniSMTzaDznYTonFU+WOhbjQFgaKL1z8dp7NN9/4bBwBFYYe87ufw7ITkdAyl92f4NNPMW3U7J3BptDMi/TFK3MoC1fCwsnZfxVxWMv9+FvRwE1YFdCVgjjYzh8uSY9yzFvTgj3fkFd8OjaE7wn+fcr0skYNU8odncAeP4Px/Tla2S5Jhy//aAysX36hPyzR/iJZfE4Yb0nzF55xl//j/DjCS7vn4iG9YOClrnpJhck5XoyLWvoH1RHd658aIEpZ8foJGoIva3u6cr/AQVn0+6WApW4AAAAAElFTkSuQmCC\" id=\"imagebca4bf5314\" transform=\"scale(1 -1)translate(0 -72)\" x=\"181.258491\" y=\"-21.384163\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 181.258491 93.384163 \n", "L 181.258491 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 252.324528 93.384163 \n", "L 252.324528 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 181.258491 93.384163 \n", "L 252.324528 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 181.258491 22.318125 \n", "L 252.324528 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_3\">\n", "    <!-- t-shirt -->\n", "    <g transform=\"translate(198.860884 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"75.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"127.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"190.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"218.554688\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"259.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 266.537736 93.384163 \n", "L 337.603774 93.384163 \n", "L 337.603774 22.318125 \n", "L 266.537736 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pe87421ccaf)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image168b0968ce\" transform=\"scale(1 -1)translate(0 -72)\" x=\"266.537736\" y=\"-21.384163\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 266.537736 93.384163 \n", "L 266.537736 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 337.603774 93.384163 \n", "L 337.603774 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 266.537736 93.384163 \n", "L 337.603774 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 266.537736 22.318125 \n", "L 337.603774 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- dress -->\n", "    <g transform=\"translate(285.987005 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-64\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"102.339844\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"163.863281\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"215.962891\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 351.816981 93.384163 \n", "L 422.883019 93.384163 \n", "L 422.883019 22.318125 \n", "L 351.816981 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p5dfc51a379)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imaged4721eac71\" transform=\"scale(1 -1)translate(0 -72)\" x=\"351.816981\" y=\"-21.384163\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 351.816981 93.384163 \n", "L 351.816981 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 422.883019 93.384163 \n", "L 422.883019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 351.816981 93.384163 \n", "L 422.883019 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 351.816981 22.318125 \n", "L 422.883019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- t-shirt -->\n", "    <g transform=\"translate(369.419375 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"75.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"127.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"190.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"218.554688\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"259.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 437.096226 93.384163 \n", "L 508.162264 93.384163 \n", "L 508.162264 22.318125 \n", "L 437.096226 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p550252b789)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image926c27d68d\" transform=\"scale(1 -1)translate(0 -72)\" x=\"437.096226\" y=\"-21.384163\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 437.096226 93.384163 \n", "L 437.096226 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 508.162264 93.384163 \n", "L 508.162264 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 437.096226 93.384163 \n", "L 508.162264 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 437.096226 22.318125 \n", "L 508.162264 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- pullover -->\n", "    <g transform=\"translate(448.302995 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-70\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"126.855469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"182.421875\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"243.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.783203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"364.306641\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 522.375472 93.384163 \n", "L 593.441509 93.384163 \n", "L 593.441509 22.318125 \n", "L 522.375472 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pc5fbc5b905)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image7a7b590684\" transform=\"scale(1 -1)translate(0 -72)\" x=\"522.375472\" y=\"-21.384163\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 522.375472 93.384163 \n", "L 522.375472 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 593.441509 93.384163 \n", "L 593.441509 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 522.375472 93.384163 \n", "L 593.441509 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 522.375472 22.318125 \n", "L 593.441509 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- sneaker -->\n", "    <g transform=\"translate(534.196303 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"177.001953\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"238.28125\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"292.566406\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"354.089844\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 607.654717 93.384163 \n", "L 678.720755 93.384163 \n", "L 678.720755 22.318125 \n", "L 607.654717 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p80e578b6fe)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image1504fc217a\" transform=\"scale(1 -1)translate(0 -72)\" x=\"607.654717\" y=\"-21.384163\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 607.654717 93.384163 \n", "L 607.654717 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 678.720755 93.384163 \n", "L 678.720755 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 607.654717 93.384163 \n", "L 678.720755 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 607.654717 22.318125 \n", "L 678.720755 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_8\">\n", "    <!-- pullover -->\n", "    <g transform=\"translate(618.861486 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-70\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"126.855469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"182.421875\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"243.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.783203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"364.306641\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 692.933962 93.384163 \n", "L 764 93.384163 \n", "L 764 22.318125 \n", "L 692.933962 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p2e806e1ba2)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagea8ad9512f6\" transform=\"scale(1 -1)translate(0 -72)\" x=\"692.933962\" y=\"-21.384163\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 692.933962 93.384163 \n", "L 692.933962 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 764 93.384163 \n", "L 764 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_45\">\n", "    <path d=\"M 692.933962 93.384163 \n", "L 764 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_46\">\n", "    <path d=\"M 692.933962 22.318125 \n", "L 764 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_9\">\n", "    <!-- sandal -->\n", "    <g transform=\"translate(708.709169 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"113.378906\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"176.757812\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"240.234375\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"301.513672\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_10\">\n", "   <g id=\"patch_47\">\n", "    <path d=\"M 10.7 182.33689 \n", "L 81.766038 182.33689 \n", "L 81.766038 111.270852 \n", "L 10.7 111.270852 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p043e80b6ed)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image660d36e1ae\" transform=\"scale(1 -1)translate(0 -72)\" x=\"10.7\" y=\"-110.33689\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_48\">\n", "    <path d=\"M 10.7 182.33689 \n", "L 10.7 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_49\">\n", "    <path d=\"M 81.766038 182.33689 \n", "L 81.766038 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_50\">\n", "    <path d=\"M 10.7 182.33689 \n", "L 81.766038 182.33689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_51\">\n", "    <path d=\"M 10.7 111.270852 \n", "L 81.766038 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_10\">\n", "    <!-- sandal -->\n", "    <g transform=\"translate(26.475206 105.270852)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"113.378906\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"176.757812\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"240.234375\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"301.513672\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_11\">\n", "   <g id=\"patch_52\">\n", "    <path d=\"M 95.979245 182.33689 \n", "L 167.045283 182.33689 \n", "L 167.045283 111.270852 \n", "L 95.979245 111.270852 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd216bd310f)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagecaf4f86fdf\" transform=\"scale(1 -1)translate(0 -72)\" x=\"95.979245\" y=\"-110.33689\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_53\">\n", "    <path d=\"M 95.979245 182.33689 \n", "L 95.979245 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_54\">\n", "    <path d=\"M 167.045283 182.33689 \n", "L 167.045283 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_55\">\n", "    <path d=\"M 95.979245 182.33689 \n", "L 167.045283 182.33689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_56\">\n", "    <path d=\"M 95.979245 111.270852 \n", "L 167.045283 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <!-- t-shirt -->\n", "    <g transform=\"translate(113.581639 105.270852)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"75.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"127.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"190.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"218.554688\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"259.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_12\">\n", "   <g id=\"patch_57\">\n", "    <path d=\"M 181.**********.33689 \n", "L 252.**********.33689 \n", "L 252.**********.270852 \n", "L 181.**********.270852 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p6a129ad7a6)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageb15e59e311\" transform=\"scale(1 -1)translate(0 -72)\" x=\"181.258491\" y=\"-110.33689\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_58\">\n", "    <path d=\"M 181.**********.33689 \n", "L 181.**********.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_59\">\n", "    <path d=\"M 252.**********.33689 \n", "L 252.**********.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_60\">\n", "    <path d=\"M 181.**********.33689 \n", "L 252.**********.33689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_61\">\n", "    <path d=\"M 181.**********.270852 \n", "L 252.**********.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <!-- ankle boot -->\n", "    <g transform=\"translate(185.068384 105.270852)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"61.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"182.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"210.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"271.875\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"303.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"367.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"428.320312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"489.501953\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_13\">\n", "   <g id=\"patch_62\">\n", "    <path d=\"M 266.537736 182.33689 \n", "L 337.603774 182.33689 \n", "L 337.603774 111.270852 \n", "L 266.537736 111.270852 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p9fa3eb0c7e)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image6c55d3eee5\" transform=\"scale(1 -1)translate(0 -72)\" x=\"266.537736\" y=\"-110.33689\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_63\">\n", "    <path d=\"M 266.537736 182.33689 \n", "L 266.537736 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_64\">\n", "    <path d=\"M 337.603774 182.33689 \n", "L 337.603774 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_65\">\n", "    <path d=\"M 266.537736 182.33689 \n", "L 337.603774 182.33689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_66\">\n", "    <path d=\"M 266.537736 111.270852 \n", "L 337.603774 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_13\">\n", "    <!-- sandal -->\n", "    <g transform=\"translate(282.312942 105.270852)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"113.378906\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"176.757812\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"240.234375\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"301.513672\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_14\">\n", "   <g id=\"patch_67\">\n", "    <path d=\"M 351.816981 182.33689 \n", "L 422.883019 182.33689 \n", "L 422.883019 111.270852 \n", "L 351.816981 111.270852 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pe860c278d5)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageebfd656c1f\" transform=\"scale(1 -1)translate(0 -72)\" x=\"351.816981\" y=\"-110.33689\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_68\">\n", "    <path d=\"M 351.816981 182.33689 \n", "L 351.816981 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_69\">\n", "    <path d=\"M 422.883019 182.33689 \n", "L 422.883019 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_70\">\n", "    <path d=\"M 351.816981 182.33689 \n", "L 422.883019 182.33689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_71\">\n", "    <path d=\"M 351.816981 111.270852 \n", "L 422.883019 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <!-- sandal -->\n", "    <g transform=\"translate(367.592188 105.270852)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"113.378906\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"176.757812\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"240.234375\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"301.513672\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_15\">\n", "   <g id=\"patch_72\">\n", "    <path d=\"M 437.096226 182.33689 \n", "L 508.162264 182.33689 \n", "L 508.162264 111.270852 \n", "L 437.096226 111.270852 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#peb4ec5f0cb)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageb93ae38258\" transform=\"scale(1 -1)translate(0 -72)\" x=\"437.096226\" y=\"-110.33689\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_73\">\n", "    <path d=\"M 437.096226 182.33689 \n", "L 437.096226 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_74\">\n", "    <path d=\"M 508.162264 182.33689 \n", "L 508.162264 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_75\">\n", "    <path d=\"M 437.096226 182.33689 \n", "L 508.162264 182.33689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_76\">\n", "    <path d=\"M 437.096226 111.270852 \n", "L 508.162264 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_15\">\n", "    <!-- sneaker -->\n", "    <g transform=\"translate(448.917058 105.270852)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"177.001953\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"238.28125\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"292.566406\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"354.089844\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_16\">\n", "   <g id=\"patch_77\">\n", "    <path d=\"M 522.375472 182.33689 \n", "L 593.441509 182.33689 \n", "L 593.441509 111.270852 \n", "L 522.375472 111.270852 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p3992fad5db)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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********************************+PsKnCpCk+Suk/NDA+aaufjX2pAKIoyR6FzVfjUcKk7GC85u7RiHyRII9S+ncV8dRz850JfHgHby22LM+/N00yAN6/xdondaaqN5S293msS/OlAsiXFfHMksaQHmje27uMc0J52EEvFJ0DRffAkUwdajLHLBb/fwM5i8ufr0vzpQLInZzQ+98P6KUJw/d7lH8ZtiLrvEQZh1pWqJMFVAZ3PP6NzOlLBZAvCsyDh+3v8WN/dzzrP8R+fXbxT73n2AVA59gFQOfYBUDn2AVA59gFQOfYBUDn2AVA59gFQOfYBUDn2AVA59j/A090BpohiyPrAAAAAElFTkSuQmCC\" id=\"image04d519337b\" transform=\"scale(1 -1)translate(0 -72)\" x=\"522.375472\" y=\"-110.33689\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_78\">\n", "    <path d=\"M 522.375472 182.33689 \n", "L 522.375472 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_79\">\n", "    <path d=\"M 593.441509 182.33689 \n", "L 593.441509 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_80\">\n", "    <path d=\"M 522.375472 182.33689 \n", "L 593.441509 182.33689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_81\">\n", "    <path d=\"M 522.375472 111.270852 \n", "L 593.441509 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_16\">\n", "    <!-- ankle boot -->\n", "    <g transform=\"translate(526.185366 105.270852)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"61.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"182.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"210.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"271.875\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"303.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"367.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"428.320312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"489.501953\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_17\">\n", "   <g id=\"patch_82\">\n", "    <path d=\"M 607.654717 182.33689 \n", "L 678.720755 182.33689 \n", "L 678.720755 111.270852 \n", "L 607.654717 111.270852 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pb46dedb4dc)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAARQklEQVR4nNWcS48jyXHHfxGZWVUkm8N57Y60sl6ADcOQDwJ888En2zef7E/rT+C<PERSON><PERSON>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\" id=\"imaged6f739bf22\" transform=\"scale(1 -1)translate(0 -72)\" x=\"607.654717\" y=\"-110.33689\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_83\">\n", "    <path d=\"M 607.654717 182.33689 \n", "L 607.654717 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_84\">\n", "    <path d=\"M 678.720755 182.33689 \n", "L 678.720755 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_85\">\n", "    <path d=\"M 607.654717 182.33689 \n", "L 678.720755 182.33689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_86\">\n", "    <path d=\"M 607.654717 111.270852 \n", "L 678.720755 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_17\">\n", "    <!-- trouser -->\n", "    <g transform=\"translate(621.746173 105.270852)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_18\">\n", "   <g id=\"patch_87\">\n", "    <path d=\"M 692.933962 182.33689 \n", "L 764 182.33689 \n", "L 764 111.270852 \n", "L 692.933962 111.270852 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p518ff5b6e9)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAATv0lEQVR4nM2c348k13XfP/fc+tU9PT92drnLpShKIkNJkKME8oOBxELyYgN+cIA8+A+w/z0jrwECJA95EBwgQBIpjmwYlMwVKXI5nN351TPdXVX3nuOHW9Vd3TOznJ3tjXmA3pqtrq6q+63z83vPLfcn7i+MLUr27hP0ySE6ylk8qghjIVSOdgzmHZqBebAMYgEmoKWl/QKIYW7jpALmQFqQxiEB8qkjm4OLhjQgAUYnkeK0Ibts4Lefo9Pp9Rt0DuzuQ87ujcRNF3IOvMcywcQlIKT7eNdtB/tkNXgTwFm37c5n6W8T67au2w5+by6BKqCZS9f2DnGbKN9P7geQ+O4PBTNkPEbefYxOKk7+5QEnP3FoAXGkWK64KlLuNIgoZg4zh4hSZRHnjCKLZKKIM3IfEWeoOdQc4ozMKc4Zi5Bz1RREdSyanFkQYuvRqxwXHK51uFAy/qri/XmLPAtYG7C2Sbd7D9BeHyDncD4BZDH948Yj5v/iEYvDjK9+rvzlH/+C/WwGgMd4kp/xw/xrxBknccyFVnin5MR0jFM8hjilICJOac0TEQAq1+IxrqzgLI5RBEEB+Coc8P+uvsM8FhwWVxxmV/z1s58x//U+o+NTmM1WAA3GcFczu7+JrV2wMykBMuNJfs6Bn9GYRxF2pCZ3aUDidAlID04vagIuoibd9+k7jy2PH0o6j5K7SJCIR5H+fGZgeoMbkLT/jnJ/gMTRKQBoxNcR3whER0RYWM5JmDDTgqN2n0/kXdSEk7DDPOZkooylWQNIMLxLg8xdJHdxCYB3ylkc86LdpTWPdp78IlQ8n+/TRE+0d1BznDzf59HpAp1eQow33Pzd5Z4+6Lotu2BIMJw61IRowjRWXMYSNdcNSjhrRyxiRiGRnaxZPXHAO0M6UyslMPbNCjSM8zjiRT2hNSGoR3HMQsFFXdGqMG9y6jbDX3hk1qJ1fW9getmOianhoiKtdRrklv6jl+R4IZdIMCGTSC4RGZiOuOSoe60pXQBYaZVGMomgoE6SE8dwzvDOKPNA5pV5ZeD/OaPYpsSINBGpFRc8tebkLqKs36Q4JXNKIYFSIoUEcnfdBMQZY98w9usakLvIyLc0zmiXcT4djyjjvKHwkePxPpbJtfPeR7ZzFgAznIJ7RXAQZ52T7rZcd5YyOMFtzllu2Nf/tpDw6pt4TbmfBqmt+SEzw9XJUbtQEE1SRGIw4I3IoSa05pOPQZfHBU2/i9JtcdSaE02oNSOop+3Or+ZQUr5kXd6k5rieit9f7m9iOnhKqrgYcW3EGSjJD/XSD143bjyBqGvuYtN3AbTmB5/OQQ8AMXMElQ6k7RkFbMvEzCBEXKu4CLHXnltMIfZPengjG2bRgzwcsHYAKCvNGZ5HcQQTBinUG8tWnLTFiMxrRARpVyEdwKNEkjmkbfdxhuIQEji9U+9TgtiZIHQapCmzDibETmOC+TWQtNuPfhtMbChqEEJKym55cj1Im5oDCaCUMMrSxPQV5nL7fpe0d4v8xP0AMk0p++D/1ra4uuWGqD3wF7I0Axk47T7v6U1TLQHVa1C8ARAd+J3ePKMKUVM278y2gtP9Nch0GZlMDWtayBrcK2xfcUv/M4xy0mXQ3mlnYkJQWUayG8/VRTEA5wwBWhXa6HHh9TifV8l2TAxAFWIXxWw9gvXOWrC1gfX7+3Cd/n4VKO5WB6/d9yF2Pug2gF6jUIVtAWSaNEgkRTGEiEsa0TneUsLSZIJ6gqzfaDSh1VUYx68q/VozWvME8+m3nalC0p7cRcwcV1pQtxnScrMvNPv/yChuXtg0adGmBrEyof67zRIkIl1Y73zQDbVcLzeF916DzBwxCk6TD9qGbM3ETC0li919qUkCpdOi1vyytAgqy4y5l9hnxl0YX/uuTxMG+zOnIOlcdcw6B7298L68ztbOpBGz7GYN6soJdUl7osqG33G0+PVoN0wAbT236mu6DAgIdciWudG2ZXsAQaI9rPNBfaLYaZA4+8b8pDe1tX0DsIblxabYcN+rrvOapvf6AJlhevtFXIRpWzGWhomvV3TG4OEGS1nwMuRzfcDBPLNYACwddB/+b4p0zlmqxdR9SxJFuP40LDnpdlAmiFNykib0Uek2LdiUVfYtHZiyTDbXiTZb+SdzCe5vm5MGwAmaw5NyyuPiAt/lPQCVtKDrBWw/4OgMIQ72uzWfMvRVoTO5THQtj+q1R80h/+wa9AqJJfx49JyH2SVftftMtSJ3kcq1ybFKXHE/JogJGXHpo2JnckE90Q8r+VSo1jHrtC9QSAI1Wuf4VbDoUja/JQ3avtt3kLtA3vHJw8izyQ7GGyLWUHpT3Az7Qxk655SOua1RHfAWNAiDmZYUMTLTkpkW5C5SuAACpUSyjpxP4PRzX71/2kwBZMkiQkfgd9+FAfjOGTF4bOHxtYO4HZTup0Fmt6qwU1hYzsJyZlqw0JzW/HKOS5zih/7jhgh2jXlkpWk9GP1xSnLK4gxVh2sECXxLnXQnPeHV5zDRhIXmRATvrJuH16QBul6gDs0qmL927mvMY3ds1O3yQL1sHSCn0GrGQvIlj1xbxkzLJU9dSPJPTfQEJ9Q+W4b00EW2oIlFBJY+qKdGYD1pDNHTBJ/8z3Y5+7ejQUOT6DUoNSOsBrrihK77iiHnfJP0vwc6ov6tKA/wlpx0nySWEpZV+UyLZflRSKTppo51g1fu5bYMG1KB2qrvtkKIHlXBiaFZ32P0bZpZHYizlDVHHLmLjKVBcUtnDWn6ea2+uoUsi3Z9/9KsOvawjR3AnXmRKZaxNYC2nwcZzGNOrTneKZW0CJbqKV1NBw3N5CaWcFOGWiZd3RW7DLoX59KItjk1tl2ATJEWjuo9XrQTSml5lE2ppGUec+ZaIBg7WU3eZcF98dlPCqabWi9HQvdRc8scqlWhbrPknM0lXtpHfBnR3MBvZ2hb1yAXYRZyas3wWNIgp6ka176AtQ0Q7q49w31R16cmRQwR3eqotu6DJBrTtmIaKsRp110W18xolTV3RSaumwRcH1noAF03r1Umrb3mDPht56D1fHt9kAtwvqg4byoAdmWeZk1NVibkVj6oD+er6aB1573ioNNv+yq+bwY1c/iuPyjzSp4HLDPsWxvFFEIUGl0/9SaFCj2P0+9bZxPvyhktr9uBZNZFsy09+rdgYnDV5Fw2JbXm175vTZAu9+mnnNUctfr1QtWE0FlOPwcvOGSjxupNTBx4SXXeeW43m9hw3x1rte2bmBohpAQu3liIyi3aIssyA1bZ+GaDwo2DcJ0GiZKLYv4WgGA1ZX5HE9x+HtRdu798NMGjlD4sW4HbbpJwKNdmS7tmzs1ot9401e/vS46OhbxNOfr5u/7vO8jWATLXRZa+qEQQZ+z4mp0s9Rw2mi25HFj5n/XJQCWTxECmY1ZZ9fC4RLWmObGoibt2r5x6vp2quUneyAfJzg5uVIH3uCyDIqfZdeR5JPcxdc9zvRfxVQzhq0TNpbm1jd+7jiOyrrI3Z4S9ivzJ4069UkO5zRdpaUKMoHfrn743QK4smf/7n3D8r3PCjtE8Dsg4cHjwgh89eMFhMeNRdkHhIpHUXziPxdLPDCPZcvDfQK0qKfOOfckiiVsqujUf03lFU2cwijz78wpff4S0Dmkhu4KHf1dTHF3hz6aEL7+6E0j3ByjLOP04Z+fnx3ywd8pfPf0FH2Yn/EP7mF/NPqCSlj2/WDaK151Z3aY9NwG02dvY119DqlUkmSKQgsMsx48C3/nZc/bKBRd1xfm84vx8TLYo2XNQAu7oGNsaQM4h4zGuKuHhA2YfHdLseaYfRX4wueD98RkHMmNXlHf8BT8ovwagMc9x2OMyVmsE2FB7NhnCFWBCUFumA9pPDHL9t2tTzl25MSlqHpZXVL5lnKeVRhc/KAmjkupJzu7BT/DzFvnsa+Lx8ZsB5LzHPX1MPJxw/IcTRv/xiJ8ePudPywseZZc8yc/4MJvxyI/YlZoPs2ecqfA38w/5fXPIaRgzj/maY4bELPYAbEowSb0+/Y2KpqaHuCpOM6+owbzJl2WL5EpZBj7YOeX98pS8a1Y/Pxzztwfv8WI+4auzXb4+GpOfj/jgv+TImwKE99i4pD0oqQ8df/bkN/y73X9gYTmNeR76Syon5M4zpiCXSEtDa57LWC7BuQmM2zQIvlnTnDMwh7Ga/nFi3RqzSCUtY6m7T0M9yXhcXZL7yKfB00pJrPwrQ/mdAJKy5PxH+5z+0DP/fkspIa35wihcilYLUy51wcwiV2oca8lJmHARKuYxX/qYfoHcbWxhP+1/EyjSEf5qjnG3NXO0XbfIwuc0wZP5yFeLPWrN2MsWPMhmtOY5C2MuQ8HRdAJfjBidOvLL+SvHfjcNKnIuvueJ/+qS7x5MyV1kGkfs+jk73YzpwgANTM1xEke81B3O44irUC61Z23g3XLLBMp6/nMb49WXJt6xXKE4JO9TwZrhRXm52OGiqTgsZ7RV6ky7aCsWMePqsmJ85ChPDT+teZWrvhtAIVC9NOafj/kyeE7fGXOYXdJaRmMe0ZwTV5A7ZaY5V1aw0GJj4DeYVteV3w90tdVr1McauLfIcM6sDhmt89eSzUwUC0IxNcqpQdO+cuh3Akgvr3j83z7n0f+a8PW/ecAn7z/mUX7Z9QE5FOFZ+86qAEWZaUmtWVpm6ZTM3TzgZVngVgNI57n+XIP6wfcd6eZWGuhFyX2kjZ7zeYWq47IuOC1GFD7yZDxlN6uxuWf/tw3lizmcnr85QBYC4fPfw+cw+d4fMW3LwdqJ1GB5Eia05jvH2LCwbE0L8kGjwXD/kJseahLcUJ85Rc1fYySHsqwBoxBCtyBGhVHRLu/DBUdx3iBnl2jd3Hie1wLoNlk1fGdLwNBUoNaWTu03BtPnNENp1a9pUjr3eqYtzggqiNm1tRqtrpZoZqJEc3jfzfn7VaZ92abVj34myMUcm15izVsECFhOCLbmqTWjJltjC7PBYt6hrPkYYcVXd0iFQfDtifrUtJmWUDUdKNGEeZt4p6zjg3Jz5D6BkvtI1i01v2xK5iEnu3JwdkE8Of3GwvW1q3kXjBeXO/x29g4nYWc52E3GMBFcHWXRLaLrPzcBdv3G7JZQ3wO+ai/uI9q1eyVxRf0DO59XfD2dkC1I60ruUNW/tgZVx3PqXz3gvz/f5cOPv+I/PP2/axqUu7i8+Uzi2poM6NqDu5U+kKLVkNAfmtRSmwYPoPARiN10kUNF8J3jjssexo6v7q7pnTGrCy7+8YDiVDj8NGLf4HvuDZBc1YyfG9JkHD/dWTGAnfMdttN5Nha9pCPTppt9TRrhVzXWxiLf4fn7JLM7sLvmqo2mDuvA2AD0oEJ5IoyfG9XLcOfl4q8NkLu4Yv/TfaqzjOffn/DiexNyiUyymgl1t5xgxRb2XNBm5/ywwaHRjCamEJ77tBK61oxFl4HPQkFUIfeRyq/ylrU8i45yJSZuutPEMguMspbT2YjsJex/2lAeXaJteDsAhedHVGfnjPd2Of3hh3z2B4ccFld8v3rJxC84DTuchJ1XhuverHqivomeWSiofGA/XzDyDXWTMQsFbfRcNCVN8FR5YLeQlf/ZiJC9L8qcMh41ael51xf5xcUek88Cxf/4e3T4Po9tA4RG9OoKgPwSPps+YDHOeLe4YNLxP71pLZlEt2rpbfGA4nHkTlFnFF6AtKS77KrvkW9Z+IzMKY2m3CeXlYPOOud/k3MWZ0s24CJmLGLGxXTMg6uAzmavNdx7h3lrWh7/nwUv66ccvfcu4eeeP3rwDICJr/FO2fezZSN5v0DuNOwsG8QlT7XVg+yKQ59Kl5O4Q6sZH46OeegvAbjSlJgetfs8mz+kVc9OVjPyLZ4VYVZJS+4iv5s/5G9+/33mlyXF70r2fwNPLyLlJ19wN8PaBkChpfz7L3h6tM/5Hzzgs58e8PHumImv2c8SMO9kU3akTm9isJRQ9q+tyDs6opSWH5df8nF+zplm/HLxPlMd8ePyS35aXCw9lwL/u07cUqMZB9ksdfJ3Gpe7yI7UVK7lRTthdjImP85455fKzn/+JVbXrw3OGwEEYHWDu5ozOp5w/OtD/tPLP8R5QzIlyyLvHkw5KOc8KGd8pzpbPuFdv6CUlgM/o3ItBzKncmml4af1Y46aPdSEyn269maGheV8UJ6w0Hy5gujTq0f83ekT5k1OGz0xCvPzip3f5JSnRvV1/UYvOHmDJZlGPL/AXV5RnJ7z8WeHWJl3k2KOuFty+qOnnB46fv3UqD4+Z3+04I+f/CM/G/+OAz/ju9kZlYvsi2NXCj4xzy9efMQXp/s8e3jI0YO91KFPilLvFaf82/EnRBx/u/gun9UP+Z/PP0D+6wPGx8rhcUP+coZrz3GXc2hb9GqGhvvozpsCBGkJlEZi28DFxdpX/mCfvdFHzOcFmgmXD8e0u57n+3t8UE6ICLsyp3KBxgJTrXnWfo+j6YT5ecUX2T57xWLpjH2XXL6XnQLwIuxyVO9xOa1473lk/OWc7PMXhC++fKMhbYrb9kvelicuS/x776I7I8LDEbMnJaFyzB472l3S+8ocq/LbgZ85Jl8Y+cxox4524taOiSWEUfo7m4OvYXSsPPjVCe7iCj2/2MqL3YbyVrpcgeQUP/1dmhEBJk5w3vPwYB83rqBpsatZmsSLEes7403T6sUb3lEErDLzwYrreNObpmB9/v2eIL01gJbS35RFzDS9gCBErG2xpsFCN9O5cfOvuTj5rck/AdWr3jSQ4n5WAAAAAElFTkSuQmCC\" id=\"image97f633d7c0\" transform=\"scale(1 -1)translate(0 -72)\" x=\"692.933962\" y=\"-110.33689\" width=\"72\" height=\"72\"/>\n", "   </g>\n", "   <g id=\"patch_88\">\n", "    <path d=\"M 692.933962 182.33689 \n", "L 692.933962 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_89\">\n", "    <path d=\"M 764 182.33689 \n", "L 764 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_90\">\n", "    <path d=\"M 692.933962 182.33689 \n", "L 764 182.33689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_91\">\n", "    <path d=\"M 692.933962 111.270852 \n", "L 764 111.270852 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- t-shirt -->\n", "    <g transform=\"translate(710.536356 105.270852)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"75.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"127.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"190.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"218.554688\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"259.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p8c1b972a22\">\n", "   <rect x=\"10.7\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p80dd918746\">\n", "   <rect x=\"95.979245\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p5b33e84a7a\">\n", "   <rect x=\"181.258491\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pe87421ccaf\">\n", "   <rect x=\"266.537736\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p5dfc51a379\">\n", "   <rect x=\"351.816981\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p550252b789\">\n", "   <rect x=\"437.096226\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pc5fbc5b905\">\n", "   <rect x=\"522.375472\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p80e578b6fe\">\n", "   <rect x=\"607.654717\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p2e806e1ba2\">\n", "   <rect x=\"692.933962\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p043e80b6ed\">\n", "   <rect x=\"10.7\" y=\"111.270852\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pd216bd310f\">\n", "   <rect x=\"95.979245\" y=\"111.270852\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6a129ad7a6\">\n", "   <rect x=\"181.258491\" y=\"111.270852\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p9fa3eb0c7e\">\n", "   <rect x=\"266.537736\" y=\"111.270852\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pe860c278d5\">\n", "   <rect x=\"351.816981\" y=\"111.270852\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"peb4ec5f0cb\">\n", "   <rect x=\"437.096226\" y=\"111.270852\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p3992fad5db\">\n", "   <rect x=\"522.375472\" y=\"111.270852\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pb46dedb4dc\">\n", "   <rect x=\"607.654717\" y=\"111.270852\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p518ff5b6e9\">\n", "   <rect x=\"692.933962\" y=\"111.270852\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 972x216 with 18 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["X, y = next(iter(data.DataLoader(mnist_train, batch_size=18)))\n", "show_images(<PERSON><PERSON>reshape(18, 28, 28), 2, 9, titles=get_fashion_mnist_labels(y));"]}, {"cell_type": "markdown", "id": "8ffe4da3", "metadata": {"origin_pos": 26}, "source": ["## 读取小批量\n", "\n", "为了使我们在读取训练集和测试集时更容易，我们使用内置的数据迭代器，而不是从零开始创建。\n", "回顾一下，在每次迭代中，数据加载器每次都会[**读取一小批量数据，大小为`batch_size`**]。\n", "通过内置数据迭代器，我们可以随机打乱了所有样本，从而无偏见地读取小批量。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "dcf11f71", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:35.493448Z", "iopub.status.busy": "2023-08-18T07:00:35.492606Z", "iopub.status.idle": "2023-08-18T07:00:35.498328Z", "shell.execute_reply": "2023-08-18T07:00:35.497372Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [], "source": ["batch_size = 256\n", "\n", "def get_dataloader_workers():  #@save\n", "    \"\"\"使用4个进程来读取数据\"\"\"\n", "    return 4\n", "\n", "train_iter = data.DataLoader(mnist_train, batch_size, shuffle=True,\n", "                             num_workers=get_dataloader_workers())"]}, {"cell_type": "markdown", "id": "f878f635", "metadata": {"origin_pos": 31}, "source": ["我们看一下读取训练数据所需的时间。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "8dc12e48", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:35.502439Z", "iopub.status.busy": "2023-08-18T07:00:35.501591Z", "iopub.status.idle": "2023-08-18T07:00:38.879964Z", "shell.execute_reply": "2023-08-18T07:00:38.878822Z"}, "origin_pos": 32, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'3.37 sec'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["timer = d2l.Timer()\n", "for X, y in train_iter:\n", "    continue\n", "f'{timer.stop():.2f} sec'"]}, {"cell_type": "markdown", "id": "0bd9a185", "metadata": {"origin_pos": 33}, "source": ["## 整合所有组件\n", "\n", "现在我们[**定义`load_data_fashion_mnist`函数**]，用于获取和读取Fashion-MNIST数据集。\n", "这个函数返回训练集和验证集的数据迭代器。\n", "此外，这个函数还接受一个可选参数`resize`，用来将图像大小调整为另一种形状。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "423baf20", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:38.885979Z", "iopub.status.busy": "2023-08-18T07:00:38.885569Z", "iopub.status.idle": "2023-08-18T07:00:38.895158Z", "shell.execute_reply": "2023-08-18T07:00:38.894185Z"}, "origin_pos": 35, "tab": ["pytorch"]}, "outputs": [], "source": ["def load_data_fashion_mnist(batch_size, resize=None):  #@save\n", "    \"\"\"下载Fashion-MNIST数据集，然后将其加载到内存中\"\"\"\n", "    trans = [transforms.ToTensor()]\n", "    if resize:\n", "        trans.insert(0, transforms.Resize(resize))\n", "    trans = transforms.Compose(trans)\n", "    mnist_train = torchvision.datasets.FashionMNIST(\n", "        root=\"../data\", train=True, transform=trans, download=True)\n", "    mnist_test = torchvision.datasets.FashionMNIST(\n", "        root=\"../data\", train=False, transform=trans, download=True)\n", "    return (data.DataLoader(mnist_train, batch_size, shuffle=True,\n", "                            num_workers=get_dataloader_workers()),\n", "            data.DataLoader(mnist_test, batch_size, shuffle=False,\n", "                            num_workers=get_dataloader_workers()))"]}, {"cell_type": "markdown", "id": "79c2b84b", "metadata": {"origin_pos": 38}, "source": ["下面，我们通过指定`resize`参数来测试`load_data_fashion_mnist`函数的图像大小调整功能。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "0807e2a3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:38.902559Z", "iopub.status.busy": "2023-08-18T07:00:38.900441Z", "iopub.status.idle": "2023-08-18T07:00:39.372670Z", "shell.execute_reply": "2023-08-18T07:00:39.371373Z"}, "origin_pos": 39, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([32, 1, 64, 64]) torch.float32 torch.<PERSON><PERSON>([32]) torch.int64\n"]}], "source": ["train_iter, test_iter = load_data_fashion_mnist(32, resize=64)\n", "for X, y in train_iter:\n", "    print(X.shape, X.dtype, y.shape, y.dtype)\n", "    break"]}, {"cell_type": "markdown", "id": "f435b06f", "metadata": {"origin_pos": 40}, "source": ["我们现在已经准备好使用Fashion-MNIST数据集，便于下面的章节调用来评估各种分类算法。\n", "\n", "## 小结\n", "\n", "* Fashion-MNIST是一个服装分类数据集，由10个类别的图像组成。我们将在后续章节中使用此数据集来评估各种分类算法。\n", "* 我们将高度$h$像素，宽度$w$像素图像的形状记为$h \\times w$或（$h$,$w$）。\n", "* 数据迭代器是获得更高性能的关键组件。依靠实现良好的数据迭代器，利用高性能计算来避免减慢训练过程。\n", "\n", "## 练习\n", "\n", "1. 减少`batch_size`（如减少到1）是否会影响读取性能？\n", "1. 数据迭代器的性能非常重要。当前的实现足够快吗？探索各种选择来改进它。\n", "1. 查阅框架的在线API文档。还有哪些其他数据集可用？\n"]}, {"cell_type": "markdown", "id": "a83d0dc0", "metadata": {"origin_pos": 42, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1787)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
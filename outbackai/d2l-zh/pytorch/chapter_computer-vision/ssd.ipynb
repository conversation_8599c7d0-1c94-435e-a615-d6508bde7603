{"cells": [{"cell_type": "markdown", "id": "7a776eb6", "metadata": {"origin_pos": 0}, "source": ["# 单发多框检测（SSD）\n", ":label:`sec_ssd`\n", "\n", "在 :numref:`sec_bbox`— :numref:`sec_object-detection-dataset`中，我们分别介绍了边界框、锚框、多尺度目标检测和用于目标检测的数据集。\n", "现在我们已经准备好使用这样的背景知识来设计一个目标检测模型：单发多框检测（SSD） :cite:`Liu.Anguelov.Erhan.ea.2016`。\n", "该模型简单、快速且被广泛使用。尽管这只是其中一种目标检测模型，但本节中的一些设计原则和实现细节也适用于其他模型。\n", "\n", "## 模型\n", "\n", " :numref:`fig_ssd`描述了单发多框检测模型的设计。\n", "此模型主要由基础网络组成，其后是几个多尺度特征块。\n", "基本网络用于从输入图像中提取特征，因此它可以使用深度卷积神经网络。\n", "单发多框检测论文中选用了在分类层之前截断的VGG :cite:`<PERSON>.Anguelov.Erhan.ea.2016`，现在也常用ResNet替代。\n", "我们可以设计基础网络，使它输出的高和宽较大。\n", "这样一来，基于该特征图生成的锚框数量较多，可以用来检测尺寸较小的目标。\n", "接下来的每个多尺度特征块将上一层提供的特征图的高和宽缩小（如减半），并使特征图中每个单元在输入图像上的感受野变得更广阔。\n", "\n", "回想一下在 :numref:`sec_multiscale-object-detection`中，通过深度神经网络分层表示图像的多尺度目标检测的设计。\n", "由于接近 :numref:`fig_ssd`顶部的多尺度特征图较小，但具有较大的感受野，它们适合检测较少但较大的物体。\n", "简而言之，通过多尺度特征块，单发多框检测生成不同大小的锚框，并通过预测边界框的类别和偏移量来检测大小不同的目标，因此这是一个多尺度目标检测模型。\n", "\n", "![单发多框检测模型主要由一个基础网络块和若干多尺度特征块串联而成。](../img/ssd.svg)\n", ":label:`fig_ssd`\n", "\n", "在下面，我们将介绍 :numref:`fig_ssd`中不同块的实施细节。\n", "首先，我们将讨论如何实施类别和边界框预测。\n", "\n", "### [**类别预测层**]\n", "\n", "设目标类别的数量为$q$。这样一来，锚框有$q+1$个类别，其中0类是背景。\n", "在某个尺度下，设特征图的高和宽分别为$h$和$w$。\n", "如果以其中每个单元为中心生成$a$个锚框，那么我们需要对$hwa$个锚框进行分类。\n", "如果使用全连接层作为输出，很容易导致模型参数过多。\n", "回忆 :numref:`sec_nin`一节介绍的使用卷积层的通道来输出类别预测的方法，\n", "单发多框检测采用同样的方法来降低模型复杂度。\n", "\n", "具体来说，类别预测层使用一个保持输入高和宽的卷积层。\n", "这样一来，输出和输入在特征图宽和高上的空间坐标一一对应。\n", "考虑输出和输入同一空间坐标（$x$、$y$）：输出特征图上（$x$、$y$）坐标的通道里包含了以输入特征图（$x$、$y$）坐标为中心生成的所有锚框的类别预测。\n", "因此输出通道数为$a(q+1)$，其中索引为$i(q+1) + j$（$0 \\leq j \\leq q$）的通道代表了索引为$i$的锚框有关类别索引为$j$的预测。\n", "\n", "在下面，我们定义了这样一个类别预测层，通过参数`num_anchors`和`num_classes`分别指定了$a$和$q$。\n", "该图层使用填充为1的$3\\times3$的卷积层。此卷积层的输入和输出的宽度和高度保持不变。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "563696ed", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:10.621444Z", "iopub.status.busy": "2023-08-18T07:17:10.620782Z", "iopub.status.idle": "2023-08-18T07:17:13.555579Z", "shell.execute_reply": "2023-08-18T07:17:13.554045Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "import torchvision\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l\n", "\n", "\n", "def cls_predictor(num_inputs, num_anchors, num_classes):\n", "    return nn.Conv2d(num_inputs, num_anchors * (num_classes + 1),\n", "                     kernel_size=3, padding=1)"]}, {"cell_type": "markdown", "id": "1e1e0dfc", "metadata": {"origin_pos": 4}, "source": ["### (**边界框预测层**)\n", "\n", "边界框预测层的设计与类别预测层的设计类似。\n", "唯一不同的是，这里需要为每个锚框预测4个偏移量，而不是$q+1$个类别。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "0e7f1560", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:13.561361Z", "iopub.status.busy": "2023-08-18T07:17:13.560758Z", "iopub.status.idle": "2023-08-18T07:17:13.566605Z", "shell.execute_reply": "2023-08-18T07:17:13.565544Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [], "source": ["def bbox_predictor(num_inputs, num_anchors):\n", "    return nn.Conv2d(num_inputs, num_anchors * 4, kernel_size=3, padding=1)"]}, {"cell_type": "markdown", "id": "2292886e", "metadata": {"origin_pos": 8}, "source": ["### [**连结多尺度的预测**]\n", "\n", "正如我们所提到的，单发多框检测使用多尺度特征图来生成锚框并预测其类别和偏移量。\n", "在不同的尺度下，特征图的形状或以同一单元为中心的锚框的数量可能会有所不同。\n", "因此，不同尺度下预测输出的形状可能会有所不同。\n", "\n", "在以下示例中，我们为同一个小批量构建两个不同比例（`Y1`和`Y2`）的特征图，其中`Y2`的高度和宽度是`Y1`的一半。\n", "以类别预测为例，假设`Y1`和`Y2`的每个单元分别生成了$5$个和$3$个锚框。\n", "进一步假设目标类别的数量为$10$，对于特征图`Y1`和`Y2`，类别预测输出中的通道数分别为$5\\times(10+1)=55$和$3\\times(10+1)=33$，其中任一输出的形状是（批量大小，通道数，高度，宽度）。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "1978218f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:13.571902Z", "iopub.status.busy": "2023-08-18T07:17:13.571041Z", "iopub.status.idle": "2023-08-18T07:17:13.700224Z", "shell.execute_reply": "2023-08-18T07:17:13.699037Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(torch.<PERSON><PERSON>([2, 55, 20, 20]), torch.<PERSON><PERSON>([2, 33, 10, 10]))"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["def forward(x, block):\n", "    return block(x)\n", "\n", "Y1 = forward(torch.zeros((2, 8, 20, 20)), cls_predictor(8, 5, 10))\n", "Y2 = forward(torch.zeros((2, 16, 10, 10)), cls_predictor(16, 3, 10))\n", "Y1.shape, Y2.shape"]}, {"cell_type": "markdown", "id": "c9abaf3e", "metadata": {"origin_pos": 12}, "source": ["正如我们所看到的，除了批量大小这一维度外，其他三个维度都具有不同的尺寸。\n", "为了将这两个预测输出链接起来以提高计算效率，我们将把这些张量转换为更一致的格式。\n", "\n", "通道维包含中心相同的锚框的预测结果。我们首先将通道维移到最后一维。\n", "因为不同尺度下批量大小仍保持不变，我们可以将预测结果转成二维的（批量大小，高$\\times$宽$\\times$通道数）的格式，以方便之后在维度$1$上的连结。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "11d90c8d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:13.711602Z", "iopub.status.busy": "2023-08-18T07:17:13.706279Z", "iopub.status.idle": "2023-08-18T07:17:13.718777Z", "shell.execute_reply": "2023-08-18T07:17:13.717594Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["def flatten_pred(pred):\n", "    return torch.flatten(pred.permute(0, 2, 3, 1), start_dim=1)\n", "\n", "def concat_preds(preds):\n", "    return torch.cat([flatten_pred(p) for p in preds], dim=1)"]}, {"cell_type": "markdown", "id": "67254e16", "metadata": {"origin_pos": 16}, "source": ["这样一来，尽管`Y1`和`Y2`在通道数、高度和宽度方面具有不同的大小，我们仍然可以在同一个小批量的两个不同尺度上连接这两个预测输出。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "82a882b4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:13.724224Z", "iopub.status.busy": "2023-08-18T07:17:13.723406Z", "iopub.status.idle": "2023-08-18T07:17:13.731796Z", "shell.execute_reply": "2023-08-18T07:17:13.730728Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 25300])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["concat_preds([Y1, Y2]).shape"]}, {"cell_type": "markdown", "id": "ba3e5c35", "metadata": {"origin_pos": 18}, "source": ["### [**高和宽减半块**]\n", "\n", "为了在多个尺度下检测目标，我们在下面定义了高和宽减半块`down_sample_blk`，该模块将输入特征图的高度和宽度减半。\n", "事实上，该块应用了在 :numref:`subsec_vgg-blocks`中的VGG模块设计。\n", "更具体地说，每个高和宽减半块由两个填充为$1$的$3\\times3$的卷积层、以及步幅为$2$的$2\\times2$最大汇聚层组成。\n", "我们知道，填充为$1$的$3\\times3$卷积层不改变特征图的形状。但是，其后的$2\\times2$的最大汇聚层将输入特征图的高度和宽度减少了一半。\n", "对于此高和宽减半块的输入和输出特征图，因为$1\\times 2+(3-1)+(3-1)=6$，所以输出中的每个单元在输入上都有一个$6\\times6$的感受野。因此，高和宽减半块会扩大每个单元在其输出特征图中的感受野。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "e9fd4f8b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:13.736409Z", "iopub.status.busy": "2023-08-18T07:17:13.735546Z", "iopub.status.idle": "2023-08-18T07:17:13.743144Z", "shell.execute_reply": "2023-08-18T07:17:13.742156Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [], "source": ["def down_sample_blk(in_channels, out_channels):\n", "    blk = []\n", "    for _ in range(2):\n", "        blk.append(nn.Conv2d(in_channels, out_channels,\n", "                             kernel_size=3, padding=1))\n", "        blk.append(nn.BatchNorm2d(out_channels))\n", "        blk.append(nn.ReLU())\n", "        in_channels = out_channels\n", "    blk.append(nn.MaxPool2d(2))\n", "    return nn.Sequential(*blk)"]}, {"cell_type": "markdown", "id": "2ad13aeb", "metadata": {"origin_pos": 22}, "source": ["在以下示例中，我们构建的高和宽减半块会更改输入通道的数量，并将输入特征图的高度和宽度减半。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "ead207aa", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:13.747496Z", "iopub.status.busy": "2023-08-18T07:17:13.746637Z", "iopub.status.idle": "2023-08-18T07:17:13.760305Z", "shell.execute_reply": "2023-08-18T07:17:13.759092Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 10, 10, 10])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["forward(torch.zeros((2, 3, 20, 20)), down_sample_blk(3, 10)).shape"]}, {"cell_type": "markdown", "id": "c39b0407", "metadata": {"origin_pos": 26}, "source": ["### [**基本网络块**]\n", "\n", "基本网络块用于从输入图像中抽取特征。\n", "为了计算简洁，我们构造了一个小的基础网络，该网络串联3个高和宽减半块，并逐步将通道数翻倍。\n", "给定输入图像的形状为$256\\times256$，此基本网络块输出的特征图形状为$32 \\times 32$（$256/2^3=32$）。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "12d15b30", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:13.765884Z", "iopub.status.busy": "2023-08-18T07:17:13.764976Z", "iopub.status.idle": "2023-08-18T07:17:13.810888Z", "shell.execute_reply": "2023-08-18T07:17:13.809472Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 64, 32, 32])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["def base_net():\n", "    blk = []\n", "    num_filters = [3, 16, 32, 64]\n", "    for i in range(len(num_filters) - 1):\n", "        blk.append(down_sample_blk(num_filters[i], num_filters[i+1]))\n", "    return nn.Sequential(*blk)\n", "\n", "forward(torch.zeros((2, 3, 256, 256)), base_net()).shape"]}, {"cell_type": "markdown", "id": "f2e2905e", "metadata": {"origin_pos": 30}, "source": ["### 完整的模型\n", "\n", "[**完整的单发多框检测模型由五个模块组成**]。每个块生成的特征图既用于生成锚框，又用于预测这些锚框的类别和偏移量。在这五个模块中，第一个是基本网络块，第二个到第四个是高和宽减半块，最后一个模块使用全局最大池将高度和宽度都降到1。从技术上讲，第二到第五个区块都是 :numref:`fig_ssd`中的多尺度特征块。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "a1299bce", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:13.816373Z", "iopub.status.busy": "2023-08-18T07:17:13.815882Z", "iopub.status.idle": "2023-08-18T07:17:13.823049Z", "shell.execute_reply": "2023-08-18T07:17:13.821892Z"}, "origin_pos": 32, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_blk(i):\n", "    if i == 0:\n", "        blk = base_net()\n", "    elif i == 1:\n", "        blk = down_sample_blk(64, 128)\n", "    elif i == 4:\n", "        blk = nn.AdaptiveMaxPool2d((1,1))\n", "    else:\n", "        blk = down_sample_blk(128, 128)\n", "    return blk"]}, {"cell_type": "markdown", "id": "ffe48b79", "metadata": {"origin_pos": 34}, "source": ["现在我们[**为每个块定义前向传播**]。与图像分类任务不同，此处的输出包括：CNN特征图`Y`；在当前尺度下根据`Y`生成的锚框；预测的这些锚框的类别和偏移量（基于`Y`）。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "a32c85fe", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:13.828048Z", "iopub.status.busy": "2023-08-18T07:17:13.827370Z", "iopub.status.idle": "2023-08-18T07:17:13.834162Z", "shell.execute_reply": "2023-08-18T07:17:13.833029Z"}, "origin_pos": 36, "tab": ["pytorch"]}, "outputs": [], "source": ["def blk_forward(X, blk, size, ratio, cls_predictor, bbox_predictor):\n", "    Y = blk(X)\n", "    anchors = d2l.multibox_prior(Y, sizes=size, ratios=ratio)\n", "    cls_preds = cls_predictor(Y)\n", "    bbox_preds = bbox_predictor(Y)\n", "    return (Y, anchors, cls_preds, bbox_preds)"]}, {"cell_type": "markdown", "id": "e342b537", "metadata": {"origin_pos": 38}, "source": ["回想一下，在 :numref:`fig_ssd`中，一个较接近顶部的多尺度特征块是用于检测较大目标的，因此需要生成更大的锚框。\n", "在上面的前向传播中，在每个多尺度特征块上，我们通过调用的`multibox_prior`函数（见 :numref:`sec_anchor`）的`sizes`参数传递两个比例值的列表。\n", "在下面，0.2和1.05之间的区间被均匀分成五个部分，以确定五个模块的在不同尺度下的较小值：0.2、0.37、0.54、0.71和0.88。\n", "之后，他们较大的值由$\\sqrt{0.2 \\times 0.37} = 0.272$、$\\sqrt{0.37 \\times 0.54} = 0.447$等给出。\n", "\n", "[~~超参数~~]\n"]}, {"cell_type": "code", "execution_count": 11, "id": "c059209e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:13.839681Z", "iopub.status.busy": "2023-08-18T07:17:13.838916Z", "iopub.status.idle": "2023-08-18T07:17:13.845388Z", "shell.execute_reply": "2023-08-18T07:17:13.844279Z"}, "origin_pos": 39, "tab": ["pytorch"]}, "outputs": [], "source": ["sizes = [[0.2, 0.272], [0.37, 0.447], [0.54, 0.619], [0.71, 0.79],\n", "         [0.88, 0.961]]\n", "ratios = [[1, 2, 0.5]] * 5\n", "num_anchors = len(sizes[0]) + len(ratios[0]) - 1"]}, {"cell_type": "markdown", "id": "7eeb21d9", "metadata": {"origin_pos": 40}, "source": ["现在，我们就可以按如下方式[**定义完整的模型**]`TinySSD`了。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "c872fa1d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:13.850809Z", "iopub.status.busy": "2023-08-18T07:17:13.850043Z", "iopub.status.idle": "2023-08-18T07:17:13.862966Z", "shell.execute_reply": "2023-08-18T07:17:13.861799Z"}, "origin_pos": 42, "tab": ["pytorch"]}, "outputs": [], "source": ["class TinySSD(nn.Module):\n", "    def __init__(self, num_classes, **kwargs):\n", "        super(<PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.num_classes = num_classes\n", "        idx_to_in_channels = [64, 128, 128, 128, 128]\n", "        for i in range(5):\n", "            # 即赋值语句self.blk_i=get_blk(i)\n", "            setattr(self, f'blk_{i}', get_blk(i))\n", "            setattr(self, f'cls_{i}', cls_predictor(idx_to_in_channels[i],\n", "                                                    num_anchors, num_classes))\n", "            setattr(self, f'bbox_{i}', bbox_predictor(idx_to_in_channels[i],\n", "                                                      num_anchors))\n", "\n", "    def forward(self, X):\n", "        anchors, cls_preds, bbox_preds = [None] * 5, [None] * 5, [None] * 5\n", "        for i in range(5):\n", "            # getattr(self,'blk_%d'%i)即访问self.blk_i\n", "            X, anchors[i], cls_preds[i], bbox_preds[i] = blk_forward(\n", "                X, getattr(self, f'blk_{i}'), sizes[i], ratios[i],\n", "                getattr(self, f'cls_{i}'), getattr(self, f'bbox_{i}'))\n", "        anchors = torch.cat(anchors, dim=1)\n", "        cls_preds = concat_preds(cls_preds)\n", "        cls_preds = cls_preds.reshape(\n", "            cls_preds.shape[0], -1, self.num_classes + 1)\n", "        bbox_preds = concat_preds(bbox_preds)\n", "        return anchors, cls_preds, bbox_preds"]}, {"cell_type": "markdown", "id": "12faa05a", "metadata": {"origin_pos": 44}, "source": ["我们[**创建一个模型实例，然后使用它**]对一个$256 \\times 256$像素的小批量图像`X`(**执行前向传播**)。\n", "\n", "如本节前面部分所示，第一个模块输出特征图的形状为$32 \\times 32$。\n", "回想一下，第二到第四个模块为高和宽减半块，第五个模块为全局汇聚层。\n", "由于以特征图的每个单元为中心有$4$个锚框生成，因此在所有五个尺度下，每个图像总共生成$(32^2 + 16^2 + 8^2 + 4^2 + 1)\\times 4 = 5444$个锚框。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "4f690e1e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:13.868497Z", "iopub.status.busy": "2023-08-18T07:17:13.867704Z", "iopub.status.idle": "2023-08-18T07:17:14.352257Z", "shell.execute_reply": "2023-08-18T07:17:14.351363Z"}, "origin_pos": 46, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["output anchors: torch.<PERSON><PERSON>([1, 5444, 4])\n", "output class preds: <PERSON>.<PERSON><PERSON>([32, 5444, 2])\n", "output bbox preds: torch.<PERSON>ze([32, 21776])\n"]}], "source": ["net = TinySSD(num_classes=1)\n", "X = torch.zeros((32, 3, 256, 256))\n", "anchors, cls_preds, bbox_preds = net(X)\n", "\n", "print('output anchors:', anchors.shape)\n", "print('output class preds:', cls_preds.shape)\n", "print('output bbox preds:', bbox_preds.shape)"]}, {"cell_type": "markdown", "id": "88c255f7", "metadata": {"origin_pos": 48}, "source": ["## 训练模型\n", "\n", "现在，我们将描述如何训练用于目标检测的单发多框检测模型。\n", "\n", "### 读取数据集和初始化\n", "\n", "首先，让我们[**读取**] :numref:`sec_object-detection-dataset`中描述的(**香蕉检测数据集**)。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "929c68e5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:14.357283Z", "iopub.status.busy": "2023-08-18T07:17:14.356878Z", "iopub.status.idle": "2023-08-18T07:17:19.113866Z", "shell.execute_reply": "2023-08-18T07:17:19.112868Z"}, "origin_pos": 49, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["read 1000 training examples\n"]}, {"name": "stdout", "output_type": "stream", "text": ["read 100 validation examples\n"]}], "source": ["batch_size = 32\n", "train_iter, _ = d2l.load_data_bananas(batch_size)"]}, {"cell_type": "markdown", "id": "c3b335ed", "metadata": {"origin_pos": 50}, "source": ["香蕉检测数据集中，目标的类别数为1。\n", "定义好模型后，我们需要(**初始化其参数并定义优化算法**)。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "1b2d1579", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:19.117581Z", "iopub.status.busy": "2023-08-18T07:17:19.116990Z", "iopub.status.idle": "2023-08-18T07:17:19.138203Z", "shell.execute_reply": "2023-08-18T07:17:19.137289Z"}, "origin_pos": 52, "tab": ["pytorch"]}, "outputs": [], "source": ["device, net = d2l.try_gpu(), TinySSD(num_classes=1)\n", "trainer = torch.optim.SGD(net.parameters(), lr=0.2, weight_decay=5e-4)"]}, {"cell_type": "markdown", "id": "ff7b1d34", "metadata": {"origin_pos": 54}, "source": ["### [**定义损失函数和评价函数**]\n", "\n", "目标检测有两种类型的损失。\n", "第一种有关锚框类别的损失：我们可以简单地复用之前图像分类问题里一直使用的交叉熵损失函数来计算；\n", "第二种有关正类锚框偏移量的损失：预测偏移量是一个回归问题。\n", "但是，对于这个回归问题，我们在这里不使用 :numref:`subsec_normal_distribution_and_squared_loss`中描述的平方损失，而是使用$L_1$范数损失，即预测值和真实值之差的绝对值。\n", "掩码变量`bbox_masks`令负类锚框和填充锚框不参与损失的计算。\n", "最后，我们将锚框类别和偏移量的损失相加，以获得模型的最终损失函数。\n"]}, {"cell_type": "code", "execution_count": 16, "id": "37ad81e9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:19.142054Z", "iopub.status.busy": "2023-08-18T07:17:19.141508Z", "iopub.status.idle": "2023-08-18T07:17:19.147193Z", "shell.execute_reply": "2023-08-18T07:17:19.146422Z"}, "origin_pos": 56, "tab": ["pytorch"]}, "outputs": [], "source": ["cls_loss = nn.CrossEntropyLoss(reduction='none')\n", "bbox_loss = nn.L1Loss(reduction='none')\n", "\n", "def calc_loss(cls_preds, cls_labels, bbox_preds, bbox_labels, bbox_masks):\n", "    batch_size, num_classes = cls_preds.shape[0], cls_preds.shape[2]\n", "    cls = cls_loss(cls_preds.reshape(-1, num_classes),\n", "                   cls_labels.reshape(-1)).reshape(batch_size, -1).mean(dim=1)\n", "    bbox = bbox_loss(bbox_preds * bbox_masks,\n", "                     bbox_labels * bbox_masks).mean(dim=1)\n", "    return cls + bbox"]}, {"cell_type": "markdown", "id": "e0d8d1ce", "metadata": {"origin_pos": 58}, "source": ["我们可以沿用准确率评价分类结果。\n", "由于偏移量使用了$L_1$范数损失，我们使用*平均绝对误差*来评价边界框的预测结果。这些预测结果是从生成的锚框及其预测偏移量中获得的。\n"]}, {"cell_type": "code", "execution_count": 17, "id": "df7b0fae", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:19.151032Z", "iopub.status.busy": "2023-08-18T07:17:19.150178Z", "iopub.status.idle": "2023-08-18T07:17:19.156061Z", "shell.execute_reply": "2023-08-18T07:17:19.154909Z"}, "origin_pos": 60, "tab": ["pytorch"]}, "outputs": [], "source": ["def cls_eval(cls_preds, cls_labels):\n", "    # 由于类别预测结果放在最后一维，argmax需要指定最后一维。\n", "    return float((cls_preds.argmax(dim=-1).type(\n", "        cls_labels.dtype) == cls_labels).sum())\n", "\n", "def bbox_eval(bbox_preds, bbox_labels, bbox_masks):\n", "    return float((torch.abs((bbox_labels - bbox_preds) * bbox_masks)).sum())"]}, {"cell_type": "markdown", "id": "c7253cca", "metadata": {"origin_pos": 62}, "source": ["### [**训练模型**]\n", "\n", "在训练模型时，我们需要在模型的前向传播过程中生成多尺度锚框（`anchors`），并预测其类别（`cls_preds`）和偏移量（`bbox_preds`）。\n", "然后，我们根据标签信息`Y`为生成的锚框标记类别（`cls_labels`）和偏移量（`bbox_labels`）。\n", "最后，我们根据类别和偏移量的预测和标注值计算损失函数。为了代码简洁，这里没有评价测试数据集。\n"]}, {"cell_type": "code", "execution_count": 18, "id": "6e08a9c2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:17:19.159726Z", "iopub.status.busy": "2023-08-18T07:17:19.159190Z", "iopub.status.idle": "2023-08-18T07:18:50.670936Z", "shell.execute_reply": "2023-08-18T07:18:50.670107Z"}, "origin_pos": 64, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["class err 3.22e-03, bbox mae 3.16e-03\n", "3353.9 examples/sec on cuda:0\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"251.690625pt\" height=\"180.65625pt\" viewBox=\"0 0 251.**********.65625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:18:50.635153</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 180.65625 \n", "L 251.**********.65625 \n", "L 251.690625 0 \n", "L 0 0 \n", "L 0 180.65625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.1 \n", "L 238.**********.1 \n", "L 238.128125 7.2 \n", "L 42.828125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 83.943914 143.1 \n", "L 83.943914 7.2 \n", "\" clip-path=\"url(#p307f949ba6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m7b4abc58ca\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7b4abc58ca\" x=\"83.943914\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(80.762664 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 135.338651 143.1 \n", "L 135.338651 7.2 \n", "\" clip-path=\"url(#p307f949ba6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m7b4abc58ca\" x=\"135.338651\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(128.976151 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 186.733388 143.1 \n", "L 186.733388 7.2 \n", "\" clip-path=\"url(#p307f949ba6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m7b4abc58ca\" x=\"186.733388\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(180.370888 157.698438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 238.**********.1 \n", "L 238.128125 7.2 \n", "\" clip-path=\"url(#p307f949ba6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m7b4abc58ca\" x=\"238.128125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(231.765625 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(125.25 171.376563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 42.828125 123.596186 \n", "L 238.128125 123.596186 \n", "\" clip-path=\"url(#p307f949ba6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m9cc68dd8fb\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9cc68dd8fb\" x=\"42.828125\" y=\"123.596186\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.005 -->\n", "      <g transform=\"translate(7.2 127.395405)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 42.828125 87.408711 \n", "L 238.128125 87.408711 \n", "\" clip-path=\"url(#p307f949ba6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m9cc68dd8fb\" x=\"42.828125\" y=\"87.408711\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.010 -->\n", "      <g transform=\"translate(7.2 91.207929)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 42.828125 51.221236 \n", "L 238.128125 51.221236 \n", "\" clip-path=\"url(#p307f949ba6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m9cc68dd8fb\" x=\"42.828125\" y=\"51.221236\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.015 -->\n", "      <g transform=\"translate(7.2 55.020454)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 42.828125 15.03376 \n", "L 238.128125 15.03376 \n", "\" clip-path=\"url(#p307f949ba6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m9cc68dd8fb\" x=\"42.828125\" y=\"15.03376\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.020 -->\n", "      <g transform=\"translate(7.2 18.832979)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 42.828125 13.377273 \n", "L 53.107072 124.526797 \n", "L 63.38602 125.303192 \n", "L 73.664967 127.397067 \n", "L 83.943914 128.880727 \n", "L 94.222862 130.175606 \n", "L 104.501809 131.88793 \n", "L 114.780757 131.938449 \n", "L 125.059704 133.327718 \n", "L 135.338651 133.580313 \n", "L 145.617599 134.037642 \n", "L 155.896546 134.699705 \n", "L 166.175493 134.919063 \n", "L 176.454441 135.373733 \n", "L 186.733388 135.719389 \n", "L 197.012336 135.365757 \n", "L 207.291283 135.953371 \n", "L 217.57023 136.108916 \n", "L 227.849178 136.328274 \n", "L 238.128125 136.48249 \n", "\" clip-path=\"url(#p307f949ba6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 42.828125 121.673646 \n", "L 53.107072 123.823698 \n", "L 63.38602 125.524467 \n", "L 73.664967 127.361631 \n", "L 83.943914 128.856985 \n", "L 94.222862 129.939357 \n", "L 104.501809 130.639479 \n", "L 114.780757 131.314173 \n", "L 125.059704 132.009605 \n", "L 135.338651 132.630218 \n", "L 145.617599 133.211109 \n", "L 155.896546 133.745295 \n", "L 166.175493 134.28492 \n", "L 176.454441 134.827081 \n", "L 186.733388 135.214977 \n", "L 197.012336 135.633916 \n", "L 207.291283 136.003686 \n", "L 217.57023 136.304619 \n", "L 227.849178 136.636756 \n", "L 238.128125 136.922727 \n", "\" clip-path=\"url(#p307f949ba6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.1 \n", "L 42.828125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 238.**********.1 \n", "L 238.128125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.1 \n", "L 238.**********.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.828125 7.2 \n", "L 238.128125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 146.921875 44.55625 \n", "L 231.128125 44.55625 \n", "Q 233.128125 44.55625 233.128125 42.55625 \n", "L 233.128125 14.2 \n", "Q 233.128125 12.2 231.128125 12.2 \n", "L 146.921875 12.2 \n", "Q 144.921875 12.2 144.921875 14.2 \n", "L 144.921875 42.55625 \n", "Q 144.921875 44.55625 146.921875 44.55625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_19\">\n", "     <path d=\"M 148.921875 20.298437 \n", "L 158.921875 20.298437 \n", "L 168.921875 20.298437 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- class error -->\n", "     <g transform=\"translate(176.921875 23.798437)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-63\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"54.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"82.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"144.042969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"196.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"248.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"280.029297\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"341.552734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"380.916016\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"419.779297\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"480.960938\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_20\">\n", "     <path d=\"M 148.921875 34.976562 \n", "L 158.921875 34.976562 \n", "L 168.921875 34.976562 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- bbox mae -->\n", "     <g transform=\"translate(176.921875 38.476562)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-62\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"126.953125\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"185.009766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"244.189453\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"275.976562\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"373.388672\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"434.667969\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p307f949ba6\">\n", "   <rect x=\"42.828125\" y=\"7.2\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["num_epochs, timer = 20, d2l.Timer()\n", "animator = d2l.Animator(xlabel='epoch', xlim=[1, num_epochs],\n", "                        legend=['class error', 'bbox mae'])\n", "net = net.to(device)\n", "for epoch in range(num_epochs):\n", "    # 训练精确度的和，训练精确度的和中的示例数\n", "    # 绝对误差的和，绝对误差的和中的示例数\n", "    metric = d2l.Accumulator(4)\n", "    net.train()\n", "    for features, target in train_iter:\n", "        timer.start()\n", "        trainer.zero_grad()\n", "        X, Y = features.to(device), target.to(device)\n", "        # 生成多尺度的锚框，为每个锚框预测类别和偏移量\n", "        anchors, cls_preds, bbox_preds = net(X)\n", "        # 为每个锚框标注类别和偏移量\n", "        bbox_labels, bbox_masks, cls_labels = d2l.multibox_target(anchors, Y)\n", "        # 根据类别和偏移量的预测和标注值计算损失函数\n", "        l = calc_loss(cls_preds, cls_labels, bbox_preds, bbox_labels,\n", "                      bbox_masks)\n", "        l.mean().backward()\n", "        trainer.step()\n", "        metric.add(cls_eval(cls_preds, cls_labels), cls_labels.numel(),\n", "                   bbox_eval(bbox_preds, bbox_labels, bbox_masks),\n", "                   bbox_labels.numel())\n", "    cls_err, bbox_mae = 1 - metric[0] / metric[1], metric[2] / metric[3]\n", "    animator.add(epoch + 1, (cls_err, bbox_mae))\n", "print(f'class err {cls_err:.2e}, bbox mae {bbox_mae:.2e}')\n", "print(f'{len(train_iter.dataset) / timer.stop():.1f} examples/sec on '\n", "      f'{str(device)}')"]}, {"cell_type": "markdown", "id": "9e6b06a4", "metadata": {"origin_pos": 66}, "source": ["## [**预测目标**]\n", "\n", "在预测阶段，我们希望能把图像里面所有我们感兴趣的目标检测出来。在下面，我们读取并调整测试图像的大小，然后将其转成卷积层需要的四维格式。\n"]}, {"cell_type": "code", "execution_count": 19, "id": "290676d2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:50.674365Z", "iopub.status.busy": "2023-08-18T07:18:50.674082Z", "iopub.status.idle": "2023-08-18T07:18:50.679714Z", "shell.execute_reply": "2023-08-18T07:18:50.678773Z"}, "origin_pos": 68, "tab": ["pytorch"]}, "outputs": [], "source": ["X = torchvision.io.read_image('../img/banana.jpg').unsqueeze(0).float()\n", "img = X.squeeze(0).permute(1, 2, 0).long()"]}, {"cell_type": "markdown", "id": "fb32477b", "metadata": {"origin_pos": 70}, "source": ["使用下面的`multibox_detection`函数，我们可以根据锚框及其预测偏移量得到预测边界框。然后，通过非极大值抑制来移除相似的预测边界框。\n"]}, {"cell_type": "code", "execution_count": 20, "id": "1da1d7a3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:50.683817Z", "iopub.status.busy": "2023-08-18T07:18:50.683541Z", "iopub.status.idle": "2023-08-18T07:18:51.223526Z", "shell.execute_reply": "2023-08-18T07:18:51.222573Z"}, "origin_pos": 72, "tab": ["pytorch"]}, "outputs": [], "source": ["def predict(X):\n", "    net.eval()\n", "    anchors, cls_preds, bbox_preds = net(X.to(device))\n", "    cls_probs = F.softmax(cls_preds, dim=2).permute(0, 2, 1)\n", "    output = d2l.multibox_detection(cls_probs, bbox_preds, anchors)\n", "    idx = [i for i, row in enumerate(output[0]) if row[0] != -1]\n", "    return output[0, idx]\n", "\n", "output = predict(X)"]}, {"cell_type": "markdown", "id": "8f3c20db", "metadata": {"origin_pos": 74}, "source": ["最后，我们[**筛选所有置信度不低于0.9的边界框，做为最终输出**]。\n"]}, {"cell_type": "code", "execution_count": 21, "id": "ddfce357", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:51.227738Z", "iopub.status.busy": "2023-08-18T07:18:51.227134Z", "iopub.status.idle": "2023-08-18T07:18:51.460658Z", "shell.execute_reply": "2023-08-18T07:18:51.459830Z"}, "origin_pos": 76, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"315.991797pt\" height=\"306.146484pt\" viewBox=\"0 0 315.**********.146484\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:18:51.364050</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 306.146484 \n", "L 315.**********.146484 \n", "L 315.991797 0 \n", "L 0 0 \n", "L 0 306.146484 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 282.268359 \n", "L 305.0875 282.268359 \n", "L 305.0875 10.468359 \n", "L 33.2875 10.468359 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pc6944274c5)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image06a644cd75\" transform=\"scale(1 -1)translate(0 -272)\" x=\"33.2875\" y=\"-10.268359\" width=\"272\" height=\"272\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 160.219422 172.415802 \n", "L 214.864638 172.415802 \n", "L 214.864638 224.468307 \n", "L 160.219422 224.468307 \n", "L 160.219422 172.415802 \n", "z\n", "\" clip-path=\"url(#pc6944274c5)\" style=\"fill: none; stroke: #ffffff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 180.773901 30.125998 \n", "L 239.633582 30.125998 \n", "L 239.633582 85.817957 \n", "L 180.773901 85.817957 \n", "L 180.773901 30.125998 \n", "z\n", "\" clip-path=\"url(#pc6944274c5)\" style=\"fill: none; stroke: #ffffff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 54.717326 216.899964 \n", "L 113.176684 216.899964 \n", "L 113.176684 269.259146 \n", "L 54.717326 269.259146 \n", "L 54.717326 216.899964 \n", "z\n", "\" clip-path=\"url(#pc6944274c5)\" style=\"fill: none; stroke: #ffffff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 225.949891 112.551695 \n", "L 279.39911 112.551695 \n", "L 279.39911 163.760727 \n", "L 225.949891 163.760727 \n", "L 225.949891 112.551695 \n", "z\n", "\" clip-path=\"url(#pc6944274c5)\" style=\"fill: none; stroke: #ffffff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m2a197b1374\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m2a197b1374\" x=\"33.818359\" y=\"282.268359\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(30.637109 296.866797)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m2a197b1374\" x=\"86.904297\" y=\"282.268359\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(80.541797 296.866797)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m2a197b1374\" x=\"139.990234\" y=\"282.268359\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(130.446484 296.866797)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m2a197b1374\" x=\"193.076172\" y=\"282.268359\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 150 -->\n", "      <g transform=\"translate(183.532422 296.866797)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m2a197b1374\" x=\"246.162109\" y=\"282.268359\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(236.618359 296.866797)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m2a197b1374\" x=\"299.248047\" y=\"282.268359\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 250 -->\n", "      <g transform=\"translate(289.704297 296.866797)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m546d088762\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m546d088762\" x=\"33.2875\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 14.798438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m546d088762\" x=\"33.2875\" y=\"64.085156\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(13.5625 67.884375)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m546d088762\" x=\"33.2875\" y=\"117.171094\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 120.970313)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m546d088762\" x=\"33.2875\" y=\"170.257031\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 150 -->\n", "      <g transform=\"translate(7.2 174.05625)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m546d088762\" x=\"33.2875\" y=\"223.342969\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(7.2 227.142188)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m546d088762\" x=\"33.2875\" y=\"276.428906\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 250 -->\n", "      <g transform=\"translate(7.2 280.228125)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 33.2875 282.268359 \n", "L 33.2875 10.468359 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 305.0875 282.268359 \n", "L 305.0875 10.468359 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 33.2875 282.268359 \n", "L 305.0875 282.268359 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 33.2875 10.468359 \n", "L 305.0875 10.468359 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_13\">\n", "    <g id=\"patch_11\">\n", "     <path d=\"M 146.239891 180.730958 \n", "L 174.198954 180.730958 \n", "L 174.198954 164.100646 \n", "L 146.239891 164.100646 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "    </g>\n", "    <!-- 1.00 -->\n", "    <g transform=\"translate(150.199891 174.89924)scale(0.09 -0.09)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-31\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <g id=\"patch_12\">\n", "     <path d=\"M 166.79437 38.441155 \n", "L 194.753432 38.441155 \n", "L 194.753432 21.810842 \n", "L 166.79437 21.810842 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "    </g>\n", "    <!-- 1.00 -->\n", "    <g transform=\"translate(170.75437 32.609436)scale(0.09 -0.09)\">\n", "     <use xlink:href=\"#DejaVuSans-31\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_15\">\n", "    <g id=\"patch_13\">\n", "     <path d=\"M 40.737795 225.215121 \n", "L 68.696858 225.215121 \n", "L 68.696858 208.584808 \n", "L 40.737795 208.584808 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "    </g>\n", "    <!-- 1.00 -->\n", "    <g transform=\"translate(44.697795 219.383402)scale(0.09 -0.09)\">\n", "     <use xlink:href=\"#DejaVuSans-31\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_16\">\n", "    <g id=\"patch_14\">\n", "     <path d=\"M 211.97036 120.866852 \n", "L 239.929423 120.866852 \n", "L 239.929423 104.236539 \n", "L 211.97036 104.236539 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "    </g>\n", "    <!-- 0.98 -->\n", "    <g transform=\"translate(215.93036 115.035133)scale(0.09 -0.09)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-30\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"95.410156\"/>\n", "     <use xlink:href=\"#DejaVuSans-38\" x=\"159.033203\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pc6944274c5\">\n", "   <rect x=\"33.2875\" y=\"10.468359\" width=\"271.8\" height=\"271.8\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["def display(img, output, threshold):\n", "    d2l.set_figsize((5, 5))\n", "    fig = d2l.plt.imshow(img)\n", "    for row in output:\n", "        score = float(row[1])\n", "        if score < threshold:\n", "            continue\n", "        h, w = img.shape[0:2]\n", "        bbox = [row[2:6] * torch.tensor((w, h, w, h), device=row.device)]\n", "        d2l.show_bboxes(fig.axes, bbox, '%.2f' % score, 'w')\n", "\n", "display(img, output.cpu(), threshold=0.9)"]}, {"cell_type": "markdown", "id": "133fb05f", "metadata": {"origin_pos": 78}, "source": ["## 小结\n", "\n", "* 单发多框检测是一种多尺度目标检测模型。基于基础网络块和各个多尺度特征块，单发多框检测生成不同数量和不同大小的锚框，并通过预测这些锚框的类别和偏移量检测不同大小的目标。\n", "* 在训练单发多框检测模型时，损失函数是根据锚框的类别和偏移量的预测及标注值计算得出的。\n", "\n", "## 练习\n", "\n", "1. 能通过改进损失函数来改进单发多框检测吗？例如，将预测偏移量用到的$L_1$范数损失替换为平滑$L_1$范数损失。它在零点附近使用平方函数从而更加平滑，这是通过一个超参数$\\sigma$来控制平滑区域的：\n", "\n", "$$\n", "f(x) =\n", "    \\begin{cases}\n", "    (\\sigma x)^2/2,& \\text{if }|x| < 1/\\sigma^2\\\\\n", "    |x|-0.5/\\sigma^2,& \\text{otherwise}\n", "    \\end{cases}\n", "$$\n", "\n", "当$\\sigma$非常大时，这种损失类似于$L_1$范数损失。当它的值较小时，损失函数较平滑。\n"]}, {"cell_type": "code", "execution_count": 22, "id": "5ff25a9d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:51.466346Z", "iopub.status.busy": "2023-08-18T07:18:51.465756Z", "iopub.status.idle": "2023-08-18T07:18:51.636211Z", "shell.execute_reply": "2023-08-18T07:18:51.635398Z"}, "origin_pos": 80, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"232.603125pt\" height=\"166.978125pt\" viewBox=\"0 0 232.**********.978125\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:18:51.596999</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 166.978125 \n", "L 232.**********.978125 \n", "L 232.603125 0 \n", "L 0 0 \n", "L 0 166.978125 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"ma96cf310d5\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma96cf310d5\" x=\"38.980398\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(31.609304 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#ma96cf310d5\" x=\"84.504874\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(77.13378 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#ma96cf310d5\" x=\"130.029349\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(126.848099 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#ma96cf310d5\" x=\"175.553825\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(172.372575 157.698438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#ma96cf310d5\" x=\"221.078301\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(217.897051 157.698438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m6b7701f609\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m6b7701f609\" x=\"30.103125\" y=\"136.922727\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 140.721946)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m6b7701f609\" x=\"30.103125\" y=\"105.958954\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 109.758173)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m6b7701f609\" x=\"30.103125\" y=\"74.995181\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 78.7944)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m6b7701f609\" x=\"30.103125\" y=\"44.031408\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 47.830627)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m6b7701f609\" x=\"30.103125\" y=\"13.067635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 16.866854)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 38.980398 13.377273 \n", "L 43.532846 19.570029 \n", "L 48.085295 25.762785 \n", "L 52.637738 31.955534 \n", "L 57.190187 38.14829 \n", "L 61.742636 44.341046 \n", "L 66.295084 50.533802 \n", "L 70.847533 56.726558 \n", "L 75.399976 62.919307 \n", "L 79.952425 69.112063 \n", "L 84.504874 75.304819 \n", "L 89.057319 81.497571 \n", "L 93.609765 87.690323 \n", "L 98.162214 93.88308 \n", "L 102.714663 100.075836 \n", "L 107.267109 106.268588 \n", "L 111.819559 112.461346 \n", "L 116.372006 118.6541 \n", "L 120.924454 124.846855 \n", "L 125.476901 131.03961 \n", "L 130.029349 136.922727 \n", "L 134.581797 131.039611 \n", "L 139.134244 124.846856 \n", "L 143.686691 118.654102 \n", "L 148.23914 112.461346 \n", "L 152.791587 106.268592 \n", "L 157.344036 100.075836 \n", "L 161.896482 93.883083 \n", "L 166.448931 87.690327 \n", "L 171.001376 81.497575 \n", "L 175.553825 75.304819 \n", "L 180.106274 69.112063 \n", "L 184.658722 62.919307 \n", "L 189.211166 56.726558 \n", "L 193.763614 50.533802 \n", "L 198.316063 44.341046 \n", "L 202.868512 38.14829 \n", "L 207.42096 31.955534 \n", "L 211.973404 25.762785 \n", "L 216.525852 19.570029 \n", "\" clip-path=\"url(#p06ac4499e5)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 38.980398 44.031408 \n", "L 43.532846 50.224164 \n", "L 48.085295 56.41692 \n", "L 52.637738 62.609669 \n", "L 57.190187 68.802425 \n", "L 61.742636 74.995181 \n", "L 66.295084 81.187937 \n", "L 70.847533 87.380693 \n", "L 75.399976 93.573442 \n", "L 79.952425 99.766198 \n", "L 84.504874 105.958954 \n", "L 89.057319 111.842069 \n", "L 93.609765 117.105909 \n", "L 98.162214 121.750476 \n", "L 102.714663 125.775769 \n", "L 107.267109 129.181782 \n", "L 111.819559 131.968523 \n", "L 116.372006 134.135988 \n", "L 120.924454 135.684176 \n", "L 125.476901 136.613089 \n", "L 130.029349 136.922727 \n", "L 134.581797 136.61309 \n", "L 139.134244 135.684176 \n", "L 143.686691 134.135988 \n", "L 148.23914 131.968523 \n", "L 152.791587 129.181784 \n", "L 157.344036 125.775769 \n", "L 161.896482 121.750479 \n", "L 166.448931 117.105911 \n", "L 171.001376 111.842073 \n", "L 175.553825 105.958954 \n", "L 180.106274 99.766198 \n", "L 184.658722 93.573442 \n", "L 189.211166 87.380693 \n", "L 193.763614 81.187937 \n", "L 198.316063 74.995181 \n", "L 202.868512 68.802425 \n", "L 207.42096 62.609669 \n", "L 211.973404 56.41692 \n", "L 216.525852 50.224164 \n", "\" clip-path=\"url(#p06ac4499e5)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 38.980398 105.958954 \n", "L 43.532846 108.977923 \n", "L 48.085295 111.842073 \n", "L 52.637738 114.5514 \n", "L 57.190187 117.105911 \n", "L 61.742636 119.505605 \n", "L 66.295084 121.750479 \n", "L 70.847533 123.840535 \n", "L 75.399976 125.775769 \n", "L 79.952425 127.556186 \n", "L 84.504874 129.181784 \n", "L 89.057319 130.652563 \n", "L 93.609765 131.968523 \n", "L 98.162214 133.129665 \n", "L 102.714663 134.135988 \n", "L 107.267109 134.987491 \n", "L 111.819559 135.684176 \n", "L 116.372006 136.226042 \n", "L 120.924454 136.61309 \n", "L 125.476901 136.845318 \n", "L 130.029349 136.922727 \n", "L 134.581797 136.845318 \n", "L 139.134244 136.61309 \n", "L 143.686691 136.226042 \n", "L 148.23914 135.684176 \n", "L 152.791587 134.987491 \n", "L 157.344036 134.135988 \n", "L 161.896482 133.129665 \n", "L 166.448931 131.968523 \n", "L 171.001376 130.652564 \n", "L 175.553825 129.181784 \n", "L 180.106274 127.556186 \n", "L 184.658722 125.775769 \n", "L 189.211166 123.840535 \n", "L 193.763614 121.750479 \n", "L 198.316063 119.505605 \n", "L 202.868512 117.105911 \n", "L 207.42096 114.5514 \n", "L 211.973404 111.842073 \n", "L 216.525852 108.977923 \n", "\" clip-path=\"url(#p06ac4499e5)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.1 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.1 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 81.328125 59.234375 \n", "L 174.178125 59.234375 \n", "Q 176.178125 59.234375 176.178125 57.234375 \n", "L 176.178125 14.2 \n", "Q 176.178125 12.2 174.178125 12.2 \n", "L 81.328125 12.2 \n", "Q 79.328125 12.2 79.328125 14.2 \n", "L 79.328125 57.234375 \n", "Q 79.328125 59.234375 81.328125 59.234375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_14\">\n", "     <path d=\"M 83.328125 20.298438 \n", "L 93.328125 20.298438 \n", "L 103.328125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- sigma=10.0 -->\n", "     <g transform=\"translate(111.328125 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-73\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"52.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"79.882812\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"143.359375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"240.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"302.050781\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"385.839844\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"449.462891\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"513.085938\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"544.873047\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_15\">\n", "     <path d=\"M 83.328125 34.976562 \n", "L 93.328125 34.976562 \n", "L 103.328125 34.976562 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- sigma=1.0 -->\n", "     <g transform=\"translate(111.328125 38.476562)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-73\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"52.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"79.882812\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"143.359375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"240.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"302.050781\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"385.839844\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"449.462891\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"481.25\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_16\">\n", "     <path d=\"M 83.328125 49.654688 \n", "L 93.328125 49.654688 \n", "L 103.328125 49.654688 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- sigma=0.5 -->\n", "     <g transform=\"translate(111.328125 53.154688)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-73\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"52.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"79.882812\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"143.359375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"240.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"302.050781\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"385.839844\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"449.462891\"/>\n", "      <use xlink:href=\"#DejaVuSans-35\" x=\"481.25\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p06ac4499e5\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["def smooth_l1(data, scalar):\n", "    out = []\n", "    for i in data:\n", "        if abs(i) < 1 / (scalar ** 2):\n", "            out.append(((scalar * i) ** 2) / 2)\n", "        else:\n", "            out.append(abs(i) - 0.5 / (scalar ** 2))\n", "    return torch.tensor(out)\n", "\n", "sigmas = [10, 1, 0.5]\n", "lines = ['-', '--', '-.']\n", "x = torch.arange(-2, 2, 0.1)\n", "d2l.set_figsize()\n", "\n", "for l, s in zip(lines, sigmas):\n", "    y = smooth_l1(x, scalar=s)\n", "    d2l.plt.plot(x, y, l, label='sigma=%.1f' % s)\n", "d2l.plt.legend();"]}, {"cell_type": "markdown", "id": "96f72911", "metadata": {"origin_pos": 82}, "source": ["此外，在类别预测时，实验中使用了交叉熵损失：设真实类别$j$的预测概率是$p_j$，交叉熵损失为$-\\log p_j$。我们还可以使用焦点损失 :cite:`Lin.Goyal.Girshick.ea.2017`。给定超参数$\\gamma > 0$和$\\alpha > 0$，此损失的定义为：\n", "\n", "$$ - \\alpha (1-p_j)^{\\gamma} \\log p_j.$$\n", "\n", "可以看到，增大$\\gamma$可以有效地减少正类预测概率较大时（例如$p_j > 0.5$）的相对损失，因此训练可以更集中在那些错误分类的困难示例上。\n"]}, {"cell_type": "code", "execution_count": 23, "id": "3a5a6fea", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:51.639973Z", "iopub.status.busy": "2023-08-18T07:18:51.639373Z", "iopub.status.idle": "2023-08-18T07:18:51.882393Z", "shell.execute_reply": "2023-08-18T07:18:51.881561Z"}, "origin_pos": 84, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"223.948476pt\" height=\"166.978125pt\" viewBox=\"0 0 223.**********.978125\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:18:51.844535</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 166.978125 \n", "L 223.**********.978125 \n", "L 223.948476 0 \n", "L 0 0 \n", "L 0 166.978125 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 20.5625 143.1 \n", "L 215.8625 143.1 \n", "L 215.8625 7.2 \n", "L 20.5625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m7fcdc4991a\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7fcdc4991a\" x=\"27.628084\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(19.676522 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m7fcdc4991a\" x=\"63.86185\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(55.910288 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m7fcdc4991a\" x=\"100.095616\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(92.144054 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m7fcdc4991a\" x=\"136.329382\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(128.37782 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m7fcdc4991a\" x=\"172.563148\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(164.611585 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m7fcdc4991a\" x=\"208.796914\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(200.845351 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m7da49984f2\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7da49984f2\" x=\"20.5625\" y=\"136.922727\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(7.2 140.721946)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m7da49984f2\" x=\"20.5625\" y=\"110.095173\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(7.2 113.894392)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m7da49984f2\" x=\"20.5625\" y=\"83.267619\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(7.2 87.066838)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m7da49984f2\" x=\"20.5625\" y=\"56.440065\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(7.2 60.239283)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m7da49984f2\" x=\"20.5625\" y=\"29.61251\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(7.2 33.411729)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 29.439773 13.377273 \n", "L 31.251461 31.972716 \n", "L 33.063149 42.850354 \n", "L 34.874837 50.56816 \n", "L 36.686526 56.554556 \n", "L 38.498214 61.445798 \n", "L 40.309903 65.581285 \n", "L 42.12159 69.163604 \n", "L 43.93328 72.323435 \n", "L 45.744968 75.15 \n", "L 47.556657 77.70694 \n", "L 49.368345 80.041241 \n", "L 51.180034 82.188595 \n", "L 52.991721 84.176728 \n", "L 54.80341 86.027638 \n", "L 56.615097 87.759047 \n", "L 58.426786 89.38546 \n", "L 60.238475 90.918879 \n", "L 62.050162 92.369371 \n", "L 63.861851 93.745444 \n", "L 65.67354 95.054366 \n", "L 67.485227 96.302383 \n", "L 69.296916 97.494917 \n", "L 71.108603 98.636685 \n", "L 72.920292 99.73184 \n", "L 74.731978 100.784035 \n", "L 76.54367 101.796517 \n", "L 78.355357 102.772172 \n", "L 80.167043 103.713585 \n", "L 81.978735 104.623081 \n", "L 83.790422 105.502752 \n", "L 85.602109 106.354491 \n", "L 87.413801 107.180021 \n", "L 89.225487 107.980904 \n", "L 91.037179 108.758568 \n", "L 92.848866 109.514322 \n", "L 94.660552 110.249371 \n", "L 96.472244 110.964816 \n", "L 98.283931 111.661674 \n", "L 100.095617 112.340889 \n", "L 101.907304 113.003331 \n", "L 103.71899 113.649808 \n", "L 105.530682 114.281075 \n", "L 107.342369 114.897827 \n", "L 109.154056 115.500717 \n", "L 110.965747 116.090359 \n", "L 112.777434 116.667318 \n", "L 114.589121 117.232128 \n", "L 116.400813 117.785295 \n", "L 118.212499 118.327284 \n", "L 120.024186 118.85854 \n", "L 121.835872 119.379479 \n", "L 123.64757 119.890497 \n", "L 125.459256 120.391962 \n", "L 127.270943 120.884224 \n", "L 129.082629 121.367615 \n", "L 130.894316 121.842451 \n", "L 132.706002 122.309029 \n", "L 134.517689 122.76763 \n", "L 136.329376 123.218523 \n", "L 138.141073 123.661966 \n", "L 139.95276 124.098196 \n", "L 141.764446 124.527445 \n", "L 143.576133 124.949935 \n", "L 145.387819 125.365874 \n", "L 147.199506 125.775463 \n", "L 149.011192 126.178892 \n", "L 150.822879 126.576344 \n", "L 152.634576 126.967996 \n", "L 154.446263 127.35401 \n", "L 156.257949 127.734549 \n", "L 158.069636 128.109765 \n", "L 159.881333 128.479809 \n", "L 161.69302 128.844814 \n", "L 163.504706 129.204921 \n", "L 165.316393 129.560257 \n", "L 167.12809 129.910952 \n", "L 168.939777 130.257118 \n", "L 170.751464 130.598874 \n", "L 172.56315 130.936332 \n", "L 174.374837 131.269597 \n", "L 176.186523 131.598774 \n", "L 177.99821 131.92396 \n", "L 179.809896 132.245251 \n", "L 181.621594 132.562743 \n", "L 183.43328 132.876518 \n", "L 185.244967 133.186667 \n", "L 187.056653 133.49327 \n", "L 188.86834 133.79641 \n", "L 190.680027 134.096162 \n", "L 192.491713 134.392601 \n", "L 194.3034 134.685801 \n", "L 196.115097 134.975833 \n", "L 197.926784 135.262761 \n", "L 199.73847 135.546653 \n", "L 201.550157 135.827572 \n", "L 203.361854 136.105582 \n", "L 205.173541 136.380739 \n", "L 206.985227 136.653102 \n", "\" clip-path=\"url(#p472d7b2285)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 29.439773 14.612722 \n", "L 31.251461 34.071715 \n", "L 33.063149 45.672525 \n", "L 34.874837 54.022344 \n", "L 36.686526 60.572967 \n", "L 38.498214 65.974413 \n", "L 40.309903 70.575185 \n", "L 42.12159 74.584334 \n", "L 43.93328 78.137371 \n", "L 45.744968 81.327271 \n", "L 47.556657 84.220676 \n", "L 49.368345 86.867019 \n", "L 51.180034 89.30403 \n", "L 52.991721 91.561168 \n", "L 54.80341 93.6619 \n", "L 56.615097 95.625235 \n", "L 58.426786 97.466797 \n", "L 60.238475 99.199572 \n", "L 62.050162 100.834508 \n", "L 63.861851 102.380901 \n", "L 65.67354 103.846725 \n", "L 67.485227 105.238861 \n", "L 69.296916 106.563314 \n", "L 71.108603 107.825336 \n", "L 72.920292 109.029562 \n", "L 74.731978 110.180095 \n", "L 76.54367 111.280593 \n", "L 78.355357 112.334326 \n", "L 80.167043 113.344235 \n", "L 81.978735 114.312976 \n", "L 83.790422 115.242945 \n", "L 85.602109 116.136326 \n", "L 87.413801 116.995116 \n", "L 89.225487 117.821125 \n", "L 91.037179 118.616025 \n", "L 92.848866 119.381348 \n", "L 94.660552 120.118514 \n", "L 96.472244 120.828823 \n", "L 98.283931 121.513485 \n", "L 100.095617 122.173623 \n", "L 101.907304 122.810282 \n", "L 103.71899 123.424433 \n", "L 105.530682 124.016986 \n", "L 107.342369 124.588783 \n", "L 109.154056 125.140622 \n", "L 110.965747 125.67325 \n", "L 112.777434 126.187361 \n", "L 114.589121 126.683616 \n", "L 116.400813 127.162637 \n", "L 118.212499 127.625005 \n", "L 120.024186 128.071275 \n", "L 121.835872 128.501968 \n", "L 123.64757 128.91758 \n", "L 125.459256 129.318575 \n", "L 127.270943 129.705401 \n", "L 129.082629 130.078478 \n", "L 130.894316 130.438208 \n", "L 132.706002 130.784973 \n", "L 134.517689 131.119137 \n", "L 136.329376 131.441045 \n", "L 138.141073 131.751031 \n", "L 139.95276 132.049405 \n", "L 141.764446 132.336473 \n", "L 143.576133 132.612522 \n", "L 145.387819 132.877828 \n", "L 147.199506 133.132657 \n", "L 149.011192 133.377261 \n", "L 150.822879 133.611884 \n", "L 152.634576 133.836761 \n", "L 154.446263 134.052112 \n", "L 156.257949 134.258155 \n", "L 158.069636 134.455098 \n", "L 159.881333 134.643139 \n", "L 161.69302 134.82247 \n", "L 163.504706 134.993276 \n", "L 165.316393 135.155735 \n", "L 167.12809 135.310019 \n", "L 168.939777 135.456293 \n", "L 170.751464 135.594718 \n", "L 172.56315 135.725448 \n", "L 174.374837 135.848633 \n", "L 176.186523 135.964416 \n", "L 177.99821 136.072937 \n", "L 179.809896 136.174331 \n", "L 181.621594 136.26873 \n", "L 183.43328 136.356258 \n", "L 185.244967 136.437039 \n", "L 187.056653 136.511192 \n", "L 188.86834 136.578832 \n", "L 190.680027 136.640071 \n", "L 192.491713 136.695016 \n", "L 194.3034 136.743773 \n", "L 196.115097 136.786445 \n", "L 197.926784 136.823129 \n", "L 199.73847 136.853924 \n", "L 201.550157 136.878921 \n", "L 203.361854 136.898213 \n", "L 205.173541 136.911888 \n", "L 206.985227 136.920031 \n", "\" clip-path=\"url(#p472d7b2285)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 29.439773 19.432223 \n", "L 31.251461 42.056223 \n", "L 33.063149 56.139572 \n", "L 34.874837 66.511578 \n", "L 36.686526 74.735374 \n", "L 38.498214 81.529909 \n", "L 40.309903 87.291316 \n", "L 42.12159 92.263936 \n", "L 43.93328 96.610702 \n", "L 45.744968 100.446553 \n", "L 47.556657 103.856284 \n", "L 49.368345 106.904553 \n", "L 51.180034 109.64209 \n", "L 52.991721 112.109582 \n", "L 54.80341 114.340302 \n", "L 56.615097 116.361886 \n", "L 58.426786 118.197606 \n", "L 60.238475 119.86727 \n", "L 62.050162 121.387933 \n", "L 63.861851 122.774394 \n", "L 65.67354 124.0396 \n", "L 67.485227 125.194928 \n", "L 69.296916 126.250472 \n", "L 71.108603 127.215204 \n", "L 72.920292 128.097155 \n", "L 74.731978 128.903527 \n", "L 76.54367 129.640811 \n", "L 78.355357 130.314875 \n", "L 80.167043 130.931035 \n", "L 81.978735 131.494126 \n", "L 83.790422 132.008545 \n", "L 85.602109 132.478309 \n", "L 87.413801 132.907091 \n", "L 89.225487 133.298249 \n", "L 91.037179 133.654867 \n", "L 92.848866 133.979773 \n", "L 94.660552 134.275566 \n", "L 96.472244 134.544638 \n", "L 98.283931 134.789188 \n", "L 100.095617 135.011243 \n", "L 101.907304 135.212671 \n", "L 103.71899 135.395193 \n", "L 105.530682 135.560397 \n", "L 107.342369 135.709746 \n", "L 109.154056 135.844591 \n", "L 110.965747 135.966178 \n", "L 112.777434 136.075655 \n", "L 114.589121 136.174083 \n", "L 116.400813 136.262438 \n", "L 118.212499 136.34162 \n", "L 120.024186 136.412459 \n", "L 121.835872 136.475718 \n", "L 123.64757 136.532102 \n", "L 125.459256 136.582255 \n", "L 127.270943 136.626772 \n", "L 129.082629 136.666198 \n", "L 130.894316 136.701034 \n", "L 132.706002 136.731739 \n", "L 134.517689 136.758732 \n", "L 136.329376 136.782396 \n", "L 138.141073 136.803083 \n", "L 139.95276 136.821112 \n", "L 141.764446 136.836773 \n", "L 143.576133 136.850332 \n", "L 145.387819 136.862028 \n", "L 147.199506 136.872079 \n", "L 149.011192 136.880681 \n", "L 150.822879 136.888011 \n", "L 152.634576 136.894228 \n", "L 154.446263 136.899475 \n", "L 156.257949 136.903881 \n", "L 158.069636 136.90756 \n", "L 159.881333 136.910613 \n", "L 161.69302 136.91313 \n", "L 163.504706 136.91519 \n", "L 165.316393 136.916865 \n", "L 167.12809 136.918214 \n", "L 168.939777 136.919292 \n", "L 170.751464 136.920145 \n", "L 172.56315 136.920812 \n", "L 174.374837 136.921328 \n", "L 176.186523 136.921721 \n", "L 177.99821 136.922018 \n", "L 179.809896 136.922237 \n", "L 181.621594 136.922396 \n", "L 183.43328 136.92251 \n", "L 185.244967 136.922589 \n", "L 187.056653 136.922642 \n", "L 188.86834 136.922677 \n", "L 190.680027 136.922699 \n", "L 192.491713 136.922712 \n", "L 194.3034 136.92272 \n", "L 196.115097 136.922724 \n", "L 197.926784 136.922726 \n", "L 199.73847 136.922727 \n", "L 201.550157 136.922727 \n", "L 203.361854 136.922727 \n", "L 205.173541 136.922727 \n", "L 206.985227 136.922727 \n", "\" clip-path=\"url(#p472d7b2285)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 20.5625 143.1 \n", "L 20.5625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 215.8625 143.1 \n", "L 215.8625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 20.5625 143.1 \n", "L 215.8625 143.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 20.5625 7.2 \n", "L 215.8625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 114.49375 59.234375 \n", "L 208.8625 59.234375 \n", "Q 210.8625 59.234375 210.8625 57.234375 \n", "L 210.8625 14.2 \n", "Q 210.8625 12.2 208.8625 12.2 \n", "L 114.49375 12.2 \n", "Q 112.49375 12.2 112.49375 14.2 \n", "L 112.49375 57.234375 \n", "Q 112.49375 59.234375 114.49375 59.234375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_15\">\n", "     <path d=\"M 116.49375 20.298438 \n", "L 126.49375 20.298438 \n", "L 136.49375 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- gamma=0.0 -->\n", "     <g transform=\"translate(144.49375 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-67\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"222.167969\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"319.580078\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"380.859375\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"464.648438\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"528.271484\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"560.058594\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_16\">\n", "     <path d=\"M 116.49375 34.976562 \n", "L 126.49375 34.976562 \n", "L 136.49375 34.976562 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- gamma=1.0 -->\n", "     <g transform=\"translate(144.49375 38.476562)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-67\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"222.167969\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"319.580078\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"380.859375\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"464.648438\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"528.271484\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"560.058594\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_17\">\n", "     <path d=\"M 116.49375 49.654688 \n", "L 126.49375 49.654688 \n", "L 136.49375 49.654688 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- gamma=5.0 -->\n", "     <g transform=\"translate(144.49375 53.154688)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-67\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"222.167969\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"319.580078\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"380.859375\"/>\n", "      <use xlink:href=\"#DejaVuSans-35\" x=\"464.648438\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"528.271484\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"560.058594\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p472d7b2285\">\n", "   <rect x=\"20.5625\" y=\"7.2\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["def focal_loss(gamma, x):\n", "    return -(1 - x) ** gamma * torch.log(x)\n", "\n", "x = torch.arange(0.01, 1, 0.01)\n", "for l, gamma in zip(lines, [0, 1, 5]):\n", "    y = d2l.plt.plot(x, focal_loss(gamma, x), l, label='gamma=%.1f' % gamma)\n", "d2l.plt.legend();"]}, {"cell_type": "markdown", "id": "fd10bf15", "metadata": {"origin_pos": 86}, "source": ["2. 由于篇幅限制，我们在本节中省略了单发多框检测模型的一些实现细节。能否从以下几个方面进一步改进模型：\n", "    1. 当目标比图像小得多时，模型可以将输入图像调大；\n", "    1. 通常会存在大量的负锚框。为了使类别分布更加平衡，我们可以将负锚框的高和宽减半；\n", "    1. 在损失函数中，给类别损失和偏移损失设置不同比重的超参数；\n", "    1. 使用其他方法评估目标检测模型，例如单发多框检测论文 :cite:`<PERSON>.Anguelov.Erhan.ea.2016`中的方法。\n"]}, {"cell_type": "markdown", "id": "03e00148", "metadata": {"origin_pos": 88, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/3204)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
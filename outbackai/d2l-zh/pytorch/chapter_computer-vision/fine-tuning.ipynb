{"cells": [{"cell_type": "markdown", "id": "07b4b134", "metadata": {"origin_pos": 0}, "source": ["# 微调\n", ":label:`sec_fine_tuning`\n", "\n", "前面的一些章节介绍了如何在只有6万张图像的Fashion-MNIST训练数据集上训练模型。\n", "我们还描述了学术界当下使用最广泛的大规模图像数据集ImageNet，它有超过1000万的图像和1000类的物体。\n", "然而，我们平常接触到的数据集的规模通常在这两者之间。\n", "\n", "假如我们想识别图片中不同类型的椅子，然后向用户推荐购买链接。\n", "一种可能的方法是首先识别100把普通椅子，为每把椅子拍摄1000张不同角度的图像，然后在收集的图像数据集上训练一个分类模型。\n", "尽管这个椅子数据集可能大于Fashion-MNIST数据集，但实例数量仍然不到ImageNet中的十分之一。\n", "适合ImageNet的复杂模型可能会在这个椅子数据集上过拟合。\n", "此外，由于训练样本数量有限，训练模型的准确性可能无法满足实际要求。\n", "\n", "为了解决上述问题，一个显而易见的解决方案是收集更多的数据。\n", "但是，收集和标记数据可能需要大量的时间和金钱。\n", "例如，为了收集ImageNet数据集，研究人员花费了数百万美元的研究资金。\n", "尽管目前的数据收集成本已大幅降低，但这一成本仍不能忽视。\n", "\n", "另一种解决方案是应用*迁移学习*（transfer learning）将从*源数据集*学到的知识迁移到*目标数据集*。\n", "例如，尽管ImageNet数据集中的大多数图像与椅子无关，但在此数据集上训练的模型可能会提取更通用的图像特征，这有助于识别边缘、纹理、形状和对象组合。\n", "这些类似的特征也可能有效地识别椅子。\n", "\n", "## 步骤\n", "\n", "本节将介绍迁移学习中的常见技巧:*微调*（fine-tuning）。如 :numref:`fig_finetune`所示，微调包括以下四个步骤。\n", "\n", "1. 在源数据集（例如ImageNet数据集）上预训练神经网络模型，即*源模型*。\n", "1. 创建一个新的神经网络模型，即*目标模型*。这将复制源模型上的所有模型设计及其参数（输出层除外）。我们假定这些模型参数包含从源数据集中学到的知识，这些知识也将适用于目标数据集。我们还假设源模型的输出层与源数据集的标签密切相关；因此不在目标模型中使用该层。\n", "1. 向目标模型添加输出层，其输出数是目标数据集中的类别数。然后随机初始化该层的模型参数。\n", "1. 在目标数据集（如椅子数据集）上训练目标模型。输出层将从头开始进行训练，而所有其他层的参数将根据源模型的参数进行微调。\n", "\n", "![微调。](../img/finetune.svg)\n", ":label:`fig_finetune`\n", "\n", "当目标数据集比源数据集小得多时，微调有助于提高模型的泛化能力。\n", "\n", "## 热狗识别\n", "\n", "让我们通过具体案例演示微调：热狗识别。\n", "我们将在一个小型数据集上微调ResNet模型。该模型已在ImageNet数据集上进行了预训练。\n", "这个小型数据集包含数千张包含热狗和不包含热狗的图像，我们将使用微调模型来识别图像中是否包含热狗。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a041c0b1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:09:16.255602Z", "iopub.status.busy": "2023-08-18T07:09:16.254993Z", "iopub.status.idle": "2023-08-18T07:09:19.697997Z", "shell.execute_reply": "2023-08-18T07:09:19.697096Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import os\n", "import torch\n", "import torchvision\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "e96e037d", "metadata": {"origin_pos": 4}, "source": ["### 获取数据集\n", "\n", "我们使用的[**热狗数据集来源于网络**]。\n", "该数据集包含1400张热狗的“正类”图像，以及包含尽可能多的其他食物的“负类”图像。\n", "含着两个类别的1000张图片用于训练，其余的则用于测试。\n", "\n", "解压下载的数据集，我们获得了两个文件夹`hotdog/train`和`hotdog/test`。\n", "这两个文件夹都有`hotdog`（有热狗）和`not-hotdog`（无热狗）两个子文件夹，\n", "子文件夹内都包含相应类的图像。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "60ecdc80", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:09:19.743010Z", "iopub.status.busy": "2023-08-18T07:09:19.742335Z", "iopub.status.idle": "2023-08-18T07:09:27.510104Z", "shell.execute_reply": "2023-08-18T07:09:27.509268Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/hotdog.zip from http://d2l-data.s3-accelerate.amazonaws.com/hotdog.zip...\n"]}], "source": ["#@save\n", "d2l.DATA_HUB['hotdog'] = (d2l.DATA_URL + 'hotdog.zip',\n", "                         'fba480ffa8aa7e0febbb511d181409f899b9baa5')\n", "\n", "data_dir = d2l.download_extract('hotdog')"]}, {"cell_type": "markdown", "id": "c72c1b3a", "metadata": {"origin_pos": 6}, "source": ["我们创建两个实例来分别读取训练和测试数据集中的所有图像文件。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "fd208011", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:09:27.514182Z", "iopub.status.busy": "2023-08-18T07:09:27.513619Z", "iopub.status.idle": "2023-08-18T07:09:27.525236Z", "shell.execute_reply": "2023-08-18T07:09:27.524465Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["train_imgs = torchvision.datasets.ImageFolder(os.path.join(data_dir, 'train'))\n", "test_imgs = torchvision.datasets.ImageFolder(os.path.join(data_dir, 'test'))"]}, {"cell_type": "markdown", "id": "5a20cf43", "metadata": {"origin_pos": 10}, "source": ["下面显示了前8个正类样本图片和最后8张负类样本图片。正如所看到的，[**图像的大小和纵横比各有不同**]。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "c5db62fb", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:09:27.528713Z", "iopub.status.busy": "2023-08-18T07:09:27.528116Z", "iopub.status.idle": "2023-08-18T07:09:28.207612Z", "shell.execute_reply": "2023-08-18T07:09:28.206842Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 806.4x201.6 with 16 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["hotdogs = [train_imgs[i][0] for i in range(8)]\n", "not_hotdogs = [train_imgs[-i - 1][0] for i in range(8)]\n", "d2l.show_images(hotdogs + not_hotdogs, 2, 8, scale=1.4);"]}, {"cell_type": "markdown", "id": "5431c601", "metadata": {"origin_pos": 12}, "source": ["在训练期间，我们首先从图像中裁切随机大小和随机长宽比的区域，然后将该区域缩放为$224 \\times 224$输入图像。\n", "在测试过程中，我们将图像的高度和宽度都缩放到256像素，然后裁剪中央$224 \\times 224$区域作为输入。\n", "此外，对于RGB（红、绿和蓝）颜色通道，我们分别*标准化*每个通道。\n", "具体而言，该通道的每个值减去该通道的平均值，然后将结果除以该通道的标准差。\n", "\n", "[~~数据增广~~]\n"]}, {"cell_type": "code", "execution_count": 5, "id": "d251b5e9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:09:28.212132Z", "iopub.status.busy": "2023-08-18T07:09:28.211616Z", "iopub.status.idle": "2023-08-18T07:09:28.217043Z", "shell.execute_reply": "2023-08-18T07:09:28.216348Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["# 使用RGB通道的均值和标准差，以标准化每个通道\n", "normalize = torchvision.transforms.Normalize(\n", "    [0.485, 0.456, 0.406], [0.229, 0.224, 0.225])\n", "\n", "train_augs = torchvision.transforms.Compose([\n", "    torchvision.transforms.RandomResizedCrop(224),\n", "    torchvision.transforms.RandomHorizontalFlip(),\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    normalize])\n", "\n", "test_augs = torchvision.transforms.Compose([\n", "    torchvision.transforms.Resize([256, 256]),\n", "    torchvision.transforms.CenterCrop(224),\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    normalize])"]}, {"cell_type": "markdown", "id": "1cc832c2", "metadata": {"origin_pos": 16}, "source": ["### [**定义和初始化模型**]\n", "\n", "我们使用在ImageNet数据集上预训练的ResNet-18作为源模型。\n", "在这里，我们指定`pretrained=True`以自动下载预训练的模型参数。\n", "如果首次使用此模型，则需要连接互联网才能下载。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "657aea3e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:09:28.220308Z", "iopub.status.busy": "2023-08-18T07:09:28.219814Z", "iopub.status.idle": "2023-08-18T07:09:28.558088Z", "shell.execute_reply": "2023-08-18T07:09:28.557042Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [], "source": ["pretrained_net = torchvision.models.resnet18(pretrained=True)"]}, {"cell_type": "markdown", "id": "b51509e0", "metadata": {"origin_pos": 21, "tab": ["pytorch"]}, "source": ["预训练的源模型实例包含许多特征层和一个输出层`fc`。\n", "此划分的主要目的是促进对除输出层以外所有层的模型参数进行微调。\n", "下面给出了源模型的成员变量`fc`。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "d622c8fe", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:09:28.563119Z", "iopub.status.busy": "2023-08-18T07:09:28.562608Z", "iopub.status.idle": "2023-08-18T07:09:28.568570Z", "shell.execute_reply": "2023-08-18T07:09:28.567682Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["Linear(in_features=512, out_features=1000, bias=True)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pretrained_net.fc"]}, {"cell_type": "markdown", "id": "82b1edb2", "metadata": {"origin_pos": 25}, "source": ["在ResNet的全局平均汇聚层后，全连接层转换为ImageNet数据集的1000个类输出。\n", "之后，我们构建一个新的神经网络作为目标模型。\n", "它的定义方式与预训练源模型的定义方式相同，只是最终层中的输出数量被设置为目标数据集中的类数（而不是1000个）。\n", "\n", "在下面的代码中，目标模型`finetune_net`中成员变量`features`的参数被初始化为源模型相应层的模型参数。\n", "由于模型参数是在ImageNet数据集上预训练的，并且足够好，因此通常只需要较小的学习率即可微调这些参数。\n", "\n", "成员变量`output`的参数是随机初始化的，通常需要更高的学习率才能从头开始训练。\n", "假设`Trainer`实例中的学习率为$\\eta$，我们将成员变量`output`中参数的学习率设置为$10\\eta$。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "18ccccf4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:09:28.572805Z", "iopub.status.busy": "2023-08-18T07:09:28.572343Z", "iopub.status.idle": "2023-08-18T07:09:28.824724Z", "shell.execute_reply": "2023-08-18T07:09:28.823733Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [], "source": ["finetune_net = torchvision.models.resnet18(pretrained=True)\n", "finetune_net.fc = nn.Linear(finetune_net.fc.in_features, 2)\n", "nn.init.xavier_uniform_(finetune_net.fc.weight);"]}, {"cell_type": "markdown", "id": "d7f20fdb", "metadata": {"origin_pos": 29}, "source": ["### [**微调模型**]\n", "\n", "首先，我们定义了一个训练函数`train_fine_tuning`，该函数使用微调，因此可以多次调用。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "bb911459", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:09:28.829772Z", "iopub.status.busy": "2023-08-18T07:09:28.829267Z", "iopub.status.idle": "2023-08-18T07:09:28.837208Z", "shell.execute_reply": "2023-08-18T07:09:28.836270Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [], "source": ["# 如果param_group=True，输出层中的模型参数将使用十倍的学习率\n", "def train_fine_tuning(net, learning_rate, batch_size=128, num_epochs=5,\n", "                      param_group=True):\n", "    train_iter = torch.utils.data.DataLoader(torchvision.datasets.ImageFolder(\n", "        os.path.join(data_dir, 'train'), transform=train_augs),\n", "        batch_size=batch_size, shuffle=True)\n", "    test_iter = torch.utils.data.DataLoader(torchvision.datasets.ImageFolder(\n", "        os.path.join(data_dir, 'test'), transform=test_augs),\n", "        batch_size=batch_size)\n", "    devices = d2l.try_all_gpus()\n", "    loss = nn.CrossEntropyLoss(reduction=\"none\")\n", "    if param_group:\n", "        params_1x = [param for name, param in net.named_parameters()\n", "             if name not in [\"fc.weight\", \"fc.bias\"]]\n", "        trainer = torch.optim.SGD([{'params': params_1x},\n", "                                   {'params': net.fc.parameters(),\n", "                                    'lr': learning_rate * 10}],\n", "                                lr=learning_rate, weight_decay=0.001)\n", "    else:\n", "        trainer = torch.optim.SGD(net.parameters(), lr=learning_rate,\n", "                                  weight_decay=0.001)\n", "    d2l.train_ch13(net, train_iter, test_iter, loss, trainer, num_epochs,\n", "                   devices)"]}, {"cell_type": "markdown", "id": "ca08a322", "metadata": {"origin_pos": 33}, "source": ["我们[**使用较小的学习率**]，通过*微调*预训练获得的模型参数。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "955d840b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:09:28.841211Z", "iopub.status.busy": "2023-08-18T07:09:28.840900Z", "iopub.status.idle": "2023-08-18T07:10:43.619696Z", "shell.execute_reply": "2023-08-18T07:10:43.618758Z"}, "origin_pos": 35, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.220, train acc 0.915, test acc 0.939\n", "999.1 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"184.455469pt\" viewBox=\"0 0 235.**********.455469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:10:43.576056</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 184.455469 \n", "L 235.**********.455469 \n", "L 235.784375 -0 \n", "L 0 -0 \n", "L 0 184.455469 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.899219 \n", "L 225.**********.899219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 30.**********.899219 \n", "L 30.103125 10.999219 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"medfec3b7b9\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#medfec3b7b9\" x=\"30.103125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(26.921875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 78.928125 146.899219 \n", "L 78.928125 10.999219 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#medfec3b7b9\" x=\"78.928125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 127.753125 146.899219 \n", "L 127.753125 10.999219 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#medfec3b7b9\" x=\"127.753125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(124.571875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 176.578125 146.899219 \n", "L 176.578125 10.999219 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#medfec3b7b9\" x=\"176.578125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(173.396875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.899219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#medfec3b7b9\" x=\"225.403125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(222.221875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 175.175781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.**********.899219 \n", "L 225.**********.899219 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m4086ccd321\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m4086ccd321\" x=\"30.103125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 150.698437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 119.719219 \n", "L 225.403125 119.719219 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m4086ccd321\" x=\"30.103125\" y=\"119.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 123.518437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 92.539219 \n", "L 225.403125 92.539219 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m4086ccd321\" x=\"30.103125\" y=\"92.539219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 96.338437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 65.359219 \n", "L 225.403125 65.359219 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m4086ccd321\" x=\"30.103125\" y=\"65.359219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 69.158437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 38.179219 \n", "L 225.403125 38.179219 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m4086ccd321\" x=\"30.103125\" y=\"38.179219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 41.978437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m4086ccd321\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 35.191914 -1 \n", "L 39.257812 104.335532 \n", "L 48.4125 103.939939 \n", "L 57.567188 98.473874 \n", "L 66.721875 100.23497 \n", "L 75.876563 102.498276 \n", "L 78.928125 103.475776 \n", "L 88.082813 111.291551 \n", "L 97.2375 102.386129 \n", "L 106.392188 73.71488 \n", "L 115.546875 76.182167 \n", "L 124.701563 83.971916 \n", "L 127.753125 84.978433 \n", "L 136.907813 121.089487 \n", "L 146.0625 121.532016 \n", "L 155.217187 121.602719 \n", "L 164.371875 118.530392 \n", "L 173.526563 116.526095 \n", "L 176.578125 114.903362 \n", "L 185.732812 115.091033 \n", "L 194.8875 110.12826 \n", "L 204.042188 113.601252 \n", "L 213.196875 116.03681 \n", "L 222.351562 117.002022 \n", "L 225.403125 116.977923 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M -1 73.890455 \n", "L -0.4125 73.640625 \n", "L 8.742188 68.096094 \n", "L 17.896875 57.626367 \n", "L 27.051562 50.919844 \n", "L 30.103125 49.798669 \n", "L 39.257812 24.801562 \n", "L 48.4125 24.624609 \n", "L 57.567188 26.099219 \n", "L 66.721875 26.836523 \n", "L 75.876563 25.863281 \n", "L 78.928125 25.608469 \n", "L 88.082813 24.09375 \n", "L 97.2375 26.217187 \n", "L 106.392188 32.469531 \n", "L 115.546875 31.171875 \n", "L 124.701563 29.473125 \n", "L 127.753125 29.141869 \n", "L 136.907813 19.492969 \n", "L 146.0625 19.492969 \n", "L 155.217187 19.728906 \n", "L 164.371875 20.554687 \n", "L 173.526563 22.041094 \n", "L 176.578125 22.414819 \n", "L 185.732812 21.616406 \n", "L 194.8875 23.385937 \n", "L 204.042188 23.267969 \n", "L 213.196875 22.678125 \n", "L 222.351562 22.536562 \n", "L 225.403125 22.482769 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 30.103125 27.477094 \n", "L 78.928125 23.400094 \n", "L 127.753125 34.611844 \n", "L 176.578125 23.569969 \n", "L 225.403125 19.323094 \n", "\" clip-path=\"url(#p048f16c616)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.899219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.899219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.899219 \n", "L 225.**********.899219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 102.466406 \n", "L 218.403125 102.466406 \n", "Q 220.403125 102.466406 220.403125 100.466406 \n", "L 220.403125 57.432031 \n", "Q 220.403125 55.432031 218.403125 55.432031 \n", "L 140.634375 55.432031 \n", "Q 138.634375 55.432031 138.634375 57.432031 \n", "L 138.634375 100.466406 \n", "Q 138.634375 102.466406 140.634375 102.466406 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 142.634375 63.530469 \n", "L 152.634375 63.530469 \n", "L 162.634375 63.530469 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 67.030469)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 142.634375 78.208594 \n", "L 152.634375 78.208594 \n", "L 162.634375 78.208594 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 81.708594)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 142.634375 92.886719 \n", "L 152.634375 92.886719 \n", "L 162.634375 92.886719 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(170.634375 96.386719)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p048f16c616\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["train_fine_tuning(finetune_net, 5e-5)"]}, {"cell_type": "markdown", "id": "292669f8", "metadata": {"origin_pos": 36}, "source": ["[**为了进行比较，**]我们定义了一个相同的模型，但是将其(**所有模型参数初始化为随机值**)。\n", "由于整个模型需要从头开始训练，因此我们需要使用更大的学习率。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "4006a8f7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:10:43.623482Z", "iopub.status.busy": "2023-08-18T07:10:43.622905Z", "iopub.status.idle": "2023-08-18T07:11:54.256773Z", "shell.execute_reply": "2023-08-18T07:11:54.255915Z"}, "origin_pos": 38, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.374, train acc 0.839, test acc 0.843\n", "1623.8 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"184.455469pt\" viewBox=\"0 0 235.**********.455469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:11:54.212208</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 184.455469 \n", "L 235.**********.455469 \n", "L 235.784375 -0 \n", "L 0 -0 \n", "L 0 184.455469 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.899219 \n", "L 225.**********.899219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 30.**********.899219 \n", "L 30.103125 10.999219 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m7828b6ada2\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7828b6ada2\" x=\"30.103125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(26.921875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 78.928125 146.899219 \n", "L 78.928125 10.999219 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m7828b6ada2\" x=\"78.928125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 127.753125 146.899219 \n", "L 127.753125 10.999219 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m7828b6ada2\" x=\"127.753125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(124.571875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 176.578125 146.899219 \n", "L 176.578125 10.999219 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m7828b6ada2\" x=\"176.578125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(173.396875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.899219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m7828b6ada2\" x=\"225.403125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(222.221875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 175.175781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.**********.899219 \n", "L 225.**********.899219 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mb8ee4b8a2e\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb8ee4b8a2e\" x=\"30.103125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 150.698437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 119.719219 \n", "L 225.403125 119.719219 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mb8ee4b8a2e\" x=\"30.103125\" y=\"119.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 123.518437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 92.539219 \n", "L 225.403125 92.539219 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mb8ee4b8a2e\" x=\"30.103125\" y=\"92.539219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 96.338437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 65.359219 \n", "L 225.403125 65.359219 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mb8ee4b8a2e\" x=\"30.103125\" y=\"65.359219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 69.158437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 38.179219 \n", "L 225.403125 38.179219 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mb8ee4b8a2e\" x=\"30.103125\" y=\"38.179219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 41.978437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#mb8ee4b8a2e\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 35.559969 -1 \n", "L 39.257812 51.69807 \n", "L 48.4125 65.447502 \n", "L 57.567188 71.483871 \n", "L 66.721875 79.247977 \n", "L 75.876563 82.738514 \n", "L 78.928125 82.751816 \n", "L 88.082813 87.791199 \n", "L 97.2375 85.24112 \n", "L 106.392188 81.346537 \n", "L 115.546875 84.661766 \n", "L 124.701563 88.241633 \n", "L 127.753125 88.264746 \n", "L 136.907813 92.081253 \n", "L 146.0625 94.010111 \n", "L 155.217187 93.534812 \n", "L 164.371875 94.165165 \n", "L 173.526563 95.618119 \n", "L 176.578125 95.653873 \n", "L 185.732812 94.887026 \n", "L 194.8875 94.231013 \n", "L 204.042188 92.807584 \n", "L 213.196875 94.215989 \n", "L 222.351562 95.883734 \n", "L 225.403125 96.044005 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M -1 77.760712 \n", "L -0.4125 77.533594 \n", "L 8.742188 69.983594 \n", "L 17.896875 62.758008 \n", "L 27.051562 60.546094 \n", "L 30.103125 61.282219 \n", "L 39.257812 43.204687 \n", "L 48.4125 44.089453 \n", "L 57.567188 42.378906 \n", "L 66.721875 39.488672 \n", "L 75.876563 38.25 \n", "L 78.928125 38.451019 \n", "L 88.082813 39.311719 \n", "L 97.2375 39.311719 \n", "L 106.392188 40.727344 \n", "L 115.546875 39.488672 \n", "L 124.701563 37.68375 \n", "L 127.753125 37.771519 \n", "L 136.907813 37.896094 \n", "L 146.0625 35.41875 \n", "L 155.217187 35.536719 \n", "L 164.371875 34.710937 \n", "L 173.526563 33.861562 \n", "L 176.578125 33.626569 \n", "L 185.732812 35.41875 \n", "L 194.8875 33.472266 \n", "L 204.042188 34.239062 \n", "L 213.196875 33.737695 \n", "L 222.351562 33.082969 \n", "L 225.403125 32.879119 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 30.103125 75.891469 \n", "L 78.928125 32.573344 \n", "L 127.753125 34.951594 \n", "L 176.578125 34.611844 \n", "L 225.403125 32.403469 \n", "\" clip-path=\"url(#p8e12c2d8a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.899219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.899219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.899219 \n", "L 225.**********.899219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 37.103125 141.899219 \n", "L 114.871875 141.899219 \n", "Q 116.871875 141.899219 116.871875 139.899219 \n", "L 116.871875 96.864844 \n", "Q 116.871875 94.864844 114.871875 94.864844 \n", "L 37.103125 94.864844 \n", "Q 35.103125 94.864844 35.103125 96.864844 \n", "L 35.103125 139.899219 \n", "Q 35.103125 141.899219 37.103125 141.899219 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 39.103125 102.963281 \n", "L 49.103125 102.963281 \n", "L 59.103125 102.963281 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(67.103125 106.463281)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 39.103125 117.641406 \n", "L 49.103125 117.641406 \n", "L 59.103125 117.641406 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(67.103125 121.141406)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 39.103125 132.319531 \n", "L 49.103125 132.319531 \n", "L 59.103125 132.319531 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(67.103125 135.819531)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p8e12c2d8a1\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["scratch_net = torchvision.models.resnet18()\n", "scratch_net.fc = nn.Linear(scratch_net.fc.in_features, 2)\n", "train_fine_tuning(scratch_net, 5e-4, param_group=False)"]}, {"cell_type": "markdown", "id": "c1c87e63", "metadata": {"origin_pos": 40}, "source": ["意料之中，微调模型往往表现更好，因为它的初始参数值更有效。\n", "\n", "## 小结\n", "\n", "* 迁移学习将从源数据集中学到的知识*迁移*到目标数据集，微调是迁移学习的常见技巧。\n", "* 除输出层外，目标模型从源模型中复制所有模型设计及其参数，并根据目标数据集对这些参数进行微调。但是，目标模型的输出层需要从头开始训练。\n", "* 通常，微调参数使用较小的学习率，而从头开始训练输出层可以使用更大的学习率。\n", "\n", "## 练习\n", "\n", "1. 继续提高`finetune_net`的学习率，模型的准确性如何变化？\n", "2. 在比较实验中进一步调整`finetune_net`和`scratch_net`的超参数。它们的准确性还有不同吗？\n", "3. 将输出层`finetune_net`之前的参数设置为源模型的参数，在训练期间不要更新它们。模型的准确性如何变化？提示：可以使用以下代码。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "83397a47", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:11:54.260642Z", "iopub.status.busy": "2023-08-18T07:11:54.260057Z", "iopub.status.idle": "2023-08-18T07:11:54.264373Z", "shell.execute_reply": "2023-08-18T07:11:54.263562Z"}, "origin_pos": 42, "tab": ["pytorch"]}, "outputs": [], "source": ["for param in finetune_net.parameters():\n", "    param.requires_grad = False"]}, {"cell_type": "markdown", "id": "46fcba55", "metadata": {"origin_pos": 44}, "source": ["4. 事实上，`ImageNet`数据集中有一个“热狗”类别。我们可以通过以下代码获取其输出层中的相应权重参数，但是我们怎样才能利用这个权重参数？\n"]}, {"cell_type": "code", "execution_count": 13, "id": "f2f48879", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:11:54.267934Z", "iopub.status.busy": "2023-08-18T07:11:54.267262Z", "iopub.status.idle": "2023-08-18T07:11:54.274070Z", "shell.execute_reply": "2023-08-18T07:11:54.273270Z"}, "origin_pos": 46, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([1, 512])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["weight = pretrained_net.fc.weight\n", "hotdog_w = torch.split(weight.data, 1, dim=0)[934]\n", "hotdog_w.shape"]}, {"cell_type": "markdown", "id": "e6efed0c", "metadata": {"origin_pos": 49, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2894)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
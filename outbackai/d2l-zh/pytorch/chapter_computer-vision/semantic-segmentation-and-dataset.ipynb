{"cells": [{"cell_type": "markdown", "id": "c418f9a3", "metadata": {"origin_pos": 0}, "source": ["# 语义分割和数据集\n", ":label:`sec_semantic_segmentation`\n", "\n", "在 :numref:`sec_bbox`— :numref:`sec_rcnn`中讨论的目标检测问题中，我们一直使用方形边界框来标注和预测图像中的目标。\n", "本节将探讨*语义分割*（semantic segmentation）问题，它重点关注于如何将图像分割成属于不同语义类别的区域。\n", "与目标检测不同，语义分割可以识别并理解图像中每一个像素的内容：其语义区域的标注和预测是像素级的。\n", " :numref:`fig_segmentation`展示了语义分割中图像有关狗、猫和背景的标签。\n", "与目标检测相比，语义分割标注的像素级的边框显然更加精细。\n", "\n", "![语义分割中图像有关狗、猫和背景的标签](../img/segmentation.svg)\n", ":label:`fig_segmentation`\n", "\n", "## 图像分割和实例分割\n", "\n", "计算机视觉领域还有2个与语义分割相似的重要问题，即*图像分割*（image segmentation）和*实例分割*（instance segmentation）。\n", "我们在这里将它们同语义分割简单区分一下。\n", "\n", "* *图像分割*将图像划分为若干组成区域，这类问题的方法通常利用图像中像素之间的相关性。它在训练时不需要有关图像像素的标签信息，在预测时也无法保证分割出的区域具有我们希望得到的语义。以 :numref:`fig_segmentation`中的图像作为输入，图像分割可能会将狗分为两个区域：一个覆盖以黑色为主的嘴和眼睛，另一个覆盖以黄色为主的其余部分身体。\n", "* *实例分割*也叫*同时检测并分割*（simultaneous detection and segmentation），它研究如何识别图像中各个目标实例的像素级区域。与语义分割不同，实例分割不仅需要区分语义，还要区分不同的目标实例。例如，如果图像中有两条狗，则实例分割需要区分像素属于的两条狗中的哪一条。\n", "\n", "## Pascal VOC2012 语义分割数据集\n", "\n", "[**最重要的语义分割数据集之一是[Pascal VOC2012](http://host.robots.ox.ac.uk/pascal/VOC/voc2012/)。**]\n", "下面我们深入了解一下这个数据集。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "daac4844", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:56:37.603612Z", "iopub.status.busy": "2023-08-18T06:56:37.603086Z", "iopub.status.idle": "2023-08-18T06:56:39.534129Z", "shell.execute_reply": "2023-08-18T06:56:39.533314Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import os\n", "import torch\n", "import torchvision\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "0d328b1a", "metadata": {"origin_pos": 4}, "source": ["数据集的tar文件大约为2GB，所以下载可能需要一段时间。\n", "提取出的数据集位于`../data/VOCdevkit/VOC2012`。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "bd79e9c2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:56:39.538309Z", "iopub.status.busy": "2023-08-18T06:56:39.537665Z", "iopub.status.idle": "2023-08-18T06:58:05.677522Z", "shell.execute_reply": "2023-08-18T06:58:05.676613Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/VOCtrainval_11-May-2012.tar from http://d2l-data.s3-accelerate.amazonaws.com/VOCtrainval_11-May-2012.tar...\n"]}], "source": ["#@save\n", "d2l.DATA_HUB['voc2012'] = (d2l.DATA_URL + 'VOCtrainval_11-May-2012.tar',\n", "                           '4e443f8a2eca6b1dac8a6c57641b67dd40621a49')\n", "\n", "voc_dir = d2l.download_extract('voc2012', 'VOCdevkit/VOC2012')"]}, {"cell_type": "markdown", "id": "6ef71ce7", "metadata": {"origin_pos": 6}, "source": ["进入路径`../data/VOCdevkit/VOC2012`之后，我们可以看到数据集的不同组件。\n", "`ImageSets/Segmentation`路径包含用于训练和测试样本的文本文件，而`JPEGImages`和`SegmentationClass`路径分别存储着每个示例的输入图像和标签。\n", "此处的标签也采用图像格式，其尺寸和它所标注的输入图像的尺寸相同。\n", "此外，标签中颜色相同的像素属于同一个语义类别。\n", "下面将`read_voc_images`函数定义为[**将所有输入的图像和标签读入内存**]。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "b39cdcf9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:05.681499Z", "iopub.status.busy": "2023-08-18T06:58:05.681220Z", "iopub.status.idle": "2023-08-18T06:58:11.042974Z", "shell.execute_reply": "2023-08-18T06:58:11.041446Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def read_voc_images(voc_dir, is_train=True):\n", "    \"\"\"读取所有VOC图像并标注\"\"\"\n", "    txt_fname = os.path.join(voc_dir, 'ImageSets', 'Segmentation',\n", "                             'train.txt' if is_train else 'val.txt')\n", "    mode = torchvision.io.image.ImageReadMode.RGB\n", "    with open(txt_fname, 'r') as f:\n", "        images = f.read().split()\n", "    features, labels = [], []\n", "    for i, fname in enumerate(images):\n", "        features.append(torchvision.io.read_image(os.path.join(\n", "            voc_dir, 'JPEGImages', f'{fname}.jpg')))\n", "        labels.append(torchvision.io.read_image(os.path.join(\n", "            voc_dir, 'SegmentationClass' ,f'{fname}.png'), mode))\n", "    return features, labels\n", "\n", "train_features, train_labels = read_voc_images(voc_dir, True)"]}, {"cell_type": "markdown", "id": "24c325e8", "metadata": {"origin_pos": 10}, "source": ["下面我们[**绘制前5个输入图像及其标签**]。\n", "在标签图像中，白色和黑色分别表示边框和背景，而其他颜色则对应不同的类别。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6de5f355", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:11.058847Z", "iopub.status.busy": "2023-08-18T06:58:11.057829Z", "iopub.status.idle": "2023-08-18T06:58:11.690152Z", "shell.execute_reply": "2023-08-18T06:58:11.689328Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 540x216 with 10 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["n = 5\n", "imgs = train_features[0:n] + train_labels[0:n]\n", "imgs = [img.permute(1,2,0) for img in imgs]\n", "d2l.show_images(imgs, 2, n);"]}, {"cell_type": "markdown", "id": "3eb5e755", "metadata": {"origin_pos": 14}, "source": ["接下来，我们[**列举RGB颜色值和类名**]。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "8127e683", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:11.694111Z", "iopub.status.busy": "2023-08-18T06:58:11.693331Z", "iopub.status.idle": "2023-08-18T06:58:11.700642Z", "shell.execute_reply": "2023-08-18T06:58:11.699767Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "VOC_COLORMAP = [[0, 0, 0], [128, 0, 0], [0, 128, 0], [128, 128, 0],\n", "                [0, 0, 128], [128, 0, 128], [0, 128, 128], [128, 128, 128],\n", "                [64, 0, 0], [192, 0, 0], [64, 128, 0], [192, 128, 0],\n", "                [64, 0, 128], [192, 0, 128], [64, 128, 128], [192, 128, 128],\n", "                [0, 64, 0], [128, 64, 0], [0, 192, 0], [128, 192, 0],\n", "                [0, 64, 128]]\n", "\n", "#@save\n", "VOC_CLASSES = ['background', 'aeroplane', 'bicycle', 'bird', 'boat',\n", "               'bottle', 'bus', 'car', 'cat', 'chair', 'cow',\n", "               'diningtable', 'dog', 'horse', 'motorbike', 'person',\n", "               'potted plant', 'sheep', 'sofa', 'train', 'tv/monitor']"]}, {"cell_type": "markdown", "id": "46f61b54", "metadata": {"origin_pos": 16}, "source": ["通过上面定义的两个常量，我们可以方便地[**查找标签中每个像素的类索引**]。\n", "我们定义了`voc_colormap2label`函数来构建从上述RGB颜色值到类别索引的映射，而`voc_label_indices`函数将RGB值映射到在Pascal VOC2012数据集中的类别索引。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "36949805", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:11.704036Z", "iopub.status.busy": "2023-08-18T06:58:11.703567Z", "iopub.status.idle": "2023-08-18T06:58:11.710356Z", "shell.execute_reply": "2023-08-18T06:58:11.709284Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def voc_colormap2label():\n", "    \"\"\"构建从RGB到VOC类别索引的映射\"\"\"\n", "    colormap2label = torch.zeros(256 ** 3, dtype=torch.long)\n", "    for i, colormap in enumerate(VOC_COLORMAP):\n", "        colormap2label[\n", "            (colormap[0] * 256 + colormap[1]) * 256 + colormap[2]] = i\n", "    return colormap2label\n", "\n", "#@save\n", "def voc_label_indices(colormap, colormap2label):\n", "    \"\"\"将VOC标签中的RGB值映射到它们的类别索引\"\"\"\n", "    colormap = colormap.permute(1, 2, 0).numpy().astype('int32')\n", "    idx = ((colormap[:, :, 0] * 256 + colormap[:, :, 1]) * 256\n", "           + colormap[:, :, 2])\n", "    return colormap2label[idx]"]}, {"cell_type": "markdown", "id": "93e6e896", "metadata": {"origin_pos": 20}, "source": ["[**例如**]，在第一张样本图像中，飞机头部区域的类别索引为1，而背景索引为0。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "8aea3591", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:11.713954Z", "iopub.status.busy": "2023-08-18T06:58:11.713226Z", "iopub.status.idle": "2023-08-18T06:58:11.740434Z", "shell.execute_reply": "2023-08-18T06:58:11.739332Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(tensor([[0, 0, 0, 0, 0, 0, 0, 0, 0, 1],\n", "         [0, 0, 0, 0, 0, 0, 0, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 0, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 1, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 0, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 0, 0, 0, 1, 1]]),\n", " 'aeroplane')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["y = voc_label_indices(train_labels[0], voc_colormap2label())\n", "y[105:115, 130:140], VOC_CLASSES[1]"]}, {"cell_type": "markdown", "id": "18bcf344", "metadata": {"origin_pos": 22}, "source": ["### 预处理数据\n", "\n", "在之前的实验，例如 :numref:`sec_alexnet`— :numref:`sec_googlenet`中，我们通过再缩放图像使其符合模型的输入形状。\n", "然而在语义分割中，这样做需要将预测的像素类别重新映射回原始尺寸的输入图像。\n", "这样的映射可能不够精确，尤其在不同语义的分割区域。\n", "为了避免这个问题，我们将图像裁剪为固定尺寸，而不是再缩放。\n", "具体来说，我们[**使用图像增广中的随机裁剪，裁剪输入图像和标签的相同区域**]。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "747378f7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:11.745126Z", "iopub.status.busy": "2023-08-18T06:58:11.744585Z", "iopub.status.idle": "2023-08-18T06:58:11.749914Z", "shell.execute_reply": "2023-08-18T06:58:11.749104Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def voc_rand_crop(feature, label, height, width):\n", "    \"\"\"随机裁剪特征和标签图像\"\"\"\n", "    rect = torchvision.transforms.RandomCrop.get_params(\n", "        feature, (height, width))\n", "    feature = torchvision.transforms.functional.crop(feature, *rect)\n", "    label = torchvision.transforms.functional.crop(label, *rect)\n", "    return feature, label"]}, {"cell_type": "code", "execution_count": 9, "id": "19f90bad", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:11.754060Z", "iopub.status.busy": "2023-08-18T06:58:11.753536Z", "iopub.status.idle": "2023-08-18T06:58:12.181252Z", "shell.execute_reply": "2023-08-18T06:58:12.180465Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 540x216 with 10 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["imgs = []\n", "for _ in range(n):\n", "    imgs += voc_rand_crop(train_features[0], train_labels[0], 200, 300)\n", "\n", "imgs = [img.permute(1, 2, 0) for img in imgs]\n", "d2l.show_images(imgs[::2] + imgs[1::2], 2, n);"]}, {"cell_type": "markdown", "id": "63a6b7aa", "metadata": {"origin_pos": 29}, "source": ["### [**自定义语义分割数据集类**]\n", "\n", "我们通过继承高级API提供的`Dataset`类，自定义了一个语义分割数据集类`VOCSegDataset`。\n", "通过实现`__getitem__`函数，我们可以任意访问数据集中索引为`idx`的输入图像及其每个像素的类别索引。\n", "由于数据集中有些图像的尺寸可能小于随机裁剪所指定的输出尺寸，这些样本可以通过自定义的`filter`函数移除掉。\n", "此外，我们还定义了`normalize_image`函数，从而对输入图像的RGB三个通道的值分别做标准化。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "82139d78", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:12.184986Z", "iopub.status.busy": "2023-08-18T06:58:12.184395Z", "iopub.status.idle": "2023-08-18T06:58:12.192756Z", "shell.execute_reply": "2023-08-18T06:58:12.192015Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class VOCSegDataset(torch.utils.data.Dataset):\n", "    \"\"\"一个用于加载VOC数据集的自定义数据集\"\"\"\n", "\n", "    def __init__(self, is_train, crop_size, voc_dir):\n", "        self.transform = torchvision.transforms.Normalize(\n", "            mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])\n", "        self.crop_size = crop_size\n", "        features, labels = read_voc_images(voc_dir, is_train=is_train)\n", "        self.features = [self.normalize_image(feature)\n", "                         for feature in self.filter(features)]\n", "        self.labels = self.filter(labels)\n", "        self.colormap2label = voc_colormap2label()\n", "        print('read ' + str(len(self.features)) + ' examples')\n", "\n", "    def normalize_image(self, img):\n", "        return self.transform(img.float() / 255)\n", "\n", "    def filter(self, imgs):\n", "        return [img for img in imgs if (\n", "            img.shape[1] >= self.crop_size[0] and\n", "            img.shape[2] >= self.crop_size[1])]\n", "\n", "    def __getitem__(self, idx):\n", "        feature, label = voc_rand_crop(self.features[idx], self.labels[idx],\n", "                                       *self.crop_size)\n", "        return (feature, voc_label_indices(label, self.colormap2label))\n", "\n", "    def __len__(self):\n", "        return len(self.features)"]}, {"cell_type": "markdown", "id": "d079ac1f", "metadata": {"origin_pos": 33}, "source": ["### [**读取数据集**]\n", "\n", "我们通过自定义的`VOCSegDataset`类来分别创建训练集和测试集的实例。\n", "假设我们指定随机裁剪的输出图像的形状为$320\\times 480$，\n", "下面我们可以查看训练集和测试集所保留的样本个数。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "79b88eeb", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:12.196249Z", "iopub.status.busy": "2023-08-18T06:58:12.195713Z", "iopub.status.idle": "2023-08-18T06:58:24.376223Z", "shell.execute_reply": "2023-08-18T06:58:24.375033Z"}, "origin_pos": 34, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["read 1114 examples\n"]}, {"name": "stdout", "output_type": "stream", "text": ["read 1078 examples\n"]}], "source": ["crop_size = (320, 480)\n", "voc_train = VOCSegDataset(True, crop_size, voc_dir)\n", "voc_test = VOCSegDataset(False, crop_size, voc_dir)"]}, {"cell_type": "markdown", "id": "74e8ab37", "metadata": {"origin_pos": 35}, "source": ["设批量大小为64，我们定义训练集的迭代器。\n", "打印第一个小批量的形状会发现：与图像分类或目标检测不同，这里的标签是一个三维数组。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "0c447d3b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:24.381208Z", "iopub.status.busy": "2023-08-18T06:58:24.380885Z", "iopub.status.idle": "2023-08-18T06:58:26.002445Z", "shell.execute_reply": "2023-08-18T06:58:26.000841Z"}, "origin_pos": 37, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([64, 3, 320, 480])\n", "torch.<PERSON><PERSON>([64, 320, 480])\n"]}], "source": ["batch_size = 64\n", "train_iter = torch.utils.data.DataLoader(voc_train, batch_size, shuffle=True,\n", "                                    drop_last=True,\n", "                                    num_workers=d2l.get_dataloader_workers())\n", "for X, Y in train_iter:\n", "    print(X.shape)\n", "    print(Y.shape)\n", "    break"]}, {"cell_type": "markdown", "id": "b3c5ab46", "metadata": {"origin_pos": 39}, "source": ["### [**整合所有组件**]\n", "\n", "最后，我们定义以下`load_data_voc`函数来下载并读取Pascal VOC2012语义分割数据集。\n", "它返回训练集和测试集的数据迭代器。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "ce15ca32", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:26.008578Z", "iopub.status.busy": "2023-08-18T06:58:26.007726Z", "iopub.status.idle": "2023-08-18T06:58:26.017217Z", "shell.execute_reply": "2023-08-18T06:58:26.015893Z"}, "origin_pos": 41, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def load_data_voc(batch_size, crop_size):\n", "    \"\"\"加载VOC语义分割数据集\"\"\"\n", "    voc_dir = d2l.download_extract('voc2012', os.path.join(\n", "        'VOCdevkit', 'VOC2012'))\n", "    num_workers = d2l.get_dataloader_workers()\n", "    train_iter = torch.utils.data.DataLoader(\n", "        VOCSegDataset(True, crop_size, voc_dir), batch_size,\n", "        shuffle=True, drop_last=True, num_workers=num_workers)\n", "    test_iter = torch.utils.data.DataLoader(\n", "        VOCSegDataset(False, crop_size, voc_dir), batch_size,\n", "        drop_last=True, num_workers=num_workers)\n", "    return train_iter, test_iter"]}, {"cell_type": "markdown", "id": "2ea20dfd", "metadata": {"origin_pos": 43}, "source": ["## 小结\n", "\n", "* 语义分割通过将图像划分为属于不同语义类别的区域，来识别并理解图像中像素级别的内容。\n", "* 语义分割的一个重要的数据集叫做Pascal VOC2012。\n", "* 由于语义分割的输入图像和标签在像素上一一对应，输入图像会被随机裁剪为固定尺寸而不是缩放。\n", "\n", "## 练习\n", "\n", "1. 如何在自动驾驶和医疗图像诊断中应用语义分割？还能想到其他领域的应用吗？\n", "1. 回想一下 :numref:`sec_image_augmentation`中对数据增强的描述。图像分类中使用的哪种图像增强方法是难以用于语义分割的？\n"]}, {"cell_type": "markdown", "id": "17f10497", "metadata": {"origin_pos": 45, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/3295)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "fec74aec", "metadata": {"origin_pos": 0}, "source": ["# 实战 Kaggle 比赛：图像分类 (CIFAR-10)\n", ":label:`sec_kaggle_cifar10`\n", "\n", "之前几节中，我们一直在使用深度学习框架的高级API直接获取张量格式的图像数据集。\n", "但是在实践中，图像数据集通常以图像文件的形式出现。\n", "本节将从原始图像文件开始，然后逐步组织、读取并将它们转换为张量格式。\n", "\n", "我们在 :numref:`sec_image_augmentation`中对CIFAR-10数据集做了一个实验。CIFAR-10是计算机视觉领域中的一个重要的数据集。\n", "本节将运用我们在前几节中学到的知识来参加CIFAR-10图像分类问题的Kaggle竞赛，(**比赛的网址是https://www.kaggle.com/c/cifar-10**)。\n", "\n", " :numref:`fig_kaggle_cifar10`显示了竞赛网站页面上的信息。\n", "为了能提交结果，首先需要注册一个Kaggle账户。\n", "\n", "![CIFAR-10 图像分类竞赛页面上的信息。竞赛用的数据集可通过点击“Data”选项卡获取。](../img/kaggle-cifar10.png)\n", ":width:`600px`\n", ":label:`fig_kaggle_cifar10`\n", "\n", "首先，导入竞赛所需的包和模块。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f7b4fa3c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:12.123905Z", "iopub.status.busy": "2023-08-18T07:02:12.123323Z", "iopub.status.idle": "2023-08-18T07:02:14.203247Z", "shell.execute_reply": "2023-08-18T07:02:14.202358Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import collections\n", "import math\n", "import os\n", "import shutil\n", "import pandas as pd\n", "import torch\n", "import torchvision\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "11f6b9d7", "metadata": {"origin_pos": 4}, "source": ["## 获取并组织数据集\n", "\n", "比赛数据集分为训练集和测试集，其中训练集包含50000张、测试集包含300000张图像。\n", "在测试集中，10000张图像将被用于评估，而剩下的290000张图像将不会被进行评估，包含它们只是为了防止手动标记测试集并提交标记结果。\n", "两个数据集中的图像都是png格式，高度和宽度均为32像素并有三个颜色通道（RGB）。\n", "这些图片共涵盖10个类别：飞机、汽车、鸟类、猫、鹿、狗、青蛙、马、船和卡车。\n", " :numref:`fig_kaggle_cifar10`的左上角显示了数据集中飞机、汽车和鸟类的一些图像。\n", "\n", "### 下载数据集\n", "\n", "登录Kaggle后，我们可以点击 :numref:`fig_kaggle_cifar10`中显示的CIFAR-10图像分类竞赛网页上的“Data”选项卡，然后单击“Download All”按钮下载数据集。\n", "在`../data`中解压下载的文件并在其中解压缩`train.7z`和`test.7z`后，在以下路径中可以找到整个数据集：\n", "\n", "* `../data/cifar-10/train/[1-50000].png`\n", "* `../data/cifar-10/test/[1-300000].png`\n", "* `../data/cifar-10/trainLabels.csv`\n", "* `../data/cifar-10/sampleSubmission.csv`\n", "\n", "`train`和`test`文件夹分别包含训练和测试图像，`trainLabels.csv`含有训练图像的标签，\n", "`sample_submission.csv`是提交文件的范例。\n", "\n", "为了便于入门，[**我们提供包含前1000个训练图像和5个随机测试图像的数据集的小规模样本**]。\n", "要使用Kaggle竞赛的完整数据集，需要将以下`demo`变量设置为`False`。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "7ae59ae9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.207394Z", "iopub.status.busy": "2023-08-18T07:02:14.207019Z", "iopub.status.idle": "2023-08-18T07:02:14.623037Z", "shell.execute_reply": "2023-08-18T07:02:14.622201Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/kaggle_cifar10_tiny.zip from http://d2l-data.s3-accelerate.amazonaws.com/kaggle_cifar10_tiny.zip...\n"]}], "source": ["#@save\n", "d2l.DATA_HUB['cifar10_tiny'] = (d2l.DATA_URL + 'kaggle_cifar10_tiny.zip',\n", "                                '2068874e4b9a9f0fb07ebe0ad2b29754449ccacd')\n", "\n", "# 如果使用完整的Kaggle竞赛的数据集，设置demo为False\n", "demo = True\n", "\n", "if demo:\n", "    data_dir = d2l.download_extract('cifar10_tiny')\n", "else:\n", "    data_dir = '../data/cifar-10/'"]}, {"cell_type": "markdown", "id": "56ef995f", "metadata": {"origin_pos": 6}, "source": ["### [**整理数据集**]\n", "\n", "我们需要整理数据集来训练和测试模型。\n", "首先，我们用以下函数读取CSV文件中的标签，它返回一个字典，该字典将文件名中不带扩展名的部分映射到其标签。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "24b4fdfb", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.627215Z", "iopub.status.busy": "2023-08-18T07:02:14.626653Z", "iopub.status.idle": "2023-08-18T07:02:14.634237Z", "shell.execute_reply": "2023-08-18T07:02:14.633299Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# 训练样本 : 1000\n", "# 类别 : 10\n"]}], "source": ["#@save\n", "def read_csv_labels(fname):\n", "    \"\"\"读取fname来给标签字典返回一个文件名\"\"\"\n", "    with open(fname, 'r') as f:\n", "        # 跳过文件头行(列名)\n", "        lines = f.readlines()[1:]\n", "    tokens = [l.rstrip().split(',') for l in lines]\n", "    return dict(((name, label) for name, label in tokens))\n", "\n", "labels = read_csv_labels(os.path.join(data_dir, 'trainLabels.csv'))\n", "print('# 训练样本 :', len(labels))\n", "print('# 类别 :', len(set(labels.values())))"]}, {"cell_type": "markdown", "id": "3359e04e", "metadata": {"origin_pos": 8}, "source": ["接下来，我们定义`reorg_train_valid`函数来[**将验证集从原始的训练集中拆分出来**]。\n", "此函数中的参数`valid_ratio`是验证集中的样本数与原始训练集中的样本数之比。\n", "更具体地说，令$n$等于样本最少的类别中的图像数量，而$r$是比率。\n", "验证集将为每个类别拆分出$\\max(\\lfloor nr\\rfloor,1)$张图像。\n", "让我们以`valid_ratio=0.1`为例，由于原始的训练集有50000张图像，因此`train_valid_test/train`路径中将有45000张图像用于训练，而剩下5000张图像将作为路径`train_valid_test/valid`中的验证集。\n", "组织数据集后，同类别的图像将被放置在同一文件夹下。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "fbfbac4f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.637882Z", "iopub.status.busy": "2023-08-18T07:02:14.637608Z", "iopub.status.idle": "2023-08-18T07:02:14.644987Z", "shell.execute_reply": "2023-08-18T07:02:14.644218Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def copyfile(filename, target_dir):\n", "    \"\"\"将文件复制到目标目录\"\"\"\n", "    os.makedirs(target_dir, exist_ok=True)\n", "    shutil.copy(filename, target_dir)\n", "\n", "#@save\n", "def reorg_train_valid(data_dir, labels, valid_ratio):\n", "    \"\"\"将验证集从原始的训练集中拆分出来\"\"\"\n", "    # 训练数据集中样本最少的类别中的样本数\n", "    n = collections.Counter(labels.values()).most_common()[-1][1]\n", "    # 验证集中每个类别的样本数\n", "    n_valid_per_label = max(1, math.floor(n * valid_ratio))\n", "    label_count = {}\n", "    for train_file in os.listdir(os.path.join(data_dir, 'train')):\n", "        label = labels[train_file.split('.')[0]]\n", "        fname = os.path.join(data_dir, 'train', train_file)\n", "        copyfile(fname, os.path.join(data_dir, 'train_valid_test',\n", "                                     'train_valid', label))\n", "        if label not in label_count or label_count[label] < n_valid_per_label:\n", "            copyfile(fname, os.path.join(data_dir, 'train_valid_test',\n", "                                         'valid', label))\n", "            label_count[label] = label_count.get(label, 0) + 1\n", "        else:\n", "            copyfile(fname, os.path.join(data_dir, 'train_valid_test',\n", "                                         'train', label))\n", "    return n_valid_per_label"]}, {"cell_type": "markdown", "id": "4d89ac6f", "metadata": {"origin_pos": 10}, "source": ["下面的`reorg_test`函数用来[**在预测期间整理测试集，以方便读取**]。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "9ad6d005", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.648388Z", "iopub.status.busy": "2023-08-18T07:02:14.648058Z", "iopub.status.idle": "2023-08-18T07:02:14.653344Z", "shell.execute_reply": "2023-08-18T07:02:14.652542Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def reorg_test(data_dir):\n", "    \"\"\"在预测期间整理测试集，以方便读取\"\"\"\n", "    for test_file in os.listdir(os.path.join(data_dir, 'test')):\n", "        copyfile(os.path.join(data_dir, 'test', test_file),\n", "                 os.path.join(data_dir, 'train_valid_test', 'test',\n", "                              'unknown'))"]}, {"cell_type": "markdown", "id": "da9e6d5a", "metadata": {"origin_pos": 12}, "source": ["最后，我们使用一个函数来[**调用前面定义的函数**]`read_csv_labels`、`reorg_train_valid`和`reorg_test`。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "37a42208", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.657456Z", "iopub.status.busy": "2023-08-18T07:02:14.656944Z", "iopub.status.idle": "2023-08-18T07:02:14.661187Z", "shell.execute_reply": "2023-08-18T07:02:14.660400Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["def reorg_cifar10_data(data_dir, valid_ratio):\n", "    labels = read_csv_labels(os.path.join(data_dir, 'trainLabels.csv'))\n", "    reorg_train_valid(data_dir, labels, valid_ratio)\n", "    reorg_test(data_dir)"]}, {"cell_type": "markdown", "id": "b62b33fa", "metadata": {"origin_pos": 14}, "source": ["在这里，我们只将样本数据集的批量大小设置为32。\n", "在实际训练和测试中，应该使用Kaggle竞赛的完整数据集，并将`batch_size`设置为更大的整数，例如128。\n", "我们将10％的训练样本作为调整超参数的验证集。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "868eb62b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.664878Z", "iopub.status.busy": "2023-08-18T07:02:14.664240Z", "iopub.status.idle": "2023-08-18T07:02:14.931508Z", "shell.execute_reply": "2023-08-18T07:02:14.930669Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["batch_size = 32 if demo else 128\n", "valid_ratio = 0.1\n", "reorg_cifar10_data(data_dir, valid_ratio)"]}, {"cell_type": "markdown", "id": "31458131", "metadata": {"origin_pos": 16}, "source": ["## [**图像增广**]\n", "\n", "我们使用图像增广来解决过拟合的问题。例如在训练中，我们可以随机水平翻转图像。\n", "我们还可以对彩色图像的三个RGB通道执行标准化。\n", "下面，我们列出了其中一些可以调整的操作。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "300ef249", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.935568Z", "iopub.status.busy": "2023-08-18T07:02:14.934993Z", "iopub.status.idle": "2023-08-18T07:02:14.940662Z", "shell.execute_reply": "2023-08-18T07:02:14.939875Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [], "source": ["transform_train = torchvision.transforms.Compose([\n", "    # 在高度和宽度上将图像放大到40像素的正方形\n", "    torchvision.<PERSON>.<PERSON><PERSON><PERSON>(40),\n", "    # 随机裁剪出一个高度和宽度均为40像素的正方形图像，\n", "    # 生成一个面积为原始图像面积0.64～1倍的小正方形，\n", "    # 然后将其缩放为高度和宽度均为32像素的正方形\n", "    torchvision.transforms.RandomResizedCrop(32, scale=(0.64, 1.0),\n", "                                                   ratio=(1.0, 1.0)),\n", "    torchvision.transforms.RandomHorizontalFlip(),\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    # 标准化图像的每个通道\n", "    torchvision.transforms.Normalize([0.4914, 0.4822, 0.4465],\n", "                                     [0.2023, 0.1994, 0.2010])])"]}, {"cell_type": "markdown", "id": "694c31d1", "metadata": {"origin_pos": 20}, "source": ["在测试期间，我们只对图像执行标准化，以消除评估结果中的随机性。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "6bd19592", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.945514Z", "iopub.status.busy": "2023-08-18T07:02:14.944858Z", "iopub.status.idle": "2023-08-18T07:02:14.949240Z", "shell.execute_reply": "2023-08-18T07:02:14.948438Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["transform_test = torchvision.transforms.Compose([\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    torchvision.transforms.Normalize([0.4914, 0.4822, 0.4465],\n", "                                     [0.2023, 0.1994, 0.2010])])"]}, {"cell_type": "markdown", "id": "c06d3e36", "metadata": {"origin_pos": 24}, "source": ["## 读取数据集\n", "\n", "接下来，我们[**读取由原始图像组成的数据集**]，每个样本都包括一张图片和一个标签。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "c1815173", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.952392Z", "iopub.status.busy": "2023-08-18T07:02:14.952109Z", "iopub.status.idle": "2023-08-18T07:02:14.966044Z", "shell.execute_reply": "2023-08-18T07:02:14.965283Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [], "source": ["train_ds, train_valid_ds = [torchvision.datasets.ImageFolder(\n", "    os.path.join(data_dir, 'train_valid_test', folder),\n", "    transform=transform_train) for folder in ['train', 'train_valid']]\n", "\n", "valid_ds, test_ds = [torchvision.datasets.ImageFolder(\n", "    os.path.join(data_dir, 'train_valid_test', folder),\n", "    transform=transform_test) for folder in ['valid', 'test']]"]}, {"cell_type": "markdown", "id": "f22934d1", "metadata": {"origin_pos": 28}, "source": ["在训练期间，我们需要[**指定上面定义的所有图像增广操作**]。\n", "当验证集在超参数调整过程中用于模型评估时，不应引入图像增广的随机性。\n", "在最终预测之前，我们根据训练集和验证集组合而成的训练模型进行训练，以充分利用所有标记的数据。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "d9528fff", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.969294Z", "iopub.status.busy": "2023-08-18T07:02:14.968975Z", "iopub.status.idle": "2023-08-18T07:02:14.974247Z", "shell.execute_reply": "2023-08-18T07:02:14.973489Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [], "source": ["train_iter, train_valid_iter = [torch.utils.data.DataLoader(\n", "    dataset, batch_size, shuffle=True, drop_last=True)\n", "    for dataset in (train_ds, train_valid_ds)]\n", "\n", "valid_iter = torch.utils.data.DataLoader(valid_ds, batch_size, shuffle=False,\n", "                                         drop_last=True)\n", "\n", "test_iter = torch.utils.data.DataLoader(test_ds, batch_size, shuffle=False,\n", "                                        drop_last=False)"]}, {"cell_type": "markdown", "id": "a40cd7e3", "metadata": {"origin_pos": 32}, "source": ["## 定义[**模型**]\n"]}, {"cell_type": "markdown", "id": "8a4e0897", "metadata": {"origin_pos": 38, "tab": ["pytorch"]}, "source": ["我们定义了 :numref:`sec_resnet`中描述的Resnet-18模型。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "c35c8dca", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.977846Z", "iopub.status.busy": "2023-08-18T07:02:14.977285Z", "iopub.status.idle": "2023-08-18T07:02:14.981584Z", "shell.execute_reply": "2023-08-18T07:02:14.980732Z"}, "origin_pos": 41, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_net():\n", "    num_classes = 10\n", "    net = d2l.resnet18(num_classes, 3)\n", "    return net\n", "\n", "loss = nn.CrossEntropyLoss(reduction=\"none\")"]}, {"cell_type": "markdown", "id": "e38ab14a", "metadata": {"origin_pos": 43}, "source": ["## 定义[**训练函数**]\n", "\n", "我们将根据模型在验证集上的表现来选择模型并调整超参数。\n", "下面我们定义了模型训练函数`train`。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "e082315c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.984795Z", "iopub.status.busy": "2023-08-18T07:02:14.984512Z", "iopub.status.idle": "2023-08-18T07:02:14.994288Z", "shell.execute_reply": "2023-08-18T07:02:14.993512Z"}, "origin_pos": 45, "tab": ["pytorch"]}, "outputs": [], "source": ["def train(net, train_iter, valid_iter, num_epochs, lr, wd, devices, lr_period,\n", "          lr_decay):\n", "    trainer = torch.optim.SGD(net.parameters(), lr=lr, momentum=0.9,\n", "                              weight_decay=wd)\n", "    scheduler = torch.optim.lr_scheduler.Step<PERSON>(trainer, lr_period, lr_decay)\n", "    num_batches, timer = len(train_iter), d2l.Timer()\n", "    legend = ['train loss', 'train acc']\n", "    if valid_iter is not None:\n", "        legend.append('valid acc')\n", "    animator = d2l.Animator(xlabel='epoch', xlim=[1, num_epochs],\n", "                            legend=legend)\n", "    net = nn.DataParallel(net, device_ids=devices).to(devices[0])\n", "    for epoch in range(num_epochs):\n", "        net.train()\n", "        metric = d2l.Accumulator(3)\n", "        for i, (features, labels) in enumerate(train_iter):\n", "            timer.start()\n", "            l, acc = d2l.train_batch_ch13(net, features, labels,\n", "                                          loss, trainer, devices)\n", "            metric.add(l, acc, labels.shape[0])\n", "            timer.stop()\n", "            if (i + 1) % (num_batches // 5) == 0 or i == num_batches - 1:\n", "                animator.add(epoch + (i + 1) / num_batches,\n", "                             (metric[0] / metric[2], metric[1] / metric[2],\n", "                              None))\n", "        if valid_iter is not None:\n", "            valid_acc = d2l.evaluate_accuracy_gpu(net, valid_iter)\n", "            animator.add(epoch + 1, (None, None, valid_acc))\n", "        scheduler.step()\n", "    measures = (f'train loss {metric[0] / metric[2]:.3f}, '\n", "                f'train acc {metric[1] / metric[2]:.3f}')\n", "    if valid_iter is not None:\n", "        measures += f', valid acc {valid_acc:.3f}'\n", "    print(measures + f'\\n{metric[2] * num_epochs / timer.sum():.1f}'\n", "          f' examples/sec on {str(devices)}')"]}, {"cell_type": "markdown", "id": "41c3007d", "metadata": {"origin_pos": 47}, "source": ["## [**训练和验证模型**]\n", "\n", "现在，我们可以训练和验证模型了，而以下所有超参数都可以调整。\n", "例如，我们可以增加周期的数量。当`lr_period`和`lr_decay`分别设置为4和0.9时，优化算法的学习速率将在每4个周期乘以0.9。\n", "为便于演示，我们在这里只训练20个周期。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "267a469f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:02:14.997959Z", "iopub.status.busy": "2023-08-18T07:02:14.997526Z", "iopub.status.idle": "2023-08-18T07:03:21.092598Z", "shell.execute_reply": "2023-08-18T07:03:21.091331Z"}, "origin_pos": 49, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train loss 0.668, train acc 0.761, valid acc 0.406\n", "758.4 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"180.65625pt\" viewBox=\"0 0 238.**********.65625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:03:21.035440</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 180.65625 \n", "L 238.**********.65625 \n", "L 238.965625 0 \n", "L 0 0 \n", "L 0 180.65625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 71.218914 143.1 \n", "L 71.218914 7.2 \n", "\" clip-path=\"url(#pc21f047930)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m51a9058107\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m51a9058107\" x=\"71.218914\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(68.037664 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 122.613651 143.1 \n", "L 122.613651 7.2 \n", "\" clip-path=\"url(#pc21f047930)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m51a9058107\" x=\"122.613651\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(116.251151 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 174.008388 143.1 \n", "L 174.008388 7.2 \n", "\" clip-path=\"url(#pc21f047930)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m51a9058107\" x=\"174.008388\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(167.645888 157.698438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 225.**********.1 \n", "L 225.403125 7.2 \n", "\" clip-path=\"url(#pc21f047930)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m51a9058107\" x=\"225.403125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(219.040625 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 171.376563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 30.103125 117.37889 \n", "L 225.403125 117.37889 \n", "\" clip-path=\"url(#pc21f047930)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"me77334c9db\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me77334c9db\" x=\"30.103125\" y=\"117.37889\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 121.178109)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.103125 89.459122 \n", "L 225.403125 89.459122 \n", "\" clip-path=\"url(#pc21f047930)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#me77334c9db\" x=\"30.103125\" y=\"89.459122\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 93.258341)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 61.539354 \n", "L 225.403125 61.539354 \n", "\" clip-path=\"url(#pc21f047930)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#me77334c9db\" x=\"30.103125\" y=\"61.539354\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 65.338573)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 33.619586 \n", "L 225.403125 33.619586 \n", "\" clip-path=\"url(#pc21f047930)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#me77334c9db\" x=\"30.103125\" y=\"33.619586\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 37.418805)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 21.659704 13.377273 \n", "L 23.49523 17.058103 \n", "L 25.330757 20.476629 \n", "L 27.166283 21.189885 \n", "L 29.001809 21.197581 \n", "L 30.103125 20.887965 \n", "L 31.938651 30.955095 \n", "L 33.774178 31.416385 \n", "L 35.609704 32.162012 \n", "L 37.44523 32.44158 \n", "L 39.280757 32.576088 \n", "L 40.382072 32.140592 \n", "L 42.217599 41.184651 \n", "L 44.053125 38.583582 \n", "L 45.888651 41.243025 \n", "L 47.724178 41.497549 \n", "L 49.559704 41.2593 \n", "L 50.66102 42.05378 \n", "L 52.496546 43.491002 \n", "L 54.332072 44.798456 \n", "L 56.167599 42.863455 \n", "L 58.003125 43.28702 \n", "L 59.838651 43.419773 \n", "L 60.939967 44.398147 \n", "L 62.775493 53.814375 \n", "L 64.61102 55.539045 \n", "L 66.446546 54.862098 \n", "L 68.282072 56.461532 \n", "L 70.117599 55.31123 \n", "L 71.218914 56.415554 \n", "L 73.054441 54.788989 \n", "L 74.889967 55.67534 \n", "L 76.725493 56.714666 \n", "L 78.56102 58.016641 \n", "L 80.396546 59.300361 \n", "L 81.497862 59.855901 \n", "L 83.333388 48.915857 \n", "L 85.168914 53.602168 \n", "L 87.004441 56.615775 \n", "L 88.839967 58.061744 \n", "L 90.675493 59.578907 \n", "L 91.776809 59.918047 \n", "L 93.612336 60.601917 \n", "L 95.447862 62.168333 \n", "L 97.283388 64.705865 \n", "L 99.118914 65.927031 \n", "L 100.954441 65.92581 \n", "L 102.055757 64.966943 \n", "L 103.891283 72.713706 \n", "L 105.726809 69.801495 \n", "L 107.562336 65.780008 \n", "L 109.397862 65.286905 \n", "L 111.233388 65.043146 \n", "L 112.334704 65.878259 \n", "L 114.17023 73.10415 \n", "L 116.005757 77.831543 \n", "L 117.841283 75.753761 \n", "L 119.676809 75.36908 \n", "L 121.512336 75.365976 \n", "L 122.613651 74.67023 \n", "L 124.449178 76.738079 \n", "L 126.284704 72.958695 \n", "L 128.12023 74.828085 \n", "L 129.955757 75.110493 \n", "L 131.791283 75.08018 \n", "L 132.892599 74.704957 \n", "L 134.728125 81.113825 \n", "L 136.563651 79.852621 \n", "L 138.399178 79.56583 \n", "L 140.234704 79.787455 \n", "L 142.07023 78.797114 \n", "L 143.171546 78.417472 \n", "L 145.007072 86.746432 \n", "L 146.842599 84.609997 \n", "L 148.678125 83.173978 \n", "L 150.513651 84.102497 \n", "L 152.349178 84.136797 \n", "L 153.450493 83.772832 \n", "L 155.28602 86.608674 \n", "L 157.121546 90.523319 \n", "L 158.957072 91.184755 \n", "L 160.792599 89.641162 \n", "L 162.628125 88.254533 \n", "L 163.729441 88.117803 \n", "L 165.564967 84.4993 \n", "L 167.400493 87.819867 \n", "L 169.23602 89.546247 \n", "L 171.071546 88.606806 \n", "L 172.907072 87.787815 \n", "L 174.008388 87.170349 \n", "L 175.843914 104.214954 \n", "L 177.679441 97.720041 \n", "L 179.514967 98.049481 \n", "L 181.350493 98.637458 \n", "L 183.18602 97.896169 \n", "L 184.287336 97.077901 \n", "L 186.122862 112.597376 \n", "L 187.958388 109.78813 \n", "L 189.793914 108.51739 \n", "L 191.629441 105.433438 \n", "L 193.464967 105.094368 \n", "L 194.566283 104.190858 \n", "L 196.401809 101.102527 \n", "L 198.237336 104.136975 \n", "L 200.072862 100.523204 \n", "L 201.908388 101.84188 \n", "L 203.743914 101.500755 \n", "L 204.84523 101.18617 \n", "L 206.680757 100.439922 \n", "L 208.516283 100.507214 \n", "L 210.351809 99.580581 \n", "L 212.187336 101.728116 \n", "L 214.022862 100.995764 \n", "L 215.124178 100.934687 \n", "L 216.959704 110.177181 \n", "L 218.79523 109.562164 \n", "L 220.630757 108.536238 \n", "L 222.466283 108.543588 \n", "L 224.301809 107.960047 \n", "L 225.403125 108.00081 \n", "\" clip-path=\"url(#pc21f047930)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 21.659704 136.922727 \n", "L 23.49523 136.050235 \n", "L 25.330757 135.177742 \n", "L 27.166283 134.915994 \n", "L 29.001809 134.13075 \n", "L 30.103125 134.018573 \n", "L 31.938651 130.989777 \n", "L 33.774178 129.593788 \n", "L 35.609704 128.663129 \n", "L 37.44523 128.285049 \n", "L 39.280757 128.546797 \n", "L 40.382072 128.845937 \n", "L 42.217599 128.546797 \n", "L 44.053125 129.070293 \n", "L 45.888651 128.314132 \n", "L 47.724178 128.110551 \n", "L 49.559704 127.988402 \n", "L 50.66102 128.409691 \n", "L 52.496546 125.056826 \n", "L 54.332072 125.580322 \n", "L 56.167599 126.685479 \n", "L 58.003125 126.452814 \n", "L 59.838651 126.103817 \n", "L 60.939967 125.729892 \n", "L 62.775493 125.75482 \n", "L 64.61102 123.660838 \n", "L 66.446546 123.660838 \n", "L 68.282072 122.701096 \n", "L 70.117599 122.683646 \n", "L 71.218914 122.115279 \n", "L 73.054441 125.405823 \n", "L 74.889967 124.184333 \n", "L 76.725493 123.660838 \n", "L 78.56102 122.613846 \n", "L 80.396546 122.055451 \n", "L 81.497862 121.741354 \n", "L 83.333388 123.660838 \n", "L 85.168914 122.264849 \n", "L 87.004441 121.33419 \n", "L 88.839967 120.781612 \n", "L 90.675493 120.799061 \n", "L 91.776809 121.118144 \n", "L 93.612336 120.519864 \n", "L 95.447862 120.519864 \n", "L 97.283388 119.472872 \n", "L 99.118914 118.251383 \n", "L 100.954441 117.727887 \n", "L 102.055757 118.251383 \n", "L 103.891283 116.331899 \n", "L 105.726809 116.855394 \n", "L 107.562336 118.076884 \n", "L 109.397862 117.902385 \n", "L 111.233388 118.076884 \n", "L 112.334704 118.189062 \n", "L 114.17023 117.37889 \n", "L 116.005757 113.71442 \n", "L 117.841283 114.121584 \n", "L 119.676809 114.93591 \n", "L 121.512336 115.00571 \n", "L 122.613651 115.446942 \n", "L 124.449178 114.237916 \n", "L 126.284704 115.633904 \n", "L 128.12023 114.819578 \n", "L 129.955757 114.761412 \n", "L 131.791283 115.424506 \n", "L 132.892599 115.509262 \n", "L 134.728125 112.143933 \n", "L 136.563651 114.586913 \n", "L 138.399178 113.772586 \n", "L 140.234704 113.452672 \n", "L 142.07023 113.679521 \n", "L 143.171546 113.764277 \n", "L 145.007072 110.049951 \n", "L 146.842599 111.271441 \n", "L 148.678125 112.143933 \n", "L 150.513651 111.445939 \n", "L 152.349178 111.585538 \n", "L 153.450493 111.956971 \n", "L 155.28602 110.747945 \n", "L 157.121546 109.002959 \n", "L 158.957072 108.53763 \n", "L 160.792599 109.177458 \n", "L 162.628125 109.421756 \n", "L 163.729441 109.775739 \n", "L 165.564967 111.794936 \n", "L 167.400493 111.445939 \n", "L 169.23602 110.282615 \n", "L 171.071546 110.1372 \n", "L 172.907072 110.398948 \n", "L 174.008388 110.461269 \n", "L 175.843914 105.512988 \n", "L 177.679441 106.908977 \n", "L 179.514967 106.210983 \n", "L 181.350493 106.123733 \n", "L 183.18602 106.071384 \n", "L 184.287336 106.41041 \n", "L 186.122862 99.580038 \n", "L 187.958388 102.023017 \n", "L 189.793914 101.906685 \n", "L 191.629441 103.419006 \n", "L 193.464967 103.907602 \n", "L 194.566283 104.104536 \n", "L 196.401809 106.210983 \n", "L 198.237336 103.593504 \n", "L 200.072862 104.58233 \n", "L 201.908388 104.814994 \n", "L 203.743914 105.163991 \n", "L 204.84523 104.977029 \n", "L 206.680757 104.465997 \n", "L 208.516283 105.512988 \n", "L 210.351809 104.931327 \n", "L 212.187336 104.465997 \n", "L 214.022862 105.024393 \n", "L 215.124178 104.977029 \n", "L 216.959704 100.627029 \n", "L 218.79523 101.848519 \n", "L 220.630757 102.372015 \n", "L 222.466283 102.284765 \n", "L 224.301809 102.721012 \n", "L 225.403125 102.795797 \n", "\" clip-path=\"url(#pc21f047930)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 30.103125 132.211266 \n", "L 40.382072 133.083759 \n", "L 50.66102 124.358832 \n", "L 60.939967 126.97631 \n", "L 71.218914 126.97631 \n", "L 81.497862 126.103817 \n", "L 91.776809 119.996368 \n", "L 102.055757 128.721295 \n", "L 112.334704 121.741354 \n", "L 122.613651 119.123875 \n", "L 132.892599 119.996368 \n", "L 143.171546 120.868861 \n", "L 153.450493 119.996368 \n", "L 163.729441 121.741354 \n", "L 174.008388 118.251383 \n", "L 184.287336 119.996368 \n", "L 194.566283 121.741354 \n", "L 204.84523 121.741354 \n", "L 215.124178 119.996368 \n", "L 225.403125 122.613846 \n", "\" clip-path=\"url(#pc21f047930)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.1 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.1 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 59.234375 \n", "L 218.403125 59.234375 \n", "Q 220.403125 59.234375 220.403125 57.234375 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 140.634375 12.2 \n", "Q 138.634375 12.2 138.634375 14.2 \n", "L 138.634375 57.234375 \n", "Q 138.634375 59.234375 140.634375 59.234375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_20\">\n", "     <path d=\"M 142.634375 20.298438 \n", "L 152.634375 20.298438 \n", "L 162.634375 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_21\">\n", "     <path d=\"M 142.634375 34.976562 \n", "L 152.634375 34.976562 \n", "L 162.634375 34.976562 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 38.476562)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_22\">\n", "     <path d=\"M 142.634375 49.654688 \n", "L 152.634375 49.654688 \n", "L 162.634375 49.654688 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- valid acc -->\n", "     <g transform=\"translate(170.634375 53.154688)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"176.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"239.501953\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"271.289062\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"332.568359\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"387.548828\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pc21f047930\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["devices, num_epochs, lr, wd = d2l.try_all_gpus(), 20, 2e-4, 5e-4\n", "lr_period, lr_decay, net = 4, 0.9, get_net()\n", "train(net, train_iter, valid_iter, num_epochs, lr, wd, devices, lr_period,\n", "      lr_decay)"]}, {"cell_type": "markdown", "id": "7ebad777", "metadata": {"origin_pos": 51}, "source": ["## 在 Kaggle 上[**对测试集进行分类并提交结果**]\n", "\n", "在获得具有超参数的满意的模型后，我们使用所有标记的数据（包括验证集）来重新训练模型并对测试集进行分类。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "92b85006", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:03:21.098390Z", "iopub.status.busy": "2023-08-18T07:03:21.097395Z", "iopub.status.idle": "2023-08-18T07:04:21.878943Z", "shell.execute_reply": "2023-08-18T07:04:21.878089Z"}, "origin_pos": 53, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train loss 0.745, train acc 0.734\n", "883.3 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"180.65625pt\" viewBox=\"0 0 238.**********.65625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:04:21.843396</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 180.65625 \n", "L 238.**********.65625 \n", "L 238.965625 0 \n", "L 0 0 \n", "L 0 180.65625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 71.218914 143.1 \n", "L 71.218914 7.2 \n", "\" clip-path=\"url(#p6135fbbd5a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mebdd902f8f\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mebdd902f8f\" x=\"71.218914\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(68.037664 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 122.613651 143.1 \n", "L 122.613651 7.2 \n", "\" clip-path=\"url(#p6135fbbd5a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mebdd902f8f\" x=\"122.613651\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(116.251151 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 174.008388 143.1 \n", "L 174.008388 7.2 \n", "\" clip-path=\"url(#p6135fbbd5a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mebdd902f8f\" x=\"174.008388\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(167.645888 157.698438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 225.**********.1 \n", "L 225.403125 7.2 \n", "\" clip-path=\"url(#p6135fbbd5a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mebdd902f8f\" x=\"225.403125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(219.040625 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 171.376563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 30.103125 116.134776 \n", "L 225.403125 116.134776 \n", "\" clip-path=\"url(#p6135fbbd5a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m7c1b20b72c\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7c1b20b72c\" x=\"30.103125\" y=\"116.134776\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 119.933995)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.103125 88.797196 \n", "L 225.403125 88.797196 \n", "\" clip-path=\"url(#p6135fbbd5a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m7c1b20b72c\" x=\"30.103125\" y=\"88.797196\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 92.596415)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 61.459617 \n", "L 225.403125 61.459617 \n", "\" clip-path=\"url(#p6135fbbd5a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m7c1b20b72c\" x=\"30.103125\" y=\"61.459617\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 65.258835)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 34.122037 \n", "L 225.403125 34.122037 \n", "\" clip-path=\"url(#p6135fbbd5a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m7c1b20b72c\" x=\"30.103125\" y=\"34.122037\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 37.921256)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 21.813651 13.377273 \n", "L 23.803125 14.084835 \n", "L 25.792599 17.265965 \n", "L 27.782072 19.966889 \n", "L 29.771546 20.09238 \n", "L 30.103125 20.723904 \n", "L 32.092599 31.234864 \n", "L 34.082072 34.937273 \n", "L 36.071546 35.430761 \n", "L 38.06102 34.895631 \n", "L 40.050493 34.548563 \n", "L 40.382072 34.778086 \n", "L 42.371546 39.273667 \n", "L 44.36102 38.005438 \n", "L 46.350493 38.671031 \n", "L 48.339967 38.23258 \n", "L 50.329441 39.751424 \n", "L 50.66102 39.972606 \n", "L 52.650493 41.617858 \n", "L 54.639967 41.795145 \n", "L 56.629441 45.377955 \n", "L 58.618914 45.599059 \n", "L 60.608388 45.504185 \n", "L 60.939967 45.388773 \n", "L 62.929441 53.213793 \n", "L 64.918914 53.83799 \n", "L 66.908388 53.316074 \n", "L 68.897862 53.32601 \n", "L 70.887336 51.119661 \n", "L 71.218914 51.191085 \n", "L 73.208388 54.00175 \n", "L 75.197862 55.321736 \n", "L 77.187336 54.41098 \n", "L 79.176809 53.542315 \n", "L 81.166283 54.259058 \n", "L 81.497862 54.235414 \n", "L 83.487336 64.782382 \n", "L 85.476809 64.844396 \n", "L 87.466283 62.36135 \n", "L 89.455757 62.738942 \n", "L 91.44523 61.523166 \n", "L 91.776809 61.460963 \n", "L 93.766283 74.609259 \n", "L 95.755757 74.152825 \n", "L 97.74523 70.668697 \n", "L 99.734704 66.955819 \n", "L 101.724178 65.820009 \n", "L 102.055757 65.340679 \n", "L 104.04523 74.489621 \n", "L 106.034704 72.909576 \n", "L 108.024178 72.024171 \n", "L 110.013651 72.0336 \n", "L 112.003125 70.738994 \n", "L 112.334704 71.243956 \n", "L 114.324178 79.692247 \n", "L 116.313651 77.785246 \n", "L 118.303125 75.304162 \n", "L 120.292599 72.761087 \n", "L 122.282072 71.88264 \n", "L 122.613651 71.213075 \n", "L 124.603125 71.361436 \n", "L 126.592599 67.537476 \n", "L 128.582072 66.357977 \n", "L 130.571546 69.5021 \n", "L 132.56102 69.129072 \n", "L 132.892599 69.367192 \n", "L 134.882072 85.569705 \n", "L 136.871546 84.290146 \n", "L 138.86102 81.491377 \n", "L 140.850493 80.570456 \n", "L 142.839967 79.719894 \n", "L 143.171546 78.955417 \n", "L 145.16102 84.253504 \n", "L 147.150493 80.529092 \n", "L 149.139967 79.259373 \n", "L 151.129441 79.908943 \n", "L 153.118914 78.716854 \n", "L 153.450493 78.916126 \n", "L 155.439967 87.131967 \n", "L 157.429441 82.679337 \n", "L 159.418914 85.152121 \n", "L 161.408388 86.758844 \n", "L 163.397862 86.179439 \n", "L 163.729441 85.663272 \n", "L 165.718914 95.21925 \n", "L 167.708388 90.493077 \n", "L 169.697862 90.395603 \n", "L 171.687336 90.895154 \n", "L 173.676809 89.833333 \n", "L 174.008388 90.155997 \n", "L 175.997862 101.724711 \n", "L 177.987336 98.988188 \n", "L 179.976809 96.964342 \n", "L 181.966283 92.875431 \n", "L 183.955757 91.517794 \n", "L 184.287336 91.933849 \n", "L 186.276809 98.351722 \n", "L 188.266283 98.838307 \n", "L 190.255757 96.468169 \n", "L 192.24523 94.562344 \n", "L 194.234704 94.552589 \n", "L 194.566283 94.277881 \n", "L 196.555757 105.301517 \n", "L 198.54523 104.037154 \n", "L 200.534704 103.684176 \n", "L 202.524178 103.213166 \n", "L 204.513651 100.847015 \n", "L 204.84523 101.077076 \n", "L 206.834704 105.603659 \n", "L 208.824178 101.670209 \n", "L 210.813651 101.67303 \n", "L 212.803125 102.091432 \n", "L 214.792599 98.293397 \n", "L 215.124178 98.348741 \n", "L 217.113651 106.402302 \n", "L 219.103125 106.757251 \n", "L 221.092599 104.423083 \n", "L 223.082072 103.100872 \n", "L 225.071546 102.364418 \n", "L 225.403125 102.723908 \n", "\" clip-path=\"url(#p6135fbbd5a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 21.813651 136.922727 \n", "L 23.803125 136.495578 \n", "L 25.792599 134.264907 \n", "L 27.782072 133.790296 \n", "L 29.771546 133.84725 \n", "L 30.103125 133.661692 \n", "L 32.092599 130.657865 \n", "L 34.082072 128.6645 \n", "L 36.071546 127.810201 \n", "L 38.06102 127.667817 \n", "L 40.050493 127.525434 \n", "L 40.382072 127.543806 \n", "L 42.371546 127.810201 \n", "L 44.36102 128.806883 \n", "L 46.350493 128.189889 \n", "L 48.339967 127.810201 \n", "L 50.329441 127.183715 \n", "L 50.66102 127.047761 \n", "L 52.650493 124.962536 \n", "L 54.639967 124.108237 \n", "L 56.629441 123.34886 \n", "L 58.618914 123.325129 \n", "L 60.608388 124.33605 \n", "L 60.939967 124.457305 \n", "L 62.929441 122.684405 \n", "L 64.918914 121.830105 \n", "L 66.908388 121.16565 \n", "L 68.897862 121.61653 \n", "L 70.887336 122.285731 \n", "L 71.218914 122.41801 \n", "L 73.208388 123.253937 \n", "L 75.197862 121.687722 \n", "L 77.187336 121.735183 \n", "L 79.176809 122.04368 \n", "L 81.166283 122.114872 \n", "L 81.497862 122.252662 \n", "L 83.487336 117.558608 \n", "L 85.476809 117.700992 \n", "L 87.466283 117.463686 \n", "L 89.455757 117.6298 \n", "L 91.44523 118.014235 \n", "L 91.776809 117.953607 \n", "L 93.766283 114.99571 \n", "L 95.755757 113.714261 \n", "L 97.74523 114.521099 \n", "L 99.734704 115.42286 \n", "L 101.724178 116.362589 \n", "L 102.055757 116.630821 \n", "L 104.04523 115.280477 \n", "L 106.034704 115.707626 \n", "L 108.024178 115.944932 \n", "L 110.013651 115.921201 \n", "L 112.003125 116.134776 \n", "L 112.334704 116.07966 \n", "L 114.324178 113.002345 \n", "L 116.313651 113.002345 \n", "L 118.303125 113.951567 \n", "L 120.292599 114.497369 \n", "L 122.282072 114.540084 \n", "L 122.613651 114.867106 \n", "L 124.603125 114.426177 \n", "L 126.592599 114.710944 \n", "L 128.582072 115.470321 \n", "L 130.571546 114.710944 \n", "L 132.56102 115.394383 \n", "L 132.892599 115.252919 \n", "L 134.882072 109.015615 \n", "L 136.871546 109.869914 \n", "L 138.86102 110.629291 \n", "L 140.850493 111.00898 \n", "L 142.839967 111.293746 \n", "L 143.171546 111.835721 \n", "L 145.16102 113.287112 \n", "L 147.150493 113.429495 \n", "L 149.139967 113.761722 \n", "L 151.129441 113.002345 \n", "L 153.118914 112.888438 \n", "L 153.450493 112.827811 \n", "L 155.439967 109.869914 \n", "L 157.429441 111.578513 \n", "L 159.418914 110.914058 \n", "L 161.408388 109.941106 \n", "L 163.397862 110.154681 \n", "L 163.729441 110.402703 \n", "L 165.718914 106.452717 \n", "L 167.708388 108.161315 \n", "L 169.697862 108.161315 \n", "L 171.687336 108.018932 \n", "L 173.676809 108.673895 \n", "L 174.008388 108.47364 \n", "L 175.997862 103.320286 \n", "L 177.987336 105.456034 \n", "L 179.976809 105.693339 \n", "L 181.966283 107.449399 \n", "L 183.955757 107.420923 \n", "L 184.287336 107.426434 \n", "L 186.276809 103.605052 \n", "L 188.266283 103.320286 \n", "L 190.255757 104.364429 \n", "L 192.24523 105.242459 \n", "L 194.234704 105.370604 \n", "L 194.566283 105.607603 \n", "L 196.555757 102.465986 \n", "L 198.54523 103.320286 \n", "L 200.534704 103.699974 \n", "L 202.524178 103.747435 \n", "L 204.513651 104.744118 \n", "L 204.84523 104.450165 \n", "L 206.834704 102.18122 \n", "L 208.824178 103.747435 \n", "L 210.813651 103.605052 \n", "L 212.803125 104.103393 \n", "L 214.792599 105.598417 \n", "L 215.124178 105.607603 \n", "L 217.113651 101.611687 \n", "L 219.103125 101.75407 \n", "L 221.092599 102.276142 \n", "L 223.082072 102.964328 \n", "L 225.071546 103.491145 \n", "L 225.403125 103.347844 \n", "\" clip-path=\"url(#p6135fbbd5a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\"/>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.1 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.1 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 44.55625 \n", "L 218.403125 44.55625 \n", "Q 220.403125 44.55625 220.403125 42.55625 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 140.634375 12.2 \n", "Q 138.634375 12.2 138.634375 14.2 \n", "L 138.634375 42.55625 \n", "Q 138.634375 44.55625 140.634375 44.55625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_20\">\n", "     <path d=\"M 142.634375 20.298437 \n", "L 152.634375 20.298437 \n", "L 162.634375 20.298437 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 23.798437)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_21\">\n", "     <path d=\"M 142.634375 34.976562 \n", "L 152.634375 34.976562 \n", "L 162.634375 34.976562 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 38.476562)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p6135fbbd5a\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["net, preds = get_net(), []\n", "train(net, train_valid_iter, None, num_epochs, lr, wd, devices, lr_period,\n", "      lr_decay)\n", "\n", "for X, _ in test_iter:\n", "    y_hat = net(X.to(devices[0]))\n", "    preds.extend(y_hat.argmax(dim=1).type(torch.int32).cpu().numpy())\n", "sorted_ids = list(range(1, len(test_ds) + 1))\n", "sorted_ids.sort(key=lambda x: str(x))\n", "df = pd.DataFrame({'id': sorted_ids, 'label': preds})\n", "df['label'] = df['label'].apply(lambda x: train_valid_ds.classes[x])\n", "df.to_csv('submission.csv', index=False)"]}, {"cell_type": "markdown", "id": "b6c0bf49", "metadata": {"origin_pos": 55}, "source": ["向Kaggle提交结果的方法与 :numref:`sec_kaggle_house`中的方法类似，上面的代码将生成一个\n", "`submission.csv`文件，其格式符合Kaggle竞赛的要求。\n", "\n", "## 小结\n", "\n", "* 将包含原始图像文件的数据集组织为所需格式后，我们可以读取它们。\n"]}, {"cell_type": "markdown", "id": "c9aff1dc", "metadata": {"origin_pos": 57, "tab": ["pytorch"]}, "source": ["* 我们可以在图像分类竞赛中使用卷积神经网络和图像增广。\n"]}, {"cell_type": "markdown", "id": "725588ee", "metadata": {"origin_pos": 59}, "source": ["## 练习\n", "\n", "1. 在这场Kaggle竞赛中使用完整的CIFAR-10数据集。将超参数设为`batch_size = 128`，`num_epochs = 100`，`lr = 0.1`，`lr_period = 50`，`lr_decay = 0.1`。看看在这场比赛中能达到什么准确度和排名。能进一步改进吗？\n", "1. 不使用图像增广时，能获得怎样的准确度？\n"]}, {"cell_type": "markdown", "id": "ae7f6d9e", "metadata": {"origin_pos": 61, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2831)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
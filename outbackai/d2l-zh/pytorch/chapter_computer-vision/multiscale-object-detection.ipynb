{"cells": [{"cell_type": "markdown", "id": "66010731", "metadata": {"origin_pos": 0}, "source": ["# 多尺度目标检测\n", ":label:`sec_multiscale-object-detection`\n", "\n", "在 :numref:`sec_anchor`中，我们以输入图像的每个像素为中心，生成了多个锚框。\n", "基本而言，这些锚框代表了图像不同区域的样本。\n", "然而，如果为每个像素都生成的锚框，我们最终可能会得到太多需要计算的锚框。\n", "想象一个$561 \\times 728$的输入图像，如果以每个像素为中心生成五个形状不同的锚框，就需要在图像上标记和预测超过200万个锚框（$561 \\times 728 \\times 5$）。\n", "\n", "## 多尺度锚框\n", ":label:`subsec_multiscale-anchor-boxes`\n", "\n", "减少图像上的锚框数量并不困难。\n", "比如，我们可以在输入图像中均匀采样一小部分像素，并以它们为中心生成锚框。\n", "此外，在不同尺度下，我们可以生成不同数量和不同大小的锚框。\n", "直观地说，比起较大的目标，较小的目标在图像上出现的可能性更多样。\n", "例如，$1 \\times 1$、$1 \\times 2$和$2 \\times 2$的目标可以分别以4、2和1种可能的方式出现在$2 \\times 2$图像上。\n", "因此，当使用较小的锚框检测较小的物体时，我们可以采样更多的区域，而对于较大的物体，我们可以采样较少的区域。\n", "\n", "为了演示如何在多个尺度下生成锚框，让我们先读取一张图像。\n", "它的高度和宽度分别为561和728像素。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "5e98dd98", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:01:14.742245Z", "iopub.status.busy": "2023-08-18T07:01:14.741240Z", "iopub.status.idle": "2023-08-18T07:01:17.317727Z", "shell.execute_reply": "2023-08-18T07:01:17.316623Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(561, 728)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["%matplotlib inline\n", "import torch\n", "from d2l import torch as d2l\n", "\n", "img = d2l.plt.imread('../img/catdog.jpg')\n", "h, w = img.shape[:2]\n", "h, w"]}, {"cell_type": "markdown", "id": "e73d91ad", "metadata": {"origin_pos": 4}, "source": ["回想一下，在 :numref:`sec_conv_layer`中，我们将卷积图层的二维数组输出称为特征图。\n", "通过定义特征图的形状，我们可以确定任何图像上均匀采样锚框的中心。\n", "\n", "`display_anchors`函数定义如下。\n", "我们[**在特征图（`fmap`）上生成锚框（`anchors`），每个单位（像素）作为锚框的中心**]。\n", "由于锚框中的$(x, y)$轴坐标值（`anchors`）已经被除以特征图（`fmap`）的宽度和高度，因此这些值介于0和1之间，表示特征图中锚框的相对位置。\n", "\n", "由于锚框（`anchors`）的中心分布于特征图（`fmap`）上的所有单位，因此这些中心必须根据其相对空间位置在任何输入图像上*均匀*分布。\n", "更具体地说，给定特征图的宽度和高度`fmap_w`和`fmap_h`，以下函数将*均匀地*对任何输入图像中`fmap_h`行和`fmap_w`列中的像素进行采样。\n", "以这些均匀采样的像素为中心，将会生成大小为`s`（假设列表`s`的长度为1）且宽高比（`ratios`）不同的锚框。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "b7ab8598", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:01:17.322639Z", "iopub.status.busy": "2023-08-18T07:01:17.321969Z", "iopub.status.idle": "2023-08-18T07:01:17.328192Z", "shell.execute_reply": "2023-08-18T07:01:17.327078Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [], "source": ["def display_anchors(fmap_w, fmap_h, s):\n", "    d2l.set_figsize()\n", "    # 前两个维度上的值不影响输出\n", "    fmap = torch.zeros((1, 10, fmap_h, fmap_w))\n", "    anchors = d2l.multibox_prior(fmap, sizes=s, ratios=[1, 2, 0.5])\n", "    bbox_scale = torch.tensor((w, h, w, h))\n", "    d2l.show_bboxes(d2l.plt.imshow(img).axes,\n", "                    anchors[0] * bbox_scale)"]}, {"cell_type": "markdown", "id": "4f1d121e", "metadata": {"origin_pos": 8}, "source": ["首先，让我们考虑[**探测小目标**]。\n", "为了在显示时更容易分辨，在这里具有不同中心的锚框不会重叠：\n", "锚框的尺度设置为0.15，特征图的高度和宽度设置为4。\n", "我们可以看到，图像上4行和4列的锚框的中心是均匀分布的。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "b8088160", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:01:17.333487Z", "iopub.status.busy": "2023-08-18T07:01:17.332535Z", "iopub.status.idle": "2023-08-18T07:01:17.807920Z", "shell.execute_reply": "2023-08-18T07:01:17.806753Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"216.84258pt\" height=\"170.656221pt\" viewBox=\"0 0 216.84258 170.656221\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:01:17.694754</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 170.656221 \n", "L 216.84258 170.656221 \n", "L 216.84258 0 \n", "L 0 0 \n", "L 0 170.656221 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 209.64258 146.778096 \n", "L 209.64258 10.878096 \n", "L 33.2875 10.878096 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pcde99c640a)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagee9c9b4ffa3\" transform=\"scale(1 -1)translate(0 -136)\" x=\"33.2875\" y=\"-10.778096\" width=\"177\" height=\"136\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.226376 17.794219 \n", "L 68.679641 17.794219 \n", "L 68.679641 38.179222 \n", "L 42.226376 38.179222 \n", "L 42.226376 17.794219 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 36.747727 20.779533 \n", "L 74.15829 20.779533 \n", "L 74.15829 35.193905 \n", "L 36.747727 35.193905 \n", "L 36.747727 20.779533 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 46.100367 13.572347 \n", "L 64.805649 13.572347 \n", "L 64.805649 42.401091 \n", "L 46.100367 42.401091 \n", "L 46.100367 13.572347 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 86.315149 17.794219 \n", "L 112.768411 17.794219 \n", "L 112.768411 38.179222 \n", "L 86.315149 38.179222 \n", "L 86.315149 17.794219 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 80.836496 20.779533 \n", "L 118.24706 20.779533 \n", "L 118.24706 35.193905 \n", "L 80.836496 35.193905 \n", "L 80.836496 20.779533 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 90.189141 13.572347 \n", "L 108.894419 13.572347 \n", "L 108.894419 42.401091 \n", "L 90.189141 42.401091 \n", "L 90.189141 13.572347 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 130.403916 17.794219 \n", "L 156.857181 17.794219 \n", "L 156.857181 38.179222 \n", "L 130.403916 38.179222 \n", "L 130.403916 17.794219 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 124.925274 20.779533 \n", "L 162.33583 20.779533 \n", "L 162.33583 35.193905 \n", "L 124.925274 35.193905 \n", "L 124.925274 20.779533 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 134.277907 13.572347 \n", "L 152.983189 13.572347 \n", "L 152.983189 42.401091 \n", "L 134.277907 42.401091 \n", "L 134.277907 13.572347 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 174.492693 17.794219 \n", "L 200.945943 17.794219 \n", "L 200.945943 38.179222 \n", "L 174.492693 38.179222 \n", "L 174.492693 17.794219 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 169.014036 20.779533 \n", "L 206.4246 20.779533 \n", "L 206.4246 35.193905 \n", "L 169.014036 35.193905 \n", "L 169.014036 20.779533 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 178.366677 13.572347 \n", "L 197.071959 13.572347 \n", "L 197.071959 42.401091 \n", "L 178.366677 42.401091 \n", "L 178.366677 13.572347 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 42.226376 51.769219 \n", "L 68.679641 51.769219 \n", "L 68.679641 72.154218 \n", "L 42.226376 72.154218 \n", "L 42.226376 51.769219 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 36.747727 54.754535 \n", "L 74.15829 54.754535 \n", "L 74.15829 69.168903 \n", "L 36.747727 69.168903 \n", "L 36.747727 54.754535 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 46.100367 47.547347 \n", "L 64.805649 47.547347 \n", "L 64.805649 76.376087 \n", "L 46.100367 76.376087 \n", "L 46.100367 47.547347 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 86.315149 51.769219 \n", "L 112.768411 51.769219 \n", "L 112.768411 72.154218 \n", "L 86.315149 72.154218 \n", "L 86.315149 51.769219 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 80.836496 54.754535 \n", "L 118.24706 54.754535 \n", "L 118.24706 69.168903 \n", "L 80.836496 69.168903 \n", "L 80.836496 54.754535 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 90.189141 47.547347 \n", "L 108.894419 47.547347 \n", "L 108.894419 76.376087 \n", "L 90.189141 76.376087 \n", "L 90.189141 47.547347 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 130.403916 51.769219 \n", "L 156.857181 51.769219 \n", "L 156.857181 72.154218 \n", "L 130.403916 72.154218 \n", "L 130.403916 51.769219 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 124.925274 54.754535 \n", "L 162.33583 54.754535 \n", "L 162.33583 69.168903 \n", "L 124.925274 69.168903 \n", "L 124.925274 54.754535 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 134.277907 47.547347 \n", "L 152.983189 47.547347 \n", "L 152.983189 76.376087 \n", "L 134.277907 76.376087 \n", "L 134.277907 47.547347 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 174.492693 51.769219 \n", "L 200.945943 51.769219 \n", "L 200.945943 72.154218 \n", "L 174.492693 72.154218 \n", "L 174.492693 51.769219 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 169.014036 54.754535 \n", "L 206.4246 54.754535 \n", "L 206.4246 69.168903 \n", "L 169.014036 69.168903 \n", "L 169.014036 54.754535 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 178.366677 47.547347 \n", "L 197.071959 47.547347 \n", "L 197.071959 76.376087 \n", "L 178.366677 76.376087 \n", "L 178.366677 47.547347 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 42.226376 85.744223 \n", "L 68.679641 85.744223 \n", "L 68.679641 106.129214 \n", "L 42.226376 106.129214 \n", "L 42.226376 85.744223 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 36.747727 88.729538 \n", "L 74.15829 88.729538 \n", "L 74.15829 103.143899 \n", "L 36.747727 103.143899 \n", "L 36.747727 88.729538 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 46.100367 81.522351 \n", "L 64.805649 81.522351 \n", "L 64.805649 110.351087 \n", "L 46.100367 110.351087 \n", "L 46.100367 81.522351 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 86.315149 85.744223 \n", "L 112.768411 85.744223 \n", "L 112.768411 106.129214 \n", "L 86.315149 106.129214 \n", "L 86.315149 85.744223 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 80.836496 88.729538 \n", "L 118.24706 88.729538 \n", "L 118.24706 103.143899 \n", "L 80.836496 103.143899 \n", "L 80.836496 88.729538 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 90.189141 81.522351 \n", "L 108.894419 81.522351 \n", "L 108.894419 110.351087 \n", "L 90.189141 110.351087 \n", "L 90.189141 81.522351 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 130.403916 85.744223 \n", "L 156.857181 85.744223 \n", "L 156.857181 106.129214 \n", "L 130.403916 106.129214 \n", "L 130.403916 85.744223 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 124.925274 88.729538 \n", "L 162.33583 88.729538 \n", "L 162.33583 103.143899 \n", "L 124.925274 103.143899 \n", "L 124.925274 88.729538 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 134.277907 81.522351 \n", "L 152.983189 81.522351 \n", "L 152.983189 110.351087 \n", "L 134.277907 110.351087 \n", "L 134.277907 81.522351 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 174.492693 85.744223 \n", "L 200.945943 85.744223 \n", "L 200.945943 106.129214 \n", "L 174.492693 106.129214 \n", "L 174.492693 85.744223 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 169.014036 88.729538 \n", "L 206.4246 88.729538 \n", "L 206.4246 103.143899 \n", "L 169.014036 103.143899 \n", "L 169.014036 88.729538 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 178.366677 81.522351 \n", "L 197.071959 81.522351 \n", "L 197.071959 110.351087 \n", "L 178.366677 110.351087 \n", "L 178.366677 81.522351 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 42.226376 119.719223 \n", "L 68.679641 119.719223 \n", "L 68.679641 140.104222 \n", "L 42.226376 140.104222 \n", "L 42.226376 119.719223 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 36.747727 122.704538 \n", "L 74.15829 122.704538 \n", "L 74.15829 137.118907 \n", "L 36.747727 137.118907 \n", "L 36.747727 122.704538 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 46.100367 115.497351 \n", "L 64.805649 115.497351 \n", "L 64.805649 144.32608 \n", "L 46.100367 144.32608 \n", "L 46.100367 115.497351 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 86.315149 119.719223 \n", "L 112.768411 119.719223 \n", "L 112.768411 140.104222 \n", "L 86.315149 140.104222 \n", "L 86.315149 119.719223 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 80.836496 122.704538 \n", "L 118.24706 122.704538 \n", "L 118.24706 137.118907 \n", "L 80.836496 137.118907 \n", "L 80.836496 122.704538 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 90.189141 115.497351 \n", "L 108.894419 115.497351 \n", "L 108.894419 144.32608 \n", "L 90.189141 144.32608 \n", "L 90.189141 115.497351 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_45\">\n", "    <path d=\"M 130.403916 119.719223 \n", "L 156.857181 119.719223 \n", "L 156.857181 140.104222 \n", "L 130.403916 140.104222 \n", "L 130.403916 119.719223 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_46\">\n", "    <path d=\"M 124.925274 122.704538 \n", "L 162.33583 122.704538 \n", "L 162.33583 137.118907 \n", "L 124.925274 137.118907 \n", "L 124.925274 122.704538 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_47\">\n", "    <path d=\"M 134.277907 115.497351 \n", "L 152.983189 115.497351 \n", "L 152.983189 144.32608 \n", "L 134.277907 144.32608 \n", "L 134.277907 115.497351 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_48\">\n", "    <path d=\"M 174.492693 119.719223 \n", "L 200.945943 119.719223 \n", "L 200.945943 140.104222 \n", "L 174.492693 140.104222 \n", "L 174.492693 119.719223 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_49\">\n", "    <path d=\"M 169.014036 122.704538 \n", "L 206.4246 122.704538 \n", "L 206.4246 137.118907 \n", "L 169.014036 137.118907 \n", "L 169.014036 122.704538 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_50\">\n", "    <path d=\"M 178.366677 115.497351 \n", "L 197.071959 115.497351 \n", "L 197.071959 144.32608 \n", "L 178.366677 144.32608 \n", "L 178.366677 115.497351 \n", "z\n", "\" clip-path=\"url(#pcde99c640a)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"me6458d0f23\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me6458d0f23\" x=\"33.408623\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(30.227373 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#me6458d0f23\" x=\"81.857821\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(72.314071 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#me6458d0f23\" x=\"130.307019\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(120.763269 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#me6458d0f23\" x=\"178.756217\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(169.212467 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"mdd717f21d3\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mdd717f21d3\" x=\"33.2875\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mdd717f21d3\" x=\"33.2875\" y=\"35.223818\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 39.023036)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mdd717f21d3\" x=\"33.2875\" y=\"59.448417\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(7.2 63.247635)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mdd717f21d3\" x=\"33.2875\" y=\"83.673016\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(7.2 87.472234)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mdd717f21d3\" x=\"33.2875\" y=\"107.897614\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(7.2 111.696833)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mdd717f21d3\" x=\"33.2875\" y=\"132.122213\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(7.2 135.921432)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_51\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 33.2875 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_52\">\n", "    <path d=\"M 209.64258 146.778096 \n", "L 209.64258 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_53\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 209.64258 146.778096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_54\">\n", "    <path d=\"M 33.2875 10.878096 \n", "L 209.64258 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pcde99c640a\">\n", "   <rect x=\"33.2875\" y=\"10.878096\" width=\"176.35508\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["display_anchors(fmap_w=4, fmap_h=4, s=[0.15])"]}, {"cell_type": "markdown", "id": "05c390b8", "metadata": {"origin_pos": 10}, "source": ["然后，我们[**将特征图的高度和宽度减小一半，然后使用较大的锚框来检测较大的目标**]。\n", "当尺度设置为0.4时，一些锚框将彼此重叠。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "28b6ce1d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:01:17.812435Z", "iopub.status.busy": "2023-08-18T07:01:17.811538Z", "iopub.status.idle": "2023-08-18T07:01:18.128829Z", "shell.execute_reply": "2023-08-18T07:01:18.127647Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"216.84258pt\" height=\"170.656221pt\" viewBox=\"0 0 216.84258 170.656221\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:01:18.034151</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 170.656221 \n", "L 216.84258 170.656221 \n", "L 216.84258 0 \n", "L 0 0 \n", "L 0 170.656221 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 209.64258 146.778096 \n", "L 209.64258 10.878096 \n", "L 33.2875 10.878096 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd0bc74a8db)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image4a496b511a\" transform=\"scale(1 -1)translate(0 -136)\" x=\"33.2875\" y=\"-10.778096\" width=\"177\" height=\"136\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.226376 17.794219 \n", "L 112.768411 17.794219 \n", "L 112.768411 72.154218 \n", "L 42.226376 72.154218 \n", "L 42.226376 17.794219 \n", "z\n", "\" clip-path=\"url(#pd0bc74a8db)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 27.616642 25.755056 \n", "L 127.378147 25.755056 \n", "L 127.378147 64.193383 \n", "L 27.616642 64.193383 \n", "L 27.616642 25.755056 \n", "z\n", "\" clip-path=\"url(#pd0bc74a8db)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.557018 6.535892 \n", "L 102.437774 6.535892 \n", "L 102.437774 83.412546 \n", "L 52.557018 83.412546 \n", "L 52.557018 6.535892 \n", "z\n", "\" clip-path=\"url(#pd0bc74a8db)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 130.403916 17.794219 \n", "L 200.945943 17.794219 \n", "L 200.945943 72.154218 \n", "L 130.403916 72.154218 \n", "L 130.403916 17.794219 \n", "z\n", "\" clip-path=\"url(#pd0bc74a8db)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 115.794179 25.755056 \n", "L 215.555695 25.755056 \n", "L 215.555695 64.193383 \n", "L 115.794179 64.193383 \n", "L 115.794179 25.755056 \n", "z\n", "\" clip-path=\"url(#pd0bc74a8db)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 140.734552 6.535892 \n", "L 190.615314 6.535892 \n", "L 190.615314 83.412546 \n", "L 140.734552 83.412546 \n", "L 140.734552 6.535892 \n", "z\n", "\" clip-path=\"url(#pd0bc74a8db)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 42.226376 85.744223 \n", "L 112.768411 85.744223 \n", "L 112.768411 140.104222 \n", "L 42.226376 140.104222 \n", "L 42.226376 85.744223 \n", "z\n", "\" clip-path=\"url(#pd0bc74a8db)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 27.616642 93.705051 \n", "L 127.378147 93.705051 \n", "L 127.378147 132.143386 \n", "L 27.616642 132.143386 \n", "L 27.616642 93.705051 \n", "z\n", "\" clip-path=\"url(#pd0bc74a8db)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 52.557018 74.485891 \n", "L 102.437774 74.485891 \n", "L 102.437774 151.362554 \n", "L 52.557018 151.362554 \n", "L 52.557018 74.485891 \n", "z\n", "\" clip-path=\"url(#pd0bc74a8db)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 130.403916 85.744223 \n", "L 200.945943 85.744223 \n", "L 200.945943 140.104222 \n", "L 130.403916 140.104222 \n", "L 130.403916 85.744223 \n", "z\n", "\" clip-path=\"url(#pd0bc74a8db)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 115.794179 93.705051 \n", "L 215.555695 93.705051 \n", "L 215.555695 132.143386 \n", "L 115.794179 132.143386 \n", "L 115.794179 93.705051 \n", "z\n", "\" clip-path=\"url(#pd0bc74a8db)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 140.734552 74.485891 \n", "L 190.615314 74.485891 \n", "L 190.615314 151.362554 \n", "L 140.734552 151.362554 \n", "L 140.734552 74.485891 \n", "z\n", "\" clip-path=\"url(#pd0bc74a8db)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m760eb09982\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m760eb09982\" x=\"33.408623\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(30.227373 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m760eb09982\" x=\"81.857821\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(72.314071 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m760eb09982\" x=\"130.307019\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(120.763269 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m760eb09982\" x=\"178.756217\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(169.212467 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"m220e755125\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m220e755125\" x=\"33.2875\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m220e755125\" x=\"33.2875\" y=\"35.223818\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 39.023036)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m220e755125\" x=\"33.2875\" y=\"59.448417\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(7.2 63.247635)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m220e755125\" x=\"33.2875\" y=\"83.673016\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(7.2 87.472234)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m220e755125\" x=\"33.2875\" y=\"107.897614\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(7.2 111.696833)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m220e755125\" x=\"33.2875\" y=\"132.122213\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(7.2 135.921432)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 33.2875 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 209.64258 146.778096 \n", "L 209.64258 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 209.64258 146.778096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 33.2875 10.878096 \n", "L 209.64258 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pd0bc74a8db\">\n", "   <rect x=\"33.2875\" y=\"10.878096\" width=\"176.35508\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["display_anchors(fmap_w=2, fmap_h=2, s=[0.4])"]}, {"cell_type": "markdown", "id": "ef16223f", "metadata": {"origin_pos": 12}, "source": ["最后，我们进一步[**将特征图的高度和宽度减小一半，然后将锚框的尺度增加到0.8**]。\n", "此时，锚框的中心即是图像的中心。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "bcc1b8c9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:01:18.134209Z", "iopub.status.busy": "2023-08-18T07:01:18.133433Z", "iopub.status.idle": "2023-08-18T07:01:18.417530Z", "shell.execute_reply": "2023-08-18T07:01:18.416424Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"216.84258pt\" height=\"170.656221pt\" viewBox=\"0 0 216.84258 170.656221\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:01:18.352215</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 170.656221 \n", "L 216.84258 170.656221 \n", "L 216.84258 0 \n", "L 0 0 \n", "L 0 170.656221 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 209.64258 146.778096 \n", "L 209.64258 10.878096 \n", "L 33.2875 10.878096 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p249032bdd7)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image97baf19944\" transform=\"scale(1 -1)translate(0 -136)\" x=\"33.2875\" y=\"-10.778096\" width=\"177\" height=\"136\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 51.04413 24.589218 \n", "L 192.128198 24.589218 \n", "L 192.128198 133.309217 \n", "L 51.04413 133.309217 \n", "L 51.04413 24.589218 \n", "z\n", "\" clip-path=\"url(#p249032bdd7)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 21.82466 40.510893 \n", "L 217.84258 40.510893 \n", "M 217.84258 117.387546 \n", "L 21.82466 117.387546 \n", "L 21.82466 40.510893 \n", "\" clip-path=\"url(#p249032bdd7)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 71.705413 2.072566 \n", "L 171.466925 2.072566 \n", "L 171.466925 155.825874 \n", "L 71.705413 155.825874 \n", "L 71.705413 2.072566 \n", "z\n", "\" clip-path=\"url(#p249032bdd7)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m788aa99645\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m788aa99645\" x=\"33.408623\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(30.227373 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m788aa99645\" x=\"81.857821\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(72.314071 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m788aa99645\" x=\"130.307019\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(120.763269 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m788aa99645\" x=\"178.756217\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(169.212467 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"m0bd52372fb\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m0bd52372fb\" x=\"33.2875\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m0bd52372fb\" x=\"33.2875\" y=\"35.223818\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 39.023036)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m0bd52372fb\" x=\"33.2875\" y=\"59.448417\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(7.2 63.247635)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m0bd52372fb\" x=\"33.2875\" y=\"83.673016\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(7.2 87.472234)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m0bd52372fb\" x=\"33.2875\" y=\"107.897614\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(7.2 111.696833)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m0bd52372fb\" x=\"33.2875\" y=\"132.122213\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(7.2 135.921432)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 33.2875 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 209.64258 146.778096 \n", "L 209.64258 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 209.64258 146.778096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 33.2875 10.878096 \n", "L 209.64258 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p249032bdd7\">\n", "   <rect x=\"33.2875\" y=\"10.878096\" width=\"176.35508\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["display_anchors(fmap_w=1, fmap_h=1, s=[0.8])"]}, {"cell_type": "markdown", "id": "b6a349bc", "metadata": {"origin_pos": 14}, "source": ["## 多尺度检测\n", "\n", "既然我们已经生成了多尺度的锚框，我们就将使用它们来检测不同尺度下各种大小的目标。\n", "下面，我们介绍一种基于CNN的多尺度目标检测方法，将在 :numref:`sec_ssd`中实现。\n", "\n", "在某种规模上，假设我们有$c$张形状为$h \\times w$的特征图。\n", "使用 :numref:`subsec_multiscale-anchor-boxes`中的方法，我们生成了$hw$组锚框，其中每组都有$a$个中心相同的锚框。\n", "例如，在 :numref:`subsec_multiscale-anchor-boxes`实验的第一个尺度上，给定10个（通道数量）$4 \\times 4$的特征图，我们生成了16组锚框，每组包含3个中心相同的锚框。\n", "接下来，每个锚框都根据真实值边界框来标记了类和偏移量。\n", "在当前尺度下，目标检测模型需要预测输入图像上$hw$组锚框类别和偏移量，其中不同组锚框具有不同的中心。\n", "\n", "\n", "假设此处的$c$张特征图是CNN基于输入图像的正向传播算法获得的中间输出。\n", "既然每张特征图上都有$hw$个不同的空间位置，那么相同空间位置可以看作含有$c$个单元。\n", "根据 :numref:`sec_conv_layer`中对感受野的定义，特征图在相同空间位置的$c$个单元在输入图像上的感受野相同：\n", "它们表征了同一感受野内的输入图像信息。\n", "因此，我们可以将特征图在同一空间位置的$c$个单元变换为使用此空间位置生成的$a$个锚框类别和偏移量。\n", "本质上，我们用输入图像在某个感受野区域内的信息，来预测输入图像上与该区域位置相近的锚框类别和偏移量。\n", "\n", "当不同层的特征图在输入图像上分别拥有不同大小的感受野时，它们可以用于检测不同大小的目标。\n", "例如，我们可以设计一个神经网络，其中靠近输出层的特征图单元具有更宽的感受野，这样它们就可以从输入图像中检测到较大的目标。\n", "\n", "简言之，我们可以利用深层神经网络在多个层次上对图像进行分层表示，从而实现多尺度目标检测。\n", "在 :numref:`sec_ssd`，我们将通过一个具体的例子来说明它是如何工作的。\n", "\n", "## 小结\n", "\n", "* 在多个尺度下，我们可以生成不同尺寸的锚框来检测不同尺寸的目标。\n", "* 通过定义特征图的形状，我们可以决定任何图像上均匀采样的锚框的中心。\n", "* 我们使用输入图像在某个感受野区域内的信息，来预测输入图像上与该区域位置相近的锚框类别和偏移量。\n", "* 我们可以通过深入学习，在多个层次上的图像分层表示进行多尺度目标检测。\n", "\n", "## 练习\n", "\n", "1. 根据我们在 :numref:`sec_alexnet`中的讨论，深度神经网络学习图像特征级别抽象层次，随网络深度的增加而升级。在多尺度目标检测中，不同尺度的特征映射是否对应于不同的抽象层次？为什么？\n", "1. 在 :numref:`subsec_multiscale-anchor-boxes`中的实验里的第一个尺度（`fmap_w=4, fmap_h=4`）下，生成可能重叠的均匀分布的锚框。\n", "1. 给定形状为$1 \\times c \\times h \\times w$的特征图变量，其中$c$、$h$和$w$分别是特征图的通道数、高度和宽度。怎样才能将这个变量转换为锚框类别和偏移量？输出的形状是什么？\n"]}, {"cell_type": "markdown", "id": "0876c3a0", "metadata": {"origin_pos": 16, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2948)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
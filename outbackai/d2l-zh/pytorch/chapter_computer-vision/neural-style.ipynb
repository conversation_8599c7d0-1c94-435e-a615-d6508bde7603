{"cells": [{"cell_type": "markdown", "id": "273bffe9", "metadata": {"origin_pos": 0}, "source": ["# 风格迁移\n", "\n", "摄影爱好者也许接触过滤波器。它能改变照片的颜色风格，从而使风景照更加锐利或者令人像更加美白。但一个滤波器通常只能改变照片的某个方面。如果要照片达到理想中的风格，可能需要尝试大量不同的组合。这个过程的复杂程度不亚于模型调参。\n", "\n", "本节将介绍如何使用卷积神经网络，自动将一个图像中的风格应用在另一图像之上，即*风格迁移*（style transfer） :cite:`Gatys.Ecker.Bethge.2016`。\n", "这里我们需要两张输入图像：一张是*内容图像*，另一张是*风格图像*。\n", "我们将使用神经网络修改内容图像，使其在风格上接近风格图像。\n", "例如， :numref:`fig_style_transfer`中的内容图像为本书作者在西雅图郊区的雷尼尔山国家公园拍摄的风景照，而风格图像则是一幅主题为秋天橡树的油画。\n", "最终输出的合成图像应用了风格图像的油画笔触让整体颜色更加鲜艳，同时保留了内容图像中物体主体的形状。\n", "\n", "![输入内容图像和风格图像，输出风格迁移后的合成图像](../img/style-transfer.svg)\n", ":label:`fig_style_transfer`\n", "\n", "## 方法\n", "\n", " :numref:`fig_style_transfer_model`用简单的例子阐述了基于卷积神经网络的风格迁移方法。\n", "首先，我们初始化合成图像，例如将其初始化为内容图像。\n", "该合成图像是风格迁移过程中唯一需要更新的变量，即风格迁移所需迭代的模型参数。\n", "然后，我们选择一个预训练的卷积神经网络来抽取图像的特征，其中的模型参数在训练中无须更新。\n", "这个深度卷积神经网络凭借多个层逐级抽取图像的特征，我们可以选择其中某些层的输出作为内容特征或风格特征。\n", "以 :numref:`fig_style_transfer_model`为例，这里选取的预训练的神经网络含有3个卷积层，其中第二层输出内容特征，第一层和第三层输出风格特征。\n", "\n", "![基于卷积神经网络的风格迁移。实线箭头和虚线箭头分别表示前向传播和反向传播](../img/neural-style.svg)\n", ":label:`fig_style_transfer_model`\n", "\n", "接下来，我们通过前向传播（实线箭头方向）计算风格迁移的损失函数，并通过反向传播（虚线箭头方向）迭代模型参数，即不断更新合成图像。\n", "风格迁移常用的损失函数由3部分组成：\n", "\n", "1. *内容损失*使合成图像与内容图像在内容特征上接近；\n", "1. *风格损失*使合成图像与风格图像在风格特征上接近；\n", "1. *全变分损失*则有助于减少合成图像中的噪点。\n", "\n", "最后，当模型训练结束时，我们输出风格迁移的模型参数，即得到最终的合成图像。\n", "\n", "在下面，我们将通过代码来进一步了解风格迁移的技术细节。\n", "\n", "## [**阅读内容和风格图像**]\n", "\n", "首先，我们读取内容和风格图像。\n", "从打印出的图像坐标轴可以看出，它们的尺寸并不一样。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a0d90f51", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:26.021505Z", "iopub.status.busy": "2023-08-18T07:23:26.020759Z", "iopub.status.idle": "2023-08-18T07:23:29.597245Z", "shell.execute_reply": "2023-08-18T07:23:29.595990Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"250.345337pt\" height=\"164.997876pt\" viewBox=\"0 0 250.**********.997876\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:23:29.411028</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 164.997876 \n", "L 250.**********.997876 \n", "L 250.345337 0 \n", "L 0 0 \n", "L 0 164.997876 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 39.65 141.119751 \n", "L 234.95 141.119751 \n", "L 234.95 10.951538 \n", "L 39.65 10.951538 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pf593424c6e)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image0e2453659a\" transform=\"scale(1 -1)translate(0 -131)\" x=\"39.65\" y=\"-10.119751\" width=\"196\" height=\"131\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m03c4e67d2e\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m03c4e67d2e\" x=\"39.697681\" y=\"141.119751\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(36.516431 155.718188)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m03c4e67d2e\" x=\"87.378345\" y=\"141.119751\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(77.834595 155.718188)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m03c4e67d2e\" x=\"135.059009\" y=\"141.119751\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(122.334009 155.718188)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m03c4e67d2e\" x=\"182.739673\" y=\"141.119751\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1500 -->\n", "      <g transform=\"translate(170.014673 155.718188)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m03c4e67d2e\" x=\"230.420337\" y=\"141.119751\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2000 -->\n", "      <g transform=\"translate(217.695337 155.718188)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"md13bc7e0ae\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md13bc7e0ae\" x=\"39.65\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.2875 14.798438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#md13bc7e0ae\" x=\"39.65\" y=\"34.839551\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 250 -->\n", "      <g transform=\"translate(13.5625 38.63877)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#md13bc7e0ae\" x=\"39.65\" y=\"58.679883\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(13.5625 62.479102)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#md13bc7e0ae\" x=\"39.65\" y=\"82.520215\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 750 -->\n", "      <g transform=\"translate(13.5625 86.319434)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#md13bc7e0ae\" x=\"39.65\" y=\"106.360547\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(7.2 110.159766)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#md13bc7e0ae\" x=\"39.65\" y=\"130.200879\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1250 -->\n", "      <g transform=\"translate(7.2 134.000098)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 39.65 141.119751 \n", "L 39.65 10.951538 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 234.95 141.119751 \n", "L 234.95 10.951538 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 39.65 141.119751 \n", "L 234.95 141.119751 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 39.65 10.951538 \n", "L 234.95 10.951538 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf593424c6e\">\n", "   <rect x=\"39.65\" y=\"10.951538\" width=\"195.3\" height=\"130.168213\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "import torch\n", "import torchvision\n", "from torch import nn\n", "from d2l import torch as d2l\n", "\n", "d2l.set_figsize()\n", "content_img = d2l.Image.open('../img/rainier.jpg')\n", "d2l.plt.imshow(content_img);"]}, {"cell_type": "code", "execution_count": 2, "id": "ec590a65", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:29.601550Z", "iopub.status.busy": "2023-08-18T07:23:29.600514Z", "iopub.status.idle": "2023-08-18T07:23:30.096132Z", "shell.execute_reply": "2023-08-18T07:23:30.095315Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"241.30025pt\" height=\"170.720719pt\" viewBox=\"0 0 241.30025 170.720719\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:23:29.959273</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 170.720719 \n", "L 241.30025 170.720719 \n", "L 241.30025 0 \n", "L 0 0 \n", "L 0 170.720719 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 39.65 146.842594 \n", "L 234.10025 146.842594 \n", "L 234.10025 10.942594 \n", "L 39.65 10.942594 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p3577a2b1c2)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagef4be2f70ba\" transform=\"scale(1 -1)translate(0 -136)\" x=\"39.65\" y=\"-10.842594\" width=\"195\" height=\"136\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m8ab82a1f07\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8ab82a1f07\" x=\"39.706625\" y=\"146.842594\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(36.525375 161.441031)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m8ab82a1f07\" x=\"96.331625\" y=\"146.842594\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(86.787875 161.441031)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m8ab82a1f07\" x=\"152.956625\" y=\"146.842594\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(140.231625 161.441031)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m8ab82a1f07\" x=\"209.581625\" y=\"146.842594\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1500 -->\n", "      <g transform=\"translate(196.856625 161.441031)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"m1538ff1789\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1538ff1789\" x=\"39.65\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.2875 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m1538ff1789\" x=\"39.65\" y=\"33.649219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(13.5625 37.448437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m1538ff1789\" x=\"39.65\" y=\"56.299219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(13.5625 60.098437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m1538ff1789\" x=\"39.65\" y=\"78.949219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(13.5625 82.748437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m1538ff1789\" x=\"39.65\" y=\"101.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(13.5625 105.398437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m1538ff1789\" x=\"39.65\" y=\"124.249219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(7.2 128.048437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 39.65 146.842594 \n", "L 39.65 10.942594 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 234.10025 146.842594 \n", "L 234.10025 10.942594 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 39.65 146.842594 \n", "L 234.10025 146.842594 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 39.65 10.942594 \n", "L 234.10025 10.942594 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p3577a2b1c2\">\n", "   <rect x=\"39.65\" y=\"10.942594\" width=\"194.45025\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["style_img = d2l.Image.open('../img/autumn-oak.jpg')\n", "d2l.plt.imshow(style_img);"]}, {"cell_type": "markdown", "id": "ddc886a6", "metadata": {"origin_pos": 6}, "source": ["## [**预处理和后处理**]\n", "\n", "下面，定义图像的预处理函数和后处理函数。\n", "预处理函数`preprocess`对输入图像在RGB三个通道分别做标准化，并将结果变换成卷积神经网络接受的输入格式。\n", "后处理函数`postprocess`则将输出图像中的像素值还原回标准化之前的值。\n", "由于图像打印函数要求每个像素的浮点数值在0～1之间，我们对小于0和大于1的值分别取0和1。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6f351192", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:30.103272Z", "iopub.status.busy": "2023-08-18T07:23:30.102388Z", "iopub.status.idle": "2023-08-18T07:23:30.112076Z", "shell.execute_reply": "2023-08-18T07:23:30.111052Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["rgb_mean = torch.tensor([0.485, 0.456, 0.406])\n", "rgb_std = torch.tensor([0.229, 0.224, 0.225])\n", "\n", "def preprocess(img, image_shape):\n", "    transforms = torchvision.transforms.Compose([\n", "        torchvision.transforms.Resize(image_shape),\n", "        torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "        torchvision.transforms.Normalize(mean=rgb_mean, std=rgb_std)])\n", "    return transforms(img).unsqueeze(0)\n", "\n", "def postprocess(img):\n", "    img = img[0].to(rgb_std.device)\n", "    img = torch.clamp(img.permute(1, 2, 0) * rgb_std + rgb_mean, 0, 1)\n", "    return torchvision.transforms.ToPILImage()(img.permute(2, 0, 1))"]}, {"cell_type": "markdown", "id": "54e9f5d8", "metadata": {"origin_pos": 10}, "source": ["## [**抽取图像特征**]\n", "\n", "我们使用基于ImageNet数据集预训练的VGG-19模型来抽取图像特征 :cite:`Gatys.Ecker.Bethge.2016`。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "f562ee81", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:30.117700Z", "iopub.status.busy": "2023-08-18T07:23:30.116834Z", "iopub.status.idle": "2023-08-18T07:23:42.885822Z", "shell.execute_reply": "2023-08-18T07:23:42.884582Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading: \"https://download.pytorch.org/models/vgg19-dcbb9e9d.pth\" to /home/<USER>/.cache/torch/hub/checkpoints/vgg19-dcbb9e9d.pth\n"]}, {"data": {"application/json": {"ascii": false, "bar_format": null, "colour": null, "elapsed": 0.007704734802246094, "initial": 0, "n": 0, "ncols": null, "nrows": null, "postfix": null, "prefix": "", "rate": null, "total": 574673361, "unit": "B", "unit_divisor": 1024, "unit_scale": true}, "application/vnd.jupyter.widget-view+json": {"model_id": "0a21ebb04f6e4afe9df09a7d7c6a0fe0", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0.00/548M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pretrained_net = torchvision.models.vgg19(pretrained=True)"]}, {"cell_type": "markdown", "id": "85fcfc18", "metadata": {"origin_pos": 14}, "source": ["为了抽取图像的内容特征和风格特征，我们可以选择VGG网络中某些层的输出。\n", "一般来说，越靠近输入层，越容易抽取图像的细节信息；反之，则越容易抽取图像的全局信息。\n", "为了避免合成图像过多保留内容图像的细节，我们选择VGG较靠近输出的层，即*内容层*，来输出图像的内容特征。\n", "我们还从VGG中选择不同层的输出来匹配局部和全局的风格，这些图层也称为*风格层*。\n", "正如 :numref:`sec_vgg`中所介绍的，VGG网络使用了5个卷积块。\n", "实验中，我们选择第四卷积块的最后一个卷积层作为内容层，选择每个卷积块的第一个卷积层作为风格层。\n", "这些层的索引可以通过打印`pretrained_net`实例获取。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "9c71d01f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:42.891197Z", "iopub.status.busy": "2023-08-18T07:23:42.890918Z", "iopub.status.idle": "2023-08-18T07:23:42.895437Z", "shell.execute_reply": "2023-08-18T07:23:42.894539Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["style_layers, content_layers = [0, 5, 10, 19, 28], [25]"]}, {"cell_type": "markdown", "id": "a02682f4", "metadata": {"origin_pos": 16}, "source": ["使用VGG层抽取特征时，我们只需要用到从输入层到最靠近输出层的内容层或风格层之间的所有层。\n", "下面构建一个新的网络`net`，它只保留需要用到的VGG的所有层。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "75c742e5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:42.901208Z", "iopub.status.busy": "2023-08-18T07:23:42.900137Z", "iopub.status.idle": "2023-08-18T07:23:42.906883Z", "shell.execute_reply": "2023-08-18T07:23:42.905548Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [], "source": ["net = nn.Sequential(*[pretrained_net.features[i] for i in\n", "                      range(max(content_layers + style_layers) + 1)])"]}, {"cell_type": "markdown", "id": "932aec9e", "metadata": {"origin_pos": 19}, "source": ["给定输入`X`，如果我们简单地调用前向传播`net(X)`，只能获得最后一层的输出。\n", "由于我们还需要中间层的输出，因此这里我们逐层计算，并保留内容层和风格层的输出。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "5cd1ccc0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:42.911258Z", "iopub.status.busy": "2023-08-18T07:23:42.910587Z", "iopub.status.idle": "2023-08-18T07:23:42.929103Z", "shell.execute_reply": "2023-08-18T07:23:42.927620Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [], "source": ["def extract_features(X, content_layers, style_layers):\n", "    contents = []\n", "    styles = []\n", "    for i in range(len(net)):\n", "        X = net[i](X)\n", "        if i in style_layers:\n", "            styles.append(X)\n", "        if i in content_layers:\n", "            contents.append(X)\n", "    return contents, styles"]}, {"cell_type": "markdown", "id": "49db6d46", "metadata": {"origin_pos": 21}, "source": ["下面定义两个函数：`get_contents`函数对内容图像抽取内容特征；\n", "`get_styles`函数对风格图像抽取风格特征。\n", "因为在训练时无须改变预训练的VGG的模型参数，所以我们可以在训练开始之前就提取出内容特征和风格特征。\n", "由于合成图像是风格迁移所需迭代的模型参数，我们只能在训练过程中通过调用`extract_features`函数来抽取合成图像的内容特征和风格特征。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "f80b015d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:42.934583Z", "iopub.status.busy": "2023-08-18T07:23:42.933552Z", "iopub.status.idle": "2023-08-18T07:23:42.955842Z", "shell.execute_reply": "2023-08-18T07:23:42.954343Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_contents(image_shape, device):\n", "    content_X = preprocess(content_img, image_shape).to(device)\n", "    contents_Y, _ = extract_features(content_X, content_layers, style_layers)\n", "    return content_X, contents_Y\n", "\n", "def get_styles(image_shape, device):\n", "    style_X = preprocess(style_img, image_shape).to(device)\n", "    _, styles_Y = extract_features(style_X, content_layers, style_layers)\n", "    return style_X, styles_Y"]}, {"cell_type": "markdown", "id": "0a04d737", "metadata": {"origin_pos": 25}, "source": ["## [**定义损失函数**]\n", "\n", "下面我们来描述风格迁移的损失函数。\n", "它由内容损失、风格损失和全变分损失3部分组成。\n", "\n", "### 内容损失\n", "\n", "与线性回归中的损失函数类似，内容损失通过平方误差函数衡量合成图像与内容图像在内容特征上的差异。\n", "平方误差函数的两个输入均为`extract_features`函数计算所得到的内容层的输出。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "1048e5c2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:42.961660Z", "iopub.status.busy": "2023-08-18T07:23:42.961194Z", "iopub.status.idle": "2023-08-18T07:23:42.967349Z", "shell.execute_reply": "2023-08-18T07:23:42.966138Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [], "source": ["def content_loss(Y_hat, Y):\n", "    # 我们从动态计算梯度的树中分离目标：\n", "    # 这是一个规定的值，而不是一个变量。\n", "    return torch.square(Y_hat - <PERSON>.detach()).mean()"]}, {"cell_type": "markdown", "id": "71b083a8", "metadata": {"origin_pos": 29}, "source": ["### 风格损失\n", "\n", "风格损失与内容损失类似，也通过平方误差函数衡量合成图像与风格图像在风格上的差异。\n", "为了表达风格层输出的风格，我们先通过`extract_features`函数计算风格层的输出。\n", "假设该输出的样本数为1，通道数为$c$，高和宽分别为$h$和$w$，我们可以将此输出转换为矩阵$\\mathbf{X}$，其有$c$行和$hw$列。\n", "这个矩阵可以被看作由$c$个长度为$hw$的向量$\\mathbf{x}_1, \\ldots, \\mathbf{x}_c$组合而成的。其中向量$\\mathbf{x}_i$代表了通道$i$上的风格特征。\n", "\n", "在这些向量的*格拉姆矩阵*$\\mathbf{X}\\mathbf{X}^\\top \\in \\mathbb{R}^{c \\times c}$中，$i$行$j$列的元素$x_{ij}$即向量$\\mathbf{x}_i$和$\\mathbf{x}_j$的内积。它表达了通道$i$和通道$j$上风格特征的相关性。我们用这样的格拉姆矩阵来表达风格层输出的风格。\n", "需要注意的是，当$hw$的值较大时，格拉姆矩阵中的元素容易出现较大的值。\n", "此外，格拉姆矩阵的高和宽皆为通道数$c$。\n", "为了让风格损失不受这些值的大小影响，下面定义的`gram`函数将格拉姆矩阵除以了矩阵中元素的个数，即$chw$。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "207704c1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:42.973259Z", "iopub.status.busy": "2023-08-18T07:23:42.971937Z", "iopub.status.idle": "2023-08-18T07:23:42.979314Z", "shell.execute_reply": "2023-08-18T07:23:42.978380Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [], "source": ["def gram(X):\n", "    num_channels, n = X.shape[1], X.numel() // X.shape[1]\n", "    X = X.reshape((num_channels, n))\n", "    return torch.matmul(X, X.T) / (num_channels * n)"]}, {"cell_type": "markdown", "id": "3c362780", "metadata": {"origin_pos": 31}, "source": ["自然地，风格损失的平方误差函数的两个格拉姆矩阵输入分别基于合成图像与风格图像的风格层输出。这里假设基于风格图像的格拉姆矩阵`gram_Y`已经预先计算好了。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "3491c1fd", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:42.984195Z", "iopub.status.busy": "2023-08-18T07:23:42.983439Z", "iopub.status.idle": "2023-08-18T07:23:42.988675Z", "shell.execute_reply": "2023-08-18T07:23:42.987781Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [], "source": ["def style_loss(Y_hat, gram_Y):\n", "    return torch.square(gram(Y_hat) - gram_Y.detach()).mean()"]}, {"cell_type": "markdown", "id": "44caeefb", "metadata": {"origin_pos": 35}, "source": ["### 全变分损失\n", "\n", "有时候，我们学到的合成图像里面有大量高频噪点，即有特别亮或者特别暗的颗粒像素。\n", "一种常见的去噪方法是*全变分去噪*（total variation denoising）：\n", "假设$x_{i, j}$表示坐标$(i, j)$处的像素值，降低全变分损失\n", "\n", "$$\\sum_{i, j} \\left|x_{i, j} - x_{i+1, j}\\right| + \\left|x_{i, j} - x_{i, j+1}\\right|$$\n", "\n", "能够尽可能使邻近的像素值相似。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "2173f076", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:42.994009Z", "iopub.status.busy": "2023-08-18T07:23:42.992920Z", "iopub.status.idle": "2023-08-18T07:23:43.000113Z", "shell.execute_reply": "2023-08-18T07:23:42.998890Z"}, "origin_pos": 36, "tab": ["pytorch"]}, "outputs": [], "source": ["def tv_loss(Y_hat):\n", "    return 0.5 * (torch.abs(Y_hat[:, :, 1:, :] - Y_hat[:, :, :-1, :]).mean() +\n", "                  torch.abs(Y_hat[:, :, :, 1:] - Y_hat[:, :, :, :-1]).mean())"]}, {"cell_type": "markdown", "id": "335a9e26", "metadata": {"origin_pos": 37}, "source": ["### 损失函数\n", "\n", "[**风格转移的损失函数是内容损失、风格损失和总变化损失的加权和**]。\n", "通过调节这些权重超参数，我们可以权衡合成图像在保留内容、迁移风格以及去噪三方面的相对重要性。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "7b2d722a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:43.004890Z", "iopub.status.busy": "2023-08-18T07:23:43.004502Z", "iopub.status.idle": "2023-08-18T07:23:43.012475Z", "shell.execute_reply": "2023-08-18T07:23:43.011392Z"}, "origin_pos": 38, "tab": ["pytorch"]}, "outputs": [], "source": ["content_weight, style_weight, tv_weight = 1, 1e3, 10\n", "\n", "def compute_loss(X, contents_Y_hat, styles_Y_hat, contents_Y, styles_Y_gram):\n", "    # 分别计算内容损失、风格损失和全变分损失\n", "    contents_l = [content_loss(Y_hat, Y) * content_weight for Y_hat, Y in zip(\n", "        contents_Y_hat, contents_Y)]\n", "    styles_l = [style_loss(Y_hat, Y) * style_weight for Y_hat, Y in zip(\n", "        styles_Y_hat, styles_Y_gram)]\n", "    tv_l = tv_loss(X) * tv_weight\n", "    # 对所有损失求和\n", "    l = sum(10 * styles_l + contents_l + [tv_l])\n", "    return contents_l, styles_l, tv_l, l"]}, {"cell_type": "markdown", "id": "9f90235c", "metadata": {"origin_pos": 39}, "source": ["## [**初始化合成图像**]\n", "\n", "在风格迁移中，合成的图像是训练期间唯一需要更新的变量。因此，我们可以定义一个简单的模型`SynthesizedImage`，并将合成的图像视为模型参数。模型的前向传播只需返回模型参数即可。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "a4f99f98", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:43.018222Z", "iopub.status.busy": "2023-08-18T07:23:43.017815Z", "iopub.status.idle": "2023-08-18T07:23:43.031792Z", "shell.execute_reply": "2023-08-18T07:23:43.030715Z"}, "origin_pos": 41, "tab": ["pytorch"]}, "outputs": [], "source": ["class SynthesizedImage(nn.Module):\n", "    def __init__(self, img_shape, **kwargs):\n", "        super(SynthesizedImage, self).__init__(**kwargs)\n", "        self.weight = nn.Parameter(torch.rand(*img_shape))\n", "\n", "    def forward(self):\n", "        return self.weight"]}, {"cell_type": "markdown", "id": "f7a98b0a", "metadata": {"origin_pos": 43}, "source": ["下面，我们定义`get_inits`函数。该函数创建了合成图像的模型实例，并将其初始化为图像`X`。风格图像在各个风格层的格拉姆矩阵`styles_Y_gram`将在训练前预先计算好。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "3055aa3b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:43.037767Z", "iopub.status.busy": "2023-08-18T07:23:43.037371Z", "iopub.status.idle": "2023-08-18T07:23:43.044082Z", "shell.execute_reply": "2023-08-18T07:23:43.043078Z"}, "origin_pos": 45, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_inits(X, device, lr, styles_Y):\n", "    gen_img = SynthesizedImage(X.shape).to(device)\n", "    gen_img.weight.data.copy_(X.data)\n", "    trainer = torch.optim.Adam(gen_img.parameters(), lr=lr)\n", "    styles_Y_gram = [gram(Y) for Y in styles_Y]\n", "    return gen_img(), styles_Y_gram, trainer"]}, {"cell_type": "markdown", "id": "6b86cbc2", "metadata": {"origin_pos": 47}, "source": ["## [**训练模型**]\n", "\n", "在训练模型进行风格迁移时，我们不断抽取合成图像的内容特征和风格特征，然后计算损失函数。下面定义了训练循环。\n"]}, {"cell_type": "code", "execution_count": 16, "id": "4fe3c6e1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:43.048455Z", "iopub.status.busy": "2023-08-18T07:23:43.048174Z", "iopub.status.idle": "2023-08-18T07:23:43.056676Z", "shell.execute_reply": "2023-08-18T07:23:43.055451Z"}, "origin_pos": 49, "tab": ["pytorch"]}, "outputs": [], "source": ["def train(X, contents_Y, styles_Y, device, lr, num_epochs, lr_decay_epoch):\n", "    X, styles_Y_gram, trainer = get_inits(X, device, lr, styles_Y)\n", "    scheduler = torch.optim.lr_scheduler.Step<PERSON>(trainer, lr_decay_epoch, 0.8)\n", "    animator = d2l.Animator(xlabel='epoch', ylabel='loss',\n", "                            xlim=[10, num_epochs],\n", "                            legend=['content', 'style', 'TV'],\n", "                            ncols=2, figsize=(7, 2.5))\n", "    for epoch in range(num_epochs):\n", "        trainer.zero_grad()\n", "        contents_Y_hat, styles_Y_hat = extract_features(\n", "            X, content_layers, style_layers)\n", "        contents_l, styles_l, tv_l, l = compute_loss(\n", "            X, contents_Y_hat, styles_Y_hat, contents_Y, styles_Y_gram)\n", "        l.backward()\n", "        trainer.step()\n", "        scheduler.step()\n", "        if (epoch + 1) % 10 == 0:\n", "            animator.axes[1].imshow(postprocess(X))\n", "            animator.add(epoch + 1, [float(sum(contents_l)),\n", "                                     float(sum(styles_l)), float(tv_l)])\n", "    return X"]}, {"cell_type": "markdown", "id": "7d70c1ac", "metadata": {"origin_pos": 51}, "source": ["现在我们[**训练模型**]：\n", "首先将内容图像和风格图像的高和宽分别调整为300和450像素，用内容图像来初始化合成图像。\n"]}, {"cell_type": "code", "execution_count": 17, "id": "c0846fe5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:23:43.061124Z", "iopub.status.busy": "2023-08-18T07:23:43.060316Z", "iopub.status.idle": "2023-08-18T07:24:35.646273Z", "shell.execute_reply": "2023-08-18T07:24:35.645421Z"}, "origin_pos": 53, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"432.040625pt\" height=\"180.65625pt\" viewBox=\"0 0 432.**********.65625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:24:35.138148</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 180.65625 \n", "L 432.**********.65625 \n", "L 432.040625 0 \n", "L 0 0 \n", "L 0 180.65625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 34.**********.1 \n", "L 211.78608 143.1 \n", "L 211.78608 7.2 \n", "L 34.240625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 66.851015 143.1 \n", "L 66.851015 7.2 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m4be61c3647\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m4be61c3647\" x=\"66.851015\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(57.307265 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 103.084781 143.1 \n", "L 103.084781 7.2 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m4be61c3647\" x=\"103.084781\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(93.541031 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 139.318547 143.1 \n", "L 139.318547 7.2 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m4be61c3647\" x=\"139.318547\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(129.774797 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 175.552313 143.1 \n", "L 175.552313 7.2 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m4be61c3647\" x=\"175.552313\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(166.008563 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 211.78608 143.1 \n", "L 211.78608 7.2 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m4be61c3647\" x=\"211.78608\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(202.24233 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(107.785227 171.376563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 34.240625 137.533894 \n", "L 211.78608 137.533894 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mb83c81ff37\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb83c81ff37\" x=\"34.240625\" y=\"137.533894\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 141.333113)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 34.240625 106.138959 \n", "L 211.78608 106.138959 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mb83c81ff37\" x=\"34.240625\" y=\"106.138959\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(20.878125 109.938178)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 34.240625 74.744024 \n", "L 211.78608 74.744024 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mb83c81ff37\" x=\"34.240625\" y=\"74.744024\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(20.878125 78.543243)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 34.240625 43.349089 \n", "L 211.78608 43.349089 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mb83c81ff37\" x=\"34.240625\" y=\"43.349089\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(20.878125 47.148308)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 34.240625 11.954154 \n", "L 211.78608 11.954154 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mb83c81ff37\" x=\"34.240625\" y=\"11.954154\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(20.878125 15.753373)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798437 84.807812)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 34.240625 109.061259 \n", "L 37.864002 110.882215 \n", "L 41.487378 110.054333 \n", "L 45.110755 114.252805 \n", "L 48.734131 116.359081 \n", "L 52.357508 117.767559 \n", "L 55.980885 118.479394 \n", "L 59.604261 118.901301 \n", "L 63.227638 118.97814 \n", "L 66.851015 118.756991 \n", "L 70.474391 119.430773 \n", "L 74.097768 121.542525 \n", "L 77.721144 121.638404 \n", "L 81.344521 122.999694 \n", "L 84.967898 122.283372 \n", "L 88.591274 123.087071 \n", "L 92.214651 123.466514 \n", "L 95.838028 122.909664 \n", "L 99.461404 122.318352 \n", "L 103.084781 123.446419 \n", "L 106.708157 124.089647 \n", "L 110.331534 124.018749 \n", "L 113.954911 124.216862 \n", "L 117.578287 124.323213 \n", "L 121.201664 124.59281 \n", "L 124.825041 124.703988 \n", "L 128.448417 124.554394 \n", "L 132.071794 124.438629 \n", "L 135.69517 125.109181 \n", "L 139.318547 124.942926 \n", "L 142.941924 125.021188 \n", "L 146.5653 125.201289 \n", "L 150.188677 125.163341 \n", "L 153.812054 125.078174 \n", "L 157.43543 125.104793 \n", "L 161.058807 125.247587 \n", "L 164.682183 125.30294 \n", "L 168.30556 125.351529 \n", "L 171.928937 125.274924 \n", "L 175.552313 125.39723 \n", "L 179.17569 125.32993 \n", "L 182.799067 125.414943 \n", "L 186.422443 125.333535 \n", "L 190.04582 125.433289 \n", "L 193.669196 125.419917 \n", "L 197.292573 125.440662 \n", "L 200.91595 125.502463 \n", "L 204.539326 125.415984 \n", "L 208.162703 125.457078 \n", "L 211.78608 125.51278 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 34.240625 109.397379 \n", "L 37.864002 124.8406 \n", "L 41.487378 131.908185 \n", "L 45.110755 134.850762 \n", "L 48.734131 136.049426 \n", "L 52.357508 136.244636 \n", "L 55.980885 135.486909 \n", "L 59.604261 136.375678 \n", "L 63.227638 136.126154 \n", "L 66.851015 134.984467 \n", "L 70.474391 136.351127 \n", "L 74.097768 136.421959 \n", "L 77.721144 136.8125 \n", "L 81.344521 136.405139 \n", "L 84.967898 136.743984 \n", "L 88.591274 136.853156 \n", "L 92.214651 136.829876 \n", "L 95.838028 136.630907 \n", "L 99.461404 136.853098 \n", "L 103.084781 136.886346 \n", "L 106.708157 136.893155 \n", "L 110.331534 136.886841 \n", "L 113.954911 136.845402 \n", "L 117.578287 136.896708 \n", "L 121.201664 136.900879 \n", "L 124.825041 136.898547 \n", "L 128.448417 136.83721 \n", "L 132.071794 136.897044 \n", "L 135.69517 136.756657 \n", "L 139.318547 136.883986 \n", "L 142.941924 136.917267 \n", "L 146.5653 136.901747 \n", "L 150.188677 136.895969 \n", "L 153.812054 136.895746 \n", "L 157.43543 136.922727 \n", "L 161.058807 136.918941 \n", "L 164.682183 136.912027 \n", "L 168.30556 136.912483 \n", "L 171.928937 136.918133 \n", "L 175.552313 136.897241 \n", "L 179.17569 136.921658 \n", "L 182.799067 136.917108 \n", "L 186.422443 136.911072 \n", "L 190.04582 136.917668 \n", "L 193.669196 136.920421 \n", "L 197.292573 136.919894 \n", "L 200.91595 136.916358 \n", "L 204.539326 136.918369 \n", "L 208.162703 136.920729 \n", "L 211.78608 136.919432 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 34.240625 13.377273 \n", "L 37.864002 15.591421 \n", "L 41.487378 35.02597 \n", "L 45.110755 52.828819 \n", "L 48.734131 69.668975 \n", "L 52.357508 80.905807 \n", "L 55.980885 83.778089 \n", "L 59.604261 85.392906 \n", "L 63.227638 92.91397 \n", "L 66.851015 88.046211 \n", "L 70.474391 88.849515 \n", "L 74.097768 95.81037 \n", "L 77.721144 100.919379 \n", "L 81.344521 104.800886 \n", "L 84.967898 105.209657 \n", "L 88.591274 107.35201 \n", "L 92.214651 109.358887 \n", "L 95.838028 110.889889 \n", "L 99.461404 108.767177 \n", "L 103.084781 109.696678 \n", "L 106.708157 111.682503 \n", "L 110.331534 112.519929 \n", "L 113.954911 113.591572 \n", "L 117.578287 114.017297 \n", "L 121.201664 114.755829 \n", "L 124.825041 115.232535 \n", "L 128.448417 115.767677 \n", "L 132.071794 115.118959 \n", "L 135.69517 115.708735 \n", "L 139.318547 116.046876 \n", "L 142.941924 116.568347 \n", "L 146.5653 116.99093 \n", "L 150.188677 117.040249 \n", "L 153.812054 117.333496 \n", "L 157.43543 117.499034 \n", "L 161.058807 117.779633 \n", "L 164.682183 117.940245 \n", "L 168.30556 118.093301 \n", "L 171.928937 118.225575 \n", "L 175.552313 118.31525 \n", "L 179.17569 118.4352 \n", "L 182.799067 118.572627 \n", "L 186.422443 118.636385 \n", "L 190.04582 118.716454 \n", "L 193.669196 118.829825 \n", "L 197.292573 118.935255 \n", "L 200.91595 119.020784 \n", "L 204.539326 119.069728 \n", "L 208.162703 119.129046 \n", "L 211.78608 119.20152 \n", "\" clip-path=\"url(#p7f6595e6a4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 34.**********.1 \n", "L 34.240625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 211.78608 143.1 \n", "L 211.78608 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 34.**********.1 \n", "L 211.78608 143.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.240625 7.2 \n", "L 211.78608 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 134.500142 59.234375 \n", "L 204.78608 59.234375 \n", "Q 206.78608 59.234375 206.78608 57.234375 \n", "L 206.78608 14.2 \n", "Q 206.78608 12.2 204.78608 12.2 \n", "L 134.500142 12.2 \n", "Q 132.500142 12.2 132.500142 14.2 \n", "L 132.500142 57.234375 \n", "Q 132.500142 59.234375 134.500142 59.234375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_24\">\n", "     <path d=\"M 136.500142 20.298438 \n", "L 146.500142 20.298438 \n", "L 156.500142 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- content -->\n", "     <g transform=\"translate(164.500142 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-63\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"54.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"116.162109\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"179.541016\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"218.75\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"280.273438\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"343.652344\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_25\">\n", "     <path d=\"M 136.500142 34.976562 \n", "L 146.500142 34.976562 \n", "L 156.500142 34.976562 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- style -->\n", "     <g transform=\"translate(164.500142 38.476562)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-73\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"52.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"91.308594\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"150.488281\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"178.271484\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 136.500142 49.654688 \n", "L 146.500142 49.654688 \n", "L 156.500142 49.654688 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- TV -->\n", "     <g transform=\"translate(164.500142 53.154688)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-56\" d=\"M 1831 0 \n", "L 50 4666 \n", "L 709 4666 \n", "L 2188 738 \n", "L 3669 4666 \n", "L 4325 4666 \n", "L 2547 0 \n", "L 1831 0 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-56\" x=\"61.083984\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 247.29517 134.331818 \n", "L 424.**********.331818 \n", "L 424.840625 15.968182 \n", "L 247.29517 15.968182 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p8dcbc0444c)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageadbc6424ac\" transform=\"scale(1 -1)translate(0 -119)\" x=\"247\" y=\"-15.65625\" width=\"178\" height=\"119\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m4be61c3647\" x=\"247.492443\" y=\"134.331818\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(244.311193 148.930256)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m4be61c3647\" x=\"286.946989\" y=\"134.331818\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(277.403239 148.930256)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m4be61c3647\" x=\"326.401534\" y=\"134.331818\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(316.857784 148.930256)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m4be61c3647\" x=\"365.85608\" y=\"134.331818\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(356.31233 148.930256)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m4be61c3647\" x=\"405.310625\" y=\"134.331818\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(395.766875 148.930256)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#mb83c81ff37\" x=\"247.29517\" y=\"16.165455\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(233.93267 19.964673)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#mb83c81ff37\" x=\"247.29517\" y=\"55.62\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(221.20767 59.419219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#mb83c81ff37\" x=\"247.29517\" y=\"95.074545\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(221.20767 98.873764)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 247.29517 134.331818 \n", "L 247.29517 15.968182 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 424.**********.331818 \n", "L 424.840625 15.968182 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 247.29517 134.331818 \n", "L 424.**********.331818 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 247.29517 15.968182 \n", "L 424.840625 15.968182 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p7f6595e6a4\">\n", "   <rect x=\"34.240625\" y=\"7.2\" width=\"177.545455\" height=\"135.9\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p8dcbc0444c\">\n", "   <rect x=\"247.29517\" y=\"15.968182\" width=\"177.545455\" height=\"118.363636\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 504x180 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["device, image_shape = d2l.try_gpu(), (300, 450)\n", "net = net.to(device)\n", "content_X, contents_Y = get_contents(image_shape, device)\n", "_, styles_Y = get_styles(image_shape, device)\n", "output = train(content_X, contents_Y, styles_Y, device, 0.3, 500, 50)"]}, {"cell_type": "markdown", "id": "7f5c2480", "metadata": {"origin_pos": 55}, "source": ["我们可以看到，合成图像保留了内容图像的风景和物体，并同时迁移了风格图像的色彩。例如，合成图像具有与风格图像中一样的色彩块，其中一些甚至具有画笔笔触的细微纹理。\n", "\n", "## 小结\n", "\n", "* 风格迁移常用的损失函数由3部分组成：（1）内容损失使合成图像与内容图像在内容特征上接近；（2）风格损失令合成图像与风格图像在风格特征上接近；（3）全变分损失则有助于减少合成图像中的噪点。\n", "* 我们可以通过预训练的卷积神经网络来抽取图像的特征，并通过最小化损失函数来不断更新合成图像来作为模型参数。\n", "* 我们使用格拉姆矩阵表达风格层输出的风格。\n", "\n", "## 练习\n", "\n", "1. 选择不同的内容和风格层，输出有什么变化？\n", "1. 调整损失函数中的权重超参数。输出是否保留更多内容或减少更多噪点？\n", "1. 替换实验中的内容图像和风格图像，能创作出更有趣的合成图像吗？\n", "1. 我们可以对文本使用风格迁移吗？提示:可以参阅调查报告 :cite:`Hu<PERSON>Lee.Aggarwal.ea.2020`。\n"]}, {"cell_type": "markdown", "id": "8888edcd", "metadata": {"origin_pos": 57, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/3300)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": [], "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {"0a21ebb04f6e4afe9df09a7d7c6a0fe0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3386849c303b40d18895bb91db97e325", "IPY_MODEL_155d363cdf40442e8faf86c2f0def49d", "IPY_MODEL_6840bc285801445eafe45e9cfc4a3216"], "layout": "IPY_MODEL_96dfbfe851a544c09ae13797ba4d4198", "tabbable": null, "tooltip": null}}, "155d363cdf40442e8faf86c2f0def49d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_6e8fb617074d452eb01dbfe715d3827f", "max": 574673361.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_a24b158989e04d0e90ba67a8b670ce52", "tabbable": null, "tooltip": null, "value": 574673361.0}}, "2d45b8677d764d7f9e04f2ab4f38d40b": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3386849c303b40d18895bb91db97e325": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_2d45b8677d764d7f9e04f2ab4f38d40b", "placeholder": "​", "style": "IPY_MODEL_bc6bcbf06ef44f2585d8d82ac182ea56", "tabbable": null, "tooltip": null, "value": "100%"}}, "5223c9213fef443497e360398c21149f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "57464fe9afd448b0a23eee081a9e085d": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6840bc285801445eafe45e9cfc4a3216": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_57464fe9afd448b0a23eee081a9e085d", "placeholder": "​", "style": "IPY_MODEL_5223c9213fef443497e360398c21149f", "tabbable": null, "tooltip": null, "value": " 548M/548M [00:10&lt;00:00, 69.9MB/s]"}}, "6e8fb617074d452eb01dbfe715d3827f": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "96dfbfe851a544c09ae13797ba4d4198": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a24b158989e04d0e90ba67a8b670ce52": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "bc6bcbf06ef44f2585d8d82ac182ea56": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}
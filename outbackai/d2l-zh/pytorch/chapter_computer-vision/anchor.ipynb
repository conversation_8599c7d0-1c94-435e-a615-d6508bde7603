{"cells": [{"cell_type": "markdown", "id": "403799f7", "metadata": {"origin_pos": 0}, "source": ["# 锚框\n", ":label:`sec_anchor`\n", "\n", "目标检测算法通常会在输入图像中采样大量的区域，然后判断这些区域中是否包含我们感兴趣的目标，并调整区域边界从而更准确地预测目标的*真实边界框*（ground-truth bounding box）。\n", "不同的模型使用的区域采样方法可能不同。\n", "这里我们介绍其中的一种方法：以每个像素为中心，生成多个缩放比和宽高比（aspect ratio）不同的边界框。\n", "这些边界框被称为*锚框*（anchor box）我们将在 :numref:`sec_ssd`中设计一个基于锚框的目标检测模型。\n", "\n", "首先，让我们修改输出精度，以获得更简洁的输出。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "e079962e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:42.677769Z", "iopub.status.busy": "2023-08-18T07:00:42.676695Z", "iopub.status.idle": "2023-08-18T07:00:45.106116Z", "shell.execute_reply": "2023-08-18T07:00:45.104773Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "from d2l import torch as d2l\n", "\n", "torch.set_printoptions(2)  # 精简输出精度"]}, {"cell_type": "markdown", "id": "dc5b92b8", "metadata": {"origin_pos": 4}, "source": ["## 生成多个锚框\n", "\n", "假设输入图像的高度为$h$，宽度为$w$。\n", "我们以图像的每个像素为中心生成不同形状的锚框：*缩放比*为$s\\in (0, 1]$，*宽高比*为$r > 0$。\n", "那么[**锚框的宽度和高度分别是$hs\\sqrt{r}$和$hs/\\sqrt{r}$。**]\n", "请注意，当中心位置给定时，已知宽和高的锚框是确定的。\n", "\n", "要生成多个不同形状的锚框，让我们设置许多缩放比（scale）取值$s_1,\\ldots, s_n$和许多宽高比（aspect ratio）取值$r_1,\\ldots, r_m$。\n", "当使用这些比例和长宽比的所有组合以每个像素为中心时，输入图像将总共有$whnm$个锚框。\n", "尽管这些锚框可能会覆盖所有真实边界框，但计算复杂性很容易过高。\n", "在实践中，(**我们只考虑**)包含$s_1$或$r_1$的(**组合：**)\n", "\n", "(**\n", "$$(s_1, r_1), (s_1, r_2), \\ldots, (s_1, r_m), (s_2, r_1), (s_3, r_1), \\ldots, (s_n, r_1).$$\n", "**)\n", "\n", "也就是说，以同一像素为中心的锚框的数量是$n+m-1$。\n", "对于整个输入图像，将共生成$wh(n+m-1)$个锚框。\n", "\n", "上述生成锚框的方法在下面的`multibox_prior`函数中实现。\n", "我们指定输入图像、尺寸列表和宽高比列表，然后此函数将返回所有的锚框。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "4c5fb635", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:45.112186Z", "iopub.status.busy": "2023-08-18T07:00:45.111657Z", "iopub.status.idle": "2023-08-18T07:00:45.126939Z", "shell.execute_reply": "2023-08-18T07:00:45.125859Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def multibox_prior(data, sizes, ratios):\n", "    \"\"\"生成以每个像素为中心具有不同形状的锚框\"\"\"\n", "    in_height, in_width = data.shape[-2:]\n", "    device, num_sizes, num_ratios = data.device, len(sizes), len(ratios)\n", "    boxes_per_pixel = (num_sizes + num_ratios - 1)\n", "    size_tensor = torch.tensor(sizes, device=device)\n", "    ratio_tensor = torch.tensor(ratios, device=device)\n", "\n", "    # 为了将锚点移动到像素的中心，需要设置偏移量。\n", "    # 因为一个像素的高为1且宽为1，我们选择偏移我们的中心0.5\n", "    offset_h, offset_w = 0.5, 0.5\n", "    steps_h = 1.0 / in_height  # 在y轴上缩放步长\n", "    steps_w = 1.0 / in_width  # 在x轴上缩放步长\n", "\n", "    # 生成锚框的所有中心点\n", "    center_h = (torch.arange(in_height, device=device) + offset_h) * steps_h\n", "    center_w = (torch.arange(in_width, device=device) + offset_w) * steps_w\n", "    shift_y, shift_x = torch.meshgrid(center_h, center_w, indexing='ij')\n", "    shift_y, shift_x = shift_y.reshape(-1), shift_x.reshape(-1)\n", "\n", "    # 生成“boxes_per_pixel”个高和宽，\n", "    # 之后用于创建锚框的四角坐标(xmin,xmax,ymin,ymax)\n", "    w = torch.cat((size_tensor * torch.sqrt(ratio_tensor[0]),\n", "                   sizes[0] * torch.sqrt(ratio_tensor[1:])))\\\n", "                   * in_height / in_width  # 处理矩形输入\n", "    h = torch.cat((size_tensor / torch.sqrt(ratio_tensor[0]),\n", "                   sizes[0] / torch.sqrt(ratio_tensor[1:])))\n", "    # 除以2来获得半高和半宽\n", "    anchor_manipulations = torch.stack((-w, -h, w, h)).T.repeat(\n", "                                        in_height * in_width, 1) / 2\n", "\n", "    # 每个中心点都将有“boxes_per_pixel”个锚框，\n", "    # 所以生成含所有锚框中心的网格，重复了“boxes_per_pixel”次\n", "    out_grid = torch.stack([shift_x, shift_y, shift_x, shift_y],\n", "                dim=1).repeat_interleave(boxes_per_pixel, dim=0)\n", "    output = out_grid + anchor_manipulations\n", "    return output.unsqueeze(0)"]}, {"cell_type": "markdown", "id": "8f03ef54", "metadata": {"origin_pos": 8}, "source": ["可以看到[**返回的锚框变量`Y`的形状**]是（批量大小，锚框的数量，4）。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f411d4af", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:45.131714Z", "iopub.status.busy": "2023-08-18T07:00:45.131003Z", "iopub.status.idle": "2023-08-18T07:00:45.238891Z", "shell.execute_reply": "2023-08-18T07:00:45.237843Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["561 728\n"]}, {"data": {"text/plain": ["torch.Size([1, 2042040, 4])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["img = d2l.plt.imread('../img/catdog.jpg')\n", "h, w = img.shape[:2]\n", "\n", "print(h, w)\n", "X = torch.rand(size=(1, 3, h, w))\n", "Y = multibox_prior(X, sizes=[0.75, 0.5, 0.25], ratios=[1, 2, 0.5])\n", "Y.shape"]}, {"cell_type": "markdown", "id": "a368b5c5", "metadata": {"origin_pos": 12}, "source": ["将锚框变量`Y`的形状更改为(图像高度,图像宽度,以同一像素为中心的锚框的数量,4)后，我们可以获得以指定像素的位置为中心的所有锚框。\n", "在接下来的内容中，我们[**访问以（250,250）为中心的第一个锚框**]。\n", "它有四个元素：锚框左上角的$(x, y)$轴坐标和右下角的$(x, y)$轴坐标。\n", "输出中两个轴的坐标各分别除以了图像的宽度和高度。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a7b7cfa3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:45.244522Z", "iopub.status.busy": "2023-08-18T07:00:45.243982Z", "iopub.status.idle": "2023-08-18T07:00:45.252916Z", "shell.execute_reply": "2023-08-18T07:00:45.251985Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0.06, 0.07, 0.63, 0.82])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["boxes = Y.reshape(h, w, 5, 4)\n", "boxes[250, 250, 0, :]"]}, {"cell_type": "markdown", "id": "eb941166", "metadata": {"origin_pos": 15}, "source": ["为了[**显示以图像中以某个像素为中心的所有锚框**]，定义下面的`show_bboxes`函数来在图像上绘制多个边界框。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b2dc5400", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:45.257994Z", "iopub.status.busy": "2023-08-18T07:00:45.257297Z", "iopub.status.idle": "2023-08-18T07:00:45.267572Z", "shell.execute_reply": "2023-08-18T07:00:45.266638Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def show_bboxes(axes, bboxes, labels=None, colors=None):\n", "    \"\"\"显示所有边界框\"\"\"\n", "    def _make_list(obj, default_values=None):\n", "        if obj is None:\n", "            obj = default_values\n", "        elif not isinstance(obj, (list, tuple)):\n", "            obj = [obj]\n", "        return obj\n", "\n", "    labels = _make_list(labels)\n", "    colors = _make_list(colors, ['b', 'g', 'r', 'm', 'c'])\n", "    for i, bbox in enumerate(bboxes):\n", "        color = colors[i % len(colors)]\n", "        rect = d2l.bbox_to_rect(bbox.detach().numpy(), color)\n", "        axes.add_patch(rect)\n", "        if labels and len(labels) > i:\n", "            text_color = 'k' if color == 'w' else 'w'\n", "            axes.text(rect.xy[0], rect.xy[1], labels[i],\n", "                      va='center', ha='center', fontsize=9, color=text_color,\n", "                      bbox=dict(facecolor=color, lw=0))"]}, {"cell_type": "markdown", "id": "0aefc961", "metadata": {"origin_pos": 17}, "source": ["正如从上面代码中所看到的，变量`boxes`中$x$轴和$y$轴的坐标值已分别除以图像的宽度和高度。\n", "绘制锚框时，我们需要恢复它们原始的坐标值。\n", "因此，在下面定义了变量`bbox_scale`。\n", "现在可以绘制出图像中所有以(250,250)为中心的锚框了。\n", "如下所示，缩放比为0.75且宽高比为1的蓝色锚框很好地围绕着图像中的狗。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "c199e557", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:45.272415Z", "iopub.status.busy": "2023-08-18T07:00:45.271753Z", "iopub.status.idle": "2023-08-18T07:00:45.634073Z", "shell.execute_reply": "2023-08-18T07:00:45.632866Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"229.502021pt\" height=\"182.601395pt\" viewBox=\"0 0 229.**********.601395\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:00:45.532795</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 182.601395 \n", "L 229.**********.601395 \n", "L 229.502021 0 \n", "L 0 0 \n", "L 0 182.601395 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 45.**********.72327 \n", "L 222.**********.72327 \n", "L 222.302021 22.82327 \n", "L 45.946941 22.82327 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pfd4dce92da)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image4ee7073173\" transform=\"scale(1 -1)translate(0 -136)\" x=\"45.946941\" y=\"-22.72327\" width=\"177\" height=\"136\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 55.788187 32.664511 \n", "L 157.713184 32.664511 \n", "L 157.713184 134.589506 \n", "L 55.788187 134.589506 \n", "L 55.788187 32.664511 \n", "z\n", "\" clip-path=\"url(#pfd4dce92da)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 72.775686 49.652011 \n", "L 140.725677 49.652011 \n", "L 140.725677 117.602006 \n", "L 72.775686 117.602006 \n", "L 72.775686 49.652011 \n", "z\n", "\" clip-path=\"url(#pfd4dce92da)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 89.763184 66.639513 \n", "L 123.738184 66.639513 \n", "L 123.738184 100.614506 \n", "L 89.763184 100.614506 \n", "L 89.763184 66.639513 \n", "z\n", "\" clip-path=\"url(#pfd4dce92da)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.678828 47.591083 \n", "L 178.822562 47.591083 \n", "L 178.822562 119.662945 \n", "L 34.678828 119.662945 \n", "L 34.678828 47.591083 \n", "z\n", "\" clip-path=\"url(#pfd4dce92da)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 70.714756 11.555156 \n", "L 142.786608 11.555156 \n", "L 142.786608 155.698862 \n", "L 70.714756 155.698862 \n", "L 70.714756 11.555156 \n", "z\n", "\" clip-path=\"url(#pfd4dce92da)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m55255c8e12\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m55255c8e12\" x=\"46.068064\" y=\"158.72327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(42.886814 173.321707)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m55255c8e12\" x=\"94.517262\" y=\"158.72327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(84.973512 173.321707)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m55255c8e12\" x=\"142.96646\" y=\"158.72327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(133.42271 173.321707)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m55255c8e12\" x=\"191.415657\" y=\"158.72327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(181.871907 173.321707)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"me0e7b9b398\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me0e7b9b398\" x=\"45.946941\" y=\"22.944393\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(32.584441 26.743612)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#me0e7b9b398\" x=\"45.946941\" y=\"47.168992\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(19.859441 50.968211)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#me0e7b9b398\" x=\"45.946941\" y=\"71.393591\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(19.859441 75.192809)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#me0e7b9b398\" x=\"45.946941\" y=\"95.61819\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(19.859441 99.417408)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#me0e7b9b398\" x=\"45.946941\" y=\"119.842789\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(19.859441 123.642007)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#me0e7b9b398\" x=\"45.946941\" y=\"144.067388\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(19.859441 147.866606)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 45.**********.72327 \n", "L 45.946941 22.82327 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 222.**********.72327 \n", "L 222.302021 22.82327 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 45.**********.72327 \n", "L 222.**********.72327 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 45.946941 22.82327 \n", "L 222.302021 22.82327 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <g id=\"patch_12\">\n", "     <path d=\"M 24.349359 40.979668 \n", "L 87.227015 40.979668 \n", "L 87.227015 24.349355 \n", "L 24.349359 24.349355 \n", "z\n", "\" style=\"fill: #0000ff\"/>\n", "    </g>\n", "    <!-- s=0.75, r=1 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(28.309359 35.147949)scale(0.09 -0.09)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2c\" d=\"M 750 794 \n", "L 1409 794 \n", "L 1409 256 \n", "L 897 -744 \n", "L 494 -744 \n", "L 750 256 \n", "L 750 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"135.888672\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"199.511719\"/>\n", "     <use xlink:href=\"#DejaVuSans-37\" x=\"231.298828\"/>\n", "     <use xlink:href=\"#DejaVuSans-35\" x=\"294.921875\"/>\n", "     <use xlink:href=\"#DejaVuSans-2c\" x=\"358.544922\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"390.332031\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"422.119141\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"463.232422\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"547.021484\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <g id=\"patch_13\">\n", "     <path d=\"M 44.199983 57.967168 \n", "L 101.351389 57.967168 \n", "L 101.351389 41.336855 \n", "L 44.199983 41.336855 \n", "z\n", "\" style=\"fill: #008000\"/>\n", "    </g>\n", "    <!-- s=0.5, r=1 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(48.159983 52.135449)scale(0.09 -0.09)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"135.888672\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"199.511719\"/>\n", "     <use xlink:href=\"#DejaVuSans-35\" x=\"231.298828\"/>\n", "     <use xlink:href=\"#DejaVuSans-2c\" x=\"294.921875\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"326.708984\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"358.496094\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"399.609375\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"483.398438\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_13\">\n", "    <g id=\"patch_14\">\n", "     <path d=\"M 58.324356 74.954669 \n", "L 121.202012 74.954669 \n", "L 121.202012 58.324357 \n", "L 58.324356 58.324357 \n", "z\n", "\" style=\"fill: #ff0000\"/>\n", "    </g>\n", "    <!-- s=0.25, r=1 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(62.284356 69.122951)scale(0.09 -0.09)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"135.888672\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"199.511719\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"231.298828\"/>\n", "     <use xlink:href=\"#DejaVuSans-35\" x=\"294.921875\"/>\n", "     <use xlink:href=\"#DejaVuSans-2c\" x=\"358.544922\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"390.332031\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"422.119141\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"463.232422\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"547.021484\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <g id=\"patch_15\">\n", "     <path d=\"M 3.24 55.90624 \n", "L 66.117656 55.90624 \n", "L 66.117656 39.275927 \n", "L 3.24 39.275927 \n", "z\n", "\" style=\"fill: #bf00bf\"/>\n", "    </g>\n", "    <!-- s=0.75, r=2 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(7.2 50.074521)scale(0.09 -0.09)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"135.888672\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"199.511719\"/>\n", "     <use xlink:href=\"#DejaVuSans-37\" x=\"231.298828\"/>\n", "     <use xlink:href=\"#DejaVuSans-35\" x=\"294.921875\"/>\n", "     <use xlink:href=\"#DejaVuSans-2c\" x=\"358.544922\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"390.332031\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"422.119141\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"463.232422\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"547.021484\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_15\">\n", "    <g id=\"patch_16\">\n", "     <path d=\"M 34.982647 19.870312 \n", "L 106.446866 19.870312 \n", "L 106.446866 3.24 \n", "L 34.982647 3.24 \n", "z\n", "\" style=\"fill: #00bfbf\"/>\n", "    </g>\n", "    <!-- s=0.75, r=0.5 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(38.942647 14.038594)scale(0.09 -0.09)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"135.888672\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"199.511719\"/>\n", "     <use xlink:href=\"#DejaVuSans-37\" x=\"231.298828\"/>\n", "     <use xlink:href=\"#DejaVuSans-35\" x=\"294.921875\"/>\n", "     <use xlink:href=\"#DejaVuSans-2c\" x=\"358.544922\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"390.332031\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"422.119141\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"463.232422\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"547.021484\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"610.644531\"/>\n", "     <use xlink:href=\"#DejaVuSans-35\" x=\"642.431641\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pfd4dce92da\">\n", "   <rect x=\"45.946941\" y=\"22.82327\" width=\"176.35508\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["d2l.set_figsize()\n", "bbox_scale = torch.tensor((w, h, w, h))\n", "fig = d2l.plt.imshow(img)\n", "show_bboxes(fig.axes, boxes[250, 250, :, :] * bbox_scale,\n", "            ['s=0.75, r=1', 's=0.5, r=1', 's=0.25, r=1', 's=0.75, r=2',\n", "             's=0.75, r=0.5'])"]}, {"cell_type": "markdown", "id": "da87f940", "metadata": {"origin_pos": 19}, "source": ["## [**交并比（IoU）**]\n", "\n", "我们刚刚提到某个锚框“较好地”覆盖了图像中的狗。\n", "如果已知目标的真实边界框，那么这里的“好”该如何如何量化呢？\n", "直观地说，可以衡量锚框和真实边界框之间的相似性。\n", "*杰卡德系数*（Jaccard）可以衡量两组之间的相似性。\n", "给定集合$\\mathcal{A}$和$\\mathcal{B}$，他们的杰卡德系数是他们交集的大小除以他们并集的大小：\n", "\n", "$$J(\\mathcal{A},\\mathcal{B}) = \\frac{\\left|\\mathcal{A} \\cap \\mathcal{B}\\right|}{\\left| \\mathcal{A} \\cup \\mathcal{B}\\right|}.$$\n", "\n", "事实上，我们可以将任何边界框的像素区域视为一组像素。通\n", "过这种方式，我们可以通过其像素集的杰卡德系数来测量两个边界框的相似性。\n", "对于两个边界框，它们的杰卡德系数通常称为*交并比*（intersection over union，IoU），即两个边界框相交面积与相并面积之比，如 :numref:`fig_iou`所示。\n", "交并比的取值范围在0和1之间：0表示两个边界框无重合像素，1表示两个边界框完全重合。\n", "\n", "![交并比是两个边界框相交面积与相并面积之比。](../img/iou.svg)\n", ":label:`fig_iou`\n", "\n", "接下来部分将使用交并比来衡量锚框和真实边界框之间、以及不同锚框之间的相似度。\n", "给定两个锚框或边界框的列表，以下`box_iou`函数将在这两个列表中计算它们成对的交并比。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "feab924c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:45.639984Z", "iopub.status.busy": "2023-08-18T07:00:45.639243Z", "iopub.status.idle": "2023-08-18T07:00:45.649165Z", "shell.execute_reply": "2023-08-18T07:00:45.648025Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def box_iou(boxes1, boxes2):\n", "    \"\"\"计算两个锚框或边界框列表中成对的交并比\"\"\"\n", "    box_area = lambda boxes: ((boxes[:, 2] - boxes[:, 0]) *\n", "                              (boxes[:, 3] - boxes[:, 1]))\n", "    # boxes1,boxes2,areas1,areas2的形状:\n", "    # boxes1：(boxes1的数量,4),\n", "    # boxes2：(boxes2的数量,4),\n", "    # areas1：(boxes1的数量,),\n", "    # areas2：(boxes2的数量,)\n", "    areas1 = box_area(boxes1)\n", "    areas2 = box_area(boxes2)\n", "    # inter_upperlefts,inter_lowerrights,inters的形状:\n", "    # (boxes1的数量,boxes2的数量,2)\n", "    inter_upperlefts = torch.max(boxes1[:, None, :2], boxes2[:, :2])\n", "    inter_lowerrights = torch.min(boxes1[:, None, 2:], boxes2[:, 2:])\n", "    inters = (inter_lowerrights - inter_upperlefts).clamp(min=0)\n", "    # inter_areasandunion_areas的形状:(boxes1的数量,boxes2的数量)\n", "    inter_areas = inters[:, :, 0] * inters[:, :, 1]\n", "    union_areas = areas1[:, None] + areas2 - inter_areas\n", "    return inter_areas / union_areas"]}, {"cell_type": "markdown", "id": "aa0f6d1e", "metadata": {"origin_pos": 23}, "source": ["## 在训练数据中标注锚框\n", ":label:`subsec_labeling-anchor-boxes`\n", "\n", "在训练集中，我们将每个锚框视为一个训练样本。\n", "为了训练目标检测模型，我们需要每个锚框的*类别*（class）和*偏移量*（offset）标签，其中前者是与锚框相关的对象的类别，后者是真实边界框相对于锚框的偏移量。\n", "在预测时，我们为每个图像生成多个锚框，预测所有锚框的类别和偏移量，根据预测的偏移量调整它们的位置以获得预测的边界框，最后只输出符合特定条件的预测边界框。\n", "\n", "目标检测训练集带有*真实边界框*的位置及其包围物体类别的标签。\n", "要标记任何生成的锚框，我们可以参考分配到的最接近此锚框的真实边界框的位置和类别标签。\n", "下文将介绍一个算法，它能够把最接近的真实边界框分配给锚框。\n", "\n", "### [**将真实边界框分配给锚框**]\n", "\n", "给定图像，假设锚框是$A_1, A_2, \\ldots, A_{n_a}$，真实边界框是$B_1, B_2, \\ldots, B_{n_b}$，其中$n_a \\geq n_b$。\n", "让我们定义一个矩阵$\\mathbf{X} \\in \\mathbb{R}^{n_a \\times n_b}$，其中第$i$行、第$j$列的元素$x_{ij}$是锚框$A_i$和真实边界框$B_j$的IoU。\n", "该算法包含以下步骤。\n", "\n", "1. 在矩阵$\\mathbf{X}$中找到最大的元素，并将它的行索引和列索引分别表示为$i_1$和$j_1$。然后将真实边界框$B_{j_1}$分配给锚框$A_{i_1}$。这很直观，因为$A_{i_1}$和$B_{j_1}$是所有锚框和真实边界框配对中最相近的。在第一个分配完成后，丢弃矩阵中${i_1}^\\mathrm{th}$行和${j_1}^\\mathrm{th}$列中的所有元素。\n", "1. 在矩阵$\\mathbf{X}$中找到剩余元素中最大的元素，并将它的行索引和列索引分别表示为$i_2$和$j_2$。我们将真实边界框$B_{j_2}$分配给锚框$A_{i_2}$，并丢弃矩阵中${i_2}^\\mathrm{th}$行和${j_2}^\\mathrm{th}$列中的所有元素。\n", "1. 此时，矩阵$\\mathbf{X}$中两行和两列中的元素已被丢弃。我们继续，直到丢弃掉矩阵$\\mathbf{X}$中$n_b$列中的所有元素。此时已经为这$n_b$个锚框各自分配了一个真实边界框。\n", "1. 只遍历剩下的$n_a - n_b$个锚框。例如，给定任何锚框$A_i$，在矩阵$\\mathbf{X}$的第$i^\\mathrm{th}$行中找到与$A_i$的IoU最大的真实边界框$B_j$，只有当此IoU大于预定义的阈值时，才将$B_j$分配给$A_i$。\n", "\n", "下面用一个具体的例子来说明上述算法。\n", "如 :numref:`fig_anchor_label`（左）所示，假设矩阵$\\mathbf{X}$中的最大值为$x_{23}$，我们将真实边界框$B_3$分配给锚框$A_2$。\n", "然后，我们丢弃矩阵第2行和第3列中的所有元素，在剩余元素（阴影区域）中找到最大的$x_{71}$，然后将真实边界框$B_1$分配给锚框$A_7$。\n", "接下来，如 :numref:`fig_anchor_label`（中）所示，丢弃矩阵第7行和第1列中的所有元素，在剩余元素（阴影区域）中找到最大的$x_{54}$，然后将真实边界框$B_4$分配给锚框$A_5$。\n", "最后，如 :numref:`fig_anchor_label`（右）所示，丢弃矩阵第5行和第4列中的所有元素，在剩余元素（阴影区域）中找到最大的$x_{92}$，然后将真实边界框$B_2$分配给锚框$A_9$。\n", "之后，我们只需要遍历剩余的锚框$A_1, A_3, A_4, A_6, A_8$，然后根据阈值确定是否为它们分配真实边界框。\n", "\n", "![将真实边界框分配给锚框。](../img/anchor-label.svg)\n", ":label:`fig_anchor_label`\n", "\n", "此算法在下面的`assign_anchor_to_bbox`函数中实现。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "8ac4113a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:45.654100Z", "iopub.status.busy": "2023-08-18T07:00:45.653303Z", "iopub.status.idle": "2023-08-18T07:00:45.664045Z", "shell.execute_reply": "2023-08-18T07:00:45.663020Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def assign_anchor_to_bbox(ground_truth, anchors, device, iou_threshold=0.5):\n", "    \"\"\"将最接近的真实边界框分配给锚框\"\"\"\n", "    num_anchors, num_gt_boxes = anchors.shape[0], ground_truth.shape[0]\n", "    # 位于第i行和第j列的元素x_ij是锚框i和真实边界框j的IoU\n", "    jaccard = box_iou(anchors, ground_truth)\n", "    # 对于每个锚框，分配的真实边界框的张量\n", "    anchors_bbox_map = torch.full((num_anchors,), -1, dtype=torch.long,\n", "                                  device=device)\n", "    # 根据阈值，决定是否分配真实边界框\n", "    max_ious, indices = torch.max(jac<PERSON>, dim=1)\n", "    anc_i = torch.nonzero(max_ious >= iou_threshold).reshape(-1)\n", "    box_j = indices[max_ious >= iou_threshold]\n", "    anchors_bbox_map[anc_i] = box_j\n", "    col_discard = torch.full((num_anchors,), -1)\n", "    row_discard = torch.full((num_gt_boxes,), -1)\n", "    for _ in range(num_gt_boxes):\n", "        max_idx = torch.argmax(jaccard)\n", "        box_idx = (max_idx % num_gt_boxes).long()\n", "        anc_idx = (max_idx / num_gt_boxes).long()\n", "        anchors_bbox_map[anc_idx] = box_idx\n", "        jaccard[:, box_idx] = col_discard\n", "        jaccard[anc_idx, :] = row_discard\n", "    return anchors_bbox_map"]}, {"cell_type": "markdown", "id": "f57512db", "metadata": {"origin_pos": 27}, "source": ["### 标记类别和偏移量\n", "\n", "现在我们可以为每个锚框标记类别和偏移量了。\n", "假设一个锚框$A$被分配了一个真实边界框$B$。\n", "一方面，锚框$A$的类别将被标记为与$B$相同。\n", "另一方面，锚框$A$的偏移量将根据$B$和$A$中心坐标的相对位置以及这两个框的相对大小进行标记。\n", "鉴于数据集内不同的框的位置和大小不同，我们可以对那些相对位置和大小应用变换，使其获得分布更均匀且易于拟合的偏移量。\n", "这里介绍一种常见的变换。\n", "[**给定框$A$和$B$，中心坐标分别为$(x_a, y_a)$和$(x_b, y_b)$，宽度分别为$w_a$和$w_b$，高度分别为$h_a$和$h_b$，可以将$A$的偏移量标记为：\n", "\n", "$$\\left( \\frac{ \\frac{x_b - x_a}{w_a} - \\mu_x }{\\sigma_x},\n", "\\frac{ \\frac{y_b - y_a}{h_a} - \\mu_y }{\\sigma_y},\n", "\\frac{ \\log \\frac{w_b}{w_a} - \\mu_w }{\\sigma_w},\n", "\\frac{ \\log \\frac{h_b}{h_a} - \\mu_h }{\\sigma_h}\\right),$$\n", "**]\n", "其中常量的默认值为 $\\mu_x = \\mu_y = \\mu_w = \\mu_h = 0, \\sigma_x=\\sigma_y=0.1$ ， $\\sigma_w=\\sigma_h=0.2$。\n", "这种转换在下面的 `offset_boxes` 函数中实现。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "a5b6bbdc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:45.668909Z", "iopub.status.busy": "2023-08-18T07:00:45.668107Z", "iopub.status.idle": "2023-08-18T07:00:45.675569Z", "shell.execute_reply": "2023-08-18T07:00:45.674509Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def offset_boxes(anchors, assigned_bb, eps=1e-6):\n", "    \"\"\"对锚框偏移量的转换\"\"\"\n", "    c_anc = d2l.box_corner_to_center(anchors)\n", "    c_assigned_bb = d2l.box_corner_to_center(assigned_bb)\n", "    offset_xy = 10 * (c_assigned_bb[:, :2] - c_anc[:, :2]) / c_anc[:, 2:]\n", "    offset_wh = 5 * torch.log(eps + c_assigned_bb[:, 2:] / c_anc[:, 2:])\n", "    offset = torch.cat([offset_xy, offset_wh], axis=1)\n", "    return offset"]}, {"cell_type": "markdown", "id": "1e309f7d", "metadata": {"origin_pos": 29}, "source": ["如果一个锚框没有被分配真实边界框，我们只需将锚框的类别标记为*背景*（background）。\n", "背景类别的锚框通常被称为*负类*锚框，其余的被称为*正类*锚框。\n", "我们使用真实边界框（`labels`参数）实现以下`multibox_target`函数，来[**标记锚框的类别和偏移量**]（`anchors`参数）。\n", "此函数将背景类别的索引设置为零，然后将新类别的整数索引递增一。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "291738a2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:45.680339Z", "iopub.status.busy": "2023-08-18T07:00:45.679651Z", "iopub.status.idle": "2023-08-18T07:00:45.692690Z", "shell.execute_reply": "2023-08-18T07:00:45.691647Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def multibox_target(anchors, labels):\n", "    \"\"\"使用真实边界框标记锚框\"\"\"\n", "    batch_size, anchors = labels.shape[0], anchors.squeeze(0)\n", "    batch_offset, batch_mask, batch_class_labels = [], [], []\n", "    device, num_anchors = anchors.device, anchors.shape[0]\n", "    for i in range(batch_size):\n", "        label = labels[i, :, :]\n", "        anchors_bbox_map = assign_anchor_to_bbox(\n", "            label[:, 1:], anchors, device)\n", "        bbox_mask = ((anchors_bbox_map >= 0).float().unsqueeze(-1)).repeat(\n", "            1, 4)\n", "        # 将类标签和分配的边界框坐标初始化为零\n", "        class_labels = torch.zeros(num_anchors, dtype=torch.long,\n", "                                   device=device)\n", "        assigned_bb = torch.zeros((num_anchors, 4), dtype=torch.float32,\n", "                                  device=device)\n", "        # 使用真实边界框来标记锚框的类别。\n", "        # 如果一个锚框没有被分配，标记其为背景（值为零）\n", "        indices_true = torch.nonzero(anchors_bbox_map >= 0)\n", "        bb_idx = anchors_bbox_map[indices_true]\n", "        class_labels[indices_true] = label[bb_idx, 0].long() + 1\n", "        assigned_bb[indices_true] = label[bb_idx, 1:]\n", "        # 偏移量转换\n", "        offset = offset_boxes(anchors, assigned_bb) * bbox_mask\n", "        batch_offset.append(offset.reshape(-1))\n", "        batch_mask.append(bbox_mask.reshape(-1))\n", "        batch_class_labels.append(class_labels)\n", "    bbox_offset = torch.stack(batch_offset)\n", "    bbox_mask = torch.stack(batch_mask)\n", "    class_labels = torch.stack(batch_class_labels)\n", "    return (bbox_offset, bbox_mask, class_labels)"]}, {"cell_type": "markdown", "id": "a78ead98", "metadata": {"origin_pos": 33}, "source": ["### 一个例子\n", "\n", "下面通过一个具体的例子来说明锚框标签。\n", "我们已经为加载图像中的狗和猫定义了真实边界框，其中第一个元素是类别（0代表狗，1代表猫），其余四个元素是左上角和右下角的$(x, y)$轴坐标（范围介于0和1之间）。\n", "我们还构建了五个锚框，用左上角和右下角的坐标进行标记：$A_0, \\ldots, A_4$（索引从0开始）。\n", "然后我们[**在图像中绘制这些真实边界框和锚框**]。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "e22f46a6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:45.697286Z", "iopub.status.busy": "2023-08-18T07:00:45.696846Z", "iopub.status.idle": "2023-08-18T07:00:46.054321Z", "shell.execute_reply": "2023-08-18T07:00:46.053210Z"}, "origin_pos": 34, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"216.84258pt\" height=\"170.656221pt\" viewBox=\"0 0 216.84258 170.656221\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:00:45.953248</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 170.656221 \n", "L 216.84258 170.656221 \n", "L 216.84258 0 \n", "L 0 0 \n", "L 0 170.656221 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 209.64258 146.778096 \n", "L 209.64258 10.878096 \n", "L 33.2875 10.878096 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pfdc233e7f8)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imaged1729ac5fb\" transform=\"scale(1 -1)translate(0 -136)\" x=\"33.2875\" y=\"-10.778096\" width=\"177\" height=\"136\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 51.044132 21.871218 \n", "L 125.113264 21.871218 \n", "L 125.113264 136.027218 \n", "L 51.044132 136.027218 \n", "L 51.044132 21.871218 \n", "z\n", "\" clip-path=\"url(#pfdc233e7f8)\" style=\"fill: none; stroke: #000000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 130.403916 38.17922 \n", "L 192.128198 38.17922 \n", "L 192.128198 130.591217 \n", "L 130.403916 130.591217 \n", "L 130.403916 38.17922 \n", "z\n", "\" clip-path=\"url(#pfdc233e7f8)\" style=\"fill: none; stroke: #000000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 33.408623 24.589219 \n", "L 68.679641 24.589219 \n", "L 68.679641 51.769219 \n", "L 33.408623 51.769219 \n", "L 33.408623 24.589219 \n", "z\n", "\" clip-path=\"url(#pfdc233e7f8)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 59.861886 38.17922 \n", "L 103.950658 38.17922 \n", "L 103.950658 65.359221 \n", "L 59.861886 65.359221 \n", "L 59.861886 38.17922 \n", "z\n", "\" clip-path=\"url(#pfdc233e7f8)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 144.51232 17.794219 \n", "L 188.601097 17.794219 \n", "L 188.601097 144.181226 \n", "L 144.51232 144.181226 \n", "L 144.51232 17.794219 \n", "z\n", "\" clip-path=\"url(#pfdc233e7f8)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 149.802979 72.154218 \n", "L 174.492693 72.154218 \n", "L 174.492693 119.719223 \n", "L 149.802979 119.719223 \n", "L 149.802979 72.154218 \n", "z\n", "\" clip-path=\"url(#pfdc233e7f8)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 133.931017 51.769219 \n", "L 195.655299 51.769219 \n", "L 195.655299 133.30921 \n", "L 133.931017 133.30921 \n", "L 133.931017 51.769219 \n", "z\n", "\" clip-path=\"url(#pfdc233e7f8)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"me324064f0e\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me324064f0e\" x=\"33.408623\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(30.227373 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#me324064f0e\" x=\"81.857821\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(72.314071 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#me324064f0e\" x=\"130.307019\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(120.763269 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#me324064f0e\" x=\"178.756217\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(169.212467 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"m42a919a6a1\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m42a919a6a1\" x=\"33.2875\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m42a919a6a1\" x=\"33.2875\" y=\"35.223818\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 39.023036)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m42a919a6a1\" x=\"33.2875\" y=\"59.448417\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(7.2 63.247635)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m42a919a6a1\" x=\"33.2875\" y=\"83.673016\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(7.2 87.472234)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m42a919a6a1\" x=\"33.2875\" y=\"107.897614\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(7.2 111.696833)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m42a919a6a1\" x=\"33.2875\" y=\"132.122213\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(7.2 135.921432)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 33.2875 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 209.64258 146.778096 \n", "L 209.64258 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 209.64258 146.778096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 33.2875 10.878096 \n", "L 209.64258 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <g id=\"patch_14\">\n", "     <path d=\"M 38.617101 30.186374 \n", "L 63.471163 30.186374 \n", "L 63.471163 13.556062 \n", "L 38.617101 13.556062 \n", "z\n", "\"/>\n", "    </g>\n", "    <!-- dog -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(42.577101 24.354656)scale(0.09 -0.09)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-64\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-67\" x=\"124.658203\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <g id=\"patch_15\">\n", "     <path d=\"M 119.447822 46.494376 \n", "L 141.360009 46.494376 \n", "L 141.360009 29.864064 \n", "L 119.447822 29.864064 \n", "z\n", "\"/>\n", "    </g>\n", "    <!-- cat -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(123.407822 40.662657)scale(0.09 -0.09)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-63\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"54.980469\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"116.259766\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_13\">\n", "    <g id=\"patch_16\">\n", "     <path d=\"M 26.585498 32.904376 \n", "L 40.231748 32.904376 \n", "L 40.231748 16.274063 \n", "L 26.585498 16.274063 \n", "z\n", "\" style=\"fill: #0000ff\"/>\n", "    </g>\n", "    <!-- 0 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(30.545498 27.072657)scale(0.09 -0.09)\">\n", "     <use xlink:href=\"#DejaVuSans-30\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <g id=\"patch_17\">\n", "     <path d=\"M 53.038761 46.494376 \n", "L 66.685011 46.494376 \n", "L 66.685011 29.864064 \n", "L 53.038761 29.864064 \n", "z\n", "\" style=\"fill: #008000\"/>\n", "    </g>\n", "    <!-- 1 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(56.998761 40.662657)scale(0.09 -0.09)\">\n", "     <use xlink:href=\"#DejaVuSans-31\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_15\">\n", "    <g id=\"patch_18\">\n", "     <path d=\"M 137.689195 26.109375 \n", "L 151.335445 26.109375 \n", "L 151.335445 9.479063 \n", "L 137.689195 9.479063 \n", "z\n", "\" style=\"fill: #ff0000\"/>\n", "    </g>\n", "    <!-- 2 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(141.649195 20.277657)scale(0.09 -0.09)\">\n", "     <use xlink:href=\"#DejaVuSans-32\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_16\">\n", "    <g id=\"patch_19\">\n", "     <path d=\"M 142.979854 80.469374 \n", "L 156.626104 80.469374 \n", "L 156.626104 63.839062 \n", "L 142.979854 63.839062 \n", "z\n", "\" style=\"fill: #bf00bf\"/>\n", "    </g>\n", "    <!-- 3 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(146.939854 74.637656)scale(0.09 -0.09)\">\n", "     <use xlink:href=\"#DejaVuSans-33\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_17\">\n", "    <g id=\"patch_20\">\n", "     <path d=\"M 127.107892 60.084376 \n", "L 140.754142 60.084376 \n", "L 140.754142 43.454063 \n", "L 127.107892 43.454063 \n", "z\n", "\" style=\"fill: #00bfbf\"/>\n", "    </g>\n", "    <!-- 4 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(131.067892 54.252657)scale(0.09 -0.09)\">\n", "     <use xlink:href=\"#DejaVuSans-34\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pfdc233e7f8\">\n", "   <rect x=\"33.2875\" y=\"10.878096\" width=\"176.35508\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["ground_truth = torch.tensor([[0, 0.1, 0.08, 0.52, 0.92],\n", "                         [1, 0.55, 0.2, 0.9, 0.88]])\n", "anchors = torch.tensor([[0, 0.1, 0.2, 0.3], [0.15, 0.2, 0.4, 0.4],\n", "                    [0.63, 0.05, 0.88, 0.98], [0.66, 0.45, 0.8, 0.8],\n", "                    [0.57, 0.3, 0.92, 0.9]])\n", "\n", "fig = d2l.plt.imshow(img)\n", "show_bboxes(fig.axes, ground_truth[:, 1:] * bbox_scale, ['dog', 'cat'], 'k')\n", "show_bboxes(fig.axes, anchors * bbox_scale, ['0', '1', '2', '3', '4']);"]}, {"cell_type": "markdown", "id": "414c8154", "metadata": {"origin_pos": 35}, "source": ["使用上面定义的`multibox_target`函数，我们可以[**根据狗和猫的真实边界框，标注这些锚框的分类和偏移量**]。\n", "在这个例子中，背景、狗和猫的类索引分别为0、1和2。\n", "下面我们为锚框和真实边界框样本添加一个维度。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "511fc2cf", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:46.059898Z", "iopub.status.busy": "2023-08-18T07:00:46.059146Z", "iopub.status.idle": "2023-08-18T07:00:46.067556Z", "shell.execute_reply": "2023-08-18T07:00:46.066157Z"}, "origin_pos": 37, "tab": ["pytorch"]}, "outputs": [], "source": ["labels = multibox_target(anchors.unsqueeze(dim=0),\n", "                         ground_truth.unsqueeze(dim=0))"]}, {"cell_type": "markdown", "id": "5c2b454d", "metadata": {"origin_pos": 39}, "source": ["返回的结果中有三个元素，都是张量格式。第三个元素包含标记的输入锚框的类别。\n", "\n", "让我们根据图像中的锚框和真实边界框的位置来分析下面返回的类别标签。\n", "首先，在所有的锚框和真实边界框配对中，锚框$A_4$与猫的真实边界框的IoU是最大的。\n", "因此，$A_4$的类别被标记为猫。\n", "去除包含$A_4$或猫的真实边界框的配对，在剩下的配对中，锚框$A_1$和狗的真实边界框有最大的IoU。\n", "因此，$A_1$的类别被标记为狗。\n", "接下来，我们需要遍历剩下的三个未标记的锚框：$A_0$、$A_2$和$A_3$。\n", "对于$A_0$，与其拥有最大IoU的真实边界框的类别是狗，但IoU低于预定义的阈值（0.5），因此该类别被标记为背景；\n", "对于$A_2$，与其拥有最大IoU的真实边界框的类别是猫，IoU超过阈值，所以类别被标记为猫；\n", "对于$A_3$，与其拥有最大IoU的真实边界框的类别是猫，但值低于阈值，因此该类别被标记为背景。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "78a2e091", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:46.075902Z", "iopub.status.busy": "2023-08-18T07:00:46.075000Z", "iopub.status.idle": "2023-08-18T07:00:46.084256Z", "shell.execute_reply": "2023-08-18T07:00:46.083114Z"}, "origin_pos": 40, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[0, 1, 2, 0, 2]])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["labels[2]"]}, {"cell_type": "markdown", "id": "cffb366b", "metadata": {"origin_pos": 41}, "source": ["返回的第二个元素是掩码（mask）变量，形状为（批量大小，锚框数的四倍）。\n", "掩码变量中的元素与每个锚框的4个偏移量一一对应。\n", "由于我们不关心对背景的检测，负类的偏移量不应影响目标函数。\n", "通过元素乘法，掩码变量中的零将在计算目标函数之前过滤掉负类偏移量。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "94a1597f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:46.097601Z", "iopub.status.busy": "2023-08-18T07:00:46.097228Z", "iopub.status.idle": "2023-08-18T07:00:46.104883Z", "shell.execute_reply": "2023-08-18T07:00:46.103744Z"}, "origin_pos": 42, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[0., 0., 0., 0., 1., 1., 1., 1., 1., 1., 1., 1., 0., 0., 0., 0., 1., 1.,\n", "         1., 1.]])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["labels[1]"]}, {"cell_type": "markdown", "id": "1cfc7bb3", "metadata": {"origin_pos": 43}, "source": ["返回的第一个元素包含了为每个锚框标记的四个偏移值。\n", "请注意，负类锚框的偏移量被标记为零。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "25e7f69b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:46.112301Z", "iopub.status.busy": "2023-08-18T07:00:46.111934Z", "iopub.status.idle": "2023-08-18T07:00:46.118802Z", "shell.execute_reply": "2023-08-18T07:00:46.117910Z"}, "origin_pos": 44, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[-0.00e+00, -0.00e+00, -0.00e+00, -0.00e+00,  1.40e+00,  1.00e+01,\n", "          2.59e+00,  7.18e+00, -1.20e+00,  2.69e-01,  1.68e+00, -1.57e+00,\n", "         -0.00e+00, -0.00e+00, -0.00e+00, -0.00e+00, -5.71e-01, -1.00e+00,\n", "          4.17e-06,  6.26e-01]])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["labels[0]"]}, {"cell_type": "markdown", "id": "ada70fbf", "metadata": {"origin_pos": 45}, "source": ["## 使用非极大值抑制预测边界框\n", ":label:`subsec_predicting-bounding-boxes-nms`\n", "\n", "在预测时，我们先为图像生成多个锚框，再为这些锚框一一预测类别和偏移量。\n", "一个*预测好的边界框*则根据其中某个带有预测偏移量的锚框而生成。\n", "下面我们实现了`offset_inverse`函数，该函数将锚框和偏移量预测作为输入，并[**应用逆偏移变换来返回预测的边界框坐标**]。\n"]}, {"cell_type": "code", "execution_count": 16, "id": "227ae1a8", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:46.127370Z", "iopub.status.busy": "2023-08-18T07:00:46.127014Z", "iopub.status.idle": "2023-08-18T07:00:46.133873Z", "shell.execute_reply": "2023-08-18T07:00:46.132925Z"}, "origin_pos": 46, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def offset_inverse(anchors, offset_preds):\n", "    \"\"\"根据带有预测偏移量的锚框来预测边界框\"\"\"\n", "    anc = d2l.box_corner_to_center(anchors)\n", "    pred_bbox_xy = (offset_preds[:, :2] * anc[:, 2:] / 10) + anc[:, :2]\n", "    pred_bbox_wh = torch.exp(offset_preds[:, 2:] / 5) * anc[:, 2:]\n", "    pred_bbox = torch.cat((pred_bbox_xy, pred_bbox_wh), axis=1)\n", "    predicted_bbox = d2l.box_center_to_corner(pred_bbox)\n", "    return predicted_bbox"]}, {"cell_type": "markdown", "id": "c8a5c307", "metadata": {"origin_pos": 47}, "source": ["当有许多锚框时，可能会输出许多相似的具有明显重叠的预测边界框，都围绕着同一目标。\n", "为了简化输出，我们可以使用*非极大值抑制*（non-maximum suppression，NMS）合并属于同一目标的类似的预测边界框。\n", "\n", "以下是非极大值抑制的工作原理。\n", "对于一个预测边界框$B$，目标检测模型会计算每个类别的预测概率。\n", "假设最大的预测概率为$p$，则该概率所对应的类别$B$即为预测的类别。\n", "具体来说，我们将$p$称为预测边界框$B$的*置信度*（confidence）。\n", "在同一张图像中，所有预测的非背景边界框都按置信度降序排序，以生成列表$L$。然后我们通过以下步骤操作排序列表$L$。\n", "\n", "1. 从$L$中选取置信度最高的预测边界框$B_1$作为基准，然后将所有与$B_1$的IoU超过预定阈值$\\epsilon$的非基准预测边界框从$L$中移除。这时，$L$保留了置信度最高的预测边界框，去除了与其太过相似的其他预测边界框。简而言之，那些具有*非极大值*置信度的边界框被*抑制*了。\n", "1. 从$L$中选取置信度第二高的预测边界框$B_2$作为又一个基准，然后将所有与$B_2$的IoU大于$\\epsilon$的非基准预测边界框从$L$中移除。\n", "1. 重复上述过程，直到$L$中的所有预测边界框都曾被用作基准。此时，$L$中任意一对预测边界框的IoU都小于阈值$\\epsilon$；因此，没有一对边界框过于相似。\n", "1. 输出列表$L$中的所有预测边界框。\n", "\n", "[**以下`nms`函数按降序对置信度进行排序并返回其索引**]。\n"]}, {"cell_type": "code", "execution_count": 17, "id": "ac5c4e3c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:46.138652Z", "iopub.status.busy": "2023-08-18T07:00:46.138060Z", "iopub.status.idle": "2023-08-18T07:00:46.151360Z", "shell.execute_reply": "2023-08-18T07:00:46.150447Z"}, "origin_pos": 49, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def nms(boxes, scores, iou_threshold):\n", "    \"\"\"对预测边界框的置信度进行排序\"\"\"\n", "    B = torch.argsort(scores, dim=-1, descending=True)\n", "    keep = []  # 保留预测边界框的指标\n", "    while B.numel() > 0:\n", "        i = B[0]\n", "        keep.append(i)\n", "        if B.numel() == 1: break\n", "        iou = box_iou(boxes[i, :].reshape(-1, 4),\n", "                      boxes[B[1:], :].reshape(-1, 4)).reshape(-1)\n", "        inds = torch.nonzero(iou <= iou_threshold).reshape(-1)\n", "        B = B[inds + 1]\n", "    return torch.tensor(keep, device=boxes.device)"]}, {"cell_type": "markdown", "id": "a21e7812", "metadata": {"origin_pos": 51}, "source": ["我们定义以下`multibox_detection`函数来[**将非极大值抑制应用于预测边界框**]。\n", "这里的实现有点复杂，请不要担心。我们将在实现之后，马上用一个具体的例子来展示它是如何工作的。\n"]}, {"cell_type": "code", "execution_count": 18, "id": "baa9f34f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:46.157117Z", "iopub.status.busy": "2023-08-18T07:00:46.156701Z", "iopub.status.idle": "2023-08-18T07:00:46.175104Z", "shell.execute_reply": "2023-08-18T07:00:46.174174Z"}, "origin_pos": 53, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def multibox_detection(cls_probs, offset_preds, anchors, nms_threshold=0.5,\n", "                       pos_threshold=0.009999999):\n", "    \"\"\"使用非极大值抑制来预测边界框\"\"\"\n", "    device, batch_size = cls_probs.device, cls_probs.shape[0]\n", "    anchors = anchors.squeeze(0)\n", "    num_classes, num_anchors = cls_probs.shape[1], cls_probs.shape[2]\n", "    out = []\n", "    for i in range(batch_size):\n", "        cls_prob, offset_pred = cls_probs[i], offset_preds[i].reshape(-1, 4)\n", "        conf, class_id = torch.max(cls_prob[1:], 0)\n", "        predicted_bb = offset_inverse(anchors, offset_pred)\n", "        keep = nms(predicted_bb, conf, nms_threshold)\n", "\n", "        # 找到所有的non_keep索引，并将类设置为背景\n", "        all_idx = torch.arange(num_anchors, dtype=torch.long, device=device)\n", "        combined = torch.cat((keep, all_idx))\n", "        uniques, counts = combined.unique(return_counts=True)\n", "        non_keep = uniques[counts == 1]\n", "        all_id_sorted = torch.cat((keep, non_keep))\n", "        class_id[non_keep] = -1\n", "        class_id = class_id[all_id_sorted]\n", "        conf, predicted_bb = conf[all_id_sorted], predicted_bb[all_id_sorted]\n", "        # pos_threshold是一个用于非背景预测的阈值\n", "        below_min_idx = (conf < pos_threshold)\n", "        class_id[below_min_idx] = -1\n", "        conf[below_min_idx] = 1 - conf[below_min_idx]\n", "        pred_info = torch.cat((class_id.unsqueeze(1),\n", "                               conf.<PERSON><PERSON><PERSON><PERSON>(1),\n", "                               predicted_bb), dim=1)\n", "        out.append(pred_info)\n", "    return torch.stack(out)"]}, {"cell_type": "markdown", "id": "3952b7dd", "metadata": {"origin_pos": 55}, "source": ["现在让我们[**将上述算法应用到一个带有四个锚框的具体示例中**]。\n", "为简单起见，我们假设预测的偏移量都是零，这意味着预测的边界框即是锚框。\n", "对于背景、狗和猫其中的每个类，我们还定义了它的预测概率。\n"]}, {"cell_type": "code", "execution_count": 19, "id": "4654e2f7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:46.179666Z", "iopub.status.busy": "2023-08-18T07:00:46.179298Z", "iopub.status.idle": "2023-08-18T07:00:46.188634Z", "shell.execute_reply": "2023-08-18T07:00:46.187737Z"}, "origin_pos": 56, "tab": ["pytorch"]}, "outputs": [], "source": ["anchors = torch.tensor([[0.1, 0.08, 0.52, 0.92], [0.08, 0.2, 0.56, 0.95],\n", "                      [0.15, 0.3, 0.62, 0.91], [0.55, 0.2, 0.9, 0.88]])\n", "offset_preds = torch.tensor([0] * anchors.numel())\n", "cls_probs = torch.tensor([[0] * 4,  # 背景的预测概率\n", "                      [0.9, 0.8, 0.7, 0.1],  # 狗的预测概率\n", "                      [0.1, 0.2, 0.3, 0.9]])  # 猫的预测概率"]}, {"cell_type": "markdown", "id": "96915571", "metadata": {"origin_pos": 58}, "source": ["我们可以[**在图像上绘制这些预测边界框和置信度**]。\n"]}, {"cell_type": "code", "execution_count": 20, "id": "b9619cba", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:46.193833Z", "iopub.status.busy": "2023-08-18T07:00:46.193198Z", "iopub.status.idle": "2023-08-18T07:00:46.435994Z", "shell.execute_reply": "2023-08-18T07:00:46.434923Z"}, "origin_pos": 59, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"216.84258pt\" height=\"170.656221pt\" viewBox=\"0 0 216.84258 170.656221\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:00:46.369699</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 170.656221 \n", "L 216.84258 170.656221 \n", "L 216.84258 0 \n", "L 0 0 \n", "L 0 170.656221 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 209.64258 146.778096 \n", "L 209.64258 10.878096 \n", "L 33.2875 10.878096 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd3121bced0)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image00b8f1d6f0\" transform=\"scale(1 -1)translate(0 -136)\" x=\"33.2875\" y=\"-10.778096\" width=\"177\" height=\"136\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 51.044132 21.871218 \n", "L 125.113264 21.871218 \n", "L 125.113264 136.027218 \n", "L 51.044132 136.027218 \n", "L 51.044132 21.871218 \n", "z\n", "\" clip-path=\"url(#pd3121bced0)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 47.517029 38.17922 \n", "L 132.167466 38.17922 \n", "L 132.167466 140.104222 \n", "L 47.517029 140.104222 \n", "L 47.517029 38.17922 \n", "z\n", "\" clip-path=\"url(#pd3121bced0)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 59.861886 51.769219 \n", "L 142.748777 51.769219 \n", "L 142.748777 134.668221 \n", "L 59.861886 134.668221 \n", "L 59.861886 51.769219 \n", "z\n", "\" clip-path=\"url(#pd3121bced0)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 130.403916 38.17922 \n", "L 192.128198 38.17922 \n", "L 192.128198 130.591217 \n", "L 130.403916 130.591217 \n", "L 130.403916 38.17922 \n", "z\n", "\" clip-path=\"url(#pd3121bced0)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m23a4e1cd1f\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m23a4e1cd1f\" x=\"33.408623\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(30.227373 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m23a4e1cd1f\" x=\"81.857821\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(72.314071 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m23a4e1cd1f\" x=\"130.307019\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(120.763269 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m23a4e1cd1f\" x=\"178.756217\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(169.212467 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"m8fd292e66e\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8fd292e66e\" x=\"33.2875\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m8fd292e66e\" x=\"33.2875\" y=\"35.223818\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 39.023036)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m8fd292e66e\" x=\"33.2875\" y=\"59.448417\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(7.2 63.247635)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m8fd292e66e\" x=\"33.2875\" y=\"83.673016\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(7.2 87.472234)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m8fd292e66e\" x=\"33.2875\" y=\"107.897614\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(7.2 111.696833)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m8fd292e66e\" x=\"33.2875\" y=\"132.122213\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(7.2 135.921432)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 33.2875 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 209.64258 146.778096 \n", "L 209.64258 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 209.64258 146.778096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 33.2875 10.878096 \n", "L 209.64258 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <g id=\"patch_11\">\n", "     <path d=\"M 27.689835 30.186374 \n", "L 74.398429 30.186374 \n", "L 74.398429 13.556062 \n", "L 27.689835 13.556062 \n", "z\n", "\" style=\"fill: #0000ff\"/>\n", "    </g>\n", "    <!-- dog=0.9 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(31.649835 24.354656)scale(0.09 -0.09)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-64\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-67\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"188.134766\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"271.923828\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"335.546875\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"367.333984\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <g id=\"patch_12\">\n", "     <path d=\"M 24.162732 46.494376 \n", "L 70.871326 46.494376 \n", "L 70.871326 29.864064 \n", "L 24.162732 29.864064 \n", "z\n", "\" style=\"fill: #008000\"/>\n", "    </g>\n", "    <!-- dog=0.8 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(28.122732 40.662657)scale(0.09 -0.09)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-64\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-67\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"188.134766\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"271.923828\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"335.546875\"/>\n", "     <use xlink:href=\"#DejaVuSans-38\" x=\"367.333984\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_13\">\n", "    <g id=\"patch_13\">\n", "     <path d=\"M 36.507589 60.084376 \n", "L 83.216183 60.084376 \n", "L 83.216183 43.454063 \n", "L 36.507589 43.454063 \n", "z\n", "\" style=\"fill: #ff0000\"/>\n", "    </g>\n", "    <!-- dog=0.7 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(40.467589 54.252657)scale(0.09 -0.09)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-64\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-67\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"188.134766\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"271.923828\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"335.546875\"/>\n", "     <use xlink:href=\"#DejaVuSans-37\" x=\"367.333984\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <g id=\"patch_14\">\n", "     <path d=\"M 108.520556 46.494376 \n", "L 152.287275 46.494376 \n", "L 152.287275 29.864064 \n", "L 108.520556 29.864064 \n", "z\n", "\" style=\"fill: #bf00bf\"/>\n", "    </g>\n", "    <!-- cat=0.9 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(112.480556 40.662657)scale(0.09 -0.09)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-63\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"54.980469\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"116.259766\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"155.46875\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"239.257812\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"302.880859\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"334.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pd3121bced0\">\n", "   <rect x=\"33.2875\" y=\"10.878096\" width=\"176.35508\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig = d2l.plt.imshow(img)\n", "show_bboxes(fig.axes, anchors * bbox_scale,\n", "            ['dog=0.9', 'dog=0.8', 'dog=0.7', 'cat=0.9'])"]}, {"cell_type": "markdown", "id": "25b63f89", "metadata": {"origin_pos": 60}, "source": ["现在我们可以调用`multibox_detection`函数来执行非极大值抑制，其中阈值设置为0.5。\n", "请注意，我们在示例的张量输入中添加了维度。\n", "\n", "我们可以看到[**返回结果的形状是（批量大小，锚框的数量，6）**]。\n", "最内层维度中的六个元素提供了同一预测边界框的输出信息。\n", "第一个元素是预测的类索引，从0开始（0代表狗，1代表猫），值-1表示背景或在非极大值抑制中被移除了。\n", "第二个元素是预测的边界框的置信度。\n", "其余四个元素分别是预测边界框左上角和右下角的$(x, y)$轴坐标（范围介于0和1之间）。\n"]}, {"cell_type": "code", "execution_count": 21, "id": "ab9c180c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:46.440560Z", "iopub.status.busy": "2023-08-18T07:00:46.439738Z", "iopub.status.idle": "2023-08-18T07:00:46.458973Z", "shell.execute_reply": "2023-08-18T07:00:46.457996Z"}, "origin_pos": 62, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[[ 0.00,  0.90,  0.10,  0.08,  0.52,  0.92],\n", "         [ 1.00,  0.90,  0.55,  0.20,  0.90,  0.88],\n", "         [-1.00,  0.80,  0.08,  0.20,  0.56,  0.95],\n", "         [-1.00,  0.70,  0.15,  0.30,  0.62,  0.91]]])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["output = multibox_detection(cls_probs.unsqueeze(dim=0),\n", "                            offset_preds.unsqueeze(dim=0),\n", "                            anchors.unsqueeze(dim=0),\n", "                            nms_threshold=0.5)\n", "output"]}, {"cell_type": "markdown", "id": "79cb8b53", "metadata": {"origin_pos": 64}, "source": ["删除-1类别（背景）的预测边界框后，我们可以[**输出由非极大值抑制保存的最终预测边界框**]。\n"]}, {"cell_type": "code", "execution_count": 22, "id": "f1e04b3f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:46.496750Z", "iopub.status.busy": "2023-08-18T07:00:46.495866Z", "iopub.status.idle": "2023-08-18T07:00:46.753536Z", "shell.execute_reply": "2023-08-18T07:00:46.752302Z"}, "origin_pos": 65, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"216.84258pt\" height=\"170.656221pt\" viewBox=\"0 0 216.84258 170.656221\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:00:46.686810</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 170.656221 \n", "L 216.84258 170.656221 \n", "L 216.84258 0 \n", "L 0 0 \n", "L 0 170.656221 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 209.64258 146.778096 \n", "L 209.64258 10.878096 \n", "L 33.2875 10.878096 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pe2aa6bfcac)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image781ecba93e\" transform=\"scale(1 -1)translate(0 -136)\" x=\"33.2875\" y=\"-10.778096\" width=\"177\" height=\"136\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 51.044132 21.871216 \n", "L 125.113264 21.871216 \n", "L 125.113264 136.027218 \n", "L 51.044132 136.027218 \n", "L 51.044132 21.871216 \n", "z\n", "\" clip-path=\"url(#pe2aa6bfcac)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 130.40393 38.179222 \n", "L 192.128198 38.179222 \n", "L 192.128198 130.591217 \n", "L 130.40393 130.591217 \n", "L 130.40393 38.179222 \n", "z\n", "\" clip-path=\"url(#pe2aa6bfcac)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m9e013a9256\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9e013a9256\" x=\"33.408623\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(30.227373 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m9e013a9256\" x=\"81.857821\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(72.314071 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m9e013a9256\" x=\"130.307019\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(120.763269 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m9e013a9256\" x=\"178.756217\" y=\"146.778096\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(169.212467 161.376533)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"mb1b03cd978\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb1b03cd978\" x=\"33.2875\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mb1b03cd978\" x=\"33.2875\" y=\"35.223818\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 39.023036)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mb1b03cd978\" x=\"33.2875\" y=\"59.448417\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(7.2 63.247635)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mb1b03cd978\" x=\"33.2875\" y=\"83.673016\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(7.2 87.472234)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mb1b03cd978\" x=\"33.2875\" y=\"107.897614\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(7.2 111.696833)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mb1b03cd978\" x=\"33.2875\" y=\"132.122213\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(7.2 135.921432)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 33.2875 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 209.64258 146.778096 \n", "L 209.64258 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 33.2875 146.778096 \n", "L 209.64258 146.778096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 33.2875 10.878096 \n", "L 209.64258 10.878096 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <g id=\"patch_9\">\n", "     <path d=\"M 27.689835 30.186372 \n", "L 74.398429 30.186372 \n", "L 74.398429 13.55606 \n", "L 27.689835 13.55606 \n", "z\n", "\" style=\"fill: #0000ff\"/>\n", "    </g>\n", "    <!-- dog=0.9 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(31.649835 24.354654)scale(0.09 -0.09)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-64\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-67\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"188.134766\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"271.923828\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"335.546875\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"367.333984\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <g id=\"patch_10\">\n", "     <path d=\"M 108.520571 46.494378 \n", "L 152.28729 46.494378 \n", "L 152.28729 29.864065 \n", "L 108.520571 29.864065 \n", "z\n", "\" style=\"fill: #0000ff\"/>\n", "    </g>\n", "    <!-- cat=0.9 -->\n", "    <g style=\"fill: #ffffff\" transform=\"translate(112.480571 40.662659)scale(0.09 -0.09)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-63\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"54.980469\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"116.259766\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"155.46875\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"239.257812\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"302.880859\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"334.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pe2aa6bfcac\">\n", "   <rect x=\"33.2875\" y=\"10.878096\" width=\"176.35508\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig = d2l.plt.imshow(img)\n", "for i in output[0].detach().numpy():\n", "    if i[0] == -1:\n", "        continue\n", "    label = ('dog=', 'cat=')[int(i[0])] + str(i[1])\n", "    show_bboxes(fig.axes, [torch.tensor(i[2:]) * bbox_scale], label)"]}, {"cell_type": "markdown", "id": "222c6994", "metadata": {"origin_pos": 66}, "source": ["实践中，在执行非极大值抑制前，我们甚至可以将置信度较低的预测边界框移除，从而减少此算法中的计算量。\n", "我们也可以对非极大值抑制的输出结果进行后处理。例如，只保留置信度更高的结果作为最终输出。\n", "\n", "## 小结\n", "\n", "* 我们以图像的每个像素为中心生成不同形状的锚框。\n", "* 交并比（IoU）也被称为杰卡德系数，用于衡量两个边界框的相似性。它是相交面积与相并面积的比率。\n", "* 在训练集中，我们需要给每个锚框两种类型的标签。一个是与锚框中目标检测的类别，另一个是锚框真实相对于边界框的偏移量。\n", "* 预测期间可以使用非极大值抑制（NMS）来移除类似的预测边界框，从而简化输出。\n", "\n", "## 练习\n", "\n", "1. 在`multibox_prior`函数中更改`sizes`和`ratios`的值。生成的锚框有什么变化？\n", "1. 构建并可视化两个IoU为0.5的边界框。它们是怎样重叠的？\n", "1. 在 :numref:`subsec_labeling-anchor-boxes`和 :numref:`subsec_predicting-bounding-boxes-nms`中修改变量`anchors`，结果如何变化？\n", "1. 非极大值抑制是一种贪心算法，它通过*移除*来抑制预测的边界框。是否存在一种可能，被移除的一些框实际上是有用的？如何修改这个算法来柔和地抑制？可以参考Soft-NMS :cite:`Bodla.Singh.Chellappa.ea.2017`。\n", "1. 如果非手动，非最大限度的抑制可以被学习吗？\n"]}, {"cell_type": "markdown", "id": "e48b2d07", "metadata": {"origin_pos": 68, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2946)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
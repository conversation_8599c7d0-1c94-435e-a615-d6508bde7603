{"cells": [{"cell_type": "markdown", "id": "98ee705e", "metadata": {"origin_pos": 0}, "source": ["# 全卷积网络\n", ":label:`sec_fcn`\n", "\n", "如 :numref:`sec_semantic_segmentation`中所介绍的那样，语义分割是对图像中的每个像素分类。\n", "*全卷积网络*（fully convolutional network，FCN）采用卷积神经网络实现了从图像像素到像素类别的变换 :cite:`Long.Shelhamer.Darrell.2015`。\n", "与我们之前在图像分类或目标检测部分介绍的卷积神经网络不同，全卷积网络将中间层特征图的高和宽变换回输入图像的尺寸：这是通过在 :numref:`sec_transposed_conv`中引入的*转置卷积*（transposed convolution）实现的。\n", "因此，输出的类别预测与输入图像在像素级别上具有一一对应关系：通道维的输出即该位置对应像素的类别预测。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "9ba53b71", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:20.570706Z", "iopub.status.busy": "2023-08-18T07:07:20.570035Z", "iopub.status.idle": "2023-08-18T07:07:22.638674Z", "shell.execute_reply": "2023-08-18T07:07:22.637517Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "import torchvision\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "a6b35251", "metadata": {"origin_pos": 4}, "source": ["## 构造模型\n", "\n", "下面我们了解一下全卷积网络模型最基本的设计。\n", "如 :numref:`fig_fcn`所示，全卷积网络先使用卷积神经网络抽取图像特征，然后通过$1\\times 1$卷积层将通道数变换为类别个数，最后在 :numref:`sec_transposed_conv`中通过转置卷积层将特征图的高和宽变换为输入图像的尺寸。\n", "因此，模型输出与输入图像的高和宽相同，且最终输出通道包含了该空间位置像素的类别预测。\n", "\n", "![全卷积网络](../img/fcn.svg)\n", ":label:`fig_fcn`\n", "\n", "下面，我们[**使用在ImageNet数据集上预训练的ResNet-18模型来提取图像特征**]，并将该网络记为`pretrained_net`。\n", "ResNet-18模型的最后几层包括全局平均汇聚层和全连接层，然而全卷积网络中不需要它们。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "37e86099", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:22.642884Z", "iopub.status.busy": "2023-08-18T07:07:22.642480Z", "iopub.status.idle": "2023-08-18T07:07:23.298176Z", "shell.execute_reply": "2023-08-18T07:07:23.297190Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading: \"https://download.pytorch.org/models/resnet18-f37072fd.pth\" to /home/<USER>/.cache/torch/hub/checkpoints/resnet18-f37072fd.pth\n"]}, {"data": {"application/json": {"ascii": false, "bar_format": null, "colour": null, "elapsed": 0.00848388671875, "initial": 0, "n": 0, "ncols": null, "nrows": null, "postfix": null, "prefix": "", "rate": null, "total": 46830571, "unit": "B", "unit_divisor": 1024, "unit_scale": true}, "application/vnd.jupyter.widget-view+json": {"model_id": "5251df7f557143a6adf3d2225231761b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0.00/44.7M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["[Sequential(\n", "   (0): BasicBlock(\n", "     (conv1): Conv2d(256, 512, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)\n", "     (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "     (relu): ReLU(inplace=True)\n", "     (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "     (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "     (downsample): Sequential(\n", "       (0): Conv2d(256, 512, kernel_size=(1, 1), stride=(2, 2), bias=False)\n", "       (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "     )\n", "   )\n", "   (1): BasicBlock(\n", "     (conv1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "     (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "     (relu): ReLU(inplace=True)\n", "     (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "     (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "   )\n", " ),\n", " AdaptiveAvgPool2d(output_size=(1, 1)),\n", " Linear(in_features=512, out_features=1000, bias=True)]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["pretrained_net = torchvision.models.resnet18(pretrained=True)\n", "list(pretrained_net.children())[-3:]"]}, {"cell_type": "markdown", "id": "c7a6c6ca", "metadata": {"origin_pos": 8}, "source": ["接下来，我们[**创建一个全卷积网络`net`**]。\n", "它复制了ResNet-18中大部分的预训练层，除了最后的全局平均汇聚层和最接近输出的全连接层。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "92397bcf", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:23.303038Z", "iopub.status.busy": "2023-08-18T07:07:23.302447Z", "iopub.status.idle": "2023-08-18T07:07:23.307017Z", "shell.execute_reply": "2023-08-18T07:07:23.306110Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["net = nn.Sequential(*list(pretrained_net.children())[:-2])"]}, {"cell_type": "markdown", "id": "41361fe4", "metadata": {"origin_pos": 11}, "source": ["给定高度为320和宽度为480的输入，`net`的前向传播将输入的高和宽减小至原来的$1/32$，即10和15。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6cbe7c99", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:23.311746Z", "iopub.status.busy": "2023-08-18T07:07:23.310972Z", "iopub.status.idle": "2023-08-18T07:07:23.369499Z", "shell.execute_reply": "2023-08-18T07:07:23.368494Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([1, 512, 10, 15])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.rand(size=(1, 3, 320, 480))\n", "net(X).shape"]}, {"cell_type": "markdown", "id": "b2aa79ff", "metadata": {"origin_pos": 15}, "source": ["接下来[**使用$1\\times1$卷积层将输出通道数转换为Pascal VOC2012数据集的类数（21类）。**]\n", "最后需要(**将特征图的高度和宽度增加32倍**)，从而将其变回输入图像的高和宽。\n", "回想一下 :numref:`sec_padding`中卷积层输出形状的计算方法：\n", "由于$(320-64+16\\times2+32)/32=10$且$(480-64+16\\times2+32)/32=15$，我们构造一个步幅为$32$的转置卷积层，并将卷积核的高和宽设为$64$，填充为$16$。\n", "我们可以看到如果步幅为$s$，填充为$s/2$（假设$s/2$是整数）且卷积核的高和宽为$2s$，转置卷积核会将输入的高和宽分别放大$s$倍。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "1e32ef24", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:23.374842Z", "iopub.status.busy": "2023-08-18T07:07:23.373922Z", "iopub.status.idle": "2023-08-18T07:07:23.405937Z", "shell.execute_reply": "2023-08-18T07:07:23.404771Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["num_classes = 21\n", "net.add_module('final_conv', nn.Conv2d(512, num_classes, kernel_size=1))\n", "net.add_module('transpose_conv', nn.ConvTranspose2d(num_classes, num_classes,\n", "                                    kernel_size=64, padding=16, stride=32))"]}, {"cell_type": "markdown", "id": "fe867380", "metadata": {"origin_pos": 19}, "source": ["## [**初始化转置卷积层**]\n", "\n", "在图像处理中，我们有时需要将图像放大，即*上采样*（upsampling）。\n", "*双线性插值*（bilinear interpolation）\n", "是常用的上采样方法之一，它也经常用于初始化转置卷积层。\n", "\n", "为了解释双线性插值，假设给定输入图像，我们想要计算上采样输出图像上的每个像素。\n", "\n", "1. 将输出图像的坐标$(x,y)$映射到输入图像的坐标$(x',y')$上。\n", "例如，根据输入与输出的尺寸之比来映射。\n", "请注意，映射后的$x′$和$y′$是实数。\n", "2. 在输入图像上找到离坐标$(x',y')$最近的4个像素。\n", "3. 输出图像在坐标$(x,y)$上的像素依据输入图像上这4个像素及其与$(x',y')$的相对距离来计算。\n", "\n", "双线性插值的上采样可以通过转置卷积层实现，内核由以下`bilinear_kernel`函数构造。\n", "限于篇幅，我们只给出`bilinear_kernel`函数的实现，不讨论算法的原理。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "81e0e496", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:23.410931Z", "iopub.status.busy": "2023-08-18T07:07:23.410049Z", "iopub.status.idle": "2023-08-18T07:07:23.418870Z", "shell.execute_reply": "2023-08-18T07:07:23.417816Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [], "source": ["def bilinear_kernel(in_channels, out_channels, kernel_size):\n", "    factor = (kernel_size + 1) // 2\n", "    if kernel_size % 2 == 1:\n", "        center = factor - 1\n", "    else:\n", "        center = factor - 0.5\n", "    og = (torch.arange(kernel_size).reshape(-1, 1),\n", "          torch.arange(kernel_size).reshape(1, -1))\n", "    filt = (1 - torch.abs(og[0] - center) / factor) * \\\n", "           (1 - torch.abs(og[1] - center) / factor)\n", "    weight = torch.zeros((in_channels, out_channels,\n", "                          kernel_size, kernel_size))\n", "    weight[range(in_channels), range(out_channels), :, :] = filt\n", "    return weight"]}, {"cell_type": "markdown", "id": "6e5b2c78", "metadata": {"origin_pos": 23}, "source": ["让我们用[**双线性插值的上采样实验**]它由转置卷积层实现。\n", "我们构造一个将输入的高和宽放大2倍的转置卷积层，并将其卷积核用`bilinear_kernel`函数初始化。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "c181ae97", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:23.423829Z", "iopub.status.busy": "2023-08-18T07:07:23.422974Z", "iopub.status.idle": "2023-08-18T07:07:23.431177Z", "shell.execute_reply": "2023-08-18T07:07:23.430098Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [], "source": ["conv_trans = nn.ConvTranspose2d(3, 3, kernel_size=4, padding=1, stride=2,\n", "                                bias=False)\n", "conv_trans.weight.data.copy_(bilinear_kernel(3, 3, 4));"]}, {"cell_type": "markdown", "id": "75884a8b", "metadata": {"origin_pos": 27}, "source": ["读取图像`X`，将上采样的结果记作`Y`。为了打印图像，我们需要调整通道维的位置。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "cdbf1f0e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:23.435665Z", "iopub.status.busy": "2023-08-18T07:07:23.435278Z", "iopub.status.idle": "2023-08-18T07:07:23.521627Z", "shell.execute_reply": "2023-08-18T07:07:23.520407Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [], "source": ["img = torchvision.transforms.ToTensor()(d2l.Image.open('../img/catdog.jpg'))\n", "X = img.unsqueeze(0)\n", "Y = conv_trans(X)\n", "out_img = Y[0].permute(1, 2, 0).detach()"]}, {"cell_type": "markdown", "id": "13f8e306", "metadata": {"origin_pos": 31}, "source": ["可以看到，转置卷积层将图像的高和宽分别放大了2倍。\n", "除了坐标刻度不同，双线性插值放大的图像和在 :numref:`sec_bbox`中打印出的原图看上去没什么两样。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "9bafc470", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:23.527421Z", "iopub.status.busy": "2023-08-18T07:07:23.526512Z", "iopub.status.idle": "2023-08-18T07:07:24.199909Z", "shell.execute_reply": "2023-08-18T07:07:24.199093Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["input image shape: torch.<PERSON>ze([561, 728, 3])\n", "output image shape: torch.Size([1122, 1456, 3])\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"223.20508pt\" height=\"170.716782pt\" viewBox=\"0 0 223.20508 170.716782\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:07:24.005352</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 170.716782 \n", "L 223.20508 170.716782 \n", "L 223.20508 0 \n", "L 0 0 \n", "L 0 170.716782 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 39.65 146.838657 \n", "L 216.00508 146.838657 \n", "L 216.00508 10.938657 \n", "L 39.65 10.938657 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p3273cf19b5)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagebda21848e6\" transform=\"scale(1 -1)translate(0 -136)\" x=\"40\" y=\"-10.716782\" width=\"177\" height=\"136\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mf0004d8a24\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf0004d8a24\" x=\"39.710561\" y=\"146.838657\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(36.529311 161.437095)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mf0004d8a24\" x=\"100.272059\" y=\"146.838657\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(90.728309 161.437095)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mf0004d8a24\" x=\"160.833556\" y=\"146.838657\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(148.108556 161.437095)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m672efb9a72\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m672efb9a72\" x=\"39.65\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.2875 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m672efb9a72\" x=\"39.65\" y=\"35.223818\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(13.5625 39.023036)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m672efb9a72\" x=\"39.65\" y=\"59.448417\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(13.5625 63.247635)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m672efb9a72\" x=\"39.65\" y=\"83.673016\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(13.5625 87.472234)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m672efb9a72\" x=\"39.65\" y=\"107.897614\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(13.5625 111.696833)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m672efb9a72\" x=\"39.65\" y=\"132.122213\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(7.2 135.921432)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 39.65 146.838657 \n", "L 39.65 10.938657 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 216.00508 146.838657 \n", "L 216.00508 10.938657 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 39.65 146.838657 \n", "L 216.00508 146.838657 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 39.65 10.938657 \n", "L 216.00508 10.938657 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p3273cf19b5\">\n", "   <rect x=\"39.65\" y=\"10.938657\" width=\"176.35508\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["d2l.set_figsize()\n", "print('input image shape:', img.permute(1, 2, 0).shape)\n", "d2l.plt.imshow(img.permute(1, 2, 0));\n", "print('output image shape:', out_img.shape)\n", "d2l.plt.imshow(out_img);"]}, {"cell_type": "markdown", "id": "e28a121f", "metadata": {"origin_pos": 35}, "source": ["全卷积网络[**用双线性插值的上采样初始化转置卷积层。对于$1\\times 1$卷积层，我们使用Xavier初始化参数。**]\n"]}, {"cell_type": "code", "execution_count": 10, "id": "3607f0c9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:24.203681Z", "iopub.status.busy": "2023-08-18T07:07:24.203097Z", "iopub.status.idle": "2023-08-18T07:07:24.209142Z", "shell.execute_reply": "2023-08-18T07:07:24.208048Z"}, "origin_pos": 37, "tab": ["pytorch"]}, "outputs": [], "source": ["W = bilinear_kernel(num_classes, num_classes, 64)\n", "net.transpose_conv.weight.data.copy_(W);"]}, {"cell_type": "markdown", "id": "ff2a5afd", "metadata": {"origin_pos": 39}, "source": ["## [**读取数据集**]\n", "\n", "我们用 :numref:`sec_semantic_segmentation`中介绍的语义分割读取数据集。\n", "指定随机裁剪的输出图像的形状为$320\\times 480$：高和宽都可以被$32$整除。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "ff06cc24", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:24.213905Z", "iopub.status.busy": "2023-08-18T07:07:24.213186Z", "iopub.status.idle": "2023-08-18T07:07:55.535066Z", "shell.execute_reply": "2023-08-18T07:07:55.534048Z"}, "origin_pos": 40, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["read 1114 examples\n"]}, {"name": "stdout", "output_type": "stream", "text": ["read 1078 examples\n"]}], "source": ["batch_size, crop_size = 32, (320, 480)\n", "train_iter, test_iter = d2l.load_data_voc(batch_size, crop_size)"]}, {"cell_type": "markdown", "id": "79c83844", "metadata": {"origin_pos": 42}, "source": ["## [**训练**]\n", "\n", "现在我们可以训练全卷积网络了。\n", "这里的损失函数和准确率计算与图像分类中的并没有本质上的不同，因为我们使用转置卷积层的通道来预测像素的类别，所以需要在损失计算中指定通道维。\n", "此外，模型基于每个像素的预测类别是否正确来计算准确率。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "244b4702", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:55.540275Z", "iopub.status.busy": "2023-08-18T07:07:55.539598Z", "iopub.status.idle": "2023-08-18T07:08:45.398121Z", "shell.execute_reply": "2023-08-18T07:08:45.397216Z"}, "origin_pos": 44, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.443, train acc 0.863, test acc 0.848\n", "254.0 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"184.455469pt\" viewBox=\"0 0 235.**********.455469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:08:45.354503</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 184.455469 \n", "L 235.**********.455469 \n", "L 235.784375 -0 \n", "L 0 -0 \n", "L 0 184.455469 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.899219 \n", "L 225.**********.899219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 30.**********.899219 \n", "L 30.103125 10.999219 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m12607e7f8b\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m12607e7f8b\" x=\"30.103125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(26.921875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 78.928125 146.899219 \n", "L 78.928125 10.999219 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m12607e7f8b\" x=\"78.928125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 127.753125 146.899219 \n", "L 127.753125 10.999219 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m12607e7f8b\" x=\"127.753125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(124.571875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 176.578125 146.899219 \n", "L 176.578125 10.999219 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m12607e7f8b\" x=\"176.578125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(173.396875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.899219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m12607e7f8b\" x=\"225.403125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(222.221875 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 175.175781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.**********.899219 \n", "L 225.**********.899219 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m9e99935c0f\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9e99935c0f\" x=\"30.103125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 150.698437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 119.719219 \n", "L 225.403125 119.719219 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m9e99935c0f\" x=\"30.103125\" y=\"119.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 123.518437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 92.539219 \n", "L 225.403125 92.539219 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m9e99935c0f\" x=\"30.103125\" y=\"92.539219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 96.338437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 65.359219 \n", "L 225.403125 65.359219 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m9e99935c0f\" x=\"30.103125\" y=\"65.359219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 69.158437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 38.179219 \n", "L 225.403125 38.179219 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m9e99935c0f\" x=\"30.103125\" y=\"38.179219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 41.978437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m9e99935c0f\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 30.316184 -1 \n", "L 38.719301 57.794608 \n", "L 47.335478 60.936166 \n", "L 55.951654 61.132136 \n", "L 64.567831 57.870953 \n", "L 73.184007 59.677574 \n", "L 78.928125 59.664137 \n", "L 87.544301 75.880119 \n", "L 96.160478 70.040812 \n", "L 104.776654 71.446429 \n", "L 113.392831 73.29838 \n", "L 122.009007 71.704614 \n", "L 127.753125 71.879637 \n", "L 136.369301 77.300016 \n", "L 144.985478 78.772657 \n", "L 153.601654 80.582829 \n", "L 162.217831 80.49326 \n", "L 170.834007 81.179315 \n", "L 176.578125 81.89926 \n", "L 185.194301 81.681283 \n", "L 193.810478 81.194465 \n", "L 202.426654 83.498498 \n", "L 211.042831 86.383689 \n", "L 219.659007 87.024511 \n", "L 225.403125 86.654475 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M -1 54.064427 \n", "L 7.126654 50.818209 \n", "L 15.742831 47.546255 \n", "L 24.359007 45.871144 \n", "L 30.103125 44.970211 \n", "L 38.719301 35.134482 \n", "L 47.335478 34.211351 \n", "L 55.951654 34.198015 \n", "L 64.567831 35.043922 \n", "L 73.184007 34.81743 \n", "L 78.928125 35.009552 \n", "L 87.544301 31.987652 \n", "L 96.160478 33.208967 \n", "L 104.776654 32.756166 \n", "L 113.392831 32.366098 \n", "L 122.009007 32.929726 \n", "L 127.753125 32.872608 \n", "L 136.369301 31.907484 \n", "L 144.985478 31.551797 \n", "L 153.601654 31.135245 \n", "L 162.217831 31.172322 \n", "L 170.834007 31.05388 \n", "L 176.578125 30.867224 \n", "L 185.194301 31.035709 \n", "L 193.810478 31.140671 \n", "L 202.426654 30.494803 \n", "L 211.042831 29.594048 \n", "L 219.659007 29.4718 \n", "L 225.403125 29.636162 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 30.103125 35.992837 \n", "L 78.928125 33.583936 \n", "L 127.753125 32.143736 \n", "L 176.578125 31.458134 \n", "L 225.403125 31.595067 \n", "\" clip-path=\"url(#pf53d548f3d)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.899219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.899219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.899219 \n", "L 225.**********.899219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 37.103125 141.899219 \n", "L 114.871875 141.899219 \n", "Q 116.871875 141.899219 116.871875 139.899219 \n", "L 116.871875 96.864844 \n", "Q 116.871875 94.864844 114.871875 94.864844 \n", "L 37.103125 94.864844 \n", "Q 35.103125 94.864844 35.103125 96.864844 \n", "L 35.103125 139.899219 \n", "Q 35.103125 141.899219 37.103125 141.899219 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 39.103125 102.963281 \n", "L 49.103125 102.963281 \n", "L 59.103125 102.963281 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(67.103125 106.463281)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 39.103125 117.641406 \n", "L 49.103125 117.641406 \n", "L 59.103125 117.641406 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(67.103125 121.141406)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 39.103125 132.319531 \n", "L 49.103125 132.319531 \n", "L 59.103125 132.319531 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(67.103125 135.819531)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf53d548f3d\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["def loss(inputs, targets):\n", "    return F.cross_entropy(inputs, targets, reduction='none').mean(1).mean(1)\n", "\n", "num_epochs, lr, wd, devices = 5, 0.001, 1e-3, d2l.try_all_gpus()\n", "trainer = torch.optim.SGD(net.parameters(), lr=lr, weight_decay=wd)\n", "d2l.train_ch13(net, train_iter, test_iter, loss, trainer, num_epochs, devices)"]}, {"cell_type": "markdown", "id": "8bcb8df5", "metadata": {"origin_pos": 46}, "source": ["## [**预测**]\n", "\n", "在预测时，我们需要将输入图像在各个通道做标准化，并转成卷积神经网络所需要的四维输入格式。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "bdb803a3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:08:45.402153Z", "iopub.status.busy": "2023-08-18T07:08:45.401873Z", "iopub.status.idle": "2023-08-18T07:08:45.406358Z", "shell.execute_reply": "2023-08-18T07:08:45.405611Z"}, "origin_pos": 48, "tab": ["pytorch"]}, "outputs": [], "source": ["def predict(img):\n", "    X = test_iter.dataset.normalize_image(img).unsqueeze(0)\n", "    pred = net(X.to(devices[0])).argmax(dim=1)\n", "    return pred.reshape(pred.shape[1], pred.shape[2])"]}, {"cell_type": "markdown", "id": "54d2aa8a", "metadata": {"origin_pos": 50}, "source": ["为了[**可视化预测的类别**]给每个像素，我们将预测类别映射回它们在数据集中的标注颜色。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "27e3aa15", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:08:45.409772Z", "iopub.status.busy": "2023-08-18T07:08:45.409264Z", "iopub.status.idle": "2023-08-18T07:08:45.413358Z", "shell.execute_reply": "2023-08-18T07:08:45.412563Z"}, "origin_pos": 52, "tab": ["pytorch"]}, "outputs": [], "source": ["def label2image(pred):\n", "    colormap = torch.tensor(d2l.VOC_COLORMAP, device=devices[0])\n", "    X = pred.long()\n", "    return colormap[X, :]"]}, {"cell_type": "markdown", "id": "e3a9d039", "metadata": {"origin_pos": 54}, "source": ["测试数据集中的图像大小和形状各异。\n", "由于模型使用了步幅为32的转置卷积层，因此当输入图像的高或宽无法被32整除时，转置卷积层输出的高或宽会与输入图像的尺寸有偏差。\n", "为了解决这个问题，我们可以在图像中截取多块高和宽为32的整数倍的矩形区域，并分别对这些区域中的像素做前向传播。\n", "请注意，这些区域的并集需要完整覆盖输入图像。\n", "当一个像素被多个区域所覆盖时，它在不同区域前向传播中转置卷积层输出的平均值可以作为`softmax`运算的输入，从而预测类别。\n", "\n", "为简单起见，我们只读取几张较大的测试图像，并从图像的左上角开始截取形状为$320\\times480$的区域用于预测。\n", "对于这些测试图像，我们逐一打印它们截取的区域，再打印预测结果，最后打印标注的类别。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "f0f8cff8", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:08:45.416847Z", "iopub.status.busy": "2023-08-18T07:08:45.416234Z", "iopub.status.idle": "2023-08-18T07:09:10.704851Z", "shell.execute_reply": "2023-08-18T07:09:10.704050Z"}, "origin_pos": 56, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"464.3pt\" height=\"312.82624pt\" viewBox=\"0 0 464.3 312.82624\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:09:10.557407</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 312.82624 \n", "L 464.3 312.82624 \n", "L 464.3 0 \n", "L 0 0 \n", "L 0 312.82624 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 10.7 71.895652 \n", "L 107.743478 71.895652 \n", "L 107.743478 7.2 \n", "L 10.7 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p0900d995fb)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image93a30a167d\" transform=\"scale(1 -1)translate(0 -65)\" x=\"10.7\" y=\"-6.895652\" width=\"98\" height=\"65\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 10.7 71.895652 \n", "L 10.7 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 107.743478 71.895652 \n", "L 107.743478 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 10.7 71.895652 \n", "L 107.743478 71.895652 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 10.7 7.2 \n", "L 107.743478 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 127.152174 71.895652 \n", "L 224.195652 71.895652 \n", "L 224.195652 7.2 \n", "L 127.152174 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p87f4e2cf27)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageeb13bca9e9\" transform=\"scale(1 -1)translate(0 -65)\" x=\"127.152174\" y=\"-6.895652\" width=\"98\" height=\"65\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 127.152174 71.895652 \n", "L 127.152174 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 224.195652 71.895652 \n", "L 224.195652 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 127.152174 71.895652 \n", "L 224.195652 71.895652 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 127.152174 7.2 \n", "L 224.195652 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 243.604348 71.895652 \n", "L 340.647826 71.895652 \n", "L 340.647826 7.2 \n", "L 243.604348 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p276401ea1e)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageb9a18841fc\" transform=\"scale(1 -1)translate(0 -65)\" x=\"243.604348\" y=\"-6.895652\" width=\"98\" height=\"65\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 243.604348 71.895652 \n", "L 243.604348 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 340.647826 71.895652 \n", "L 340.647826 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 243.604348 71.895652 \n", "L 340.647826 71.895652 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 243.604348 7.2 \n", "L 340.647826 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 360.056522 71.895652 \n", "L 457.1 71.895652 \n", "L 457.1 7.2 \n", "L 360.056522 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p6b1831d2c6)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image439fc2e139\" transform=\"scale(1 -1)translate(0 -65)\" x=\"360.056522\" y=\"-6.895652\" width=\"98\" height=\"65\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 360.056522 71.895652 \n", "L 360.056522 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 457.1 71.895652 \n", "L 457.1 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 360.056522 71.895652 \n", "L 457.1 71.895652 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 360.056522 7.2 \n", "L 457.1 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 10.7 187.010946 \n", "L 107.743478 187.010946 \n", "L 107.743478 122.315294 \n", "L 10.7 122.315294 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p2c85b65cab)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGIAAABBCAYAAAAqnXGWAAADa0lEQVR4nO2aS2sTURSAvzGpjUWl4qP1tVHcKEoXIvhaiviP/FOCCxeCG9Gl4MqF+GirNUHbWpu+bNo04+LcS8aaxpmQO3OSnA+GTNJ0Hvk4596550RAjFE4h4q+AEMwEUowEUowEUowEUowEUowEUowEUowEUowEUowEUoo9+Mg3uaYO+BhYALYBTaBHaCJLWp1o2cRE8Blt51MfDYOlBAZe4iEDbf9AlaBOrCCiFlzr7uMtqyIDPceAaeAa8ANYNJ9FmU4oT9Zy+1vu9ffwBZQA+aAqns/KqQWcQK4g0g4QrYfPwsx7Uj5AswCC0gU7QU6pwb+K2IcuAXcBI4TTkAn/IU1gB9IpMwDS0gEtXK8ltAcKCICzgMPgIvomF7FyI+/hYipAd/c/iYyxgwqHUWMAbfdViHfKMiCT2PbSOqqAl+RiFmlPf4MAv+IOA08BC6hIwqy4G+kiUTIMgdHSROJpu/ue1sUm+qiyYSIaeAR+Y8FRZFMdYtINFWBn8h0ezfHa4keJ0SUkWeAUZDQCZ/qdpBZ2xIiZg6RsxPw3OVKwIMPGv6ZqOK2M8BVJI0tAc+RqAnBoA0DuRMhk5ezyPgZChOREv+QGQoTkZImslYWChORkjqwHvD4JiIlNWSpJRQmIgUx4WZLnr4UhoYV/0zxHvgY+Fwm4gD2kCX4l0haCr1mZSL24RcRXwFvyG9F10QkiJHZ0VOk7pEnJsIRI6uxz5D6Rt6YCETCAvAEiYgiGGkRMTIovwNeIDWMoijvnw0M+hJ4t9lN8t5aSAp6DXyg+DJrNJO49ilgBn3lUd/35J9sW7T7oVpIhS1ZXVtBZj4e3wY05t6XkPrC233fK5K/SqURstx7H7hCcXnLp4w1ZA7/yb1uJP7eSOwPSl26Gx2bB0qIiHvAOfJZB/FNZovAZ9ptM41OFziEdO1rqgDXgbtIHbufQnxaqSPTxlmkoazOcPUrpSVVp98x4AKStqaREuJR0te3k90VDWRdfx6pBdfQk6eLJFPvq/+HCtJ4PIU0nyXHkjLSnhkhOX0TSTHLiIB1JAUNc/tkL2QWkeaAJbffYjTTTC/0XYTRG1YYUoKJUIKJUIKJUIKJUIKJUIKJUIKJUIKJUIKJUIKJUIKJUIKJUIKJUIKJUIKJUIKJUIKJUIKJUIKJUMIf/q/aMqyfxi8AAAAASUVORK5CYII=\" id=\"image04e6327056\" transform=\"scale(1 -1)translate(0 -65)\" x=\"10.7\" y=\"-122.010946\" width=\"98\" height=\"65\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 10.7 187.010946 \n", "L 10.7 122.315294 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 107.743478 187.010946 \n", "L 107.743478 122.315294 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 10.7 187.010946 \n", "L 107.743478 187.010946 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 10.7 122.315294 \n", "L 107.743478 122.315294 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 127.152174 187.010946 \n", "L 224.195652 187.010946 \n", "L 224.195652 122.315294 \n", "L 127.152174 122.315294 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p34bf184cfd)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image4ef1daa649\" transform=\"scale(1 -1)translate(0 -65)\" x=\"127.152174\" y=\"-122.010946\" width=\"98\" height=\"65\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 127.152174 187.010946 \n", "L 127.152174 122.315294 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 224.195652 187.010946 \n", "L 224.195652 122.315294 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 127.152174 187.010946 \n", "L 224.195652 187.010946 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 127.152174 122.315294 \n", "L 224.195652 122.315294 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 243.604348 187.010946 \n", "L 340.647826 187.010946 \n", "L 340.647826 122.315294 \n", "L 243.604348 122.315294 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pf6899b6464)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGIAAABBCAYAAAAqnXGWAAACeUlEQVR4nO3ZzYtNcRjA8c+9M94NxjQoRbKhJJSSlJWFv1XZyULJxlIpC8mChbdQChkaHIvnnGZSXrp3pvOcO8+3TnNr7j395vc593deZoRG1XvjvgdQRQWRpIJIUkEkqSCSNN/3API3xhxG7evv7baxFQRiondhGYexDUvYge3Ybw1iBe/wFq/bn1/wc6oRjGyp+4juyIYFHMFxHMWiwBive8/fasTkf8FL3MObiUc2YxBjMZkHcBB7221f+/tFcbSPBMSO9vX/TPzfavARN/Bioj3MwNI0L5aUEziJQwJj/dG/2Y0E9klbEGJe/OGXxNLSHel9NRLnk8kaIMRIrOtXcUwc+VlamviTA4M4gCs4q/9vwMY2IIjTuCZOuLMD0DUQiGVct3b1M3sN5BHHEezpexD/qMGniT89EIjHeIAffQ/kDzX4gPsT72FAN3TzOIPLYqnKdJ74hpt4MvEeBgTRtRsXcd7aM6C+asSzpzt4aJqpHCBE14LAuKAfkAbvcQvPp97bgCG6FnCu3bpL281GafAUt8W5YfpmAKJrF06Jm72DYgnrrs43CqbBqliG7uLrBu13piC6xuKp6oI4qe8X35QlgbUXO9v3/X7R2IE1637+EBO+gmd4hFem/f/D780gxJ8aiedS2wXSnMCZa7dlazCrYv3/iM/i/mC13TZvdFsEIncDuaGb/QoiSQWRpIJIUkEkqSCSVBBJKogkFUSSCiJJBZGkgkhSQSSpIJJUEEkqiCQVRJIKIkkFkaSCSFJBJKkgklQQSSqIJBVEkgoiSQWRpIJIUkEkqSCSVBBJKogkFUSSCiJJBZGkgkhSQSSpIJJUEEn6BYuwTwJm+6fHAAAAAElFTkSuQmCC\" id=\"imagea7e951685d\" transform=\"scale(1 -1)translate(0 -65)\" x=\"243.604348\" y=\"-122.010946\" width=\"98\" height=\"65\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 243.604348 187.010946 \n", "L 243.604348 122.315294 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 340.647826 187.010946 \n", "L 340.647826 122.315294 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 243.604348 187.010946 \n", "L 340.647826 187.010946 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 243.604348 122.315294 \n", "L 340.647826 122.315294 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 360.056522 187.010946 \n", "L 457.1 187.010946 \n", "L 457.1 122.315294 \n", "L 360.056522 122.315294 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pf896938ce8)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image118b8f21c5\" transform=\"scale(1 -1)translate(0 -65)\" x=\"360.056522\" y=\"-122.010946\" width=\"98\" height=\"65\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 360.056522 187.010946 \n", "L 360.056522 122.315294 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 457.1 187.010946 \n", "L 457.1 122.315294 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 360.056522 187.010946 \n", "L 457.1 187.010946 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 360.056522 122.315294 \n", "L 457.1 122.315294 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 10.7 302.12624 \n", "L 107.743478 302.12624 \n", "L 107.743478 237.430588 \n", "L 10.7 237.430588 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p6ac168c9c4)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagef0b1b15015\" transform=\"scale(1 -1)translate(0 -65)\" x=\"10.7\" y=\"-237.12624\" width=\"98\" height=\"65\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 10.7 302.12624 \n", "L 10.7 237.430588 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 107.743478 302.12624 \n", "L 107.743478 237.430588 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_45\">\n", "    <path d=\"M 10.7 302.12624 \n", "L 107.743478 302.12624 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_46\">\n", "    <path d=\"M 10.7 237.430588 \n", "L 107.743478 237.430588 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_10\">\n", "   <g id=\"patch_47\">\n", "    <path d=\"M 127.152174 302.12624 \n", "L 224.195652 302.12624 \n", "L 224.195652 237.430588 \n", "L 127.152174 237.430588 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p881aa2271e)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagee26582356c\" transform=\"scale(1 -1)translate(0 -65)\" x=\"127.152174\" y=\"-237.12624\" width=\"98\" height=\"65\"/>\n", "   </g>\n", "   <g id=\"patch_48\">\n", "    <path d=\"M 127.152174 302.12624 \n", "L 127.152174 237.430588 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_49\">\n", "    <path d=\"M 224.195652 302.12624 \n", "L 224.195652 237.430588 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_50\">\n", "    <path d=\"M 127.152174 302.12624 \n", "L 224.195652 302.12624 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_51\">\n", "    <path d=\"M 127.152174 237.430588 \n", "L 224.195652 237.430588 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_11\">\n", "   <g id=\"patch_52\">\n", "    <path d=\"M 243.604348 302.12624 \n", "L 340.647826 302.12624 \n", "L 340.647826 237.430588 \n", "L 243.604348 237.430588 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pa3a37e07c2)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image870ed19c71\" transform=\"scale(1 -1)translate(0 -65)\" x=\"243.604348\" y=\"-237.12624\" width=\"98\" height=\"65\"/>\n", "   </g>\n", "   <g id=\"patch_53\">\n", "    <path d=\"M 243.604348 302.12624 \n", "L 243.604348 237.430588 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_54\">\n", "    <path d=\"M 340.647826 302.12624 \n", "L 340.647826 237.430588 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_55\">\n", "    <path d=\"M 243.604348 302.12624 \n", "L 340.647826 302.12624 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_56\">\n", "    <path d=\"M 243.604348 237.430588 \n", "L 340.647826 237.430588 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_12\">\n", "   <g id=\"patch_57\">\n", "    <path d=\"M 360.056522 302.12624 \n", "L 457.1 302.12624 \n", "L 457.1 237.430588 \n", "L 360.056522 237.430588 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pcb5d2301a3)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image9b375b1d28\" transform=\"scale(1 -1)translate(0 -65)\" x=\"360.056522\" y=\"-237.12624\" width=\"98\" height=\"65\"/>\n", "   </g>\n", "   <g id=\"patch_58\">\n", "    <path d=\"M 360.056522 302.12624 \n", "L 360.056522 237.430588 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_59\">\n", "    <path d=\"M 457.1 302.12624 \n", "L 457.1 237.430588 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_60\">\n", "    <path d=\"M 360.056522 302.12624 \n", "L 457.1 302.12624 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_61\">\n", "    <path d=\"M 360.056522 237.430588 \n", "L 457.1 237.430588 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p0900d995fb\">\n", "   <rect x=\"10.7\" y=\"7.2\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p87f4e2cf27\">\n", "   <rect x=\"127.152174\" y=\"7.2\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p276401ea1e\">\n", "   <rect x=\"243.604348\" y=\"7.2\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6b1831d2c6\">\n", "   <rect x=\"360.056522\" y=\"7.2\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p2c85b65cab\">\n", "   <rect x=\"10.7\" y=\"122.315294\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p34bf184cfd\">\n", "   <rect x=\"127.152174\" y=\"122.315294\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pf6899b6464\">\n", "   <rect x=\"243.604348\" y=\"122.315294\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pf896938ce8\">\n", "   <rect x=\"360.056522\" y=\"122.315294\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6ac168c9c4\">\n", "   <rect x=\"10.7\" y=\"237.430588\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p881aa2271e\">\n", "   <rect x=\"127.152174\" y=\"237.430588\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pa3a37e07c2\">\n", "   <rect x=\"243.604348\" y=\"237.430588\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pcb5d2301a3\">\n", "   <rect x=\"360.056522\" y=\"237.430588\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 576x432 with 12 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["voc_dir = d2l.download_extract('voc2012', 'VOCdevkit/VOC2012')\n", "test_images, test_labels = d2l.read_voc_images(voc_dir, False)\n", "n, imgs = 4, []\n", "for i in range(n):\n", "    crop_rect = (0, 0, 320, 480)\n", "    X = torchvision.transforms.functional.crop(test_images[i], *crop_rect)\n", "    pred = label2image(predict(X))\n", "    imgs += [X.permute(1,2,0), pred.cpu(),\n", "             torchvision.transforms.functional.crop(\n", "                 test_labels[i], *crop_rect).permute(1,2,0)]\n", "d2l.show_images(imgs[::3] + imgs[1::3] + imgs[2::3], 3, n, scale=2);"]}, {"cell_type": "markdown", "id": "b82349b4", "metadata": {"origin_pos": 58}, "source": ["## 小结\n", "\n", "* 全卷积网络先使用卷积神经网络抽取图像特征，然后通过$1\\times 1$卷积层将通道数变换为类别个数，最后通过转置卷积层将特征图的高和宽变换为输入图像的尺寸。\n", "* 在全卷积网络中，我们可以将转置卷积层初始化为双线性插值的上采样。\n", "\n", "## 练习\n", "\n", "1. 如果将转置卷积层改用Xavier随机初始化，结果有什么变化？\n", "1. 调节超参数，能进一步提升模型的精度吗？\n", "1. 预测测试图像中所有像素的类别。\n", "1. 最初的全卷积网络的论文中 :cite:`<PERSON>.Shelhamer.Darrell.2015`还使用了某些卷积神经网络中间层的输出。试着实现这个想法。\n"]}, {"cell_type": "markdown", "id": "314d9c7f", "metadata": {"origin_pos": 60, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/3297)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": [], "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {"1a2196f22729431f83d039862392acc0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_d19858e45b944e8c8f9059db20975b35", "max": 46830571.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_8734c89a8ec0418fb79ebae7cb29e024", "tabbable": null, "tooltip": null, "value": 46830571.0}}, "5251df7f557143a6adf3d2225231761b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7cb4d17593b14ecbb1faa48dd9cfa341", "IPY_MODEL_1a2196f22729431f83d039862392acc0", "IPY_MODEL_a3afec78bd61439f8ac2cb8cd21ec2a7"], "layout": "IPY_MODEL_eadd40b78048487882acca1c18a48abb", "tabbable": null, "tooltip": null}}, "56a96b11f6874202b0c125339b47a25f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "7cb4d17593b14ecbb1faa48dd9cfa341": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_9741cfd24c0c4bdb9f733551ad298ce4", "placeholder": "​", "style": "IPY_MODEL_7e79ccfb796b4045abce79262f800cda", "tabbable": null, "tooltip": null, "value": "100%"}}, "7e79ccfb796b4045abce79262f800cda": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "8734c89a8ec0418fb79ebae7cb29e024": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "886e463378e84ac29c4788e4908e8a70": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9741cfd24c0c4bdb9f733551ad298ce4": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a3afec78bd61439f8ac2cb8cd21ec2a7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_886e463378e84ac29c4788e4908e8a70", "placeholder": "​", "style": "IPY_MODEL_56a96b11f6874202b0c125339b47a25f", "tabbable": null, "tooltip": null, "value": " 44.7M/44.7M [00:00&lt;00:00, 163MB/s]"}}, "d19858e45b944e8c8f9059db20975b35": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eadd40b78048487882acca1c18a48abb": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}
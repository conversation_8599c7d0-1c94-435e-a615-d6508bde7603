{"cells": [{"cell_type": "markdown", "id": "5df6c10f", "metadata": {"origin_pos": 0}, "source": ["# 序列模型\n", ":label:`sec_sequence`\n", "\n", "想象一下有人正在看网飞（Netflix，一个国外的视频网站）上的电影。\n", "一名忠实的用户会对每一部电影都给出评价，\n", "毕竟一部好电影需要更多的支持和认可。\n", "然而事实证明，事情并不那么简单。\n", "随着时间的推移，人们对电影的看法会发生很大的变化。\n", "事实上，心理学家甚至对这些现象起了名字：\n", "\n", "* *锚定*（anchoring）效应：基于其他人的意见做出评价。\n", "  例如，奥斯卡颁奖后，受到关注的电影的评分会上升，尽管它还是原来那部电影。\n", "  这种影响将持续几个月，直到人们忘记了这部电影曾经获得的奖项。\n", "  结果表明（ :cite:`<PERSON><PERSON><PERSON>.Beutel.ea.2017`），这种效应会使评分提高半个百分点以上。\n", "* *享乐适应*（hedonic adaption）：人们迅速接受并且适应一种更好或者更坏的情况\n", "  作为新的常态。\n", "  例如，在看了很多好电影之后，人们会强烈期望下部电影会更好。\n", "  因此，在许多精彩的电影被看过之后，即使是一部普通的也可能被认为是糟糕的。\n", "* *季节性*（seasonality）：少有观众喜欢在八月看圣诞老人的电影。\n", "* 有时，电影会由于导演或演员在制作中的不当行为变得不受欢迎。\n", "* 有些电影因为其极度糟糕只能成为小众电影。*Plan9from Outer Space*和*Troll2*就因为这个原因而臭名昭著的。\n", "\n", "简而言之，电影评分决不是固定不变的。\n", "因此，使用时间动力学可以得到更准确的电影推荐 :cite:`<PERSON>ren.2009`。\n", "当然，序列数据不仅仅是关于电影评分的。\n", "下面给出了更多的场景。\n", "\n", "* 在使用程序时，许多用户都有很强的特定习惯。\n", "  例如，在学生放学后社交媒体应用更受欢迎。在市场开放时股市交易软件更常用。\n", "* 预测明天的股价要比过去的股价更困难，尽管两者都只是估计一个数字。\n", "  毕竟，先见之明比事后诸葛亮难得多。\n", "  在统计学中，前者（对超出已知观测范围进行预测）称为*外推法*（extrapolation），\n", "  而后者（在现有观测值之间进行估计）称为*内插法*（interpolation）。\n", "* 在本质上，音乐、语音、文本和视频都是连续的。\n", "  如果它们的序列被我们重排，那么就会失去原有的意义。\n", "  比如，一个文本标题“狗咬人”远没有“人咬狗”那么令人惊讶，尽管组成两句话的字完全相同。\n", "* 地震具有很强的相关性，即大地震发生后，很可能会有几次小余震，\n", "  这些余震的强度比非大地震后的余震要大得多。\n", "  事实上，地震是时空相关的，即余震通常发生在很短的时间跨度和很近的距离内。\n", "* 人类之间的互动也是连续的，这可以从微博上的争吵和辩论中看出。\n", "\n", "## 统计工具\n", "\n", "处理序列数据需要统计工具和新的深度神经网络架构。\n", "为了简单起见，我们以 :numref:`fig_ftse100`所示的股票价格（富时100指数）为例。\n", "\n", "![近30年的富时100指数](../img/ftse100.png)\n", ":width:`400px`\n", ":label:`fig_ftse100`\n", "\n", "其中，用$x_t$表示价格，即在*时间步*（time step）\n", "$t \\in \\mathbb{Z}^+$时，观察到的价格$x_t$。\n", "请注意，$t$对于本文中的序列通常是离散的，并在整数或其子集上变化。\n", "假设一个交易员想在$t$日的股市中表现良好，于是通过以下途径预测$x_t$：\n", "\n", "$$x_t \\sim P(x_t \\mid x_{t-1}, \\ldots, x_1).$$\n", "\n", "### 自回归模型\n", "\n", "为了实现这个预测，交易员可以使用回归模型，\n", "例如在 :numref:`sec_linear_concise`中训练的模型。\n", "仅有一个主要问题：输入数据的数量，\n", "输入$x_{t-1}, \\ldots, x_1$本身因$t$而异。\n", "也就是说，输入数据的数量这个数字将会随着我们遇到的数据量的增加而增加，\n", "因此需要一个近似方法来使这个计算变得容易处理。\n", "本章后面的大部分内容将围绕着如何有效估计\n", "$P(x_t \\mid x_{t-1}, \\ldots, x_1)$展开。\n", "简单地说，它归结为以下两种策略。\n", "\n", "第一种策略，假设在现实情况下相当长的序列\n", "$x_{t-1}, \\ldots, x_1$可能是不必要的，\n", "因此我们只需要满足某个长度为$\\tau$的时间跨度，\n", "即使用观测序列$x_{t-1}, \\ldots, x_{t-\\tau}$。\n", "当下获得的最直接的好处就是参数的数量总是不变的，\n", "至少在$t > \\tau$时如此，这就使我们能够训练一个上面提及的深度网络。\n", "这种模型被称为*自回归模型*（autoregressive models），\n", "因为它们是对自己执行回归。\n", "\n", "第二种策略，如 :numref:`fig_sequence-model`所示，\n", "是保留一些对过去观测的总结$h_t$，\n", "并且同时更新预测$\\hat{x}_t$和总结$h_t$。\n", "这就产生了基于$\\hat{x}_t = P(x_t \\mid h_{t})$估计$x_t$，\n", "以及公式$h_t = g(h_{t-1}, x_{t-1})$更新的模型。\n", "由于$h_t$从未被观测到，这类模型也被称为\n", "*隐变量自回归模型*（latent autoregressive models）。\n", "\n", "![隐变量自回归模型](../img/sequence-model.svg)\n", ":label:`fig_sequence-model`\n", "\n", "这两种情况都有一个显而易见的问题：如何生成训练数据？\n", "一个经典方法是使用历史观测来预测下一个未来观测。\n", "显然，我们并不指望时间会停滞不前。\n", "然而，一个常见的假设是虽然特定值$x_t$可能会改变，\n", "但是序列本身的动力学不会改变。\n", "这样的假设是合理的，因为新的动力学一定受新的数据影响，\n", "而我们不可能用目前所掌握的数据来预测新的动力学。\n", "统计学家称不变的动力学为*静止的*（stationary）。\n", "因此，整个序列的估计值都将通过以下的方式获得：\n", "\n", "$$P(x_1, \\ldots, x_T) = \\prod_{t=1}^T P(x_t \\mid x_{t-1}, \\ldots, x_1).$$\n", "\n", "注意，如果我们处理的是离散的对象（如单词），\n", "而不是连续的数字，则上述的考虑仍然有效。\n", "唯一的差别是，对于离散的对象，\n", "我们需要使用分类器而不是回归模型来估计$P(x_t \\mid  x_{t-1}, \\ldots, x_1)$。\n", "\n", "### 马尔可夫模型\n", "\n", "回想一下，在自回归模型的近似法中，\n", "我们使用$x_{t-1}, \\ldots, x_{t-\\tau}$\n", "而不是$x_{t-1}, \\ldots, x_1$来估计$x_t$。\n", "只要这种是近似精确的，我们就说序列满足*马尔可夫条件*（Markov condition）。\n", "特别是，如果$\\tau = 1$，得到一个\n", "*一阶马尔可夫模型*（first-order Markov model），\n", "$P(x)$由下式给出：\n", "\n", "$$P(x_1, \\ldots, x_T) = \\prod_{t=1}^T P(x_t \\mid x_{t-1}) \\text{ 当 } P(x_1 \\mid x_0) = P(x_1).$$\n", "\n", "当假设$x_t$仅是离散值时，这样的模型特别棒，\n", "因为在这种情况下，使用动态规划可以沿着马尔可夫链精确地计算结果。\n", "例如，我们可以高效地计算$P(x_{t+1} \\mid x_{t-1})$：\n", "\n", "$$\n", "\\begin{aligned}\n", "P(x_{t+1} \\mid x_{t-1})\n", "&= \\frac{\\sum_{x_t} P(x_{t+1}, x_t, x_{t-1})}{P(x_{t-1})}\\\\\n", "&= \\frac{\\sum_{x_t} P(x_{t+1} \\mid x_t, x_{t-1}) P(x_t, x_{t-1})}{P(x_{t-1})}\\\\\n", "&= \\sum_{x_t} P(x_{t+1} \\mid x_t) P(x_t \\mid x_{t-1})\n", "\\end{aligned}\n", "$$\n", "\n", "利用这一事实，我们只需要考虑过去观察中的一个非常短的历史：\n", "$P(x_{t+1} \\mid x_t, x_{t-1}) = P(x_{t+1} \\mid x_t)$。\n", "隐马尔可夫模型中的动态规划超出了本节的范围\n", "（我们将在 :numref:`sec_bi_rnn`再次遇到），\n", "而动态规划这些计算工具已经在控制算法和强化学习算法广泛使用。\n", "\n", "### 因果关系\n", "\n", "原则上，将$P(x_1, \\ldots, x_T)$倒序展开也没什么问题。\n", "毕竟，基于条件概率公式，我们总是可以写出：\n", "\n", "$$P(x_1, \\ldots, x_T) = \\prod_{t=T}^1 P(x_t \\mid x_{t+1}, \\ldots, x_T).$$\n", "\n", "事实上，如果基于一个马尔可夫模型，\n", "我们还可以得到一个反向的条件概率分布。\n", "然而，在许多情况下，数据存在一个自然的方向，即在时间上是前进的。\n", "很明显，未来的事件不能影响过去。\n", "因此，如果我们改变$x_t$，可能会影响未来发生的事情$x_{t+1}$，但不能反过来。\n", "也就是说，如果我们改变$x_t$，基于过去事件得到的分布不会改变。\n", "因此，解释$P(x_{t+1} \\mid x_t)$应该比解释$P(x_t \\mid x_{t+1})$更容易。\n", "例如，在某些情况下，对于某些可加性噪声$\\epsilon$，\n", "显然我们可以找到$x_{t+1} = f(x_t) + \\epsilon$，\n", "而反之则不行 :cite:`Hoyer.Janzing.Mooij.ea.2009`。\n", "而这个向前推进的方向恰好也是我们通常感兴趣的方向。\n", "彼得斯等人 :cite:<PERSON><PERSON>.Janzing.Scholkopf.2017`\n", "对该主题的更多内容做了详尽的解释，而我们的上述讨论只是其中的冰山一角。\n", "\n", "## 训练\n", "\n", "在了解了上述统计工具后，让我们在实践中尝试一下！\n", "首先，我们生成一些数据：(**使用正弦函数和一些可加性噪声来生成序列数据，\n", "时间步为$1, 2, \\ldots, 1000$。**)\n"]}, {"cell_type": "code", "execution_count": 1, "id": "cec552f1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:29.737097Z", "iopub.status.busy": "2023-08-18T07:00:29.736329Z", "iopub.status.idle": "2023-08-18T07:00:32.767900Z", "shell.execute_reply": "2023-08-18T07:00:32.766800Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "code", "execution_count": 2, "id": "8703181c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:32.773582Z", "iopub.status.busy": "2023-08-18T07:00:32.772564Z", "iopub.status.idle": "2023-08-18T07:00:33.054920Z", "shell.execute_reply": "2023-08-18T07:00:33.053899Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"406.885938pt\" height=\"208.057659pt\" viewBox=\"0 0 406.**********.057659\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:00:33.001240</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 208.057659 \n", "L 406.**********.057659 \n", "L 406.885938 0 \n", "L 0 0 \n", "L 0 208.057659 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.501409 \n", "L 386.**********.501409 \n", "L 386.960938 7.421409 \n", "L 52.160938 7.421409 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 118.852829 170.501409 \n", "L 118.852829 7.421409 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m17d3d1b35e\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m17d3d1b35e\" x=\"118.852829\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(109.309079 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 185.879856 170.501409 \n", "L 185.879856 7.421409 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m17d3d1b35e\" x=\"185.879856\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(176.336106 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 252.906883 170.501409 \n", "L 252.906883 7.421409 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m17d3d1b35e\" x=\"252.906883\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(243.363133 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 319.93391 170.501409 \n", "L 319.93391 7.421409 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m17d3d1b35e\" x=\"319.93391\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(310.39016 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 386.**********.501409 \n", "L 386.960938 7.421409 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m17d3d1b35e\" x=\"386.960938\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(374.235937 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- time -->\n", "     <g transform=\"translate(208.264844 198.777972)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 52.**********.350493 \n", "L 386.**********.350493 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m80a189af2d\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m80a189af2d\" x=\"52.160938\" y=\"170.350493\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1.5 -->\n", "      <g transform=\"translate(20.878125 174.149712)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 52.160938 143.791947 \n", "L 386.960938 143.791947 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m80a189af2d\" x=\"52.160938\" y=\"143.791947\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 147.591166)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 52.160938 117.233402 \n", "L 386.960938 117.233402 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m80a189af2d\" x=\"52.160938\" y=\"117.233402\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 121.03262)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 52.160938 90.674856 \n", "L 386.960938 90.674856 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m80a189af2d\" x=\"52.160938\" y=\"90.674856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257812 94.474075)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 52.160938 64.11631 \n", "L 386.960938 64.11631 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m80a189af2d\" x=\"52.160938\" y=\"64.11631\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257812 67.915529)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 52.160938 37.557764 \n", "L 386.960938 37.557764 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m80a189af2d\" x=\"52.160938\" y=\"37.557764\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257812 41.356983)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 52.160938 10.999219 \n", "L 386.960938 10.999219 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m80a189af2d\" x=\"52.160938\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(29.257812 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- x -->\n", "     <g transform=\"translate(14.798438 91.920784)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 52.160938 84.54498 \n", "L 52.496073 66.292182 \n", "L 52.831208 84.811538 \n", "L 53.166343 85.913688 \n", "L 53.501478 99.10471 \n", "L 53.836613 98.383487 \n", "L 54.171748 81.436069 \n", "L 54.506883 72.074729 \n", "L 54.842019 103.48116 \n", "L 55.177154 65.125338 \n", "L 55.847424 83.059261 \n", "L 56.182559 53.359426 \n", "L 56.852829 100.104027 \n", "L 57.187965 100.759161 \n", "L 57.5231 80.859436 \n", "L 57.858235 74.808693 \n", "L 58.19337 77.262553 \n", "L 58.528505 83.659933 \n", "L 58.86364 69.296344 \n", "L 59.198775 67.966281 \n", "L 59.53391 86.256409 \n", "L 59.869046 59.839099 \n", "L 60.204181 58.998411 \n", "L 60.539316 65.198439 \n", "L 60.874451 64.879003 \n", "L 61.209586 71.720712 \n", "L 61.544721 98.535389 \n", "L 61.879856 79.315594 \n", "L 62.214992 89.378511 \n", "L 62.550127 81.74239 \n", "L 62.885262 80.90693 \n", "L 63.220397 77.151232 \n", "L 63.555532 85.20364 \n", "L 63.890667 103.590006 \n", "L 64.225802 83.896333 \n", "L 64.560938 72.609344 \n", "L 64.896073 81.028173 \n", "L 65.231208 70.004145 \n", "L 65.566343 76.238885 \n", "L 65.901478 68.938553 \n", "L 66.236613 64.290176 \n", "L 66.571748 68.481729 \n", "L 66.906883 67.475493 \n", "L 67.242019 89.425816 \n", "L 67.577154 81.024249 \n", "L 68.247424 58.698031 \n", "L 68.582559 55.445919 \n", "L 68.917694 66.109903 \n", "L 69.252829 54.629493 \n", "L 69.587965 74.563535 \n", "L 69.9231 60.821665 \n", "L 70.258235 55.828289 \n", "L 70.59337 58.586673 \n", "L 70.928505 52.777573 \n", "L 71.26364 98.160334 \n", "L 71.598775 46.326426 \n", "L 71.93391 78.336651 \n", "L 72.269046 45.615388 \n", "L 72.604181 62.458218 \n", "L 72.939316 39.487375 \n", "L 73.274451 41.38225 \n", "L 73.609586 62.194979 \n", "L 73.944721 64.427513 \n", "L 74.279856 54.49569 \n", "L 74.614992 54.392354 \n", "L 74.950127 57.777462 \n", "L 75.285262 54.386054 \n", "L 75.620397 56.643145 \n", "L 75.955532 67.783528 \n", "L 76.625802 55.971055 \n", "L 76.960938 14.834137 \n", "L 77.296073 48.76046 \n", "L 77.631208 71.155554 \n", "L 77.966343 39.456873 \n", "L 78.301478 39.34624 \n", "L 78.636613 48.47593 \n", "L 78.971748 60.788507 \n", "L 79.306883 53.261684 \n", "L 79.642019 63.45615 \n", "L 79.977154 39.988756 \n", "L 80.312289 27.673231 \n", "L 80.982559 48.208837 \n", "L 81.317694 42.292745 \n", "L 81.652829 61.904525 \n", "L 81.987965 40.472104 \n", "L 82.3231 49.66569 \n", "L 82.658235 52.429358 \n", "L 82.99337 50.912636 \n", "L 83.328505 52.493283 \n", "L 83.66364 43.657537 \n", "L 83.998775 44.336294 \n", "L 84.33391 49.57625 \n", "L 84.669046 34.110678 \n", "L 85.004181 56.486585 \n", "L 85.339316 45.579434 \n", "L 85.674451 51.652064 \n", "L 86.009586 30.714363 \n", "L 86.344721 41.407806 \n", "L 86.679856 36.584649 \n", "L 87.014992 55.312844 \n", "L 87.350127 58.702464 \n", "L 87.685262 35.968914 \n", "L 88.020397 43.857614 \n", "L 88.355532 62.821678 \n", "L 88.690667 66.993342 \n", "L 89.025802 62.461916 \n", "L 89.360938 37.760624 \n", "L 89.696073 35.707742 \n", "L 90.031208 45.950081 \n", "L 90.366343 47.301185 \n", "L 90.701478 37.908323 \n", "L 91.036613 32.293912 \n", "L 91.706883 48.448483 \n", "L 92.042019 47.022885 \n", "L 92.377154 31.093988 \n", "L 93.047424 65.55191 \n", "L 93.382559 42.302215 \n", "L 93.717694 53.448343 \n", "L 94.052829 68.807062 \n", "L 94.387965 48.180641 \n", "L 95.058235 33.589931 \n", "L 95.39337 45.27996 \n", "L 95.728505 42.160285 \n", "L 96.06364 47.417382 \n", "L 96.398775 56.077208 \n", "L 96.73391 40.657224 \n", "L 97.069046 18.049109 \n", "L 97.404181 43.602625 \n", "L 97.739316 58.45975 \n", "L 98.074451 43.442747 \n", "L 98.409586 55.452257 \n", "L 99.079856 46.28057 \n", "L 99.750127 45.987003 \n", "L 100.085262 47.122267 \n", "L 100.420397 38.404831 \n", "L 100.755532 43.397831 \n", "L 101.090667 44.618533 \n", "L 101.425802 35.511271 \n", "L 101.760938 41.99563 \n", "L 102.096073 37.845566 \n", "L 102.431208 45.158929 \n", "L 102.766343 22.130071 \n", "L 103.101478 39.837537 \n", "L 103.436613 36.497279 \n", "L 103.771748 38.494775 \n", "L 104.106883 26.027803 \n", "L 104.442019 44.34421 \n", "L 105.112289 33.091402 \n", "L 105.447424 24.437015 \n", "L 105.782559 56.886881 \n", "L 106.117694 38.053995 \n", "L 106.452829 30.723468 \n", "L 106.787965 39.930808 \n", "L 107.1231 24.601641 \n", "L 107.458235 28.128663 \n", "L 107.79337 34.541492 \n", "L 108.128505 33.015949 \n", "L 108.46364 37.567604 \n", "L 108.798775 35.114498 \n", "L 109.13391 48.4828 \n", "L 109.469046 38.126348 \n", "L 109.804181 53.879936 \n", "L 110.139316 32.273579 \n", "L 110.474451 57.036501 \n", "L 110.809586 40.773041 \n", "L 111.144721 38.72511 \n", "L 111.479856 30.786573 \n", "L 111.814992 26.671215 \n", "L 112.150127 51.505379 \n", "L 112.485262 50.290762 \n", "L 112.820397 23.754134 \n", "L 113.155532 48.554726 \n", "L 113.490667 39.448543 \n", "L 113.825802 38.972497 \n", "L 114.160938 47.945709 \n", "L 114.496073 29.878431 \n", "L 114.831208 41.213286 \n", "L 115.166343 43.724394 \n", "L 115.501478 21.598489 \n", "L 115.836613 35.318523 \n", "L 116.171748 43.404929 \n", "L 116.506883 25.732989 \n", "L 116.842019 17.022487 \n", "L 117.177154 33.884782 \n", "L 117.847424 32.208233 \n", "L 118.182559 27.515594 \n", "L 118.517694 35.865955 \n", "L 118.852829 32.401595 \n", "L 119.187965 33.34022 \n", "L 119.5231 29.688773 \n", "L 120.19337 59.032642 \n", "L 120.528505 36.259175 \n", "L 120.86364 34.849223 \n", "L 121.198775 46.45888 \n", "L 121.869046 20.24243 \n", "L 122.204181 54.531077 \n", "L 122.539316 42.129382 \n", "L 122.874451 25.504123 \n", "L 123.544721 54.851428 \n", "L 123.879856 47.974741 \n", "L 124.214992 46.127125 \n", "L 124.550127 48.499871 \n", "L 124.885262 46.645796 \n", "L 125.220397 40.449356 \n", "L 125.555532 45.795994 \n", "L 125.890667 36.663242 \n", "L 126.560938 60.546142 \n", "L 126.896073 46.543897 \n", "L 127.231208 63.218463 \n", "L 127.566343 37.169749 \n", "L 127.901478 41.355035 \n", "L 128.236613 57.502195 \n", "L 128.571748 41.818389 \n", "L 128.906883 61.396197 \n", "L 129.242019 45.829772 \n", "L 129.577154 53.28837 \n", "L 129.912289 56.326143 \n", "L 130.247424 68.245998 \n", "L 130.582559 42.339007 \n", "L 130.917694 52.110289 \n", "L 131.252829 48.027598 \n", "L 131.587965 62.824407 \n", "L 131.9231 50.029334 \n", "L 132.258235 66.397525 \n", "L 132.59337 64.924465 \n", "L 132.928505 75.628488 \n", "L 133.26364 32.170411 \n", "L 133.598775 59.250601 \n", "L 133.93391 69.364114 \n", "L 134.269046 67.536235 \n", "L 134.604181 72.552055 \n", "L 134.939316 57.637916 \n", "L 135.274451 63.102064 \n", "L 135.609586 63.681656 \n", "L 135.944721 63.737821 \n", "L 136.279856 71.969288 \n", "L 136.614992 59.402846 \n", "L 136.950127 71.931375 \n", "L 137.285262 63.189817 \n", "L 137.620397 40.128976 \n", "L 137.955532 67.161438 \n", "L 138.290667 59.840476 \n", "L 138.625802 62.356222 \n", "L 138.960938 72.257517 \n", "L 139.296073 65.887155 \n", "L 139.631208 50.920867 \n", "L 139.966343 52.025699 \n", "L 140.636613 58.285828 \n", "L 140.971748 69.019007 \n", "L 141.306883 67.073205 \n", "L 141.642019 56.187737 \n", "L 141.977154 54.274711 \n", "L 142.312289 50.948918 \n", "L 142.647424 76.034604 \n", "L 142.982559 58.561037 \n", "L 143.317694 80.419717 \n", "L 143.652829 51.130683 \n", "L 143.987965 57.147936 \n", "L 144.3231 83.284041 \n", "L 144.658235 73.199675 \n", "L 144.99337 79.227366 \n", "L 145.328505 59.683457 \n", "L 145.998775 78.914328 \n", "L 146.33391 62.272192 \n", "L 146.669046 62.786212 \n", "L 147.339316 67.693742 \n", "L 147.674451 61.868831 \n", "L 148.009586 63.724188 \n", "L 148.344721 62.815998 \n", "L 148.679856 66.97589 \n", "L 149.014992 77.427063 \n", "L 149.350127 74.540118 \n", "L 149.685262 66.71499 \n", "L 150.355532 91.899918 \n", "L 150.690667 84.57065 \n", "L 151.025802 89.230496 \n", "L 151.360938 74.574653 \n", "L 151.696073 103.44619 \n", "L 152.031208 79.944455 \n", "L 152.366343 88.574606 \n", "L 152.701478 76.030621 \n", "L 153.036613 92.332349 \n", "L 153.371748 82.288451 \n", "L 154.042019 88.579158 \n", "L 154.377154 115.859711 \n", "L 154.712289 83.007429 \n", "L 155.047424 77.758114 \n", "L 155.382559 62.213402 \n", "L 155.717694 89.966695 \n", "L 156.052829 98.687412 \n", "L 156.387965 76.073346 \n", "L 156.7231 98.614911 \n", "L 157.058235 84.218717 \n", "L 157.39337 77.346388 \n", "L 157.728505 76.233927 \n", "L 158.73391 100.880805 \n", "L 159.069046 94.014133 \n", "L 159.404181 99.545176 \n", "L 159.739316 98.434284 \n", "L 160.074451 89.38885 \n", "L 160.409586 73.217774 \n", "L 160.744721 93.973039 \n", "L 161.079856 102.128746 \n", "L 161.414992 101.767972 \n", "L 161.750127 88.115443 \n", "L 162.085262 110.822666 \n", "L 162.420397 91.882926 \n", "L 162.755532 108.550954 \n", "L 163.090667 118.493844 \n", "L 163.425802 118.593583 \n", "L 163.760938 107.257128 \n", "L 164.096073 110.920942 \n", "L 164.431208 99.254481 \n", "L 164.766343 95.16897 \n", "L 165.101478 105.807878 \n", "L 165.436613 108.122016 \n", "L 165.771748 111.781756 \n", "L 166.106883 113.494228 \n", "L 166.442019 102.126682 \n", "L 166.777154 99.613707 \n", "L 167.447424 110.190515 \n", "L 167.782559 83.86778 \n", "L 168.787965 121.00769 \n", "L 169.1231 115.176999 \n", "L 169.458235 102.356077 \n", "L 169.79337 127.873204 \n", "L 170.128505 110.000922 \n", "L 170.46364 116.987944 \n", "L 170.798775 111.333733 \n", "L 171.13391 119.059185 \n", "L 171.804181 103.358708 \n", "L 172.139316 121.87097 \n", "L 172.474451 131.489488 \n", "L 172.809586 111.716055 \n", "L 173.144721 128.107711 \n", "L 173.479856 130.792716 \n", "L 173.814992 120.034606 \n", "L 174.150127 98.322501 \n", "L 174.485262 107.080946 \n", "L 174.820397 146.124745 \n", "L 175.155532 99.137816 \n", "L 175.490667 128.712776 \n", "L 175.825802 134.998334 \n", "L 176.160938 114.260019 \n", "L 176.496073 104.568567 \n", "L 176.831208 133.608598 \n", "L 177.166343 135.818832 \n", "L 177.501478 121.669317 \n", "L 177.836613 119.3673 \n", "L 178.171748 106.027033 \n", "L 178.506883 110.81337 \n", "L 178.842019 117.522824 \n", "L 179.177154 127.301239 \n", "L 179.847424 120.006299 \n", "L 180.182559 135.072923 \n", "L 180.517694 118.60455 \n", "L 181.187965 147.460098 \n", "L 181.858235 129.856946 \n", "L 182.19337 131.289402 \n", "L 182.528505 135.89961 \n", "L 182.86364 117.01423 \n", "L 183.198775 120.215339 \n", "L 183.53391 127.848417 \n", "L 183.869046 151.662921 \n", "L 184.204181 127.199907 \n", "L 184.539316 128.242413 \n", "L 184.874451 121.736731 \n", "L 185.209586 163.088682 \n", "L 185.544721 135.269748 \n", "L 185.879856 132.511864 \n", "L 186.214992 119.013873 \n", "L 186.550127 136.87706 \n", "L 186.885262 135.22574 \n", "L 187.220397 132.584803 \n", "L 187.890667 136.21814 \n", "L 188.225802 140.711582 \n", "L 188.560938 148.430649 \n", "L 188.896073 120.308075 \n", "L 189.231208 127.357328 \n", "L 189.566343 145.992272 \n", "L 189.901478 120.722827 \n", "L 190.571748 142.161869 \n", "L 190.906883 129.423141 \n", "L 191.242019 130.38629 \n", "L 191.577154 153.791281 \n", "L 191.912289 138.172743 \n", "L 192.247424 153.385042 \n", "L 192.582559 137.629475 \n", "L 192.917694 152.421773 \n", "L 193.252829 155.887405 \n", "L 193.587965 134.414592 \n", "L 193.9231 147.05353 \n", "L 194.258235 127.727342 \n", "L 194.59337 137.282238 \n", "L 195.26364 129.974343 \n", "L 195.598775 136.190324 \n", "L 195.93391 120.239878 \n", "L 196.269046 143.37572 \n", "L 196.604181 113.899784 \n", "L 196.939316 148.312709 \n", "L 197.274451 140.686681 \n", "L 197.609586 148.542689 \n", "L 198.279856 131.408615 \n", "L 198.614992 129.228288 \n", "L 198.950127 153.213849 \n", "L 199.285262 123.951214 \n", "L 199.620397 132.082428 \n", "L 199.955532 123.51233 \n", "L 200.290667 136.8864 \n", "L 200.625802 122.461076 \n", "L 200.960938 138.790321 \n", "L 201.296073 138.98152 \n", "L 201.631208 120.75053 \n", "L 201.966343 146.110947 \n", "L 202.301478 134.906814 \n", "L 202.636613 134.366253 \n", "L 202.971748 147.911985 \n", "L 203.306883 144.294744 \n", "L 203.642019 152.524498 \n", "L 203.977154 132.817513 \n", "L 204.312289 156.474013 \n", "L 204.647424 143.204694 \n", "L 204.982559 149.870855 \n", "L 205.652829 153.701151 \n", "L 205.987965 150.154975 \n", "L 206.3231 125.501961 \n", "L 206.658235 131.02527 \n", "L 206.99337 140.960295 \n", "L 207.328505 138.13237 \n", "L 207.66364 145.002572 \n", "L 207.998775 129.310677 \n", "L 208.33391 124.604051 \n", "L 208.669046 147.347565 \n", "L 209.004181 126.25501 \n", "L 209.674451 151.431858 \n", "L 210.009586 123.039044 \n", "L 210.344721 150.939376 \n", "L 210.679856 143.325304 \n", "L 211.014992 132.17177 \n", "L 211.350127 148.969197 \n", "L 211.685262 149.320759 \n", "L 212.020397 141.738102 \n", "L 212.355532 150.587169 \n", "L 212.690667 146.005113 \n", "L 213.025802 138.521516 \n", "L 213.360938 140.399643 \n", "L 213.696073 129.195826 \n", "L 214.031208 145.249415 \n", "L 214.366343 142.239097 \n", "L 214.701478 127.185967 \n", "L 215.036613 154.924769 \n", "L 215.371748 129.598668 \n", "L 215.706883 131.649135 \n", "L 216.042019 138.36973 \n", "L 216.377154 141.951872 \n", "L 216.712289 152.811631 \n", "L 217.047424 152.959522 \n", "L 217.382559 129.085092 \n", "L 217.717694 140.805873 \n", "L 218.052829 146.707344 \n", "L 218.387965 144.099425 \n", "L 218.7231 152.548021 \n", "L 219.058235 133.970604 \n", "L 219.728505 159.501303 \n", "L 220.06364 139.683077 \n", "L 220.398775 151.462606 \n", "L 220.73391 151.664409 \n", "L 221.069046 133.682382 \n", "L 221.404181 140.461843 \n", "L 221.739316 128.899702 \n", "L 222.074451 130.475623 \n", "L 222.409586 133.215124 \n", "L 222.744721 146.958359 \n", "L 223.414992 136.786154 \n", "L 223.750127 139.625246 \n", "L 224.085262 134.907495 \n", "L 224.420397 134.545972 \n", "L 224.755532 150.005158 \n", "L 225.090667 115.079702 \n", "L 225.425802 136.75922 \n", "L 226.096073 117.987403 \n", "L 226.431208 137.910039 \n", "L 226.766343 135.242349 \n", "L 227.101478 135.837217 \n", "L 227.436613 118.287118 \n", "L 227.771748 144.755553 \n", "L 228.106883 141.8602 \n", "L 228.442019 130.180565 \n", "L 228.777154 138.524929 \n", "L 229.112289 140.048752 \n", "L 229.447424 135.12993 \n", "L 229.782559 139.822553 \n", "L 230.117694 139.434325 \n", "L 230.787965 123.444846 \n", "L 231.1231 131.427767 \n", "L 231.458235 110.097899 \n", "L 232.128505 137.08054 \n", "L 232.46364 126.249017 \n", "L 232.798775 141.510877 \n", "L 233.13391 137.45232 \n", "L 233.469046 114.554762 \n", "L 233.804181 145.135545 \n", "L 234.139316 134.088118 \n", "L 234.474451 137.838452 \n", "L 234.809586 96.471317 \n", "L 235.144721 147.221272 \n", "L 235.479856 134.19828 \n", "L 235.814992 147.135226 \n", "L 236.150127 113.259693 \n", "L 236.485262 134.816835 \n", "L 236.820397 116.322069 \n", "L 237.155532 114.770414 \n", "L 237.490667 114.630099 \n", "L 237.825802 131.96475 \n", "L 238.160938 130.031502 \n", "L 238.496073 112.495885 \n", "L 238.831208 124.354268 \n", "L 239.166343 119.680666 \n", "L 239.501478 124.2743 \n", "L 239.836613 123.76343 \n", "L 240.171748 112.985848 \n", "L 240.506883 120.805913 \n", "L 240.842019 134.084217 \n", "L 241.177154 115.087109 \n", "L 241.512289 141.467312 \n", "L 242.182559 116.489395 \n", "L 242.517694 112.828457 \n", "L 242.852829 152.177773 \n", "L 243.187965 130.510674 \n", "L 243.5231 143.278298 \n", "L 243.858235 103.709014 \n", "L 244.19337 116.45574 \n", "L 244.528505 121.305927 \n", "L 244.86364 107.213508 \n", "L 245.198775 125.940038 \n", "L 245.53391 102.937015 \n", "L 245.869046 90.60603 \n", "L 246.204181 112.902119 \n", "L 246.539316 98.995809 \n", "L 246.874451 123.494806 \n", "L 247.209586 114.318605 \n", "L 247.544721 124.681641 \n", "L 247.879856 106.728178 \n", "L 248.214992 124.086001 \n", "L 248.550127 114.069273 \n", "L 248.885262 117.753865 \n", "L 249.220397 123.412163 \n", "L 249.555532 106.866709 \n", "L 249.890667 105.825302 \n", "L 250.225802 105.935285 \n", "L 250.896073 119.196961 \n", "L 251.566343 94.493911 \n", "L 251.901478 94.749753 \n", "L 252.236613 96.761404 \n", "L 252.571748 97.836934 \n", "L 252.906883 90.026585 \n", "L 253.242019 101.853703 \n", "L 253.577154 108.440726 \n", "L 253.912289 95.860411 \n", "L 254.247424 99.802056 \n", "L 254.582559 105.959499 \n", "L 254.917694 102.042288 \n", "L 255.252829 106.699025 \n", "L 255.587965 115.402274 \n", "L 255.9231 98.637104 \n", "L 256.258235 111.49276 \n", "L 256.59337 112.597103 \n", "L 256.928505 114.440815 \n", "L 257.26364 101.856277 \n", "L 257.598775 78.586715 \n", "L 257.93391 108.447629 \n", "L 258.269046 100.257078 \n", "L 258.604181 73.004778 \n", "L 259.274451 100.409904 \n", "L 259.609586 100.704571 \n", "L 259.944721 94.561343 \n", "L 260.279856 96.361888 \n", "L 260.614992 104.163943 \n", "L 260.950127 82.582756 \n", "L 261.285262 84.910339 \n", "L 261.620397 92.937649 \n", "L 261.955532 78.600805 \n", "L 262.290667 108.856442 \n", "L 262.625802 70.706696 \n", "L 263.296073 100.137695 \n", "L 263.631208 98.349311 \n", "L 263.966343 101.688611 \n", "L 264.636613 73.478121 \n", "L 264.971748 93.030623 \n", "L 265.306883 87.804738 \n", "L 265.642019 76.961937 \n", "L 265.977154 87.915583 \n", "L 266.312289 87.194329 \n", "L 266.647424 98.705772 \n", "L 266.982559 79.789276 \n", "L 267.317694 97.41211 \n", "L 267.652829 80.073273 \n", "L 267.987965 89.77167 \n", "L 268.3231 77.953791 \n", "L 268.658235 95.129084 \n", "L 268.99337 63.973855 \n", "L 269.328505 77.878982 \n", "L 269.66364 75.280722 \n", "L 269.998775 98.424471 \n", "L 270.33391 77.318166 \n", "L 270.669046 76.119075 \n", "L 271.004181 73.714889 \n", "L 271.339316 79.33557 \n", "L 271.674451 87.517092 \n", "L 272.009586 67.913112 \n", "L 272.344721 79.826816 \n", "L 272.679856 71.586511 \n", "L 273.014992 73.29953 \n", "L 273.350127 73.599557 \n", "L 273.685262 75.684886 \n", "L 274.355532 88.35781 \n", "L 274.690667 71.129042 \n", "L 275.025802 69.077664 \n", "L 275.360937 70.295271 \n", "L 275.696073 69.721285 \n", "L 276.031208 58.216042 \n", "L 276.701478 68.842849 \n", "L 277.371748 70.842253 \n", "L 277.706883 68.00532 \n", "L 278.042019 76.036609 \n", "L 278.377154 66.113186 \n", "L 278.712289 69.094534 \n", "L 279.047424 71.033317 \n", "L 279.382559 47.360001 \n", "L 279.717694 80.559478 \n", "L 280.052829 56.78371 \n", "L 280.387965 56.335056 \n", "L 280.7231 54.642885 \n", "L 281.058235 71.099381 \n", "L 281.39337 47.294242 \n", "L 281.728505 65.825622 \n", "L 282.06364 60.604086 \n", "L 282.398775 51.714356 \n", "L 282.73391 70.250516 \n", "L 283.069046 49.560585 \n", "L 283.404181 78.321167 \n", "L 283.739316 55.129487 \n", "L 284.074451 66.531915 \n", "L 284.409586 44.555127 \n", "L 284.744721 69.711323 \n", "L 285.079856 38.533116 \n", "L 285.414992 60.491205 \n", "L 285.750127 54.495392 \n", "L 286.420397 70.427234 \n", "L 286.755532 54.823032 \n", "L 287.090667 57.277879 \n", "L 287.425802 62.248865 \n", "L 287.760938 52.395244 \n", "L 288.096073 48.910353 \n", "L 288.431208 83.917016 \n", "L 288.766343 50.17403 \n", "L 289.101478 60.322256 \n", "L 289.436613 48.247469 \n", "L 289.771748 60.027724 \n", "L 290.106883 53.737671 \n", "L 290.442019 49.999339 \n", "L 290.777154 60.160108 \n", "L 291.112289 51.158177 \n", "L 291.447424 59.930606 \n", "L 291.782559 59.469338 \n", "L 292.117694 64.98612 \n", "L 292.452829 26.650136 \n", "L 292.787965 47.924797 \n", "L 293.458235 46.311647 \n", "L 293.79337 49.94495 \n", "L 294.128505 49.372402 \n", "L 294.46364 32.554343 \n", "L 294.798775 35.632707 \n", "L 295.13391 51.261351 \n", "L 295.469046 49.567718 \n", "L 295.804181 49.512281 \n", "L 296.139316 58.009012 \n", "L 296.474451 43.344854 \n", "L 296.809586 42.644814 \n", "L 297.144721 61.054096 \n", "L 297.479856 36.180183 \n", "L 297.814992 61.900111 \n", "L 298.150127 31.626311 \n", "L 298.485262 66.808516 \n", "L 298.820397 45.825175 \n", "L 299.155532 33.770046 \n", "L 299.490667 33.26604 \n", "L 299.825802 38.356743 \n", "L 300.160938 49.610674 \n", "L 300.496073 44.605866 \n", "L 300.831208 56.573546 \n", "L 301.166343 43.94207 \n", "L 301.501478 37.184769 \n", "L 301.836613 33.47596 \n", "L 302.171748 35.821428 \n", "L 302.506883 49.941274 \n", "L 302.842019 37.220019 \n", "L 303.177154 37.262267 \n", "L 303.512289 30.17358 \n", "L 303.847424 44.169831 \n", "L 304.182559 34.122697 \n", "L 304.517694 34.20174 \n", "L 304.852829 44.261868 \n", "L 305.187965 34.211136 \n", "L 305.5231 45.127415 \n", "L 305.858235 37.99762 \n", "L 306.19337 16.111235 \n", "L 306.528505 47.345465 \n", "L 306.86364 34.656476 \n", "L 307.198775 39.697478 \n", "L 307.53391 26.181121 \n", "L 307.869046 39.560076 \n", "L 308.204181 34.552073 \n", "L 308.539316 22.216763 \n", "L 308.874451 25.561821 \n", "L 309.209586 48.959109 \n", "L 309.544721 50.98321 \n", "L 309.879856 26.451303 \n", "L 310.214992 43.726569 \n", "L 310.885262 28.089113 \n", "L 311.220397 25.574409 \n", "L 311.555532 38.142704 \n", "L 311.890667 44.763945 \n", "L 312.225802 32.455195 \n", "L 312.560938 34.526327 \n", "L 312.896073 39.553988 \n", "L 313.231208 62.248349 \n", "L 313.566343 30.036466 \n", "L 313.901478 37.407264 \n", "L 314.236613 40.906517 \n", "L 314.571748 38.999088 \n", "L 315.242019 51.798942 \n", "L 315.577154 48.931005 \n", "L 315.912289 42.526024 \n", "L 316.247424 53.564679 \n", "L 316.582559 52.725125 \n", "L 316.917694 42.300451 \n", "L 317.252829 38.816348 \n", "L 317.587965 28.223404 \n", "L 317.9231 50.932012 \n", "L 318.258235 24.784252 \n", "L 318.59337 55.014139 \n", "L 319.26364 25.045303 \n", "L 319.598775 49.809744 \n", "L 319.93391 35.336272 \n", "L 320.269046 30.886398 \n", "L 320.604181 60.08805 \n", "L 320.939316 34.21857 \n", "L 321.274451 58.871865 \n", "L 321.609586 41.220796 \n", "L 321.944721 38.172702 \n", "L 322.279856 51.174998 \n", "L 322.614992 54.562208 \n", "L 322.950127 47.563975 \n", "L 323.285262 45.523839 \n", "L 323.620397 55.554181 \n", "L 323.955532 43.6036 \n", "L 324.290667 38.863991 \n", "L 324.625802 49.973406 \n", "L 324.960938 34.144631 \n", "L 325.296073 57.794156 \n", "L 325.631208 47.22805 \n", "L 325.966343 43.992854 \n", "L 326.301478 53.849681 \n", "L 326.636613 41.126233 \n", "L 326.971748 44.787956 \n", "L 327.306883 22.839292 \n", "L 327.642019 47.170077 \n", "L 327.977154 36.975539 \n", "L 328.312289 44.681575 \n", "L 328.647424 40.202542 \n", "L 328.982559 20.921631 \n", "L 329.317694 48.507517 \n", "L 329.652829 42.868674 \n", "L 329.987965 28.547503 \n", "L 331.328505 46.561855 \n", "L 331.66364 35.551309 \n", "L 331.998775 40.734909 \n", "L 332.33391 24.803077 \n", "L 332.669046 47.851972 \n", "L 333.004181 54.589414 \n", "L 333.339316 38.210906 \n", "L 333.674451 51.780146 \n", "L 334.009586 51.087834 \n", "L 334.344721 55.439634 \n", "L 334.679856 40.666485 \n", "L 335.014992 43.76376 \n", "L 335.350127 40.075521 \n", "L 335.685262 59.465593 \n", "L 336.020397 42.63965 \n", "L 336.355532 73.083455 \n", "L 336.690667 44.536128 \n", "L 337.025802 44.807608 \n", "L 337.360937 56.877535 \n", "L 337.696073 49.852758 \n", "L 338.031208 29.803991 \n", "L 339.036613 60.498629 \n", "L 339.371748 26.932672 \n", "L 339.706883 24.257684 \n", "L 340.042019 57.502233 \n", "L 340.377154 71.939445 \n", "L 340.712289 51.314167 \n", "L 341.047424 66.751181 \n", "L 341.382559 32.857331 \n", "L 341.717694 85.417532 \n", "L 342.387965 37.864185 \n", "L 343.058235 51.609836 \n", "L 343.39337 53.106986 \n", "L 343.728505 66.964169 \n", "L 344.73391 39.312034 \n", "L 345.069046 63.002031 \n", "L 345.404181 51.273895 \n", "L 345.739316 55.628484 \n", "L 346.074451 48.876194 \n", "L 346.409586 56.094248 \n", "L 346.744721 56.957158 \n", "L 347.079856 74.9738 \n", "L 347.414992 68.481347 \n", "L 347.750127 50.710118 \n", "L 348.085262 87.194047 \n", "L 348.420397 69.994267 \n", "L 348.755532 66.03104 \n", "L 349.090667 55.979799 \n", "L 349.425802 74.752721 \n", "L 349.760938 63.374634 \n", "L 350.431208 75.178121 \n", "L 350.766343 58.206842 \n", "L 351.101478 62.501729 \n", "L 351.436613 56.426661 \n", "L 351.771748 77.814716 \n", "L 352.106883 63.250177 \n", "L 352.442019 59.13342 \n", "L 352.777154 62.885781 \n", "L 353.112289 78.607904 \n", "L 353.447424 46.659745 \n", "L 353.782559 60.052134 \n", "L 354.117694 78.305613 \n", "L 354.787965 64.582181 \n", "L 355.1231 46.448986 \n", "L 355.458235 86.503339 \n", "L 355.79337 69.844282 \n", "L 356.128505 75.658944 \n", "L 356.46364 67.30708 \n", "L 356.798775 73.016804 \n", "L 357.13391 40.025485 \n", "L 357.804181 91.190244 \n", "L 358.139316 67.851492 \n", "L 358.474451 59.503766 \n", "L 358.809586 59.208708 \n", "L 359.144721 70.976183 \n", "L 359.479856 73.236885 \n", "L 359.814992 79.181973 \n", "L 360.150127 73.811473 \n", "L 360.485262 64.900367 \n", "L 360.820397 92.21929 \n", "L 361.155532 65.478167 \n", "L 361.490667 75.61441 \n", "L 361.825802 59.909201 \n", "L 362.160938 83.884979 \n", "L 362.496073 92.477308 \n", "L 362.831208 98.051157 \n", "L 363.501478 68.57869 \n", "L 363.836613 88.051114 \n", "L 364.171748 80.546687 \n", "L 364.506883 96.087567 \n", "L 364.842019 89.667737 \n", "L 365.177154 73.275441 \n", "L 365.512289 117.961673 \n", "L 365.847424 83.555797 \n", "L 366.182559 87.239697 \n", "L 366.517694 98.581996 \n", "L 366.852829 98.89668 \n", "L 367.187965 80.599499 \n", "L 367.5231 110.871946 \n", "L 367.858235 86.909661 \n", "L 368.19337 105.505027 \n", "L 368.86364 76.945225 \n", "L 369.198775 78.906776 \n", "L 369.53391 89.995222 \n", "L 369.869046 90.480386 \n", "L 370.204181 70.812058 \n", "L 370.539316 109.27787 \n", "L 370.874451 91.043122 \n", "L 371.209586 103.756206 \n", "L 371.544721 99.753872 \n", "L 371.879856 100.60051 \n", "L 372.214992 108.655015 \n", "L 372.550127 102.559199 \n", "L 372.885262 88.998853 \n", "L 373.220397 108.805205 \n", "L 373.890667 93.584136 \n", "L 374.225802 108.050527 \n", "L 374.560938 92.47331 \n", "L 374.896073 127.846612 \n", "L 375.231208 111.937597 \n", "L 375.566343 110.652957 \n", "L 376.236613 93.181595 \n", "L 376.571748 116.28849 \n", "L 376.906883 92.778991 \n", "L 377.242019 96.396629 \n", "L 377.577154 89.801226 \n", "L 377.912289 115.761385 \n", "L 378.247424 105.704727 \n", "L 378.582559 109.848799 \n", "L 378.917694 94.644811 \n", "L 379.252829 95.146298 \n", "L 379.587965 110.927752 \n", "L 379.9231 115.719563 \n", "L 380.258235 103.479645 \n", "L 380.59337 117.940622 \n", "L 380.928505 117.331302 \n", "L 381.26364 106.117411 \n", "L 381.598775 107.3568 \n", "L 381.93391 110.196901 \n", "L 382.269046 126.978276 \n", "L 382.604181 112.201479 \n", "L 382.939316 108.366834 \n", "L 383.274451 99.895389 \n", "L 383.609586 114.677829 \n", "L 383.944721 112.091267 \n", "L 384.614992 103.95886 \n", "L 384.950127 110.465745 \n", "L 385.285262 101.820585 \n", "L 385.620397 125.377372 \n", "L 385.955532 109.982809 \n", "L 386.290667 111.553565 \n", "L 386.625802 117.887867 \n", "L 386.960938 144.781945 \n", "L 386.960938 144.781945 \n", "\" clip-path=\"url(#p3cfe078264)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.501409 \n", "L 52.160938 7.421409 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 386.**********.501409 \n", "L 386.960938 7.421409 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.501409 \n", "L 386.**********.501409 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 7.421409 \n", "L 386.960938 7.421409 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p3cfe078264\">\n", "   <rect x=\"52.160938\" y=\"7.421409\" width=\"334.8\" height=\"163.08\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 432x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["T = 1000  # 总共产生1000个点\n", "time = torch.arange(1, T + 1, dtype=torch.float32)\n", "x = torch.sin(0.01 * time) + torch.normal(0, 0.2, (T,))\n", "d2l.plot(time, [x], 'time', 'x', xlim=[1, 1000], figsize=(6, 3))"]}, {"cell_type": "markdown", "id": "91a4997c", "metadata": {"origin_pos": 7}, "source": ["接下来，我们将这个序列转换为模型的*特征－标签*（feature-label）对。\n", "基于嵌入维度$\\tau$，我们[**将数据映射为数据对$y_t = x_t$\n", "和$\\mathbf{x}_t = [x_{t-\\tau}, \\ldots, x_{t-1}]$。**]\n", "这比我们提供的数据样本少了$\\tau$个，\n", "因为我们没有足够的历史记录来描述前$\\tau$个数据样本。\n", "一个简单的解决办法是：如果拥有足够长的序列就丢弃这几项；\n", "另一个方法是用零填充序列。\n", "在这里，我们仅使用前600个“特征－标签”对进行训练。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "15071cc2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:33.062134Z", "iopub.status.busy": "2023-08-18T07:00:33.061290Z", "iopub.status.idle": "2023-08-18T07:00:33.068807Z", "shell.execute_reply": "2023-08-18T07:00:33.067785Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["tau = 4\n", "features = torch.zeros((T - tau, tau))\n", "for i in range(tau):\n", "    features[:, i] = x[i: T - tau + i]\n", "labels = x[tau:].reshape((-1, 1))"]}, {"cell_type": "code", "execution_count": 4, "id": "de5cf9b0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:33.073641Z", "iopub.status.busy": "2023-08-18T07:00:33.072850Z", "iopub.status.idle": "2023-08-18T07:00:33.078757Z", "shell.execute_reply": "2023-08-18T07:00:33.077578Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["batch_size, n_train = 16, 600\n", "# 只有前n_train个样本用于训练\n", "train_iter = d2l.load_array((features[:n_train], labels[:n_train]),\n", "                            batch_size, is_train=True)"]}, {"cell_type": "markdown", "id": "3f830b1f", "metadata": {"origin_pos": 11}, "source": ["在这里，我们[**使用一个相当简单的架构训练模型：\n", "一个拥有两个全连接层的多层感知机**]，ReLU激活函数和平方损失。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "bf897572", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:33.083521Z", "iopub.status.busy": "2023-08-18T07:00:33.082814Z", "iopub.status.idle": "2023-08-18T07:00:33.089936Z", "shell.execute_reply": "2023-08-18T07:00:33.088945Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["# 初始化网络权重的函数\n", "def init_weights(m):\n", "    if type(m) == nn.Linear:\n", "        nn.init.xavier_uniform_(m.weight)\n", "\n", "# 一个简单的多层感知机\n", "def get_net():\n", "    net = nn.Sequential(nn.<PERSON>(4, 10),\n", "                        nn.ReLU(),\n", "                        nn.<PERSON><PERSON>(10, 1))\n", "    net.apply(init_weights)\n", "    return net\n", "\n", "# 平方损失。注意：MSELoss计算平方误差时不带系数1/2\n", "loss = nn.MSELoss(reduction='none')"]}, {"cell_type": "markdown", "id": "62592eee", "metadata": {"origin_pos": 16}, "source": ["现在，准备[**训练模型**]了。实现下面的训练代码的方式与前面几节（如 :numref:`sec_linear_concise`）中的循环训练基本相同。因此，我们不会深入探讨太多细节。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "8842e1a5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:33.095006Z", "iopub.status.busy": "2023-08-18T07:00:33.094051Z", "iopub.status.idle": "2023-08-18T07:00:33.409280Z", "shell.execute_reply": "2023-08-18T07:00:33.408040Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 1, loss: 0.076846\n", "epoch 2, loss: 0.056340\n", "epoch 3, loss: 0.053779\n", "epoch 4, loss: 0.056320\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 5, loss: 0.051650\n"]}], "source": ["def train(net, train_iter, loss, epochs, lr):\n", "    trainer = torch.optim.Adam(net.parameters(), lr)\n", "    for epoch in range(epochs):\n", "        for X, y in train_iter:\n", "            trainer.zero_grad()\n", "            l = loss(net(X), y)\n", "            l.sum().backward()\n", "            trainer.step()\n", "        print(f'epoch {epoch + 1}, '\n", "              f'loss: {d2l.evaluate_loss(net, train_iter, loss):f}')\n", "\n", "net = get_net()\n", "train(net, train_iter, loss, 5, 0.01)"]}, {"cell_type": "markdown", "id": "e60966e7", "metadata": {"origin_pos": 21}, "source": ["## 预测\n", "\n", "由于训练损失很小，因此我们期望模型能有很好的工作效果。\n", "让我们看看这在实践中意味着什么。\n", "首先是检查[**模型预测下一个时间步**]的能力，\n", "也就是*单步预测*（one-step-ahead prediction）。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "c8fc264a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:33.413628Z", "iopub.status.busy": "2023-08-18T07:00:33.413334Z", "iopub.status.idle": "2023-08-18T07:00:33.703430Z", "shell.execute_reply": "2023-08-18T07:00:33.702292Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"406.885938pt\" height=\"208.057659pt\" viewBox=\"0 0 406.**********.057659\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:00:33.652151</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 208.057659 \n", "L 406.**********.057659 \n", "L 406.885938 0 \n", "L 0 0 \n", "L 0 208.057659 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.501409 \n", "L 386.**********.501409 \n", "L 386.960938 7.421409 \n", "L 52.160938 7.421409 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 118.852829 170.501409 \n", "L 118.852829 7.421409 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m6b44dd81a8\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m6b44dd81a8\" x=\"118.852829\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(109.309079 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 185.879856 170.501409 \n", "L 185.879856 7.421409 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m6b44dd81a8\" x=\"185.879856\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(176.336106 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 252.906883 170.501409 \n", "L 252.906883 7.421409 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m6b44dd81a8\" x=\"252.906883\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(243.363133 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 319.93391 170.501409 \n", "L 319.93391 7.421409 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m6b44dd81a8\" x=\"319.93391\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(310.39016 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 386.**********.501409 \n", "L 386.960938 7.421409 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m6b44dd81a8\" x=\"386.960938\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(374.235937 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- time -->\n", "     <g transform=\"translate(208.264844 198.777972)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 52.**********.350493 \n", "L 386.**********.350493 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mb06d0a7e16\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb06d0a7e16\" x=\"52.160938\" y=\"170.350493\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1.5 -->\n", "      <g transform=\"translate(20.878125 174.149712)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 52.160938 143.791947 \n", "L 386.960938 143.791947 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mb06d0a7e16\" x=\"52.160938\" y=\"143.791947\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 147.591166)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 52.160938 117.233402 \n", "L 386.960938 117.233402 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mb06d0a7e16\" x=\"52.160938\" y=\"117.233402\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 121.03262)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 52.160938 90.674856 \n", "L 386.960938 90.674856 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mb06d0a7e16\" x=\"52.160938\" y=\"90.674856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257812 94.474075)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 52.160938 64.11631 \n", "L 386.960938 64.11631 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mb06d0a7e16\" x=\"52.160938\" y=\"64.11631\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257812 67.915529)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 52.160938 37.557764 \n", "L 386.960938 37.557764 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#mb06d0a7e16\" x=\"52.160938\" y=\"37.557764\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257812 41.356983)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 52.160938 10.999219 \n", "L 386.960938 10.999219 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#mb06d0a7e16\" x=\"52.160938\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(29.257812 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- x -->\n", "     <g transform=\"translate(14.798438 91.920784)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 52.160938 84.54498 \n", "L 52.496073 66.292182 \n", "L 52.831208 84.811538 \n", "L 53.166343 85.913688 \n", "L 53.501478 99.10471 \n", "L 53.836613 98.383487 \n", "L 54.171748 81.436069 \n", "L 54.506883 72.074729 \n", "L 54.842019 103.48116 \n", "L 55.177154 65.125338 \n", "L 55.847424 83.059261 \n", "L 56.182559 53.359426 \n", "L 56.852829 100.104027 \n", "L 57.187965 100.759161 \n", "L 57.5231 80.859436 \n", "L 57.858235 74.808693 \n", "L 58.19337 77.262553 \n", "L 58.528505 83.659933 \n", "L 58.86364 69.296344 \n", "L 59.198775 67.966281 \n", "L 59.53391 86.256409 \n", "L 59.869046 59.839099 \n", "L 60.204181 58.998411 \n", "L 60.539316 65.198439 \n", "L 60.874451 64.879003 \n", "L 61.209586 71.720712 \n", "L 61.544721 98.535389 \n", "L 61.879856 79.315594 \n", "L 62.214992 89.378511 \n", "L 62.550127 81.74239 \n", "L 62.885262 80.90693 \n", "L 63.220397 77.151232 \n", "L 63.555532 85.20364 \n", "L 63.890667 103.590006 \n", "L 64.225802 83.896333 \n", "L 64.560938 72.609344 \n", "L 64.896073 81.028173 \n", "L 65.231208 70.004145 \n", "L 65.566343 76.238885 \n", "L 65.901478 68.938553 \n", "L 66.236613 64.290176 \n", "L 66.571748 68.481729 \n", "L 66.906883 67.475493 \n", "L 67.242019 89.425816 \n", "L 67.577154 81.024249 \n", "L 68.247424 58.698031 \n", "L 68.582559 55.445919 \n", "L 68.917694 66.109903 \n", "L 69.252829 54.629493 \n", "L 69.587965 74.563535 \n", "L 69.9231 60.821665 \n", "L 70.258235 55.828289 \n", "L 70.59337 58.586673 \n", "L 70.928505 52.777573 \n", "L 71.26364 98.160334 \n", "L 71.598775 46.326426 \n", "L 71.93391 78.336651 \n", "L 72.269046 45.615388 \n", "L 72.604181 62.458218 \n", "L 72.939316 39.487375 \n", "L 73.274451 41.38225 \n", "L 73.609586 62.194979 \n", "L 73.944721 64.427513 \n", "L 74.279856 54.49569 \n", "L 74.614992 54.392354 \n", "L 74.950127 57.777462 \n", "L 75.285262 54.386054 \n", "L 75.620397 56.643145 \n", "L 75.955532 67.783528 \n", "L 76.625802 55.971055 \n", "L 76.960938 14.834137 \n", "L 77.296073 48.76046 \n", "L 77.631208 71.155554 \n", "L 77.966343 39.456873 \n", "L 78.301478 39.34624 \n", "L 78.636613 48.47593 \n", "L 78.971748 60.788507 \n", "L 79.306883 53.261684 \n", "L 79.642019 63.45615 \n", "L 79.977154 39.988756 \n", "L 80.312289 27.673231 \n", "L 80.982559 48.208837 \n", "L 81.317694 42.292745 \n", "L 81.652829 61.904525 \n", "L 81.987965 40.472104 \n", "L 82.3231 49.66569 \n", "L 82.658235 52.429358 \n", "L 82.99337 50.912636 \n", "L 83.328505 52.493283 \n", "L 83.66364 43.657537 \n", "L 83.998775 44.336294 \n", "L 84.33391 49.57625 \n", "L 84.669046 34.110678 \n", "L 85.004181 56.486585 \n", "L 85.339316 45.579434 \n", "L 85.674451 51.652064 \n", "L 86.009586 30.714363 \n", "L 86.344721 41.407806 \n", "L 86.679856 36.584649 \n", "L 87.014992 55.312844 \n", "L 87.350127 58.702464 \n", "L 87.685262 35.968914 \n", "L 88.020397 43.857614 \n", "L 88.355532 62.821678 \n", "L 88.690667 66.993342 \n", "L 89.025802 62.461916 \n", "L 89.360938 37.760624 \n", "L 89.696073 35.707742 \n", "L 90.031208 45.950081 \n", "L 90.366343 47.301185 \n", "L 90.701478 37.908323 \n", "L 91.036613 32.293912 \n", "L 91.706883 48.448483 \n", "L 92.042019 47.022885 \n", "L 92.377154 31.093988 \n", "L 93.047424 65.55191 \n", "L 93.382559 42.302215 \n", "L 93.717694 53.448343 \n", "L 94.052829 68.807062 \n", "L 94.387965 48.180641 \n", "L 95.058235 33.589931 \n", "L 95.39337 45.27996 \n", "L 95.728505 42.160285 \n", "L 96.06364 47.417382 \n", "L 96.398775 56.077208 \n", "L 96.73391 40.657224 \n", "L 97.069046 18.049109 \n", "L 97.404181 43.602625 \n", "L 97.739316 58.45975 \n", "L 98.074451 43.442747 \n", "L 98.409586 55.452257 \n", "L 99.079856 46.28057 \n", "L 99.750127 45.987003 \n", "L 100.085262 47.122267 \n", "L 100.420397 38.404831 \n", "L 100.755532 43.397831 \n", "L 101.090667 44.618533 \n", "L 101.425802 35.511271 \n", "L 101.760938 41.99563 \n", "L 102.096073 37.845566 \n", "L 102.431208 45.158929 \n", "L 102.766343 22.130071 \n", "L 103.101478 39.837537 \n", "L 103.436613 36.497279 \n", "L 103.771748 38.494775 \n", "L 104.106883 26.027803 \n", "L 104.442019 44.34421 \n", "L 105.112289 33.091402 \n", "L 105.447424 24.437015 \n", "L 105.782559 56.886881 \n", "L 106.117694 38.053995 \n", "L 106.452829 30.723468 \n", "L 106.787965 39.930808 \n", "L 107.1231 24.601641 \n", "L 107.458235 28.128663 \n", "L 107.79337 34.541492 \n", "L 108.128505 33.015949 \n", "L 108.46364 37.567604 \n", "L 108.798775 35.114498 \n", "L 109.13391 48.4828 \n", "L 109.469046 38.126348 \n", "L 109.804181 53.879936 \n", "L 110.139316 32.273579 \n", "L 110.474451 57.036501 \n", "L 110.809586 40.773041 \n", "L 111.144721 38.72511 \n", "L 111.479856 30.786573 \n", "L 111.814992 26.671215 \n", "L 112.150127 51.505379 \n", "L 112.485262 50.290762 \n", "L 112.820397 23.754134 \n", "L 113.155532 48.554726 \n", "L 113.490667 39.448543 \n", "L 113.825802 38.972497 \n", "L 114.160938 47.945709 \n", "L 114.496073 29.878431 \n", "L 114.831208 41.213286 \n", "L 115.166343 43.724394 \n", "L 115.501478 21.598489 \n", "L 115.836613 35.318523 \n", "L 116.171748 43.404929 \n", "L 116.506883 25.732989 \n", "L 116.842019 17.022487 \n", "L 117.177154 33.884782 \n", "L 117.847424 32.208233 \n", "L 118.182559 27.515594 \n", "L 118.517694 35.865955 \n", "L 118.852829 32.401595 \n", "L 119.187965 33.34022 \n", "L 119.5231 29.688773 \n", "L 120.19337 59.032642 \n", "L 120.528505 36.259175 \n", "L 120.86364 34.849223 \n", "L 121.198775 46.45888 \n", "L 121.869046 20.24243 \n", "L 122.204181 54.531077 \n", "L 122.539316 42.129382 \n", "L 122.874451 25.504123 \n", "L 123.544721 54.851428 \n", "L 123.879856 47.974741 \n", "L 124.214992 46.127125 \n", "L 124.550127 48.499871 \n", "L 124.885262 46.645796 \n", "L 125.220397 40.449356 \n", "L 125.555532 45.795994 \n", "L 125.890667 36.663242 \n", "L 126.560938 60.546142 \n", "L 126.896073 46.543897 \n", "L 127.231208 63.218463 \n", "L 127.566343 37.169749 \n", "L 127.901478 41.355035 \n", "L 128.236613 57.502195 \n", "L 128.571748 41.818389 \n", "L 128.906883 61.396197 \n", "L 129.242019 45.829772 \n", "L 129.577154 53.28837 \n", "L 129.912289 56.326143 \n", "L 130.247424 68.245998 \n", "L 130.582559 42.339007 \n", "L 130.917694 52.110289 \n", "L 131.252829 48.027598 \n", "L 131.587965 62.824407 \n", "L 131.9231 50.029334 \n", "L 132.258235 66.397525 \n", "L 132.59337 64.924465 \n", "L 132.928505 75.628488 \n", "L 133.26364 32.170411 \n", "L 133.598775 59.250601 \n", "L 133.93391 69.364114 \n", "L 134.269046 67.536235 \n", "L 134.604181 72.552055 \n", "L 134.939316 57.637916 \n", "L 135.274451 63.102064 \n", "L 135.609586 63.681656 \n", "L 135.944721 63.737821 \n", "L 136.279856 71.969288 \n", "L 136.614992 59.402846 \n", "L 136.950127 71.931375 \n", "L 137.285262 63.189817 \n", "L 137.620397 40.128976 \n", "L 137.955532 67.161438 \n", "L 138.290667 59.840476 \n", "L 138.625802 62.356222 \n", "L 138.960938 72.257517 \n", "L 139.296073 65.887155 \n", "L 139.631208 50.920867 \n", "L 139.966343 52.025699 \n", "L 140.636613 58.285828 \n", "L 140.971748 69.019007 \n", "L 141.306883 67.073205 \n", "L 141.642019 56.187737 \n", "L 141.977154 54.274711 \n", "L 142.312289 50.948918 \n", "L 142.647424 76.034604 \n", "L 142.982559 58.561037 \n", "L 143.317694 80.419717 \n", "L 143.652829 51.130683 \n", "L 143.987965 57.147936 \n", "L 144.3231 83.284041 \n", "L 144.658235 73.199675 \n", "L 144.99337 79.227366 \n", "L 145.328505 59.683457 \n", "L 145.998775 78.914328 \n", "L 146.33391 62.272192 \n", "L 146.669046 62.786212 \n", "L 147.339316 67.693742 \n", "L 147.674451 61.868831 \n", "L 148.009586 63.724188 \n", "L 148.344721 62.815998 \n", "L 148.679856 66.97589 \n", "L 149.014992 77.427063 \n", "L 149.350127 74.540118 \n", "L 149.685262 66.71499 \n", "L 150.355532 91.899918 \n", "L 150.690667 84.57065 \n", "L 151.025802 89.230496 \n", "L 151.360938 74.574653 \n", "L 151.696073 103.44619 \n", "L 152.031208 79.944455 \n", "L 152.366343 88.574606 \n", "L 152.701478 76.030621 \n", "L 153.036613 92.332349 \n", "L 153.371748 82.288451 \n", "L 154.042019 88.579158 \n", "L 154.377154 115.859711 \n", "L 154.712289 83.007429 \n", "L 155.047424 77.758114 \n", "L 155.382559 62.213402 \n", "L 155.717694 89.966695 \n", "L 156.052829 98.687412 \n", "L 156.387965 76.073346 \n", "L 156.7231 98.614911 \n", "L 157.058235 84.218717 \n", "L 157.39337 77.346388 \n", "L 157.728505 76.233927 \n", "L 158.73391 100.880805 \n", "L 159.069046 94.014133 \n", "L 159.404181 99.545176 \n", "L 159.739316 98.434284 \n", "L 160.074451 89.38885 \n", "L 160.409586 73.217774 \n", "L 160.744721 93.973039 \n", "L 161.079856 102.128746 \n", "L 161.414992 101.767972 \n", "L 161.750127 88.115443 \n", "L 162.085262 110.822666 \n", "L 162.420397 91.882926 \n", "L 162.755532 108.550954 \n", "L 163.090667 118.493844 \n", "L 163.425802 118.593583 \n", "L 163.760938 107.257128 \n", "L 164.096073 110.920942 \n", "L 164.431208 99.254481 \n", "L 164.766343 95.16897 \n", "L 165.101478 105.807878 \n", "L 165.436613 108.122016 \n", "L 165.771748 111.781756 \n", "L 166.106883 113.494228 \n", "L 166.442019 102.126682 \n", "L 166.777154 99.613707 \n", "L 167.447424 110.190515 \n", "L 167.782559 83.86778 \n", "L 168.787965 121.00769 \n", "L 169.1231 115.176999 \n", "L 169.458235 102.356077 \n", "L 169.79337 127.873204 \n", "L 170.128505 110.000922 \n", "L 170.46364 116.987944 \n", "L 170.798775 111.333733 \n", "L 171.13391 119.059185 \n", "L 171.804181 103.358708 \n", "L 172.139316 121.87097 \n", "L 172.474451 131.489488 \n", "L 172.809586 111.716055 \n", "L 173.144721 128.107711 \n", "L 173.479856 130.792716 \n", "L 173.814992 120.034606 \n", "L 174.150127 98.322501 \n", "L 174.485262 107.080946 \n", "L 174.820397 146.124745 \n", "L 175.155532 99.137816 \n", "L 175.490667 128.712776 \n", "L 175.825802 134.998334 \n", "L 176.160938 114.260019 \n", "L 176.496073 104.568567 \n", "L 176.831208 133.608598 \n", "L 177.166343 135.818832 \n", "L 177.501478 121.669317 \n", "L 177.836613 119.3673 \n", "L 178.171748 106.027033 \n", "L 178.506883 110.81337 \n", "L 178.842019 117.522824 \n", "L 179.177154 127.301239 \n", "L 179.847424 120.006299 \n", "L 180.182559 135.072923 \n", "L 180.517694 118.60455 \n", "L 181.187965 147.460098 \n", "L 181.858235 129.856946 \n", "L 182.19337 131.289402 \n", "L 182.528505 135.89961 \n", "L 182.86364 117.01423 \n", "L 183.198775 120.215339 \n", "L 183.53391 127.848417 \n", "L 183.869046 151.662921 \n", "L 184.204181 127.199907 \n", "L 184.539316 128.242413 \n", "L 184.874451 121.736731 \n", "L 185.209586 163.088682 \n", "L 185.544721 135.269748 \n", "L 185.879856 132.511864 \n", "L 186.214992 119.013873 \n", "L 186.550127 136.87706 \n", "L 186.885262 135.22574 \n", "L 187.220397 132.584803 \n", "L 187.890667 136.21814 \n", "L 188.225802 140.711582 \n", "L 188.560938 148.430649 \n", "L 188.896073 120.308075 \n", "L 189.231208 127.357328 \n", "L 189.566343 145.992272 \n", "L 189.901478 120.722827 \n", "L 190.571748 142.161869 \n", "L 190.906883 129.423141 \n", "L 191.242019 130.38629 \n", "L 191.577154 153.791281 \n", "L 191.912289 138.172743 \n", "L 192.247424 153.385042 \n", "L 192.582559 137.629475 \n", "L 192.917694 152.421773 \n", "L 193.252829 155.887405 \n", "L 193.587965 134.414592 \n", "L 193.9231 147.05353 \n", "L 194.258235 127.727342 \n", "L 194.59337 137.282238 \n", "L 195.26364 129.974343 \n", "L 195.598775 136.190324 \n", "L 195.93391 120.239878 \n", "L 196.269046 143.37572 \n", "L 196.604181 113.899784 \n", "L 196.939316 148.312709 \n", "L 197.274451 140.686681 \n", "L 197.609586 148.542689 \n", "L 198.279856 131.408615 \n", "L 198.614992 129.228288 \n", "L 198.950127 153.213849 \n", "L 199.285262 123.951214 \n", "L 199.620397 132.082428 \n", "L 199.955532 123.51233 \n", "L 200.290667 136.8864 \n", "L 200.625802 122.461076 \n", "L 200.960938 138.790321 \n", "L 201.296073 138.98152 \n", "L 201.631208 120.75053 \n", "L 201.966343 146.110947 \n", "L 202.301478 134.906814 \n", "L 202.636613 134.366253 \n", "L 202.971748 147.911985 \n", "L 203.306883 144.294744 \n", "L 203.642019 152.524498 \n", "L 203.977154 132.817513 \n", "L 204.312289 156.474013 \n", "L 204.647424 143.204694 \n", "L 204.982559 149.870855 \n", "L 205.652829 153.701151 \n", "L 205.987965 150.154975 \n", "L 206.3231 125.501961 \n", "L 206.658235 131.02527 \n", "L 206.99337 140.960295 \n", "L 207.328505 138.13237 \n", "L 207.66364 145.002572 \n", "L 207.998775 129.310677 \n", "L 208.33391 124.604051 \n", "L 208.669046 147.347565 \n", "L 209.004181 126.25501 \n", "L 209.674451 151.431858 \n", "L 210.009586 123.039044 \n", "L 210.344721 150.939376 \n", "L 210.679856 143.325304 \n", "L 211.014992 132.17177 \n", "L 211.350127 148.969197 \n", "L 211.685262 149.320759 \n", "L 212.020397 141.738102 \n", "L 212.355532 150.587169 \n", "L 212.690667 146.005113 \n", "L 213.025802 138.521516 \n", "L 213.360938 140.399643 \n", "L 213.696073 129.195826 \n", "L 214.031208 145.249415 \n", "L 214.366343 142.239097 \n", "L 214.701478 127.185967 \n", "L 215.036613 154.924769 \n", "L 215.371748 129.598668 \n", "L 215.706883 131.649135 \n", "L 216.042019 138.36973 \n", "L 216.377154 141.951872 \n", "L 216.712289 152.811631 \n", "L 217.047424 152.959522 \n", "L 217.382559 129.085092 \n", "L 217.717694 140.805873 \n", "L 218.052829 146.707344 \n", "L 218.387965 144.099425 \n", "L 218.7231 152.548021 \n", "L 219.058235 133.970604 \n", "L 219.728505 159.501303 \n", "L 220.06364 139.683077 \n", "L 220.398775 151.462606 \n", "L 220.73391 151.664409 \n", "L 221.069046 133.682382 \n", "L 221.404181 140.461843 \n", "L 221.739316 128.899702 \n", "L 222.074451 130.475623 \n", "L 222.409586 133.215124 \n", "L 222.744721 146.958359 \n", "L 223.414992 136.786154 \n", "L 223.750127 139.625246 \n", "L 224.085262 134.907495 \n", "L 224.420397 134.545972 \n", "L 224.755532 150.005158 \n", "L 225.090667 115.079702 \n", "L 225.425802 136.75922 \n", "L 226.096073 117.987403 \n", "L 226.431208 137.910039 \n", "L 226.766343 135.242349 \n", "L 227.101478 135.837217 \n", "L 227.436613 118.287118 \n", "L 227.771748 144.755553 \n", "L 228.106883 141.8602 \n", "L 228.442019 130.180565 \n", "L 228.777154 138.524929 \n", "L 229.112289 140.048752 \n", "L 229.447424 135.12993 \n", "L 229.782559 139.822553 \n", "L 230.117694 139.434325 \n", "L 230.787965 123.444846 \n", "L 231.1231 131.427767 \n", "L 231.458235 110.097899 \n", "L 232.128505 137.08054 \n", "L 232.46364 126.249017 \n", "L 232.798775 141.510877 \n", "L 233.13391 137.45232 \n", "L 233.469046 114.554762 \n", "L 233.804181 145.135545 \n", "L 234.139316 134.088118 \n", "L 234.474451 137.838452 \n", "L 234.809586 96.471317 \n", "L 235.144721 147.221272 \n", "L 235.479856 134.19828 \n", "L 235.814992 147.135226 \n", "L 236.150127 113.259693 \n", "L 236.485262 134.816835 \n", "L 236.820397 116.322069 \n", "L 237.155532 114.770414 \n", "L 237.490667 114.630099 \n", "L 237.825802 131.96475 \n", "L 238.160938 130.031502 \n", "L 238.496073 112.495885 \n", "L 238.831208 124.354268 \n", "L 239.166343 119.680666 \n", "L 239.501478 124.2743 \n", "L 239.836613 123.76343 \n", "L 240.171748 112.985848 \n", "L 240.506883 120.805913 \n", "L 240.842019 134.084217 \n", "L 241.177154 115.087109 \n", "L 241.512289 141.467312 \n", "L 242.182559 116.489395 \n", "L 242.517694 112.828457 \n", "L 242.852829 152.177773 \n", "L 243.187965 130.510674 \n", "L 243.5231 143.278298 \n", "L 243.858235 103.709014 \n", "L 244.19337 116.45574 \n", "L 244.528505 121.305927 \n", "L 244.86364 107.213508 \n", "L 245.198775 125.940038 \n", "L 245.53391 102.937015 \n", "L 245.869046 90.60603 \n", "L 246.204181 112.902119 \n", "L 246.539316 98.995809 \n", "L 246.874451 123.494806 \n", "L 247.209586 114.318605 \n", "L 247.544721 124.681641 \n", "L 247.879856 106.728178 \n", "L 248.214992 124.086001 \n", "L 248.550127 114.069273 \n", "L 248.885262 117.753865 \n", "L 249.220397 123.412163 \n", "L 249.555532 106.866709 \n", "L 249.890667 105.825302 \n", "L 250.225802 105.935285 \n", "L 250.896073 119.196961 \n", "L 251.566343 94.493911 \n", "L 251.901478 94.749753 \n", "L 252.236613 96.761404 \n", "L 252.571748 97.836934 \n", "L 252.906883 90.026585 \n", "L 253.242019 101.853703 \n", "L 253.577154 108.440726 \n", "L 253.912289 95.860411 \n", "L 254.247424 99.802056 \n", "L 254.582559 105.959499 \n", "L 254.917694 102.042288 \n", "L 255.252829 106.699025 \n", "L 255.587965 115.402274 \n", "L 255.9231 98.637104 \n", "L 256.258235 111.49276 \n", "L 256.59337 112.597103 \n", "L 256.928505 114.440815 \n", "L 257.26364 101.856277 \n", "L 257.598775 78.586715 \n", "L 257.93391 108.447629 \n", "L 258.269046 100.257078 \n", "L 258.604181 73.004778 \n", "L 259.274451 100.409904 \n", "L 259.609586 100.704571 \n", "L 259.944721 94.561343 \n", "L 260.279856 96.361888 \n", "L 260.614992 104.163943 \n", "L 260.950127 82.582756 \n", "L 261.285262 84.910339 \n", "L 261.620397 92.937649 \n", "L 261.955532 78.600805 \n", "L 262.290667 108.856442 \n", "L 262.625802 70.706696 \n", "L 263.296073 100.137695 \n", "L 263.631208 98.349311 \n", "L 263.966343 101.688611 \n", "L 264.636613 73.478121 \n", "L 264.971748 93.030623 \n", "L 265.306883 87.804738 \n", "L 265.642019 76.961937 \n", "L 265.977154 87.915583 \n", "L 266.312289 87.194329 \n", "L 266.647424 98.705772 \n", "L 266.982559 79.789276 \n", "L 267.317694 97.41211 \n", "L 267.652829 80.073273 \n", "L 267.987965 89.77167 \n", "L 268.3231 77.953791 \n", "L 268.658235 95.129084 \n", "L 268.99337 63.973855 \n", "L 269.328505 77.878982 \n", "L 269.66364 75.280722 \n", "L 269.998775 98.424471 \n", "L 270.33391 77.318166 \n", "L 270.669046 76.119075 \n", "L 271.004181 73.714889 \n", "L 271.339316 79.33557 \n", "L 271.674451 87.517092 \n", "L 272.009586 67.913112 \n", "L 272.344721 79.826816 \n", "L 272.679856 71.586511 \n", "L 273.014992 73.29953 \n", "L 273.350127 73.599557 \n", "L 273.685262 75.684886 \n", "L 274.355532 88.35781 \n", "L 274.690667 71.129042 \n", "L 275.025802 69.077664 \n", "L 275.360937 70.295271 \n", "L 275.696073 69.721285 \n", "L 276.031208 58.216042 \n", "L 276.701478 68.842849 \n", "L 277.371748 70.842253 \n", "L 277.706883 68.00532 \n", "L 278.042019 76.036609 \n", "L 278.377154 66.113186 \n", "L 278.712289 69.094534 \n", "L 279.047424 71.033317 \n", "L 279.382559 47.360001 \n", "L 279.717694 80.559478 \n", "L 280.052829 56.78371 \n", "L 280.387965 56.335056 \n", "L 280.7231 54.642885 \n", "L 281.058235 71.099381 \n", "L 281.39337 47.294242 \n", "L 281.728505 65.825622 \n", "L 282.06364 60.604086 \n", "L 282.398775 51.714356 \n", "L 282.73391 70.250516 \n", "L 283.069046 49.560585 \n", "L 283.404181 78.321167 \n", "L 283.739316 55.129487 \n", "L 284.074451 66.531915 \n", "L 284.409586 44.555127 \n", "L 284.744721 69.711323 \n", "L 285.079856 38.533116 \n", "L 285.414992 60.491205 \n", "L 285.750127 54.495392 \n", "L 286.420397 70.427234 \n", "L 286.755532 54.823032 \n", "L 287.090667 57.277879 \n", "L 287.425802 62.248865 \n", "L 287.760938 52.395244 \n", "L 288.096073 48.910353 \n", "L 288.431208 83.917016 \n", "L 288.766343 50.17403 \n", "L 289.101478 60.322256 \n", "L 289.436613 48.247469 \n", "L 289.771748 60.027724 \n", "L 290.106883 53.737671 \n", "L 290.442019 49.999339 \n", "L 290.777154 60.160108 \n", "L 291.112289 51.158177 \n", "L 291.447424 59.930606 \n", "L 291.782559 59.469338 \n", "L 292.117694 64.98612 \n", "L 292.452829 26.650136 \n", "L 292.787965 47.924797 \n", "L 293.458235 46.311647 \n", "L 293.79337 49.94495 \n", "L 294.128505 49.372402 \n", "L 294.46364 32.554343 \n", "L 294.798775 35.632707 \n", "L 295.13391 51.261351 \n", "L 295.469046 49.567718 \n", "L 295.804181 49.512281 \n", "L 296.139316 58.009012 \n", "L 296.474451 43.344854 \n", "L 296.809586 42.644814 \n", "L 297.144721 61.054096 \n", "L 297.479856 36.180183 \n", "L 297.814992 61.900111 \n", "L 298.150127 31.626311 \n", "L 298.485262 66.808516 \n", "L 298.820397 45.825175 \n", "L 299.155532 33.770046 \n", "L 299.490667 33.26604 \n", "L 299.825802 38.356743 \n", "L 300.160938 49.610674 \n", "L 300.496073 44.605866 \n", "L 300.831208 56.573546 \n", "L 301.166343 43.94207 \n", "L 301.501478 37.184769 \n", "L 301.836613 33.47596 \n", "L 302.171748 35.821428 \n", "L 302.506883 49.941274 \n", "L 302.842019 37.220019 \n", "L 303.177154 37.262267 \n", "L 303.512289 30.17358 \n", "L 303.847424 44.169831 \n", "L 304.182559 34.122697 \n", "L 304.517694 34.20174 \n", "L 304.852829 44.261868 \n", "L 305.187965 34.211136 \n", "L 305.5231 45.127415 \n", "L 305.858235 37.99762 \n", "L 306.19337 16.111235 \n", "L 306.528505 47.345465 \n", "L 306.86364 34.656476 \n", "L 307.198775 39.697478 \n", "L 307.53391 26.181121 \n", "L 307.869046 39.560076 \n", "L 308.204181 34.552073 \n", "L 308.539316 22.216763 \n", "L 308.874451 25.561821 \n", "L 309.209586 48.959109 \n", "L 309.544721 50.98321 \n", "L 309.879856 26.451303 \n", "L 310.214992 43.726569 \n", "L 310.885262 28.089113 \n", "L 311.220397 25.574409 \n", "L 311.555532 38.142704 \n", "L 311.890667 44.763945 \n", "L 312.225802 32.455195 \n", "L 312.560938 34.526327 \n", "L 312.896073 39.553988 \n", "L 313.231208 62.248349 \n", "L 313.566343 30.036466 \n", "L 313.901478 37.407264 \n", "L 314.236613 40.906517 \n", "L 314.571748 38.999088 \n", "L 315.242019 51.798942 \n", "L 315.577154 48.931005 \n", "L 315.912289 42.526024 \n", "L 316.247424 53.564679 \n", "L 316.582559 52.725125 \n", "L 316.917694 42.300451 \n", "L 317.252829 38.816348 \n", "L 317.587965 28.223404 \n", "L 317.9231 50.932012 \n", "L 318.258235 24.784252 \n", "L 318.59337 55.014139 \n", "L 319.26364 25.045303 \n", "L 319.598775 49.809744 \n", "L 319.93391 35.336272 \n", "L 320.269046 30.886398 \n", "L 320.604181 60.08805 \n", "L 320.939316 34.21857 \n", "L 321.274451 58.871865 \n", "L 321.609586 41.220796 \n", "L 321.944721 38.172702 \n", "L 322.279856 51.174998 \n", "L 322.614992 54.562208 \n", "L 322.950127 47.563975 \n", "L 323.285262 45.523839 \n", "L 323.620397 55.554181 \n", "L 323.955532 43.6036 \n", "L 324.290667 38.863991 \n", "L 324.625802 49.973406 \n", "L 324.960938 34.144631 \n", "L 325.296073 57.794156 \n", "L 325.631208 47.22805 \n", "L 325.966343 43.992854 \n", "L 326.301478 53.849681 \n", "L 326.636613 41.126233 \n", "L 326.971748 44.787956 \n", "L 327.306883 22.839292 \n", "L 327.642019 47.170077 \n", "L 327.977154 36.975539 \n", "L 328.312289 44.681575 \n", "L 328.647424 40.202542 \n", "L 328.982559 20.921631 \n", "L 329.317694 48.507517 \n", "L 329.652829 42.868674 \n", "L 329.987965 28.547503 \n", "L 331.328505 46.561855 \n", "L 331.66364 35.551309 \n", "L 331.998775 40.734909 \n", "L 332.33391 24.803077 \n", "L 332.669046 47.851972 \n", "L 333.004181 54.589414 \n", "L 333.339316 38.210906 \n", "L 333.674451 51.780146 \n", "L 334.009586 51.087834 \n", "L 334.344721 55.439634 \n", "L 334.679856 40.666485 \n", "L 335.014992 43.76376 \n", "L 335.350127 40.075521 \n", "L 335.685262 59.465593 \n", "L 336.020397 42.63965 \n", "L 336.355532 73.083455 \n", "L 336.690667 44.536128 \n", "L 337.025802 44.807608 \n", "L 337.360937 56.877535 \n", "L 337.696073 49.852758 \n", "L 338.031208 29.803991 \n", "L 339.036613 60.498629 \n", "L 339.371748 26.932672 \n", "L 339.706883 24.257684 \n", "L 340.042019 57.502233 \n", "L 340.377154 71.939445 \n", "L 340.712289 51.314167 \n", "L 341.047424 66.751181 \n", "L 341.382559 32.857331 \n", "L 341.717694 85.417532 \n", "L 342.387965 37.864185 \n", "L 343.058235 51.609836 \n", "L 343.39337 53.106986 \n", "L 343.728505 66.964169 \n", "L 344.73391 39.312034 \n", "L 345.069046 63.002031 \n", "L 345.404181 51.273895 \n", "L 345.739316 55.628484 \n", "L 346.074451 48.876194 \n", "L 346.409586 56.094248 \n", "L 346.744721 56.957158 \n", "L 347.079856 74.9738 \n", "L 347.414992 68.481347 \n", "L 347.750127 50.710118 \n", "L 348.085262 87.194047 \n", "L 348.420397 69.994267 \n", "L 348.755532 66.03104 \n", "L 349.090667 55.979799 \n", "L 349.425802 74.752721 \n", "L 349.760938 63.374634 \n", "L 350.431208 75.178121 \n", "L 350.766343 58.206842 \n", "L 351.101478 62.501729 \n", "L 351.436613 56.426661 \n", "L 351.771748 77.814716 \n", "L 352.106883 63.250177 \n", "L 352.442019 59.13342 \n", "L 352.777154 62.885781 \n", "L 353.112289 78.607904 \n", "L 353.447424 46.659745 \n", "L 353.782559 60.052134 \n", "L 354.117694 78.305613 \n", "L 354.787965 64.582181 \n", "L 355.1231 46.448986 \n", "L 355.458235 86.503339 \n", "L 355.79337 69.844282 \n", "L 356.128505 75.658944 \n", "L 356.46364 67.30708 \n", "L 356.798775 73.016804 \n", "L 357.13391 40.025485 \n", "L 357.804181 91.190244 \n", "L 358.139316 67.851492 \n", "L 358.474451 59.503766 \n", "L 358.809586 59.208708 \n", "L 359.144721 70.976183 \n", "L 359.479856 73.236885 \n", "L 359.814992 79.181973 \n", "L 360.150127 73.811473 \n", "L 360.485262 64.900367 \n", "L 360.820397 92.21929 \n", "L 361.155532 65.478167 \n", "L 361.490667 75.61441 \n", "L 361.825802 59.909201 \n", "L 362.160938 83.884979 \n", "L 362.496073 92.477308 \n", "L 362.831208 98.051157 \n", "L 363.501478 68.57869 \n", "L 363.836613 88.051114 \n", "L 364.171748 80.546687 \n", "L 364.506883 96.087567 \n", "L 364.842019 89.667737 \n", "L 365.177154 73.275441 \n", "L 365.512289 117.961673 \n", "L 365.847424 83.555797 \n", "L 366.182559 87.239697 \n", "L 366.517694 98.581996 \n", "L 366.852829 98.89668 \n", "L 367.187965 80.599499 \n", "L 367.5231 110.871946 \n", "L 367.858235 86.909661 \n", "L 368.19337 105.505027 \n", "L 368.86364 76.945225 \n", "L 369.198775 78.906776 \n", "L 369.53391 89.995222 \n", "L 369.869046 90.480386 \n", "L 370.204181 70.812058 \n", "L 370.539316 109.27787 \n", "L 370.874451 91.043122 \n", "L 371.209586 103.756206 \n", "L 371.544721 99.753872 \n", "L 371.879856 100.60051 \n", "L 372.214992 108.655015 \n", "L 372.550127 102.559199 \n", "L 372.885262 88.998853 \n", "L 373.220397 108.805205 \n", "L 373.890667 93.584136 \n", "L 374.225802 108.050527 \n", "L 374.560938 92.47331 \n", "L 374.896073 127.846612 \n", "L 375.231208 111.937597 \n", "L 375.566343 110.652957 \n", "L 376.236613 93.181595 \n", "L 376.571748 116.28849 \n", "L 376.906883 92.778991 \n", "L 377.242019 96.396629 \n", "L 377.577154 89.801226 \n", "L 377.912289 115.761385 \n", "L 378.247424 105.704727 \n", "L 378.582559 109.848799 \n", "L 378.917694 94.644811 \n", "L 379.252829 95.146298 \n", "L 379.587965 110.927752 \n", "L 379.9231 115.719563 \n", "L 380.258235 103.479645 \n", "L 380.59337 117.940622 \n", "L 380.928505 117.331302 \n", "L 381.26364 106.117411 \n", "L 381.598775 107.3568 \n", "L 381.93391 110.196901 \n", "L 382.269046 126.978276 \n", "L 382.604181 112.201479 \n", "L 382.939316 108.366834 \n", "L 383.274451 99.895389 \n", "L 383.609586 114.677829 \n", "L 383.944721 112.091267 \n", "L 384.614992 103.95886 \n", "L 384.950127 110.465745 \n", "L 385.285262 101.820585 \n", "L 385.620397 125.377372 \n", "L 385.955532 109.982809 \n", "L 386.290667 111.553565 \n", "L 386.625802 117.887867 \n", "L 386.960938 144.781945 \n", "L 386.960938 144.781945 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 53.501478 82.259956 \n", "L 53.836613 84.232191 \n", "L 54.171748 95.834543 \n", "L 54.842019 91.789077 \n", "L 55.177154 79.724841 \n", "L 55.512289 83.862962 \n", "L 55.847424 78.159466 \n", "L 56.182559 77.840561 \n", "L 56.517694 64.073401 \n", "L 57.5231 86.258665 \n", "L 58.19337 93.659818 \n", "L 58.528505 79.897894 \n", "L 58.86364 79.562792 \n", "L 59.198775 74.138099 \n", "L 59.53391 73.460138 \n", "L 59.869046 75.77085 \n", "L 60.204181 67.099498 \n", "L 60.539316 66.35955 \n", "L 60.874451 66.01376 \n", "L 61.209586 61.594109 \n", "L 61.544721 65.513615 \n", "L 61.879856 77.874512 \n", "L 62.550127 83.346073 \n", "L 62.885262 89.750477 \n", "L 63.220397 82.547496 \n", "L 63.555532 82.045707 \n", "L 63.890667 81.249872 \n", "L 64.225802 85.98868 \n", "L 64.560938 92.952812 \n", "L 65.566343 75.732149 \n", "L 65.901478 75.215029 \n", "L 66.236613 72.984284 \n", "L 66.571748 68.331783 \n", "L 66.906883 69.491325 \n", "L 67.242019 66.745291 \n", "L 67.577154 74.825469 \n", "L 67.912289 77.868211 \n", "L 68.247424 73.842489 \n", "L 68.582559 72.079564 \n", "L 68.917694 64.27859 \n", "L 69.252829 62.765693 \n", "L 69.587965 56.375467 \n", "L 69.9231 64.411048 \n", "L 70.59337 59.098581 \n", "L 70.928505 61.89357 \n", "L 71.26364 55.12452 \n", "L 71.598775 69.820226 \n", "L 71.93391 59.380209 \n", "L 72.269046 69.381341 \n", "L 72.604181 62.033569 \n", "L 72.939316 58.873426 \n", "L 73.609586 46.202533 \n", "L 73.944721 53.529232 \n", "L 74.279856 51.722559 \n", "L 74.614992 52.878187 \n", "L 74.950127 57.400715 \n", "L 75.285262 57.267096 \n", "L 75.620397 53.6734 \n", "L 75.955532 54.808281 \n", "L 76.290667 59.984777 \n", "L 76.960938 58.521066 \n", "L 77.296073 42.198322 \n", "L 77.631208 52.939209 \n", "L 77.966343 50.799871 \n", "L 78.301478 38.08664 \n", "L 78.636613 48.151048 \n", "L 78.971748 49.909059 \n", "L 79.306883 47.699671 \n", "L 79.642019 48.3265 \n", "L 79.977154 56.551093 \n", "L 80.647424 42.284102 \n", "L 80.982559 43.400307 \n", "L 81.317694 38.937278 \n", "L 81.652829 37.937779 \n", "L 81.987965 49.574892 \n", "L 82.3231 44.190838 \n", "L 82.658235 48.81819 \n", "L 82.99337 50.635849 \n", "L 83.328505 46.648436 \n", "L 83.66364 50.138042 \n", "L 83.998775 47.093871 \n", "L 84.33391 45.986911 \n", "L 84.669046 46.806975 \n", "L 85.004181 38.902489 \n", "L 85.339316 49.061192 \n", "L 85.674451 44.12513 \n", "L 86.009586 47.080168 \n", "L 86.344721 41.496056 \n", "L 86.679856 43.844661 \n", "L 87.014992 37.930551 \n", "L 87.685262 47.78131 \n", "L 88.020397 41.508939 \n", "L 88.355532 48.116396 \n", "L 88.690667 52.287995 \n", "L 89.025802 52.478606 \n", "L 89.360938 57.392169 \n", "L 90.031208 48.425216 \n", "L 90.366343 45.714953 \n", "L 90.701478 40.535693 \n", "L 91.036613 39.254925 \n", "L 91.371748 38.707019 \n", "L 92.377154 40.744385 \n", "L 92.712289 37.867177 \n", "L 93.047424 47.022138 \n", "L 93.382559 50.289239 \n", "L 93.717694 42.736055 \n", "L 94.387965 59.422621 \n", "L 94.7231 49.261889 \n", "L 95.058235 49.14373 \n", "L 95.39337 44.851309 \n", "L 95.728505 42.336702 \n", "L 96.06364 38.410628 \n", "L 96.398775 42.328062 \n", "L 96.73391 47.734269 \n", "L 97.069046 42.875098 \n", "L 97.404181 35.724933 \n", "L 97.739316 45.211738 \n", "L 98.409586 38.42945 \n", "L 98.744721 50.469291 \n", "L 99.079856 50.825918 \n", "L 99.414992 46.608431 \n", "L 99.750127 48.15113 \n", "L 100.085262 45.876547 \n", "L 100.420397 44.904795 \n", "L 100.755532 41.360221 \n", "L 101.090667 43.022619 \n", "L 101.425802 42.026555 \n", "L 101.760938 37.988081 \n", "L 102.096073 41.34365 \n", "L 102.431208 37.721356 \n", "L 102.766343 40.759446 \n", "L 103.101478 31.873426 \n", "L 103.436613 40.917623 \n", "L 103.771748 34.076834 \n", "L 104.106883 35.507978 \n", "L 104.442019 32.525753 \n", "L 104.777154 40.171375 \n", "L 105.112289 34.59061 \n", "L 105.447424 35.218015 \n", "L 105.782559 32.926648 \n", "L 106.117694 44.561617 \n", "L 106.452829 34.545494 \n", "L 106.787965 37.306141 \n", "L 107.1231 41.411029 \n", "L 107.458235 30.233019 \n", "L 107.79337 32.697865 \n", "L 108.128505 32.474287 \n", "L 108.46364 30.637713 \n", "L 108.798775 34.582973 \n", "L 109.13391 34.336752 \n", "L 109.469046 40.446522 \n", "L 109.804181 36.836379 \n", "L 110.139316 45.806742 \n", "L 110.474451 38.738344 \n", "L 110.809586 49.60236 \n", "L 111.144721 42.955572 \n", "L 111.479856 41.749649 \n", "L 111.814992 38.704888 \n", "L 112.150127 33.27954 \n", "L 112.485262 41.358752 \n", "L 112.820397 38.719601 \n", "L 113.155532 33.87139 \n", "L 113.490667 48.538535 \n", "L 113.825802 38.340558 \n", "L 114.160938 38.297747 \n", "L 114.496073 43.362846 \n", "L 114.831208 34.887672 \n", "L 115.166343 41.540688 \n", "L 115.501478 39.715515 \n", "L 115.836613 30.649909 \n", "L 116.171748 39.263999 \n", "L 116.506883 35.944124 \n", "L 116.842019 29.457242 \n", "L 117.177154 29.746705 \n", "L 117.512289 34.432992 \n", "L 117.847424 27.923328 \n", "L 118.182559 30.6208 \n", "L 118.517694 31.127048 \n", "L 118.852829 33.908344 \n", "L 119.187965 31.438091 \n", "L 119.5231 33.163442 \n", "L 119.858235 32.136161 \n", "L 120.528505 42.832948 \n", "L 120.86364 37.594861 \n", "L 121.198775 42.460902 \n", "L 121.53391 44.54214 \n", "L 121.869046 35.278036 \n", "L 122.204181 31.994526 \n", "L 122.539316 46.163572 \n", "L 122.874451 35.501931 \n", "L 123.209586 34.12742 \n", "L 123.544721 43.411749 \n", "L 124.214992 40.643895 \n", "L 124.550127 45.664528 \n", "L 124.885262 48.183455 \n", "L 125.555532 42.669876 \n", "L 125.890667 44.278945 \n", "L 126.225802 39.369738 \n", "L 126.896073 48.666046 \n", "L 127.231208 44.684044 \n", "L 127.566343 55.296137 \n", "L 127.901478 47.223545 \n", "L 128.236613 46.532167 \n", "L 128.571748 51.155448 \n", "L 128.906883 40.93623 \n", "L 129.242019 52.15437 \n", "L 129.577154 48.742971 \n", "L 129.912289 50.183601 \n", "L 130.582559 56.694919 \n", "L 130.917694 50.294856 \n", "L 131.252829 54.06026 \n", "L 131.587965 51.260348 \n", "L 131.9231 51.995961 \n", "L 132.258235 50.602166 \n", "L 132.59337 57.613987 \n", "L 132.928505 60.667081 \n", "L 133.26364 65.009954 \n", "L 133.598775 52.504852 \n", "L 133.93391 60.535023 \n", "L 134.269046 58.747874 \n", "L 134.604181 55.972828 \n", "L 134.939316 67.221242 \n", "L 135.274451 64.051261 \n", "L 135.609586 64.689157 \n", "L 135.944721 63.892285 \n", "L 136.279856 61.180841 \n", "L 136.614992 66.220156 \n", "L 136.950127 62.364738 \n", "L 137.285262 67.504087 \n", "L 137.620397 65.232282 \n", "L 137.955532 53.600477 \n", "L 138.290667 63.113522 \n", "L 138.625802 56.627742 \n", "L 138.960938 56.209083 \n", "L 139.296073 66.396441 \n", "L 139.631208 63.794645 \n", "L 139.966343 59.379556 \n", "L 140.301478 58.88375 \n", "L 140.636613 55.489173 \n", "L 140.971748 53.463965 \n", "L 141.306883 59.397783 \n", "L 141.642019 61.708738 \n", "L 141.977154 59.974775 \n", "L 142.312289 60.000765 \n", "L 142.647424 55.4652 \n", "L 142.982559 62.132995 \n", "L 143.317694 57.356849 \n", "L 143.652829 67.96432 \n", "L 143.987965 62.368424 \n", "L 144.3231 60.508643 \n", "L 144.658235 67.611966 \n", "L 144.99337 65.054451 \n", "L 145.328505 73.286747 \n", "L 145.66364 70.851184 \n", "L 145.998775 71.045109 \n", "L 146.33391 72.357906 \n", "L 146.669046 65.132193 \n", "L 147.004181 67.593209 \n", "L 147.339316 67.073796 \n", "L 147.674451 64.212152 \n", "L 148.009586 62.79478 \n", "L 148.344721 63.871247 \n", "L 148.679856 63.230355 \n", "L 149.014992 63.672288 \n", "L 149.350127 68.968931 \n", "L 149.685262 69.977098 \n", "L 150.020397 69.629253 \n", "L 150.690667 79.792959 \n", "L 151.025802 82.373943 \n", "L 151.360938 86.622687 \n", "L 151.696073 87.290989 \n", "L 152.031208 81.598088 \n", "L 152.366343 94.263699 \n", "L 152.701478 84.031492 \n", "L 153.036613 88.46336 \n", "L 153.371748 83.106421 \n", "L 153.706883 88.816352 \n", "L 154.042019 83.788633 \n", "L 154.377154 88.246629 \n", "L 154.712289 88.410798 \n", "L 155.047424 100.229479 \n", "L 155.382559 94.875096 \n", "L 156.052829 76.163534 \n", "L 156.387965 86.828059 \n", "L 156.7231 82.040926 \n", "L 157.058235 83.917356 \n", "L 157.39337 94.599017 \n", "L 157.728505 82.643346 \n", "L 158.06364 81.811899 \n", "L 158.398775 80.056919 \n", "L 159.404181 97.051114 \n", "L 159.739316 94.554308 \n", "L 160.074451 98.131412 \n", "L 160.409586 97.565566 \n", "L 160.744721 96.037102 \n", "L 161.079856 81.790644 \n", "L 161.414992 91.818881 \n", "L 162.085262 99.536794 \n", "L 162.420397 93.899722 \n", "L 162.755532 103.65007 \n", "L 163.090667 94.925471 \n", "L 163.425802 103.512729 \n", "L 163.760938 107.475773 \n", "L 164.096073 115.654818 \n", "L 164.431208 115.208925 \n", "L 164.766343 113.492489 \n", "L 165.436613 99.904156 \n", "L 165.771748 101.560946 \n", "L 166.106883 102.528297 \n", "L 166.777154 111.651091 \n", "L 167.112289 109.860865 \n", "L 167.447424 104.573785 \n", "L 167.782559 101.1595 \n", "L 168.117694 104.818227 \n", "L 168.787965 95.249558 \n", "L 169.1231 99.397045 \n", "L 169.458235 109.292029 \n", "L 169.79337 114.456747 \n", "L 170.128505 111.604475 \n", "L 170.46364 115.460684 \n", "L 170.798775 112.624444 \n", "L 171.13391 119.263429 \n", "L 171.469046 111.955568 \n", "L 171.804181 116.285782 \n", "L 172.139316 114.258519 \n", "L 172.474451 109.880631 \n", "L 172.809586 111.419439 \n", "L 173.144721 119.07714 \n", "L 173.814992 123.542644 \n", "L 174.150127 123.669254 \n", "L 174.485262 129.310063 \n", "L 175.155532 102.80625 \n", "L 175.490667 117.820338 \n", "L 175.825802 116.379464 \n", "L 176.160938 124.513528 \n", "L 176.496073 120.943679 \n", "L 176.831208 128.42754 \n", "L 177.166343 117.00925 \n", "L 177.501478 117.898216 \n", "L 178.171748 131.979298 \n", "L 178.506883 128.067787 \n", "L 178.842019 116.322289 \n", "L 179.177154 111.60967 \n", "L 179.512289 109.575609 \n", "L 179.847424 117.868801 \n", "L 180.182559 122.919257 \n", "L 180.517694 122.74663 \n", "L 180.852829 126.918413 \n", "L 181.187965 124.018143 \n", "L 181.5231 127.319007 \n", "L 181.858235 132.232498 \n", "L 182.19337 140.21128 \n", "L 182.528505 139.846605 \n", "L 182.86364 133.153406 \n", "L 183.198775 134.185448 \n", "L 184.204181 118.869904 \n", "L 184.539316 133.83519 \n", "L 184.874451 136.706484 \n", "L 185.209586 138.152807 \n", "L 185.544721 122.600771 \n", "L 185.879856 138.163653 \n", "L 186.214992 141.097945 \n", "L 186.550127 147.46258 \n", "L 186.885262 128.835799 \n", "L 187.220397 129.389559 \n", "L 187.555532 130.23137 \n", "L 187.890667 135.616064 \n", "L 188.225802 134.444732 \n", "L 188.560938 134.299665 \n", "L 188.896073 136.59454 \n", "L 189.231208 144.403782 \n", "L 189.901478 131.756812 \n", "L 190.236613 132.244092 \n", "L 190.571748 131.632092 \n", "L 190.906883 133.287059 \n", "L 191.242019 132.239475 \n", "L 191.577154 135.7932 \n", "L 191.912289 132.981345 \n", "L 192.247424 138.180826 \n", "L 192.582559 140.10349 \n", "L 192.917694 150.64386 \n", "L 193.252829 142.946169 \n", "L 193.587965 148.235458 \n", "L 193.9231 150.652452 \n", "L 194.258235 148.873646 \n", "L 194.59337 148.729111 \n", "L 194.928505 136.952266 \n", "L 195.26364 138.688891 \n", "L 195.598775 133.455799 \n", "L 195.93391 134.015112 \n", "L 196.269046 134.944692 \n", "L 196.604181 127.918848 \n", "L 196.939316 135.637846 \n", "L 197.274451 123.961684 \n", "L 197.609586 135.459925 \n", "L 197.944721 132.888409 \n", "L 198.279856 147.313961 \n", "L 198.614992 145.376385 \n", "L 198.950127 142.46658 \n", "L 199.285262 132.922897 \n", "L 199.620397 139.659873 \n", "L 199.955532 136.321286 \n", "L 200.290667 138.79922 \n", "L 200.625802 125.799071 \n", "L 200.960938 132.062941 \n", "L 201.296073 126.832689 \n", "L 201.631208 132.739638 \n", "L 201.966343 134.765387 \n", "L 202.301478 132.383245 \n", "L 202.636613 135.97064 \n", "L 202.971748 134.034989 \n", "L 203.306883 138.554227 \n", "L 203.642019 139.130596 \n", "L 203.977154 141.820134 \n", "L 204.312289 150.690577 \n", "L 204.647424 142.949506 \n", "L 204.982559 148.769667 \n", "L 205.317694 144.203214 \n", "L 205.652829 150.959405 \n", "L 205.987965 148.709475 \n", "L 206.3231 153.089468 \n", "L 206.658235 155.571689 \n", "L 207.328505 136.146151 \n", "L 207.66364 132.219954 \n", "L 207.998775 136.407624 \n", "L 208.33391 143.266083 \n", "L 208.669046 139.430152 \n", "L 209.004181 132.579551 \n", "L 209.339316 134.790244 \n", "L 209.674451 132.375339 \n", "L 210.009586 137.558639 \n", "L 210.344721 140.99049 \n", "L 210.679856 137.799212 \n", "L 211.014992 142.728916 \n", "L 211.350127 139.968066 \n", "L 211.685262 142.677893 \n", "L 212.020397 141.588976 \n", "L 212.690667 147.405307 \n", "L 213.025802 148.384166 \n", "L 213.360938 147.658329 \n", "L 213.696073 146.615637 \n", "L 214.031208 143.763308 \n", "L 214.366343 135.930944 \n", "L 214.701478 138.645022 \n", "L 215.036613 140.398348 \n", "L 215.371748 137.657558 \n", "L 215.706883 143.312599 \n", "L 216.042019 138.21676 \n", "L 216.377154 140.016557 \n", "L 216.712289 132.787609 \n", "L 217.047424 136.358171 \n", "L 217.717694 151.953233 \n", "L 218.387965 141.580485 \n", "L 218.7231 138.762995 \n", "L 219.39337 150.040599 \n", "L 219.728505 144.306034 \n", "L 220.06364 143.76691 \n", "L 220.398775 147.517682 \n", "L 220.73391 148.994525 \n", "L 221.069046 151.470407 \n", "L 221.739316 147.288538 \n", "L 222.074451 144.287139 \n", "L 222.409586 135.295535 \n", "L 222.744721 134.059221 \n", "L 223.079856 129.748728 \n", "L 223.750127 141.785612 \n", "L 224.085262 143.285839 \n", "L 225.090667 135.843581 \n", "L 225.425802 142.671985 \n", "L 225.760938 133.60572 \n", "L 226.096073 135.522454 \n", "L 226.431208 127.417742 \n", "L 226.766343 127.353554 \n", "L 227.101478 127.643211 \n", "L 227.436613 129.905887 \n", "L 227.771748 138.70208 \n", "L 228.106883 129.051187 \n", "L 229.112289 139.930365 \n", "L 229.447424 137.360787 \n", "L 229.782559 136.787892 \n", "L 230.117694 138.453718 \n", "L 230.452829 138.985142 \n", "L 230.787965 139.259902 \n", "L 231.1231 139.306164 \n", "L 231.458235 132.586453 \n", "L 231.79337 131.368815 \n", "L 232.128505 121.883672 \n", "L 232.46364 120.163153 \n", "L 232.798775 122.509285 \n", "L 233.13391 127.454873 \n", "L 233.469046 135.334886 \n", "L 233.804181 137.296305 \n", "L 234.139316 130.754667 \n", "L 234.474451 132.808588 \n", "L 234.809586 130.591733 \n", "L 235.144721 143.906792 \n", "L 235.479856 121.572788 \n", "L 235.814992 127.758239 \n", "L 236.150127 123.392138 \n", "L 236.485262 146.576441 \n", "L 236.820397 131.91535 \n", "L 237.155532 134.108504 \n", "L 237.490667 122.00728 \n", "L 237.825802 123.241419 \n", "L 238.160938 113.647204 \n", "L 238.831208 126.672672 \n", "L 239.166343 125.417599 \n", "L 239.501478 122.92589 \n", "L 239.836613 118.188212 \n", "L 240.171748 122.898209 \n", "L 240.506883 123.544566 \n", "L 241.177154 117.988407 \n", "L 241.512289 123.141465 \n", "L 241.847424 121.810705 \n", "L 242.182559 130.743 \n", "L 242.517694 129.866657 \n", "L 242.852829 131.605169 \n", "L 243.187965 117.083247 \n", "L 243.5231 127.316293 \n", "L 243.858235 130.759653 \n", "L 244.19337 146.687942 \n", "L 244.528505 127.309676 \n", "L 244.86364 121.961566 \n", "L 245.198775 113.963547 \n", "L 245.53391 113.981419 \n", "L 245.869046 119.643292 \n", "L 246.204181 113.747883 \n", "L 246.539316 104.364367 \n", "L 246.874451 105.905627 \n", "L 247.209586 97.862031 \n", "L 247.544721 112.664789 \n", "L 247.879856 110.523233 \n", "L 248.214992 122.31788 \n", "L 248.550127 114.368554 \n", "L 248.885262 119.102246 \n", "L 249.220397 114.372189 \n", "L 249.555532 118.40448 \n", "L 249.890667 119.357437 \n", "L 250.225802 117.018277 \n", "L 250.560938 112.802749 \n", "L 250.896073 105.165358 \n", "L 251.231208 107.025536 \n", "L 251.566343 113.08392 \n", "L 251.901478 114.750665 \n", "L 252.236613 107.949214 \n", "L 252.571748 96.932859 \n", "L 252.906883 95.97259 \n", "L 253.242019 97.143748 \n", "L 253.577154 92.113856 \n", "L 253.912289 98.555509 \n", "L 254.247424 102.128111 \n", "L 254.582559 101.769552 \n", "L 254.917694 99.363762 \n", "L 255.252829 101.718152 \n", "L 255.587965 101.634472 \n", "L 255.9231 103.378995 \n", "L 256.258235 108.378122 \n", "L 256.59337 106.172409 \n", "L 256.928505 107.837205 \n", "L 257.26364 106.30007 \n", "L 257.598775 113.65958 \n", "L 257.93391 112.391913 \n", "L 258.269046 94.56846 \n", "L 258.604181 101.790511 \n", "L 258.939316 89.315208 \n", "L 259.274451 91.952831 \n", "L 259.609586 88.332401 \n", "L 259.944721 94.00393 \n", "L 260.279856 97.683186 \n", "L 260.614992 97.686211 \n", "L 260.950127 95.971089 \n", "L 261.285262 99.183057 \n", "L 261.955532 88.635878 \n", "L 262.290667 87.671078 \n", "L 262.625802 83.38924 \n", "L 262.960938 95.149322 \n", "L 263.296073 86.853293 \n", "L 263.631208 88.319784 \n", "L 264.636613 100.49482 \n", "L 264.971748 97.378027 \n", "L 265.306883 83.054185 \n", "L 265.642019 90.510478 \n", "L 265.977154 82.159346 \n", "L 266.312289 83.462964 \n", "L 266.647424 88.006906 \n", "L 266.982559 88.003271 \n", "L 267.317694 93.788697 \n", "L 267.652829 85.266154 \n", "L 267.987965 94.043185 \n", "L 268.3231 84.690245 \n", "L 268.658235 88.661115 \n", "L 268.99337 83.474653 \n", "L 269.328505 83.58765 \n", "L 269.66364 77.406071 \n", "L 269.998775 76.036543 \n", "L 270.669046 86.099539 \n", "L 271.004181 80.445211 \n", "L 271.674451 77.273713 \n", "L 272.009586 80.579425 \n", "L 272.344721 75.252159 \n", "L 272.679856 78.26372 \n", "L 273.350127 72.706937 \n", "L 273.685262 74.511282 \n", "L 274.020397 73.589025 \n", "L 274.690667 81.75691 \n", "L 275.025802 78.72561 \n", "L 275.360937 76.798312 \n", "L 276.031208 69.570764 \n", "L 276.366343 64.384454 \n", "L 277.036613 65.459823 \n", "L 277.371748 64.789672 \n", "L 277.706883 67.871653 \n", "L 278.042019 68.520597 \n", "L 278.377154 71.945449 \n", "L 278.712289 68.614878 \n", "L 279.047424 69.339128 \n", "L 279.382559 70.617748 \n", "L 279.717694 58.988574 \n", "L 280.052829 68.941501 \n", "L 280.387965 61.009803 \n", "L 280.7231 58.045495 \n", "L 281.058235 61.059164 \n", "L 281.39337 61.224165 \n", "L 281.728505 53.249701 \n", "L 282.06364 60.513177 \n", "L 282.398775 60.403652 \n", "L 282.73391 53.504847 \n", "L 283.069046 63.768576 \n", "L 283.404181 54.585247 \n", "L 283.739316 64.257122 \n", "L 284.074451 60.245233 \n", "L 284.409586 62.171551 \n", "L 284.744721 57.413053 \n", "L 285.079856 61.12839 \n", "L 285.414992 49.943021 \n", "L 285.750127 56.096413 \n", "L 286.085262 54.796852 \n", "L 286.420397 53.949167 \n", "L 286.755532 62.840291 \n", "L 287.090667 57.666439 \n", "L 287.425802 60.244087 \n", "L 287.760938 61.246954 \n", "L 288.096073 54.217783 \n", "L 288.431208 53.008364 \n", "L 288.766343 65.045649 \n", "L 289.101478 54.088925 \n", "L 289.436613 60.007506 \n", "L 289.771748 58.352969 \n", "L 290.106883 54.720547 \n", "L 290.442019 53.980935 \n", "L 290.777154 50.769417 \n", "L 291.112289 56.264244 \n", "L 291.447424 51.534259 \n", "L 291.782559 55.034459 \n", "L 292.452829 58.655426 \n", "L 292.787965 45.69816 \n", "L 293.1231 53.666824 \n", "L 293.458235 46.035101 \n", "L 293.79337 41.667721 \n", "L 294.128505 46.788292 \n", "L 294.46364 46.706983 \n", "L 294.798775 40.135853 \n", "L 295.13391 41.291746 \n", "L 295.469046 42.988331 \n", "L 295.804181 40.777799 \n", "L 296.139316 45.22949 \n", "L 296.474451 52.048359 \n", "L 296.809586 46.722458 \n", "L 297.144721 46.243619 \n", "L 297.479856 52.764552 \n", "L 297.814992 41.041032 \n", "L 298.150127 54.131917 \n", "L 298.485262 42.618751 \n", "L 298.820397 55.672118 \n", "L 299.490667 42.40272 \n", "L 299.825802 43.217823 \n", "L 300.160938 37.401933 \n", "L 300.496073 39.975519 \n", "L 300.831208 39.589675 \n", "L 301.166343 47.835009 \n", "L 301.836613 43.001973 \n", "L 302.506883 37.018027 \n", "L 302.842019 40.608578 \n", "L 303.177154 36.082637 \n", "L 303.512289 39.770079 \n", "L 303.847424 35.84378 \n", "L 304.182559 39.346316 \n", "L 304.517694 33.813667 \n", "L 304.852829 36.118382 \n", "L 305.187965 39.576922 \n", "L 305.5231 34.420189 \n", "L 305.858235 41.033022 \n", "L 306.19337 37.850049 \n", "L 306.528505 30.021294 \n", "L 306.86364 44.222583 \n", "L 307.198775 31.221687 \n", "L 307.53391 36.961102 \n", "L 307.869046 33.288031 \n", "L 308.204181 38.092816 \n", "L 308.874451 29.63326 \n", "L 309.209586 32.014979 \n", "L 309.544721 37.129775 \n", "L 309.879856 37.044431 \n", "L 310.214992 34.113363 \n", "L 310.550127 45.834242 \n", "L 310.885262 36.473755 \n", "L 311.220397 33.065668 \n", "L 311.555532 32.225127 \n", "L 311.890667 34.239745 \n", "L 312.225802 35.202843 \n", "L 312.560938 33.468026 \n", "L 312.896073 37.569954 \n", "L 313.231208 37.401673 \n", "L 313.566343 45.079256 \n", "L 313.901478 35.624773 \n", "L 314.236613 44.138551 \n", "L 314.571748 41.831481 \n", "L 314.906883 36.4068 \n", "L 315.242019 41.157934 \n", "L 315.577154 43.894105 \n", "L 315.912289 44.560411 \n", "L 316.247424 44.575852 \n", "L 316.582559 49.14998 \n", "L 316.917694 48.309735 \n", "L 317.252829 44.477695 \n", "L 317.587965 44.234896 \n", "L 317.9231 36.874156 \n", "L 318.258235 43.918981 \n", "L 318.59337 30.449202 \n", "L 318.928505 46.957057 \n", "L 319.26364 39.059379 \n", "L 319.598775 34.646965 \n", "L 319.93391 45.991613 \n", "L 320.269046 33.770622 \n", "L 320.604181 35.541836 \n", "L 320.939316 47.933564 \n", "L 321.274451 35.406647 \n", "L 321.609586 50.569901 \n", "L 321.944721 45.498197 \n", "L 322.279856 42.321635 \n", "L 322.614992 47.839587 \n", "L 322.950127 45.720402 \n", "L 323.285262 45.275493 \n", "L 323.620397 47.583338 \n", "L 323.955532 50.877977 \n", "L 324.290667 45.069992 \n", "L 324.625802 43.52375 \n", "L 324.960938 46.961423 \n", "L 325.296073 37.787058 \n", "L 325.631208 48.696852 \n", "L 325.966343 45.124277 \n", "L 326.301478 44.477917 \n", "L 326.636613 50.729215 \n", "L 326.971748 43.359785 \n", "L 327.306883 45.091534 \n", "L 327.642019 35.511486 \n", "L 327.977154 44.496565 \n", "L 328.312289 35.428429 \n", "L 328.647424 39.959065 \n", "L 328.982559 40.077851 \n", "L 329.317694 32.271958 \n", "L 329.652829 44.6623 \n", "L 329.987965 36.45386 \n", "L 330.3231 33.873853 \n", "L 330.658235 38.680652 \n", "L 330.99337 35.928813 \n", "L 331.328505 36.188294 \n", "L 331.66364 40.373289 \n", "L 331.998775 37.77281 \n", "L 332.33391 41.117143 \n", "L 332.669046 32.89217 \n", "L 333.004181 42.307078 \n", "L 333.339316 42.233781 \n", "L 333.674451 38.411205 \n", "L 334.009586 48.63178 \n", "L 334.344721 47.94683 \n", "L 334.679856 48.189145 \n", "L 335.014992 46.148011 \n", "L 335.350127 46.176037 \n", "L 335.685262 42.894493 \n", "L 336.020397 47.666361 \n", "L 336.355532 43.065449 \n", "L 336.690667 57.170617 \n", "L 337.025802 50.655244 \n", "L 337.360937 49.999174 \n", "L 337.696073 55.195806 \n", "L 338.366343 40.395061 \n", "L 338.701478 45.095938 \n", "L 339.036613 41.947231 \n", "L 339.371748 45.736016 \n", "L 339.706883 37.463569 \n", "L 340.042019 40.012036 \n", "L 340.377154 47.162935 \n", "L 341.047424 47.078892 \n", "L 341.382559 62.448831 \n", "L 341.717694 50.024813 \n", "L 342.052829 64.340753 \n", "L 342.387965 59.661535 \n", "L 342.7231 49.518438 \n", "L 343.058235 56.92866 \n", "L 343.39337 48.876096 \n", "L 343.728505 45.478299 \n", "L 344.06364 54.763086 \n", "L 344.398775 55.270033 \n", "L 344.73391 54.424948 \n", "L 345.069046 50.304835 \n", "L 345.404181 55.125602 \n", "L 345.739316 48.913569 \n", "L 346.074451 51.29001 \n", "L 346.409586 52.655827 \n", "L 346.744721 52.456548 \n", "L 347.079856 53.615899 \n", "L 347.414992 61.021748 \n", "L 347.750127 63.124303 \n", "L 348.085262 58.817061 \n", "L 348.420397 70.581571 \n", "L 349.090667 66.401892 \n", "L 349.425802 67.684009 \n", "L 349.760938 68.459785 \n", "L 350.096073 63.406975 \n", "L 350.431208 65.83549 \n", "L 350.766343 71.564098 \n", "L 351.101478 63.619925 \n", "L 351.436613 65.762678 \n", "L 351.771748 61.593346 \n", "L 352.106883 66.005151 \n", "L 352.442019 62.964323 \n", "L 352.777154 62.02415 \n", "L 353.112289 65.579087 \n", "L 353.447424 67.93252 \n", "L 353.782559 56.628331 \n", "L 354.452829 65.919713 \n", "L 354.787965 63.187433 \n", "L 355.1231 66.674468 \n", "L 355.458235 61.507148 \n", "L 355.79337 68.52845 \n", "L 356.128505 65.515888 \n", "L 356.798775 73.247525 \n", "L 357.13391 71.570049 \n", "L 357.469046 58.39113 \n", "L 358.139316 69.216977 \n", "L 358.474451 63.278887 \n", "L 358.809586 68.741946 \n", "L 359.479856 65.501448 \n", "L 359.814992 65.910942 \n", "L 360.150127 71.183283 \n", "L 360.485262 73.464967 \n", "L 360.820397 70.814794 \n", "L 361.155532 76.569798 \n", "L 361.490667 73.763091 \n", "L 361.825802 73.883174 \n", "L 362.160938 70.122808 \n", "L 362.496073 72.462849 \n", "L 362.831208 80.494835 \n", "L 363.166343 85.433448 \n", "L 363.501478 92.800479 \n", "L 364.171748 78.837654 \n", "L 364.506883 82.030686 \n", "L 364.842019 82.971859 \n", "L 365.177154 93.211285 \n", "L 365.512289 84.875347 \n", "L 365.847424 80.414624 \n", "L 366.182559 102.085742 \n", "L 366.517694 87.15153 \n", "L 367.187965 95.500623 \n", "L 367.5231 94.249277 \n", "L 367.858235 87.657142 \n", "L 368.19337 101.629179 \n", "L 368.528505 90.428054 \n", "L 368.86364 102.014843 \n", "L 369.198775 92.924216 \n", "L 369.53391 88.858542 \n", "L 369.869046 83.307828 \n", "L 370.204181 88.084642 \n", "L 370.539316 82.297976 \n", "L 370.874451 79.973862 \n", "L 371.209586 98.599058 \n", "L 371.544721 88.513717 \n", "L 371.879856 101.116493 \n", "L 372.214992 97.803271 \n", "L 372.550127 99.535581 \n", "L 372.885262 103.477243 \n", "L 373.220397 104.776653 \n", "L 373.555532 97.457008 \n", "L 373.890667 103.420364 \n", "L 374.225802 98.801704 \n", "L 374.560938 99.221907 \n", "L 374.896073 103.185135 \n", "L 375.231208 93.446465 \n", "L 375.566343 114.713483 \n", "L 375.901478 109.971268 \n", "L 376.236613 118.482123 \n", "L 376.906883 99.137213 \n", "L 377.242019 106.634969 \n", "L 377.577154 100.405242 \n", "L 377.912289 101.477789 \n", "L 378.247424 90.958907 \n", "L 378.582559 106.981819 \n", "L 378.917694 102.287799 \n", "L 379.252829 111.836123 \n", "L 379.9231 96.45815 \n", "L 380.258235 104.022428 \n", "L 381.26364 111.816424 \n", "L 381.93391 114.444425 \n", "L 382.604181 105.722674 \n", "L 382.939316 114.701762 \n", "L 383.274451 117.013699 \n", "L 383.609586 117.66338 \n", "L 383.944721 105.901209 \n", "L 384.279856 107.571158 \n", "L 384.614992 108.341613 \n", "L 384.950127 112.396332 \n", "L 385.285262 107.799868 \n", "L 385.620397 107.858525 \n", "L 385.955532 103.126762 \n", "L 386.290667 113.227028 \n", "L 386.625802 111.993186 \n", "L 386.960938 115.688926 \n", "L 386.960938 115.688926 \n", "\" clip-path=\"url(#p6ab48af28d)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.501409 \n", "L 52.160938 7.421409 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 386.**********.501409 \n", "L 386.960938 7.421409 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.501409 \n", "L 386.**********.501409 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 7.421409 \n", "L 386.960938 7.421409 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 59.160938 165.501409 \n", "L 153.885938 165.501409 \n", "Q 155.885938 165.501409 155.885938 163.501409 \n", "L 155.885938 135.145159 \n", "Q 155.885938 133.145159 153.885938 133.145159 \n", "L 59.160938 133.145159 \n", "Q 57.160938 133.145159 57.160938 135.145159 \n", "L 57.160938 163.501409 \n", "Q 57.160938 165.501409 59.160938 165.501409 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 61.160938 141.243597 \n", "L 71.160938 141.243597 \n", "L 81.160938 141.243597 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- data -->\n", "     <g transform=\"translate(89.160938 144.743597)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-64\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"163.964844\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 61.160938 155.921722 \n", "L 71.160938 155.921722 \n", "L 81.160938 155.921722 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- 1-step preds -->\n", "     <g transform=\"translate(89.160938 159.421722)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"99.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"151.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"191.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"252.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"316.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"347.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"411.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"450.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"511.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"575.142578\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p6ab48af28d\">\n", "   <rect x=\"52.160938\" y=\"7.421409\" width=\"334.8\" height=\"163.08\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 432x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["onestep_preds = net(features)\n", "d2l.plot([time, time[tau:]],\n", "         [x.detach().numpy(), onestep_preds.detach().numpy()], 'time',\n", "         'x', legend=['data', '1-step preds'], xlim=[1, 1000],\n", "         figsize=(6, 3))"]}, {"cell_type": "markdown", "id": "8170de92", "metadata": {"origin_pos": 23}, "source": ["正如我们所料，单步预测效果不错。\n", "即使这些预测的时间步超过了$600+4$（`n_train + tau`），\n", "其结果看起来仍然是可信的。\n", "然而有一个小问题：如果数据观察序列的时间步只到$604$，\n", "我们需要一步一步地向前迈进：\n", "$$\n", "\\hat{x}_{605} = f(x_{601}, x_{602}, x_{603}, x_{604}), \\\\\n", "\\hat{x}_{606} = f(x_{602}, x_{603}, x_{604}, \\hat{x}_{605}), \\\\\n", "\\hat{x}_{607} = f(x_{603}, x_{604}, \\hat{x}_{605}, \\hat{x}_{606}),\\\\\n", "\\hat{x}_{608} = f(x_{604}, \\hat{x}_{605}, \\hat{x}_{606}, \\hat{x}_{607}),\\\\\n", "\\hat{x}_{609} = f(\\hat{x}_{605}, \\hat{x}_{606}, \\hat{x}_{607}, \\hat{x}_{608}),\\\\\n", "\\ldots\n", "$$\n", "\n", "通常，对于直到$x_t$的观测序列，其在时间步$t+k$处的预测输出$\\hat{x}_{t+k}$\n", "称为$k$*步预测*（$k$-step-ahead-prediction）。\n", "由于我们的观察已经到了$x_{604}$，它的$k$步预测是$\\hat{x}_{604+k}$。\n", "换句话说，我们必须使用我们自己的预测（而不是原始数据）来[**进行多步预测**]。\n", "让我们看看效果如何。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "89ba8713", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:33.710400Z", "iopub.status.busy": "2023-08-18T07:00:33.709599Z", "iopub.status.idle": "2023-08-18T07:00:33.765864Z", "shell.execute_reply": "2023-08-18T07:00:33.764650Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [], "source": ["multistep_preds = torch.zeros(T)\n", "multistep_preds[: n_train + tau] = x[: n_train + tau]\n", "for i in range(n_train + tau, T):\n", "    multistep_preds[i] = net(\n", "        multistep_preds[i - tau:i].reshape((1, -1)))"]}, {"cell_type": "code", "execution_count": 9, "id": "f29da1fa", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:33.770459Z", "iopub.status.busy": "2023-08-18T07:00:33.769838Z", "iopub.status.idle": "2023-08-18T07:00:34.140732Z", "shell.execute_reply": "2023-08-18T07:00:34.139616Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"406.885938pt\" height=\"208.057659pt\" viewBox=\"0 0 406.**********.057659\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:00:34.059922</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 208.057659 \n", "L 406.**********.057659 \n", "L 406.885938 0 \n", "L 0 0 \n", "L 0 208.057659 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.501409 \n", "L 386.**********.501409 \n", "L 386.960938 7.421409 \n", "L 52.160938 7.421409 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 118.852829 170.501409 \n", "L 118.852829 7.421409 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m100db56e04\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m100db56e04\" x=\"118.852829\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(109.309079 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 185.879856 170.501409 \n", "L 185.879856 7.421409 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m100db56e04\" x=\"185.879856\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(176.336106 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 252.906883 170.501409 \n", "L 252.906883 7.421409 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m100db56e04\" x=\"252.906883\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(243.363133 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 319.93391 170.501409 \n", "L 319.93391 7.421409 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m100db56e04\" x=\"319.93391\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(310.39016 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 386.**********.501409 \n", "L 386.960938 7.421409 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m100db56e04\" x=\"386.960938\" y=\"170.501409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(374.235937 185.099847)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- time -->\n", "     <g transform=\"translate(208.264844 198.777972)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 52.**********.350493 \n", "L 386.**********.350493 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mf86e71d4b5\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf86e71d4b5\" x=\"52.160938\" y=\"170.350493\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1.5 -->\n", "      <g transform=\"translate(20.878125 174.149712)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 52.160938 143.791947 \n", "L 386.960938 143.791947 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mf86e71d4b5\" x=\"52.160938\" y=\"143.791947\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 147.591166)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 52.160938 117.233402 \n", "L 386.960938 117.233402 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mf86e71d4b5\" x=\"52.160938\" y=\"117.233402\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 121.03262)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 52.160938 90.674856 \n", "L 386.960938 90.674856 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mf86e71d4b5\" x=\"52.160938\" y=\"90.674856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257812 94.474075)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 52.160938 64.11631 \n", "L 386.960938 64.11631 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mf86e71d4b5\" x=\"52.160938\" y=\"64.11631\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257812 67.915529)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 52.160938 37.557764 \n", "L 386.960938 37.557764 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#mf86e71d4b5\" x=\"52.160938\" y=\"37.557764\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257812 41.356983)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 52.160938 10.999219 \n", "L 386.960938 10.999219 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#mf86e71d4b5\" x=\"52.160938\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(29.257812 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- x -->\n", "     <g transform=\"translate(14.798438 91.920784)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 52.160938 84.54498 \n", "L 52.496073 66.292182 \n", "L 52.831208 84.811538 \n", "L 53.166343 85.913688 \n", "L 53.501478 99.10471 \n", "L 53.836613 98.383487 \n", "L 54.171748 81.436069 \n", "L 54.506883 72.074729 \n", "L 54.842019 103.48116 \n", "L 55.177154 65.125338 \n", "L 55.847424 83.059261 \n", "L 56.182559 53.359426 \n", "L 56.852829 100.104027 \n", "L 57.187965 100.759161 \n", "L 57.5231 80.859436 \n", "L 57.858235 74.808693 \n", "L 58.19337 77.262553 \n", "L 58.528505 83.659933 \n", "L 58.86364 69.296344 \n", "L 59.198775 67.966281 \n", "L 59.53391 86.256409 \n", "L 59.869046 59.839099 \n", "L 60.204181 58.998411 \n", "L 60.539316 65.198439 \n", "L 60.874451 64.879003 \n", "L 61.209586 71.720712 \n", "L 61.544721 98.535389 \n", "L 61.879856 79.315594 \n", "L 62.214992 89.378511 \n", "L 62.550127 81.74239 \n", "L 62.885262 80.90693 \n", "L 63.220397 77.151232 \n", "L 63.555532 85.20364 \n", "L 63.890667 103.590006 \n", "L 64.225802 83.896333 \n", "L 64.560938 72.609344 \n", "L 64.896073 81.028173 \n", "L 65.231208 70.004145 \n", "L 65.566343 76.238885 \n", "L 65.901478 68.938553 \n", "L 66.236613 64.290176 \n", "L 66.571748 68.481729 \n", "L 66.906883 67.475493 \n", "L 67.242019 89.425816 \n", "L 67.577154 81.024249 \n", "L 68.247424 58.698031 \n", "L 68.582559 55.445919 \n", "L 68.917694 66.109903 \n", "L 69.252829 54.629493 \n", "L 69.587965 74.563535 \n", "L 69.9231 60.821665 \n", "L 70.258235 55.828289 \n", "L 70.59337 58.586673 \n", "L 70.928505 52.777573 \n", "L 71.26364 98.160334 \n", "L 71.598775 46.326426 \n", "L 71.93391 78.336651 \n", "L 72.269046 45.615388 \n", "L 72.604181 62.458218 \n", "L 72.939316 39.487375 \n", "L 73.274451 41.38225 \n", "L 73.609586 62.194979 \n", "L 73.944721 64.427513 \n", "L 74.279856 54.49569 \n", "L 74.614992 54.392354 \n", "L 74.950127 57.777462 \n", "L 75.285262 54.386054 \n", "L 75.620397 56.643145 \n", "L 75.955532 67.783528 \n", "L 76.625802 55.971055 \n", "L 76.960938 14.834137 \n", "L 77.296073 48.76046 \n", "L 77.631208 71.155554 \n", "L 77.966343 39.456873 \n", "L 78.301478 39.34624 \n", "L 78.636613 48.47593 \n", "L 78.971748 60.788507 \n", "L 79.306883 53.261684 \n", "L 79.642019 63.45615 \n", "L 79.977154 39.988756 \n", "L 80.312289 27.673231 \n", "L 80.982559 48.208837 \n", "L 81.317694 42.292745 \n", "L 81.652829 61.904525 \n", "L 81.987965 40.472104 \n", "L 82.3231 49.66569 \n", "L 82.658235 52.429358 \n", "L 82.99337 50.912636 \n", "L 83.328505 52.493283 \n", "L 83.66364 43.657537 \n", "L 83.998775 44.336294 \n", "L 84.33391 49.57625 \n", "L 84.669046 34.110678 \n", "L 85.004181 56.486585 \n", "L 85.339316 45.579434 \n", "L 85.674451 51.652064 \n", "L 86.009586 30.714363 \n", "L 86.344721 41.407806 \n", "L 86.679856 36.584649 \n", "L 87.014992 55.312844 \n", "L 87.350127 58.702464 \n", "L 87.685262 35.968914 \n", "L 88.020397 43.857614 \n", "L 88.355532 62.821678 \n", "L 88.690667 66.993342 \n", "L 89.025802 62.461916 \n", "L 89.360938 37.760624 \n", "L 89.696073 35.707742 \n", "L 90.031208 45.950081 \n", "L 90.366343 47.301185 \n", "L 90.701478 37.908323 \n", "L 91.036613 32.293912 \n", "L 91.706883 48.448483 \n", "L 92.042019 47.022885 \n", "L 92.377154 31.093988 \n", "L 93.047424 65.55191 \n", "L 93.382559 42.302215 \n", "L 93.717694 53.448343 \n", "L 94.052829 68.807062 \n", "L 94.387965 48.180641 \n", "L 95.058235 33.589931 \n", "L 95.39337 45.27996 \n", "L 95.728505 42.160285 \n", "L 96.06364 47.417382 \n", "L 96.398775 56.077208 \n", "L 96.73391 40.657224 \n", "L 97.069046 18.049109 \n", "L 97.404181 43.602625 \n", "L 97.739316 58.45975 \n", "L 98.074451 43.442747 \n", "L 98.409586 55.452257 \n", "L 99.079856 46.28057 \n", "L 99.750127 45.987003 \n", "L 100.085262 47.122267 \n", "L 100.420397 38.404831 \n", "L 100.755532 43.397831 \n", "L 101.090667 44.618533 \n", "L 101.425802 35.511271 \n", "L 101.760938 41.99563 \n", "L 102.096073 37.845566 \n", "L 102.431208 45.158929 \n", "L 102.766343 22.130071 \n", "L 103.101478 39.837537 \n", "L 103.436613 36.497279 \n", "L 103.771748 38.494775 \n", "L 104.106883 26.027803 \n", "L 104.442019 44.34421 \n", "L 105.112289 33.091402 \n", "L 105.447424 24.437015 \n", "L 105.782559 56.886881 \n", "L 106.117694 38.053995 \n", "L 106.452829 30.723468 \n", "L 106.787965 39.930808 \n", "L 107.1231 24.601641 \n", "L 107.458235 28.128663 \n", "L 107.79337 34.541492 \n", "L 108.128505 33.015949 \n", "L 108.46364 37.567604 \n", "L 108.798775 35.114498 \n", "L 109.13391 48.4828 \n", "L 109.469046 38.126348 \n", "L 109.804181 53.879936 \n", "L 110.139316 32.273579 \n", "L 110.474451 57.036501 \n", "L 110.809586 40.773041 \n", "L 111.144721 38.72511 \n", "L 111.479856 30.786573 \n", "L 111.814992 26.671215 \n", "L 112.150127 51.505379 \n", "L 112.485262 50.290762 \n", "L 112.820397 23.754134 \n", "L 113.155532 48.554726 \n", "L 113.490667 39.448543 \n", "L 113.825802 38.972497 \n", "L 114.160938 47.945709 \n", "L 114.496073 29.878431 \n", "L 114.831208 41.213286 \n", "L 115.166343 43.724394 \n", "L 115.501478 21.598489 \n", "L 115.836613 35.318523 \n", "L 116.171748 43.404929 \n", "L 116.506883 25.732989 \n", "L 116.842019 17.022487 \n", "L 117.177154 33.884782 \n", "L 117.847424 32.208233 \n", "L 118.182559 27.515594 \n", "L 118.517694 35.865955 \n", "L 118.852829 32.401595 \n", "L 119.187965 33.34022 \n", "L 119.5231 29.688773 \n", "L 120.19337 59.032642 \n", "L 120.528505 36.259175 \n", "L 120.86364 34.849223 \n", "L 121.198775 46.45888 \n", "L 121.869046 20.24243 \n", "L 122.204181 54.531077 \n", "L 122.539316 42.129382 \n", "L 122.874451 25.504123 \n", "L 123.544721 54.851428 \n", "L 123.879856 47.974741 \n", "L 124.214992 46.127125 \n", "L 124.550127 48.499871 \n", "L 124.885262 46.645796 \n", "L 125.220397 40.449356 \n", "L 125.555532 45.795994 \n", "L 125.890667 36.663242 \n", "L 126.560938 60.546142 \n", "L 126.896073 46.543897 \n", "L 127.231208 63.218463 \n", "L 127.566343 37.169749 \n", "L 127.901478 41.355035 \n", "L 128.236613 57.502195 \n", "L 128.571748 41.818389 \n", "L 128.906883 61.396197 \n", "L 129.242019 45.829772 \n", "L 129.577154 53.28837 \n", "L 129.912289 56.326143 \n", "L 130.247424 68.245998 \n", "L 130.582559 42.339007 \n", "L 130.917694 52.110289 \n", "L 131.252829 48.027598 \n", "L 131.587965 62.824407 \n", "L 131.9231 50.029334 \n", "L 132.258235 66.397525 \n", "L 132.59337 64.924465 \n", "L 132.928505 75.628488 \n", "L 133.26364 32.170411 \n", "L 133.598775 59.250601 \n", "L 133.93391 69.364114 \n", "L 134.269046 67.536235 \n", "L 134.604181 72.552055 \n", "L 134.939316 57.637916 \n", "L 135.274451 63.102064 \n", "L 135.609586 63.681656 \n", "L 135.944721 63.737821 \n", "L 136.279856 71.969288 \n", "L 136.614992 59.402846 \n", "L 136.950127 71.931375 \n", "L 137.285262 63.189817 \n", "L 137.620397 40.128976 \n", "L 137.955532 67.161438 \n", "L 138.290667 59.840476 \n", "L 138.625802 62.356222 \n", "L 138.960938 72.257517 \n", "L 139.296073 65.887155 \n", "L 139.631208 50.920867 \n", "L 139.966343 52.025699 \n", "L 140.636613 58.285828 \n", "L 140.971748 69.019007 \n", "L 141.306883 67.073205 \n", "L 141.642019 56.187737 \n", "L 141.977154 54.274711 \n", "L 142.312289 50.948918 \n", "L 142.647424 76.034604 \n", "L 142.982559 58.561037 \n", "L 143.317694 80.419717 \n", "L 143.652829 51.130683 \n", "L 143.987965 57.147936 \n", "L 144.3231 83.284041 \n", "L 144.658235 73.199675 \n", "L 144.99337 79.227366 \n", "L 145.328505 59.683457 \n", "L 145.998775 78.914328 \n", "L 146.33391 62.272192 \n", "L 146.669046 62.786212 \n", "L 147.339316 67.693742 \n", "L 147.674451 61.868831 \n", "L 148.009586 63.724188 \n", "L 148.344721 62.815998 \n", "L 148.679856 66.97589 \n", "L 149.014992 77.427063 \n", "L 149.350127 74.540118 \n", "L 149.685262 66.71499 \n", "L 150.355532 91.899918 \n", "L 150.690667 84.57065 \n", "L 151.025802 89.230496 \n", "L 151.360938 74.574653 \n", "L 151.696073 103.44619 \n", "L 152.031208 79.944455 \n", "L 152.366343 88.574606 \n", "L 152.701478 76.030621 \n", "L 153.036613 92.332349 \n", "L 153.371748 82.288451 \n", "L 154.042019 88.579158 \n", "L 154.377154 115.859711 \n", "L 154.712289 83.007429 \n", "L 155.047424 77.758114 \n", "L 155.382559 62.213402 \n", "L 155.717694 89.966695 \n", "L 156.052829 98.687412 \n", "L 156.387965 76.073346 \n", "L 156.7231 98.614911 \n", "L 157.058235 84.218717 \n", "L 157.39337 77.346388 \n", "L 157.728505 76.233927 \n", "L 158.73391 100.880805 \n", "L 159.069046 94.014133 \n", "L 159.404181 99.545176 \n", "L 159.739316 98.434284 \n", "L 160.074451 89.38885 \n", "L 160.409586 73.217774 \n", "L 160.744721 93.973039 \n", "L 161.079856 102.128746 \n", "L 161.414992 101.767972 \n", "L 161.750127 88.115443 \n", "L 162.085262 110.822666 \n", "L 162.420397 91.882926 \n", "L 162.755532 108.550954 \n", "L 163.090667 118.493844 \n", "L 163.425802 118.593583 \n", "L 163.760938 107.257128 \n", "L 164.096073 110.920942 \n", "L 164.431208 99.254481 \n", "L 164.766343 95.16897 \n", "L 165.101478 105.807878 \n", "L 165.436613 108.122016 \n", "L 165.771748 111.781756 \n", "L 166.106883 113.494228 \n", "L 166.442019 102.126682 \n", "L 166.777154 99.613707 \n", "L 167.447424 110.190515 \n", "L 167.782559 83.86778 \n", "L 168.787965 121.00769 \n", "L 169.1231 115.176999 \n", "L 169.458235 102.356077 \n", "L 169.79337 127.873204 \n", "L 170.128505 110.000922 \n", "L 170.46364 116.987944 \n", "L 170.798775 111.333733 \n", "L 171.13391 119.059185 \n", "L 171.804181 103.358708 \n", "L 172.139316 121.87097 \n", "L 172.474451 131.489488 \n", "L 172.809586 111.716055 \n", "L 173.144721 128.107711 \n", "L 173.479856 130.792716 \n", "L 173.814992 120.034606 \n", "L 174.150127 98.322501 \n", "L 174.485262 107.080946 \n", "L 174.820397 146.124745 \n", "L 175.155532 99.137816 \n", "L 175.490667 128.712776 \n", "L 175.825802 134.998334 \n", "L 176.160938 114.260019 \n", "L 176.496073 104.568567 \n", "L 176.831208 133.608598 \n", "L 177.166343 135.818832 \n", "L 177.501478 121.669317 \n", "L 177.836613 119.3673 \n", "L 178.171748 106.027033 \n", "L 178.506883 110.81337 \n", "L 178.842019 117.522824 \n", "L 179.177154 127.301239 \n", "L 179.847424 120.006299 \n", "L 180.182559 135.072923 \n", "L 180.517694 118.60455 \n", "L 181.187965 147.460098 \n", "L 181.858235 129.856946 \n", "L 182.19337 131.289402 \n", "L 182.528505 135.89961 \n", "L 182.86364 117.01423 \n", "L 183.198775 120.215339 \n", "L 183.53391 127.848417 \n", "L 183.869046 151.662921 \n", "L 184.204181 127.199907 \n", "L 184.539316 128.242413 \n", "L 184.874451 121.736731 \n", "L 185.209586 163.088682 \n", "L 185.544721 135.269748 \n", "L 185.879856 132.511864 \n", "L 186.214992 119.013873 \n", "L 186.550127 136.87706 \n", "L 186.885262 135.22574 \n", "L 187.220397 132.584803 \n", "L 187.890667 136.21814 \n", "L 188.225802 140.711582 \n", "L 188.560938 148.430649 \n", "L 188.896073 120.308075 \n", "L 189.231208 127.357328 \n", "L 189.566343 145.992272 \n", "L 189.901478 120.722827 \n", "L 190.571748 142.161869 \n", "L 190.906883 129.423141 \n", "L 191.242019 130.38629 \n", "L 191.577154 153.791281 \n", "L 191.912289 138.172743 \n", "L 192.247424 153.385042 \n", "L 192.582559 137.629475 \n", "L 192.917694 152.421773 \n", "L 193.252829 155.887405 \n", "L 193.587965 134.414592 \n", "L 193.9231 147.05353 \n", "L 194.258235 127.727342 \n", "L 194.59337 137.282238 \n", "L 195.26364 129.974343 \n", "L 195.598775 136.190324 \n", "L 195.93391 120.239878 \n", "L 196.269046 143.37572 \n", "L 196.604181 113.899784 \n", "L 196.939316 148.312709 \n", "L 197.274451 140.686681 \n", "L 197.609586 148.542689 \n", "L 198.279856 131.408615 \n", "L 198.614992 129.228288 \n", "L 198.950127 153.213849 \n", "L 199.285262 123.951214 \n", "L 199.620397 132.082428 \n", "L 199.955532 123.51233 \n", "L 200.290667 136.8864 \n", "L 200.625802 122.461076 \n", "L 200.960938 138.790321 \n", "L 201.296073 138.98152 \n", "L 201.631208 120.75053 \n", "L 201.966343 146.110947 \n", "L 202.301478 134.906814 \n", "L 202.636613 134.366253 \n", "L 202.971748 147.911985 \n", "L 203.306883 144.294744 \n", "L 203.642019 152.524498 \n", "L 203.977154 132.817513 \n", "L 204.312289 156.474013 \n", "L 204.647424 143.204694 \n", "L 204.982559 149.870855 \n", "L 205.652829 153.701151 \n", "L 205.987965 150.154975 \n", "L 206.3231 125.501961 \n", "L 206.658235 131.02527 \n", "L 206.99337 140.960295 \n", "L 207.328505 138.13237 \n", "L 207.66364 145.002572 \n", "L 207.998775 129.310677 \n", "L 208.33391 124.604051 \n", "L 208.669046 147.347565 \n", "L 209.004181 126.25501 \n", "L 209.674451 151.431858 \n", "L 210.009586 123.039044 \n", "L 210.344721 150.939376 \n", "L 210.679856 143.325304 \n", "L 211.014992 132.17177 \n", "L 211.350127 148.969197 \n", "L 211.685262 149.320759 \n", "L 212.020397 141.738102 \n", "L 212.355532 150.587169 \n", "L 212.690667 146.005113 \n", "L 213.025802 138.521516 \n", "L 213.360938 140.399643 \n", "L 213.696073 129.195826 \n", "L 214.031208 145.249415 \n", "L 214.366343 142.239097 \n", "L 214.701478 127.185967 \n", "L 215.036613 154.924769 \n", "L 215.371748 129.598668 \n", "L 215.706883 131.649135 \n", "L 216.042019 138.36973 \n", "L 216.377154 141.951872 \n", "L 216.712289 152.811631 \n", "L 217.047424 152.959522 \n", "L 217.382559 129.085092 \n", "L 217.717694 140.805873 \n", "L 218.052829 146.707344 \n", "L 218.387965 144.099425 \n", "L 218.7231 152.548021 \n", "L 219.058235 133.970604 \n", "L 219.728505 159.501303 \n", "L 220.06364 139.683077 \n", "L 220.398775 151.462606 \n", "L 220.73391 151.664409 \n", "L 221.069046 133.682382 \n", "L 221.404181 140.461843 \n", "L 221.739316 128.899702 \n", "L 222.074451 130.475623 \n", "L 222.409586 133.215124 \n", "L 222.744721 146.958359 \n", "L 223.414992 136.786154 \n", "L 223.750127 139.625246 \n", "L 224.085262 134.907495 \n", "L 224.420397 134.545972 \n", "L 224.755532 150.005158 \n", "L 225.090667 115.079702 \n", "L 225.425802 136.75922 \n", "L 226.096073 117.987403 \n", "L 226.431208 137.910039 \n", "L 226.766343 135.242349 \n", "L 227.101478 135.837217 \n", "L 227.436613 118.287118 \n", "L 227.771748 144.755553 \n", "L 228.106883 141.8602 \n", "L 228.442019 130.180565 \n", "L 228.777154 138.524929 \n", "L 229.112289 140.048752 \n", "L 229.447424 135.12993 \n", "L 229.782559 139.822553 \n", "L 230.117694 139.434325 \n", "L 230.787965 123.444846 \n", "L 231.1231 131.427767 \n", "L 231.458235 110.097899 \n", "L 232.128505 137.08054 \n", "L 232.46364 126.249017 \n", "L 232.798775 141.510877 \n", "L 233.13391 137.45232 \n", "L 233.469046 114.554762 \n", "L 233.804181 145.135545 \n", "L 234.139316 134.088118 \n", "L 234.474451 137.838452 \n", "L 234.809586 96.471317 \n", "L 235.144721 147.221272 \n", "L 235.479856 134.19828 \n", "L 235.814992 147.135226 \n", "L 236.150127 113.259693 \n", "L 236.485262 134.816835 \n", "L 236.820397 116.322069 \n", "L 237.155532 114.770414 \n", "L 237.490667 114.630099 \n", "L 237.825802 131.96475 \n", "L 238.160938 130.031502 \n", "L 238.496073 112.495885 \n", "L 238.831208 124.354268 \n", "L 239.166343 119.680666 \n", "L 239.501478 124.2743 \n", "L 239.836613 123.76343 \n", "L 240.171748 112.985848 \n", "L 240.506883 120.805913 \n", "L 240.842019 134.084217 \n", "L 241.177154 115.087109 \n", "L 241.512289 141.467312 \n", "L 242.182559 116.489395 \n", "L 242.517694 112.828457 \n", "L 242.852829 152.177773 \n", "L 243.187965 130.510674 \n", "L 243.5231 143.278298 \n", "L 243.858235 103.709014 \n", "L 244.19337 116.45574 \n", "L 244.528505 121.305927 \n", "L 244.86364 107.213508 \n", "L 245.198775 125.940038 \n", "L 245.53391 102.937015 \n", "L 245.869046 90.60603 \n", "L 246.204181 112.902119 \n", "L 246.539316 98.995809 \n", "L 246.874451 123.494806 \n", "L 247.209586 114.318605 \n", "L 247.544721 124.681641 \n", "L 247.879856 106.728178 \n", "L 248.214992 124.086001 \n", "L 248.550127 114.069273 \n", "L 248.885262 117.753865 \n", "L 249.220397 123.412163 \n", "L 249.555532 106.866709 \n", "L 249.890667 105.825302 \n", "L 250.225802 105.935285 \n", "L 250.896073 119.196961 \n", "L 251.566343 94.493911 \n", "L 251.901478 94.749753 \n", "L 252.236613 96.761404 \n", "L 252.571748 97.836934 \n", "L 252.906883 90.026585 \n", "L 253.242019 101.853703 \n", "L 253.577154 108.440726 \n", "L 253.912289 95.860411 \n", "L 254.247424 99.802056 \n", "L 254.582559 105.959499 \n", "L 254.917694 102.042288 \n", "L 255.252829 106.699025 \n", "L 255.587965 115.402274 \n", "L 255.9231 98.637104 \n", "L 256.258235 111.49276 \n", "L 256.59337 112.597103 \n", "L 256.928505 114.440815 \n", "L 257.26364 101.856277 \n", "L 257.598775 78.586715 \n", "L 257.93391 108.447629 \n", "L 258.269046 100.257078 \n", "L 258.604181 73.004778 \n", "L 259.274451 100.409904 \n", "L 259.609586 100.704571 \n", "L 259.944721 94.561343 \n", "L 260.279856 96.361888 \n", "L 260.614992 104.163943 \n", "L 260.950127 82.582756 \n", "L 261.285262 84.910339 \n", "L 261.620397 92.937649 \n", "L 261.955532 78.600805 \n", "L 262.290667 108.856442 \n", "L 262.625802 70.706696 \n", "L 263.296073 100.137695 \n", "L 263.631208 98.349311 \n", "L 263.966343 101.688611 \n", "L 264.636613 73.478121 \n", "L 264.971748 93.030623 \n", "L 265.306883 87.804738 \n", "L 265.642019 76.961937 \n", "L 265.977154 87.915583 \n", "L 266.312289 87.194329 \n", "L 266.647424 98.705772 \n", "L 266.982559 79.789276 \n", "L 267.317694 97.41211 \n", "L 267.652829 80.073273 \n", "L 267.987965 89.77167 \n", "L 268.3231 77.953791 \n", "L 268.658235 95.129084 \n", "L 268.99337 63.973855 \n", "L 269.328505 77.878982 \n", "L 269.66364 75.280722 \n", "L 269.998775 98.424471 \n", "L 270.33391 77.318166 \n", "L 270.669046 76.119075 \n", "L 271.004181 73.714889 \n", "L 271.339316 79.33557 \n", "L 271.674451 87.517092 \n", "L 272.009586 67.913112 \n", "L 272.344721 79.826816 \n", "L 272.679856 71.586511 \n", "L 273.014992 73.29953 \n", "L 273.350127 73.599557 \n", "L 273.685262 75.684886 \n", "L 274.355532 88.35781 \n", "L 274.690667 71.129042 \n", "L 275.025802 69.077664 \n", "L 275.360937 70.295271 \n", "L 275.696073 69.721285 \n", "L 276.031208 58.216042 \n", "L 276.701478 68.842849 \n", "L 277.371748 70.842253 \n", "L 277.706883 68.00532 \n", "L 278.042019 76.036609 \n", "L 278.377154 66.113186 \n", "L 278.712289 69.094534 \n", "L 279.047424 71.033317 \n", "L 279.382559 47.360001 \n", "L 279.717694 80.559478 \n", "L 280.052829 56.78371 \n", "L 280.387965 56.335056 \n", "L 280.7231 54.642885 \n", "L 281.058235 71.099381 \n", "L 281.39337 47.294242 \n", "L 281.728505 65.825622 \n", "L 282.06364 60.604086 \n", "L 282.398775 51.714356 \n", "L 282.73391 70.250516 \n", "L 283.069046 49.560585 \n", "L 283.404181 78.321167 \n", "L 283.739316 55.129487 \n", "L 284.074451 66.531915 \n", "L 284.409586 44.555127 \n", "L 284.744721 69.711323 \n", "L 285.079856 38.533116 \n", "L 285.414992 60.491205 \n", "L 285.750127 54.495392 \n", "L 286.420397 70.427234 \n", "L 286.755532 54.823032 \n", "L 287.090667 57.277879 \n", "L 287.425802 62.248865 \n", "L 287.760938 52.395244 \n", "L 288.096073 48.910353 \n", "L 288.431208 83.917016 \n", "L 288.766343 50.17403 \n", "L 289.101478 60.322256 \n", "L 289.436613 48.247469 \n", "L 289.771748 60.027724 \n", "L 290.106883 53.737671 \n", "L 290.442019 49.999339 \n", "L 290.777154 60.160108 \n", "L 291.112289 51.158177 \n", "L 291.447424 59.930606 \n", "L 291.782559 59.469338 \n", "L 292.117694 64.98612 \n", "L 292.452829 26.650136 \n", "L 292.787965 47.924797 \n", "L 293.458235 46.311647 \n", "L 293.79337 49.94495 \n", "L 294.128505 49.372402 \n", "L 294.46364 32.554343 \n", "L 294.798775 35.632707 \n", "L 295.13391 51.261351 \n", "L 295.469046 49.567718 \n", "L 295.804181 49.512281 \n", "L 296.139316 58.009012 \n", "L 296.474451 43.344854 \n", "L 296.809586 42.644814 \n", "L 297.144721 61.054096 \n", "L 297.479856 36.180183 \n", "L 297.814992 61.900111 \n", "L 298.150127 31.626311 \n", "L 298.485262 66.808516 \n", "L 298.820397 45.825175 \n", "L 299.155532 33.770046 \n", "L 299.490667 33.26604 \n", "L 299.825802 38.356743 \n", "L 300.160938 49.610674 \n", "L 300.496073 44.605866 \n", "L 300.831208 56.573546 \n", "L 301.166343 43.94207 \n", "L 301.501478 37.184769 \n", "L 301.836613 33.47596 \n", "L 302.171748 35.821428 \n", "L 302.506883 49.941274 \n", "L 302.842019 37.220019 \n", "L 303.177154 37.262267 \n", "L 303.512289 30.17358 \n", "L 303.847424 44.169831 \n", "L 304.182559 34.122697 \n", "L 304.517694 34.20174 \n", "L 304.852829 44.261868 \n", "L 305.187965 34.211136 \n", "L 305.5231 45.127415 \n", "L 305.858235 37.99762 \n", "L 306.19337 16.111235 \n", "L 306.528505 47.345465 \n", "L 306.86364 34.656476 \n", "L 307.198775 39.697478 \n", "L 307.53391 26.181121 \n", "L 307.869046 39.560076 \n", "L 308.204181 34.552073 \n", "L 308.539316 22.216763 \n", "L 308.874451 25.561821 \n", "L 309.209586 48.959109 \n", "L 309.544721 50.98321 \n", "L 309.879856 26.451303 \n", "L 310.214992 43.726569 \n", "L 310.885262 28.089113 \n", "L 311.220397 25.574409 \n", "L 311.555532 38.142704 \n", "L 311.890667 44.763945 \n", "L 312.225802 32.455195 \n", "L 312.560938 34.526327 \n", "L 312.896073 39.553988 \n", "L 313.231208 62.248349 \n", "L 313.566343 30.036466 \n", "L 313.901478 37.407264 \n", "L 314.236613 40.906517 \n", "L 314.571748 38.999088 \n", "L 315.242019 51.798942 \n", "L 315.577154 48.931005 \n", "L 315.912289 42.526024 \n", "L 316.247424 53.564679 \n", "L 316.582559 52.725125 \n", "L 316.917694 42.300451 \n", "L 317.252829 38.816348 \n", "L 317.587965 28.223404 \n", "L 317.9231 50.932012 \n", "L 318.258235 24.784252 \n", "L 318.59337 55.014139 \n", "L 319.26364 25.045303 \n", "L 319.598775 49.809744 \n", "L 319.93391 35.336272 \n", "L 320.269046 30.886398 \n", "L 320.604181 60.08805 \n", "L 320.939316 34.21857 \n", "L 321.274451 58.871865 \n", "L 321.609586 41.220796 \n", "L 321.944721 38.172702 \n", "L 322.279856 51.174998 \n", "L 322.614992 54.562208 \n", "L 322.950127 47.563975 \n", "L 323.285262 45.523839 \n", "L 323.620397 55.554181 \n", "L 323.955532 43.6036 \n", "L 324.290667 38.863991 \n", "L 324.625802 49.973406 \n", "L 324.960938 34.144631 \n", "L 325.296073 57.794156 \n", "L 325.631208 47.22805 \n", "L 325.966343 43.992854 \n", "L 326.301478 53.849681 \n", "L 326.636613 41.126233 \n", "L 326.971748 44.787956 \n", "L 327.306883 22.839292 \n", "L 327.642019 47.170077 \n", "L 327.977154 36.975539 \n", "L 328.312289 44.681575 \n", "L 328.647424 40.202542 \n", "L 328.982559 20.921631 \n", "L 329.317694 48.507517 \n", "L 329.652829 42.868674 \n", "L 329.987965 28.547503 \n", "L 331.328505 46.561855 \n", "L 331.66364 35.551309 \n", "L 331.998775 40.734909 \n", "L 332.33391 24.803077 \n", "L 332.669046 47.851972 \n", "L 333.004181 54.589414 \n", "L 333.339316 38.210906 \n", "L 333.674451 51.780146 \n", "L 334.009586 51.087834 \n", "L 334.344721 55.439634 \n", "L 334.679856 40.666485 \n", "L 335.014992 43.76376 \n", "L 335.350127 40.075521 \n", "L 335.685262 59.465593 \n", "L 336.020397 42.63965 \n", "L 336.355532 73.083455 \n", "L 336.690667 44.536128 \n", "L 337.025802 44.807608 \n", "L 337.360937 56.877535 \n", "L 337.696073 49.852758 \n", "L 338.031208 29.803991 \n", "L 339.036613 60.498629 \n", "L 339.371748 26.932672 \n", "L 339.706883 24.257684 \n", "L 340.042019 57.502233 \n", "L 340.377154 71.939445 \n", "L 340.712289 51.314167 \n", "L 341.047424 66.751181 \n", "L 341.382559 32.857331 \n", "L 341.717694 85.417532 \n", "L 342.387965 37.864185 \n", "L 343.058235 51.609836 \n", "L 343.39337 53.106986 \n", "L 343.728505 66.964169 \n", "L 344.73391 39.312034 \n", "L 345.069046 63.002031 \n", "L 345.404181 51.273895 \n", "L 345.739316 55.628484 \n", "L 346.074451 48.876194 \n", "L 346.409586 56.094248 \n", "L 346.744721 56.957158 \n", "L 347.079856 74.9738 \n", "L 347.414992 68.481347 \n", "L 347.750127 50.710118 \n", "L 348.085262 87.194047 \n", "L 348.420397 69.994267 \n", "L 348.755532 66.03104 \n", "L 349.090667 55.979799 \n", "L 349.425802 74.752721 \n", "L 349.760938 63.374634 \n", "L 350.431208 75.178121 \n", "L 350.766343 58.206842 \n", "L 351.101478 62.501729 \n", "L 351.436613 56.426661 \n", "L 351.771748 77.814716 \n", "L 352.106883 63.250177 \n", "L 352.442019 59.13342 \n", "L 352.777154 62.885781 \n", "L 353.112289 78.607904 \n", "L 353.447424 46.659745 \n", "L 353.782559 60.052134 \n", "L 354.117694 78.305613 \n", "L 354.787965 64.582181 \n", "L 355.1231 46.448986 \n", "L 355.458235 86.503339 \n", "L 355.79337 69.844282 \n", "L 356.128505 75.658944 \n", "L 356.46364 67.30708 \n", "L 356.798775 73.016804 \n", "L 357.13391 40.025485 \n", "L 357.804181 91.190244 \n", "L 358.139316 67.851492 \n", "L 358.474451 59.503766 \n", "L 358.809586 59.208708 \n", "L 359.144721 70.976183 \n", "L 359.479856 73.236885 \n", "L 359.814992 79.181973 \n", "L 360.150127 73.811473 \n", "L 360.485262 64.900367 \n", "L 360.820397 92.21929 \n", "L 361.155532 65.478167 \n", "L 361.490667 75.61441 \n", "L 361.825802 59.909201 \n", "L 362.160938 83.884979 \n", "L 362.496073 92.477308 \n", "L 362.831208 98.051157 \n", "L 363.501478 68.57869 \n", "L 363.836613 88.051114 \n", "L 364.171748 80.546687 \n", "L 364.506883 96.087567 \n", "L 364.842019 89.667737 \n", "L 365.177154 73.275441 \n", "L 365.512289 117.961673 \n", "L 365.847424 83.555797 \n", "L 366.182559 87.239697 \n", "L 366.517694 98.581996 \n", "L 366.852829 98.89668 \n", "L 367.187965 80.599499 \n", "L 367.5231 110.871946 \n", "L 367.858235 86.909661 \n", "L 368.19337 105.505027 \n", "L 368.86364 76.945225 \n", "L 369.198775 78.906776 \n", "L 369.53391 89.995222 \n", "L 369.869046 90.480386 \n", "L 370.204181 70.812058 \n", "L 370.539316 109.27787 \n", "L 370.874451 91.043122 \n", "L 371.209586 103.756206 \n", "L 371.544721 99.753872 \n", "L 371.879856 100.60051 \n", "L 372.214992 108.655015 \n", "L 372.550127 102.559199 \n", "L 372.885262 88.998853 \n", "L 373.220397 108.805205 \n", "L 373.890667 93.584136 \n", "L 374.225802 108.050527 \n", "L 374.560938 92.47331 \n", "L 374.896073 127.846612 \n", "L 375.231208 111.937597 \n", "L 375.566343 110.652957 \n", "L 376.236613 93.181595 \n", "L 376.571748 116.28849 \n", "L 376.906883 92.778991 \n", "L 377.242019 96.396629 \n", "L 377.577154 89.801226 \n", "L 377.912289 115.761385 \n", "L 378.247424 105.704727 \n", "L 378.582559 109.848799 \n", "L 378.917694 94.644811 \n", "L 379.252829 95.146298 \n", "L 379.587965 110.927752 \n", "L 379.9231 115.719563 \n", "L 380.258235 103.479645 \n", "L 380.59337 117.940622 \n", "L 380.928505 117.331302 \n", "L 381.26364 106.117411 \n", "L 381.598775 107.3568 \n", "L 381.93391 110.196901 \n", "L 382.269046 126.978276 \n", "L 382.604181 112.201479 \n", "L 382.939316 108.366834 \n", "L 383.274451 99.895389 \n", "L 383.609586 114.677829 \n", "L 383.944721 112.091267 \n", "L 384.614992 103.95886 \n", "L 384.950127 110.465745 \n", "L 385.285262 101.820585 \n", "L 385.620397 125.377372 \n", "L 385.955532 109.982809 \n", "L 386.290667 111.553565 \n", "L 386.625802 117.887867 \n", "L 386.960938 144.781945 \n", "L 386.960938 144.781945 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 53.501478 82.259956 \n", "L 53.836613 84.232191 \n", "L 54.171748 95.834543 \n", "L 54.842019 91.789077 \n", "L 55.177154 79.724841 \n", "L 55.512289 83.862962 \n", "L 55.847424 78.159466 \n", "L 56.182559 77.840561 \n", "L 56.517694 64.073401 \n", "L 57.5231 86.258665 \n", "L 58.19337 93.659818 \n", "L 58.528505 79.897894 \n", "L 58.86364 79.562792 \n", "L 59.198775 74.138099 \n", "L 59.53391 73.460138 \n", "L 59.869046 75.77085 \n", "L 60.204181 67.099498 \n", "L 60.539316 66.35955 \n", "L 60.874451 66.01376 \n", "L 61.209586 61.594109 \n", "L 61.544721 65.513615 \n", "L 61.879856 77.874512 \n", "L 62.550127 83.346073 \n", "L 62.885262 89.750477 \n", "L 63.220397 82.547496 \n", "L 63.555532 82.045707 \n", "L 63.890667 81.249872 \n", "L 64.225802 85.98868 \n", "L 64.560938 92.952812 \n", "L 65.566343 75.732149 \n", "L 65.901478 75.215029 \n", "L 66.236613 72.984284 \n", "L 66.571748 68.331783 \n", "L 66.906883 69.491325 \n", "L 67.242019 66.745291 \n", "L 67.577154 74.825469 \n", "L 67.912289 77.868211 \n", "L 68.247424 73.842489 \n", "L 68.582559 72.079564 \n", "L 68.917694 64.27859 \n", "L 69.252829 62.765693 \n", "L 69.587965 56.375467 \n", "L 69.9231 64.411048 \n", "L 70.59337 59.098581 \n", "L 70.928505 61.89357 \n", "L 71.26364 55.12452 \n", "L 71.598775 69.820226 \n", "L 71.93391 59.380209 \n", "L 72.269046 69.381341 \n", "L 72.604181 62.033569 \n", "L 72.939316 58.873426 \n", "L 73.609586 46.202533 \n", "L 73.944721 53.529232 \n", "L 74.279856 51.722559 \n", "L 74.614992 52.878187 \n", "L 74.950127 57.400715 \n", "L 75.285262 57.267096 \n", "L 75.620397 53.6734 \n", "L 75.955532 54.808281 \n", "L 76.290667 59.984777 \n", "L 76.960938 58.521066 \n", "L 77.296073 42.198322 \n", "L 77.631208 52.939209 \n", "L 77.966343 50.799871 \n", "L 78.301478 38.08664 \n", "L 78.636613 48.151048 \n", "L 78.971748 49.909059 \n", "L 79.306883 47.699671 \n", "L 79.642019 48.3265 \n", "L 79.977154 56.551093 \n", "L 80.647424 42.284102 \n", "L 80.982559 43.400307 \n", "L 81.317694 38.937278 \n", "L 81.652829 37.937779 \n", "L 81.987965 49.574892 \n", "L 82.3231 44.190838 \n", "L 82.658235 48.81819 \n", "L 82.99337 50.635849 \n", "L 83.328505 46.648436 \n", "L 83.66364 50.138042 \n", "L 83.998775 47.093871 \n", "L 84.33391 45.986911 \n", "L 84.669046 46.806975 \n", "L 85.004181 38.902489 \n", "L 85.339316 49.061192 \n", "L 85.674451 44.12513 \n", "L 86.009586 47.080168 \n", "L 86.344721 41.496056 \n", "L 86.679856 43.844661 \n", "L 87.014992 37.930551 \n", "L 87.685262 47.78131 \n", "L 88.020397 41.508939 \n", "L 88.355532 48.116396 \n", "L 88.690667 52.287995 \n", "L 89.025802 52.478606 \n", "L 89.360938 57.392169 \n", "L 90.031208 48.425216 \n", "L 90.366343 45.714953 \n", "L 90.701478 40.535693 \n", "L 91.036613 39.254925 \n", "L 91.371748 38.707019 \n", "L 92.377154 40.744385 \n", "L 92.712289 37.867177 \n", "L 93.047424 47.022138 \n", "L 93.382559 50.289239 \n", "L 93.717694 42.736055 \n", "L 94.387965 59.422621 \n", "L 94.7231 49.261889 \n", "L 95.058235 49.14373 \n", "L 95.39337 44.851309 \n", "L 95.728505 42.336702 \n", "L 96.06364 38.410628 \n", "L 96.398775 42.328062 \n", "L 96.73391 47.734269 \n", "L 97.069046 42.875098 \n", "L 97.404181 35.724933 \n", "L 97.739316 45.211738 \n", "L 98.409586 38.42945 \n", "L 98.744721 50.469291 \n", "L 99.079856 50.825918 \n", "L 99.414992 46.608431 \n", "L 99.750127 48.15113 \n", "L 100.085262 45.876547 \n", "L 100.420397 44.904795 \n", "L 100.755532 41.360221 \n", "L 101.090667 43.022619 \n", "L 101.425802 42.026555 \n", "L 101.760938 37.988081 \n", "L 102.096073 41.34365 \n", "L 102.431208 37.721356 \n", "L 102.766343 40.759446 \n", "L 103.101478 31.873426 \n", "L 103.436613 40.917623 \n", "L 103.771748 34.076834 \n", "L 104.106883 35.507978 \n", "L 104.442019 32.525753 \n", "L 104.777154 40.171375 \n", "L 105.112289 34.59061 \n", "L 105.447424 35.218015 \n", "L 105.782559 32.926648 \n", "L 106.117694 44.561617 \n", "L 106.452829 34.545494 \n", "L 106.787965 37.306141 \n", "L 107.1231 41.411029 \n", "L 107.458235 30.233019 \n", "L 107.79337 32.697865 \n", "L 108.128505 32.474287 \n", "L 108.46364 30.637713 \n", "L 108.798775 34.582973 \n", "L 109.13391 34.336752 \n", "L 109.469046 40.446522 \n", "L 109.804181 36.836379 \n", "L 110.139316 45.806742 \n", "L 110.474451 38.738344 \n", "L 110.809586 49.60236 \n", "L 111.144721 42.955572 \n", "L 111.479856 41.749649 \n", "L 111.814992 38.704888 \n", "L 112.150127 33.27954 \n", "L 112.485262 41.358752 \n", "L 112.820397 38.719601 \n", "L 113.155532 33.87139 \n", "L 113.490667 48.538535 \n", "L 113.825802 38.340558 \n", "L 114.160938 38.297747 \n", "L 114.496073 43.362846 \n", "L 114.831208 34.887672 \n", "L 115.166343 41.540688 \n", "L 115.501478 39.715515 \n", "L 115.836613 30.649909 \n", "L 116.171748 39.263999 \n", "L 116.506883 35.944124 \n", "L 116.842019 29.457242 \n", "L 117.177154 29.746705 \n", "L 117.512289 34.432992 \n", "L 117.847424 27.923328 \n", "L 118.182559 30.6208 \n", "L 118.517694 31.127048 \n", "L 118.852829 33.908344 \n", "L 119.187965 31.438091 \n", "L 119.5231 33.163442 \n", "L 119.858235 32.136161 \n", "L 120.528505 42.832948 \n", "L 120.86364 37.594861 \n", "L 121.198775 42.460902 \n", "L 121.53391 44.54214 \n", "L 121.869046 35.278036 \n", "L 122.204181 31.994526 \n", "L 122.539316 46.163572 \n", "L 122.874451 35.501931 \n", "L 123.209586 34.12742 \n", "L 123.544721 43.411749 \n", "L 124.214992 40.643895 \n", "L 124.550127 45.664528 \n", "L 124.885262 48.183455 \n", "L 125.555532 42.669876 \n", "L 125.890667 44.278945 \n", "L 126.225802 39.369738 \n", "L 126.896073 48.666046 \n", "L 127.231208 44.684044 \n", "L 127.566343 55.296137 \n", "L 127.901478 47.223545 \n", "L 128.236613 46.532167 \n", "L 128.571748 51.155448 \n", "L 128.906883 40.93623 \n", "L 129.242019 52.15437 \n", "L 129.577154 48.742971 \n", "L 129.912289 50.183601 \n", "L 130.582559 56.694919 \n", "L 130.917694 50.294856 \n", "L 131.252829 54.06026 \n", "L 131.587965 51.260348 \n", "L 131.9231 51.995961 \n", "L 132.258235 50.602166 \n", "L 132.59337 57.613987 \n", "L 132.928505 60.667081 \n", "L 133.26364 65.009954 \n", "L 133.598775 52.504852 \n", "L 133.93391 60.535023 \n", "L 134.269046 58.747874 \n", "L 134.604181 55.972828 \n", "L 134.939316 67.221242 \n", "L 135.274451 64.051261 \n", "L 135.609586 64.689157 \n", "L 135.944721 63.892285 \n", "L 136.279856 61.180841 \n", "L 136.614992 66.220156 \n", "L 136.950127 62.364738 \n", "L 137.285262 67.504087 \n", "L 137.620397 65.232282 \n", "L 137.955532 53.600477 \n", "L 138.290667 63.113522 \n", "L 138.625802 56.627742 \n", "L 138.960938 56.209083 \n", "L 139.296073 66.396441 \n", "L 139.631208 63.794645 \n", "L 139.966343 59.379556 \n", "L 140.301478 58.88375 \n", "L 140.636613 55.489173 \n", "L 140.971748 53.463965 \n", "L 141.306883 59.397783 \n", "L 141.642019 61.708738 \n", "L 141.977154 59.974775 \n", "L 142.312289 60.000765 \n", "L 142.647424 55.4652 \n", "L 142.982559 62.132995 \n", "L 143.317694 57.356849 \n", "L 143.652829 67.96432 \n", "L 143.987965 62.368424 \n", "L 144.3231 60.508643 \n", "L 144.658235 67.611966 \n", "L 144.99337 65.054451 \n", "L 145.328505 73.286747 \n", "L 145.66364 70.851184 \n", "L 145.998775 71.045109 \n", "L 146.33391 72.357906 \n", "L 146.669046 65.132193 \n", "L 147.004181 67.593209 \n", "L 147.339316 67.073796 \n", "L 147.674451 64.212152 \n", "L 148.009586 62.79478 \n", "L 148.344721 63.871247 \n", "L 148.679856 63.230355 \n", "L 149.014992 63.672288 \n", "L 149.350127 68.968931 \n", "L 149.685262 69.977098 \n", "L 150.020397 69.629253 \n", "L 150.690667 79.792959 \n", "L 151.025802 82.373943 \n", "L 151.360938 86.622687 \n", "L 151.696073 87.290989 \n", "L 152.031208 81.598088 \n", "L 152.366343 94.263699 \n", "L 152.701478 84.031492 \n", "L 153.036613 88.46336 \n", "L 153.371748 83.106421 \n", "L 153.706883 88.816352 \n", "L 154.042019 83.788633 \n", "L 154.377154 88.246629 \n", "L 154.712289 88.410798 \n", "L 155.047424 100.229479 \n", "L 155.382559 94.875096 \n", "L 156.052829 76.163534 \n", "L 156.387965 86.828059 \n", "L 156.7231 82.040926 \n", "L 157.058235 83.917356 \n", "L 157.39337 94.599017 \n", "L 157.728505 82.643346 \n", "L 158.06364 81.811899 \n", "L 158.398775 80.056919 \n", "L 159.404181 97.051114 \n", "L 159.739316 94.554308 \n", "L 160.074451 98.131412 \n", "L 160.409586 97.565566 \n", "L 160.744721 96.037102 \n", "L 161.079856 81.790644 \n", "L 161.414992 91.818881 \n", "L 162.085262 99.536794 \n", "L 162.420397 93.899722 \n", "L 162.755532 103.65007 \n", "L 163.090667 94.925471 \n", "L 163.425802 103.512729 \n", "L 163.760938 107.475773 \n", "L 164.096073 115.654818 \n", "L 164.431208 115.208925 \n", "L 164.766343 113.492489 \n", "L 165.436613 99.904156 \n", "L 165.771748 101.560946 \n", "L 166.106883 102.528297 \n", "L 166.777154 111.651091 \n", "L 167.112289 109.860865 \n", "L 167.447424 104.573785 \n", "L 167.782559 101.1595 \n", "L 168.117694 104.818227 \n", "L 168.787965 95.249558 \n", "L 169.1231 99.397045 \n", "L 169.458235 109.292029 \n", "L 169.79337 114.456747 \n", "L 170.128505 111.604475 \n", "L 170.46364 115.460684 \n", "L 170.798775 112.624444 \n", "L 171.13391 119.263429 \n", "L 171.469046 111.955568 \n", "L 171.804181 116.285782 \n", "L 172.139316 114.258519 \n", "L 172.474451 109.880631 \n", "L 172.809586 111.419439 \n", "L 173.144721 119.07714 \n", "L 173.814992 123.542644 \n", "L 174.150127 123.669254 \n", "L 174.485262 129.310063 \n", "L 175.155532 102.80625 \n", "L 175.490667 117.820338 \n", "L 175.825802 116.379464 \n", "L 176.160938 124.513528 \n", "L 176.496073 120.943679 \n", "L 176.831208 128.42754 \n", "L 177.166343 117.00925 \n", "L 177.501478 117.898216 \n", "L 178.171748 131.979298 \n", "L 178.506883 128.067787 \n", "L 178.842019 116.322289 \n", "L 179.177154 111.60967 \n", "L 179.512289 109.575609 \n", "L 179.847424 117.868801 \n", "L 180.182559 122.919257 \n", "L 180.517694 122.74663 \n", "L 180.852829 126.918413 \n", "L 181.187965 124.018143 \n", "L 181.5231 127.319007 \n", "L 181.858235 132.232498 \n", "L 182.19337 140.21128 \n", "L 182.528505 139.846605 \n", "L 182.86364 133.153406 \n", "L 183.198775 134.185448 \n", "L 184.204181 118.869904 \n", "L 184.539316 133.83519 \n", "L 184.874451 136.706484 \n", "L 185.209586 138.152807 \n", "L 185.544721 122.600771 \n", "L 185.879856 138.163653 \n", "L 186.214992 141.097945 \n", "L 186.550127 147.46258 \n", "L 186.885262 128.835799 \n", "L 187.220397 129.389559 \n", "L 187.555532 130.23137 \n", "L 187.890667 135.616064 \n", "L 188.225802 134.444732 \n", "L 188.560938 134.299665 \n", "L 188.896073 136.59454 \n", "L 189.231208 144.403782 \n", "L 189.901478 131.756812 \n", "L 190.236613 132.244092 \n", "L 190.571748 131.632092 \n", "L 190.906883 133.287059 \n", "L 191.242019 132.239475 \n", "L 191.577154 135.7932 \n", "L 191.912289 132.981345 \n", "L 192.247424 138.180826 \n", "L 192.582559 140.10349 \n", "L 192.917694 150.64386 \n", "L 193.252829 142.946169 \n", "L 193.587965 148.235458 \n", "L 193.9231 150.652452 \n", "L 194.258235 148.873646 \n", "L 194.59337 148.729111 \n", "L 194.928505 136.952266 \n", "L 195.26364 138.688891 \n", "L 195.598775 133.455799 \n", "L 195.93391 134.015112 \n", "L 196.269046 134.944692 \n", "L 196.604181 127.918848 \n", "L 196.939316 135.637846 \n", "L 197.274451 123.961684 \n", "L 197.609586 135.459925 \n", "L 197.944721 132.888409 \n", "L 198.279856 147.313961 \n", "L 198.614992 145.376385 \n", "L 198.950127 142.46658 \n", "L 199.285262 132.922897 \n", "L 199.620397 139.659873 \n", "L 199.955532 136.321286 \n", "L 200.290667 138.79922 \n", "L 200.625802 125.799071 \n", "L 200.960938 132.062941 \n", "L 201.296073 126.832689 \n", "L 201.631208 132.739638 \n", "L 201.966343 134.765387 \n", "L 202.301478 132.383245 \n", "L 202.636613 135.97064 \n", "L 202.971748 134.034989 \n", "L 203.306883 138.554227 \n", "L 203.642019 139.130596 \n", "L 203.977154 141.820134 \n", "L 204.312289 150.690577 \n", "L 204.647424 142.949506 \n", "L 204.982559 148.769667 \n", "L 205.317694 144.203214 \n", "L 205.652829 150.959405 \n", "L 205.987965 148.709475 \n", "L 206.3231 153.089468 \n", "L 206.658235 155.571689 \n", "L 207.328505 136.146151 \n", "L 207.66364 132.219954 \n", "L 207.998775 136.407624 \n", "L 208.33391 143.266083 \n", "L 208.669046 139.430152 \n", "L 209.004181 132.579551 \n", "L 209.339316 134.790244 \n", "L 209.674451 132.375339 \n", "L 210.009586 137.558639 \n", "L 210.344721 140.99049 \n", "L 210.679856 137.799212 \n", "L 211.014992 142.728916 \n", "L 211.350127 139.968066 \n", "L 211.685262 142.677893 \n", "L 212.020397 141.588976 \n", "L 212.690667 147.405307 \n", "L 213.025802 148.384166 \n", "L 213.360938 147.658329 \n", "L 213.696073 146.615637 \n", "L 214.031208 143.763308 \n", "L 214.366343 135.930944 \n", "L 214.701478 138.645022 \n", "L 215.036613 140.398348 \n", "L 215.371748 137.657558 \n", "L 215.706883 143.312599 \n", "L 216.042019 138.21676 \n", "L 216.377154 140.016557 \n", "L 216.712289 132.787609 \n", "L 217.047424 136.358171 \n", "L 217.717694 151.953233 \n", "L 218.387965 141.580485 \n", "L 218.7231 138.762995 \n", "L 219.39337 150.040599 \n", "L 219.728505 144.306034 \n", "L 220.06364 143.76691 \n", "L 220.398775 147.517682 \n", "L 220.73391 148.994525 \n", "L 221.069046 151.470407 \n", "L 221.739316 147.288538 \n", "L 222.074451 144.287139 \n", "L 222.409586 135.295535 \n", "L 222.744721 134.059221 \n", "L 223.079856 129.748728 \n", "L 223.750127 141.785612 \n", "L 224.085262 143.285839 \n", "L 225.090667 135.843581 \n", "L 225.425802 142.671985 \n", "L 225.760938 133.60572 \n", "L 226.096073 135.522454 \n", "L 226.431208 127.417742 \n", "L 226.766343 127.353554 \n", "L 227.101478 127.643211 \n", "L 227.436613 129.905887 \n", "L 227.771748 138.70208 \n", "L 228.106883 129.051187 \n", "L 229.112289 139.930365 \n", "L 229.447424 137.360787 \n", "L 229.782559 136.787892 \n", "L 230.117694 138.453718 \n", "L 230.452829 138.985142 \n", "L 230.787965 139.259902 \n", "L 231.1231 139.306164 \n", "L 231.458235 132.586453 \n", "L 231.79337 131.368815 \n", "L 232.128505 121.883672 \n", "L 232.46364 120.163153 \n", "L 232.798775 122.509285 \n", "L 233.13391 127.454873 \n", "L 233.469046 135.334886 \n", "L 233.804181 137.296305 \n", "L 234.139316 130.754667 \n", "L 234.474451 132.808588 \n", "L 234.809586 130.591733 \n", "L 235.144721 143.906792 \n", "L 235.479856 121.572788 \n", "L 235.814992 127.758239 \n", "L 236.150127 123.392138 \n", "L 236.485262 146.576441 \n", "L 236.820397 131.91535 \n", "L 237.155532 134.108504 \n", "L 237.490667 122.00728 \n", "L 237.825802 123.241419 \n", "L 238.160938 113.647204 \n", "L 238.831208 126.672672 \n", "L 239.166343 125.417599 \n", "L 239.501478 122.92589 \n", "L 239.836613 118.188212 \n", "L 240.171748 122.898209 \n", "L 240.506883 123.544566 \n", "L 241.177154 117.988407 \n", "L 241.512289 123.141465 \n", "L 241.847424 121.810705 \n", "L 242.182559 130.743 \n", "L 242.517694 129.866657 \n", "L 242.852829 131.605169 \n", "L 243.187965 117.083247 \n", "L 243.5231 127.316293 \n", "L 243.858235 130.759653 \n", "L 244.19337 146.687942 \n", "L 244.528505 127.309676 \n", "L 244.86364 121.961566 \n", "L 245.198775 113.963547 \n", "L 245.53391 113.981419 \n", "L 245.869046 119.643292 \n", "L 246.204181 113.747883 \n", "L 246.539316 104.364367 \n", "L 246.874451 105.905627 \n", "L 247.209586 97.862031 \n", "L 247.544721 112.664789 \n", "L 247.879856 110.523233 \n", "L 248.214992 122.31788 \n", "L 248.550127 114.368554 \n", "L 248.885262 119.102246 \n", "L 249.220397 114.372189 \n", "L 249.555532 118.40448 \n", "L 249.890667 119.357437 \n", "L 250.225802 117.018277 \n", "L 250.560938 112.802749 \n", "L 250.896073 105.165358 \n", "L 251.231208 107.025536 \n", "L 251.566343 113.08392 \n", "L 251.901478 114.750665 \n", "L 252.236613 107.949214 \n", "L 252.571748 96.932859 \n", "L 252.906883 95.97259 \n", "L 253.242019 97.143748 \n", "L 253.577154 92.113856 \n", "L 253.912289 98.555509 \n", "L 254.247424 102.128111 \n", "L 254.582559 101.769552 \n", "L 254.917694 99.363762 \n", "L 255.252829 101.718152 \n", "L 255.587965 101.634472 \n", "L 255.9231 103.378995 \n", "L 256.258235 108.378122 \n", "L 256.59337 106.172409 \n", "L 256.928505 107.837205 \n", "L 257.26364 106.30007 \n", "L 257.598775 113.65958 \n", "L 257.93391 112.391913 \n", "L 258.269046 94.56846 \n", "L 258.604181 101.790511 \n", "L 258.939316 89.315208 \n", "L 259.274451 91.952831 \n", "L 259.609586 88.332401 \n", "L 259.944721 94.00393 \n", "L 260.279856 97.683186 \n", "L 260.614992 97.686211 \n", "L 260.950127 95.971089 \n", "L 261.285262 99.183057 \n", "L 261.955532 88.635878 \n", "L 262.290667 87.671078 \n", "L 262.625802 83.38924 \n", "L 262.960938 95.149322 \n", "L 263.296073 86.853293 \n", "L 263.631208 88.319784 \n", "L 264.636613 100.49482 \n", "L 264.971748 97.378027 \n", "L 265.306883 83.054185 \n", "L 265.642019 90.510478 \n", "L 265.977154 82.159346 \n", "L 266.312289 83.462964 \n", "L 266.647424 88.006906 \n", "L 266.982559 88.003271 \n", "L 267.317694 93.788697 \n", "L 267.652829 85.266154 \n", "L 267.987965 94.043185 \n", "L 268.3231 84.690245 \n", "L 268.658235 88.661115 \n", "L 268.99337 83.474653 \n", "L 269.328505 83.58765 \n", "L 269.66364 77.406071 \n", "L 269.998775 76.036543 \n", "L 270.669046 86.099539 \n", "L 271.004181 80.445211 \n", "L 271.674451 77.273713 \n", "L 272.009586 80.579425 \n", "L 272.344721 75.252159 \n", "L 272.679856 78.26372 \n", "L 273.350127 72.706937 \n", "L 273.685262 74.511282 \n", "L 274.020397 73.589025 \n", "L 274.690667 81.75691 \n", "L 275.025802 78.72561 \n", "L 275.360937 76.798312 \n", "L 276.031208 69.570764 \n", "L 276.366343 64.384454 \n", "L 277.036613 65.459823 \n", "L 277.371748 64.789672 \n", "L 277.706883 67.871653 \n", "L 278.042019 68.520597 \n", "L 278.377154 71.945449 \n", "L 278.712289 68.614878 \n", "L 279.047424 69.339128 \n", "L 279.382559 70.617748 \n", "L 279.717694 58.988574 \n", "L 280.052829 68.941501 \n", "L 280.387965 61.009803 \n", "L 280.7231 58.045495 \n", "L 281.058235 61.059164 \n", "L 281.39337 61.224165 \n", "L 281.728505 53.249701 \n", "L 282.06364 60.513177 \n", "L 282.398775 60.403652 \n", "L 282.73391 53.504847 \n", "L 283.069046 63.768576 \n", "L 283.404181 54.585247 \n", "L 283.739316 64.257122 \n", "L 284.074451 60.245233 \n", "L 284.409586 62.171551 \n", "L 284.744721 57.413053 \n", "L 285.079856 61.12839 \n", "L 285.414992 49.943021 \n", "L 285.750127 56.096413 \n", "L 286.085262 54.796852 \n", "L 286.420397 53.949167 \n", "L 286.755532 62.840291 \n", "L 287.090667 57.666439 \n", "L 287.425802 60.244087 \n", "L 287.760938 61.246954 \n", "L 288.096073 54.217783 \n", "L 288.431208 53.008364 \n", "L 288.766343 65.045649 \n", "L 289.101478 54.088925 \n", "L 289.436613 60.007506 \n", "L 289.771748 58.352969 \n", "L 290.106883 54.720547 \n", "L 290.442019 53.980935 \n", "L 290.777154 50.769417 \n", "L 291.112289 56.264244 \n", "L 291.447424 51.534259 \n", "L 291.782559 55.034459 \n", "L 292.452829 58.655426 \n", "L 292.787965 45.69816 \n", "L 293.1231 53.666824 \n", "L 293.458235 46.035101 \n", "L 293.79337 41.667721 \n", "L 294.128505 46.788292 \n", "L 294.46364 46.706983 \n", "L 294.798775 40.135853 \n", "L 295.13391 41.291746 \n", "L 295.469046 42.988331 \n", "L 295.804181 40.777799 \n", "L 296.139316 45.22949 \n", "L 296.474451 52.048359 \n", "L 296.809586 46.722458 \n", "L 297.144721 46.243619 \n", "L 297.479856 52.764552 \n", "L 297.814992 41.041032 \n", "L 298.150127 54.131917 \n", "L 298.485262 42.618751 \n", "L 298.820397 55.672118 \n", "L 299.490667 42.40272 \n", "L 299.825802 43.217823 \n", "L 300.160938 37.401933 \n", "L 300.496073 39.975519 \n", "L 300.831208 39.589675 \n", "L 301.166343 47.835009 \n", "L 301.836613 43.001973 \n", "L 302.506883 37.018027 \n", "L 302.842019 40.608578 \n", "L 303.177154 36.082637 \n", "L 303.512289 39.770079 \n", "L 303.847424 35.84378 \n", "L 304.182559 39.346316 \n", "L 304.517694 33.813667 \n", "L 304.852829 36.118382 \n", "L 305.187965 39.576922 \n", "L 305.5231 34.420189 \n", "L 305.858235 41.033022 \n", "L 306.19337 37.850049 \n", "L 306.528505 30.021294 \n", "L 306.86364 44.222583 \n", "L 307.198775 31.221687 \n", "L 307.53391 36.961102 \n", "L 307.869046 33.288031 \n", "L 308.204181 38.092816 \n", "L 308.874451 29.63326 \n", "L 309.209586 32.014979 \n", "L 309.544721 37.129775 \n", "L 309.879856 37.044431 \n", "L 310.214992 34.113363 \n", "L 310.550127 45.834242 \n", "L 310.885262 36.473755 \n", "L 311.220397 33.065668 \n", "L 311.555532 32.225127 \n", "L 311.890667 34.239745 \n", "L 312.225802 35.202843 \n", "L 312.560938 33.468026 \n", "L 312.896073 37.569954 \n", "L 313.231208 37.401673 \n", "L 313.566343 45.079256 \n", "L 313.901478 35.624773 \n", "L 314.236613 44.138551 \n", "L 314.571748 41.831481 \n", "L 314.906883 36.4068 \n", "L 315.242019 41.157934 \n", "L 315.577154 43.894105 \n", "L 315.912289 44.560411 \n", "L 316.247424 44.575852 \n", "L 316.582559 49.14998 \n", "L 316.917694 48.309735 \n", "L 317.252829 44.477695 \n", "L 317.587965 44.234896 \n", "L 317.9231 36.874156 \n", "L 318.258235 43.918981 \n", "L 318.59337 30.449202 \n", "L 318.928505 46.957057 \n", "L 319.26364 39.059379 \n", "L 319.598775 34.646965 \n", "L 319.93391 45.991613 \n", "L 320.269046 33.770622 \n", "L 320.604181 35.541836 \n", "L 320.939316 47.933564 \n", "L 321.274451 35.406647 \n", "L 321.609586 50.569901 \n", "L 321.944721 45.498197 \n", "L 322.279856 42.321635 \n", "L 322.614992 47.839587 \n", "L 322.950127 45.720402 \n", "L 323.285262 45.275493 \n", "L 323.620397 47.583338 \n", "L 323.955532 50.877977 \n", "L 324.290667 45.069992 \n", "L 324.625802 43.52375 \n", "L 324.960938 46.961423 \n", "L 325.296073 37.787058 \n", "L 325.631208 48.696852 \n", "L 325.966343 45.124277 \n", "L 326.301478 44.477917 \n", "L 326.636613 50.729215 \n", "L 326.971748 43.359785 \n", "L 327.306883 45.091534 \n", "L 327.642019 35.511486 \n", "L 327.977154 44.496565 \n", "L 328.312289 35.428429 \n", "L 328.647424 39.959065 \n", "L 328.982559 40.077851 \n", "L 329.317694 32.271958 \n", "L 329.652829 44.6623 \n", "L 329.987965 36.45386 \n", "L 330.3231 33.873853 \n", "L 330.658235 38.680652 \n", "L 330.99337 35.928813 \n", "L 331.328505 36.188294 \n", "L 331.66364 40.373289 \n", "L 331.998775 37.77281 \n", "L 332.33391 41.117143 \n", "L 332.669046 32.89217 \n", "L 333.004181 42.307078 \n", "L 333.339316 42.233781 \n", "L 333.674451 38.411205 \n", "L 334.009586 48.63178 \n", "L 334.344721 47.94683 \n", "L 334.679856 48.189145 \n", "L 335.014992 46.148011 \n", "L 335.350127 46.176037 \n", "L 335.685262 42.894493 \n", "L 336.020397 47.666361 \n", "L 336.355532 43.065449 \n", "L 336.690667 57.170617 \n", "L 337.025802 50.655244 \n", "L 337.360937 49.999174 \n", "L 337.696073 55.195806 \n", "L 338.366343 40.395061 \n", "L 338.701478 45.095938 \n", "L 339.036613 41.947231 \n", "L 339.371748 45.736016 \n", "L 339.706883 37.463569 \n", "L 340.042019 40.012036 \n", "L 340.377154 47.162935 \n", "L 341.047424 47.078892 \n", "L 341.382559 62.448831 \n", "L 341.717694 50.024813 \n", "L 342.052829 64.340753 \n", "L 342.387965 59.661535 \n", "L 342.7231 49.518438 \n", "L 343.058235 56.92866 \n", "L 343.39337 48.876096 \n", "L 343.728505 45.478299 \n", "L 344.06364 54.763086 \n", "L 344.398775 55.270033 \n", "L 344.73391 54.424948 \n", "L 345.069046 50.304835 \n", "L 345.404181 55.125602 \n", "L 345.739316 48.913569 \n", "L 346.074451 51.29001 \n", "L 346.409586 52.655827 \n", "L 346.744721 52.456548 \n", "L 347.079856 53.615899 \n", "L 347.414992 61.021748 \n", "L 347.750127 63.124303 \n", "L 348.085262 58.817061 \n", "L 348.420397 70.581571 \n", "L 349.090667 66.401892 \n", "L 349.425802 67.684009 \n", "L 349.760938 68.459785 \n", "L 350.096073 63.406975 \n", "L 350.431208 65.83549 \n", "L 350.766343 71.564098 \n", "L 351.101478 63.619925 \n", "L 351.436613 65.762678 \n", "L 351.771748 61.593346 \n", "L 352.106883 66.005151 \n", "L 352.442019 62.964323 \n", "L 352.777154 62.02415 \n", "L 353.112289 65.579087 \n", "L 353.447424 67.93252 \n", "L 353.782559 56.628331 \n", "L 354.452829 65.919713 \n", "L 354.787965 63.187433 \n", "L 355.1231 66.674468 \n", "L 355.458235 61.507148 \n", "L 355.79337 68.52845 \n", "L 356.128505 65.515888 \n", "L 356.798775 73.247525 \n", "L 357.13391 71.570049 \n", "L 357.469046 58.39113 \n", "L 358.139316 69.216977 \n", "L 358.474451 63.278887 \n", "L 358.809586 68.741946 \n", "L 359.479856 65.501448 \n", "L 359.814992 65.910942 \n", "L 360.150127 71.183283 \n", "L 360.485262 73.464967 \n", "L 360.820397 70.814794 \n", "L 361.155532 76.569798 \n", "L 361.490667 73.763091 \n", "L 361.825802 73.883174 \n", "L 362.160938 70.122808 \n", "L 362.496073 72.462849 \n", "L 362.831208 80.494835 \n", "L 363.166343 85.433448 \n", "L 363.501478 92.800479 \n", "L 364.171748 78.837654 \n", "L 364.506883 82.030686 \n", "L 364.842019 82.971859 \n", "L 365.177154 93.211285 \n", "L 365.512289 84.875347 \n", "L 365.847424 80.414624 \n", "L 366.182559 102.085742 \n", "L 366.517694 87.15153 \n", "L 367.187965 95.500623 \n", "L 367.5231 94.249277 \n", "L 367.858235 87.657142 \n", "L 368.19337 101.629179 \n", "L 368.528505 90.428054 \n", "L 368.86364 102.014843 \n", "L 369.198775 92.924216 \n", "L 369.53391 88.858542 \n", "L 369.869046 83.307828 \n", "L 370.204181 88.084642 \n", "L 370.539316 82.297976 \n", "L 370.874451 79.973862 \n", "L 371.209586 98.599058 \n", "L 371.544721 88.513717 \n", "L 371.879856 101.116493 \n", "L 372.214992 97.803271 \n", "L 372.550127 99.535581 \n", "L 372.885262 103.477243 \n", "L 373.220397 104.776653 \n", "L 373.555532 97.457008 \n", "L 373.890667 103.420364 \n", "L 374.225802 98.801704 \n", "L 374.560938 99.221907 \n", "L 374.896073 103.185135 \n", "L 375.231208 93.446465 \n", "L 375.566343 114.713483 \n", "L 375.901478 109.971268 \n", "L 376.236613 118.482123 \n", "L 376.906883 99.137213 \n", "L 377.242019 106.634969 \n", "L 377.577154 100.405242 \n", "L 377.912289 101.477789 \n", "L 378.247424 90.958907 \n", "L 378.582559 106.981819 \n", "L 378.917694 102.287799 \n", "L 379.252829 111.836123 \n", "L 379.9231 96.45815 \n", "L 380.258235 104.022428 \n", "L 381.26364 111.816424 \n", "L 381.93391 114.444425 \n", "L 382.604181 105.722674 \n", "L 382.939316 114.701762 \n", "L 383.274451 117.013699 \n", "L 383.609586 117.66338 \n", "L 383.944721 105.901209 \n", "L 384.279856 107.571158 \n", "L 384.614992 108.341613 \n", "L 384.950127 112.396332 \n", "L 385.285262 107.799868 \n", "L 385.620397 107.858525 \n", "L 385.955532 103.126762 \n", "L 386.290667 113.227028 \n", "L 386.625802 111.993186 \n", "L 386.960938 115.688926 \n", "L 386.960938 115.688926 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 254.582559 101.769552 \n", "L 254.917694 99.947405 \n", "L 255.252829 99.517147 \n", "L 255.587965 100.005473 \n", "L 255.9231 99.587627 \n", "L 256.258235 98.963172 \n", "L 256.59337 98.940713 \n", "L 257.26364 98.192943 \n", "L 258.269046 97.301682 \n", "L 259.274451 96.666128 \n", "L 260.614992 96.121089 \n", "L 262.290667 95.726729 \n", "L 264.301478 95.482174 \n", "L 267.652829 95.323008 \n", "L 275.025802 95.261208 \n", "L 349.760938 95.256722 \n", "L 386.960938 95.256722 \n", "L 386.960938 95.256722 \n", "\" clip-path=\"url(#pef7a65e927)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.501409 \n", "L 52.160938 7.421409 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 386.**********.501409 \n", "L 386.960938 7.421409 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.501409 \n", "L 386.**********.501409 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 7.421409 \n", "L 386.960938 7.421409 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 164.40625 59.455784 \n", "L 274.715625 59.455784 \n", "Q 276.715625 59.455784 276.715625 57.455784 \n", "L 276.715625 14.421409 \n", "Q 276.715625 12.421409 274.715625 12.421409 \n", "L 164.40625 12.421409 \n", "Q 162.40625 12.421409 162.40625 14.421409 \n", "L 162.40625 57.455784 \n", "Q 162.40625 59.455784 164.40625 59.455784 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 166.40625 20.519847 \n", "L 176.40625 20.519847 \n", "L 186.40625 20.519847 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- data -->\n", "     <g transform=\"translate(194.40625 24.019847)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-64\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"163.964844\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_29\">\n", "     <path d=\"M 166.40625 35.197972 \n", "L 176.40625 35.197972 \n", "L 186.40625 35.197972 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- 1-step preds -->\n", "     <g transform=\"translate(194.40625 38.697972)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"99.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"151.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"191.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"252.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"316.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"347.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"411.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"450.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"511.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"575.142578\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 166.40625 49.876097 \n", "L 176.40625 49.876097 \n", "L 186.40625 49.876097 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- multistep preds -->\n", "     <g transform=\"translate(194.40625 53.376097)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6d\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"97.412109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"160.791016\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"188.574219\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"227.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"255.566406\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"307.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"346.875\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"408.398438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"471.875\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"503.662109\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"567.138672\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"606.001953\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"667.525391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.001953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pef7a65e927\">\n", "   <rect x=\"52.160938\" y=\"7.421409\" width=\"334.8\" height=\"163.08\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 432x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["d2l.plot([time, time[tau:], time[n_train + tau:]],\n", "         [x.detach().numpy(), onestep_preds.detach().numpy(),\n", "          multistep_preds[n_train + tau:].detach().numpy()], 'time',\n", "         'x', legend=['data', '1-step preds', 'multistep preds'],\n", "         xlim=[1, 1000], figsize=(6, 3))"]}, {"cell_type": "markdown", "id": "5e25e464", "metadata": {"origin_pos": 28}, "source": ["如上面的例子所示，绿线的预测显然并不理想。\n", "经过几个预测步骤之后，预测的结果很快就会衰减到一个常数。\n", "为什么这个算法效果这么差呢？事实是由于错误的累积：\n", "假设在步骤$1$之后，我们积累了一些错误$\\epsilon_1 = \\bar\\epsilon$。\n", "于是，步骤$2$的输入被扰动了$\\epsilon_1$，\n", "结果积累的误差是依照次序的$\\epsilon_2 = \\bar\\epsilon + c \\epsilon_1$，\n", "其中$c$为某个常数，后面的预测误差依此类推。\n", "因此误差可能会相当快地偏离真实的观测结果。\n", "例如，未来$24$小时的天气预报往往相当准确，\n", "但超过这一点，精度就会迅速下降。\n", "我们将在本章及后续章节中讨论如何改进这一点。\n", "\n", "基于$k = 1, 4, 16, 64$，通过对整个序列预测的计算，\n", "让我们[**更仔细地看一下$k$步预测**]的困难。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "86fd62af", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:34.147991Z", "iopub.status.busy": "2023-08-18T07:00:34.147118Z", "iopub.status.idle": "2023-08-18T07:00:34.152314Z", "shell.execute_reply": "2023-08-18T07:00:34.151283Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [], "source": ["max_steps = 64"]}, {"cell_type": "code", "execution_count": 11, "id": "b5468e42", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:34.156964Z", "iopub.status.busy": "2023-08-18T07:00:34.156220Z", "iopub.status.idle": "2023-08-18T07:00:34.188969Z", "shell.execute_reply": "2023-08-18T07:00:34.187836Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [], "source": ["features = torch.zeros((T - tau - max_steps + 1, tau + max_steps))\n", "# 列i（i<tau）是来自x的观测，其时间步从（i）到（i+T-tau-max_steps+1）\n", "for i in range(tau):\n", "    features[:, i] = x[i: i + T - tau - max_steps + 1]\n", "\n", "# 列i（i>=tau）是来自（i-tau+1）步的预测，其时间步从（i）到（i+T-tau-max_steps+1）\n", "for i in range(tau, tau + max_steps):\n", "    features[:, i] = net(features[:, i - tau:i]).reshape(-1)"]}, {"cell_type": "code", "execution_count": 12, "id": "e41dbccd", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:00:34.193832Z", "iopub.status.busy": "2023-08-18T07:00:34.193416Z", "iopub.status.idle": "2023-08-18T07:00:34.567071Z", "shell.execute_reply": "2023-08-18T07:00:34.566267Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"406.885938pt\" height=\"207.83625pt\" viewBox=\"0 0 406.**********.83625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:00:34.491900</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 207.83625 \n", "L 406.**********.83625 \n", "L 406.885938 0 \n", "L 0 0 \n", "L 0 207.83625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.28 \n", "L 386.**********.28 \n", "L 386.960938 7.2 \n", "L 52.160938 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 117.775008 170.28 \n", "L 117.775008 7.2 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m3de4c3ec4a\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3de4c3ec4a\" x=\"117.775008\" y=\"170.28\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(108.231258 184.878438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 185.07149 170.28 \n", "L 185.07149 7.2 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m3de4c3ec4a\" x=\"185.07149\" y=\"170.28\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(175.52774 184.878438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 252.367973 170.28 \n", "L 252.367973 7.2 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m3de4c3ec4a\" x=\"252.367973\" y=\"170.28\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(242.824223 184.878438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 319.664455 170.28 \n", "L 319.664455 7.2 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m3de4c3ec4a\" x=\"319.664455\" y=\"170.28\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(310.120705 184.878438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 386.**********.28 \n", "L 386.960938 7.2 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m3de4c3ec4a\" x=\"386.960938\" y=\"170.28\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(374.235938 184.878438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- time -->\n", "     <g transform=\"translate(208.264844 198.556563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 52.160938 149.070207 \n", "L 386.960938 149.070207 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m1ce58c4b81\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1ce58c4b81\" x=\"52.160938\" y=\"149.070207\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1.5 -->\n", "      <g transform=\"translate(20.878125 152.869426)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 52.160938 124.029309 \n", "L 386.960938 124.029309 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m1ce58c4b81\" x=\"52.160938\" y=\"124.029309\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 127.828528)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 52.160938 98.988412 \n", "L 386.960938 98.988412 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m1ce58c4b81\" x=\"52.160938\" y=\"98.988412\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 102.78763)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 52.160938 73.947514 \n", "L 386.960938 73.947514 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m1ce58c4b81\" x=\"52.160938\" y=\"73.947514\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257812 77.746733)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 52.160938 48.906617 \n", "L 386.960938 48.906617 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m1ce58c4b81\" x=\"52.160938\" y=\"48.906617\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257812 52.705835)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 52.160938 23.865719 \n", "L 386.960938 23.865719 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m1ce58c4b81\" x=\"52.160938\" y=\"23.865719\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257812 27.664938)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- x -->\n", "     <g transform=\"translate(14.798438 91.699375)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 52.160938 66.013471 \n", "L 52.49742 67.873006 \n", "L 52.833902 78.812359 \n", "L 53.506867 74.998065 \n", "L 53.84335 63.623222 \n", "L 54.179832 67.524876 \n", "L 54.516314 62.147297 \n", "L 54.852797 61.846616 \n", "L 55.189279 48.866159 \n", "L 56.198726 69.783679 \n", "L 56.871691 76.761905 \n", "L 57.208174 63.786385 \n", "L 57.544656 63.470433 \n", "L 57.881139 58.355725 \n", "L 58.217621 57.716505 \n", "L 58.554103 59.895175 \n", "L 58.890586 51.719334 \n", "L 59.227068 51.02167 \n", "L 59.563551 50.695639 \n", "L 59.900033 46.528543 \n", "L 60.236515 50.224074 \n", "L 60.572998 61.878627 \n", "L 61.245963 67.037524 \n", "L 61.582445 73.075957 \n", "L 61.918927 66.28458 \n", "L 62.25541 65.811465 \n", "L 62.591892 65.061107 \n", "L 62.928375 69.529123 \n", "L 63.264857 76.095299 \n", "L 64.274304 59.858685 \n", "L 64.610787 59.371116 \n", "L 64.947269 57.267843 \n", "L 65.283752 52.881202 \n", "L 65.620234 53.974485 \n", "L 65.956716 51.385368 \n", "L 66.293199 59.003816 \n", "L 66.629681 61.872685 \n", "L 66.966164 58.077007 \n", "L 67.302646 56.414822 \n", "L 67.639128 49.059623 \n", "L 67.975611 47.633179 \n", "L 68.312093 41.608112 \n", "L 68.648576 49.184512 \n", "L 69.321541 44.175618 \n", "L 69.658023 46.810892 \n", "L 69.994505 40.428648 \n", "L 70.330988 54.284591 \n", "L 70.66747 44.441152 \n", "L 71.003953 53.870785 \n", "L 71.340435 46.94289 \n", "L 71.676917 43.963329 \n", "L 72.349882 32.016496 \n", "L 72.686365 38.924521 \n", "L 73.022847 37.221088 \n", "L 73.359329 38.310679 \n", "L 73.695812 42.574774 \n", "L 74.032294 42.44879 \n", "L 74.368777 39.060451 \n", "L 74.705259 40.130481 \n", "L 75.041742 45.011174 \n", "L 75.714706 43.631105 \n", "L 76.051189 28.241099 \n", "L 76.387671 38.368214 \n", "L 76.724154 36.351126 \n", "L 77.060636 24.364372 \n", "L 77.397118 33.853665 \n", "L 77.733601 35.511218 \n", "L 78.070083 33.428081 \n", "L 78.406566 34.019091 \n", "L 78.743048 41.773702 \n", "L 79.416013 28.321977 \n", "L 79.752495 29.374398 \n", "L 80.088978 25.166402 \n", "L 80.42546 24.224019 \n", "L 80.761943 35.196146 \n", "L 81.098425 30.119755 \n", "L 81.434907 34.482684 \n", "L 81.77139 36.196476 \n", "L 82.107872 32.436918 \n", "L 82.444355 35.727116 \n", "L 82.780837 32.856899 \n", "L 83.117319 31.813195 \n", "L 83.453802 32.586397 \n", "L 83.790284 25.133602 \n", "L 84.126767 34.7118 \n", "L 84.463249 30.057802 \n", "L 84.799731 32.843979 \n", "L 85.136214 27.578963 \n", "L 85.472696 29.793361 \n", "L 85.809179 24.217204 \n", "L 86.482144 33.505055 \n", "L 86.818626 27.591109 \n", "L 87.155108 33.820993 \n", "L 87.491591 37.754213 \n", "L 87.828073 37.933931 \n", "L 88.164556 42.566717 \n", "L 88.83752 34.112167 \n", "L 89.174003 31.556777 \n", "L 89.510485 26.673478 \n", "L 89.846968 25.465898 \n", "L 90.18345 24.949301 \n", "L 91.192897 26.870245 \n", "L 91.52938 24.157451 \n", "L 91.865862 32.789265 \n", "L 92.202345 35.869673 \n", "L 92.538827 28.748104 \n", "L 93.211792 44.481141 \n", "L 93.548274 34.901029 \n", "L 93.884757 34.789622 \n", "L 94.221239 30.742485 \n", "L 94.557721 28.371572 \n", "L 94.894204 24.669847 \n", "L 95.230686 28.363425 \n", "L 95.567169 33.460703 \n", "L 95.903651 28.879201 \n", "L 96.240133 22.137622 \n", "L 96.576616 31.082318 \n", "L 97.249581 24.687594 \n", "L 97.586063 36.039436 \n", "L 97.922546 36.375684 \n", "L 98.259028 32.399198 \n", "L 98.59551 33.853743 \n", "L 98.931993 31.709137 \n", "L 99.268475 30.792915 \n", "L 99.604958 27.45089 \n", "L 99.94144 29.018292 \n", "L 100.277922 28.079148 \n", "L 100.614405 24.271446 \n", "L 100.950887 27.435266 \n", "L 101.28737 24.019963 \n", "L 101.623852 26.884445 \n", "L 101.960334 18.506203 \n", "L 102.296817 27.033584 \n", "L 102.633299 20.583701 \n", "L 102.969782 21.933065 \n", "L 103.306264 19.121255 \n", "L 103.642747 26.329979 \n", "L 103.979229 21.068118 \n", "L 104.315711 21.659671 \n", "L 104.652194 19.499241 \n", "L 104.988676 30.469347 \n", "L 105.325159 21.02558 \n", "L 105.661641 23.628475 \n", "L 105.998123 27.498795 \n", "L 106.334606 16.959535 \n", "L 106.671088 19.283531 \n", "L 107.007571 19.072729 \n", "L 107.344053 17.341104 \n", "L 107.680535 21.060918 \n", "L 108.017018 20.828766 \n", "L 108.3535 26.589403 \n", "L 108.689983 23.185556 \n", "L 109.026465 31.643321 \n", "L 109.362948 24.978836 \n", "L 109.69943 35.222045 \n", "L 110.035912 28.955077 \n", "L 110.372395 27.818064 \n", "L 110.708877 24.947292 \n", "L 111.04536 19.831967 \n", "L 111.381842 27.449505 \n", "L 111.718324 24.961164 \n", "L 112.054807 20.389997 \n", "L 112.391289 34.21901 \n", "L 112.727772 24.603781 \n", "L 113.064254 24.563416 \n", "L 113.400736 29.339078 \n", "L 113.737219 21.348205 \n", "L 114.073701 27.621044 \n", "L 114.410184 25.900168 \n", "L 114.746666 17.352602 \n", "L 115.083149 25.474453 \n", "L 115.419631 22.344287 \n", "L 115.756113 16.228088 \n", "L 116.092596 16.501011 \n", "L 116.429078 20.919507 \n", "L 116.765561 14.781828 \n", "L 117.102043 17.325157 \n", "L 117.438525 17.802476 \n", "L 117.775008 20.424839 \n", "L 118.11149 18.095745 \n", "L 118.447973 19.722503 \n", "L 118.784455 18.753925 \n", "L 119.45742 28.839461 \n", "L 119.793902 23.900696 \n", "L 120.130385 28.488675 \n", "L 120.466867 30.450983 \n", "L 120.80335 21.716262 \n", "L 121.139832 18.620383 \n", "L 121.476314 31.979761 \n", "L 121.812797 21.927363 \n", "L 122.149279 20.631397 \n", "L 122.485762 29.385186 \n", "L 123.158726 26.775497 \n", "L 123.495209 31.509233 \n", "L 123.831691 33.884221 \n", "L 124.504656 28.685707 \n", "L 124.841139 30.202828 \n", "L 125.177621 25.57415 \n", "L 125.850586 34.339235 \n", "L 126.187068 30.584778 \n", "L 126.523551 40.590459 \n", "L 126.860033 32.979163 \n", "L 127.196515 32.327293 \n", "L 127.532998 36.686383 \n", "L 127.86948 27.051127 \n", "L 128.205963 37.628223 \n", "L 128.542445 34.411764 \n", "L 128.878927 35.770071 \n", "L 129.551892 41.909309 \n", "L 129.888375 35.874968 \n", "L 130.224857 39.425204 \n", "L 130.56134 36.785289 \n", "L 130.897822 37.478866 \n", "L 131.234304 36.164718 \n", "L 131.570787 42.775859 \n", "L 131.907269 45.654488 \n", "L 132.243752 49.749195 \n", "L 132.580234 37.958678 \n", "L 132.916716 45.529976 \n", "L 133.253199 43.844952 \n", "L 133.589681 41.228481 \n", "L 133.926164 51.834122 \n", "L 134.262646 48.845285 \n", "L 134.599128 49.446729 \n", "L 134.935611 48.695393 \n", "L 135.272093 46.138891 \n", "L 135.608576 50.890242 \n", "L 135.945058 47.255136 \n", "L 136.281541 52.100804 \n", "L 136.618023 49.958818 \n", "L 136.954505 38.991695 \n", "L 137.290988 47.961132 \n", "L 137.62747 41.845971 \n", "L 137.963953 41.451236 \n", "L 138.300435 51.056452 \n", "L 138.636917 48.603333 \n", "L 138.9734 44.440537 \n", "L 139.309882 43.973064 \n", "L 139.646365 40.772464 \n", "L 139.982847 38.862983 \n", "L 140.319329 44.457723 \n", "L 140.655812 46.636621 \n", "L 140.992294 45.001744 \n", "L 141.328777 45.026248 \n", "L 141.665259 40.749861 \n", "L 142.001742 47.036635 \n", "L 142.338224 42.533415 \n", "L 142.674706 52.534737 \n", "L 143.011189 47.25861 \n", "L 143.347671 45.505104 \n", "L 143.684154 52.202519 \n", "L 144.020636 49.791149 \n", "L 144.357118 57.553023 \n", "L 144.693601 55.256636 \n", "L 145.030083 55.43948 \n", "L 145.366566 56.677259 \n", "L 145.703048 49.864448 \n", "L 146.03953 52.184833 \n", "L 146.376013 51.695101 \n", "L 146.712495 48.996982 \n", "L 147.048978 47.660603 \n", "L 147.38546 48.675557 \n", "L 147.721943 48.071288 \n", "L 148.058425 48.487967 \n", "L 148.394907 53.481942 \n", "L 148.73139 54.432499 \n", "L 149.067872 54.104531 \n", "L 149.740837 63.687447 \n", "L 150.077319 66.120945 \n", "L 150.413802 70.1269 \n", "L 150.750284 70.757013 \n", "L 151.086767 65.389424 \n", "L 151.423249 77.331278 \n", "L 151.759731 67.683776 \n", "L 152.096214 71.862391 \n", "L 152.432696 66.811566 \n", "L 152.769179 72.195212 \n", "L 153.105661 67.454794 \n", "L 153.442144 71.658044 \n", "L 153.778626 71.812832 \n", "L 154.115108 82.956153 \n", "L 154.451591 77.907738 \n", "L 155.124556 60.26542 \n", "L 155.461038 70.320536 \n", "L 155.79752 65.806957 \n", "L 156.134003 67.576162 \n", "L 156.470485 77.647435 \n", "L 156.806968 66.374953 \n", "L 157.14345 65.591017 \n", "L 157.479932 63.936323 \n", "L 158.48938 79.95941 \n", "L 158.825862 77.605281 \n", "L 159.162345 80.977977 \n", "L 159.498827 80.444465 \n", "L 159.835309 79.003343 \n", "L 160.171792 65.570977 \n", "L 160.508274 75.026166 \n", "L 161.181239 82.30305 \n", "L 161.517721 76.9881 \n", "L 161.854204 86.181279 \n", "L 162.190686 77.955234 \n", "L 162.527169 86.051787 \n", "L 162.863651 89.788368 \n", "L 163.200133 97.500034 \n", "L 163.536616 97.07962 \n", "L 163.873098 95.461268 \n", "L 164.546063 82.649419 \n", "L 164.882546 84.211535 \n", "L 165.219028 85.123608 \n", "L 165.891993 93.725094 \n", "L 166.228475 92.037168 \n", "L 166.564958 87.05221 \n", "L 166.90144 83.833029 \n", "L 167.237922 87.282684 \n", "L 167.910887 78.260802 \n", "L 168.24737 82.171287 \n", "L 168.583852 91.500836 \n", "L 168.920334 96.370425 \n", "L 169.256817 93.681141 \n", "L 169.593299 97.316993 \n", "L 169.929782 94.642826 \n", "L 170.266264 100.902436 \n", "L 170.602747 94.012172 \n", "L 170.939229 98.094942 \n", "L 171.275711 96.183524 \n", "L 171.612194 92.055804 \n", "L 171.948676 93.50668 \n", "L 172.285159 100.726792 \n", "L 172.958123 104.937122 \n", "L 173.294606 105.056497 \n", "L 173.631088 110.37497 \n", "L 174.304053 85.385678 \n", "L 174.640535 99.541809 \n", "L 174.977018 98.183271 \n", "L 175.3135 105.852526 \n", "L 175.649983 102.486671 \n", "L 175.986465 109.542878 \n", "L 176.322948 98.777069 \n", "L 176.65943 99.615237 \n", "L 177.332395 112.891676 \n", "L 177.668877 109.203683 \n", "L 178.00536 98.129363 \n", "L 178.341842 93.68604 \n", "L 178.678324 91.768213 \n", "L 179.014807 99.587502 \n", "L 179.351289 104.349358 \n", "L 179.687772 104.186595 \n", "L 180.024254 108.119987 \n", "L 180.360736 105.38545 \n", "L 180.697219 108.49769 \n", "L 181.033701 113.130407 \n", "L 181.370184 120.653254 \n", "L 181.706666 120.309417 \n", "L 182.043149 113.998691 \n", "L 182.379631 114.971758 \n", "L 183.389078 100.531399 \n", "L 183.725561 114.641516 \n", "L 184.062043 117.348734 \n", "L 184.398525 118.712409 \n", "L 184.735008 104.049071 \n", "L 185.07149 118.722636 \n", "L 185.407973 121.489251 \n", "L 185.744455 127.490189 \n", "L 186.080938 109.927807 \n", "L 186.41742 110.449923 \n", "L 186.753902 111.243631 \n", "L 187.090385 116.320624 \n", "L 187.426867 115.216227 \n", "L 187.76335 115.079449 \n", "L 188.099832 117.243187 \n", "L 188.436314 124.606181 \n", "L 189.109279 112.681903 \n", "L 189.445762 113.141338 \n", "L 189.782244 112.564311 \n", "L 190.118726 114.124707 \n", "L 190.455209 113.136986 \n", "L 190.791691 116.487638 \n", "L 191.128174 113.836462 \n", "L 191.464656 118.738827 \n", "L 191.801139 120.551623 \n", "L 192.137621 130.489679 \n", "L 192.474103 123.231862 \n", "L 192.810586 128.218902 \n", "L 193.147068 130.497781 \n", "L 193.483551 128.820622 \n", "L 193.820033 128.684346 \n", "L 194.156515 117.580471 \n", "L 194.492998 119.217859 \n", "L 194.82948 114.283805 \n", "L 195.165963 114.811157 \n", "L 195.502445 115.687617 \n", "L 195.838927 109.063254 \n", "L 196.17541 116.341162 \n", "L 196.511892 105.332216 \n", "L 196.848375 116.173408 \n", "L 197.184857 113.748838 \n", "L 197.52134 127.350062 \n", "L 197.857822 125.523206 \n", "L 198.194304 122.779678 \n", "L 198.530787 113.781354 \n", "L 198.867269 120.133356 \n", "L 199.203752 116.985548 \n", "L 199.540234 119.321884 \n", "L 199.876716 107.064608 \n", "L 200.213199 112.970539 \n", "L 200.549681 108.039162 \n", "L 200.886164 113.608567 \n", "L 201.222646 115.518559 \n", "L 201.559128 113.27254 \n", "L 201.895611 116.654938 \n", "L 202.232093 114.829897 \n", "L 202.568576 119.090891 \n", "L 202.905058 119.634323 \n", "L 203.241541 122.170172 \n", "L 203.578023 130.533727 \n", "L 203.914505 123.235008 \n", "L 204.250988 128.722585 \n", "L 204.58747 124.417075 \n", "L 204.923953 130.787193 \n", "L 205.260435 128.665832 \n", "L 205.596917 132.795537 \n", "L 205.9334 135.135916 \n", "L 206.606365 116.820421 \n", "L 206.942847 113.11858 \n", "L 207.279329 117.066952 \n", "L 207.615812 123.533495 \n", "L 207.952294 119.916762 \n", "L 208.288777 113.457628 \n", "L 208.625259 115.541995 \n", "L 208.961742 113.265086 \n", "L 209.298224 118.152194 \n", "L 209.634706 121.387937 \n", "L 209.971189 118.37902 \n", "L 210.307671 123.027024 \n", "L 210.644154 120.423938 \n", "L 210.980636 122.978916 \n", "L 211.317118 121.952224 \n", "L 211.990083 127.436189 \n", "L 212.326566 128.359112 \n", "L 212.663048 127.674753 \n", "L 212.99953 126.691643 \n", "L 213.336013 124.002306 \n", "L 213.672495 116.617511 \n", "L 214.008978 119.176497 \n", "L 214.34546 120.829632 \n", "L 214.681943 118.24546 \n", "L 215.018425 123.577352 \n", "L 215.354907 118.772708 \n", "L 215.69139 120.469658 \n", "L 216.027872 113.653798 \n", "L 216.364355 117.020324 \n", "L 217.037319 131.72423 \n", "L 217.710284 121.944218 \n", "L 218.046767 119.287729 \n", "L 218.719731 129.920891 \n", "L 219.056214 124.514019 \n", "L 219.392696 124.005703 \n", "L 219.729179 127.542142 \n", "L 220.065661 128.934593 \n", "L 220.402144 131.268995 \n", "L 221.075108 127.326092 \n", "L 221.411591 124.496204 \n", "L 221.748073 116.018412 \n", "L 222.084556 114.852745 \n", "L 222.421038 110.788569 \n", "L 223.094003 122.137623 \n", "L 223.430485 123.552122 \n", "L 224.439932 116.53514 \n", "L 224.776415 122.973345 \n", "L 225.112897 114.425158 \n", "L 225.44938 116.232363 \n", "L 225.785862 108.590784 \n", "L 226.122345 108.530263 \n", "L 226.458827 108.803368 \n", "L 226.795309 110.936747 \n", "L 227.131792 119.230295 \n", "L 227.468274 110.130887 \n", "L 228.477721 120.388391 \n", "L 228.814204 117.965648 \n", "L 229.150686 117.42549 \n", "L 229.487169 118.996125 \n", "L 229.823651 119.497182 \n", "L 230.160133 119.756241 \n", "L 230.496616 119.79986 \n", "L 230.833098 113.464136 \n", "L 231.169581 112.316078 \n", "L 231.506063 103.37295 \n", "L 231.842546 101.750747 \n", "L 232.179028 103.962813 \n", "L 232.51551 108.625793 \n", "L 232.851993 116.055514 \n", "L 233.188475 117.904851 \n", "L 233.524958 111.737024 \n", "L 233.86144 113.673577 \n", "L 234.197922 111.583402 \n", "L 234.534405 124.137591 \n", "L 234.870887 103.07983 \n", "L 235.20737 108.911823 \n", "L 235.543852 104.795216 \n", "L 235.880334 126.654688 \n", "L 236.216817 112.831383 \n", "L 236.553299 114.899211 \n", "L 236.889782 103.489494 \n", "L 237.226264 104.65311 \n", "L 237.562747 95.607142 \n", "L 238.235711 107.888289 \n", "L 238.572194 106.704936 \n", "L 238.908676 104.355611 \n", "L 239.245159 99.88866 \n", "L 239.581641 104.329513 \n", "L 239.918123 104.938934 \n", "L 240.591088 99.700273 \n", "L 240.927571 104.558867 \n", "L 241.264053 103.304152 \n", "L 241.600535 111.726024 \n", "L 241.937018 110.899758 \n", "L 242.2735 112.538926 \n", "L 242.609983 98.846837 \n", "L 242.946465 108.495132 \n", "L 243.282948 111.741726 \n", "L 243.61943 126.759817 \n", "L 243.955912 108.488893 \n", "L 244.292395 103.446392 \n", "L 244.628877 95.905408 \n", "L 244.96536 95.922258 \n", "L 245.301842 101.260592 \n", "L 245.638324 95.702068 \n", "L 245.974807 86.854758 \n", "L 246.311289 88.307946 \n", "L 246.647772 80.723989 \n", "L 246.984254 94.680866 \n", "L 247.320736 92.661685 \n", "L 247.657219 103.782345 \n", "L 247.993701 96.287271 \n", "L 248.330184 100.750464 \n", "L 248.666666 96.290698 \n", "L 249.003149 100.09257 \n", "L 249.339631 100.991073 \n", "L 249.676113 98.78558 \n", "L 250.012596 94.810942 \n", "L 250.349078 87.609979 \n", "L 250.685561 89.363859 \n", "L 251.022043 95.076046 \n", "L 251.358525 96.647547 \n", "L 251.695008 90.234755 \n", "L 252.03149 79.847913 \n", "L 252.367973 78.942517 \n", "L 252.704455 80.046751 \n", "L 253.040938 75.304285 \n", "L 253.37742 81.37784 \n", "L 253.713902 84.74629 \n", "L 254.050385 84.408221 \n", "L 254.386867 82.139906 \n", "L 254.72335 84.359758 \n", "L 255.059832 84.28086 \n", "L 255.396314 85.925694 \n", "L 255.732797 90.639154 \n", "L 256.069279 88.559483 \n", "L 256.405762 90.129147 \n", "L 256.742244 88.679849 \n", "L 257.078726 95.61881 \n", "L 257.415209 94.423582 \n", "L 257.751691 77.618625 \n", "L 258.088174 84.427982 \n", "L 258.424656 72.665562 \n", "L 258.761139 75.152461 \n", "L 259.097621 71.738916 \n", "L 259.434103 77.086353 \n", "L 259.770586 80.555363 \n", "L 260.107068 80.558216 \n", "L 260.443551 78.941102 \n", "L 260.780033 81.969527 \n", "L 261.452998 72.02505 \n", "L 261.78948 71.115383 \n", "L 262.125963 67.078223 \n", "L 262.462445 78.166294 \n", "L 262.798927 70.344328 \n", "L 263.13541 71.727019 \n", "L 264.144857 83.206331 \n", "L 264.48134 80.267642 \n", "L 264.817822 66.762315 \n", "L 265.154304 73.792529 \n", "L 265.490787 65.918611 \n", "L 265.827269 67.147735 \n", "L 266.163752 71.43202 \n", "L 266.500234 71.428593 \n", "L 266.836716 76.883419 \n", "L 267.173199 68.847884 \n", "L 267.509681 77.123365 \n", "L 267.846164 68.304885 \n", "L 268.182646 72.048845 \n", "L 268.519128 67.158756 \n", "L 268.855611 67.265296 \n", "L 269.192093 61.436954 \n", "L 269.528576 60.145686 \n", "L 270.201541 69.633646 \n", "L 270.538023 64.302427 \n", "L 271.210988 61.31216 \n", "L 271.54747 64.428972 \n", "L 271.883953 59.406124 \n", "L 272.220435 62.245594 \n", "L 272.8934 57.006345 \n", "L 273.229882 58.707583 \n", "L 273.566365 57.838027 \n", "L 274.239329 65.539171 \n", "L 274.575812 62.68109 \n", "L 274.912294 60.863925 \n", "L 275.585259 54.049384 \n", "L 275.921742 49.159437 \n", "L 276.594706 50.173356 \n", "L 276.931189 49.5415 \n", "L 277.267671 52.447366 \n", "L 277.604154 53.059227 \n", "L 277.940636 56.288371 \n", "L 278.277118 53.14812 \n", "L 278.613601 53.830984 \n", "L 278.950083 55.03654 \n", "L 279.286566 44.071898 \n", "L 279.623048 53.456079 \n", "L 279.95953 45.977626 \n", "L 280.296013 43.182709 \n", "L 280.632495 46.024167 \n", "L 280.968978 46.179739 \n", "L 281.30546 38.660963 \n", "L 281.641943 45.509379 \n", "L 281.978425 45.406112 \n", "L 282.314907 38.90153 \n", "L 282.65139 48.578753 \n", "L 282.987872 39.920192 \n", "L 283.324355 49.039382 \n", "L 283.660837 45.256746 \n", "L 283.997319 47.072987 \n", "L 284.333802 42.586406 \n", "L 284.670284 46.089436 \n", "L 285.006767 35.543239 \n", "L 285.343249 41.345005 \n", "L 285.679731 40.119705 \n", "L 286.016214 39.32046 \n", "L 286.352696 47.703514 \n", "L 286.689179 42.825314 \n", "L 287.025661 45.255666 \n", "L 287.362144 46.201226 \n", "L 287.698626 39.573725 \n", "L 288.035108 38.433417 \n", "L 288.371591 49.78285 \n", "L 288.708073 39.452232 \n", "L 289.044556 45.032604 \n", "L 289.381038 43.472613 \n", "L 289.71752 40.047761 \n", "L 290.054003 39.350413 \n", "L 290.390485 36.322412 \n", "L 290.726968 41.503245 \n", "L 291.06345 37.043548 \n", "L 291.399932 40.343734 \n", "L 292.072897 43.757787 \n", "L 292.40938 31.540944 \n", "L 292.745862 39.054251 \n", "L 293.082345 31.858631 \n", "L 293.418827 27.740819 \n", "L 293.755309 32.568782 \n", "L 294.091792 32.492119 \n", "L 294.428274 26.296486 \n", "L 294.764757 27.386328 \n", "L 295.101239 28.985964 \n", "L 295.437721 26.90175 \n", "L 295.774204 31.099056 \n", "L 296.110686 37.52827 \n", "L 296.447169 32.50671 \n", "L 296.783651 32.055233 \n", "L 297.120133 38.203537 \n", "L 297.456616 27.14994 \n", "L 297.793098 39.492766 \n", "L 298.129581 28.637503 \n", "L 298.466063 40.944956 \n", "L 299.139028 28.433817 \n", "L 299.47551 29.202342 \n", "L 299.811993 23.718792 \n", "L 300.148475 26.145314 \n", "L 300.484958 25.781519 \n", "L 300.82144 33.555686 \n", "L 301.494405 28.998827 \n", "L 302.16737 23.356824 \n", "L 302.503852 26.742199 \n", "L 302.840334 22.474886 \n", "L 303.176817 25.951614 \n", "L 303.513299 22.249677 \n", "L 303.849782 25.552066 \n", "L 304.186264 20.335573 \n", "L 304.522747 22.508588 \n", "L 304.859229 25.769495 \n", "L 305.195711 20.907436 \n", "L 305.532194 27.142388 \n", "L 305.868676 24.141301 \n", "L 306.205159 16.759909 \n", "L 306.541641 30.149687 \n", "L 306.878123 17.891707 \n", "L 307.214606 23.303152 \n", "L 307.551088 19.839973 \n", "L 307.887571 24.370196 \n", "L 308.560535 16.394048 \n", "L 308.897018 18.639667 \n", "L 309.2335 23.462186 \n", "L 309.569983 23.38172 \n", "L 309.906465 20.618143 \n", "L 310.242948 31.66925 \n", "L 310.57943 22.843654 \n", "L 310.915912 19.630317 \n", "L 311.252395 18.837807 \n", "L 311.588877 20.737302 \n", "L 311.92536 21.645366 \n", "L 312.261842 20.009682 \n", "L 312.598324 23.877212 \n", "L 312.934807 23.718547 \n", "L 313.271289 30.957406 \n", "L 313.607772 22.043185 \n", "L 313.944254 30.070456 \n", "L 314.280736 27.89522 \n", "L 314.617219 22.780525 \n", "L 314.953701 27.260163 \n", "L 315.290184 29.839979 \n", "L 315.626666 30.46821 \n", "L 315.963149 30.482768 \n", "L 316.299631 34.795514 \n", "L 316.636113 34.003285 \n", "L 316.972596 30.390221 \n", "L 317.309078 30.161296 \n", "L 317.645561 23.221175 \n", "L 317.982043 29.863433 \n", "L 318.318525 17.163364 \n", "L 318.655008 32.727904 \n", "L 318.99149 25.281526 \n", "L 319.327973 21.121253 \n", "L 319.664455 31.817628 \n", "L 320.000938 20.294987 \n", "L 320.33742 21.964988 \n", "L 320.673902 33.648609 \n", "L 321.010385 21.837524 \n", "L 321.346867 36.134296 \n", "L 321.68335 31.352408 \n", "L 322.019832 28.357366 \n", "L 322.356314 33.560002 \n", "L 322.692797 31.561915 \n", "L 323.029279 31.142429 \n", "L 323.365762 33.318397 \n", "L 323.702244 36.424768 \n", "L 324.038726 30.948672 \n", "L 324.375209 29.490787 \n", "L 324.711691 32.73202 \n", "L 325.048174 24.08191 \n", "L 325.384656 34.36828 \n", "L 325.721139 30.999855 \n", "L 326.057621 30.39043 \n", "L 326.394103 36.284507 \n", "L 326.730586 29.336191 \n", "L 327.067068 30.968983 \n", "L 327.403551 21.936372 \n", "L 327.740033 30.408012 \n", "L 328.076515 21.858061 \n", "L 328.412998 26.129801 \n", "L 328.74948 26.241799 \n", "L 329.085963 18.881963 \n", "L 329.422445 30.564277 \n", "L 329.758927 22.824896 \n", "L 330.09541 20.392319 \n", "L 330.431892 24.924441 \n", "L 330.768375 22.329851 \n", "L 331.104857 22.574505 \n", "L 331.44134 26.520354 \n", "L 331.777822 24.068477 \n", "L 332.114304 27.221702 \n", "L 332.450787 19.466733 \n", "L 332.787269 28.34364 \n", "L 333.123752 28.274532 \n", "L 333.460234 24.670391 \n", "L 333.796716 34.306927 \n", "L 334.133199 33.661117 \n", "L 334.469681 33.889585 \n", "L 334.806164 31.965089 \n", "L 335.142646 31.991513 \n", "L 335.479128 28.897488 \n", "L 335.815611 33.396675 \n", "L 336.152093 29.058675 \n", "L 336.488576 42.357825 \n", "L 336.825058 36.214763 \n", "L 337.161541 35.596183 \n", "L 337.498023 40.495861 \n", "L 338.170988 26.540883 \n", "L 338.50747 30.973135 \n", "L 338.843953 28.004356 \n", "L 339.180435 31.576637 \n", "L 339.516917 23.776906 \n", "L 339.8534 26.179745 \n", "L 340.189882 32.922016 \n", "L 340.862847 32.842776 \n", "L 341.199329 47.334423 \n", "L 341.535812 35.620356 \n", "L 341.872294 49.118234 \n", "L 342.208777 44.706403 \n", "L 342.545259 35.142918 \n", "L 342.881742 42.129694 \n", "L 343.218224 34.537282 \n", "L 343.554706 31.333646 \n", "L 343.891189 40.087868 \n", "L 344.227671 40.565847 \n", "L 344.564154 39.769053 \n", "L 344.900636 35.884377 \n", "L 345.237118 40.429669 \n", "L 345.573601 34.572613 \n", "L 345.910083 36.813256 \n", "L 346.246566 38.101026 \n", "L 346.583048 37.913134 \n", "L 346.91953 39.006236 \n", "L 347.256013 45.988889 \n", "L 347.592495 47.971296 \n", "L 347.928978 43.910185 \n", "L 348.26546 55.00243 \n", "L 348.938425 51.061593 \n", "L 349.274907 52.270445 \n", "L 349.61139 53.00189 \n", "L 349.947872 48.237815 \n", "L 350.284355 50.527557 \n", "L 350.620837 55.928812 \n", "L 350.957319 48.438597 \n", "L 351.293802 50.458905 \n", "L 351.630284 46.527823 \n", "L 351.966767 50.687523 \n", "L 352.303249 47.820458 \n", "L 352.639731 46.93401 \n", "L 352.976214 50.285805 \n", "L 353.312696 52.504755 \n", "L 353.649179 41.846527 \n", "L 354.322144 50.606967 \n", "L 354.658626 48.030819 \n", "L 354.995108 51.318593 \n", "L 355.331591 46.446551 \n", "L 355.668073 53.066631 \n", "L 356.004556 50.226218 \n", "L 356.67752 57.516042 \n", "L 357.014003 55.934423 \n", "L 357.350485 43.508593 \n", "L 358.02345 53.715813 \n", "L 358.359932 48.117047 \n", "L 358.696415 53.267927 \n", "L 359.36938 50.212603 \n", "L 359.705862 50.598697 \n", "L 360.042345 55.569757 \n", "L 360.378827 57.721059 \n", "L 360.715309 55.222325 \n", "L 361.051792 60.648469 \n", "L 361.388274 58.002147 \n", "L 361.724757 58.115368 \n", "L 362.061239 54.569882 \n", "L 362.397721 56.776205 \n", "L 362.734204 64.349215 \n", "L 363.070686 69.005618 \n", "L 363.407169 75.951672 \n", "L 364.080133 62.786731 \n", "L 364.416616 65.797302 \n", "L 364.753098 66.684694 \n", "L 365.089581 76.339003 \n", "L 365.426063 68.479409 \n", "L 365.762546 64.273588 \n", "L 365.762546 64.273588 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 53.170385 66.113812 \n", "L 53.506867 77.04016 \n", "L 53.84335 79.3159 \n", "L 54.179832 74.010041 \n", "L 54.516314 65.876829 \n", "L 54.852797 75.029504 \n", "L 55.189279 62.173342 \n", "L 55.525762 58.825256 \n", "L 55.862244 61.746029 \n", "L 56.198726 46.739548 \n", "L 56.535209 54.205033 \n", "L 57.208174 76.137663 \n", "L 57.544656 74.564819 \n", "L 58.217621 61.96751 \n", "L 58.554103 64.049218 \n", "L 58.890586 57.575486 \n", "L 59.227068 54.911116 \n", "L 59.563551 60.998323 \n", "L 59.900033 50.506558 \n", "L 60.236515 46.987488 \n", "L 60.572998 47.584984 \n", "L 60.90948 46.734369 \n", "L 61.245963 51.335985 \n", "L 61.582445 67.42325 \n", "L 61.918927 66.334215 \n", "L 62.25541 73.903514 \n", "L 62.928375 67.764396 \n", "L 63.264857 64.077416 \n", "L 63.60134 67.404916 \n", "L 63.937822 77.306443 \n", "L 64.274304 73.585999 \n", "L 64.610787 65.339103 \n", "L 64.947269 63.755643 \n", "L 65.283752 57.967788 \n", "L 65.620234 58.97464 \n", "L 66.293199 51.188563 \n", "L 66.629681 51.85064 \n", "L 66.966164 50.732425 \n", "L 67.302646 61.283328 \n", "L 67.639128 63.532175 \n", "L 68.648576 43.789882 \n", "L 68.985058 45.966453 \n", "L 69.321541 40.639803 \n", "L 69.658023 50.313862 \n", "L 70.330988 42.821699 \n", "L 70.66747 42.984414 \n", "L 71.003953 38.185203 \n", "L 71.340435 58.087331 \n", "L 71.676917 42.06998 \n", "L 72.0134 54.041178 \n", "L 72.349882 40.790673 \n", "L 72.686365 42.855783 \n", "L 73.022847 32.313126 \n", "L 73.359329 27.860736 \n", "L 73.695812 36.882854 \n", "L 74.032294 40.756225 \n", "L 74.368777 39.491387 \n", "L 74.705259 39.694786 \n", "L 75.041742 40.107182 \n", "L 75.378224 37.979627 \n", "L 75.714706 38.982868 \n", "L 76.051189 45.375724 \n", "L 76.387671 45.169799 \n", "L 76.724154 42.414193 \n", "L 77.060636 23.029328 \n", "L 77.397118 29.436691 \n", "L 77.733601 38.641718 \n", "L 78.070083 28.623034 \n", "L 78.406566 28.488525 \n", "L 78.743048 29.915469 \n", "L 79.07953 35.277087 \n", "L 79.416013 35.496648 \n", "L 79.752495 42.326025 \n", "L 80.42546 23.296596 \n", "L 80.761943 24.231228 \n", "L 81.098425 25.877652 \n", "L 81.434907 25.336837 \n", "L 81.77139 36.224906 \n", "L 82.107872 29.206739 \n", "L 82.444355 31.938793 \n", "L 82.780837 33.514924 \n", "L 83.117319 32.608198 \n", "L 83.453802 34.539906 \n", "L 83.790284 30.115672 \n", "L 84.126767 28.55998 \n", "L 84.463249 30.278369 \n", "L 84.799731 24.045303 \n", "L 85.136214 32.983241 \n", "L 85.472696 29.489486 \n", "L 85.809179 32.445277 \n", "L 86.145661 24.699 \n", "L 86.482144 25.782484 \n", "L 86.818626 23.034802 \n", "L 87.491591 35.454856 \n", "L 87.828073 27.15629 \n", "L 88.164556 28.64599 \n", "L 88.83752 42.344096 \n", "L 89.174003 44.792667 \n", "L 89.510485 33.282101 \n", "L 89.846968 27.063277 \n", "L 90.18345 27.453317 \n", "L 90.519932 27.091137 \n", "L 91.192897 22.316794 \n", "L 91.52938 23.554289 \n", "L 91.865862 26.476796 \n", "L 92.202345 27.543655 \n", "L 92.538827 22.798662 \n", "L 93.211792 37.825064 \n", "L 93.548274 30.285596 \n", "L 93.884757 35.463137 \n", "L 94.221239 44.010228 \n", "L 94.557721 35.696527 \n", "L 95.230686 24.912113 \n", "L 95.567169 26.062884 \n", "L 95.903651 25.022003 \n", "L 96.240133 27.74449 \n", "L 96.576616 33.535175 \n", "L 96.913098 28.051365 \n", "L 97.249581 18.801185 \n", "L 97.922546 30.383254 \n", "L 98.259028 27.388767 \n", "L 98.59551 35.017027 \n", "L 98.931993 34.465224 \n", "L 99.268475 31.132187 \n", "L 99.604958 30.475837 \n", "L 99.94144 29.071618 \n", "L 100.277922 29.086842 \n", "L 100.614405 25.627153 \n", "L 100.950887 26.550131 \n", "L 101.28737 26.728491 \n", "L 101.623852 23.446305 \n", "L 101.960334 25.243054 \n", "L 102.296817 23.46098 \n", "L 102.633299 25.989259 \n", "L 102.969782 18.306607 \n", "L 103.306264 23.308979 \n", "L 103.642747 21.385298 \n", "L 103.979229 22.46319 \n", "L 104.315711 18.622324 \n", "L 104.652194 24.319297 \n", "L 105.325159 21.353184 \n", "L 105.661641 17.895826 \n", "L 105.998123 29.124204 \n", "L 106.334606 23.849295 \n", "L 106.671088 21.838783 \n", "L 107.007571 24.092719 \n", "L 107.344053 17.519715 \n", "L 107.680535 17.817407 \n", "L 108.017018 19.271262 \n", "L 108.3535 19.043684 \n", "L 108.689983 21.447262 \n", "L 109.026465 21.186185 \n", "L 109.362948 26.312704 \n", "L 109.69943 23.888236 \n", "L 110.035912 31.254281 \n", "L 110.372395 24.047267 \n", "L 110.708877 33.498864 \n", "L 111.04536 27.828336 \n", "L 111.381842 25.566311 \n", "L 112.054807 18.266833 \n", "L 112.391289 26.317904 \n", "L 112.727772 27.889528 \n", "L 113.064254 20.416236 \n", "L 113.400736 28.509699 \n", "L 113.737219 24.408179 \n", "L 114.073701 24.007473 \n", "L 114.410184 27.571154 \n", "L 114.746666 21.323345 \n", "L 115.083149 24.727911 \n", "L 115.419631 25.319234 \n", "L 115.756113 17.744445 \n", "L 116.092596 21.496409 \n", "L 116.429078 23.210942 \n", "L 116.765561 18.028078 \n", "L 117.102043 14.612727 \n", "L 117.438525 18.651817 \n", "L 117.775008 17.82487 \n", "L 118.11149 18.843114 \n", "L 118.447973 17.748141 \n", "L 118.784455 20.196192 \n", "L 119.120937 19.269382 \n", "L 119.45742 19.925574 \n", "L 119.793902 18.703172 \n", "L 120.130385 23.656272 \n", "L 120.466867 31.585028 \n", "L 120.80335 25.429101 \n", "L 121.139832 24.46475 \n", "L 121.476314 27.073146 \n", "L 122.149279 17.135436 \n", "L 122.485762 28.353804 \n", "L 123.158726 20.006924 \n", "L 123.831691 29.142094 \n", "L 124.168174 29.0254 \n", "L 124.841139 31.063243 \n", "L 125.177621 29.572388 \n", "L 125.514103 26.580964 \n", "L 125.850586 27.767661 \n", "L 126.187068 24.216618 \n", "L 126.523551 27.821288 \n", "L 126.860033 35.278618 \n", "L 127.196515 31.444027 \n", "L 127.532998 40.575026 \n", "L 127.86948 29.942935 \n", "L 128.205963 27.780637 \n", "L 128.542445 34.009494 \n", "L 128.878927 27.636068 \n", "L 129.21541 37.593584 \n", "L 129.551892 32.607365 \n", "L 130.224857 37.309342 \n", "L 130.56134 44.126916 \n", "L 130.897822 34.036736 \n", "L 131.234304 35.749241 \n", "L 131.570787 32.644022 \n", "L 131.907269 38.70077 \n", "L 132.243752 35.457023 \n", "L 132.580234 43.479951 \n", "L 132.916716 45.860475 \n", "L 133.253199 52.742907 \n", "L 133.589681 33.006907 \n", "L 134.262646 44.124041 \n", "L 134.599128 46.29697 \n", "L 134.935611 53.116712 \n", "L 135.272093 46.878096 \n", "L 135.608576 47.075471 \n", "L 136.281541 46.212876 \n", "L 136.618023 51.38649 \n", "L 136.954505 46.485781 \n", "L 137.290988 51.809043 \n", "L 137.62747 48.691534 \n", "L 137.963953 35.167417 \n", "L 138.300435 44.24844 \n", "L 138.636917 41.65819 \n", "L 138.9734 43.421502 \n", "L 139.309882 51.013768 \n", "L 139.646365 49.462905 \n", "L 139.982847 41.730125 \n", "L 140.319329 38.873043 \n", "L 140.655812 37.756965 \n", "L 140.992294 38.768931 \n", "L 141.328777 45.974704 \n", "L 141.665259 48.215839 \n", "L 142.001742 43.917248 \n", "L 142.338224 41.16957 \n", "L 142.674706 36.907971 \n", "L 143.011189 48.3885 \n", "L 143.347671 43.860092 \n", "L 143.684154 55.42923 \n", "L 144.020636 43.986359 \n", "L 144.357118 42.383049 \n", "L 144.693601 53.510562 \n", "L 145.030083 54.109173 \n", "L 145.366566 60.149024 \n", "L 145.703048 51.686589 \n", "L 146.03953 53.423278 \n", "L 146.376013 57.385474 \n", "L 146.712495 50.34895 \n", "L 147.048978 49.296036 \n", "L 147.38546 48.785099 \n", "L 147.721943 49.115711 \n", "L 148.058425 46.971942 \n", "L 148.394907 47.321707 \n", "L 148.73139 46.564301 \n", "L 149.067872 48.32658 \n", "L 149.404355 55.031963 \n", "L 149.740837 56.450924 \n", "L 150.077319 53.680265 \n", "L 150.413802 59.149307 \n", "L 150.750284 66.874974 \n", "L 151.423249 74.69392 \n", "L 151.759731 66.547073 \n", "L 152.096214 77.332399 \n", "L 152.432696 70.476516 \n", "L 152.769179 74.228871 \n", "L 153.105661 67.727184 \n", "L 153.442144 73.567301 \n", "L 153.778626 70.62149 \n", "L 154.451591 73.985041 \n", "L 154.788073 83.324542 \n", "L 155.461038 71.19114 \n", "L 155.79752 58.510597 \n", "L 156.134003 62.987423 \n", "L 156.470485 73.186303 \n", "L 156.806968 68.177639 \n", "L 157.14345 77.083081 \n", "L 158.152897 62.761061 \n", "L 158.48938 64.919171 \n", "L 159.162345 79.055349 \n", "L 159.498827 77.908359 \n", "L 159.835309 80.318961 \n", "L 160.171792 79.87622 \n", "L 160.508274 77.028104 \n", "L 160.844757 67.57037 \n", "L 161.517721 77.487128 \n", "L 161.854204 80.939584 \n", "L 162.190686 78.079642 \n", "L 162.527169 85.107154 \n", "L 162.863651 78.585749 \n", "L 163.873098 95.77569 \n", "L 164.209581 94.156364 \n", "L 164.546063 94.704911 \n", "L 165.219028 82.34931 \n", "L 165.55551 84.879768 \n", "L 165.891993 86.001167 \n", "L 166.564958 92.762564 \n", "L 167.237922 86.361966 \n", "L 167.574405 85.641563 \n", "L 167.910887 87.410941 \n", "L 168.24737 78.803469 \n", "L 168.583852 78.690609 \n", "L 168.920334 81.42787 \n", "L 169.256817 91.846088 \n", "L 169.593299 94.451484 \n", "L 169.929782 91.548864 \n", "L 170.266264 100.708828 \n", "L 170.602747 94.013602 \n", "L 170.939229 98.366996 \n", "L 171.275711 95.758146 \n", "L 171.612194 97.762327 \n", "L 172.285159 91.217742 \n", "L 172.958123 102.533621 \n", "L 173.294606 98.887718 \n", "L 173.631088 106.697366 \n", "L 173.967571 107.372466 \n", "L 174.304053 104.888337 \n", "L 174.640535 95.614267 \n", "L 174.977018 92.593046 \n", "L 175.3135 105.232355 \n", "L 175.649983 92.035354 \n", "L 175.986465 106.449243 \n", "L 176.322948 107.929012 \n", "L 176.65943 101.886244 \n", "L 176.995912 98.388895 \n", "L 177.332395 105.164689 \n", "L 177.668877 107.657857 \n", "L 178.00536 107.126466 \n", "L 178.341842 107.140639 \n", "L 178.678324 97.596934 \n", "L 179.014807 94.961388 \n", "L 179.351289 95.372685 \n", "L 179.687772 100.677586 \n", "L 180.024254 102.91098 \n", "L 180.360736 103.836707 \n", "L 180.697219 109.986291 \n", "L 181.033701 104.408925 \n", "L 181.706666 117.569142 \n", "L 182.043149 117.437225 \n", "L 182.379631 117.452365 \n", "L 182.716113 115.91024 \n", "L 183.052596 115.298768 \n", "L 183.389078 107.358877 \n", "L 183.725561 106.36552 \n", "L 184.062043 106.307111 \n", "L 184.398525 116.995942 \n", "L 184.735008 113.040406 \n", "L 185.07149 115.356479 \n", "L 185.407973 109.411652 \n", "L 185.744455 123.943308 \n", "L 186.080938 118.30281 \n", "L 186.41742 120.93998 \n", "L 186.753902 112.288723 \n", "L 187.090385 113.761139 \n", "L 187.426867 112.935503 \n", "L 187.76335 114.26127 \n", "L 188.099832 116.125473 \n", "L 188.436314 116.475829 \n", "L 188.772797 118.961934 \n", "L 189.109279 123.912908 \n", "L 189.445762 114.267855 \n", "L 189.782244 114.271951 \n", "L 190.118726 117.946242 \n", "L 190.455209 108.946907 \n", "L 190.791691 114.701033 \n", "L 191.128174 117.153078 \n", "L 191.464656 113.435288 \n", "L 191.801139 114.992302 \n", "L 192.137621 123.499608 \n", "L 192.474103 119.959014 \n", "L 192.810586 129.432085 \n", "L 193.147068 124.266297 \n", "L 193.483551 129.675515 \n", "L 193.820033 131.987254 \n", "L 194.156515 124.973314 \n", "L 194.492998 128.877691 \n", "L 194.82948 117.966654 \n", "L 195.165963 119.076144 \n", "L 195.838927 113.839077 \n", "L 196.17541 116.057436 \n", "L 196.511892 108.931107 \n", "L 196.848375 117.176765 \n", "L 197.184857 105.481101 \n", "L 197.52134 118.777281 \n", "L 197.857822 117.568719 \n", "L 198.194304 124.59691 \n", "L 198.530787 124.850924 \n", "L 199.203752 116.37064 \n", "L 199.540234 123.380001 \n", "L 199.876716 113.506554 \n", "L 200.213199 116.961524 \n", "L 200.549681 110.334468 \n", "L 200.886164 113.268561 \n", "L 201.222646 108.327049 \n", "L 201.559128 115.022816 \n", "L 201.895611 116.300902 \n", "L 202.232093 110.605292 \n", "L 202.568576 120.155846 \n", "L 202.905058 115.747161 \n", "L 203.241541 117.423803 \n", "L 203.578023 123.448234 \n", "L 203.914505 123.103413 \n", "L 204.250988 129.267857 \n", "L 204.58747 122.791851 \n", "L 204.923953 131.008885 \n", "L 205.260435 125.897479 \n", "L 205.596917 129.783164 \n", "L 206.269882 132.750116 \n", "L 206.606365 132.774385 \n", "L 206.942847 121.822951 \n", "L 207.279329 118.789425 \n", "L 207.952294 117.14803 \n", "L 208.288777 122.669682 \n", "L 208.961742 113.690252 \n", "L 209.298224 119.807878 \n", "L 209.634706 112.0054 \n", "L 210.307671 124.138296 \n", "L 210.644154 114.760213 \n", "L 210.980636 125.893396 \n", "L 211.653601 119.525892 \n", "L 211.990083 126.072938 \n", "L 212.326566 125.949432 \n", "L 212.663048 125.482651 \n", "L 212.99953 129.89267 \n", "L 213.336013 127.792085 \n", "L 213.672495 124.877426 \n", "L 214.008978 124.129036 \n", "L 214.34546 117.202971 \n", "L 214.681943 121.738681 \n", "L 215.018425 121.035043 \n", "L 215.354907 116.416315 \n", "L 215.69139 126.833048 \n", "L 216.027872 117.06237 \n", "L 216.364355 118.058282 \n", "L 216.700837 118.392984 \n", "L 217.037319 118.945122 \n", "L 217.373802 126.316338 \n", "L 217.710284 130.128523 \n", "L 218.046767 122.620828 \n", "L 218.383249 124.402269 \n", "L 218.719731 123.529937 \n", "L 219.056214 123.69004 \n", "L 219.392696 129.671294 \n", "L 219.729179 123.124434 \n", "L 220.065661 126.416261 \n", "L 220.402144 131.27918 \n", "L 220.738626 125.954548 \n", "L 221.075108 131.97437 \n", "L 221.411591 131.11488 \n", "L 221.748073 123.821665 \n", "L 222.084556 124.682517 \n", "L 222.421038 116.545534 \n", "L 222.75752 114.608701 \n", "L 223.094003 114.086044 \n", "L 223.430485 119.791877 \n", "L 223.766968 121.294169 \n", "L 224.10345 121.563234 \n", "L 224.439932 122.160823 \n", "L 224.776415 118.753615 \n", "L 225.112897 117.731335 \n", "L 225.44938 123.681926 \n", "L 225.785862 110.630245 \n", "L 226.122345 117.11514 \n", "L 226.795309 105.939909 \n", "L 227.131792 113.123801 \n", "L 227.468274 112.404136 \n", "L 227.804757 115.840395 \n", "L 228.141239 109.77448 \n", "L 228.477721 117.912466 \n", "L 228.814204 117.610976 \n", "L 229.150686 116.323158 \n", "L 229.487169 120.007599 \n", "L 229.823651 119.153781 \n", "L 230.160133 117.985601 \n", "L 230.496616 120.410004 \n", "L 230.833098 120.132741 \n", "L 231.169581 117.557948 \n", "L 231.506063 112.632765 \n", "L 231.842546 112.671052 \n", "L 232.179028 101.811494 \n", "L 232.51551 103.878224 \n", "L 232.851993 108.582016 \n", "L 233.188475 107.227873 \n", "L 233.524958 116.855532 \n", "L 233.86144 117.096286 \n", "L 234.197922 108.648002 \n", "L 234.534405 118.447546 \n", "L 234.870887 113.518632 \n", "L 235.20737 117.868883 \n", "L 235.543852 101.197603 \n", "L 235.880334 115.18845 \n", "L 236.216817 109.806286 \n", "L 236.553299 120.49822 \n", "L 236.889782 110.558677 \n", "L 237.226264 115.158017 \n", "L 237.562747 104.406818 \n", "L 238.235711 99.09881 \n", "L 238.572194 104.294679 \n", "L 238.908676 106.617806 \n", "L 239.245159 102.663927 \n", "L 239.581641 105.967578 \n", "L 239.918123 101.74266 \n", "L 240.254606 103.771288 \n", "L 240.591088 104.640973 \n", "L 240.927571 100.357206 \n", "L 241.264053 102.106658 \n", "L 241.600535 106.843385 \n", "L 241.937018 101.711081 \n", "L 242.2735 113.783313 \n", "L 242.946465 106.731261 \n", "L 243.282948 102.637855 \n", "L 243.61943 114.646597 \n", "L 243.955912 110.731276 \n", "L 244.292395 121.112788 \n", "L 244.628877 105.310613 \n", "L 244.96536 103.737127 \n", "L 245.301842 100.274939 \n", "L 245.638324 93.628556 \n", "L 245.974807 101.777747 \n", "L 246.647772 84.295663 \n", "L 246.984254 90.454685 \n", "L 247.320736 83.124884 \n", "L 247.657219 93.844537 \n", "L 247.993701 94.374498 \n", "L 248.330184 101.178454 \n", "L 248.666666 95.777705 \n", "L 249.003149 101.440767 \n", "L 249.339631 97.204198 \n", "L 249.676113 99.073529 \n", "L 250.012596 101.819267 \n", "L 250.349078 95.36559 \n", "L 250.685561 93.457831 \n", "L 251.022043 89.923782 \n", "L 251.358525 91.212717 \n", "L 251.695008 95.108103 \n", "L 252.03149 92.481995 \n", "L 252.704455 81.166363 \n", "L 253.040938 78.91594 \n", "L 253.37742 79.456402 \n", "L 253.713902 76.469369 \n", "L 254.386867 84.944549 \n", "L 254.72335 82.090831 \n", "L 255.059832 82.744947 \n", "L 255.396314 84.369358 \n", "L 255.732797 83.999945 \n", "L 256.069279 86.649928 \n", "L 256.405762 91.116474 \n", "L 256.742244 86.139053 \n", "L 257.078726 91.173949 \n", "L 257.415209 91.033509 \n", "L 257.751691 93.353463 \n", "L 258.088174 89.85227 \n", "L 258.424656 75.006973 \n", "L 258.761139 83.949686 \n", "L 259.097621 80.154832 \n", "L 259.434103 71.685279 \n", "L 260.107068 75.993767 \n", "L 260.443551 79.885926 \n", "L 260.780033 79.643967 \n", "L 261.116515 79.059769 \n", "L 261.452998 82.129831 \n", "L 261.78948 75.514809 \n", "L 262.125963 73.617311 \n", "L 262.462445 75.468573 \n", "L 262.798927 69.675632 \n", "L 263.13541 80.533844 \n", "L 263.471892 68.975782 \n", "L 263.808375 73.210247 \n", "L 264.144857 75.41294 \n", "L 264.817822 81.437098 \n", "L 265.154304 77.485206 \n", "L 265.490787 67.846925 \n", "L 265.827269 73.469123 \n", "L 266.163752 72.504824 \n", "L 266.500234 65.415809 \n", "L 266.836716 71.598744 \n", "L 267.173199 72.558826 \n", "L 267.509681 78.207352 \n", "L 267.846164 70.974749 \n", "L 268.182646 77.362869 \n", "L 268.519128 70.759224 \n", "L 268.855611 74.459094 \n", "L 269.192093 69.2868 \n", "L 269.528576 75.220904 \n", "L 269.865058 60.72401 \n", "L 270.538023 58.926399 \n", "L 270.874505 73.241318 \n", "L 271.883953 60.148898 \n", "L 272.220435 61.244274 \n", "L 272.556917 66.867902 \n", "L 272.8934 57.726445 \n", "L 273.229882 61.461255 \n", "L 273.566365 57.93994 \n", "L 273.902847 57.092318 \n", "L 274.239329 57.527836 \n", "L 274.575812 58.159317 \n", "L 274.912294 62.646925 \n", "L 275.248777 69.564337 \n", "L 275.585259 60.842419 \n", "L 275.921742 56.979082 \n", "L 276.594706 53.396898 \n", "L 276.931189 46.970574 \n", "L 277.267671 47.183654 \n", "L 278.277118 53.094514 \n", "L 278.613601 52.499806 \n", "L 278.950083 56.493584 \n", "L 279.286566 52.538227 \n", "L 279.623048 52.920686 \n", "L 279.95953 53.858468 \n", "L 280.296013 40.904767 \n", "L 280.632495 53.533793 \n", "L 280.968978 44.96534 \n", "L 281.30546 42.28054 \n", "L 281.641943 40.904221 \n", "L 281.978425 46.94587 \n", "L 282.314907 37.401275 \n", "L 282.65139 44.600318 \n", "L 282.987872 43.780467 \n", "L 283.324355 38.312634 \n", "L 283.660837 47.460207 \n", "L 283.997319 38.404083 \n", "L 284.333802 51.164361 \n", "L 284.670284 43.948711 \n", "L 285.006767 47.451935 \n", "L 285.343249 37.231091 \n", "L 285.679731 45.484507 \n", "L 286.016214 32.304206 \n", "L 286.352696 39.221011 \n", "L 286.689179 37.623423 \n", "L 287.025661 41.444663 \n", "L 287.362144 48.65285 \n", "L 287.698626 42.601971 \n", "L 288.035108 42.456053 \n", "L 288.371591 43.891818 \n", "L 288.708073 38.431967 \n", "L 289.044556 35.34501 \n", "L 289.381038 51.373004 \n", "L 289.71752 39.988023 \n", "L 290.054003 43.372499 \n", "L 290.390485 37.541157 \n", "L 290.726968 39.402055 \n", "L 291.06345 37.732651 \n", "L 291.399932 34.937818 \n", "L 291.736415 39.814274 \n", "L 292.072897 35.957736 \n", "L 292.40938 39.972625 \n", "L 292.745862 41.30518 \n", "L 293.082345 44.725299 \n", "L 293.418827 27.97232 \n", "L 293.755309 31.412931 \n", "L 294.091792 28.949214 \n", "L 294.428274 27.909444 \n", "L 294.764757 31.039234 \n", "L 295.101239 31.340136 \n", "L 295.437721 24.357032 \n", "L 295.774204 23.482037 \n", "L 296.110686 27.735732 \n", "L 296.447169 28.695784 \n", "L 296.783651 31.159477 \n", "L 297.120133 36.922383 \n", "L 297.456616 30.70946 \n", "L 297.793098 28.456791 \n", "L 298.129581 36.680661 \n", "L 298.466063 26.941705 \n", "L 298.802546 38.020792 \n", "L 299.139028 26.534083 \n", "L 299.47551 40.00499 \n", "L 300.148475 25.715429 \n", "L 300.484958 23.554354 \n", "L 300.82144 22.556385 \n", "L 301.157922 26.645705 \n", "L 301.494405 26.563913 \n", "L 301.830887 33.905281 \n", "L 302.840334 22.911732 \n", "L 303.176817 21.921715 \n", "L 303.513299 26.680831 \n", "L 303.849782 23.588373 \n", "L 304.186264 23.875474 \n", "L 304.522747 20.541688 \n", "L 304.859229 24.388023 \n", "L 305.195711 21.366199 \n", "L 305.532194 21.48226 \n", "L 305.868676 24.809503 \n", "L 306.205159 21.698489 \n", "L 306.541641 25.836129 \n", "L 306.878123 23.821886 \n", "L 307.214606 15.859995 \n", "L 307.551088 25.498591 \n", "L 307.887571 20.612191 \n", "L 308.224053 23.241515 \n", "L 308.560535 18.974847 \n", "L 308.897018 22.514403 \n", "L 309.2335 20.697541 \n", "L 309.569983 16.518462 \n", "L 309.906465 16.586468 \n", "L 310.242948 24.019858 \n", "L 310.57943 27.190341 \n", "L 310.915912 21.064721 \n", "L 311.252395 26.623191 \n", "L 311.92536 19.102622 \n", "L 312.261842 17.269324 \n", "L 312.934807 23.69349 \n", "L 313.271289 21.114447 \n", "L 313.607772 21.877996 \n", "L 313.944254 22.942431 \n", "L 314.280736 33.424222 \n", "L 314.617219 23.735509 \n", "L 314.953701 25.238498 \n", "L 315.290184 24.932444 \n", "L 315.626666 23.258578 \n", "L 316.299631 30.010677 \n", "L 316.636113 30.633343 \n", "L 316.972596 28.266511 \n", "L 317.309078 32.841329 \n", "L 317.645561 33.558662 \n", "L 317.982043 28.833881 \n", "L 318.318525 26.349517 \n", "L 318.655008 20.757965 \n", "L 318.99149 27.517347 \n", "L 319.327973 18.715435 \n", "L 319.664455 30.638964 \n", "L 320.33742 19.647123 \n", "L 320.673902 27.561109 \n", "L 321.010385 22.08469 \n", "L 321.346867 20.774724 \n", "L 321.68335 32.515883 \n", "L 322.019832 23.93246 \n", "L 322.356314 35.290412 \n", "L 322.692797 29.19739 \n", "L 323.029279 25.732817 \n", "L 323.365762 30.290235 \n", "L 323.702244 32.487128 \n", "L 324.375209 30.475935 \n", "L 324.711691 34.67823 \n", "L 325.048174 29.527304 \n", "L 325.384656 26.402293 \n", "L 325.721139 29.788077 \n", "L 326.057621 23.544892 \n", "L 326.394103 33.418383 \n", "L 326.730586 30.684759 \n", "L 327.067068 28.887201 \n", "L 327.403551 33.677407 \n", "L 327.740033 27.918662 \n", "L 328.076515 28.17645 \n", "L 328.412998 19.72836 \n", "L 328.74948 26.479533 \n", "L 329.085963 22.697766 \n", "L 329.422445 25.756403 \n", "L 329.758927 25.109166 \n", "L 330.09541 17.871092 \n", "L 330.431892 26.500459 \n", "L 330.768375 24.557709 \n", "L 331.104857 20.470165 \n", "L 331.44134 21.514081 \n", "L 331.777822 22.166208 \n", "L 332.114304 23.603746 \n", "L 332.450787 26.532656 \n", "L 332.787269 23.541489 \n", "L 333.123752 24.926635 \n", "L 333.460234 18.682575 \n", "L 333.796716 25.940136 \n", "L 334.133199 30.024426 \n", "L 334.469681 25.628365 \n", "L 334.806164 31.862022 \n", "L 335.142646 32.171611 \n", "L 335.479128 34.886145 \n", "L 335.815611 29.278519 \n", "L 336.152093 28.27283 \n", "L 336.488576 25.991952 \n", "L 336.825058 33.980696 \n", "L 337.161541 28.899751 \n", "L 337.498023 44.326662 \n", "L 337.834505 35.093801 \n", "L 338.170988 32.142948 \n", "L 338.50747 36.496172 \n", "L 338.843953 32.670718 \n", "L 339.180435 24.215938 \n", "L 339.8534 27.04339 \n", "L 340.189882 34.47286 \n", "L 340.526365 23.843922 \n", "L 340.862847 20.276061 \n", "L 341.535812 39.633195 \n", "L 341.872294 36.915774 \n", "L 342.208777 46.466715 \n", "L 342.545259 30.839692 \n", "L 342.881742 50.327068 \n", "L 343.218224 46.088947 \n", "L 343.554706 33.39195 \n", "L 344.227671 31.290395 \n", "L 344.564154 32.368553 \n", "L 344.900636 42.193981 \n", "L 345.237118 41.232738 \n", "L 345.573601 37.84868 \n", "L 345.910083 30.163466 \n", "L 346.246566 38.382763 \n", "L 346.583048 34.736344 \n", "L 346.91953 37.118791 \n", "L 347.256013 34.84847 \n", "L 347.928978 38.201286 \n", "L 348.26546 48.728056 \n", "L 348.601943 50.218177 \n", "L 348.938425 42.097598 \n", "L 349.274907 56.347451 \n", "L 349.947872 51.836215 \n", "L 350.284355 46.742246 \n", "L 350.620837 52.18851 \n", "L 350.957319 48.253971 \n", "L 351.293802 51.350582 \n", "L 351.630284 55.755542 \n", "L 351.966767 47.584918 \n", "L 352.303249 47.576584 \n", "L 352.639731 42.969112 \n", "L 352.976214 52.192354 \n", "L 353.649179 45.910348 \n", "L 353.985661 46.83263 \n", "L 354.322144 54.077627 \n", "L 354.658626 40.107266 \n", "L 354.995108 43.257193 \n", "L 355.331591 51.669026 \n", "L 355.668073 52.374118 \n", "L 356.004556 51.330414 \n", "L 356.341038 40.708231 \n", "L 356.67752 54.084859 \n", "L 357.014003 52.449254 \n", "L 357.350485 56.657457 \n", "L 357.686968 54.886419 \n", "L 358.02345 55.465929 \n", "L 358.359932 38.215522 \n", "L 358.696415 45.633282 \n", "L 359.032897 56.091372 \n", "L 360.042345 46.392356 \n", "L 360.715309 52.625137 \n", "L 361.051792 58.309077 \n", "L 361.388274 58.344807 \n", "L 361.724757 53.466042 \n", "L 362.061239 64.09718 \n", "L 362.397721 55.578775 \n", "L 362.734204 57.579835 \n", "L 363.070686 50.469416 \n", "L 364.080133 77.115379 \n", "L 364.416616 72.128075 \n", "L 364.753098 62.395256 \n", "L 365.089581 65.632874 \n", "L 365.426063 65.597723 \n", "L 365.762546 75.655936 \n", "L 366.099028 74.727522 \n", "L 366.43551 65.147733 \n", "L 366.771993 82.21562 \n", "L 366.771993 82.21562 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 57.208174 74.412651 \n", "L 57.544656 77.651791 \n", "L 57.881139 78.50681 \n", "L 58.217621 77.507038 \n", "L 58.554103 75.398816 \n", "L 58.890586 76.978871 \n", "L 59.227068 69.397932 \n", "L 59.563551 57.969775 \n", "L 59.900033 60.059841 \n", "L 60.236515 44.674886 \n", "L 60.572998 50.490512 \n", "L 61.245963 77.833286 \n", "L 61.582445 77.503222 \n", "L 61.918927 76.095082 \n", "L 62.25541 61.824738 \n", "L 62.591892 63.704938 \n", "L 62.928375 57.207511 \n", "L 63.264857 53.412699 \n", "L 63.60134 58.923563 \n", "L 63.937822 48.709128 \n", "L 64.274304 43.581737 \n", "L 64.947269 41.468296 \n", "L 65.283752 46.897418 \n", "L 65.620234 73.348686 \n", "L 66.293199 77.047922 \n", "L 66.629681 76.795022 \n", "L 66.966164 75.727465 \n", "L 67.302646 67.689139 \n", "L 67.639128 75.046609 \n", "L 67.975611 77.780464 \n", "L 68.312093 77.428483 \n", "L 68.648576 75.007211 \n", "L 68.985058 63.694005 \n", "L 69.321541 57.428391 \n", "L 69.658023 57.613271 \n", "L 69.994505 53.927917 \n", "L 70.330988 48.665826 \n", "L 70.66747 48.448603 \n", "L 71.003953 46.957983 \n", "L 71.340435 59.373653 \n", "L 71.676917 63.269222 \n", "L 72.349882 48.621676 \n", "L 72.686365 39.360684 \n", "L 73.022847 39.925273 \n", "L 73.359329 34.676543 \n", "L 73.695812 44.66262 \n", "L 74.032294 42.161775 \n", "L 74.368777 38.084766 \n", "L 74.705259 37.254315 \n", "L 75.041742 31.384883 \n", "L 75.378224 54.154227 \n", "L 75.714706 39.216569 \n", "L 76.051189 49.801583 \n", "L 76.387671 36.847901 \n", "L 76.724154 36.543647 \n", "L 77.060636 26.344457 \n", "L 77.397118 23.490443 \n", "L 77.733601 27.122167 \n", "L 78.070083 32.266486 \n", "L 78.406566 33.196551 \n", "L 78.743048 33.43275 \n", "L 79.07953 33.015856 \n", "L 79.416013 30.683466 \n", "L 79.752495 31.711382 \n", "L 80.088978 38.593028 \n", "L 80.42546 39.624198 \n", "L 80.761943 37.289029 \n", "L 81.098425 22.844215 \n", "L 81.434907 23.537411 \n", "L 81.77139 27.102432 \n", "L 82.107872 24.138295 \n", "L 82.780837 23.789551 \n", "L 83.453802 27.433072 \n", "L 83.790284 35.20162 \n", "L 84.126767 26.136693 \n", "L 84.463249 22.164655 \n", "L 84.799731 21.623611 \n", "L 85.136214 22.056093 \n", "L 85.472696 22.296006 \n", "L 85.809179 26.682099 \n", "L 86.145661 24.158752 \n", "L 86.818626 25.536135 \n", "L 87.155108 25.202617 \n", "L 87.491591 26.569313 \n", "L 87.828073 24.381862 \n", "L 88.164556 23.57537 \n", "L 88.501038 23.921341 \n", "L 88.83752 22.119091 \n", "L 89.174003 24.785553 \n", "L 89.510485 23.858638 \n", "L 89.846968 25.066905 \n", "L 90.18345 22.55777 \n", "L 90.519932 22.381691 \n", "L 90.856415 21.387053 \n", "L 91.52938 26.327167 \n", "L 91.865862 23.542133 \n", "L 92.202345 23.633203 \n", "L 92.538827 26.877979 \n", "L 93.211792 39.287716 \n", "L 93.548274 28.035837 \n", "L 93.884757 23.30791 \n", "L 94.221239 22.75754 \n", "L 94.557721 22.743122 \n", "L 94.894204 22.264776 \n", "L 95.230686 21.344354 \n", "L 95.567169 21.403125 \n", "L 96.240133 22.987644 \n", "L 96.576616 21.762329 \n", "L 96.913098 23.438299 \n", "L 97.249581 27.507801 \n", "L 97.586063 24.675525 \n", "L 97.922546 27.532016 \n", "L 98.259028 35.976778 \n", "L 98.59551 28.820445 \n", "L 99.268475 22.343875 \n", "L 99.94144 22.07564 \n", "L 100.277922 23.031716 \n", "L 100.614405 25.186698 \n", "L 100.950887 23.590367 \n", "L 101.28737 20.725469 \n", "L 101.623852 21.715062 \n", "L 101.960334 23.265121 \n", "L 102.296817 23.241993 \n", "L 102.633299 26.646645 \n", "L 102.969782 26.515907 \n", "L 103.306264 24.77101 \n", "L 103.642747 24.342602 \n", "L 103.979229 23.650642 \n", "L 104.315711 23.590492 \n", "L 104.652194 22.566899 \n", "L 105.325159 22.664237 \n", "L 105.661641 21.757045 \n", "L 105.998123 22.107485 \n", "L 106.334606 21.571025 \n", "L 106.671088 22.311338 \n", "L 107.007571 20.231458 \n", "L 107.344053 21.236962 \n", "L 107.680535 20.65446 \n", "L 108.017018 21.124626 \n", "L 108.3535 20.054913 \n", "L 108.689983 21.463991 \n", "L 109.362948 20.937263 \n", "L 109.69943 19.76171 \n", "L 110.035912 22.818663 \n", "L 110.372395 21.862133 \n", "L 110.708877 21.344712 \n", "L 111.04536 21.577425 \n", "L 111.381842 19.644472 \n", "L 111.718324 19.459115 \n", "L 112.054807 19.732993 \n", "L 112.391289 19.838863 \n", "L 112.727772 20.653493 \n", "L 113.064254 20.688705 \n", "L 113.400736 22.247546 \n", "L 113.737219 21.834097 \n", "L 114.073701 24.089474 \n", "L 114.410184 22.195002 \n", "L 114.746666 25.104867 \n", "L 115.083149 23.366705 \n", "L 115.419631 22.592505 \n", "L 116.092596 19.775686 \n", "L 116.429078 21.947196 \n", "L 116.765561 22.896455 \n", "L 117.102043 21.240663 \n", "L 117.438525 23.147478 \n", "L 117.775008 21.87867 \n", "L 118.11149 21.832389 \n", "L 118.447973 22.826472 \n", "L 118.784455 21.145116 \n", "L 119.120937 21.906754 \n", "L 119.45742 22.03435 \n", "L 119.793902 20.051581 \n", "L 120.466867 21.062631 \n", "L 121.139832 18.722665 \n", "L 121.476314 19.334254 \n", "L 121.812797 19.254599 \n", "L 122.149279 19.81863 \n", "L 122.485762 19.514728 \n", "L 122.822244 20.110095 \n", "L 123.158726 19.958064 \n", "L 123.495209 20.202204 \n", "L 123.831691 19.826128 \n", "L 124.168174 21.225153 \n", "L 124.504656 23.782058 \n", "L 124.841139 22.747408 \n", "L 125.177621 22.235868 \n", "L 125.514103 22.569973 \n", "L 126.187068 19.772963 \n", "L 126.523551 22.628482 \n", "L 126.860033 21.971406 \n", "L 127.196515 20.897967 \n", "L 127.532998 21.728919 \n", "L 127.86948 23.012778 \n", "L 128.542445 24.169161 \n", "L 128.878927 24.428582 \n", "L 129.21541 23.834304 \n", "L 129.551892 22.885511 \n", "L 129.888375 23.094391 \n", "L 130.224857 22.005316 \n", "L 130.56134 22.98768 \n", "L 130.897822 25.885422 \n", "L 131.234304 24.905089 \n", "L 131.570787 32.584648 \n", "L 131.907269 24.807034 \n", "L 132.243752 23.439279 \n", "L 132.580234 25.2598 \n", "L 132.916716 23.364961 \n", "L 133.253199 28.458447 \n", "L 133.589681 25.731749 \n", "L 133.926164 26.756461 \n", "L 134.262646 28.969095 \n", "L 134.599128 36.630266 \n", "L 134.935611 27.860715 \n", "L 135.272093 28.134465 \n", "L 135.608576 25.421497 \n", "L 135.945058 29.705894 \n", "L 136.281541 27.926101 \n", "L 136.618023 36.265507 \n", "L 136.954505 39.938037 \n", "L 137.290988 48.147525 \n", "L 137.62747 29.055499 \n", "L 137.963953 31.94422 \n", "L 138.636917 40.324889 \n", "L 138.9734 49.482998 \n", "L 139.309882 43.598218 \n", "L 139.982847 41.605766 \n", "L 140.319329 41.048222 \n", "L 140.655812 46.856603 \n", "L 140.992294 42.524773 \n", "L 141.328777 47.658519 \n", "L 141.665259 44.807392 \n", "L 142.001742 30.346346 \n", "L 142.338224 37.087358 \n", "L 142.674706 34.613014 \n", "L 143.011189 37.324262 \n", "L 143.347671 46.117207 \n", "L 143.684154 45.444217 \n", "L 144.020636 37.555739 \n", "L 144.357118 32.730453 \n", "L 144.693601 29.979506 \n", "L 145.030083 30.89048 \n", "L 145.366566 39.252832 \n", "L 145.703048 43.198004 \n", "L 146.376013 35.717077 \n", "L 146.712495 29.857424 \n", "L 147.048978 41.130972 \n", "L 147.38546 38.555866 \n", "L 147.721943 51.227314 \n", "L 148.058425 40.657875 \n", "L 148.394907 37.072937 \n", "L 148.73139 47.589318 \n", "L 149.067872 50.68402 \n", "L 149.404355 58.816347 \n", "L 149.740837 50.44079 \n", "L 150.077319 50.353446 \n", "L 150.413802 54.127113 \n", "L 150.750284 47.884462 \n", "L 151.423249 44.492207 \n", "L 151.759731 44.451344 \n", "L 152.096214 42.677661 \n", "L 152.432696 42.779009 \n", "L 152.769179 41.725893 \n", "L 153.105661 43.44612 \n", "L 153.442144 51.034013 \n", "L 153.778626 54.008891 \n", "L 154.115108 51.905029 \n", "L 154.451591 56.971733 \n", "L 154.788073 74.115976 \n", "L 155.124556 76.480797 \n", "L 155.461038 77.318648 \n", "L 155.79752 75.719146 \n", "L 156.134003 77.449638 \n", "L 156.470485 76.893925 \n", "L 156.806968 77.136661 \n", "L 157.14345 76.084949 \n", "L 157.479932 76.895696 \n", "L 157.816415 76.70792 \n", "L 158.152897 76.830762 \n", "L 158.48938 77.261778 \n", "L 158.825862 79.119023 \n", "L 159.162345 78.308773 \n", "L 159.498827 76.945758 \n", "L 159.835309 59.249362 \n", "L 160.171792 61.57377 \n", "L 160.508274 77.135458 \n", "L 160.844757 76.123228 \n", "L 161.181239 77.52973 \n", "L 161.517721 77.173763 \n", "L 161.854204 74.910327 \n", "L 162.190686 63.247642 \n", "L 162.527169 65.1976 \n", "L 162.863651 77.023622 \n", "L 163.200133 78.254523 \n", "L 163.536616 78.29291 \n", "L 163.873098 78.584726 \n", "L 164.209581 78.666042 \n", "L 164.546063 78.144712 \n", "L 164.882546 76.246954 \n", "L 165.219028 76.779742 \n", "L 165.55551 78.104899 \n", "L 165.891993 78.924752 \n", "L 166.228475 78.397997 \n", "L 166.564958 79.471533 \n", "L 166.90144 78.735832 \n", "L 167.237922 79.685996 \n", "L 167.574405 89.636907 \n", "L 167.910887 95.324213 \n", "L 168.24737 95.138542 \n", "L 168.583852 93.47424 \n", "L 169.256817 80.91634 \n", "L 169.593299 80.311446 \n", "L 170.602747 91.654946 \n", "L 170.939229 89.578263 \n", "L 171.275711 85.420269 \n", "L 171.612194 83.048703 \n", "L 171.948676 85.046467 \n", "L 172.285159 78.981792 \n", "L 172.621641 78.40512 \n", "L 172.958123 78.90416 \n", "L 173.294606 88.710149 \n", "L 173.631088 94.801285 \n", "L 173.967571 92.331302 \n", "L 174.304053 97.010703 \n", "L 174.640535 96.262528 \n", "L 174.977018 96.941326 \n", "L 175.3135 96.299919 \n", "L 175.649983 96.330215 \n", "L 175.986465 95.55109 \n", "L 176.322948 91.060545 \n", "L 176.65943 93.684365 \n", "L 176.995912 101.723194 \n", "L 177.332395 101.796314 \n", "L 178.00536 108.292115 \n", "L 178.341842 107.207968 \n", "L 179.014807 90.603524 \n", "L 179.351289 100.331516 \n", "L 179.687772 98.242433 \n", "L 180.024254 102.430939 \n", "L 180.360736 108.620548 \n", "L 180.697219 105.259368 \n", "L 181.033701 99.570442 \n", "L 181.370184 101.616903 \n", "L 181.706666 108.749935 \n", "L 182.043149 110.185174 \n", "L 182.379631 108.451409 \n", "L 182.716113 99.461817 \n", "L 183.052596 93.59554 \n", "L 183.389078 93.956771 \n", "L 184.062043 104.071596 \n", "L 184.398525 104.854459 \n", "L 184.735008 109.169244 \n", "L 185.07149 107.718705 \n", "L 185.407973 109.687611 \n", "L 185.744455 117.876835 \n", "L 186.080938 121.475448 \n", "L 186.41742 120.860934 \n", "L 186.753902 117.952484 \n", "L 187.090385 116.822615 \n", "L 187.76335 106.696064 \n", "L 188.099832 106.242122 \n", "L 188.436314 115.861079 \n", "L 188.772797 118.715818 \n", "L 189.109279 117.077152 \n", "L 189.445762 111.913958 \n", "L 189.782244 121.065593 \n", "L 190.118726 125.385682 \n", "L 190.455209 123.865905 \n", "L 190.791691 116.220635 \n", "L 191.128174 112.951282 \n", "L 191.464656 115.232597 \n", "L 192.137621 118.13308 \n", "L 192.474103 118.545111 \n", "L 192.810586 120.948499 \n", "L 193.147068 126.095422 \n", "L 193.820033 115.004723 \n", "L 194.156515 117.863677 \n", "L 194.492998 114.162561 \n", "L 194.82948 114.68143 \n", "L 195.165963 118.392005 \n", "L 195.502445 117.271647 \n", "L 195.838927 116.836272 \n", "L 196.17541 123.31911 \n", "L 196.511892 125.265371 \n", "L 196.848375 130.969332 \n", "L 197.184857 130.208984 \n", "L 197.52134 131.353516 \n", "L 197.857822 136.06555 \n", "L 198.194304 131.624605 \n", "L 198.530787 130.799808 \n", "L 198.867269 123.61617 \n", "L 199.203752 120.109296 \n", "L 199.540234 118.994113 \n", "L 199.876716 116.260015 \n", "L 200.213199 117.387848 \n", "L 200.549681 112.712539 \n", "L 200.886164 115.963895 \n", "L 201.222646 111.022831 \n", "L 202.568576 129.523495 \n", "L 203.241541 118.988191 \n", "L 203.578023 123.056791 \n", "L 204.58747 113.347338 \n", "L 204.923953 113.042824 \n", "L 205.260435 111.874097 \n", "L 205.596917 114.517989 \n", "L 205.9334 118.767185 \n", "L 206.269882 114.804646 \n", "L 206.606365 119.025851 \n", "L 206.942847 119.941192 \n", "L 207.279329 119.742671 \n", "L 207.615812 124.727598 \n", "L 207.952294 126.962828 \n", "L 208.288777 131.97323 \n", "L 208.625259 129.018248 \n", "L 208.961742 131.540533 \n", "L 209.298224 131.764559 \n", "L 209.634706 132.671805 \n", "L 209.971189 135.584129 \n", "L 210.307671 136.975697 \n", "L 210.644154 137.828775 \n", "L 211.317118 120.446183 \n", "L 211.653601 119.081207 \n", "L 211.990083 120.029265 \n", "L 212.326566 124.718487 \n", "L 212.663048 122.386467 \n", "L 212.99953 116.208098 \n", "L 213.336013 119.206444 \n", "L 213.672495 117.095397 \n", "L 214.008978 119.261454 \n", "L 214.34546 125.974095 \n", "L 214.681943 121.143134 \n", "L 215.018425 125.019613 \n", "L 215.354907 126.990751 \n", "L 215.69139 123.739193 \n", "L 216.364355 129.673831 \n", "L 216.700837 130.074069 \n", "L 217.037319 132.635028 \n", "L 217.373802 132.434763 \n", "L 218.046767 127.176455 \n", "L 218.383249 121.373047 \n", "L 218.719731 122.264358 \n", "L 219.056214 124.60119 \n", "L 219.392696 120.897215 \n", "L 219.729179 126.283573 \n", "L 220.402144 119.962781 \n", "L 220.738626 120.118753 \n", "L 221.075108 121.09816 \n", "L 221.748073 134.323166 \n", "L 222.421038 125.863288 \n", "L 222.75752 126.127774 \n", "L 223.094003 127.35577 \n", "L 223.430485 132.447301 \n", "L 223.766968 129.220961 \n", "L 224.10345 128.146788 \n", "L 224.439932 133.686885 \n", "L 224.776415 132.576347 \n", "L 225.112897 134.395913 \n", "L 225.44938 135.570499 \n", "L 225.785862 129.785898 \n", "L 226.122345 126.966673 \n", "L 226.795309 116.330639 \n", "L 227.131792 115.753624 \n", "L 227.804757 125.037631 \n", "L 228.141239 125.311819 \n", "L 228.477721 124.811378 \n", "L 229.150686 120.234008 \n", "L 229.487169 124.669113 \n", "L 229.823651 117.547196 \n", "L 230.496616 113.988392 \n", "L 230.833098 108.202722 \n", "L 231.506063 114.778535 \n", "L 231.842546 117.86339 \n", "L 232.179028 113.867827 \n", "L 232.51551 116.316024 \n", "L 232.851993 120.805578 \n", "L 233.188475 120.225291 \n", "L 233.524958 121.487144 \n", "L 233.86144 121.732932 \n", "L 234.197922 121.222007 \n", "L 234.534405 122.516146 \n", "L 234.870887 123.069567 \n", "L 235.20737 121.052972 \n", "L 235.543852 115.759752 \n", "L 235.880334 113.259408 \n", "L 236.216817 105.490176 \n", "L 236.553299 102.512164 \n", "L 236.889782 107.786327 \n", "L 237.226264 109.981396 \n", "L 237.562747 116.773671 \n", "L 237.899229 120.283549 \n", "L 238.235711 113.265366 \n", "L 238.572194 116.292006 \n", "L 238.908676 117.450881 \n", "L 239.245159 119.57212 \n", "L 239.581641 107.726586 \n", "L 239.918123 109.439211 \n", "L 240.254606 113.789978 \n", "L 240.591088 121.043843 \n", "L 241.264053 113.752793 \n", "L 241.937018 101.836118 \n", "L 242.2735 99.203065 \n", "L 242.609983 102.375945 \n", "L 242.946465 108.030046 \n", "L 243.282948 105.670492 \n", "L 243.61943 104.983845 \n", "L 243.955912 102.972423 \n", "L 244.292395 103.599293 \n", "L 244.628877 105.511266 \n", "L 244.96536 102.114416 \n", "L 245.301842 101.251252 \n", "L 245.638324 105.984973 \n", "L 245.974807 104.966567 \n", "L 246.311289 111.56456 \n", "L 246.647772 114.219231 \n", "L 247.320736 103.632033 \n", "L 247.993701 115.849329 \n", "L 248.330184 121.771344 \n", "L 249.003149 102.098148 \n", "L 249.339631 99.998889 \n", "L 249.676113 94.97237 \n", "L 250.012596 99.179951 \n", "L 250.349078 95.138005 \n", "L 250.685561 86.025906 \n", "L 251.022043 85.733758 \n", "L 251.358525 83.317515 \n", "L 252.367973 99.68624 \n", "L 252.704455 98.152641 \n", "L 253.040938 98.947229 \n", "L 253.713902 98.340073 \n", "L 254.050385 101.402776 \n", "L 255.059832 88.749222 \n", "L 255.396314 88.998391 \n", "L 255.732797 93.763629 \n", "L 256.069279 93.483596 \n", "L 256.405762 87.521922 \n", "L 256.742244 79.427383 \n", "L 257.078726 78.433677 \n", "L 257.415209 78.500469 \n", "L 257.751691 78.036987 \n", "L 258.088174 78.568748 \n", "L 258.424656 82.00292 \n", "L 258.761139 80.043521 \n", "L 259.097621 79.44821 \n", "L 259.770586 82.30995 \n", "L 260.107068 84.203156 \n", "L 260.443551 88.772127 \n", "L 260.780033 86.864708 \n", "L 261.116515 87.996638 \n", "L 261.78948 92.229439 \n", "L 262.125963 90.505111 \n", "L 262.462445 78.58674 \n", "L 263.13541 79.320206 \n", "L 263.471892 77.019396 \n", "L 263.808375 77.137401 \n", "L 264.144857 77.627059 \n", "L 264.48134 78.63 \n", "L 265.154304 78.481791 \n", "L 265.490787 79.06009 \n", "L 265.827269 77.959979 \n", "L 266.163752 77.324224 \n", "L 266.500234 77.526023 \n", "L 266.836716 76.493157 \n", "L 267.173199 78.156704 \n", "L 267.509681 76.71587 \n", "L 267.846164 76.85809 \n", "L 268.855611 78.923002 \n", "L 269.192093 78.335144 \n", "L 269.528576 76.367154 \n", "L 270.201541 77.128053 \n", "L 270.538023 74.550868 \n", "L 270.874505 76.591105 \n", "L 271.210988 77.025859 \n", "L 271.54747 77.977591 \n", "L 271.883953 76.954422 \n", "L 272.220435 77.693931 \n", "L 272.556917 76.909862 \n", "L 272.8934 77.189621 \n", "L 273.229882 76.419446 \n", "L 273.566365 77.234867 \n", "L 273.902847 62.948827 \n", "L 274.239329 58.629017 \n", "L 274.575812 57.389178 \n", "L 274.912294 76.743444 \n", "L 275.248777 76.22232 \n", "L 275.585259 63.191183 \n", "L 275.921742 59.875851 \n", "L 276.258224 60.1946 \n", "L 276.594706 74.428621 \n", "L 276.931189 57.797225 \n", "L 277.267671 60.544788 \n", "L 277.604154 56.886823 \n", "L 277.940636 55.500251 \n", "L 278.277118 55.85894 \n", "L 278.613601 56.386817 \n", "L 278.950083 61.657007 \n", "L 279.286566 76.19756 \n", "L 279.623048 61.726105 \n", "L 279.95953 56.23456 \n", "L 280.296013 52.75274 \n", "L 280.632495 50.536353 \n", "L 280.968978 43.646529 \n", "L 281.30546 42.653804 \n", "L 281.978425 46.675055 \n", "L 282.314907 49.841063 \n", "L 282.65139 49.629459 \n", "L 282.987872 53.719912 \n", "L 283.324355 50.113551 \n", "L 283.660837 50.114569 \n", "L 283.997319 50.824406 \n", "L 284.333802 37.126191 \n", "L 284.670284 48.112611 \n", "L 285.006767 40.528464 \n", "L 285.679731 34.953179 \n", "L 286.016214 40.200747 \n", "L 286.352696 31.573798 \n", "L 286.689179 38.22672 \n", "L 287.025661 37.740117 \n", "L 287.362144 32.120864 \n", "L 287.698626 41.216233 \n", "L 288.035108 32.453838 \n", "L 288.371591 45.140297 \n", "L 288.708073 39.419625 \n", "L 289.044556 42.571493 \n", "L 289.381038 31.861697 \n", "L 289.71752 38.508144 \n", "L 290.054003 26.573253 \n", "L 290.390485 31.33904 \n", "L 290.726968 29.719246 \n", "L 291.06345 33.953257 \n", "L 291.399932 42.918353 \n", "L 291.736415 37.797282 \n", "L 292.072897 37.027647 \n", "L 292.40938 37.638946 \n", "L 292.745862 31.925023 \n", "L 293.082345 28.193794 \n", "L 293.418827 44.304781 \n", "L 293.755309 34.847831 \n", "L 294.091792 37.867856 \n", "L 294.428274 31.282073 \n", "L 294.764757 31.576446 \n", "L 295.101239 30.317438 \n", "L 295.437721 27.518983 \n", "L 295.774204 31.985865 \n", "L 296.110686 28.330873 \n", "L 296.447169 32.470638 \n", "L 296.783651 34.388841 \n", "L 297.120133 38.448364 \n", "L 297.456616 24.613378 \n", "L 297.793098 24.732442 \n", "L 298.129581 23.41406 \n", "L 298.466063 23.216297 \n", "L 298.802546 24.293921 \n", "L 299.139028 24.507857 \n", "L 299.47551 22.364192 \n", "L 299.811993 21.615139 \n", "L 300.148475 22.659688 \n", "L 300.484958 23.287659 \n", "L 300.82144 24.449203 \n", "L 301.157922 28.138521 \n", "L 301.494405 24.74883 \n", "L 301.830887 23.669729 \n", "L 302.16737 27.176311 \n", "L 302.503852 23.339021 \n", "L 302.840334 28.982468 \n", "L 303.176817 23.300913 \n", "L 303.513299 30.765109 \n", "L 303.849782 26.052728 \n", "L 304.186264 23.055137 \n", "L 304.522747 21.694035 \n", "L 304.859229 21.045079 \n", "L 305.195711 22.352425 \n", "L 305.532194 22.711665 \n", "L 305.868676 25.470268 \n", "L 306.541641 22.782221 \n", "L 306.878123 21.487549 \n", "L 307.214606 20.915716 \n", "L 307.551088 22.313845 \n", "L 307.887571 21.779559 \n", "L 308.224053 21.80544 \n", "L 308.560535 20.621916 \n", "L 308.897018 21.552082 \n", "L 309.2335 20.87165 \n", "L 309.569983 20.87802 \n", "L 309.906465 21.747134 \n", "L 310.242948 21.036607 \n", "L 310.57943 22.242173 \n", "L 310.915912 21.744478 \n", "L 311.252395 19.48908 \n", "L 311.588877 21.701492 \n", "L 311.92536 20.486488 \n", "L 312.261842 21.428737 \n", "L 312.598324 20.183953 \n", "L 312.934807 20.930899 \n", "L 313.271289 20.48647 \n", "L 313.607772 19.363872 \n", "L 313.944254 18.971504 \n", "L 314.617219 22.566821 \n", "L 314.953701 21.363082 \n", "L 315.290184 22.58112 \n", "L 315.963149 20.17821 \n", "L 316.299631 19.338839 \n", "L 316.636113 20.106209 \n", "L 316.972596 21.296121 \n", "L 317.309078 20.914934 \n", "L 317.982043 21.165606 \n", "L 318.318525 24.429415 \n", "L 318.655008 22.338837 \n", "L 318.99149 22.435948 \n", "L 319.664455 21.462648 \n", "L 320.673902 24.173248 \n", "L 321.010385 23.564492 \n", "L 321.346867 25.029308 \n", "L 321.68335 25.589147 \n", "L 322.019832 23.858752 \n", "L 322.356314 22.835338 \n", "L 322.692797 20.864426 \n", "L 323.029279 22.579699 \n", "L 323.365762 20.271506 \n", "L 323.702244 23.685371 \n", "L 324.375209 20.705201 \n", "L 324.711691 22.559914 \n", "L 325.048174 21.155217 \n", "L 325.384656 20.791452 \n", "L 325.721139 24.102889 \n", "L 326.057621 22.124076 \n", "L 326.394103 26.365105 \n", "L 326.730586 23.983595 \n", "L 327.067068 22.706333 \n", "L 327.740033 24.647895 \n", "L 328.076515 24.717102 \n", "L 328.412998 24.434695 \n", "L 328.74948 26.152347 \n", "L 329.085963 24.055563 \n", "L 329.422445 22.922371 \n", "L 329.758927 23.657299 \n", "L 330.09541 21.88119 \n", "L 330.431892 24.94208 \n", "L 331.104857 23.81053 \n", "L 331.44134 25.560627 \n", "L 331.777822 23.452831 \n", "L 332.114304 23.404048 \n", "L 332.450787 20.782407 \n", "L 332.787269 22.293182 \n", "L 333.123752 21.27101 \n", "L 333.460234 22.311147 \n", "L 333.796716 22.187026 \n", "L 334.133199 20.104149 \n", "L 334.469681 22.165193 \n", "L 334.806164 21.809589 \n", "L 335.142646 20.893358 \n", "L 335.815611 20.823548 \n", "L 336.152093 21.442845 \n", "L 336.488576 22.516641 \n", "L 336.825058 21.809971 \n", "L 337.161541 22.048374 \n", "L 337.498023 20.157463 \n", "L 338.170988 23.418681 \n", "L 338.50747 22.724417 \n", "L 338.843953 24.625118 \n", "L 339.180435 24.769096 \n", "L 339.516917 26.480506 \n", "L 339.8534 24.162331 \n", "L 340.189882 23.487834 \n", "L 340.526365 22.543818 \n", "L 340.862847 25.11473 \n", "L 341.199329 23.836877 \n", "L 341.535812 35.853637 \n", "L 341.872294 28.618452 \n", "L 342.208777 25.797857 \n", "L 342.545259 27.675463 \n", "L 343.218224 22.514588 \n", "L 343.554706 22.339648 \n", "L 343.891189 22.539872 \n", "L 344.227671 25.35315 \n", "L 344.564154 22.578744 \n", "L 344.900636 20.846671 \n", "L 345.237118 22.976032 \n", "L 345.573601 28.436462 \n", "L 345.910083 30.092042 \n", "L 346.246566 40.841101 \n", "L 346.583048 25.985569 \n", "L 346.91953 42.538442 \n", "L 347.256013 40.784279 \n", "L 347.592495 28.533824 \n", "L 347.928978 25.892365 \n", "L 348.26546 24.201547 \n", "L 348.601943 24.77338 \n", "L 348.938425 33.983049 \n", "L 349.274907 34.886587 \n", "L 349.61139 31.636256 \n", "L 349.947872 24.761565 \n", "L 350.284355 29.032764 \n", "L 350.620837 26.833038 \n", "L 350.957319 29.309152 \n", "L 351.293802 27.438102 \n", "L 351.630284 28.399041 \n", "L 351.966767 30.267775 \n", "L 352.303249 41.998967 \n", "L 352.639731 45.87307 \n", "L 352.976214 38.256218 \n", "L 353.312696 51.736151 \n", "L 353.649179 51.099786 \n", "L 353.985661 49.448421 \n", "L 354.322144 43.731165 \n", "L 354.658626 47.381394 \n", "L 354.995108 44.07039 \n", "L 355.331591 47.532082 \n", "L 355.668073 52.639683 \n", "L 356.004556 44.621647 \n", "L 356.341038 43.577985 \n", "L 356.67752 37.721717 \n", "L 357.014003 46.630711 \n", "L 357.686968 42.019576 \n", "L 358.02345 42.074789 \n", "L 358.359932 49.215578 \n", "L 358.696415 36.076729 \n", "L 359.032897 37.612737 \n", "L 359.36938 45.353867 \n", "L 359.705862 48.429976 \n", "L 360.042345 48.914382 \n", "L 360.378827 37.03685 \n", "L 360.715309 48.226773 \n", "L 361.051792 48.735827 \n", "L 361.388274 54.456057 \n", "L 361.724757 53.337983 \n", "L 362.061239 52.997759 \n", "L 362.397721 34.856232 \n", "L 362.734204 39.294093 \n", "L 363.070686 51.240642 \n", "L 363.407169 50.11612 \n", "L 363.743651 47.585805 \n", "L 364.080133 42.363195 \n", "L 364.416616 44.279816 \n", "L 364.753098 48.373002 \n", "L 365.089581 55.778578 \n", "L 365.426063 57.143817 \n", "L 365.762546 52.023235 \n", "L 366.099028 62.767786 \n", "L 366.43551 54.883581 \n", "L 366.771993 55.689583 \n", "L 367.108475 48.346619 \n", "L 367.444958 55.362442 \n", "L 367.78144 74.05106 \n", "L 368.117922 77.742939 \n", "L 368.454405 77.140024 \n", "L 368.790887 64.032779 \n", "L 369.12737 68.11653 \n", "L 369.463852 73.894895 \n", "L 369.800334 77.344002 \n", "L 370.136817 77.605229 \n", "L 370.473299 74.930453 \n", "L 370.809782 78.37582 \n", "L 370.809782 78.37582 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 73.359329 78.256809 \n", "L 75.378224 78.242827 \n", "L 75.714706 48.820431 \n", "L 76.051189 54.647892 \n", "L 76.387671 22.187062 \n", "L 76.724154 27.937937 \n", "L 77.060636 75.739546 \n", "L 77.397118 78.266412 \n", "L 78.070083 78.261863 \n", "L 78.406566 59.619316 \n", "L 78.743048 65.421682 \n", "L 79.07953 46.701971 \n", "L 79.416013 36.059451 \n", "L 79.752495 51.464802 \n", "L 80.088978 24.99743 \n", "L 80.42546 21.825667 \n", "L 81.098425 21.352438 \n", "L 81.434907 23.313408 \n", "L 81.77139 78.253706 \n", "L 84.799731 78.258527 \n", "L 85.136214 65.291757 \n", "L 85.472696 47.312187 \n", "L 85.809179 47.815721 \n", "L 86.482144 24.935958 \n", "L 86.818626 24.679149 \n", "L 87.155108 23.357021 \n", "L 87.491591 52.730032 \n", "L 87.828073 63.660396 \n", "L 88.501038 24.88719 \n", "L 88.83752 21.058888 \n", "L 89.174003 21.123862 \n", "L 89.510485 20.712341 \n", "L 89.846968 22.172542 \n", "L 90.519932 20.933478 \n", "L 90.856415 20.86562 \n", "L 91.192897 20.591384 \n", "L 91.52938 38.088512 \n", "L 91.865862 21.044948 \n", "L 92.202345 26.571886 \n", "L 92.538827 20.837351 \n", "L 92.875309 20.816258 \n", "L 93.211792 20.479914 \n", "L 93.548274 20.419872 \n", "L 94.894204 20.659087 \n", "L 95.903651 20.600638 \n", "L 96.240133 20.978195 \n", "L 96.576616 21.08859 \n", "L 96.913098 20.868605 \n", "L 97.249581 20.406469 \n", "L 99.604958 20.502536 \n", "L 99.94144 20.737828 \n", "L 100.277922 20.475652 \n", "L 100.950887 20.380666 \n", "L 105.998123 20.452917 \n", "L 107.007571 20.37583 \n", "L 108.017018 20.421132 \n", "L 108.689983 20.490494 \n", "L 109.026465 20.686394 \n", "L 109.362948 21.050554 \n", "L 109.69943 20.515915 \n", "L 110.372395 20.404463 \n", "L 114.073701 20.504613 \n", "L 114.410184 20.780097 \n", "L 114.746666 20.53142 \n", "L 115.756113 20.393794 \n", "L 118.447973 20.414857 \n", "L 119.120937 20.483276 \n", "L 120.80335 20.400576 \n", "L 131.907269 20.371155 \n", "L 132.580234 20.387496 \n", "L 134.599128 20.405967 \n", "L 135.945058 20.347925 \n", "L 138.9734 20.349047 \n", "L 144.020636 20.409902 \n", "L 148.058425 20.447675 \n", "L 149.067872 20.417406 \n", "L 149.404355 20.524178 \n", "L 150.077319 20.488237 \n", "L 150.413802 20.533969 \n", "L 150.750284 20.821244 \n", "L 151.086767 20.512028 \n", "L 152.432696 20.513252 \n", "L 153.105661 21.12554 \n", "L 153.442144 24.354808 \n", "L 153.778626 20.536309 \n", "L 154.451591 20.770539 \n", "L 154.788073 21.1768 \n", "L 155.124556 26.044358 \n", "L 155.461038 21.830706 \n", "L 156.134003 21.377065 \n", "L 156.470485 21.283332 \n", "L 156.806968 23.285038 \n", "L 157.14345 21.561592 \n", "L 157.479932 23.899546 \n", "L 158.152897 20.564721 \n", "L 158.48938 20.852874 \n", "L 158.825862 20.70886 \n", "L 159.162345 20.870719 \n", "L 159.498827 22.831988 \n", "L 159.835309 22.498988 \n", "L 160.171792 20.889997 \n", "L 160.508274 20.633433 \n", "L 161.181239 20.57785 \n", "L 161.517721 21.045652 \n", "L 161.854204 21.719176 \n", "L 162.190686 21.079885 \n", "L 162.863651 20.552871 \n", "L 163.200133 21.294061 \n", "L 163.536616 20.975729 \n", "L 163.873098 29.896257 \n", "L 164.209581 21.226102 \n", "L 164.546063 20.853011 \n", "L 164.882546 23.829725 \n", "L 165.219028 28.39128 \n", "L 165.55551 51.187145 \n", "L 165.891993 27.86731 \n", "L 166.228475 27.652095 \n", "L 166.564958 38.019559 \n", "L 166.90144 24.116176 \n", "L 167.237922 22.864902 \n", "L 167.574405 22.113019 \n", "L 167.910887 22.097676 \n", "L 168.24737 21.594679 \n", "L 168.583852 21.61733 \n", "L 168.920334 21.399095 \n", "L 169.256817 21.786221 \n", "L 169.929782 37.711039 \n", "L 170.266264 31.839956 \n", "L 170.602747 46.002008 \n", "L 170.939229 78.255835 \n", "L 175.649983 78.264324 \n", "L 175.986465 52.419649 \n", "L 176.322948 58.899587 \n", "L 176.65943 78.264476 \n", "L 178.00536 78.258123 \n", "L 178.341842 63.611304 \n", "L 178.678324 78.1919 \n", "L 179.351289 78.267184 \n", "L 183.389078 78.2712 \n", "L 183.725561 83.399334 \n", "L 184.062043 92.756074 \n", "L 184.398525 92.564181 \n", "L 184.735008 89.791815 \n", "L 185.07149 79.498264 \n", "L 185.407973 78.27508 \n", "L 186.080938 78.310123 \n", "L 186.41742 79.579134 \n", "L 186.753902 86.753524 \n", "L 187.090385 83.402441 \n", "L 187.426867 78.392376 \n", "L 187.76335 78.287392 \n", "L 188.099832 78.35323 \n", "L 188.772797 78.268029 \n", "L 189.109279 78.269194 \n", "L 189.445762 81.029085 \n", "L 189.782244 91.971289 \n", "L 190.118726 87.935182 \n", "L 190.455209 95.479471 \n", "L 190.791691 94.525165 \n", "L 191.128174 95.348822 \n", "L 191.464656 94.571823 \n", "L 191.801139 94.407155 \n", "L 192.137621 93.292265 \n", "L 192.474103 85.848668 \n", "L 192.810586 90.056658 \n", "L 193.147068 103.309767 \n", "L 193.483551 103.522444 \n", "L 194.156515 114.242855 \n", "L 194.492998 112.3971 \n", "L 195.165963 85.146527 \n", "L 195.502445 100.81833 \n", "L 195.838927 97.844047 \n", "L 196.17541 104.219843 \n", "L 196.511892 114.872286 \n", "L 196.848375 109.180993 \n", "L 197.184857 99.934389 \n", "L 197.52134 103.105138 \n", "L 197.857822 114.938934 \n", "L 198.194304 117.257309 \n", "L 198.530787 114.508735 \n", "L 199.203752 90.002356 \n", "L 199.540234 90.592147 \n", "L 200.213199 107.212064 \n", "L 200.549681 108.516798 \n", "L 200.886164 115.555057 \n", "L 201.222646 113.360929 \n", "L 201.559128 116.351583 \n", "L 201.895611 129.918043 \n", "L 202.232093 135.876485 \n", "L 202.568576 134.903537 \n", "L 202.905058 130.182028 \n", "L 203.241541 128.25221 \n", "L 203.914505 111.572664 \n", "L 204.250988 110.859594 \n", "L 204.58747 126.434882 \n", "L 204.923953 131.453123 \n", "L 205.260435 128.597157 \n", "L 205.596917 120.356033 \n", "L 205.9334 134.923221 \n", "L 206.269882 142.495627 \n", "L 206.606365 139.721369 \n", "L 206.942847 127.537676 \n", "L 207.279329 121.785219 \n", "L 207.615812 125.670677 \n", "L 208.288777 130.41332 \n", "L 208.625259 131.083739 \n", "L 208.961742 134.996063 \n", "L 209.298224 143.440258 \n", "L 209.971189 125.238039 \n", "L 210.307671 129.957297 \n", "L 210.644154 123.977983 \n", "L 210.980636 124.59276 \n", "L 211.317118 130.871665 \n", "L 211.653601 128.996475 \n", "L 211.990083 128.249947 \n", "L 212.326566 138.834852 \n", "L 212.663048 142.211146 \n", "L 212.99953 151.343647 \n", "L 213.336013 150.476109 \n", "L 213.672495 152.02769 \n", "L 214.008978 159.970465 \n", "L 214.34546 152.713322 \n", "L 214.681943 151.217699 \n", "L 215.018425 139.685775 \n", "L 215.354907 133.57237 \n", "L 215.69139 131.942865 \n", "L 216.027872 127.308856 \n", "L 216.364355 129.164536 \n", "L 216.700837 121.611503 \n", "L 217.037319 126.660753 \n", "L 217.373802 118.958829 \n", "L 218.046767 136.238519 \n", "L 218.383249 144.097083 \n", "L 218.719731 149.255176 \n", "L 219.392696 131.908059 \n", "L 219.729179 138.389653 \n", "L 220.402144 129.736089 \n", "L 220.738626 122.740806 \n", "L 221.075108 121.89485 \n", "L 221.411591 120.226181 \n", "L 221.748073 124.288046 \n", "L 222.084556 131.488341 \n", "L 222.421038 124.979594 \n", "L 222.75752 131.728409 \n", "L 223.094003 133.509844 \n", "L 223.430485 132.952566 \n", "L 223.766968 141.230359 \n", "L 224.10345 144.948167 \n", "L 224.439932 153.075445 \n", "L 224.776415 148.497777 \n", "L 225.112897 152.316076 \n", "L 225.44938 153.017265 \n", "L 225.785862 154.208938 \n", "L 226.122345 159.198343 \n", "L 226.795309 162.867273 \n", "L 227.468274 134.269202 \n", "L 227.804757 132.017815 \n", "L 228.141239 133.495426 \n", "L 228.477721 141.153928 \n", "L 228.814204 137.53993 \n", "L 229.150686 127.283733 \n", "L 229.487169 132.098939 \n", "L 229.823651 128.831386 \n", "L 230.160133 132.08898 \n", "L 230.496616 143.31557 \n", "L 230.833098 135.485352 \n", "L 231.169581 141.546392 \n", "L 231.506063 145.145059 \n", "L 231.842546 139.596795 \n", "L 232.51551 149.433757 \n", "L 232.851993 150.044283 \n", "L 233.188475 154.248598 \n", "L 233.524958 154.024691 \n", "L 234.197922 145.341061 \n", "L 234.534405 135.875798 \n", "L 234.870887 137.10814 \n", "L 235.20737 141.107026 \n", "L 235.543852 135.001502 \n", "L 235.880334 143.674332 \n", "L 236.216817 139.560329 \n", "L 236.553299 133.322845 \n", "L 236.889782 133.767178 \n", "L 237.226264 135.233964 \n", "L 237.899229 157.026832 \n", "L 238.572194 143.119246 \n", "L 238.908676 143.635795 \n", "L 239.245159 145.538998 \n", "L 239.581641 153.895806 \n", "L 239.918123 148.816097 \n", "L 240.254606 146.817848 \n", "L 240.591088 155.993364 \n", "L 240.927571 154.255142 \n", "L 241.600535 159.213692 \n", "L 241.937018 149.683133 \n", "L 242.2735 144.95917 \n", "L 242.946465 127.423096 \n", "L 243.282948 126.530358 \n", "L 243.955912 141.764896 \n", "L 244.292395 142.215355 \n", "L 244.628877 141.431579 \n", "L 245.301842 133.871537 \n", "L 245.638324 141.080489 \n", "L 245.974807 129.683151 \n", "L 246.647772 123.789993 \n", "L 246.984254 114.033694 \n", "L 247.657219 124.905474 \n", "L 247.993701 129.853786 \n", "L 248.330184 123.539423 \n", "L 248.666666 127.251076 \n", "L 249.003149 134.859363 \n", "L 249.339631 133.806784 \n", "L 249.676113 135.91913 \n", "L 250.012596 136.370867 \n", "L 250.349078 135.485711 \n", "L 250.685561 137.598295 \n", "L 251.022043 138.559941 \n", "L 251.358525 135.250562 \n", "L 251.695008 126.596233 \n", "L 252.03149 122.383834 \n", "L 252.367973 109.784059 \n", "L 252.704455 104.596954 \n", "L 253.040938 113.332093 \n", "L 253.37742 116.955562 \n", "L 253.713902 127.992613 \n", "L 254.050385 134.003055 \n", "L 254.386867 122.500331 \n", "L 254.72335 127.220927 \n", "L 255.059832 129.417727 \n", "L 255.396314 132.606276 \n", "L 255.732797 113.648368 \n", "L 256.069279 115.785833 \n", "L 256.405762 123.461512 \n", "L 256.742244 134.846468 \n", "L 257.415209 123.04546 \n", "L 258.424656 99.338511 \n", "L 258.761139 104.32188 \n", "L 259.097621 113.732252 \n", "L 259.434103 109.927353 \n", "L 259.770586 108.715842 \n", "L 260.107068 105.539469 \n", "L 260.443551 106.376099 \n", "L 260.780033 109.635258 \n", "L 261.116515 104.084032 \n", "L 261.452998 102.571496 \n", "L 261.78948 110.329818 \n", "L 262.125963 108.789657 \n", "L 262.462445 119.355473 \n", "L 262.798927 124.089513 \n", "L 263.471892 106.658464 \n", "L 264.144857 126.735662 \n", "L 264.48134 136.137843 \n", "L 265.154304 103.940263 \n", "L 265.490787 100.688708 \n", "L 265.827269 92.282497 \n", "L 266.163752 99.049905 \n", "L 266.500234 92.741593 \n", "L 266.836716 78.493211 \n", "L 267.509681 78.29208 \n", "L 267.846164 82.346153 \n", "L 268.182646 93.025313 \n", "L 268.519128 99.823537 \n", "L 268.855611 97.663164 \n", "L 269.192093 98.657151 \n", "L 269.528576 98.480908 \n", "L 269.865058 97.703702 \n", "L 270.201541 102.850825 \n", "L 271.210988 81.540337 \n", "L 271.54747 81.954059 \n", "L 271.883953 90.196558 \n", "L 272.220435 89.845354 \n", "L 272.556917 79.209627 \n", "L 272.8934 78.271134 \n", "L 276.258224 78.310472 \n", "L 276.594706 81.330715 \n", "L 276.931189 78.731599 \n", "L 277.267671 79.560258 \n", "L 277.940636 87.645479 \n", "L 278.277118 84.987357 \n", "L 278.613601 78.26972 \n", "L 289.71752 78.264102 \n", "L 290.390485 50.658948 \n", "L 290.726968 47.179224 \n", "L 291.06345 78.26282 \n", "L 291.399932 78.262121 \n", "L 291.736415 63.458924 \n", "L 292.072897 54.163649 \n", "L 292.40938 55.041859 \n", "L 292.745862 78.256775 \n", "L 293.082345 48.360127 \n", "L 293.418827 56.02514 \n", "L 293.755309 45.785134 \n", "L 294.091792 41.901235 \n", "L 294.428274 42.898413 \n", "L 294.764757 44.375599 \n", "L 295.437721 78.261667 \n", "L 296.110686 43.966968 \n", "L 296.447169 34.196159 \n", "L 297.120133 21.845064 \n", "L 297.456616 21.58887 \n", "L 297.793098 22.092058 \n", "L 298.129581 23.164356 \n", "L 298.466063 26.651863 \n", "L 298.802546 26.290946 \n", "L 299.139028 36.893454 \n", "L 299.47551 27.181627 \n", "L 299.811993 27.178863 \n", "L 300.148475 28.78546 \n", "L 300.484958 20.857638 \n", "L 300.82144 24.311566 \n", "L 301.157922 21.205917 \n", "L 301.494405 20.866199 \n", "L 301.830887 20.72541 \n", "L 302.16737 21.159235 \n", "L 302.503852 20.597014 \n", "L 302.840334 20.944809 \n", "L 303.176817 20.903316 \n", "L 303.513299 20.613462 \n", "L 303.849782 21.309004 \n", "L 304.186264 20.624006 \n", "L 304.522747 22.359231 \n", "L 304.859229 21.065222 \n", "L 305.195711 21.570744 \n", "L 305.532194 20.605337 \n", "L 305.868676 20.969848 \n", "L 306.205159 20.484732 \n", "L 306.541641 20.589868 \n", "L 306.878123 20.549623 \n", "L 307.214606 20.679636 \n", "L 307.551088 21.649008 \n", "L 307.887571 20.908809 \n", "L 308.560535 20.895185 \n", "L 308.897018 20.60714 \n", "L 309.2335 20.519061 \n", "L 309.569983 22.041729 \n", "L 309.906465 20.720848 \n", "L 310.242948 20.914015 \n", "L 310.57943 20.588489 \n", "L 312.934807 20.698442 \n", "L 313.271289 20.9646 \n", "L 313.607772 20.443687 \n", "L 320.673902 20.38217 \n", "L 321.68335 20.403657 \n", "L 322.356314 20.436798 \n", "L 324.038726 20.384146 \n", "L 357.350485 20.427293 \n", "L 357.686968 20.772891 \n", "L 358.02345 20.527605 \n", "L 359.36938 20.399526 \n", "L 361.388274 20.408976 \n", "L 362.061239 20.558471 \n", "L 362.397721 21.250616 \n", "L 362.734204 20.472523 \n", "L 363.070686 21.558279 \n", "L 363.407169 21.242389 \n", "L 363.743651 20.526297 \n", "L 364.753098 20.44681 \n", "L 365.089581 20.680633 \n", "L 365.426063 20.722234 \n", "L 366.099028 20.446589 \n", "L 366.43551 20.535026 \n", "L 367.444958 20.502637 \n", "L 368.117922 20.562297 \n", "L 368.454405 21.448295 \n", "L 368.790887 22.704691 \n", "L 369.12737 20.949126 \n", "L 369.463852 31.306527 \n", "L 369.800334 29.55937 \n", "L 370.473299 21.870461 \n", "L 370.809782 23.664678 \n", "L 371.146264 21.971537 \n", "L 371.482747 23.797629 \n", "L 371.819229 33.861895 \n", "L 372.155711 22.164978 \n", "L 372.492194 21.823517 \n", "L 372.828676 20.902343 \n", "L 373.165159 23.130708 \n", "L 373.838123 21.456779 \n", "L 374.174606 21.465824 \n", "L 374.511088 25.631518 \n", "L 374.847571 20.78787 \n", "L 375.184053 20.893567 \n", "L 375.520535 22.449686 \n", "L 375.857018 24.65679 \n", "L 376.1935 25.249887 \n", "L 376.529983 20.851214 \n", "L 376.866465 24.42357 \n", "L 377.202948 25.011576 \n", "L 377.53943 38.973405 \n", "L 377.875912 35.84709 \n", "L 378.212395 34.880494 \n", "L 378.548877 20.721828 \n", "L 378.88536 21.050291 \n", "L 379.221842 29.908416 \n", "L 380.231289 21.525442 \n", "L 380.567772 22.037806 \n", "L 380.904254 24.58819 \n", "L 381.240736 42.661163 \n", "L 381.577219 46.505972 \n", "L 381.913701 32.176109 \n", "L 382.250184 62.241448 \n", "L 382.586666 40.189634 \n", "L 382.923149 42.423796 \n", "L 383.259631 24.57384 \n", "L 383.596113 41.483672 \n", "L 383.932596 78.255685 \n", "L 384.605561 78.264856 \n", "L 384.942043 76.098341 \n", "L 385.278525 78.237196 \n", "L 386.960938 78.266591 \n", "L 386.960938 78.266591 \n", "\" clip-path=\"url(#p48090c1046)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.28 \n", "L 52.160938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 386.**********.28 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.28 \n", "L 386.**********.28 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 7.2 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 59.160938 165.28 \n", "L 160.248438 165.28 \n", "Q 162.248438 165.28 162.248438 163.28 \n", "L 162.248438 105.5675 \n", "Q 162.248438 103.5675 160.248438 103.5675 \n", "L 59.160938 103.5675 \n", "Q 57.160938 103.5675 57.160938 105.5675 \n", "L 57.160938 163.28 \n", "Q 57.160938 165.28 59.160938 165.28 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 61.160938 111.665938 \n", "L 71.160938 111.665938 \n", "L 81.160938 111.665938 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- 1-step preds -->\n", "     <g transform=\"translate(89.160938 115.165938)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"99.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"151.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"191.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"252.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"316.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"347.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"411.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"450.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"511.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"575.142578\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 61.160938 126.344063 \n", "L 71.160938 126.344063 \n", "L 81.160938 126.344063 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- 4-step preds -->\n", "     <g transform=\"translate(89.160938 129.844063)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-34\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"99.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"151.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"191.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"252.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"316.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"347.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"411.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"450.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"511.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"575.142578\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_29\">\n", "     <path d=\"M 61.160938 141.022188 \n", "L 71.160938 141.022188 \n", "L 81.160938 141.022188 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- 16-step preds -->\n", "     <g transform=\"translate(89.160938 144.522188)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"127.246094\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"163.330078\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"215.429688\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"254.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"316.162109\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"379.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"411.425781\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"474.902344\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"513.765625\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"575.289062\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"638.765625\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 61.160938 155.700313 \n", "L 71.160938 155.700313 \n", "L 81.160938 155.700313 \n", "\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- 64-step preds -->\n", "     <g transform=\"translate(89.160938 159.200313)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-36\"/>\n", "      <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"127.246094\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"163.330078\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"215.429688\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"254.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"316.162109\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"379.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"411.425781\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"474.902344\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"513.765625\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"575.289062\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"638.765625\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p48090c1046\">\n", "   <rect x=\"52.160938\" y=\"7.2\" width=\"334.8\" height=\"163.08\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 432x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["steps = (1, 4, 16, 64)\n", "d2l.plot([time[tau + i - 1: T - max_steps + i] for i in steps],\n", "         [features[:, (tau + i - 1)].detach().numpy() for i in steps], 'time', 'x',\n", "         legend=[f'{i}-step preds' for i in steps], xlim=[5, 1000],\n", "         figsize=(6, 3))"]}, {"cell_type": "markdown", "id": "13e7b084", "metadata": {"origin_pos": 34}, "source": ["以上例子清楚地说明了当我们试图预测更远的未来时，预测的质量是如何变化的。\n", "虽然“$4$步预测”看起来仍然不错，但超过这个跨度的任何预测几乎都是无用的。\n", "\n", "## 小结\n", "\n", "* 内插法（在现有观测值之间进行估计）和外推法（对超出已知观测范围进行预测）在实践的难度上差别很大。因此，对于所拥有的序列数据，在训练时始终要尊重其时间顺序，即最好不要基于未来的数据进行训练。\n", "* 序列模型的估计需要专门的统计工具，两种较流行的选择是自回归模型和隐变量自回归模型。\n", "* 对于时间是向前推进的因果模型，正向估计通常比反向估计更容易。\n", "* 对于直到时间步$t$的观测序列，其在时间步$t+k$的预测输出是“$k$步预测”。随着我们对预测时间$k$值的增加，会造成误差的快速累积和预测质量的极速下降。\n", "\n", "## 练习\n", "\n", "1. 改进本节实验中的模型。\n", "    1. 是否包含了过去$4$个以上的观测结果？真实值需要是多少个？\n", "    1. 如果没有噪音，需要多少个过去的观测结果？提示：把$\\sin$和$\\cos$写成微分方程。\n", "    1. 可以在保持特征总数不变的情况下合并旧的观察结果吗？这能提高正确度吗？为什么？\n", "    1. 改变神经网络架构并评估其性能。\n", "1. 一位投资者想要找到一种好的证券来购买。他查看过去的回报，以决定哪一种可能是表现良好的。这一策略可能会出什么问题呢？\n", "1. 时间是向前推进的因果模型在多大程度上适用于文本呢？\n", "1. 举例说明什么时候可能需要隐变量自回归模型来捕捉数据的动力学模型。\n"]}, {"cell_type": "markdown", "id": "0938a128", "metadata": {"origin_pos": 36, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2091)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "90ff0864", "metadata": {"origin_pos": 0}, "source": ["# 循环神经网络\n", ":label:`sec_rnn`\n", "\n", "在 :numref:`sec_language_model`中，\n", "我们介绍了$n$元语法模型，\n", "其中单词$x_t$在时间步$t$的条件概率仅取决于前面$n-1$个单词。\n", "对于时间步$t-(n-1)$之前的单词，\n", "如果我们想将其可能产生的影响合并到$x_t$上，\n", "需要增加$n$，然而模型参数的数量也会随之呈指数增长，\n", "因为词表$\\mathcal{V}$需要存储$|\\mathcal{V}|^n$个数字，\n", "因此与其将$P(x_t \\mid x_{t-1}, \\ldots, x_{t-n+1})$模型化，\n", "不如使用隐变量模型：\n", "\n", "$$P(x_t \\mid x_{t-1}, \\ldots, x_1) \\approx P(x_t \\mid h_{t-1}),$$\n", "\n", "其中$h_{t-1}$是*隐状态*（hidden state），\n", "也称为*隐藏变量*（hidden variable），\n", "它存储了到时间步$t-1$的序列信息。\n", "通常，我们可以基于当前输入$x_{t}$和先前隐状态$h_{t-1}$\n", "来计算时间步$t$处的任何时间的隐状态：\n", "\n", "$$h_t = f(x_{t}, h_{t-1}).$$\n", ":eqlabel:`eq_ht_xt`\n", "\n", "对于 :eqref:`eq_ht_xt`中的函数$f$，隐变量模型不是近似值。\n", "毕竟$h_t$是可以仅仅存储到目前为止观察到的所有数据，\n", "然而这样的操作可能会使计算和存储的代价都变得昂贵。\n", "\n", "回想一下，我们在 :numref:`chap_perceptrons`中\n", "讨论过的具有隐藏单元的隐藏层。\n", "值得注意的是，隐藏层和隐状态指的是两个截然不同的概念。\n", "如上所述，隐藏层是在从输入到输出的路径上（以观测角度来理解）的隐藏的层，\n", "而隐状态则是在给定步骤所做的任何事情（以技术角度来定义）的*输入*，\n", "并且这些状态只能通过先前时间步的数据来计算。\n", "\n", "*循环神经网络*（recurrent neural networks，RNNs）\n", "是具有隐状态的神经网络。\n", "在介绍循环神经网络模型之前，\n", "我们首先回顾 :numref:`sec_mlp`中介绍的多层感知机模型。\n", "\n", "## 无隐状态的神经网络\n", "\n", "让我们来看一看只有单隐藏层的多层感知机。\n", "设隐藏层的激活函数为$\\phi$，\n", "给定一个小批量样本$\\mathbf{X} \\in \\mathbb{R}^{n \\times d}$，\n", "其中批量大小为$n$，输入维度为$d$，\n", "则隐藏层的输出$\\mathbf{H} \\in \\mathbb{R}^{n \\times h}$通过下式计算：\n", "\n", "$$\\mathbf{H} = \\phi(\\mathbf{X} \\mathbf{W}_{xh} + \\mathbf{b}_h).$$\n", ":eqlabel:`rnn_h_without_state`\n", "\n", "在 :eqref:`rnn_h_without_state`中，\n", "我们拥有的隐藏层权重参数为$\\mathbf{W}_{xh} \\in \\mathbb{R}^{d \\times h}$，\n", "偏置参数为$\\mathbf{b}_h \\in \\mathbb{R}^{1 \\times h}$，\n", "以及隐藏单元的数目为$h$。\n", "因此求和时可以应用广播机制（见 :numref:`subsec_broadcasting`）。\n", "接下来，将隐藏变量$\\mathbf{H}$用作输出层的输入。\n", "输出层由下式给出：\n", "\n", "$$\\mathbf{O} = \\mathbf{H} \\mathbf{W}_{hq} + \\mathbf{b}_q,$$\n", "\n", "其中，$\\mathbf{O} \\in \\mathbb{R}^{n \\times q}$是输出变量，\n", "$\\mathbf{W}_{hq} \\in \\mathbb{R}^{h \\times q}$是权重参数，\n", "$\\mathbf{b}_q \\in \\mathbb{R}^{1 \\times q}$是输出层的偏置参数。\n", "如果是分类问题，我们可以用$\\text{softmax}(\\mathbf{O})$\n", "来计算输出类别的概率分布。\n", "\n", "这完全类似于之前在 :numref:`sec_sequence`中解决的回归问题，\n", "因此我们省略了细节。\n", "无须多言，只要可以随机选择“特征-标签”对，\n", "并且通过自动微分和随机梯度下降能够学习网络参数就可以了。\n", "\n", "## 有隐状态的循环神经网络\n", ":label:`subsec_rnn_w_hidden_states`\n", "\n", "有了隐状态后，情况就完全不同了。\n", "假设我们在时间步$t$有小批量输入$\\mathbf{X}_t \\in \\mathbb{R}^{n \\times d}$。\n", "换言之，对于$n$个序列样本的小批量，\n", "$\\mathbf{X}_t$的每一行对应于来自该序列的时间步$t$处的一个样本。\n", "接下来，用$\\mathbf{H}_t  \\in \\mathbb{R}^{n \\times h}$\n", "表示时间步$t$的隐藏变量。\n", "与多层感知机不同的是，\n", "我们在这里保存了前一个时间步的隐藏变量$\\mathbf{H}_{t-1}$，\n", "并引入了一个新的权重参数$\\mathbf{W}_{hh} \\in \\mathbb{R}^{h \\times h}$，\n", "来描述如何在当前时间步中使用前一个时间步的隐藏变量。\n", "具体地说，当前时间步隐藏变量由当前时间步的输入\n", "与前一个时间步的隐藏变量一起计算得出：\n", "\n", "$$\\mathbf{H}_t = \\phi(\\mathbf{X}_t \\mathbf{W}_{xh} + \\mathbf{H}_{t-1} \\mathbf{W}_{hh}  + \\mathbf{b}_h).$$\n", ":eqlabel:`rnn_h_with_state`\n", "\n", "与 :eqref:`rnn_h_without_state`相比，\n", " :eqref:`rnn_h_with_state`多添加了一项\n", "$\\mathbf{H}_{t-1} \\mathbf{W}_{hh}$，\n", "从而实例化了 :eqref:`eq_ht_xt`。\n", "从相邻时间步的隐藏变量$\\mathbf{H}_t$和\n", "$\\mathbf{H}_{t-1}$之间的关系可知，\n", "这些变量捕获并保留了序列直到其当前时间步的历史信息，\n", "就如当前时间步下神经网络的状态或记忆，\n", "因此这样的隐藏变量被称为*隐状态*（hidden state）。\n", "由于在当前时间步中，\n", "隐状态使用的定义与前一个时间步中使用的定义相同，\n", "因此 :eqref:`rnn_h_with_state`的计算是*循环的*（recurrent）。\n", "于是基于循环计算的隐状态神经网络被命名为\n", "*循环神经网络*（recurrent neural network）。\n", "在循环神经网络中执行 :eqref:`rnn_h_with_state`计算的层\n", "称为*循环层*（recurrent layer）。\n", "\n", "有许多不同的方法可以构建循环神经网络，\n", "由 :eqref:`rnn_h_with_state`定义的隐状态的循环神经网络是非常常见的一种。\n", "对于时间步$t$，输出层的输出类似于多层感知机中的计算：\n", "\n", "$$\\mathbf{O}_t = \\mathbf{H}_t \\mathbf{W}_{hq} + \\mathbf{b}_q.$$\n", "\n", "循环神经网络的参数包括隐藏层的权重\n", "$\\mathbf{W}_{xh} \\in \\mathbb{R}^{d \\times h}, \\mathbf{W}_{hh} \\in \\mathbb{R}^{h \\times h}$和偏置$\\mathbf{b}_h \\in \\mathbb{R}^{1 \\times h}$，\n", "以及输出层的权重$\\mathbf{W}_{hq} \\in \\mathbb{R}^{h \\times q}$\n", "和偏置$\\mathbf{b}_q \\in \\mathbb{R}^{1 \\times q}$。\n", "值得一提的是，即使在不同的时间步，循环神经网络也总是使用这些模型参数。\n", "因此，循环神经网络的参数开销不会随着时间步的增加而增加。\n", "\n", " :numref:`fig_rnn`展示了循环神经网络在三个相邻时间步的计算逻辑。\n", "在任意时间步$t$，隐状态的计算可以被视为：\n", "\n", "1. 拼接当前时间步$t$的输入$\\mathbf{X}_t$和前一时间步$t-1$的隐状态$\\mathbf{H}_{t-1}$；\n", "1. 将拼接的结果送入带有激活函数$\\phi$的全连接层。\n", "   全连接层的输出是当前时间步$t$的隐状态$\\mathbf{H}_t$。\n", "   \n", "在本例中，模型参数是$\\mathbf{W}_{xh}$和$\\mathbf{W}_{hh}$的拼接，\n", "以及$\\mathbf{b}_h$的偏置，所有这些参数都来自 :eqref:`rnn_h_with_state`。\n", "当前时间步$t$的隐状态$\\mathbf{H}_t$\n", "将参与计算下一时间步$t+1$的隐状态$\\mathbf{H}_{t+1}$。\n", "而且$\\mathbf{H}_t$还将送入全连接输出层，\n", "用于计算当前时间步$t$的输出$\\mathbf{O}_t$。\n", "\n", "![具有隐状态的循环神经网络](../img/rnn.svg)\n", ":label:`fig_rnn`\n", "\n", "我们刚才提到，隐状态中\n", "$\\mathbf{X}_t \\mathbf{W}_{xh} + \\mathbf{H}_{t-1} \\mathbf{W}_{hh}$的计算，\n", "相当于$\\mathbf{X}_t$和$\\mathbf{H}_{t-1}$的拼接\n", "与$\\mathbf{W}_{xh}$和$\\mathbf{W}_{hh}$的拼接的矩阵乘法。\n", "虽然这个性质可以通过数学证明，\n", "但在下面我们使用一个简单的代码来说明一下。\n", "首先，我们定义矩阵`X`、`W_xh`、`H`和`W_hh`，\n", "它们的形状分别为$(3，1)$、$(1，4)$、$(3，4)$和$(4，4)$。\n", "分别将`X`乘以`W_xh`，将`H`乘以`W_hh`，\n", "然后将这两个乘法相加，我们得到一个形状为$(3，4)$的矩阵。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "3b32e0ed", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:10.528336Z", "iopub.status.busy": "2023-08-18T06:58:10.527597Z", "iopub.status.idle": "2023-08-18T06:58:12.493106Z", "shell.execute_reply": "2023-08-18T06:58:12.492193Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "code", "execution_count": 2, "id": "cc0b1ab9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:12.497284Z", "iopub.status.busy": "2023-08-18T06:58:12.496796Z", "iopub.status.idle": "2023-08-18T06:58:12.510001Z", "shell.execute_reply": "2023-08-18T06:58:12.508927Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[-1.6506, -0.7309,  2.0021, -0.1055],\n", "        [ 1.7334,  2.2035, -3.3148, -2.1629],\n", "        [-2.0071, -1.0902,  0.2376, -1.3144]])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["X, W_xh = torch.normal(0, 1, (3, 1)), torch.normal(0, 1, (1, 4))\n", "H, W_hh = torch.normal(0, 1, (3, 4)), torch.normal(0, 1, (4, 4))\n", "torch.matmul(X, W_xh) + torch.matmul(H, W_hh)"]}, {"cell_type": "markdown", "id": "d5d0879c", "metadata": {"origin_pos": 7}, "source": ["现在，我们沿列（轴1）拼接矩阵`X`和`H`，\n", "沿行（轴0）拼接矩阵`W_xh`和`W_hh`。\n", "这两个拼接分别产生形状$(3, 5)$和形状$(5, 4)$的矩阵。\n", "再将这两个拼接的矩阵相乘，\n", "我们得到与上面相同形状$(3, 4)$的输出矩阵。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "1a310233", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T06:58:12.513639Z", "iopub.status.busy": "2023-08-18T06:58:12.513360Z", "iopub.status.idle": "2023-08-18T06:58:12.520602Z", "shell.execute_reply": "2023-08-18T06:58:12.519678Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[-1.6506, -0.7309,  2.0021, -0.1055],\n", "        [ 1.7334,  2.2035, -3.3148, -2.1629],\n", "        [-2.0071, -1.0902,  0.2376, -1.3144]])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.matmul(torch.cat((X, H), 1), torch.cat((W_xh, W_hh), 0))"]}, {"cell_type": "markdown", "id": "816b1d00", "metadata": {"origin_pos": 9}, "source": ["## 基于循环神经网络的字符级语言模型\n", "\n", "回想一下 :numref:`sec_language_model`中的语言模型，\n", "我们的目标是根据过去的和当前的词元预测下一个词元，\n", "因此我们将原始序列移位一个词元作为标签。\n", "Bengio等人首先提出使用神经网络进行语言建模\n", " :cite:`Bengio.Ducharme.Vincent.ea.2003`。\n", "接下来，我们看一下如何使用循环神经网络来构建语言模型。\n", "设小批量大小为1，批量中的文本序列为“machine”。\n", "为了简化后续部分的训练，我们考虑使用\n", "*字符级语言模型*（character-level language model），\n", "将文本词元化为字符而不是单词。\n", " :numref:`fig_rnn_train`演示了\n", "如何通过基于字符级语言建模的循环神经网络，\n", "使用当前的和先前的字符预测下一个字符。\n", "\n", "![基于循环神经网络的字符级语言模型：输入序列和标签序列分别为“machin”和“achine”](../img/rnn-train.svg)\n", ":label:`fig_rnn_train`\n", "\n", "在训练过程中，我们对每个时间步的输出层的输出进行softmax操作，\n", "然后利用交叉熵损失计算模型输出和标签之间的误差。\n", "由于隐藏层中隐状态的循环计算，\n", " :numref:`fig_rnn_train`中的第$3$个时间步的输出$\\mathbf{O}_3$\n", "由文本序列“m”“a”和“c”确定。\n", "由于训练数据中这个文本序列的下一个字符是“h”，\n", "因此第$3$个时间步的损失将取决于下一个字符的概率分布，\n", "而下一个字符是基于特征序列“m”“a”“c”和这个时间步的标签“h”生成的。\n", "\n", "在实践中，我们使用的批量大小为$n>1$，\n", "每个词元都由一个$d$维向量表示。\n", "因此，在时间步$t$输入$\\mathbf X_t$将是一个$n\\times d$矩阵，\n", "这与我们在 :numref:`subsec_rnn_w_hidden_states`中的讨论相同。\n", "\n", "## 困惑度（Perplexity）\n", ":label:`subsec_perplexity`\n", "\n", "最后，让我们讨论如何度量语言模型的质量，\n", "这将在后续部分中用于评估基于循环神经网络的模型。\n", "一个好的语言模型能够用高度准确的词元来预测我们接下来会看到什么。\n", "考虑一下由不同的语言模型给出的对“It is raining ...”（“...下雨了”）的续写：\n", "\n", "1. \"It is raining outside\"（外面下雨了）；\n", "1. \"It is raining banana tree\"（香蕉树下雨了）；\n", "1. \"It is raining piouw;kcj pwepoiut\"（piouw;kcj pwepoiut下雨了）。\n", "\n", "就质量而言，例$1$显然是最合乎情理、在逻辑上最连贯的。\n", "虽然这个模型可能没有很准确地反映出后续词的语义，\n", "比如，“It is raining in San Francisco”（旧金山下雨了）\n", "和“It is raining in winter”（冬天下雨了）\n", "可能才是更完美的合理扩展，\n", "但该模型已经能够捕捉到跟在后面的是哪类单词。\n", "例$2$则要糟糕得多，因为其产生了一个无意义的续写。\n", "尽管如此，至少该模型已经学会了如何拼写单词，\n", "以及单词之间的某种程度的相关性。\n", "最后，例$3$表明了训练不足的模型是无法正确地拟合数据的。\n", "\n", "我们可以通过计算序列的似然概率来度量模型的质量。\n", "然而这是一个难以理解、难以比较的数字。\n", "毕竟，较短的序列比较长的序列更有可能出现，\n", "因此评估模型产生托尔斯泰的巨著《战争与和平》的可能性\n", "不可避免地会比产生圣埃克苏佩里的中篇小说《小王子》可能性要小得多。\n", "而缺少的可能性值相当于平均数。\n", "\n", "在这里，信息论可以派上用场了。\n", "我们在引入softmax回归\n", "（ :numref:`subsec_info_theory_basics`）时定义了熵、惊异和交叉熵，\n", "并在[信息论的在线附录](https://d2l.ai/chapter_appendix-mathematics-for-deep-learning/information-theory.html)\n", "中讨论了更多的信息论知识。\n", "如果想要压缩文本，我们可以根据当前词元集预测的下一个词元。\n", "一个更好的语言模型应该能让我们更准确地预测下一个词元。\n", "因此，它应该允许我们在压缩序列时花费更少的比特。\n", "所以我们可以通过一个序列中所有的$n$个词元的交叉熵损失的平均值来衡量：\n", "\n", "$$\\frac{1}{n} \\sum_{t=1}^n -\\log P(x_t \\mid x_{t-1}, \\ldots, x_1),$$\n", ":eqlabel:`eq_avg_ce_for_lm`\n", "\n", "其中$P$由语言模型给出，\n", "$x_t$是在时间步$t$从该序列中观察到的实际词元。\n", "这使得不同长度的文档的性能具有了可比性。\n", "由于历史原因，自然语言处理的科学家更喜欢使用一个叫做*困惑度*（perplexity）的量。\n", "简而言之，它是 :eqref:`eq_avg_ce_for_lm`的指数：\n", "\n", "$$\\exp\\left(-\\frac{1}{n} \\sum_{t=1}^n \\log P(x_t \\mid x_{t-1}, \\ldots, x_1)\\right).$$\n", "\n", "困惑度的最好的理解是“下一个词元的实际选择数的调和平均数”。\n", "我们看看一些案例。\n", "\n", "* 在最好的情况下，模型总是完美地估计标签词元的概率为1。\n", "  在这种情况下，模型的困惑度为1。\n", "* 在最坏的情况下，模型总是预测标签词元的概率为0。\n", "  在这种情况下，困惑度是正无穷大。\n", "* 在基线上，该模型的预测是词表的所有可用词元上的均匀分布。\n", "  在这种情况下，困惑度等于词表中唯一词元的数量。\n", "  事实上，如果我们在没有任何压缩的情况下存储序列，\n", "  这将是我们能做的最好的编码方式。\n", "  因此，这种方式提供了一个重要的上限，\n", "  而任何实际模型都必须超越这个上限。\n", "\n", "在接下来的小节中，我们将基于循环神经网络实现字符级语言模型，\n", "并使用困惑度来评估这样的模型。\n", "\n", "## 小结\n", "\n", "* 对隐状态使用循环计算的神经网络称为循环神经网络（RNN）。\n", "* 循环神经网络的隐状态可以捕获直到当前时间步序列的历史信息。\n", "* 循环神经网络模型的参数数量不会随着时间步的增加而增加。\n", "* 我们可以使用循环神经网络创建字符级语言模型。\n", "* 我们可以使用困惑度来评价语言模型的质量。\n", "\n", "## 练习\n", "\n", "1. 如果我们使用循环神经网络来预测文本序列中的下一个字符，那么任意输出所需的维度是多少？\n", "1. 为什么循环神经网络可以基于文本序列中所有先前的词元，在某个时间步表示当前词元的条件概率？\n", "1. 如果基于一个长序列进行反向传播，梯度会发生什么状况？\n", "1. 与本节中描述的语言模型相关的问题有哪些？\n"]}, {"cell_type": "markdown", "id": "d4ae34aa", "metadata": {"origin_pos": 11, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2100)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "b9f10712", "metadata": {"origin_pos": 0}, "source": ["# 含并行连结的网络（GoogLeNet）\n", ":label:`sec_googlenet`\n", "\n", "在2014年的ImageNet图像识别挑战赛中，一个名叫*GoogLeNet* :cite:`Szegedy.Liu.Jia.ea.2015`的网络架构大放异彩。\n", "GoogLeNet吸收了NiN中串联网络的思想，并在此基础上做了改进。\n", "这篇论文的一个重点是解决了什么样大小的卷积核最合适的问题。\n", "毕竟，以前流行的网络使用小到$1 \\times 1$，大到$11 \\times 11$的卷积核。\n", "本文的一个观点是，有时使用不同大小的卷积核组合是有利的。\n", "本节将介绍一个稍微简化的GoogLeNet版本：我们省略了一些为稳定训练而添加的特殊特性，现在有了更好的训练方法，这些特性不是必要的。\n", "\n", "## (**Inception块**)\n", "\n", "在GoogLeNet中，基本的卷积块被称为*Inception块*（Inception block）。这很可能得名于电影《盗梦空间》（Inception），因为电影中的一句话“我们需要走得更深”（“We need to go deeper”）。\n", "\n", "![Inception块的架构。](../img/inception.svg)\n", ":label:`fig_inception`\n", "\n", "如 :numref:`fig_inception`所示，Inception块由四条并行路径组成。\n", "前三条路径使用窗口大小为$1\\times 1$、$3\\times 3$和$5\\times 5$的卷积层，从不同空间大小中提取信息。\n", "中间的两条路径在输入上执行$1\\times 1$卷积，以减少通道数，从而降低模型的复杂性。\n", "第四条路径使用$3\\times 3$最大汇聚层，然后使用$1\\times 1$卷积层来改变通道数。\n", "这四条路径都使用合适的填充来使输入与输出的高和宽一致，最后我们将每条线路的输出在通道维度上连结，并构成Inception块的输出。在Inception块中，通常调整的超参数是每层输出通道数。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "82e98083", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:45.698938Z", "iopub.status.busy": "2023-08-18T07:18:45.698048Z", "iopub.status.idle": "2023-08-18T07:18:49.844955Z", "shell.execute_reply": "2023-08-18T07:18:49.843595Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l\n", "\n", "\n", "class Inception(nn.Module):\n", "    # c1--c4是每条路径的输出通道数\n", "    def __init__(self, in_channels, c1, c2, c3, c4, **kwargs):\n", "        super(Inception, self).__init__(**kwargs)\n", "        # 线路1，单1x1卷积层\n", "        self.p1_1 = nn.Conv2d(in_channels, c1, kernel_size=1)\n", "        # 线路2，1x1卷积层后接3x3卷积层\n", "        self.p2_1 = nn.Conv2d(in_channels, c2[0], kernel_size=1)\n", "        self.p2_2 = nn.Conv2d(c2[0], c2[1], kernel_size=3, padding=1)\n", "        # 线路3，1x1卷积层后接5x5卷积层\n", "        self.p3_1 = nn.Conv2d(in_channels, c3[0], kernel_size=1)\n", "        self.p3_2 = nn.Conv2d(c3[0], c3[1], kernel_size=5, padding=2)\n", "        # 线路4，3x3最大汇聚层后接1x1卷积层\n", "        self.p4_1 = nn.MaxPool2d(kernel_size=3, stride=1, padding=1)\n", "        self.p4_2 = nn.Conv2d(in_channels, c4, kernel_size=1)\n", "\n", "    def forward(self, x):\n", "        p1 = <PERSON><PERSON>relu(self.p1_1(x))\n", "        p2 = <PERSON><PERSON>relu(self.p2_2(<PERSON><PERSON>relu(self.p2_1(x))))\n", "        p3 = <PERSON><PERSON>relu(self.p3_2(<PERSON><PERSON>relu(self.p3_1(x))))\n", "        p4 = <PERSON><PERSON>relu(self.p4_2(self.p4_1(x)))\n", "        # 在通道维度上连结输出\n", "        return torch.cat((p1, p2, p3, p4), dim=1)"]}, {"cell_type": "markdown", "id": "67ed1e2a", "metadata": {"origin_pos": 5}, "source": ["那么为什么GoogLeNet这个网络如此有效呢？\n", "首先我们考虑一下滤波器（filter）的组合，它们可以用各种滤波器尺寸探索图像，这意味着不同大小的滤波器可以有效地识别不同范围的图像细节。\n", "同时，我们可以为不同的滤波器分配不同数量的参数。\n", "\n", "## [**GoogLeNet模型**]\n", "\n", "如 :numref:`fig_inception_full`所示，GoogLeNet一共使用9个Inception块和全局平均汇聚层的堆叠来生成其估计值。Inception块之间的最大汇聚层可降低维度。\n", "第一个模块类似于AlexNet和LeNet，Inception块的组合从VGG继承，全局平均汇聚层避免了在最后使用全连接层。\n", "\n", "![GoogLeNet架构。](../img/inception-full.svg)\n", ":label:`fig_inception_full`\n", "\n", "现在，我们逐一实现GoogLeNet的每个模块。第一个模块使用64个通道、$7\\times 7$卷积层。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "4278ba0a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:49.850742Z", "iopub.status.busy": "2023-08-18T07:18:49.849860Z", "iopub.status.idle": "2023-08-18T07:18:49.877802Z", "shell.execute_reply": "2023-08-18T07:18:49.876517Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["b1 = nn.Sequential(nn.Conv2d(1, 64, kernel_size=7, stride=2, padding=3),\n", "                   nn.ReLU(),\n", "                   nn.MaxPool2d(kernel_size=3, stride=2, padding=1))"]}, {"cell_type": "markdown", "id": "b4d6d53a", "metadata": {"origin_pos": 10}, "source": ["第二个模块使用两个卷积层：第一个卷积层是64个通道、$1\\times 1$卷积层；第二个卷积层使用将通道数量增加三倍的$3\\times 3$卷积层。\n", "这对应于Inception块中的第二条路径。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "d5a1d1b0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:49.883499Z", "iopub.status.busy": "2023-08-18T07:18:49.882740Z", "iopub.status.idle": "2023-08-18T07:18:49.892430Z", "shell.execute_reply": "2023-08-18T07:18:49.891290Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["b2 = nn.Sequential(nn.Conv2d(64, 64, kernel_size=1),\n", "                   nn.ReLU(),\n", "                   nn.Conv2d(64, 192, kernel_size=3, padding=1),\n", "                   nn.ReLU(),\n", "                   nn.MaxPool2d(kernel_size=3, stride=2, padding=1))"]}, {"cell_type": "markdown", "id": "c5532216", "metadata": {"origin_pos": 15}, "source": ["第三个模块串联两个完整的Inception块。\n", "第一个Inception块的输出通道数为$64+128+32+32=256$，四个路径之间的输出通道数量比为$64:128:32:32=2:4:1:1$。\n", "第二个和第三个路径首先将输入通道的数量分别减少到$96/192=1/2$和$16/192=1/12$，然后连接第二个卷积层。第二个Inception块的输出通道数增加到$128+192+96+64=480$，四个路径之间的输出通道数量比为$128:192:96:64 = 4:6:3:2$。\n", "第二条和第三条路径首先将输入通道的数量分别减少到$128/256=1/2$和$32/256=1/8$。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "11b7cf54", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:49.897526Z", "iopub.status.busy": "2023-08-18T07:18:49.896704Z", "iopub.status.idle": "2023-08-18T07:18:49.916207Z", "shell.execute_reply": "2023-08-18T07:18:49.915075Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["b3 = nn.Sequential(Inception(192, 64, (96, 128), (16, 32), 32),\n", "                   Inception(256, 128, (128, 192), (32, 96), 64),\n", "                   nn.MaxPool2d(kernel_size=3, stride=2, padding=1))"]}, {"cell_type": "markdown", "id": "f54c6828", "metadata": {"origin_pos": 20}, "source": ["第四模块更加复杂，\n", "它串联了5个Inception块，其输出通道数分别是$192+208+48+64=512$、$160+224+64+64=512$、$128+256+64+64=512$、$112+288+64+64=528$和$256+320+128+128=832$。\n", "这些路径的通道数分配和第三模块中的类似，首先是含$3×3$卷积层的第二条路径输出最多通道，其次是仅含$1×1$卷积层的第一条路径，之后是含$5×5$卷积层的第三条路径和含$3×3$最大汇聚层的第四条路径。\n", "其中第二、第三条路径都会先按比例减小通道数。\n", "这些比例在各个Inception块中都略有不同。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b55bd896", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:49.921459Z", "iopub.status.busy": "2023-08-18T07:18:49.920668Z", "iopub.status.idle": "2023-08-18T07:18:49.984376Z", "shell.execute_reply": "2023-08-18T07:18:49.982781Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["b4 = nn.Sequential(Inception(480, 192, (96, 208), (16, 48), 64),\n", "                   Inception(512, 160, (112, 224), (24, 64), 64),\n", "                   Inception(512, 128, (128, 256), (24, 64), 64),\n", "                   Inception(512, 112, (144, 288), (32, 64), 64),\n", "                   Inception(528, 256, (160, 320), (32, 128), 128),\n", "                   nn.MaxPool2d(kernel_size=3, stride=2, padding=1))"]}, {"cell_type": "markdown", "id": "2a44e596", "metadata": {"origin_pos": 25}, "source": ["第五模块包含输出通道数为$256+320+128+128=832$和$384+384+128+128=1024$的两个Inception块。\n", "其中每条路径通道数的分配思路和第三、第四模块中的一致，只是在具体数值上有所不同。\n", "需要注意的是，第五模块的后面紧跟输出层，该模块同NiN一样使用全局平均汇聚层，将每个通道的高和宽变成1。\n", "最后我们将输出变成二维数组，再接上一个输出个数为标签类别数的全连接层。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "8fc4cc33", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:49.990643Z", "iopub.status.busy": "2023-08-18T07:18:49.989777Z", "iopub.status.idle": "2023-08-18T07:18:50.042949Z", "shell.execute_reply": "2023-08-18T07:18:50.041737Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [], "source": ["b5 = nn.Sequential(Inception(832, 256, (160, 320), (32, 128), 128),\n", "                   Inception(832, 384, (192, 384), (48, 128), 128),\n", "                   nn.AdaptiveAvgPool2d((1,1)),\n", "                   nn.<PERSON>())\n", "\n", "net = nn.Sequential(b1, b2, b3, b4, b5, nn.Linear(1024, 10))"]}, {"cell_type": "markdown", "id": "7e92ad19", "metadata": {"origin_pos": 30}, "source": ["GoogLeNet模型的计算复杂，而且不如VGG那样便于修改通道数。\n", "[**为了使Fashion-MNIST上的训练短小精悍，我们将输入的高和宽从224降到96**]，这简化了计算。下面演示各个模块输出的形状变化。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "51845ad5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:50.048543Z", "iopub.status.busy": "2023-08-18T07:18:50.047764Z", "iopub.status.idle": "2023-08-18T07:18:50.254447Z", "shell.execute_reply": "2023-08-18T07:18:50.252245Z"}, "origin_pos": 32, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential output shape:\t torch.Size([1, 64, 24, 24])\n", "Sequential output shape:\t torch.Size([1, 192, 12, 12])\n", "Sequential output shape:\t torch.Size([1, 480, 6, 6])\n", "Sequential output shape:\t torch.Size([1, 832, 3, 3])\n", "Sequential output shape:\t torch.Size([1, 1024])\n", "Linear output shape:\t torch.Size([1, 10])\n"]}], "source": ["X = torch.rand(size=(1, 1, 96, 96))\n", "for layer in net:\n", "    X = layer(X)\n", "    print(layer.__class__.__name__,'output shape:\\t', X.shape)"]}, {"cell_type": "markdown", "id": "1dfcf730", "metadata": {"origin_pos": 35}, "source": ["## [**训练模型**]\n", "\n", "和以前一样，我们使用Fashion-MNIST数据集来训练我们的模型。在训练之前，我们将图片转换为$96 \\times 96$分辨率。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "fe051d5b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:18:50.260612Z", "iopub.status.busy": "2023-08-18T07:18:50.259996Z", "iopub.status.idle": "2023-08-18T07:22:36.284598Z", "shell.execute_reply": "2023-08-18T07:22:36.283681Z"}, "origin_pos": 36, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.262, train acc 0.900, test acc 0.886\n", "3265.5 examples/sec on cuda:0\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"180.65625pt\" viewBox=\"0 0 238.**********.65625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:22:36.241281</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 180.65625 \n", "L 238.**********.65625 \n", "L 238.965625 0 \n", "L 0 0 \n", "L 0 180.65625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 51.803125 143.1 \n", "L 51.803125 7.2 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m831a01f36b\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m831a01f36b\" x=\"51.803125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(48.621875 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 95.203125 143.1 \n", "L 95.203125 7.2 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m831a01f36b\" x=\"95.203125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(92.021875 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 138.603125 143.1 \n", "L 138.603125 7.2 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m831a01f36b\" x=\"138.603125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(135.421875 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 182.003125 143.1 \n", "L 182.003125 7.2 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m831a01f36b\" x=\"182.003125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(178.821875 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.1 \n", "L 225.403125 7.2 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m831a01f36b\" x=\"225.403125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 171.376563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.103125 142.797085 \n", "L 225.403125 142.797085 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m059a7f96a0\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m059a7f96a0\" x=\"30.103125\" y=\"142.797085\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 146.596304)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 114.690834 \n", "L 225.403125 114.690834 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m059a7f96a0\" x=\"30.103125\" y=\"114.690834\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 118.490053)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 86.584584 \n", "L 225.403125 86.584584 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m059a7f96a0\" x=\"30.103125\" y=\"86.584584\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 90.383802)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 58.478333 \n", "L 225.403125 58.478333 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m059a7f96a0\" x=\"30.103125\" y=\"58.478333\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 62.277552)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 30.372082 \n", "L 225.403125 30.372082 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m059a7f96a0\" x=\"30.103125\" y=\"30.372082\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 34.171301)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 12.70611 13.377273 \n", "L 17.009095 13.508168 \n", "L 21.31208 15.105481 \n", "L 25.615065 20.387647 \n", "L 29.91805 30.010592 \n", "L 30.103125 30.35679 \n", "L 34.40611 82.672371 \n", "L 38.709095 86.20489 \n", "L 43.01208 84.318099 \n", "L 47.315065 87.274065 \n", "L 51.61805 90.34379 \n", "L 51.803125 90.428162 \n", "L 56.10611 107.560622 \n", "L 60.409095 109.065203 \n", "L 64.71208 110.228406 \n", "L 69.015065 111.227236 \n", "L 73.31805 112.277295 \n", "L 73.503125 112.3122 \n", "L 77.80611 117.670163 \n", "L 82.109095 117.752957 \n", "L 86.41208 118.224832 \n", "L 90.715065 118.524289 \n", "L 95.01805 117.750438 \n", "L 95.203125 117.79568 \n", "L 99.50611 121.175214 \n", "L 103.809095 121.444166 \n", "L 108.11208 121.531813 \n", "L 112.415065 121.746039 \n", "L 116.71805 121.997787 \n", "L 116.903125 122.000607 \n", "L 121.20611 124.491491 \n", "L 125.509095 124.084998 \n", "L 129.81208 124.111221 \n", "L 134.115065 124.150211 \n", "L 138.41805 124.120474 \n", "L 138.603125 124.142482 \n", "L 142.90611 125.478574 \n", "L 147.209095 125.530724 \n", "L 151.51208 125.556255 \n", "L 155.815065 125.658215 \n", "L 160.11805 125.743134 \n", "L 160.303125 125.710949 \n", "L 164.60611 126.60394 \n", "L 168.909095 126.701531 \n", "L 173.21208 126.7597 \n", "L 177.515065 126.652546 \n", "L 181.81805 126.804535 \n", "L 182.003125 126.828647 \n", "L 186.30611 127.324956 \n", "L 190.609095 127.43468 \n", "L 194.91208 127.596378 \n", "L 199.215065 124.670507 \n", "L 203.51805 124.781779 \n", "L 203.703125 124.800578 \n", "L 208.00611 128.560143 \n", "L 212.309095 128.266501 \n", "L 216.61208 128.115628 \n", "L 220.915065 127.966665 \n", "L 225.21805 128.078087 \n", "L 225.403125 128.079298 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 12.70611 136.922727 \n", "L 17.009095 136.334819 \n", "L 21.31208 134.94572 \n", "L 25.615065 132.58543 \n", "L 29.91805 129.441894 \n", "L 30.103125 129.337938 \n", "L 34.40611 112.074762 \n", "L 38.709095 110.216595 \n", "L 43.01208 110.373213 \n", "L 47.315065 108.979391 \n", "L 51.61805 107.592495 \n", "L 51.803125 107.549036 \n", "L 56.10611 100.193827 \n", "L 60.409095 99.532725 \n", "L 64.71208 99.096713 \n", "L 69.015065 98.681557 \n", "L 73.31805 98.277577 \n", "L 73.503125 98.262731 \n", "L 77.80611 96.170553 \n", "L 82.109095 96.18708 \n", "L 86.41208 95.939167 \n", "L 90.715065 95.850627 \n", "L 95.01805 96.057221 \n", "L 95.203125 96.038589 \n", "L 99.50611 94.796406 \n", "L 103.809095 94.784601 \n", "L 108.11208 94.742889 \n", "L 112.415065 94.60752 \n", "L 116.71805 94.512133 \n", "L 116.903125 94.504925 \n", "L 121.20611 93.644201 \n", "L 125.509095 93.7292 \n", "L 129.81208 93.711885 \n", "L 134.115065 93.671354 \n", "L 138.41805 93.660257 \n", "L 138.603125 93.651432 \n", "L 142.90611 93.205041 \n", "L 147.209095 93.228652 \n", "L 151.51208 93.18143 \n", "L 155.815065 93.11532 \n", "L 160.11805 93.103987 \n", "L 160.303125 93.122097 \n", "L 164.60611 92.798936 \n", "L 168.909095 92.737548 \n", "L 173.21208 92.687178 \n", "L 177.515065 92.757617 \n", "L 181.81805 92.707326 \n", "L 182.003125 92.696756 \n", "L 186.30611 92.444775 \n", "L 190.609095 92.399914 \n", "L 194.91208 92.380239 \n", "L 199.215065 93.305387 \n", "L 203.51805 93.272096 \n", "L 203.703125 93.266376 \n", "L 208.00611 91.977282 \n", "L 212.309095 92.140196 \n", "L 216.61208 92.172464 \n", "L 220.915065 92.209848 \n", "L 225.21805 92.198278 \n", "L 225.403125 92.195528 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 30.103125 114.623379 \n", "L 51.803125 105.258376 \n", "L 73.503125 96.905199 \n", "L 95.203125 96.635379 \n", "L 116.903125 94.459955 \n", "L 138.603125 93.864103 \n", "L 160.303125 93.352569 \n", "L 182.003125 93.240144 \n", "L 203.703125 93.144582 \n", "L 225.403125 92.964702 \n", "\" clip-path=\"url(#p5ef268f698)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.1 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.1 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 59.234375 \n", "L 218.403125 59.234375 \n", "Q 220.403125 59.234375 220.403125 57.234375 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 140.634375 12.2 \n", "Q 138.634375 12.2 138.634375 14.2 \n", "L 138.634375 57.234375 \n", "Q 138.634375 59.234375 140.634375 59.234375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_24\">\n", "     <path d=\"M 142.634375 20.298438 \n", "L 152.634375 20.298438 \n", "L 162.634375 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 23.798438)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_25\">\n", "     <path d=\"M 142.634375 34.976562 \n", "L 152.634375 34.976562 \n", "L 162.634375 34.976562 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 38.476562)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 142.634375 49.654688 \n", "L 152.634375 49.654688 \n", "L 162.634375 49.654688 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(170.634375 53.154688)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p5ef268f698\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["lr, num_epochs, batch_size = 0.1, 10, 128\n", "train_iter, test_iter = d2l.load_data_fashion_mnist(batch_size, resize=96)\n", "d2l.train_ch6(net, train_iter, test_iter, num_epochs, lr, d2l.try_gpu())"]}, {"cell_type": "markdown", "id": "535046e9", "metadata": {"origin_pos": 37}, "source": ["## 小结\n", "\n", "* Inception块相当于一个有4条路径的子网络。它通过不同窗口形状的卷积层和最大汇聚层来并行抽取信息，并使用$1×1$卷积层减少每像素级别上的通道维数从而降低模型复杂度。\n", "*  GoogLeNet将多个设计精细的Inception块与其他层（卷积层、全连接层）串联起来。其中Inception块的通道数分配之比是在ImageNet数据集上通过大量的实验得来的。\n", "* GoogLeNet和它的后继者们一度是ImageNet上最有效的模型之一：它以较低的计算复杂度提供了类似的测试精度。\n", "\n", "## 练习\n", "\n", "1. GoogLeNet有一些后续版本。尝试实现并运行它们，然后观察实验结果。这些后续版本包括：\n", "    * 添加批量规范化层 :cite:`Ioffe.Szegedy.2015`（batch normalization），在 :numref:`sec_batch_norm`中将介绍；\n", "    * 对Inception模块进行调整 :cite:`Szegedy.Vanhoucke.Ioffe.ea.2016`；\n", "    * 使用标签平滑（label smoothing）进行模型正则化 :cite:`Szegedy.Vanhoucke.Ioffe.ea.2016`；\n", "    * 加入残差连接 :cite:`Szegedy.Ioffe.Vanhoucke.ea.2017`。（ :numref:`sec_resnet`将介绍）。\n", "1. 使用GoogLeNet的最小图像大小是多少？\n", "1. 将AlexNet、VGG和NiN的模型参数大小与GoogLeNet进行比较。后两个网络架构是如何显著减少模型参数大小的？\n"]}, {"cell_type": "markdown", "id": "cf7d384c", "metadata": {"origin_pos": 39, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1871)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
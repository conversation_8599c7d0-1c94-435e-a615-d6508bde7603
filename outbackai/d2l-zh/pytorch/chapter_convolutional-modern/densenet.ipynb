{"cells": [{"cell_type": "markdown", "id": "9f271f9b", "metadata": {"origin_pos": 0}, "source": ["# 稠密连接网络（DenseNet）\n", "\n", "ResNet极大地改变了如何参数化深层网络中函数的观点。\n", "*稠密连接网络*（DenseNet） :cite:`<PERSON><PERSON><PERSON>.<PERSON>-<PERSON>-Maaten.ea.2017`在某种程度上是ResNet的逻辑扩展。让我们先从数学上了解一下。\n", "\n", "## 从ResNet到DenseNet\n", "\n", "回想一下任意函数的泰勒展开式（Taylor expansion），它把这个函数分解成越来越高阶的项。在$x$接近0时，\n", "\n", "$$f(x) = f(0) + f'(0) x + \\frac{f''(0)}{2!}  x^2 + \\frac{f'''(0)}{3!}  x^3 + \\ldots.$$\n", "\n", "同样，ResNet将函数展开为\n", "\n", "$$f(\\mathbf{x}) = \\mathbf{x} + g(\\mathbf{x}).$$\n", "\n", "也就是说，ResNet将$f$分解为两部分：一个简单的线性项和一个复杂的非线性项。\n", "那么再向前拓展一步，如果我们想将$f$拓展成超过两部分的信息呢？\n", "一种方案便是DenseNet。\n", "\n", "![ResNet（左）与 DenseNet（右）在跨层连接上的主要区别：使用相加和使用连结。](../img/densenet-block.svg)\n", ":label:`fig_densenet_block`\n", "\n", "如 :numref:`fig_densenet_block`所示，ResNet和DenseNet的关键区别在于，DenseNet输出是*连接*（用图中的$[,]$表示）而不是如ResNet的简单相加。\n", "因此，在应用越来越复杂的函数序列后，我们执行从$\\mathbf{x}$到其展开式的映射：\n", "\n", "$$\\mathbf{x} \\to \\left[\n", "\\mathbf{x},\n", "f_1(\\mathbf{x}),\n", "f_2([\\mathbf{x}, f_1(\\mathbf{x})]), f_3([\\mathbf{x}, f_1(\\mathbf{x}), f_2([\\mathbf{x}, f_1(\\mathbf{x})])]), \\ldots\\right].$$\n", "\n", "最后，将这些展开式结合到多层感知机中，再次减少特征的数量。\n", "实现起来非常简单：我们不需要添加术语，而是将它们连接起来。\n", "DenseNet这个名字由变量之间的“稠密连接”而得来，最后一层与之前的所有层紧密相连。\n", "稠密连接如 :numref:`fig_densenet`所示。\n", "\n", "![稠密连接。](../img/densenet.svg)\n", ":label:`fig_densenet`\n", "\n", "稠密网络主要由2部分构成：*稠密块*（dense block）和*过渡层*（transition layer）。\n", "前者定义如何连接输入和输出，而后者则控制通道数量，使其不会太复杂。\n", "\n", "## (**稠密块体**)\n", "\n", "DenseNet使用了ResNet改良版的“批量规范化、激活和卷积”架构（参见 :numref:`sec_resnet`中的练习）。\n", "我们首先实现一下这个架构。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c0d77805", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:16:08.050557Z", "iopub.status.busy": "2023-08-18T07:16:08.050029Z", "iopub.status.idle": "2023-08-18T07:16:11.746531Z", "shell.execute_reply": "2023-08-18T07:16:11.745702Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l\n", "\n", "\n", "def conv_block(input_channels, num_channels):\n", "    return nn.Sequential(\n", "        nn.BatchNorm2d(input_channels), nn.ReLU(),\n", "        nn.Conv2d(input_channels, num_channels, kernel_size=3, padding=1))"]}, {"cell_type": "markdown", "id": "64e078d7", "metadata": {"origin_pos": 5}, "source": ["一个*稠密块*由多个卷积块组成，每个卷积块使用相同数量的输出通道。\n", "然而，在前向传播中，我们将每个卷积块的输入和输出在通道维上连结。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "26c9a602", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:16:11.750525Z", "iopub.status.busy": "2023-08-18T07:16:11.750169Z", "iopub.status.idle": "2023-08-18T07:16:11.756520Z", "shell.execute_reply": "2023-08-18T07:16:11.755779Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["class DenseBlock(nn.Module):\n", "    def __init__(self, num_convs, input_channels, num_channels):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        layer = []\n", "        for i in range(num_convs):\n", "            layer.append(conv_block(\n", "                num_channels * i + input_channels, num_channels))\n", "        self.net = nn.Sequential(*layer)\n", "\n", "    def forward(self, X):\n", "        for blk in self.net:\n", "            Y = blk(X)\n", "            # 连接通道维度上每个块的输入和输出\n", "            X = torch.cat((X, Y), dim=1)\n", "        return X"]}, {"cell_type": "markdown", "id": "6e0cf1d4", "metadata": {"origin_pos": 10}, "source": ["在下面的例子中，我们[**定义一个**]有2个输出通道数为10的(**`DenseBlock`**)。\n", "使用通道数为3的输入时，我们会得到通道数为$3+2\\times 10=23$的输出。\n", "卷积块的通道数控制了输出通道数相对于输入通道数的增长，因此也被称为*增长率*（growth rate）。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6894a1d5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:16:11.759699Z", "iopub.status.busy": "2023-08-18T07:16:11.759433Z", "iopub.status.idle": "2023-08-18T07:16:11.773609Z", "shell.execute_reply": "2023-08-18T07:16:11.772841Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([4, 23, 8, 8])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["blk = <PERSON><PERSON><PERSON>lock(2, 3, 10)\n", "X = torch.randn(4, 3, 8, 8)\n", "Y = blk(X)\n", "Y.shape"]}, {"cell_type": "markdown", "id": "79590d57", "metadata": {"origin_pos": 15}, "source": ["## [**过渡层**]\n", "\n", "由于每个稠密块都会带来通道数的增加，使用过多则会过于复杂化模型。\n", "而过渡层可以用来控制模型复杂度。\n", "它通过$1\\times 1$卷积层来减小通道数，并使用步幅为2的平均汇聚层减半高和宽，从而进一步降低模型复杂度。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "19c97dd5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:16:11.778396Z", "iopub.status.busy": "2023-08-18T07:16:11.778129Z", "iopub.status.idle": "2023-08-18T07:16:11.782692Z", "shell.execute_reply": "2023-08-18T07:16:11.781920Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["def transition_block(input_channels, num_channels):\n", "    return nn.Sequential(\n", "        nn.BatchNorm2d(input_channels), nn.ReLU(),\n", "        nn.Conv2d(input_channels, num_channels, kernel_size=1),\n", "        nn.AvgPool2d(kernel_size=2, stride=2))"]}, {"cell_type": "markdown", "id": "911d280a", "metadata": {"origin_pos": 20}, "source": ["对上一个例子中稠密块的输出[**使用**]通道数为10的[**过渡层**]。\n", "此时输出的通道数减为10，高和宽均减半。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "7ca47bbc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:16:11.786750Z", "iopub.status.busy": "2023-08-18T07:16:11.786485Z", "iopub.status.idle": "2023-08-18T07:16:11.794052Z", "shell.execute_reply": "2023-08-18T07:16:11.792935Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([4, 10, 4, 4])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["blk = transition_block(23, 10)\n", "blk(Y).shape"]}, {"cell_type": "markdown", "id": "a4994898", "metadata": {"origin_pos": 24}, "source": ["## [**DenseNet模型**]\n", "\n", "我们来构造DenseNet模型。DenseNet首先使用同ResNet一样的单卷积层和最大汇聚层。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "2592cd17", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:16:11.799267Z", "iopub.status.busy": "2023-08-18T07:16:11.798419Z", "iopub.status.idle": "2023-08-18T07:16:11.805699Z", "shell.execute_reply": "2023-08-18T07:16:11.804494Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [], "source": ["b1 = nn.Sequential(\n", "    nn.Conv2d(1, 64, kernel_size=7, stride=2, padding=3),\n", "    nn.<PERSON><PERSON><PERSON><PERSON>2d(64), nn.<PERSON><PERSON><PERSON>(),\n", "    nn.MaxPool2d(kernel_size=3, stride=2, padding=1))"]}, {"cell_type": "markdown", "id": "1480601a", "metadata": {"origin_pos": 29}, "source": ["接下来，类似于ResNet使用的4个残差块，DenseNet使用的是4个稠密块。\n", "与ResNet类似，我们可以设置每个稠密块使用多少个卷积层。\n", "这里我们设成4，从而与 :numref:`sec_resnet`的ResNet-18保持一致。\n", "稠密块里的卷积层通道数（即增长率）设为32，所以每个稠密块将增加128个通道。\n", "\n", "在每个模块之间，ResNet通过步幅为2的残差块减小高和宽，DenseNet则使用过渡层来减半高和宽，并减半通道数。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "572c0b37", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:16:11.810681Z", "iopub.status.busy": "2023-08-18T07:16:11.809914Z", "iopub.status.idle": "2023-08-18T07:16:11.835094Z", "shell.execute_reply": "2023-08-18T07:16:11.834042Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [], "source": ["# num_channels为当前的通道数\n", "num_channels, growth_rate = 64, 32\n", "num_convs_in_dense_blocks = [4, 4, 4, 4]\n", "blks = []\n", "for i, num_convs in enumerate(num_convs_in_dense_blocks):\n", "    blks.append(DenseBlock(num_convs, num_channels, growth_rate))\n", "    # 上一个稠密块的输出通道数\n", "    num_channels += num_convs * growth_rate\n", "    # 在稠密块之间添加一个转换层，使通道数量减半\n", "    if i != len(num_convs_in_dense_blocks) - 1:\n", "        blks.append(transition_block(num_channels, num_channels // 2))\n", "        num_channels = num_channels // 2"]}, {"cell_type": "markdown", "id": "10c456d6", "metadata": {"origin_pos": 34}, "source": ["与ResNet类似，最后接上全局汇聚层和全连接层来输出结果。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "2ea5a6f7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:16:11.840349Z", "iopub.status.busy": "2023-08-18T07:16:11.839579Z", "iopub.status.idle": "2023-08-18T07:16:11.847204Z", "shell.execute_reply": "2023-08-18T07:16:11.846173Z"}, "origin_pos": 36, "tab": ["pytorch"]}, "outputs": [], "source": ["net = nn.Sequential(\n", "    b1, *blks,\n", "    nn.BatchNorm2d(num_channels), nn.ReLU(),\n", "    nn.AdaptiveAvgPool2d((1, 1)),\n", "    nn.<PERSON>(),\n", "    nn.Linear(num_channels, 10))"]}, {"cell_type": "markdown", "id": "c9ac6a83", "metadata": {"origin_pos": 39}, "source": ["## [**训练模型**]\n", "\n", "由于这里使用了比较深的网络，本节里我们将输入高和宽从224降到96来简化计算。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "dab03cd3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:16:11.852215Z", "iopub.status.busy": "2023-08-18T07:16:11.851453Z", "iopub.status.idle": "2023-08-18T07:18:39.645340Z", "shell.execute_reply": "2023-08-18T07:18:39.643627Z"}, "origin_pos": 40, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.140, train acc 0.948, test acc 0.885\n", "5626.3 examples/sec on cuda:0\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"180.65625pt\" viewBox=\"0 0 238.**********.65625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:18:39.593886</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 180.65625 \n", "L 238.**********.65625 \n", "L 238.965625 0 \n", "L 0 0 \n", "L 0 180.65625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 51.803125 143.1 \n", "L 51.803125 7.2 \n", "\" clip-path=\"url(#p308a9c4788)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m9e3e273b22\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9e3e273b22\" x=\"51.803125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(48.621875 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 95.203125 143.1 \n", "L 95.203125 7.2 \n", "\" clip-path=\"url(#p308a9c4788)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m9e3e273b22\" x=\"95.203125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(92.021875 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 138.603125 143.1 \n", "L 138.603125 7.2 \n", "\" clip-path=\"url(#p308a9c4788)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m9e3e273b22\" x=\"138.603125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(135.421875 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 182.003125 143.1 \n", "L 182.003125 7.2 \n", "\" clip-path=\"url(#p308a9c4788)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m9e3e273b22\" x=\"182.003125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(178.821875 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.1 \n", "L 225.403125 7.2 \n", "\" clip-path=\"url(#p308a9c4788)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m9e3e273b22\" x=\"225.403125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 171.376563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.103125 127.173986 \n", "L 225.403125 127.173986 \n", "\" clip-path=\"url(#p308a9c4788)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m7fcf519f9e\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7fcf519f9e\" x=\"30.103125\" y=\"127.173986\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 130.973205)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 96.870508 \n", "L 225.403125 96.870508 \n", "\" clip-path=\"url(#p308a9c4788)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m7fcf519f9e\" x=\"30.103125\" y=\"96.870508\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 100.669726)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 66.56703 \n", "L 225.403125 66.56703 \n", "\" clip-path=\"url(#p308a9c4788)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m7fcf519f9e\" x=\"30.103125\" y=\"66.56703\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 70.366248)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 36.263551 \n", "L 225.403125 36.263551 \n", "\" clip-path=\"url(#p308a9c4788)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m7fcf519f9e\" x=\"30.103125\" y=\"36.263551\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 40.06277)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 12.743125 18.210709 \n", "L 17.083125 48.909784 \n", "L 21.423125 63.050175 \n", "L 25.763125 71.189328 \n", "L 30.103125 77.777825 \n", "L 34.443125 109.269534 \n", "L 38.783125 108.420333 \n", "L 43.123125 109.802359 \n", "L 47.463125 110.411659 \n", "L 51.803125 111.204948 \n", "L 56.143125 117.666514 \n", "L 60.483125 117.072275 \n", "L 64.823125 117.417983 \n", "L 69.163125 117.8848 \n", "L 73.503125 118.25044 \n", "L 77.843125 123.420604 \n", "L 82.183125 123.022621 \n", "L 86.523125 122.983477 \n", "L 90.863125 122.787226 \n", "L 95.203125 122.816086 \n", "L 99.543125 124.751918 \n", "L 103.883125 125.328719 \n", "L 108.223125 125.56782 \n", "L 112.563125 125.692357 \n", "L 116.903125 125.931528 \n", "L 121.243125 126.900266 \n", "L 125.583125 128.36049 \n", "L 129.923125 128.735217 \n", "L 134.263125 128.439802 \n", "L 138.603125 128.428478 \n", "L 142.943125 131.736328 \n", "L 147.283125 130.681015 \n", "L 151.623125 130.823179 \n", "L 155.963125 130.595625 \n", "L 160.303125 130.752338 \n", "L 164.643125 134.70501 \n", "L 168.983125 134.080606 \n", "L 173.323125 133.262061 \n", "L 177.663125 132.961206 \n", "L 182.003125 132.693633 \n", "L 186.343125 135.903904 \n", "L 190.683125 135.703071 \n", "L 195.023125 135.000597 \n", "L 199.363125 134.852569 \n", "L 203.703125 134.651967 \n", "L 208.043125 136.254471 \n", "L 212.383125 136.922727 \n", "L 216.723125 136.900051 \n", "L 221.063125 136.637578 \n", "L 225.403125 136.279003 \n", "\" clip-path=\"url(#p308a9c4788)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 12.743125 53.397408 \n", "L 17.083125 43.518303 \n", "L 21.423125 38.9324 \n", "L 25.763125 36.359257 \n", "L 30.103125 34.210491 \n", "L 34.443125 22.872295 \n", "L 38.783125 23.558607 \n", "L 43.123125 23.115757 \n", "L 47.463125 23.039151 \n", "L 51.803125 22.866889 \n", "L 56.143125 20.706322 \n", "L 60.483125 21.077812 \n", "L 64.823125 20.794472 \n", "L 69.163125 20.545763 \n", "L 73.503125 20.402206 \n", "L 77.843125 18.41442 \n", "L 82.183125 18.420716 \n", "L 86.523125 18.431211 \n", "L 90.863125 18.439606 \n", "L 95.203125 18.490562 \n", "L 99.543125 17.847741 \n", "L 103.883125 17.734405 \n", "L 108.223125 17.5665 \n", "L 112.563125 17.630514 \n", "L 116.903125 17.505699 \n", "L 121.243125 17.129947 \n", "L 125.583125 16.783644 \n", "L 129.923125 16.651418 \n", "L 134.263125 16.742717 \n", "L 138.603125 16.773364 \n", "L 142.943125 15.366946 \n", "L 147.283125 15.807696 \n", "L 151.623125 15.74893 \n", "L 155.963125 15.861216 \n", "L 160.303125 15.710217 \n", "L 164.643125 13.918766 \n", "L 168.983125 14.170623 \n", "L 173.323125 14.615571 \n", "L 177.663125 14.825453 \n", "L 182.003125 14.950105 \n", "L 186.343125 13.377273 \n", "L 190.683125 13.673205 \n", "L 195.023125 13.998521 \n", "L 199.363125 14.063584 \n", "L 203.703125 14.202619 \n", "L 208.043125 14.208402 \n", "L 212.383125 13.572462 \n", "L 216.723125 13.641723 \n", "L 221.063125 13.704687 \n", "L 225.403125 13.846554 \n", "\" clip-path=\"url(#p308a9c4788)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 30.103125 31.142264 \n", "L 51.803125 23.233056 \n", "L 73.503125 35.839303 \n", "L 95.203125 25.945217 \n", "L 116.903125 24.263374 \n", "L 138.603125 21.914855 \n", "L 160.303125 26.157342 \n", "L 182.003125 20.642108 \n", "L 203.703125 22.65729 \n", "L 225.403125 23.369422 \n", "\" clip-path=\"url(#p308a9c4788)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.1 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.1 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 98.667187 \n", "L 218.403125 98.667187 \n", "Q 220.403125 98.667187 220.403125 96.667187 \n", "L 220.403125 53.632812 \n", "Q 220.403125 51.632812 218.403125 51.632812 \n", "L 140.634375 51.632812 \n", "Q 138.634375 51.632812 138.634375 53.632812 \n", "L 138.634375 96.667187 \n", "Q 138.634375 98.667187 140.634375 98.667187 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_22\">\n", "     <path d=\"M 142.634375 59.73125 \n", "L 152.634375 59.73125 \n", "L 162.634375 59.73125 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 63.23125)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_23\">\n", "     <path d=\"M 142.634375 74.409375 \n", "L 152.634375 74.409375 \n", "L 162.634375 74.409375 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 77.909375)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_24\">\n", "     <path d=\"M 142.634375 89.0875 \n", "L 152.634375 89.0875 \n", "L 162.634375 89.0875 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(170.634375 92.5875)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p308a9c4788\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["lr, num_epochs, batch_size = 0.1, 10, 256\n", "train_iter, test_iter = d2l.load_data_fashion_mnist(batch_size, resize=96)\n", "d2l.train_ch6(net, train_iter, test_iter, num_epochs, lr, d2l.try_gpu())"]}, {"cell_type": "markdown", "id": "459b1b01", "metadata": {"origin_pos": 41}, "source": ["## 小结\n", "\n", "* 在跨层连接上，不同于ResNet中将输入与输出相加，稠密连接网络（DenseNet）在通道维上连结输入与输出。\n", "* DenseNet的主要构建模块是稠密块和过渡层。\n", "* 在构建DenseNet时，我们需要通过添加过渡层来控制网络的维数，从而再次减少通道的数量。\n", "\n", "## 练习\n", "\n", "1. 为什么我们在过渡层使用平均汇聚层而不是最大汇聚层？\n", "1. DenseNet的优点之一是其模型参数比ResNet小。为什么呢？\n", "1. DenseNet一个诟病的问题是内存或显存消耗过多。\n", "    1. 真的是这样吗？可以把输入形状换成$224 \\times 224$，来看看实际的显存消耗。\n", "    1. 有另一种方法来减少显存消耗吗？需要改变框架么？\n", "1. 实现DenseNet论文 :cite:<PERSON><PERSON><PERSON><PERSON>-Maaten.ea.2017`表1所示的不同DenseNet版本。\n", "1. 应用DenseNet的思想设计一个基于多层感知机的模型。将其应用于 :numref:`sec_kaggle_house`中的房价预测任务。\n"]}, {"cell_type": "markdown", "id": "710e8fed", "metadata": {"origin_pos": 43, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1880)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
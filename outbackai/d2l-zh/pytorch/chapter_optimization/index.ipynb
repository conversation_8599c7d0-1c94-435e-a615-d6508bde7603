{"cells": [{"cell_type": "markdown", "id": "18ed6c9f", "metadata": {"origin_pos": 0}, "source": ["# 优化算法\n", ":label:`chap_optimization`\n", "\n", "截止到目前，本书已经使用了许多优化算法来训练深度学习模型。优化算法使我们能够继续更新模型参数，并使损失函数的值最小化。这就像在训练集上评估一样。事实上，任何满足于将优化视为黑盒装置，以在简单的设置中最小化目标函数的人，都可能会知道存在着一系列此类“咒语”（名称如“SGD”和“Adam”）。\n", "\n", "但是，为了做得更好，还需要更深入的知识。优化算法对于深度学习非常重要。一方面，训练复杂的深度学习模型可能需要数小时、几天甚至数周。优化算法的性能直接影响模型的训练效率。另一方面，了解不同优化算法的原则及其超参数的作用将使我们能够以有针对性的方式调整超参数，以提高深度学习模型的性能。\n", "\n", "在本章中，我们深入探讨常见的深度学习优化算法。深度学习中出现的几乎所有优化问题都是*非凸*的。尽管如此，在*凸问题*背景下设计和分析算法是非常有启发性的。正是出于这个原因，本章包括了凸优化的入门，以及凸目标函数上非常简单的随机梯度下降算法的证明。\n", "\n", ":begin_tab:toc\n", " - [optimization-intro](optimization-intro.ipynb)\n", " - [convexity](convexity.ipynb)\n", " - [gd](gd.ipynb)\n", " - [sgd](sgd.ipynb)\n", " - [minibatch-sgd](minibatch-sgd.ipynb)\n", " - [momentum](momentum.ipynb)\n", " - [adagrad](adagrad.ipynb)\n", " - [rmsprop](rmsprop.ipynb)\n", " - [adadelta](adadelta.ipynb)\n", " - [adam](adam.ipynb)\n", " - [lr-scheduler](lr-scheduler.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "6efa4466", "metadata": {"origin_pos": 0}, "source": ["# RMSProp算法\n", ":label:`sec_rmsprop`\n", "\n", " :numref:`sec_adagrad`中的关键问题之一，是学习率按预定时间表$\\mathcal{O}(t^{-\\frac{1}{2}})$显著降低。\n", "虽然这通常适用于凸问题，但对于深度学习中遇到的非凸问题，可能并不理想。\n", "但是，作为一个预处理器，Adagrad算法按坐标顺序的适应性是非常可取的。\n", "\n", " :cite:`Tieleman.Hinton.2012`建议以RMSProp算法作为将速率调度与坐标自适应学习率分离的简单修复方法。\n", "问题在于，Adagrad算法将梯度$\\mathbf{g}_t$的平方累加成状态矢量$\\mathbf{s}_t = \\mathbf{s}_{t-1} + \\mathbf{g}_t^2$。\n", "因此，由于缺乏规范化，没有约束力，$\\mathbf{s}_t$持续增长，几乎上是在算法收敛时呈线性递增。\n", "\n", "解决此问题的一种方法是使用$\\mathbf{s}_t / t$。\n", "对$\\mathbf{g}_t$的合理分布来说，它将收敛。\n", "遗憾的是，限制行为生效可能需要很长时间，因为该流程记住了值的完整轨迹。\n", "另一种方法是按动量法中的方式使用泄漏平均值，即$\\mathbf{s}_t \\leftarrow \\gamma \\mathbf{s}_{t-1} + (1-\\gamma) \\mathbf{g}_t^2$，其中参数$\\gamma > 0$。\n", "保持所有其它部分不变就产生了RMSProp算法。\n", "\n", "## 算法\n", "\n", "让我们详细写出这些方程式。\n", "\n", "$$\\begin{aligned}\n", "    \\mathbf{s}_t & \\leftarrow \\gamma \\mathbf{s}_{t-1} + (1 - \\gamma) \\mathbf{g}_t^2, \\\\\n", "    \\mathbf{x}_t & \\leftarrow \\mathbf{x}_{t-1} - \\frac{\\eta}{\\sqrt{\\mathbf{s}_t + \\epsilon}} \\odot \\mathbf{g}_t.\n", "\\end{aligned}$$\n", "\n", "常数$\\epsilon > 0$通常设置为$10^{-6}$，以确保我们不会因除以零或步长过大而受到影响。\n", "鉴于这种扩展，我们现在可以自由控制学习率$\\eta$，而不考虑基于每个坐标应用的缩放。\n", "就泄漏平均值而言，我们可以采用与之前在动量法中适用的相同推理。\n", "扩展$\\mathbf{s}_t$定义可获得\n", "\n", "$$\n", "\\begin{aligned}\n", "\\mathbf{s}_t & = (1 - \\gamma) \\mathbf{g}_t^2 + \\gamma \\mathbf{s}_{t-1} \\\\\n", "& = (1 - \\gamma) \\left(\\mathbf{g}_t^2 + \\gamma \\mathbf{g}_{t-1}^2 + \\gamma^2 \\mathbf{g}_{t-2} + \\ldots, \\right).\n", "\\end{aligned}\n", "$$\n", "\n", "同之前在 :numref:`sec_momentum`小节一样，我们使用$1 + \\gamma + \\gamma^2 + \\ldots, = \\frac{1}{1-\\gamma}$。\n", "因此，权重总和标准化为$1$且观测值的半衰期为$\\gamma^{-1}$。\n", "让我们图像化各种数值的$\\gamma$在过去40个时间步长的权重。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "30751083", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:32.547846Z", "iopub.status.busy": "2023-08-18T07:05:32.547295Z", "iopub.status.idle": "2023-08-18T07:05:34.484898Z", "shell.execute_reply": "2023-08-18T07:05:34.483633Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import math\n", "import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "code", "execution_count": 2, "id": "254f2129", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:34.489092Z", "iopub.status.busy": "2023-08-18T07:05:34.488387Z", "iopub.status.idle": "2023-08-18T07:05:34.615400Z", "shell.execute_reply": "2023-08-18T07:05:34.614588Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"234.6408pt\" height=\"180.65625pt\" viewBox=\"0 0 234.6408 180.65625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:05:34.587794</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 180.65625 \n", "L 234.6408 180.65625 \n", "L 234.6408 0 \n", "L 0 0 \n", "L 0 180.65625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m1f3e79b22b\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1f3e79b22b\" x=\"38.980398\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(35.799148 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m1f3e79b22b\" x=\"84.504873\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(78.142373 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m1f3e79b22b\" x=\"130.029349\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(123.666849 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m1f3e79b22b\" x=\"175.553824\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(169.191324 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m1f3e79b22b\" x=\"221.0783\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(214.7158 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- time -->\n", "     <g transform=\"translate(116.457031 171.376563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m8d35825861\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8d35825861\" x=\"30.103125\" y=\"136.92284\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 140.722058)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m8d35825861\" x=\"30.103125\" y=\"95.740984\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.1 -->\n", "      <g transform=\"translate(7.2 99.540203)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m8d35825861\" x=\"30.103125\" y=\"54.559128\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 58.358347)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m8d35825861\" x=\"30.103125\" y=\"13.377273\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.3 -->\n", "      <g transform=\"translate(7.2 17.176491)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_10\">\n", "    <path d=\"M 38.980398 116.331912 \n", "L 43.532845 117.361458 \n", "L 48.085293 118.339527 \n", "L 52.63774 119.268693 \n", "L 57.190188 120.1514 \n", "L 61.742635 120.989972 \n", "L 66.295083 121.786616 \n", "L 70.847531 122.543427 \n", "L 75.399978 123.262397 \n", "L 79.952426 123.94542 \n", "L 84.504873 124.594291 \n", "L 89.057321 125.210718 \n", "L 93.609768 125.796324 \n", "L 98.162216 126.35265 \n", "L 102.714663 126.881159 \n", "L 107.267111 127.383243 \n", "L 111.819559 127.860223 \n", "L 116.372006 128.313354 \n", "L 120.924454 128.743828 \n", "L 125.476901 129.152779 \n", "L 130.029349 129.541282 \n", "L 134.581796 129.91036 \n", "L 139.134244 130.260984 \n", "L 143.686691 130.594077 \n", "L 148.239139 130.910515 \n", "L 152.791587 131.211131 \n", "L 157.344034 131.496716 \n", "L 161.896482 131.768023 \n", "L 166.448929 132.025763 \n", "L 171.001377 132.270617 \n", "L 175.553824 132.503228 \n", "L 180.106272 132.724209 \n", "L 184.658719 132.93414 \n", "L 189.211167 133.133575 \n", "L 193.763615 133.323039 \n", "L 198.316062 133.503029 \n", "L 202.86851 133.674019 \n", "L 207.420957 133.83646 \n", "L 211.973405 133.990779 \n", "L 216.525852 134.137382 \n", "\" clip-path=\"url(#pec40656a68)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 38.980398 95.740984 \n", "L 43.532845 99.85917 \n", "L 48.085293 103.565537 \n", "L 52.63774 106.901267 \n", "L 57.190188 109.903424 \n", "L 61.742635 112.605366 \n", "L 66.295083 115.037113 \n", "L 70.847531 117.225686 \n", "L 75.399978 119.195401 \n", "L 79.952426 120.968145 \n", "L 84.504873 122.563614 \n", "L 89.057321 123.999537 \n", "L 93.609768 125.291867 \n", "L 98.162216 126.454964 \n", "L 102.714663 127.501752 \n", "L 107.267111 128.443861 \n", "L 111.819559 129.291759 \n", "L 116.372006 130.054867 \n", "L 120.924454 130.741664 \n", "L 125.476901 131.359782 \n", "L 130.029349 131.916087 \n", "L 134.581796 132.416763 \n", "L 139.134244 132.86737 \n", "L 143.686691 133.272917 \n", "L 148.239139 133.637909 \n", "L 152.791587 133.966403 \n", "L 157.344034 134.262046 \n", "L 161.896482 134.528126 \n", "L 166.448929 134.767597 \n", "L 171.001377 134.983121 \n", "L 175.553824 135.177093 \n", "L 180.106272 135.351668 \n", "L 184.658719 135.508785 \n", "L 189.211167 135.65019 \n", "L 193.763615 135.777455 \n", "L 198.316062 135.891994 \n", "L 202.86851 135.995078 \n", "L 207.420957 136.087854 \n", "L 211.973405 136.171353 \n", "L 216.525852 136.246502 \n", "\" clip-path=\"url(#pec40656a68)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 38.980398 54.559128 \n", "L 43.532845 71.031871 \n", "L 48.085293 84.210064 \n", "L 52.63774 94.752619 \n", "L 57.190188 103.186664 \n", "L 61.742635 109.933899 \n", "L 66.295083 115.331687 \n", "L 70.847531 119.649917 \n", "L 75.399978 123.104502 \n", "L 79.952426 125.868169 \n", "L 84.504873 128.079103 \n", "L 89.057321 129.847851 \n", "L 93.609768 131.262849 \n", "L 98.162216 132.394847 \n", "L 102.714663 133.300445 \n", "L 107.267111 134.024924 \n", "L 111.819559 134.604507 \n", "L 116.372006 135.068174 \n", "L 120.924454 135.439107 \n", "L 125.476901 135.735853 \n", "L 130.029349 135.973251 \n", "L 134.581796 136.163168 \n", "L 139.134244 136.315103 \n", "L 143.686691 136.43665 \n", "L 148.239139 136.533888 \n", "L 152.791587 136.611678 \n", "L 157.344034 136.673911 \n", "L 161.896482 136.723696 \n", "L 166.448929 136.763525 \n", "L 171.001377 136.795388 \n", "L 175.553824 136.820878 \n", "L 180.106272 136.841271 \n", "L 184.658719 136.857584 \n", "L 189.211167 136.870635 \n", "L 193.763615 136.881076 \n", "L 198.316062 136.889429 \n", "L 202.86851 136.896111 \n", "L 207.420957 136.901457 \n", "L 211.973405 136.905733 \n", "L 216.525852 136.909155 \n", "\" clip-path=\"url(#pec40656a68)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 38.980398 13.377273 \n", "L 43.532845 50.440943 \n", "L 48.085293 76.385512 \n", "L 52.63774 94.54671 \n", "L 57.190188 107.259549 \n", "L 61.742635 116.158536 \n", "L 66.295083 122.387827 \n", "L 70.847531 126.748331 \n", "L 75.399978 129.800684 \n", "L 79.952426 131.93733 \n", "L 84.504873 133.432983 \n", "L 89.057321 134.47994 \n", "L 93.609768 135.21281 \n", "L 98.162216 135.725819 \n", "L 102.714663 136.084925 \n", "L 107.267111 136.336299 \n", "L 111.819559 136.512262 \n", "L 116.372006 136.635435 \n", "L 120.924454 136.721656 \n", "L 125.476901 136.782011 \n", "L 130.029349 136.82426 \n", "L 134.581796 136.853834 \n", "L 139.134244 136.874536 \n", "L 143.686691 136.889027 \n", "L 148.239139 136.899171 \n", "L 152.791587 136.906271 \n", "L 157.344034 136.911242 \n", "L 161.896482 136.914721 \n", "L 166.448929 136.917157 \n", "L 171.001377 136.918862 \n", "L 175.553824 136.920055 \n", "L 180.106272 136.92089 \n", "L 184.658719 136.921475 \n", "L 189.211167 136.921885 \n", "L 193.763615 136.922171 \n", "L 198.316062 136.922372 \n", "L 202.86851 136.922512 \n", "L 207.420957 136.92261 \n", "L 211.973405 136.922679 \n", "L 216.525852 136.922727 \n", "\" clip-path=\"url(#pec40656a68)\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.1 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.1 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.1 \n", "L 225.**********.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pec40656a68\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["d2l.set_figsize()\n", "gammas = [0.95, 0.9, 0.8, 0.7]\n", "for gamma in gammas:\n", "    x = torch.arange(40).detach().numpy()\n", "    d2l.plt.plot(x, (1-gamma) * gamma ** x, label=f'gamma = {gamma:.2f}')\n", "d2l.plt.xlabel('time');"]}, {"cell_type": "markdown", "id": "19893348", "metadata": {"origin_pos": 6}, "source": ["## 从零开始实现\n", "\n", "和之前一样，我们使用二次函数$f(\\mathbf{x})=0.1x_1^2+2x_2^2$来观察RMSProp算法的轨迹。\n", "回想在 :numref:`sec_adagrad`一节中，当我们使用学习率为0.4的Adagrad算法时，变量在算法的后期阶段移动非常缓慢，因为学习率衰减太快。\n", "RMSProp算法中不会发生这种情况，因为$\\eta$是单独控制的。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c3f8b14e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:34.622343Z", "iopub.status.busy": "2023-08-18T07:05:34.621764Z", "iopub.status.idle": "2023-08-18T07:05:34.731199Z", "shell.execute_reply": "2023-08-18T07:05:34.730392Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 20, x1: -0.010599, x2: 0.000000\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"245.120313pt\" height=\"180.65625pt\" viewBox=\"0 0 245.**********.65625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:05:34.702544</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 180.65625 \n", "L 245.**********.65625 \n", "L 245.120313 0 \n", "L 0 0 \n", "L 0 180.65625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.1 \n", "L 237.**********.1 \n", "L 237.920313 7.2 \n", "L 42.620312 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mbcd4ce8ae3\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mbcd4ce8ae3\" x=\"88.39375\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(81.022656 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mbcd4ce8ae3\" x=\"149.425\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(142.053907 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mbcd4ce8ae3\" x=\"210.456251\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(207.275001 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- x1 -->\n", "     <g transform=\"translate(134.129687 171.376563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m6e6c958be9\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m6e6c958be9\" x=\"42.620312\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −3 -->\n", "      <g transform=\"translate(20.878125 146.899219)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m6e6c958be9\" x=\"42.620312\" y=\"108.253846\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(20.878125 112.053065)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m6e6c958be9\" x=\"42.620312\" y=\"73.407692\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(20.878125 77.206911)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m6e6c958be9\" x=\"42.620312\" y=\"38.561538\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 42.360757)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- x2 -->\n", "     <g transform=\"translate(14.798437 81.290625)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_8\">\n", "    <path d=\"M 57.878125 108.253846 \n", "L 96.477484 64.176564 \n", "L 120.357142 48.253159 \n", "L 138.043262 41.967444 \n", "L 152.011133 39.642221 \n", "L 163.368488 38.864648 \n", "L 172.726062 38.63476 \n", "L 180.468975 38.576222 \n", "L 186.866078 38.563848 \n", "L 192.121325 38.561796 \n", "L 196.400251 38.561554 \n", "L 199.844228 38.561538 \n", "L 202.578132 38.561538 \n", "L 204.714224 38.561538 \n", "L 206.353866 38.561538 \n", "L 207.58804 38.561538 \n", "L 208.497344 38.561538 \n", "L 209.151871 38.561538 \n", "L 209.611244 38.561538 \n", "L 209.924923 38.561538 \n", "L 210.132826 38.561538 \n", "\" clip-path=\"url(#p1357a9a15c)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m6e5362a764\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p1357a9a15c)\">\n", "     <use xlink:href=\"#m6e5362a764\" x=\"57.878125\" y=\"108.253846\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"96.477484\" y=\"64.176564\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"120.357142\" y=\"48.253159\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"138.043262\" y=\"41.967444\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"152.011133\" y=\"39.642221\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"163.368488\" y=\"38.864648\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"172.726062\" y=\"38.63476\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"180.468975\" y=\"38.576222\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"186.866078\" y=\"38.563848\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"192.121325\" y=\"38.561796\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"196.400251\" y=\"38.561554\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"199.844228\" y=\"38.561538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"202.578132\" y=\"38.561538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"204.714224\" y=\"38.561538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"206.353866\" y=\"38.561538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"207.58804\" y=\"38.561538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"208.497344\" y=\"38.561538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"209.151871\" y=\"38.561538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"209.611244\" y=\"38.561538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"209.924923\" y=\"38.561538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m6e5362a764\" x=\"210.132826\" y=\"38.561538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"PathCollection_1\"/>\n", "   <g id=\"PathCollection_2\">\n", "    <path d=\"M 97.100868 7.2 \n", "L 94.496869 7.855929 \n", "L 91.44531 8.645091 \n", "L 88.393757 9.454749 \n", "L 85.342191 10.284908 \n", "L 83.908323 10.684614 \n", "L 82.290631 11.19569 \n", "L 79.239071 12.182998 \n", "L 76.187512 13.193534 \n", "L 73.307377 14.16923 \n", "L 73.135938 14.236244 \n", "L 70.084378 15.455857 \n", "L 67.032818 16.702275 \n", "L 64.752171 17.653845 \n", "L 63.981244 18.033987 \n", "L 60.929685 19.570384 \n", "L 57.878125 21.138461 \n", "L 54.826565 23.093713 \n", "L 52.48604 24.623076 \n", "L 51.775006 25.220431 \n", "L 48.723432 27.833908 \n", "L 48.409729 28.107691 \n", "L 45.671872 31.452924 \n", "L 45.55989 31.592307 \n", "L 43.880133 35.076923 \n", "L 43.320214 38.561539 \n", "L 43.880133 42.046154 \n", "L 45.55989 45.530769 \n", "L 45.671872 45.670152 \n", "L 48.409729 49.015384 \n", "L 48.723432 49.289168 \n", "L 51.775006 51.902645 \n", "L 52.486042 52.500001 \n", "L 54.826565 54.029361 \n", "L 57.878125 55.984615 \n", "L 60.929685 57.552692 \n", "L 63.981244 59.089089 \n", "L 64.752171 59.469231 \n", "L 67.032818 60.4208 \n", "L 70.084378 61.667219 \n", "L 73.135938 62.886832 \n", "L 73.307377 62.953845 \n", "L 76.187512 63.929541 \n", "L 79.239071 64.940078 \n", "L 82.290631 65.927386 \n", "L 83.908319 66.438459 \n", "L 85.342191 66.838166 \n", "L 88.393757 67.668327 \n", "L 91.44531 68.477984 \n", "L 94.496869 69.267147 \n", "L 97.100868 69.923076 \n", "L 97.548436 70.023948 \n", "L 100.599996 70.69336 \n", "L 103.651563 71.344434 \n", "L 106.703122 71.977164 \n", "L 109.754682 72.591556 \n", "L 112.806249 73.187609 \n", "L 113.968755 73.407692 \n", "L 115.857816 73.731264 \n", "L 118.909375 74.237361 \n", "L 121.960942 74.726869 \n", "L 125.012502 75.199781 \n", "L 128.064069 75.6561 \n", "L 131.115628 76.095825 \n", "L 134.167188 76.518956 \n", "L 136.969651 76.892308 \n", "L 137.218755 76.922609 \n", "L 140.270314 77.278645 \n", "L 143.321874 77.619532 \n", "L 146.373441 77.945268 \n", "L 149.425 78.255852 \n", "L 152.476564 78.551287 \n", "L 155.528127 78.831573 \n", "L 158.57969 79.096705 \n", "L 161.631253 79.346689 \n", "L 164.682813 79.581522 \n", "L 167.734376 79.801203 \n", "L 170.785939 80.005736 \n", "L 173.837499 80.195117 \n", "L 176.889062 80.369347 \n", "L 177.034329 80.37692 \n", "L 179.940626 80.516308 \n", "L 182.992189 80.648722 \n", "L 186.04375 80.767201 \n", "L 189.095313 80.871738 \n", "L 192.146877 80.962338 \n", "L 195.198438 81.038999 \n", "L 198.250001 81.101723 \n", "L 201.301564 81.150507 \n", "L 204.353126 81.185354 \n", "L 207.404689 81.206262 \n", "L 210.456251 81.213231 \n", "L 213.507813 81.206262 \n", "L 216.559376 81.185354 \n", "L 219.610939 81.150507 \n", "L 222.662501 81.101723 \n", "L 225.714063 81.038999 \n", "L 228.765626 80.962338 \n", "L 231.817188 80.871738 \n", "L 234.868751 80.767201 \n", "L 237.920313 80.648722 \n", "\" clip-path=\"url(#p1357a9a15c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_3\">\n", "    <path d=\"M 42.620312 81.038999 \n", "L 45.671872 81.798647 \n", "L 48.723432 82.544351 \n", "L 51.775006 83.276123 \n", "L 54.263642 83.861536 \n", "L 54.826565 83.984147 \n", "L 57.878125 84.635897 \n", "L 60.929685 85.274743 \n", "L 63.981244 85.900682 \n", "L 67.032818 86.513718 \n", "L 70.084378 87.113847 \n", "L 71.29158 87.346153 \n", "L 73.135938 87.676592 \n", "L 76.187512 88.211301 \n", "L 79.239071 88.733993 \n", "L 82.290631 89.244669 \n", "L 85.342191 89.74333 \n", "L 88.393757 90.229974 \n", "L 91.44531 90.704602 \n", "L 92.277553 90.830769 \n", "L 94.496869 91.145507 \n", "L 97.548436 91.567034 \n", "L 100.599996 91.977319 \n", "L 103.651563 92.376364 \n", "L 106.703122 92.764167 \n", "L 109.754682 93.14073 \n", "L 112.806249 93.506052 \n", "L 115.857816 93.860135 \n", "L 118.909375 94.202976 \n", "L 119.943828 94.315385 \n", "L 121.960942 94.521292 \n", "L 125.012502 94.822237 \n", "L 128.064069 95.112621 \n", "L 131.115628 95.392446 \n", "L 134.167188 95.661713 \n", "L 137.218755 95.92042 \n", "L 140.270314 96.168567 \n", "L 143.321874 96.406152 \n", "L 146.373441 96.633182 \n", "L 149.425 96.84965 \n", "L 152.476564 97.055559 \n", "L 155.528127 97.25091 \n", "L 158.57969 97.435699 \n", "L 161.631253 97.60993 \n", "L 164.682813 97.773602 \n", "L 165.20896 97.800001 \n", "L 167.734376 97.919472 \n", "L 170.785939 98.053879 \n", "L 173.837499 98.17833 \n", "L 176.889062 98.292825 \n", "L 179.940626 98.397363 \n", "L 182.992189 98.491946 \n", "L 186.04375 98.576571 \n", "L 189.095313 98.651243 \n", "L 192.146877 98.715957 \n", "L 195.198438 98.770714 \n", "L 198.250001 98.815518 \n", "L 201.301564 98.850364 \n", "L 204.353126 98.875252 \n", "L 207.404689 98.890187 \n", "L 210.456251 98.895165 \n", "L 213.507813 98.890187 \n", "L 216.559376 98.875252 \n", "L 219.610939 98.850364 \n", "L 222.662501 98.815518 \n", "L 225.714063 98.770714 \n", "L 228.765626 98.715957 \n", "L 231.817188 98.651243 \n", "L 234.868751 98.576571 \n", "L 237.920313 98.491946 \n", "\" clip-path=\"url(#p1357a9a15c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_4\">\n", "    <path d=\"M 42.620312 98.770713 \n", "L 45.671872 99.313318 \n", "L 48.723432 99.845966 \n", "L 51.775006 100.368661 \n", "L 54.826565 100.881396 \n", "L 57.27387 101.284618 \n", "L 57.878125 101.378794 \n", "L 60.929685 101.844979 \n", "L 63.981244 102.301746 \n", "L 67.032818 102.749096 \n", "L 70.084378 103.187027 \n", "L 73.135938 103.615539 \n", "L 76.187512 104.034638 \n", "L 79.239071 104.444316 \n", "L 81.716227 104.769234 \n", "L 82.290631 104.840712 \n", "L 85.342191 105.211508 \n", "L 88.393757 105.573373 \n", "L 91.44531 105.926301 \n", "L 94.496869 106.270296 \n", "L 97.548436 106.605354 \n", "L 100.599996 106.931478 \n", "L 103.651563 107.248667 \n", "L 106.703122 107.556923 \n", "L 109.754682 107.856243 \n", "L 112.806249 108.146626 \n", "L 113.968764 108.253846 \n", "L 115.857816 108.419576 \n", "L 118.909375 108.6788 \n", "L 121.960942 108.929523 \n", "L 125.012502 109.171742 \n", "L 128.064069 109.405468 \n", "L 131.115628 109.630695 \n", "L 134.167188 109.847419 \n", "L 137.218755 110.055645 \n", "L 140.270314 110.255373 \n", "L 143.321874 110.446602 \n", "L 146.373441 110.629333 \n", "L 149.425 110.803565 \n", "L 152.476564 110.969295 \n", "L 155.528127 111.126526 \n", "L 158.57969 111.275263 \n", "L 161.631253 111.415497 \n", "L 164.682813 111.547229 \n", "L 167.734376 111.670467 \n", "L 169.542874 111.738466 \n", "L 170.785939 111.783032 \n", "L 173.837499 111.884327 \n", "L 176.889062 111.97752 \n", "L 179.940626 112.062609 \n", "L 182.992189 112.139595 \n", "L 186.04375 112.208477 \n", "L 189.095313 112.269257 \n", "L 192.146877 112.32193 \n", "L 195.198438 112.366503 \n", "L 198.250001 112.402969 \n", "L 201.301564 112.431332 \n", "L 204.353126 112.451592 \n", "L 207.404689 112.463745 \n", "L 210.456251 112.467798 \n", "L 213.507813 112.463745 \n", "L 216.559376 112.451592 \n", "L 219.610939 112.431332 \n", "L 222.662501 112.402969 \n", "L 225.714063 112.366503 \n", "L 228.765626 112.32193 \n", "L 231.817188 112.269257 \n", "L 234.868751 112.208477 \n", "L 237.920313 112.139595 \n", "\" clip-path=\"url(#p1357a9a15c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_5\">\n", "    <path d=\"M 42.620312 112.366503 \n", "L 45.671872 112.808155 \n", "L 48.723432 113.241707 \n", "L 51.775006 113.667157 \n", "L 54.826565 114.084499 \n", "L 57.878125 114.493739 \n", "L 60.929685 114.894875 \n", "L 63.477901 115.223078 \n", "L 63.981244 115.285026 \n", "L 67.032818 115.652848 \n", "L 70.084378 116.012922 \n", "L 73.135938 116.365258 \n", "L 76.187512 116.709846 \n", "L 79.239071 117.046692 \n", "L 82.290631 117.375794 \n", "L 85.342191 117.697154 \n", "L 88.393757 118.01077 \n", "L 91.44531 118.31664 \n", "L 94.496869 118.614767 \n", "L 95.47338 118.70769 \n", "L 97.548436 118.896748 \n", "L 100.599996 119.167362 \n", "L 103.651563 119.430562 \n", "L 106.703122 119.686349 \n", "L 109.754682 119.934722 \n", "L 112.806249 120.175678 \n", "L 115.857816 120.409221 \n", "L 118.909375 120.635353 \n", "L 121.960942 120.854069 \n", "L 125.012502 121.065367 \n", "L 128.064069 121.269256 \n", "L 131.115628 121.465731 \n", "L 134.167188 121.654788 \n", "L 137.218755 121.836432 \n", "L 140.270314 122.010663 \n", "L 143.321874 122.17748 \n", "L 143.605786 122.192311 \n", "L 146.373441 122.330983 \n", "L 149.425 122.476769 \n", "L 152.476564 122.615441 \n", "L 155.528127 122.747002 \n", "L 158.57969 122.871455 \n", "L 161.631253 122.988794 \n", "L 164.682813 123.099019 \n", "L 167.734376 123.202136 \n", "L 170.785939 123.298142 \n", "L 173.837499 123.387034 \n", "L 176.889062 123.468815 \n", "L 179.940626 123.543485 \n", "L 182.992189 123.611044 \n", "L 186.04375 123.671492 \n", "L 189.095313 123.724829 \n", "L 192.146877 123.771052 \n", "L 195.198438 123.810167 \n", "L 198.250001 123.842168 \n", "L 201.301564 123.867058 \n", "L 204.353126 123.884837 \n", "L 207.404689 123.895502 \n", "L 210.456251 123.899059 \n", "L 213.507813 123.895502 \n", "L 216.559376 123.884837 \n", "L 219.610939 123.867058 \n", "L 222.662501 123.842168 \n", "L 225.714063 123.810167 \n", "L 228.765626 123.771052 \n", "L 231.817188 123.724829 \n", "L 234.868751 123.671492 \n", "L 237.920313 123.611044 \n", "\" clip-path=\"url(#p1357a9a15c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_6\">\n", "    <path d=\"M 42.620312 123.810167 \n", "L 45.671872 124.197739 \n", "L 48.723432 124.578203 \n", "L 51.775006 124.951556 \n", "L 54.826565 125.317795 \n", "L 57.878125 125.676923 \n", "L 60.929685 126.015136 \n", "L 63.981244 126.346516 \n", "L 67.032818 126.671065 \n", "L 70.084378 126.988778 \n", "L 73.135938 127.299663 \n", "L 76.187512 127.603712 \n", "L 79.239071 127.900929 \n", "L 82.290631 128.191314 \n", "L 85.342191 128.474866 \n", "L 88.393757 128.751587 \n", "L 91.44531 129.021472 \n", "L 93.070112 129.161535 \n", "L 94.496869 129.279884 \n", "L 97.548436 129.526437 \n", "L 100.599996 129.766414 \n", "L 103.651563 129.999818 \n", "L 106.703122 130.226647 \n", "L 109.754682 130.446903 \n", "L 112.806249 130.66058 \n", "L 115.857816 130.867685 \n", "L 118.909375 131.068216 \n", "L 121.960942 131.262171 \n", "L 125.012502 131.449548 \n", "L 128.064069 131.630355 \n", "L 131.115628 131.804587 \n", "L 134.167188 131.972242 \n", "L 137.218755 132.133322 \n", "L 140.270314 132.287828 \n", "L 143.321874 132.43576 \n", "L 146.373441 132.577118 \n", "L 147.936485 132.646155 \n", "L 149.425 132.70951 \n", "L 152.476564 132.833055 \n", "L 155.528127 132.950264 \n", "L 158.57969 133.06114 \n", "L 161.631253 133.165679 \n", "L 164.682813 133.263879 \n", "L 167.734376 133.355747 \n", "L 170.785939 133.44128 \n", "L 173.837499 133.520475 \n", "L 176.889062 133.593334 \n", "L 179.940626 133.659858 \n", "L 182.992189 133.720047 \n", "L 186.04375 133.773901 \n", "L 189.095313 133.82142 \n", "L 192.146877 133.8626 \n", "L 195.198438 133.897448 \n", "L 198.250001 133.925958 \n", "L 201.301564 133.948133 \n", "L 204.353126 133.963972 \n", "L 207.404689 133.973474 \n", "L 210.456251 133.976643 \n", "L 213.507813 133.973474 \n", "L 216.559376 133.963972 \n", "L 219.610939 133.948133 \n", "L 222.662501 133.925958 \n", "L 225.714063 133.897448 \n", "L 228.765626 133.8626 \n", "L 231.817188 133.82142 \n", "L 234.868751 133.773901 \n", "L 237.920313 133.720047 \n", "\" clip-path=\"url(#p1357a9a15c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_7\">\n", "    <path d=\"M 42.620312 133.897445 \n", "L 45.671872 134.242743 \n", "L 48.723432 134.581696 \n", "L 51.775006 134.914323 \n", "L 54.826565 135.240612 \n", "L 57.878125 135.560557 \n", "L 60.929685 135.874175 \n", "L 63.477871 136.130768 \n", "L 63.981244 136.179677 \n", "L 67.032818 136.470059 \n", "L 70.084378 136.754332 \n", "L 73.135938 137.032489 \n", "L 76.187512 137.304535 \n", "L 79.239071 137.570466 \n", "L 82.290631 137.830284 \n", "L 85.342191 138.083987 \n", "L 88.393757 138.331578 \n", "L 91.44531 138.573055 \n", "L 94.496869 138.80842 \n", "L 97.548436 139.037672 \n", "L 100.599996 139.260809 \n", "L 103.651563 139.477831 \n", "L 105.641791 139.615388 \n", "L 106.703122 139.686256 \n", "L 109.754682 139.88411 \n", "L 112.806249 140.076061 \n", "L 115.857816 140.262103 \n", "L 118.909375 140.442243 \n", "L 121.960942 140.616473 \n", "L 125.012502 140.784796 \n", "L 128.064069 140.947215 \n", "L 131.115628 141.103726 \n", "L 134.167188 141.254334 \n", "L 137.218755 141.399034 \n", "L 140.270314 141.537831 \n", "L 143.321874 141.670719 \n", "L 146.373441 141.797698 \n", "L 149.425 141.918775 \n", "L 152.476564 142.033943 \n", "L 155.528127 142.143209 \n", "L 158.57969 142.246565 \n", "L 161.631253 142.344014 \n", "L 164.682813 142.435559 \n", "L 167.734376 142.521202 \n", "L 170.785939 142.60093 \n", "L 173.837499 142.674761 \n", "L 176.889062 142.742678 \n", "L 179.940626 142.804692 \n", "L 182.992189 142.860804 \n", "L 186.04375 142.911006 \n", "L 189.095313 142.9553 \n", "L 192.146877 142.993692 \n", "L 195.198438 143.026174 \n", "L 198.250001 143.052749 \n", "L 201.301564 143.07342 \n", "L 204.353126 143.088189 \n", "L 207.404689 143.097049 \n", "L 210.456251 143.1 \n", "\" clip-path=\"url(#p1357a9a15c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 210.456251 143.1 \n", "L 213.507813 143.097049 \n", "L 216.559376 143.088189 \n", "L 219.610939 143.07342 \n", "L 222.662501 143.052749 \n", "L 225.714063 143.026174 \n", "L 228.765626 142.993692 \n", "L 231.817188 142.9553 \n", "L 234.868751 142.911006 \n", "L 237.920313 142.860804 \n", "\" clip-path=\"url(#p1357a9a15c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_8\">\n", "    <path d=\"M 42.**********.026174 \n", "L 43.320206 143.1 \n", "\" clip-path=\"url(#p1357a9a15c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_9\"/>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.1 \n", "L 42.620312 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.1 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.1 \n", "L 237.**********.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 7.2 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p1357a9a15c\">\n", "   <rect x=\"42.620312\" y=\"7.2\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["def rmsprop_2d(x1, x2, s1, s2):\n", "    g1, g2, eps = 0.2 * x1, 4 * x2, 1e-6\n", "    s1 = gamma * s1 + (1 - gamma) * g1 ** 2\n", "    s2 = gamma * s2 + (1 - gamma) * g2 ** 2\n", "    x1 -= eta / math.sqrt(s1 + eps) * g1\n", "    x2 -= eta / math.sqrt(s2 + eps) * g2\n", "    return x1, x2, s1, s2\n", "\n", "def f_2d(x1, x2):\n", "    return 0.1 * x1 ** 2 + 2 * x2 ** 2\n", "\n", "eta, gamma = 0.4, 0.9\n", "d2l.show_trace_2d(f_2d, d2l.train_2d(rmsprop_2d))"]}, {"cell_type": "markdown", "id": "40cd5b7f", "metadata": {"origin_pos": 8}, "source": ["接下来，我们在深度网络中实现RMSProp算法。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "26c70a5c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:34.737342Z", "iopub.status.busy": "2023-08-18T07:05:34.736751Z", "iopub.status.idle": "2023-08-18T07:05:34.740975Z", "shell.execute_reply": "2023-08-18T07:05:34.740221Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["def init_rmsprop_states(feature_dim):\n", "    s_w = torch.zeros((feature_dim, 1))\n", "    s_b = torch.zeros(1)\n", "    return (s_w, s_b)"]}, {"cell_type": "code", "execution_count": 5, "id": "3fc7c2f1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:34.745617Z", "iopub.status.busy": "2023-08-18T07:05:34.744961Z", "iopub.status.idle": "2023-08-18T07:05:34.749992Z", "shell.execute_reply": "2023-08-18T07:05:34.749197Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["def rmsprop(params, states, hyperparams):\n", "    gamma, eps = hyperparams['gamma'], 1e-6\n", "    for p, s in zip(params, states):\n", "        with torch.no_grad():\n", "            s[:] = gamma * s + (1 - gamma) * torch.square(p.grad)\n", "            p[:] -= hyperparams['lr'] * p.grad / torch.sqrt(s + eps)\n", "        p.grad.data.zero_()"]}, {"cell_type": "markdown", "id": "2d099676", "metadata": {"origin_pos": 16}, "source": ["我们将初始学习率设置为0.01，加权项$\\gamma$设置为0.9。\n", "也就是说，$\\mathbf{s}$累加了过去的$1/(1-\\gamma) = 10$次平方梯度观测值的平均值。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "692fe2a3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:34.754673Z", "iopub.status.busy": "2023-08-18T07:05:34.754066Z", "iopub.status.idle": "2023-08-18T07:05:37.535518Z", "shell.execute_reply": "2023-08-18T07:05:37.534481Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss: 0.247, 0.014 sec/epoch\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"266.957813pt\" height=\"184.455469pt\" viewBox=\"0 0 266.**********.455469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:05:37.487309</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 184.455469 \n", "L 266.**********.455469 \n", "L 266.957813 0 \n", "L -0 0 \n", "L -0 184.455469 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 56.50625 146.899219 \n", "L 251.80625 146.899219 \n", "L 251.80625 10.999219 \n", "L 56.50625 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 56.50625 146.899219 \n", "L 56.50625 10.999219 \n", "\" clip-path=\"url(#p3fe7cf5c69)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m5e8567a70e\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5e8567a70e\" x=\"56.50625\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(48.554688 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 105.33125 146.899219 \n", "L 105.33125 10.999219 \n", "\" clip-path=\"url(#p3fe7cf5c69)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m5e8567a70e\" x=\"105.33125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(97.379688 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 154.15625 146.899219 \n", "L 154.15625 10.999219 \n", "\" clip-path=\"url(#p3fe7cf5c69)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m5e8567a70e\" x=\"154.15625\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(146.204688 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 202.98125 146.899219 \n", "L 202.98125 10.999219 \n", "\" clip-path=\"url(#p3fe7cf5c69)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m5e8567a70e\" x=\"202.98125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(195.029688 161.497656)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 251.80625 146.899219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#p3fe7cf5c69)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m5e8567a70e\" x=\"251.80625\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(243.854688 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(138.928125 175.175781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 56.50625 141.672296 \n", "L 251.80625 141.672296 \n", "\" clip-path=\"url(#p3fe7cf5c69)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"ma5c0bf3be5\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma5c0bf3be5\" x=\"56.50625\" y=\"141.672296\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.225 -->\n", "      <g transform=\"translate(20.878125 145.471514)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 56.50625 115.53768 \n", "L 251.80625 115.53768 \n", "\" clip-path=\"url(#p3fe7cf5c69)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#ma5c0bf3be5\" x=\"56.50625\" y=\"115.53768\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.250 -->\n", "      <g transform=\"translate(20.878125 119.336899)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 56.50625 89.403065 \n", "L 251.80625 89.403065 \n", "\" clip-path=\"url(#p3fe7cf5c69)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#ma5c0bf3be5\" x=\"56.50625\" y=\"89.403065\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.275 -->\n", "      <g transform=\"translate(20.878125 93.202284)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 56.50625 63.26845 \n", "L 251.80625 63.26845 \n", "\" clip-path=\"url(#p3fe7cf5c69)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#ma5c0bf3be5\" x=\"56.50625\" y=\"63.26845\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.300 -->\n", "      <g transform=\"translate(20.878125 67.067668)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 56.50625 37.133834 \n", "L 251.80625 37.133834 \n", "\" clip-path=\"url(#p3fe7cf5c69)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#ma5c0bf3be5\" x=\"56.50625\" y=\"37.133834\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.325 -->\n", "      <g transform=\"translate(20.878125 40.933053)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#p3fe7cf5c69)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#ma5c0bf3be5\" x=\"56.50625\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.350 -->\n", "      <g transform=\"translate(20.878125 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798438 88.607031)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 69.52625 1.502265 \n", "L 82.54625 68.046359 \n", "L 95.56625 95.010827 \n", "L 108.58625 110.991615 \n", "L 121.60625 117.76759 \n", "L 134.62625 119.335692 \n", "L 147.64625 121.835428 \n", "L 160.66625 118.016816 \n", "L 173.68625 122.549049 \n", "L 186.70625 119.993293 \n", "L 199.72625 121.139046 \n", "L 212.74625 121.520967 \n", "L 225.76625 122.913418 \n", "L 238.78625 122.998598 \n", "L 251.80625 119.139435 \n", "\" clip-path=\"url(#p3fe7cf5c69)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 56.50625 146.899219 \n", "L 56.50625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 251.80625 146.899219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 56.50625 146.899219 \n", "L 251.80625 146.899219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p3fe7cf5c69\">\n", "   <rect x=\"56.50625\" y=\"10.999219\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["data_iter, feature_dim = d2l.get_data_ch11(batch_size=10)\n", "d2l.train_ch11(rmsprop, init_rmsprop_states(feature_dim),\n", "               {'lr': 0.01, 'gamma': 0.9}, data_iter, feature_dim);"]}, {"cell_type": "markdown", "id": "30b0a9cd", "metadata": {"origin_pos": 18}, "source": ["## 简洁实现\n", "\n", "我们可直接使用深度学习框架中提供的RMSProp算法来训练模型。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "32e89d5e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:37.540843Z", "iopub.status.busy": "2023-08-18T07:05:37.540147Z", "iopub.status.idle": "2023-08-18T07:05:44.802579Z", "shell.execute_reply": "2023-08-18T07:05:44.801508Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss: 0.244, 0.017 sec/epoch\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"262.1875pt\" height=\"184.455469pt\" viewBox=\"0 0 262.1875 184.455469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:05:44.767183</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 184.455469 \n", "L 262.1875 184.455469 \n", "L 262.1875 0 \n", "L -0 0 \n", "L -0 184.455469 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 56.50625 146.899219 \n", "L 251.80625 146.899219 \n", "L 251.80625 10.999219 \n", "L 56.50625 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 56.50625 146.899219 \n", "L 56.50625 10.999219 \n", "\" clip-path=\"url(#pbd43bff672)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m3baa5d27dd\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3baa5d27dd\" x=\"56.50625\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(53.325 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 105.33125 146.899219 \n", "L 105.33125 10.999219 \n", "\" clip-path=\"url(#pbd43bff672)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m3baa5d27dd\" x=\"105.33125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(102.15 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 154.15625 146.899219 \n", "L 154.15625 10.999219 \n", "\" clip-path=\"url(#pbd43bff672)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m3baa5d27dd\" x=\"154.15625\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(150.975 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 202.98125 146.899219 \n", "L 202.98125 10.999219 \n", "\" clip-path=\"url(#pbd43bff672)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m3baa5d27dd\" x=\"202.98125\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(199.8 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 251.80625 146.899219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#pbd43bff672)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m3baa5d27dd\" x=\"251.80625\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(248.625 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(138.928125 175.175781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 56.50625 141.672296 \n", "L 251.80625 141.672296 \n", "\" clip-path=\"url(#pbd43bff672)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m5f1a83f0f6\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5f1a83f0f6\" x=\"56.50625\" y=\"141.672296\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.225 -->\n", "      <g transform=\"translate(20.878125 145.471514)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 56.50625 115.53768 \n", "L 251.80625 115.53768 \n", "\" clip-path=\"url(#pbd43bff672)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m5f1a83f0f6\" x=\"56.50625\" y=\"115.53768\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.250 -->\n", "      <g transform=\"translate(20.878125 119.336899)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 56.50625 89.403065 \n", "L 251.80625 89.403065 \n", "\" clip-path=\"url(#pbd43bff672)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m5f1a83f0f6\" x=\"56.50625\" y=\"89.403065\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.275 -->\n", "      <g transform=\"translate(20.878125 93.202284)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 56.50625 63.26845 \n", "L 251.80625 63.26845 \n", "\" clip-path=\"url(#pbd43bff672)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m5f1a83f0f6\" x=\"56.50625\" y=\"63.26845\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.300 -->\n", "      <g transform=\"translate(20.878125 67.067668)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 56.50625 37.133834 \n", "L 251.80625 37.133834 \n", "\" clip-path=\"url(#pbd43bff672)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m5f1a83f0f6\" x=\"56.50625\" y=\"37.133834\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.325 -->\n", "      <g transform=\"translate(20.878125 40.933053)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#pbd43bff672)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m5f1a83f0f6\" x=\"56.50625\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.350 -->\n", "      <g transform=\"translate(20.878125 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798438 88.607031)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 64.105856 -1 \n", "L 69.52625 59.693572 \n", "L 76.03625 98.844089 \n", "L 82.54625 116.040834 \n", "L 89.05625 116.870267 \n", "L 95.56625 116.99829 \n", "L 102.07625 122.48131 \n", "L 108.58625 120.26986 \n", "L 115.09625 117.786783 \n", "L 121.60625 122.894727 \n", "L 128.11625 123.254056 \n", "L 134.62625 122.259205 \n", "L 141.13625 122.759589 \n", "L 147.64625 123.689618 \n", "L 154.15625 122.798397 \n", "L 160.66625 123.953 \n", "L 167.17625 123.390055 \n", "L 173.68625 121.150122 \n", "L 180.19625 120.76247 \n", "L 186.70625 118.921515 \n", "L 193.21625 121.275608 \n", "L 199.72625 121.113154 \n", "L 206.23625 118.109068 \n", "L 212.74625 117.8355 \n", "L 219.25625 122.928007 \n", "L 225.76625 121.045105 \n", "L 232.27625 120.163757 \n", "L 238.78625 122.639458 \n", "L 245.29625 122.336753 \n", "L 251.80625 122.320817 \n", "\" clip-path=\"url(#pbd43bff672)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 56.50625 146.899219 \n", "L 56.50625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 251.80625 146.899219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 56.50625 146.899219 \n", "L 251.80625 146.899219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pbd43bff672\">\n", "   <rect x=\"56.50625\" y=\"10.999219\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["trainer = torch.optim.RMSprop\n", "d2l.train_concise_ch11(trainer, {'lr': 0.01, 'alpha': 0.9},\n", "                       data_iter)"]}, {"cell_type": "markdown", "id": "c00792e2", "metadata": {"origin_pos": 23}, "source": ["## 小结\n", "\n", "* RMSProp算法与Adagrad算法非常相似，因为两者都使用梯度的平方来缩放系数。\n", "* RMSProp算法与动量法都使用泄漏平均值。但是，RMSProp算法使用该技术来调整按系数顺序的预处理器。\n", "* 在实验中，学习率需要由实验者调度。\n", "* 系数$\\gamma$决定了在调整每坐标比例时历史记录的时长。\n", "\n", "## 练习\n", "\n", "1. 如果我们设置$\\gamma = 1$，实验会发生什么？为什么？\n", "1. 旋转优化问题以最小化$f(\\mathbf{x}) = 0.1 (x_1 + x_2)^2 + 2 (x_1 - x_2)^2$。收敛会发生什么？\n", "1. 试试在真正的机器学习问题上应用RMSProp算法会发生什么，例如在Fashion-MNIST上的训练。试验不同的取值来调整学习率。\n", "1. 随着优化的进展，需要调整$\\gamma$吗？RMSProp算法对此有多敏感？\n"]}, {"cell_type": "markdown", "id": "04ff42ae", "metadata": {"origin_pos": 25, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/4322)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
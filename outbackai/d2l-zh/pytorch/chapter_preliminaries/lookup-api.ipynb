{"cells": [{"cell_type": "markdown", "id": "01132d59", "metadata": {"origin_pos": 0}, "source": ["# 查阅文档\n"]}, {"cell_type": "markdown", "id": "b7f72d17", "metadata": {"origin_pos": 2, "tab": ["pytorch"]}, "source": ["由于篇幅限制，本书不可能介绍每一个PyTorch函数和类。\n", "API文档、其他教程和示例提供了本书之外的大量文档。\n", "本节提供了一些查看PyTorch API的指导。\n"]}, {"cell_type": "markdown", "id": "97173144", "metadata": {"origin_pos": 4}, "source": ["## 查找模块中的所有函数和类\n", "\n", "为了知道模块中可以调用哪些函数和类，可以调用`dir`函数。\n", "例如，我们可以(**查询随机数生成模块中的所有属性：**)\n"]}, {"cell_type": "code", "execution_count": 1, "id": "8f7f4d63", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:30.519062Z", "iopub.status.busy": "2023-08-18T07:05:30.518501Z", "iopub.status.idle": "2023-08-18T07:05:31.469749Z", "shell.execute_reply": "2023-08-18T07:05:31.468858Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['AbsTransform', 'AffineTransform', '<PERSON><PERSON><PERSON>', 'Beta', 'Binomial', 'CatTransform', 'Categorical', 'Cauchy', 'Chi2', 'ComposeTransform', 'Continuous<PERSON>ernoulli', 'CorrCholeskyTransform', 'CumulativeDistributionTransform', 'Dirichlet', 'Distribution', 'ExpTransform', 'Exponential', 'ExponentialFamily', 'FisherSnedecor', 'Gamma', 'Geometric', 'Gumbel', 'HalfCauchy', 'HalfNormal', 'Independent', 'IndependentTransform', 'Kumar<PERSON>wamy', 'LKJCholesky', 'Laplace', 'LogNormal', 'LogisticNormal', 'LowRankMultivariateNormal', 'LowerCholeskyTransform', 'MixtureSameFamily', 'Multinomial', 'MultivariateNormal', 'NegativeBinomial', 'Normal', 'OneHotCategorical', 'OneHotCategoricalStraightThrough', 'Pareto', 'Poisson', 'PowerTransform', 'RelaxedBernoulli', 'RelaxedOneHotCategorical', 'ReshapeTransform', 'SigmoidTransform', 'SoftmaxTransform', 'SoftplusTransform', 'StackTransform', 'StickBreakingTransform', 'StudentT', 'TanhTransform', 'Transform', 'TransformedDistribution', 'Uniform', 'VonMises', 'Weibull', 'Wishart', '__all__', '__builtins__', '__cached__', '__doc__', '__file__', '__loader__', '__name__', '__package__', '__path__', '__spec__', 'bernoulli', 'beta', 'biject_to', 'binomial', 'categorical', 'cauchy', 'chi2', 'constraint_registry', 'constraints', 'continuous_bernoulli', 'dirichlet', 'distribution', 'exp_family', 'exponential', 'fishersnedecor', 'gamma', 'geometric', 'gumbel', 'half_cauchy', 'half_normal', 'identity_transform', 'independent', 'kl', 'kl_divergence', 'kumaraswamy', 'laplace', 'lkj_cholesky', 'log_normal', 'logistic_normal', 'lowrank_multivariate_normal', 'mixture_same_family', 'multinomial', 'multivariate_normal', 'negative_binomial', 'normal', 'one_hot_categorical', 'pareto', 'poisson', 'register_kl', 'relaxed_bernoulli', 'relaxed_categorical', 'studentT', 'transform_to', 'transformed_distribution', 'transforms', 'uniform', 'utils', 'von_mises', 'weibull', 'wishart']\n"]}], "source": ["import torch\n", "\n", "print(dir(torch.distributions))"]}, {"cell_type": "markdown", "id": "a6e589e9", "metadata": {"origin_pos": 9}, "source": ["通常可以忽略以“`__`”（双下划线）开始和结束的函数，它们是Python中的特殊对象，\n", "或以单个“`_`”（单下划线）开始的函数，它们通常是内部函数。\n", "根据剩余的函数名或属性名，我们可能会猜测这个模块提供了各种生成随机数的方法，\n", "包括从均匀分布（`uniform`）、正态分布（`normal`）和多项分布（`multinomial`）中采样。\n", "\n", "## 查找特定函数和类的用法\n", "\n", "有关如何使用给定函数或类的更具体说明，可以调用`help`函数。\n", "例如，我们来[**查看张量`ones`函数的用法。**]\n"]}, {"cell_type": "code", "execution_count": 2, "id": "a16494ed", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:31.473606Z", "iopub.status.busy": "2023-08-18T07:05:31.472946Z", "iopub.status.idle": "2023-08-18T07:05:31.477780Z", "shell.execute_reply": "2023-08-18T07:05:31.476938Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on built-in function ones in module torch:\n", "\n", "ones(...)\n", "    ones(*size, *, out=None, dtype=None, layout=torch.strided, device=None, requires_grad=False) -> Tensor\n", "    \n", "    Returns a tensor filled with the scalar value `1`, with the shape defined\n", "    by the variable argument :attr:`size`.\n", "    \n", "    Args:\n", "        size (int...): a sequence of integers defining the shape of the output tensor.\n", "            Can be a variable number of arguments or a collection like a list or tuple.\n", "    \n", "    Keyword arguments:\n", "        out (Tensor, optional): the output tensor.\n", "        dtype (:class:`torch.dtype`, optional): the desired data type of returned tensor.\n", "            Default: if ``None``, uses a global default (see :func:`torch.set_default_tensor_type`).\n", "        layout (:class:`torch.layout`, optional): the desired layout of returned Tensor.\n", "            Default: ``torch.strided``.\n", "        device (:class:`torch.device`, optional): the desired device of returned tensor.\n", "            Default: if ``None``, uses the current device for the default tensor type\n", "            (see :func:`torch.set_default_tensor_type`). :attr:`device` will be the CPU\n", "            for CPU tensor types and the current CUDA device for CUDA tensor types.\n", "        requires_grad (bool, optional): If autograd should record operations on the\n", "            returned tensor. Default: ``False``.\n", "    \n", "    Example::\n", "    \n", "        >>> torch.ones(2, 3)\n", "        tensor([[ 1.,  1.,  1.],\n", "                [ 1.,  1.,  1.]])\n", "    \n", "        >>> torch.ones(5)\n", "        tensor([ 1.,  1.,  1.,  1.,  1.])\n", "\n"]}], "source": ["help(torch.ones)"]}, {"cell_type": "markdown", "id": "903c096e", "metadata": {"origin_pos": 14}, "source": ["从文档中，我们可以看到`ones`函数创建一个具有指定形状的新张量，并将所有元素值设置为1。\n", "下面来[**运行一个快速测试**]来确认这一解释：\n"]}, {"cell_type": "code", "execution_count": 3, "id": "7870b2f5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:05:31.481310Z", "iopub.status.busy": "2023-08-18T07:05:31.480685Z", "iopub.status.idle": "2023-08-18T07:05:31.490398Z", "shell.execute_reply": "2023-08-18T07:05:31.489581Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([1., 1., 1., 1.])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.ones(4)"]}, {"cell_type": "markdown", "id": "dd4f531d", "metadata": {"origin_pos": 19}, "source": ["在Jupyter记事本中，我们可以使用`?`指令在另一个浏览器窗口中显示文档。\n", "例如，`list?`指令将创建与`help(list)`指令几乎相同的内容，并在新的浏览器窗口中显示它。\n", "此外，如果我们使用两个问号，如`list??`，将显示实现该函数的Python代码。\n", "\n", "## 小结\n", "\n", "* 官方文档提供了本书之外的大量描述和示例。\n", "* 可以通过调用`dir`和`help`函数或在Jupyter记事本中使用`?`和`??`查看API的用法文档。\n", "\n", "## 练习\n", "\n", "1. 在深度学习框架中查找任何函数或类的文档。请尝试在这个框架的官方网站上找到文档。\n"]}, {"cell_type": "markdown", "id": "197b3dc7", "metadata": {"origin_pos": 21, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1765)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
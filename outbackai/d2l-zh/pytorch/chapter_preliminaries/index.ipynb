{"cells": [{"cell_type": "markdown", "id": "fc08a2aa", "metadata": {"origin_pos": 0}, "source": ["#  预备知识\n", ":label:`chap_preliminaries`\n", "\n", "要学习深度学习，首先需要先掌握一些基本技能。\n", "所有机器学习方法都涉及从数据中提取信息。\n", "因此，我们先学习一些关于数据的实用技能，包括存储、操作和预处理数据。\n", "\n", "机器学习通常需要处理大型数据集。\n", "我们可以将某些数据集视为一个表，其中表的行对应样本，列对应属性。\n", "线性代数为人们提供了一些用来处理表格数据的方法。\n", "我们不会太深究细节，而是将重点放在矩阵运算的基本原理及其实现上。\n", "\n", "深度学习是关于优化的学习。\n", "对于一个带有参数的模型，我们想要找到其中能拟合数据的最好模型。\n", "在算法的每个步骤中，决定以何种方式调整参数需要一点微积分知识。\n", "本章将简要介绍这些知识。\n", "幸运的是，`autograd`包会自动计算微分，本章也将介绍它。\n", "\n", "机器学习还涉及如何做出预测：给定观察到的信息，某些未知属性可能的值是多少？\n", "要在不确定的情况下进行严格的推断，我们需要借用概率语言。\n", "\n", "最后，官方文档提供了本书之外的大量描述和示例。\n", "在本章的结尾，我们将展示如何在官方文档中查找所需信息。\n", "\n", "本书对读者数学基础无过分要求，只要可以正确理解深度学习所需的数学知识即可。\n", "但这并不意味着本书中不涉及数学方面的内容，本章会快速介绍一些基本且常用的数学知识，\n", "以便读者能够理解书中的大部分数学内容。\n", "如果读者想要深入理解全部数学内容，可以进一步学习本书数学附录中给出的数学基础知识。\n", "\n", ":begin_tab:toc\n", " - [ndarray](ndarray.ipynb)\n", " - [pandas](pandas.ipynb)\n", " - [linear-algebra](linear-algebra.ipynb)\n", " - [calculus](calculus.ipynb)\n", " - [autograd](autograd.ipynb)\n", " - [probability](probability.ipynb)\n", " - [lookup-api](lookup-api.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
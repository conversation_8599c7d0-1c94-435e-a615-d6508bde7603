{"cells": [{"cell_type": "markdown", "id": "53d03399", "metadata": {"origin_pos": 0}, "source": ["# 概率\n", ":label:`sec_prob`\n", "\n", "简单地说，机器学习就是做出预测。\n", "\n", "根据病人的临床病史，我们可能想预测他们在下一年心脏病发作的*概率*。\n", "在飞机喷气发动机的异常检测中，我们想要评估一组发动机读数为正常运行情况的概率有多大。\n", "在强化学习中，我们希望智能体（agent）能在一个环境中智能地行动。\n", "这意味着我们需要考虑在每种可行的行为下获得高奖励的概率。\n", "当我们建立推荐系统时，我们也需要考虑概率。\n", "例如，假设我们为一家大型在线书店工作，我们可能希望估计某些用户购买特定图书的概率。\n", "为此，我们需要使用概率学。\n", "有完整的课程、专业、论文、职业、甚至院系，都致力于概率学的工作。\n", "所以很自然地，我们在这部分的目标不是教授整个科目。\n", "相反，我们希望教给读者基础的概率知识，使读者能够开始构建第一个深度学习模型，\n", "以便读者可以开始自己探索它。\n", "\n", "现在让我们更认真地考虑第一个例子：根据照片区分猫和狗。\n", "这听起来可能很简单，但对于机器却可能是一个艰巨的挑战。\n", "首先，问题的难度可能取决于图像的分辨率。\n", "\n", "![不同分辨率的图像 ($10 \\times 10$, $20 \\times 20$, $40 \\times 40$, $80 \\times 80$, 和 $160 \\times 160$ pixels)](../img/cat-dog-pixels.png)\n", ":width:`300px`\n", ":label:`fig_cat_dog`\n", "\n", "如 :numref:`fig_cat_dog`所示，虽然人类很容易以$160 \\times 160$像素的分辨率识别猫和狗，\n", "但它在$40\\times40$像素上变得具有挑战性，而且在$10 \\times 10$像素下几乎是不可能的。\n", "换句话说，我们在很远的距离（从而降低分辨率）区分猫和狗的能力可能会变为猜测。\n", "概率给了我们一种正式的途径来说明我们的确定性水平。\n", "如果我们完全肯定图像是一只猫，我们说标签$y$是\"猫\"的*概率*，表示为$P(y=$\"猫\"$)$等于$1$。\n", "如果我们没有证据表明$y=$“猫”或$y=$“狗”，那么我们可以说这两种可能性是相等的，\n", "即$P(y=$\"猫\"$)=P(y=$\"狗\"$)=0.5$。\n", "如果我们不十分确定图像描绘的是一只猫，我们可以将概率赋值为$0.5<P(y=$\"猫\"$)<1$。\n", "\n", "现在考虑第二个例子：给出一些天气监测数据，我们想预测明天北京下雨的概率。\n", "如果是夏天，下雨的概率是0.5。\n", "\n", "在这两种情况下，我们都不确定结果，但这两种情况之间有一个关键区别。\n", "在第一种情况中，图像实际上是狗或猫二选一。\n", "在第二种情况下，结果实际上是一个随机的事件。\n", "因此，概率是一种灵活的语言，用于说明我们的确定程度，并且它可以有效地应用于广泛的领域中。\n", "\n", "## 基本概率论\n", "\n", "假设我们掷骰子，想知道看到1的几率有多大，而不是看到另一个数字。\n", "如果骰子是公平的，那么所有六个结果$\\{1, \\ldots, 6\\}$都有相同的可能发生，\n", "因此我们可以说$1$发生的概率为$\\frac{1}{6}$。\n", "\n", "然而现实生活中，对于我们从工厂收到的真实骰子，我们需要检查它是否有瑕疵。\n", "检查骰子的唯一方法是多次投掷并记录结果。\n", "对于每个骰子，我们将观察到$\\{1, \\ldots, 6\\}$中的一个值。\n", "对于每个值，一种自然的方法是将它出现的次数除以投掷的总次数，\n", "即此*事件*（event）概率的*估计值*。\n", "*大数定律*（law of large numbers）告诉我们：\n", "随着投掷次数的增加，这个估计值会越来越接近真实的潜在概率。\n", "让我们用代码试一试！\n", "\n", "首先，我们导入必要的软件包。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "fffe28f9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:03:52.138045Z", "iopub.status.busy": "2023-08-18T07:03:52.137333Z", "iopub.status.idle": "2023-08-18T07:03:55.099774Z", "shell.execute_reply": "2023-08-18T07:03:55.098743Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "from torch.distributions import multinomial\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "e8bfc741", "metadata": {"origin_pos": 5}, "source": ["在统计学中，我们把从概率分布中抽取样本的过程称为*抽样*（sampling）。\n", "笼统来说，可以把*分布*（distribution）看作对事件的概率分配，\n", "稍后我们将给出的更正式定义。\n", "将概率分配给一些离散选择的分布称为*多项分布*（multinomial distribution）。\n", "\n", "为了抽取一个样本，即掷骰子，我们只需传入一个概率向量。\n", "输出是另一个相同长度的向量：它在索引$i$处的值是采样结果中$i$出现的次数。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "5e03f32a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:03:55.105250Z", "iopub.status.busy": "2023-08-18T07:03:55.104342Z", "iopub.status.idle": "2023-08-18T07:03:55.117395Z", "shell.execute_reply": "2023-08-18T07:03:55.116406Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0., 0., 1., 0., 0., 0.])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["fair_probs = torch.ones([6]) / 6\n", "multinomial.Multinomial(1, fair_probs).sample()"]}, {"cell_type": "markdown", "id": "076723aa", "metadata": {"origin_pos": 10}, "source": ["在估计一个骰子的公平性时，我们希望从同一分布中生成多个样本。\n", "如果用Python的for循环来完成这个任务，速度会慢得惊人。\n", "因此我们使用深度学习框架的函数同时抽取多个样本，得到我们想要的任意形状的独立样本数组。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "aef6c2b1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:03:55.121792Z", "iopub.status.busy": "2023-08-18T07:03:55.121330Z", "iopub.status.idle": "2023-08-18T07:03:55.128618Z", "shell.execute_reply": "2023-08-18T07:03:55.127639Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([5., 3., 2., 0., 0., 0.])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["multinomial.Multinomial(10, fair_probs).sample()"]}, {"cell_type": "markdown", "id": "cb561790", "metadata": {"origin_pos": 15}, "source": ["现在我们知道如何对骰子进行采样，我们可以模拟1000次投掷。\n", "然后，我们可以统计1000次投掷后，每个数字被投中了多少次。\n", "具体来说，我们计算相对频率，以作为真实概率的估计。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "22aadbd3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:03:55.132265Z", "iopub.status.busy": "2023-08-18T07:03:55.131761Z", "iopub.status.idle": "2023-08-18T07:03:55.139359Z", "shell.execute_reply": "2023-08-18T07:03:55.138380Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0.1550, 0.1820, 0.1770, 0.1710, 0.1600, 0.1550])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将结果存储为32位浮点数以进行除法\n", "counts = multinomial.Multinomial(1000, fair_probs).sample()\n", "counts / 1000  # 相对频率作为估计值"]}, {"cell_type": "markdown", "id": "af0608f2", "metadata": {"origin_pos": 20}, "source": ["因为我们是从一个公平的骰子中生成的数据，我们知道每个结果都有真实的概率$\\frac{1}{6}$，\n", "大约是$0.167$，所以上面输出的估计值看起来不错。\n", "\n", "我们也可以看到这些概率如何随着时间的推移收敛到真实概率。\n", "让我们进行500组实验，每组抽取10个样本。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ebe14d68", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:03:55.143254Z", "iopub.status.busy": "2023-08-18T07:03:55.142607Z", "iopub.status.idle": "2023-08-18T07:03:55.387633Z", "shell.execute_reply": "2023-08-18T07:03:55.386483Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"398.50625pt\" height=\"293.175449pt\" viewBox=\"0 0 398.50625 293.175449\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:03:55.318521</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 293.175449 \n", "L 398.50625 293.175449 \n", "L 398.50625 0 \n", "L 0 0 \n", "L 0 293.175449 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 56.50625 255.619199 \n", "L 391.30625 255.619199 \n", "L 391.30625 10.999199 \n", "L 56.50625 10.999199 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m7e0cf4c392\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7e0cf4c392\" x=\"71.724432\" y=\"255.619199\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(68.543182 270.217637)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m7e0cf4c392\" x=\"132.719149\" y=\"255.619199\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(123.175399 270.217637)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m7e0cf4c392\" x=\"193.713865\" y=\"255.619199\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(184.170115 270.217637)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m7e0cf4c392\" x=\"254.708582\" y=\"255.619199\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(245.164832 270.217637)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m7e0cf4c392\" x=\"315.703299\" y=\"255.619199\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(306.159549 270.217637)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m7e0cf4c392\" x=\"376.698015\" y=\"255.619199\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(367.154265 270.217637)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- Groups of experiments -->\n", "     <g transform=\"translate(166.760156 283.895762)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-47\" d=\"M 3809 666 \n", "L 3809 1919 \n", "L 2778 1919 \n", "L 2778 2438 \n", "L 4434 2438 \n", "L 4434 434 \n", "Q 4069 175 3628 42 \n", "Q 3188 -91 2688 -91 \n", "Q 1594 -91 976 548 \n", "Q 359 1188 359 2328 \n", "Q 359 3472 976 4111 \n", "Q 1594 4750 2688 4750 \n", "Q 3144 4750 3555 4637 \n", "Q 3966 4525 4313 4306 \n", "L 4313 3634 \n", "Q 3963 3931 3569 4081 \n", "Q 3175 4231 2741 4231 \n", "Q 1884 4231 1454 3753 \n", "Q 1025 3275 1025 2328 \n", "Q 1025 1384 1454 906 \n", "Q 1884 428 2741 428 \n", "Q 3075 428 3337 486 \n", "Q 3600 544 3809 666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-47\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"77.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"116.353516\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"177.535156\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"240.914062\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"304.390625\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"356.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"388.277344\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"449.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"484.664062\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"516.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"576.224609\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"635.404297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"698.880859\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"760.404297\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"801.517578\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"829.300781\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"926.712891\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"988.236328\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1051.615234\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1090.824219\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m3eccd66cb1\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3eccd66cb1\" x=\"56.50625\" y=\"244.50011\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.100 -->\n", "      <g transform=\"translate(20.878125 248.299329)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m3eccd66cb1\" x=\"56.50625\" y=\"211.14284\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.125 -->\n", "      <g transform=\"translate(20.878125 214.942059)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m3eccd66cb1\" x=\"56.50625\" y=\"177.78557\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.150 -->\n", "      <g transform=\"translate(20.878125 181.584789)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m3eccd66cb1\" x=\"56.50625\" y=\"144.4283\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.175 -->\n", "      <g transform=\"translate(20.878125 148.227518)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m3eccd66cb1\" x=\"56.50625\" y=\"111.071029\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.200 -->\n", "      <g transform=\"translate(20.878125 114.870248)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m3eccd66cb1\" x=\"56.50625\" y=\"77.713759\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.225 -->\n", "      <g transform=\"translate(20.878125 81.512978)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m3eccd66cb1\" x=\"56.50625\" y=\"44.356489\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0.250 -->\n", "      <g transform=\"translate(20.878125 48.155708)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m3eccd66cb1\" x=\"56.50625\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0.275 -->\n", "      <g transform=\"translate(20.878125 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Estimated probability -->\n", "     <g transform=\"translate(14.798438 186.832637)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-45\" d=\"M 628 4666 \n", "L 3578 4666 \n", "L 3578 4134 \n", "L 1259 4134 \n", "L 1259 2753 \n", "L 3481 2753 \n", "L 3481 2222 \n", "L 1259 2222 \n", "L 1259 531 \n", "L 3634 531 \n", "L 3634 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-45\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"63.183594\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"115.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"154.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"182.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"279.6875\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"340.966797\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"380.175781\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"441.699219\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"505.175781\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"536.962891\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"600.439453\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"639.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"700.484375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"763.960938\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"825.240234\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"888.716797\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"916.5\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"944.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"972.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"1011.275391\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 71.724432 111.071025 \n", "L 72.334379 111.071025 \n", "L 72.944326 155.547383 \n", "L 73.554273 177.785562 \n", "L 74.16422 164.442667 \n", "L 74.774168 200.023741 \n", "L 75.384115 206.377506 \n", "L 75.994062 177.785562 \n", "L 76.604009 185.198288 \n", "L 77.213956 204.471393 \n", "L 77.823903 208.110364 \n", "L 78.433851 188.904661 \n", "L 79.653745 196.846858 \n", "L 80.263692 208.919024 \n", "L 80.873639 211.14284 \n", "L 81.483586 205.256254 \n", "L 82.093534 185.198288 \n", "L 82.703481 181.296868 \n", "L 83.923375 161.901149 \n", "L 84.533322 153.525732 \n", "L 85.143269 157.481153 \n", "L 85.753217 144.428304 \n", "L 86.363164 148.431164 \n", "L 86.973111 157.258012 \n", "L 88.193005 163.4896 \n", "L 88.802952 166.283069 \n", "L 89.4129 164.442667 \n", "L 90.022847 171.329322 \n", "L 91.242741 175.763911 \n", "L 91.852688 162.088024 \n", "L 93.072583 159.253756 \n", "L 93.68253 161.557718 \n", "L 94.902424 158.968641 \n", "L 95.512371 164.442667 \n", "L 96.122319 163.140921 \n", "L 96.732266 158.724266 \n", "L 97.342213 163.822055 \n", "L 97.95216 162.623181 \n", "L 98.562107 164.442667 \n", "L 99.782002 162.17153 \n", "L 101.611843 151.099751 \n", "L 102.22179 150.314871 \n", "L 102.831737 154.692079 \n", "L 103.441685 156.386563 \n", "L 104.661579 154.738723 \n", "L 105.271526 151.576295 \n", "L 105.881473 146.183947 \n", "L 106.49142 143.278042 \n", "L 107.101368 144.993682 \n", "L 107.711315 148.875935 \n", "L 109.541156 153.429461 \n", "L 110.151103 150.682796 \n", "L 110.761051 152.126125 \n", "L 111.370998 155.547383 \n", "L 111.980945 154.883567 \n", "L 112.590892 156.201457 \n", "L 113.810786 154.912019 \n", "L 114.420734 158.053093 \n", "L 115.030681 159.253756 \n", "L 116.250575 157.951513 \n", "L 116.860522 159.105493 \n", "L 117.470469 161.984754 \n", "L 118.080417 161.323543 \n", "L 118.690364 162.3899 \n", "L 119.910258 157.771199 \n", "L 120.520205 153.90012 \n", "L 121.130152 153.377807 \n", "L 121.7401 154.475678 \n", "L 122.350047 153.958952 \n", "L 123.569941 156.064566 \n", "L 124.179888 155.547383 \n", "L 126.00973 149.617214 \n", "L 126.619677 149.193618 \n", "L 127.229624 150.229555 \n", "L 127.839571 149.808503 \n", "L 128.449518 152.235319 \n", "L 129.669413 151.377728 \n", "L 130.27936 152.337754 \n", "L 130.889307 154.639708 \n", "L 131.499254 154.199629 \n", "L 132.109201 155.102632 \n", "L 133.939043 153.820152 \n", "L 134.54899 154.692079 \n", "L 135.768884 158.904103 \n", "L 136.378832 157.210056 \n", "L 136.988779 156.782841 \n", "L 137.598726 157.587584 \n", "L 140.648462 155.547383 \n", "L 141.258409 157.481153 \n", "L 141.868356 158.2313 \n", "L 142.478303 160.109061 \n", "L 143.08825 160.824254 \n", "L 143.698198 162.64865 \n", "L 145.528039 164.661394 \n", "L 146.137986 164.225709 \n", "L 147.357881 167.644959 \n", "L 147.967828 168.254914 \n", "L 148.577775 166.754025 \n", "L 149.797669 165.890728 \n", "L 150.407616 166.495414 \n", "L 151.627511 163.634006 \n", "L 152.237458 163.238782 \n", "L 154.067299 165.031327 \n", "L 154.677247 161.715645 \n", "L 155.287194 161.348654 \n", "L 155.897141 158.107173 \n", "L 156.507088 158.724266 \n", "L 157.117035 156.49369 \n", "L 157.726982 156.173821 \n", "L 158.33693 156.79149 \n", "L 158.946877 158.327153 \n", "L 160.166771 159.507616 \n", "L 161.386665 162.45925 \n", "L 162.60656 159.995035 \n", "L 163.216507 156.136481 \n", "L 163.826454 155.839994 \n", "L 164.436401 157.291554 \n", "L 165.046348 157.857847 \n", "L 165.656296 159.277675 \n", "L 166.266243 158.968641 \n", "L 166.87619 156.963831 \n", "L 167.486137 156.673368 \n", "L 168.096084 154.708223 \n", "L 168.706031 151.933682 \n", "L 169.315979 151.679882 \n", "L 169.925926 152.252836 \n", "L 170.535873 153.637353 \n", "L 171.14582 154.191398 \n", "L 171.755767 155.547383 \n", "L 172.975662 156.612687 \n", "L 173.585609 156.341609 \n", "L 174.195556 156.863266 \n", "L 174.805503 155.809017 \n", "L 175.41545 155.547383 \n", "L 176.025397 156.840301 \n", "L 177.245292 154.780556 \n", "L 178.465186 155.80009 \n", "L 179.68508 158.295938 \n", "L 180.295028 158.032097 \n", "L 180.904975 157.02994 \n", "L 181.514922 158.250367 \n", "L 183.344763 159.656615 \n", "L 183.954711 160.836462 \n", "L 184.564658 159.851563 \n", "L 185.174605 161.01775 \n", "L 185.784552 160.042335 \n", "L 186.394499 161.195181 \n", "L 187.004446 161.63363 \n", "L 187.614394 162.766056 \n", "L 188.834288 162.230363 \n", "L 189.444235 162.654436 \n", "L 190.054182 162.3899 \n", "L 190.664129 163.4896 \n", "L 191.274077 163.223513 \n", "L 191.884024 163.634006 \n", "L 193.103918 163.108373 \n", "L 193.713865 163.5133 \n", "L 196.153654 162.490048 \n", "L 196.763601 163.535866 \n", "L 197.373548 163.926995 \n", "L 197.983495 163.031388 \n", "L 199.20339 162.536533 \n", "L 199.813337 163.557359 \n", "L 200.423284 163.309783 \n", "L 201.033231 163.69095 \n", "L 201.643178 162.821568 \n", "L 202.253126 162.580851 \n", "L 202.863073 163.577838 \n", "L 203.47302 161.491231 \n", "L 204.692914 159.812255 \n", "L 205.302861 160.803695 \n", "L 207.74265 159.915604 \n", "L 208.352597 160.291542 \n", "L 208.962544 158.892949 \n", "L 209.572492 156.918917 \n", "L 210.182439 156.717825 \n", "L 210.792386 157.10114 \n", "L 211.402333 158.061265 \n", "L 212.01228 158.435473 \n", "L 212.622227 158.2313 \n", "L 213.232175 159.174226 \n", "L 213.842122 159.538851 \n", "L 214.452069 159.33261 \n", "L 215.062016 159.693497 \n", "L 215.671963 158.925337 \n", "L 216.28191 159.845519 \n", "L 217.501805 159.439081 \n", "L 218.721699 157.936621 \n", "L 219.331646 158.84193 \n", "L 219.941593 159.192995 \n", "L 220.551541 158.451975 \n", "L 221.161488 158.801748 \n", "L 221.771435 158.068323 \n", "L 222.381382 158.954843 \n", "L 222.991329 158.76254 \n", "L 223.601276 159.639218 \n", "L 224.211224 159.445722 \n", "L 224.821171 160.312717 \n", "L 225.431118 160.118088 \n", "L 226.041065 160.450304 \n", "L 227.260959 159.022105 \n", "L 227.870907 159.3547 \n", "L 228.480854 158.650383 \n", "L 229.090801 158.466688 \n", "L 229.700748 159.310779 \n", "L 230.310695 158.614733 \n", "L 230.920642 158.433266 \n", "L 231.53059 158.760512 \n", "L 232.140537 157.569034 \n", "L 232.750484 157.393571 \n", "L 233.970378 158.046055 \n", "L 234.580325 158.866525 \n", "L 235.190273 157.696799 \n", "L 238.240008 156.845968 \n", "L 238.849956 157.649916 \n", "L 239.459903 157.481153 \n", "L 240.679797 158.107173 \n", "L 241.289744 156.982103 \n", "L 241.899691 157.771199 \n", "L 242.509639 158.079855 \n", "L 243.729533 157.747638 \n", "L 244.33948 156.643644 \n", "L 244.949427 156.483729 \n", "L 245.559374 157.258012 \n", "L 246.779269 157.863871 \n", "L 247.389216 158.625331 \n", "L 248.60911 159.215542 \n", "L 249.829005 160.708478 \n", "L 251.048899 161.27654 \n", "L 251.658846 160.656167 \n", "L 252.87874 160.323374 \n", "L 254.098635 160.884557 \n", "L 254.708582 160.719055 \n", "L 255.318529 161.438304 \n", "L 255.928476 161.712424 \n", "L 256.538423 162.42366 \n", "L 257.148371 162.69277 \n", "L 258.368265 161.487235 \n", "L 258.978212 160.457124 \n", "L 260.198106 160.999339 \n", "L 260.808054 160.838788 \n", "L 261.418001 161.106943 \n", "L 262.027948 161.799648 \n", "L 262.637895 162.063032 \n", "L 263.247842 162.748321 \n", "L 264.467737 163.264113 \n", "L 265.077684 163.099983 \n", "L 265.687631 163.773422 \n", "L 266.297578 163.608736 \n", "L 266.907525 163.860726 \n", "L 267.517472 163.282404 \n", "L 268.12742 163.533858 \n", "L 269.347314 164.85322 \n", "L 269.957261 165.097536 \n", "L 270.567208 165.748389 \n", "L 271.177155 165.174899 \n", "L 273.616944 164.523032 \n", "L 274.226891 163.961829 \n", "L 274.836838 164.602462 \n", "L 276.66668 165.313718 \n", "L 277.276627 164.363713 \n", "L 278.496521 164.835107 \n", "L 279.106469 164.286152 \n", "L 279.716416 164.520685 \n", "L 280.326363 165.142868 \n", "L 280.93631 165.373564 \n", "L 281.546257 164.829421 \n", "L 282.156204 164.674039 \n", "L 282.766152 164.135045 \n", "L 283.376099 164.36598 \n", "L 283.986046 164.213263 \n", "L 284.595993 164.442667 \n", "L 285.20594 163.530319 \n", "L 287.035782 163.085747 \n", "L 287.645729 162.563374 \n", "L 288.255676 163.168339 \n", "L 288.865623 163.396152 \n", "L 290.695465 162.960109 \n", "L 291.305412 163.185994 \n", "L 292.525306 162.898852 \n", "L 293.745201 163.345989 \n", "L 294.355148 162.474042 \n", "L 294.965095 161.970399 \n", "L 295.575042 161.832097 \n", "L 296.184989 161.332927 \n", "L 296.794936 161.557718 \n", "L 298.014831 161.286283 \n", "L 298.624778 160.078502 \n", "L 299.234725 160.660979 \n", "L 299.844672 159.817126 \n", "L 300.454619 160.397198 \n", "L 301.064567 159.912443 \n", "L 301.674514 160.489214 \n", "L 302.284461 160.358825 \n", "L 303.504355 160.800494 \n", "L 304.114302 160.670323 \n", "L 304.72425 161.237571 \n", "L 305.334197 161.454409 \n", "L 307.164038 161.063838 \n", "L 307.773985 160.591092 \n", "L 308.383933 160.463804 \n", "L 308.99388 159.995035 \n", "L 310.213774 159.74541 \n", "L 310.823721 159.961076 \n", "L 311.433668 159.836989 \n", "L 312.043616 160.051322 \n", "L 312.653563 159.590705 \n", "L 313.26351 159.804581 \n", "L 313.873457 159.682124 \n", "L 314.483404 160.229111 \n", "L 315.093351 160.106218 \n", "L 315.703299 158.985721 \n", "L 316.313246 159.198443 \n", "L 316.923193 159.079009 \n", "L 317.53314 159.290459 \n", "L 318.143087 159.830308 \n", "L 319.362982 159.590705 \n", "L 320.582876 158.0485 \n", "L 321.192823 157.933917 \n", "L 321.80277 158.469193 \n", "L 323.022665 157.593509 \n", "L 323.632612 157.803429 \n", "L 324.242559 157.369314 \n", "L 324.852506 156.616524 \n", "L 325.462453 156.827278 \n", "L 326.0724 156.717825 \n", "L 326.682348 157.245765 \n", "L 327.902242 157.026421 \n", "L 329.122136 156.178254 \n", "L 330.951978 156.800238 \n", "L 331.561925 157.318117 \n", "L 332.171872 156.898298 \n", "L 332.781819 156.79149 \n", "L 333.391766 155.443954 \n", "L 334.001714 155.960163 \n", "L 334.611661 155.856258 \n", "L 337.051449 157.893616 \n", "L 338.271344 157.07056 \n", "L 338.881291 156.053949 \n", "L 339.491238 155.648466 \n", "L 340.101185 155.849955 \n", "L 340.711132 155.446758 \n", "L 341.32108 155.34659 \n", "L 341.931027 155.547383 \n", "L 342.540974 155.447434 \n", "L 343.760868 155.845899 \n", "L 344.370815 155.74595 \n", "L 344.980763 156.240785 \n", "L 346.200657 156.040469 \n", "L 347.420551 156.43102 \n", "L 348.030498 156.331111 \n", "L 348.640446 156.524886 \n", "L 349.250393 157.010436 \n", "L 349.86034 157.201864 \n", "L 350.470287 156.809821 \n", "L 351.690181 157.771199 \n", "L 352.300129 157.380469 \n", "L 354.12997 157.943739 \n", "L 355.349865 157.742568 \n", "L 355.959812 158.214061 \n", "L 356.569759 158.113337 \n", "L 357.179706 158.582027 \n", "L 360.229442 159.48833 \n", "L 360.839389 159.948212 \n", "L 362.059283 159.743263 \n", "L 362.669231 160.199725 \n", "L 363.279178 160.375725 \n", "L 363.889125 159.717058 \n", "L 365.109019 159.515191 \n", "L 365.718966 159.138657 \n", "L 366.328914 159.315014 \n", "L 366.938861 159.215542 \n", "L 368.768702 159.739843 \n", "L 369.378649 159.367444 \n", "L 370.598544 159.714215 \n", "L 371.208491 159.615359 \n", "L 371.818438 158.97558 \n", "L 372.428385 159.148717 \n", "L 373.038332 158.782024 \n", "L 373.64828 159.223873 \n", "L 375.478121 159.736543 \n", "L 376.088068 159.372355 \n", "L 376.088068 159.372355 \n", "\" clip-path=\"url(#p469350dd24)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 71.724432 111.071025 \n", "L 72.334379 111.071025 \n", "L 72.944326 22.11829 \n", "L 73.554273 44.356489 \n", "L 74.16422 84.385215 \n", "L 74.774168 66.594668 \n", "L 75.384115 53.887137 \n", "L 75.994062 77.713767 \n", "L 76.604009 96.245573 \n", "L 77.213956 97.72813 \n", "L 78.433851 144.428304 \n", "L 79.653745 120.601674 \n", "L 80.873639 152.767613 \n", "L 81.483586 158.16364 \n", "L 82.703481 139.161355 \n", "L 83.313428 151.099751 \n", "L 83.923375 149.193618 \n", "L 84.533322 135.330855 \n", "L 85.143269 140.07736 \n", "L 85.753217 149.987843 \n", "L 86.363164 143.09401 \n", "L 86.973111 121.3348 \n", "L 88.193005 120.601674 \n", "L 88.802952 124.874041 \n", "L 89.4129 133.309204 \n", "L 90.022847 136.896004 \n", "L 90.632794 131.919319 \n", "L 91.242741 139.374177 \n", "L 92.462635 145.38137 \n", "L 93.68253 157.951513 \n", "L 95.512371 164.442667 \n", "L 96.122319 163.140921 \n", "L 96.732266 165.078031 \n", "L 97.95216 162.623181 \n", "L 98.562107 164.442667 \n", "L 99.172054 169.083675 \n", "L 100.391949 172.226022 \n", "L 101.001896 168.254914 \n", "L 101.611843 169.779821 \n", "L 102.22179 168.628665 \n", "L 103.441685 171.491742 \n", "L 104.051632 170.372836 \n", "L 104.661579 171.72061 \n", "L 105.271526 170.637591 \n", "L 105.881473 171.933412 \n", "L 107.101368 169.870286 \n", "L 107.711315 171.114114 \n", "L 108.931209 169.177242 \n", "L 110.151103 171.53109 \n", "L 110.761051 168.548181 \n", "L 111.370998 171.72061 \n", "L 111.980945 166.832442 \n", "L 112.590892 164.050226 \n", "L 114.420734 167.449514 \n", "L 115.030681 170.372836 \n", "L 115.640628 171.388294 \n", "L 116.860522 176.89604 \n", "L 117.470469 174.274276 \n", "L 118.080417 173.453447 \n", "L 120.520205 176.96193 \n", "L 121.7401 172.159038 \n", "L 122.350047 173.020248 \n", "L 123.569941 171.579563 \n", "L 124.179888 172.417729 \n", "L 124.789835 171.72061 \n", "L 126.00973 167.407761 \n", "L 126.619677 166.78866 \n", "L 127.229624 169.083675 \n", "L 128.449518 167.849371 \n", "L 129.059466 170.060721 \n", "L 129.669413 170.836137 \n", "L 130.27936 170.219999 \n", "L 130.889307 170.977959 \n", "L 131.499254 173.068383 \n", "L 132.109201 173.782701 \n", "L 132.719149 173.161791 \n", "L 133.329096 173.861178 \n", "L 133.939043 175.842427 \n", "L 134.54899 176.502605 \n", "L 135.158937 178.420946 \n", "L 136.988779 176.550124 \n", "L 137.598726 174.72527 \n", "L 138.208673 175.359581 \n", "L 139.428567 174.211586 \n", "L 140.038515 171.291227 \n", "L 140.648462 173.103854 \n", "L 141.868356 169.733813 \n", "L 143.698198 168.254914 \n", "L 144.308145 168.890298 \n", "L 144.918092 166.207018 \n", "L 146.137986 163.140921 \n", "L 146.747933 163.797043 \n", "L 147.357881 163.375236 \n", "L 147.967828 164.01907 \n", "L 149.187722 167.361415 \n", "L 149.797669 165.890728 \n", "L 151.017564 165.053794 \n", "L 151.627511 163.634006 \n", "L 152.847405 162.849483 \n", "L 153.457352 164.442667 \n", "L 154.067299 163.069125 \n", "L 154.677247 163.663512 \n", "L 155.287194 165.216155 \n", "L 155.897141 164.826637 \n", "L 157.117035 165.956757 \n", "L 157.726982 163.69095 \n", "L 158.33693 163.322985 \n", "L 158.946877 163.886713 \n", "L 160.776718 162.808844 \n", "L 161.996613 163.905362 \n", "L 162.60656 162.663602 \n", "L 163.216507 164.089216 \n", "L 163.826454 164.618229 \n", "L 164.436401 164.268238 \n", "L 165.046348 164.789238 \n", "L 166.266243 162.3899 \n", "L 166.87619 162.063032 \n", "L 167.486137 163.429276 \n", "L 168.096084 163.939163 \n", "L 169.315979 163.282404 \n", "L 169.925926 163.783761 \n", "L 171.14582 161.513738 \n", "L 172.365714 160.905991 \n", "L 172.975662 159.009619 \n", "L 173.585609 158.724266 \n", "L 174.195556 160.021339 \n", "L 174.805503 160.518282 \n", "L 175.41545 161.789687 \n", "L 176.025397 160.719055 \n", "L 176.635345 161.203353 \n", "L 177.855239 160.630399 \n", "L 178.465186 158.074447 \n", "L 180.295028 161.759168 \n", "L 180.904975 162.218851 \n", "L 181.514922 161.936261 \n", "L 182.124869 160.923646 \n", "L 182.734816 161.380347 \n", "L 183.954711 160.836462 \n", "L 184.564658 161.286283 \n", "L 185.174605 159.590705 \n", "L 185.784552 160.042335 \n", "L 186.394499 159.077259 \n", "L 187.004446 160.229111 \n", "L 187.614394 160.670323 \n", "L 188.224341 159.717058 \n", "L 188.834288 160.156341 \n", "L 189.444235 159.215542 \n", "L 190.054182 159.652897 \n", "L 190.664129 158.724266 \n", "L 191.274077 158.482376 \n", "L 191.884024 158.916808 \n", "L 192.493971 157.335375 \n", "L 193.103918 157.104062 \n", "L 195.543707 158.817714 \n", "L 196.153654 158.58479 \n", "L 196.763601 157.706442 \n", "L 197.983495 158.540989 \n", "L 199.20339 158.088901 \n", "L 199.813337 159.130803 \n", "L 200.423284 159.533483 \n", "L 201.033231 160.558803 \n", "L 201.643178 160.327549 \n", "L 202.253126 159.477852 \n", "L 204.082967 158.811709 \n", "L 204.692914 157.984459 \n", "L 205.302861 157.771199 \n", "L 205.912809 158.16364 \n", "L 206.522756 157.951513 \n", "L 207.74265 158.724266 \n", "L 208.352597 159.698508 \n", "L 208.962544 159.483359 \n", "L 209.572492 160.445671 \n", "L 210.182439 160.814332 \n", "L 210.792386 160.01444 \n", "L 211.402333 158.641396 \n", "L 212.01228 159.013079 \n", "L 212.622227 158.80644 \n", "L 213.842122 160.679271 \n", "L 214.452069 161.035962 \n", "L 215.062016 160.824254 \n", "L 216.891858 161.874566 \n", "L 217.501805 161.662897 \n", "L 218.721699 162.34749 \n", "L 219.941593 161.927195 \n", "L 220.551541 162.264223 \n", "L 221.161488 160.971344 \n", "L 221.771435 161.849713 \n", "L 222.991329 162.513569 \n", "L 224.211224 164.230023 \n", "L 224.821171 162.960109 \n", "L 225.431118 163.809787 \n", "L 226.651012 162.349658 \n", "L 227.870907 161.950596 \n", "L 228.480854 162.787728 \n", "L 229.700748 162.3899 \n", "L 230.310695 162.7045 \n", "L 230.920642 161.998155 \n", "L 233.360431 163.238782 \n", "L 233.970378 162.543671 \n", "L 235.190273 163.153009 \n", "L 235.80022 163.948488 \n", "L 236.410167 163.261011 \n", "L 237.020114 163.559666 \n", "L 237.630061 163.367402 \n", "L 238.240008 163.663512 \n", "L 238.849956 162.98707 \n", "L 239.459903 162.798962 \n", "L 241.899691 165.872257 \n", "L 243.119586 166.429901 \n", "L 243.729533 166.234297 \n", "L 244.33948 166.979691 \n", "L 245.559374 164.722592 \n", "L 246.169322 165.000549 \n", "L 246.779269 164.813296 \n", "L 247.389216 163.703953 \n", "L 247.999163 164.442667 \n", "L 248.60911 164.259251 \n", "L 249.829005 164.806973 \n", "L 250.438952 165.531889 \n", "L 251.048899 165.799567 \n", "L 251.658846 165.614679 \n", "L 252.87874 166.14411 \n", "L 253.488688 165.513656 \n", "L 254.098635 165.332189 \n", "L 254.708582 166.038494 \n", "L 255.318529 165.85647 \n", "L 255.928476 166.116036 \n", "L 257.148371 167.504967 \n", "L 257.758318 166.884494 \n", "L 258.368265 167.13732 \n", "L 259.588159 166.774425 \n", "L 260.198106 167.025163 \n", "L 261.418001 166.666483 \n", "L 262.027948 167.341433 \n", "L 262.637895 167.587161 \n", "L 263.247842 167.407761 \n", "L 263.857789 168.073963 \n", "L 264.467737 167.894146 \n", "L 265.077684 168.554622 \n", "L 266.297578 167.361415 \n", "L 266.907525 168.017398 \n", "L 268.737367 168.725572 \n", "L 269.957261 168.371863 \n", "L 270.567208 169.012715 \n", "L 271.177155 168.429263 \n", "L 271.787103 169.06604 \n", "L 272.39705 169.294629 \n", "L 273.616944 168.943882 \n", "L 274.226891 168.369397 \n", "L 274.836838 168.996829 \n", "L 275.446786 169.222217 \n", "L 276.66668 168.877096 \n", "L 277.276627 169.100834 \n", "L 278.496521 167.18973 \n", "L 279.106469 167.807737 \n", "L 279.716416 168.031992 \n", "L 280.326363 167.087912 \n", "L 280.93631 166.925054 \n", "L 281.546257 167.149925 \n", "L 282.156204 166.987843 \n", "L 282.766152 166.442168 \n", "L 283.376099 166.283069 \n", "L 283.986046 165.742544 \n", "L 284.595993 164.823893 \n", "L 285.20594 164.670739 \n", "L 285.815887 164.897538 \n", "L 287.645729 166.697797 \n", "L 288.255676 165.042342 \n", "L 288.865623 164.891156 \n", "L 289.47557 164.368127 \n", "L 290.085518 164.962991 \n", "L 291.305412 165.403647 \n", "L 291.915359 164.884972 \n", "L 292.525306 165.471863 \n", "L 293.135253 165.322407 \n", "L 293.745201 164.808226 \n", "L 294.355148 164.661394 \n", "L 294.965095 165.242519 \n", "L 295.575042 165.457886 \n", "L 297.404884 165.018105 \n", "L 298.014831 165.590443 \n", "L 301.064567 164.867376 \n", "L 301.674514 164.01907 \n", "L 302.894408 163.038148 \n", "L 304.114302 164.163238 \n", "L 304.72425 163.676237 \n", "L 305.944144 163.402952 \n", "L 306.554091 161.884686 \n", "L 307.164038 162.098164 \n", "L 308.383933 161.835815 \n", "L 308.99388 162.047782 \n", "L 309.603827 162.599899 \n", "L 310.213774 162.808844 \n", "L 310.823721 163.356208 \n", "L 312.653563 163.970935 \n", "L 313.26351 163.501609 \n", "L 313.873457 163.705106 \n", "L 315.093351 163.441942 \n", "L 315.703299 163.97682 \n", "L 316.923193 163.714272 \n", "L 317.53314 163.91423 \n", "L 318.143087 163.783761 \n", "L 318.753034 164.311204 \n", "L 319.362982 164.508219 \n", "L 319.972929 165.031327 \n", "L 320.582876 164.899387 \n", "L 321.192823 165.418976 \n", "L 321.80277 165.286738 \n", "L 322.412717 165.802867 \n", "L 323.022665 165.993421 \n", "L 323.632612 165.860745 \n", "L 324.852506 166.23883 \n", "L 325.462453 165.786544 \n", "L 326.0724 165.655657 \n", "L 326.682348 165.206929 \n", "L 327.902242 166.217496 \n", "L 328.512189 166.086809 \n", "L 329.122136 165.641322 \n", "L 329.732083 165.512603 \n", "L 330.342031 165.698464 \n", "L 330.951978 166.196659 \n", "L 331.561925 165.75507 \n", "L 332.171872 165.939062 \n", "L 332.781819 165.500136 \n", "L 333.391766 165.994156 \n", "L 334.001714 165.247569 \n", "L 334.611661 165.431025 \n", "L 335.831555 163.950754 \n", "L 336.441502 163.829193 \n", "L 337.661397 164.19839 \n", "L 338.271344 164.686366 \n", "L 340.101185 164.321642 \n", "L 341.32108 164.683622 \n", "L 342.540974 164.442667 \n", "L 343.150921 164.622166 \n", "L 343.760868 164.502354 \n", "L 346.200657 165.21188 \n", "L 346.810604 164.796893 \n", "L 348.640446 165.322407 \n", "L 349.86034 165.08499 \n", "L 350.470287 164.093073 \n", "L 351.080234 164.268238 \n", "L 351.690181 164.152601 \n", "L 352.300129 163.748012 \n", "L 352.910076 163.634006 \n", "L 353.520023 163.232281 \n", "L 354.12997 163.407445 \n", "L 355.959812 163.071233 \n", "L 356.569759 163.530319 \n", "L 357.179706 162.849483 \n", "L 357.789653 162.455413 \n", "L 358.3996 162.912908 \n", "L 359.009548 163.085747 \n", "L 359.619495 163.539962 \n", "L 360.229442 163.710773 \n", "L 360.839389 163.319048 \n", "L 361.449336 163.209277 \n", "L 362.059283 162.820256 \n", "L 362.669231 162.711996 \n", "L 363.279178 163.1613 \n", "L 363.889125 162.774805 \n", "L 364.499072 162.944701 \n", "L 365.109019 163.390724 \n", "L 365.718966 163.558652 \n", "L 366.328914 163.450213 \n", "L 366.938861 162.516889 \n", "L 367.548808 162.685572 \n", "L 368.158755 162.031617 \n", "L 369.378649 162.368924 \n", "L 369.988597 162.264223 \n", "L 370.598544 161.888225 \n", "L 371.818438 162.764645 \n", "L 372.428385 162.930107 \n", "L 373.038332 162.286232 \n", "L 373.64828 162.182983 \n", "L 374.258227 162.348604 \n", "L 374.868174 162.781505 \n", "L 376.088068 162.574648 \n", "L 376.088068 162.574648 \n", "\" clip-path=\"url(#p469350dd24)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 71.724432 111.071025 \n", "L 72.334379 111.071025 \n", "L 72.944326 155.547383 \n", "L 73.554273 211.14284 \n", "L 74.16422 217.814298 \n", "L 74.774168 177.785562 \n", "L 75.384115 168.254914 \n", "L 75.994062 177.785562 \n", "L 76.604009 170.372836 \n", "L 77.213956 191.128477 \n", "L 77.823903 183.850534 \n", "L 78.433851 188.904661 \n", "L 79.043798 182.917449 \n", "L 79.653745 168.254914 \n", "L 80.263692 164.442667 \n", "L 81.483586 173.861178 \n", "L 82.093534 185.198288 \n", "L 82.703481 174.274276 \n", "L 83.923375 168.254914 \n", "L 84.533322 171.72061 \n", "L 85.143269 180.686197 \n", "L 85.753217 183.345122 \n", "L 86.363164 175.116995 \n", "L 86.973111 177.785562 \n", "L 87.583058 175.314667 \n", "L 88.193005 177.785562 \n", "L 89.4129 164.442667 \n", "L 90.632794 169.446252 \n", "L 91.242741 167.677308 \n", "L 91.852688 173.861178 \n", "L 92.462635 175.879448 \n", "L 93.072583 181.491935 \n", "L 93.68253 179.588665 \n", "L 94.292477 174.274276 \n", "L 94.902424 172.653675 \n", "L 95.512371 164.442667 \n", "L 96.122319 166.395285 \n", "L 97.342213 163.822055 \n", "L 97.95216 165.655657 \n", "L 99.172054 163.282404 \n", "L 99.782002 167.849371 \n", "L 100.391949 169.446252 \n", "L 101.001896 173.701004 \n", "L 102.22179 166.012409 \n", "L 104.051632 162.960109 \n", "L 104.661579 157.164704 \n", "L 105.271526 156.341609 \n", "L 105.881473 157.888247 \n", "L 106.49142 152.480053 \n", "L 107.101368 154.039714 \n", "L 108.321262 148.256179 \n", "L 108.931209 149.808503 \n", "L 111.370998 139.374177 \n", "L 111.980945 140.943203 \n", "L 112.590892 140.503919 \n", "L 113.810786 147.287504 \n", "L 114.420734 148.656691 \n", "L 115.640628 154.938124 \n", "L 116.250575 156.148431 \n", "L 117.470469 154.962182 \n", "L 118.690364 157.258012 \n", "L 119.910258 162.774805 \n", "L 120.520205 162.136478 \n", "L 121.7401 164.121147 \n", "L 122.350047 161.901149 \n", "L 122.959994 164.442667 \n", "L 123.569941 162.270565 \n", "L 124.179888 164.749394 \n", "L 124.789835 164.139419 \n", "L 126.00973 165.925204 \n", "L 126.619677 162.3899 \n", "L 127.229624 164.732732 \n", "L 127.839571 162.721003 \n", "L 128.449518 162.17153 \n", "L 129.059466 160.229111 \n", "L 129.669413 159.717058 \n", "L 130.27936 161.966661 \n", "L 130.889307 158.724266 \n", "L 131.499254 156.895157 \n", "L 132.109201 157.771199 \n", "L 132.719149 155.98774 \n", "L 133.939043 155.115575 \n", "L 135.158937 156.818152 \n", "L 135.768884 156.386563 \n", "L 136.378832 158.457065 \n", "L 136.988779 158.018299 \n", "L 138.208673 159.590705 \n", "L 138.81862 159.153588 \n", "L 139.428567 161.106943 \n", "L 140.648462 160.229111 \n", "L 141.258409 157.481153 \n", "L 141.868356 158.2313 \n", "L 142.478303 156.687803 \n", "L 143.08825 154.039714 \n", "L 143.698198 155.921134 \n", "L 144.308145 156.659291 \n", "L 145.528039 160.286671 \n", "L 146.137986 160.971344 \n", "L 146.747933 159.492883 \n", "L 147.357881 160.172923 \n", "L 147.967828 156.606344 \n", "L 148.577775 156.247803 \n", "L 149.187722 157.979687 \n", "L 150.407616 157.258012 \n", "L 151.017564 157.923996 \n", "L 151.627511 156.558209 \n", "L 152.237458 156.21621 \n", "L 152.847405 156.875036 \n", "L 153.457352 155.547383 \n", "L 154.067299 155.220356 \n", "L 154.677247 156.845968 \n", "L 155.287194 154.580518 \n", "L 155.897141 154.267488 \n", "L 156.507088 154.912019 \n", "L 157.117035 156.49369 \n", "L 157.726982 155.234174 \n", "L 158.33693 155.858405 \n", "L 158.946877 151.84103 \n", "L 160.166771 151.282531 \n", "L 160.776718 150.101313 \n", "L 161.386665 149.837592 \n", "L 161.996613 150.472896 \n", "L 162.60656 150.210229 \n", "L 163.216507 151.718295 \n", "L 165.046348 148.327199 \n", "L 165.656296 148.08684 \n", "L 166.87619 151.014774 \n", "L 168.096084 150.512323 \n", "L 168.706031 151.933682 \n", "L 169.315979 152.508624 \n", "L 169.925926 153.90012 \n", "L 170.535873 154.455954 \n", "L 171.14582 153.377807 \n", "L 171.755767 154.738723 \n", "L 172.365714 153.671888 \n", "L 173.585609 153.164726 \n", "L 174.195556 151.336611 \n", "L 174.805503 151.884632 \n", "L 175.41545 151.645963 \n", "L 176.025397 152.961547 \n", "L 176.635345 152.719418 \n", "L 177.855239 155.293245 \n", "L 179.075133 156.301227 \n", "L 180.295028 154.305026 \n", "L 180.904975 154.806124 \n", "L 181.514922 153.090127 \n", "L 183.344763 152.405036 \n", "L 183.954711 150.739143 \n", "L 184.564658 151.243223 \n", "L 185.174605 149.601348 \n", "L 186.394499 150.605572 \n", "L 187.614394 150.191599 \n", "L 188.224341 150.682796 \n", "L 188.834288 149.786195 \n", "L 191.274077 154.418555 \n", "L 191.884024 154.199629 \n", "L 192.493971 155.323884 \n", "L 193.713865 154.883567 \n", "L 194.93376 155.766488 \n", "L 195.543707 156.855511 \n", "L 197.373548 156.191973 \n", "L 197.983495 157.258012 \n", "L 200.423284 158.904103 \n", "L 201.033231 158.053093 \n", "L 201.643178 156.586561 \n", "L 202.253126 156.995464 \n", "L 202.863073 156.165112 \n", "L 203.47302 157.187072 \n", "L 204.082967 156.975522 \n", "L 204.692914 157.37518 \n", "L 205.302861 157.164704 \n", "L 205.912809 157.559888 \n", "L 206.522756 157.350486 \n", "L 207.74265 158.128607 \n", "L 208.352597 157.919463 \n", "L 208.962544 158.892949 \n", "L 209.572492 158.094488 \n", "L 210.182439 157.888247 \n", "L 210.792386 158.849128 \n", "L 211.402333 158.641396 \n", "L 212.01228 157.857847 \n", "L 212.622227 158.80644 \n", "L 213.232175 158.028896 \n", "L 213.842122 157.828222 \n", "L 214.452069 158.764826 \n", "L 216.28191 158.16364 \n", "L 216.891858 158.524884 \n", "L 217.501805 158.327153 \n", "L 218.111752 158.68472 \n", "L 218.721699 157.936621 \n", "L 219.331646 157.743761 \n", "L 219.941593 156.458796 \n", "L 220.551541 156.273531 \n", "L 221.161488 156.632171 \n", "L 221.771435 155.907515 \n", "L 222.381382 154.650683 \n", "L 222.991329 155.01153 \n", "L 223.601276 154.302044 \n", "L 224.211224 155.192998 \n", "L 224.821171 154.488422 \n", "L 225.431118 152.734648 \n", "L 226.041065 153.095933 \n", "L 226.651012 152.931127 \n", "L 227.260959 153.288833 \n", "L 227.870907 152.086201 \n", "L 229.700748 153.152518 \n", "L 230.920642 154.868357 \n", "L 232.750484 155.883059 \n", "L 233.360431 155.714595 \n", "L 234.580325 156.377179 \n", "L 235.190273 156.208754 \n", "L 235.80022 155.547383 \n", "L 236.410167 155.875623 \n", "L 237.630061 155.547383 \n", "L 238.240008 154.898101 \n", "L 240.679797 154.267488 \n", "L 241.289744 154.590916 \n", "L 241.899691 154.435475 \n", "L 242.509639 153.806314 \n", "L 243.729533 154.447266 \n", "L 244.33948 155.234174 \n", "L 244.949427 155.547383 \n", "L 245.559374 155.391882 \n", "L 246.169322 155.702367 \n", "L 246.779269 155.547383 \n", "L 247.389216 156.316875 \n", "L 247.999163 155.700757 \n", "L 248.60911 156.005913 \n", "L 249.219057 155.395083 \n", "L 249.829005 155.699186 \n", "L 251.658846 155.246879 \n", "L 252.268793 155.996648 \n", "L 252.87874 155.398145 \n", "L 253.488688 156.142386 \n", "L 254.708582 155.842916 \n", "L 255.318529 154.811035 \n", "L 256.538423 155.401088 \n", "L 257.148371 155.255747 \n", "L 259.588159 156.410999 \n", "L 260.198106 155.834327 \n", "L 260.808054 156.548466 \n", "L 262.027948 157.110464 \n", "L 262.637895 156.113974 \n", "L 263.247842 156.818152 \n", "L 264.467737 157.371342 \n", "L 265.687631 157.081058 \n", "L 266.297578 157.354244 \n", "L 266.907525 157.210056 \n", "L 267.517472 156.652392 \n", "L 268.12742 156.924364 \n", "L 268.737367 156.371035 \n", "L 270.567208 155.955431 \n", "L 271.177155 156.632171 \n", "L 271.787103 156.49369 \n", "L 272.39705 156.760374 \n", "L 273.006997 156.219232 \n", "L 273.616944 156.48514 \n", "L 274.226891 157.15015 \n", "L 274.836838 157.012185 \n", "L 275.446786 156.47675 \n", "L 276.66668 156.999142 \n", "L 277.276627 156.863266 \n", "L 278.496521 158.16364 \n", "L 279.106469 157.634249 \n", "L 280.326363 157.362753 \n", "L 280.93631 156.840301 \n", "L 281.546257 156.707645 \n", "L 282.766152 157.982689 \n", "L 283.376099 157.847886 \n", "L 283.986046 156.949217 \n", "L 284.595993 157.580586 \n", "L 285.20594 157.828222 \n", "L 285.815887 157.695387 \n", "L 286.425835 157.941294 \n", "L 287.035782 158.562741 \n", "L 288.255676 159.045527 \n", "L 290.695465 158.512478 \n", "L 291.305412 158.75067 \n", "L 291.915359 159.356131 \n", "L 292.525306 159.590705 \n", "L 293.745201 159.324836 \n", "L 294.355148 159.922106 \n", "L 295.575042 160.381769 \n", "L 296.184989 160.971344 \n", "L 297.404884 160.702334 \n", "L 298.014831 160.927603 \n", "L 298.624778 161.509364 \n", "L 299.234725 161.01775 \n", "L 299.844672 161.240354 \n", "L 300.454619 161.106943 \n", "L 301.064567 161.682063 \n", "L 302.284461 162.1191 \n", "L 302.894408 162.687024 \n", "L 303.504355 162.901754 \n", "L 304.114302 162.766056 \n", "L 305.334197 161.801875 \n", "L 305.944144 162.016686 \n", "L 306.554091 162.57604 \n", "L 307.773985 162.310549 \n", "L 308.383933 162.864833 \n", "L 308.99388 163.074155 \n", "L 309.603827 162.599899 \n", "L 310.213774 162.468455 \n", "L 310.823721 161.658642 \n", "L 311.433668 161.530241 \n", "L 312.043616 161.740299 \n", "L 312.653563 161.612355 \n", "L 313.873457 162.028874 \n", "L 315.093351 160.439786 \n", "L 316.313246 160.857994 \n", "L 318.753034 160.367493 \n", "L 319.362982 159.590705 \n", "L 320.582876 160.005891 \n", "L 321.192823 160.537429 \n", "L 321.80277 160.41706 \n", "L 323.022665 161.470395 \n", "L 324.242559 161.22751 \n", "L 324.852506 161.748411 \n", "L 326.682348 161.385576 \n", "L 327.292295 160.948082 \n", "L 327.902242 161.146549 \n", "L 329.122136 162.17153 \n", "L 329.732083 162.051003 \n", "L 330.951978 162.438095 \n", "L 331.561925 162.942752 \n", "L 333.391766 162.580851 \n", "L 334.001714 162.770928 \n", "L 334.611661 162.651255 \n", "L 335.221608 163.148436 \n", "L 335.831555 163.028446 \n", "L 336.441502 163.215719 \n", "L 338.271344 162.85857 \n", "L 338.881291 163.04455 \n", "L 339.491238 163.532924 \n", "L 340.101185 163.413947 \n", "L 340.711132 162.993651 \n", "L 341.32108 162.876444 \n", "L 342.540974 163.243296 \n", "L 343.150921 163.126327 \n", "L 343.760868 162.71136 \n", "L 344.370815 162.596101 \n", "L 344.980763 162.184176 \n", "L 345.59071 162.070587 \n", "L 346.810604 162.435331 \n", "L 347.420551 162.91102 \n", "L 348.640446 163.26966 \n", "L 349.250393 162.862586 \n", "L 349.86034 162.749256 \n", "L 351.080234 163.10547 \n", "L 352.300129 164.037462 \n", "L 352.910076 163.922799 \n", "L 353.520023 164.096851 \n", "L 354.12997 163.407445 \n", "L 354.739917 163.294891 \n", "L 355.349865 163.469141 \n", "L 357.179706 163.133982 \n", "L 359.009548 163.651125 \n", "L 359.619495 162.975797 \n", "L 360.839389 162.757248 \n", "L 362.059283 163.099983 \n", "L 362.669231 162.991126 \n", "L 363.279178 162.32562 \n", "L 364.499072 163.222101 \n", "L 365.109019 162.837077 \n", "L 365.718966 163.006157 \n", "L 366.938861 163.892439 \n", "L 367.548808 164.058298 \n", "L 368.158755 163.949502 \n", "L 368.768702 164.38797 \n", "L 371.818438 165.200468 \n", "L 372.428385 164.820792 \n", "L 373.038332 165.251327 \n", "L 373.64828 165.142093 \n", "L 374.258227 164.496349 \n", "L 374.868174 164.924936 \n", "L 375.478121 164.817014 \n", "L 376.088068 165.243235 \n", "L 376.088068 165.243235 \n", "\" clip-path=\"url(#p469350dd24)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 71.724432 244.500108 \n", "L 72.334379 177.785562 \n", "L 72.944326 155.547383 \n", "L 73.554273 77.713767 \n", "L 74.16422 57.699404 \n", "L 74.774168 88.832847 \n", "L 75.384115 111.071025 \n", "L 76.604009 111.071025 \n", "L 77.213956 71.0423 \n", "L 77.823903 50.421441 \n", "L 79.043798 59.752151 \n", "L 79.653745 82.479081 \n", "L 80.263692 39.908857 \n", "L 81.483586 48.280873 \n", "L 82.093534 44.356489 \n", "L 82.703481 54.890367 \n", "L 83.313428 57.699404 \n", "L 84.533322 86.811196 \n", "L 85.143269 93.667233 \n", "L 85.753217 94.392386 \n", "L 86.973111 105.939138 \n", "L 87.583058 96.245573 \n", "L 88.802952 97.26803 \n", "L 89.4129 93.280478 \n", "L 90.632794 94.392386 \n", "L 91.242741 90.854497 \n", "L 91.852688 99.297872 \n", "L 92.462635 103.446511 \n", "L 93.072583 111.071025 \n", "L 93.68253 111.071025 \n", "L 94.292477 114.582312 \n", "L 94.902424 121.3348 \n", "L 95.512371 124.413941 \n", "L 96.122319 124.088504 \n", "L 96.732266 126.955439 \n", "L 97.342213 120.380044 \n", "L 97.95216 120.168474 \n", "L 98.562107 114.03612 \n", "L 99.172054 116.872296 \n", "L 99.782002 111.071025 \n", "L 101.001896 116.517116 \n", "L 101.611843 121.745354 \n", "L 103.441685 128.693745 \n", "L 105.271526 134.897656 \n", "L 105.881473 139.161355 \n", "L 106.49142 140.97754 \n", "L 108.321262 139.506733 \n", "L 108.931209 136.896004 \n", "L 109.541156 136.486087 \n", "L 110.151103 138.173812 \n", "L 111.370998 137.352506 \n", "L 112.590892 144.428304 \n", "L 114.420734 137.380998 \n", "L 115.030681 137.015577 \n", "L 115.640628 134.832381 \n", "L 116.250575 130.905074 \n", "L 116.860522 130.640637 \n", "L 117.470469 128.627496 \n", "L 118.080417 130.132322 \n", "L 118.690364 129.887946 \n", "L 119.300311 131.338731 \n", "L 119.910258 131.085388 \n", "L 120.520205 134.132836 \n", "L 122.350047 138.074538 \n", "L 122.959994 136.187094 \n", "L 123.569941 135.89504 \n", "L 124.789835 138.363351 \n", "L 126.00973 137.756836 \n", "L 126.619677 138.929843 \n", "L 127.839571 138.330744 \n", "L 129.059466 140.565873 \n", "L 129.669413 140.258649 \n", "L 130.27936 135.831059 \n", "L 130.889307 134.216899 \n", "L 131.499254 136.678629 \n", "L 132.109201 135.088269 \n", "L 132.719149 137.492638 \n", "L 133.329096 138.541717 \n", "L 133.939043 138.275013 \n", "L 134.54899 135.447506 \n", "L 135.158937 131.403071 \n", "L 135.768884 132.470024 \n", "L 136.378832 132.270046 \n", "L 136.988779 134.544662 \n", "L 137.598726 133.10519 \n", "L 138.208673 130.478893 \n", "L 138.81862 131.506122 \n", "L 139.428567 130.132322 \n", "L 140.038515 131.144439 \n", "L 141.868356 130.625288 \n", "L 142.478303 131.598575 \n", "L 143.08825 131.424623 \n", "L 143.698198 129.011069 \n", "L 144.308145 128.861573 \n", "L 144.918092 129.817264 \n", "L 145.528039 128.569916 \n", "L 146.137986 129.512445 \n", "L 146.747933 128.287685 \n", "L 148.577775 127.880988 \n", "L 149.187722 128.792083 \n", "L 149.797669 127.620369 \n", "L 152.237458 131.135552 \n", "L 153.457352 128.861573 \n", "L 154.067299 130.692948 \n", "L 156.507088 133.944589 \n", "L 157.117035 132.836061 \n", "L 157.726982 134.562059 \n", "L 158.33693 134.39779 \n", "L 158.946877 135.162391 \n", "L 159.556824 134.076052 \n", "L 160.776718 137.393782 \n", "L 161.386665 137.215913 \n", "L 161.996613 136.144943 \n", "L 162.60656 137.756836 \n", "L 163.216507 138.463758 \n", "L 163.826454 138.283543 \n", "L 164.436401 137.233589 \n", "L 165.046348 137.930132 \n", "L 165.656296 139.47852 \n", "L 166.266243 136.730463 \n", "L 166.87619 138.266762 \n", "L 167.486137 138.09464 \n", "L 168.096084 139.603044 \n", "L 168.706031 139.424718 \n", "L 169.315979 140.07736 \n", "L 170.535873 138.084281 \n", "L 171.14582 139.546757 \n", "L 172.365714 139.203664 \n", "L 172.975662 139.834178 \n", "L 173.585609 139.66297 \n", "L 174.805503 140.89636 \n", "L 176.635345 138.065353 \n", "L 177.245292 138.677037 \n", "L 177.855239 138.51929 \n", "L 178.465186 139.12147 \n", "L 179.68508 138.806293 \n", "L 180.295028 137.905935 \n", "L 180.904975 137.756836 \n", "L 182.124869 140.396096 \n", "L 182.734816 138.777623 \n", "L 183.344763 140.07736 \n", "L 183.954711 140.641804 \n", "L 184.564658 139.048104 \n", "L 185.174605 138.898488 \n", "L 185.784552 137.331013 \n", "L 186.394499 137.192074 \n", "L 187.004446 138.459105 \n", "L 188.224341 138.173812 \n", "L 188.834288 138.724715 \n", "L 189.444235 137.894403 \n", "L 190.054182 136.388345 \n", "L 190.664129 136.259168 \n", "L 191.274077 135.454007 \n", "L 191.884024 135.330855 \n", "L 193.103918 136.422562 \n", "L 193.713865 135.632592 \n", "L 194.323812 136.171546 \n", "L 195.543707 134.617333 \n", "L 196.153654 135.804217 \n", "L 196.763601 136.331878 \n", "L 197.373548 136.20984 \n", "L 197.983495 136.730463 \n", "L 198.593443 137.884521 \n", "L 199.20339 138.392221 \n", "L 200.423284 136.875704 \n", "L 201.033231 137.380998 \n", "L 201.643178 138.505054 \n", "L 202.863073 138.251035 \n", "L 204.082967 140.449918 \n", "L 205.302861 141.395827 \n", "L 205.912809 140.654847 \n", "L 206.522756 140.521595 \n", "L 207.132703 139.791192 \n", "L 207.74265 140.258649 \n", "L 208.962544 140.000336 \n", "L 209.572492 141.048481 \n", "L 210.182439 140.917018 \n", "L 211.402333 141.817734 \n", "L 212.01228 141.684621 \n", "L 212.622227 140.97754 \n", "L 213.232175 141.421854 \n", "L 214.452069 138.892404 \n", "L 215.062016 138.774521 \n", "L 215.671963 139.220624 \n", "L 216.28191 137.981091 \n", "L 216.891858 137.310216 \n", "L 218.721699 138.639022 \n", "L 219.331646 137.976478 \n", "L 219.941593 138.413057 \n", "L 220.551541 138.301457 \n", "L 222.381382 139.586124 \n", "L 224.211224 139.245298 \n", "L 225.431118 140.07736 \n", "L 226.041065 138.912525 \n", "L 226.651012 139.849845 \n", "L 227.260959 139.73743 \n", "L 227.870907 140.664252 \n", "L 229.090801 140.435722 \n", "L 230.920642 141.627299 \n", "L 231.53059 141.003785 \n", "L 232.140537 141.395827 \n", "L 232.750484 141.281384 \n", "L 233.360431 140.164585 \n", "L 233.970378 140.555355 \n", "L 234.580325 139.947468 \n", "L 235.190273 139.840123 \n", "L 236.410167 141.597237 \n", "L 237.630061 142.351101 \n", "L 238.240008 142.236956 \n", "L 238.849956 142.608818 \n", "L 239.459903 143.461419 \n", "L 240.06985 143.826183 \n", "L 240.679797 143.228396 \n", "L 241.289744 143.59137 \n", "L 241.899691 143.475237 \n", "L 242.509639 142.885085 \n", "L 243.119586 143.245415 \n", "L 244.33948 144.898127 \n", "L 244.949427 145.247601 \n", "L 246.779269 144.891585 \n", "L 247.389216 143.851175 \n", "L 247.999163 144.198243 \n", "L 248.60911 144.084416 \n", "L 250.438952 145.10906 \n", "L 251.048899 144.541375 \n", "L 252.268793 144.315988 \n", "L 252.87874 144.65218 \n", "L 253.488688 144.093602 \n", "L 254.098635 144.873055 \n", "L 254.708582 145.204059 \n", "L 255.318529 145.091027 \n", "L 255.928476 144.098036 \n", "L 256.538423 143.989398 \n", "L 257.148371 143.443982 \n", "L 257.758318 143.77423 \n", "L 258.368265 143.667719 \n", "L 258.978212 143.995084 \n", "L 260.198106 142.921848 \n", "L 260.808054 142.819433 \n", "L 261.418001 143.572979 \n", "L 262.027948 143.042853 \n", "L 262.637895 142.941034 \n", "L 263.247842 143.263449 \n", "L 263.857789 141.894838 \n", "L 266.297578 143.177397 \n", "L 266.907525 143.077388 \n", "L 268.737367 144.016478 \n", "L 269.347314 143.504564 \n", "L 269.957261 143.405072 \n", "L 270.567208 142.898148 \n", "L 271.177155 143.207917 \n", "L 271.787103 143.110234 \n", "L 272.39705 142.608818 \n", "L 273.006997 142.916639 \n", "L 273.616944 143.624515 \n", "L 274.836838 142.630609 \n", "L 275.446786 142.934692 \n", "L 276.056733 142.44274 \n", "L 276.66668 142.74559 \n", "L 278.496521 142.466102 \n", "L 279.106469 141.982758 \n", "L 281.546257 141.624357 \n", "L 282.156204 142.30732 \n", "L 282.766152 142.217292 \n", "L 283.376099 141.744387 \n", "L 283.986046 142.421127 \n", "L 285.20594 143.002769 \n", "L 285.815887 142.912066 \n", "L 286.425835 142.065887 \n", "L 287.645729 141.891259 \n", "L 288.255676 142.5543 \n", "L 288.865623 142.092351 \n", "L 289.47557 142.751117 \n", "L 290.085518 142.291195 \n", "L 290.695465 142.575117 \n", "L 291.305412 141.748622 \n", "L 291.915359 142.032465 \n", "L 292.525306 141.947189 \n", "L 293.135253 142.228923 \n", "L 293.745201 142.874667 \n", "L 294.355148 143.152345 \n", "L 294.965095 143.064922 \n", "L 295.575042 142.252822 \n", "L 296.184989 142.168322 \n", "L 296.794936 141.72365 \n", "L 297.404884 142.000672 \n", "L 298.014831 141.558864 \n", "L 299.234725 142.10935 \n", "L 299.844672 142.026579 \n", "L 300.454619 142.299109 \n", "L 301.064567 141.86235 \n", "L 302.284461 141.69987 \n", "L 302.894408 141.268142 \n", "L 303.504355 141.188871 \n", "L 304.72425 141.728362 \n", "L 305.334197 141.648534 \n", "L 305.944144 142.262246 \n", "L 306.554091 142.527101 \n", "L 307.164038 142.445821 \n", "L 307.773985 142.708847 \n", "L 308.99388 142.546606 \n", "L 310.213774 143.066771 \n", "L 310.823721 143.664399 \n", "L 311.433668 143.581668 \n", "L 312.653563 144.091355 \n", "L 313.26351 144.008187 \n", "L 313.873457 143.254919 \n", "L 314.483404 143.843083 \n", "L 315.703299 144.345115 \n", "L 316.313246 143.930426 \n", "L 316.923193 144.179991 \n", "L 317.53314 143.767768 \n", "L 319.972929 144.755331 \n", "L 321.192823 144.591022 \n", "L 321.80277 144.184823 \n", "L 323.022665 142.732169 \n", "L 324.242559 143.22261 \n", "L 324.852506 143.145327 \n", "L 325.462453 143.708359 \n", "L 326.0724 143.63028 \n", "L 326.682348 143.234122 \n", "L 327.292295 143.157555 \n", "L 327.902242 143.398272 \n", "L 328.512189 143.954027 \n", "L 329.122136 143.876286 \n", "L 329.732083 144.428304 \n", "L 330.342031 144.663772 \n", "L 330.951978 144.584898 \n", "L 331.561925 145.131388 \n", "L 332.171872 145.363556 \n", "L 333.391766 146.445242 \n", "L 334.611661 146.899199 \n", "L 335.221608 146.816468 \n", "L 336.441502 147.879048 \n", "L 337.051449 147.488596 \n", "L 337.661397 147.405268 \n", "L 338.271344 147.626938 \n", "L 338.881291 147.54367 \n", "L 341.32108 148.419116 \n", "L 341.931027 148.034489 \n", "L 343.150921 147.868729 \n", "L 343.760868 148.084911 \n", "L 344.370815 148.002299 \n", "L 344.980763 148.217209 \n", "L 345.59071 148.134657 \n", "L 346.200657 148.348334 \n", "L 346.810604 148.265861 \n", "L 348.030498 146.926418 \n", "L 348.640446 145.967864 \n", "L 349.250393 146.476557 \n", "L 349.86034 146.107121 \n", "L 350.470287 146.613269 \n", "L 351.080234 146.245145 \n", "L 351.690181 146.458743 \n", "L 352.300129 146.960855 \n", "L 352.910076 146.883154 \n", "L 353.520023 147.094008 \n", "L 354.739917 146.939064 \n", "L 355.349865 147.148426 \n", "L 355.959812 147.642605 \n", "L 356.569759 147.279352 \n", "L 357.179706 147.202149 \n", "L 357.789653 147.409165 \n", "L 358.3996 147.048715 \n", "L 359.009548 146.972506 \n", "L 359.619495 147.178687 \n", "L 360.839389 146.464846 \n", "L 361.449336 146.670809 \n", "L 362.059283 147.155624 \n", "L 362.669231 146.52185 \n", "L 363.279178 146.726401 \n", "L 363.889125 146.65212 \n", "L 364.499072 146.300736 \n", "L 365.718966 146.707373 \n", "L 366.328914 146.358058 \n", "L 367.548808 146.761931 \n", "L 368.158755 147.236605 \n", "L 369.378649 147.088699 \n", "L 369.988597 146.742883 \n", "L 370.598544 146.941986 \n", "L 371.208491 146.59788 \n", "L 371.818438 145.984525 \n", "L 372.428385 145.64374 \n", "L 373.038332 145.843459 \n", "L 373.64828 145.773354 \n", "L 374.258227 145.972 \n", "L 374.868174 145.366061 \n", "L 375.478121 145.297327 \n", "L 376.088068 144.962009 \n", "L 376.088068 144.962009 \n", "\" clip-path=\"url(#p469350dd24)\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 71.724432 111.071025 \n", "L 72.334379 177.785562 \n", "L 72.944326 200.023741 \n", "L 73.554273 244.500108 \n", "L 75.384115 244.500108 \n", "L 75.994062 227.821479 \n", "L 76.604009 214.849203 \n", "L 77.213956 217.814298 \n", "L 78.433851 177.785562 \n", "L 79.043798 182.917449 \n", "L 80.263692 208.919024 \n", "L 81.483586 166.012409 \n", "L 82.093534 177.785562 \n", "L 82.703481 181.296868 \n", "L 83.313428 171.114114 \n", "L 83.923375 180.962445 \n", "L 84.533322 183.850534 \n", "L 85.143269 180.686197 \n", "L 86.363164 185.791323 \n", "L 86.973111 188.049337 \n", "L 87.583058 195.08193 \n", "L 88.802952 189.288075 \n", "L 89.4129 191.128477 \n", "L 90.022847 179.937662 \n", "L 90.632794 181.955237 \n", "L 91.242741 175.763911 \n", "L 91.852688 173.861178 \n", "L 93.072583 162.960109 \n", "L 94.292477 160.229111 \n", "L 94.902424 152.126125 \n", "L 96.732266 149.193618 \n", "L 97.342213 151.410057 \n", "L 97.95216 156.558209 \n", "L 98.562107 155.547383 \n", "L 99.172054 157.481153 \n", "L 101.001896 154.639708 \n", "L 101.611843 153.768338 \n", "L 102.22179 155.547383 \n", "L 102.831737 146.994237 \n", "L 103.441685 143.798924 \n", "L 104.051632 143.192846 \n", "L 105.271526 146.810961 \n", "L 106.49142 154.780556 \n", "L 108.931209 151.960583 \n", "L 110.151103 154.852451 \n", "L 111.370998 161.612355 \n", "L 111.980945 162.849483 \n", "L 113.810786 160.630399 \n", "L 114.420734 161.811657 \n", "L 115.640628 156.765921 \n", "L 116.250575 156.148431 \n", "L 117.470469 158.473468 \n", "L 118.080417 157.857847 \n", "L 118.690364 153.836754 \n", "L 119.300311 148.228502 \n", "L 119.910258 149.431889 \n", "L 120.520205 152.252836 \n", "L 122.350047 150.782069 \n", "L 122.959994 151.884632 \n", "L 123.569941 154.513057 \n", "L 124.179888 152.480053 \n", "L 126.619677 156.524886 \n", "L 127.229624 151.679882 \n", "L 127.839571 154.112663 \n", "L 129.059466 150.397502 \n", "L 129.669413 149.987843 \n", "L 130.889307 151.916663 \n", "L 132.719149 150.703434 \n", "L 133.329096 151.622999 \n", "L 133.939043 151.229306 \n", "L 134.54899 152.126125 \n", "L 135.158937 151.735136 \n", "L 135.768884 150.092743 \n", "L 136.378832 150.975048 \n", "L 137.598726 150.242876 \n", "L 138.208673 151.099751 \n", "L 139.428567 150.384956 \n", "L 140.038515 152.398614 \n", "L 140.648462 152.036097 \n", "L 141.258409 152.840125 \n", "L 141.868356 151.329812 \n", "L 143.698198 153.678629 \n", "L 144.308145 152.211659 \n", "L 144.918092 151.871649 \n", "L 145.528039 152.630901 \n", "L 146.137986 152.293018 \n", "L 146.747933 153.036623 \n", "L 147.357881 151.633477 \n", "L 147.967828 153.429461 \n", "L 148.577775 152.045322 \n", "L 149.187722 151.725195 \n", "L 149.797669 153.47873 \n", "L 151.017564 150.794197 \n", "L 151.627511 152.514907 \n", "L 153.457352 151.59393 \n", "L 154.067299 149.333789 \n", "L 154.677247 149.0545 \n", "L 155.287194 147.812362 \n", "L 155.897141 149.467897 \n", "L 156.507088 148.240551 \n", "L 157.726982 151.47561 \n", "L 158.33693 150.259995 \n", "L 158.946877 150.914427 \n", "L 159.556824 152.480053 \n", "L 160.166771 152.196429 \n", "L 161.386665 149.837592 \n", "L 164.436401 152.931127 \n", "L 165.656296 150.669335 \n", "L 166.266243 152.126125 \n", "L 167.486137 149.917479 \n", "L 168.096084 149.673163 \n", "L 168.706031 151.099751 \n", "L 171.14582 153.377807 \n", "L 171.755767 150.695421 \n", "L 172.365714 152.06431 \n", "L 172.975662 152.617819 \n", "L 174.195556 152.126125 \n", "L 174.805503 152.669513 \n", "L 175.41545 152.426251 \n", "L 176.025397 151.410057 \n", "L 177.245292 152.480053 \n", "L 177.855239 151.480978 \n", "L 178.465186 152.009494 \n", "L 179.075133 149.516688 \n", "L 179.68508 147.801506 \n", "L 182.734816 150.44355 \n", "L 183.344763 150.229555 \n", "L 183.954711 150.739143 \n", "L 185.174605 153.168981 \n", "L 185.784552 153.65477 \n", "L 186.394499 152.723494 \n", "L 187.004446 151.099751 \n", "L 188.224341 150.682796 \n", "L 188.834288 151.168883 \n", "L 190.664129 150.55515 \n", "L 191.274077 151.709328 \n", "L 191.884024 150.830205 \n", "L 192.493971 150.630405 \n", "L 193.103918 149.765458 \n", "L 193.713865 149.572956 \n", "L 194.323812 150.042898 \n", "L 194.93376 149.850913 \n", "L 195.543707 150.314871 \n", "L 196.153654 149.472569 \n", "L 196.763601 149.933862 \n", "L 197.373548 149.101542 \n", "L 197.983495 148.918703 \n", "L 198.593443 147.46078 \n", "L 199.20339 147.287504 \n", "L 199.813337 144.586389 \n", "L 201.033231 143.018835 \n", "L 202.253126 145.204059 \n", "L 202.863073 144.428304 \n", "L 203.47302 144.274572 \n", "L 204.082967 144.734335 \n", "L 204.692914 145.799141 \n", "L 205.302861 144.428304 \n", "L 205.912809 144.277356 \n", "L 206.522756 144.728808 \n", "L 207.132703 143.979556 \n", "L 207.74265 143.832625 \n", "L 208.962544 144.723499 \n", "L 209.572492 144.575255 \n", "L 210.182439 145.013525 \n", "L 210.792386 144.282645 \n", "L 212.622227 143.853183 \n", "L 213.232175 143.139819 \n", "L 213.842122 143.572979 \n", "L 214.452069 143.434677 \n", "L 215.062016 144.428304 \n", "L 215.671963 144.287556 \n", "L 216.28191 145.269233 \n", "L 216.891858 144.567879 \n", "L 217.501805 145.540212 \n", "L 218.111752 144.843529 \n", "L 218.721699 145.255335 \n", "L 219.331646 145.114667 \n", "L 219.941593 145.521979 \n", "L 220.551541 146.470573 \n", "L 222.381382 147.656424 \n", "L 222.991329 146.973639 \n", "L 223.601276 146.830028 \n", "L 224.211224 145.624374 \n", "L 226.041065 146.792192 \n", "L 226.651012 147.698614 \n", "L 227.260959 148.076759 \n", "L 227.870907 147.93275 \n", "L 228.480854 147.272712 \n", "L 229.090801 147.132938 \n", "L 229.700748 146.481051 \n", "L 230.310695 146.345393 \n", "L 231.53059 145.062475 \n", "L 232.750484 144.805932 \n", "L 233.970378 145.552698 \n", "L 234.580325 145.424038 \n", "L 235.190273 145.792361 \n", "L 235.80022 145.663761 \n", "L 236.410167 146.028466 \n", "L 237.020114 145.899945 \n", "L 238.240008 146.619651 \n", "L 238.849956 146.490376 \n", "L 240.06985 145.271261 \n", "L 241.289744 145.02609 \n", "L 241.899691 144.428304 \n", "L 242.509639 145.259272 \n", "L 244.33948 142.079188 \n", "L 245.559374 142.795435 \n", "L 246.779269 141.648534 \n", "L 247.389216 142.00441 \n", "L 248.60911 141.791807 \n", "L 249.219057 140.77271 \n", "L 250.438952 139.66297 \n", "L 251.048899 139.566062 \n", "L 251.658846 140.371343 \n", "L 252.268793 140.721931 \n", "L 252.87874 141.517926 \n", "L 253.488688 141.416108 \n", "L 254.708582 139.44134 \n", "L 255.318529 140.231032 \n", "L 256.538423 139.161355 \n", "L 257.148371 139.069259 \n", "L 257.758318 139.413802 \n", "L 258.368265 138.886857 \n", "L 259.588159 138.706821 \n", "L 260.198106 139.048104 \n", "L 260.808054 138.529112 \n", "L 261.418001 138.441092 \n", "L 262.027948 137.501068 \n", "L 263.247842 137.33326 \n", "L 263.857789 137.672395 \n", "L 264.467737 137.167559 \n", "L 265.077684 137.505084 \n", "L 265.687631 137.003946 \n", "L 266.907525 136.842381 \n", "L 267.517472 137.176725 \n", "L 268.12742 136.682804 \n", "L 268.737367 135.78012 \n", "L 269.347314 135.704089 \n", "L 269.957261 136.037817 \n", "L 270.567208 135.55344 \n", "L 271.177155 136.292372 \n", "L 273.006997 136.063783 \n", "L 273.616944 135.586623 \n", "L 274.226891 135.91369 \n", "L 274.836838 135.83931 \n", "L 276.056733 136.486087 \n", "L 276.66668 136.410673 \n", "L 277.276627 137.125229 \n", "L 278.496521 136.971975 \n", "L 279.106469 137.678579 \n", "L 279.716416 137.210644 \n", "L 280.326363 137.134435 \n", "L 280.93631 137.44655 \n", "L 282.156204 137.294091 \n", "L 283.376099 138.677037 \n", "L 286.425835 140.175958 \n", "L 287.035782 139.339899 \n", "L 287.645729 139.26027 \n", "L 288.255676 139.930687 \n", "L 290.085518 140.804522 \n", "L 291.305412 140.639796 \n", "L 291.915359 139.820936 \n", "L 292.525306 139.374177 \n", "L 294.355148 140.235864 \n", "L 294.965095 140.156393 \n", "L 295.575042 140.802514 \n", "L 296.184989 141.083533 \n", "L 296.794936 141.002413 \n", "L 297.404884 141.281384 \n", "L 298.014831 141.200184 \n", "L 298.624778 141.834833 \n", "L 299.844672 142.382396 \n", "L 300.454619 141.589383 \n", "L 301.674514 141.427918 \n", "L 302.284461 142.05193 \n", "L 302.894408 141.970391 \n", "L 304.114302 141.110037 \n", "L 304.72425 141.379981 \n", "L 305.334197 141.996 \n", "L 305.944144 141.222532 \n", "L 306.554091 141.835767 \n", "L 307.164038 141.756277 \n", "L 307.773985 142.021072 \n", "L 308.383933 142.627527 \n", "L 308.99388 142.888724 \n", "L 310.213774 142.045647 \n", "L 310.823721 141.287786 \n", "L 311.433668 141.888416 \n", "L 312.043616 141.13479 \n", "L 313.26351 141.655532 \n", "L 315.093351 143.427579 \n", "L 316.313246 143.26661 \n", "L 316.923193 143.517805 \n", "L 318.143087 142.698667 \n", "L 318.753034 142.949405 \n", "L 319.362982 143.526752 \n", "L 319.972929 143.120176 \n", "L 320.582876 143.36805 \n", "L 321.192823 142.638403 \n", "L 321.80277 143.210879 \n", "L 323.022665 143.055239 \n", "L 323.632612 143.300271 \n", "L 324.852506 143.145327 \n", "L 325.462453 142.748433 \n", "L 326.0724 142.672661 \n", "L 326.682348 143.234122 \n", "L 327.902242 143.081325 \n", "L 328.512189 142.689302 \n", "L 329.732083 142.540144 \n", "L 330.342031 142.152157 \n", "L 330.951978 142.079188 \n", "L 331.561925 141.694104 \n", "L 332.781819 141.551328 \n", "L 334.001714 142.029045 \n", "L 335.221608 141.269753 \n", "L 336.441502 139.903985 \n", "L 337.051449 139.837856 \n", "L 337.661397 140.382676 \n", "L 338.271344 140.620391 \n", "L 338.881291 141.160956 \n", "L 340.101185 141.024502 \n", "L 340.711132 141.258599 \n", "L 341.931027 141.122622 \n", "L 342.540974 141.354929 \n", "L 343.150921 141.287031 \n", "L 343.760868 141.816441 \n", "L 344.370815 142.045647 \n", "L 344.980763 141.679491 \n", "L 345.59071 141.90798 \n", "L 346.810604 141.181136 \n", "L 347.420551 141.409208 \n", "L 348.030498 141.342383 \n", "L 348.640446 141.569103 \n", "L 349.250393 141.502219 \n", "L 349.86034 141.727606 \n", "L 350.470287 142.243318 \n", "L 351.690181 141.527668 \n", "L 352.300129 141.461599 \n", "L 352.910076 141.684621 \n", "L 354.739917 141.487128 \n", "L 355.349865 141.708181 \n", "L 355.959812 141.356858 \n", "L 356.569759 141.86235 \n", "L 357.179706 142.081197 \n", "L 357.789653 141.731324 \n", "L 359.009548 141.601412 \n", "L 360.229442 140.909601 \n", "L 360.839389 141.127692 \n", "L 362.059283 141.001657 \n", "L 362.669231 141.497328 \n", "L 363.279178 141.155249 \n", "L 363.889125 141.370557 \n", "L 366.328914 140.017415 \n", "L 366.938861 139.957747 \n", "L 367.548808 139.074667 \n", "L 368.158755 139.291147 \n", "L 368.768702 138.959885 \n", "L 369.988597 139.390679 \n", "L 370.598544 139.061246 \n", "L 371.818438 139.488978 \n", "L 372.428385 139.971665 \n", "L 373.038332 140.182837 \n", "L 374.258227 141.139562 \n", "L 374.868174 141.079179 \n", "L 376.088068 141.492854 \n", "L 376.088068 141.492854 \n", "\" clip-path=\"url(#p469350dd24)\" style=\"fill: none; stroke: #9467bd; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 71.724432 244.500108 \n", "L 72.944326 244.500108 \n", "L 73.554273 177.785562 \n", "L 74.16422 164.442667 \n", "L 74.774168 155.547383 \n", "L 75.384115 149.193618 \n", "L 75.994062 161.106943 \n", "L 76.604009 155.547383 \n", "L 77.213956 151.099751 \n", "L 77.823903 171.72061 \n", "L 79.043798 182.917449 \n", "L 79.653745 168.254914 \n", "L 80.263692 173.33793 \n", "L 80.873639 169.446252 \n", "L 82.093534 192.611015 \n", "L 83.313428 211.14284 \n", "L 83.923375 200.023741 \n", "L 84.533322 202.045412 \n", "L 85.143269 180.686197 \n", "L 85.753217 177.785562 \n", "L 86.973111 182.917449 \n", "L 87.583058 185.198288 \n", "L 88.193005 182.550896 \n", "L 89.4129 186.680845 \n", "L 90.632794 181.955237 \n", "L 91.242741 183.850534 \n", "L 92.462635 179.691696 \n", "L 93.072583 166.666483 \n", "L 93.68253 161.557718 \n", "L 94.902424 165.811158 \n", "L 95.512371 164.442667 \n", "L 97.342213 170.028073 \n", "L 97.95216 165.655657 \n", "L 98.562107 170.372836 \n", "L 99.172054 163.282404 \n", "L 99.782002 165.010451 \n", "L 100.391949 163.886713 \n", "L 102.22179 168.628665 \n", "L 102.831737 170.087741 \n", "L 103.441685 168.974222 \n", "L 105.271526 173.020248 \n", "L 105.881473 167.251704 \n", "L 106.49142 170.884074 \n", "L 107.711315 168.890298 \n", "L 108.321262 172.317164 \n", "L 108.931209 173.481402 \n", "L 109.541156 172.490758 \n", "L 110.761051 174.706441 \n", "L 111.370998 167.677308 \n", "L 113.810786 164.442667 \n", "L 115.030681 155.547383 \n", "L 116.250575 157.951513 \n", "L 116.860522 153.768338 \n", "L 117.470469 154.962182 \n", "L 118.080417 154.392151 \n", "L 118.690364 155.547383 \n", "L 119.300311 158.362345 \n", "L 120.520205 153.90012 \n", "L 121.130152 155.004989 \n", "L 121.7401 154.475678 \n", "L 122.350047 155.547383 \n", "L 122.959994 153.454394 \n", "L 123.569941 152.961547 \n", "L 124.179888 150.946378 \n", "L 124.789835 152.009494 \n", "L 126.619677 159.457393 \n", "L 127.229624 158.931461 \n", "L 127.839571 159.851563 \n", "L 128.449518 159.33261 \n", "L 130.889307 162.808844 \n", "L 131.499254 160.938459 \n", "L 132.109201 160.439786 \n", "L 132.719149 161.272067 \n", "L 133.329096 159.471768 \n", "L 136.988779 156.782841 \n", "L 137.598726 158.811709 \n", "L 140.038515 161.844921 \n", "L 140.648462 161.399533 \n", "L 141.868356 165.132807 \n", "L 142.478303 163.530319 \n", "L 143.08825 165.34726 \n", "L 143.698198 163.769903 \n", "L 145.528039 162.474042 \n", "L 146.137986 163.140921 \n", "L 147.357881 162.307805 \n", "L 147.967828 162.960109 \n", "L 148.577775 164.652785 \n", "L 149.187722 161.106943 \n", "L 149.797669 162.787728 \n", "L 150.407616 163.416293 \n", "L 151.627511 166.666483 \n", "L 153.457352 168.396119 \n", "L 154.067299 169.936793 \n", "L 154.677247 170.481056 \n", "L 155.897141 173.465914 \n", "L 156.507088 172.067181 \n", "L 157.117035 171.634578 \n", "L 158.946877 173.152625 \n", "L 159.556824 172.724476 \n", "L 160.166771 171.388294 \n", "L 161.386665 170.573192 \n", "L 162.60656 171.558885 \n", "L 163.216507 171.158293 \n", "L 165.046348 172.587029 \n", "L 165.656296 172.190154 \n", "L 166.266243 173.508999 \n", "L 168.706031 175.283769 \n", "L 169.315979 174.056185 \n", "L 169.925926 172.020119 \n", "L 171.14582 171.276833 \n", "L 171.755767 171.72061 \n", "L 172.365714 171.355249 \n", "L 172.975662 171.79324 \n", "L 173.585609 173.020248 \n", "L 174.195556 172.653675 \n", "L 174.805503 171.506555 \n", "L 176.025397 172.355318 \n", "L 176.635345 173.543614 \n", "L 177.245292 172.417729 \n", "L 177.855239 172.067181 \n", "L 178.465186 172.478729 \n", "L 179.075133 172.131799 \n", "L 180.295028 172.94038 \n", "L 180.904975 172.596652 \n", "L 182.124869 170.454294 \n", "L 182.734816 170.858923 \n", "L 183.344763 169.808829 \n", "L 183.954711 169.491326 \n", "L 184.564658 169.894602 \n", "L 185.784552 172.107741 \n", "L 186.394499 172.490758 \n", "L 187.614394 170.450457 \n", "L 188.224341 171.53109 \n", "L 188.834288 171.217821 \n", "L 190.054182 171.969439 \n", "L 191.274077 169.99656 \n", "L 192.493971 170.745334 \n", "L 193.103918 171.781271 \n", "L 193.713865 172.143053 \n", "L 194.323812 170.519628 \n", "L 194.93376 170.884074 \n", "L 195.543707 169.936793 \n", "L 196.153654 170.300523 \n", "L 196.763601 169.365291 \n", "L 197.373548 169.728266 \n", "L 197.983495 168.804764 \n", "L 198.593443 169.166943 \n", "L 199.20339 168.890298 \n", "L 200.423284 170.862362 \n", "L 201.033231 170.581661 \n", "L 201.643178 170.92706 \n", "L 202.253126 170.648665 \n", "L 202.863073 171.608293 \n", "L 203.47302 171.944208 \n", "L 204.692914 171.388294 \n", "L 206.522756 172.376274 \n", "L 207.132703 173.298046 \n", "L 207.74265 172.424589 \n", "L 208.352597 170.965871 \n", "L 208.962544 171.291227 \n", "L 209.572492 172.201527 \n", "L 210.792386 171.667643 \n", "L 212.622227 172.609436 \n", "L 213.232175 171.772662 \n", "L 213.842122 171.513255 \n", "L 214.452069 171.823839 \n", "L 215.062016 171.001043 \n", "L 215.671963 171.31117 \n", "L 216.28191 170.497419 \n", "L 216.891858 171.365349 \n", "L 217.501805 170.55816 \n", "L 219.941593 171.770316 \n", "L 221.771435 171.033093 \n", "L 222.381382 170.253282 \n", "L 222.991329 170.55146 \n", "L 223.601276 169.779821 \n", "L 224.211224 169.545923 \n", "L 225.431118 170.138441 \n", "L 226.041065 170.956526 \n", "L 226.651012 170.721674 \n", "L 229.090801 171.861119 \n", "L 230.310695 170.372836 \n", "L 232.140537 171.215197 \n", "L 232.750484 170.988238 \n", "L 233.360431 171.264605 \n", "L 234.580325 169.819645 \n", "L 235.190273 170.593293 \n", "L 236.410167 169.169309 \n", "L 237.630061 168.743665 \n", "L 238.240008 169.020151 \n", "L 238.849956 168.809436 \n", "L 239.459903 169.083675 \n", "L 240.06985 168.39254 \n", "L 241.289744 167.981649 \n", "L 241.899691 167.301847 \n", "L 242.509639 167.10175 \n", "L 243.729533 167.648737 \n", "L 244.33948 167.449514 \n", "L 245.559374 167.98833 \n", "L 246.169322 167.790002 \n", "L 246.779269 168.519669 \n", "L 247.389216 168.782575 \n", "L 247.999163 168.123471 \n", "L 248.60911 167.92741 \n", "L 249.219057 168.18964 \n", "L 249.829005 167.083915 \n", "L 250.438952 166.439564 \n", "L 251.658846 166.966986 \n", "L 252.268793 165.880289 \n", "L 252.87874 165.248603 \n", "L 254.708582 166.038494 \n", "L 255.318529 165.85647 \n", "L 255.928476 166.556393 \n", "L 256.538423 165.496041 \n", "L 257.148371 165.317615 \n", "L 257.758318 165.576366 \n", "L 258.978212 166.955295 \n", "L 260.808054 167.703314 \n", "L 261.418001 166.666483 \n", "L 262.027948 166.488852 \n", "L 262.637895 167.162232 \n", "L 263.247842 165.713416 \n", "L 264.467737 165.368673 \n", "L 265.077684 164.358743 \n", "L 266.297578 164.859622 \n", "L 266.907525 164.276389 \n", "L 267.517472 164.525537 \n", "L 268.12742 163.946957 \n", "L 269.347314 164.442667 \n", "L 270.567208 164.116236 \n", "L 271.177155 163.547716 \n", "L 271.787103 163.388199 \n", "L 272.39705 163.634006 \n", "L 273.006997 164.28142 \n", "L 273.616944 164.121147 \n", "L 274.226891 164.763212 \n", "L 275.446786 163.646075 \n", "L 276.056733 163.4896 \n", "L 276.66668 162.938119 \n", "L 279.716416 164.130551 \n", "L 280.326363 164.753867 \n", "L 280.93631 164.985697 \n", "L 281.546257 165.602909 \n", "L 282.156204 164.674039 \n", "L 283.376099 164.36598 \n", "L 283.986046 164.977903 \n", "L 284.595993 164.442667 \n", "L 285.20594 164.670739 \n", "L 287.645729 164.066808 \n", "L 288.255676 163.543143 \n", "L 288.865623 163.769903 \n", "L 290.695465 163.330759 \n", "L 291.305412 163.55559 \n", "L 291.915359 164.14779 \n", "L 292.525306 164.001574 \n", "L 293.745201 162.980429 \n", "L 296.184989 162.417715 \n", "L 296.794936 163.000193 \n", "L 298.624778 162.582521 \n", "L 299.234725 162.088024 \n", "L 299.844672 162.663602 \n", "L 300.454619 162.881256 \n", "L 301.064567 163.451684 \n", "L 301.674514 163.666097 \n", "L 302.284461 163.527317 \n", "L 302.894408 163.740397 \n", "L 303.504355 163.251964 \n", "L 305.334197 162.844294 \n", "L 307.773985 163.686098 \n", "L 308.383933 162.864833 \n", "L 308.99388 162.732037 \n", "L 310.823721 163.356208 \n", "L 311.433668 162.884854 \n", "L 313.873457 162.364112 \n", "L 314.483404 161.566745 \n", "L 316.313246 162.185647 \n", "L 316.923193 162.058817 \n", "L 317.53314 162.593158 \n", "L 318.143087 162.46593 \n", "L 318.753034 161.682063 \n", "L 319.362982 161.557718 \n", "L 319.972929 161.760997 \n", "L 320.582876 162.289533 \n", "L 321.192823 162.164611 \n", "L 321.80277 161.715645 \n", "L 322.412717 161.916577 \n", "L 323.022665 162.439606 \n", "L 323.632612 161.993244 \n", "L 325.462453 162.586816 \n", "L 326.0724 163.101991 \n", "L 326.682348 162.977805 \n", "L 327.292295 163.171918 \n", "L 327.902242 162.414296 \n", "L 328.512189 162.292615 \n", "L 329.122136 162.802402 \n", "L 330.342031 161.931052 \n", "L 331.561925 160.442908 \n", "L 333.391766 161.029361 \n", "L 334.001714 160.603876 \n", "L 335.221608 160.991386 \n", "L 335.831555 161.491231 \n", "L 336.441502 161.068589 \n", "L 337.661397 160.839762 \n", "L 338.271344 160.421514 \n", "L 338.881291 160.916966 \n", "L 340.101185 160.690922 \n", "L 340.711132 160.880521 \n", "L 341.32108 160.768086 \n", "L 341.931027 160.956671 \n", "L 343.150921 160.732973 \n", "L 343.760868 160.323374 \n", "L 346.200657 159.886556 \n", "L 346.810604 160.368945 \n", "L 347.420551 159.965569 \n", "L 348.030498 160.445671 \n", "L 348.640446 160.630399 \n", "L 349.250393 160.229111 \n", "L 350.470287 160.597116 \n", "L 351.080234 160.489214 \n", "L 351.690181 159.801658 \n", "L 353.520023 159.485904 \n", "L 354.739917 160.425451 \n", "L 356.569759 159.253756 \n", "L 357.789653 159.616512 \n", "L 359.009548 158.84543 \n", "L 359.619495 159.026519 \n", "L 360.229442 159.48833 \n", "L 360.839389 159.667312 \n", "L 362.669231 159.362315 \n", "L 363.279178 159.540044 \n", "L 363.889125 159.995035 \n", "L 366.328914 160.693407 \n", "L 366.938861 161.14132 \n", "L 367.548808 161.312846 \n", "L 368.158755 161.209676 \n", "L 368.768702 160.833519 \n", "L 369.988597 160.630399 \n", "L 370.598544 160.80121 \n", "L 371.208491 160.42895 \n", "L 371.818438 160.870123 \n", "L 372.428385 160.769318 \n", "L 373.038332 160.938459 \n", "L 373.64828 160.299913 \n", "L 374.868174 159.566329 \n", "L 375.478121 159.469143 \n", "L 376.088068 159.639218 \n", "L 376.088068 159.639218 \n", "\" clip-path=\"url(#p469350dd24)\" style=\"fill: none; stroke: #8c564b; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 56.50625 155.102626 \n", "L 391.30625 155.102626 \n", "\" clip-path=\"url(#p469350dd24)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 56.50625 255.619199 \n", "L 56.50625 10.999199 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 391.30625 255.619199 \n", "L 391.30625 10.999199 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 56.50625 255.619199 \n", "L 391.30625 255.619199 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 56.50625 10.999199 \n", "L 391.30625 10.999199 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 308.451563 107.067949 \n", "L 384.30625 107.067949 \n", "Q 386.30625 107.067949 386.30625 105.067949 \n", "L 386.30625 17.999199 \n", "Q 386.30625 15.999199 384.30625 15.999199 \n", "L 308.451563 15.999199 \n", "Q 306.451563 15.999199 306.451563 17.999199 \n", "L 306.451563 105.067949 \n", "Q 306.451563 107.067949 308.451563 107.067949 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_22\">\n", "     <path d=\"M 310.451563 24.097637 \n", "L 320.451563 24.097637 \n", "L 330.451563 24.097637 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- P(die=1) -->\n", "     <g transform=\"translate(338.451563 27.597637)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"162.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"252.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"335.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"399.511719\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_23\">\n", "     <path d=\"M 310.451563 38.775762 \n", "L 320.451563 38.775762 \n", "L 330.451563 38.775762 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- P(die=2) -->\n", "     <g transform=\"translate(338.451563 42.275762)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"162.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"252.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"335.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"399.511719\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_24\">\n", "     <path d=\"M 310.451563 53.453887 \n", "L 320.451563 53.453887 \n", "L 330.451563 53.453887 \n", "\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- P(die=3) -->\n", "     <g transform=\"translate(338.451563 56.953887)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"162.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"252.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-33\" x=\"335.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"399.511719\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_25\">\n", "     <path d=\"M 310.451563 68.132012 \n", "L 320.451563 68.132012 \n", "L 330.451563 68.132012 \n", "\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_20\">\n", "     <!-- P(die=4) -->\n", "     <g transform=\"translate(338.451563 71.632012)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"162.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"252.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-34\" x=\"335.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"399.511719\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 310.451563 82.810137 \n", "L 320.451563 82.810137 \n", "L 330.451563 82.810137 \n", "\" style=\"fill: none; stroke: #9467bd; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- P(die=5) -->\n", "     <g transform=\"translate(338.451563 86.310137)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"162.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"252.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-35\" x=\"335.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"399.511719\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 310.451563 97.488262 \n", "L 320.451563 97.488262 \n", "L 330.451563 97.488262 \n", "\" style=\"fill: none; stroke: #8c564b; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_22\">\n", "     <!-- P(die=6) -->\n", "     <g transform=\"translate(338.451563 100.988262)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"162.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"252.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"335.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"399.511719\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p469350dd24\">\n", "   <rect x=\"56.50625\" y=\"10.999199\" width=\"334.8\" height=\"244.62\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 432x324 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["counts = multinomial.Multinomial(10, fair_probs).sample((500,))\n", "cum_counts = counts.cumsum(dim=0)\n", "estimates = cum_counts / cum_counts.sum(dim=1, keepdims=True)\n", "\n", "d2l.set_figsize((6, 4.5))\n", "for i in range(6):\n", "    d2l.plt.plot(estimates[:, i].numpy(),\n", "                 label=(\"P(die=\" + str(i + 1) + \")\"))\n", "d2l.plt.axhline(y=0.167, color='black', linestyle='dashed')\n", "d2l.plt.gca().set_xlabel('Groups of experiments')\n", "d2l.plt.gca().set_ylabel('Estimated probability')\n", "d2l.plt.legend();"]}, {"cell_type": "markdown", "id": "d97d4cca", "metadata": {"origin_pos": 25}, "source": ["每条实线对应于骰子的6个值中的一个，并给出骰子在每组实验后出现值的估计概率。\n", "当我们通过更多的实验获得更多的数据时，这$6$条实体曲线向真实概率收敛。\n", "\n", "### 概率论公理\n", "\n", "在处理骰子掷出时，我们将集合$\\mathcal{S} = \\{1, 2, 3, 4, 5, 6\\}$\n", "称为*样本空间*（sample space）或*结果空间*（outcome space），\n", "其中每个元素都是*结果*（outcome）。\n", "*事件*（event）是一组给定样本空间的随机结果。\n", "例如，“看到$5$”（$\\{5\\}$）和“看到奇数”（$\\{1, 3, 5\\}$）都是掷出骰子的有效事件。\n", "注意，如果一个随机实验的结果在$\\mathcal{A}$中，则事件$\\mathcal{A}$已经发生。\n", "也就是说，如果投掷出$3$点，因为$3 \\in \\{1, 3, 5\\}$，我们可以说，“看到奇数”的事件发生了。\n", "\n", "*概率*（probability）可以被认为是将集合映射到真实值的函数。\n", "在给定的样本空间$\\mathcal{S}$中，事件$\\mathcal{A}$的概率，\n", "表示为$P(\\mathcal{A})$，满足以下属性：\n", "\n", "* 对于任意事件$\\mathcal{A}$，其概率从不会是负数，即$P(\\mathcal{A}) \\geq 0$；\n", "* 整个样本空间的概率为$1$，即$P(\\mathcal{S}) = 1$；\n", "* 对于*互斥*（mutually exclusive）事件（对于所有$i \\neq j$都有$\\mathcal{A}_i \\cap \\mathcal{A}_j = \\emptyset$）的任意一个可数序列$\\mathcal{A}_1, \\mathcal{A}_2, \\ldots$，序列中任意一个事件发生的概率等于它们各自发生的概率之和，即$P(\\bigcup_{i=1}^{\\infty} \\mathcal{A}_i) = \\sum_{i=1}^{\\infty} P(\\mathcal{A}_i)$。\n", "\n", "以上也是概率论的公理，由科尔莫戈罗夫于1933年提出。\n", "有了这个公理系统，我们可以避免任何关于随机性的哲学争论；\n", "相反，我们可以用数学语言严格地推理。\n", "例如，假设事件$\\mathcal{A}_1$为整个样本空间，\n", "且当所有$i > 1$时的$\\mathcal{A}_i = \\emptyset$，\n", "那么我们可以证明$P(\\emptyset) = 0$，即不可能发生事件的概率是$0$。\n", "\n", "### 随机变量\n", "\n", "在我们掷骰子的随机实验中，我们引入了*随机变量*（random variable）的概念。\n", "随机变量几乎可以是任何数量，并且它可以在随机实验的一组可能性中取一个值。\n", "考虑一个随机变量$X$，其值在掷骰子的样本空间$\\mathcal{S}=\\{1,2,3,4,5,6\\}$中。\n", "我们可以将事件“看到一个$5$”表示为$\\{X=5\\}$或$X=5$，\n", "其概率表示为$P(\\{X=5\\})$或$P(X=5)$。\n", "通过$P(X=a)$，我们区分了随机变量$X$和$X$可以采取的值（例如$a$）。\n", "然而，这可能会导致繁琐的表示。\n", "为了简化符号，一方面，我们可以将$P(X)$表示为随机变量$X$上的*分布*（distribution）：\n", "分布告诉我们$X$获得某一值的概率。\n", "另一方面，我们可以简单用$P(a)$表示随机变量取值$a$的概率。\n", "由于概率论中的事件是来自样本空间的一组结果，因此我们可以为随机变量指定值的可取范围。\n", "例如，$P(1 \\leq X \\leq 3)$表示事件$\\{1 \\leq X \\leq 3\\}$，\n", "即$\\{X = 1, 2, \\text{or}, 3\\}$的概率。\n", "等价地，$P(1 \\leq X \\leq 3)$表示随机变量$X$从$\\{1, 2, 3\\}$中取值的概率。\n", "\n", "请注意，*离散*（discrete）随机变量（如骰子的每一面）\n", "和*连续*（continuous）随机变量（如人的体重和身高）之间存在微妙的区别。\n", "现实生活中，测量两个人是否具有完全相同的身高没有太大意义。\n", "如果我们进行足够精确的测量，最终会发现这个星球上没有两个人具有完全相同的身高。\n", "在这种情况下，询问某人的身高是否落入给定的区间，比如是否在1.79米和1.81米之间更有意义。\n", "在这些情况下，我们将这个看到某个数值的可能性量化为*密度*（density）。\n", "高度恰好为1.80米的概率为0，但密度不是0。\n", "在任何两个不同高度之间的区间，我们都有非零的概率。\n", "在本节的其余部分中，我们将考虑离散空间中的概率。\n", "连续随机变量的概率可以参考深度学习数学附录中[随机变量](https://d2l.ai/chapter_appendix-mathematics-for-deep-learning/random-variables.html)\n", "的一节。\n", "\n", "## 处理多个随机变量\n", "\n", "很多时候，我们会考虑多个随机变量。\n", "比如，我们可能需要对疾病和症状之间的关系进行建模。\n", "给定一个疾病和一个症状，比如“流感”和“咳嗽”，以某个概率存在或不存在于某个患者身上。\n", "我们需要估计这些概率以及概率之间的关系，以便我们可以运用我们的推断来实现更好的医疗服务。\n", "\n", "再举一个更复杂的例子：图像包含数百万像素，因此有数百万个随机变量。\n", "在许多情况下，图像会附带一个*标签*（label），标识图像中的对象。\n", "我们也可以将标签视为一个随机变量。\n", "我们甚至可以将所有元数据视为随机变量，例如位置、时间、光圈、焦距、ISO、对焦距离和相机类型。\n", "所有这些都是联合发生的随机变量。\n", "当我们处理多个随机变量时，会有若干个变量是我们感兴趣的。\n", "\n", "### 联合概率\n", "\n", "第一个被称为*联合概率*（joint probability）$P(A=a,B=b)$。\n", "给定任意值$a$和$b$，联合概率可以回答：$A=a$和$B=b$同时满足的概率是多少？\n", "请注意，对于任何$a$和$b$的取值，$P(A = a, B=b) \\leq P(A=a)$。\n", "这点是确定的，因为要同时发生$A=a$和$B=b$，$A=a$就必须发生，$B=b$也必须发生（反之亦然）。因此，$A=a$和$B=b$同时发生的可能性不大于$A=a$或是$B=b$单独发生的可能性。\n", "\n", "### 条件概率\n", "\n", "联合概率的不等式带给我们一个有趣的比率：\n", "$0 \\leq \\frac{P(A=a, B=b)}{P(A=a)} \\leq 1$。\n", "我们称这个比率为*条件概率*（conditional probability），\n", "并用$P(B=b \\mid A=a)$表示它：它是$B=b$的概率，前提是$A=a$已发生。\n", "\n", "### 贝叶斯定理\n", "\n", "使用条件概率的定义，我们可以得出统计学中最有用的方程之一：\n", "*Bay<PERSON>定理*（<PERSON><PERSON>' theorem）。\n", "根据*乘法法则*（multiplication rule ）可得到$P(A, B) = P(B \\mid A) P(A)$。\n", "根据对称性，可得到$P(A, B) = P(A \\mid B) P(B)$。\n", "假设$P(B)>0$，求解其中一个条件变量，我们得到\n", "\n", "$$P(A \\mid B) = \\frac{P(B \\mid A) P(A)}{P(B)}.$$\n", "\n", "请注意，这里我们使用紧凑的表示法：\n", "其中$P(A, B)$是一个*联合分布*（joint distribution），\n", "$P(A \\mid B)$是一个*条件分布*（conditional distribution）。\n", "这种分布可以在给定值$A = a, B=b$上进行求值。\n", "\n", "### 边际化\n", "\n", "为了能进行事件概率求和，我们需要*求和法则*（sum rule），\n", "即$B$的概率相当于计算$A$的所有可能选择，并将所有选择的联合概率聚合在一起：\n", "\n", "$$P(B) = \\sum_{A} P(A, B),$$\n", "\n", "这也称为*边际化*（marginalization）。\n", "边际化结果的概率或分布称为*边际概率*（marginal probability）\n", "或*边际分布*（marginal distribution）。\n", "\n", "### 独立性\n", "\n", "另一个有用属性是*依赖*（dependence）与*独立*（independence）。\n", "如果两个随机变量$A$和$B$是独立的，意味着事件$A$的发生跟$B$事件的发生无关。\n", "在这种情况下，统计学家通常将这一点表述为$A \\perp  B$。\n", "根据贝叶斯定理，马上就能同样得到$P(A \\mid B) = P(A)$。\n", "在所有其他情况下，我们称$A$和$B$依赖。\n", "比如，两次连续抛出一个骰子的事件是相互独立的。\n", "相比之下，灯开关的位置和房间的亮度并不是（因为可能存在灯泡坏掉、电源故障，或者开关故障）。\n", "\n", "由于$P(A \\mid B) = \\frac{P(A, B)}{P(B)} = P(A)$等价于$P(A, B) = P(A)P(B)$，\n", "因此两个随机变量是独立的，当且仅当两个随机变量的联合分布是其各自分布的乘积。\n", "同样地，给定另一个随机变量$C$时，两个随机变量$A$和$B$是*条件独立的*（conditionally independent），\n", "当且仅当$P(A, B \\mid C) = P(A \\mid C)P(B \\mid C)$。\n", "这个情况表示为$A \\perp B \\mid C$。\n", "\n", "### 应用\n", ":label:`subsec_probability_hiv_app`\n", "\n", "我们实战演练一下！\n", "假设一个医生对患者进行艾滋病病毒（HIV）测试。\n", "这个测试是相当准确的，如果患者健康但测试显示他患病，这个概率只有1%；\n", "如果患者真正感染HIV，它永远不会检测不出。\n", "我们使用$D_1$来表示诊断结果（如果阳性，则为$1$，如果阴性，则为$0$），\n", "$H$来表示感染艾滋病病毒的状态（如果阳性，则为$1$，如果阴性，则为$0$）。\n", "在 :numref:`conditional_prob_D1`中列出了这样的条件概率。\n", "\n", ":条件概率为$P(D_1 \\mid H)$\n", "\n", "| 条件概率 | $H=1$ | $H=0$ |\n", "|---|---|---|\n", "|$P(D_1 = 1 \\mid H)$|            1 |         0.01 |\n", "|$P(D_1 = 0 \\mid H)$|            0 |         0.99 |\n", ":label:`conditional_prob_D1`\n", "\n", "请注意，每列的加和都是1（但每行的加和不是），因为条件概率需要总和为1，就像概率一样。\n", "让我们计算如果测试出来呈阳性，患者感染HIV的概率，即$P(H = 1 \\mid D_1 = 1)$。\n", "显然，这将取决于疾病有多常见，因为它会影响错误警报的数量。\n", "假设人口总体是相当健康的，例如，$P(H=1) = 0.0015$。\n", "为了应用贝叶斯定理，我们需要运用边际化和乘法法则来确定\n", "\n", "$$\\begin{aligned}\n", "&P(D_1 = 1) \\\\\n", "=& P(D_1=1, H=0) + P(D_1=1, H=1)  \\\\\n", "=& P(D_1=1 \\mid H=0) P(H=0) + P(D_1=1 \\mid H=1) P(H=1) \\\\\n", "=& 0.011485.\n", "\\end{aligned}\n", "$$\n", "因此，我们得到\n", "\n", "$$\\begin{aligned}\n", "&P(H = 1 \\mid D_1 = 1)\\\\ =& \\frac{P(D_1=1 \\mid H=1) P(H=1)}{P(D_1=1)} \\\\ =& 0.1306 \\end{aligned}.$$\n", "\n", "换句话说，尽管使用了非常准确的测试，患者实际上患有艾滋病的几率只有13.06%。\n", "正如我们所看到的，概率可能是违反直觉的。\n", "\n", "患者在收到这样可怕的消息后应该怎么办？\n", "很可能，患者会要求医生进行另一次测试来确定病情。\n", "第二个测试具有不同的特性，它不如第一个测试那么精确，\n", "如 :numref:`conditional_prob_D2`所示。\n", "\n", ":条件概率为$P(D_2 \\mid H)$\n", "\n", "| 条件概率 | $H=1$ | $H=0$ |\n", "|---|---|---|\n", "|$P(D_2 = 1 \\mid H)$|            0.98 |         0.03 |\n", "|$P(D_2 = 0 \\mid H)$|            0.02 |         0.97 |\n", ":label:`conditional_prob_D2`\n", "\n", "不幸的是，第二次测试也显示阳性。让我们通过假设条件独立性来计算出应用Bayes定理的必要概率：\n", "\n", "$$\\begin{aligned}\n", "&P(D_1 = 1, D_2 = 1 \\mid H = 0) \\\\\n", "=& P(D_1 = 1 \\mid H = 0) P(D_2 = 1 \\mid H = 0)  \\\\\n", "=& 0.0003,\n", "\\end{aligned}\n", "$$\n", "\n", "$$\\begin{aligned}\n", "&P(D_1 = 1, D_2 = 1 \\mid H = 1) \\\\\n", "=& P(D_1 = 1 \\mid H = 1) P(D_2 = 1 \\mid H = 1)  \\\\\n", "=& 0.98.\n", "\\end{aligned}\n", "$$\n", "现在我们可以应用边际化和乘法规则：\n", "\n", "$$\\begin{aligned}\n", "&P(D_1 = 1, D_2 = 1) \\\\\n", "=& P(D_1 = 1, D_2 = 1, H = 0) + P(D_1 = 1, D_2 = 1, H = 1)  \\\\\n", "=& P(D_1 = 1, D_2 = 1 \\mid H = 0)P(H=0) + P(D_1 = 1, D_2 = 1 \\mid H = 1)P(H=1)\\\\\n", "=& 0.00176955.\n", "\\end{aligned}\n", "$$\n", "\n", "最后，鉴于存在两次阳性检测，患者患有艾滋病的概率为\n", "\n", "$$\\begin{aligned}\n", "&P(H = 1 \\mid D_1 = 1, D_2 = 1)\\\\\n", "=& \\frac{P(D_1 = 1, D_2 = 1 \\mid H=1) P(H=1)}{P(D_1 = 1, D_2 = 1)} \\\\\n", "=& 0.8307.\n", "\\end{aligned}\n", "$$\n", "\n", "也就是说，第二次测试使我们能够对患病的情况获得更高的信心。\n", "尽管第二次检验比第一次检验的准确性要低得多，但它仍然显著提高我们的预测概率。\n", "\n", "## 期望和方差\n", "\n", "为了概括概率分布的关键特征，我们需要一些测量方法。\n", "一个随机变量$X$的*期望*（expectation，或平均值（average））表示为\n", "\n", "$$E[X] = \\sum_{x} x P(X = x).$$\n", "\n", "当函数$f(x)$的输入是从分布$P$中抽取的随机变量时，$f(x)$的期望值为\n", "\n", "$$E_{x \\sim P}[f(x)] = \\sum_x f(x) P(x).$$\n", "\n", "在许多情况下，我们希望衡量随机变量$X$与其期望值的偏置。这可以通过方差来量化\n", "\n", "$$\\mathrm{Var}[X] = E\\left[(X - E[X])^2\\right] =\n", "E[X^2] - E[X]^2.$$\n", "\n", "方差的平方根被称为*标准差*（standard deviation）。\n", "随机变量函数的方差衡量的是：当从该随机变量分布中采样不同值$x$时，\n", "函数值偏离该函数的期望的程度：\n", "\n", "$$\\mathrm{Var}[f(x)] = E\\left[\\left(f(x) - E[f(x)]\\right)^2\\right].$$\n", "\n", "## 小结\n", "\n", "* 我们可以从概率分布中采样。\n", "* 我们可以使用联合分布、条件分布、Bayes定理、边缘化和独立性假设来分析多个随机变量。\n", "* 期望和方差为概率分布的关键特征的概括提供了实用的度量形式。\n", "\n", "## 练习\n", "\n", "1. 进行$m=500$组实验，每组抽取$n=10$个样本。改变$m$和$n$，观察和分析实验结果。\n", "2. 给定两个概率为$P(\\mathcal{A})$和$P(\\mathcal{B})$的事件，计算$P(\\mathcal{A} \\cup \\mathcal{B})$和$P(\\mathcal{A} \\cap \\mathcal{B})$的上限和下限。（提示：使用[友元图](https://en.wikipedia.org/wiki/Venn_diagram)来展示这些情况。)\n", "3. 假设我们有一系列随机变量，例如$A$、$B$和$C$，其中$B$只依赖于$A$，而$C$只依赖于$B$，能简化联合概率$P(A, B, C)$吗？（提示：这是一个[马尔可夫链](https://en.wikipedia.org/wiki/Markov_chain)。)\n", "4. 在 :numref:`subsec_probability_hiv_app`中，第一个测试更准确。为什么不运行第一个测试两次，而是同时运行第一个和第二个测试?\n"]}, {"cell_type": "markdown", "id": "2594045e", "metadata": {"origin_pos": 27, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1762)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
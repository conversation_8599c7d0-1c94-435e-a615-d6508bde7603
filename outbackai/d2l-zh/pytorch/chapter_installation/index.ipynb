{"cells": [{"cell_type": "markdown", "id": "6262ada7", "metadata": {"origin_pos": 0}, "source": ["# 安装\n", ":label:`chap_installation`\n", "\n", "我们需要配置一个环境来运行 Python、Jupyter Notebook、相关库以及运行本书所需的代码，以快速入门并获得动手学习经验。\n", "\n", "## 安装 Miniconda\n", "\n", "最简单的方法就是安装依赖Python 3.x的[Miniconda](https://conda.io/en/latest/miniconda.html)。\n", "如果已安装conda，则可以跳过以下步骤。访问Miniconda网站，根据Python3.x版本确定适合的版本。\n", "\n", "如果我们使用macOS，假设Python版本是3.9（我们的测试版本），将下载名称包含字符串“MacOSX”的bash脚本，并执行以下操作：\n", "\n", "```bash\n", "# 以Intel处理器为例，文件名可能会更改\n", "sh Miniconda3-py39_4.12.0-MacOSX-x86_64.sh -b\n", "```\n", "\n", "如果我们使用Linux，假设Python版本是3.9（我们的测试版本），将下载名称包含字符串“Linux”的bash脚本，并执行以下操作：\n", "\n", "```bash\n", "# 文件名可能会更改\n", "sh Miniconda3-py39_4.12.0-Linux-x86_64.sh -b\n", "```\n", "\n", "接下来，初始化终端Shell，以便我们可以直接运行`conda`。\n", "\n", "```bash\n", "~/miniconda3/bin/conda init\n", "```\n", "\n", "现在关闭并重新打开当前的shell。并使用下面的命令创建一个新的环境：\n", "\n", "```bash\n", "conda create --name d2l python=3.9 -y\n", "```\n", "\n", "现在激活 `d2l` 环境：\n", "\n", "```bash\n", "conda activate d2l\n", "```\n", "\n", "## 安装深度学习框架和`d2l`软件包\n", "\n", "在安装深度学习框架之前，请先检查计算机上是否有可用的GPU。\n", "例如可以查看计算机是否装有NVIDIA GPU并已安装[CUDA](https://developer.nvidia.com/cuda-downloads)。\n", "如果机器没有任何GPU，没有必要担心，因为CPU在前几章完全够用。\n", "但是，如果想流畅地学习全部章节，请提早获取GPU并且安装深度学习框架的GPU版本。\n"]}, {"cell_type": "markdown", "id": "ed0912e5", "metadata": {"origin_pos": 2, "tab": ["pytorch"]}, "source": ["我们可以按如下方式安装PyTorch的CPU或GPU版本：\n", "\n", "```bash\n", "pip install torch==1.12.0\n", "pip install torchvision==0.13.0\n", "```\n"]}, {"cell_type": "markdown", "id": "4e508102", "metadata": {"origin_pos": 5}, "source": ["我们的下一步是安装`d2l`包，以方便调取本书中经常使用的函数和类：\n", "\n", "```bash\n", "pip install d2l==0.17.6\n", "```\n", "\n", "## 下载 D2L Notebook\n", "\n", "接下来，需要下载这本书的代码。\n", "可以点击本书HTML页面顶部的“Jupyter 记事本”选项下载后解压代码，或者可以按照如下方式进行下载：\n"]}, {"cell_type": "markdown", "id": "d6f62ffb", "metadata": {"origin_pos": 7, "tab": ["pytorch"]}, "source": ["```bash\n", "mkdir d2l-zh && cd d2l-zh\n", "curl https://zh-v2.d2l.ai/d2l-zh-2.0.0.zip -o d2l-zh.zip\n", "unzip d2l-zh.zip && rm d2l-zh.zip\n", "cd pytorch\n", "```\n", "\n", "\n", "注意：如果没有安装`unzip`，则可以通过运行`sudo apt install unzip`进行安装。\n"]}, {"cell_type": "markdown", "id": "668ab9cb", "metadata": {"origin_pos": 10}, "source": ["安装完成后我们可以通过运行以下命令打开Jupyter笔记本（在Window系统的命令行窗口中运行以下命令前，需先将当前路径定位到刚下载的本书代码解压后的目录）：\n", "\n", "```bash\n", "jupyter notebook\n", "```\n", "\n", "现在可以在Web浏览器中打开<http://localhost:8888>（通常会自动打开）。\n", "由此，我们可以运行这本书中每个部分的代码。\n", "在运行书籍代码、更新深度学习框架或`d2l`软件包之前，请始终执行`conda activate d2l`以激活运行时环境。\n", "要退出环境，请运行`conda deactivate`。\n"]}, {"cell_type": "markdown", "id": "04be90e9", "metadata": {"origin_pos": 12, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2083)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
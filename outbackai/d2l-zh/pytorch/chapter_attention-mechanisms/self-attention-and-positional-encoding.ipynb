{"cells": [{"cell_type": "markdown", "id": "8bb0bdbd", "metadata": {"origin_pos": 0}, "source": ["# 自注意力和位置编码\n", ":label:`sec_self-attention-and-positional-encoding`\n", "\n", "在深度学习中，经常使用卷积神经网络（CNN）或循环神经网络（RNN）对序列进行编码。\n", "想象一下，有了注意力机制之后，我们将词元序列输入注意力池化中，\n", "以便同一组词元同时充当查询、键和值。\n", "具体来说，每个查询都会关注所有的键－值对并生成一个注意力输出。\n", "由于查询、键和值来自同一组输入，因此被称为\n", "*自注意力*（self-attention）\n", " :cite:`<PERSON><PERSON>Feng.Santos.ea.2017,Vaswani.Shazeer.Parmar.ea.2017`，\n", "也被称为*内部注意力*（intra-attention） :cite:`<PERSON><PERSON><PERSON>.2016,Parikh.Tackstrom.Das.ea.2016,Paulus.Xiong.Socher.2017`。\n", "本节将使用自注意力进行序列编码，以及如何使用序列的顺序作为补充信息。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "1f68f3c6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:01:34.234618Z", "iopub.status.busy": "2023-08-18T07:01:34.233587Z", "iopub.status.idle": "2023-08-18T07:01:37.175197Z", "shell.execute_reply": "2023-08-18T07:01:37.174050Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import math\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "b917fbb9", "metadata": {"origin_pos": 5}, "source": ["## [**自注意力**]\n", "\n", "给定一个由词元组成的输入序列$\\mathbf{x}_1, \\ldots, \\mathbf{x}_n$，\n", "其中任意$\\mathbf{x}_i \\in \\mathbb{R}^d$（$1 \\leq i \\leq n$）。\n", "该序列的自注意力输出为一个长度相同的序列\n", "$\\mathbf{y}_1, \\ldots, \\mathbf{y}_n$，其中：\n", "\n", "$$\\mathbf{y}_i = f(\\mathbf{x}_i, (\\mathbf{x}_1, \\mathbf{x}_1), \\ldots, (\\mathbf{x}_n, \\mathbf{x}_n)) \\in \\mathbb{R}^d$$\n", "\n", "根据 :eqref:`eq_attn-pooling`中定义的注意力汇聚函数$f$。\n", "下面的代码片段是基于多头注意力对一个张量完成自注意力的计算，\n", "张量的形状为（批量大小，时间步的数目或词元序列的长度，$d$）。\n", "输出与输入的张量形状相同。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "91993c5f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:01:37.181087Z", "iopub.status.busy": "2023-08-18T07:01:37.180270Z", "iopub.status.idle": "2023-08-18T07:01:37.209854Z", "shell.execute_reply": "2023-08-18T07:01:37.208705Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["MultiHeadAttention(\n", "  (attention): DotProductAttention(\n", "    (dropout): Dropout(p=0.5, inplace=False)\n", "  )\n", "  (W_q): Linear(in_features=100, out_features=100, bias=False)\n", "  (W_k): Linear(in_features=100, out_features=100, bias=False)\n", "  (W_v): Linear(in_features=100, out_features=100, bias=False)\n", "  (W_o): Linear(in_features=100, out_features=100, bias=False)\n", ")"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["num_hiddens, num_heads = 100, 5\n", "attention = d2l.MultiHeadAttention(num_hiddens, num_hiddens, num_hiddens,\n", "                                   num_hiddens, num_heads, 0.5)\n", "attention.eval()"]}, {"cell_type": "code", "execution_count": 3, "id": "05a56888", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:01:37.214732Z", "iopub.status.busy": "2023-08-18T07:01:37.214099Z", "iopub.status.idle": "2023-08-18T07:01:37.231099Z", "shell.execute_reply": "2023-08-18T07:01:37.229941Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 4, 100])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["batch_size, num_queries, valid_lens = 2, 4, torch.tensor([3, 2])\n", "X = torch.ones((batch_size, num_queries, num_hiddens))\n", "attention(X, X, X, valid_lens).shape"]}, {"cell_type": "markdown", "id": "7c242dc9", "metadata": {"origin_pos": 12}, "source": ["## 比较卷积神经网络、循环神经网络和自注意力\n", ":label:`subsec_cnn-rnn-self-attention`\n", "\n", "接下来比较下面几个架构，目标都是将由$n$个词元组成的序列映射到另一个长度相等的序列，其中的每个输入词元或输出词元都由$d$维向量表示。具体来说，将比较的是卷积神经网络、循环神经网络和自注意力这几个架构的计算复杂性、顺序操作和最大路径长度。请注意，顺序操作会妨碍并行计算，而任意的序列位置组合之间的路径越短，则能更轻松地学习序列中的远距离依赖关系 :cite:`Hochreiter.Bengio.Frasconi.ea.2001`。\n", "\n", "![比较卷积神经网络（填充词元被忽略）、循环神经网络和自注意力三种架构](../img/cnn-rnn-self-attention.svg)\n", ":label:`fig_cnn-rnn-self-attention`\n", "\n", "考虑一个卷积核大小为$k$的卷积层。\n", "在后面的章节将提供关于使用卷积神经网络处理序列的更多详细信息。\n", "目前只需要知道的是，由于序列长度是$n$，输入和输出的通道数量都是$d$，\n", "所以卷积层的计算复杂度为$\\mathcal{O}(knd^2)$。\n", "如 :numref:`fig_cnn-rnn-self-attention`所示，\n", "卷积神经网络是分层的，因此为有$\\mathcal{O}(1)$个顺序操作，\n", "最大路径长度为$\\mathcal{O}(n/k)$。\n", "例如，$\\mathbf{x}_1$和$\\mathbf{x}_5$处于\n", " :numref:`fig_cnn-rnn-self-attention`中卷积核大小为3的双层卷积神经网络的感受野内。\n", "\n", "当更新循环神经网络的隐状态时，\n", "$d \\times d$权重矩阵和$d$维隐状态的乘法计算复杂度为$\\mathcal{O}(d^2)$。\n", "由于序列长度为$n$，因此循环神经网络层的计算复杂度为$\\mathcal{O}(nd^2)$。\n", "根据 :numref:`fig_cnn-rnn-self-attention`，\n", "有$\\mathcal{O}(n)$个顺序操作无法并行化，最大路径长度也是$\\mathcal{O}(n)$。\n", "\n", "在自注意力中，查询、键和值都是$n \\times d$矩阵。\n", "考虑 :eqref:`eq_softmax_QK_V`中缩放的”点－积“注意力，\n", "其中$n \\times d$矩阵乘以$d \\times n$矩阵。\n", "之后输出的$n \\times n$矩阵乘以$n \\times d$矩阵。\n", "因此，自注意力具有$\\mathcal{O}(n^2d)$计算复杂性。\n", "正如在 :numref:`fig_cnn-rnn-self-attention`中所讲，\n", "每个词元都通过自注意力直接连接到任何其他词元。\n", "因此，有$\\mathcal{O}(1)$个顺序操作可以并行计算，\n", "最大路径长度也是$\\mathcal{O}(1)$。\n", "\n", "总而言之，卷积神经网络和自注意力都拥有并行计算的优势，\n", "而且自注意力的最大路径长度最短。\n", "但是因为其计算复杂度是关于序列长度的二次方，所以在很长的序列中计算会非常慢。\n", "\n", "## [**位置编码**]\n", ":label:`subsec_positional-encoding`\n", "\n", "在处理词元序列时，循环神经网络是逐个的重复地处理词元的，\n", "而自注意力则因为并行计算而放弃了顺序操作。\n", "为了使用序列的顺序信息，通过在输入表示中添加\n", "*位置编码*（positional encoding）来注入绝对的或相对的位置信息。\n", "位置编码可以通过学习得到也可以直接固定得到。\n", "接下来描述的是基于正弦函数和余弦函数的固定位置编码\n", " :cite:`<PERSON><PERSON><PERSON><PERSON>.Shazeer.Parmar.ea.2017`。\n", "\n", "假设输入表示$\\mathbf{X} \\in \\mathbb{R}^{n \\times d}$\n", "包含一个序列中$n$个词元的$d$维嵌入表示。\n", "位置编码使用相同形状的位置嵌入矩阵\n", "$\\mathbf{P} \\in \\mathbb{R}^{n \\times d}$输出$\\mathbf{X} + \\mathbf{P}$，\n", "矩阵第$i$行、第$2j$列和$2j+1$列上的元素为：\n", "\n", "$$\\begin{aligned} p_{i, 2j} &= \\sin\\left(\\frac{i}{10000^{2j/d}}\\right),\\\\p_{i, 2j+1} &= \\cos\\left(\\frac{i}{10000^{2j/d}}\\right).\\end{aligned}$$\n", ":eqlabel:`eq_positional-encoding-def`\n", "\n", "乍一看，这种基于三角函数的设计看起来很奇怪。\n", "在解释这个设计之前，让我们先在下面的`PositionalEncoding`类中实现它。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a1520381", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:01:37.236150Z", "iopub.status.busy": "2023-08-18T07:01:37.235749Z", "iopub.status.idle": "2023-08-18T07:01:37.246341Z", "shell.execute_reply": "2023-08-18T07:01:37.245419Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class PositionalEncoding(nn.Module):\n", "    \"\"\"位置编码\"\"\"\n", "    def __init__(self, num_hiddens, dropout, max_len=1000):\n", "        super(<PERSON>si<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.dropout = nn.Dropout(dropout)\n", "        # 创建一个足够长的P\n", "        self.P = torch.zeros((1, max_len, num_hiddens))\n", "        X = torch.arange(max_len, dtype=torch.float32).reshape(\n", "            -1, 1) / torch.pow(10000, torch.arange(\n", "            0, num_hiddens, 2, dtype=torch.float32) / num_hiddens)\n", "        self.P[:, :, 0::2] = torch.sin(X)\n", "        self.P[:, :, 1::2] = torch.cos(X)\n", "\n", "    def forward(self, X):\n", "        X = X + self.P[:, :X.shape[1], :].to(X.device)\n", "        return self.dropout(X)"]}, {"cell_type": "markdown", "id": "b2f91685", "metadata": {"origin_pos": 17}, "source": ["在位置嵌入矩阵$\\mathbf{P}$中，\n", "[**行代表词元在序列中的位置，列代表位置编码的不同维度**]。\n", "从下面的例子中可以看到位置嵌入矩阵的第$6$列和第$7$列的频率高于第$8$列和第$9$列。\n", "第$6$列和第$7$列之间的偏移量（第$8$列和第$9$列相同）是由于正弦函数和余弦函数的交替。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "2530db11", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:01:37.253441Z", "iopub.status.busy": "2023-08-18T07:01:37.251675Z", "iopub.status.idle": "2023-08-18T07:01:37.511460Z", "shell.execute_reply": "2023-08-18T07:01:37.510281Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"380.482812pt\" height=\"180.65625pt\" viewBox=\"0 0 380.**********.65625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:01:37.459076</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 180.65625 \n", "L 380.**********.65625 \n", "L 380.482812 0 \n", "L -0 0 \n", "L -0 180.65625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 38.**********.1 \n", "L 373.**********.1 \n", "L 373.282813 7.2 \n", "L 38.482813 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 53.700994 143.1 \n", "L 53.700994 7.2 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m7b94a4ab65\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7b94a4ab65\" x=\"53.700994\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(50.519744 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 105.288051 143.1 \n", "L 105.288051 7.2 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m7b94a4ab65\" x=\"105.288051\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(98.925551 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 156.875108 143.1 \n", "L 156.875108 7.2 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m7b94a4ab65\" x=\"156.875108\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(150.512608 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 208.462165 143.1 \n", "L 208.462165 7.2 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m7b94a4ab65\" x=\"208.462165\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(202.099665 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 260.049222 143.1 \n", "L 260.049222 7.2 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m7b94a4ab65\" x=\"260.049222\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(253.686722 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 311.636279 143.1 \n", "L 311.636279 7.2 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m7b94a4ab65\" x=\"311.636279\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(305.273779 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 363.223336 143.1 \n", "L 363.223336 7.2 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m7b94a4ab65\" x=\"363.223336\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(356.860836 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- Row (position) -->\n", "     <g transform=\"translate(170.189844 171.376563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-52\" d=\"M 2841 2188 \n", "Q 3044 2119 3236 1894 \n", "Q 3428 1669 3622 1275 \n", "L 4263 0 \n", "L 3584 0 \n", "L 2988 1197 \n", "Q 2756 1666 2539 1819 \n", "Q 2322 1972 1947 1972 \n", "L 1259 1972 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "L 2053 4666 \n", "Q 2853 4666 3247 4331 \n", "Q 3641 3997 3641 3322 \n", "Q 3641 2881 3436 2590 \n", "Q 3231 2300 2841 2188 \n", "z\n", "M 1259 4147 \n", "L 1259 2491 \n", "L 2053 2491 \n", "Q 2509 2491 2742 2702 \n", "Q 2975 2913 2975 3322 \n", "Q 2975 3731 2742 3939 \n", "Q 2509 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-52\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"64.982422\"/>\n", "      <use xlink:href=\"#DejaVuSans-77\" x=\"126.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"207.951172\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"239.738281\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"278.751953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"342.228516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"403.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"455.509766\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"483.292969\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"522.501953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"550.285156\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"611.466797\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"674.845703\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 38.482813 136.922727 \n", "L 373.282813 136.922727 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <defs>\n", "       <path id=\"m6647ad99f9\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m6647ad99f9\" x=\"38.482813\" y=\"136.922727\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(7.2 140.721946)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 38.482813 106.036364 \n", "L 373.282813 106.036364 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m6647ad99f9\" x=\"38.482813\" y=\"106.036364\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(7.2 109.835582)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 38.482813 75.15 \n", "L 373.282813 75.15 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m6647ad99f9\" x=\"38.482813\" y=\"75.15\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(15.579688 78.949219)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 38.482813 44.263636 \n", "L 373.282813 44.263636 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m6647ad99f9\" x=\"38.482813\" y=\"44.263636\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(15.579688 48.062855)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 38.482813 13.377273 \n", "L 373.282813 13.377273 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m6647ad99f9\" x=\"38.482813\" y=\"13.377273\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(15.579688 17.176491)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 53.700994 75.15 \n", "L 58.8597 64.222887 \n", "L 64.018406 53.640411 \n", "L 69.177111 43.736334 \n", "L 74.335817 34.823035 \n", "L 79.494523 27.181626 \n", "L 84.653229 21.053116 \n", "L 89.811934 16.630798 \n", "L 94.97064 14.05415 \n", "L 100.129346 13.404434 \n", "L 105.288051 14.702142 \n", "L 110.446757 17.906352 \n", "L 115.605463 22.916001 \n", "L 120.764168 29.57308 \n", "L 125.922874 37.667644 \n", "L 131.08158 46.944371 \n", "L 136.240286 57.11069 \n", "L 141.398991 67.845975 \n", "L 146.557697 78.81161 \n", "L 151.716403 89.661775 \n", "L 156.875108 100.05423 \n", "L 162.033814 109.661231 \n", "L 167.19252 118.179753 \n", "L 172.351225 125.341136 \n", "L 177.509931 130.919532 \n", "L 182.668637 134.738964 \n", "L 187.827343 136.678994 \n", "L 192.986048 136.678427 \n", "L 198.144754 134.737281 \n", "L 203.30346 130.916781 \n", "L 208.462165 125.337425 \n", "L 213.620871 118.175187 \n", "L 218.779577 109.655955 \n", "L 223.938282 100.048393 \n", "L 229.096988 89.655575 \n", "L 234.255694 78.805258 \n", "L 239.4144 67.839656 \n", "L 244.573105 57.104589 \n", "L 249.731811 46.938698 \n", "L 254.890517 37.662574 \n", "L 260.049222 29.568787 \n", "L 265.207928 22.912588 \n", "L 270.366634 17.903955 \n", "L 275.525339 14.700831 \n", "L 280.684045 13.404247 \n", "L 285.842751 14.055089 \n", "L 291.001457 16.632831 \n", "L 296.160162 21.056179 \n", "L 301.318868 27.185658 \n", "L 306.477574 34.827873 \n", "L 311.636279 43.741832 \n", "L 316.794985 53.646389 \n", "L 321.953691 64.229157 \n", "L 327.112396 75.156364 \n", "L 332.271102 86.08337 \n", "L 337.429808 96.665543 \n", "L 342.588514 106.569177 \n", "L 347.747219 115.48181 \n", "L 352.905925 123.122402 \n", "L 358.064631 129.249966 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 53.700994 13.377273 \n", "L 58.8597 14.351415 \n", "L 64.018406 17.243112 \n", "L 69.177111 21.961171 \n", "L 74.335817 28.356777 \n", "L 79.494523 36.228223 \n", "L 84.653229 45.327242 \n", "L 89.811934 55.366859 \n", "L 94.97064 66.030417 \n", "L 100.129346 76.981611 \n", "L 105.288051 87.875035 \n", "L 110.446757 98.367119 \n", "L 115.605463 108.126952 \n", "L 120.764168 116.846695 \n", "L 125.922874 124.251351 \n", "L 131.08158 130.107369 \n", "L 136.240286 134.230057 \n", "L 141.398991 136.489392 \n", "L 146.557697 136.81411 \n", "L 151.716403 135.193971 \n", "L 156.875108 131.680073 \n", "L 162.033814 126.38324 \n", "L 167.19252 119.470538 \n", "L 172.351225 111.159995 \n", "L 177.509931 101.713683 \n", "L 182.668637 91.429594 \n", "L 187.827343 80.632052 \n", "L 192.986048 69.66161 \n", "L 198.144754 58.864239 \n", "L 203.30346 48.580543 \n", "L 208.462165 39.134835 \n", "L 213.620871 30.825029 \n", "L 218.779577 23.913207 \n", "L 223.938282 18.617357 \n", "L 229.096988 15.104534 \n", "L 234.255694 13.485514 \n", "L 239.4144 13.811359 \n", "L 244.573105 16.071806 \n", "L 249.731811 20.195543 \n", "L 254.890517 26.052518 \n", "L 260.049222 33.458003 \n", "L 265.207928 42.178453 \n", "L 270.366634 51.938792 \n", "L 275.525339 62.431199 \n", "L 280.684045 73.324751 \n", "L 285.842751 84.27587 \n", "L 291.001457 94.939163 \n", "L 296.160162 104.978317 \n", "L 301.318868 114.076744 \n", "L 306.477574 121.947395 \n", "L 311.636279 128.342073 \n", "L 316.794985 133.059108 \n", "L 321.953691 135.949712 \n", "L 327.112396 136.922727 \n", "L 332.271102 135.947462 \n", "L 337.429808 133.054675 \n", "L 342.588514 128.335571 \n", "L 347.747219 121.939044 \n", "L 352.905925 114.066818 \n", "L 358.064631 104.96717 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 53.700994 75.15 \n", "L 58.8597 68.983017 \n", "L 64.018406 62.877654 \n", "L 69.177111 56.89491 \n", "L 74.335817 51.094566 \n", "L 79.494523 45.534576 \n", "L 84.653229 40.270494 \n", "L 89.811934 35.354918 \n", "L 94.97064 30.836958 \n", "L 100.129346 26.761763 \n", "L 105.288051 23.170044 \n", "L 110.446757 20.097689 \n", "L 115.605463 17.575404 \n", "L 120.764168 15.628382 \n", "L 125.922874 14.276083 \n", "L 131.08158 13.532014 \n", "L 136.240286 13.403613 \n", "L 141.398991 13.892159 \n", "L 146.557697 14.992776 \n", "L 151.716403 16.694463 \n", "L 156.875108 18.980219 \n", "L 162.033814 21.8272 \n", "L 167.19252 25.206975 \n", "L 172.351225 29.085753 \n", "L 177.509931 33.424803 \n", "L 182.668637 38.180745 \n", "L 187.827343 43.30607 \n", "L 192.986048 48.749581 \n", "L 198.144754 54.456865 \n", "L 203.30346 60.370922 \n", "L 208.462165 66.432633 \n", "L 213.620871 72.581443 \n", "L 218.779577 78.755933 \n", "L 223.938282 84.894379 \n", "L 229.096988 90.935477 \n", "L 234.255694 96.818837 \n", "L 239.4144 102.485689 \n", "L 244.573105 107.879426 \n", "L 249.731811 112.946127 \n", "L 254.890517 117.635197 \n", "L 260.049222 121.899754 \n", "L 265.207928 125.697206 \n", "L 270.366634 128.989605 \n", "L 275.525339 131.744073 \n", "L 280.684045 133.933057 \n", "L 285.842751 135.534702 \n", "L 291.001457 136.533001 \n", "L 296.160162 136.917985 \n", "L 301.318868 136.685802 \n", "L 306.477574 135.838775 \n", "L 311.636279 134.385369 \n", "L 316.794985 132.340102 \n", "L 321.953691 129.723409 \n", "L 327.112396 126.561424 \n", "L 332.271102 122.885767 \n", "L 337.429808 118.733148 \n", "L 342.588514 114.145067 \n", "L 347.747219 109.167358 \n", "L 352.905925 103.849734 \n", "L 358.064631 98.245375 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 53.700994 13.377273 \n", "L 58.8597 13.685878 \n", "L 64.018406 14.608613 \n", "L 69.177111 16.136258 \n", "L 74.335817 18.253551 \n", "L 79.494523 20.939332 \n", "L 84.653229 24.166768 \n", "L 89.811934 27.903611 \n", "L 94.97064 32.112526 \n", "L 100.129346 36.751456 \n", "L 105.288051 41.774051 \n", "L 110.446757 47.130131 \n", "L 115.605463 52.766176 \n", "L 120.764168 58.625865 \n", "L 125.922874 64.650665 \n", "L 131.08158 70.78037 \n", "L 136.240286 76.953736 \n", "L 141.398991 83.109079 \n", "L 146.557697 89.18489 \n", "L 151.716403 95.120477 \n", "L 156.875108 100.856526 \n", "L 162.033814 106.335717 \n", "L 167.19252 111.503321 \n", "L 172.351225 116.307684 \n", "L 177.509931 120.700826 \n", "L 182.668637 124.638824 \n", "L 187.827343 128.082353 \n", "L 192.986048 130.997003 \n", "L 198.144754 133.353645 \n", "L 203.30346 135.128734 \n", "L 208.462165 136.304537 \n", "L 213.620871 136.869302 \n", "L 218.779577 136.817391 \n", "L 223.938282 136.149321 \n", "L 229.096988 134.871761 \n", "L 234.255694 132.997483 \n", "L 239.4144 130.545214 \n", "L 244.573105 127.53945 \n", "L 249.731811 124.010236 \n", "L 254.890517 119.992814 \n", "L 260.049222 115.527348 \n", "L 265.207928 110.658448 \n", "L 270.366634 105.434758 \n", "L 275.525339 99.908448 \n", "L 280.684045 94.134784 \n", "L 285.842751 88.171432 \n", "L 291.001457 82.077973 \n", "L 296.160162 75.915293 \n", "L 301.318868 69.744938 \n", "L 306.477574 63.628616 \n", "L 311.636279 57.627412 \n", "L 316.794985 51.801289 \n", "L 321.953691 46.208458 \n", "L 327.112396 40.904774 \n", "L 332.271102 35.943284 \n", "L 337.429808 31.373535 \n", "L 342.588514 27.241185 \n", "L 347.747219 23.587521 \n", "L 352.905925 20.449038 \n", "L 358.064631 17.857124 \n", "\" clip-path=\"url(#p874afd2c2a)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 38.**********.1 \n", "L 38.482813 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 373.**********.1 \n", "L 373.282813 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 38.**********.1 \n", "L 373.282812 143.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 38.482813 7.2 \n", "L 373.282812 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 45.482813 138.1 \n", "L 102.903125 138.1 \n", "Q 104.903125 138.1 104.903125 136.1 \n", "L 104.903125 78.3875 \n", "Q 104.903125 76.3875 102.903125 76.3875 \n", "L 45.482813 76.3875 \n", "Q 43.482813 76.3875 43.482813 78.3875 \n", "L 43.482813 136.1 \n", "Q 43.482813 138.1 45.482813 138.1 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_29\">\n", "     <path d=\"M 47.482813 84.485937 \n", "L 57.482813 84.485937 \n", "L 67.482812 84.485937 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- Col 6 -->\n", "     <g transform=\"translate(75.482812 87.985937)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"190.576172\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 47.482813 99.164062 \n", "L 57.482813 99.164062 \n", "L 67.482812 99.164062 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- Col 7 -->\n", "     <g transform=\"translate(75.482812 102.664062)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-37\" x=\"190.576172\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_31\">\n", "     <path d=\"M 47.482813 113.842187 \n", "L 57.482813 113.842187 \n", "L 67.482812 113.842187 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Col 8 -->\n", "     <g transform=\"translate(75.482812 117.342187)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-38\" x=\"190.576172\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_32\">\n", "     <path d=\"M 47.482813 128.520312 \n", "L 57.482813 128.520312 \n", "L 67.482812 128.520312 \n", "\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Col 9 -->\n", "     <g transform=\"translate(75.482812 132.020312)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-39\" x=\"190.576172\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p874afd2c2a\">\n", "   <rect x=\"38.482813\" y=\"7.2\" width=\"334.8\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 432x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["encoding_dim, num_steps = 32, 60\n", "pos_encoding = PositionalEncoding(encoding_dim, 0)\n", "pos_encoding.eval()\n", "X = pos_encoding(torch.zeros((1, num_steps, encoding_dim)))\n", "P = pos_encoding.P[:, :X.shape[1], :]\n", "d2l.plot(torch.arange(num_steps), P[0, :, 6:10].T, xlabel='Row (position)',\n", "         figsize=(6, 2.5), legend=[\"Col %d\" % d for d in torch.arange(6, 10)])"]}, {"cell_type": "markdown", "id": "28698b9d", "metadata": {"origin_pos": 22}, "source": ["### 绝对位置信息\n", "\n", "为了明白沿着编码维度单调降低的频率与绝对位置信息的关系，\n", "让我们打印出$0, 1, \\ldots, 7$的[**二进制表示**]形式。\n", "正如所看到的，每个数字、每两个数字和每四个数字上的比特值\n", "在第一个最低位、第二个最低位和第三个最低位上分别交替。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "07196b9a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:01:37.516113Z", "iopub.status.busy": "2023-08-18T07:01:37.515203Z", "iopub.status.idle": "2023-08-18T07:01:37.523367Z", "shell.execute_reply": "2023-08-18T07:01:37.520554Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0的二进制是：000\n", "1的二进制是：001\n", "2的二进制是：010\n", "3的二进制是：011\n", "4的二进制是：100\n", "5的二进制是：101\n", "6的二进制是：110\n", "7的二进制是：111\n"]}], "source": ["for i in range(8):\n", "    print(f'{i}的二进制是：{i:>03b}')"]}, {"cell_type": "markdown", "id": "bc8f0fc5", "metadata": {"origin_pos": 24}, "source": ["在二进制表示中，较高比特位的交替频率低于较低比特位，\n", "与下面的热图所示相似，只是位置编码通过使用三角函数[**在编码维度上降低频率**]。\n", "由于输出是浮点数，因此此类连续表示比二进制表示法更节省空间。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "fb689860", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:01:37.528541Z", "iopub.status.busy": "2023-08-18T07:01:37.527891Z", "iopub.status.idle": "2023-08-18T07:01:37.784120Z", "shell.execute_reply": "2023-08-18T07:01:37.782997Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"211.342137pt\" height=\"264.183469pt\" viewBox=\"0 0 211.**********.183469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:01:37.711974</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 264.183469 \n", "L 211.**********.183469 \n", "L 211.342137 0 \n", "L 0 0 \n", "L 0 264.183469 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.627219 \n", "L 156.**********.627219 \n", "L 156.571125 9.187219 \n", "L 40.603125 9.187219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pb25821e4b0)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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****************************************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*************************************+Gbi6e6+xA5L9fqS2CcYZBdZOdrWiSzPlUqxp5GbHp9PQn89tIRiq2+Kzr4mOx8bY6ZmsUG4QhTKDfvjXH8HEy9+fawefI7f92Fk7KJwC3MkT52GcW2gjRcHP64DLyZkFY3fLzfC5LQkiRb7LSj6XhhNkhTKDKJQZRKHMoN/csg0m9i9wTlSyC4F7dc8pI+dNmwpc62k8fZl9Q5WRz5Gms7lZpLtDr80YzKBui2MnUz0lyJUaetYfp2axskKZQRTKDKJQZtBuZoFSStVstLWIiqvmAffZ2+8bee43FwJ35IPDMJ5UssjIR8lRW352/A5JGanED3WQMPTnzUrgYyd9kBXKDKJQZtAr19wGE1se3WjklevXInfgt0auqrgduCNkW51S5FxIIvWE8jPJlusgxm1xEJOxQPD/pemrD7JCmUEUygyiUGb4XxLztoh576hpAAAAAElFTkSuQmCC\" id=\"image246b8b0e1e\" transform=\"scale(1 -1)translate(0 -218)\" x=\"40.603125\" y=\"-8.627219\" width=\"116\" height=\"218\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mf5c671cece\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf5c671cece\" x=\"42.415125\" y=\"226.627219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(39.233875 241.225656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mf5c671cece\" x=\"114.895125\" y=\"226.627219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(108.532625 241.225656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- Column (encoding dimension) -->\n", "     <g transform=\"translate(23.498844 254.903781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"222.167969\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"319.580078\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"382.958984\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"414.746094\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"453.759766\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"515.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"578.662109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"633.642578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"694.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"758.300781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"786.083984\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"849.462891\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"912.939453\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"944.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"1008.203125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"1035.986328\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"1133.398438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"1194.921875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1258.300781\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"1310.400391\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"1338.183594\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"1399.365234\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"1462.744141\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"m60fa3f5940\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m60fa3f5940\" x=\"40.603125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(27.240625 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m60fa3f5940\" x=\"40.603125\" y=\"47.239219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(20.878125 51.038437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m60fa3f5940\" x=\"40.603125\" y=\"83.479219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(20.878125 87.278437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m60fa3f5940\" x=\"40.603125\" y=\"119.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(20.878125 123.518437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m60fa3f5940\" x=\"40.603125\" y=\"155.959219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(20.878125 159.758437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m60fa3f5940\" x=\"40.603125\" y=\"192.199219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(20.878125 195.998437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Row (position) -->\n", "     <g transform=\"translate(14.798438 153.600187)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-52\" d=\"M 2841 2188 \n", "Q 3044 2119 3236 1894 \n", "Q 3428 1669 3622 1275 \n", "L 4263 0 \n", "L 3584 0 \n", "L 2988 1197 \n", "Q 2756 1666 2539 1819 \n", "Q 2322 1972 1947 1972 \n", "L 1259 1972 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "L 2053 4666 \n", "Q 2853 4666 3247 4331 \n", "Q 3641 3997 3641 3322 \n", "Q 3641 2881 3436 2590 \n", "Q 3231 2300 2841 2188 \n", "z\n", "M 1259 4147 \n", "L 1259 2491 \n", "L 2053 2491 \n", "Q 2509 2491 2742 2702 \n", "Q 2975 2913 2975 3322 \n", "Q 2975 3731 2742 3939 \n", "Q 2509 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-52\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"64.982422\"/>\n", "      <use xlink:href=\"#DejaVuSans-77\" x=\"126.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"207.951172\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"239.738281\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"278.751953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"342.228516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"403.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"455.509766\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"483.292969\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"522.501953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"550.285156\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"611.466797\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"674.845703\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.627219 \n", "L 40.603125 9.187219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 156.**********.627219 \n", "L 156.571125 9.187219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.627219 \n", "L 156.**********.627219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 9.187219 \n", "L 156.571125 9.187219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 166.336125 183.139219 \n", "L 172.859325 183.139219 \n", "L 172.859325 52.675219 \n", "L 166.336125 52.675219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path clip-path=\"url(#p6bfc04468a)\" style=\"fill: #ffffff; stroke: #ffffff; stroke-width: 0.01; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAcAAACDCAYAAABBcwEdAAAAqElEQVR4nO2WQQoEMQgEXcj/vzuHTXRfYAVaJBnYuTbVapuE+TzfCEu+4blmA7QLSScyXkVel+0J0pgE8V3LRnLJthtSr3mCTLW+mlC0VDPVbCyqybZOtkROnBNt5YTQdtNQS7YoTjlbPkO47EK2PQ0dqFl4GPUzJI/yF82Ge35ZNluB29vVbRRGAVG3RZL/MA401EQWEiLSfMni7LHtEddtDeli5Pv8AVjrYLu4ozIGAAAAAElFTkSuQmCC\" id=\"image8f2f97b260\" transform=\"scale(1 -1)translate(0 -131)\" x=\"166\" y=\"-52\" width=\"7\" height=\"131\"/>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_9\">\n", "      <defs>\n", "       <path id=\"m013d4070d5\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m013d4070d5\" x=\"172.859325\" y=\"183.139219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(179.859325 186.938437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m013d4070d5\" x=\"172.859325\" y=\"150.523219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(179.859325 154.322437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m013d4070d5\" x=\"172.859325\" y=\"117.907219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(179.859325 121.706437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m013d4070d5\" x=\"172.859325\" y=\"85.291219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(179.859325 89.090437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m013d4070d5\" x=\"172.859325\" y=\"52.675219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(179.859325 56.474437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 166.336125 183.139219 \n", "L 169.597725 183.139219 \n", "L 172.859325 183.139219 \n", "L 172.859325 52.675219 \n", "L 169.597725 52.675219 \n", "L 166.336125 52.675219 \n", "L 166.336125 183.139219 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb25821e4b0\">\n", "   <rect x=\"40.603125\" y=\"9.187219\" width=\"115.968\" height=\"217.44\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6bfc04468a\">\n", "   <rect x=\"166.336125\" y=\"52.675219\" width=\"6.5232\" height=\"130.464\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["P = P[0, :, :].unsqueeze(0).unsqueeze(0)\n", "d2l.show_heatmaps(P, xlabel='Column (encoding dimension)',\n", "                  ylabel='Row (position)', figsize=(3.5, 4), cmap='Blues')"]}, {"cell_type": "markdown", "id": "6c836c1d", "metadata": {"origin_pos": 29}, "source": ["### 相对位置信息\n", "\n", "除了捕获绝对位置信息之外，上述的位置编码还允许模型学习得到输入序列中相对位置信息。\n", "这是因为对于任何确定的位置偏移$\\delta$，位置$i + \\delta$处\n", "的位置编码可以线性投影位置$i$处的位置编码来表示。\n", "\n", "这种投影的数学解释是，令$\\omega_j = 1/10000^{2j/d}$，\n", "对于任何确定的位置偏移$\\delta$，\n", " :eqref:`eq_positional-encoding-def`中的任何一对\n", "$(p_{i, 2j}, p_{i, 2j+1})$都可以线性投影到\n", "$(p_{i+\\delta, 2j}, p_{i+\\delta, 2j+1})$：\n", "\n", "$$\\begin{aligned}\n", "&\\begin{bmatrix} \\cos(\\delta \\omega_j) & \\sin(\\delta \\omega_j) \\\\  -\\sin(\\delta \\omega_j) & \\cos(\\delta \\omega_j) \\\\ \\end{bmatrix}\n", "\\begin{bmatrix} p_{i, 2j} \\\\  p_{i, 2j+1} \\\\ \\end{bmatrix}\\\\\n", "=&\\begin{bmatrix} \\cos(\\delta \\omega_j) \\sin(i \\omega_j) + \\sin(\\delta \\omega_j) \\cos(i \\omega_j) \\\\  -\\sin(\\delta \\omega_j) \\sin(i \\omega_j) + \\cos(\\delta \\omega_j) \\cos(i \\omega_j) \\\\ \\end{bmatrix}\\\\\n", "=&\\begin{bmatrix} \\sin\\left((i+\\delta) \\omega_j\\right) \\\\  \\cos\\left((i+\\delta) \\omega_j\\right) \\\\ \\end{bmatrix}\\\\\n", "=& \n", "\\begin{bmatrix} p_{i+\\delta, 2j} \\\\  p_{i+\\delta, 2j+1} \\\\ \\end{bmatrix},\n", "\\end{aligned}$$\n", "\n", "$2\\times 2$投影矩阵不依赖于任何位置的索引$i$。\n", "\n", "## 小结\n", "\n", "* 在自注意力中，查询、键和值都来自同一组输入。\n", "* 卷积神经网络和自注意力都拥有并行计算的优势，而且自注意力的最大路径长度最短。但是因为其计算复杂度是关于序列长度的二次方，所以在很长的序列中计算会非常慢。\n", "* 为了使用序列的顺序信息，可以通过在输入表示中添加位置编码，来注入绝对的或相对的位置信息。\n", "\n", "## 练习\n", "\n", "1. 假设设计一个深度架构，通过堆叠基于位置编码的自注意力层来表示序列。可能会存在什么问题？\n", "1. 请设计一种可学习的位置编码方法。\n"]}, {"cell_type": "markdown", "id": "3d4bb49e", "metadata": {"origin_pos": 31, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/5762)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
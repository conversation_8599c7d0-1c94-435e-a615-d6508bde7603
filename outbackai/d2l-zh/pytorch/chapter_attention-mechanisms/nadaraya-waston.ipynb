{"cells": [{"cell_type": "markdown", "id": "12605036", "metadata": {"origin_pos": 0}, "source": ["# 注意力汇聚：Nadaraya-Watson 核回归\n", ":label:`sec_nadar<PERSON>-watson`\n", "\n", "上节介绍了框架下的注意力机制的主要成分 :numref:`fig_qkv`：\n", "查询（自主提示）和键（非自主提示）之间的交互形成了注意力汇聚；\n", "注意力汇聚有选择地聚合了值（感官输入）以生成最终的输出。\n", "本节将介绍注意力汇聚的更多细节，\n", "以便从宏观上了解注意力机制在实践中的运作方式。\n", "具体来说，1964年提出的Nadaraya-Watson核回归模型\n", "是一个简单但完整的例子，可以用于演示具有注意力机制的机器学习。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "47b48700", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:14.699824Z", "iopub.status.busy": "2023-08-18T07:07:14.699278Z", "iopub.status.idle": "2023-08-18T07:07:16.694044Z", "shell.execute_reply": "2023-08-18T07:07:16.693186Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "45448f28", "metadata": {"origin_pos": 5}, "source": ["## [**生成数据集**]\n", "\n", "简单起见，考虑下面这个回归问题：\n", "给定的成对的“输入－输出”数据集\n", "$\\{(x_1, y_1), \\ldots, (x_n, y_n)\\}$，\n", "如何学习$f$来预测任意新输入$x$的输出$\\hat{y} = f(x)$？\n", "\n", "根据下面的非线性函数生成一个人工数据集，\n", "其中加入的噪声项为$\\epsilon$：\n", "\n", "$$y_i = 2\\sin(x_i) + x_i^{0.8} + \\epsilon,$$\n", "\n", "其中$\\epsilon$服从均值为$0$和标准差为$0.5$的正态分布。\n", "在这里生成了$50$个训练样本和$50$个测试样本。\n", "为了更好地可视化之后的注意力模式，需要将训练样本进行排序。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "77ea63dd", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:16.698617Z", "iopub.status.busy": "2023-08-18T07:07:16.697928Z", "iopub.status.idle": "2023-08-18T07:07:16.720614Z", "shell.execute_reply": "2023-08-18T07:07:16.719799Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["n_train = 50  # 训练样本数\n", "x_train, _ = torch.sort(torch.rand(n_train) * 5)   # 排序后的训练样本"]}, {"cell_type": "code", "execution_count": 3, "id": "2b36fd68", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:16.724389Z", "iopub.status.busy": "2023-08-18T07:07:16.723850Z", "iopub.status.idle": "2023-08-18T07:07:16.734529Z", "shell.execute_reply": "2023-08-18T07:07:16.733732Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["50"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["def f(x):\n", "    return 2 * torch.sin(x) + x**0.8\n", "\n", "y_train = f(x_train) + torch.normal(0.0, 0.5, (n_train,))  # 训练样本的输出\n", "x_test = torch.arange(0, 5, 0.1)  # 测试样本\n", "y_truth = f(x_test)  # 测试样本的真实输出\n", "n_test = len(x_test)  # 测试样本数\n", "n_test"]}, {"cell_type": "markdown", "id": "a8cd762c", "metadata": {"origin_pos": 14}, "source": ["下面的函数将绘制所有的训练样本（样本由圆圈表示），\n", "不带噪声项的真实数据生成函数$f$（标记为“Truth”），\n", "以及学习得到的预测函数（标记为“Pred”）。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "84166e26", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:16.738117Z", "iopub.status.busy": "2023-08-18T07:07:16.737614Z", "iopub.status.idle": "2023-08-18T07:07:16.742118Z", "shell.execute_reply": "2023-08-18T07:07:16.741332Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["def plot_kernel_reg(y_hat):\n", "    d2l.plot(x_test, [y_truth, y_hat], 'x', 'y', legend=['Truth', 'Pred'],\n", "             xlim=[0, 5], ylim=[-1, 5])\n", "    d2l.plt.plot(x_train, y_train, 'o', alpha=0.5);"]}, {"cell_type": "markdown", "id": "83acd3e5", "metadata": {"origin_pos": 16}, "source": ["## 平均汇聚\n", "\n", "先使用最简单的估计器来解决回归问题。\n", "基于平均汇聚来计算所有训练样本输出值的平均值：\n", "\n", "$$f(x) = \\frac{1}{n}\\sum_{i=1}^n y_i,$$\n", ":eqlabel:`eq_avg-pooling`\n", "\n", "如下图所示，这个估计器确实不够聪明。\n", "真实函数$f$（“Truth”）和预测函数（“Pred”）相差很大。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b5227412", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:16.745659Z", "iopub.status.busy": "2023-08-18T07:07:16.745145Z", "iopub.status.idle": "2023-08-18T07:07:16.921666Z", "shell.execute_reply": "2023-08-18T07:07:16.920767Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"248.301563pt\" height=\"184.455469pt\" viewBox=\"0 0 248.**********.455469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:07:16.873741</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 184.455469 \n", "L 248.**********.455469 \n", "L 248.301563 -0 \n", "L 0 -0 \n", "L 0 184.455469 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.899219 \n", "L 237.**********.899219 \n", "L 237.920313 10.999219 \n", "L 42.620312 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 42.**********.899219 \n", "L 42.620312 10.999219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m0777dff393\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m0777dff393\" x=\"42.620312\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(39.439062 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 81.680312 146.899219 \n", "L 81.680312 10.999219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m0777dff393\" x=\"81.680312\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(78.499062 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 120.740313 146.899219 \n", "L 120.740313 10.999219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m0777dff393\" x=\"120.740313\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(117.559062 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 159.800313 146.899219 \n", "L 159.800313 10.999219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m0777dff393\" x=\"159.800313\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(156.619063 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 198.860313 146.899219 \n", "L 198.860313 10.999219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m0777dff393\" x=\"198.860313\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(195.679063 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 237.**********.899219 \n", "L 237.920313 10.999219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m0777dff393\" x=\"237.920313\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(234.739063 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- x -->\n", "     <g transform=\"translate(137.310937 175.175781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 42.**********.899219 \n", "L 237.**********.899219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"m0ea0cf7c53\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m0ea0cf7c53\" x=\"42.620312\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(20.878125 150.698437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 42.620312 124.249219 \n", "L 237.920313 124.249219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m0ea0cf7c53\" x=\"42.620312\" y=\"124.249219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 128.048437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 42.620312 101.599219 \n", "L 237.920313 101.599219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m0ea0cf7c53\" x=\"42.620312\" y=\"101.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(29.257812 105.398437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 42.620312 78.949219 \n", "L 237.920313 78.949219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m0ea0cf7c53\" x=\"42.620312\" y=\"78.949219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(29.257812 82.748437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 42.620312 56.299219 \n", "L 237.920313 56.299219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m0ea0cf7c53\" x=\"42.620312\" y=\"56.299219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(29.257812 60.098437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 42.620312 33.649219 \n", "L 237.920313 33.649219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m0ea0cf7c53\" x=\"42.620312\" y=\"33.649219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(29.257812 37.448437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_25\">\n", "      <path d=\"M 42.620312 10.999219 \n", "L 237.920313 10.999219 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m0ea0cf7c53\" x=\"42.620312\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(29.257812 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- y -->\n", "     <g transform=\"translate(14.798437 81.908594)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 42.620312 124.249219 \n", "L 46.526313 116.136982 \n", "L 50.432313 108.999323 \n", "L 54.338313 102.217148 \n", "L 58.244313 95.726379 \n", "L 62.150312 89.522233 \n", "L 66.056313 83.619087 \n", "L 69.962312 78.038824 \n", "L 73.868313 72.806 \n", "L 77.774314 67.945395 \n", "L 81.680312 63.480584 \n", "L 85.586313 59.432954 \n", "L 89.492314 55.821098 \n", "L 93.398311 52.660258 \n", "L 97.304312 49.962036 \n", "L 101.210313 47.734078 \n", "L 105.116313 45.979911 \n", "L 109.022314 44.69881 \n", "L 112.928315 43.885737 \n", "L 116.834312 43.531371 \n", "L 120.740313 43.622105 \n", "L 124.646309 44.140188 \n", "L 128.552314 45.063873 \n", "L 132.458311 46.367563 \n", "L 136.364316 48.022135 \n", "L 140.270312 49.995107 \n", "L 144.176318 52.251038 \n", "L 148.082314 54.751808 \n", "L 151.98832 57.457039 \n", "L 155.894316 60.324422 \n", "L 159.800313 63.310193 \n", "L 163.706318 66.369546 \n", "L 167.612314 69.45704 \n", "L 171.518311 72.527086 \n", "L 175.424316 75.534393 \n", "L 179.330312 78.434371 \n", "L 183.236318 81.183652 \n", "L 187.142314 83.740416 \n", "L 191.048311 86.06491 \n", "L 194.954316 88.119776 \n", "L 198.860313 89.870412 \n", "L 202.766309 91.285368 \n", "L 206.672305 92.336626 \n", "L 210.57832 92.999866 \n", "L 214.484316 93.254719 \n", "L 218.390312 93.084986 \n", "L 222.296309 92.478756 \n", "L 226.202305 91.428572 \n", "L 230.10832 89.931453 \n", "L 234.014316 87.988973 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 42.620312 72.218682 \n", "L 46.526313 72.218682 \n", "L 50.432313 72.218682 \n", "L 54.338313 72.218682 \n", "L 58.244313 72.218682 \n", "L 62.150312 72.218682 \n", "L 66.056313 72.218682 \n", "L 69.962312 72.218682 \n", "L 73.868313 72.218682 \n", "L 77.774314 72.218682 \n", "L 81.680312 72.218682 \n", "L 85.586313 72.218682 \n", "L 89.492314 72.218682 \n", "L 93.398311 72.218682 \n", "L 97.304312 72.218682 \n", "L 101.210313 72.218682 \n", "L 105.116313 72.218682 \n", "L 109.022314 72.218682 \n", "L 112.928315 72.218682 \n", "L 116.834312 72.218682 \n", "L 120.740313 72.218682 \n", "L 124.646309 72.218682 \n", "L 128.552314 72.218682 \n", "L 132.458311 72.218682 \n", "L 136.364316 72.218682 \n", "L 140.270312 72.218682 \n", "L 144.176318 72.218682 \n", "L 148.082314 72.218682 \n", "L 151.98832 72.218682 \n", "L 155.894316 72.218682 \n", "L 159.800313 72.218682 \n", "L 163.706318 72.218682 \n", "L 167.612314 72.218682 \n", "L 171.518311 72.218682 \n", "L 175.424316 72.218682 \n", "L 179.330312 72.218682 \n", "L 183.236318 72.218682 \n", "L 187.142314 72.218682 \n", "L 191.048311 72.218682 \n", "L 194.954316 72.218682 \n", "L 198.860313 72.218682 \n", "L 202.766309 72.218682 \n", "L 206.672305 72.218682 \n", "L 210.57832 72.218682 \n", "L 214.484316 72.218682 \n", "L 218.390312 72.218682 \n", "L 222.296309 72.218682 \n", "L 226.202305 72.218682 \n", "L 230.10832 72.218682 \n", "L 234.014316 72.218682 \n", "\" clip-path=\"url(#pd6c575ca4c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <defs>\n", "     <path id=\"me067149fa7\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pd6c575ca4c)\">\n", "     <use xlink:href=\"#me067149fa7\" x=\"44.53748\" y=\"115.691614\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"53.448794\" y=\"95.24464\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"53.456186\" y=\"102.006132\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"61.323938\" y=\"81.377229\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"61.973284\" y=\"104.985433\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"67.234966\" y=\"98.531446\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"72.737485\" y=\"67.035529\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"74.025049\" y=\"58.504771\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"74.427716\" y=\"73.529851\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"74.703754\" y=\"53.01323\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"78.980509\" y=\"55.547978\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"79.210298\" y=\"81.702658\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"80.324042\" y=\"57.907997\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"94.654421\" y=\"50.576982\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"98.469233\" y=\"64.032823\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"100.57467\" y=\"51.948261\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"103.139452\" y=\"50.25032\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"103.852892\" y=\"40.531134\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"114.970822\" y=\"26.130925\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"115.993162\" y=\"26.850143\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"128.190472\" y=\"49.921324\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"133.626701\" y=\"49.196966\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"144.848727\" y=\"38.478676\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"145.132576\" y=\"44.551941\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"149.033739\" y=\"46.507082\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"149.883442\" y=\"65.130421\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"151.084157\" y=\"57.961767\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"153.751042\" y=\"65.197847\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"161.633224\" y=\"60.275329\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"163.618761\" y=\"54.63113\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"175.661025\" y=\"82.915384\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"183.692535\" y=\"65.358778\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"186.00514\" y=\"74.095531\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"186.471526\" y=\"77.891356\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"187.771746\" y=\"73.369498\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"191.008313\" y=\"96.511927\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"191.295002\" y=\"83.783606\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"192.550121\" y=\"84.167646\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"193.17227\" y=\"93.204219\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"195.53724\" y=\"85.473731\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"200.542788\" y=\"104.955262\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"202.773498\" y=\"96.949169\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"204.106247\" y=\"95.890974\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"206.505944\" y=\"86.361542\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"208.370798\" y=\"101.968985\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"209.090348\" y=\"105.224567\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"219.972677\" y=\"79.851265\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"227.266441\" y=\"69.892603\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"230.799932\" y=\"70.822735\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#me067149fa7\" x=\"234.592128\" y=\"94.993761\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.899219 \n", "L 42.620312 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.899219 \n", "L 237.920313 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.899219 \n", "L 237.**********.899219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 10.999219 \n", "L 237.920313 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 173.58125 48.355469 \n", "L 230.920313 48.355469 \n", "Q 232.920313 48.355469 232.920313 46.355469 \n", "L 232.920313 17.999219 \n", "Q 232.920313 15.999219 230.920313 15.999219 \n", "L 173.58125 15.999219 \n", "Q 171.58125 15.999219 171.58125 17.999219 \n", "L 171.58125 46.355469 \n", "Q 171.58125 48.355469 173.58125 48.355469 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 175.58125 24.097656 \n", "L 185.58125 24.097656 \n", "L 195.58125 24.097656 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Truth -->\n", "     <g transform=\"translate(203.58125 27.597656)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"46.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"87.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"150.826172\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"190.035156\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_31\">\n", "     <path d=\"M 175.58125 38.775781 \n", "L 185.58125 38.775781 \n", "L 195.58125 38.775781 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Pred -->\n", "     <g transform=\"translate(203.58125 42.275781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"58.552734\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"97.416016\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"158.939453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pd6c575ca4c\">\n", "   <rect x=\"42.620312\" y=\"10.999219\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["y_hat = torch.repeat_interleave(y_train.mean(), n_test)\n", "plot_kernel_reg(y_hat)"]}, {"cell_type": "markdown", "id": "2d4155fa", "metadata": {"origin_pos": 21}, "source": ["## [**非参数注意力汇聚**]\n", "\n", "显然，平均汇聚忽略了输入$x_i$。\n", "于是Nadaraya :cite:`<PERSON><PERSON><PERSON>.1964`和\n", "Watson :cite:<PERSON><PERSON>.1964`提出了一个更好的想法，\n", "根据输入的位置对输出$y_i$进行加权：\n", "\n", "$$f(x) = \\sum_{i=1}^n \\frac{K(x - x_i)}{\\sum_{j=1}^n K(x - x_j)} y_i,$$\n", ":eqlabel:`eq_nadar<PERSON>-watson`\n", "\n", "其中$K$是*核*（kernel）。\n", "公式 :eqref:`eq_nadar<PERSON>-watson`所描述的估计器被称为\n", "*Nadaraya-Watson核回归*（Nadaraya-Watson kernel regression）。\n", "这里不会深入讨论核函数的细节，\n", "但受此启发，\n", "我们可以从 :numref:`fig_qkv`中的注意力机制框架的角度\n", "重写 :eqref:`eq_nadar<PERSON>-watson`，\n", "成为一个更加通用的*注意力汇聚*（attention pooling）公式：\n", "\n", "$$f(x) = \\sum_{i=1}^n \\alpha(x, x_i) y_i,$$\n", ":eqlabel:`eq_attn-pooling`\n", "\n", "其中$x$是查询，$(x_i, y_i)$是键值对。\n", "比较 :eqref:`eq_attn-pooling`和 :eqref:`eq_avg-pooling`，\n", "注意力汇聚是$y_i$的加权平均。\n", "将查询$x$和键$x_i$之间的关系建模为\n", "*注意力权重*（attention weight）$\\alpha(x, x_i)$，\n", "如 :eqref:`eq_attn-pooling`所示，\n", "这个权重将被分配给每一个对应值$y_i$。\n", "对于任何查询，模型在所有键值对注意力权重都是一个有效的概率分布：\n", "它们是非负的，并且总和为1。\n", "\n", "为了更好地理解注意力汇聚，\n", "下面考虑一个*高斯核*（Gaussian kernel），其定义为：\n", "\n", "$$K(u) = \\frac{1}{\\sqrt{2\\pi}} \\exp(-\\frac{u^2}{2}).$$\n", "\n", "将高斯核代入 :eqref:`eq_attn-pooling`和\n", " :eqref:`eq_nadar<PERSON>-watson`可以得到：\n", "\n", "$$\\begin{aligned} f(x) &=\\sum_{i=1}^n \\alpha(x, x_i) y_i\\\\ &= \\sum_{i=1}^n \\frac{\\exp\\left(-\\frac{1}{2}(x - x_i)^2\\right)}{\\sum_{j=1}^n \\exp\\left(-\\frac{1}{2}(x - x_j)^2\\right)} y_i \\\\&= \\sum_{i=1}^n \\mathrm{softmax}\\left(-\\frac{1}{2}(x - x_i)^2\\right) y_i. \\end{aligned}$$\n", ":eqlabel:`eq_nadar<PERSON>-watson-gaussian`\n", "\n", "在 :eqref:`eq_nadaraya-watson-gaussian`中，\n", "如果一个键$x_i$越是接近给定的查询$x$，\n", "那么分配给这个键对应值$y_i$的注意力权重就会越大，\n", "也就“获得了更多的注意力”。\n", "\n", "值得注意的是，Na<PERSON>aya-Watson核回归是一个非参数模型。\n", "因此， :eqref:`eq_nadar<PERSON>-watson-gaussian`是\n", "*非参数的注意力汇聚*（nonparametric attention pooling）模型。\n", "接下来，我们将基于这个非参数的注意力汇聚模型来绘制预测结果。\n", "从绘制的结果会发现新的模型预测线是平滑的，并且比平均汇聚的预测更接近真实。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "892e92a9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:16.925532Z", "iopub.status.busy": "2023-08-18T07:07:16.924886Z", "iopub.status.idle": "2023-08-18T07:07:17.137585Z", "shell.execute_reply": "2023-08-18T07:07:17.136490Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"248.301563pt\" height=\"184.455469pt\" viewBox=\"0 0 248.**********.455469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:07:17.059877</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 184.455469 \n", "L 248.**********.455469 \n", "L 248.301563 -0 \n", "L 0 -0 \n", "L 0 184.455469 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.899219 \n", "L 237.**********.899219 \n", "L 237.920313 10.999219 \n", "L 42.620312 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 42.**********.899219 \n", "L 42.620312 10.999219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mfad18cadbf\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mfad18cadbf\" x=\"42.620312\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(39.439062 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 81.680312 146.899219 \n", "L 81.680312 10.999219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mfad18cadbf\" x=\"81.680312\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(78.499062 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 120.740313 146.899219 \n", "L 120.740313 10.999219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mfad18cadbf\" x=\"120.740313\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(117.559062 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 159.800313 146.899219 \n", "L 159.800313 10.999219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mfad18cadbf\" x=\"159.800313\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(156.619063 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 198.860313 146.899219 \n", "L 198.860313 10.999219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mfad18cadbf\" x=\"198.860313\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(195.679063 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 237.**********.899219 \n", "L 237.920313 10.999219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mfad18cadbf\" x=\"237.920313\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(234.739063 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- x -->\n", "     <g transform=\"translate(137.310937 175.175781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 42.**********.899219 \n", "L 237.**********.899219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"m759de4d5c5\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m759de4d5c5\" x=\"42.620312\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(20.878125 150.698437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 42.620312 124.249219 \n", "L 237.920313 124.249219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m759de4d5c5\" x=\"42.620312\" y=\"124.249219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 128.048437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 42.620312 101.599219 \n", "L 237.920313 101.599219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m759de4d5c5\" x=\"42.620312\" y=\"101.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(29.257812 105.398437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 42.620312 78.949219 \n", "L 237.920313 78.949219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m759de4d5c5\" x=\"42.620312\" y=\"78.949219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(29.257812 82.748437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 42.620312 56.299219 \n", "L 237.920313 56.299219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m759de4d5c5\" x=\"42.620312\" y=\"56.299219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(29.257812 60.098437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 42.620312 33.649219 \n", "L 237.920313 33.649219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m759de4d5c5\" x=\"42.620312\" y=\"33.649219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(29.257812 37.448437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_25\">\n", "      <path d=\"M 42.620312 10.999219 \n", "L 237.920313 10.999219 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m759de4d5c5\" x=\"42.620312\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(29.257812 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- y -->\n", "     <g transform=\"translate(14.798437 81.908594)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 42.620312 124.249219 \n", "L 46.526313 116.136982 \n", "L 50.432313 108.999323 \n", "L 54.338313 102.217148 \n", "L 58.244313 95.726379 \n", "L 62.150312 89.522233 \n", "L 66.056313 83.619087 \n", "L 69.962312 78.038824 \n", "L 73.868313 72.806 \n", "L 77.774314 67.945395 \n", "L 81.680312 63.480584 \n", "L 85.586313 59.432954 \n", "L 89.492314 55.821098 \n", "L 93.398311 52.660258 \n", "L 97.304312 49.962036 \n", "L 101.210313 47.734078 \n", "L 105.116313 45.979911 \n", "L 109.022314 44.69881 \n", "L 112.928315 43.885737 \n", "L 116.834312 43.531371 \n", "L 120.740313 43.622105 \n", "L 124.646309 44.140188 \n", "L 128.552314 45.063873 \n", "L 132.458311 46.367563 \n", "L 136.364316 48.022135 \n", "L 140.270312 49.995107 \n", "L 144.176318 52.251038 \n", "L 148.082314 54.751808 \n", "L 151.98832 57.457039 \n", "L 155.894316 60.324422 \n", "L 159.800313 63.310193 \n", "L 163.706318 66.369546 \n", "L 167.612314 69.45704 \n", "L 171.518311 72.527086 \n", "L 175.424316 75.534393 \n", "L 179.330312 78.434371 \n", "L 183.236318 81.183652 \n", "L 187.142314 83.740416 \n", "L 191.048311 86.06491 \n", "L 194.954316 88.119776 \n", "L 198.860313 89.870412 \n", "L 202.766309 91.285368 \n", "L 206.672305 92.336626 \n", "L 210.57832 92.999866 \n", "L 214.484316 93.254719 \n", "L 218.390312 93.084986 \n", "L 222.296309 92.478756 \n", "L 226.202305 91.428572 \n", "L 230.10832 89.931453 \n", "L 234.014316 87.988973 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 42.620312 76.564823 \n", "L 46.526313 75.548206 \n", "L 50.432313 74.510367 \n", "L 54.338313 73.452645 \n", "L 58.244313 72.377042 \n", "L 62.150312 71.286389 \n", "L 66.056313 70.184477 \n", "L 69.962312 69.07636 \n", "L 73.868313 67.968502 \n", "L 77.774314 66.86896 \n", "L 81.680312 65.787655 \n", "L 85.586313 64.736483 \n", "L 89.492314 63.72936 \n", "L 93.398311 62.782179 \n", "L 97.304312 61.912588 \n", "L 101.210313 61.139557 \n", "L 105.116313 60.48276 \n", "L 109.022314 59.961632 \n", "L 112.928315 59.594355 \n", "L 116.834312 59.396595 \n", "L 120.740313 59.380324 \n", "L 124.646309 59.552676 \n", "L 128.552314 59.915223 \n", "L 132.458311 60.463449 \n", "L 136.364316 61.186858 \n", "L 140.270312 62.069485 \n", "L 144.176318 63.090902 \n", "L 148.082314 64.227397 \n", "L 151.98832 65.453454 \n", "L 155.894316 66.743104 \n", "L 159.800313 68.071105 \n", "L 163.706318 69.414022 \n", "L 167.612314 70.750859 \n", "L 171.518311 72.063562 \n", "L 175.424316 73.337113 \n", "L 179.330312 74.559547 \n", "L 183.236318 75.721757 \n", "L 187.142314 76.817195 \n", "L 191.048311 77.841588 \n", "L 194.954316 78.792554 \n", "L 198.860313 79.669273 \n", "L 202.766309 80.472189 \n", "L 206.672305 81.20273 \n", "L 210.57832 81.863065 \n", "L 214.484316 82.455894 \n", "L 218.390312 82.984336 \n", "L 222.296309 83.451717 \n", "L 226.202305 83.861542 \n", "L 230.10832 84.217371 \n", "L 234.014316 84.522778 \n", "\" clip-path=\"url(#pb7b4e92db6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <defs>\n", "     <path id=\"m7f2e5b6497\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pb7b4e92db6)\">\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"44.53748\" y=\"115.691614\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"53.448794\" y=\"95.24464\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"53.456186\" y=\"102.006132\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"61.323938\" y=\"81.377229\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"61.973284\" y=\"104.985433\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"67.234966\" y=\"98.531446\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"72.737485\" y=\"67.035529\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"74.025049\" y=\"58.504771\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"74.427716\" y=\"73.529851\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"74.703754\" y=\"53.01323\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"78.980509\" y=\"55.547978\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"79.210298\" y=\"81.702658\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"80.324042\" y=\"57.907997\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"94.654421\" y=\"50.576982\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"98.469233\" y=\"64.032823\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"100.57467\" y=\"51.948261\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"103.139452\" y=\"50.25032\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"103.852892\" y=\"40.531134\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"114.970822\" y=\"26.130925\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"115.993162\" y=\"26.850143\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"128.190472\" y=\"49.921324\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"133.626701\" y=\"49.196966\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"144.848727\" y=\"38.478676\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"145.132576\" y=\"44.551941\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"149.033739\" y=\"46.507082\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"149.883442\" y=\"65.130421\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"151.084157\" y=\"57.961767\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"153.751042\" y=\"65.197847\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"161.633224\" y=\"60.275329\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"163.618761\" y=\"54.63113\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"175.661025\" y=\"82.915384\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"183.692535\" y=\"65.358778\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"186.00514\" y=\"74.095531\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"186.471526\" y=\"77.891356\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"187.771746\" y=\"73.369498\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"191.008313\" y=\"96.511927\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"191.295002\" y=\"83.783606\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"192.550121\" y=\"84.167646\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"193.17227\" y=\"93.204219\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"195.53724\" y=\"85.473731\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"200.542788\" y=\"104.955262\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"202.773498\" y=\"96.949169\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"204.106247\" y=\"95.890974\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"206.505944\" y=\"86.361542\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"208.370798\" y=\"101.968985\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"209.090348\" y=\"105.224567\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"219.972677\" y=\"79.851265\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"227.266441\" y=\"69.892603\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"230.799932\" y=\"70.822735\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m7f2e5b6497\" x=\"234.592128\" y=\"94.993761\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.899219 \n", "L 42.620312 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.899219 \n", "L 237.920313 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.899219 \n", "L 237.**********.899219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 10.999219 \n", "L 237.920313 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 173.58125 48.355469 \n", "L 230.920313 48.355469 \n", "Q 232.920313 48.355469 232.920313 46.355469 \n", "L 232.920313 17.999219 \n", "Q 232.920313 15.999219 230.920313 15.999219 \n", "L 173.58125 15.999219 \n", "Q 171.58125 15.999219 171.58125 17.999219 \n", "L 171.58125 46.355469 \n", "Q 171.58125 48.355469 173.58125 48.355469 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 175.58125 24.097656 \n", "L 185.58125 24.097656 \n", "L 195.58125 24.097656 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Truth -->\n", "     <g transform=\"translate(203.58125 27.597656)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"46.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"87.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"150.826172\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"190.035156\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_31\">\n", "     <path d=\"M 175.58125 38.775781 \n", "L 185.58125 38.775781 \n", "L 195.58125 38.775781 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Pred -->\n", "     <g transform=\"translate(203.58125 42.275781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"58.552734\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"97.416016\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"158.939453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb7b4e92db6\">\n", "   <rect x=\"42.620312\" y=\"10.999219\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# X_repeat的形状:(n_test,n_train),\n", "# 每一行都包含着相同的测试输入（例如：同样的查询）\n", "X_repeat = x_test.repeat_interleave(n_train).reshape((-1, n_train))\n", "# x_train包含着键。attention_weights的形状：(n_test,n_train),\n", "# 每一行都包含着要在给定的每个查询的值（y_train）之间分配的注意力权重\n", "attention_weights = nn.functional.softmax(-(X_repeat - x_train)**2 / 2, dim=1)\n", "# y_hat的每个元素都是值的加权平均值，其中的权重是注意力权重\n", "y_hat = torch.matmul(attention_weights, y_train)\n", "plot_kernel_reg(y_hat)"]}, {"cell_type": "markdown", "id": "60a40ebc", "metadata": {"origin_pos": 26}, "source": ["现在来观察注意力的权重。\n", "这里测试数据的输入相当于查询，而训练数据的输入相当于键。\n", "因为两个输入都是经过排序的，因此由观察可知“查询-键”对越接近，\n", "注意力汇聚的[**注意力权重**]就越高。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "5068c7e7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:17.141869Z", "iopub.status.busy": "2023-08-18T07:07:17.141398Z", "iopub.status.idle": "2023-08-18T07:07:17.325925Z", "shell.execute_reply": "2023-08-18T07:07:17.325070Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"199.72075pt\" height=\"159.039469pt\" viewBox=\"0 0 199.72075 159.039469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:07:17.274591</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 159.039469 \n", "L 199.72075 159.039469 \n", "L 199.72075 0 \n", "L 0 0 \n", "L 0 159.039469 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.483219 \n", "L 152.**********.483219 \n", "L 152.203125 9.883219 \n", "L 40.603125 9.883219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd1dedcbce6)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image820c03a7f7\" transform=\"scale(1 -1)translate(0 -112)\" x=\"40.603125\" y=\"-9.483219\" width=\"112\" height=\"112\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m0d93bb7cc1\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m0d93bb7cc1\" x=\"41.719125\" y=\"121.483219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(38.537875 136.081656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m0d93bb7cc1\" x=\"86.359125\" y=\"121.483219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(79.996625 136.081656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m0d93bb7cc1\" x=\"130.999125\" y=\"121.483219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(124.636625 136.081656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- Sorted training inputs -->\n", "     <g transform=\"translate(41.889844 149.759781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"124.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"165.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"204.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"266.503906\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"329.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"361.767578\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"400.976562\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"442.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"503.369141\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"531.152344\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"594.53125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"622.314453\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"685.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"749.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"780.957031\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"808.740234\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"872.119141\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"935.595703\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"998.974609\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1038.183594\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m1115eca684\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1115eca684\" x=\"40.603125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(27.240625 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m1115eca684\" x=\"40.603125\" y=\"33.319219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(20.878125 37.118437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m1115eca684\" x=\"40.603125\" y=\"55.639219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(20.878125 59.438437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m1115eca684\" x=\"40.603125\" y=\"77.959219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(20.878125 81.758437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m1115eca684\" x=\"40.603125\" y=\"100.279219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(20.878125 104.078437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Sorted testing inputs -->\n", "     <g transform=\"translate(14.798438 118.160562)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"124.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"165.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"204.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"266.503906\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"329.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"361.767578\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"400.976562\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"462.5\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"514.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"553.808594\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"581.591797\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"644.970703\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"708.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"740.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"768.017578\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"831.396484\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"894.873047\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"958.251953\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"997.460938\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.483219 \n", "L 40.603125 9.883219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 152.**********.483219 \n", "L 152.203125 9.883219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.483219 \n", "L 152.**********.483219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 9.883219 \n", "L 152.203125 9.883219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 159.178125 106.453219 \n", "L 163.255125 106.453219 \n", "L 163.255125 24.913219 \n", "L 159.178125 24.913219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path clip-path=\"url(#pafb8fe0273)\" style=\"fill: #ffffff; stroke: #ffffff; stroke-width: 0.01; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAQAAABRCAYAAAD1sgc6AAAAnklEQVR4nJ2Suw7CQAwEjZT//1QqCnR+0XKzJxmSLqPx7krJo1/Ptq/nsgzbQRVBDqBH4wCkhTtkWDhqR8OSIHjii6H/Z2jtaLD2EDpm3DCkNvldftgB0GJIqM/TBfAfE+AAJcaSpZJRg1Fs0QyCjN5BRA0gkycEmTyRFhox7simsb/blSbGDWCDcdgB4MxwGu8CWAJ4shgqJ9IiBms/jwXJt9gA8G4AAAAASUVORK5CYII=\" id=\"image7fb7394891\" transform=\"scale(1 -1)translate(0 -81)\" x=\"159\" y=\"-25\" width=\"4\" height=\"81\"/>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <defs>\n", "       <path id=\"m866ee04ffe\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m866ee04ffe\" x=\"163.255125\" y=\"85.676442\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.02 -->\n", "      <g transform=\"translate(170.255125 89.475661)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m866ee04ffe\" x=\"163.255125\" y=\"64.899201\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.04 -->\n", "      <g transform=\"translate(170.255125 68.69842)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m866ee04ffe\" x=\"163.255125\" y=\"44.12196\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.06 -->\n", "      <g transform=\"translate(170.255125 47.921179)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 159.178125 106.453219 \n", "L 161.216625 106.453219 \n", "L 163.255125 106.453219 \n", "L 163.255125 24.913219 \n", "L 161.216625 24.913219 \n", "L 159.178125 24.913219 \n", "L 159.178125 106.453219 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pd1dedcbce6\">\n", "   <rect x=\"40.603125\" y=\"9.883219\" width=\"111.6\" height=\"111.6\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pafb8fe0273\">\n", "   <rect x=\"159.178125\" y=\"24.913219\" width=\"4.077\" height=\"81.54\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 180x180 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["d2l.show_heatmaps(attention_weights.unsqueeze(0).unsqueeze(0),\n", "                  xlabel='Sorted training inputs',\n", "                  ylabel='Sorted testing inputs')"]}, {"cell_type": "markdown", "id": "73a72797", "metadata": {"origin_pos": 31}, "source": ["## [**带参数注意力汇聚**]\n", "\n", "非参数的Nadaraya-Watson核回归具有*一致性*（consistency）的优点：\n", "如果有足够的数据，此模型会收敛到最优结果。\n", "尽管如此，我们还是可以轻松地将可学习的参数集成到注意力汇聚中。\n", "\n", "例如，与 :eqref:`eq_nadar<PERSON>-watson-gaussian`略有不同，\n", "在下面的查询$x$和键$x_i$之间的距离乘以可学习参数$w$：\n", "\n", "$$\\begin{aligned}f(x) &= \\sum_{i=1}^n \\alpha(x, x_i) y_i \\\\&= \\sum_{i=1}^n \\frac{\\exp\\left(-\\frac{1}{2}((x - x_i)w)^2\\right)}{\\sum_{j=1}^n \\exp\\left(-\\frac{1}{2}((x - x_j)w)^2\\right)} y_i \\\\&= \\sum_{i=1}^n \\mathrm{softmax}\\left(-\\frac{1}{2}((x - x_i)w)^2\\right) y_i.\\end{aligned}$$\n", ":eqlabel:`eq_nadaraya-watson-gaussian-para`\n", "\n", "本节的余下部分将通过训练这个模型\n", " :eqref:`eq_nadar<PERSON>-watson-gaussian-para`来学习注意力汇聚的参数。\n", "\n", "### 批量矩阵乘法\n", "\n", ":label:`subsec_batch_dot`\n", "\n", "为了更有效地计算小批量数据的注意力，\n", "我们可以利用深度学习开发框架中提供的批量矩阵乘法。\n", "\n", "假设第一个小批量数据包含$n$个矩阵$\\mathbf{X}_1,\\ldots, \\mathbf{X}_n$，\n", "形状为$a\\times b$，\n", "第二个小批量包含$n$个矩阵$\\mathbf{Y}_1, \\ldots, \\mathbf{Y}_n$，\n", "形状为$b\\times c$。\n", "它们的批量矩阵乘法得到$n$个矩阵\n", "$\\mathbf{X}_1\\mathbf{Y}_1, \\ldots, \\mathbf{X}_n\\mathbf{Y}_n$，\n", "形状为$a\\times c$。\n", "因此，[**假定两个张量的形状分别是$(n,a,b)$和$(n,b,c)$，\n", "它们的批量矩阵乘法输出的形状为$(n,a,c)$**]。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "c5a4dc9e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:17.329873Z", "iopub.status.busy": "2023-08-18T07:07:17.329293Z", "iopub.status.idle": "2023-08-18T07:07:17.336079Z", "shell.execute_reply": "2023-08-18T07:07:17.335148Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 1, 6])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.ones((2, 1, 4))\n", "Y = torch.ones((2, 4, 6))\n", "torch.bmm(X, Y).shape"]}, {"cell_type": "markdown", "id": "acac0804", "metadata": {"origin_pos": 36}, "source": ["在注意力机制的背景中，我们可以[**使用小批量矩阵乘法来计算小批量数据中的加权平均值**]。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "7161cf35", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:17.341257Z", "iopub.status.busy": "2023-08-18T07:07:17.340341Z", "iopub.status.idle": "2023-08-18T07:07:17.348773Z", "shell.execute_reply": "2023-08-18T07:07:17.347847Z"}, "origin_pos": 38, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[[ 4.5000]],\n", "\n", "        [[14.5000]]])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["weights = torch.ones((2, 10)) * 0.1\n", "values = torch.arange(20.0).reshape((2, 10))\n", "torch.bmm(weights.unsqueeze(1), values.unsqueeze(-1))"]}, {"cell_type": "markdown", "id": "f31cb449", "metadata": {"origin_pos": 41}, "source": ["### 定义模型\n", "\n", "基于 :eqref:`eq_nadar<PERSON>-watson-gaussian-para`中的\n", "[**带参数的注意力汇聚**]，使用小批量矩阵乘法，\n", "定义Nadaraya-Watson核回归的带参数版本为：\n"]}, {"cell_type": "code", "execution_count": 10, "id": "e7aee504", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:17.353382Z", "iopub.status.busy": "2023-08-18T07:07:17.353094Z", "iopub.status.idle": "2023-08-18T07:07:17.359677Z", "shell.execute_reply": "2023-08-18T07:07:17.358720Z"}, "origin_pos": 43, "tab": ["pytorch"]}, "outputs": [], "source": ["class NWKernelRegression(nn.Module):\n", "    def __init__(self, **kwargs):\n", "        super().__init__(**kwargs)\n", "        self.w = nn.Parameter(torch.rand((1,), requires_grad=True))\n", "\n", "    def forward(self, queries, keys, values):\n", "        # queries和attention_weights的形状为(查询个数，“键－值”对个数)\n", "        queries = queries.repeat_interleave(keys.shape[1]).reshape((-1, keys.shape[1]))\n", "        self.attention_weights = nn.functional.softmax(\n", "            -((queries - keys) * self.w)**2 / 2, dim=1)\n", "        # values的形状为(查询个数，“键－值”对个数)\n", "        return torch.bmm(self.attention_weights.unsqueeze(1),\n", "                         values.unsqueeze(-1)).reshape(-1)"]}, {"cell_type": "markdown", "id": "192a922f", "metadata": {"origin_pos": 46}, "source": ["### 训练\n", "\n", "接下来，[**将训练数据集变换为键和值**]用于训练注意力模型。\n", "在带参数的注意力汇聚模型中，\n", "任何一个训练样本的输入都会和除自己以外的所有训练样本的“键－值”对进行计算，\n", "从而得到其对应的预测输出。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "c738e178", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:17.363589Z", "iopub.status.busy": "2023-08-18T07:07:17.362749Z", "iopub.status.idle": "2023-08-18T07:07:17.369359Z", "shell.execute_reply": "2023-08-18T07:07:17.368357Z"}, "origin_pos": 48, "tab": ["pytorch"]}, "outputs": [], "source": ["# X_tile的形状:(n_train，n_train)，每一行都包含着相同的训练输入\n", "X_tile = x_train.repeat((n_train, 1))\n", "# Y_tile的形状:(n_train，n_train)，每一行都包含着相同的训练输出\n", "Y_tile = y_train.repeat((n_train, 1))\n", "# keys的形状:('n_train'，'n_train'-1)\n", "keys = X_tile[(1 - torch.eye(n_train)).type(torch.bool)].reshape((n_train, -1))\n", "# values的形状:('n_train'，'n_train'-1)\n", "values = Y_tile[(1 - torch.eye(n_train)).type(torch.bool)].reshape((n_train, -1))"]}, {"cell_type": "markdown", "id": "245d8e27", "metadata": {"origin_pos": 51}, "source": ["[**训练带参数的注意力汇聚模型**]时，使用平方损失函数和随机梯度下降。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "37b732ff", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:17.373285Z", "iopub.status.busy": "2023-08-18T07:07:17.372386Z", "iopub.status.idle": "2023-08-18T07:07:18.050395Z", "shell.execute_reply": "2023-08-18T07:07:18.049441Z"}, "origin_pos": 53, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"246.284375pt\" height=\"184.423051pt\" viewBox=\"0 0 246.**********.423051\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:07:18.019132</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 184.423051 \n", "L 246.**********.423051 \n", "L 246.284375 0 \n", "L 0 0 \n", "L 0 184.423051 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.866801 \n", "L 235.**********.866801 \n", "L 235.903125 10.966801 \n", "L 40.603125 10.966801 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 40.**********.866801 \n", "L 40.603125 10.966801 \n", "\" clip-path=\"url(#p411ed6b226)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m90ebfe6f33\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m90ebfe6f33\" x=\"40.603125\" y=\"146.866801\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(37.421875 161.465238)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 89.428125 146.866801 \n", "L 89.428125 10.966801 \n", "\" clip-path=\"url(#p411ed6b226)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m90ebfe6f33\" x=\"89.428125\" y=\"146.866801\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(86.246875 161.465238)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 138.253125 146.866801 \n", "L 138.253125 10.966801 \n", "\" clip-path=\"url(#p411ed6b226)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m90ebfe6f33\" x=\"138.253125\" y=\"146.866801\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(135.071875 161.465238)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 187.078125 146.866801 \n", "L 187.078125 10.966801 \n", "\" clip-path=\"url(#p411ed6b226)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m90ebfe6f33\" x=\"187.078125\" y=\"146.866801\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(183.896875 161.465238)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 235.**********.866801 \n", "L 235.903125 10.966801 \n", "\" clip-path=\"url(#p411ed6b226)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m90ebfe6f33\" x=\"235.903125\" y=\"146.866801\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(232.721875 161.465238)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(123.025 175.143363)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 40.603125 114.379921 \n", "L 235.903125 114.379921 \n", "\" clip-path=\"url(#p411ed6b226)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mddc2eb5a39\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mddc2eb5a39\" x=\"40.603125\" y=\"114.379921\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(20.878125 118.179139)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 40.603125 79.919687 \n", "L 235.903125 79.919687 \n", "\" clip-path=\"url(#p411ed6b226)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mddc2eb5a39\" x=\"40.603125\" y=\"79.919687\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(20.878125 83.718905)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 40.603125 45.459453 \n", "L 235.903125 45.459453 \n", "\" clip-path=\"url(#p411ed6b226)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mddc2eb5a39\" x=\"40.603125\" y=\"45.459453\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(20.878125 49.258671)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 40.603125 10.999219 \n", "L 235.903125 10.999219 \n", "\" clip-path=\"url(#p411ed6b226)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mddc2eb5a39\" x=\"40.603125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(20.878125 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798437 88.574613)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 40.603125 17.144073 \n", "L 89.428125 129.468508 \n", "L 138.253125 136.720706 \n", "L 187.078125 139.105918 \n", "L 235.903125 140.689528 \n", "\" clip-path=\"url(#p411ed6b226)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.866801 \n", "L 40.603125 10.966801 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 235.**********.866801 \n", "L 235.903125 10.966801 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.866801 \n", "L 235.**********.866801 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 10.966801 \n", "L 235.903125 10.966801 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p411ed6b226\">\n", "   <rect x=\"40.603125\" y=\"10.966801\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["net = NWKernelRegression()\n", "loss = nn.MSELoss(reduction='none')\n", "trainer = torch.optim.SGD(net.parameters(), lr=0.5)\n", "animator = d2l.Animator(xlabel='epoch', ylabel='loss', xlim=[1, 5])\n", "\n", "for epoch in range(5):\n", "    trainer.zero_grad()\n", "    l = loss(net(x_train, keys, values), y_train)\n", "    l.sum().backward()\n", "    trainer.step()\n", "    print(f'epoch {epoch + 1}, loss {float(l.sum()):.6f}')\n", "    animator.add(epoch + 1, float(l.sum()))"]}, {"cell_type": "markdown", "id": "bcd73f8a", "metadata": {"origin_pos": 56}, "source": ["如下所示，训练完带参数的注意力汇聚模型后可以发现：\n", "在尝试拟合带噪声的训练数据时，\n", "[**预测结果绘制**]的线不如之前非参数模型的平滑。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "79e57e0d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:18.054215Z", "iopub.status.busy": "2023-08-18T07:07:18.053635Z", "iopub.status.idle": "2023-08-18T07:07:18.224763Z", "shell.execute_reply": "2023-08-18T07:07:18.223767Z"}, "origin_pos": 58, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"248.301563pt\" height=\"184.455469pt\" viewBox=\"0 0 248.**********.455469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:07:18.177522</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 184.455469 \n", "L 248.**********.455469 \n", "L 248.301563 -0 \n", "L 0 -0 \n", "L 0 184.455469 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.899219 \n", "L 237.**********.899219 \n", "L 237.920313 10.999219 \n", "L 42.620312 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 42.**********.899219 \n", "L 42.620312 10.999219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mbc4b531aed\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mbc4b531aed\" x=\"42.620312\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(39.439062 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 81.680312 146.899219 \n", "L 81.680312 10.999219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mbc4b531aed\" x=\"81.680312\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(78.499062 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 120.740313 146.899219 \n", "L 120.740313 10.999219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mbc4b531aed\" x=\"120.740313\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(117.559062 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 159.800313 146.899219 \n", "L 159.800313 10.999219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mbc4b531aed\" x=\"159.800313\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(156.619063 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 198.860313 146.899219 \n", "L 198.860313 10.999219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mbc4b531aed\" x=\"198.860313\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(195.679063 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 237.**********.899219 \n", "L 237.920313 10.999219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mbc4b531aed\" x=\"237.920313\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(234.739063 161.497656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- x -->\n", "     <g transform=\"translate(137.310937 175.175781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 42.**********.899219 \n", "L 237.**********.899219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"m701bb0a9fb\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m701bb0a9fb\" x=\"42.620312\" y=\"146.899219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(20.878125 150.698437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 42.620312 124.249219 \n", "L 237.920313 124.249219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m701bb0a9fb\" x=\"42.620312\" y=\"124.249219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 128.048437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 42.620312 101.599219 \n", "L 237.920313 101.599219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m701bb0a9fb\" x=\"42.620312\" y=\"101.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(29.257812 105.398437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 42.620312 78.949219 \n", "L 237.920313 78.949219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m701bb0a9fb\" x=\"42.620312\" y=\"78.949219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(29.257812 82.748437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 42.620312 56.299219 \n", "L 237.920313 56.299219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m701bb0a9fb\" x=\"42.620312\" y=\"56.299219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(29.257812 60.098437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 42.620312 33.649219 \n", "L 237.920313 33.649219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m701bb0a9fb\" x=\"42.620312\" y=\"33.649219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(29.257812 37.448437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_25\">\n", "      <path d=\"M 42.620312 10.999219 \n", "L 237.920313 10.999219 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m701bb0a9fb\" x=\"42.620312\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(29.257812 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- y -->\n", "     <g transform=\"translate(14.798437 81.908594)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 42.620312 124.249219 \n", "L 46.526313 116.136982 \n", "L 50.432313 108.999323 \n", "L 54.338313 102.217148 \n", "L 58.244313 95.726379 \n", "L 62.150312 89.522233 \n", "L 66.056313 83.619087 \n", "L 69.962312 78.038824 \n", "L 73.868313 72.806 \n", "L 77.774314 67.945395 \n", "L 81.680312 63.480584 \n", "L 85.586313 59.432954 \n", "L 89.492314 55.821098 \n", "L 93.398311 52.660258 \n", "L 97.304312 49.962036 \n", "L 101.210313 47.734078 \n", "L 105.116313 45.979911 \n", "L 109.022314 44.69881 \n", "L 112.928315 43.885737 \n", "L 116.834312 43.531371 \n", "L 120.740313 43.622105 \n", "L 124.646309 44.140188 \n", "L 128.552314 45.063873 \n", "L 132.458311 46.367563 \n", "L 136.364316 48.022135 \n", "L 140.270312 49.995107 \n", "L 144.176318 52.251038 \n", "L 148.082314 54.751808 \n", "L 151.98832 57.457039 \n", "L 155.894316 60.324422 \n", "L 159.800313 63.310193 \n", "L 163.706318 66.369546 \n", "L 167.612314 69.45704 \n", "L 171.518311 72.527086 \n", "L 175.424316 75.534393 \n", "L 179.330312 78.434371 \n", "L 183.236318 81.183652 \n", "L 187.142314 83.740416 \n", "L 191.048311 86.06491 \n", "L 194.954316 88.119776 \n", "L 198.860313 89.870412 \n", "L 202.766309 91.285368 \n", "L 206.672305 92.336626 \n", "L 210.57832 92.999866 \n", "L 214.484316 93.254719 \n", "L 218.390312 93.084986 \n", "L 222.296309 92.478756 \n", "L 226.202305 91.428572 \n", "L 230.10832 89.931453 \n", "L 234.014316 87.988973 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 42.620312 108.110059 \n", "L 46.526313 104.601263 \n", "L 50.432313 100.987257 \n", "L 54.338313 97.522217 \n", "L 58.244313 93.611601 \n", "L 62.150312 88.062388 \n", "L 66.056313 80.802663 \n", "L 69.962312 73.871677 \n", "L 73.868313 69.042771 \n", "L 77.774314 66.252249 \n", "L 81.680312 64.462218 \n", "L 85.586313 62.216791 \n", "L 89.492314 58.38115 \n", "L 93.398311 54.663769 \n", "L 97.304312 52.3271 \n", "L 101.210313 50.095556 \n", "L 105.116313 46.684348 \n", "L 109.022314 41.508647 \n", "L 112.928315 35.968386 \n", "L 116.834312 33.253699 \n", "L 120.740313 35.229349 \n", "L 124.646309 40.562563 \n", "L 128.552314 45.325328 \n", "L 132.458311 47.412611 \n", "L 136.364316 47.982028 \n", "L 140.270312 48.889469 \n", "L 144.176318 50.638236 \n", "L 148.082314 52.796413 \n", "L 151.98832 54.970628 \n", "L 155.894316 56.872081 \n", "L 159.800313 58.391276 \n", "L 163.706318 60.196848 \n", "L 167.612314 63.965413 \n", "L 171.518311 70.039558 \n", "L 175.424316 74.924464 \n", "L 179.330312 77.343939 \n", "L 183.236318 79.276324 \n", "L 187.142314 81.678295 \n", "L 191.048311 84.641306 \n", "L 194.954316 88.092443 \n", "L 198.860313 91.653371 \n", "L 202.766309 94.500338 \n", "L 206.672305 95.975584 \n", "L 210.57832 95.701222 \n", "L 214.484316 92.751012 \n", "L 218.390312 86.551142 \n", "L 222.296309 80.375069 \n", "L 226.202305 77.837559 \n", "L 230.10832 78.270508 \n", "L 234.014316 80.06218 \n", "\" clip-path=\"url(#pa02ac21894)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <defs>\n", "     <path id=\"m4606ba7ad7\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pa02ac21894)\">\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"44.53748\" y=\"115.691614\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"53.448794\" y=\"95.24464\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"53.456186\" y=\"102.006132\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"61.323938\" y=\"81.377229\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"61.973284\" y=\"104.985433\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"67.234966\" y=\"98.531446\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"72.737485\" y=\"67.035529\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"74.025049\" y=\"58.504771\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"74.427716\" y=\"73.529851\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"74.703754\" y=\"53.01323\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"78.980509\" y=\"55.547978\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"79.210298\" y=\"81.702658\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"80.324042\" y=\"57.907997\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"94.654421\" y=\"50.576982\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"98.469233\" y=\"64.032823\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"100.57467\" y=\"51.948261\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"103.139452\" y=\"50.25032\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"103.852892\" y=\"40.531134\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"114.970822\" y=\"26.130925\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"115.993162\" y=\"26.850143\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"128.190472\" y=\"49.921324\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"133.626701\" y=\"49.196966\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"144.848727\" y=\"38.478676\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"145.132576\" y=\"44.551941\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"149.033739\" y=\"46.507082\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"149.883442\" y=\"65.130421\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"151.084157\" y=\"57.961767\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"153.751042\" y=\"65.197847\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"161.633224\" y=\"60.275329\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"163.618761\" y=\"54.63113\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"175.661025\" y=\"82.915384\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"183.692535\" y=\"65.358778\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"186.00514\" y=\"74.095531\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"186.471526\" y=\"77.891356\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"187.771746\" y=\"73.369498\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"191.008313\" y=\"96.511927\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"191.295002\" y=\"83.783606\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"192.550121\" y=\"84.167646\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"193.17227\" y=\"93.204219\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"195.53724\" y=\"85.473731\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"200.542788\" y=\"104.955262\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"202.773498\" y=\"96.949169\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"204.106247\" y=\"95.890974\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"206.505944\" y=\"86.361542\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"208.370798\" y=\"101.968985\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"209.090348\" y=\"105.224567\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"219.972677\" y=\"79.851265\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"227.266441\" y=\"69.892603\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"230.799932\" y=\"70.822735\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m4606ba7ad7\" x=\"234.592128\" y=\"94.993761\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.899219 \n", "L 42.620312 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.899219 \n", "L 237.920313 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.899219 \n", "L 237.**********.899219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 10.999219 \n", "L 237.920313 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 173.58125 48.355469 \n", "L 230.920313 48.355469 \n", "Q 232.920313 48.355469 232.920313 46.355469 \n", "L 232.920313 17.999219 \n", "Q 232.920313 15.999219 230.920313 15.999219 \n", "L 173.58125 15.999219 \n", "Q 171.58125 15.999219 171.58125 17.999219 \n", "L 171.58125 46.355469 \n", "Q 171.58125 48.355469 173.58125 48.355469 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 175.58125 24.097656 \n", "L 185.58125 24.097656 \n", "L 195.58125 24.097656 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Truth -->\n", "     <g transform=\"translate(203.58125 27.597656)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"46.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"87.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"150.826172\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"190.035156\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_31\">\n", "     <path d=\"M 175.58125 38.775781 \n", "L 185.58125 38.775781 \n", "L 195.58125 38.775781 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Pred -->\n", "     <g transform=\"translate(203.58125 42.275781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"58.552734\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"97.416016\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"158.939453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pa02ac21894\">\n", "   <rect x=\"42.620312\" y=\"10.999219\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# keys的形状:(n_test，n_train)，每一行包含着相同的训练输入（例如，相同的键）\n", "keys = x_train.repeat((n_test, 1))\n", "# value的形状:(n_test，n_train)\n", "values = y_train.repeat((n_test, 1))\n", "y_hat = net(x_test, keys, values).unsqueeze(1).detach()\n", "plot_kernel_reg(y_hat)"]}, {"cell_type": "markdown", "id": "962bf3fe", "metadata": {"origin_pos": 61}, "source": ["为什么新的模型更不平滑了呢？\n", "下面看一下输出结果的绘制图：\n", "与非参数的注意力汇聚模型相比，\n", "带参数的模型加入可学习的参数后，\n", "[**曲线在注意力权重较大的区域变得更不平滑**]。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "15949408", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:07:18.228629Z", "iopub.status.busy": "2023-08-18T07:07:18.227958Z", "iopub.status.idle": "2023-08-18T07:07:18.391875Z", "shell.execute_reply": "2023-08-18T07:07:18.391062Z"}, "origin_pos": 63, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"193.35825pt\" height=\"159.039469pt\" viewBox=\"0 0 193.35825 159.039469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:07:18.347398</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 159.039469 \n", "L 193.35825 159.039469 \n", "L 193.35825 0 \n", "L 0 0 \n", "L 0 159.039469 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.483219 \n", "L 152.**********.483219 \n", "L 152.203125 9.883219 \n", "L 40.603125 9.883219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p81e3486071)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image59a0a2da63\" transform=\"scale(1 -1)translate(0 -112)\" x=\"40.603125\" y=\"-9.483219\" width=\"112\" height=\"112\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m170685e700\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m170685e700\" x=\"41.719125\" y=\"121.483219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(38.537875 136.081656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m170685e700\" x=\"86.359125\" y=\"121.483219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(79.996625 136.081656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m170685e700\" x=\"130.999125\" y=\"121.483219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(124.636625 136.081656)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- Sorted training inputs -->\n", "     <g transform=\"translate(41.889844 149.759781)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"124.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"165.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"204.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"266.503906\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"329.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"361.767578\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"400.976562\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"442.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"503.369141\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"531.152344\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"594.53125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"622.314453\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"685.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"749.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"780.957031\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"808.740234\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"872.119141\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"935.595703\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"998.974609\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1038.183594\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m5d6eb57906\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5d6eb57906\" x=\"40.603125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(27.240625 14.798437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m5d6eb57906\" x=\"40.603125\" y=\"33.319219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(20.878125 37.118437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m5d6eb57906\" x=\"40.603125\" y=\"55.639219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(20.878125 59.438437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m5d6eb57906\" x=\"40.603125\" y=\"77.959219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(20.878125 81.758437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m5d6eb57906\" x=\"40.603125\" y=\"100.279219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(20.878125 104.078437)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Sorted testing inputs -->\n", "     <g transform=\"translate(14.798438 118.160562)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"124.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"165.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"204.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"266.503906\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"329.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"361.767578\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"400.976562\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"462.5\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"514.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"553.808594\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"581.591797\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"644.970703\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"708.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"740.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"768.017578\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"831.396484\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"894.873047\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"958.251953\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"997.460938\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.483219 \n", "L 40.603125 9.883219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 152.**********.483219 \n", "L 152.203125 9.883219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.483219 \n", "L 152.**********.483219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 9.883219 \n", "L 152.203125 9.883219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 159.178125 106.453219 \n", "L 163.255125 106.453219 \n", "L 163.255125 24.913219 \n", "L 159.178125 24.913219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path clip-path=\"url(#p1c432059e3)\" style=\"fill: #ffffff; stroke: #ffffff; stroke-width: 0.01; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAQAAABRCAYAAAD1sgc6AAAAnklEQVR4nJ2Suw7CQAwEjZT//1QqCnR+0XKzJxmSLqPx7krJo1/Ptq/nsgzbQRVBDqBH4wCkhTtkWDhqR8OSIHjii6H/Z2jtaLD2EDpm3DCkNvldftgB0GJIqM/TBfAfE+AAJcaSpZJRg1Fs0QyCjN5BRA0gkycEmTyRFhox7simsb/blSbGDWCDcdgB4MxwGu8CWAJ4shgqJ9IiBms/jwXJt9gA8G4AAAAASUVORK5CYII=\" id=\"image4fd4525e99\" transform=\"scale(1 -1)translate(0 -81)\" x=\"159\" y=\"-25\" width=\"4\" height=\"81\"/>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <defs>\n", "       <path id=\"m8ff20d1870\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8ff20d1870\" x=\"163.255125\" y=\"106.453219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(170.255125 110.252437)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m8ff20d1870\" x=\"163.255125\" y=\"77.920565\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(170.255125 81.719784)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m8ff20d1870\" x=\"163.255125\" y=\"49.387912\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(170.255125 53.187131)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 159.178125 106.453219 \n", "L 161.216625 106.453219 \n", "L 163.255125 106.453219 \n", "L 163.255125 24.913219 \n", "L 161.216625 24.913219 \n", "L 159.178125 24.913219 \n", "L 159.178125 106.453219 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p81e3486071\">\n", "   <rect x=\"40.603125\" y=\"9.883219\" width=\"111.6\" height=\"111.6\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p1c432059e3\">\n", "   <rect x=\"159.178125\" y=\"24.913219\" width=\"4.077\" height=\"81.54\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 180x180 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["d2l.show_heatmaps(net.attention_weights.unsqueeze(0).unsqueeze(0),\n", "                  xlabel='Sorted training inputs',\n", "                  ylabel='Sorted testing inputs')"]}, {"cell_type": "markdown", "id": "0e4f7d57", "metadata": {"origin_pos": 66}, "source": ["## 小结\n", "\n", "* Nadaraya-Watson核回归是具有注意力机制的机器学习范例。\n", "* Nadaraya-Watson核回归的注意力汇聚是对训练数据中输出的加权平均。从注意力的角度来看，分配给每个值的注意力权重取决于将值所对应的键和查询作为输入的函数。\n", "* 注意力汇聚可以分为非参数型和带参数型。\n", "\n", "## 练习\n", "\n", "1. 增加训练数据的样本数量，能否得到更好的非参数的Nadaraya-Watson核回归模型？\n", "1. 在带参数的注意力汇聚的实验中学习得到的参数$w$的价值是什么？为什么在可视化注意力权重时，它会使加权区域更加尖锐？\n", "1. 如何将超参数添加到非参数的Nadaraya-Watson核回归中以实现更好地预测结果？\n", "1. 为本节的核回归设计一个新的带参数的注意力汇聚模型。训练这个新模型并可视化其注意力权重。\n"]}, {"cell_type": "markdown", "id": "72ded07b", "metadata": {"origin_pos": 68, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/5760)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "ad81766b", "metadata": {"origin_pos": 0}, "source": ["# Transformer\n", ":label:`sec_transformer`\n", "\n", " :numref:`subsec_cnn-rnn-self-attention`中比较了卷积神经网络（CNN）、循环神经网络（RNN）和自注意力（self-attention）。值得注意的是，自注意力同时具有并行计算和最短的最大路径长度这两个优势。因此，使用自注意力来设计深度架构是很有吸引力的。对比之前仍然依赖循环神经网络实现输入表示的自注意力模型 :cite:`Cheng.Dong.Lapata.2016,Lin.Feng.Santos.ea.2017,Paulus.Xiong.Socher.2017`，Transformer模型完全基于注意力机制，没有任何卷积层或循环神经网络层 :cite:`Vaswani.Shazeer.Parmar.ea.2017`。尽管Transformer最初是应用于在文本数据上的序列到序列学习，但现在已经推广到各种现代的深度学习中，例如语言、视觉、语音和强化学习领域。\n", "\n", "## 模型\n", "\n", "Transformer作为编码器－解码器架构的一个实例，其整体架构图在 :numref:`fig_transformer`中展示。正如所见到的，Transformer是由编码器和解码器组成的。与 :numref:`fig_s2s_attention_details`中基于Bahdanau注意力实现的序列到序列的学习相比，Transformer的编码器和解码器是基于自注意力的模块叠加而成的，源（输入）序列和目标（输出）序列的*嵌入*（embedding）表示将加上*位置编码*（positional encoding），再分别输入到编码器和解码器中。\n", "\n", "![transformer架构](../img/transformer.svg)\n", ":width:`500px`\n", ":label:`fig_transformer`\n", "\n", "图 :numref:`fig_transformer`中概述了Transformer的架构。从宏观角度来看，Transformer的编码器是由多个相同的层叠加而成的，每个层都有两个子层（子层表示为$\\mathrm{sublayer}$）。第一个子层是*多头自注意力*（multi-head self-attention）汇聚；第二个子层是*基于位置的前馈网络*（positionwise feed-forward network）。具体来说，在计算编码器的自注意力时，查询、键和值都来自前一个编码器层的输出。受 :numref:`sec_resnet`中残差网络的启发，每个子层都采用了*残差连接*（residual connection）。在Transformer中，对于序列中任何位置的任何输入$\\mathbf{x} \\in \\mathbb{R}^d$，都要求满足$\\mathrm{sublayer}(\\mathbf{x}) \\in \\mathbb{R}^d$，以便残差连接满足$\\mathbf{x} + \\mathrm{sublayer}(\\mathbf{x}) \\in \\mathbb{R}^d$。在残差连接的加法计算之后，紧接着应用*层规范化*（layer normalization） :cite:`Ba.Kiros.Hinton.2016`。因此，输入序列对应的每个位置，Transformer编码器都将输出一个$d$维表示向量。\n", "\n", "Transformer解码器也是由多个相同的层叠加而成的，并且层中使用了残差连接和层规范化。除了编码器中描述的两个子层之外，解码器还在这两个子层之间插入了第三个子层，称为*编码器－解码器注意力*（encoder-decoder attention）层。在编码器－解码器注意力中，查询来自前一个解码器层的输出，而键和值来自整个编码器的输出。在解码器自注意力中，查询、键和值都来自上一个解码器层的输出。但是，解码器中的每个位置只能考虑该位置之前的所有位置。这种*掩蔽*（masked）注意力保留了*自回归*（auto-regressive）属性，确保预测仅依赖于已生成的输出词元。\n", "\n", "在此之前已经描述并实现了基于缩放点积多头注意力 :numref:`sec_multihead-attention`和位置编码 :numref:`subsec_positional-encoding`。接下来将实现Transformer模型的剩余部分。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "9309c9bd", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:02.524217Z", "iopub.status.busy": "2023-08-18T07:21:02.523886Z", "iopub.status.idle": "2023-08-18T07:21:05.481998Z", "shell.execute_reply": "2023-08-18T07:21:05.481087Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import math\n", "import pandas as pd\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "660c2057", "metadata": {"origin_pos": 5}, "source": ["## [**基于位置的前馈网络**]\n", "\n", "基于位置的前馈网络对序列中的所有位置的表示进行变换时使用的是同一个多层感知机（MLP），这就是称前馈网络是*基于位置的*（positionwise）的原因。在下面的实现中，输入`X`的形状（批量大小，时间步数或序列长度，隐单元数或特征维度）将被一个两层的感知机转换成形状为（批量大小，时间步数，`ffn_num_outputs`）的输出张量。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "5a030ddc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.487583Z", "iopub.status.busy": "2023-08-18T07:21:05.486906Z", "iopub.status.idle": "2023-08-18T07:21:05.492826Z", "shell.execute_reply": "2023-08-18T07:21:05.492063Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class PositionWiseFFN(nn.Module):\n", "    \"\"\"基于位置的前馈网络\"\"\"\n", "    def __init__(self, ffn_num_input, ffn_num_hiddens, ffn_num_outputs,\n", "                 **kwargs):\n", "        super(Position<PERSON>iseFF<PERSON>, self).__init__(**kwargs)\n", "        self.dense1 = nn.Linear(ffn_num_input, ffn_num_hiddens)\n", "        self.relu = nn.ReLU()\n", "        self.dense2 = nn.Linear(ffn_num_hiddens, ffn_num_outputs)\n", "\n", "    def forward(self, X):\n", "        return self.dense2(self.relu(self.dense1(X)))"]}, {"cell_type": "markdown", "id": "d5d02e37", "metadata": {"origin_pos": 10}, "source": ["下面的例子显示，[**改变张量的最里层维度的尺寸**]，会改变成基于位置的前馈网络的输出尺寸。因为用同一个多层感知机对所有位置上的输入进行变换，所以当所有这些位置的输入相同时，它们的输出也是相同的。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "be51755b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.498028Z", "iopub.status.busy": "2023-08-18T07:21:05.497721Z", "iopub.status.idle": "2023-08-18T07:21:05.527774Z", "shell.execute_reply": "2023-08-18T07:21:05.526996Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[-0.8290,  1.0067,  0.3619,  0.3594, -0.5328,  0.2712,  0.7394,  0.0747],\n", "        [-0.8290,  1.0067,  0.3619,  0.3594, -0.5328,  0.2712,  0.7394,  0.0747],\n", "        [-0.8290,  1.0067,  0.3619,  0.3594, -0.5328,  0.2712,  0.7394,  0.0747]],\n", "       grad_fn=<SelectBackward0>)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["ffn = PositionWiseFFN(4, 4, 8)\n", "ffn.eval()\n", "ffn(torch.ones((2, 3, 4)))[0]"]}, {"cell_type": "markdown", "id": "3b4b7f94", "metadata": {"origin_pos": 15}, "source": ["## 残差连接和层规范化\n", "\n", "现在让我们关注 :numref:`fig_transformer`中的*加法和规范化*（add&norm）组件。正如在本节开头所述，这是由残差连接和紧随其后的层规范化组成的。两者都是构建有效的深度架构的关键。\n", "\n", " :numref:`sec_batch_norm`中解释了在一个小批量的样本内基于批量规范化对数据进行重新中心化和重新缩放的调整。层规范化和批量规范化的目标相同，但层规范化是基于特征维度进行规范化。尽管批量规范化在计算机视觉中被广泛应用，但在自然语言处理任务中（输入通常是变长序列）批量规范化通常不如层规范化的效果好。\n", "\n", "以下代码[**对比不同维度的层规范化和批量规范化的效果**]。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "1da6d2d6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.531626Z", "iopub.status.busy": "2023-08-18T07:21:05.531338Z", "iopub.status.idle": "2023-08-18T07:21:05.541716Z", "shell.execute_reply": "2023-08-18T07:21:05.540694Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["layer norm: tensor([[-1.0000,  1.0000],\n", "        [-1.0000,  1.0000]], grad_fn=<NativeLayerNormBackward0>) \n", "batch norm: tensor([[-1.0000, -1.0000],\n", "        [ 1.0000,  1.0000]], grad_fn=<NativeBatchNormBackward0>)\n"]}], "source": ["ln = nn.<PERSON>erNorm(2)\n", "bn = nn.BatchNorm1d(2)\n", "X = torch.tensor([[1, 2], [2, 3]], dtype=torch.float32)\n", "# 在训练模式下计算X的均值和方差\n", "print('layer norm:', ln(X), '\\nbatch norm:', bn(X))"]}, {"cell_type": "markdown", "id": "a7438b9a", "metadata": {"origin_pos": 20}, "source": ["现在可以[**使用残差连接和层规范化**]来实现`AddNorm`类。暂退法也被作为正则化方法使用。\n"]}, {"cell_type": "code", "execution_count": 5, "id": "41dc88c3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.547230Z", "iopub.status.busy": "2023-08-18T07:21:05.546508Z", "iopub.status.idle": "2023-08-18T07:21:05.553654Z", "shell.execute_reply": "2023-08-18T07:21:05.552629Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class AddNorm(nn.Module):\n", "    \"\"\"残差连接后进行层规范化\"\"\"\n", "    def __init__(self, normalized_shape, dropout, **kwargs):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.dropout = nn.Dropout(dropout)\n", "        self.ln = nn.LayerNorm(normalized_shape)\n", "\n", "    def forward(self, X, Y):\n", "        return self.ln(self.dropout(Y) + X)"]}, {"cell_type": "markdown", "id": "17336160", "metadata": {"origin_pos": 25}, "source": ["残差连接要求两个输入的形状相同，以便[**加法操作后输出张量的形状相同**]。\n"]}, {"cell_type": "code", "execution_count": 6, "id": "9ddbf972", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.559356Z", "iopub.status.busy": "2023-08-18T07:21:05.558397Z", "iopub.status.idle": "2023-08-18T07:21:05.568412Z", "shell.execute_reply": "2023-08-18T07:21:05.567252Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 3, 4])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["add_norm = AddNorm([3, 4], 0.5)\n", "add_norm.eval()\n", "add_norm(torch.ones((2, 3, 4)), torch.ones((2, 3, 4))).shape"]}, {"cell_type": "markdown", "id": "af623091", "metadata": {"origin_pos": 29}, "source": ["## 编码器\n", "\n", "有了组成Transformer编码器的基础组件，现在可以先[**实现编码器中的一个层**]。下面的`EncoderBlock`类包含两个子层：多头自注意力和基于位置的前馈网络，这两个子层都使用了残差连接和紧随的层规范化。\n"]}, {"cell_type": "code", "execution_count": 7, "id": "3a4bae18", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.572991Z", "iopub.status.busy": "2023-08-18T07:21:05.572138Z", "iopub.status.idle": "2023-08-18T07:21:05.581937Z", "shell.execute_reply": "2023-08-18T07:21:05.580861Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class EncoderBlock(nn.Module):\n", "    \"\"\"Transformer编码器块\"\"\"\n", "    def __init__(self, key_size, query_size, value_size, num_hiddens,\n", "                 norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,\n", "                 dropout, use_bias=False, **kwargs):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.attention = d2l.MultiHeadAttention(\n", "            key_size, query_size, value_size, num_hiddens, num_heads, dropout,\n", "            use_bias)\n", "        self.addnorm1 = AddNorm(norm_shape, dropout)\n", "        self.ffn = PositionWiseFFN(\n", "            ffn_num_input, ffn_num_hiddens, num_hiddens)\n", "        self.addnorm2 = AddNorm(norm_shape, dropout)\n", "\n", "    def forward(self, X, valid_lens):\n", "        Y = self.addnorm1(X, self.attention(X, X, X, valid_lens))\n", "        return self.addnorm2(Y, self.ffn(Y))"]}, {"cell_type": "markdown", "id": "683dbd77", "metadata": {"origin_pos": 34}, "source": ["正如从代码中所看到的，[**Transformer编码器中的任何层都不会改变其输入的形状**]。\n"]}, {"cell_type": "code", "execution_count": 8, "id": "f55eefee", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.586090Z", "iopub.status.busy": "2023-08-18T07:21:05.585452Z", "iopub.status.idle": "2023-08-18T07:21:05.601167Z", "shell.execute_reply": "2023-08-18T07:21:05.599824Z"}, "origin_pos": 36, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 100, 24])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.ones((2, 100, 24))\n", "valid_lens = torch.tensor([3, 2])\n", "encoder_blk = EncoderBlock(24, 24, 24, 24, [100, 24], 24, 48, 8, 0.5)\n", "encoder_blk.eval()\n", "encoder_blk(X, valid_lens).shape"]}, {"cell_type": "markdown", "id": "cde53f8e", "metadata": {"origin_pos": 38}, "source": ["下面实现的[**Transformer编码器**]的代码中，堆叠了`num_layers`个`EncoderBlock`类的实例。由于这里使用的是值范围在$-1$和$1$之间的固定位置编码，因此通过学习得到的输入的嵌入表示的值需要先乘以嵌入维度的平方根进行重新缩放，然后再与位置编码相加。\n"]}, {"cell_type": "code", "execution_count": 9, "id": "94c5fb5d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.605447Z", "iopub.status.busy": "2023-08-18T07:21:05.604941Z", "iopub.status.idle": "2023-08-18T07:21:05.618289Z", "shell.execute_reply": "2023-08-18T07:21:05.616871Z"}, "origin_pos": 40, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class TransformerEncoder(d2l.Encoder):\n", "    \"\"\"Transformer编码器\"\"\"\n", "    def __init__(self, vocab_size, key_size, query_size, value_size,\n", "                 num_hiddens, norm_shape, ffn_num_input, ffn_num_hiddens,\n", "                 num_heads, num_layers, dropout, use_bias=False, **kwargs):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.num_hiddens = num_hiddens\n", "        self.embedding = nn.Embedding(vocab_size, num_hiddens)\n", "        self.pos_encoding = d2l.PositionalEncoding(num_hiddens, dropout)\n", "        self.blks = nn.Sequential()\n", "        for i in range(num_layers):\n", "            self.blks.add_module(\"block\"+str(i),\n", "                EncoderBlock(key_size, query_size, value_size, num_hiddens,\n", "                             norm_shape, ffn_num_input, ffn_num_hiddens,\n", "                             num_heads, dropout, use_bias))\n", "\n", "    def forward(self, X, valid_lens, *args):\n", "        # 因为位置编码值在-1和1之间，\n", "        # 因此嵌入值乘以嵌入维度的平方根进行缩放，\n", "        # 然后再与位置编码相加。\n", "        X = self.pos_encoding(self.embedding(X) * math.sqrt(self.num_hiddens))\n", "        self.attention_weights = [None] * len(self.blks)\n", "        for i, blk in enumerate(self.blks):\n", "            X = blk(X, valid_lens)\n", "            self.attention_weights[\n", "                i] = blk.attention.attention.attention_weights\n", "        return X"]}, {"cell_type": "markdown", "id": "a0025bec", "metadata": {"origin_pos": 43}, "source": ["下面我们指定了超参数来[**创建一个两层的Transformer编码器**]。\n", "Transformer编码器输出的形状是（批量大小，时间步数目，`num_hiddens`）。\n"]}, {"cell_type": "code", "execution_count": 10, "id": "b16ed63f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.624985Z", "iopub.status.busy": "2023-08-18T07:21:05.623935Z", "iopub.status.idle": "2023-08-18T07:21:05.643729Z", "shell.execute_reply": "2023-08-18T07:21:05.642680Z"}, "origin_pos": 45, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 100, 24])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["encoder = TransformerEncoder(\n", "    200, 24, 24, 24, 24, [100, 24], 24, 48, 8, 2, 0.5)\n", "encoder.eval()\n", "encoder(torch.ones((2, 100), dtype=torch.long), valid_lens).shape"]}, {"cell_type": "markdown", "id": "db537625", "metadata": {"origin_pos": 48}, "source": ["## 解码器\n", "\n", "如 :numref:`fig_transformer`所示，[**Transformer解码器也是由多个相同的层组成**]。在`DecoderBlock`类中实现的每个层包含了三个子层：解码器自注意力、“编码器-解码器”注意力和基于位置的前馈网络。这些子层也都被残差连接和紧随的层规范化围绕。\n", "\n", "正如在本节前面所述，在掩蔽多头解码器自注意力层（第一个子层）中，查询、键和值都来自上一个解码器层的输出。关于*序列到序列模型*（sequence-to-sequence model），在训练阶段，其输出序列的所有位置（时间步）的词元都是已知的；然而，在预测阶段，其输出序列的词元是逐个生成的。因此，在任何解码器时间步中，只有生成的词元才能用于解码器的自注意力计算中。为了在解码器中保留自回归的属性，其掩蔽自注意力设定了参数`dec_valid_lens`，以便任何查询都只会与解码器中所有已经生成词元的位置（即直到该查询位置为止）进行注意力计算。\n"]}, {"cell_type": "code", "execution_count": 11, "id": "b97dbb3a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.648104Z", "iopub.status.busy": "2023-08-18T07:21:05.647725Z", "iopub.status.idle": "2023-08-18T07:21:05.662950Z", "shell.execute_reply": "2023-08-18T07:21:05.661933Z"}, "origin_pos": 50, "tab": ["pytorch"]}, "outputs": [], "source": ["class DecoderBlock(nn.Module):\n", "    \"\"\"解码器中第i个块\"\"\"\n", "    def __init__(self, key_size, query_size, value_size, num_hiddens,\n", "                 norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,\n", "                 dropout, i, **kwargs):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.i = i\n", "        self.attention1 = d2l.MultiHeadAttention(\n", "            key_size, query_size, value_size, num_hiddens, num_heads, dropout)\n", "        self.addnorm1 = AddNorm(norm_shape, dropout)\n", "        self.attention2 = d2l.MultiHeadAttention(\n", "            key_size, query_size, value_size, num_hiddens, num_heads, dropout)\n", "        self.addnorm2 = AddNorm(norm_shape, dropout)\n", "        self.ffn = PositionWiseFFN(ffn_num_input, ffn_num_hiddens,\n", "                                   num_hiddens)\n", "        self.addnorm3 = AddNorm(norm_shape, dropout)\n", "\n", "    def forward(self, X, state):\n", "        enc_outputs, enc_valid_lens = state[0], state[1]\n", "        # 训练阶段，输出序列的所有词元都在同一时间处理，\n", "        # 因此state[2][self.i]初始化为None。\n", "        # 预测阶段，输出序列是通过词元一个接着一个解码的，\n", "        # 因此state[2][self.i]包含着直到当前时间步第i个块解码的输出表示\n", "        if state[2][self.i] is None:\n", "            key_values = X\n", "        else:\n", "            key_values = torch.cat((state[2][self.i], X), axis=1)\n", "        state[2][self.i] = key_values\n", "        if self.training:\n", "            batch_size, num_steps, _ = X.shape\n", "            # dec_valid_lens的开头:(batch_size,num_steps),\n", "            # 其中每一行是[1,2,...,num_steps]\n", "            dec_valid_lens = torch.arange(\n", "                1, num_steps + 1, device=X.device).repeat(batch_size, 1)\n", "        else:\n", "            dec_valid_lens = None\n", "\n", "        # 自注意力\n", "        X2 = self.attention1(X, key_values, key_values, dec_valid_lens)\n", "        Y = self.addnorm1(X, X2)\n", "        # 编码器－解码器注意力。\n", "        # enc_outputs的开头:(batch_size,num_steps,num_hiddens)\n", "        Y2 = self.attention2(Y, enc_outputs, enc_outputs, enc_valid_lens)\n", "        Z = self.addnorm2(Y, Y2)\n", "        return self.addnorm3(Z, self.ffn(Z)), state"]}, {"cell_type": "markdown", "id": "860e7d0c", "metadata": {"origin_pos": 53}, "source": ["为了便于在“编码器－解码器”注意力中进行缩放点积计算和残差连接中进行加法计算，[**编码器和解码器的特征维度都是`num_hiddens`。**]\n"]}, {"cell_type": "code", "execution_count": 12, "id": "92119d80", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.666987Z", "iopub.status.busy": "2023-08-18T07:21:05.666595Z", "iopub.status.idle": "2023-08-18T07:21:05.685799Z", "shell.execute_reply": "2023-08-18T07:21:05.684460Z"}, "origin_pos": 55, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 100, 24])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["decoder_blk = DecoderBlock(24, 24, 24, 24, [100, 24], 24, 48, 8, 0.5, 0)\n", "decoder_blk.eval()\n", "X = torch.ones((2, 100, 24))\n", "state = [encoder_blk(X, valid_lens), valid_lens, [None]]\n", "decoder_blk(X, state)[0].shape"]}, {"cell_type": "markdown", "id": "7b3b57e3", "metadata": {"origin_pos": 57}, "source": ["现在我们构建了由`num_layers`个`DecoderBlock`实例组成的完整的[**Transformer解码器**]。最后，通过一个全连接层计算所有`vocab_size`个可能的输出词元的预测值。解码器的自注意力权重和编码器解码器注意力权重都被存储下来，方便日后可视化的需要。\n"]}, {"cell_type": "code", "execution_count": 13, "id": "1fa8eade", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.691062Z", "iopub.status.busy": "2023-08-18T07:21:05.690657Z", "iopub.status.idle": "2023-08-18T07:21:05.706405Z", "shell.execute_reply": "2023-08-18T07:21:05.704976Z"}, "origin_pos": 59, "tab": ["pytorch"]}, "outputs": [], "source": ["class TransformerDecoder(d2l.AttentionDecoder):\n", "    def __init__(self, vocab_size, key_size, query_size, value_size,\n", "                 num_hiddens, norm_shape, ffn_num_input, ffn_num_hiddens,\n", "                 num_heads, num_layers, dropout, **kwargs):\n", "        super(TransformerDecoder, self).__init__(**kwargs)\n", "        self.num_hiddens = num_hiddens\n", "        self.num_layers = num_layers\n", "        self.embedding = nn.Embedding(vocab_size, num_hiddens)\n", "        self.pos_encoding = d2l.PositionalEncoding(num_hiddens, dropout)\n", "        self.blks = nn.Sequential()\n", "        for i in range(num_layers):\n", "            self.blks.add_module(\"block\"+str(i),\n", "                DecoderBlock(key_size, query_size, value_size, num_hiddens,\n", "                             norm_shape, ffn_num_input, ffn_num_hiddens,\n", "                             num_heads, dropout, i))\n", "        self.dense = nn.Linear(num_hiddens, vocab_size)\n", "\n", "    def init_state(self, enc_outputs, enc_valid_lens, *args):\n", "        return [enc_outputs, enc_valid_lens, [None] * self.num_layers]\n", "\n", "    def forward(self, X, state):\n", "        X = self.pos_encoding(self.embedding(X) * math.sqrt(self.num_hiddens))\n", "        self._attention_weights = [[None] * len(self.blks) for _ in range (2)]\n", "        for i, blk in enumerate(self.blks):\n", "            X, state = blk(X, state)\n", "            # 解码器自注意力权重\n", "            self._attention_weights[0][\n", "                i] = blk.attention1.attention.attention_weights\n", "            # “编码器－解码器”自注意力权重\n", "            self._attention_weights[1][\n", "                i] = blk.attention2.attention.attention_weights\n", "        return self.dense(X), state\n", "\n", "    @property\n", "    def attention_weights(self):\n", "        return self._attention_weights"]}, {"cell_type": "markdown", "id": "3adc5f9d", "metadata": {"origin_pos": 62}, "source": ["## [**训练**]\n", "\n", "依照Transformer架构来实例化编码器－解码器模型。在这里，指定Transformer的编码器和解码器都是2层，都使用4头注意力。与 :numref:`sec_seq2seq_training`类似，为了进行序列到序列的学习，下面在“英语－法语”机器翻译数据集上训练Transformer模型。\n"]}, {"cell_type": "code", "execution_count": 14, "id": "1adc9e94", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:21:05.711753Z", "iopub.status.busy": "2023-08-18T07:21:05.710942Z", "iopub.status.idle": "2023-08-18T07:22:31.086151Z", "shell.execute_reply": "2023-08-18T07:22:31.084931Z"}, "origin_pos": 64, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.030, 5202.9 tokens/sec on cuda:0\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"262.1875pt\" height=\"180.65625pt\" viewBox=\"0 0 262.1875 180.65625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:22:31.056238</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 180.65625 \n", "L 262.1875 180.65625 \n", "L 262.1875 0 \n", "L 0 0 \n", "L 0 180.65625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 50.14375 143.1 \n", "L 245.44375 143.1 \n", "L 245.44375 7.2 \n", "L 50.14375 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 91.259539 143.1 \n", "L 91.259539 7.2 \n", "\" clip-path=\"url(#p76c69024c0)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"me9169adf4a\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me9169adf4a\" x=\"91.259539\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(84.897039 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 142.654276 143.1 \n", "L 142.654276 7.2 \n", "\" clip-path=\"url(#p76c69024c0)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#me9169adf4a\" x=\"142.654276\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(133.110526 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 194.049013 143.1 \n", "L 194.049013 7.2 \n", "\" clip-path=\"url(#p76c69024c0)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#me9169adf4a\" x=\"194.049013\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 150 -->\n", "      <g transform=\"translate(184.505263 157.698438)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 245.44375 143.1 \n", "L 245.44375 7.2 \n", "\" clip-path=\"url(#p76c69024c0)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#me9169adf4a\" x=\"245.44375\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(235.9 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(132.565625 171.376563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 50.14375 120.306751 \n", "L 245.44375 120.306751 \n", "\" clip-path=\"url(#p76c69024c0)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"mc22b544906\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc22b544906\" x=\"50.14375\" y=\"120.306751\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.05 -->\n", "      <g transform=\"translate(20.878125 124.10597)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 50.14375 79.516006 \n", "L 245.44375 79.516006 \n", "\" clip-path=\"url(#p76c69024c0)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mc22b544906\" x=\"50.14375\" y=\"79.516006\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.10 -->\n", "      <g transform=\"translate(20.878125 83.315225)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 50.14375 38.725261 \n", "L 245.44375 38.725261 \n", "\" clip-path=\"url(#p76c69024c0)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mc22b544906\" x=\"50.14375\" y=\"38.725261\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.15 -->\n", "      <g transform=\"translate(20.878125 42.524479)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798437 84.807812)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 50.14375 13.377273 \n", "L 60.422697 57.928861 \n", "L 70.701645 82.051079 \n", "L 80.980592 99.34588 \n", "L 91.259539 109.391103 \n", "L 101.538487 117.440373 \n", "L 111.817434 118.241515 \n", "L 122.096382 123.670124 \n", "L 132.375329 127.908463 \n", "L 142.654276 127.361297 \n", "L 152.933224 128.166965 \n", "L 163.212171 131.263802 \n", "L 173.491118 132.011479 \n", "L 183.770066 132.902065 \n", "L 194.049013 131.329748 \n", "L 204.327961 133.293251 \n", "L 214.606908 135.235647 \n", "L 224.885855 136.045012 \n", "L 235.164803 134.447561 \n", "L 245.44375 136.922727 \n", "\" clip-path=\"url(#p76c69024c0)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 50.14375 143.1 \n", "L 50.14375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 245.44375 143.1 \n", "L 245.44375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 50.14375 143.1 \n", "L 245.44375 143.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 50.14375 7.2 \n", "L 245.44375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p76c69024c0\">\n", "   <rect x=\"50.14375\" y=\"7.2\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["num_hiddens, num_layers, dropout, batch_size, num_steps = 32, 2, 0.1, 64, 10\n", "lr, num_epochs, device = 0.005, 200, d2l.try_gpu()\n", "ffn_num_input, ffn_num_hiddens, num_heads = 32, 64, 4\n", "key_size, query_size, value_size = 32, 32, 32\n", "norm_shape = [32]\n", "\n", "train_iter, src_vocab, tgt_vocab = d2l.load_data_nmt(batch_size, num_steps)\n", "\n", "encoder = TransformerEncoder(\n", "    len(src_vocab), key_size, query_size, value_size, num_hiddens,\n", "    norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,\n", "    num_layers, dropout)\n", "decoder = TransformerDecoder(\n", "    len(tgt_vocab), key_size, query_size, value_size, num_hiddens,\n", "    norm_shape, ffn_num_input, ffn_num_hiddens, num_heads,\n", "    num_layers, dropout)\n", "net = d2l.EncoderDecoder(encoder, decoder)\n", "d2l.train_seq2seq(net, train_iter, lr, num_epochs, tgt_vocab, device)"]}, {"cell_type": "markdown", "id": "feb9d093", "metadata": {"origin_pos": 67}, "source": ["训练结束后，使用Transformer模型[**将一些英语句子翻译成法语**]，并且计算它们的BLEU分数。\n"]}, {"cell_type": "code", "execution_count": 15, "id": "a21c8779", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:22:31.089687Z", "iopub.status.busy": "2023-08-18T07:22:31.089409Z", "iopub.status.idle": "2023-08-18T07:22:31.190689Z", "shell.execute_reply": "2023-08-18T07:22:31.189851Z"}, "origin_pos": 68, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["go . => va !,  bleu 1.000\n", "i lost . => j'ai perdu .,  bleu 1.000\n", "he's calm . => il est calme .,  bleu 1.000\n", "i'm home . => je suis chez moi .,  bleu 1.000\n"]}], "source": ["engs = ['go .', \"i lost .\", 'he\\'s calm .', 'i\\'m home .']\n", "fras = ['va !', 'j\\'ai perdu .', 'il est calme .', 'je suis chez moi .']\n", "for eng, fra in zip(engs, fras):\n", "    translation, dec_attention_weight_seq = d2l.predict_seq2seq(\n", "        net, eng, src_vocab, tgt_vocab, num_steps, device, True)\n", "    print(f'{eng} => {translation}, ',\n", "          f'bleu {d2l.bleu(translation, fra, k=2):.3f}')"]}, {"cell_type": "markdown", "id": "a9d3170e", "metadata": {"origin_pos": 70}, "source": ["当进行最后一个英语到法语的句子翻译工作时，让我们[**可视化Transformer的注意力权重**]。编码器自注意力权重的形状为（编码器层数，注意力头数，`num_steps`或查询的数目，`num_steps`或“键－值”对的数目）。\n"]}, {"cell_type": "code", "execution_count": 16, "id": "937e4ab4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:22:31.194154Z", "iopub.status.busy": "2023-08-18T07:22:31.193869Z", "iopub.status.idle": "2023-08-18T07:22:31.200327Z", "shell.execute_reply": "2023-08-18T07:22:31.199543Z"}, "origin_pos": 71, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 4, 10, 10])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["enc_attention_weights = torch.cat(net.encoder.attention_weights, 0).reshape((num_layers, num_heads,\n", "    -1, num_steps))\n", "enc_attention_weights.shape"]}, {"cell_type": "markdown", "id": "ca2a5bf2", "metadata": {"origin_pos": 72}, "source": ["在编码器的自注意力中，查询和键都来自相同的输入序列。因为填充词元是不携带信息的，因此通过指定输入序列的有效长度可以避免查询与使用填充词元的位置计算注意力。接下来，将逐行呈现两层多头注意力的权重。每个注意力头都根据查询、键和值的不同的表示子空间来表示不同的注意力。\n"]}, {"cell_type": "code", "execution_count": 17, "id": "0b408905", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:22:31.204505Z", "iopub.status.busy": "2023-08-18T07:22:31.203733Z", "iopub.status.idle": "2023-08-18T07:22:31.970964Z", "shell.execute_reply": "2023-08-18T07:22:31.969901Z"}, "origin_pos": 74, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"402.06155pt\" height=\"231.582992pt\" viewBox=\"0 0 402.06155 231.582992\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:22:31.736938</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 231.582992 \n", "L 402.06155 231.582992 \n", "L 402.06155 0 \n", "L 0 0 \n", "L 0 231.582992 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "L 34.240625 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pf1c08b28e8)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAAAxElEQVR4nO3bsQnDMBBAUTukywoZMCN5wAyQxrXTf5ALN4ngvVYIxOfgKq3H/jmWgdfjOTpatv09PJvZ7dcP+DeChCAhSAgSgoQgIUgIEoKEICFICBKChCAhSAgSgoQgIUgIEoKEICFICBKChCAhSAgSgoQgIUgIEoKEICFICBKChCAhSAgSgoQgIUgIEoKEICFICBKChCAhSAgS69mvzCtm/8lpQkKQECQECUFCkLhfvTharzOs1jMmJAQJQUKQECQEiS93fxFPkE3HVwAAAABJRU5ErkJggg==\" id=\"image3a4b9323d0\" transform=\"scale(1 -1)translate(0 -68)\" x=\"34.240625\" y=\"-22.24856\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"ma538cfee52\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"37.637147\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"71.602364\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"m0bc5c11ce8\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"34.240625\" y=\"25.714647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 29.513865)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"34.240625\" y=\"59.679864\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 63.479083)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 95.477874)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-51\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 3406 84 \n", "L 4238 -825 \n", "L 3475 -825 \n", "L 2784 -78 \n", "Q 2681 -84 2626 -87 \n", "Q 2572 -91 2522 -91 \n", "Q 1538 -91 948 567 \n", "Q 359 1225 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1516 4351 937 \n", "Q 4025 359 3406 84 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 34.240625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.240625 22.318125 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-48\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2753 \n", "L 3553 2753 \n", "L 3553 4666 \n", "L 4184 4666 \n", "L 4184 0 \n", "L 3553 0 \n", "L 3553 2222 \n", "L 1259 2222 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "L 115.757147 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p5535158d7b)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAAAxElEQVR4nO3asQ3CMBBAUULLCgzISAzIAGlSh/5JcREFhUj/tS5sfbk63bQu83o70Ovx3Dx7L58jr/qJ+9kP+DcFQUFQEBQEBUFBUBAUBAVBQVAQFAQFQUFQEBQEBUFBUBAUBAVBQVAQFAQFQUFQEBQEBUFBUBAUBAVBQVAQFAQFQUFQEEyjxd2rL+Hu0Q9BQVAQFAQFQUFQEBQEBUFBUBAUBAVBQVAQFAQFQUFQEAxnqiNb89arz1r7ISgICoKCoCAoCL7NSBUvr+8WrgAAAABJRU5ErkJggg==\" id=\"image9036903436\" transform=\"scale(1 -1)translate(0 -68)\" x=\"115.757147\" y=\"-22.24856\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"119.153668\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"153.118886\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"115.757147\" y=\"25.714647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"115.757147\" y=\"59.679864\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 115.757147 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 115.757147 22.318125 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "L 197.273668 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p466cce5ec3)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAAAuUlEQVR4nO3csQ2DQBAAQXCKS6A/uyQXSAEkxJCP9DEW2klfJ71WF9/8md7nNPA7ttHTY73u/sC/KQgKgoKgICgICoKCoCAoCAqCgqAgKAgKgoKgICgICoKCoCAoCAqCgqAgKAgKgoKgICgICoKCoCAoCAqCgqAgKAgKgoKgICgICoKCoCAoCAqCgqAgKAgKgoKgICgICoKCoCAoCAqCgmA+j314DOG7rMPBpx5KaENQEBQEBUFBUBBc5B0LTZze9r0AAAAASUVORK5CYII=\" id=\"image4e1f3b8e98\" transform=\"scale(1 -1)translate(0 -68)\" x=\"197.273668\" y=\"-22.24856\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"200.67019\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"234.635408\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"197.273668\" y=\"25.714647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"197.273668\" y=\"59.679864\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 197.273668 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 197.273668 22.318125 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "L 278.79019 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p699981006e)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAAAvUlEQVR4nO3asQ3CQBAAQZsUl+D+cEkukAJIHJt8BcknGGkm/OD0Wl1483m8zmnAdl8/vu/Hc2TcZdx+/YGrESQECUFCkBAkBAlBQpAQJAQJQUKQECQECUFCkBAkBAlBQpAQJAQJQUKQECQECUFCkBAkBAlBQpAQJAQJQUKQECQECUFifkzL18Pdfz/CHWFDQpAQJAQJQUKQECQECUFCkBAkBAlBQpAQJAQJQUKQECQECUFCkBAkBAlBQpB4A6FTC01gg5piAAAAAElFTkSuQmCC\" id=\"imagece0f8d38f5\" transform=\"scale(1 -1)translate(0 -68)\" x=\"278.79019\" y=\"-22.24856\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"282.186712\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"316.151929\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"278.79019\" y=\"25.714647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"278.79019\" y=\"59.679864\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 278.79019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 278.79019 22.318125 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 34.240625 194.026742 \n", "L 102.17106 194.026742 \n", "L 102.17106 126.096307 \n", "L 34.240625 126.096307 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pb5e72313f2)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAABTElEQVR4nO3aMUoDQRhA4ZnJCBKQgNhY21hJWhs9hLfwDHYewsN4AavcwELSiUWQQBCzu/aP/LKCugl5X5mfIcNjYGB3c/M861Lg9uwqGqWH5TycRXLOP17z38rQG9g2BgGDgEHAIGAQqKlZh8NR2v5r8rd5QsAgYBAwCBgEDAI1tW04LPt363pCyCBgEDAIGAQMAgYBg4BBwCBgEDAI1NSFbzJT8ZmqDAIGAYOAQcAgUFMbv8qs+3frekLIIGAQMAgYBAwCtXt6DIc3J0fxyujLo+8+zh3VntsajicEDAIGAYOAQcAgUNP5NBzOlqtwdlmClnm3G+/27v+AQcAgYBAwCNRycR0O5x9NvHLxuvn3w3G8Zjzpua3heELAIGAQMAgYBAwC+e5gEn5CdL94CRd272+bB+vP+M+OT/vvbCCeEDAIGAQMAgYBg8AXr9Ul7WN5EGMAAAAASUVORK5CYII=\" id=\"imaged85592ea5d\" transform=\"scale(1 -1)translate(0 -68)\" x=\"34.240625\" y=\"-126.026742\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_9\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"37.637147\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(34.455897 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"71.602364\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(68.421114 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(35.142561 222.303304)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4b\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2694 \n", "L 3353 4666 \n", "L 4166 4666 \n", "L 1850 2491 \n", "L 4331 0 \n", "L 3500 0 \n", "L 1259 2247 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"34.240625\" y=\"129.492829\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 133.292047)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"34.240625\" y=\"163.458046\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 167.257265)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 199.256055)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 34.240625 194.026742 \n", "L 34.240625 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 102.17106 194.026742 \n", "L 102.17106 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 34.240625 194.026742 \n", "L 102.17106 194.026742 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 34.240625 126.096307 \n", "L 102.17106 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 120.096307)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 115.757147 194.026742 \n", "L 183.687582 194.026742 \n", "L 183.687582 126.096307 \n", "L 115.757147 126.096307 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p19dd49f6fe)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAABSklEQVR4nO3bP0oDQRSA8RkZYkRDCglY5QraeAdvIp5IPImnsLdKEe1CRBEVRXftP/Ytq81s4PuVGR4ZPh4s5E9uXp/aFLiaLaOjdP2y6j5omnAmT6bh2Vjs1b7A2BgEDAIGAYOAQaCknGvfYVTcEDAIGAQMAgYBg4BBwCBgEDAIGAQMAgYBg4BBwCBgEDAIGARKasNvMlNOPZ+39sztMjcEDAIGAYOAQcAgUPoO/1drtx/HbggYBAwCBgGDgEGgpK+P8PB8th9P/nx3v/75Hs9MDgZeqx43BAwCBgGDgEHAIFDazUN4eDY/jCeDH/y2b8/xyHwx9F7VuCFgEDAIGAQMAvn2+CT8EPRifR8Otpv1399sEf/LcyzcEDAIGAQMAgYBg0Buto/hY/dmeRoOXq7uug9K/O1onh4Nv1klbggYBAwCBgGDgEHgF4WHKI15TMhcAAAAAElFTkSuQmCC\" id=\"image66366edb84\" transform=\"scale(1 -1)translate(0 -68)\" x=\"115.757147\" y=\"-126.026742\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_11\">\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"119.153668\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(115.972418 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"153.118886\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(149.937636 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(116.659083 222.303304)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_12\">\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"115.757147\" y=\"129.492829\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"115.757147\" y=\"163.458046\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 115.757147 194.026742 \n", "L 115.757147 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 183.687582 194.026742 \n", "L 183.687582 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 115.757147 194.026742 \n", "L 183.687582 194.026742 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 115.757147 126.096307 \n", "L 183.687582 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 120.096307)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 197.273668 194.026742 \n", "L 265.204103 194.026742 \n", "L 265.204103 126.096307 \n", "L 197.273668 126.096307 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pb3f0443fff)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAABEElEQVR4nO3asQnCQBhA4ZxGLJxBnMARHMTexoWcwClsLNzE2kKC2pmzf+Q/0CYevK/McSE8fjg0Sfl5z01gv1hGS83hcR28nlIK99RgMvYD/BuDgEHAIGAQMAi0v26s/XiNOCFgEDAIGAQMAgYBg4BBwCBgEDAIGAQMAgYBg4BBwCBgEDAIGAQMAgYBg4BBwCBgECi+yizVynn4w6PaX3E6IWAQMAgYBAwCBoHisftuwm96qz9eI04IGAQMAgYBg4BBwCBgEDAIGAQMAgaBNr+6cHE1n4Vr0X+qJTX8IHRCwCBgEDAIGAQMAqnvbuH52Z+O4cZ8OQ/fcLsL90zXmy8ebRxOCBgEDAIGAYOAQeADW9sca9OgjFcAAAAASUVORK5CYII=\" id=\"image2c2ca2d6bf\" transform=\"scale(1 -1)translate(0 -68)\" x=\"197.273668\" y=\"-126.026742\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_13\">\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"200.67019\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(197.48894 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"234.635408\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(231.454158 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(198.175605 222.303304)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_14\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"197.273668\" y=\"129.492829\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"197.273668\" y=\"163.458046\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 197.273668 194.026742 \n", "L 197.273668 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 265.204103 194.026742 \n", "L 265.204103 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 197.273668 194.026742 \n", "L 265.204103 194.026742 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 197.273668 126.096307 \n", "L 265.204103 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_22\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 120.096307)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 278.79019 194.026742 \n", "L 346.720625 194.026742 \n", "L 346.720625 126.096307 \n", "L 278.79019 126.096307 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pbd0f4a3c83)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAABRUlEQVR4nO3aP0rEQBhA8Zl1jAFtPICFt/IKtnoMD2DnKbyHVxA7K/8tCKubtX/k+whYZI3vV2bIZvIYGCZsHdYvuxK4PDmLhsrt++P4wC78uVLbYTi2L1ZzT2DfGAQMAgYBg4BBwCBgEDAIGAQMAgYBg0DLBmup8WByqv3LXCFgEDAIGAQMAuku05JNphR3mX/BIGAQMAgYBAwCLTukrbLD3UK5QsAgYBAwCBgEDALpaXdY6Ik24woBg4BBwCBgEDAItLL5DAfP++SPttEp+XuTPK2bOK35uELAIGAQMAgYBAwCrWy/wsEu+8Zcl9lymW/1CwYBg4BBwCBQh9fn8MPpcH8X3/nxNn69iw9wBxdXkyc2F1cIGAQMAgYBg4BBoN70p+G2e/30EN95dDx+fdjGD+v6yRObiysEDAIGAYOAQcAg8APscydcBYmE2wAAAABJRU5ErkJggg==\" id=\"image2458252dc6\" transform=\"scale(1 -1)translate(0 -68)\" x=\"278.79019\" y=\"-126.026742\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_15\">\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"282.186712\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(279.005462 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#ma538cfee52\" x=\"316.151929\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(312.970679 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(279.692126 222.303304)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_16\">\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"278.79019\" y=\"129.492829\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m0bc5c11ce8\" x=\"278.79019\" y=\"163.458046\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 278.79019 194.026742 \n", "L 278.79019 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 346.720625 194.026742 \n", "L 346.720625 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 278.79019 194.026742 \n", "L 346.720625 194.026742 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 278.79019 126.096307 \n", "L 346.720625 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_26\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 120.096307)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 366.250625 165.250433 \n", "L 371.958425 165.250433 \n", "L 371.958425 51.094433 \n", "L 366.250625 51.094433 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path clip-path=\"url(#p3141df643f)\" style=\"fill: #ffffff; stroke: #ffffff; stroke-width: 0.01; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAYAAAByCAYAAABwf6CfAAAAzElEQVR4nLXWQQ7DIBBDUSpx/7N2001hhl6AF2kQzTKW/fGQkLzW573a5upt5e5+6y0pBIS1RZwwlhmKqgtkZLkHhZgSCIfgkZhxDW5hjr3g5boHBTAOetAxNat6lGd1bz/cQ7M66KGH+qAH34/6fujIuAivCwcF9Z4HC5IBIcWgow5f89pyzRg8E6tRGTjbQ0LqaxAS6gxHlRmTzeuMfdJTD/0BpAQktZ5NBc1AVCiKDjP2hqeCcnBWFhTF5Q7tIB2zPJLvvSg6xv8ZP9W5DE4Qbu8+AAAAAElFTkSuQmCC\" id=\"image09cc96388d\" transform=\"scale(1 -1)translate(0 -114)\" x=\"366\" y=\"-51\" width=\"6\" height=\"114\"/>\n", "   <g id=\"matplotlib.axis_17\">\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_33\">\n", "      <defs>\n", "       <path id=\"m499d769101\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m499d769101\" x=\"371.958425\" y=\"165.250433\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(378.**********.049652)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m499d769101\" x=\"371.958425\" y=\"141.378755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(378.958425 145.177974)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m499d769101\" x=\"371.958425\" y=\"117.507076\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(378.958425 121.306295)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m499d769101\" x=\"371.958425\" y=\"93.635398\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(378.958425 97.434617)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m499d769101\" x=\"371.958425\" y=\"69.763719\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_31\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(378.958425 73.562938)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 366.250625 165.250433 \n", "L 369.104525 165.250433 \n", "L 371.958425 165.250433 \n", "L 371.958425 51.094433 \n", "L 369.104525 51.094433 \n", "L 366.250625 51.094433 \n", "L 366.250625 165.250433 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf1c08b28e8\">\n", "   <rect x=\"34.240625\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p5535158d7b\">\n", "   <rect x=\"115.757147\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p466cce5ec3\">\n", "   <rect x=\"197.273668\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p699981006e\">\n", "   <rect x=\"278.79019\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pb5e72313f2\">\n", "   <rect x=\"34.240625\" y=\"126.096307\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p19dd49f6fe\">\n", "   <rect x=\"115.757147\" y=\"126.096307\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pb3f0443fff\">\n", "   <rect x=\"197.273668\" y=\"126.096307\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pbd0f4a3c83\">\n", "   <rect x=\"278.79019\" y=\"126.096307\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p3141df643f\">\n", "   <rect x=\"366.250625\" y=\"51.094433\" width=\"5.7078\" height=\"114.156\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 504x252 with 9 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["d2l.show_heatmaps(\n", "    enc_attention_weights.cpu(), xlabel='Key positions',\n", "    ylabel='Query positions', titles=['Head %d' % i for i in range(1, 5)],\n", "    figsize=(7, 3.5))"]}, {"cell_type": "markdown", "id": "5943a056", "metadata": {"origin_pos": 75}, "source": ["[**为了可视化解码器的自注意力权重和“编码器－解码器”的注意力权重，我们需要完成更多的数据操作工作。**]例如用零填充被掩蔽住的注意力权重。值得注意的是，解码器的自注意力权重和“编码器－解码器”的注意力权重都有相同的查询：即以*序列开始词元*（beginning-of-sequence,BOS）打头，再与后续输出的词元共同组成序列。\n"]}, {"cell_type": "code", "execution_count": 18, "id": "b4898564", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:22:31.975096Z", "iopub.status.busy": "2023-08-18T07:22:31.974177Z", "iopub.status.idle": "2023-08-18T07:22:31.987757Z", "shell.execute_reply": "2023-08-18T07:22:31.986824Z"}, "origin_pos": 77, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(torch.<PERSON><PERSON>([2, 4, 6, 10]), torch.<PERSON><PERSON>([2, 4, 6, 10]))"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["dec_attention_weights_2d = [head[0].tolist()\n", "                            for step in dec_attention_weight_seq\n", "                            for attn in step for blk in attn for head in blk]\n", "dec_attention_weights_filled = torch.tensor(\n", "    pd.DataFrame(dec_attention_weights_2d).fillna(0.0).values)\n", "dec_attention_weights = dec_attention_weights_filled.reshape((-1, 2, num_layers, num_heads, num_steps))\n", "dec_self_attention_weights, dec_inter_attention_weights = \\\n", "    dec_attention_weights.permute(1, 2, 3, 0, 4)\n", "dec_self_attention_weights.shape, dec_inter_attention_weights.shape"]}, {"cell_type": "markdown", "id": "3e4e5d7a", "metadata": {"origin_pos": 80}, "source": ["由于解码器自注意力的自回归属性，查询不会对当前位置之后的“键－值”对进行注意力计算。\n"]}, {"cell_type": "code", "execution_count": 19, "id": "8f162beb", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:22:31.991865Z", "iopub.status.busy": "2023-08-18T07:22:31.990973Z", "iopub.status.idle": "2023-08-18T07:22:32.664530Z", "shell.execute_reply": "2023-08-18T07:22:32.663454Z"}, "origin_pos": 81, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"402.06155pt\" height=\"231.582992pt\" viewBox=\"0 0 402.06155 231.582992\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:22:32.516045</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 231.582992 \n", "L 402.06155 231.582992 \n", "L 402.06155 0 \n", "L 0 0 \n", "L 0 231.582992 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "L 34.240625 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd55de13844)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAAAwklEQVR4nO3XsQ3CQBAAQT8plEB/uCQKpAASxyafyA5eArQTf3BaXXA/9u29L39uvd4Pv71MnOMnFQQFQUFQEBQEBUFBUBAUBGPm6X7mZH5ur1ljnNKGoCAoCAqCgqAgKAgKgoKgICgIxmO5Hf7LfMt/Y6Y2BAVBQVAQFAQFQUFQEBQEBUFBUBAUBAVBQVAQFAQFQUFQEBQEBUFBUBAUBAVBQVAQFAQFQUFQEBQEBUFBUBAUBAVBQVAQFAQFQUFQEHwAQ1MPvoc3VmMAAAAASUVORK5CYII=\" id=\"imagec1e4d24fa4\" transform=\"scale(1 -1)translate(0 -68)\" x=\"34.240625\" y=\"-22.24856\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m3bd6ffd4a1\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"39.901495\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"96.51019\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"m4bf2692ee2\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"34.240625\" y=\"27.978995\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 31.778213)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"34.240625\" y=\"50.622473\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(20.878125 54.421692)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"34.240625\" y=\"73.265951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(20.878125 77.06517)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 95.477874)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-51\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 3406 84 \n", "L 4238 -825 \n", "L 3475 -825 \n", "L 2784 -78 \n", "Q 2681 -84 2626 -87 \n", "Q 2572 -91 2522 -91 \n", "Q 1538 -91 948 567 \n", "Q 359 1225 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1516 4351 937 \n", "Q 4025 359 3406 84 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 34.240625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.240625 22.318125 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-48\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2753 \n", "L 3553 2753 \n", "L 3553 4666 \n", "L 4184 4666 \n", "L 4184 0 \n", "L 3553 0 \n", "L 3553 2222 \n", "L 1259 2222 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "L 115.757147 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p7dc706b36f)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAAA1UlEQVR4nO3asQ0CMRAAQYPIoAT6g5IokAJIiCGf6I304kE7sQNrdcFZ8u71fLzGn7sez4vP7le8x08qCAqCgqAgKAgKgoKgICgIDt++wCdmVvExxrg974vPNiEoCAqCgqAgKAgKgoKgICgICoLNvGVm3iczb5NZTQgKgoKgICgICoKCoCAoCAqCVVf3razjM5oQFAQFQUFQEBQEBUFBUBAUBLvLOC3+/L+V9XpNTQgKgoKgICgICoKCoCAoCAqCgqAgKAgKgoKgICgICoKCoCAoCAqCN2pMEeIOrc2OAAAAAElFTkSuQmCC\" id=\"imageae9d7925d2\" transform=\"scale(1 -1)translate(0 -68)\" x=\"115.757147\" y=\"-22.24856\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"121.418016\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"178.026712\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"115.757147\" y=\"27.978995\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"115.757147\" y=\"50.622473\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"115.757147\" y=\"73.265951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 115.757147 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 115.757147 22.318125 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "L 197.273668 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pe78af58107)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAAA0ElEQVR4nO3asRGCQBBA0cNUS7A/LckCLcCEWPMfcQEjju/FFzB/NjhYlvf6eo+N7ufr1qNjjDEe63Pq/BGcvv0ARyNICBKChCAhSAgSgoQgIUgst3HZfHX/xav4LBMSgoQgIUgIEoKEICFICBKChCCxzKwh9jSz4tjzncqEhCAhSAgSgoQgIUgIEoKEIGENESYkBAlBQpAQJAQJQUKQECQEiamv7n7+/0OChCAhSAgSgoQgIUgIEoKEIGENESYkBAlBQpAQJAQJQUKQECQEiQ81uyDVflGJGAAAAABJRU5ErkJggg==\" id=\"imagec2bc10207d\" transform=\"scale(1 -1)translate(0 -68)\" x=\"197.273668\" y=\"-22.24856\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"202.934538\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"259.543234\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"197.273668\" y=\"27.978995\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"197.273668\" y=\"50.622473\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"197.273668\" y=\"73.265951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 197.273668 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 197.273668 22.318125 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 16.318125)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "L 278.79019 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pff4ccbfa01)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAAA1klEQVR4nO3asQ3CMBBAUQfRwQjsByMxIAPQUENF86u4QATxXu3C+roi1mV5Pu7PsdLlcFp7dIwxxvVxmzq/BbtvX2BrBAlBQpAQJAQJQUKQECQEiWXm030rPvmEMCEhSAgSgoQgIUgIEoKEICFICBL7b1/gbeZ98sn1hgkJQUKQECQECUFCkBAkBAlBYmoN4Q+iPyRICBKChCAhSAgSgoQgIUgIEoKEICFICBKChCAhSAgSgoQgIUgs53FcvYb4xbXCLBMSgoQgIUgIEoKEICFICBKCxAvgYRwTVkx+CAAAAABJRU5ErkJggg==\" id=\"imagecc137f6979\" transform=\"scale(1 -1)translate(0 -68)\" x=\"278.79019\" y=\"-22.24856\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"284.45106\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"341.059755\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"278.79019\" y=\"27.978995\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"278.79019\" y=\"50.622473\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"278.79019\" y=\"73.265951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 278.79019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 278.79019 22.318125 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_8\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 34.240625 194.026742 \n", "L 102.17106 194.026742 \n", "L 102.17106 126.096307 \n", "L 34.240625 126.096307 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p0a2ab6b475)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAABIklEQVR4nO3ZoVKCURBA4b0XmjwARV+MBMUx2Uz6AEYT2K08B+MDGCwG0AYJiV7oJ+0NjI5zvryzc+ew6ae0r/djJLXlc3Y0IiLq7CE9+3M3Sc8OHl+63hHHlh6tfZv/P4OAQcAgYBAwCBgEDAIGAYPAsL2t0sN1et+3fb9Lj25fP9Kz49r7O+bnvRAwCBgEDAIGAYOAQcAgYBAwCBgEStvv0n9DlDroWn47ukrPPn2vu3afixcCBgGDgEHAIGAQMAgYBAwCBoFhlHyTm4vLruXzw6b3Pb/OCwGDgEHAIGAQMAgYBAwCBgGDQLmOUfqr++Lwec63/AleCBgEDAIGAYOAQcAgYBAwCBgEDAIGAYOAQcAgYBAwCBgEDAIGAYOAQeAEGhYiOHGLNskAAAAASUVORK5CYII=\" id=\"image031121c2ec\" transform=\"scale(1 -1)translate(0 -68)\" x=\"34.240625\" y=\"-126.026742\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_9\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"39.901495\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(36.720245 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"96.51019\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(93.32894 208.625179)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(35.142561 222.303304)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4b\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2694 \n", "L 3353 4666 \n", "L 4166 4666 \n", "L 1850 2491 \n", "L 4331 0 \n", "L 3500 0 \n", "L 1259 2247 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"34.240625\" y=\"131.757176\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 135.556395)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"34.240625\" y=\"154.400655\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(20.878125 158.199873)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"34.240625\" y=\"177.044133\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(20.878125 180.843352)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 199.256055)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 34.240625 194.026742 \n", "L 34.240625 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 102.17106 194.026742 \n", "L 102.17106 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 34.240625 194.026742 \n", "L 102.17106 194.026742 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 34.240625 126.096307 \n", "L 102.17106 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_16\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 120.096307)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 115.757147 194.026742 \n", "L 183.687582 194.026742 \n", "L 183.687582 126.096307 \n", "L 115.757147 126.096307 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p08c121f343)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAABMklEQVR4nO3bMU4CQRSA4RkYqVArWjkMN7Aw8QZqY+kVbGm8gaeBAxBCsfEEkmgpY2H3V+8VJhD+r36ZTP5MsbPZrf37s5eg3sOjfw4/4dE6buHZl6t5ahuv+yE8O0qtfAYMAgYBg4BBwCBgEDAIGAQMAvHn5VJKrTW1eOZB/3l6E55dfn2k9pHhCQGDgEHAIGAQMAgYBAwCBgGDgEGgvl/PwleO+906t/oocVVqk/BobRe5fSR4QsAgYBAwCBgEDAIGAYOAQcAg0O4eF+Hhw2aVW33YhkfHt0+5tf+JJwQMAgYBg4BBwCBgEDAIGAQMAvWhXIbfur8lv9zJfnF0DDwhYBAwCBgEDAIGAYOAQcAgYBAwCLSe+GfhFO8mWZ4QMAgYBAwCBgGDgEHAIGAQMAj8AmBvIFEzAI9ZAAAAAElFTkSuQmCC\" id=\"image679b1af2e1\" transform=\"scale(1 -1)translate(0 -68)\" x=\"115.757147\" y=\"-126.026742\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_11\">\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"121.418016\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(118.236766 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"178.026712\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(174.845462 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(116.659083 222.303304)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_12\">\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"115.757147\" y=\"131.757176\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"115.757147\" y=\"154.400655\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"115.757147\" y=\"177.044133\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 115.757147 194.026742 \n", "L 115.757147 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 183.687582 194.026742 \n", "L 183.687582 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 115.757147 194.026742 \n", "L 183.687582 194.026742 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 115.757147 126.096307 \n", "L 183.687582 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_20\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 120.096307)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 197.273668 194.026742 \n", "L 265.204103 194.026742 \n", "L 265.204103 126.096307 \n", "L 197.273668 126.096307 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pcb42cb80d5)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAABOklEQVR4nO3aMUoDURCA4R0TrBJvkCo3sPEIFlaWniA2niNHiJ1gY+cNBA9gI+m10EYQlGAIRNhN/1czxWKE/6sfw/Dzirck0f58dU1PIqKv0SVXo0n67EGPe/xLBgGDgEHAIGAQMAgYBAwCBoFoV5/5p/t2Uxp+Nz1On714fc4PHh6W9ojBMH3WGwIGAYOAQcAgYBAwCBgEDAIGAYNAtN8f6W+Z1flpafjRzW36bPuyTJ8dnJyV9qjwhoBBwCBgEDAIGAQMAgYBg4BBIC6bcfrpvli/lYZ3Xf4Xjn35t5E3BAwCBgGDgEHAIGAQMAgYBAwC8Tufpd/Xm8en0vDx/UN5ob/mDQGDgEHAIGAQMAgYBAwCBgGDgEEgZs0o/S1zvX7vc5e94A0Bg4BBwCBgEDAIGAQMAgYBg8AOTKQpa3p/Fv0AAAAASUVORK5CYII=\" id=\"image18634ec6fb\" transform=\"scale(1 -1)translate(0 -68)\" x=\"197.273668\" y=\"-126.026742\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_13\">\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"202.934538\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(199.753288 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"259.543234\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(256.361984 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_23\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(198.175605 222.303304)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_14\">\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"197.273668\" y=\"131.757176\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"197.273668\" y=\"154.400655\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"197.273668\" y=\"177.044133\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 197.273668 194.026742 \n", "L 197.273668 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 265.204103 194.026742 \n", "L 265.204103 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 197.273668 194.026742 \n", "L 265.204103 194.026742 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 197.273668 126.096307 \n", "L 265.204103 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_24\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 120.096307)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 278.79019 194.026742 \n", "L 346.720625 194.026742 \n", "L 346.720625 126.096307 \n", "L 278.79019 126.096307 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p86953856f2)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAABKklEQVR4nO3asWoCQRRA0RkzSIRgSiEIqSz9tnTp0/lbgkWwC/mANDZpgoWCYJy1v9WbYkDhnvoxDHdfsQub6/53SFGXc3g0pZTq1zo8u3v7CM++fm6b7tFi1O3kO2UQMAgYBAwCBgGDgEHAIGAQKGmIv7nX703T4Xm+CM/Oli/h2eF0aLvH41N41g0Bg4BBwCBgEDAIGAQMAgYBg4BBIL8/PIc/Zlb7n553Ccujfs/RDQGDgEHAIGAQMAgYBAwCBgGDQJmWHJ+ul7bT6394NI8nbWd34oaAQcAgYBAwCBgEDAIGAYOAQaD8nWvDePxvo5Ru53W8hRsCBgGDgEHAIGAQMAgYBAwCBgGDQDnW+LdMLuOOV7kNbggYBAwCBgGDgEHAIGAQMAgYBK4BcCOSCB+tJAAAAABJRU5ErkJggg==\" id=\"imagea2249645c5\" transform=\"scale(1 -1)translate(0 -68)\" x=\"278.79019\" y=\"-126.026742\" width=\"68\" height=\"68\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_15\">\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"284.45106\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_25\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(281.26981 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m3bd6ffd4a1\" x=\"341.059755\" y=\"194.026742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_26\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(337.878505 208.625179)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_27\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(279.692126 222.303304)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_16\">\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"278.79019\" y=\"131.757176\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"278.79019\" y=\"154.400655\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#m4bf2692ee2\" x=\"278.79019\" y=\"177.044133\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 278.79019 194.026742 \n", "L 278.79019 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 346.720625 194.026742 \n", "L 346.720625 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 278.79019 194.026742 \n", "L 346.720625 194.026742 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 278.79019 126.096307 \n", "L 346.720625 126.096307 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_28\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 120.096307)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 366.250625 165.250433 \n", "L 371.958425 165.250433 \n", "L 371.958425 51.094433 \n", "L 366.250625 51.094433 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path clip-path=\"url(#p6ea4e33b4c)\" style=\"fill: #ffffff; stroke: #ffffff; stroke-width: 0.01; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAYAAAByCAYAAABwf6CfAAAAzElEQVR4nLXWQQ7DIBBDUSpx/7N2001hhl6AF2kQzTKW/fGQkLzW573a5upt5e5+6y0pBIS1RZwwlhmKqgtkZLkHhZgSCIfgkZhxDW5hjr3g5boHBTAOetAxNat6lGd1bz/cQ7M66KGH+qAH34/6fujIuAivCwcF9Z4HC5IBIcWgow5f89pyzRg8E6tRGTjbQ0LqaxAS6gxHlRmTzeuMfdJTD/0BpAQktZ5NBc1AVCiKDjP2hqeCcnBWFhTF5Q7tIB2zPJLvvSg6xv8ZP9W5DE4Qbu8+AAAAAElFTkSuQmCC\" id=\"image300f290d17\" transform=\"scale(1 -1)translate(0 -114)\" x=\"366\" y=\"-51\" width=\"6\" height=\"114\"/>\n", "   <g id=\"matplotlib.axis_17\">\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_41\">\n", "      <defs>\n", "       <path id=\"mdc748c61cf\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mdc748c61cf\" x=\"371.958425\" y=\"165.250433\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(378.**********.049652)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#mdc748c61cf\" x=\"371.958425\" y=\"142.419233\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(378.958425 146.218452)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#mdc748c61cf\" x=\"371.958425\" y=\"119.588033\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_31\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(378.958425 123.387252)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#mdc748c61cf\" x=\"371.958425\" y=\"96.756833\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_32\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(378.958425 100.556052)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_29\">\n", "     <g id=\"line2d_45\">\n", "      <g>\n", "       <use xlink:href=\"#mdc748c61cf\" x=\"371.958425\" y=\"73.925633\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_33\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(378.958425 77.724852)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_30\">\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#mdc748c61cf\" x=\"371.958425\" y=\"51.094433\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_34\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(378.958425 54.893652)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 366.250625 165.250433 \n", "L 369.104525 165.250433 \n", "L 371.958425 165.250433 \n", "L 371.958425 51.094433 \n", "L 369.104525 51.094433 \n", "L 366.250625 51.094433 \n", "L 366.250625 165.250433 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pd55de13844\">\n", "   <rect x=\"34.240625\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p7dc706b36f\">\n", "   <rect x=\"115.757147\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pe78af58107\">\n", "   <rect x=\"197.273668\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pff4ccbfa01\">\n", "   <rect x=\"278.79019\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p0a2ab6b475\">\n", "   <rect x=\"34.240625\" y=\"126.096307\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p08c121f343\">\n", "   <rect x=\"115.757147\" y=\"126.096307\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pcb42cb80d5\">\n", "   <rect x=\"197.273668\" y=\"126.096307\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p86953856f2\">\n", "   <rect x=\"278.79019\" y=\"126.096307\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6ea4e33b4c\">\n", "   <rect x=\"366.250625\" y=\"51.094433\" width=\"5.7078\" height=\"114.156\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 504x252 with 9 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Plusonetoincludethebeginning-of-sequencetoken\n", "d2l.show_heatmaps(\n", "    dec_self_attention_weights[:, :, :, :len(translation.split()) + 1],\n", "    xlabel='Key positions', ylabel='Query positions',\n", "    titles=['Head %d' % i for i in range(1, 5)], figsize=(7, 3.5))"]}, {"cell_type": "markdown", "id": "ddb7c476", "metadata": {"origin_pos": 82}, "source": ["与编码器的自注意力的情况类似，通过指定输入序列的有效长度，[**输出序列的查询不会与输入序列中填充位置的词元进行注意力计算**]。\n"]}, {"cell_type": "code", "execution_count": 20, "id": "910d950f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:22:32.668822Z", "iopub.status.busy": "2023-08-18T07:22:32.667894Z", "iopub.status.idle": "2023-08-18T07:22:33.326167Z", "shell.execute_reply": "2023-08-18T07:22:33.325372Z"}, "origin_pos": 83, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"402.06155pt\" height=\"208.108094pt\" viewBox=\"0 0 402.06155 208.108094\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:22:33.186096</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 208.108094 \n", "L 402.06155 208.108094 \n", "L 402.06155 0 \n", "L 0 0 \n", "L 0 208.108094 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 34.240625 66.773662 \n", "L 102.17106 66.773662 \n", "L 102.17106 26.015401 \n", "L 34.240625 26.015401 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pdcb219d066)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAAApCAYAAACMeY82AAABIElEQVR4nO3YMUoDQRiG4R1ZMSyrRoV0uYN3SG1ja+MF0qXwCBYWhpzAk6TLDZLCYLGFlYIgikuEnfQf88nauBHep9zhzw4vA8MmNB9vMTO+ri/cUta7nSafNw/3dia/mdm1XbHX9QZ2DUEEQQRBBEEEQURo3l/ttRuf135yUycfz0dXdmRUrdrvrCOcEEEQQRBBEEEQQRARmpfKXrt3w3M7OFkv0gvFoX9Zr2y9sa5wQgRBBEEEQQRBBEFEHopju/hUf/vJ4ij9vP70M1y7/w9BBEEEQQRBRB6j/bbLLs/8h1qslumFsm9nQn/Qdl+d4YQIggiCCIIIggiCiDAtTu29O340/5v+pDzxL9s/+P3v/TFOiCCIIIggiCCIIIjYAv21LEnSGC1EAAAAAElFTkSuQmCC\" id=\"imagea4b62bcdad\" transform=\"scale(1 -1)translate(0 -41)\" x=\"34.240625\" y=\"-25.773662\" width=\"68\" height=\"41\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m1bcbdf871f\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"37.637147\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"71.602364\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"md96a740cd7\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"34.240625\" y=\"29.411923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 33.211141)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"34.240625\" y=\"63.37714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 67.176359)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 85.589063)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-51\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 3406 84 \n", "L 4238 -825 \n", "L 3475 -825 \n", "L 2784 -78 \n", "Q 2681 -84 2626 -87 \n", "Q 2572 -91 2522 -91 \n", "Q 1538 -91 948 567 \n", "Q 359 1225 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1516 4351 937 \n", "Q 4025 359 3406 84 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 34.240625 66.773662 \n", "L 34.240625 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 102.17106 66.773662 \n", "L 102.17106 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 34.240625 66.773662 \n", "L 102.17106 66.773662 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.240625 26.015401 \n", "L 102.17106 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 20.015401)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-48\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2753 \n", "L 3553 2753 \n", "L 3553 4666 \n", "L 4184 4666 \n", "L 4184 0 \n", "L 3553 0 \n", "L 3553 2222 \n", "L 1259 2222 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 115.757147 66.773662 \n", "L 183.687582 66.773662 \n", "L 183.687582 26.015401 \n", "L 115.757147 26.015401 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p9c033ac701)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAAApCAYAAACMeY82AAABD0lEQVR4nO3ZsUrDQADG8VyI4BCFYIcW6e7sq/gAHX2jFlwcOnSyXX0FwcXJsVCcQyEY0NbE/SNfUDokLf/feMc11z8HR0h4vBjUkTH5eHdTUVRXzeNfpV0S0sz/Xk/EXW+gbwgiCCIIIggiCCKSy6SlSQh+br8z49+H7ahjnBBBEEEQQRBBEEEQEerPrX3b/VlO7cJysWocTx/m/mHZ8B9b6wYnRBBEEEQQRBBEEESEqsjttfs0vrEL79ZvzRNF7h92df33nXWEEyIIIggiCCIIIsLraGxvmduXZ7/y7Lx5PPaN+ZR5hAgiCCIIIggiCCLCfZTaa3dWbPzCluv1mJ3mvzoAQQRBBEEEQQRBxC9HBSbCm4HiUwAAAABJRU5ErkJggg==\" id=\"imagec56135ea6f\" transform=\"scale(1 -1)translate(0 -41)\" x=\"115.757147\" y=\"-25.773662\" width=\"68\" height=\"41\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"119.153668\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"153.118886\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"115.757147\" y=\"29.411923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"115.757147\" y=\"63.37714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 115.757147 66.773662 \n", "L 115.757147 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 183.687582 66.773662 \n", "L 183.687582 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 115.757147 66.773662 \n", "L 183.687582 66.773662 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 115.757147 26.015401 \n", "L 183.687582 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 20.015401)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 197.273668 66.773662 \n", "L 265.204103 66.773662 \n", "L 265.204103 26.015401 \n", "L 197.273668 26.015401 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p3828318c07)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAAApCAYAAACMeY82AAABHklEQVR4nO3XMUsCYRzHcZ+zpRosh5AIncW9ydXNpTcRrr0GX4JQaxk4ieJQQ1M4tLW4trW1CRER6l37j+cnbnfC9zPen//d8eXg4cJ6dpuVjL/HkRuVDgcP8cFmZXfCac3OiiLJ+wWKhiCCIIIggiCCICJkP0t77L7Um3axs3iND9KNf1j1fOcXywtfiCCIIIggiCCIIIg4WPd7dtj5eLezdD6JDypVu1O+5NjdOwQRBBEEEQQRBBFb/3Z7xxd28e7709wx+IdtmRUFX4ggiCCIIIggiAjp8sueMunzvV1M2t3o9d+ba7tzNHza/c1ywhciCCIIIggiCCIIIsL45Mweu1dvU7uYNFrR61lmb8fP3T4iiCCIIIggiCCI+AcyRzB1R4yHbwAAAABJRU5ErkJggg==\" id=\"imageb750b5da18\" transform=\"scale(1 -1)translate(0 -41)\" x=\"197.273668\" y=\"-25.773662\" width=\"68\" height=\"41\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"200.67019\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"234.635408\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"197.273668\" y=\"29.411923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"197.273668\" y=\"63.37714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 197.273668 66.773662 \n", "L 197.273668 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 265.204103 66.773662 \n", "L 265.204103 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 197.273668 66.773662 \n", "L 265.204103 66.773662 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 197.273668 26.015401 \n", "L 265.204103 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 20.015401)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 278.79019 66.773662 \n", "L 346.720625 66.773662 \n", "L 346.720625 26.015401 \n", "L 278.79019 26.015401 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pac77bd197b)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAAApCAYAAACMeY82AAABKUlEQVR4nO3XP0sDMQCG8eR6/Selg4ODUPwA/QCCX8jNyVUQwcVFKIiOXZz8DtZNcHVxcVcEFQQL0l7dX/LC3dJ0eH5jQi7hIRAuVl9vq2BUD3duKoT5b3J49fRol5TnU/+9DVHkPsCmIYggiCCIIIggiIjHraF9di8+XvzKxV96vN31m3X6tQ+WCzdEEEQQRBBEEEQQRMTF/a19dueXE7uwf3qWHC92Rn6z7d0GR8uDGyIIIggiCCIIIggiymJ8YCd7+zM7Fzvpv9rlVfo5DiGE8uSm/sky4YYIggiCCIIIgoh4GAb25+76+9WvrJbNN+tuNV6zbtwQQRBBEEEQQRBBEBGrn0/77B4N9+zCyftzeqJV+s16g/ony4QbIggiCCIIIggiCCL+AWfUKl+gFvJnAAAAAElFTkSuQmCC\" id=\"image10323accb3\" transform=\"scale(1 -1)translate(0 -41)\" x=\"278.79019\" y=\"-25.773662\" width=\"68\" height=\"41\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"282.186712\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"316.151929\" y=\"66.773662\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"278.79019\" y=\"29.411923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"278.79019\" y=\"63.37714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 278.79019 66.773662 \n", "L 278.79019 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 346.720625 66.773662 \n", "L 346.720625 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 278.79019 66.773662 \n", "L 346.720625 66.773662 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 278.79019 26.015401 \n", "L 346.720625 26.015401 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 20.015401)scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 34.240625 170.551844 \n", "L 102.17106 170.551844 \n", "L 102.17106 129.793583 \n", "L 34.240625 129.793583 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p81d6e78ee3)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAAApCAYAAACMeY82AAABHklEQVR4nO3XMUoDQRiG4Z3sbgRBdrvUSSvkCDaKF7CytxE8g1dImRtYeJkUEYuAiKBVQAlBJAnZtf+YDxZSTIr3KWf4k5+XgZCwubtuM6N8nLirrF1/x8/fXuxMfnlr745FL/UCx4YggiCCIIIggiCi+H39spd1eeInt5vocRidH7pTUrwQQRBBEEEQQRBBEBGaj7n9t9ssZn5yMY+fn1V2JL956LxYKrwQQRBBEEEQQRBBEBGa1dL+7N5XQzs4Xb2bTwz+y/Ki+2aJ8EIEQQRBBEEEQUSRNXt7eVWf+sm/dfx8v/Mz9aDjWunwQgRBBEEEQQRBBEFE+LkY2z931dOzHWyXn/GLsm9nesNx980S4YUIggiCCIIIggiCiH8JXSxOJJpGhwAAAABJRU5ErkJggg==\" id=\"imagee34a3499b0\" transform=\"scale(1 -1)translate(0 -41)\" x=\"34.240625\" y=\"-129.551844\" width=\"68\" height=\"41\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_9\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"37.637147\" y=\"170.551844\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(34.455897 185.150281)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"71.602364\" y=\"170.551844\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(68.421114 185.150281)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(35.142561 198.828406)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4b\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2694 \n", "L 3353 4666 \n", "L 4166 4666 \n", "L 1850 2491 \n", "L 4331 0 \n", "L 3500 0 \n", "L 1259 2247 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"34.240625\" y=\"133.190104\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 136.989323)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"34.240625\" y=\"167.155322\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 170.954541)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 189.367244)rotate(-90)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 34.240625 170.551844 \n", "L 34.240625 129.793583 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 102.17106 170.551844 \n", "L 102.17106 129.793583 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 34.240625 170.551844 \n", "L 102.17106 170.551844 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 34.240625 129.793583 \n", "L 102.17106 129.793583 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 123.793583)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 115.757147 170.551844 \n", "L 183.687582 170.551844 \n", "L 183.687582 129.793583 \n", "L 115.757147 129.793583 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p862a67cd42)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAAApCAYAAACMeY82AAABE0lEQVR4nO3aQUoCURyA8Xk2aYnQDXLVHaQDtK+Fm1q4CLpBELj2Cl6iXVfpBEYQRKCgi7Aa9x/zHwSJp/D9lu/PGx8fDwbU9Pf1XhWR9Xc4Wo6Gtevd66twz9HdYzjbF63cB9g3BgGDgEHAIGAQKIv2aTj8Gd+Hs+7tTf2gf7HrmbLyhoBBwCBgEDAIGATK2eUgHJ6/PDfsbNcuV4vPXc+UlTcEDAIGAYOAQcAgkKrVPPyS+emsH26cfLwGT0zxh530tj9ZJt4QMAgYBAwCBoH0UPTCt8x0+Rbv/F3Xr1fxL6PpuLP1wXLxhoBBwCBgEDAIGATKTmpo0vAPoqIV7Gt63gE47NP/A4OAQcAgYBAwCGwAfiUioA68wcAAAAAASUVORK5CYII=\" id=\"image6fd22273c5\" transform=\"scale(1 -1)translate(0 -41)\" x=\"115.757147\" y=\"-129.551844\" width=\"68\" height=\"41\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_11\">\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"119.153668\" y=\"170.551844\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(115.972418 185.150281)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"153.118886\" y=\"170.551844\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(149.937636 185.150281)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(116.659083 198.828406)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_12\">\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"115.757147\" y=\"133.190104\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"115.757147\" y=\"167.155322\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 115.757147 170.551844 \n", "L 115.757147 129.793583 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 183.687582 170.551844 \n", "L 183.687582 129.793583 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 115.757147 170.551844 \n", "L 183.687582 170.551844 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 115.757147 129.793583 \n", "L 183.687582 129.793583 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 123.793583)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 197.273668 170.551844 \n", "L 265.204103 170.551844 \n", "L 265.204103 129.793583 \n", "L 197.273668 129.793583 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pabeaa8b218)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAAApCAYAAACMeY82AAABHklEQVR4nO3YsUqCURjGcY+aQwhNQhAuhhIOEd5A4OLg0NA95P04tDsLIS1NgZfg4gU0FBIRhE5anvaH88AHDsfh/xvPywuHPwc++MLf4jWWnOhH+6dJ+vxjZXdq46mdHYty7gscG4IIggiCCIIIgohquGjbYfx8s7PNfJE8r/dah94pK16IIIggiCCIIIggiKjGr3c7jPNnOzvtnCfPQ39w8KVy4oUIggiCCIIIggiCiLBff9s/ydPmlV28n43Tg5Oa3anc9IvfLBNeiCCIIIggiCCICNuHgf3KlId3fnP9kz6/7NqVyvVt4YvlwgsRBBEEEQQRBBEEEWFUqtvP7uPyxS82munB787vnDWK3ywTXoggiCCIIIggiCCI+AfpBirhnabx+wAAAABJRU5ErkJggg==\" id=\"imageb30e758e81\" transform=\"scale(1 -1)translate(0 -41)\" x=\"197.273668\" y=\"-129.551844\" width=\"68\" height=\"41\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_13\">\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"200.67019\" y=\"170.551844\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(197.48894 185.150281)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"234.635408\" y=\"170.551844\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(231.454158 185.150281)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(198.175605 198.828406)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_14\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"197.273668\" y=\"133.190104\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"197.273668\" y=\"167.155322\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 197.273668 170.551844 \n", "L 197.273668 129.793583 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 265.204103 170.551844 \n", "L 265.204103 129.793583 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 197.273668 170.551844 \n", "L 265.204103 170.551844 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 197.273668 129.793583 \n", "L 265.204103 129.793583 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_22\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 123.793583)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 278.79019 170.551844 \n", "L 346.720625 170.551844 \n", "L 346.720625 129.793583 \n", "L 278.79019 129.793583 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pcdeff6e656)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAEQAAAApCAYAAACMeY82AAABI0lEQVR4nO3XMUoDQRiG4Z3dASGCouQIihewSSMEBCGFxxCs0nkDEdKn1RNYWdnar5AbBAwIWkREFKPs2n/MBxGLifA+5fz8MLwMLBuap2lbGM3kzo2Kr6vL5PmsfrA7O/e1na2KMvcFVg1BBEEEQQRBBEFELN5f/bQMdhTWYvK800mf/xe8EEEQQRBBEEEQQRARzqpN+7d7Mbmxi+3nR3rw9mJ3qv2jpS+WCy9EEEQQRBBEEEQQRIRm/mg/u/PBoV3c6O2lB92u3YnD0fI3y4QXIggiCCIIIggi4unWrh2Or8/trOwNkuft8+zPl8qJFyIIIggiCCIIIggiwnd9a3/uFiP/2Y3b68nz8qBvd6rjk19cLQ9eiCCIIIggiCCIIIj4AUuyK6emR8TMAAAAAElFTkSuQmCC\" id=\"imagec2954ae4fd\" transform=\"scale(1 -1)translate(0 -41)\" x=\"278.79019\" y=\"-129.551844\" width=\"68\" height=\"41\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_15\">\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"282.186712\" y=\"170.551844\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(279.005462 185.150281)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m1bcbdf871f\" x=\"316.151929\" y=\"170.551844\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(312.970679 185.150281)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(279.692126 198.828406)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_16\">\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"278.79019\" y=\"133.190104\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#md96a740cd7\" x=\"278.79019\" y=\"167.155322\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 278.79019 170.551844 \n", "L 278.79019 129.793583 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 346.720625 170.551844 \n", "L 346.720625 129.793583 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 278.79019 170.551844 \n", "L 346.720625 170.551844 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 278.79019 129.793583 \n", "L 346.720625 129.793583 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_26\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 123.793583)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 366.250625 155.361622 \n", "L 371.958425 155.361622 \n", "L 371.958425 41.205622 \n", "L 366.250625 41.205622 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path clip-path=\"url(#p7f9a3ed71e)\" style=\"fill: #ffffff; stroke: #ffffff; stroke-width: 0.01; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAYAAAByCAYAAABwf6CfAAAAzUlEQVR4nLWVQQ7EMAgDqcT/v7qXPTUQ+gGmklHaY9Bgm6TJVf9fWfO51e7WzW0nFJhoJSYaxQQVBjl0u1gIKCSJoyscSaxTrdhV6jmAKN1VkLiegzVoiHorJA7mOLhRg1lhDvoNBjl0uyBeg/2QcyAha1SiuErUoivjnPhe5+xiISiHbjfhbk8k6PFKKrxoyK3kQsb3GkEam15OLPQba+aJRL9uvg3FgQi0S610cSQwYH8O3wjYDvPFhNoKiRv8etBIdI2FJ1FuhcQNxAMVLAxITCrWxAAAAABJRU5ErkJggg==\" id=\"imageae964fe366\" transform=\"scale(1 -1)translate(0 -114)\" x=\"366\" y=\"-41\" width=\"6\" height=\"114\"/>\n", "   <g id=\"matplotlib.axis_17\">\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_33\">\n", "      <defs>\n", "       <path id=\"m5fa1c9c32a\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5fa1c9c32a\" x=\"371.958425\" y=\"155.361622\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(378.958425 159.160841)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m5fa1c9c32a\" x=\"371.958425\" y=\"119.948789\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(378.958425 123.748008)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m5fa1c9c32a\" x=\"371.958425\" y=\"84.535956\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(378.958425 88.335174)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m5fa1c9c32a\" x=\"371.958425\" y=\"49.123122\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(378.958425 52.922341)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 366.250625 155.361622 \n", "L 369.104525 155.361622 \n", "L 371.958425 155.361622 \n", "L 371.958425 41.205622 \n", "L 369.104525 41.205622 \n", "L 366.250625 41.205622 \n", "L 366.250625 155.361622 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pdcb219d066\">\n", "   <rect x=\"34.240625\" y=\"26.015401\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p9c033ac701\">\n", "   <rect x=\"115.757147\" y=\"26.015401\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p3828318c07\">\n", "   <rect x=\"197.273668\" y=\"26.015401\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pac77bd197b\">\n", "   <rect x=\"278.79019\" y=\"26.015401\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p81d6e78ee3\">\n", "   <rect x=\"34.240625\" y=\"129.793583\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p862a67cd42\">\n", "   <rect x=\"115.757147\" y=\"129.793583\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pabeaa8b218\">\n", "   <rect x=\"197.273668\" y=\"129.793583\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pcdeff6e656\">\n", "   <rect x=\"278.79019\" y=\"129.793583\" width=\"67.930435\" height=\"40.758261\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p7f9a3ed71e\">\n", "   <rect x=\"366.250625\" y=\"41.205622\" width=\"5.7078\" height=\"114.156\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 504x252 with 9 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["d2l.show_heatmaps(\n", "    dec_inter_attention_weights, xlabel='Key positions',\n", "    ylabel='Query positions', titles=['Head %d' % i for i in range(1, 5)],\n", "    figsize=(7, 3.5))"]}, {"cell_type": "markdown", "id": "771a0d76", "metadata": {"origin_pos": 84}, "source": ["尽管Transformer架构是为了*序列到序列*的学习而提出的，但正如本书后面将提及的那样，Transformer编码器或Transformer解码器通常被单独用于不同的深度学习任务中。\n", "\n", "## 小结\n", "\n", "* Transformer是编码器－解码器架构的一个实践，尽管在实际情况中编码器或解码器可以单独使用。\n", "* 在Transformer中，多头自注意力用于表示输入序列和输出序列，不过解码器必须通过掩蔽机制来保留自回归属性。\n", "* Transformer中的残差连接和层规范化是训练非常深度模型的重要工具。\n", "* Transformer模型中基于位置的前馈网络使用同一个多层感知机，作用是对所有序列位置的表示进行转换。\n", "\n", "## 练习\n", "\n", "1. 在实验中训练更深的Transformer将如何影响训练速度和翻译效果？\n", "1. 在Transformer中使用加性注意力取代缩放点积注意力是不是个好办法？为什么？\n", "1. 对于语言模型，应该使用Transformer的编码器还是解码器，或者两者都用？如何设计？\n", "1. 如果输入序列很长，Transformer会面临什么挑战？为什么？\n", "1. 如何提高Transformer的计算速度和内存使用效率？提示：可以参考论文 :cite:`Tay.Dehghani.Bahri.ea.2020`。\n", "1. 如果不使用卷积神经网络，如何设计基于Transformer模型的图像分类任务？提示：可以参考Vision Transformer :cite:`Dosovitskiy.Beyer.Kolesnikov.ea.2021`。\n"]}, {"cell_type": "markdown", "id": "b36a7e4f", "metadata": {"origin_pos": 86, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/5756)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
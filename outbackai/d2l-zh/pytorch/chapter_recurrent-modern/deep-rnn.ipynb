{"cells": [{"cell_type": "markdown", "id": "6570c16f", "metadata": {"origin_pos": 0}, "source": ["# 深度循环神经网络\n", "\n", ":label:`sec_deep_rnn`\n", "\n", "到目前为止，我们只讨论了具有一个单向隐藏层的循环神经网络。\n", "其中，隐变量和观测值与具体的函数形式的交互方式是相当随意的。\n", "只要交互类型建模具有足够的灵活性，这就不是一个大问题。\n", "然而，对一个单层来说，这可能具有相当的挑战性。\n", "之前在线性模型中，我们通过添加更多的层来解决这个问题。\n", "而在循环神经网络中，我们首先需要确定如何添加更多的层，\n", "以及在哪里添加额外的非线性，因此这个问题有点棘手。\n", "\n", "事实上，我们可以将多层循环神经网络堆叠在一起，\n", "通过对几个简单层的组合，产生了一个灵活的机制。\n", "特别是，数据可能与不同层的堆叠有关。\n", "例如，我们可能希望保持有关金融市场状况\n", "（熊市或牛市）的宏观数据可用，\n", "而微观数据只记录较短期的时间动态。\n", "\n", " :numref:`fig_deep_rnn`描述了一个具有$L$个隐藏层的深度循环神经网络，\n", "每个隐状态都连续地传递到当前层的下一个时间步和下一层的当前时间步。\n", "\n", "![深度循环神经网络结构](../img/deep-rnn.svg)\n", ":label:`fig_deep_rnn`\n", "\n", "## 函数依赖关系\n", "\n", "我们可以将深度架构中的函数依赖关系形式化，\n", "这个架构是由 :numref:`fig_deep_rnn`中描述了$L$个隐藏层构成。\n", "后续的讨论主要集中在经典的循环神经网络模型上，\n", "但是这些讨论也适应于其他序列模型。\n", "\n", "假设在时间步$t$有一个小批量的输入数据\n", "$\\mathbf{X}_t \\in \\mathbb{R}^{n \\times d}$\n", "（样本数：$n$，每个样本中的输入数：$d$）。\n", "同时，将$l^\\mathrm{th}$隐藏层（$l=1,\\ldots,L$）\n", "的隐状态设为$\\mathbf{H}_t^{(l)}  \\in \\mathbb{R}^{n \\times h}$\n", "（隐藏单元数：$h$），\n", "输出层变量设为$\\mathbf{O}_t \\in \\mathbb{R}^{n \\times q}$\n", "（输出数：$q$）。\n", "设置$\\mathbf{H}_t^{(0)} = \\mathbf{X}_t$，\n", "第$l$个隐藏层的隐状态使用激活函数$\\phi_l$，则：\n", "\n", "$$\\mathbf{H}_t^{(l)} = \\phi_l(\\mathbf{H}_t^{(l-1)} \\mathbf{W}_{xh}^{(l)} + \\mathbf{H}_{t-1}^{(l)} \\mathbf{W}_{hh}^{(l)}  + \\mathbf{b}_h^{(l)}),$$\n", ":eqlabel:`eq_deep_rnn_H`\n", "\n", "其中，权重$\\mathbf{W}_{xh}^{(l)} \\in \\mathbb{R}^{h \\times h}$，\n", "$\\mathbf{W}_{hh}^{(l)} \\in \\mathbb{R}^{h \\times h}$和\n", "偏置$\\mathbf{b}_h^{(l)} \\in \\mathbb{R}^{1 \\times h}$\n", "都是第$l$个隐藏层的模型参数。\n", "\n", "最后，输出层的计算仅基于第$l$个隐藏层最终的隐状态：\n", "\n", "$$\\mathbf{O}_t = \\mathbf{H}_t^{(L)} \\mathbf{W}_{hq} + \\mathbf{b}_q,$$\n", "\n", "其中，权重$\\mathbf{W}_{hq} \\in \\mathbb{R}^{h \\times q}$和偏置$\\mathbf{b}_q \\in \\mathbb{R}^{1 \\times q}$都是输出层的模型参数。\n", "\n", "与多层感知机一样，隐藏层数目$L$和隐藏单元数目$h$都是超参数。\n", "也就是说，它们可以由我们调整的。\n", "另外，用门控循环单元或长短期记忆网络的隐状态\n", "来代替 :eqref:`eq_deep_rnn_H`中的隐状态进行计算，\n", "可以很容易地得到深度门控循环神经网络或深度长短期记忆神经网络。\n", "\n", "## 简洁实现\n", "\n", "实现多层循环神经网络所需的许多逻辑细节在高级API中都是现成的。\n", "简单起见，我们仅示范使用此类内置函数的实现方式。\n", "以长短期记忆网络模型为例，\n", "该代码与之前在 :numref:`sec_lstm`中使用的代码非常相似，\n", "实际上唯一的区别是我们指定了层的数量，\n", "而不是使用单一层这个默认值。\n", "像往常一样，我们从加载数据集开始。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "0c3000b3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:24:40.256397Z", "iopub.status.busy": "2023-08-18T07:24:40.255737Z", "iopub.status.idle": "2023-08-18T07:24:43.344661Z", "shell.execute_reply": "2023-08-18T07:24:43.343759Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l\n", "\n", "batch_size, num_steps = 32, 35\n", "train_iter, vocab = d2l.load_data_time_machine(batch_size, num_steps)"]}, {"cell_type": "markdown", "id": "f83fd896", "metadata": {"origin_pos": 4}, "source": ["像选择超参数这类架构决策也跟 :numref:`sec_lstm`中的决策非常相似。\n", "因为我们有不同的词元，所以输入和输出都选择相同数量，即`vocab_size`。\n", "隐藏单元的数量仍然是$256$。\n", "唯一的区别是，我们现在(**通过`num_layers`的值来设定隐藏层数**)。\n"]}, {"cell_type": "code", "execution_count": 2, "id": "d9ce730c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:24:43.348714Z", "iopub.status.busy": "2023-08-18T07:24:43.348349Z", "iopub.status.idle": "2023-08-18T07:24:45.383961Z", "shell.execute_reply": "2023-08-18T07:24:45.383098Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [], "source": ["vocab_size, num_hiddens, num_layers = len(vocab), 256, 2\n", "num_inputs = vocab_size\n", "device = d2l.try_gpu()\n", "lstm_layer = nn.LSTM(num_inputs, num_hiddens, num_layers)\n", "model = d2l.RNNModel(lstm_layer, len(vocab))\n", "model = model.to(device)"]}, {"cell_type": "markdown", "id": "bc7a543d", "metadata": {"origin_pos": 8}, "source": ["## [**训练**]与预测\n", "\n", "由于使用了长短期记忆网络模型来实例化两个层，因此训练速度被大大降低了。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "97524e5b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T07:24:45.388179Z", "iopub.status.busy": "2023-08-18T07:24:45.387601Z", "iopub.status.idle": "2023-08-18T07:25:17.829775Z", "shell.execute_reply": "2023-08-18T07:25:17.828890Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["perplexity 1.0, 186005.7 tokens/sec on cuda:0\n", "time traveller for so it will be convenient to speak of himwas e\n", "travelleryou can show black is white by argument said filby\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"252.646875pt\" height=\"180.65625pt\" viewBox=\"0 0 252.**********.65625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T07:25:17.793279</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.5.1, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 180.65625 \n", "L 252.**********.65625 \n", "L 252.646875 0 \n", "L 0 0 \n", "L 0 180.65625 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.1 \n", "L 235.**********.1 \n", "L 235.903125 7.2 \n", "L 40.603125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 76.474554 143.1 \n", "L 76.474554 7.2 \n", "\" clip-path=\"url(#p295505e868)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mf48e2aad6f\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf48e2aad6f\" x=\"76.474554\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(66.930804 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 116.331696 143.1 \n", "L 116.331696 7.2 \n", "\" clip-path=\"url(#p295505e868)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mf48e2aad6f\" x=\"116.331696\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(106.787946 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 156.188839 143.1 \n", "L 156.188839 7.2 \n", "\" clip-path=\"url(#p295505e868)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mf48e2aad6f\" x=\"156.188839\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(146.645089 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 196.045982 143.1 \n", "L 196.045982 7.2 \n", "\" clip-path=\"url(#p295505e868)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mf48e2aad6f\" x=\"196.045982\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(186.502232 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 235.**********.1 \n", "L 235.903125 7.2 \n", "\" clip-path=\"url(#p295505e868)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mf48e2aad6f\" x=\"235.903125\" y=\"143.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(226.359375 157.698438)scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(123.025 171.376563)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 40.603125 107.163782 \n", "L 235.903125 107.163782 \n", "\" clip-path=\"url(#p295505e868)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m330f65ffa6\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m330f65ffa6\" x=\"40.603125\" y=\"107.163782\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(27.240625 110.963001)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 40.603125 69.761109 \n", "L 235.903125 69.761109 \n", "\" clip-path=\"url(#p295505e868)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m330f65ffa6\" x=\"40.603125\" y=\"69.761109\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(20.878125 73.560327)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 40.603125 32.358435 \n", "L 235.903125 32.358435 \n", "\" clip-path=\"url(#p295505e868)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m330f65ffa6\" x=\"40.603125\" y=\"32.358435\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(20.878125 36.157654)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- perplexity -->\n", "     <g transform=\"translate(14.798437 100.276562)rotate(-90)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-70\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"166.113281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"229.589844\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"257.373047\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"317.146484\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"376.326172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"404.109375\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"443.318359\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 40.603125 13.377273 \n", "L 44.588839 14.173165 \n", "L 48.574554 16.221135 \n", "L 52.560268 24.689564 \n", "L 56.545982 37.339537 \n", "L 60.531696 55.093469 \n", "L 64.517411 61.216224 \n", "L 68.503125 67.143057 \n", "L 72.488839 70.310792 \n", "L 76.474554 76.545343 \n", "L 80.460268 81.895015 \n", "L 84.445982 86.368332 \n", "L 88.431696 91.555997 \n", "L 92.417411 96.634748 \n", "L 96.403125 101.381433 \n", "L 100.388839 108.901228 \n", "L 104.374554 113.784442 \n", "L 108.360268 120.361638 \n", "L 112.345982 125.997245 \n", "L 116.331696 129.834052 \n", "L 120.317411 133.823263 \n", "L 124.303125 135.622796 \n", "L 128.288839 135.892039 \n", "L 132.274554 136.312956 \n", "L 136.260268 136.452893 \n", "L 140.245982 133.269093 \n", "L 144.231696 136.414684 \n", "L 148.217411 136.634164 \n", "L 152.203125 136.622482 \n", "L 156.188839 136.733294 \n", "L 160.174554 136.795947 \n", "L 164.160268 136.732808 \n", "L 168.145982 136.812662 \n", "L 172.131696 136.780278 \n", "L 176.117411 136.822719 \n", "L 180.103125 136.820697 \n", "L 184.088839 136.850958 \n", "L 188.074554 136.812331 \n", "L 192.060268 136.821786 \n", "L 196.045982 136.841669 \n", "L 200.031696 136.854685 \n", "L 204.017411 136.882012 \n", "L 208.003125 136.841638 \n", "L 211.988839 136.883914 \n", "L 215.974554 136.922727 \n", "L 219.960268 136.857606 \n", "L 223.945982 136.909785 \n", "L 227.931696 136.839566 \n", "L 231.917411 136.879456 \n", "L 235.903125 136.877425 \n", "\" clip-path=\"url(#p295505e868)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.1 \n", "L 40.603125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 235.**********.1 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.1 \n", "L 235.**********.1 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 7.2 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 173.628125 29.878125 \n", "L 228.903125 29.878125 \n", "Q 230.903125 29.878125 230.903125 27.878125 \n", "L 230.903125 14.2 \n", "Q 230.903125 12.2 228.903125 12.2 \n", "L 173.628125 12.2 \n", "Q 171.628125 12.2 171.628125 14.2 \n", "L 171.628125 27.878125 \n", "Q 171.628125 29.878125 173.628125 29.878125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_18\">\n", "     <path d=\"M 175.628125 20.298437 \n", "L 185.628125 20.298437 \n", "L 195.628125 20.298437 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train -->\n", "     <g transform=\"translate(203.628125 23.798437)scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p295505e868\">\n", "   <rect x=\"40.603125\" y=\"7.2\" width=\"195.3\" height=\"135.9\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 252x180 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["num_epochs, lr = 500, 2\n", "d2l.train_ch8(model, train_iter, vocab, lr*1.0, num_epochs, device)"]}, {"cell_type": "markdown", "id": "95f552d9", "metadata": {"origin_pos": 10}, "source": ["## 小结\n", "\n", "* 在深度循环神经网络中，隐状态的信息被传递到当前层的下一时间步和下一层的当前时间步。\n", "* 有许多不同风格的深度循环神经网络，\n", "  如长短期记忆网络、门控循环单元、或经典循环神经网络。\n", "  这些模型在深度学习框架的高级API中都有涵盖。\n", "* 总体而言，深度循环神经网络需要大量的调参（如学习率和修剪）\n", "  来确保合适的收敛，模型的初始化也需要谨慎。\n", "\n", "## 练习\n", "\n", "1. 基于我们在 :numref:`sec_rnn_scratch`中讨论的单层实现，\n", "   尝试从零开始实现两层循环神经网络。\n", "1. 在本节训练模型中，比较使用门控循环单元替换长短期记忆网络后模型的精确度和训练速度。\n", "1. 如果增加训练数据，能够将困惑度降到多低？\n", "1. 在为文本建模时，是否可以将不同作者的源数据合并？有何优劣呢？\n"]}, {"cell_type": "markdown", "id": "1affbed5", "metadata": {"origin_pos": 12, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/2770)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
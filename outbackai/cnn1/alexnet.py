import torch
from torch import nn
from d2l import torch as d2l




class AlexNet(d2l.Classifier):
    def __init__(self, lr=0.1, num_classes=10):
        super().__init__()
        self.save_hyperparameters()
        self.net = nn.Sequential(
            nn.LazyConv2d(96, kernel_size=11, stride=4, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(kernel_size=3, stride=2),
            nn.LazyConv2d(256, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.MaxPool2d(kernel_size=3, stride=2),
            nn.LazyConv2d(384, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.LazyConv2d(384, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.LazyConv2d(256, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(kernel_size=3, stride=2),
            nn.<PERSON><PERSON>(),
            nn.<PERSON><PERSON><PERSON><PERSON>ar(4096),
            nn.<PERSON>(),
            nn.Dropout(p=0.5),
            nn.<PERSON>zy<PERSON>ar(4096),
            nn.ReL<PERSON>(),
            nn.Dropout(p=0.5),
            nn.LazyLinear(num_classes)
        )

        self.net.apply(d2l.init_cnn)


if __name__ == '__main__':
    model = AlexNet(lr=0.01)
    data = d2l.FashionMNIST(batch_size=128, resize=(224, 224))
    trainer = d2l.Trainer(max_epochs=1, num_gpus=1)
    trainer.fit(model, data)

{"cells": [{"cell_type": "markdown", "id": "625d329c", "metadata": {"origin_pos": 1}, "source": ["# Numerical Stability and Initialization\n", ":label:`sec_numerical_stability`\n", "\n", "\n", "Thus far, every model that we have implemented\n", "required that we initialize its parameters\n", "according to some pre-specified distribution.\n", "Until now, we took the initialization scheme for granted,\n", "glossing over the details of how these choices are made.\n", "You might have even gotten the impression that these choices\n", "are not especially important.\n", "On the contrary, the choice of initialization scheme\n", "plays a significant role in neural network learning,\n", "and it can be crucial for maintaining numerical stability.\n", "Moreover, these choices can be tied up in interesting ways\n", "with the choice of the nonlinear activation function.\n", "Which function we choose and how we initialize parameters\n", "can determine how quickly our optimization algorithm converges.\n", "Poor choices here can cause us to encounter\n", "exploding or vanishing gradients while training.\n", "In this section, we delve into these topics in greater detail\n", "and discuss some useful heuristics\n", "that you will find useful\n", "throughout your career in deep learning.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "b508135e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:19.444553Z", "iopub.status.busy": "2023-08-18T19:43:19.443902Z", "iopub.status.idle": "2023-08-18T19:43:22.266893Z", "shell.execute_reply": "2023-08-18T19:43:22.265631Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "262d79ee", "metadata": {"origin_pos": 6}, "source": ["## Vanishing and Exploding Gradients\n", "\n", "Consider a deep network with $L$ layers,\n", "input $\\mathbf{x}$ and output $\\mathbf{o}$.\n", "With each layer $l$ defined by a transformation $f_l$\n", "parametrized by weights $\\mathbf{W}^{(l)}$,\n", "whose hidden layer output is $\\mathbf{h}^{(l)}$ (let $\\mathbf{h}^{(0)} = \\mathbf{x}$),\n", "our network can be expressed as:\n", "\n", "$$\\mathbf{h}^{(l)} = f_l (\\mathbf{h}^{(l-1)}) \\textrm{ and thus } \\mathbf{o} = f_L \\circ \\cdots \\circ f_1(\\mathbf{x}).$$\n", "\n", "If all the hidden layer output and the input are vectors,\n", "we can write the gradient of $\\mathbf{o}$ with respect to\n", "any set of parameters $\\mathbf{W}^{(l)}$ as follows:\n", "\n", "$$\\partial_{\\mathbf{W}^{(l)}} \\mathbf{o} = \\underbrace{\\partial_{\\mathbf{h}^{(L-1)}} \\mathbf{h}^{(L)}}_{ \\mathbf{M}^{(L)} \\stackrel{\\textrm{def}}{=}} \\cdots \\underbrace{\\partial_{\\mathbf{h}^{(l)}} \\mathbf{h}^{(l+1)}}_{ \\mathbf{M}^{(l+1)} \\stackrel{\\textrm{def}}{=}} \\underbrace{\\partial_{\\mathbf{W}^{(l)}} \\mathbf{h}^{(l)}}_{ \\mathbf{v}^{(l)} \\stackrel{\\textrm{def}}{=}}.$$\n", "\n", "In other words, this gradient is\n", "the product of $L-l$ matrices\n", "$\\mathbf{M}^{(L)} \\cdots \\mathbf{M}^{(l+1)}$\n", "and the gradient vector $\\mathbf{v}^{(l)}$.\n", "Thus we are susceptible to the same\n", "problems of numerical underflow that often crop up\n", "when multiplying together too many probabilities.\n", "When dealing with probabilities, a common trick is to\n", "switch into log-space, i.e., shifting\n", "pressure from the mantissa to the exponent\n", "of the numerical representation.\n", "Unfortunately, our problem above is more serious:\n", "initially the matrices $\\mathbf{M}^{(l)}$ may have a wide variety of eigenvalues.\n", "They might be small or large, and\n", "their product might be *very large* or *very small*.\n", "\n", "The risks posed by unstable gradients\n", "go beyond numerical representation.\n", "Gradients of unpredictable magnitude\n", "also threaten the stability of our optimization algorithms.\n", "We may be facing parameter updates that are either\n", "(i) excessively large, destroying our model\n", "(the *exploding gradient* problem);\n", "or (ii) excessively small\n", "(the *vanishing gradient* problem),\n", "rendering learning impossible as parameters\n", "hardly move on each update.\n", "\n", "\n", "### (**Vanishing Gradients**)\n", "\n", "One frequent culprit causing the vanishing gradient problem\n", "is the choice of the activation function $\\sigma$\n", "that is appended following each layer's linear operations.\n", "Historically, the sigmoid function\n", "$1/(1 + \\exp(-x))$ (introduced in :numref:`sec_mlp`)\n", "was popular because it resembles a thresholding function.\n", "Since early artificial neural networks were inspired\n", "by biological neural networks,\n", "the idea of neurons that fire either *fully* or *not at all*\n", "(like biological neurons) seemed appealing.\n", "Let's take a closer look at the sigmoid\n", "to see why it can cause vanishing gradients.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "80946a84", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:22.272259Z", "iopub.status.busy": "2023-08-18T19:43:22.271870Z", "iopub.status.idle": "2023-08-18T19:43:22.638228Z", "shell.execute_reply": "2023-08-18T19:43:22.637370Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"288.403125pt\" height=\"169.678125pt\" viewBox=\"0 0 288.**********.678125\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:43:22.583883</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 169.678125 \n", "L 288.**********.678125 \n", "L 288.403125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 281.**********.8 \n", "L 281.203125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 48.695149 145.8 \n", "L 48.695149 7.2 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m2530e70050\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m2530e70050\" x=\"48.695149\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −7.5 -->\n", "      <g transform=\"translate(36.553743 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 84.587088 145.8 \n", "L 84.587088 7.2 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m2530e70050\" x=\"84.587088\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −5.0 -->\n", "      <g transform=\"translate(72.445682 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 120.479027 145.8 \n", "L 120.479027 7.2 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m2530e70050\" x=\"120.479027\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- −2.5 -->\n", "      <g transform=\"translate(108.337621 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 156.370967 145.8 \n", "L 156.370967 7.2 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m2530e70050\" x=\"156.370967\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(148.419404 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 192.262906 145.8 \n", "L 192.262906 7.2 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m2530e70050\" x=\"192.262906\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(184.311343 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 228.154845 145.8 \n", "L 228.154845 7.2 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m2530e70050\" x=\"228.154845\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 5.0 -->\n", "      <g transform=\"translate(220.203282 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 264.046784 145.8 \n", "L 264.046784 7.2 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m2530e70050\" x=\"264.046784\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 7.5 -->\n", "      <g transform=\"translate(256.095221 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 139.54227 \n", "L 281.203125 139.54227 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <defs>\n", "       <path id=\"me74453679b\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me74453679b\" x=\"30.103125\" y=\"139.54227\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 143.341489) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 114.32447 \n", "L 281.203125 114.32447 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#me74453679b\" x=\"30.103125\" y=\"114.32447\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 118.123688) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 89.106669 \n", "L 281.203125 89.106669 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#me74453679b\" x=\"30.103125\" y=\"89.106669\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 92.905888) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 63.888869 \n", "L 281.203125 63.888869 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#me74453679b\" x=\"30.103125\" y=\"63.888869\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 67.688088) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 30.103125 38.671069 \n", "L 281.203125 38.671069 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#me74453679b\" x=\"30.103125\" y=\"38.671069\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.470288) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_25\">\n", "      <path d=\"M 30.103125 13.453269 \n", "L 281.203125 13.453269 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#me74453679b\" x=\"30.103125\" y=\"13.453269\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 17.252487) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 41.516761 139.499986 \n", "L 64.487601 139.333113 \n", "L 77.4087 139.02907 \n", "L 86.022764 138.610278 \n", "L 93.201152 138.013008 \n", "L 98.943864 137.274407 \n", "L 103.250896 136.500118 \n", "L 107.557928 135.470167 \n", "L 110.429284 134.603903 \n", "L 113.30064 133.562389 \n", "L 116.171995 132.314322 \n", "L 119.043348 130.824677 \n", "L 121.914704 129.055108 \n", "L 124.786059 126.964831 \n", "L 127.657415 124.512093 \n", "L 130.528769 121.656412 \n", "L 133.400125 118.361636 \n", "L 136.271481 114.599835 \n", "L 139.142835 110.355791 \n", "L 142.014191 105.631713 \n", "L 144.885546 100.451463 \n", "L 147.756901 94.863425 \n", "L 152.063934 85.88415 \n", "L 164.985032 58.132113 \n", "L 167.856387 52.544076 \n", "L 170.727742 47.363821 \n", "L 173.599098 42.639742 \n", "L 176.470452 38.3957 \n", "L 179.341808 34.633904 \n", "L 182.213164 31.339136 \n", "L 185.084518 28.483452 \n", "L 187.955874 26.030704 \n", "L 190.827229 23.940429 \n", "L 193.698585 22.170861 \n", "L 196.569941 20.681216 \n", "L 199.441293 19.433149 \n", "L 202.312649 18.391637 \n", "L 205.184005 17.525364 \n", "L 209.491037 16.495419 \n", "L 213.798069 15.721135 \n", "L 219.540781 14.982536 \n", "L 225.283492 14.482477 \n", "L 232.46188 14.079534 \n", "L 242.51162 13.765034 \n", "L 256.868396 13.568135 \n", "L 269.789489 13.5 \n", "L 269.789489 13.5 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 41.516761 139.5 \n", "L 64.487601 139.33346 \n", "L 77.4087 139.031159 \n", "L 86.022764 138.617167 \n", "L 93.201152 138.031555 \n", "L 98.943864 137.315197 \n", "L 103.250896 136.573516 \n", "L 107.557928 135.601678 \n", "L 111.86496 134.341223 \n", "L 114.736316 133.309241 \n", "L 117.607672 132.102046 \n", "L 120.479027 130.702963 \n", "L 123.35038 129.099634 \n", "L 126.221736 127.287088 \n", "L 129.093091 125.271485 \n", "L 133.400125 121.919593 \n", "L 144.885546 112.570609 \n", "L 147.756901 110.695092 \n", "L 150.628256 109.248032 \n", "L 152.063934 108.718765 \n", "L 153.499611 108.333151 \n", "L 154.935289 108.098692 \n", "L 156.370967 108.02002 \n", "L 157.806644 108.098694 \n", "L 159.242322 108.333152 \n", "L 160.677999 108.718765 \n", "L 162.113677 109.248032 \n", "L 164.985032 110.695092 \n", "L 167.856387 112.570609 \n", "L 170.727742 114.751668 \n", "L 176.470452 119.533852 \n", "L 180.777486 123.074328 \n", "L 185.084518 126.303728 \n", "L 187.955874 128.219439 \n", "L 190.827229 129.927355 \n", "L 193.698585 131.427398 \n", "L 196.569941 132.728658 \n", "L 199.441293 133.84599 \n", "L 203.748325 135.216679 \n", "L 208.055361 136.277873 \n", "L 212.362393 137.090267 \n", "L 216.669421 137.70695 \n", "L 222.412133 138.299941 \n", "L 229.590528 138.78283 \n", "L 238.204585 139.123188 \n", "L 251.125684 139.371202 \n", "L 269.789489 139.495556 \n", "L 269.789489 139.495556 \n", "\" clip-path=\"url(#p03b66e1db7)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 281.**********.8 \n", "L 281.203125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 281.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 281.203125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 37.103125 44.55625 \n", "L 111.228125 44.55625 \n", "Q 113.228125 44.55625 113.228125 42.55625 \n", "L 113.228125 14.2 \n", "Q 113.228125 12.2 111.228125 12.2 \n", "L 37.103125 12.2 \n", "Q 35.103125 12.2 35.103125 14.2 \n", "L 35.103125 42.55625 \n", "Q 35.103125 44.55625 37.103125 44.55625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_29\">\n", "     <path d=\"M 39.103125 20.298438 \n", "L 49.103125 20.298438 \n", "L 59.103125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- sigmoid -->\n", "     <g transform=\"translate(67.103125 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-73\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"52.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"79.882812\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"143.359375\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"240.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"301.953125\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"329.736328\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 39.103125 34.976563 \n", "L 49.103125 34.976563 \n", "L 59.103125 34.976563 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- gradient -->\n", "     <g transform=\"translate(67.103125 38.476563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-67\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"104.589844\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"165.869141\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"229.345703\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"257.128906\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"318.652344\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"382.03125\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p03b66e1db7\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"251.1\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 450x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x = torch.arange(-8.0, 8.0, 0.1, requires_grad=True)\n", "y = torch.sigmoid(x)\n", "y.backward(torch.ones_like(x))\n", "\n", "d2l.plot(x.detach().numpy(), [y.detach().numpy(), x.grad.numpy()],\n", "         legend=['sigmoid', 'gradient'], figsize=(4.5, 2.5))"]}, {"cell_type": "markdown", "id": "c4770b55", "metadata": {"origin_pos": 11}, "source": ["As you can see, (**the sigmoid's gradient vanishes\n", "both when its inputs are large and when they are small**).\n", "Moreover, when backpropagating through many layers,\n", "unless we are in the Goldilocks zone, where\n", "the inputs to many of the sigmoids are close to zero,\n", "the gradients of the overall product may vanish.\n", "When our network boasts many layers,\n", "unless we are careful, the gradient\n", "will likely be cut off at some layer.\n", "Indeed, this problem used to plague deep network training.\n", "Consequently, ReLUs, which are more stable\n", "(but less neurally plausible),\n", "have emerged as the default choice for practitioners.\n", "\n", "\n", "### [**Exploding Gradients**]\n", "\n", "The opposite problem, when gradients explode,\n", "can be similarly vexing.\n", "To illustrate this a bit better,\n", "we draw 100 Gaussian random matrices\n", "and multiply them with some initial matrix.\n", "For the scale that we picked\n", "(the choice of the variance $\\sigma^2=1$),\n", "the matrix product explodes.\n", "When this happens because of the initialization\n", "of a deep network, we have no chance of getting\n", "a gradient descent optimizer to converge.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "debf7ebc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:22.642610Z", "iopub.status.busy": "2023-08-18T19:43:22.642013Z", "iopub.status.idle": "2023-08-18T19:43:22.655626Z", "shell.execute_reply": "2023-08-18T19:43:22.654378Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a single matrix \n", " tensor([[-0.8755, -1.2171,  1.3316,  0.1357],\n", "        [ 0.4399,  1.4073, -1.9131, -0.4608],\n", "        [-2.1420,  0.3643, -0.5267,  1.0277],\n", "        [-0.1734, -0.7549,  2.3024,  1.3085]])\n", "after multiplying 100 matrices\n", " tensor([[-2.9185e+23,  1.3915e+25, -1.1865e+25,  1.4354e+24],\n", "        [ 4.9142e+23, -2.3430e+25,  1.9979e+25, -2.4169e+24],\n", "        [ 2.6578e+23, -1.2672e+25,  1.0805e+25, -1.3072e+24],\n", "        [-5.2223e+23,  2.4899e+25, -2.1231e+25,  2.5684e+24]])\n"]}], "source": ["M = torch.normal(0, 1, size=(4, 4))\n", "print('a single matrix \\n',M)\n", "for i in range(100):\n", "    M = M @ torch.normal(0, 1, size=(4, 4))\n", "print('after multiplying 100 matrices\\n', M)"]}, {"cell_type": "markdown", "id": "c356bb9b", "metadata": {"origin_pos": 16}, "source": ["### Breaking the Symmetry\n", "\n", "Another problem in neural network design\n", "is the symmetry inherent in their parametrization.\n", "Assume that we have a simple MLP\n", "with one hidden layer and two units.\n", "In this case, we could permute the weights $\\mathbf{W}^{(1)}$\n", "of the first layer and likewise permute\n", "the weights of the output layer\n", "to obtain the same function.\n", "There is nothing special differentiating\n", "the first and second hidden units.\n", "In other words, we have permutation symmetry\n", "among the hidden units of each layer.\n", "\n", "This is more than just a theoretical nuisance.\n", "Consider the aforementioned one-hidden-layer MLP\n", "with two hidden units.\n", "For illustration,\n", "suppose that the output layer transforms the two hidden units into only one output unit.\n", "Imagine what would happen if we initialized\n", "all the parameters of the hidden layer\n", "as $\\mathbf{W}^{(1)} = c$ for some constant $c$.\n", "In this case, during forward propagation\n", "either hidden unit takes the same inputs and parameters\n", "producing the same activation\n", "which is fed to the output unit.\n", "During backpropagation,\n", "differentiating the output unit with respect to parameters $\\mathbf{W}^{(1)}$ gives a gradient all of whose elements take the same value.\n", "Thus, after gradient-based iteration (e.g., minibatch stochastic gradient descent),\n", "all the elements of $\\mathbf{W}^{(1)}$ still take the same value.\n", "Such iterations would\n", "never *break the symmetry* on their own\n", "and we might never be able to realize\n", "the network's expressive power.\n", "The hidden layer would behave\n", "as if it had only a single unit.\n", "Note that while minibatch stochastic gradient descent would not break this symmetry,\n", "dropout regularization (to be introduced later) would!\n", "\n", "\n", "## Parameter Initialization\n", "\n", "One way of addressing---or at least mitigating---the\n", "issues raised above is through careful initialization.\n", "As we will see later,\n", "additional care during optimization\n", "and suitable regularization can further enhance stability.\n", "\n", "\n", "### Default Initialization\n", "\n", "In the previous sections, e.g., in :numref:`sec_linear_concise`,\n", "we used a normal distribution\n", "to initialize the values of our weights.\n", "If we do not specify the initialization method, the framework will\n", "use a default random initialization method, which often works well in practice\n", "for moderate problem sizes.\n", "\n", "\n", "\n", "\n", "\n", "\n", "### Xavier Initialization\n", ":label:`subsec_xavier`\n", "\n", "Let's look at the scale distribution of\n", "an output $o_{i}$ for some fully connected layer\n", "*without nonlinearities*.\n", "With $n_\\textrm{in}$ inputs $x_j$\n", "and their associated weights $w_{ij}$ for this layer,\n", "an output is given by\n", "\n", "$$o_{i} = \\sum_{j=1}^{n_\\textrm{in}} w_{ij} x_j.$$\n", "\n", "The weights $w_{ij}$ are all drawn\n", "independently from the same distribution.\n", "Furthermore, let's assume that this distribution\n", "has zero mean and variance $\\sigma^2$.\n", "Note that this does not mean that the distribution has to be Gaussian,\n", "just that the mean and variance need to exist.\n", "For now, let's assume that the inputs to the layer $x_j$\n", "also have zero mean and variance $\\gamma^2$\n", "and that they are independent of $w_{ij}$ and independent of each other.\n", "In this case, we can compute the mean of $o_i$:\n", "\n", "$$\n", "\\begin{aligned}\n", "    E[o_i] & = \\sum_{j=1}^{n_\\textrm{in}} E[w_{ij} x_j] \\\\&= \\sum_{j=1}^{n_\\textrm{in}} E[w_{ij}] E[x_j] \\\\&= 0, \\end{aligned}$$\n", "\n", "and the variance:\n", "\n", "$$\n", "\\begin{aligned}\n", "    \\textrm{Var}[o_i] & = E[o_i^2] - (E[o_i])^2 \\\\\n", "        & = \\sum_{j=1}^{n_\\textrm{in}} E[w^2_{ij} x^2_j] - 0 \\\\\n", "        & = \\sum_{j=1}^{n_\\textrm{in}} E[w^2_{ij}] E[x^2_j] \\\\\n", "        & = n_\\textrm{in} \\sigma^2 \\gamma^2.\n", "\\end{aligned}\n", "$$\n", "\n", "One way to keep the variance fixed\n", "is to set $n_\\textrm{in} \\sigma^2 = 1$.\n", "Now consider backpropagation.\n", "There we face a similar problem,\n", "albeit with gradients being propagated from the layers closer to the output.\n", "Using the same reasoning as for forward propagation,\n", "we see that the gradients' variance can blow up\n", "unless $n_\\textrm{out} \\sigma^2 = 1$,\n", "where $n_\\textrm{out}$ is the number of outputs of this layer.\n", "This leaves us in a dilemma:\n", "we cannot possibly satisfy both conditions simultaneously.\n", "Instead, we simply try to satisfy:\n", "\n", "$$\n", "\\begin{aligned}\n", "\\frac{1}{2} (n_\\textrm{in} + n_\\textrm{out}) \\sigma^2 = 1 \\textrm{ or equivalently }\n", "\\sigma = \\sqrt{\\frac{2}{n_\\textrm{in} + n_\\textrm{out}}}.\n", "\\end{aligned}\n", "$$\n", "\n", "This is the reasoning underlying the now-standard\n", "and practically beneficial *Xavier initialization*,\n", "named after the first author of its creators :cite:`Glorot.Bengio.2010`.\n", "Typically, the Xavier initialization\n", "samples weights from a Gaussian distribution\n", "with zero mean and variance\n", "$\\sigma^2 = \\frac{2}{n_\\textrm{in} + n_\\textrm{out}}$.\n", "We can also adapt this to\n", "choose the variance when sampling weights\n", "from a uniform distribution.\n", "Note that the uniform distribution $U(-a, a)$ has variance $\\frac{a^2}{3}$.\n", "Plugging $\\frac{a^2}{3}$ into our condition on $\\sigma^2$\n", "prompts us to initialize according to\n", "\n", "$$U\\left(-\\sqrt{\\frac{6}{n_\\textrm{in} + n_\\textrm{out}}}, \\sqrt{\\frac{6}{n_\\textrm{in} + n_\\textrm{out}}}\\right).$$\n", "\n", "Though the assumption for nonexistence of nonlinearities\n", "in the above mathematical reasoning\n", "can be easily violated in neural networks,\n", "the Xavier initialization method\n", "turns out to work well in practice.\n", "\n", "\n", "### Beyond\n", "\n", "The reasoning above barely scratches the surface\n", "of modern approaches to parameter initialization.\n", "A deep learning framework often implements over a dozen different heuristics.\n", "Moreover, parameter initialization continues to be\n", "a hot area of fundamental research in deep learning.\n", "Among these are heuristics specialized for\n", "tied (shared) parameters, super-resolution,\n", "sequence models, and other situations.\n", "For instance,\n", ":citet:`<PERSON><PERSON><PERSON>.Sohl-Dickstein.ea.2018` demonstrated the possibility of training\n", "10,000-layer neural networks without architectural tricks\n", "by using a carefully-designed initialization method.\n", "\n", "If the topic interests you we suggest\n", "a deep dive into this module's offerings,\n", "reading the papers that proposed and analyzed each heuristic,\n", "and then exploring the latest publications on the topic.\n", "Perhaps you will stumble across or even invent\n", "a clever idea and contribute an implementation to deep learning frameworks.\n", "\n", "\n", "## Summary\n", "\n", "Vanishing and exploding gradients are common issues in deep networks. Great care in parameter initialization is required to ensure that gradients and parameters remain well controlled.\n", "Initialization heuristics are needed to ensure that the initial gradients are neither too large nor too small.\n", "Random initialization is key to ensuring that symmetry is broken before optimization.\n", "Xavier initialization suggests that, for each layer, variance of any output is not affected by the number of inputs, and variance of any gradient is not affected by the number of outputs.\n", "ReLU activation functions mitigate the vanishing gradient problem. This can accelerate convergence.\n", "\n", "## Exercises\n", "\n", "1. Can you design other cases where a neural network might exhibit symmetry that needs breaking, besides the permutation symmetry in an MLP's layers?\n", "1. Can we initialize all weight parameters in linear regression or in softmax regression to the same value?\n", "1. Look up analytic bounds on the eigenvalues of the product of two matrices. What does this tell you about ensuring that gradients are well conditioned?\n", "1. If we know that some terms diverge, can we fix this after the fact? Look at the paper on layerwise adaptive rate scaling  for inspiration :cite:`<PERSON><PERSON>Gitman.Ginsburg.2017`.\n"]}, {"cell_type": "markdown", "id": "703c0185", "metadata": {"origin_pos": 18, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/104)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
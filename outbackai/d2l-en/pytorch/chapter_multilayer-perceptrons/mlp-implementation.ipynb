{"cells": [{"cell_type": "markdown", "id": "3333804c", "metadata": {"origin_pos": 1}, "source": ["# Implementation of Multilayer Perceptrons\n", ":label:`sec_mlp-implementation`\n", "\n", "Multilayer perceptrons (MLPs) are not much more complex to implement than simple linear models. The key conceptual\n", "difference is that we now concatenate multiple layers.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "87926c3b", "metadata": {"origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "1f2763e5", "metadata": {"origin_pos": 6}, "source": ["## Implementation from Scratch\n", "\n", "Let's begin again by implementing such a network from scratch.\n", "\n", "### Initializing Model Parameters\n", "\n", "Recall that Fashion-MNIST contains 10 classes,\n", "and that each image consists of a $28 \\times 28 = 784$\n", "grid of grayscale pixel values.\n", "As before we will disregard the spatial structure\n", "among the pixels for now,\n", "so we can think of this as a classification dataset\n", "with 784 input features and 10 classes.\n", "To begin, we will [**implement an MLP\n", "with one hidden layer and 256 hidden units.**]\n", "Both the number of layers and their width are adjustable\n", "(they are considered hyperparameters).\n", "Typically, we choose the layer widths to be divisible by larger powers of 2.\n", "This is computationally efficient due to the way\n", "memory is allocated and addressed in hardware.\n", "\n", "Again, we will represent our parameters with several tensors.\n", "Note that *for every layer*, we must keep track of\n", "one weight matrix and one bias vector.\n", "As always, we allocate memory\n", "for the gradients of the loss with respect to these parameters.\n"]}, {"cell_type": "markdown", "id": "08aa4fb7", "metadata": {"origin_pos": 8, "tab": ["pytorch"]}, "source": ["In the code below we use `nn.Parameter`\n", "to automatically register\n", "a class attribute as a parameter to be tracked by `autograd` (:numref:`sec_autograd`).\n"]}, {"cell_type": "code", "execution_count": 7, "id": "bcccd30d", "metadata": {"origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["class MLPScratch(d2l.Classifier):\n", "    \"\"\"\n", "    自定义的多层感知机分类器类，继承自 d2l.Classifier。\n", "    该类用于从零开始实现一个简单的多层感知机。\n", "    \"\"\"\n", "    def __init__(self, num_inputs, num_outputs, num_hiddens, lr, sigma=0.01):\n", "        \"\"\"\n", "        初始化多层感知机的参数。\n", "\n", "        参数:\n", "        num_inputs (int): 输入层的神经元数量，即输入特征的维度。\n", "        num_outputs (int): 输出层的神经元数量，通常对应分类的类别数。\n", "        num_hiddens (int): 隐藏层的神经元数量。\n", "        lr (float): 学习率，用于控制模型参数更新的步长。\n", "        sigma (float, 可选): 权重初始化的缩放因子，默认为 0.01。\n", "        \"\"\"\n", "        super().__init__()\n", "        # 保存传入的超参数，方便后续使用\n", "        self.save_hyperparameters()\n", "        # 定义第一个隐藏层的权重矩阵，使用随机正态分布初始化并乘以缩放因子 sigma\n", "        self.W1 = nn.Parameter(torch.randn(num_inputs, num_hiddens) * sigma)\n", "        # 定义第一个隐藏层的偏置向量，初始化为全零\n", "        self.b1 = nn.Parameter(torch.zeros(num_hiddens))\n", "        # 定义输出层的权重矩阵，使用随机正态分布初始化并乘以缩放因子 sigma\n", "        self.W2 = nn.Parameter(torch.randn(num_hiddens, num_outputs) * sigma)\n", "        # 定义输出层的偏置向量，初始化为全零\n", "        self.b2 = nn.Parameter(torch.zeros(num_outputs))"]}, {"cell_type": "markdown", "id": "49aeeb41", "metadata": {"origin_pos": 15}, "source": ["### Model\n", "\n", "To make sure we know how everything works,\n", "we will [**implement the ReLU activation**] ourselves\n", "rather than invoking the built-in `relu` function directly.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "2af157bb", "metadata": {"origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["def relu(X):\n", "    a = torch.zeros_like(X)\n", "    return torch.max(X, a)"]}, {"cell_type": "markdown", "id": "3f8bd74c", "metadata": {"origin_pos": 20}, "source": ["Since we are disregarding spatial structure,\n", "we `reshape` each two-dimensional image into\n", "a flat vector of length  `num_inputs`.\n", "Finally, we (**implement our model**)\n", "with just a few lines of code. Since we use the framework built-in autograd this is all that it takes.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "7438b40c", "metadata": {"origin_pos": 21, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(MLPScratch)\n", "# 使用 d2l 库中的 add_to_class 装饰器，将下面定义的 forward 方法添加到 MLPScratch 类中\n", "def forward(self, X):\n", "    \"\"\"\n", "    定义多层感知机的前向传播过程。\n", "\n", "    参数:\n", "    X (torch.Tensor): 输入数据，形状为 (batch_size, num_features)\n", "\n", "    返回:\n", "    torch.Tensor: 前向传播的输出，形状为 (batch_size, num_outputs)\n", "    \"\"\"\n", "    # 调整输入数据 X 的形状，确保其每个样本是一个一维向量\n", "    # -1 表示自动计算该维度的大小，self.num_inputs 表示输入特征的数量\n", "    X = X.reshape((-1, self.num_inputs))\n", "    # 计算第一个隐藏层的输出 H\n", "    # 先进行矩阵乘法 X * self.W1，然后加上偏置 self.b1\n", "    # 接着使用 relu 激活函数对结果进行非线性变换\n", "    H = relu(torch.matmul(X, self.W1) + self.b1)\n", "    # 计算最终输出\n", "    # 对隐藏层的输出 H 进行矩阵乘法 H * self.W2，然后加上输出层的偏置 self.b2\n", "    return torch.mat<PERSON>l(H, self.W2) + self.b2"]}, {"cell_type": "markdown", "id": "f07056fc", "metadata": {"origin_pos": 22}, "source": ["### Training\n", "\n", "Fortunately, [**the training loop for MLPs\n", "is exactly the same as for softmax regression.**] We define the model, data, and trainer, then finally invoke the `fit` method on model and data.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "82d57362", "metadata": {"origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"186.109831pt\" viewBox=\"0 0 238.**********.109831\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-29T12:11:27.165547</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 186.109831 \n", "L 238.**********.109831 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.553581 \n", "L 225.**********.553581 \n", "L 225.403125 9.953581 \n", "L 30.103125 9.953581 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mf11d03bb37\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf11d03bb37\" x=\"30.103125\" y=\"148.553581\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 163.152019) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mf11d03bb37\" x=\"69.163125\" y=\"148.553581\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(65.981875 163.152019) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mf11d03bb37\" x=\"108.223125\" y=\"148.553581\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(105.041875 163.152019) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mf11d03bb37\" x=\"147.283125\" y=\"148.553581\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(144.101875 163.152019) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mf11d03bb37\" x=\"186.343125\" y=\"148.553581\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(183.161875 163.152019) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mf11d03bb37\" x=\"225.403125\" y=\"148.553581\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 163.152019) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 176.830144) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m252d78cc19\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m252d78cc19\" x=\"30.103125\" y=\"139.219587\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 143.018806) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m252d78cc19\" x=\"30.103125\" y=\"113.575514\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 117.374732) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m252d78cc19\" x=\"30.103125\" y=\"87.93144\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 91.730659) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m252d78cc19\" x=\"30.103125\" y=\"62.287366\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 66.086585) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m252d78cc19\" x=\"30.103125\" y=\"36.643292\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.2 -->\n", "      <g transform=\"translate(7.2 40.442511) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m252d78cc19\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.4 -->\n", "      <g transform=\"translate(7.2 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 34.923295 16.253581 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 49.633125 105.813655 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_17\"/>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 49.633125 105.813655 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 49.633125 92.025977 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 49.633125 105.813655 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 49.633125 92.025977 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 49.633125 105.813655 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 49.633125 92.025977 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 49.633125 92.025977 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "L 171.051551 137.978623 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "L 171.051551 137.978623 \n", "L 180.774997 139.108943 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "L 171.051551 137.978623 \n", "L 180.774997 139.108943 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "L 186.343125 131.475115 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "L 171.051551 137.978623 \n", "L 180.774997 139.108943 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "L 186.343125 131.475115 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "L 186.343125 83.298477 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "L 171.051551 137.978623 \n", "L 180.774997 139.108943 \n", "L 190.498444 139.890344 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "L 186.343125 131.475115 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "L 186.343125 83.298477 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "L 171.051551 137.978623 \n", "L 180.774997 139.108943 \n", "L 190.498444 139.890344 \n", "L 200.221891 141.054564 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "L 186.343125 131.475115 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "L 186.343125 83.298477 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "L 171.051551 137.978623 \n", "L 180.774997 139.108943 \n", "L 190.498444 139.890344 \n", "L 200.221891 141.054564 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "L 186.343125 131.475115 \n", "L 205.873125 137.28931 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "L 186.343125 83.298477 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "L 171.051551 137.978623 \n", "L 180.774997 139.108943 \n", "L 190.498444 139.890344 \n", "L 200.221891 141.054564 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "L 186.343125 131.475115 \n", "L 205.873125 137.28931 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "L 186.343125 83.298477 \n", "L 205.873125 81.395206 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "L 171.051551 137.978623 \n", "L 180.774997 139.108943 \n", "L 190.498444 139.890344 \n", "L 200.221891 141.054564 \n", "L 209.945338 140.769365 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "L 186.343125 131.475115 \n", "L 205.873125 137.28931 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "L 186.343125 83.298477 \n", "L 205.873125 81.395206 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "L 171.051551 137.978623 \n", "L 180.774997 139.108943 \n", "L 190.498444 139.890344 \n", "L 200.221891 141.054564 \n", "L 209.945338 140.769365 \n", "L 219.668785 142.253581 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "L 186.343125 131.475115 \n", "L 205.873125 137.28931 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "L 186.343125 83.298477 \n", "L 205.873125 81.395206 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "L 171.051551 137.978623 \n", "L 180.774997 139.108943 \n", "L 190.498444 139.890344 \n", "L 200.221891 141.054564 \n", "L 209.945338 140.769365 \n", "L 219.668785 142.253581 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "L 186.343125 131.475115 \n", "L 205.873125 137.28931 \n", "L 225.403125 133.477164 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "L 186.343125 83.298477 \n", "L 205.873125 81.395206 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 34.923295 16.253581 \n", "L 44.646742 94.920582 \n", "L 54.370189 110.40512 \n", "L 64.093636 116.582953 \n", "L 73.817082 123.544576 \n", "L 83.540529 123.963499 \n", "L 93.263976 127.910373 \n", "L 102.987423 129.784541 \n", "L 112.71087 131.774761 \n", "L 122.434316 132.419077 \n", "L 132.157763 134.173247 \n", "L 141.88121 135.681154 \n", "L 151.604657 136.19896 \n", "L 161.328104 137.509147 \n", "L 171.051551 137.978623 \n", "L 180.774997 139.108943 \n", "L 190.498444 139.890344 \n", "L 200.221891 141.054564 \n", "L 209.945338 140.769365 \n", "L 219.668785 142.253581 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_127\">\n", "    <path d=\"M 49.633125 105.813655 \n", "L 69.163125 115.837042 \n", "L 88.693125 119.197019 \n", "L 108.223125 125.345692 \n", "L 127.753125 130.018206 \n", "L 147.283125 131.182257 \n", "L 166.813125 133.865245 \n", "L 186.343125 131.475115 \n", "L 205.873125 137.28931 \n", "L 225.403125 133.477164 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_128\">\n", "    <path d=\"M 49.633125 92.025977 \n", "L 69.163125 89.834711 \n", "L 88.693125 87.93144 \n", "L 108.223125 85.940518 \n", "L 127.753125 84.325242 \n", "L 147.283125 83.098133 \n", "L 166.813125 82.897789 \n", "L 186.343125 83.298477 \n", "L 205.873125 81.395206 \n", "L 225.403125 82.522143 \n", "\" clip-path=\"url(#pdbd53c5d96)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.553581 \n", "L 30.103125 9.953581 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.553581 \n", "L 225.403125 9.953581 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.553581 \n", "L 225.**********.553581 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 9.953581 \n", "L 225.403125 9.953581 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 62.822331 \n", "L 218.403125 62.822331 \n", "Q 220.403125 62.822331 220.403125 60.822331 \n", "L 220.403125 16.953581 \n", "Q 220.403125 14.953581 218.403125 14.953581 \n", "L 138.8125 14.953581 \n", "Q 136.8125 14.953581 136.8125 16.953581 \n", "L 136.8125 60.822331 \n", "Q 136.8125 62.822331 138.8125 62.822331 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_129\">\n", "     <path d=\"M 140.8125 23.052019 \n", "L 150.8125 23.052019 \n", "L 160.8125 23.052019 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 26.552019) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_130\">\n", "     <path d=\"M 140.8125 38.008269 \n", "L 150.8125 38.008269 \n", "L 160.8125 38.008269 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 41.508269) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_131\">\n", "     <path d=\"M 140.8125 52.964519 \n", "L 150.8125 52.964519 \n", "L 160.8125 52.964519 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 56.464519) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pdbd53c5d96\">\n", "   <rect x=\"30.103125\" y=\"9.953581\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = MLPScratch(num_inputs=784, num_outputs=10, num_hiddens=256, lr=0.1)\n", "data = d2l.FashionMNIST(batch_size=256)\n", "trainer = d2l.Trainer(max_epochs=10)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "0c068a33", "metadata": {"origin_pos": 24}, "source": ["## Concise Implementation\n", "\n", "As you might expect, by relying on the high-level APIs, we can implement MLPs even more concisely.\n", "\n", "### Model\n", "\n", "Compared with our concise implementation\n", "of softmax regression implementation\n", "(:numref:`sec_softmax_concise`),\n", "the only difference is that we add\n", "*two* fully connected layers where we previously added only *one*.\n", "The first is [**the hidden layer**],\n", "the second is the output layer.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "a5087507", "metadata": {"origin_pos": 26, "tab": ["pytorch"]}, "outputs": [], "source": ["class MLP(d2l.Classifier):\n", "    def __init__(self, num_outputs, num_hiddens, lr):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        self.net = nn.Se<PERSON>(nn.<PERSON>(), nn.<PERSON><PERSON><PERSON><PERSON>(num_hiddens),\n", "                                 nn.<PERSON><PERSON><PERSON>(), nn.<PERSON><PERSON><PERSON>inear(num_outputs))"]}, {"cell_type": "markdown", "id": "c5fb2be8", "metadata": {"origin_pos": 29}, "source": ["Previously, we defined `forward` methods for models to transform input using the model parameters.\n", "These operations are essentially a pipeline:\n", "you take an input and\n", "apply a transformation (e.g.,\n", "matrix multiplication with weights followed by bias addition),\n", "then repetitively use the output of the current transformation as\n", "input to the next transformation.\n", "However, you may have noticed that \n", "no `forward` method is defined here.\n", "In fact, `MLP` inherits the `forward` method from the `Module` class (:numref:`subsec_oo-design-models`) to \n", "simply invoke `self.net(X)` (`X` is input),\n", "which is now defined as a sequence of transformations\n", "via the `Sequential` class.\n", "The `Sequential` class abstracts the forward process\n", "enabling us to focus on the transformations.\n", "We will further discuss how the `Sequential` class works in :numref:`subsec_model-construction-sequential`.\n", "\n", "\n", "### Training\n", "\n", "[**The training loop**] is exactly the same\n", "as when we implemented softmax regression.\n", "This modularity enables us to separate\n", "matters concerning the model architecture\n", "from orthogonal considerations.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "baf391c1", "metadata": {"origin_pos": 30, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-29T12:12:15.274260</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"md1e30b7e19\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md1e30b7e19\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#md1e30b7e19\" x=\"69.163125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(65.981875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#md1e30b7e19\" x=\"108.223125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(105.041875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#md1e30b7e19\" x=\"147.283125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(144.101875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#md1e30b7e19\" x=\"186.343125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(183.161875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#md1e30b7e19\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m8620ba94d3\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8620ba94d3\" x=\"30.103125\" y=\"133.888223\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 137.687442) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m8620ba94d3\" x=\"30.103125\" y=\"97.594914\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 101.394133) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m8620ba94d3\" x=\"30.103125\" y=\"61.301604\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 65.100823) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m8620ba94d3\" x=\"30.103125\" y=\"25.008295\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 28.807514) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 34.923295 13.5 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 49.633125 92.589074 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_15\"/>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 49.633125 92.589074 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 49.633125 64.969922 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 49.633125 92.589074 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 49.633125 64.969922 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 49.633125 92.589074 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 49.633125 64.969922 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 49.633125 64.969922 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "L 171.051551 136.323266 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "L 171.051551 136.323266 \n", "L 180.774997 134.396479 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "L 171.051551 136.323266 \n", "L 180.774997 134.396479 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "L 186.343125 131.024202 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "L 171.051551 136.323266 \n", "L 180.774997 134.396479 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "L 186.343125 131.024202 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "L 186.343125 52.40549 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "L 171.051551 136.323266 \n", "L 180.774997 134.396479 \n", "L 190.498444 136.907281 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "L 186.343125 131.024202 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "L 186.343125 52.40549 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "L 171.051551 136.323266 \n", "L 180.774997 134.396479 \n", "L 190.498444 136.907281 \n", "L 200.221891 137.954905 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "L 186.343125 131.024202 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "L 186.343125 52.40549 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "L 171.051551 136.323266 \n", "L 180.774997 134.396479 \n", "L 190.498444 136.907281 \n", "L 200.221891 137.954905 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "L 186.343125 131.024202 \n", "L 205.873125 131.376796 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "L 186.343125 52.40549 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "L 171.051551 136.323266 \n", "L 180.774997 134.396479 \n", "L 190.498444 136.907281 \n", "L 200.221891 137.954905 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "L 186.343125 131.024202 \n", "L 205.873125 131.376796 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "L 186.343125 52.40549 \n", "L 205.873125 52.086506 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "L 171.051551 136.323266 \n", "L 180.774997 134.396479 \n", "L 190.498444 136.907281 \n", "L 200.221891 137.954905 \n", "L 209.945338 139.032663 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "L 186.343125 131.024202 \n", "L 205.873125 131.376796 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "L 186.343125 52.40549 \n", "L 205.873125 52.086506 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "L 171.051551 136.323266 \n", "L 180.774997 134.396479 \n", "L 190.498444 136.907281 \n", "L 200.221891 137.954905 \n", "L 209.945338 139.032663 \n", "L 219.668785 139.5 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "L 186.343125 131.024202 \n", "L 205.873125 131.376796 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "L 186.343125 52.40549 \n", "L 205.873125 52.086506 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "L 171.051551 136.323266 \n", "L 180.774997 134.396479 \n", "L 190.498444 136.907281 \n", "L 200.221891 137.954905 \n", "L 209.945338 139.032663 \n", "L 219.668785 139.5 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "L 186.343125 131.024202 \n", "L 205.873125 131.376796 \n", "L 225.403125 121.055525 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "L 186.343125 52.40549 \n", "L 205.873125 52.086506 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 84.792528 \n", "L 54.370189 100.641811 \n", "L 64.093636 108.785654 \n", "L 73.817082 113.94532 \n", "L 83.540529 119.140475 \n", "L 93.263976 121.96001 \n", "L 102.987423 124.253586 \n", "L 112.71087 125.878876 \n", "L 122.434316 126.968194 \n", "L 132.157763 131.493445 \n", "L 141.88121 129.823343 \n", "L 151.604657 132.369124 \n", "L 161.328104 133.249852 \n", "L 171.051551 136.323266 \n", "L 180.774997 134.396479 \n", "L 190.498444 136.907281 \n", "L 200.221891 137.954905 \n", "L 209.945338 139.032663 \n", "L 219.668785 139.5 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 49.633125 92.589074 \n", "L 69.163125 104.194828 \n", "L 88.693125 116.562071 \n", "L 108.223125 105.057847 \n", "L 127.753125 121.311063 \n", "L 147.283125 123.883004 \n", "L 166.813125 124.155111 \n", "L 186.343125 131.024202 \n", "L 205.873125 131.376796 \n", "L 225.403125 121.055525 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 49.633125 64.969922 \n", "L 69.163125 61.177555 \n", "L 88.693125 56.818105 \n", "L 108.223125 62.861082 \n", "L 127.753125 55.17002 \n", "L 147.283125 54.301674 \n", "L 166.813125 54.24851 \n", "L 186.343125 52.40549 \n", "L 205.873125 52.086506 \n", "L 225.403125 56.215579 \n", "\" clip-path=\"url(#p1841cf5361)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 100.434375 \n", "L 218.403125 100.434375 \n", "Q 220.403125 100.434375 220.403125 98.434375 \n", "L 220.403125 54.565625 \n", "Q 220.403125 52.565625 218.403125 52.565625 \n", "L 138.8125 52.565625 \n", "Q 136.8125 52.565625 136.8125 54.565625 \n", "L 136.8125 98.434375 \n", "Q 136.8125 100.434375 138.8125 100.434375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_127\">\n", "     <path d=\"M 140.8125 60.664063 \n", "L 150.8125 60.664063 \n", "L 160.8125 60.664063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 64.164063) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_128\">\n", "     <path d=\"M 140.8125 75.620313 \n", "L 150.8125 75.620313 \n", "L 160.8125 75.620313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 79.120313) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_129\">\n", "     <path d=\"M 140.8125 90.576563 \n", "L 150.8125 90.576563 \n", "L 160.8125 90.576563 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 94.076563) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p1841cf5361\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = MLP(num_outputs=10, num_hiddens=256, lr=0.1)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "8b353853", "metadata": {"origin_pos": 31}, "source": ["## Summary\n", "\n", "Now that we have more practice in designing deep networks, the step from a single to multiple layers of deep networks does not pose such a significant challenge any longer. In particular, we can reuse the training algorithm and data loader. Note, though, that implementing MLPs from scratch is nonetheless messy: naming and keeping track of the model parameters makes it difficult to extend models. For instance, imagine wanting to insert another layer between layers 42 and 43. This might now be layer 42b, unless we are willing to perform sequential renaming. Moreover, if we implement the network from scratch, it is much more difficult for the framework to perform meaningful performance optimizations.\n", "\n", "Nonetheless, you have now reached the state of the art of the late 1980s when fully connected deep networks were the method of choice for neural network modeling. Our next conceptual step will be to consider images. Before we do so, we need to review a number of statistical basics and details on how to compute models efficiently.\n", "\n", "\n", "## Exercises\n", "\n", "1. Change the number of hidden units `num_hiddens` and plot how its number affects the accuracy of the model. What is the best value of this hyperparameter?\n", "1. Try adding a hidden layer to see how it affects the results.\n", "1. Why is it a bad idea to insert a hidden layer with a single neuron? What could go wrong?\n", "1. How does changing the learning rate alter your results? With all other parameters fixed, which learning rate gives you the best results? How does this relate to the number of epochs?\n", "1. Let's optimize over all hyperparameters jointly, i.e., learning rate, number of epochs, number of hidden layers, and number of hidden units per layer.\n", "    1. What is the best result you can get by optimizing over all of them?\n", "    1. Why it is much more challenging to deal with multiple hyperparameters?\n", "    1. Describe an efficient strategy for optimizing over multiple parameters jointly.\n", "1. Compare the speed of the framework and the from-scratch implementation for a challenging problem. How does it change with the complexity of the network?\n", "1. Measure the speed of tensor--matrix multiplications for well-aligned and misaligned matrices. For instance, test for matrices with dimension 1024, 1025, 1026, 1028, and 1032.\n", "    1. How does this change between GPUs and CPUs?\n", "    1. Determine the memory bus width of your CPU and GPU.\n", "1. Try out different activation functions. Which one works best?\n", "1. Is there a difference between weight initializations of the network? Does it matter?\n"]}, {"cell_type": "markdown", "id": "60c1e284", "metadata": {"origin_pos": 33, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/93)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "f4709271", "metadata": {"origin_pos": 1}, "source": ["# Dropout\n", ":label:`sec_dropout`\n", "\n", "\n", "Let's think briefly about what we\n", "expect from a good predictive model.\n", "We want it to peform well on unseen data.\n", "Classical generalization theory\n", "suggests that to close the gap between\n", "train and test performance,\n", "we should aim for a simple model.\n", "Simplicity can come in the form\n", "of a small number of dimensions.\n", "We explored this when discussing the\n", "monomial basis functions of linear models\n", "in :numref:`sec_generalization_basics`.\n", "Additionally, as we saw when discussing weight decay\n", "($\\ell_2$ regularization) in :numref:`sec_weight_decay`,\n", "the (inverse) norm of the parameters also\n", "represents a useful measure of simplicity.\n", "Another useful notion of simplicity is smoothness,\n", "i.e., that the function should not be sensitive\n", "to small changes to its inputs.\n", "For instance, when we classify images,\n", "we would expect that adding some random noise\n", "to the pixels should be mostly harmless.\n", "\n", ":citet:`<PERSON>.1995` formalized\n", "this idea when he proved that training with input noise\n", "is equivalent to <PERSON><PERSON><PERSON><PERSON> regularization.\n", "This work drew a clear mathematical connection\n", "between the requirement that a function be smooth (and thus simple),\n", "and the requirement that it be resilient\n", "to perturbations in the input.\n", "\n", "Then, :citet:`Srivastava.Hinton.Krizhevsky.ea.2014`\n", "developed a clever idea for how to apply <PERSON>'s idea\n", "to the internal layers of a network, too.\n", "Their idea, called *dropout*, involves\n", "injecting noise while computing\n", "each internal layer during forward propagation,\n", "and it has become a standard technique\n", "for training neural networks.\n", "The method is called *dropout* because we literally\n", "*drop out* some neurons during training.\n", "Throughout training, on each iteration,\n", "standard dropout consists of zeroing out\n", "some fraction of the nodes in each layer\n", "before calculating the subsequent layer.\n", "\n", "To be clear, we are imposing\n", "our own narrative with the link to <PERSON>.\n", "The original paper on dropout\n", "offers intuition through a surprising\n", "analogy to sexual reproduction.\n", "The authors argue that neural network overfitting\n", "is characterized by a state in which\n", "each layer relies on a specific\n", "pattern of activations in the previous layer,\n", "calling this condition *co-adaptation*.\n", "Dropout, they claim, breaks up co-adaptation\n", "just as sexual reproduction is argued to\n", "break up co-adapted genes.\n", "While such an justification of this theory is certainly up for debate,\n", "the dropout technique itself has proved enduring,\n", "and various forms of dropout are implemented\n", "in most deep learning libraries. \n", "\n", "\n", "The key challenge is how to inject this noise.\n", "One idea is to inject it in an *unbiased* manner\n", "so that the expected value of each layer---while fixing\n", "the others---equals the value it would have taken absent noise.\n", "In <PERSON>'s work, he added Gaussian noise\n", "to the inputs to a linear model.\n", "At each training iteration, he added noise\n", "sampled from a distribution with mean zero\n", "$\\epsilon \\sim \\mathcal{N}(0,\\sigma^2)$ to the input $\\mathbf{x}$,\n", "yielding a perturbed point $\\mathbf{x}' = \\mathbf{x} + \\epsilon$.\n", "In expectation, $E[\\mathbf{x}'] = \\mathbf{x}$.\n", "\n", "In standard dropout regularization,\n", "one zeros out some fraction of the nodes in each layer\n", "and then *debiases* each layer by normalizing\n", "by the fraction of nodes that were retained (not dropped out).\n", "In other words,\n", "with *dropout probability* $p$,\n", "each intermediate activation $h$ is replaced by\n", "a random variable $h'$ as follows:\n", "\n", "$$\n", "\\begin{aligned}\n", "h' =\n", "\\begin{cases}\n", "    0 & \\textrm{ with probability } p \\\\\n", "    \\frac{h}{1-p} & \\textrm{ otherwise}\n", "\\end{cases}\n", "\\end{aligned}\n", "$$\n", "\n", "By design, the expectation remains unchanged, i.e., $E[h'] = h$.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "54feb90c", "metadata": {"origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "4a048453", "metadata": {"origin_pos": 6}, "source": ["## Dropout in Practice\n", "\n", "Recall the MLP with a hidden layer and five hidden units\n", "from :numref:`fig_mlp`.\n", "When we apply dropout to a hidden layer,\n", "zeroing out each hidden unit with probability $p$,\n", "the result can be viewed as a network\n", "containing only a subset of the original neurons.\n", "In :numref:`fig_dropout2`, $h_2$ and $h_5$ are removed.\n", "Consequently, the calculation of the outputs\n", "no longer depends on $h_2$ or $h_5$\n", "and their respective gradient also vanishes\n", "when performing backpropagation.\n", "In this way, the calculation of the output layer\n", "cannot be overly dependent on any\n", "one element of $h_1, \\ldots, h_5$.\n", "\n", "![MLP before and after dropout.](../img/dropout2.svg)\n", ":label:`fig_dropout2`\n", "\n", "Typically, we disable dropout at test time.\n", "Given a trained model and a new example,\n", "we do not drop out any nodes\n", "and thus do not need to normalize.\n", "However, there are some exceptions:\n", "some researchers use dropout at test time as a heuristic\n", "for estimating the *uncertainty* of neural network predictions:\n", "if the predictions agree across many different dropout outputs,\n", "then we might say that the network is more confident.\n", "\n", "## Implementation from Scratch\n", "\n", "To implement the dropout function for a single layer,\n", "we must draw as many samples\n", "from a Bernoulli (binary) random variable\n", "as our layer has dimensions,\n", "where the random variable takes value $1$ (keep)\n", "with probability $1-p$ and $0$ (drop) with probability $p$.\n", "One easy way to implement this is to first draw samples\n", "from the uniform distribution $U[0, 1]$.\n", "Then we can keep those nodes for which the corresponding\n", "sample is greater than $p$, dropping the rest.\n", "\n", "In the following code, we (**implement a `dropout_layer` function\n", "that drops out the elements in the tensor input `X`\n", "with probability `dropout`**),\n", "rescaling the remainder as described above:\n", "dividing the survivors by `1.0-dropout`.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "dcda4b93", "metadata": {"origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["def dropout_layer(X, dropout):\n", "    \"\"\"\n", "    此函数用于实现 Dropout 正则化。\n", "\n", "    参数:\n", "    X (torch.Tensor): 输入张量，代表神经网络某一层的输出。\n", "    dropout (float): Dropout 概率，取值范围在 0 到 1 之间。\n", "\n", "    返回:\n", "    torch.Tensor: 经过 Dropout 处理后的张量。\n", "    \"\"\"\n", "    # 确保 dropout 概率在 0 到 1 之间\n", "    assert 0 <= dropout <= 1\n", "    # 如果 dropout 概率为 1，意味着所有神经元都被丢弃，返回全零张量\n", "    if dropout == 1: return torch.zeros_like(X)\n", "    # 生成一个与 X 形状相同的随机张量，元素取值范围在 [0, 1)\n", "    # 将随机张量中大于 dropout 概率的元素设为 1，小于等于的设为 0\n", "    # 这样就得到了一个掩码，用于决定哪些神经元的输出被保留\n", "    mask = (torch.rand(X.shape) > dropout).float()\n", "    # 将掩码与输入张量相乘，丢弃一部分神经元的输出\n", "    # 然后除以 (1.0 - dropout) 进行缩放，以保证期望输出不变\n", "    # 最后返回经过 Dropout 处理后的张量, 这里的dropout是指保留的概率\n", "    # 1-dropout是指丢弃的概率, 所以这里要除以1-dropout\n", "    return mask * X / (1.0 - dropout)"]}, {"cell_type": "markdown", "id": "7a0d5f19", "metadata": {"origin_pos": 11}, "source": ["We can [**test out the `dropout_layer` function on a few examples**].\n", "In the following lines of code,\n", "we pass our input `X` through the dropout operation,\n", "with probabilities 0, 0.5, and 1, respectively.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "1effb931", "metadata": {"origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dropout_p = 0: tensor([[ 0.,  1.,  2.,  3.,  4.,  5.,  6.,  7.],\n", "        [ 8.,  9., 10., 11., 12., 13., 14., 15.]])\n", "dropout_p = 0.5: tensor([[ 0.,  0.,  4.,  0.,  0., 10., 12., 14.],\n", "        [ 0., 18.,  0.,  0.,  0.,  0., 28.,  0.]])\n", "dropout_p = 1: tensor([[0., 0., 0., 0., 0., 0., 0., 0.],\n", "        [0., 0., 0., 0., 0., 0., 0., 0.]])\n"]}], "source": ["X = torch.arange(16, dtype=torch.float32).reshape((2, 8))\n", "print('dropout_p = 0:', dropout_layer(X, 0))\n", "print('dropout_p = 0.5:', dropout_layer(X, 0.5))\n", "print('dropout_p = 1:', dropout_layer(X, 1))"]}, {"cell_type": "markdown", "id": "087cdc90", "metadata": {"origin_pos": 13}, "source": ["### Defining the Model\n", "\n", "The model below applies dropout to the output\n", "of each hidden layer (following the activation function).\n", "We can set dropout probabilities for each layer separately.\n", "A common choice is to set\n", "a lower dropout probability closer to the input layer.\n", "We ensure that dropout is only active during training.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a98d0264", "metadata": {"origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["# 定义一个名为 DropoutMLPScratch 的类，继承自 d2l.Classifier\n", "class DropoutMLPScratch(d2l.Classifier):\n", "    # 类的构造函数，初始化类的实例\n", "    def __init__(self, num_outputs, num_hiddens_1, num_hiddens_2,\n", "                 dropout_1, dropout_2, lr):\n", "        # 调用父类的构造函数，确保父类的初始化逻辑被执行\n", "        super().__init__()\n", "        # save_hyperparameters 方法会将传入的所有参数保存为类的属性，方便后续使用\n", "        # 因此后续可以直接通过 self.dropout_1 等方式访问这些参数\n", "        self.save_hyperparameters()\n", "        # 定义第一个线性层，使用 nn.LazyLinear 可以在第一次前向传播时自动确定输入维度\n", "        self.lin1 = nn.LazyLinear(num_hiddens_1)\n", "        # 定义第二个线性层\n", "        self.lin2 = nn.LazyLinear(num_hiddens_2)\n", "        # 定义输出层，输出维度为 num_outputs\n", "        self.lin3 = nn.LazyLinear(num_outputs)\n", "        # 定义 ReLU 激活函数，用于引入非线性\n", "        self.relu = nn.ReLU()\n", "\n", "    # 定义前向传播方法，用于计算模型的输出\n", "    def forward(self, X):\n", "        # 这里存在错误，X.reshape((X.shape[0], 0)) 会导致错误，可能是想将输入 X 展平\n", "        # 正确的做法可能是 X.reshape((X.shape[0], -1))\n", "        xreash = X.reshape((X.shape[0], -1))\n", "        # 将输入通过第一个线性层\n", "        lin1 = self.lin1(xreash)\n", "        # 对第一个线性层的输出应用 ReLU 激活函数\n", "        H1 = self.relu(lin1)\n", "        # 如果模型处于训练模式\n", "        if self.training:\n", "            # 对 H1 应用 Dropout 正则化，丢弃概率为 self.dropout_1\n", "            H1 = dropout_layer(H1, self.dropout_1)\n", "        # 将 H1 通过第二个线性层并应用 ReLU 激活函数\n", "        H2 = self.relu(self.lin2(H1))\n", "        # 如果模型处于训练模式\n", "        if self.training:\n", "            # 对 H2 应用 Dropout 正则化，丢弃概率为 self.dropout_2\n", "            H2 = dropout_layer(H2, self.dropout_2)\n", "        # 将 H2 通过输出层得到最终输出\n", "        return self.lin3(H2)"]}, {"cell_type": "markdown", "id": "2793a6d2", "metadata": {"origin_pos": 18}, "source": ["### [**Training**]\n", "\n", "The following is similar to the training of MLPs described previously.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "12f6e01f", "metadata": {"origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-23T09:54:08.463307</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"me0f012d176\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me0f012d176\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#me0f012d176\" x=\"69.163125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(65.981875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#me0f012d176\" x=\"108.223125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(105.041875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#me0f012d176\" x=\"147.283125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(144.101875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#me0f012d176\" x=\"186.343125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(183.161875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#me0f012d176\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m695aa808bd\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m695aa808bd\" x=\"30.103125\" y=\"140.608358\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 144.407577) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m695aa808bd\" x=\"30.103125\" y=\"117.435435\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 121.234653) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m695aa808bd\" x=\"30.103125\" y=\"94.262511\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 98.06173) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m695aa808bd\" x=\"30.103125\" y=\"71.089588\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 74.888806) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m695aa808bd\" x=\"30.103125\" y=\"47.916664\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.2 -->\n", "      <g transform=\"translate(7.2 51.715883) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m695aa808bd\" x=\"30.103125\" y=\"24.74374\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.4 -->\n", "      <g transform=\"translate(7.2 28.542959) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 34.923295 13.5 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 49.633125 104.850182 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_17\"/>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 49.633125 104.850182 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 49.633125 102.397927 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 49.633125 104.850182 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 49.633125 102.397927 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 49.633125 104.850182 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 49.633125 102.397927 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 49.633125 102.397927 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "L 171.051551 134.749964 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "L 171.051551 134.749964 \n", "L 180.774997 136.881594 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "L 171.051551 134.749964 \n", "L 180.774997 136.881594 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "L 186.343125 138.329696 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "L 171.051551 134.749964 \n", "L 180.774997 136.881594 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "L 186.343125 138.329696 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "L 186.343125 88.763468 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "L 171.051551 134.749964 \n", "L 180.774997 136.881594 \n", "L 190.498444 135.860221 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "L 186.343125 138.329696 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "L 186.343125 88.763468 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "L 171.051551 134.749964 \n", "L 180.774997 136.881594 \n", "L 190.498444 135.860221 \n", "L 200.221891 137.266581 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "L 186.343125 138.329696 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "L 186.343125 88.763468 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "L 171.051551 134.749964 \n", "L 180.774997 136.881594 \n", "L 190.498444 135.860221 \n", "L 200.221891 137.266581 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "L 186.343125 138.329696 \n", "L 205.873125 139.5 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "L 186.343125 88.763468 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "L 171.051551 134.749964 \n", "L 180.774997 136.881594 \n", "L 190.498444 135.860221 \n", "L 200.221891 137.266581 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "L 186.343125 138.329696 \n", "L 205.873125 139.5 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "L 186.343125 88.763468 \n", "L 205.873125 88.027999 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "L 171.051551 134.749964 \n", "L 180.774997 136.881594 \n", "L 190.498444 135.860221 \n", "L 200.221891 137.266581 \n", "L 209.945338 138.601547 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "L 186.343125 138.329696 \n", "L 205.873125 139.5 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "L 186.343125 88.763468 \n", "L 205.873125 88.027999 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "L 171.051551 134.749964 \n", "L 180.774997 136.881594 \n", "L 190.498444 135.860221 \n", "L 200.221891 137.266581 \n", "L 209.945338 138.601547 \n", "L 219.668785 138.184654 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "L 186.343125 138.329696 \n", "L 205.873125 139.5 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "L 186.343125 88.763468 \n", "L 205.873125 88.027999 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "L 171.051551 134.749964 \n", "L 180.774997 136.881594 \n", "L 190.498444 135.860221 \n", "L 200.221891 137.266581 \n", "L 209.945338 138.601547 \n", "L 219.668785 138.184654 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "L 186.343125 138.329696 \n", "L 205.873125 139.5 \n", "L 225.403125 138.72906 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "L 186.343125 88.763468 \n", "L 205.873125 88.027999 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.396691 \n", "L 54.370189 101.39422 \n", "L 64.093636 111.105543 \n", "L 73.817082 116.58923 \n", "L 83.540529 121.68679 \n", "L 93.263976 123.767331 \n", "L 102.987423 126.841304 \n", "L 112.71087 127.772917 \n", "L 122.434316 130.696666 \n", "L 132.157763 131.740012 \n", "L 141.88121 132.054729 \n", "L 151.604657 133.793218 \n", "L 161.328104 134.438059 \n", "L 171.051551 134.749964 \n", "L 180.774997 136.881594 \n", "L 190.498444 135.860221 \n", "L 200.221891 137.266581 \n", "L 209.945338 138.601547 \n", "L 219.668785 138.184654 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_127\">\n", "    <path d=\"M 49.633125 104.850182 \n", "L 69.163125 122.518972 \n", "L 88.693125 125.668554 \n", "L 108.223125 132.973586 \n", "L 127.753125 135.098045 \n", "L 147.283125 136.814757 \n", "L 166.813125 138.072244 \n", "L 186.343125 138.329696 \n", "L 205.873125 139.5 \n", "L 225.403125 138.72906 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_128\">\n", "    <path d=\"M 49.633125 102.397927 \n", "L 69.163125 93.583617 \n", "L 88.693125 93.662821 \n", "L 108.223125 89.974162 \n", "L 127.753125 89.996792 \n", "L 147.283125 89.487622 \n", "L 166.813125 88.548485 \n", "L 186.343125 88.763468 \n", "L 205.873125 88.027999 \n", "L 225.403125 88.276927 \n", "\" clip-path=\"url(#p1529e1d75c)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 60.06875 \n", "L 218.403125 60.06875 \n", "Q 220.403125 60.06875 220.403125 58.06875 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 138.8125 12.2 \n", "Q 136.8125 12.2 136.8125 14.2 \n", "L 136.8125 58.06875 \n", "Q 136.8125 60.06875 138.8125 60.06875 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_129\">\n", "     <path d=\"M 140.8125 20.298438 \n", "L 150.8125 20.298438 \n", "L 160.8125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_130\">\n", "     <path d=\"M 140.8125 35.254688 \n", "L 150.8125 35.254688 \n", "L 160.8125 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_131\">\n", "     <path d=\"M 140.8125 50.210938 \n", "L 150.8125 50.210938 \n", "L 160.8125 50.210938 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 53.710938) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p1529e1d75c\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["hparams = {\n", "    'num_outputs': 10,\n", "    'num_hiddens_1': 256,\n", "    'num_hiddens_2': 256,\n", "    'dropout_1': 0.5,\n", "    'dropout_2': 0.5,\n", "    'lr': 0.1\n", "}\n", "model = DropoutMLPScratch(**hparams)\n", "data = d2l.FashionMNIST(batch_size=256)\n", "trainer = d2l.Trainer(max_epochs=10)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "101c89c1", "metadata": {"origin_pos": 20}, "source": ["## [**Concise Implementation**]\n", "\n", "With high-level APIs, all we need to do is add a `Dropout` layer\n", "after each fully connected layer,\n", "passing in the dropout probability\n", "as the only argument to its constructor.\n", "During training, the `Dropout` layer will randomly\n", "drop out outputs of the previous layer\n", "(or equivalently, the inputs to the subsequent layer)\n", "according to the specified dropout probability.\n", "When not in training mode,\n", "the `Dropout` layer simply passes the data through during testing.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "224bafde", "metadata": {"origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["class DropoutMLP(d2l.Classifier):\n", "    def __init__(self, num_outputs, num_hiddens_1, num_hiddens_2,\n", "                 dropout_1, dropout_2, lr):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        self.net = nn.Sequential(\n", "            nn.<PERSON>(),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>ar(num_hiddens_1),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout_1),\n", "            nn.<PERSON><PERSON><PERSON><PERSON><PERSON>(num_hiddens_2),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout_2),\n", "            nn.LazyLinear(num_outputs)\n", "        )"]}, {"cell_type": "markdown", "id": "877d8ec2", "metadata": {"origin_pos": 27}, "source": ["Next, we [**train the model**].\n"]}, {"cell_type": "code", "execution_count": 7, "id": "d9e0ea94", "metadata": {"origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-23T09:54:42.731439</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m287b688161\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m287b688161\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m287b688161\" x=\"69.163125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(65.981875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m287b688161\" x=\"108.223125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(105.041875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m287b688161\" x=\"147.283125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(144.101875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m287b688161\" x=\"186.343125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(183.161875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m287b688161\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m11b7aa68c5\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m11b7aa68c5\" x=\"30.103125\" y=\"138.825268\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 142.624487) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m11b7aa68c5\" x=\"30.103125\" y=\"115.059679\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 118.858898) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m11b7aa68c5\" x=\"30.103125\" y=\"91.29409\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 95.093309) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m11b7aa68c5\" x=\"30.103125\" y=\"67.528501\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 71.32772) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m11b7aa68c5\" x=\"30.103125\" y=\"43.762912\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.2 -->\n", "      <g transform=\"translate(7.2 47.562131) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m11b7aa68c5\" x=\"30.103125\" y=\"19.997323\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.4 -->\n", "      <g transform=\"translate(7.2 23.796542) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 34.923295 13.5 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 49.633125 103.292536 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_17\"/>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 49.633125 103.292536 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 49.633125 100.531106 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 49.633125 103.292536 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 49.633125 100.531106 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 49.633125 103.292536 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 49.633125 100.531106 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 49.633125 100.531106 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "L 171.051551 134.025269 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "L 171.051551 134.025269 \n", "L 180.774997 133.929078 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "L 171.051551 134.025269 \n", "L 180.774997 133.929078 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "L 186.343125 135.898992 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "L 171.051551 134.025269 \n", "L 180.774997 133.929078 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "L 186.343125 135.898992 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "L 186.343125 86.269432 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "L 171.051551 134.025269 \n", "L 180.774997 133.929078 \n", "L 190.498444 135.372222 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "L 186.343125 135.898992 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "L 186.343125 86.269432 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "L 171.051551 134.025269 \n", "L 180.774997 133.929078 \n", "L 190.498444 135.372222 \n", "L 200.221891 136.074252 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "L 186.343125 135.898992 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "L 186.343125 86.269432 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "L 171.051551 134.025269 \n", "L 180.774997 133.929078 \n", "L 190.498444 135.372222 \n", "L 200.221891 136.074252 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "L 186.343125 135.898992 \n", "L 205.873125 136.993013 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "L 186.343125 86.269432 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "L 171.051551 134.025269 \n", "L 180.774997 133.929078 \n", "L 190.498444 135.372222 \n", "L 200.221891 136.074252 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "L 186.343125 135.898992 \n", "L 205.873125 136.993013 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "L 186.343125 86.269432 \n", "L 205.873125 85.491944 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "L 171.051551 134.025269 \n", "L 180.774997 133.929078 \n", "L 190.498444 135.372222 \n", "L 200.221891 136.074252 \n", "L 209.945338 135.954207 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "L 186.343125 135.898992 \n", "L 205.873125 136.993013 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "L 186.343125 86.269432 \n", "L 205.873125 85.491944 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "L 171.051551 134.025269 \n", "L 180.774997 133.929078 \n", "L 190.498444 135.372222 \n", "L 200.221891 136.074252 \n", "L 209.945338 135.954207 \n", "L 219.668785 137.350876 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "L 186.343125 135.898992 \n", "L 205.873125 136.993013 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "L 186.343125 86.269432 \n", "L 205.873125 85.491944 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "L 171.051551 134.025269 \n", "L 180.774997 133.929078 \n", "L 190.498444 135.372222 \n", "L 200.221891 136.074252 \n", "L 209.945338 135.954207 \n", "L 219.668785 137.350876 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "L 186.343125 135.898992 \n", "L 205.873125 136.993013 \n", "L 225.403125 139.5 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "L 186.343125 86.269432 \n", "L 205.873125 85.491944 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 85.474401 \n", "L 54.370189 101.192638 \n", "L 64.093636 108.857887 \n", "L 73.817082 115.287227 \n", "L 83.540529 118.986583 \n", "L 93.263976 122.241361 \n", "L 102.987423 124.046139 \n", "L 112.71087 126.348131 \n", "L 122.434316 128.570072 \n", "L 132.157763 129.409959 \n", "L 141.88121 131.211598 \n", "L 151.604657 131.154373 \n", "L 161.328104 133.03731 \n", "L 171.051551 134.025269 \n", "L 180.774997 133.929078 \n", "L 190.498444 135.372222 \n", "L 200.221891 136.074252 \n", "L 209.945338 135.954207 \n", "L 219.668785 137.350876 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_127\">\n", "    <path d=\"M 49.633125 103.292536 \n", "L 69.163125 115.403169 \n", "L 88.693125 126.14892 \n", "L 108.223125 129.851429 \n", "L 127.753125 132.858374 \n", "L 147.283125 134.132429 \n", "L 166.813125 136.243737 \n", "L 186.343125 135.898992 \n", "L 205.873125 136.993013 \n", "L 225.403125 139.5 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_128\">\n", "    <path d=\"M 49.633125 100.531106 \n", "L 69.163125 93.823826 \n", "L 88.693125 89.762324 \n", "L 108.223125 88.532269 \n", "L 127.753125 87.197775 \n", "L 147.283125 86.780021 \n", "L 166.813125 85.666009 \n", "L 186.343125 86.269432 \n", "L 205.873125 85.491944 \n", "L 225.403125 84.66804 \n", "\" clip-path=\"url(#p62d5a51653)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 60.06875 \n", "L 218.403125 60.06875 \n", "Q 220.403125 60.06875 220.403125 58.06875 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 138.8125 12.2 \n", "Q 136.8125 12.2 136.8125 14.2 \n", "L 136.8125 58.06875 \n", "Q 136.8125 60.06875 138.8125 60.06875 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_129\">\n", "     <path d=\"M 140.8125 20.298438 \n", "L 150.8125 20.298438 \n", "L 160.8125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_130\">\n", "     <path d=\"M 140.8125 35.254688 \n", "L 150.8125 35.254688 \n", "L 160.8125 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_131\">\n", "     <path d=\"M 140.8125 50.210938 \n", "L 150.8125 50.210938 \n", "L 160.8125 50.210938 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 53.710938) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p62d5a51653\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = DropoutMLP(**hparams)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "54985a45", "metadata": {"origin_pos": 29}, "source": ["## Summary\n", "\n", "Beyond controlling the number of dimensions and the size of the weight vector, dropout is yet another tool for avoiding overfitting. Often tools are used jointly.\n", "Note that dropout is\n", "used only during training:\n", "it replaces an activation $h$ with a random variable with expected value $h$.\n", "\n", "\n", "## Exercises\n", "\n", "1. What happens if you change the dropout probabilities for the first and second layers? In particular, what happens if you switch the ones for both layers? Design an experiment to answer these questions, describe your results quantitatively, and summarize the qualitative takeaways.\n", "1. Increase the number of epochs and compare the results obtained when using dropout with those when not using it.\n", "1. What is the variance of the activations in each hidden layer when dropout is and is not applied? Draw a plot to show how this quantity evolves over time for both models.\n", "1. Why is dropout not typically used at test time?\n", "1. Using the model in this section as an example, compare the effects of using dropout and weight decay. What happens when dropout and weight decay are used at the same time? Are the results additive? Are there diminished returns (or worse)? Do they cancel each other out?\n", "1. What happens if we apply dropout to the individual weights of the weight matrix rather than the activations?\n", "1. Invent another technique for injecting random noise at each layer that is different from the standard dropout technique. Can you develop a method that outperforms dropout on the Fashion-MNIST dataset (for a fixed architecture)?\n"]}, {"cell_type": "markdown", "id": "29eae3ea", "metadata": {"origin_pos": 31, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/101)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON> (aideep)", "language": "python", "name": "<PERSON><PERSON>"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "0d9388b7", "metadata": {"origin_pos": 1}, "source": ["# Predicting House Prices on Kaggle\n", ":label:`sec_kaggle_house`\n", "\n", "Now that we have introduced some basic tools\n", "for building and training deep networks\n", "and regularizing them with techniques including\n", "weight decay and dropout,\n", "we are ready to put all this knowledge into practice\n", "by participating in a Kaggle competition.\n", "The house price prediction competition\n", "is a great place to start.\n", "The data is fairly generic and do not exhibit exotic structure\n", "that might require specialized models (as audio or video might).\n", "This dataset, collected by :citet:`De-Cock.2011`,\n", "covers house prices in Ames, Iowa from the period 2006--2010.\n", "It is considerably larger than the famous [Boston housing dataset](https://archive.ics.uci.edu/ml/machine-learning-databases/housing/housing.names) of <PERSON> and <PERSON> (1978),\n", "boasting both more examples and more features.\n", "\n", "\n", "In this section, we will walk you through details of\n", "data preprocessing, model design, and hyperparameter selection.\n", "We hope that through a hands-on approach,\n", "you will gain some intuitions that will guide you\n", "in your career as a data scientist.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "1c33eb92", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:21.587414Z", "iopub.status.busy": "2023-08-18T19:32:21.586752Z", "iopub.status.idle": "2023-08-18T19:32:24.821984Z", "shell.execute_reply": "2023-08-18T19:32:24.820834Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import pandas as pd\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "22637186", "metadata": {"origin_pos": 6}, "source": ["## Downloading Data\n", "\n", "Throughout the book, we will train and test models\n", "on various downloaded datasets.\n", "Here, we (**implement two utility functions**)\n", "for downloading and extracting zip or tar files.\n", "Again, we skip implementation details of\n", "such utility functions.\n"]}, {"cell_type": "code", "id": "c5b9dd70", "metadata": {"attributes": {"classes": [], "id": "", "n": "2"}, "execution": {"iopub.execute_input": "2023-08-18T19:32:24.826201Z", "iopub.status.busy": "2023-08-18T19:32:24.825720Z", "iopub.status.idle": "2023-08-18T19:32:24.831209Z", "shell.execute_reply": "2023-08-18T19:32:24.830384Z"}, "origin_pos": 7, "tab": ["pytorch"], "ExecuteTime": {"end_time": "2025-03-23T03:10:07.311736Z", "start_time": "2025-03-23T03:10:07.307047Z"}}, "source": ["def download(url, folder, sha1_hash=None):\n", "    \"\"\"Download a file to folder and return the local filepath.\"\"\"\n", "\n", "def extract(filename, folder):\n", "    \"\"\"Extract a zip/tar file into folder.\"\"\""], "outputs": [], "execution_count": 1}, {"cell_type": "markdown", "id": "ff0b2664", "metadata": {"origin_pos": 8}, "source": ["## Kaggle\n", "\n", "[Kaggle](https://www.kaggle.com) is a popular platform\n", "that hosts machine learning competitions.\n", "Each competition centers on a dataset and many\n", "are sponsored by stakeholders who offer prizes\n", "to the winning solutions.\n", "The platform helps users to interact\n", "via forums and shared code,\n", "fostering both collaboration and competition.\n", "While leaderboard chasing often spirals out of control,\n", "with researchers focusing myopically on preprocessing steps\n", "rather than asking fundamental questions,\n", "there is also tremendous value in the objectivity of a platform\n", "that facilitates direct quantitative comparisons\n", "among competing approaches as well as code sharing\n", "so that everyone can learn what did and did not work.\n", "If you want to participate in a Kaggle competition,\n", "you will first need to register for an account\n", "(see :numref:`fig_kaggle`).\n", "\n", "![The Kaggle website.](../img/kaggle.png)\n", ":width:`400px`\n", ":label:`fig_kaggle`\n", "\n", "On the house price prediction competition page, as illustrated\n", "in :numref:`fig_house_pricing`,\n", "you can find the dataset (under the \"Data\" tab),\n", "submit predictions, and see your ranking,\n", "The URL is right here:\n", "\n", "> https://www.kaggle.com/c/house-prices-advanced-regression-techniques\n", "\n", "![The house price prediction competition page.](../img/house-pricing.png)\n", ":width:`400px`\n", ":label:`fig_house_pricing`\n", "\n", "## Accessing and Reading the Dataset\n", "\n", "Note that the competition data is separated\n", "into training and test sets.\n", "Each record includes the property value of the house\n", "and attributes such as street type, year of construction,\n", "roof type, basement condition, etc.\n", "The features consist of various data types.\n", "For example, the year of construction\n", "is represented by an integer,\n", "the roof type by discrete categorical assignments,\n", "and other features by floating point numbers.\n", "And here is where reality complicates things:\n", "for some examples, some data is altogether missing\n", "with the missing value marked simply as \"na\".\n", "The price of each house is included\n", "for the training set only\n", "(it is a competition after all).\n", "We will want to partition the training set\n", "to create a validation set,\n", "but we only get to evaluate our models on the official test set\n", "after uploading predictions to Ka<PERSON>.\n", "The \"Data\" tab on the competition tab\n", "in :numref:`fig_house_pricing`\n", "has links for downloading the data.\n", "\n", "To get started, we will [**read in and process the data\n", "using `pandas`**], which we introduced in :numref:`sec_pandas`.\n", "For convenience, we can download and cache\n", "the Kaggle housing dataset.\n", "If a file corresponding to this dataset already exists in the cache directory and its SHA-1 matches `sha1_hash`, our code will use the cached file to avoid clogging up your Internet with redundant downloads.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "48c41477", "metadata": {"attributes": {"classes": [], "id": "", "n": "30"}, "execution": {"iopub.execute_input": "2023-08-18T19:32:24.835437Z", "iopub.status.busy": "2023-08-18T19:32:24.834529Z", "iopub.status.idle": "2023-08-18T19:32:24.840555Z", "shell.execute_reply": "2023-08-18T19:32:24.839683Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["class KaggleHouse(d2l.DataModule):\n", "    def __init__(self, batch_size, train=None, val=None):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        if self.train is None:\n", "            self.raw_train = pd.read_csv(d2l.download(\n", "                d2l.DATA_URL + 'kaggle_house_pred_train.csv', self.root,\n", "                sha1_hash='585e9cc93e70b39160e7921475f9bcd7d31219ce'))\n", "            self.raw_val = pd.read_csv(d2l.download(\n", "                d2l.DATA_URL + 'kaggle_house_pred_test.csv', self.root,\n", "                sha1_hash='fa19780a7b011d9b009e8bff8e99922a8ee2eb90'))"]}, {"cell_type": "markdown", "id": "45dfab1b", "metadata": {"origin_pos": 10}, "source": ["The training dataset includes 1460 examples,\n", "80 features, and one label, while the validation data\n", "contains 1459 examples and 80 features.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "7e9e8f7c", "metadata": {"attributes": {"classes": [], "id": "", "n": "31"}, "execution": {"iopub.execute_input": "2023-08-18T19:32:24.844705Z", "iopub.status.busy": "2023-08-18T19:32:24.843955Z", "iopub.status.idle": "2023-08-18T19:32:25.218067Z", "shell.execute_reply": "2023-08-18T19:32:25.217232Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/kaggle_house_pred_train.csv from http://d2l-data.s3-accelerate.amazonaws.com/kaggle_house_pred_train.csv...\n", "Downloading ../data/kaggle_house_pred_test.csv from http://d2l-data.s3-accelerate.amazonaws.com/kaggle_house_pred_test.csv...\n", "(1460, 81)\n", "(1459, 80)\n"]}], "source": ["data = KaggleHouse(batch_size=64)\n", "print(data.raw_train.shape)\n", "print(data.raw_val.shape)"]}, {"cell_type": "markdown", "id": "017e793e", "metadata": {"origin_pos": 12}, "source": ["## Data Preprocessing\n", "\n", "Let's [**take a look at the first four and final two features\n", "as well as the label (SalePrice)**] from the first four examples.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "92621a85", "metadata": {"attributes": {"classes": [], "id": "", "n": "10"}, "execution": {"iopub.execute_input": "2023-08-18T19:32:25.221755Z", "iopub.status.busy": "2023-08-18T19:32:25.221161Z", "iopub.status.idle": "2023-08-18T19:32:25.230323Z", "shell.execute_reply": "2023-08-18T19:32:25.229502Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Id  MSSubClass MSZoning  LotFrontage SaleType SaleCondition  SalePrice\n", "0   1          60       RL         65.0       WD        Normal     208500\n", "1   2          20       RL         80.0       WD        Normal     181500\n", "2   3          60       RL         68.0       WD        Normal     223500\n", "3   4          70       RL         60.0       WD       Abnorml     140000\n"]}], "source": ["print(data.raw_train.iloc[:4, [0, 1, 2, 3, -3, -2, -1]])"]}, {"cell_type": "markdown", "id": "caf77495", "metadata": {"origin_pos": 14}, "source": ["We can see that in each example, the first feature is the identifier.\n", "This helps the model determine each training example.\n", "While this is convenient, it does not carry\n", "any information for prediction purposes.\n", "Hence, we will remove it from the dataset\n", "before feeding the data into the model.\n", "Furthermore, given a wide variety of data types,\n", "we will need to preprocess the data before we can start modeling.\n", "\n", "\n", "Let's start with the numerical features.\n", "First, we apply a heuristic,\n", "[**replacing all missing values\n", "by the corresponding feature's mean.**]\n", "Then, to put all features on a common scale,\n", "we (***standardize* the data by\n", "rescaling features to zero mean and unit variance**):\n", "\n", "$$x \\leftarrow \\frac{x - \\mu}{\\sigma},$$\n", "\n", "where $\\mu$ and $\\sigma$ denote mean and standard deviation, respectively.\n", "To verify that this indeed transforms\n", "our feature (variable) such that it has zero mean and unit variance,\n", "note that $E[\\frac{x-\\mu}{\\sigma}] = \\frac{\\mu - \\mu}{\\sigma} = 0$\n", "and that $E[(x-\\mu)^2] = (\\sigma^2 + \\mu^2) - 2\\mu^2+\\mu^2 = \\sigma^2$.\n", "Intuitively, we standardize the data\n", "for two reasons.\n", "First, it proves convenient for optimization.\n", "Second, because we do not know *a priori*\n", "which features will be relevant,\n", "we do not want to penalize coefficients\n", "assigned to one feature more than any other.\n", "\n", "[**Next we deal with discrete values.**]\n", "These include features such as \"MSZoning\".\n", "(**We replace them by a one-hot encoding**)\n", "in the same way that we earlier transformed\n", "multiclass labels into vectors (see :numref:`subsec_classification-problem`).\n", "For instance, \"MSZoning\" assumes the values \"RL\" and \"RM\".\n", "Dropping the \"MSZoning\" feature,\n", "two new indicator features\n", "\"MSZoning_RL\" and \"MSZoning_RM\" are created with values being either 0 or 1.\n", "According to one-hot encoding,\n", "if the original value of \"MSZoning\" is \"RL\",\n", "then \"MSZoning_RL\" is 1 and \"MSZoning_RM\" is 0.\n", "The `pandas` package does this automatically for us.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "11b2ad6e", "metadata": {"attributes": {"classes": [], "id": "", "n": "32"}, "execution": {"iopub.execute_input": "2023-08-18T19:32:25.233625Z", "iopub.status.busy": "2023-08-18T19:32:25.233151Z", "iopub.status.idle": "2023-08-18T19:32:25.239490Z", "shell.execute_reply": "2023-08-18T19:32:25.238692Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(KaggleHouse)\n", "def preprocess(self):\n", "    # Remove the ID and label columns\n", "    label = 'SalePrice'\n", "    features = pd.concat(\n", "        (self.raw_train.drop(columns=['Id', label]),\n", "         self.raw_val.drop(columns=['Id'])))\n", "    # Standardize numerical columns\n", "    numeric_features = features.dtypes[features.dtypes!='object'].index\n", "    features[numeric_features] = features[numeric_features].apply(\n", "        lambda x: (x - x.mean()) / (x.std()))\n", "    # Replace NAN numerical features by 0\n", "    features[numeric_features] = features[numeric_features].fillna(0)\n", "    # Replace discrete features by one-hot encoding\n", "    features = pd.get_dummies(features, dummy_na=True)\n", "    # Save preprocessed features\n", "    self.train = features[:self.raw_train.shape[0]].copy()\n", "    self.train[label] = self.raw_train[label]\n", "    self.val = features[self.raw_train.shape[0]:].copy()"]}, {"cell_type": "markdown", "id": "3d38d0ed", "metadata": {"origin_pos": 16}, "source": ["You can see that this conversion increases\n", "the number of features from 79 to 331 (excluding ID and label columns).\n"]}, {"cell_type": "code", "execution_count": 8, "id": "a9e39c34", "metadata": {"attributes": {"classes": [], "id": "", "n": "33"}, "execution": {"iopub.execute_input": "2023-08-18T19:32:25.242819Z", "iopub.status.busy": "2023-08-18T19:32:25.242192Z", "iopub.status.idle": "2023-08-18T19:32:25.356247Z", "shell.execute_reply": "2023-08-18T19:32:25.355251Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(1460, 331)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["data.preprocess()\n", "data.train.shape"]}, {"cell_type": "markdown", "id": "4a4f19be", "metadata": {"origin_pos": 18}, "source": ["## E<PERSON>r Me<PERSON>ure\n", "\n", "To get started we will train a linear model with squared loss. Not surprisingly, our linear model will not lead to a competition-winning submission but it does provide a sanity check to see whether there is meaningful information in the data. If we cannot do better than random guessing here, then there might be a good chance that we have a data processing bug. And if things work, the linear model will serve as a baseline giving us some intuition about how close the simple model gets to the best reported models, giving us a sense of how much gain we should expect from fancier models.\n", "\n", "With house prices, as with stock prices,\n", "we care about relative quantities\n", "more than absolute quantities.\n", "Thus [**we tend to care more about\n", "the relative error $\\frac{y - \\hat{y}}{y}$**]\n", "than about the absolute error $y - \\hat{y}$.\n", "For instance, if our prediction is off by \\$100,000\n", "when estimating the price of a house in rural Ohio,\n", "where the value of a typical house is \\$125,000,\n", "then we are probably doing a horrible job.\n", "On the other hand, if we err by this amount\n", "in Los Altos Hills, California,\n", "this might represent a stunningly accurate prediction\n", "(there, the median house price exceeds \\$4 million).\n", "\n", "(**One way to address this problem is to\n", "measure the discrepancy in the logarithm of the price estimates.**)\n", "In fact, this is also the official error measure\n", "used by the competition to evaluate the quality of submissions.\n", "After all, a small value $\\delta$ for $|\\log y - \\log \\hat{y}| \\leq \\delta$\n", "translates into $e^{-\\delta} \\leq \\frac{\\hat{y}}{y} \\leq e^\\delta$.\n", "This leads to the following root-mean-squared-error between the logarithm of the predicted price and the logarithm of the label price:\n", "\n", "$$\\sqrt{\\frac{1}{n}\\sum_{i=1}^n\\left(\\log y_i -\\log \\hat{y}_i\\right)^2}.$$\n"]}, {"cell_type": "code", "execution_count": 9, "id": "22cee03d", "metadata": {"attributes": {"classes": [], "id": "", "n": "60"}, "execution": {"iopub.execute_input": "2023-08-18T19:32:25.360088Z", "iopub.status.busy": "2023-08-18T19:32:25.359480Z", "iopub.status.idle": "2023-08-18T19:32:25.365132Z", "shell.execute_reply": "2023-08-18T19:32:25.364342Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(KaggleHouse)\n", "def get_dataloader(self, train):\n", "    label = 'SalePrice'\n", "    data = self.train if train else self.val\n", "    if label not in data: return\n", "    get_tensor = lambda x: torch.tensor(x.values.astype(float),\n", "                                      dtype=torch.float32)\n", "    # Logarithm of prices\n", "    tensors = (get_tensor(data.drop(columns=[label])),  # X\n", "               torch.log(get_tensor(data[label])).reshape((-1, 1)))  # Y\n", "    return self.get_tensorloader(tensors, train)"]}, {"cell_type": "markdown", "id": "737b03af", "metadata": {"origin_pos": 20}, "source": ["## $K$-Fold Cross-Validation\n", "\n", "You might recall that we introduced [**cross-validation**]\n", "in :numref:`subsec_generalization-model-selection`, where we discussed how to deal\n", "with model selection.\n", "We will put this to good use to select the model design\n", "and to adjust the hyperparameters.\n", "We first need a function that returns\n", "the $i^\\textrm{th}$ fold of the data\n", "in a $K$-fold cross-validation procedure.\n", "It proceeds by slicing out the $i^\\textrm{th}$ segment\n", "as validation data and returning the rest as training data.\n", "Note that this is not the most efficient way of handling data\n", "and we would definitely do something much smarter\n", "if our dataset was considerably larger.\n", "But this added complexity might obfuscate our code unnecessarily\n", "so we can safely omit it here owing to the simplicity of our problem.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "e6949856", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:25.368517Z", "iopub.status.busy": "2023-08-18T19:32:25.367949Z", "iopub.status.idle": "2023-08-18T19:32:25.372985Z", "shell.execute_reply": "2023-08-18T19:32:25.372067Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [], "source": ["def k_fold_data(data, k):\n", "    rets = []\n", "    fold_size = data.train.shape[0] // k\n", "    for j in range(k):\n", "        idx = range(j * fold_size, (j+1) * fold_size)\n", "        rets.append(KaggleHouse(data.batch_size, data.train.drop(index=idx),\n", "                                data.train.loc[idx]))\n", "    return rets"]}, {"cell_type": "markdown", "id": "f7071050", "metadata": {"origin_pos": 22}, "source": ["[**The average validation error is returned**]\n", "when we train $K$ times in the $K$-fold cross-validation.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "c626ec24", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:25.376435Z", "iopub.status.busy": "2023-08-18T19:32:25.375867Z", "iopub.status.idle": "2023-08-18T19:32:25.381314Z", "shell.execute_reply": "2023-08-18T19:32:25.380464Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [], "source": ["def k_fold(trainer, data, k, lr):\n", "    val_loss, models = [], []\n", "    for i, data_fold in enumerate(k_fold_data(data, k)):\n", "        model = d2l.LinearRegression(lr)\n", "        model.board.yscale='log'\n", "        if i != 0: model.board.display = False\n", "        trainer.fit(model, data_fold)\n", "        val_loss.append(float(model.board.data['val_loss'][-1].y))\n", "        models.append(model)\n", "    print(f'average validation log mse = {sum(val_loss)/len(val_loss)}')\n", "    return models"]}, {"cell_type": "markdown", "id": "bcb1790f", "metadata": {"origin_pos": 24}, "source": ["## [**Model Selection**]\n", "\n", "In this example, we pick an untuned set of hyperparameters\n", "and leave it up to the reader to improve the model.\n", "Finding a good choice can take time,\n", "depending on how many variables one optimizes over.\n", "With a large enough dataset,\n", "and the normal sorts of hyperparameters,\n", "$K$-fold cross-validation tends to be\n", "reasonably resilient against multiple testing.\n", "However, if we try an unreasonably large number of options\n", "we might find that our validation\n", "performance is no longer representative of the true error.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "c86184c4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:25.384646Z", "iopub.status.busy": "2023-08-18T19:32:25.384079Z", "iopub.status.idle": "2023-08-18T19:32:37.095341Z", "shell.execute_reply": "2023-08-18T19:32:37.094054Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/aideep/lib/python3.9/site-packages/torch/nn/modules/lazy.py:180: UserWarning: Lazy modules are a new feature under heavy development so changes to the API or functionality can happen at any moment.\n", "  warnings.warn('Lazy modules are a new feature under heavy development '\n", "/opt/anaconda3/envs/aideep/lib/python3.9/site-packages/torch/nn/modules/lazy.py:180: UserWarning: Lazy modules are a new feature under heavy development so changes to the API or functionality can happen at any moment.\n", "  warnings.warn('Lazy modules are a new feature under heavy development '\n", "/opt/anaconda3/envs/aideep/lib/python3.9/site-packages/torch/nn/modules/lazy.py:180: UserWarning: Lazy modules are a new feature under heavy development so changes to the API or functionality can happen at any moment.\n", "  warnings.warn('Lazy modules are a new feature under heavy development '\n", "/opt/anaconda3/envs/aideep/lib/python3.9/site-packages/torch/nn/modules/lazy.py:180: UserWarning: Lazy modules are a new feature under heavy development so changes to the API or functionality can happen at any moment.\n", "  warnings.warn('Lazy modules are a new feature under heavy development '\n"]}, {"name": "stdout", "output_type": "stream", "text": ["average validation log mse = 0.18400497257709506\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"240.6625pt\" height=\"183.35625pt\" viewBox=\"0 0 240.6625 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-23T11:04:30.857914</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 240.6625 183.35625 \n", "L 240.6625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 31.8 145.8 \n", "L 227.1 145.8 \n", "L 227.1 7.2 \n", "L 31.8 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mc26590e225\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc26590e225\" x=\"31.8\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(28.61875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mc26590e225\" x=\"70.86\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(67.67875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mc26590e225\" x=\"109.92\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(106.73875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mc26590e225\" x=\"148.98\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(145.79875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mc26590e225\" x=\"188.04\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(184.85875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mc26590e225\" x=\"227.1\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(220.7375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(114.221875 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m906577839c\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m906577839c\" x=\"31.8\" y=\"91.311306\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{10^{0}}$ -->\n", "      <g transform=\"translate(7.2 95.110525) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m906577839c\" x=\"31.8\" y=\"31.916913\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(7.2 35.716132) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <defs>\n", "       <path id=\"m9eba896f00\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"132.826205\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"122.367371\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"114.946711\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"109.1908\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"104.487878\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"100.511614\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"97.067217\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"94.029044\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"73.431812\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"62.972979\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"55.552318\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"49.796407\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"45.093485\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"41.117221\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"37.672825\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"34.634652\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m9eba896f00\" x=\"31.8\" y=\"14.037419\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 35.911579 13.5 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 51.33 112.579599 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 51.33 112.579599 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 51.33 112.579599 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "L 156.175263 132.636208 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "L 156.175263 132.636208 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "L 168.51 133.951261 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "L 156.175263 132.636208 \n", "L 165.426316 131.843084 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "L 168.51 133.951261 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "L 156.175263 132.636208 \n", "L 165.426316 131.843084 \n", "L 174.677368 134.289202 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "L 168.51 133.951261 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "L 156.175263 132.636208 \n", "L 165.426316 131.843084 \n", "L 174.677368 134.289202 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "L 168.51 133.951261 \n", "L 188.04 135.332264 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "L 156.175263 132.636208 \n", "L 165.426316 131.843084 \n", "L 174.677368 134.289202 \n", "L 183.928421 134.297229 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "L 168.51 133.951261 \n", "L 188.04 135.332264 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "L 156.175263 132.636208 \n", "L 165.426316 131.843084 \n", "L 174.677368 134.289202 \n", "L 183.928421 134.297229 \n", "L 193.179474 136.96033 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "L 168.51 133.951261 \n", "L 188.04 135.332264 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "L 156.175263 132.636208 \n", "L 165.426316 131.843084 \n", "L 174.677368 134.289202 \n", "L 183.928421 134.297229 \n", "L 193.179474 136.96033 \n", "L 202.430526 139.5 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "L 168.51 133.951261 \n", "L 188.04 135.332264 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "L 156.175263 132.636208 \n", "L 165.426316 131.843084 \n", "L 174.677368 134.289202 \n", "L 183.928421 134.297229 \n", "L 193.179474 136.96033 \n", "L 202.430526 139.5 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "L 168.51 133.951261 \n", "L 188.04 135.332264 \n", "L 207.57 137.143738 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "L 156.175263 132.636208 \n", "L 165.426316 131.843084 \n", "L 174.677368 134.289202 \n", "L 183.928421 134.297229 \n", "L 193.179474 136.96033 \n", "L 202.430526 139.5 \n", "L 211.681579 139.42284 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "L 168.51 133.951261 \n", "L 188.04 135.332264 \n", "L 207.57 137.143738 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "L 156.175263 132.636208 \n", "L 165.426316 131.843084 \n", "L 174.677368 134.289202 \n", "L 183.928421 134.297229 \n", "L 193.179474 136.96033 \n", "L 202.430526 139.5 \n", "L 211.681579 139.42284 \n", "L 220.932632 138.371793 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "L 168.51 133.951261 \n", "L 188.04 135.332264 \n", "L 207.57 137.143738 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 35.911579 13.5 \n", "L 45.162632 105.439491 \n", "L 54.413684 115.902789 \n", "L 63.664737 113.505394 \n", "L 72.915789 120.134183 \n", "L 82.166842 118.419896 \n", "L 91.417895 119.591255 \n", "L 100.668947 122.722554 \n", "L 109.92 128.333233 \n", "L 119.171053 128.333083 \n", "L 128.422105 125.091047 \n", "L 137.673158 133.27711 \n", "L 146.924211 129.286787 \n", "L 156.175263 132.636208 \n", "L 165.426316 131.843084 \n", "L 174.677368 134.289202 \n", "L 183.928421 134.297229 \n", "L 193.179474 136.96033 \n", "L 202.430526 139.5 \n", "L 211.681579 139.42284 \n", "L 220.932632 138.371793 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 51.33 112.579599 \n", "L 70.86 118.163629 \n", "L 90.39 123.043681 \n", "L 109.92 125.465099 \n", "L 129.45 129.358393 \n", "L 148.98 131.495575 \n", "L 168.51 133.951261 \n", "L 188.04 135.332264 \n", "L 207.57 137.143738 \n", "L 227.1 138.743704 \n", "\" clip-path=\"url(#p74d9f858ed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 31.8 145.8 \n", "L 31.8 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 227.1 145.8 \n", "L 227.1 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 31.8 145.8 \n", "L 227.1 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 31.8 7.2 \n", "L 227.1 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.509375 45.1125 \n", "L 220.1 45.1125 \n", "Q 222.1 45.1125 222.1 43.1125 \n", "L 222.1 14.2 \n", "Q 222.1 12.2 220.1 12.2 \n", "L 140.509375 12.2 \n", "Q 138.509375 12.2 138.509375 14.2 \n", "L 138.509375 43.1125 \n", "Q 138.509375 45.1125 140.509375 45.1125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_86\">\n", "     <path d=\"M 142.509375 20.298438 \n", "L 152.509375 20.298438 \n", "L 162.509375 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(170.509375 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_87\">\n", "     <path d=\"M 142.509375 35.254688 \n", "L 152.509375 35.254688 \n", "L 162.509375 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(170.509375 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p74d9f858ed\">\n", "   <rect x=\"31.8\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer = d2l.Trainer(max_epochs=10)\n", "models = k_fold(trainer, data, k=5, lr=0.01)"]}, {"cell_type": "markdown", "id": "158afa13", "metadata": {"origin_pos": 26}, "source": ["Notice that sometimes the number of training errors\n", "for a set of hyperparameters can be very low,\n", "even as the number of errors on $K$-fold cross-validation\n", "grows considerably higher.\n", "This indicates that we are overfitting.\n", "Throughout training you will want to monitor both numbers.\n", "Less overfitting might indicate that our data can support a more powerful model.\n", "Massive overfitting might suggest that we can gain\n", "by incorporating regularization techniques.\n", "\n", "##  [**Submitting Predictions on Kaggle**]\n", "\n", "Now that we know what a good choice of hyperparameters should be,\n", "we might \n", "calculate the average predictions \n", "on the test set\n", "by all the $K$ models.\n", "Saving the predictions in a csv file\n", "will simplify uploading the results to Ka<PERSON>.\n", "The following code will generate a file called `submission.csv`.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "f4a3bcde", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:37.100208Z", "iopub.status.busy": "2023-08-18T19:32:37.099453Z", "iopub.status.idle": "2023-08-18T19:32:37.266811Z", "shell.execute_reply": "2023-08-18T19:32:37.265844Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [], "source": ["preds = [model(torch.tensor(data.val.values.astype(float), dtype=torch.float32))\n", "         for model in models]\n", "# Taking exponentiation of predictions in the logarithm scale\n", "ensemble_preds = torch.exp(torch.cat(preds, 1)).mean(1)\n", "submission = pd.DataFrame({'Id':data.raw_val.Id,\n", "                           'SalePrice':ensemble_preds.detach().numpy()})\n", "submission.to_csv('submission.csv', index=False)"]}, {"cell_type": "markdown", "id": "206db870", "metadata": {"origin_pos": 28}, "source": ["Next, as demonstrated in :numref:`fig_kaggle_submit2`,\n", "we can submit our predictions on <PERSON><PERSON>\n", "and see how they compare with the actual house prices (labels)\n", "on the test set.\n", "The steps are quite simple:\n", "\n", "* Log in to the Kaggle website and visit the house price prediction competition page.\n", "* Click the “Submit Predictions” or “Late Submission” button.\n", "* Click the “Upload Submission File” button in the dashed box at the bottom of the page and select the prediction file you wish to upload.\n", "* Click the “Make Submission” button at the bottom of the page to view your results.\n", "\n", "![Submitting data to Kaggle](../img/kaggle-submit2.png)\n", ":width:`400px`\n", ":label:`fig_kaggle_submit2`\n", "\n", "## Summary and Discussion\n", "\n", "Real data often contains a mix of different data types and needs to be preprocessed.\n", "Rescaling real-valued data to zero mean and unit variance is a good default. So is replacing missing values with their mean.\n", "Furthermore, transforming categorical features into indicator features allows us to treat them like one-hot vectors.\n", "When we tend to care more about\n", "the relative error than about the absolute error,\n", "we can \n", "measure the discrepancy in the logarithm of the prediction.\n", "To select the model and adjust the hyperparameters,\n", "we can use $K$-fold cross-validation .\n", "\n", "\n", "\n", "## Exercises\n", "\n", "1. Submit your predictions for this section to Kaggle. How good are they?\n", "1. Is it always a good idea to replace missing values by a mean? Hint: can you construct a situation where the values are not missing at random?\n", "1. Improve the score by tuning the hyperparameters through $K$-fold cross-validation.\n", "1. Improve the score by improving the model (e.g., layers, weight decay, and dropout).\n", "1. What happens if we do not standardize the continuous numerical features as we have done in this section?\n"]}, {"cell_type": "markdown", "id": "fe43aac4", "metadata": {"origin_pos": 30, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/107)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON> (aideep)", "language": "python", "name": "<PERSON><PERSON>"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "fb1c337f", "metadata": {"origin_pos": 1}, "source": ["# Calculus\n", ":label:`sec_calculus`\n", "\n", "For a long time, how to calculate \n", "the area of a circle remained a mystery.\n", "Then, in Ancient Greece, the mathematician <PERSON><PERSON><PERSON>\n", "came up with the clever idea \n", "to inscribe a series of polygons \n", "with increasing numbers of vertices\n", "on the inside of a circle\n", "(:numref:`fig_circle_area`). \n", "For a polygon with $n$ vertices,\n", "we obtain $n$ triangles.\n", "The height of each triangle approaches the radius $r$ \n", "as we partition the circle more finely. \n", "At the same time, its base approaches $2 \\pi r/n$, \n", "since the ratio between arc and secant approaches 1 \n", "for a large number of vertices. \n", "Thus, the area of the polygon approaches\n", "$n \\cdot r \\cdot \\frac{1}{2} (2 \\pi r/n) = \\pi r^2$.\n", "\n", "![Finding the area of a circle as a limit procedure.](../img/polygon-circle.svg)\n", ":label:`fig_circle_area`\n", "\n", "This limiting procedure is at the root of both \n", "*differential calculus* and *integral calculus*. \n", "The former can tell us how to increase\n", "or decrease a function's value by\n", "manipulating its arguments. \n", "This comes in handy for the *optimization problems*\n", "that we face in deep learning,\n", "where we repeatedly update our parameters \n", "in order to decrease the loss function.\n", "Optimization addresses how to fit our models to training data,\n", "and calculus is its key prerequisite.\n", "However, do not forget that our ultimate goal\n", "is to perform well on *previously unseen* data.\n", "That problem is called *generalization*\n", "and will be a key focus of other chapters.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "5162883d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:00.158561Z", "iopub.status.busy": "2023-08-18T19:31:00.158199Z", "iopub.status.idle": "2023-08-18T19:31:03.258372Z", "shell.execute_reply": "2023-08-18T19:31:03.256925Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import numpy as np\n", "from matplotlib_inline import backend_inline\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "8ad81b23", "metadata": {"origin_pos": 6}, "source": ["## Derivatives and Differentiation\n", "\n", "Put simply, a *derivative* is the rate of change\n", "in a function with respect to changes in its arguments.\n", "Derivatives can tell us how rapidly a loss function\n", "would increase or decrease were we \n", "to *increase* or *decrease* each parameter\n", "by an infinitesimally small amount.\n", "Formally, for functions $f: \\mathbb{R} \\rightarrow \\mathbb{R}$,\n", "that map from scalars to scalars,\n", "[**the *derivative* of $f$ at a point $x$ is defined as**]\n", "\n", "(**$$f'(x) = \\lim_{h \\rightarrow 0} \\frac{f(x+h) - f(x)}{h}.$$**)\n", ":eqlabel:`eq_derivative`\n", "\n", "This term on the right hand side is called a *limit* \n", "and it tells us what happens \n", "to the value of an expression\n", "as a specified variable \n", "approaches a particular value.\n", "This limit tells us what \n", "the ratio between a perturbation $h$\n", "and the change in the function value \n", "$f(x + h) - f(x)$ converges to \n", "as we shrink its size to zero.\n", "\n", "When $f'(x)$ exists, $f$ is said \n", "to be *differentiable* at $x$;\n", "and when $f'(x)$ exists for all $x$\n", "on a set, e.g., the interval $[a,b]$, \n", "we say that $f$ is differentiable on this set.\n", "Not all functions are differentiable,\n", "including many that we wish to optimize,\n", "such as accuracy and the area under the\n", "receiving operating characteristic (AUC).\n", "However, because computing the derivative of the loss \n", "is a crucial step in nearly all \n", "algorithms for training deep neural networks,\n", "we often optimize a differentiable *surrogate* instead.\n", "\n", "\n", "We can interpret the derivative \n", "$f'(x)$\n", "as the *instantaneous* rate of change \n", "of $f(x)$ with respect to $x$.\n", "Let's develop some intuition with an example.\n", "(**Define $u = f(x) = 3x^2-4x$.**)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "e351acdb", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:03.263539Z", "iopub.status.busy": "2023-08-18T19:31:03.262795Z", "iopub.status.idle": "2023-08-18T19:31:03.267363Z", "shell.execute_reply": "2023-08-18T19:31:03.266349Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["def f(x):\n", "    return 3 * x ** 2 - 4 * x"]}, {"cell_type": "markdown", "id": "50148144", "metadata": {"origin_pos": 11}, "source": ["[**Setting $x=1$, we see that $\\frac{f(x+h) - f(x)}{h}$**] (**approaches $2$\n", "as $h$ approaches $0$.**)\n", "While this experiment lacks \n", "the rigor of a mathematical proof,\n", "we can quickly see that indeed $f'(1) = 2$.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "7b5e7cf2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:03.271432Z", "iopub.status.busy": "2023-08-18T19:31:03.270665Z", "iopub.status.idle": "2023-08-18T19:31:03.276568Z", "shell.execute_reply": "2023-08-18T19:31:03.275548Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["h=0.10000, numerical limit=2.30000\n", "h=0.01000, numerical limit=2.03000\n", "h=0.00100, numerical limit=2.00300\n", "h=0.00010, numerical limit=2.00030\n", "h=0.00001, numerical limit=2.00003\n"]}], "source": ["for h in 10.0**np.arange(-1, -6, -1):\n", "    print(f'h={h:.5f}, numerical limit={(f(1+h)-f(1))/h:.5f}')"]}, {"cell_type": "markdown", "id": "0be7fece", "metadata": {"origin_pos": 13}, "source": ["There are several equivalent notational conventions for derivatives.\n", "Given $y = f(x)$, the following expressions are equivalent:\n", "\n", "$$f'(x) = y' = \\frac{dy}{dx} = \\frac{df}{dx} = \\frac{d}{dx} f(x) = Df(x) = D_x f(x),$$\n", "\n", "where the symbols $\\frac{d}{dx}$ and $D$ are *differentiation operators*.\n", "Below, we present the derivatives of some common functions:\n", "\n", "$$\\begin{aligned} \\frac{d}{dx} C & = 0 && \\textrm{for any constant $C$} \\\\ \\frac{d}{dx} x^n & = n x^{n-1} && \\textrm{for } n \\neq 0 \\\\ \\frac{d}{dx} e^x & = e^x \\\\ \\frac{d}{dx} \\ln x & = x^{-1}. \\end{aligned}$$\n", "\n", "Functions composed from differentiable functions \n", "are often themselves differentiable.\n", "The following rules come in handy \n", "for working with compositions \n", "of any differentiable functions \n", "$f$ and $g$, and constant $C$.\n", "\n", "$$\\begin{aligned} \\frac{d}{dx} [C f(x)] & = C \\frac{d}{dx} f(x) && \\textrm{Constant multiple rule} \\\\ \\frac{d}{dx} [f(x) + g(x)] & = \\frac{d}{dx} f(x) + \\frac{d}{dx} g(x) && \\textrm{Sum rule} \\\\ \\frac{d}{dx} [f(x) g(x)] & = f(x) \\frac{d}{dx} g(x) + g(x) \\frac{d}{dx} f(x) && \\textrm{Product rule} \\\\ \\frac{d}{dx} \\frac{f(x)}{g(x)} & = \\frac{g(x) \\frac{d}{dx} f(x) - f(x) \\frac{d}{dx} g(x)}{g^2(x)} && \\textrm{Quotient rule} \\end{aligned}$$\n", "\n", "Using this, we can apply the rules \n", "to find the derivative of $3 x^2 - 4x$ via\n", "\n", "$$\\frac{d}{dx} [3 x^2 - 4x] = 3 \\frac{d}{dx} x^2 - 4 \\frac{d}{dx} x = 6x - 4.$$\n", "\n", "Plugging in $x = 1$ shows that, indeed, \n", "the derivative equals $2$ at this location. \n", "Note that derivatives tell us \n", "the *slope* of a function \n", "at a particular location.  \n", "\n", "## Visualization Utilities\n", "\n", "[**We can visualize the slopes of functions using the `matplotlib` library**].\n", "We need to define a few functions. \n", "As its name indicates, `use_svg_display` \n", "tells `matplotlib` to output graphics \n", "in SVG format for crisper images. \n", "The comment `#@save` is a special modifier \n", "that allows us to save any function, \n", "class, or other code block to the `d2l` package \n", "so that we can invoke it later \n", "without repeating the code, \n", "e.g., via `d2l.use_svg_display()`.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "70d92d2a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:03.280403Z", "iopub.status.busy": "2023-08-18T19:31:03.280065Z", "iopub.status.idle": "2023-08-18T19:31:03.284973Z", "shell.execute_reply": "2023-08-18T19:31:03.283950Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["def use_svg_display():  #@save\n", "    \"\"\"Use the svg format to display a plot in Jupyter.\"\"\"\n", "    backend_inline.set_matplotlib_formats('svg')"]}, {"cell_type": "markdown", "id": "ef7c490f", "metadata": {"origin_pos": 15}, "source": ["Conveniently, we can set figure sizes with `set_figsize`. \n", "Since the import statement `from matplotlib import pyplot as plt` \n", "was marked via `#@save` in the `d2l` package, we can call `d2l.plt`.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "c868cdf6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:03.289140Z", "iopub.status.busy": "2023-08-18T19:31:03.288531Z", "iopub.status.idle": "2023-08-18T19:31:03.293764Z", "shell.execute_reply": "2023-08-18T19:31:03.292757Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["def set_figsize(figsize=(3.5, 2.5)):  #@save\n", "    \"\"\"Set the figure size for matplotlib.\"\"\"\n", "    use_svg_display()\n", "    d2l.plt.rcParams['figure.figsize'] = figsize"]}, {"cell_type": "markdown", "id": "f2bd084c", "metadata": {"origin_pos": 17}, "source": ["The `set_axes` function can associate axes\n", "with properties, including labels, ranges,\n", "and scales.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "8860f929", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:03.297796Z", "iopub.status.busy": "2023-08-18T19:31:03.297092Z", "iopub.status.idle": "2023-08-18T19:31:03.303068Z", "shell.execute_reply": "2023-08-18T19:31:03.302068Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def set_axes(axes, xlabel, ylabel, xlim, ylim, xscale, yscale, legend):\n", "    \"\"\"Set the axes for matplotlib.\"\"\"\n", "    axes.set_xlabel(xlabel), axes.set_ylabel(ylabel)\n", "    axes.set_xscale(xscale), axes.set_yscale(yscale)\n", "    axes.set_xlim(xlim),     axes.set_ylim(ylim)\n", "    if legend:\n", "        axes.legend(legend)\n", "    axes.grid()"]}, {"cell_type": "markdown", "id": "d8825398", "metadata": {"origin_pos": 19}, "source": ["With these three functions, we can define a `plot` function \n", "to overlay multiple curves. \n", "Much of the code here is just ensuring \n", "that the sizes and shapes of inputs match.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "0d56dd86", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:03.307130Z", "iopub.status.busy": "2023-08-18T19:31:03.306443Z", "iopub.status.idle": "2023-08-18T19:31:03.316351Z", "shell.execute_reply": "2023-08-18T19:31:03.315391Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def plot(X, Y=None, xlabel=None, ylabel=None, legend=[], xlim=None,\n", "         ylim=None, xscale='linear', yscale='linear',\n", "         fmts=('-', 'm--', 'g-.', 'r:'), figsize=(3.5, 2.5), axes=None):\n", "    \"\"\"Plot data points.\"\"\"\n", "\n", "    def has_one_axis(X):  # True if X (tensor or list) has 1 axis\n", "        return (hasattr(X, \"ndim\") and X.ndim == 1 or isinstance(X, list)\n", "                and not hasattr(X[0], \"__len__\"))\n", "\n", "    if has_one_axis(X): X = [X]\n", "    if Y is None:\n", "        X, Y = [[]] * len(X), X\n", "    elif has_one_axis(Y):\n", "        Y = [Y]\n", "    if len(X) != len(Y):\n", "        X = X * len(Y)\n", "\n", "    set_figsize(figsize)\n", "    if axes is None:\n", "        axes = d2l.plt.gca()\n", "    axes.cla()\n", "    for x, y, fmt in zip(X, Y, fmts):\n", "        axes.plot(x,y,fmt) if len(x) else axes.plot(y,fmt)\n", "    set_axes(axes, xlabel, ylabel, xlim, ylim, xscale, yscale, legend)"]}, {"cell_type": "markdown", "id": "68b9ef55", "metadata": {"origin_pos": 21}, "source": ["Now we can [**plot the function $u = f(x)$ and its tangent line $y = 2x - 3$ at $x=1$**],\n", "where the coefficient $2$ is the slope of the tangent line.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1a22ce3a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:03.320345Z", "iopub.status.busy": "2023-08-18T19:31:03.319698Z", "iopub.status.idle": "2023-08-18T19:31:03.596083Z", "shell.execute_reply": "2023-08-18T19:31:03.594940Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"243.529359pt\" height=\"183.35625pt\" viewBox=\"0 0 243.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:31:03.544502</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 243.**********.35625 \n", "L 243.529359 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.8 \n", "L 235.**********.8 \n", "L 235.903125 7.2 \n", "L 40.603125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 49.480398 145.8 \n", "L 49.480398 7.2 \n", "\" clip-path=\"url(#p9df1b8d47c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mff2ee93b5f\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mff2ee93b5f\" x=\"49.480398\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(46.299148 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 110.702968 145.8 \n", "L 110.702968 7.2 \n", "\" clip-path=\"url(#p9df1b8d47c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mff2ee93b5f\" x=\"110.702968\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(107.521718 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 171.925539 145.8 \n", "L 171.925539 7.2 \n", "\" clip-path=\"url(#p9df1b8d47c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mff2ee93b5f\" x=\"171.925539\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(168.744289 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 233.148109 145.8 \n", "L 233.148109 7.2 \n", "\" clip-path=\"url(#p9df1b8d47c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mff2ee93b5f\" x=\"233.148109\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(229.966859 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- x -->\n", "     <g transform=\"translate(135.29375 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 40.603125 116.769994 \n", "L 235.903125 116.769994 \n", "\" clip-path=\"url(#p9df1b8d47c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m33f19828dd\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m33f19828dd\" x=\"40.603125\" y=\"116.769994\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(27.240625 120.569213) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 40.603125 78.886651 \n", "L 235.903125 78.886651 \n", "\" clip-path=\"url(#p9df1b8d47c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m33f19828dd\" x=\"40.603125\" y=\"78.886651\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(27.240625 82.685869) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 40.603125 41.003307 \n", "L 235.903125 41.003307 \n", "\" clip-path=\"url(#p9df1b8d47c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m33f19828dd\" x=\"40.603125\" y=\"41.003307\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(20.878125 44.802526) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- f(x) -->\n", "     <g transform=\"translate(14.798437 85.121094) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-66\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"35.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"74.21875\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"133.398438\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 49.480398 116.769994 \n", "L 55.602655 119.573361 \n", "L 61.724912 121.922129 \n", "L 67.847169 123.816296 \n", "L 73.969426 125.255863 \n", "L 80.091683 126.24083 \n", "L 86.21394 126.771197 \n", "L 92.336197 126.846963 \n", "L 98.458454 126.46813 \n", "L 104.580711 125.634696 \n", "L 110.702968 124.346663 \n", "L 116.825225 122.604029 \n", "L 122.947482 120.406795 \n", "L 129.069739 117.754961 \n", "L 135.191996 114.648527 \n", "L 141.314254 111.087492 \n", "L 147.436511 107.071858 \n", "L 153.558768 102.601624 \n", "L 159.681025 97.676789 \n", "L 165.803282 92.297354 \n", "L 171.925539 86.463319 \n", "L 178.047796 80.174684 \n", "L 184.170053 73.431449 \n", "L 190.29231 66.233614 \n", "L 196.414567 58.581179 \n", "L 202.536824 50.474143 \n", "L 208.659081 41.912508 \n", "L 214.781338 32.896272 \n", "L 220.903595 23.425436 \n", "L 227.025852 13.5 \n", "\" clip-path=\"url(#p9df1b8d47c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 49.480398 139.5 \n", "L 55.602655 137.984666 \n", "L 61.724912 136.469333 \n", "L 67.847169 134.953999 \n", "L 73.969426 133.438665 \n", "L 80.091683 131.923331 \n", "L 86.21394 130.407998 \n", "L 92.336197 128.892664 \n", "L 98.458454 127.37733 \n", "L 104.580711 125.861996 \n", "L 110.702968 124.346663 \n", "L 116.825225 122.831329 \n", "L 122.947482 121.315995 \n", "L 129.069739 119.800661 \n", "L 135.191996 118.285328 \n", "L 141.314254 116.769994 \n", "L 147.436511 115.25466 \n", "L 153.558768 113.739327 \n", "L 159.681025 112.223993 \n", "L 165.803282 110.708659 \n", "L 171.925539 109.193325 \n", "L 178.047796 107.677992 \n", "L 184.170053 106.162658 \n", "L 190.29231 104.647324 \n", "L 196.414567 103.13199 \n", "L 202.536824 101.616657 \n", "L 208.659081 100.101323 \n", "L 214.781338 98.585989 \n", "L 220.903595 97.070655 \n", "L 227.025852 95.555322 \n", "\" clip-path=\"url(#p9df1b8d47c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.8 \n", "L 40.603125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 235.**********.8 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.8 \n", "L 235.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 7.2 \n", "L 235.903125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 47.603125 44.55625 \n", "L 172.153125 44.55625 \n", "Q 174.153125 44.55625 174.153125 42.55625 \n", "L 174.153125 14.2 \n", "Q 174.153125 12.2 172.153125 12.2 \n", "L 47.603125 12.2 \n", "Q 45.603125 12.2 45.603125 14.2 \n", "L 45.603125 42.55625 \n", "Q 45.603125 44.55625 47.603125 44.55625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_17\">\n", "     <path d=\"M 49.603125 20.298438 \n", "L 59.603125 20.298438 \n", "L 69.603125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- f(x) -->\n", "     <g transform=\"translate(77.603125 23.798438) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-66\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"35.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"74.21875\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"133.398438\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_18\">\n", "     <path d=\"M 49.603125 34.976562 \n", "L 59.603125 34.976562 \n", "L 69.603125 34.976562 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- Tangent line (x=1) -->\n", "     <g transform=\"translate(77.603125 38.476562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"44.583984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"105.863281\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"169.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"232.71875\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"294.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"357.621094\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"396.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"428.617188\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.400391\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"484.183594\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"547.5625\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"609.085938\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"640.873047\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"679.886719\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"739.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"822.855469\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"886.478516\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p9df1b8d47c\">\n", "   <rect x=\"40.603125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x = np.arange(0, 3, 0.1)\n", "plot(x, [f(x), 2 * x - 3], 'x', 'f(x)', legend=['f(x)', 'Tangent line (x=1)'])"]}, {"cell_type": "markdown", "id": "ebd783d4", "metadata": {"origin_pos": 23}, "source": ["## Partial Derivatives and Gradients\n", ":label:`subsec_calculus-grad`\n", "\n", "Thus far, we have been differentiating\n", "functions of just one variable.\n", "In deep learning, we also need to work\n", "with functions of *many* variables.\n", "We briefly introduce notions of the derivative\n", "that apply to such *multivariate* functions.\n", "\n", "\n", "Let $y = f(x_1, x_2, \\ldots, x_n)$ be a function with $n$ variables. \n", "The *partial derivative* of $y$ \n", "with respect to its $i^\\textrm{th}$ parameter $x_i$ is\n", "\n", "$$ \\frac{\\partial y}{\\partial x_i} = \\lim_{h \\rightarrow 0} \\frac{f(x_1, \\ldots, x_{i-1}, x_i+h, x_{i+1}, \\ldots, x_n) - f(x_1, \\ldots, x_i, \\ldots, x_n)}{h}.$$\n", "\n", "\n", "To calculate $\\frac{\\partial y}{\\partial x_i}$, \n", "we can treat $x_1, \\ldots, x_{i-1}, x_{i+1}, \\ldots, x_n$ as constants \n", "and calculate the derivative of $y$ with respect to $x_i$.\n", "The following notational conventions for partial derivatives \n", "are all common and all mean the same thing:\n", "\n", "$$\\frac{\\partial y}{\\partial x_i} = \\frac{\\partial f}{\\partial x_i} = \\partial_{x_i} f = \\partial_i f = f_{x_i} = f_i = D_i f = D_{x_i} f.$$\n", "\n", "We can concatenate partial derivatives \n", "of a multivariate function \n", "with respect to all its variables \n", "to obtain a vector that is called\n", "the *gradient* of the function.\n", "Suppose that the input of function \n", "$f: \\mathbb{R}^n \\rightarrow \\mathbb{R}$ \n", "is an $n$-dimensional vector \n", "$\\mathbf{x} = [x_1, x_2, \\ldots, x_n]^\\top$ \n", "and the output is a scalar. \n", "The gradient of the function $f$ \n", "with respect to $\\mathbf{x}$ \n", "is a vector of $n$ partial derivatives:\n", "\n", "$$\\nabla_{\\mathbf{x}} f(\\mathbf{x}) = \\left[\\partial_{x_1} f(\\mathbf{x}), \\partial_{x_2} f(\\mathbf{x}), \\ldots\n", "\\partial_{x_n} f(\\mathbf{x})\\right]^\\top.$$ \n", "\n", "When there is no ambiguity,\n", "$\\nabla_{\\mathbf{x}} f(\\mathbf{x})$ \n", "is typically replaced \n", "by $\\nabla f(\\mathbf{x})$.\n", "The following rules come in handy \n", "for differentiating multivariate functions:\n", "\n", "* For all $\\mathbf{A} \\in \\mathbb{R}^{m \\times n}$ we have $\\nabla_{\\mathbf{x}} \\mathbf{A} \\mathbf{x} = \\mathbf{A}^\\top$ and $\\nabla_{\\mathbf{x}} \\mathbf{x}^\\top \\mathbf{A}  = \\mathbf{A}$.\n", "* For square matrices $\\mathbf{A} \\in \\mathbb{R}^{n \\times n}$ we have that $\\nabla_{\\mathbf{x}} \\mathbf{x}^\\top \\mathbf{A} \\mathbf{x}  = (\\mathbf{A} + \\mathbf{A}^\\top)\\mathbf{x}$ and in particular\n", "$\\nabla_{\\mathbf{x}} \\|\\mathbf{x} \\|^2 = \\nabla_{\\mathbf{x}} \\mathbf{x}^\\top \\mathbf{x} = 2\\mathbf{x}$.\n", "\n", "Similarly, for any matrix $\\mathbf{X}$, \n", "we have $\\nabla_{\\mathbf{X}} \\|\\mathbf{X} \\|_\\textrm{F}^2 = 2\\mathbf{X}$. \n", "\n", "\n", "\n", "## Chain Rule\n", "\n", "In deep learning, the gradients of concern\n", "are often difficult to calculate\n", "because we are working with \n", "deeply nested functions \n", "(of functions (of functions...)).\n", "Fortunately, the *chain rule* takes care of this. \n", "Returning to functions of a single variable,\n", "suppose that $y = f(g(x))$\n", "and that the underlying functions \n", "$y=f(u)$ and $u=g(x)$ \n", "are both differentiable.\n", "The chain rule states that \n", "\n", "\n", "$$\\frac{dy}{dx} = \\frac{dy}{du} \\frac{du}{dx}.$$\n", "\n", "\n", "\n", "Turning back to multivariate functions,\n", "suppose that $y = f(\\mathbf{u})$ has variables\n", "$u_1, u_2, \\ldots, u_m$, \n", "where each $u_i = g_i(\\mathbf{x})$ \n", "has variables $x_1, x_2, \\ldots, x_n$,\n", "i.e.,  $\\mathbf{u} = g(\\mathbf{x})$.\n", "Then the chain rule states that\n", "\n", "$$\\frac{\\partial y}{\\partial x_{i}} = \\frac{\\partial y}{\\partial u_{1}} \\frac{\\partial u_{1}}{\\partial x_{i}} + \\frac{\\partial y}{\\partial u_{2}} \\frac{\\partial u_{2}}{\\partial x_{i}} + \\ldots + \\frac{\\partial y}{\\partial u_{m}} \\frac{\\partial u_{m}}{\\partial x_{i}} \\ \\textrm{ and so } \\ \\nabla_{\\mathbf{x}} y =  \\mathbf{A} \\nabla_{\\mathbf{u}} y,$$\n", "\n", "where $\\mathbf{A} \\in \\mathbb{R}^{n \\times m}$ is a *matrix*\n", "that contains the derivative of vector $\\mathbf{u}$\n", "with respect to vector $\\mathbf{x}$.\n", "Thus, evaluating the gradient requires \n", "computing a vector--matrix product. \n", "This is one of the key reasons why linear algebra \n", "is such an integral building block \n", "in building deep learning systems. \n", "\n", "\n", "\n", "## Discussion\n", "\n", "While we have just scratched the surface of a deep topic,\n", "a number of concepts already come into focus: \n", "first, the composition rules for differentiation\n", "can be applied routinely, enabling\n", "us to compute gradients *automatically*.\n", "This task requires no creativity and thus \n", "we can focus our cognitive powers elsewhere.\n", "Second, computing the derivatives of vector-valued functions \n", "requires us to multiply matrices as we trace \n", "the dependency graph of variables from output to input. \n", "In particular, this graph is traversed in a *forward* direction \n", "when we evaluate a function \n", "and in a *backwards* direction \n", "when we compute gradients. \n", "Later chapters will formally introduce backpropagation,\n", "a computational procedure for applying the chain rule.\n", "\n", "From the viewpoint of optimization, gradients allow us \n", "to determine how to move the parameters of a model\n", "in order to lower the loss,\n", "and each step of the optimization algorithms used \n", "throughout this book will require calculating the gradient.\n", "\n", "## Exercises\n", "\n", "1. So far we took the rules for derivatives for granted. \n", "   Using the definition and limits prove the properties \n", "   for (i) $f(x) = c$, (ii) $f(x) = x^n$, (iii) $f(x) = e^x$ and (iv) $f(x) = \\log x$.\n", "1. In the same vein, prove the product, sum, and quotient rule from first principles. \n", "1. Prove that the constant multiple rule follows as a special case of the product rule. \n", "1. Calculate the derivative of $f(x) = x^x$. \n", "1. What does it mean that $f'(x) = 0$ for some $x$? \n", "   Give an example of a function $f$ \n", "   and a location $x$ for which this might hold. \n", "1. Plot the function $y = f(x) = x^3 - \\frac{1}{x}$ \n", "   and plot its tangent line at $x = 1$.\n", "1. Find the gradient of the function \n", "   $f(\\mathbf{x}) = 3x_1^2 + 5e^{x_2}$.\n", "1. What is the gradient of the function \n", "   $f(\\mathbf{x}) = \\|\\mathbf{x}\\|_2$? What happens for $\\mathbf{x} = \\mathbf{0}$?\n", "1. Can you write out the chain rule for the case \n", "   where $u = f(x, y, z)$ and $x = x(a, b)$, $y = y(a, b)$, and $z = z(a, b)$?\n", "1. Given a function $f(x)$ that is invertible, \n", "   compute the derivative of its inverse $f^{-1}(x)$. \n", "   Here we have that $f^{-1}(f(x)) = x$ and conversely $f(f^{-1}(y)) = y$. \n", "   Hint: use these properties in your derivation.\n"]}, {"cell_type": "markdown", "id": "e6a8d8e9", "metadata": {"origin_pos": 25, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/33)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "7cac0fb8", "metadata": {"origin_pos": 0}, "source": ["#  Preliminaries\n", ":label:`chap_preliminaries`\n", "\n", "To prepare for your dive into deep learning,\n", "you will need a few survival skills:\n", "(i) techniques for storing and manipulating data;\n", "(ii) libraries for ingesting \n", "and preprocessing data from a variety of sources;\n", "(iii) knowledge of the basic linear algebraic operations\n", "that we apply to high-dimensional data elements;\n", "(iv) just enough calculus to determine\n", "which direction to adjust each parameter\n", "in order to decrease the loss function;\n", "(v) the ability to automatically compute derivatives\n", "so that you can forget much of \n", "the calculus you just learned;\n", "(vi) some basic fluency in probability,\n", "our primary language for reasoning under uncertainty;\n", "and (vii) some aptitude for finding answers \n", "in the official documentation when you get stuck.\n", "\n", "In short, this chapter provides a rapid introduction \n", "to the basics that you will need to follow \n", "*most* of the technical content in this book.\n", "\n", ":begin_tab:toc\n", " - [ndarray](ndarray.ipynb)\n", " - [pandas](pandas.ipynb)\n", " - [linear-algebra](linear-algebra.ipynb)\n", " - [calculus](calculus.ipynb)\n", " - [autograd](autograd.ipynb)\n", " - [probability](probability.ipynb)\n", " - [lookup-api](lookup-api.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
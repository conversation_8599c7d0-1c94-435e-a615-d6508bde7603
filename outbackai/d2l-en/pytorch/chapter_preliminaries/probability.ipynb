{"cells": [{"cell_type": "markdown", "id": "0ad19fa4", "metadata": {"origin_pos": 1}, "source": ["# Probability and Statistics\n", ":label:`sec_prob`\n", "\n", "One way or another,\n", "machine learning is all about uncertainty.\n", "In supervised learning, we want to predict\n", "something unknown (the *target*)\n", "given something known (the *features*).\n", "Depending on our objective,\n", "we might attempt to predict\n", "the most likely value of the target.\n", "Or we might predict the value with the smallest\n", "expected distance from the target.\n", "And sometimes we wish not only\n", "to predict a specific value\n", "but to *quantify our uncertainty*.\n", "For example, given some features\n", "describing a patient,\n", "we might want to know *how likely* they are\n", "to suffer a heart attack in the next year.\n", "In unsupervised learning,\n", "we often care about uncertainty.\n", "To determine whether a set of measurements are anomalous,\n", "it helps to know how likely one is\n", "to observe values in a population of interest.\n", "Furthermore, in reinforcement learning,\n", "we wish to develop agents\n", "that act intelligently in various environments.\n", "This requires reasoning about\n", "how an environment might be expected to change\n", "and what rewards one might expect to encounter\n", "in response to each of the available actions.\n", "\n", "*Probability* is the mathematical field\n", "concerned with reasoning under uncertainty.\n", "Given a probabilistic model of some process,\n", "we can reason about the likelihood of various events.\n", "The use of probabilities to describe\n", "the frequencies of repeatable events\n", "(like coin tosses)\n", "is fairly uncontroversial.\n", "In fact, *frequentist* scholars adhere\n", "to an interpretation of probability\n", "that applies *only* to such repeatable events.\n", "By contrast *Bayesian* scholars\n", "use the language of probability more broadly\n", "to formalize reasoning under uncertainty.\n", "Bayesian probability is characterized\n", "by two unique features:\n", "(i) assigning degrees of belief\n", "to non-repeatable events,\n", "e.g., what is the *probability*\n", "that a dam will collapse?;\n", "and (ii) subjectivity. While Bayesian\n", "probability provides unambiguous rules\n", "for how one should update their beliefs\n", "in light of new evidence,\n", "it allows for different individuals\n", "to start off with different *prior* beliefs.\n", "*Statistics* helps us to reason backwards,\n", "starting off with collection and organization of data\n", "and backing out to what inferences\n", "we might draw about the process\n", "that generated the data.\n", "Whenever we analyze a dataset, hunting for patterns\n", "that we hope might characterize a broader population,\n", "we are employing statistical thinking.\n", "Many courses, majors, theses, careers, departments,\n", "companies, and institutions have been devoted\n", "to the study of probability and statistics.\n", "While this section only scratches the surface,\n", "we will provide the foundation\n", "that you need to begin building models.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "15d26295", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:41.010215Z", "iopub.status.busy": "2023-08-18T19:35:41.009884Z", "iopub.status.idle": "2023-08-18T19:35:44.240517Z", "shell.execute_reply": "2023-08-18T19:35:44.239244Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import random\n", "import torch\n", "from torch.distributions.multinomial import Multinomial\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "390fa88a", "metadata": {"origin_pos": 6}, "source": ["## A Simple Example: Tossing Coins\n", "\n", "Imagine that we plan to toss a coin\n", "and want to quantify how likely\n", "we are to see heads (vs. tails).\n", "If the coin is *fair*,\n", "then both outcomes\n", "(heads and tails),\n", "are equally likely.\n", "Moreover if we plan to toss the coin $n$ times\n", "then the fraction of heads\n", "that we *expect* to see\n", "should exactly match\n", "the *expected* fraction of tails.\n", "One intuitive way to see this\n", "is by symmetry:\n", "for every possible outcome\n", "with $n_\\textrm{h}$ heads and $n_\\textrm{t} = (n - n_\\textrm{h})$ tails,\n", "there is an equally likely outcome\n", "with $n_\\textrm{t}$ heads and $n_\\textrm{h}$ tails.\n", "Note that this is only possible\n", "if on average we expect to see\n", "$1/2$ of tosses come up heads\n", "and $1/2$ come up tails.\n", "Of course, if you conduct this experiment\n", "many times with $n=1000000$ tosses each,\n", "you might never see a trial\n", "where $n_\\textrm{h} = n_\\textrm{t}$ exactly.\n", "\n", "\n", "Formally, the quantity $1/2$ is called a *probability*\n", "and here it captures the certainty with which\n", "any given toss will come up heads.\n", "Probabilities assign scores between $0$ and $1$\n", "to outcomes of interest, called *events*.\n", "Here the event of interest is $\\textrm{heads}$\n", "and we denote the corresponding probability $P(\\textrm{heads})$.\n", "A probability of $1$ indicates absolute certainty\n", "(imagine a trick coin where both sides were heads)\n", "and a probability of $0$ indicates impossibility\n", "(e.g., if both sides were tails).\n", "The frequencies $n_\\textrm{h}/n$ and $n_\\textrm{t}/n$ are not probabilities\n", "but rather *statistics*.\n", "Probabilities are *theoretical* quantities\n", "that underly the data generating process.\n", "Here, the probability $1/2$\n", "is a property of the coin itself.\n", "By contrast, statistics are *empirical* quantities\n", "that are computed as functions of the observed data.\n", "Our interests in probabilistic and statistical quantities\n", "are inextricably intertwined.\n", "We often design special statistics called *estimators*\n", "that, given a dataset, produce *estimates*\n", "of model parameters such as probabilities.\n", "Moreover, when those estimators satisfy\n", "a nice property called *consistency*,\n", "our estimates will converge\n", "to the corresponding probability.\n", "In turn, these inferred probabilities\n", "tell about the likely statistical properties\n", "of data from the same population\n", "that we might encounter in the future.\n", "\n", "Suppose that we stumbled upon a real coin\n", "for which we did not know\n", "the true $P(\\textrm{heads})$.\n", "To investigate this quantity\n", "with statistical methods,\n", "we need to (i) collect some data;\n", "and (ii) design an estimator.\n", "Data acquisition here is easy;\n", "we can toss the coin many times\n", "and record all the outcomes.\n", "Formally, drawing realizations\n", "from some underlying random process\n", "is called *sampling*.\n", "As you might have guessed,\n", "one natural estimator\n", "is the ratio of\n", "the number of observed *heads*\n", "to the total number of tosses.\n", "\n", "Now, suppose that the coin was in fact fair,\n", "i.e., $P(\\textrm{heads}) = 0.5$.\n", "To simulate tosses of a fair coin,\n", "we can invoke any random number generator.\n", "There are some easy ways to draw samples\n", "of an event with probability $0.5$.\n", "For example Python's `random.random`\n", "yields numbers in the interval $[0,1]$\n", "where the probability of lying\n", "in any sub-interval $[a, b] \\subset [0,1]$\n", "is equal to $b-a$.\n", "Thus we can get out `0` and `1` with probability `0.5` each\n", "by testing whether the returned float number is greater than `0.5`:\n"]}, {"cell_type": "code", "execution_count": 2, "id": "3a500e66", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:44.245216Z", "iopub.status.busy": "2023-08-18T19:35:44.244448Z", "iopub.status.idle": "2023-08-18T19:35:44.250559Z", "shell.execute_reply": "2023-08-18T19:35:44.249469Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["heads, tails:  [44, 56]\n"]}], "source": ["num_tosses = 100\n", "heads = sum([random.random() > 0.5 for _ in range(num_tosses)])\n", "tails = num_tosses - heads\n", "print(\"heads, tails: \", [heads, tails])"]}, {"cell_type": "markdown", "id": "096c1837", "metadata": {"origin_pos": 8}, "source": ["More generally, we can simulate multiple draws\n", "from any variable with a finite number\n", "of possible outcomes\n", "(like the toss of a coin or roll of a die)\n", "by calling the multinomial function,\n", "setting the first argument\n", "to the number of draws\n", "and the second as a list of probabilities\n", "associated with each of the possible outcomes.\n", "To simulate ten tosses of a fair coin,\n", "we assign probability vector `[0.5, 0.5]`,\n", "interpreting index 0 as heads\n", "and index 1 as tails.\n", "The function returns a vector\n", "with length equal to the number\n", "of possible outcomes (here, 2),\n", "where the first component tells us\n", "the number of occurrences of heads\n", "and the second component tells us\n", "the number of occurrences of tails.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "b70ba754", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:44.256289Z", "iopub.status.busy": "2023-08-18T19:35:44.255841Z", "iopub.status.idle": "2023-08-18T19:35:44.292323Z", "shell.execute_reply": "2023-08-18T19:35:44.291255Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([50., 50.])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["fair_probs = torch.tensor([0.5, 0.5])\n", "Multinomial(100, fair_probs).sample()"]}, {"cell_type": "markdown", "id": "ca81b0bc", "metadata": {"origin_pos": 13}, "source": ["Each time you run this sampling process,\n", "you will receive a new random value\n", "that may differ from the previous outcome.\n", "Dividing by the number of tosses\n", "gives us the *frequency*\n", "of each outcome in our data.\n", "Note that these frequencies,\n", "just like the probabilities\n", "that they are intended\n", "to estimate, sum to $1$.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "d4157453", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:44.297194Z", "iopub.status.busy": "2023-08-18T19:35:44.296806Z", "iopub.status.idle": "2023-08-18T19:35:44.309679Z", "shell.execute_reply": "2023-08-18T19:35:44.308709Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0.4800, 0.5200])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["Multinomial(100, fair_probs).sample() / 100"]}, {"cell_type": "markdown", "id": "5135ef92", "metadata": {"origin_pos": 18}, "source": ["Here, even though our simulated coin is fair\n", "(we ourselves set the probabilities `[0.5, 0.5]`),\n", "the counts of heads and tails may not be identical.\n", "That is because we only drew a relatively small number of samples.\n", "If we did not implement the simulation ourselves,\n", "and only saw the outcome,\n", "how would we know if the coin were slightly unfair\n", "or if the possible deviation from $1/2$ was\n", "just an artifact of the small sample size?\n", "Let's see what happens when we simulate 10,000 tosses.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "3b639145", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:44.313908Z", "iopub.status.busy": "2023-08-18T19:35:44.313549Z", "iopub.status.idle": "2023-08-18T19:35:44.325094Z", "shell.execute_reply": "2023-08-18T19:35:44.324133Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0.4966, 0.5034])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["counts = Multinomial(10000, fair_probs).sample()\n", "counts / 10000"]}, {"cell_type": "markdown", "id": "8725b688", "metadata": {"origin_pos": 23}, "source": ["In general, for averages of repeated events (like coin tosses),\n", "as the number of repetitions grows,\n", "our estimates are guaranteed to converge\n", "to the true underlying probabilities.\n", "The mathematical formulation of this phenomenon\n", "is called the *law of large numbers*\n", "and the *central limit theorem*\n", "tells us that in many situations,\n", "as the sample size $n$ grows,\n", "these errors should go down\n", "at a rate of $(1/\\sqrt{n})$.\n", "Let's get some more intuition by studying\n", "how our estimate evolves as we grow\n", "the number of tosses from 1 to 10,000.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "fda7f94d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:44.329246Z", "iopub.status.busy": "2023-08-18T19:35:44.328647Z", "iopub.status.idle": "2023-08-18T19:35:44.675913Z", "shell.execute_reply": "2023-08-18T19:35:44.674711Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"306.596693pt\" height=\"238.79625pt\" viewBox=\"0 0 306.**********.79625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:35:44.589130</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 238.79625 \n", "L 306.**********.79625 \n", "L 306.596693 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 43.78125 201.24 \n", "L 294.88125 201.24 \n", "L 294.88125 7.2 \n", "L 43.78125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mbc75ac1e93\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mbc75ac1e93\" x=\"55.194886\" y=\"201.24\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(52.013636 215.838437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mbc75ac1e93\" x=\"100.853998\" y=\"201.24\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2000 -->\n", "      <g transform=\"translate(88.128998 215.838437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mbc75ac1e93\" x=\"146.513109\" y=\"201.24\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4000 -->\n", "      <g transform=\"translate(133.788109 215.838437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mbc75ac1e93\" x=\"192.17222\" y=\"201.24\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6000 -->\n", "      <g transform=\"translate(179.44722 215.838437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mbc75ac1e93\" x=\"237.831332\" y=\"201.24\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8000 -->\n", "      <g transform=\"translate(225.106332 215.838437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mbc75ac1e93\" x=\"283.490443\" y=\"201.24\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10000 -->\n", "      <g transform=\"translate(267.584193 215.838437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"254.492188\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- Samples -->\n", "     <g transform=\"translate(147.978125 229.516562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"222.167969\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"285.644531\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"374.951172\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m67976ff756\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m67976ff756\" x=\"43.78125\" y=\"192.42\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(20.878125 196.219219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m67976ff756\" x=\"43.78125\" y=\"157.14\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(20.878125 160.939219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m67976ff756\" x=\"43.78125\" y=\"121.86\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(20.878125 125.659219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m67976ff756\" x=\"43.78125\" y=\"86.58\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(20.878125 90.379219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m67976ff756\" x=\"43.78125\" y=\"51.3\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(20.878125 55.099219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m67976ff756\" x=\"43.78125\" y=\"16.02\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(20.878125 19.819219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- Estimated probability -->\n", "     <g transform=\"translate(14.798438 157.743437) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-45\" d=\"M 628 4666 \n", "L 3578 4666 \n", "L 3578 4134 \n", "L 1259 4134 \n", "L 1259 2753 \n", "L 3481 2753 \n", "L 3481 2222 \n", "L 1259 2222 \n", "L 1259 531 \n", "L 3634 531 \n", "L 3634 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-45\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"63.183594\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"115.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"154.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"182.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"279.6875\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"340.966797\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"380.175781\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"441.699219\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"505.175781\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"536.962891\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"600.439453\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"639.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"700.484375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"763.960938\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"825.240234\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"888.716797\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"916.5\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"944.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"972.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"1011.275391\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 55.194886 192.42 \n", "L 55.331864 66.419997 \n", "L 55.423182 48.092728 \n", "L 55.446011 60.12 \n", "L 55.582989 94.419995 \n", "L 55.605818 90.293685 \n", "L 55.628648 86.579996 \n", "L 55.674307 96.201815 \n", "L 55.742796 107.748002 \n", "L 55.788455 100.953335 \n", "L 55.811284 97.920003 \n", "L 55.856943 104.22 \n", "L 55.879773 107.065164 \n", "L 55.971091 106.74 \n", "L 56.03958 108.862103 \n", "L 56.108069 102.068783 \n", "L 56.130898 104.22 \n", "L 56.176557 100.210913 \n", "L 56.199387 102.259995 \n", "L 56.267875 104.22 \n", "L 56.359194 97.43538 \n", "L 56.382023 99.227546 \n", "L 56.427682 96.201815 \n", "L 56.450512 97.920003 \n", "L 56.519001 93.755589 \n", "L 56.56466 96.99049 \n", "L 56.610319 100.019995 \n", "L 56.655978 97.43538 \n", "L 56.701637 95.005072 \n", "L 56.770126 96.659995 \n", "L 56.929933 100.783635 \n", "L 56.952762 99.696923 \n", "L 57.021251 100.953335 \n", "L 57.04408 102.068783 \n", "L 57.089739 100.019995 \n", "L 57.112569 101.107054 \n", "L 57.181058 102.215456 \n", "L 57.272376 98.467827 \n", "L 57.386524 99.673613 \n", "L 57.409353 98.820003 \n", "L 57.455012 100.692003 \n", "L 57.477842 99.853659 \n", "L 57.546331 102.52385 \n", "L 57.59199 100.891701 \n", "L 57.614819 100.098505 \n", "L 57.660478 101.792478 \n", "L 57.865944 105.714917 \n", "L 58.002922 104.22 \n", "L 58.299706 110.014161 \n", "L 58.345365 109.930794 \n", "L 58.459513 111.569998 \n", "L 58.505172 110.261094 \n", "L 58.573661 110.731407 \n", "L 58.642149 112.343685 \n", "L 58.710638 111.617418 \n", "L 58.893274 109.089937 \n", "L 58.938933 110.1 \n", "L 58.984593 108.973292 \n", "L 59.007422 108.42 \n", "L 59.053081 109.408235 \n", "L 59.167229 110.771998 \n", "L 59.212888 110.697967 \n", "L 59.349865 109.521642 \n", "L 59.441184 110.351553 \n", "L 59.395525 109.464323 \n", "L 59.486843 110.286665 \n", "L 59.509672 109.790528 \n", "L 59.555331 110.651252 \n", "L 59.62382 111.90923 \n", "L 59.692309 111.347275 \n", "L 59.806457 110.737243 \n", "L 60.034752 114.572114 \n", "L 60.080411 113.655349 \n", "L 60.1489 112.311742 \n", "L 60.194559 113.040002 \n", "L 60.331536 114.366902 \n", "L 60.377196 114.277893 \n", "L 60.559832 112.442035 \n", "L 60.582662 112.779496 \n", "L 60.628321 111.969792 \n", "L 60.65115 111.569998 \n", "L 60.696809 112.238179 \n", "L 60.788128 113.541953 \n", "L 60.856616 113.075419 \n", "L 61.039253 110.740623 \n", "L 61.062082 111.057207 \n", "L 61.107741 111.683079 \n", "L 61.17623 111.262588 \n", "L 61.221889 110.543775 \n", "L 61.267548 111.157077 \n", "L 61.404526 112.296923 \n", "L 61.450185 112.238179 \n", "L 61.518673 111.83439 \n", "L 61.541503 112.123227 \n", "L 61.655651 112.915776 \n", "L 61.67848 112.575787 \n", "L 61.861117 111.14355 \n", "L 61.883946 111.420002 \n", "L 61.929605 110.775404 \n", "L 62.066583 110.06106 \n", "L 62.112242 110.602897 \n", "L 62.18073 110.253224 \n", "L 62.272049 109.608426 \n", "L 62.294878 109.873848 \n", "L 62.386196 109.802278 \n", "L 62.477515 110.834999 \n", "L 62.568833 109.664446 \n", "L 62.637322 109.884221 \n", "L 62.751469 110.595905 \n", "L 62.774299 110.311894 \n", "L 62.842788 110.520002 \n", "L 62.888447 109.960828 \n", "L 62.956935 109.651672 \n", "L 63.025424 110.373486 \n", "L 63.071083 109.82809 \n", "L 63.139572 110.032609 \n", "L 63.185231 110.502049 \n", "L 63.23089 109.966742 \n", "L 63.25372 110.199659 \n", "L 63.390697 109.609998 \n", "L 63.413526 109.839388 \n", "L 63.459186 109.322481 \n", "L 63.596163 108.761461 \n", "L 63.618992 108.98757 \n", "L 63.687481 108.712759 \n", "L 63.778799 108.197187 \n", "L 63.801629 108.42 \n", "L 63.847288 108.862103 \n", "L 63.892947 108.376019 \n", "L 64.098413 107.15248 \n", "L 64.166902 106.906293 \n", "L 64.303879 108.189001 \n", "L 64.577834 106.360777 \n", "L 64.646322 106.13277 \n", "L 64.714811 106.75206 \n", "L 64.851788 106.300188 \n", "L 65.125743 107.861283 \n", "L 65.217061 107.427275 \n", "L 65.239891 107.620001 \n", "L 65.26272 107.811855 \n", "L 65.30838 107.398376 \n", "L 65.354039 107.779639 \n", "L 65.491016 107.342125 \n", "L 65.627993 108.071531 \n", "L 65.673652 108.054782 \n", "L 65.696482 107.855142 \n", "L 65.742141 108.220434 \n", "L 65.764971 108.401894 \n", "L 65.81063 108.005407 \n", "L 65.833459 107.808438 \n", "L 65.879118 108.169256 \n", "L 65.947607 108.704747 \n", "L 65.993266 108.313669 \n", "L 66.107414 107.718541 \n", "L 66.130244 107.895002 \n", "L 66.175903 108.245726 \n", "L 66.221562 107.864626 \n", "L 66.31288 107.111805 \n", "L 66.381369 107.273767 \n", "L 66.678153 108.769998 \n", "L 66.792301 108.552022 \n", "L 66.860789 108.354375 \n", "L 67.020596 109.488206 \n", "L 67.157574 108.755999 \n", "L 67.180403 108.915058 \n", "L 67.248892 109.388621 \n", "L 67.31738 109.193686 \n", "L 67.591335 107.462645 \n", "L 67.614165 107.618535 \n", "L 67.751142 108.54196 \n", "L 67.819631 108.359348 \n", "L 67.910949 107.697418 \n", "L 67.956608 108.000002 \n", "L 67.979438 108.150482 \n", "L 68.047926 107.973191 \n", "L 68.093585 107.648269 \n", "L 68.139244 107.946758 \n", "L 68.162074 108.09522 \n", "L 68.207733 107.772716 \n", "L 68.253392 108.068166 \n", "L 68.299051 107.748002 \n", "L 68.34471 108.041492 \n", "L 68.413199 108.477933 \n", "L 68.481688 108.304732 \n", "L 68.550176 108.434336 \n", "L 68.641495 107.807796 \n", "L 68.709983 107.640909 \n", "L 68.801302 108.208947 \n", "L 68.938279 107.876717 \n", "L 68.961108 108.016689 \n", "L 69.006768 107.713068 \n", "L 69.029597 107.852619 \n", "L 69.120915 107.54013 \n", "L 69.143745 107.678823 \n", "L 69.280722 108.501554 \n", "L 69.326381 108.203227 \n", "L 69.349211 108.054782 \n", "L 69.39487 108.325619 \n", "L 69.4177 108.460385 \n", "L 69.463359 108.16505 \n", "L 69.486188 108.299427 \n", "L 69.554677 108.14 \n", "L 69.577506 108.273568 \n", "L 69.760143 108.77493 \n", "L 69.851461 108.197918 \n", "L 69.89712 108.459071 \n", "L 69.988438 108.704747 \n", "L 69.942779 108.445965 \n", "L 70.011268 108.562153 \n", "L 70.239564 107.694547 \n", "L 70.262393 107.822721 \n", "L 70.308052 107.545792 \n", "L 70.330882 107.40795 \n", "L 70.376541 107.663241 \n", "L 70.44503 108.043316 \n", "L 70.490689 107.76903 \n", "L 70.513518 107.632497 \n", "L 70.559177 107.884093 \n", "L 70.582007 107.748002 \n", "L 70.604836 107.873253 \n", "L 70.650496 107.602301 \n", "L 70.764643 107.448403 \n", "L 70.855962 107.943147 \n", "L 70.92445 107.799133 \n", "L 70.94728 107.666311 \n", "L 70.992939 107.91091 \n", "L 71.015768 107.778504 \n", "L 71.107087 108.010832 \n", "L 71.061428 107.768273 \n", "L 71.152746 108.000002 \n", "L 71.335382 107.20983 \n", "L 71.358212 107.330013 \n", "L 71.47236 107.678823 \n", "L 71.403871 107.321265 \n", "L 71.495189 107.550628 \n", "L 71.518019 107.422796 \n", "L 71.563678 107.659556 \n", "L 71.586507 107.532102 \n", "L 71.677826 107.757759 \n", "L 71.632166 107.522913 \n", "L 71.700655 107.631052 \n", "L 71.723485 107.504691 \n", "L 71.769144 107.738292 \n", "L 71.883292 108.075737 \n", "L 71.928951 108.065233 \n", "L 72.088758 107.671821 \n", "L 72.294224 108.218399 \n", "L 72.431201 107.953335 \n", "L 72.47686 108.1762 \n", "L 72.522519 107.933684 \n", "L 72.591008 107.803486 \n", "L 72.613837 107.914243 \n", "L 72.705156 108.124686 \n", "L 72.659496 107.904596 \n", "L 72.750815 108.114545 \n", "L 72.933451 107.621026 \n", "L 73.138917 108.366635 \n", "L 73.161747 108.249443 \n", "L 73.390042 107.535787 \n", "L 73.412872 107.642029 \n", "L 73.458531 107.413259 \n", "L 73.52702 107.07224 \n", "L 73.595508 107.170927 \n", "L 73.732486 107.583102 \n", "L 73.755315 107.470615 \n", "L 73.823804 107.566636 \n", "L 73.869463 107.343077 \n", "L 74.052099 107.73947 \n", "L 74.211906 107.392662 \n", "L 74.257565 107.596076 \n", "L 74.303224 107.377516 \n", "L 74.371713 107.47113 \n", "L 74.417372 107.254163 \n", "L 74.577179 107.540471 \n", "L 74.736986 107.204599 \n", "L 74.919622 107.584857 \n", "L 75.079429 107.254405 \n", "L 75.239236 107.531261 \n", "L 75.330554 107.316491 \n", "L 75.353384 107.41276 \n", "L 75.513191 107.684648 \n", "L 75.53602 107.581882 \n", "L 75.58168 107.77168 \n", "L 75.832805 108.410716 \n", "L 76.0611 107.786558 \n", "L 76.243737 108.328994 \n", "L 76.266566 108.229092 \n", "L 76.289396 108.129407 \n", "L 76.335055 108.311261 \n", "L 76.631839 109.099147 \n", "L 76.654669 109.000234 \n", "L 76.700328 109.177158 \n", "L 76.814476 109.616202 \n", "L 76.882964 109.506433 \n", "L 76.905794 109.408235 \n", "L 76.951453 109.582262 \n", "L 77.019942 109.657617 \n", "L 77.042771 109.559877 \n", "L 77.11126 109.451428 \n", "L 77.134089 109.537671 \n", "L 77.271067 109.686942 \n", "L 77.339555 109.579218 \n", "L 77.362385 109.664446 \n", "L 77.522192 109.895792 \n", "L 77.59068 109.968266 \n", "L 77.704828 109.492339 \n", "L 77.750487 109.660041 \n", "L 77.796146 109.471058 \n", "L 77.818976 109.376855 \n", "L 77.864635 109.543943 \n", "L 77.933124 109.439457 \n", "L 77.978783 109.605588 \n", "L 78.070101 109.408235 \n", "L 78.092931 109.490914 \n", "L 78.13859 109.655783 \n", "L 78.207078 109.552212 \n", "L 78.458204 108.889414 \n", "L 78.526692 108.961936 \n", "L 78.549522 108.871172 \n", "L 78.572351 108.780586 \n", "L 78.61801 108.943468 \n", "L 78.754988 109.086798 \n", "L 78.891965 108.888915 \n", "L 79.028942 109.199711 \n", "L 79.051772 109.110629 \n", "L 79.120261 109.012562 \n", "L 79.14309 109.091998 \n", "L 79.348556 109.633598 \n", "L 79.371386 109.545283 \n", "L 79.485534 109.271833 \n", "L 79.508363 109.349834 \n", "L 79.576852 109.41794 \n", "L 79.599681 109.330656 \n", "L 79.827977 108.793335 \n", "L 79.964954 108.930499 \n", "L 80.079102 108.828063 \n", "L 80.124761 108.981025 \n", "L 80.17042 108.811235 \n", "L 80.19325 108.726569 \n", "L 80.238909 108.879016 \n", "L 80.261739 108.955034 \n", "L 80.307398 108.786211 \n", "L 80.330227 108.862103 \n", "L 80.535693 108.42756 \n", "L 80.786818 108.936576 \n", "L 80.969455 108.590972 \n", "L 81.174921 108.943615 \n", "L 81.22058 108.780739 \n", "L 81.289069 108.845875 \n", "L 81.334728 108.991728 \n", "L 81.380387 108.829756 \n", "L 81.403216 108.74898 \n", "L 81.448875 108.894372 \n", "L 81.654341 109.238277 \n", "L 81.791319 109.061165 \n", "L 81.814148 109.132598 \n", "L 81.859807 108.973292 \n", "L 82.065273 108.562615 \n", "L 82.110933 108.704747 \n", "L 82.156592 108.547916 \n", "L 82.22508 108.611391 \n", "L 82.270739 108.455385 \n", "L 82.339228 108.518823 \n", "L 82.362058 108.44116 \n", "L 82.567524 107.895002 \n", "L 82.590353 107.965379 \n", "L 82.613183 108.03564 \n", "L 82.658842 107.882789 \n", "L 82.887137 107.126094 \n", "L 82.955626 107.191403 \n", "L 83.092603 107.321065 \n", "L 83.229581 107.162394 \n", "L 83.366558 107.290931 \n", "L 83.480706 107.065164 \n", "L 83.503535 107.133943 \n", "L 83.663342 107.329613 \n", "L 83.686172 107.256508 \n", "L 83.731831 107.392662 \n", "L 83.754661 107.319683 \n", "L 83.914467 107.512614 \n", "L 84.074274 107.285401 \n", "L 84.097104 107.352597 \n", "L 84.142763 107.208652 \n", "L 84.165593 107.275749 \n", "L 84.188422 107.203952 \n", "L 84.234081 107.33783 \n", "L 84.348229 107.394649 \n", "L 84.439547 107.247145 \n", "L 84.462377 107.313532 \n", "L 84.667843 107.633312 \n", "L 84.781991 107.552153 \n", "L 84.82765 107.682819 \n", "L 84.873309 107.541907 \n", "L 84.941797 107.601901 \n", "L 85.010286 107.391689 \n", "L 85.124434 107.581283 \n", "L 85.147263 107.511547 \n", "L 85.192923 107.372396 \n", "L 85.238582 107.501548 \n", "L 85.558195 108.129691 \n", "L 85.695173 107.980209 \n", "L 85.786491 108.100534 \n", "L 85.809321 108.031924 \n", "L 85.969127 107.815998 \n", "L 86.014787 107.941243 \n", "L 86.060446 107.805368 \n", "L 86.106105 107.669891 \n", "L 86.174593 107.727215 \n", "L 86.243082 107.784292 \n", "L 86.265912 107.716916 \n", "L 86.448548 107.31022 \n", "L 86.471378 107.372296 \n", "L 86.494207 107.434288 \n", "L 86.539866 107.301225 \n", "L 86.699673 106.838539 \n", "L 86.745332 106.962298 \n", "L 86.768162 107.024048 \n", "L 86.813821 106.892725 \n", "L 86.973628 106.689347 \n", "L 87.064946 106.808548 \n", "L 87.087776 106.743606 \n", "L 87.110605 106.678754 \n", "L 87.156264 106.801156 \n", "L 87.201923 106.923206 \n", "L 87.247583 106.793807 \n", "L 87.338901 106.661306 \n", "L 87.36173 106.722126 \n", "L 87.407389 106.843513 \n", "L 87.475878 106.775617 \n", "L 87.498708 106.711527 \n", "L 87.544367 106.83241 \n", "L 87.681344 106.94528 \n", "L 87.818321 106.810487 \n", "L 87.955299 107.04535 \n", "L 87.978128 106.982007 \n", "L 88.115106 106.848276 \n", "L 88.206424 106.962918 \n", "L 88.229253 106.900111 \n", "L 88.297742 106.955353 \n", "L 88.38906 106.705361 \n", "L 88.457549 106.881727 \n", "L 88.526038 106.815892 \n", "L 88.640185 106.746876 \n", "L 88.845651 107.03044 \n", "L 88.868481 106.96878 \n", "L 88.91414 107.08441 \n", "L 89.051117 107.310567 \n", "L 89.073947 107.24909 \n", "L 89.119606 107.126388 \n", "L 89.165265 107.240952 \n", "L 89.347902 107.460479 \n", "L 89.370731 107.399438 \n", "L 89.41639 107.512798 \n", "L 89.43922 107.569365 \n", "L 89.484879 107.447546 \n", "L 89.530538 107.326049 \n", "L 89.576197 107.438977 \n", "L 89.667515 107.547201 \n", "L 89.690345 107.486665 \n", "L 89.758834 107.538416 \n", "L 89.804493 107.41776 \n", "L 89.918641 107.581104 \n", "L 89.94147 107.520983 \n", "L 90.169766 107.154246 \n", "L 90.192595 107.20983 \n", "L 90.238254 107.091092 \n", "L 90.329573 106.96909 \n", "L 90.352402 107.024542 \n", "L 90.557868 107.292777 \n", "L 90.694845 107.054192 \n", "L 90.717675 107.109019 \n", "L 90.854652 107.210786 \n", "L 91.060118 106.913128 \n", "L 91.082948 106.967486 \n", "L 91.128607 106.851998 \n", "L 91.197096 106.679317 \n", "L 91.242755 106.78785 \n", "L 91.402562 106.943251 \n", "L 91.49388 106.825533 \n", "L 91.516709 106.879298 \n", "L 91.539539 106.932994 \n", "L 91.585198 106.818999 \n", "L 91.699346 106.645498 \n", "L 91.722175 106.699073 \n", "L 91.790664 106.749426 \n", "L 91.813494 106.692896 \n", "L 91.881982 106.743133 \n", "L 91.927641 106.630436 \n", "L 92.064619 106.730642 \n", "L 92.087448 106.674543 \n", "L 92.133107 106.780469 \n", "L 92.292914 106.932179 \n", "L 92.315744 106.876301 \n", "L 92.361403 106.981328 \n", "L 92.384233 106.925524 \n", "L 92.407062 106.977938 \n", "L 92.452721 106.866539 \n", "L 92.475551 106.810945 \n", "L 92.52121 106.915598 \n", "L 92.544039 106.860073 \n", "L 92.612528 106.909022 \n", "L 92.635358 106.853638 \n", "L 92.658187 106.798317 \n", "L 92.703846 106.902482 \n", "L 92.817994 107.054807 \n", "L 92.840824 106.999639 \n", "L 92.863653 106.944528 \n", "L 92.909312 107.047947 \n", "L 93.114778 107.297976 \n", "L 93.183267 107.345406 \n", "L 93.251756 107.181152 \n", "L 93.388733 107.275912 \n", "L 93.411563 107.221432 \n", "L 93.457222 107.323042 \n", "L 93.52571 107.264998 \n", "L 93.571369 107.366255 \n", "L 93.754006 107.142601 \n", "L 93.822495 107.08533 \n", "L 93.868154 107.18602 \n", "L 93.982301 107.125411 \n", "L 94.142108 107.268504 \n", "L 94.187767 107.161722 \n", "L 94.256256 107.208084 \n", "L 94.279086 107.257827 \n", "L 94.324745 107.151428 \n", "L 94.507381 106.933057 \n", "L 94.621529 107.180416 \n", "L 94.667188 107.075031 \n", "L 94.735677 107.01919 \n", "L 94.758506 107.068444 \n", "L 94.895484 107.160002 \n", "L 94.941143 107.055359 \n", "L 94.986802 107.153258 \n", "L 95.009631 107.202123 \n", "L 95.055291 107.097732 \n", "L 95.07812 107.146544 \n", "L 95.169438 107.039178 \n", "L 95.192268 107.087885 \n", "L 95.283586 107.181751 \n", "L 95.306416 107.1299 \n", "L 95.397734 107.02318 \n", "L 95.420563 107.071614 \n", "L 95.489052 107.01683 \n", "L 95.557541 107.161664 \n", "L 95.671689 107.004218 \n", "L 95.694518 107.052336 \n", "L 95.717348 107.100408 \n", "L 95.763007 106.997951 \n", "L 95.945643 106.787971 \n", "L 96.151109 107.020778 \n", "L 96.219598 107.065164 \n", "L 96.310916 106.863064 \n", "L 96.470723 106.999103 \n", "L 96.63053 106.745551 \n", "L 96.653359 106.792703 \n", "L 96.790337 106.880996 \n", "L 96.927314 106.679375 \n", "L 96.950144 106.726231 \n", "L 97.087121 106.81412 \n", "L 97.15561 106.857849 \n", "L 97.201269 106.759162 \n", "L 97.269757 106.707201 \n", "L 97.361076 106.892725 \n", "L 97.452394 106.791704 \n", "L 97.475223 106.837919 \n", "L 97.566542 106.927269 \n", "L 97.589371 106.878341 \n", "L 97.840496 106.532359 \n", "L 98.114451 106.892725 \n", "L 98.456894 106.359873 \n", "L 98.479724 106.405242 \n", "L 98.525383 106.310045 \n", "L 98.822167 105.880669 \n", "L 98.844997 105.925907 \n", "L 98.890656 105.832009 \n", "L 98.913485 105.877205 \n", "L 98.936315 105.830327 \n", "L 98.981974 105.920571 \n", "L 99.004804 105.873751 \n", "L 99.027633 105.918805 \n", "L 99.073292 105.825307 \n", "L 99.21027 105.637417 \n", "L 99.233099 105.682381 \n", "L 99.392906 105.813704 \n", "L 99.598372 105.489066 \n", "L 99.621202 105.533715 \n", "L 99.71252 105.621435 \n", "L 99.73535 105.575535 \n", "L 99.758179 105.529677 \n", "L 99.803838 105.61857 \n", "L 99.826668 105.572759 \n", "L 99.872327 105.661473 \n", "L 99.917986 105.569999 \n", "L 100.100622 105.385243 \n", "L 100.2376 105.471065 \n", "L 100.351748 105.423336 \n", "L 100.557214 105.639719 \n", "L 100.71702 105.502104 \n", "L 100.968146 105.802853 \n", "L 101.013805 105.713424 \n", "L 101.082293 105.755056 \n", "L 101.105123 105.798132 \n", "L 101.150782 105.708977 \n", "L 101.219271 105.663035 \n", "L 101.2421 105.706028 \n", "L 101.447566 105.916991 \n", "L 101.561714 105.782598 \n", "L 101.584544 105.825212 \n", "L 101.653032 105.779528 \n", "L 101.721521 105.907002 \n", "L 101.76718 105.818924 \n", "L 101.835669 105.859725 \n", "L 101.858498 105.902055 \n", "L 101.904157 105.814235 \n", "L 102.086794 105.63635 \n", "L 102.132453 105.720732 \n", "L 102.178112 105.6336 \n", "L 102.200942 105.590097 \n", "L 102.246601 105.674317 \n", "L 102.26943 105.630856 \n", "L 102.337919 105.756885 \n", "L 102.383578 105.670095 \n", "L 102.474896 105.497025 \n", "L 102.520555 105.58085 \n", "L 102.566214 105.664507 \n", "L 102.611874 105.578227 \n", "L 102.748851 105.320386 \n", "L 102.81734 105.361065 \n", "L 102.840169 105.40276 \n", "L 102.885828 105.317226 \n", "L 102.908658 105.358884 \n", "L 103.205442 104.974561 \n", "L 103.228272 105.016103 \n", "L 103.273931 104.931626 \n", "L 103.29676 104.973131 \n", "L 103.31959 104.930953 \n", "L 103.365249 105.013842 \n", "L 103.502226 105.094918 \n", "L 103.662033 104.967459 \n", "L 103.776181 105.089987 \n", "L 103.79901 105.048171 \n", "L 104.027306 104.797007 \n", "L 104.095795 104.755044 \n", "L 104.187113 104.918368 \n", "L 104.232772 104.835636 \n", "L 104.301261 104.875764 \n", "L 104.34692 104.95705 \n", "L 104.392579 104.874544 \n", "L 104.461068 104.914488 \n", "L 104.506727 104.832219 \n", "L 104.735022 105.073153 \n", "L 104.986147 104.785904 \n", "L 105.237272 105.064595 \n", "L 105.260102 105.02401 \n", "L 105.305761 105.103608 \n", "L 105.488398 105.26047 \n", "L 105.671034 105.097215 \n", "L 105.739523 105.05621 \n", "L 105.808011 105.174374 \n", "L 105.89933 105.093267 \n", "L 105.922159 105.132548 \n", "L 105.967818 105.211011 \n", "L 106.036307 105.17009 \n", "L 106.173284 105.088578 \n", "L 106.310262 105.322499 \n", "L 106.355921 105.242838 \n", "L 106.424409 105.202184 \n", "L 106.447239 105.241013 \n", "L 106.470068 105.279811 \n", "L 106.515728 105.200434 \n", "L 106.584216 105.159964 \n", "L 106.607046 105.198694 \n", "L 106.812512 105.389759 \n", "L 107.063637 105.112477 \n", "L 107.42891 105.568622 \n", "L 107.588717 105.372436 \n", "L 107.611546 105.410335 \n", "L 107.748524 105.48383 \n", "L 107.839842 105.328713 \n", "L 107.885501 105.404147 \n", "L 107.999649 105.592169 \n", "L 108.045308 105.514821 \n", "L 108.068137 105.476191 \n", "L 108.113796 105.551179 \n", "L 108.387751 105.847029 \n", "L 108.570388 105.690629 \n", "L 108.661706 105.688117 \n", "L 108.707365 105.611641 \n", "L 108.753024 105.685614 \n", "L 108.867172 105.720001 \n", "L 109.118297 105.45174 \n", "L 109.232445 105.486385 \n", "L 109.255274 105.448617 \n", "L 109.300933 105.521981 \n", "L 109.323763 105.558618 \n", "L 109.369422 105.483183 \n", "L 109.392252 105.445516 \n", "L 109.437911 105.518695 \n", "L 109.552058 105.552998 \n", "L 109.689036 105.401908 \n", "L 109.711865 105.438335 \n", "L 109.96299 105.689999 \n", "L 110.236945 105.390148 \n", "L 110.419582 105.532064 \n", "L 110.533729 105.42025 \n", "L 110.556559 105.456109 \n", "L 110.716366 105.561309 \n", "L 110.967491 105.302653 \n", "L 111.081639 105.408485 \n", "L 111.104468 105.372 \n", "L 111.264275 105.261027 \n", "L 111.424082 105.508638 \n", "L 111.469741 105.436058 \n", "L 111.492571 105.399816 \n", "L 111.53823 105.470303 \n", "L 111.789355 105.713708 \n", "L 111.880673 105.640292 \n", "L 111.903503 105.67521 \n", "L 111.926332 105.710107 \n", "L 111.971991 105.638006 \n", "L 112.01765 105.566025 \n", "L 112.06331 105.635729 \n", "L 112.268776 105.877499 \n", "L 112.291605 105.841583 \n", "L 112.314435 105.805698 \n", "L 112.360094 105.87485 \n", "L 112.451412 105.94252 \n", "L 112.474242 105.906692 \n", "L 112.588389 105.798132 \n", "L 112.611219 105.832561 \n", "L 112.725367 105.864346 \n", "L 112.839514 105.826174 \n", "L 112.862344 105.860445 \n", "L 112.908003 105.789395 \n", "L 113.022151 105.751491 \n", "L 113.273276 105.987468 \n", "L 113.387424 105.949412 \n", "L 113.638549 106.18306 \n", "L 113.752697 106.144861 \n", "L 113.844015 106.14187 \n", "L 113.889674 106.071787 \n", "L 113.935333 106.138879 \n", "L 114.026651 106.20433 \n", "L 114.049481 106.169359 \n", "L 114.186458 106.096596 \n", "L 114.277776 106.093699 \n", "L 114.346265 105.989445 \n", "L 114.391924 106.056084 \n", "L 114.59739 106.286925 \n", "L 114.62022 106.252259 \n", "L 114.734368 106.146943 \n", "L 114.757197 106.18 \n", "L 115.1453 106.603782 \n", "L 115.213788 106.567529 \n", "L 115.236618 106.60016 \n", "L 115.282277 106.665349 \n", "L 115.327936 106.596549 \n", "L 115.464913 106.457561 \n", "L 115.487743 106.490097 \n", "L 115.64755 106.650576 \n", "L 115.670379 106.616378 \n", "L 115.830186 106.576871 \n", "L 115.853016 106.609166 \n", "L 115.898675 106.541054 \n", "L 115.944334 106.473043 \n", "L 116.012823 106.569795 \n", "L 116.195459 106.694746 \n", "L 116.446584 106.520299 \n", "L 116.560732 106.548819 \n", "L 116.743368 106.476507 \n", "L 116.926005 106.66547 \n", "L 116.948834 106.631971 \n", "L 117.19996 106.459895 \n", "L 117.382596 106.582791 \n", "L 117.451085 106.547857 \n", "L 117.496744 106.610769 \n", "L 117.633721 106.670001 \n", "L 117.884846 106.499649 \n", "L 117.976164 106.496337 \n", "L 118.135971 106.45858 \n", "L 118.295778 106.548609 \n", "L 118.478415 106.478274 \n", "L 118.546903 106.507608 \n", "L 118.569733 106.475025 \n", "L 118.661051 106.408349 \n", "L 118.70671 106.470162 \n", "L 118.980665 106.712952 \n", "L 119.163301 106.51704 \n", "L 119.20896 106.578291 \n", "L 119.391597 106.697002 \n", "L 119.528574 106.629153 \n", "L 119.551404 106.659576 \n", "L 119.574233 106.689973 \n", "L 119.619893 106.625736 \n", "L 119.779699 106.588621 \n", "L 119.871018 106.647522 \n", "L 119.893847 106.615553 \n", "L 120.076484 106.546769 \n", "L 120.167802 106.66742 \n", "L 120.213461 106.603782 \n", "L 120.418927 106.503697 \n", "L 120.533075 106.592127 \n", "L 120.555904 106.5605 \n", "L 120.578734 106.5289 \n", "L 120.647223 106.618744 \n", "L 120.7842 106.675116 \n", "L 120.921177 106.670001 \n", "L 121.080984 106.755713 \n", "L 121.263621 106.62684 \n", "L 121.28645 106.656464 \n", "L 121.354939 106.684369 \n", "L 121.377768 106.653105 \n", "L 121.560405 106.58575 \n", "L 121.697382 106.701949 \n", "L 121.720212 106.670842 \n", "L 121.902848 106.603782 \n", "L 122.062655 106.748601 \n", "L 122.085485 106.717647 \n", "L 122.199632 106.743433 \n", "L 122.268121 106.77087 \n", "L 122.31378 106.709151 \n", "L 122.519246 106.611863 \n", "L 122.770371 106.751913 \n", "L 122.884519 106.717909 \n", "L 122.907349 106.746797 \n", "L 123.021496 106.712868 \n", "L 123.158474 106.64861 \n", "L 123.181303 106.677403 \n", "L 123.386769 106.758552 \n", "L 123.523747 106.694547 \n", "L 123.546576 106.723172 \n", "L 123.660724 106.6896 \n", "L 123.774872 106.714844 \n", "L 123.957508 106.825313 \n", "L 123.980338 106.795184 \n", "L 124.094485 106.820134 \n", "L 124.185804 106.875045 \n", "L 124.231463 106.814977 \n", "L 124.39127 106.779896 \n", "L 124.688054 106.971724 \n", "L 124.779372 106.910257 \n", "L 124.825031 106.966314 \n", "L 125.213134 107.267327 \n", "L 125.464259 107.113214 \n", "L 125.509918 107.168587 \n", "L 125.578407 107.079921 \n", "L 125.738213 106.987842 \n", "L 125.761043 107.015473 \n", "L 125.852361 107.011862 \n", "L 125.875191 106.98248 \n", "L 126.012168 106.977138 \n", "L 126.194805 107.02675 \n", "L 126.308952 107.050554 \n", "L 126.468759 107.07245 \n", "L 126.651396 107.008823 \n", "L 126.971009 107.220764 \n", "L 127.222135 107.070568 \n", "L 127.427601 107.201802 \n", "L 127.45043 107.172998 \n", "L 127.633067 107.054402 \n", "L 127.655896 107.081293 \n", "L 127.838533 107.129522 \n", "L 128.112487 106.952956 \n", "L 128.249465 106.947835 \n", "L 128.340783 106.944429 \n", "L 128.363612 106.971093 \n", "L 128.454931 106.967665 \n", "L 128.47776 106.93934 \n", "L 128.637567 106.906015 \n", "L 128.797374 106.927537 \n", "L 128.865863 106.84305 \n", "L 128.934351 106.922506 \n", "L 129.00284 106.947273 \n", "L 129.048499 106.891074 \n", "L 129.231135 106.830112 \n", "L 129.299624 106.90919 \n", "L 129.368113 106.825292 \n", "L 129.52792 106.792613 \n", "L 129.642067 106.815708 \n", "L 129.664897 106.787882 \n", "L 129.916022 106.644557 \n", "L 130.121488 106.772237 \n", "L 130.144318 106.744605 \n", "L 130.304125 106.658835 \n", "L 130.326954 106.684884 \n", "L 130.441102 106.654393 \n", "L 130.509591 106.571998 \n", "L 130.578079 106.649972 \n", "L 130.783545 106.723261 \n", "L 130.897693 106.692896 \n", "L 130.920523 106.718735 \n", "L 131.125989 106.791504 \n", "L 131.331455 106.70525 \n", "L 131.468432 106.700792 \n", "L 131.605409 106.696345 \n", "L 131.742387 106.691913 \n", "L 131.902193 106.608041 \n", "L 131.925023 106.633564 \n", "L 132.153319 106.731031 \n", "L 132.335955 106.672898 \n", "L 132.450103 106.643221 \n", "L 132.564251 106.665664 \n", "L 132.769717 106.737035 \n", "L 132.998012 106.626162 \n", "L 133.180649 106.723776 \n", "L 133.203478 106.697238 \n", "L 133.363285 106.666421 \n", "L 133.500262 106.662147 \n", "L 133.660069 106.5289 \n", "L 133.705728 106.578837 \n", "L 133.934024 106.72539 \n", "L 133.956853 106.69911 \n", "L 134.11666 106.617572 \n", "L 134.13949 106.642375 \n", "L 134.253638 106.715265 \n", "L 134.299297 106.66293 \n", "L 134.459104 106.581818 \n", "L 134.481933 106.606527 \n", "L 134.527592 106.655902 \n", "L 134.596081 106.577749 \n", "L 134.687399 106.575036 \n", "L 134.710229 106.599677 \n", "L 134.847206 106.646134 \n", "L 134.870036 106.620174 \n", "L 134.938524 106.542379 \n", "L 135.007013 106.616053 \n", "L 135.075502 106.6392 \n", "L 135.121161 106.587448 \n", "L 135.532093 106.27466 \n", "L 135.714729 106.320003 \n", "L 135.897366 106.16559 \n", "L 135.943025 106.214345 \n", "L 136.125661 106.259593 \n", "L 136.285468 106.230752 \n", "L 136.422445 106.32649 \n", "L 136.468105 106.275769 \n", "L 136.71923 106.145981 \n", "L 136.856207 106.142748 \n", "L 136.901866 106.0924 \n", "L 136.970355 106.164681 \n", "L 137.152991 106.209471 \n", "L 137.426946 106.055968 \n", "L 137.655241 106.148536 \n", "L 137.883537 106.045833 \n", "L 137.929196 106.093489 \n", "L 137.997685 106.019006 \n", "L 138.340128 105.793701 \n", "L 138.614083 105.933325 \n", "L 138.728231 105.906887 \n", "L 138.75106 105.930518 \n", "L 139.047844 106.092511 \n", "L 139.207651 106.06499 \n", "L 139.504436 106.225632 \n", "L 139.687072 106.173649 \n", "L 139.80122 106.194804 \n", "L 139.824049 106.170484 \n", "L 140.098004 105.974514 \n", "L 140.120834 105.997746 \n", "L 140.3263 106.064396 \n", "L 140.417618 106.062419 \n", "L 140.440447 106.08554 \n", "L 140.554595 106.059464 \n", "L 140.714402 105.985412 \n", "L 140.737232 106.008476 \n", "L 140.965527 106.097594 \n", "L 141.148164 106.046763 \n", "L 141.376459 106.182076 \n", "L 141.399289 106.158204 \n", "L 141.513436 106.132323 \n", "L 141.536266 106.155128 \n", "L 141.627584 106.153089 \n", "L 141.650414 106.129295 \n", "L 141.810221 106.056047 \n", "L 141.83305 106.0788 \n", "L 141.947198 106.099561 \n", "L 141.970028 106.075867 \n", "L 142.107005 106.072944 \n", "L 142.175494 106.094624 \n", "L 142.221153 106.047378 \n", "L 142.3353 106.068086 \n", "L 142.586426 106.177955 \n", "L 142.700573 106.106384 \n", "L 142.746232 106.151385 \n", "L 142.906039 106.170821 \n", "L 142.951698 106.123927 \n", "L 143.020187 106.191208 \n", "L 143.202824 106.232866 \n", "L 143.225653 106.209471 \n", "L 143.294142 106.276479 \n", "L 143.476778 106.317826 \n", "L 143.659415 106.267988 \n", "L 143.933369 106.397776 \n", "L 144.207324 106.255387 \n", "L 144.344301 106.252259 \n", "L 144.504108 106.226084 \n", "L 144.686745 106.266979 \n", "L 144.823722 106.263851 \n", "L 145.006358 106.304526 \n", "L 145.143336 106.30135 \n", "L 145.280313 106.298185 \n", "L 145.44012 106.272199 \n", "L 145.599927 106.290841 \n", "L 145.94237 106.083379 \n", "L 146.056518 106.058886 \n", "L 146.261984 105.98842 \n", "L 146.46745 106.050606 \n", "L 146.604427 106.047867 \n", "L 146.741405 106.045133 \n", "L 146.901212 106.019999 \n", "L 147.038189 106.017318 \n", "L 147.175166 106.014642 \n", "L 147.289314 105.990559 \n", "L 147.540439 105.876748 \n", "L 147.677416 105.874293 \n", "L 147.905712 105.783365 \n", "L 148.01986 105.759761 \n", "L 148.225326 105.691444 \n", "L 148.407962 105.731751 \n", "L 148.52211 105.751475 \n", "L 148.636258 105.72806 \n", "L 148.727576 105.769634 \n", "L 148.864553 105.767368 \n", "L 149.001531 105.765107 \n", "L 149.092849 105.720732 \n", "L 149.252656 105.696775 \n", "L 149.435292 105.73664 \n", "L 149.777736 105.539592 \n", "L 150.028861 105.642238 \n", "L 150.165838 105.640187 \n", "L 150.325645 105.658966 \n", "L 150.55394 105.571077 \n", "L 150.713747 105.589892 \n", "L 150.873554 105.566566 \n", "L 151.07902 105.626666 \n", "L 151.261657 105.58208 \n", "L 151.375804 105.559532 \n", "L 151.558441 105.515215 \n", "L 151.672589 105.492819 \n", "L 151.923714 105.385453 \n", "L 152.12918 105.44529 \n", "L 152.357475 105.359535 \n", "L 152.540112 105.398759 \n", "L 152.65426 105.418032 \n", "L 152.814066 105.436694 \n", "L 153.019532 105.372405 \n", "L 153.224998 105.431595 \n", "L 153.407635 105.38835 \n", "L 153.498953 105.346308 \n", "L 153.750078 105.241308 \n", "L 153.887056 105.239888 \n", "L 154.138181 105.135571 \n", "L 154.389306 105.234726 \n", "L 154.549113 105.21283 \n", "L 154.800238 105.311385 \n", "L 155.005704 105.248631 \n", "L 155.233999 105.326778 \n", "L 155.485124 105.223644 \n", "L 155.73625 105.321248 \n", "L 155.873227 105.319749 \n", "L 156.078693 105.377378 \n", "L 156.329818 105.274974 \n", "L 156.74075 105.508607 \n", "L 156.969046 105.426595 \n", "L 157.151682 105.463921 \n", "L 157.402807 105.362385 \n", "L 157.722421 105.515903 \n", "L 157.882228 105.494281 \n", "L 158.133353 105.588957 \n", "L 158.407308 105.468295 \n", "L 158.544285 105.466644 \n", "L 158.681262 105.464993 \n", "L 158.79541 105.444191 \n", "L 159.023706 105.363946 \n", "L 159.115024 105.324197 \n", "L 159.343319 105.244457 \n", "L 159.640104 105.376469 \n", "L 159.731422 105.413973 \n", "L 159.914058 105.450342 \n", "L 160.096695 105.409815 \n", "L 160.439138 105.578101 \n", "L 160.598945 105.556941 \n", "L 160.758752 105.57399 \n", "L 160.918559 105.552903 \n", "L 161.078366 105.569905 \n", "L 161.443639 105.37579 \n", "L 161.557786 105.355624 \n", "L 161.717593 105.335021 \n", "L 161.923059 105.389459 \n", "L 162.082866 105.368877 \n", "L 162.219843 105.367411 \n", "L 162.585116 105.176046 \n", "L 162.722094 105.174831 \n", "L 163.018878 105.041505 \n", "L 163.133026 105.021986 \n", "L 163.270003 105.020971 \n", "L 163.40698 105.019956 \n", "L 163.521128 105.037699 \n", "L 163.726594 105.0918 \n", "L 163.840742 105.109412 \n", "L 163.95489 105.089971 \n", "L 164.183185 105.014263 \n", "L 164.479969 105.141054 \n", "L 164.731095 105.047046 \n", "L 164.868072 105.046016 \n", "L 164.98222 105.063491 \n", "L 165.164856 105.098703 \n", "L 165.301833 105.097609 \n", "L 165.438811 105.096521 \n", "L 165.667106 105.021817 \n", "L 165.895402 105.09291 \n", "L 166.100868 105.036837 \n", "L 166.351993 105.125546 \n", "L 166.625948 105.01492 \n", "L 166.740095 104.996057 \n", "L 166.968391 104.922431 \n", "L 167.151027 104.957249 \n", "L 167.333664 104.920145 \n", "L 167.447812 104.901497 \n", "L 167.607619 104.882619 \n", "L 167.721766 104.864056 \n", "L 167.858744 104.863273 \n", "L 168.018551 104.880206 \n", "L 168.132698 104.897365 \n", "L 168.246846 104.878871 \n", "L 168.383823 104.878077 \n", "L 168.56646 104.912532 \n", "L 168.817585 104.822409 \n", "L 169.228517 105.03209 \n", "L 169.365494 105.031117 \n", "L 169.525301 105.047588 \n", "L 169.685108 105.028851 \n", "L 169.867745 105.062676 \n", "L 170.050381 105.061335 \n", "L 170.255847 105.112324 \n", "L 170.484143 105.075635 \n", "L 170.62112 105.074615 \n", "L 170.895075 105.002997 \n", "L 171.032052 104.967311 \n", "L 171.237518 104.948638 \n", "L 171.420154 104.947492 \n", "L 171.808257 104.789699 \n", "L 171.990893 104.788811 \n", "L 172.17353 104.787922 \n", "L 172.333337 104.769959 \n", "L 172.607291 104.700092 \n", "L 172.789928 104.69935 \n", "L 172.995394 104.681424 \n", "L 173.292178 104.765496 \n", "L 173.451985 104.781782 \n", "L 173.70311 104.831557 \n", "L 173.885746 104.830616 \n", "L 174.045553 104.812857 \n", "L 174.182531 104.846014 \n", "L 174.342337 104.828276 \n", "L 174.661951 104.725541 \n", "L 174.844588 104.724769 \n", "L 175.095713 104.673343 \n", "L 175.324008 104.705995 \n", "L 175.506645 104.705259 \n", "L 175.73494 104.737743 \n", "L 175.940406 104.72019 \n", "L 176.145872 104.735982 \n", "L 176.511145 104.601673 \n", "L 176.670952 104.584598 \n", "L 176.876418 104.567438 \n", "L 177.081884 104.583373 \n", "L 177.218861 104.615962 \n", "L 177.378668 104.598965 \n", "L 177.606964 104.565367 \n", "L 177.880919 104.630235 \n", "L 178.109214 104.596715 \n", "L 178.246191 104.563574 \n", "L 178.405998 104.579467 \n", "L 178.565805 104.562681 \n", "L 178.771271 104.545821 \n", "L 178.999567 104.577742 \n", "L 179.227862 104.544622 \n", "L 179.410499 104.544144 \n", "L 179.707283 104.462527 \n", "L 179.935579 104.494364 \n", "L 180.095386 104.51013 \n", "L 180.300852 104.525749 \n", "L 180.551977 104.476958 \n", "L 180.780272 104.508548 \n", "L 180.940079 104.524193 \n", "L 181.168375 104.555605 \n", "L 181.442329 104.491089 \n", "L 181.602136 104.474824 \n", "L 181.830432 104.442566 \n", "L 181.990239 104.426411 \n", "L 182.150046 104.442009 \n", "L 182.309852 104.42589 \n", "L 182.492489 104.425596 \n", "L 182.675125 104.425301 \n", "L 182.880591 104.409204 \n", "L 183.063228 104.408931 \n", "L 183.223035 104.424423 \n", "L 183.428501 104.439795 \n", "L 183.542648 104.486657 \n", "L 183.702455 104.470655 \n", "L 183.907921 104.454615 \n", "L 184.159046 104.500994 \n", "L 184.547149 104.360076 \n", "L 184.889592 104.468363 \n", "L 185.117888 104.436935 \n", "L 185.369013 104.482914 \n", "L 185.551649 104.482546 \n", "L 185.779945 104.512922 \n", "L 185.962581 104.512512 \n", "L 186.168047 104.527426 \n", "L 186.350684 104.526995 \n", "L 186.55615 104.541841 \n", "L 186.761616 104.526039 \n", "L 186.875764 104.479907 \n", "L 187.172548 104.403053 \n", "L 187.400843 104.433192 \n", "L 187.56065 104.448144 \n", "L 187.766116 104.462974 \n", "L 187.971582 104.447439 \n", "L 188.199878 104.477321 \n", "L 188.382514 104.476968 \n", "L 188.633639 104.521743 \n", "L 188.930424 104.445804 \n", "L 189.11306 104.445499 \n", "L 189.387015 104.385027 \n", "L 189.523992 104.354887 \n", "L 189.889265 104.234946 \n", "L 190.071901 104.234925 \n", "L 190.231708 104.249818 \n", "L 190.482833 104.294404 \n", "L 190.619811 104.32406 \n", "L 190.848106 104.353568 \n", "L 191.122061 104.294057 \n", "L 191.350356 104.323502 \n", "L 191.601482 104.279038 \n", "L 191.852607 104.323124 \n", "L 192.309198 104.146589 \n", "L 192.491834 104.146684 \n", "L 192.72013 104.117528 \n", "L 192.902766 104.117665 \n", "L 193.108232 104.103218 \n", "L 193.336528 104.132553 \n", "L 193.519164 104.132669 \n", "L 193.793119 104.190949 \n", "L 194.089903 104.118537 \n", "L 194.318199 104.147641 \n", "L 194.455176 104.176629 \n", "L 194.888938 104.335294 \n", "L 195.322699 104.176902 \n", "L 195.482506 104.162592 \n", "L 195.642313 104.176997 \n", "L 195.939097 104.248609 \n", "L 196.144563 104.234284 \n", "L 196.418518 104.291276 \n", "L 196.601154 104.291187 \n", "L 196.82945 104.319502 \n", "L 196.989257 104.333586 \n", "L 197.194723 104.347601 \n", "L 197.35453 104.361617 \n", "L 197.514336 104.347312 \n", "L 197.719802 104.333002 \n", "L 197.925268 104.346949 \n", "L 198.062246 104.375006 \n", "L 198.267712 104.388859 \n", "L 198.427519 104.402727 \n", "L 198.587326 104.38848 \n", "L 198.747132 104.402317 \n", "L 198.929769 104.402086 \n", "L 199.295042 104.289857 \n", "L 199.477678 104.289767 \n", "L 199.705974 104.261794 \n", "L 200.002758 104.331225 \n", "L 200.208224 104.317183 \n", "L 200.550667 104.413909 \n", "L 200.710474 104.42753 \n", "L 200.847452 104.399689 \n", "L 201.052918 104.385636 \n", "L 201.235554 104.385426 \n", "L 201.532338 104.316305 \n", "L 201.851952 104.398459 \n", "L 202.103077 104.357043 \n", "L 202.377032 104.411501 \n", "L 202.628157 104.370207 \n", "L 202.787964 104.356407 \n", "L 202.947771 104.369886 \n", "L 203.198896 104.41044 \n", "L 203.586998 104.287838 \n", "L 203.838123 104.328355 \n", "L 204.112078 104.274075 \n", "L 204.363203 104.314476 \n", "L 204.52301 104.327855 \n", "L 204.682817 104.314276 \n", "L 204.888283 104.300697 \n", "L 205.185067 104.367646 \n", "L 205.367704 104.367468 \n", "L 205.55034 104.367289 \n", "L 205.869954 104.286808 \n", "L 206.07542 104.300061 \n", "L 206.235227 104.313304 \n", "L 206.417863 104.313193 \n", "L 206.691818 104.259865 \n", "L 206.805966 104.22 \n", "L 207.07992 104.166977 \n", "L 207.559341 104.338922 \n", "L 207.856125 104.27275 \n", "L 208.13008 104.325311 \n", "L 208.335546 104.312026 \n", "L 208.723648 104.429812 \n", "L 208.837796 104.468967 \n", "L 208.997603 104.455619 \n", "L 209.13458 104.481563 \n", "L 209.454194 104.559332 \n", "L 209.591171 104.585108 \n", "L 209.796637 104.597646 \n", "L 209.933615 104.623332 \n", "L 210.093422 104.609921 \n", "L 210.458695 104.505268 \n", "L 210.618501 104.492025 \n", "L 210.869627 104.452786 \n", "L 211.052263 104.452512 \n", "L 211.326218 104.400525 \n", "L 211.508854 104.400314 \n", "L 211.73715 104.374328 \n", "L 211.919786 104.37415 \n", "L 212.193741 104.322588 \n", "L 212.490525 104.386393 \n", "L 212.695991 104.373393 \n", "L 212.855798 104.360465 \n", "L 213.084093 104.334763 \n", "L 213.312389 104.36006 \n", "L 213.472196 104.372641 \n", "L 213.746151 104.423167 \n", "L 214.088594 104.359377 \n", "L 214.316889 104.359177 \n", "L 214.613674 104.321032 \n", "L 214.79631 104.295687 \n", "L 215.093094 104.257772 \n", "L 215.32139 104.25772 \n", "L 215.618174 104.22 \n", "L 216.006277 104.307636 \n", "L 216.166083 104.345072 \n", "L 216.394379 104.344894 \n", "L 216.645504 104.332229 \n", "L 216.987947 104.394211 \n", "L 217.35322 104.319323 \n", "L 217.604345 104.331567 \n", "L 218.083766 104.195281 \n", "L 218.471869 104.28165 \n", "L 218.791482 104.232307 \n", "L 218.974119 104.207709 \n", "L 219.202414 104.207719 \n", "L 219.43071 104.20774 \n", "L 219.750324 104.158828 \n", "L 220.092767 104.22 \n", "L 220.343892 104.207814 \n", "L 220.754824 104.305123 \n", "L 221.005949 104.292853 \n", "L 221.279904 104.316978 \n", "L 221.5082 104.316842 \n", "L 221.941961 104.425254 \n", "L 222.284404 104.364592 \n", "L 222.5127 104.364392 \n", "L 222.763825 104.352164 \n", "L 222.900802 104.304035 \n", "L 223.03778 104.351949 \n", "L 223.288905 104.363725 \n", "L 223.699837 104.267793 \n", "L 223.973792 104.291571 \n", "L 224.202087 104.291476 \n", "L 224.54453 104.350771 \n", "L 224.886974 104.291187 \n", "L 225.206588 104.338422 \n", "L 225.480542 104.314586 \n", "L 225.822986 104.373393 \n", "L 226.051281 104.373188 \n", "L 226.393724 104.431678 \n", "L 226.64485 104.419629 \n", "L 226.895975 104.431063 \n", "L 227.169929 104.407312 \n", "L 227.329736 104.372047 \n", "L 227.580861 104.36015 \n", "L 227.877646 104.394884 \n", "L 228.060282 104.417994 \n", "L 228.334237 104.440936 \n", "L 228.585362 104.429002 \n", "L 228.904976 104.474981 \n", "L 229.224589 104.428235 \n", "L 229.452885 104.427967 \n", "L 229.795328 104.369902 \n", "L 229.977965 104.346707 \n", "L 230.366067 104.265974 \n", "L 230.548704 104.242963 \n", "L 230.70851 104.277355 \n", "L 231.005295 104.311611 \n", "L 231.279249 104.2886 \n", "L 231.484715 104.277103 \n", "L 231.713011 104.277029 \n", "L 232.055454 104.333838 \n", "L 232.329409 104.310927 \n", "L 232.717511 104.390115 \n", "L 232.945807 104.3899 \n", "L 233.31108 104.45737 \n", "L 233.676353 104.389201 \n", "L 233.858989 104.366495 \n", "L 234.247092 104.287465 \n", "L 234.475387 104.287381 \n", "L 234.726512 104.276073 \n", "L 235.068956 104.33193 \n", "L 235.411399 104.275857 \n", "L 235.776672 104.342638 \n", "L 236.004967 104.342486 \n", "L 236.141945 104.297884 \n", "L 236.324581 104.320038 \n", "L 236.552877 104.319912 \n", "L 236.735513 104.341992 \n", "L 236.986638 104.3529 \n", "L 237.443229 104.242096 \n", "L 237.740014 104.275147 \n", "L 237.94548 104.286098 \n", "L 238.173775 104.286019 \n", "L 238.402071 104.285935 \n", "L 238.744514 104.340656 \n", "L 239.155446 104.252831 \n", "L 239.360912 104.241864 \n", "L 239.726185 104.176355 \n", "L 240.045799 104.22 \n", "L 240.388242 104.165641 \n", "L 240.616538 104.165715 \n", "L 240.867663 104.154938 \n", "L 241.164447 104.187521 \n", "L 241.643868 104.068826 \n", "L 242.191777 104.22 \n", "L 242.511391 104.177007 \n", "L 242.648368 104.134077 \n", "L 242.876664 104.134183 \n", "L 243.310425 104.230704 \n", "L 243.538721 104.230688 \n", "L 243.721357 104.209318 \n", "L 243.903994 104.230667 \n", "L 244.155119 104.241307 \n", "L 244.451903 104.20936 \n", "L 244.748687 104.241244 \n", "L 244.931324 104.262446 \n", "L 245.228108 104.294162 \n", "L 245.433574 104.304666 \n", "L 245.593381 104.272871 \n", "L 245.798847 104.283375 \n", "L 246.118461 104.325453 \n", "L 246.369586 104.314781 \n", "L 246.552222 104.293647 \n", "L 246.803347 104.283043 \n", "L 247.031643 104.28297 \n", "L 247.214279 104.303883 \n", "L 247.533893 104.345609 \n", "L 247.853507 104.303604 \n", "L 248.127461 104.324354 \n", "L 248.492734 104.261663 \n", "L 248.652541 104.230409 \n", "L 248.880837 104.230393 \n", "L 249.154791 104.251138 \n", "L 249.520064 104.18892 \n", "L 249.72553 104.178605 \n", "L 249.953826 104.178647 \n", "L 250.410417 104.281882 \n", "L 250.638713 104.281808 \n", "L 251.049645 104.363914 \n", "L 251.27794 104.363751 \n", "L 251.551895 104.384054 \n", "L 251.825849 104.363346 \n", "L 251.985656 104.332539 \n", "L 252.213952 104.332408 \n", "L 252.487907 104.352664 \n", "L 252.898839 104.270915 \n", "L 253.3326 104.362258 \n", "L 253.652214 104.321447 \n", "L 253.85768 104.311211 \n", "L 254.177294 104.270589 \n", "L 254.474078 104.28062 \n", "L 254.953499 104.189761 \n", "L 255.41009 104.270279 \n", "L 255.684044 104.270211 \n", "L 255.957999 104.270143 \n", "L 256.231954 104.270074 \n", "L 256.460249 104.250008 \n", "L 256.734204 104.249971 \n", "L 257.076647 104.279837 \n", "L 257.396261 104.259828 \n", "L 257.784363 104.309445 \n", "L 258.355102 104.180361 \n", "L 258.583398 104.160605 \n", "L 258.948671 104.121187 \n", "L 259.176966 104.101557 \n", "L 259.656387 104.013216 \n", "L 259.861853 103.983913 \n", "L 260.135808 103.984228 \n", "L 260.569569 104.053349 \n", "L 260.889183 104.034024 \n", "L 261.07182 103.995079 \n", "L 261.345774 103.995373 \n", "L 261.642558 104.005446 \n", "L 261.870854 104.025171 \n", "L 262.190468 104.044927 \n", "L 262.57857 103.996709 \n", "L 262.921014 104.026149 \n", "L 263.331946 103.968499 \n", "L 263.674389 103.997886 \n", "L 263.902684 104.017422 \n", "L 264.176639 104.017685 \n", "L 264.564742 103.969981 \n", "L 264.930014 104.008811 \n", "L 265.272458 103.980401 \n", "L 265.729049 104.057429 \n", "L 266.094322 104.019525 \n", "L 266.368276 104.019787 \n", "L 266.68789 104.001051 \n", "L 267.007504 104.020387 \n", "L 267.21297 104.04907 \n", "L 267.555413 104.077784 \n", "L 267.829368 104.077973 \n", "L 268.080493 104.068679 \n", "L 268.514255 104.002923 \n", "L 268.811039 104.012648 \n", "L 268.993675 104.050489 \n", "L 269.518755 104.154244 \n", "L 269.76988 104.163707 \n", "L 270.203642 104.229363 \n", "L 270.568915 104.191958 \n", "L 270.865699 104.201327 \n", "L 271.230972 104.164085 \n", "L 271.710392 104.247894 \n", "L 272.075665 104.210716 \n", "L 272.418109 104.238537 \n", "L 272.692063 104.238516 \n", "L 273.217143 104.340052 \n", "L 273.422609 104.367615 \n", "L 273.742223 104.385826 \n", "L 274.198814 104.311931 \n", "L 274.495598 104.320989 \n", "L 274.769553 104.320863 \n", "L 275.226144 104.393859 \n", "L 275.522928 104.384485 \n", "L 275.819712 104.393391 \n", "L 276.093667 104.393175 \n", "L 276.527429 104.456508 \n", "L 276.824213 104.447108 \n", "L 277.098168 104.446829 \n", "L 277.303634 104.473814 \n", "L 277.531929 104.455441 \n", "L 277.737395 104.482368 \n", "L 278.125498 104.527064 \n", "L 278.308134 104.562912 \n", "L 278.559259 104.553512 \n", "L 278.833214 104.553102 \n", "L 279.266975 104.615352 \n", "L 279.632248 104.578831 \n", "L 279.974692 104.605153 \n", "L 280.271476 104.595701 \n", "L 280.56826 104.604138 \n", "L 280.819385 104.612634 \n", "L 281.230317 104.665363 \n", "L 281.481442 104.673769 \n", "L 281.732567 104.664375 \n", "L 281.983693 104.672765 \n", "L 282.417454 104.733926 \n", "L 282.759897 104.70661 \n", "L 283.12517 104.74116 \n", "L 283.444784 104.722792 \n", "L 283.467614 104.731561 \n", "L 283.467614 104.731561 \n", "\" clip-path=\"url(#p3a05faa0b4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 55.194886 16.02 \n", "L 55.331864 142.019998 \n", "L 55.423182 160.347272 \n", "L 55.446011 148.32 \n", "L 55.582989 114.019999 \n", "L 55.605818 118.146315 \n", "L 55.628648 121.859999 \n", "L 55.674307 112.238179 \n", "L 55.742796 100.692003 \n", "L 55.788455 107.486665 \n", "L 55.811284 110.520002 \n", "L 55.856943 104.22 \n", "L 55.879773 101.374841 \n", "L 55.971091 101.699995 \n", "L 56.03958 99.577891 \n", "L 56.108069 106.371217 \n", "L 56.130898 104.22 \n", "L 56.176557 108.229092 \n", "L 56.199387 106.18 \n", "L 56.267875 104.22 \n", "L 56.359194 111.004615 \n", "L 56.382023 109.212454 \n", "L 56.427682 112.238179 \n", "L 56.450512 110.520002 \n", "L 56.519001 114.684406 \n", "L 56.56466 111.44951 \n", "L 56.610319 108.42 \n", "L 56.655978 111.004615 \n", "L 56.701637 113.434923 \n", "L 56.770126 111.78 \n", "L 56.929933 107.656365 \n", "L 56.952762 108.743077 \n", "L 57.021251 107.486665 \n", "L 57.04408 106.371217 \n", "L 57.089739 108.42 \n", "L 57.112569 107.332941 \n", "L 57.181058 106.224544 \n", "L 57.272376 109.972173 \n", "L 57.386524 108.766392 \n", "L 57.409353 109.619997 \n", "L 57.455012 107.748002 \n", "L 57.477842 108.586335 \n", "L 57.546331 105.916155 \n", "L 57.59199 107.548299 \n", "L 57.614819 108.341495 \n", "L 57.660478 106.647522 \n", "L 57.865944 102.725083 \n", "L 58.002922 104.22 \n", "L 58.299706 98.425844 \n", "L 58.345365 98.509212 \n", "L 58.459513 96.869996 \n", "L 58.505172 98.178906 \n", "L 58.573661 97.708593 \n", "L 58.642149 96.096315 \n", "L 58.710638 96.822577 \n", "L 58.893274 99.350058 \n", "L 58.938933 98.339995 \n", "L 58.984593 99.466703 \n", "L 59.007422 100.019995 \n", "L 59.053081 99.03176 \n", "L 59.167229 97.667997 \n", "L 59.212888 97.742039 \n", "L 59.349865 98.918363 \n", "L 59.441184 98.088452 \n", "L 59.395525 98.975677 \n", "L 59.486843 98.153335 \n", "L 59.509672 98.649472 \n", "L 59.555331 97.788754 \n", "L 59.62382 96.530764 \n", "L 59.692309 97.092731 \n", "L 59.806457 97.702757 \n", "L 60.034752 93.867892 \n", "L 60.080411 94.784651 \n", "L 60.1489 96.128258 \n", "L 60.194559 95.399998 \n", "L 60.331536 94.073098 \n", "L 60.377196 94.162102 \n", "L 60.559832 95.997965 \n", "L 60.582662 95.66051 \n", "L 60.628321 96.470213 \n", "L 60.65115 96.869996 \n", "L 60.696809 96.201815 \n", "L 60.788128 94.898047 \n", "L 60.856616 95.364575 \n", "L 61.039253 97.699382 \n", "L 61.062082 97.382787 \n", "L 61.107741 96.756926 \n", "L 61.17623 97.177412 \n", "L 61.221889 97.89623 \n", "L 61.267548 97.282923 \n", "L 61.404526 96.143072 \n", "L 61.450185 96.201815 \n", "L 61.518673 96.605615 \n", "L 61.541503 96.316778 \n", "L 61.655651 95.524224 \n", "L 61.67848 95.864213 \n", "L 61.861117 97.296455 \n", "L 61.883946 97.020003 \n", "L 61.929605 97.664591 \n", "L 62.066583 98.37894 \n", "L 62.112242 97.837109 \n", "L 62.18073 98.186771 \n", "L 62.272049 98.831579 \n", "L 62.294878 98.566157 \n", "L 62.386196 98.637717 \n", "L 62.477515 97.604996 \n", "L 62.568833 98.775559 \n", "L 62.637322 98.555779 \n", "L 62.751469 97.844101 \n", "L 62.774299 98.128112 \n", "L 62.842788 97.920003 \n", "L 62.888447 98.479172 \n", "L 62.956935 98.788334 \n", "L 63.025424 98.066509 \n", "L 63.071083 98.611904 \n", "L 63.139572 98.407391 \n", "L 63.185231 97.937951 \n", "L 63.23089 98.473253 \n", "L 63.25372 98.240341 \n", "L 63.390697 98.830002 \n", "L 63.413526 98.600612 \n", "L 63.459186 99.117525 \n", "L 63.596163 99.678534 \n", "L 63.618992 99.452435 \n", "L 63.687481 99.727236 \n", "L 63.778799 100.242813 \n", "L 63.801629 100.019995 \n", "L 63.847288 99.577891 \n", "L 63.892947 100.063976 \n", "L 64.098413 101.28752 \n", "L 64.166902 101.533701 \n", "L 64.303879 100.251004 \n", "L 64.577834 102.079223 \n", "L 64.646322 102.307225 \n", "L 64.714811 101.687946 \n", "L 64.851788 102.139806 \n", "L 65.125743 100.578712 \n", "L 65.217061 101.01273 \n", "L 65.239891 100.820004 \n", "L 65.26272 100.62814 \n", "L 65.30838 101.041624 \n", "L 65.354039 100.660355 \n", "L 65.491016 101.097875 \n", "L 65.627993 100.368469 \n", "L 65.673652 100.385218 \n", "L 65.696482 100.584863 \n", "L 65.742141 100.219566 \n", "L 65.764971 100.038101 \n", "L 65.81063 100.434593 \n", "L 65.833459 100.631567 \n", "L 65.879118 100.27075 \n", "L 65.947607 99.735258 \n", "L 65.993266 100.126326 \n", "L 66.107414 100.721464 \n", "L 66.130244 100.545004 \n", "L 66.175903 100.194269 \n", "L 66.221562 100.575369 \n", "L 66.31288 101.3282 \n", "L 66.381369 101.166228 \n", "L 66.678153 99.669996 \n", "L 66.792301 99.887978 \n", "L 66.860789 100.085625 \n", "L 67.020596 98.951788 \n", "L 67.157574 99.684001 \n", "L 67.180403 99.524942 \n", "L 67.248892 99.051379 \n", "L 67.31738 99.246314 \n", "L 67.591335 100.97735 \n", "L 67.614165 100.821465 \n", "L 67.751142 99.89804 \n", "L 67.819631 100.080652 \n", "L 67.910949 100.742577 \n", "L 67.956608 100.439998 \n", "L 67.979438 100.289518 \n", "L 68.047926 100.466809 \n", "L 68.093585 100.791731 \n", "L 68.139244 100.493242 \n", "L 68.162074 100.34478 \n", "L 68.207733 100.667284 \n", "L 68.253392 100.371834 \n", "L 68.299051 100.692003 \n", "L 68.34471 100.398508 \n", "L 68.413199 99.962072 \n", "L 68.481688 100.135263 \n", "L 68.550176 100.005664 \n", "L 68.641495 100.632198 \n", "L 68.709983 100.799091 \n", "L 68.801302 100.231058 \n", "L 68.938279 100.563288 \n", "L 68.961108 100.423311 \n", "L 69.006768 100.726932 \n", "L 69.029597 100.587376 \n", "L 69.120915 100.89987 \n", "L 69.143745 100.761177 \n", "L 69.280722 99.938446 \n", "L 69.326381 100.236778 \n", "L 69.349211 100.385218 \n", "L 69.39487 100.114381 \n", "L 69.4177 99.97962 \n", "L 69.463359 100.274955 \n", "L 69.486188 100.140573 \n", "L 69.554677 100.3 \n", "L 69.577506 100.166438 \n", "L 69.760143 99.665076 \n", "L 69.851461 100.242088 \n", "L 69.89712 99.980935 \n", "L 69.988438 99.735258 \n", "L 69.942779 99.994035 \n", "L 70.011268 99.877842 \n", "L 70.239564 100.745458 \n", "L 70.262393 100.617279 \n", "L 70.308052 100.894203 \n", "L 70.330882 101.032045 \n", "L 70.376541 100.776759 \n", "L 70.44503 100.396679 \n", "L 70.490689 100.670964 \n", "L 70.513518 100.807503 \n", "L 70.559177 100.555907 \n", "L 70.582007 100.692003 \n", "L 70.604836 100.566747 \n", "L 70.650496 100.837699 \n", "L 70.764643 100.991597 \n", "L 70.855962 100.496859 \n", "L 70.92445 100.640873 \n", "L 70.94728 100.773689 \n", "L 70.992939 100.529095 \n", "L 71.015768 100.661501 \n", "L 71.107087 100.429168 \n", "L 71.061428 100.671721 \n", "L 71.152746 100.439998 \n", "L 71.335382 101.230165 \n", "L 71.358212 101.109987 \n", "L 71.47236 100.761177 \n", "L 71.403871 101.118735 \n", "L 71.495189 100.889367 \n", "L 71.518019 101.017209 \n", "L 71.563678 100.780449 \n", "L 71.586507 100.907903 \n", "L 71.677826 100.682236 \n", "L 71.632166 100.917093 \n", "L 71.700655 100.808953 \n", "L 71.723485 100.935314 \n", "L 71.769144 100.701708 \n", "L 71.883292 100.364263 \n", "L 71.928951 100.374767 \n", "L 72.088758 100.768179 \n", "L 72.294224 100.221595 \n", "L 72.431201 100.48667 \n", "L 72.47686 100.2638 \n", "L 72.522519 100.506311 \n", "L 72.591008 100.636509 \n", "L 72.613837 100.525762 \n", "L 72.705156 100.315309 \n", "L 72.659496 100.535404 \n", "L 72.750815 100.325455 \n", "L 72.933451 100.818974 \n", "L 73.138917 100.073365 \n", "L 73.161747 100.190557 \n", "L 73.390042 100.904213 \n", "L 73.412872 100.797977 \n", "L 73.458531 101.026746 \n", "L 73.52702 101.367765 \n", "L 73.595508 101.269068 \n", "L 73.732486 100.856898 \n", "L 73.755315 100.969391 \n", "L 73.823804 100.873364 \n", "L 73.869463 101.096918 \n", "L 74.052099 100.70053 \n", "L 74.211906 101.047343 \n", "L 74.257565 100.843924 \n", "L 74.303224 101.062484 \n", "L 74.371713 100.968875 \n", "L 74.417372 101.185837 \n", "L 74.577179 100.899534 \n", "L 74.736986 101.235401 \n", "L 74.919622 100.855143 \n", "L 75.079429 101.185595 \n", "L 75.239236 100.908734 \n", "L 75.330554 101.123509 \n", "L 75.353384 101.02724 \n", "L 75.513191 100.755352 \n", "L 75.53602 100.858118 \n", "L 75.58168 100.668325 \n", "L 75.832805 100.029279 \n", "L 76.0611 100.653448 \n", "L 76.243737 100.111006 \n", "L 76.266566 100.210913 \n", "L 76.289396 100.310599 \n", "L 76.335055 100.128733 \n", "L 76.631839 99.340847 \n", "L 76.654669 99.439766 \n", "L 76.700328 99.262842 \n", "L 76.814476 98.823798 \n", "L 76.882964 98.933567 \n", "L 76.905794 99.03176 \n", "L 76.951453 98.857738 \n", "L 77.019942 98.782383 \n", "L 77.042771 98.880123 \n", "L 77.11126 98.988578 \n", "L 77.134089 98.902329 \n", "L 77.271067 98.753058 \n", "L 77.339555 98.860787 \n", "L 77.362385 98.775559 \n", "L 77.522192 98.544214 \n", "L 77.59068 98.471728 \n", "L 77.704828 98.947656 \n", "L 77.750487 98.779964 \n", "L 77.796146 98.968937 \n", "L 77.818976 99.063145 \n", "L 77.864635 98.896052 \n", "L 77.933124 99.000543 \n", "L 77.978783 98.834418 \n", "L 78.070101 99.03176 \n", "L 78.092931 98.949086 \n", "L 78.13859 98.784212 \n", "L 78.207078 98.887788 \n", "L 78.458204 99.550586 \n", "L 78.526692 99.478069 \n", "L 78.549522 99.568828 \n", "L 78.572351 99.659419 \n", "L 78.61801 99.496532 \n", "L 78.754988 99.353202 \n", "L 78.891965 99.551091 \n", "L 79.028942 99.240289 \n", "L 79.051772 99.329366 \n", "L 79.120261 99.427432 \n", "L 79.14309 99.347997 \n", "L 79.348556 98.806397 \n", "L 79.371386 98.894717 \n", "L 79.485534 99.168172 \n", "L 79.508363 99.090166 \n", "L 79.576852 99.022055 \n", "L 79.599681 99.109344 \n", "L 79.827977 99.646665 \n", "L 79.964954 99.509507 \n", "L 80.079102 99.611937 \n", "L 80.124761 99.458975 \n", "L 80.17042 99.62877 \n", "L 80.19325 99.713431 \n", "L 80.238909 99.560984 \n", "L 80.261739 99.484966 \n", "L 80.307398 99.653783 \n", "L 80.330227 99.577891 \n", "L 80.535693 100.012435 \n", "L 80.786818 99.503419 \n", "L 80.969455 99.849023 \n", "L 81.174921 99.496385 \n", "L 81.22058 99.659261 \n", "L 81.289069 99.594125 \n", "L 81.334728 99.448272 \n", "L 81.380387 99.610244 \n", "L 81.403216 99.691014 \n", "L 81.448875 99.545634 \n", "L 81.654341 99.201723 \n", "L 81.791319 99.378835 \n", "L 81.814148 99.307402 \n", "L 81.859807 99.466703 \n", "L 82.065273 99.87739 \n", "L 82.110933 99.735258 \n", "L 82.156592 99.892079 \n", "L 82.22508 99.828604 \n", "L 82.270739 99.984615 \n", "L 82.339228 99.921171 \n", "L 82.362058 99.99884 \n", "L 82.567524 100.545004 \n", "L 82.590353 100.474621 \n", "L 82.613183 100.404354 \n", "L 82.658842 100.557211 \n", "L 82.887137 101.313901 \n", "L 82.955626 101.248597 \n", "L 83.092603 101.118935 \n", "L 83.229581 101.277606 \n", "L 83.366558 101.149069 \n", "L 83.480706 101.374841 \n", "L 83.503535 101.306057 \n", "L 83.663342 101.110387 \n", "L 83.686172 101.183492 \n", "L 83.731831 101.047343 \n", "L 83.754661 101.120323 \n", "L 83.914467 100.927386 \n", "L 84.074274 101.154599 \n", "L 84.097104 101.087403 \n", "L 84.142763 101.231343 \n", "L 84.165593 101.164251 \n", "L 84.188422 101.236053 \n", "L 84.234081 101.102165 \n", "L 84.348229 101.045356 \n", "L 84.439547 101.19285 \n", "L 84.462377 101.126474 \n", "L 84.667843 100.806682 \n", "L 84.781991 100.887852 \n", "L 84.82765 100.757181 \n", "L 84.873309 100.898093 \n", "L 84.941797 100.838099 \n", "L 85.010286 101.048311 \n", "L 85.124434 100.858717 \n", "L 85.147263 100.928459 \n", "L 85.192923 101.067604 \n", "L 85.238582 100.938447 \n", "L 85.558195 100.310304 \n", "L 85.695173 100.459785 \n", "L 85.786491 100.33946 \n", "L 85.809321 100.408076 \n", "L 85.969127 100.624008 \n", "L 86.014787 100.498751 \n", "L 86.060446 100.634638 \n", "L 86.106105 100.770114 \n", "L 86.174593 100.71278 \n", "L 86.243082 100.655708 \n", "L 86.265912 100.723084 \n", "L 86.448548 101.129786 \n", "L 86.471378 101.067699 \n", "L 86.494207 101.005717 \n", "L 86.539866 101.138775 \n", "L 86.699673 101.601466 \n", "L 86.745332 101.477702 \n", "L 86.768162 101.415952 \n", "L 86.813821 101.547275 \n", "L 86.973628 101.750653 \n", "L 87.064946 101.631452 \n", "L 87.087776 101.696399 \n", "L 87.110605 101.76124 \n", "L 87.156264 101.638844 \n", "L 87.201923 101.516794 \n", "L 87.247583 101.646193 \n", "L 87.338901 101.778694 \n", "L 87.36173 101.717869 \n", "L 87.407389 101.596482 \n", "L 87.475878 101.664383 \n", "L 87.498708 101.728478 \n", "L 87.544367 101.607585 \n", "L 87.681344 101.494714 \n", "L 87.818321 101.629507 \n", "L 87.955299 101.39465 \n", "L 87.978128 101.457999 \n", "L 88.115106 101.59173 \n", "L 88.206424 101.477082 \n", "L 88.229253 101.539894 \n", "L 88.297742 101.484642 \n", "L 88.38906 101.734639 \n", "L 88.457549 101.558273 \n", "L 88.526038 101.624103 \n", "L 88.640185 101.693129 \n", "L 88.845651 101.40956 \n", "L 88.868481 101.471215 \n", "L 88.91414 101.35559 \n", "L 89.051117 101.129439 \n", "L 89.073947 101.190905 \n", "L 89.119606 101.313606 \n", "L 89.165265 101.199043 \n", "L 89.347902 100.979516 \n", "L 89.370731 101.040562 \n", "L 89.41639 100.927197 \n", "L 89.43922 100.87063 \n", "L 89.484879 100.992459 \n", "L 89.530538 101.113951 \n", "L 89.576197 101.001017 \n", "L 89.667515 100.892805 \n", "L 89.690345 100.953335 \n", "L 89.758834 100.901584 \n", "L 89.804493 101.022246 \n", "L 89.918641 100.858896 \n", "L 89.94147 100.919017 \n", "L 90.169766 101.285754 \n", "L 90.192595 101.230165 \n", "L 90.238254 101.348903 \n", "L 90.329573 101.47091 \n", "L 90.352402 101.415458 \n", "L 90.557868 101.147229 \n", "L 90.694845 101.385808 \n", "L 90.717675 101.330986 \n", "L 90.854652 101.229208 \n", "L 91.060118 101.526867 \n", "L 91.082948 101.472508 \n", "L 91.128607 101.587997 \n", "L 91.197096 101.760683 \n", "L 91.242755 101.652155 \n", "L 91.402562 101.496744 \n", "L 91.49388 101.614472 \n", "L 91.516709 101.560702 \n", "L 91.539539 101.507006 \n", "L 91.585198 101.621001 \n", "L 91.699346 101.794497 \n", "L 91.722175 101.740927 \n", "L 91.790664 101.690574 \n", "L 91.813494 101.747099 \n", "L 91.881982 101.696862 \n", "L 91.927641 101.809564 \n", "L 92.064619 101.709353 \n", "L 92.087448 101.765457 \n", "L 92.133107 101.659526 \n", "L 92.292914 101.507826 \n", "L 92.315744 101.563699 \n", "L 92.361403 101.458672 \n", "L 92.384233 101.514481 \n", "L 92.407062 101.462057 \n", "L 92.452721 101.573456 \n", "L 92.475551 101.629055 \n", "L 92.52121 101.524396 \n", "L 92.544039 101.579922 \n", "L 92.612528 101.530978 \n", "L 92.635358 101.586357 \n", "L 92.658187 101.641683 \n", "L 92.703846 101.537518 \n", "L 92.817994 101.385187 \n", "L 92.840824 101.440366 \n", "L 92.863653 101.495472 \n", "L 92.909312 101.392053 \n", "L 93.114778 101.142024 \n", "L 93.183267 101.094594 \n", "L 93.251756 101.258848 \n", "L 93.388733 101.164083 \n", "L 93.411563 101.218568 \n", "L 93.457222 101.116958 \n", "L 93.52571 101.174997 \n", "L 93.571369 101.073745 \n", "L 93.754006 101.297393 \n", "L 93.822495 101.354675 \n", "L 93.868154 101.25398 \n", "L 93.982301 101.314584 \n", "L 94.142108 101.171496 \n", "L 94.187767 101.278278 \n", "L 94.256256 101.231921 \n", "L 94.279086 101.182168 \n", "L 94.324745 101.288572 \n", "L 94.507381 101.506943 \n", "L 94.621529 101.259584 \n", "L 94.667188 101.364969 \n", "L 94.735677 101.42081 \n", "L 94.758506 101.371561 \n", "L 94.895484 101.280003 \n", "L 94.941143 101.384641 \n", "L 94.986802 101.286742 \n", "L 95.009631 101.237883 \n", "L 95.055291 101.342268 \n", "L 95.07812 101.293451 \n", "L 95.169438 101.400822 \n", "L 95.192268 101.35212 \n", "L 95.283586 101.258249 \n", "L 95.306416 101.310105 \n", "L 95.397734 101.416825 \n", "L 95.420563 101.368386 \n", "L 95.489052 101.423175 \n", "L 95.557541 101.278342 \n", "L 95.671689 101.435782 \n", "L 95.694518 101.387658 \n", "L 95.717348 101.339598 \n", "L 95.763007 101.442049 \n", "L 95.945643 101.652029 \n", "L 96.151109 101.419222 \n", "L 96.219598 101.374841 \n", "L 96.310916 101.576936 \n", "L 96.470723 101.440892 \n", "L 96.63053 101.694454 \n", "L 96.653359 101.647297 \n", "L 96.790337 101.558999 \n", "L 96.927314 101.76062 \n", "L 96.950144 101.713769 \n", "L 97.087121 101.62588 \n", "L 97.15561 101.582151 \n", "L 97.201269 101.680838 \n", "L 97.269757 101.732799 \n", "L 97.361076 101.547275 \n", "L 97.452394 101.648296 \n", "L 97.475223 101.602086 \n", "L 97.566542 101.512725 \n", "L 97.589371 101.561659 \n", "L 97.840496 101.907641 \n", "L 98.114451 101.547275 \n", "L 98.456894 102.080127 \n", "L 98.479724 102.034758 \n", "L 98.525383 102.129955 \n", "L 98.822167 102.559325 \n", "L 98.844997 102.514093 \n", "L 98.890656 102.607985 \n", "L 98.913485 102.562795 \n", "L 98.936315 102.609668 \n", "L 98.981974 102.519424 \n", "L 99.004804 102.566254 \n", "L 99.027633 102.521201 \n", "L 99.073292 102.614693 \n", "L 99.21027 102.802583 \n", "L 99.233099 102.757614 \n", "L 99.392906 102.626301 \n", "L 99.598372 102.950939 \n", "L 99.621202 102.906285 \n", "L 99.71252 102.818565 \n", "L 99.73535 102.86447 \n", "L 99.758179 102.910323 \n", "L 99.803838 102.821435 \n", "L 99.826668 102.867235 \n", "L 99.872327 102.778527 \n", "L 99.917986 102.870001 \n", "L 100.100622 103.054757 \n", "L 100.2376 102.96894 \n", "L 100.351748 103.016664 \n", "L 100.557214 102.800281 \n", "L 100.71702 102.937891 \n", "L 100.968146 102.637152 \n", "L 101.013805 102.726576 \n", "L 101.082293 102.684939 \n", "L 101.105123 102.641873 \n", "L 101.150782 102.731023 \n", "L 101.219271 102.77697 \n", "L 101.2421 102.733978 \n", "L 101.447566 102.523009 \n", "L 101.561714 102.657402 \n", "L 101.584544 102.614788 \n", "L 101.653032 102.660472 \n", "L 101.721521 102.532998 \n", "L 101.76718 102.621076 \n", "L 101.835669 102.58027 \n", "L 101.858498 102.53795 \n", "L 101.904157 102.625765 \n", "L 102.086794 102.803645 \n", "L 102.132453 102.719268 \n", "L 102.178112 102.8064 \n", "L 102.200942 102.849908 \n", "L 102.246601 102.765689 \n", "L 102.26943 102.809144 \n", "L 102.337919 102.68312 \n", "L 102.383578 102.769905 \n", "L 102.474896 102.94297 \n", "L 102.520555 102.85915 \n", "L 102.566214 102.775488 \n", "L 102.611874 102.861768 \n", "L 102.748851 103.11962 \n", "L 102.81734 103.07894 \n", "L 102.840169 103.03724 \n", "L 102.885828 103.122774 \n", "L 102.908658 103.081116 \n", "L 103.205442 103.465434 \n", "L 103.228272 103.423892 \n", "L 103.273931 103.508374 \n", "L 103.29676 103.466874 \n", "L 103.31959 103.509047 \n", "L 103.365249 103.426163 \n", "L 103.502226 103.345087 \n", "L 103.662033 103.472541 \n", "L 103.776181 103.350019 \n", "L 103.79901 103.391834 \n", "L 104.027306 103.642988 \n", "L 104.095795 103.684961 \n", "L 104.187113 103.521632 \n", "L 104.232772 103.604369 \n", "L 104.301261 103.564236 \n", "L 104.34692 103.48295 \n", "L 104.392579 103.565456 \n", "L 104.461068 103.525512 \n", "L 104.506727 103.607786 \n", "L 104.735022 103.366841 \n", "L 104.986147 103.654102 \n", "L 105.237272 103.3754 \n", "L 105.260102 103.415985 \n", "L 105.305761 103.336392 \n", "L 105.488398 103.17953 \n", "L 105.671034 103.342785 \n", "L 105.739523 103.38379 \n", "L 105.808011 103.265631 \n", "L 105.89933 103.346728 \n", "L 105.922159 103.307446 \n", "L 105.967818 103.228989 \n", "L 106.036307 103.26991 \n", "L 106.173284 103.351428 \n", "L 106.310262 103.117496 \n", "L 106.355921 103.197162 \n", "L 106.424409 103.237821 \n", "L 106.447239 103.198981 \n", "L 106.470068 103.160184 \n", "L 106.515728 103.239566 \n", "L 106.584216 103.280036 \n", "L 106.607046 103.241301 \n", "L 106.812512 103.050236 \n", "L 107.063637 103.327518 \n", "L 107.42891 102.871378 \n", "L 107.588717 103.067564 \n", "L 107.611546 103.02966 \n", "L 107.748524 102.956165 \n", "L 107.839842 103.111292 \n", "L 107.885501 103.035853 \n", "L 107.999649 102.847826 \n", "L 108.045308 102.925179 \n", "L 108.068137 102.963809 \n", "L 108.113796 102.888821 \n", "L 108.387751 102.592971 \n", "L 108.570388 102.749371 \n", "L 108.661706 102.751883 \n", "L 108.707365 102.828354 \n", "L 108.753024 102.754386 \n", "L 108.867172 102.720004 \n", "L 109.118297 102.988265 \n", "L 109.232445 102.95361 \n", "L 109.255274 102.991377 \n", "L 109.300933 102.918019 \n", "L 109.323763 102.881388 \n", "L 109.369422 102.956817 \n", "L 109.392252 102.994479 \n", "L 109.437911 102.9213 \n", "L 109.552058 102.887002 \n", "L 109.689036 103.038092 \n", "L 109.711865 103.001671 \n", "L 109.96299 102.750001 \n", "L 110.236945 103.049847 \n", "L 110.419582 102.907936 \n", "L 110.533729 103.019755 \n", "L 110.556559 102.983891 \n", "L 110.716366 102.878696 \n", "L 110.967491 103.137347 \n", "L 111.081639 103.03151 \n", "L 111.104468 103.068005 \n", "L 111.264275 103.178973 \n", "L 111.424082 102.931362 \n", "L 111.469741 103.003942 \n", "L 111.492571 103.040184 \n", "L 111.53823 102.969697 \n", "L 111.789355 102.726292 \n", "L 111.880673 102.799713 \n", "L 111.903503 102.764784 \n", "L 111.926332 102.729898 \n", "L 111.971991 102.801994 \n", "L 112.01765 102.873975 \n", "L 112.06331 102.804265 \n", "L 112.268776 102.562501 \n", "L 112.291605 102.598417 \n", "L 112.314435 102.634302 \n", "L 112.360094 102.56515 \n", "L 112.451412 102.49748 \n", "L 112.474242 102.533303 \n", "L 112.588389 102.641873 \n", "L 112.611219 102.607439 \n", "L 112.725367 102.575654 \n", "L 112.839514 102.613821 \n", "L 112.862344 102.579555 \n", "L 112.908003 102.65061 \n", "L 113.022151 102.688503 \n", "L 113.273276 102.452532 \n", "L 113.387424 102.490583 \n", "L 113.638549 102.256935 \n", "L 113.752697 102.295134 \n", "L 113.844015 102.29813 \n", "L 113.889674 102.368208 \n", "L 113.935333 102.301116 \n", "L 114.026651 102.235675 \n", "L 114.049481 102.270635 \n", "L 114.186458 102.343404 \n", "L 114.277776 102.346306 \n", "L 114.346265 102.450555 \n", "L 114.391924 102.383916 \n", "L 114.59739 102.153075 \n", "L 114.62022 102.187741 \n", "L 114.734368 102.293052 \n", "L 114.757197 102.259995 \n", "L 115.1453 101.836218 \n", "L 115.213788 101.872471 \n", "L 115.236618 101.839845 \n", "L 115.282277 101.774657 \n", "L 115.327936 101.843451 \n", "L 115.464913 101.982439 \n", "L 115.487743 101.949898 \n", "L 115.64755 101.789419 \n", "L 115.670379 101.823622 \n", "L 115.830186 101.863134 \n", "L 115.853016 101.830834 \n", "L 115.898675 101.898946 \n", "L 115.944334 101.966952 \n", "L 116.012823 101.87021 \n", "L 116.195459 101.745248 \n", "L 116.446584 101.919701 \n", "L 116.560732 101.891176 \n", "L 116.743368 101.963493 \n", "L 116.926005 101.77453 \n", "L 116.948834 101.808029 \n", "L 117.19996 101.980105 \n", "L 117.382596 101.857215 \n", "L 117.451085 101.892143 \n", "L 117.496744 101.829226 \n", "L 117.633721 101.769999 \n", "L 117.884846 101.940351 \n", "L 117.976164 101.943663 \n", "L 118.135971 101.98142 \n", "L 118.295778 101.891396 \n", "L 118.478415 101.961726 \n", "L 118.546903 101.932392 \n", "L 118.569733 101.964975 \n", "L 118.661051 102.031646 \n", "L 118.70671 101.969843 \n", "L 118.980665 101.727048 \n", "L 119.163301 101.92296 \n", "L 119.20896 101.861715 \n", "L 119.391597 101.742998 \n", "L 119.528574 101.810847 \n", "L 119.551404 101.780429 \n", "L 119.574233 101.750022 \n", "L 119.619893 101.814264 \n", "L 119.779699 101.851379 \n", "L 119.871018 101.792478 \n", "L 119.893847 101.824442 \n", "L 120.076484 101.893236 \n", "L 120.167802 101.772585 \n", "L 120.213461 101.836218 \n", "L 120.418927 101.936303 \n", "L 120.533075 101.847878 \n", "L 120.555904 101.879494 \n", "L 120.578734 101.9111 \n", "L 120.647223 101.821256 \n", "L 120.7842 101.764889 \n", "L 120.921177 101.769999 \n", "L 121.080984 101.684287 \n", "L 121.263621 101.81316 \n", "L 121.28645 101.783531 \n", "L 121.354939 101.755636 \n", "L 121.377768 101.786895 \n", "L 121.560405 101.85425 \n", "L 121.697382 101.738046 \n", "L 121.720212 101.769158 \n", "L 121.902848 101.836218 \n", "L 122.062655 101.691394 \n", "L 122.085485 101.722359 \n", "L 122.199632 101.696567 \n", "L 122.268121 101.669136 \n", "L 122.31378 101.730844 \n", "L 122.519246 101.828132 \n", "L 122.770371 101.688082 \n", "L 122.884519 101.722085 \n", "L 122.907349 101.693203 \n", "L 123.021496 101.727132 \n", "L 123.158474 101.791395 \n", "L 123.181303 101.762597 \n", "L 123.386769 101.681448 \n", "L 123.523747 101.745448 \n", "L 123.546576 101.716828 \n", "L 123.660724 101.7504 \n", "L 123.774872 101.725156 \n", "L 123.957508 101.614693 \n", "L 123.980338 101.644816 \n", "L 124.094485 101.619866 \n", "L 124.185804 101.56496 \n", "L 124.231463 101.625028 \n", "L 124.39127 101.660104 \n", "L 124.688054 101.468271 \n", "L 124.779372 101.529737 \n", "L 124.825031 101.473686 \n", "L 125.213134 101.172673 \n", "L 125.464259 101.326791 \n", "L 125.509918 101.271413 \n", "L 125.578407 101.360079 \n", "L 125.738213 101.452153 \n", "L 125.761043 101.424532 \n", "L 125.852361 101.428138 \n", "L 125.875191 101.457515 \n", "L 126.012168 101.462867 \n", "L 126.194805 101.41325 \n", "L 126.308952 101.389446 \n", "L 126.468759 101.367555 \n", "L 126.651396 101.431177 \n", "L 126.971009 101.219241 \n", "L 127.222135 101.369427 \n", "L 127.427601 101.238198 \n", "L 127.45043 101.266997 \n", "L 127.633067 101.385598 \n", "L 127.655896 101.358713 \n", "L 127.838533 101.310484 \n", "L 128.112487 101.487039 \n", "L 128.249465 101.49217 \n", "L 128.340783 101.495566 \n", "L 128.363612 101.468913 \n", "L 128.454931 101.47234 \n", "L 128.47776 101.500655 \n", "L 128.637567 101.533985 \n", "L 128.797374 101.512463 \n", "L 128.865863 101.596955 \n", "L 128.934351 101.517488 \n", "L 129.00284 101.492727 \n", "L 129.048499 101.548926 \n", "L 129.231135 101.609888 \n", "L 129.299624 101.53081 \n", "L 129.368113 101.614703 \n", "L 129.52792 101.647392 \n", "L 129.642067 101.624292 \n", "L 129.664897 101.652113 \n", "L 129.916022 101.795443 \n", "L 130.121488 101.667758 \n", "L 130.144318 101.6954 \n", "L 130.304125 101.781165 \n", "L 130.326954 101.755111 \n", "L 130.441102 101.785602 \n", "L 130.509591 101.868002 \n", "L 130.578079 101.790028 \n", "L 130.783545 101.716744 \n", "L 130.897693 101.747099 \n", "L 130.920523 101.721265 \n", "L 131.125989 101.648496 \n", "L 131.331455 101.734745 \n", "L 131.468432 101.739213 \n", "L 131.605409 101.743661 \n", "L 131.742387 101.748087 \n", "L 131.902193 101.831959 \n", "L 131.925023 101.806441 \n", "L 132.153319 101.708964 \n", "L 132.335955 101.767097 \n", "L 132.450103 101.796779 \n", "L 132.564251 101.774341 \n", "L 132.769717 101.70297 \n", "L 132.998012 101.813843 \n", "L 133.180649 101.716229 \n", "L 133.203478 101.742767 \n", "L 133.363285 101.773574 \n", "L 133.500262 101.777853 \n", "L 133.660069 101.9111 \n", "L 133.705728 101.861168 \n", "L 133.934024 101.71461 \n", "L 133.956853 101.740895 \n", "L 134.11666 101.822433 \n", "L 134.13949 101.79762 \n", "L 134.253638 101.724735 \n", "L 134.299297 101.777064 \n", "L 134.459104 101.858182 \n", "L 134.481933 101.833473 \n", "L 134.527592 101.784098 \n", "L 134.596081 101.862251 \n", "L 134.687399 101.864964 \n", "L 134.710229 101.840318 \n", "L 134.847206 101.793866 \n", "L 134.870036 101.819826 \n", "L 134.938524 101.897621 \n", "L 135.007013 101.823947 \n", "L 135.075502 101.800795 \n", "L 135.121161 101.852557 \n", "L 135.532093 102.165345 \n", "L 135.714729 102.119997 \n", "L 135.897366 102.27441 \n", "L 135.943025 102.225655 \n", "L 136.125661 102.180402 \n", "L 136.285468 102.209253 \n", "L 136.422445 102.11351 \n", "L 136.468105 102.164231 \n", "L 136.71923 102.294019 \n", "L 136.856207 102.297247 \n", "L 136.901866 102.3476 \n", "L 136.970355 102.275314 \n", "L 137.152991 102.230523 \n", "L 137.426946 102.384032 \n", "L 137.655241 102.291464 \n", "L 137.883537 102.394167 \n", "L 137.929196 102.346506 \n", "L 137.997685 102.420989 \n", "L 138.340128 102.646299 \n", "L 138.614083 102.50668 \n", "L 138.728231 102.533113 \n", "L 138.75106 102.509488 \n", "L 139.047844 102.347495 \n", "L 139.207651 102.37501 \n", "L 139.504436 102.214373 \n", "L 139.687072 102.266356 \n", "L 139.80122 102.245191 \n", "L 139.824049 102.26951 \n", "L 140.098004 102.465486 \n", "L 140.120834 102.442249 \n", "L 140.3263 102.375599 \n", "L 140.417618 102.377576 \n", "L 140.440447 102.354455 \n", "L 140.554595 102.38053 \n", "L 140.714402 102.454593 \n", "L 140.737232 102.431524 \n", "L 140.965527 102.342406 \n", "L 141.148164 102.393232 \n", "L 141.376459 102.257924 \n", "L 141.399289 102.281791 \n", "L 141.513436 102.307677 \n", "L 141.536266 102.284872 \n", "L 141.627584 102.286911 \n", "L 141.650414 102.310705 \n", "L 141.810221 102.383947 \n", "L 141.83305 102.361205 \n", "L 141.947198 102.340439 \n", "L 141.970028 102.364139 \n", "L 142.107005 102.367062 \n", "L 142.175494 102.345371 \n", "L 142.221153 102.392622 \n", "L 142.3353 102.371909 \n", "L 142.586426 102.262045 \n", "L 142.700573 102.333616 \n", "L 142.746232 102.288615 \n", "L 142.906039 102.269184 \n", "L 142.951698 102.316078 \n", "L 143.020187 102.248797 \n", "L 143.202824 102.20714 \n", "L 143.225653 102.230523 \n", "L 143.294142 102.163527 \n", "L 143.476778 102.122174 \n", "L 143.659415 102.172012 \n", "L 143.933369 102.042224 \n", "L 144.207324 102.184618 \n", "L 144.344301 102.187741 \n", "L 144.504108 102.213921 \n", "L 144.686745 102.173021 \n", "L 144.823722 102.176154 \n", "L 145.006358 102.135474 \n", "L 145.143336 102.13865 \n", "L 145.280313 102.141815 \n", "L 145.44012 102.167795 \n", "L 145.599927 102.149164 \n", "L 145.94237 102.356621 \n", "L 146.056518 102.381119 \n", "L 146.261984 102.451575 \n", "L 146.46745 102.389394 \n", "L 146.604427 102.392138 \n", "L 146.741405 102.394872 \n", "L 146.901212 102.420001 \n", "L 147.038189 102.422682 \n", "L 147.175166 102.425363 \n", "L 147.289314 102.449441 \n", "L 147.540439 102.563258 \n", "L 147.677416 102.565707 \n", "L 147.905712 102.656635 \n", "L 148.01986 102.680239 \n", "L 148.225326 102.748561 \n", "L 148.407962 102.708249 \n", "L 148.52211 102.688525 \n", "L 148.636258 102.71194 \n", "L 148.727576 102.670366 \n", "L 148.864553 102.672627 \n", "L 149.001531 102.674888 \n", "L 149.092849 102.719268 \n", "L 149.252656 102.74322 \n", "L 149.435292 102.70336 \n", "L 149.777736 102.900408 \n", "L 150.028861 102.797757 \n", "L 150.165838 102.799807 \n", "L 150.325645 102.781039 \n", "L 150.55394 102.868918 \n", "L 150.713747 102.850108 \n", "L 150.873554 102.873439 \n", "L 151.07902 102.813339 \n", "L 151.261657 102.85792 \n", "L 151.375804 102.880462 \n", "L 151.558441 102.92478 \n", "L 151.672589 102.947186 \n", "L 151.923714 103.054547 \n", "L 152.12918 102.99471 \n", "L 152.357475 103.080465 \n", "L 152.540112 103.041246 \n", "L 152.65426 103.021963 \n", "L 152.814066 103.003311 \n", "L 153.019532 103.067595 \n", "L 153.224998 103.00841 \n", "L 153.407635 103.051656 \n", "L 153.498953 103.093692 \n", "L 153.750078 103.198697 \n", "L 153.887056 103.200106 \n", "L 154.138181 103.304429 \n", "L 154.389306 103.205269 \n", "L 154.549113 103.22717 \n", "L 154.800238 103.12862 \n", "L 155.005704 103.191369 \n", "L 155.233999 103.113227 \n", "L 155.485124 103.216361 \n", "L 155.73625 103.118747 \n", "L 155.873227 103.120251 \n", "L 156.078693 103.062622 \n", "L 156.329818 103.16502 \n", "L 156.74075 102.931393 \n", "L 156.969046 103.013405 \n", "L 157.151682 102.976079 \n", "L 157.402807 103.077615 \n", "L 157.722421 102.924097 \n", "L 157.882228 102.945714 \n", "L 158.133353 102.851043 \n", "L 158.407308 102.971705 \n", "L 158.544285 102.973356 \n", "L 158.681262 102.975007 \n", "L 158.79541 102.995814 \n", "L 159.023706 103.076059 \n", "L 159.115024 103.115803 \n", "L 159.343319 103.195543 \n", "L 159.640104 103.063537 \n", "L 159.731422 103.026022 \n", "L 159.914058 102.989663 \n", "L 160.096695 103.030185 \n", "L 160.439138 102.861905 \n", "L 160.598945 102.883059 \n", "L 160.758752 102.866016 \n", "L 160.918559 102.887097 \n", "L 161.078366 102.870095 \n", "L 161.443639 103.06421 \n", "L 161.557786 103.084376 \n", "L 161.717593 103.104984 \n", "L 161.923059 103.050541 \n", "L 162.082866 103.071117 \n", "L 162.219843 103.072589 \n", "L 162.585116 103.263949 \n", "L 162.722094 103.265169 \n", "L 163.018878 103.398489 \n", "L 163.133026 103.418014 \n", "L 163.270003 103.419024 \n", "L 163.40698 103.420044 \n", "L 163.521128 103.402306 \n", "L 163.726594 103.3482 \n", "L 163.840742 103.330588 \n", "L 163.95489 103.350029 \n", "L 164.183185 103.425742 \n", "L 164.479969 103.298951 \n", "L 164.731095 103.392948 \n", "L 164.868072 103.393989 \n", "L 164.98222 103.376504 \n", "L 165.164856 103.341292 \n", "L 165.301833 103.342385 \n", "L 165.438811 103.343479 \n", "L 165.667106 103.418183 \n", "L 165.895402 103.347096 \n", "L 166.100868 103.403168 \n", "L 166.351993 103.314459 \n", "L 166.625948 103.42508 \n", "L 166.740095 103.443943 \n", "L 166.968391 103.517574 \n", "L 167.151027 103.482751 \n", "L 167.333664 103.519855 \n", "L 167.447812 103.538508 \n", "L 167.607619 103.557381 \n", "L 167.721766 103.575939 \n", "L 167.858744 103.576727 \n", "L 168.018551 103.559789 \n", "L 168.132698 103.54264 \n", "L 168.246846 103.561124 \n", "L 168.383823 103.561923 \n", "L 168.56646 103.527468 \n", "L 168.817585 103.617586 \n", "L 169.228517 103.40791 \n", "L 169.365494 103.408888 \n", "L 169.525301 103.392412 \n", "L 169.685108 103.411149 \n", "L 169.867745 103.377324 \n", "L 170.050381 103.378659 \n", "L 170.255847 103.327676 \n", "L 170.484143 103.364371 \n", "L 170.62112 103.36538 \n", "L 170.895075 103.437003 \n", "L 171.032052 103.472689 \n", "L 171.237518 103.491362 \n", "L 171.420154 103.492508 \n", "L 171.808257 103.650295 \n", "L 171.990893 103.651189 \n", "L 172.17353 103.652083 \n", "L 172.333337 103.670041 \n", "L 172.607291 103.739908 \n", "L 172.789928 103.740655 \n", "L 172.995394 103.758582 \n", "L 173.292178 103.674499 \n", "L 173.451985 103.658213 \n", "L 173.70311 103.608449 \n", "L 173.885746 103.609384 \n", "L 174.045553 103.627143 \n", "L 174.182531 103.593992 \n", "L 174.342337 103.611729 \n", "L 174.661951 103.714464 \n", "L 174.844588 103.715231 \n", "L 175.095713 103.766657 \n", "L 175.324008 103.733999 \n", "L 175.506645 103.734746 \n", "L 175.73494 103.702257 \n", "L 175.940406 103.719816 \n", "L 176.145872 103.704013 \n", "L 176.511145 103.838322 \n", "L 176.670952 103.855397 \n", "L 176.876418 103.872556 \n", "L 177.081884 103.856627 \n", "L 177.218861 103.824044 \n", "L 177.378668 103.841035 \n", "L 177.606964 103.874638 \n", "L 177.880919 103.809765 \n", "L 178.109214 103.843285 \n", "L 178.246191 103.876426 \n", "L 178.405998 103.860539 \n", "L 178.565805 103.877319 \n", "L 178.771271 103.894174 \n", "L 178.999567 103.862252 \n", "L 179.227862 103.895372 \n", "L 179.410499 103.895856 \n", "L 179.707283 103.977468 \n", "L 179.935579 103.945641 \n", "L 180.095386 103.92987 \n", "L 180.300852 103.914256 \n", "L 180.551977 103.963042 \n", "L 180.780272 103.931447 \n", "L 180.940079 103.915812 \n", "L 181.168375 103.884395 \n", "L 181.442329 103.948911 \n", "L 181.602136 103.965176 \n", "L 181.830432 103.997434 \n", "L 181.990239 104.013595 \n", "L 182.150046 103.997991 \n", "L 182.309852 104.01411 \n", "L 182.492489 104.014404 \n", "L 182.675125 104.014699 \n", "L 182.880591 104.030796 \n", "L 183.063228 104.031069 \n", "L 183.223035 104.015582 \n", "L 183.428501 104.00021 \n", "L 183.542648 103.953348 \n", "L 183.702455 103.96934 \n", "L 183.907921 103.985385 \n", "L 184.159046 103.939006 \n", "L 184.547149 104.079929 \n", "L 184.889592 103.971632 \n", "L 185.117888 104.003059 \n", "L 185.369013 103.957091 \n", "L 185.551649 103.957459 \n", "L 185.779945 103.927083 \n", "L 185.962581 103.927493 \n", "L 186.168047 103.912574 \n", "L 186.350684 103.913005 \n", "L 186.55615 103.898159 \n", "L 186.761616 103.913961 \n", "L 186.875764 103.960098 \n", "L 187.172548 104.036947 \n", "L 187.400843 104.006813 \n", "L 187.56065 103.991862 \n", "L 187.766116 103.977026 \n", "L 187.971582 103.992566 \n", "L 188.199878 103.962685 \n", "L 188.382514 103.963032 \n", "L 188.633639 103.918251 \n", "L 188.930424 103.994196 \n", "L 189.11306 103.994501 \n", "L 189.387015 104.054968 \n", "L 189.523992 104.085113 \n", "L 189.889265 104.205049 \n", "L 190.071901 104.20507 \n", "L 190.231708 104.190182 \n", "L 190.482833 104.145591 \n", "L 190.619811 104.11594 \n", "L 190.848106 104.086427 \n", "L 191.122061 104.145948 \n", "L 191.350356 104.116498 \n", "L 191.601482 104.160962 \n", "L 191.852607 104.116876 \n", "L 192.309198 104.293416 \n", "L 192.491834 104.293316 \n", "L 192.72013 104.322472 \n", "L 192.902766 104.322335 \n", "L 193.108232 104.336782 \n", "L 193.336528 104.307442 \n", "L 193.519164 104.307326 \n", "L 193.793119 104.249051 \n", "L 194.089903 104.321463 \n", "L 194.318199 104.292354 \n", "L 194.455176 104.263371 \n", "L 194.888938 104.104701 \n", "L 195.322699 104.263103 \n", "L 195.482506 104.277403 \n", "L 195.642313 104.263003 \n", "L 195.939097 104.191391 \n", "L 196.144563 104.205722 \n", "L 196.418518 104.148724 \n", "L 196.601154 104.148818 \n", "L 196.82945 104.120504 \n", "L 196.989257 104.106414 \n", "L 197.194723 104.092399 \n", "L 197.35453 104.078383 \n", "L 197.514336 104.092683 \n", "L 197.719802 104.106993 \n", "L 197.925268 104.093051 \n", "L 198.062246 104.064988 \n", "L 198.267712 104.051141 \n", "L 198.427519 104.037273 \n", "L 198.587326 104.05152 \n", "L 198.747132 104.037683 \n", "L 198.929769 104.037914 \n", "L 199.295042 104.150143 \n", "L 199.477678 104.150227 \n", "L 199.705974 104.178206 \n", "L 200.002758 104.10878 \n", "L 200.208224 104.122817 \n", "L 200.550667 104.026096 \n", "L 200.710474 104.01247 \n", "L 200.847452 104.040311 \n", "L 201.052918 104.054369 \n", "L 201.235554 104.054569 \n", "L 201.532338 104.1237 \n", "L 201.851952 104.041541 \n", "L 202.103077 104.082957 \n", "L 202.377032 104.028504 \n", "L 202.628157 104.069793 \n", "L 202.787964 104.083599 \n", "L 202.947771 104.070119 \n", "L 203.198896 104.029566 \n", "L 203.586998 104.152162 \n", "L 203.838123 104.111651 \n", "L 204.112078 104.165925 \n", "L 204.363203 104.125519 \n", "L 204.52301 104.112145 \n", "L 204.682817 104.125729 \n", "L 204.888283 104.139303 \n", "L 205.185067 104.072348 \n", "L 205.367704 104.072527 \n", "L 205.55034 104.072706 \n", "L 205.869954 104.153192 \n", "L 206.07542 104.139944 \n", "L 206.235227 104.126696 \n", "L 206.417863 104.126812 \n", "L 206.691818 104.18013 \n", "L 206.805966 104.22 \n", "L 207.07992 104.273018 \n", "L 207.559341 104.101084 \n", "L 207.856125 104.16725 \n", "L 208.13008 104.114689 \n", "L 208.335546 104.127979 \n", "L 208.723648 104.010188 \n", "L 208.837796 103.971033 \n", "L 208.997603 103.984386 \n", "L 209.13458 103.958437 \n", "L 209.454194 103.880673 \n", "L 209.591171 103.854892 \n", "L 209.796637 103.842349 \n", "L 209.933615 103.816662 \n", "L 210.093422 103.830079 \n", "L 210.458695 103.934727 \n", "L 210.618501 103.947975 \n", "L 210.869627 103.987214 \n", "L 211.052263 103.987488 \n", "L 211.326218 104.03947 \n", "L 211.508854 104.03968 \n", "L 211.73715 104.065672 \n", "L 211.919786 104.06585 \n", "L 212.193741 104.117412 \n", "L 212.490525 104.053612 \n", "L 212.695991 104.066607 \n", "L 212.855798 104.079529 \n", "L 213.084093 104.105237 \n", "L 213.312389 104.07994 \n", "L 213.472196 104.067364 \n", "L 213.746151 104.016833 \n", "L 214.088594 104.080623 \n", "L 214.316889 104.080823 \n", "L 214.613674 104.118968 \n", "L 214.79631 104.144318 \n", "L 215.093094 104.182222 \n", "L 215.32139 104.182285 \n", "L 215.618174 104.22 \n", "L 216.006277 104.132364 \n", "L 216.166083 104.094933 \n", "L 216.394379 104.095101 \n", "L 216.645504 104.107771 \n", "L 216.987947 104.045789 \n", "L 217.35322 104.120672 \n", "L 217.604345 104.108433 \n", "L 218.083766 104.244719 \n", "L 218.471869 104.158344 \n", "L 218.791482 104.207698 \n", "L 218.974119 104.232291 \n", "L 219.202414 104.232275 \n", "L 219.43071 104.23226 \n", "L 219.750324 104.281172 \n", "L 220.092767 104.22 \n", "L 220.343892 104.232191 \n", "L 220.754824 104.134877 \n", "L 221.005949 104.147147 \n", "L 221.279904 104.123027 \n", "L 221.5082 104.123153 \n", "L 221.941961 104.014741 \n", "L 222.284404 104.075408 \n", "L 222.5127 104.075608 \n", "L 222.763825 104.087836 \n", "L 222.900802 104.13597 \n", "L 223.03778 104.088057 \n", "L 223.288905 104.07627 \n", "L 223.699837 104.172213 \n", "L 223.973792 104.148429 \n", "L 224.202087 104.148524 \n", "L 224.54453 104.089224 \n", "L 224.886974 104.148818 \n", "L 225.206588 104.101578 \n", "L 225.480542 104.125414 \n", "L 225.822986 104.066607 \n", "L 226.051281 104.066818 \n", "L 226.393724 104.008316 \n", "L 226.64485 104.020376 \n", "L 226.895975 104.008937 \n", "L 227.169929 104.032688 \n", "L 227.329736 104.067953 \n", "L 227.580861 104.079855 \n", "L 227.877646 104.045116 \n", "L 228.060282 104.022006 \n", "L 228.334237 103.999064 \n", "L 228.585362 104.010998 \n", "L 228.904976 103.965019 \n", "L 229.224589 104.011765 \n", "L 229.452885 104.012038 \n", "L 229.795328 104.070098 \n", "L 229.977965 104.093293 \n", "L 230.366067 104.174032 \n", "L 230.548704 104.197037 \n", "L 230.70851 104.162645 \n", "L 231.005295 104.128389 \n", "L 231.279249 104.151394 \n", "L 231.484715 104.162897 \n", "L 231.713011 104.162971 \n", "L 232.055454 104.106162 \n", "L 232.329409 104.129073 \n", "L 232.717511 104.049879 \n", "L 232.945807 104.0501 \n", "L 233.31108 103.98263 \n", "L 233.676353 104.050794 \n", "L 233.858989 104.073505 \n", "L 234.247092 104.15253 \n", "L 234.475387 104.152625 \n", "L 234.726512 104.163927 \n", "L 235.068956 104.108076 \n", "L 235.411399 104.164138 \n", "L 235.776672 104.097362 \n", "L 236.004967 104.097519 \n", "L 236.141945 104.14211 \n", "L 236.324581 104.119967 \n", "L 236.552877 104.120083 \n", "L 236.735513 104.098003 \n", "L 236.986638 104.0871 \n", "L 237.443229 104.19791 \n", "L 237.740014 104.164853 \n", "L 237.94548 104.153897 \n", "L 238.173775 104.153981 \n", "L 238.402071 104.154065 \n", "L 238.744514 104.099338 \n", "L 239.155446 104.187164 \n", "L 239.360912 104.198141 \n", "L 239.726185 104.263639 \n", "L 240.045799 104.22 \n", "L 240.388242 104.274359 \n", "L 240.616538 104.27429 \n", "L 240.867663 104.285062 \n", "L 241.164447 104.252479 \n", "L 241.643868 104.371174 \n", "L 242.191777 104.22 \n", "L 242.511391 104.262993 \n", "L 242.648368 104.305923 \n", "L 242.876664 104.305817 \n", "L 243.310425 104.209296 \n", "L 243.538721 104.209307 \n", "L 243.721357 104.230677 \n", "L 243.903994 104.209328 \n", "L 244.155119 104.198688 \n", "L 244.451903 104.23064 \n", "L 244.748687 104.198761 \n", "L 244.931324 104.177554 \n", "L 245.228108 104.145843 \n", "L 245.433574 104.135339 \n", "L 245.593381 104.167124 \n", "L 245.798847 104.15662 \n", "L 246.118461 104.114552 \n", "L 246.369586 104.125214 \n", "L 246.552222 104.146348 \n", "L 246.803347 104.156957 \n", "L 247.031643 104.15703 \n", "L 247.214279 104.136117 \n", "L 247.533893 104.094386 \n", "L 247.853507 104.136401 \n", "L 248.127461 104.115646 \n", "L 248.492734 104.178343 \n", "L 248.652541 104.209591 \n", "L 248.880837 104.209601 \n", "L 249.154791 104.188857 \n", "L 249.520064 104.25108 \n", "L 249.72553 104.2614 \n", "L 249.953826 104.261353 \n", "L 250.410417 104.158124 \n", "L 250.638713 104.158197 \n", "L 251.049645 104.076081 \n", "L 251.27794 104.076249 \n", "L 251.551895 104.055946 \n", "L 251.825849 104.076649 \n", "L 251.985656 104.107466 \n", "L 252.213952 104.107592 \n", "L 252.487907 104.087342 \n", "L 252.898839 104.169079 \n", "L 253.3326 104.077742 \n", "L 253.652214 104.118548 \n", "L 253.85768 104.128789 \n", "L 254.177294 104.169405 \n", "L 254.474078 104.159385 \n", "L 254.953499 104.250239 \n", "L 255.41009 104.169721 \n", "L 255.684044 104.169794 \n", "L 255.957999 104.169857 \n", "L 256.231954 104.169931 \n", "L 256.460249 104.189992 \n", "L 256.734204 104.190034 \n", "L 257.076647 104.160163 \n", "L 257.396261 104.180172 \n", "L 257.784363 104.130555 \n", "L 258.355102 104.259639 \n", "L 258.583398 104.279395 \n", "L 258.948671 104.318813 \n", "L 259.176966 104.338443 \n", "L 259.656387 104.426789 \n", "L 259.861853 104.456092 \n", "L 260.135808 104.455777 \n", "L 260.569569 104.386656 \n", "L 260.889183 104.405971 \n", "L 261.07182 104.444926 \n", "L 261.345774 104.444627 \n", "L 261.642558 104.434549 \n", "L 261.870854 104.414829 \n", "L 262.190468 104.395078 \n", "L 262.57857 104.443291 \n", "L 262.921014 104.413846 \n", "L 263.331946 104.471501 \n", "L 263.674389 104.442119 \n", "L 263.902684 104.422583 \n", "L 264.176639 104.422315 \n", "L 264.564742 104.470024 \n", "L 264.930014 104.431189 \n", "L 265.272458 104.459594 \n", "L 265.729049 104.382571 \n", "L 266.094322 104.420475 \n", "L 266.368276 104.420218 \n", "L 266.68789 104.438954 \n", "L 267.007504 104.419613 \n", "L 267.21297 104.39093 \n", "L 267.555413 104.362211 \n", "L 267.829368 104.362027 \n", "L 268.080493 104.371321 \n", "L 268.514255 104.437077 \n", "L 268.811039 104.427352 \n", "L 268.993675 104.389506 \n", "L 269.518755 104.285756 \n", "L 269.76988 104.276299 \n", "L 270.203642 104.210632 \n", "L 270.568915 104.248047 \n", "L 270.865699 104.238668 \n", "L 271.230972 104.275915 \n", "L 271.710392 104.192106 \n", "L 272.075665 104.229284 \n", "L 272.418109 104.201463 \n", "L 272.692063 104.201484 \n", "L 273.217143 104.099948 \n", "L 273.422609 104.07238 \n", "L 273.742223 104.05418 \n", "L 274.198814 104.128063 \n", "L 274.495598 104.119011 \n", "L 274.769553 104.119137 \n", "L 275.226144 104.046147 \n", "L 275.522928 104.055515 \n", "L 275.819712 104.046609 \n", "L 276.093667 104.04683 \n", "L 276.527429 103.983492 \n", "L 276.824213 103.992892 \n", "L 277.098168 103.993176 \n", "L 277.303634 103.966186 \n", "L 277.531929 103.984554 \n", "L 277.737395 103.957638 \n", "L 278.125498 103.912931 \n", "L 278.308134 103.877088 \n", "L 278.559259 103.886488 \n", "L 278.833214 103.886898 \n", "L 279.266975 103.824643 \n", "L 279.632248 103.861169 \n", "L 279.974692 103.834852 \n", "L 280.271476 103.844304 \n", "L 280.56826 103.835862 \n", "L 280.819385 103.827366 \n", "L 281.230317 103.774637 \n", "L 281.481442 103.766236 \n", "L 281.732567 103.775625 \n", "L 281.983693 103.767235 \n", "L 282.417454 103.706074 \n", "L 282.759897 103.73339 \n", "L 283.12517 103.69884 \n", "L 283.444784 103.717208 \n", "L 283.467614 103.708439 \n", "L 283.467614 103.708439 \n", "\" clip-path=\"url(#p3a05faa0b4)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 43.78125 104.22 \n", "L 294.88125 104.22 \n", "\" clip-path=\"url(#p3a05faa0b4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 43.78125 201.24 \n", "L 43.78125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 294.88125 201.24 \n", "L 294.88125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 43.78125 201.24 \n", "L 294.88125 201.24 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 43.78125 7.2 \n", "L 294.88125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 182.759375 44.55625 \n", "L 287.88125 44.55625 \n", "Q 289.88125 44.55625 289.88125 42.55625 \n", "L 289.88125 14.2 \n", "Q 289.88125 12.2 287.88125 12.2 \n", "L 182.759375 12.2 \n", "Q 180.759375 12.2 180.759375 14.2 \n", "L 180.759375 42.55625 \n", "Q 180.759375 44.55625 182.759375 44.55625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_16\">\n", "     <path d=\"M 184.759375 20.298438 \n", "L 194.759375 20.298438 \n", "L 204.759375 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- P(coin=heads) -->\n", "     <g transform=\"translate(212.759375 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"154.296875\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"215.478516\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"243.261719\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"306.640625\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"390.429688\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"453.808594\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"515.332031\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"576.611328\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"640.087891\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"692.1875\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_17\">\n", "     <path d=\"M 184.759375 34.976562 \n", "L 194.759375 34.976562 \n", "L 204.759375 34.976562 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- P(coin=tails) -->\n", "     <g transform=\"translate(212.759375 38.476562) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"60.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"99.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"154.296875\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"215.478516\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"243.261719\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"306.640625\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"390.429688\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"429.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"490.917969\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"518.701172\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"546.484375\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"598.583984\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p3a05faa0b4\">\n", "   <rect x=\"43.78125\" y=\"7.2\" width=\"251.1\" height=\"194.04\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 450x350 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["counts = Multinomial(1, fair_probs).sample((10000,))\n", "cum_counts = counts.cumsum(dim=0)\n", "estimates = cum_counts / cum_counts.sum(dim=1, keepdims=True)\n", "estimates = estimates.numpy()\n", "\n", "d2l.set_figsize((4.5, 3.5))\n", "d2l.plt.plot(estimates[:, 0], label=(\"P(coin=heads)\"))\n", "d2l.plt.plot(estimates[:, 1], label=(\"P(coin=tails)\"))\n", "d2l.plt.axhline(y=0.5, color='black', linestyle='dashed')\n", "d2l.plt.gca().set_xlabel('Samples')\n", "d2l.plt.gca().set_ylabel('Estimated probability')\n", "d2l.plt.legend();"]}, {"cell_type": "markdown", "id": "ec40585d", "metadata": {"origin_pos": 29}, "source": ["Each solid curve corresponds to one of the two values of the coin\n", "and gives our estimated probability that the coin turns up that value\n", "after each group of experiments.\n", "The dashed black line gives the true underlying probability.\n", "As we get more data by conducting more experiments,\n", "the curves converge towards the true probability.\n", "You might already begin to see the shape\n", "of some of the more advanced questions\n", "that preoccupy statisticians:\n", "How quickly does this convergence happen?\n", "If we had already tested many coins\n", "manufactured at the same plant,\n", "how might we incorporate this information?\n", "\n", "##  A More Formal Treatment\n", "\n", "We have already gotten pretty far: posing\n", "a probabilistic model,\n", "generating synthetic data,\n", "running a statistical estimator,\n", "empirically assessing convergence,\n", "and reporting error metrics (checking the deviation).\n", "However, to go much further,\n", "we will need to be more precise.\n", "\n", "\n", "When dealing with randomness,\n", "we denote the set of possible outcomes $\\mathcal{S}$\n", "and call it the *sample space* or *outcome space*.\n", "Here, each element is a distinct possible *outcome*.\n", "In the case of rolling a single coin,\n", "$\\mathcal{S} = \\{\\textrm{heads}, \\textrm{tails}\\}$.\n", "For a single die, $\\mathcal{S} = \\{1, 2, 3, 4, 5, 6\\}$.\n", "When flipping two coins, possible outcomes are\n", "$\\{(\\textrm{heads}, \\textrm{heads}), (\\textrm{heads}, \\textrm{tails}), (\\textrm{tails}, \\textrm{heads}),  (\\textrm{tails}, \\textrm{tails})\\}$.\n", "*Events* are subsets of the sample space.\n", "For instance, the event \"the first coin toss comes up heads\"\n", "corresponds to the set $\\{(\\textrm{heads}, \\textrm{heads}), (\\textrm{heads}, \\textrm{tails})\\}$.\n", "Whenever the outcome $z$ of a random experiment satisfies\n", "$z \\in \\mathcal{A}$, then event $\\mathcal{A}$ has occurred.\n", "For a single roll of a die, we could define the events\n", "\"seeing a $5$\" ($\\mathcal{A} = \\{5\\}$)\n", "and \"seeing an odd number\"  ($\\mathcal{B} = \\{1, 3, 5\\}$).\n", "In this case, if the die came up $5$,\n", "we would say that both $\\mathcal{A}$ and $\\mathcal{B}$ occurred.\n", "On the other hand, if $z = 3$,\n", "then $\\mathcal{A}$ did not occur\n", "but $\\mathcal{B}$ did.\n", "\n", "\n", "A *probability* function maps events\n", "onto real values ${P: \\mathcal{A} \\subseteq \\mathcal{S} \\rightarrow [0,1]}$.\n", "The probability, denoted $P(\\mathcal{A})$, of an event $\\mathcal{A}$\n", "in the given sample space $\\mathcal{S}$,\n", "has the following properties:\n", "\n", "* The probability of any event $\\mathcal{A}$ is a nonnegative real number, i.e., $P(\\mathcal{A}) \\geq 0$;\n", "* The probability of the entire sample space is $1$, i.e., $P(\\mathcal{S}) = 1$;\n", "* For any countable sequence of events $\\mathcal{A}_1, \\mathcal{A}_2, \\ldots$ that are *mutually exclusive* (i.e., $\\mathcal{A}_i \\cap \\mathcal{A}_j = \\emptyset$ for all $i \\neq j$), the probability that any of them happens is equal to the sum of their individual probabilities, i.e., $P(\\bigcup_{i=1}^{\\infty} \\mathcal{A}_i) = \\sum_{i=1}^{\\infty} P(\\mathcal{A}_i)$.\n", "\n", "These axioms of probability theory,\n", "proposed by :citet:<PERSON><PERSON><PERSON><PERSON><PERSON>.1933`,\n", "can be applied to rapidly derive a number of important consequences.\n", "For instance, it follows immediately\n", "that the probability of any event $\\mathcal{A}$\n", "*or* its complement $\\mathcal{A}'$ occurring is 1\n", "(because $\\mathcal{A} \\cup \\mathcal{A}' = \\mathcal{S}$).\n", "We can also prove that $P(\\emptyset) = 0$\n", "because $1 = P(\\mathcal{S} \\cup \\mathcal{S}') = P(\\mathcal{S} \\cup \\emptyset) = P(\\mathcal{S}) + P(\\emptyset) = 1 + P(\\emptyset)$.\n", "Consequently, the probability of any event $\\mathcal{A}$\n", "*and* its complement $\\mathcal{A}'$ occurring simultaneously\n", "is $P(\\mathcal{A} \\cap \\mathcal{A}') = 0$.\n", "Informally, this tells us that impossible events\n", "have zero probability of occurring.\n", "\n", "\n", "\n", "## Random Variables\n", "\n", "When we spoke about events like the roll of a die\n", "coming up odds or the first coin toss coming up heads,\n", "we were invoking the idea of a *random variable*.\n", "Formally, random variables are mappings\n", "from an underlying sample space\n", "to a set of (possibly many) values.\n", "You might wonder how a random variable\n", "is different from the sample space,\n", "since both are collections of outcomes.\n", "Importantly, random variables can be much coarser\n", "than the raw sample space.\n", "We can define a binary random variable like \"greater than 0.5\"\n", "even when the underlying sample space is infinite,\n", "e.g., points on the line segment between $0$ and $1$.\n", "Additionally, multiple random variables\n", "can share the same underlying sample space.\n", "For example \"whether my home alarm goes off\"\n", "and \"whether my house was burgled\"\n", "are both binary random variables\n", "that share an underlying sample space.\n", "Consequently, knowing the value taken by one random variable\n", "can tell us something about the likely value of another random variable.\n", "Knowing that the alarm went off,\n", "we might suspect that the house was likely burgled.\n", "\n", "\n", "Every value taken by a random variable corresponds\n", "to a subset of the underlying sample space.\n", "Thus the occurrence where the random variable $X$\n", "takes value $v$, denoted by $X=v$, is an *event*\n", "and $P(X=v)$ denotes its probability.\n", "Sometimes this notation can get clunky,\n", "and we can abuse notation when the context is clear.\n", "For example, we might use $P(X)$ to refer broadly\n", "to the *distribution* of $X$, i.e.,\n", "the function that tells us the probability\n", "that $X$ takes any given value.\n", "Other times we write expressions\n", "like $P(X,Y) = P(X) P(Y)$,\n", "as a shorthand to express a statement\n", "that is true for all of the values\n", "that the random variables $X$ and $Y$ can take, i.e.,\n", "for all $i,j$ it holds that $P(X=i \\textrm{ and } Y=j) = P(X=i)P(Y=j)$.\n", "Other times, we abuse notation by writing\n", "$P(v)$ when the random variable is clear from the context.\n", "Since an event in probability theory is a set of outcomes from the sample space,\n", "we can specify a range of values for a random variable to take.\n", "For example, $P(1 \\leq X \\leq 3)$ denotes the probability of the event $\\{1 \\leq X \\leq 3\\}$.\n", "\n", "\n", "Note that there is a subtle difference\n", "between *discrete* random variables,\n", "like flips of a coin or tosses of a die,\n", "and *continuous* ones,\n", "like the weight and the height of a person\n", "sampled at random from the population.\n", "In this case we seldom really care about\n", "someone's exact height.\n", "Moreover, if we took precise enough measurements,\n", "we would find that no two people on the planet\n", "have the exact same height.\n", "In fact, with fine enough measurements,\n", "you would never have the same height\n", "when you wake up and when you go to sleep.\n", "There is little point in asking about\n", "the exact probability that someone\n", "is 1.801392782910287192 meters tall.\n", "Instead, we typically care more about being able to say\n", "whether someone's height falls into a given interval,\n", "say between 1.79 and 1.81 meters.\n", "In these cases we work with probability *densities*.\n", "The height of exactly 1.80 meters\n", "has no probability, but nonzero density.\n", "To work out the probability assigned to an interval,\n", "we must take an *integral* of the density\n", "over that interval.\n", "\n", "## Multiple Random Variables\n", "\n", "You might have noticed that we could not even\n", "make it through the previous section without\n", "making statements involving interactions\n", "among multiple random variables\n", "(recall $P(X,Y) = P(X) P(Y)$).\n", "Most of machine learning\n", "is concerned with such relationships.\n", "Here, the sample space would be\n", "the population of interest,\n", "say customers who transact with a business,\n", "photographs on the Internet,\n", "or proteins known to biologists.\n", "Each random variable would represent\n", "the (unknown) value of a different attribute.\n", "Whenever we sample an individual from the population,\n", "we observe a realization of each of the random variables.\n", "Because the values taken by random variables\n", "correspond to subsets of the sample space\n", "that could be overlapping, partially overlapping,\n", "or entirely disjoint,\n", "knowing the value taken by one random variable\n", "can cause us to update our beliefs\n", "about which values of another random variable are likely.\n", "If a patient walks into a hospital\n", "and we observe that they\n", "are having trouble breathing\n", "and have lost their sense of smell,\n", "then we believe that they are more likely\n", "to have COVID-19 than we might\n", "if they had no trouble breathing\n", "and a perfectly ordinary sense of smell.\n", "\n", "\n", "When working with multiple random variables,\n", "we can construct events corresponding\n", "to every combination of values\n", "that the variables can jointly take.\n", "The probability function that assigns\n", "probabilities to each of these combinations\n", "(e.g. $A=a$ and $B=b$)\n", "is called the *joint probability* function\n", "and simply returns the probability assigned\n", "to the intersection of the corresponding subsets\n", "of the sample space.\n", "The *joint probability* assigned to the event\n", "where random variables $A$ and $B$\n", "take values $a$ and $b$, respectively,\n", "is denoted $P(A = a, B = b)$,\n", "where the comma indicates \"and\".\n", "Note that for any values $a$ and $b$,\n", "it follows that\n", "\n", "$$P(A=a, B=b) \\leq P(A=a) \\textrm{ and } P(A=a, B=b) \\leq P(B = b),$$\n", "\n", "since for $A=a$ and $B=b$ to happen,\n", "$A=a$ has to happen *and* $B=b$ also has to happen.\n", "Interestingly, the joint probability\n", "tells us all that we can know about these\n", "random variables in a probabilistic sense,\n", "and can be used to derive many other\n", "useful quantities, including recovering the\n", "individual distributions $P(A)$ and $P(B)$.\n", "To recover $P(A=a)$ we simply sum up\n", "$P(A=a, B=v)$ over all values $v$\n", "that the random variable $B$ can take:\n", "$P(A=a) = \\sum_v P(A=a, B=v)$.\n", "\n", "\n", "The ratio $\\frac{P(A=a, B=b)}{P(A=a)} \\leq 1$\n", "turns out to be extremely important.\n", "It is called the *conditional probability*,\n", "and is denoted via the \"$\\mid$\" symbol:\n", "\n", "$$P(B=b \\mid A=a) = P(A=a,B=b)/P(A=a).$$\n", "\n", "It tells us the new probability\n", "associated with the event $B=b$,\n", "once we condition on the fact $A=a$ took place.\n", "We can think of this conditional probability\n", "as restricting attention only to the subset\n", "of the sample space associated with $A=a$\n", "and then renormalizing so that\n", "all probabilities sum to 1.\n", "Conditional probabilities\n", "are in fact just ordinary probabilities\n", "and thus respect all of the axioms,\n", "as long as we condition all terms\n", "on the same event and thus\n", "restrict attention to the same sample space.\n", "For instance, for disjoint events\n", "$\\mathcal{B}$ and $\\mathcal{B}'$, we have that\n", "$P(\\mathcal{B} \\cup \\mathcal{B}' \\mid A = a) = P(\\mathcal{B} \\mid A = a) + P(\\mathcal{B}' \\mid A = a)$.\n", "\n", "\n", "Using the definition of conditional probabilities,\n", "we can derive the famous result called *<PERSON><PERSON>' theorem*.\n", "By construction, we have that $P(A, B) = P(B\\mid A) P(A)$\n", "and $P(A, B) = P(A\\mid B) P(B)$.\n", "Combining both equations yields\n", "$P(B\\mid A) P(A) = P(A\\mid B) P(B)$ and hence\n", "\n", "$$P(A \\mid B) = \\frac{P(B\\mid A) P(A)}{P(B)}.$$\n", "\n", "\n", "\n", "\n", "\n", "\n", "This simple equation has profound implications because\n", "it allows us to reverse the order of conditioning.\n", "If we know how to estimate $P(B\\mid A)$, $P(A)$, and $P(B)$,\n", "then we can estimate $P(A\\mid B)$.\n", "We often find it easier to estimate one term directly\n", "but not the other and <PERSON><PERSON>' theorem can come to the rescue here.\n", "For instance, if we know the prevalence of symptoms for a given disease,\n", "and the overall prevalences of the disease and symptoms, respectively,\n", "we can determine how likely someone is\n", "to have the disease based on their symptoms.\n", "In some cases we might not have direct access to $P(B)$,\n", "such as the prevalence of symptoms.\n", "In this case a simplified version of <PERSON><PERSON>' theorem comes in handy:\n", "\n", "$$P(A \\mid B) \\propto P(B \\mid A) P(A).$$\n", "\n", "Since we know that $P(A \\mid B)$ must be normalized to $1$, i.e., $\\sum_a P(A=a \\mid B) = 1$,\n", "we can use it to compute\n", "\n", "$$P(A \\mid B) = \\frac{P(B \\mid A) P(A)}{\\sum_a P(B \\mid A=a) P(A = a)}.$$\n", "\n", "In Bayesian statistics, we think of an observer\n", "as possessing some (subjective) prior beliefs\n", "about the plausibility of the available hypotheses\n", "encoded in the *prior* $P(H)$,\n", "and a *likelihood function* that says how likely\n", "one is to observe any value of the collected evidence\n", "for each of the hypotheses in the class $P(E \\mid H)$.\n", "<PERSON><PERSON>' theorem is then interpreted as telling us\n", "how to update the initial *prior* $P(H)$\n", "in light of the available evidence $E$\n", "to produce *posterior* beliefs\n", "$P(H \\mid E) = \\frac{P(E \\mid H) P(H)}{P(E)}$.\n", "Informally, this can be stated as\n", "\"posterior equals prior times likelihood, divided by the evidence\".\n", "Now, because the evidence $P(E)$ is the same for all hypotheses,\n", "we can get away with simply normalizing over the hypotheses.\n", "\n", "Note that $\\sum_a P(A=a \\mid B) = 1$ also allows us to *marginalize* over random variables. That is, we can drop variables from a joint distribution such as $P(A, B)$. After all, we have that\n", "\n", "$$\\sum_a P(B \\mid A=a) P(A=a) = \\sum_a P(B, A=a) = P(B).$$\n", "\n", "Independence is another fundamentally important concept\n", "that forms the backbone of\n", "many important ideas in statistics.\n", "In short, two variables are *independent*\n", "if conditioning on the value of $A$ does not\n", "cause any change to the probability distribution\n", "associated with $B$ and vice versa.\n", "More formally, independence, denoted $A \\perp B$,\n", "requires that $P(A \\mid B) = P(A)$ and, consequently,\n", "that $P(A,B) = P(A \\mid B) P(B) = P(A) P(B)$.\n", "Independence is often an appropriate assumption.\n", "For example, if the random variable $A$\n", "represents the outcome from tossing one fair coin\n", "and the random variable $B$\n", "represents the outcome from tossing another,\n", "then knowing whether $A$ came up heads\n", "should not influence the probability\n", "of $B$ coming up heads.\n", "\n", "\n", "Independence is especially useful when it holds among the successive\n", "draws of our data from some underlying distribution\n", "(allowing us to make strong statistical conclusions)\n", "or when it holds among various variables in our data,\n", "allowing us to work with simpler models\n", "that encode this independence structure.\n", "On the other hand, estimating the dependencies\n", "among random variables is often the very aim of learning.\n", "We care to estimate the probability of disease given symptoms\n", "specifically because we believe\n", "that diseases and symptoms are *not* independent.\n", "\n", "\n", "Note that because conditional probabilities are proper probabilities,\n", "the concepts of independence and dependence also apply to them.\n", "Two random variables $A$ and $B$ are *conditionally independent*\n", "given a third variable $C$ if and only if $P(A, B \\mid C) = P(A \\mid C)P(B \\mid C)$.\n", "Interestingly, two variables can be independent in general\n", "but become dependent when conditioning on a third.\n", "This often occurs when the two random variables $A$ and $B$\n", "correspond to causes of some third variable $C$.\n", "For example, broken bones and lung cancer might be independent\n", "in the general population but if we condition on being in the hospital\n", "then we might find that broken bones are negatively correlated with lung cancer.\n", "That is because the broken bone *explains away* why some person is in the hospital\n", "and thus lowers the probability that they are hospitalized because of having lung cancer.\n", "\n", "\n", "And conversely, two dependent random variables\n", "can become independent upon conditioning on a third.\n", "This often happens when two otherwise unrelated events\n", "have a common cause.\n", "Shoe size and reading level are highly correlated\n", "among elementary school students,\n", "but this correlation disappears if we condition on age.\n", "\n", "\n", "\n", "## An Example\n", ":label:`subsec_probability_hiv_app`\n", "\n", "Let's put our skills to the test.\n", "Assume that a doctor administers an HIV test to a patient.\n", "This test is fairly accurate and fails only with 1% probability\n", "if the patient is healthy but reported as diseased,\n", "i.e., healthy patients test positive in 1% of cases.\n", "Moreover, it never fails to detect HIV if the patient actually has it.\n", "We use $D_1 \\in \\{0, 1\\}$ to indicate the diagnosis\n", "($0$ if negative and $1$ if positive)\n", "and $H \\in \\{0, 1\\}$ to denote the HIV status.\n", "\n", "| Conditional probability | $H=1$ | $H=0$ |\n", "|:------------------------|------:|------:|\n", "| $P(D_1 = 1 \\mid H)$        |     1 |  0.01 |\n", "| $P(D_1 = 0 \\mid H)$        |     0 |  0.99 |\n", "\n", "Note that the column sums are all 1 (but the row sums do not),\n", "since they are conditional probabilities.\n", "Let's compute the probability of the patient having HIV\n", "if the test comes back positive, i.e., $P(H = 1 \\mid D_1 = 1)$.\n", "Intuitively this is going to depend on how common the disease is,\n", "since it affects the number of false alarms.\n", "Assume that the population is fairly free of the disease, e.g., $P(H=1) = 0.0015$.\n", "To apply <PERSON><PERSON>' theorem, we need to apply marginalization\n", "to determine\n", "\n", "$$\\begin{aligned}\n", "P(D_1 = 1)\n", "=& P(D_1=1, H=0) + P(D_1=1, H=1)  \\\\\n", "=& P(D_1=1 \\mid H=0) P(H=0) + P(D_1=1 \\mid H=1) P(H=1) \\\\\n", "=& 0.011485.\n", "\\end{aligned}\n", "$$\n", "\n", "This leads us to\n", "\n", "$$P(H = 1 \\mid D_1 = 1) = \\frac{P(D_1=1 \\mid H=1) P(H=1)}{P(D_1=1)} = 0.1306.$$\n", "\n", "In other words, there is only a 13.06% chance\n", "that the patient actually has HIV,\n", "despite the test being pretty accurate.\n", "As we can see, probability can be counterintuitive.\n", "What should a patient do upon receiving such terrifying news?\n", "Likely, the patient would ask the physician\n", "to administer another test to get clarity.\n", "The second test has different characteristics\n", "and it is not as good as the first one.\n", "\n", "| Conditional probability | $H=1$ | $H=0$ |\n", "|:------------------------|------:|------:|\n", "| $P(D_2 = 1 \\mid H)$          |  0.98 |  0.03 |\n", "| $P(D_2 = 0 \\mid H)$          |  0.02 |  0.97 |\n", "\n", "Unfortunately, the second test comes back positive, too.\n", "Let's calculate the requisite probabilities to invoke <PERSON><PERSON>' theorem\n", "by assuming conditional independence:\n", "\n", "$$\\begin{aligned}\n", "P(D_1 = 1, D_2 = 1 \\mid H = 0)\n", "& = P(D_1 = 1 \\mid H = 0) P(D_2 = 1 \\mid H = 0)\n", "=& 0.0003, \\\\\n", "P(D_1 = 1, D_2 = 1 \\mid H = 1)\n", "& = P(D_1 = 1 \\mid H = 1) P(D_2 = 1 \\mid H = 1)\n", "=& 0.98.\n", "\\end{aligned}\n", "$$\n", "\n", "Now we can apply marginalization to obtain the probability\n", "that both tests come back positive:\n", "\n", "$$\\begin{aligned}\n", "&P(D_1 = 1, D_2 = 1)\\\\\n", "&= P(D_1 = 1, D_2 = 1, H = 0) + P(D_1 = 1, D_2 = 1, H = 1)  \\\\\n", "&= P(D_1 = 1, D_2 = 1 \\mid H = 0)P(H=0) + P(D_1 = 1, D_2 = 1 \\mid H = 1)P(H=1)\\\\\n", "&= 0.00176955.\n", "\\end{aligned}\n", "$$\n", "\n", "Finally, the probability of the patient having HIV given that both tests are positive is\n", "\n", "$$P(H = 1 \\mid D_1 = 1, D_2 = 1)\n", "= \\frac{P(D_1 = 1, D_2 = 1 \\mid H=1) P(H=1)}{P(D_1 = 1, D_2 = 1)}\n", "= 0.8307.$$\n", "\n", "That is, the second test allowed us to gain much higher confidence that not all is well.\n", "Despite the second test being considerably less accurate than the first one,\n", "it still significantly improved our estimate.\n", "The assumption of both tests being conditionally independent of each other\n", "was crucial for our ability to generate a more accurate estimate.\n", "Take the extreme case where we run the same test twice.\n", "In this situation we would expect the same outcome both times,\n", "hence no additional insight is gained from running the same test again.\n", "The astute reader might have noticed that the diagnosis behaved\n", "like a classifier hiding in plain sight\n", "where our ability to decide whether a patient is healthy\n", "increases as we obtain more features (test outcomes).\n", "\n", "\n", "## Expectations\n", "\n", "Often, making decisions requires not just looking\n", "at the probabilities assigned to individual events\n", "but composing them together into useful aggregates\n", "that can provide us with guidance.\n", "For example, when random variables take continuous scalar values,\n", "we often care about knowing what value to expect *on average*.\n", "This quantity is formally called an *expectation*.\n", "If we are making investments,\n", "the first quantity of interest\n", "might be the return we can expect,\n", "averaging over all the possible outcomes\n", "(and weighting by the appropriate probabilities).\n", "For instance, say that with 50% probability,\n", "an investment might fail altogether,\n", "with 40% probability it might provide a 2$\\times$ return,\n", "and with 10% probability it might provide a 10$\\times$ return 10$\\times$.\n", "To calculate the expected return,\n", "we sum over all returns, multiplying each\n", "by the probability that they will occur.\n", "This yields the expectation\n", "$0.5 \\cdot 0 + 0.4 \\cdot 2 + 0.1 \\cdot 10 = 1.8$.\n", "Hence the expected return is 1.8$\\times$.\n", "\n", "\n", "In general, the *expectation* (or average)\n", "of the random variable $X$ is defined as\n", "\n", "$$E[X] = E_{x \\sim P}[x] = \\sum_{x} x P(X = x).$$\n", "\n", "Likewise, for densities we obtain $E[X] = \\int x \\;dp(x)$.\n", "Sometimes we are interested in the expected value\n", "of some function of $x$.\n", "We can calculate these expectations as\n", "\n", "$$E_{x \\sim P}[f(x)] = \\sum_x f(x) P(x) \\textrm{ and } E_{x \\sim P}[f(x)] = \\int f(x) p(x) \\;dx$$\n", "\n", "for discrete probabilities and densities, respectively.\n", "Returning to the investment example from above,\n", "$f$ might be the *utility* (happiness)\n", "associated with the return.\n", "Behavior economists have long noted\n", "that people associate greater disutility\n", "with losing money than the utility gained\n", "from earning one dollar relative to their baseline.\n", "Moreover, the value of money tends to be sub-linear.\n", "Possessing 100k dollars versus zero dollars\n", "can make the difference between paying the rent,\n", "eating well, and enjoying quality healthcare\n", "versus suffering through homelessness.\n", "On the other hand, the gains due to possessing\n", "200k versus 100k are less dramatic.\n", "Reasoning like this motivates the cliché\n", "that \"the utility of money is logarithmic\".\n", "\n", "\n", "If  the utility associated with a total loss were $-1$,\n", "and the utilities associated with returns of $1$, $2$, and $10$\n", "were $1$, $2$ and $4$, respectively,\n", "then the expected happiness of investing\n", "would be $0.5 \\cdot (-1) + 0.4 \\cdot 2 + 0.1 \\cdot 4 = 0.7$\n", "(an expected loss of utility of 30%).\n", "If indeed this were your utility function,\n", "you might be best off keeping the money in the bank.\n", "\n", "For financial decisions,\n", "we might also want to measure\n", "how *risky* an investment is.\n", "Here, we care not just about the expected value\n", "but how much the actual values tend to *vary*\n", "relative to this value.\n", "Note that we cannot just take\n", "the expectation of the difference\n", "between the actual and expected values.\n", "This is because the expectation of a difference\n", "is the difference of the expectations,\n", "i.e., $E[X - E[X]] = E[X] - E[E[X]] = 0$.\n", "However, we can look at the expectation\n", "of any non-negative function of this difference.\n", "The *variance* of a random variable is calculated by looking\n", "at the expected value of the *squared* differences:\n", "\n", "$$\\textrm{Var}[X] = E\\left[(X - E[X])^2\\right] = E[X^2] - E[X]^2.$$\n", "\n", "Here the equality follows by expanding\n", "$(X - E[X])^2 = X^2 - 2 X E[X] + E[X]^2$\n", "and taking expectations for each term.\n", "The square root of the variance is another\n", "useful quantity called the *standard deviation*.\n", "While this and the variance\n", "convey the same information (either can be calculated from the other),\n", "the standard deviation has the nice property\n", "that it is expressed in the same units\n", "as the original quantity represented\n", "by the random variable.\n", "\n", "Lastly, the variance of a function\n", "of a random variable\n", "is defined analogously as\n", "\n", "$$\\textrm{Var}_{x \\sim P}[f(x)] = E_{x \\sim P}[f^2(x)] - E_{x \\sim P}[f(x)]^2.$$\n", "\n", "Returning to our investment example,\n", "we can now compute the variance of the investment.\n", "It is given by $0.5 \\cdot 0 + 0.4 \\cdot 2^2 + 0.1 \\cdot 10^2 - 1.8^2 = 8.36$.\n", "For all intents and purposes this is a risky investment.\n", "Note that by mathematical convention mean and variance\n", "are often referenced as $\\mu$ and $\\sigma^2$.\n", "This is particularly the case whenever we use it\n", "to parametrize a Gaussian distribution.\n", "\n", "In the same way as we introduced expectations\n", "and variance for *scalar* random variables,\n", "we can do so for vector-valued ones.\n", "Expectations are easy, since we can apply them elementwise.\n", "For instance, $\\boldsymbol{\\mu} \\stackrel{\\textrm{def}}{=} E_{\\mathbf{x} \\sim P}[\\mathbf{x}]$\n", "has coordinates $\\mu_i = E_{\\mathbf{x} \\sim P}[x_i]$.\n", "*Covariances* are more complicated.\n", "We define them by taking expectations of the *outer product*\n", "of the difference between random variables and their mean:\n", "\n", "$$\\boldsymbol{\\Sigma} \\stackrel{\\textrm{def}}{=} \\textrm{Cov}_{\\mathbf{x} \\sim P}[\\mathbf{x}] = E_{\\mathbf{x} \\sim P}\\left[(\\mathbf{x} - \\boldsymbol{\\mu}) (\\mathbf{x} - \\boldsymbol{\\mu})^\\top\\right].$$\n", "\n", "This matrix $\\boldsymbol{\\Sigma}$ is referred to as the covariance matrix.\n", "An easy way to see its effect is to consider some vector $\\mathbf{v}$\n", "of the same size as $\\mathbf{x}$.\n", "It follows that\n", "\n", "$$\\mathbf{v}^\\top \\boldsymbol{\\Sigma} \\mathbf{v} = E_{\\mathbf{x} \\sim P}\\left[\\mathbf{v}^\\top(\\mathbf{x} - \\boldsymbol{\\mu}) (\\mathbf{x} - \\boldsymbol{\\mu})^\\top \\mathbf{v}\\right] = \\textrm{Var}_{x \\sim P}[\\mathbf{v}^\\top \\mathbf{x}].$$\n", "\n", "As such, $\\boldsymbol{\\Sigma}$ allows us to compute the variance\n", "for any linear function of $\\mathbf{x}$\n", "by a simple matrix multiplication.\n", "The off-diagonal elements tell us how correlated the coordinates are:\n", "a value of 0 means no correlation,\n", "where a larger positive value\n", "means that they are more strongly correlated.\n", "\n", "\n", "\n", "## Discussion\n", "\n", "In machine learning, there are many things to be uncertain about!\n", "We can be uncertain about the value of a label given an input.\n", "We can be uncertain about the estimated value of a parameter.\n", "We can even be uncertain about whether data arriving at deployment\n", "is even from the same distribution as the training data.\n", "\n", "By *aleatoric uncertainty*, we mean uncertainty\n", "that is intrinsic to the problem,\n", "and due to genuine randomness\n", "unaccounted for by the observed variables.\n", "By *epistemic uncertainty*, we mean uncertainty\n", "over a model's parameters, the sort of uncertainty\n", "that we can hope to reduce by collecting more data.\n", "We might have epistemic uncertainty\n", "concerning the probability\n", "that a coin turns up heads,\n", "but even once we know this probability,\n", "we are left with aleatoric uncertainty\n", "about the outcome of any future toss.\n", "No matter how long we watch someone tossing a fair coin,\n", "we will never be more or less than 50% certain\n", "that the next toss will come up heads.\n", "These terms come from mechanical modeling,\n", "(see e.g., :citet:`<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.Ditlevsen.2009` for a review on this aspect of [uncertainty quantification](https://en.wikipedia.org/wiki/Uncertainty_quantification)).\n", "It is worth noting, however, that these terms constitute a slight abuse of language.\n", "The term *epistemic* refers to anything concerning *knowledge*\n", "and thus, in the philosophical sense, all uncertainty is epistemic.\n", "\n", "\n", "We saw that sampling data from some unknown probability distribution\n", "can provide us with information that can be used to estimate\n", "the parameters of the data generating distribution.\n", "That said, the rate at which this is possible can be quite slow.\n", "In our coin tossing example (and many others)\n", "we can do no better than to design estimators\n", "that converge at a rate of $1/\\sqrt{n}$,\n", "where $n$ is the sample size (e.g., the number of tosses).\n", "This means that by going from 10 to 1000 observations (usually a very achievable task)\n", "we see a tenfold reduction of uncertainty,\n", "whereas the next 1000 observations help comparatively little,\n", "offering only a 1.41 times reduction.\n", "This is a persistent feature of machine learning:\n", "while there are often easy gains, it takes a very large amount of data,\n", "and often with it an enormous amount of computation, to make further gains.\n", "For an empirical review of this fact for large scale language models see :citet:`Revels.Lubin.Papamarkou.2016`.\n", "\n", "We also sharpened our language and tools for statistical modeling.\n", "In the process of that we learned about conditional probabilities\n", "and about one of the most important equations in statistics---<PERSON><PERSON>' theorem.\n", "It is an effective tool for decoupling information conveyed by data\n", "through a likelihood term $P(B \\mid A)$ that addresses\n", "how well observations $B$ match a choice of parameters $A$,\n", "and a prior probability $P(A)$ which governs how plausible\n", "a particular choice of $A$ was in the first place.\n", "In particular, we saw how this rule can be applied\n", "to assign probabilities to diagnoses,\n", "based on the efficacy of the test *and*\n", "the prevalence of the disease itself (i.e., our prior).\n", "\n", "Lastly, we introduced a first set of nontrivial questions\n", "about the effect of a specific probability distribution,\n", "namely expectations and variances.\n", "While there are many more than just linear and quadratic\n", "expectations for a probability distribution,\n", "these two already provide a good deal of knowledge\n", "about the possible behavior of the distribution.\n", "For instance, [<PERSON><PERSON><PERSON><PERSON><PERSON>'s inequality](https://en.wikipedia.org/wiki/Chebyshev%27s_inequality)\n", "states that $P(|X - \\mu| \\geq k \\sigma) \\leq 1/k^2$,\n", "where $\\mu$ is the expectation, $\\sigma^2$ is the variance of the distribution,\n", "and $k > 1$ is a confidence parameter of our choosing.\n", "It tells us that draws from a distribution lie\n", "with at least 50% probability\n", "within a $[-\\sqrt{2} \\sigma, \\sqrt{2} \\sigma]$\n", "interval centered on the expectation.\n", "\n", "\n", "\n", "\n", "## Exercises\n", "\n", "1. Give an example where observing more data can reduce the amount of uncertainty about the outcome to an arbitrarily low level.\n", "1. Give an example where observing more data will only reduce the amount of uncertainty up to a point and then no further. Explain why this is the case and where you expect this point to occur.\n", "1. We empirically demonstrated convergence to the mean for the toss of a coin. Calculate the variance of the estimate of the probability that we see a head after drawing $n$ samples.\n", "    1. How does the variance scale with the number of observations?\n", "    1. Use <PERSON><PERSON><PERSON>'s inequality to bound the deviation from the expectation.\n", "    1. How does it relate to the central limit theorem?\n", "1. Assume that we draw $m$ samples $x_i$ from a probability distribution with zero mean and unit variance. Compute the averages $z_m \\stackrel{\\textrm{def}}{=} m^{-1} \\sum_{i=1}^m x_i$. Can we apply <PERSON><PERSON><PERSON><PERSON><PERSON>'s inequality for every $z_m$ independently? Why not?\n", "1. Given two events with probability $P(\\mathcal{A})$ and $P(\\mathcal{B})$, compute upper and lower bounds on $P(\\mathcal{A} \\cup \\mathcal{B})$ and $P(\\mathcal{A} \\cap \\mathcal{B})$. Hint: graph the situation using a [Venn diagram](https://en.wikipedia.org/wiki/Venn_diagram).\n", "1. Assume that we have a sequence of random variables, say $A$, $B$, and $C$, where $B$ only depends on $A$, and $C$ only depends on $B$, can you simplify the joint probability $P(A, B, C)$? Hint: this is a [Markov chain](https://en.wikipedia.org/wiki/Markov_chain).\n", "1. In :numref:`subsec_probability_hiv_app`, assume that the outcomes of the two tests are not independent. In particular assume that either test on its own has a false positive rate of 10% and a false negative rate of 1%. That is, assume that $P(D =1 \\mid H=0) = 0.1$ and that $P(D = 0 \\mid H=1) = 0.01$. Moreover, assume that for $H = 1$ (infected) the test outcomes are conditionally independent, i.e., that $P(D_1, D_2 \\mid H=1) = P(D_1 \\mid H=1) P(D_2 \\mid H=1)$ but that for healthy patients the outcomes are coupled via $P(D_1 = D_2 = 1 \\mid H=0) = 0.02$.\n", "    1. Work out the joint probability table for $D_1$ and $D_2$, given $H=0$ based on the information you have so far.\n", "    1. Derive the probability that the patient is diseased ($H=1$) after one test returns positive. You can assume the same baseline probability $P(H=1) = 0.0015$ as before.\n", "    1. Derive the probability that the patient is diseased ($H=1$) after both tests return positive.\n", "1. Assume that you are an asset manager for an investment bank and you have a choice of stocks $s_i$ to invest in. Your portfolio needs to add up to $1$ with weights $\\alpha_i$ for each stock. The stocks have an average return $\\boldsymbol{\\mu} = E_{\\mathbf{s} \\sim P}[\\mathbf{s}]$ and covariance $\\boldsymbol{\\Sigma} = \\textrm{Cov}_{\\mathbf{s} \\sim P}[\\mathbf{s}]$.\n", "    1. Compute the expected return for a given portfolio $\\boldsymbol{\\alpha}$.\n", "    1. If you wanted to maximize the return of the portfolio, how should you choose your investment?\n", "    1. Compute the *variance* of the portfolio.\n", "    1. Formulate an optimization problem of maximizing the return while keeping the variance constrained to an upper bound. This is the Nobel-Prize winning [<PERSON><PERSON><PERSON> portfolio](https://en.wikipedia.org/wiki/<PERSON><PERSON><PERSON>_model) :cite:`Mangram.2013`. To solve it you will need a quadratic programming solver, something way beyond the scope of this book.\n"]}, {"cell_type": "markdown", "id": "126c2b93", "metadata": {"origin_pos": 31, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/37)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
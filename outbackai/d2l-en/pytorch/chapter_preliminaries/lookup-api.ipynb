{"cells": [{"cell_type": "markdown", "id": "05d9c330", "metadata": {"origin_pos": 1}, "source": ["# Documentation\n"]}, {"cell_type": "markdown", "id": "c9890286", "metadata": {"origin_pos": 3, "tab": ["pytorch"]}, "source": ["While we cannot possibly introduce every single PyTorch function and class \n", "(and the information might become outdated quickly), \n", "the [API documentation](https://pytorch.org/docs/stable/index.html) and additional [tutorials](https://pytorch.org/tutorials/beginner/basics/intro.html) and examples \n", "provide such documentation.\n", "This section provides some guidance for how to explore the PyTorch API.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a253b1fa", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:02.297125Z", "iopub.status.busy": "2023-08-18T19:32:02.296778Z", "iopub.status.idle": "2023-08-18T19:32:04.430872Z", "shell.execute_reply": "2023-08-18T19:32:04.429820Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch"]}, {"cell_type": "markdown", "id": "257c6aae", "metadata": {"origin_pos": 9}, "source": ["## Functions and Classes in a Module\n", "\n", "To know which functions and classes can be called in a module,\n", "we invoke the `dir` function. For instance, we can\n", "(**query all properties in the module for generating random numbers**):\n"]}, {"cell_type": "code", "execution_count": 2, "id": "bde8a5b0", "metadata": {"attributes": {"classes": [], "id": "", "n": "1"}, "execution": {"iopub.execute_input": "2023-08-18T19:32:04.435694Z", "iopub.status.busy": "2023-08-18T19:32:04.434971Z", "iopub.status.idle": "2023-08-18T19:32:04.440648Z", "shell.execute_reply": "2023-08-18T19:32:04.439467Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['AbsTransform', 'AffineTransform', '<PERSON><PERSON><PERSON>', 'Beta', 'Binomial', 'CatTransform', 'Categorical', 'Cauchy', 'Chi2', 'ComposeTransform', 'Continuous<PERSON>ernoulli', 'CorrCholeskyTransform', 'CumulativeDistributionTransform', 'Dirichlet', 'Distribution', 'ExpTransform', 'Exponential', 'ExponentialFamily', 'FisherSnedecor', 'Gamma', 'Geometric', 'Gumbel', 'HalfCauchy', 'HalfNormal', 'Independent', 'IndependentTransform', 'Kumar<PERSON>wamy', 'LKJCholesky', 'Laplace', 'LogNormal', 'LogisticNormal', 'LowRankMultivariateNormal', 'LowerCholeskyTransform', 'MixtureSameFamily', 'Multinomial', 'MultivariateNormal', 'NegativeBinomial', 'Normal', 'OneHotCategorical', 'OneHotCategoricalStraightThrough', '<PERSON>reto', 'Poisson', 'PositiveDefiniteTransform', 'PowerTransform', 'RelaxedBernoulli', 'RelaxedOneHotCategorical', 'ReshapeTransform', 'SigmoidTransform', 'SoftmaxTransform', 'SoftplusTransform', 'StackTransform', 'StickBreakingTransform', 'StudentT', 'TanhTransform', 'Transform', 'TransformedDistribution', 'Uniform', 'VonMises', 'Weibull', 'Wishart', '__all__', '__builtins__', '__cached__', '__doc__', '__file__', '__loader__', '__name__', '__package__', '__path__', '__spec__', 'bernoulli', 'beta', 'biject_to', 'binomial', 'categorical', 'cauchy', 'chi2', 'constraint_registry', 'constraints', 'continuous_bernoulli', 'dirichlet', 'distribution', 'exp_family', 'exponential', 'fishersnedecor', 'gamma', 'geometric', 'gumbel', 'half_cauchy', 'half_normal', 'identity_transform', 'independent', 'kl', 'kl_divergence', 'kumaraswamy', 'laplace', 'lkj_cholesky', 'log_normal', 'logistic_normal', 'lowrank_multivariate_normal', 'mixture_same_family', 'multinomial', 'multivariate_normal', 'negative_binomial', 'normal', 'one_hot_categorical', 'pareto', 'poisson', 'register_kl', 'relaxed_bernoulli', 'relaxed_categorical', 'studentT', 'transform_to', 'transformed_distribution', 'transforms', 'uniform', 'utils', 'von_mises', 'weibull', 'wishart']\n"]}], "source": ["print(dir(torch.distributions))"]}, {"cell_type": "markdown", "id": "428e4ff3", "metadata": {"origin_pos": 14}, "source": ["Generally, we can ignore functions that start and end with `__` (special objects in Python) \n", "or functions that start with a single `_`(usually internal functions). \n", "Based on the remaining function or attribute names, \n", "we might hazard a guess that this module offers \n", "various methods for generating random numbers, \n", "including sampling from the uniform distribution (`uniform`), \n", "normal distribution (`normal`), and multinomial distribution (`multinomial`).\n", "\n", "## Specific Functions and Classes\n", "\n", "For specific instructions on how to use a given function or class,\n", "we can invoke the  `help` function. As an example, let's\n", "[**explore the usage instructions for tensors' `ones` function**].\n"]}, {"cell_type": "code", "execution_count": 3, "id": "31f291f6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:04.444454Z", "iopub.status.busy": "2023-08-18T19:32:04.444071Z", "iopub.status.idle": "2023-08-18T19:32:04.449954Z", "shell.execute_reply": "2023-08-18T19:32:04.448745Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on built-in function ones in module torch:\n", "\n", "ones(...)\n", "    ones(*size, *, out=None, dtype=None, layout=torch.strided, device=None, requires_grad=False) -> Tensor\n", "    \n", "    Returns a tensor filled with the scalar value `1`, with the shape defined\n", "    by the variable argument :attr:`size`.\n", "    \n", "    Args:\n", "        size (int...): a sequence of integers defining the shape of the output tensor.\n", "            Can be a variable number of arguments or a collection like a list or tuple.\n", "    \n", "    Keyword arguments:\n", "        out (Tensor, optional): the output tensor.\n", "        dtype (:class:`torch.dtype`, optional): the desired data type of returned tensor.\n", "            Default: if ``None``, uses a global default (see :func:`torch.set_default_tensor_type`).\n", "        layout (:class:`torch.layout`, optional): the desired layout of returned Tensor.\n", "            Default: ``torch.strided``.\n", "        device (:class:`torch.device`, optional): the desired device of returned tensor.\n", "            Default: if ``None``, uses the current device for the default tensor type\n", "            (see :func:`torch.set_default_tensor_type`). :attr:`device` will be the CPU\n", "            for CPU tensor types and the current CUDA device for CUDA tensor types.\n", "        requires_grad (bool, optional): If autograd should record operations on the\n", "            returned tensor. Default: ``False``.\n", "    \n", "    Example::\n", "    \n", "        >>> torch.ones(2, 3)\n", "        tensor([[ 1.,  1.,  1.],\n", "                [ 1.,  1.,  1.]])\n", "    \n", "        >>> torch.ones(5)\n", "        tensor([ 1.,  1.,  1.,  1.,  1.])\n", "\n"]}], "source": ["help(torch.ones)"]}, {"cell_type": "markdown", "id": "f9625ad2", "metadata": {"origin_pos": 19}, "source": ["From the documentation, we can see that the `ones` function \n", "creates a new tensor with the specified shape \n", "and sets all the elements to the value of 1. \n", "Whenever possible, you should (**run a quick test**) \n", "to confirm your interpretation:\n"]}, {"cell_type": "code", "execution_count": 4, "id": "aef47cd1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:04.453936Z", "iopub.status.busy": "2023-08-18T19:32:04.453370Z", "iopub.status.idle": "2023-08-18T19:32:04.485935Z", "shell.execute_reply": "2023-08-18T19:32:04.485079Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([1., 1., 1., 1.])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.ones(4)"]}, {"cell_type": "markdown", "id": "53ebdea1", "metadata": {"origin_pos": 24}, "source": ["In the <PERSON><PERSON><PERSON> notebook, we can use `?` to display the document in another\n", "window. For example, `list?` will create content\n", "that is almost identical to `help(list)`,\n", "displaying it in a new browser window.\n", "In addition, if we use two question marks, such as `list??`,\n", "the Python code implementing the function will also be displayed.\n", "\n", "The official documentation provides plenty of descriptions and examples that are beyond this book. \n", "We emphasize important use cases \n", "that will get you started quickly with practical problems, \n", "rather than completeness of coverage. \n", "We also encourage you to study the source code of the libraries \n", "to see examples of high-quality implementations of production code. \n", "By doing this you will become a better engineer \n", "in addition to becoming a better scientist.\n"]}, {"cell_type": "markdown", "id": "b4188fc4", "metadata": {"origin_pos": 26, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/39)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
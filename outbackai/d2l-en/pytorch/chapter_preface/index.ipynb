{"cells": [{"cell_type": "markdown", "id": "bc7fc286", "metadata": {"origin_pos": 0}, "source": ["# Preface\n", "\n", "Just a few years ago, there were no legions of deep learning scientists\n", "developing intelligent products and services at major companies and startups.\n", "When we entered the field, machine learning\n", "did not command headlines in daily newspapers.\n", "Our parents had no idea what machine learning was,\n", "let alone why we might prefer it to a career in medicine or law.\n", "Machine learning was a blue skies academic discipline\n", "whose industrial significance was limited\n", "to a narrow set of real-world applications,\n", "including speech recognition and computer vision.\n", "Moreover, many of these applications\n", "required so much domain knowledge\n", "that they were often regarded as entirely separate areas\n", "for which machine learning was one small component.\n", "At that time, neural networks---the\n", "predecessors of the deep learning methods\n", "that we focus on in this book---were\n", "generally regarded as outmoded.\n", "\n", "\n", "Yet in just few years, deep learning has taken the world by surprise,\n", "driving rapid progress in such diverse fields\n", "as computer vision, natural language processing,\n", "automatic speech recognition, reinforcement learning,\n", "and biomedical informatics.\n", "Moreover, the success of deep learning\n", "in so many tasks of practical interest\n", "has even catalyzed developments\n", "in theoretical machine learning and statistics.\n", "With these advances in hand,\n", "we can now build cars that drive themselves\n", "with more autonomy than ever before\n", "(though less autonomy than some companies might have you believe),\n", "dialogue systems that debug code by asking clarifying questions,\n", "and software agents beating the best human players in the world at board games such as Go, a feat once thought to be decades away.\n", "Already, these tools exert ever-wider influence on industry and society,\n", "changing the way movies are made, diseases are diagnosed,\n", "and playing a growing role in basic sciences---from astrophysics, to climate modeling, to weather prediction, to biomedicine.\n", "\n", "\n", "\n", "## About This Book\n", "\n", "This book represents our attempt to make deep learning approachable,\n", "teaching you the *concepts*, the *context*, and the *code*.\n", "\n", "### One Medium Combining Code, Math, and HTML\n", "\n", "For any computing technology to reach its full impact,\n", "it must be well understood, well documented, and supported by\n", "mature, well-maintained tools.\n", "The key ideas should be clearly distilled,\n", "minimizing the onboarding time needed\n", "to bring new practitioners up to date.\n", "Mature libraries should automate common tasks,\n", "and exemplar code should make it easy for practitioners\n", "to modify, apply, and extend common applications to suit their needs.\n", "\n", "\n", "As an example, take dynamic web applications.\n", "Despite a large number of companies, such as Amazon,\n", "developing successful database-driven web applications in the 1990s,\n", "the potential of this technology to aid creative entrepreneurs\n", "was realized to a far greater degree only in the past ten years,\n", "owing in part to the development of powerful, well-documented frameworks.\n", "\n", "\n", "Testing the potential of deep learning presents unique challenges\n", "because any single application brings together various disciplines.\n", "Applying deep learning requires simultaneously understanding\n", "(i) the motivations for casting a problem in a particular way;\n", "(ii) the mathematical form of a given model;\n", "(iii) the optimization algorithms for fitting the models to data;\n", "(iv) the statistical principles that tell us\n", "when we should expect our models\n", "to generalize to unseen data\n", "and practical methods for certifying\n", "that they have, in fact, generalized;\n", "and (v) the engineering techniques\n", "required to train models efficiently,\n", "navigating the pitfalls of numerical computing\n", "and getting the most out of available hardware.\n", "Teaching the critical thinking skills\n", "required to formulate problems,\n", "the mathematics to solve them,\n", "and the software tools to implement those solutions\n", "all in one place presents formidable challenges.\n", "Our goal in this book is to present a unified resource\n", "to bring would-be practitioners up to speed.\n", "\n", "When we started this book project,\n", "there were no resources that simultaneously \n", "(i) remained up to date;\n", "(ii) covered the breadth of modern machine learning practices \n", "with sufficient technical depth;\n", "and (iii) interleaved exposition of \n", "the quality one expects of a textbook \n", "with the clean runnable code\n", "that one expects of a hands-on tutorial.\n", "We found plenty of code examples illustrating\n", "how to use a given deep learning framework\n", "(e.g., how to do basic numerical computing with matrices in TensorFlow)\n", "or for implementing particular techniques\n", "(e.g., code snippets for LeNet, AlexNet, ResNet, etc.)\n", "scattered across various blog posts and GitHub repositories.\n", "However, these examples typically focused on\n", "*how* to implement a given approach,\n", "but left out the discussion of\n", "*why* certain algorithmic decisions are made.\n", "While some interactive resources\n", "have popped up sporadically\n", "to address a particular topic,\n", "e.g., the engaging blog posts\n", "published on the website [Distill](http://distill.pub), or personal blogs,\n", "they only covered selected topics in deep learning,\n", "and often lacked associated code.\n", "On the other hand, while several deep learning textbooks\n", "have emerged---e.g., :citet:`Goodfellow.Bengio.Courville.2016`,\n", "which offers a comprehensive survey\n", "on the basics of deep learning---these\n", "resources do not marry the descriptions\n", "to realizations of the concepts in code,\n", "sometimes leaving readers clueless\n", "as to how to implement them.\n", "Moreover, too many resources\n", "are hidden behind the paywalls\n", "of commercial course providers.\n", "\n", "We set out to create a resource that could\n", "(i) be freely available for everyone;\n", "(ii) offer sufficient technical depth\n", "to provide a starting point on the path\n", "to actually becoming an applied machine learning scientist;\n", "(iii) include runnable code, showing readers\n", "*how* to solve problems in practice;\n", "(iv) allow for rapid updates, both by us\n", "and also by the community at large;\n", "and (v) be complemented by a [forum](https://discuss.d2l.ai/c/5)\n", "for interactive discussion of technical details and to answer questions.\n", "\n", "These goals were often in conflict.\n", "Equations, theorems, and citations\n", "are best managed and laid out in LaTeX.\n", "Code is best described in Python.\n", "And webpages are native in HTML and JavaScript.\n", "Furthermore, we want the content to be\n", "accessible both as executable code, as a physical book,\n", "as a downloadable PDF, and on the Internet as a website.\n", "No workflows seemed suited to these demands, \n", "so we decided to assemble our own (:numref:`sec_how_to_contribute`).\n", "We settled on GitHub to share the source \n", "and to facilitate community contributions;\n", "Jupyter notebooks for mixing code, equations and text;\n", "Sphinx as a rendering engine; \n", "and Discourse as a discussion platform.\n", "While our system is not perfect,\n", "these choices strike a compromise \n", "among the competing concerns.\n", "We believe that *Dive into Deep Learning*\n", "might be the first book published\n", "using such an integrated workflow.\n", "\n", "\n", "### Learning by Doing\n", "\n", "Many textbooks present concepts in succession,\n", "covering each in exhaustive detail.\n", "For example, \n", "the excellent textbook of \n", ":citet:`<PERSON>.2006`,\n", "teaches each topic so thoroughly\n", "that getting to the chapter\n", "on linear regression requires\n", "a nontrivial amount of work.\n", "While experts love this book\n", "precisely for its thoroughness,\n", "for true beginners, this property limits\n", "its usefulness as an introductory text.\n", "\n", "In this book, we teach most concepts *just in time*.\n", "In other words, you will learn concepts at the very moment\n", "that they are needed to accomplish some practical end.\n", "While we take some time at the outset to teach\n", "fundamental preliminaries, like linear algebra and probability,\n", "we want you to taste the satisfaction of training your first model\n", "before worrying about more esoteric concepts.\n", "\n", "Aside from a few preliminary notebooks that provide a crash course\n", "in the basic mathematical background,\n", "each subsequent chapter both introduces a reasonable number of new concepts\n", "and provides several self-contained working examples, using real datasets.\n", "This presented an organizational challenge.\n", "Some models might logically be grouped together in a single notebook.\n", "And some ideas might be best taught\n", "by executing several models in succession.\n", "By contrast, there is a big advantage to adhering\n", "to a policy of *one working example, one notebook*:\n", "This makes it as easy as possible for you to\n", "start your own research projects by leveraging our code.\n", "Just copy a notebook and start modifying it.\n", "\n", "Throughout, we interleave the runnable code\n", "with background material as needed.\n", "In general, we err on the side of making tools\n", "available before explaining them fully\n", "(often filling in the background later).\n", "For instance, we might use *stochastic gradient descent*\n", "before explaining why it is useful \n", "or offering some intuition for why it works.\n", "This helps to give practitioners the necessary\n", "ammunition to solve problems quickly,\n", "at the expense of requiring the reader\n", "to trust us with some curatorial decisions.\n", "\n", "This book teaches deep learning concepts from scratch.\n", "Sometimes, we delve into fine details about models\n", "that would typically be hidden from users\n", "by modern deep learning frameworks.\n", "This comes up especially in the basic tutorials,\n", "where we want you to understand everything\n", "that happens in a given layer or optimizer.\n", "In these cases, we often present \n", "two versions of the example:\n", "one where we implement everything from scratch,\n", "relying only on NumPy-like functionality\n", "and automatic differentiation,\n", "and a more practical example,\n", "where we write succinct code \n", "using the high-level APIs of deep learning frameworks.\n", "After explaining how some component works,\n", "we rely on the high-level API in subsequent tutorials.\n", "\n", "\n", "### Content and Structure\n", "\n", "The book can be divided into roughly three parts,\n", "dealing with preliminaries, \n", "deep learning techniques,\n", "and advanced topics\n", "focused on real systems\n", "and applications (:numref:`fig_book_org`).\n", "\n", "![Book structure.](../img/book-org.svg)\n", ":label:`fig_book_org`\n", "\n", "\n", "* **Part 1: Basics and Preliminaries**.\n", ":numref:`chap_introduction` is \n", "an introduction to deep learning.\n", "Then, in :numref:`chap_preliminaries`,\n", "we quickly bring you up to speed\n", "on the prerequisites required\n", "for hands-on deep learning,\n", "such as how to store and manipulate data,\n", "and how to apply various numerical operations\n", "based on elementary concepts from linear algebra,\n", "calculus, and probability.\n", ":numref:`chap_regression` and :numref:`chap_perceptrons`\n", "cover the most fundamental concepts and techniques in deep learning,\n", "including regression and classification;\n", "linear models; multilayer perceptrons;\n", "and overfitting and regularization.\n", "\n", "* **Part 2: Modern Deep Learning Techniques**.\n", ":numref:`chap_computation` describes\n", "the key computational components\n", "of deep learning systems\n", "and lays the groundwork\n", "for our subsequent implementations\n", "of more complex models.\n", "Next, :numref:`chap_cnn` and :numref:`chap_modern_cnn`\n", "present convolutional neural networks (CNNs), \n", "powerful tools that form the backbone \n", "of most modern computer vision systems.\n", "Similarly, :numref:`chap_rnn` and :numref:`chap_modern_rnn`\n", "introduce recurrent neural networks (RNNs),\n", "models that exploit sequential (e.g., temporal)\n", "structure in data and are commonly used\n", "for natural language processing\n", "and time series prediction.\n", "In :numref:`chap_attention-and-transformers`, \n", "we describe a relatively new class of models,\n", "based on so-called *attention mechanisms*,\n", "that has displaced RNNs as the dominant architecture\n", "for most natural language processing tasks.\n", "These sections will bring you up to speed\n", "on the most powerful and general tools\n", "that are widely used by deep learning practitioners.\n", "\n", "* **Part 3: Scalability, Efficiency, and Applications** (available [online](https://d2l.ai)).\n", "In Chapter 12,\n", "we discuss several common optimization algorithms\n", "used to train deep learning models.\n", "Next, in Chapter 13,\n", "we examine several key factors\n", "that influence the computational performance \n", "of deep learning code.\n", "Then, in Chapter 14,\n", "we illustrate major applications \n", "of deep learning in computer vision.\n", "Finally, in Chapter 15 and Chapter 16,\n", "we demonstrate how to pretrain language representation models \n", "and apply them to natural language processing tasks.\n", "\n", "\n", "### Code\n", ":label:`sec_code`\n", "\n", "Most sections of this book feature executable code.\n", "We believe that some intuitions are best developed\n", "via trial and error,\n", "tweaking the code in small ways and observing the results.\n", "Ideally, an elegant mathematical theory might tell us\n", "precisely how to tweak our code to achieve a desired result.\n", "However, deep learning practitioners today\n", "must often tread where no solid theory provides guidance. \n", "Despite our best attempts, formal explanations \n", "for the efficacy of various techniques are\n", "still lacking, for a variety of reasons: the mathematics to characterize these models\n", "can be so difficult;\n", "the explanation likely depends on properties \n", "of the data that currently lack clear definitions;\n", "and serious inquiry on these topics\n", "has only recently kicked into high gear.\n", "We are hopeful that as the theory of deep learning progresses,\n", "each future edition of this book will provide insights \n", "that eclipse those presently available.\n", "\n", "To avoid unnecessary repetition, we capture\n", "some of our most frequently imported and used\n", "functions and classes in the `d2l` package.\n", "Throughout, we mark blocks of code\n", "(such as functions, classes,\n", "or collection of import statements) with `#@save`\n", "to indicate that they will be accessed later\n", "via the `d2l` package.\n", "We offer a detailed overview \n", "of these classes and functions in :numref:`sec_d2l`.\n", "The `d2l` package is lightweight and only requires\n", "the following dependencies:\n"]}, {"cell_type": "code", "execution_count": 1, "id": "1ec1addc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:13.372553Z", "iopub.status.busy": "2023-08-18T19:26:13.371850Z", "iopub.status.idle": "2023-08-18T19:26:14.298913Z", "shell.execute_reply": "2023-08-18T19:26:14.297835Z"}, "origin_pos": 1, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "import collections\n", "import hashlib\n", "import inspect\n", "import math\n", "import os\n", "import random\n", "import re\n", "import shutil\n", "import sys\n", "import tarfile\n", "import time\n", "import zipfile\n", "from collections import defaultdict\n", "import pandas as pd\n", "import requests\n", "from IPython import display\n", "from matplotlib import pyplot as plt\n", "from matplotlib_inline import backend_inline\n", "\n", "d2l = sys.modules[__name__]"]}, {"cell_type": "markdown", "id": "43382b4a", "metadata": {"origin_pos": 3, "tab": ["pytorch"]}, "source": ["Most of the code in this book is based on <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "a popular open-source framework\n", "that has been enthusiastically embraced\n", "by the deep learning research community.\n", "All of the code in this book has passed tests\n", "under the latest stable version of PyTorch.\n", "However, due to the rapid development of deep learning,\n", "some code *in the print edition*\n", "may not work properly in future versions of PyTorch.\n", "We plan to keep the online version up to date.\n", "In case you encounter any problems,\n", "please consult :ref:`chap_installation`\n", "to update your code and runtime environment.\n", "Below lists dependencies in our PyTorch implementation.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "4a9cc53a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:14.303163Z", "iopub.status.busy": "2023-08-18T19:26:14.302440Z", "iopub.status.idle": "2023-08-18T19:26:16.150398Z", "shell.execute_reply": "2023-08-18T19:26:16.148962Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "import numpy as np\n", "import torch\n", "import torchvision\n", "from PIL import Image\n", "from scipy.spatial import distance_matrix\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from torchvision import transforms"]}, {"cell_type": "markdown", "id": "9381f00a", "metadata": {"origin_pos": 10}, "source": ["### Target Audience\n", "\n", "This book is for students (undergraduate or graduate),\n", "engineers, and researchers, who seek a solid grasp\n", "of the practical techniques of deep learning.\n", "Because we explain every concept from scratch,\n", "no previous background in deep learning or machine learning is required.\n", "Fully explaining the methods of deep learning\n", "requires some mathematics and programming,\n", "but we will only assume that you enter with some basics,\n", "including modest amounts of linear algebra,\n", "calculus, probability, and Python programming.\n", "Just in case you have forgotten anything,\n", "the [online Appendix](https://d2l.ai/chapter_appendix-mathematics-for-deep-learning/index.html) provides a refresher\n", "on most of the mathematics\n", "you will find in this book.\n", "Usually, we will prioritize\n", "intuition and ideas\n", "over mathematical rigor.\n", "If you would like to extend these foundations\n", "beyond the prerequisites to understand our book,\n", "we happily recommend some other terrific resources:\n", "*Linear Analysis* by :citet:`Bollobas.1999`\n", "covers linear algebra and functional analysis in great depth.\n", "*All of Statistics* :cite:`Wasserman.2013`\n", "provides a marvelous introduction to statistics.\n", "<PERSON>'s [books](https://www.amazon.com/Introduction-Probability-<PERSON>-Statistical-Science/dp/1138369918)\n", "and [courses](https://projects.iq.harvard.edu/stat110/home)\n", "on probability and inference are pedagogical gems.\n", "And if you have not used Python before,\n", "you may want to peruse this [Python tutorial](http://learnpython.org/).\n", "\n", "\n", "### Notebooks, Website, GitHub, and Forum\n", "\n", "All of our notebooks are available for download\n", "on the [D2L.ai website](https://d2l.ai)\n", "and on [GitHub](https://github.com/d2l-ai/d2l-en).\n", "Associated with this book, we have launched a discussion forum,\n", "located at [discuss.d2l.ai](https://discuss.d2l.ai/c/5).\n", "Whenever you have questions on any section of the book,\n", "you can find a link to the associated discussion page\n", "at the end of each notebook.\n", "\n", "\n", "\n", "## Acknowledgments\n", "\n", "We are indebted to the hundreds of contributors for both\n", "the English and the Chinese drafts.\n", "They helped improve the content and offered valuable feedback.\n", "This book was originally implemented with MXNet as the primary framework.\n", "We thank <PERSON><PERSON><PERSON><PERSON> and <PERSON> for adapting a majority part of earlier MXNet code into PyTorch and TensorFlow implementations, respectively.\n", "Since July 2021, we have redesigned and reimplemented this book in PyTorch, MXNet, and TensorFlow, choosing PyTorch as the primary framework.\n", "We thank <PERSON><PERSON><PERSON><PERSON> for adapting a majority part of more recent PyTorch code into JAX implementations.\n", "We thank <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> from Baidu for adapting a majority part of more recent PyTorch code into PaddlePaddle implementations in the Chinese draft.\n", "We thank <PERSON><PERSON> for integrating the LaTeX style from the press into the PDF building.\n", "\n", "On GitHub, we thank every contributor of this English draft\n", "for making it better for everyone.\n", "Their GitHub IDs or names are (in no particular order):\n", "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, bowen0701, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>,\n", "<PERSON><PERSON>, <PERSON>, <PERSON> (<PERSON><PERSON>) <PERSON>, <PERSON>,\n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, sad<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,\n", "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, vfdev-5, d<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,\n", "mani2106, mtn, lkevinzc, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>,\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>,\n", "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>182, <PERSON><PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, d<PERSON><PERSON>77, <PERSON>, <PERSON><PERSON>, <PERSON>, ch<PERSON><PERSON><PERSON>, vn09,\n", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>0214, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, d<PERSON><PERSON>1337, <PERSON><PERSON><PERSON><PERSON>,\n", "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, rig<PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, m<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>,\n", "sl7423, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, c<PERSON>4, c<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON>, t<PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NotAnotherSystem, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "the-great-<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, bi<PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>730, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, l<PERSON><PERSON><PERSON>p, netyster, y<PERSON><PERSON>a, <PERSON><PERSON><PERSON>hara<PERSON>, heiligerl, SportsTHU,\n", "<PERSON><PERSON>, man<PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON><PERSON>wicklung, aterz<PERSON>-<PERSON>, n<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>,\n", "mathresearch, mzz2017, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON>, BSharmi, vkramdev, si<PERSON><PERSON><PERSON>es, LakshKD,\n", "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>95, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>233, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>,\n", "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>,\n", "j<PERSON>221, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,\n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, quake2005, nils-werner, <PERSON><PERSON>, <PERSON><PERSON>,\n", "<PERSON> \"<PERSON><PERSON>\" <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>,\n", "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>,\n", "<PERSON>, <PERSON><PERSON>5474, kx<PERSON><PERSON>, z<PERSON><PERSON>1992, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, bolded, <PERSON>, <PERSON><PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>,\n", "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>,\n", "pgg<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.\n", "\n", "We thank Amazon Web Services, especially <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,\n", "and <PERSON> for their generous support in writing this book.\n", "Without the available time, resources, discussions with colleagues,\n", "and continuous encouragement, this book would not have happened.\n", "During the preparation of the book for publication,\n", "Cambridge University Press has offered excellent support.\n", "We thank our commissioning editor <PERSON>\n", "for his help and professionalism.\n", "\n", "\n", "## Summary\n", "\n", "Deep learning has revolutionized pattern recognition, \n", "introducing technology that now powers a wide range of  technologies, \n", "in such diverse fields as computer vision,\n", "natural language processing,\n", "and automatic speech recognition.\n", "To successfully apply deep learning, \n", "you must understand how to cast a problem,\n", "the basic mathematics of modeling,\n", "the algorithms for fitting your models to data,\n", "and the engineering techniques to implement it all.\n", "This book presents a comprehensive resource, \n", "including prose, figures, mathematics, and code, all in one place.\n", "\n", "\n", "\n", "## Exercises\n", "\n", "1. Register an account on the discussion forum of this book [discuss.d2l.ai](https://discuss.d2l.ai/).\n", "1. Install Python on your computer.\n", "1. Follow the links at the bottom of the section to the forum, where you will be able to seek out help and discuss the book and find answers to your questions by engaging the authors and broader community.\n"]}, {"cell_type": "markdown", "id": "3fe50a56", "metadata": {"origin_pos": 12, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/20)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "5c254ef4", "metadata": {"origin_pos": 0}, "source": ["# Builders' Guide\n", ":label:`chap_computation`\n", "\n", "Alongside giant datasets and powerful hardware,\n", "great software tools have played an indispensable role\n", "in the rapid progress of deep learning.\n", "Starting with the pathbreaking Theano library released in 2007,\n", "flexible open-source tools have enabled researchers\n", "to rapidly prototype models, avoiding repetitive work\n", "when recycling standard components\n", "while still maintaining the ability to make low-level modifications.\n", "Over time, deep learning's libraries have evolved\n", "to offer increasingly coarse abstractions.\n", "Just as semiconductor designers went from specifying transistors\n", "to logical circuits to writing code,\n", "neural networks researchers have moved from thinking about\n", "the behavior of individual artificial neurons\n", "to conceiving of networks in terms of whole layers,\n", "and now often design architectures with far coarser *blocks* in mind.\n", "\n", "\n", "So far, we have introduced some basic machine learning concepts,\n", "ramping up to fully-functional deep learning models.\n", "In the last chapter,\n", "we implemented each component of an MLP from scratch\n", "and even showed how to leverage high-level APIs\n", "to roll out the same models effortlessly.\n", "To get you that far that fast, we *called upon* the libraries,\n", "but skipped over more advanced details about *how they work*.\n", "In this chapter, we will peel back the curtain,\n", "digging deeper into the key components of deep learning computation,\n", "namely model construction, parameter access and initialization,\n", "designing custom layers and blocks, reading and writing models to disk,\n", "and leveraging GPUs to achieve dramatic speedups.\n", "These insights will move you from *end user* to *power user*,\n", "giving you the tools needed to reap the benefits\n", "of a mature deep learning library while retaining the flexibility\n", "to implement more complex models, including those you invent yourself!\n", "While this chapter does not introduce any new models or datasets,\n", "the advanced modeling chapters that follow rely heavily on these techniques.\n", "\n", ":begin_tab:toc\n", " - [model-construction](model-construction.ipynb)\n", " - [parameters](parameters.ipynb)\n", " - [init-param](init-param.ipynb)\n", " - [lazy-init](lazy-init.ipynb)\n", " - [custom-layer](custom-layer.ipynb)\n", " - [read-write](read-write.ipynb)\n", " - [use-gpu](use-gpu.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
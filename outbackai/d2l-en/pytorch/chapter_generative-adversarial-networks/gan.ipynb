{"cells": [{"cell_type": "markdown", "id": "5f5f7758", "metadata": {"origin_pos": 0}, "source": ["# Generative Adversarial Networks\n", ":label:`sec_basic_gan`\n", "\n", "Throughout most of this book, we have talked about how to make predictions. In some form or another, we used deep neural networks to learn mappings from data examples to labels. This kind of learning is called discriminative learning, as in, we'd like to be able to discriminate between photos of cats and photos of dogs. Classifiers and regressors are both examples of discriminative learning. And neural networks trained by backpropagation have upended everything we thought we knew about discriminative learning on large complicated datasets. Classification accuracies on high-res images have gone from useless to human-level (with some caveats) in just 5-6 years. We will spare you another spiel about all the other discriminative tasks where deep neural networks do astoundingly well.\n", "\n", "But there is more to machine learning than just solving discriminative tasks. For example, given a large dataset, without any labels, we might want to learn a model that concisely captures the characteristics of this data. Given such a model, we could sample synthetic data examples that resemble the distribution of the training data. For example, given a large corpus of photographs of faces, we might want to be able to generate a new photorealistic image that looks like it might plausibly have come from the same dataset. This kind of learning is called generative modeling.\n", "\n", "Until recently, we had no method that could synthesize novel photorealistic images. But the success of deep neural networks for discriminative learning opened up new possibilities. One big trend over the last three years has been the application of discriminative deep nets to overcome challenges in problems that we do not generally think of as supervised learning problems. The recurrent neural network language models are one example of using a discriminative network (trained to predict the next character) that once trained can act as a generative model.\n", "\n", "In 2014, a breakthrough paper introduced Generative adversarial networks (GANs) :cite:`Goodfellow.Pouget-Abadie.Mirza.ea.2014`, a clever new way to leverage the power of discriminative models to get good generative models. At their heart, GANs rely on the idea that a data generator is good if we cannot tell fake data apart from real data. In statistics, this is called a two-sample test - a test to answer the question whether datasets $X=\\{x_1,\\ldots, x_n\\}$ and $X'=\\{x'_1,\\ldots, x'_n\\}$ were drawn from the same distribution. The main difference between most statistics papers and GANs is that the latter use this idea in a constructive way. In other words, rather than just training a model to say \"hey, these two datasets do not look like they came from the same distribution\", they use the [two-sample test](https://en.wikipedia.org/wiki/Two-sample_hypothesis_testing) to provide training signals to a generative model. This allows us to improve the data generator until it generates something that resembles the real data. At the very least, it needs to fool the classifier even if our classifier is a state of the art deep neural network.\n", "\n", "![Generative Adversarial Networks](../img/gan.svg)\n", ":label:`fig_gan`\n", "\n", "\n", "The GAN architecture is illustrated in :numref:`fig_gan`.\n", "As you can see, there are two pieces in GAN architecture - first off, we need a device (say, a deep network but it really could be anything, such as a game rendering engine) that might potentially be able to generate data that looks just like the real thing. If we are dealing with images, this needs to generate images. If we are dealing with speech, it needs to generate audio sequences, and so on. We call this the generator network. The second component is the discriminator network. It attempts to distinguish fake and real data from each other. Both networks are in competition with each other. The generator network attempts to fool the discriminator network. At that point, the discriminator network adapts to the new fake data. This information, in turn is used to improve the generator network, and so on.\n", "\n", "\n", "The discriminator is a binary classifier to distinguish if the input $x$ is real (from real data) or fake (from the generator). Typically, the discriminator outputs a scalar prediction $o\\in\\mathbb R$ for input $\\mathbf x$, such as using a fully connected layer with hidden size 1, and then applies sigmoid function to obtain the predicted probability $D(\\mathbf x) = 1/(1+e^{-o})$. Assume the label $y$ for the true data is $1$ and $0$ for the fake data. We train the discriminator to minimize the cross-entropy loss, *i.e.*,\n", "\n", "$$ \\min_D \\{ - y \\log D(\\mathbf x) - (1-y)\\log(1-D(\\mathbf x)) \\},$$\n", "\n", "For the generator, it first draws some parameter $\\mathbf z\\in\\mathbb R^d$ from a source of randomness, *e.g.*, a normal distribution $\\mathbf z \\sim \\mathcal{N} (0, 1)$. We often call $\\mathbf z$ as the latent variable.\n", "It then applies a function to generate $\\mathbf x'=G(\\mathbf z)$. The goal of the generator is to fool the discriminator to classify $\\mathbf x'=G(\\mathbf z)$ as true data, *i.e.*, we want $D( G(\\mathbf z)) \\approx 1$.\n", "In other words, for a given discriminator $D$, we update the parameters of the generator $G$ to maximize the cross-entropy loss when $y=0$, *i.e.*,\n", "\n", "$$ \\max_G \\{ - (1-y) \\log(1-D(G(\\mathbf z))) \\} = \\max_G \\{ - \\log(1-D(G(\\mathbf z))) \\}.$$\n", "\n", "If the generator does a perfect job, then $D(\\mathbf x')\\approx 1$, so the above loss is near 0, which results in the gradients that are too small to make good progress for the discriminator. So commonly, we minimize the following loss:\n", "\n", "$$ \\min_G \\{ - y \\log(D(G(\\mathbf z))) \\} = \\min_G \\{ - \\log(D(G(\\mathbf z))) \\}, $$\n", "\n", "which is just feeding $\\mathbf x'=G(\\mathbf z)$ into the discriminator but giving label $y=1$.\n", "\n", "\n", "To sum up, $D$ and $G$ are playing a \"minimax\" game with the comprehensive objective function:\n", "\n", "$$\\min_D \\max_G \\{ -E_{x \\sim \\textrm{Data}} \\log D(\\mathbf x) - E_{z \\sim \\textrm{Noise}} \\log(1 - D(G(\\mathbf z))) \\}.$$\n", "\n", "\n", "\n", "Many of the GANs applications are in the context of images. As a demonstration purpose, we are going to content ourselves with fitting a much simpler distribution first. We will illustrate what happens if we use GANs to build the world's most inefficient estimator of parameters for a Gaussian. Let's get started.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "e550e9d0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:50.913395Z", "iopub.status.busy": "2023-08-18T19:37:50.912459Z", "iopub.status.idle": "2023-08-18T19:37:54.553799Z", "shell.execute_reply": "2023-08-18T19:37:54.549156Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "c34697c4", "metadata": {"origin_pos": 4}, "source": ["## Generate Some \"Real\" Data\n", "\n", "Since this is going to be the world's lamest example, we simply generate data drawn from a Gaussian.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "a23fc851", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:54.560462Z", "iopub.status.busy": "2023-08-18T19:37:54.559808Z", "iopub.status.idle": "2023-08-18T19:37:54.592970Z", "shell.execute_reply": "2023-08-18T19:37:54.591379Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [], "source": ["X = torch.normal(0.0, 1, (1000, 2))\n", "A = torch.tensor([[1, 2], [-0.1, 0.5]])\n", "b = torch.tensor([1, 2])\n", "data = torch.matmul(X, A) + b"]}, {"cell_type": "markdown", "id": "3eae2cb2", "metadata": {"origin_pos": 7}, "source": ["Let's see what we got. This should be a Gaussian shifted in some rather arbitrary way with mean $b$ and covariance matrix $A^TA$.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "d8224fa2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:54.598846Z", "iopub.status.busy": "2023-08-18T19:37:54.596687Z", "iopub.status.idle": "2023-08-18T19:37:54.819864Z", "shell.execute_reply": "2023-08-18T19:37:54.818679Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The covariance matrix is\n", "tensor([[1.0100, 1.9500],\n", "        [1.9500, 4.2500]])\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"231.442187pt\" height=\"169.678125pt\" viewBox=\"0 0 231.**********.678125\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:37:54.774380</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 169.678125 \n", "L 231.**********.678125 \n", "L 231.442187 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 28.**********.8 \n", "L 224.**********.8 \n", "L 224.242188 7.2 \n", "L 28.942188 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"PathCollection_1\">\n", "    <defs>\n", "     <path id=\"m582b3c2d1f\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #1f77b4\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p86f5878c74)\">\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"118.365529\" y=\"85.176468\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"115.645693\" y=\"105.943479\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"123.656095\" y=\"96.391606\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"152.669838\" y=\"79.667357\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"189.159548\" y=\"33.165445\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"122.122848\" y=\"84.943091\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"177.095198\" y=\"52.087485\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"37.81946\" y=\"139.5\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"145.037025\" y=\"88.736882\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"114.028258\" y=\"89.384359\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"134.601526\" y=\"97.674451\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"173.77713\" y=\"48.592766\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"196.802078\" y=\"17.216698\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"79.897752\" y=\"118.99858\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"175.632891\" y=\"57.320413\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"143.15007\" y=\"85.518612\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"121.730408\" y=\"104.469472\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"208.097904\" y=\"29.984883\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"80.01725\" y=\"130.060656\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"130.578119\" y=\"84.019259\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"117.572741\" y=\"84.499232\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"164.217141\" y=\"59.2704\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"134.683056\" y=\"68.618106\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"99.138672\" y=\"116.033487\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"131.948941\" y=\"77.439485\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"150.226163\" y=\"72.865561\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"136.416384\" y=\"70.935615\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"195.98932\" y=\"21.576112\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"136.18814\" y=\"96.461901\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"164.881717\" y=\"67.0608\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"98.494902\" y=\"106.664098\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"113.585228\" y=\"86.636769\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"187.278229\" y=\"53.828171\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"138.661145\" y=\"75.138248\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"178.774624\" y=\"39.447271\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"130.653839\" y=\"87.848731\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"119.365908\" y=\"96.586348\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"187.93903\" y=\"32.180584\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"154.357205\" y=\"77.277179\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"105.577854\" y=\"104.67978\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"140.148356\" y=\"75.369159\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"143.854731\" y=\"70.38021\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"109.618903\" y=\"105.001714\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"157.478845\" y=\"57.512369\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"171.330389\" y=\"51.566127\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"108.215792\" y=\"103.613339\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"119.406383\" y=\"95.874677\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"145.331785\" y=\"73.159155\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"128.562209\" y=\"91.296344\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"176.831348\" y=\"53.598487\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"144.246244\" y=\"84.705283\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"203.068781\" y=\"35.469857\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"190.411173\" y=\"44.679103\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"128.984804\" y=\"94.953905\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"161.867681\" y=\"69.022336\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"183.506681\" y=\"42.725217\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"160.965345\" y=\"66.412583\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"159.127814\" y=\"60.67178\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"141.539797\" y=\"90.979288\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"121.081695\" y=\"84.178715\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"143.276135\" y=\"55.312727\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"149.020627\" y=\"86.235211\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"143.717031\" y=\"82.70225\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"146.200122\" y=\"68.863476\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"137.268869\" y=\"75.648411\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"126.582423\" y=\"99.25498\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"194.534629\" y=\"29.373981\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"126.867375\" y=\"93.558238\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"158.186217\" y=\"62.202089\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"168.257179\" y=\"43.474274\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"153.275113\" y=\"73.060867\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"121.965113\" y=\"83.864484\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"215.364915\" y=\"13.5\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"184.05289\" y=\"40.72357\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"151.286381\" y=\"75.21654\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"148.91029\" y=\"69.141026\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"159.750262\" y=\"62.728224\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"195.817637\" y=\"30.811543\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"125.778235\" y=\"102.966175\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"162.597345\" y=\"60.076855\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"193.987711\" y=\"23.469624\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"132.586678\" y=\"78.572307\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"126.827353\" y=\"89.478341\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"130.959441\" y=\"91.863925\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"179.642748\" y=\"51.630117\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"167.308634\" y=\"67.338125\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"141.486264\" y=\"56.18629\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"161.039751\" y=\"51.952076\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"124.296962\" y=\"94.159039\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"104.989076\" y=\"102.15821\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"149.371229\" y=\"65.454789\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"156.836848\" y=\"39.204909\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"159.082237\" y=\"53.006363\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"143.113686\" y=\"62.336721\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"125.56927\" y=\"83.213304\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"118.593816\" y=\"99.600305\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"101.428921\" y=\"108.810523\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"148.062524\" y=\"58.176075\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"184.870532\" y=\"40.994025\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m582b3c2d1f\" x=\"142.658863\" y=\"64.471668\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"ma76bc3388b\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma76bc3388b\" x=\"52.908599\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(45.537505 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#ma76bc3388b\" x=\"115.5444\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(112.36315 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#ma76bc3388b\" x=\"178.180202\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(174.998952 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m4e2ace5e16\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m4e2ace5e16\" x=\"28.942188\" y=\"145.162186\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(7.2 148.961405) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m4e2ace5e16\" x=\"28.942188\" y=\"121.035898\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(7.2 124.835117) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m4e2ace5e16\" x=\"28.942188\" y=\"96.90961\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(15.579688 100.708829) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m4e2ace5e16\" x=\"28.942188\" y=\"72.783323\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(15.579688 76.582541) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m4e2ace5e16\" x=\"28.942188\" y=\"48.657035\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(15.579688 52.456254) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m4e2ace5e16\" x=\"28.942188\" y=\"24.530747\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(15.579688 28.329966) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 28.**********.8 \n", "L 28.942188 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 224.**********.8 \n", "L 224.242188 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 28.**********.8 \n", "L 224.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 28.942188 7.2 \n", "L 224.242188 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p86f5878c74\">\n", "   <rect x=\"28.942188\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.set_figsize()\n", "d2l.plt.scatter(data[:100, (0)].detach().numpy(), data[:100, (1)].detach().numpy());\n", "print(f'The covariance matrix is\\n{torch.matmul(A.T, A)}')"]}, {"cell_type": "code", "execution_count": 4, "id": "58699d06", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:54.824909Z", "iopub.status.busy": "2023-08-18T19:37:54.824547Z", "iopub.status.idle": "2023-08-18T19:37:54.830317Z", "shell.execute_reply": "2023-08-18T19:37:54.829167Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["batch_size = 8\n", "data_iter = d2l.load_array((data,), batch_size)"]}, {"cell_type": "markdown", "id": "19ffd737", "metadata": {"origin_pos": 11}, "source": ["## Generator\n", "\n", "Our generator network will be the simplest network possible - a single layer linear model. This is since we will be driving that linear network with a Gaussian data generator. Hence, it literally only needs to learn the parameters to fake things perfectly.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ccc0fd58", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:54.834958Z", "iopub.status.busy": "2023-08-18T19:37:54.834346Z", "iopub.status.idle": "2023-08-18T19:37:54.841305Z", "shell.execute_reply": "2023-08-18T19:37:54.840296Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["net_G = nn.Sequential(nn.<PERSON>ar(2, 2))"]}, {"cell_type": "markdown", "id": "3b0389a4", "metadata": {"origin_pos": 15}, "source": ["## Discriminator\n", "\n", "For the discriminator we will be a bit more discriminating: we will use an MLP with 3 layers to make things a bit more interesting.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "1ee21c33", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:54.847079Z", "iopub.status.busy": "2023-08-18T19:37:54.846478Z", "iopub.status.idle": "2023-08-18T19:37:54.851943Z", "shell.execute_reply": "2023-08-18T19:37:54.850943Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["net_D = nn.Sequential(\n", "    nn.<PERSON><PERSON>(2, 5), nn.<PERSON>(),\n", "    nn.<PERSON><PERSON>(5, 3), nn.<PERSON>(),\n", "    nn.<PERSON><PERSON>(3, 1))"]}, {"cell_type": "markdown", "id": "5e4dfd02", "metadata": {"origin_pos": 19}, "source": ["## Training\n", "\n", "First we define a function to update the discriminator.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "b3b8cf85", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:54.857667Z", "iopub.status.busy": "2023-08-18T19:37:54.857059Z", "iopub.status.idle": "2023-08-18T19:37:54.866336Z", "shell.execute_reply": "2023-08-18T19:37:54.864795Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def update_D(X, Z, net_D, net_G, loss, trainer_D):\n", "    \"\"\"Update discriminator.\"\"\"\n", "    batch_size = X.shape[0]\n", "    ones = torch.ones((batch_size,), device=X.device)\n", "    zeros = torch.zeros((batch_size,), device=X.device)\n", "    trainer_<PERSON>.zero_grad()\n", "    real_Y = net_D(X)\n", "    fake_X = net_G(Z)\n", "    # Do not need to compute gradient for `net_G`, detach it from\n", "    # computing gradients.\n", "    fake_Y = net_D(fake_X.detach())\n", "    loss_D = (loss(real_Y, ones.reshape(real_Y.shape)) +\n", "              loss(fake_Y, zeros.reshape(fake_Y.shape))) / 2\n", "    loss_D.backward()\n", "    trainer_<PERSON>.step()\n", "    return loss_D"]}, {"cell_type": "markdown", "id": "5918495e", "metadata": {"origin_pos": 23}, "source": ["The generator is updated similarly. Here we reuse the cross-entropy loss but change the label of the fake data from $0$ to $1$.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "e30b1d11", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:54.871558Z", "iopub.status.busy": "2023-08-18T19:37:54.870130Z", "iopub.status.idle": "2023-08-18T19:37:54.877351Z", "shell.execute_reply": "2023-08-18T19:37:54.876243Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def update_G(Z, net_D, net_G, loss, trainer_G):\n", "    \"\"\"Update generator.\"\"\"\n", "    batch_size = Z.shape[0]\n", "    ones = torch.ones((batch_size,), device=Z.device)\n", "    trainer_<PERSON>.zero_grad()\n", "    # We could reuse `fake_X` from `update_D` to save computation\n", "    fake_X = net_G(Z)\n", "    # Recomputing `fake_Y` is needed since `net_D` is changed\n", "    fake_Y = net_D(fake_X)\n", "    loss_G = loss(fake_Y, ones.reshape(fake_Y.shape))\n", "    loss_G.backward()\n", "    trainer_<PERSON>.step()\n", "    return loss_G"]}, {"cell_type": "markdown", "id": "1ceac030", "metadata": {"origin_pos": 27}, "source": ["Both the discriminator and the generator performs a binary logistic regression with the cross-entropy loss. We use <PERSON> to smooth the training process. In each iteration, we first update the discriminator and then the generator. We visualize both losses and generated examples.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "742a9e3b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:54.881764Z", "iopub.status.busy": "2023-08-18T19:37:54.881214Z", "iopub.status.idle": "2023-08-18T19:37:54.892786Z", "shell.execute_reply": "2023-08-18T19:37:54.891599Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [], "source": ["def train(net_D, net_G, data_iter, num_epochs, lr_D, lr_G, latent_dim, data):\n", "    loss = nn.BCEWithLogitsLoss(reduction='sum')\n", "    for w in net_D.parameters():\n", "        nn.init.normal_(w, 0, 0.02)\n", "    for w in net_G.parameters():\n", "        nn.init.normal_(w, 0, 0.02)\n", "    trainer_D = torch.optim.Adam(net_D.parameters(), lr=lr_D)\n", "    trainer_G = torch.optim.Adam(net_G.parameters(), lr=lr_G)\n", "    animator = d2l.Animator(xlabel='epoch', ylabel='loss',\n", "                            xlim=[1, num_epochs], nrows=2, figsize=(5, 5),\n", "                            legend=['discriminator', 'generator'])\n", "    animator.fig.subplots_adjust(hspace=0.3)\n", "    for epoch in range(num_epochs):\n", "        # Train one epoch\n", "        timer = d2l.Timer()\n", "        metric = d2l.Accumulator(3)  # loss_D, loss_G, num_examples\n", "        for (X,) in data_iter:\n", "            batch_size = X.shape[0]\n", "            Z = torch.normal(0, 1, size=(batch_size, latent_dim))\n", "            metric.add(update_D(X, Z, net_D, net_G, loss, trainer_D),\n", "                       update_G(Z, net_D, net_G, loss, trainer_G),\n", "                       batch_size)\n", "        # Visualize generated examples\n", "        Z = torch.normal(0, 1, size=(100, latent_dim))\n", "        fake_X = net_G(Z).detach().numpy()\n", "        animator.axes[1].cla()\n", "        animator.axes[1].scatter(data[:, 0], data[:, 1])\n", "        animator.axes[1].scatter(fake_X[:, 0], fake_X[:, 1])\n", "        animator.axes[1].legend(['real', 'generated'])\n", "        # Show the losses\n", "        loss_D, loss_G = metric[0]/metric[2], metric[1]/metric[2]\n", "        animator.add(epoch + 1, (loss_D, loss_G))\n", "    print(f'loss_D {loss_D:.3f}, loss_G {loss_G:.3f}, '\n", "          f'{metric[2] / timer.stop():.1f} examples/sec')"]}, {"cell_type": "markdown", "id": "71239e94", "metadata": {"origin_pos": 31}, "source": ["Now we specify the hyperparameters to fit the Gaussian distribution.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "b4106ed3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:54.897440Z", "iopub.status.busy": "2023-08-18T19:37:54.896873Z", "iopub.status.idle": "2023-08-18T19:38:14.489948Z", "shell.execute_reply": "2023-08-18T19:38:14.485966Z"}, "origin_pos": 32, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss_D 0.693, loss_G 0.693, 1020.0 examples/sec\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"341.114062pt\" height=\"308.278125pt\" viewBox=\"0 0 341.**********.278125\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:38:14.324147</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 308.278125 \n", "L 341.**********.278125 \n", "L 341.114062 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 43.78125 127.721739 \n", "L 322.78125 127.721739 \n", "L 322.78125 7.2 \n", "L 43.78125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 65.807566 127.721739 \n", "L 65.807566 7.2 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m85f4ce73ff\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"65.807566\" y=\"127.721739\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(57.856003 142.320177) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 102.518092 127.721739 \n", "L 102.518092 7.2 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"102.518092\" y=\"127.721739\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 5.0 -->\n", "      <g transform=\"translate(94.56653 142.320177) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 139.228618 127.721739 \n", "L 139.228618 7.2 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"139.228618\" y=\"127.721739\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 7.5 -->\n", "      <g transform=\"translate(131.277056 142.320177) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 175.939145 127.721739 \n", "L 175.939145 7.2 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"175.939145\" y=\"127.721739\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 10.0 -->\n", "      <g transform=\"translate(164.806332 142.320177) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 212.649671 127.721739 \n", "L 212.649671 7.2 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"212.649671\" y=\"127.721739\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 12.5 -->\n", "      <g transform=\"translate(201.516859 142.320177) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 249.360197 127.721739 \n", "L 249.360197 7.2 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"249.360197\" y=\"127.721739\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 15.0 -->\n", "      <g transform=\"translate(238.227385 142.320177) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 286.070724 127.721739 \n", "L 286.070724 7.2 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"286.070724\" y=\"127.721739\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 17.5 -->\n", "      <g transform=\"translate(274.937911 142.320177) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 322.78125 127.721739 \n", "L 322.78125 7.2 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"322.78125\" y=\"127.721739\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 20.0 -->\n", "      <g transform=\"translate(311.648438 142.320177) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(168.053125 155.998302) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 43.78125 125.0627 \n", "L 322.78125 125.0627 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <defs>\n", "       <path id=\"m9f9739c5d5\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9f9739c5d5\" x=\"43.78125\" y=\"125.0627\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(20.878125 128.861919) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 43.78125 103.238514 \n", "L 322.78125 103.238514 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m9f9739c5d5\" x=\"43.78125\" y=\"103.238514\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(20.878125 107.037733) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 43.78125 81.414328 \n", "L 322.78125 81.414328 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m9f9739c5d5\" x=\"43.78125\" y=\"81.414328\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(20.878125 85.213547) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 43.78125 59.590142 \n", "L 322.78125 59.590142 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m9f9739c5d5\" x=\"43.78125\" y=\"59.590142\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(20.878125 63.389361) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_25\">\n", "      <path d=\"M 43.78125 37.765956 \n", "L 322.78125 37.765956 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m9f9739c5d5\" x=\"43.78125\" y=\"37.765956\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 1.2 -->\n", "      <g transform=\"translate(20.878125 41.565174) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_27\">\n", "      <path d=\"M 43.78125 15.94177 \n", "L 322.78125 15.94177 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m9f9739c5d5\" x=\"43.78125\" y=\"15.94177\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 1.4 -->\n", "      <g transform=\"translate(20.878125 19.740988) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798438 77.118682) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 43.78125 122.243478 \n", "L 58.465461 117.326555 \n", "L 73.149671 108.928847 \n", "L 87.833882 103.002845 \n", "L 102.518092 98.789515 \n", "L 117.202303 96.277413 \n", "L 131.886513 94.513704 \n", "L 146.570724 94.098627 \n", "L 161.254934 93.991561 \n", "L 175.939145 93.177296 \n", "L 190.623355 93.046845 \n", "L 205.307566 93.069742 \n", "L 219.991776 93.070357 \n", "L 234.675987 93.070966 \n", "L 249.360197 93.072961 \n", "L 264.044408 93.072465 \n", "L 278.728618 93.073401 \n", "L 293.412829 93.072469 \n", "L 308.097039 93.073387 \n", "L 322.78125 93.073001 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 43.78125 12.678261 \n", "L 58.465461 38.149882 \n", "L 73.149671 57.078722 \n", "L 87.833882 65.945116 \n", "L 102.518092 77.681416 \n", "L 117.202303 84.939461 \n", "L 131.886513 87.792465 \n", "L 146.570724 89.961938 \n", "L 161.254934 89.456507 \n", "L 175.939145 91.731053 \n", "L 190.623355 93.12571 \n", "L 205.307566 93.083098 \n", "L 219.991776 93.082186 \n", "L 234.675987 93.08163 \n", "L 249.360197 93.076097 \n", "L 264.044408 93.077914 \n", "L 278.728618 93.075392 \n", "L 293.412829 93.078636 \n", "L 308.097039 93.07569 \n", "L 322.78125 93.077438 \n", "\" clip-path=\"url(#pb450bf7cdf)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 43.78125 127.721739 \n", "L 43.78125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 322.78125 127.721739 \n", "L 322.78125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 43.78125 127.721739 \n", "L 322.78125 127.721739 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 43.78125 7.2 \n", "L 322.78125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 217.923437 44.55625 \n", "L 315.78125 44.55625 \n", "Q 317.78125 44.55625 317.78125 42.55625 \n", "L 317.78125 14.2 \n", "Q 317.78125 12.2 315.78125 12.2 \n", "L 217.923437 12.2 \n", "Q 215.923437 12.2 215.923437 14.2 \n", "L 215.923437 42.55625 \n", "Q 215.923437 44.55625 217.923437 44.55625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_31\">\n", "     <path d=\"M 219.923437 20.298438 \n", "L 229.923437 20.298438 \n", "L 239.923437 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- discriminator -->\n", "     <g transform=\"translate(247.923437 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-64\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"91.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"143.359375\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"198.339844\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"239.453125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"267.236328\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"364.648438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"392.431641\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"455.810547\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"517.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"556.298828\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"617.480469\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_32\">\n", "     <path d=\"M 219.923437 34.976563 \n", "L 229.923437 34.976563 \n", "L 239.923437 34.976563 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- generator -->\n", "     <g transform=\"translate(247.923437 38.476563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-67\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"188.378906\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"249.902344\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"291.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"352.294922\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"391.503906\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"452.685547\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 43.78125 284.4 \n", "L 322.78125 284.4 \n", "L 322.78125 163.878261 \n", "L 43.78125 163.878261 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"PathCollection_1\">\n", "    <defs>\n", "     <path id=\"mf05861aebc\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #1f77b4\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pee3058d77f)\">\n", "     <use xlink:href=\"#mf05861aebc\" x=\"170.371737\" y=\"231.683885\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"166.525331\" y=\"249.742156\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"177.853684\" y=\"241.436179\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"218.885071\" y=\"226.893354\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"270.489008\" y=\"186.456909\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"175.685358\" y=\"231.480949\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"253.427541\" y=\"202.910857\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"56.463068\" y=\"278.921739\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"208.090706\" y=\"234.779897\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"164.237946\" y=\"235.342921\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"193.332769\" y=\"242.551697\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"248.735112\" y=\"199.87197\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"281.297114\" y=\"172.588433\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"115.970408\" y=\"261.094417\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"251.359539\" y=\"207.461229\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"205.422164\" y=\"231.981401\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"175.130367\" y=\"248.460411\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"297.271729\" y=\"183.691203\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"116.139402\" y=\"270.713614\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"187.642846\" y=\"230.677617\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"169.250573\" y=\"231.094984\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"235.215327\" y=\"209.156869\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"193.448069\" y=\"217.28531\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"143.181017\" y=\"258.516076\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"189.58147\" y=\"224.956074\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"215.429212\" y=\"220.978749\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"195.899351\" y=\"219.300535\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"280.147707\" y=\"176.379228\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"195.576567\" y=\"241.497306\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"236.155174\" y=\"215.93113\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"142.270594\" y=\"250.368781\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"163.611412\" y=\"232.953712\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"267.828436\" y=\"204.424496\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"199.073904\" y=\"222.954998\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"255.802594\" y=\"191.919366\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"187.74993\" y=\"234.007592\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"171.786479\" y=\"241.60552\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"268.762945\" y=\"185.600508\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"221.271354\" y=\"224.814939\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"152.287341\" y=\"248.643287\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"201.177124\" y=\"223.155791\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"206.4187\" y=\"218.817574\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"158.002214\" y=\"248.923229\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"225.685993\" y=\"207.628147\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"245.274918\" y=\"202.457502\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"156.017927\" y=\"247.715947\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"171.843719\" y=\"240.986675\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"208.507557\" y=\"221.234048\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"184.791935\" y=\"237.005516\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"253.054404\" y=\"204.224771\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"206.972379\" y=\"231.274159\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"290.159517\" y=\"188.460745\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"272.259063\" y=\"196.468785\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"185.389571\" y=\"240.186004\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"231.892708\" y=\"217.636814\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"262.494694\" y=\"194.769754\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"230.616619\" y=\"215.367464\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"228.017973\" y=\"210.375461\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"203.144909\" y=\"236.729815\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"174.212954\" y=\"230.816274\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"205.600446\" y=\"205.715415\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"213.724336\" y=\"232.604531\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"206.223964\" y=\"229.532391\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"209.735564\" y=\"217.498675\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"197.104939\" y=\"223.398618\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"181.992112\" y=\"243.926069\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"278.090476\" y=\"183.159984\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"182.395093\" y=\"238.972381\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"226.686362\" y=\"211.706164\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"240.928769\" y=\"195.421108\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"219.741054\" y=\"221.14858\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"175.462288\" y=\"230.54303\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"307.548774\" y=\"169.356522\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"263.267146\" y=\"193.029192\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"216.928577\" y=\"223.023079\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"213.568298\" y=\"217.740023\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"228.898243\" y=\"212.163673\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"279.904912\" y=\"184.410037\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"180.854825\" y=\"247.153196\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"232.924602\" y=\"209.858135\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"277.317021\" y=\"178.02576\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"190.483361\" y=\"225.941137\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"182.338494\" y=\"235.424644\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"188.182114\" y=\"237.499065\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"257.030299\" y=\"202.513145\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"239.587332\" y=\"216.172283\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"203.069202\" y=\"206.475034\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"230.721845\" y=\"202.79311\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"178.760002\" y=\"239.494816\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"151.454689\" y=\"246.450618\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"214.22016\" y=\"214.534599\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"224.778078\" y=\"191.708617\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"227.953518\" y=\"203.709881\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"205.37071\" y=\"211.823236\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"180.559306\" y=\"229.976786\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"170.694582\" y=\"244.226352\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"146.419898\" y=\"252.235238\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"212.369383\" y=\"208.205283\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"264.423459\" y=\"193.26437\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mf05861aebc\" x=\"204.727497\" y=\"213.679711\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"PathCollection_2\">\n", "    <defs>\n", "     <path id=\"md18c3713bf\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pee3058d77f)\">\n", "     <use xlink:href=\"#md18c3713bf\" x=\"181.106536\" y=\"230.750359\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"270.391723\" y=\"173.414947\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"250.661511\" y=\"216.800846\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"187.60839\" y=\"234.8204\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"208.990577\" y=\"214.504688\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"192.766646\" y=\"228.299873\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"206.063847\" y=\"225.257593\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"227.251139\" y=\"223.644037\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"220.006431\" y=\"240.549678\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"228.51321\" y=\"221.660484\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"223.064248\" y=\"214.44268\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"224.249261\" y=\"213.142258\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"158.244151\" y=\"248.191676\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"278.736181\" y=\"202.923879\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"186.103349\" y=\"226.998249\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"199.895103\" y=\"226.204003\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"128.308092\" y=\"250.378591\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"196.797147\" y=\"233.543387\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"273.163393\" y=\"212.892101\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"295.319164\" y=\"188.922288\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"181.52878\" y=\"258.329085\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"232.579802\" y=\"201.218895\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"253.676477\" y=\"185.824522\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"261.144266\" y=\"201.746613\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"161.878472\" y=\"241.012458\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"159.982865\" y=\"246.473543\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"148.279278\" y=\"246.948175\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"240.935717\" y=\"218.108338\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"235.166093\" y=\"201.662091\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"239.497771\" y=\"196.252784\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"164.274881\" y=\"242.160824\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"199.956935\" y=\"235.193198\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"104.366799\" y=\"271.445473\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"147.143304\" y=\"253.882158\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"266.482358\" y=\"205.782085\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"290.163118\" y=\"172.469098\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"250.606776\" y=\"198.338706\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"239.448785\" y=\"220.068918\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"210.973338\" y=\"215.538035\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"241.385596\" y=\"212.143278\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"222.572062\" y=\"230.988441\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"214.070309\" y=\"224.873877\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"266.881266\" y=\"206.696252\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"304.788499\" y=\"183.659986\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"132.464186\" y=\"253.970642\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"286.060482\" y=\"193.30657\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"186.846762\" y=\"239.071079\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"194.964504\" y=\"231.065985\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"139.303185\" y=\"257.060102\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"122.027689\" y=\"262.515903\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"262.345509\" y=\"207.727504\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"309.195017\" y=\"196.298456\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"193.977498\" y=\"240.753104\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"188.156536\" y=\"254.081308\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"173.528848\" y=\"229.213987\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"155.978448\" y=\"252.054013\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"165.325255\" y=\"233.581503\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"185.000653\" y=\"218.185017\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"292.162399\" y=\"177.310817\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"224.336092\" y=\"201.144857\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"210.999641\" y=\"228.545255\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"225.678295\" y=\"207.19752\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"212.047671\" y=\"226.050412\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"282.542274\" y=\"198.260036\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"250.343595\" y=\"199.156307\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"244.827183\" y=\"211.559425\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"248.443838\" y=\"190.058363\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"221.554645\" y=\"241.744464\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"235.710854\" y=\"211.516493\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"310.099432\" y=\"176.419518\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"210.535362\" y=\"228.658304\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"132.103007\" y=\"266.331987\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"176.865828\" y=\"253.243872\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"185.094057\" y=\"226.44093\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"217.534099\" y=\"209.912212\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"184.667\" y=\"235.657755\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"279.148827\" y=\"194.800215\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"230.932819\" y=\"206.221169\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"237.247141\" y=\"205.773982\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"218.512794\" y=\"225.736053\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"230.061836\" y=\"193.970744\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"240.538636\" y=\"210.057544\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"235.653595\" y=\"218.522271\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"270.493845\" y=\"199.272171\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"163.276619\" y=\"246.182298\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"176.375269\" y=\"220.695113\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"216.996982\" y=\"219.83516\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"196.818422\" y=\"220.001963\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"142.679462\" y=\"237.039174\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"167.134849\" y=\"241.342606\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"287.382062\" y=\"188.404859\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"253.257379\" y=\"198.153752\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"171.802825\" y=\"238.77259\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"239.720456\" y=\"203.550729\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"161.988397\" y=\"250.473501\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"238.984804\" y=\"205.251916\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"183.406095\" y=\"232.045484\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"218.134335\" y=\"214.61657\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"236.006268\" y=\"229.265769\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#md18c3713bf\" x=\"267.377428\" y=\"191.752348\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"77.802207\" y=\"284.4\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(70.431113 298.998438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"122.092145\" y=\"284.4\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(114.721051 298.998438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"166.382083\" y=\"284.4\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(163.200833 298.998438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"210.672021\" y=\"284.4\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(207.490771 298.998438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"254.961959\" y=\"284.4\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(251.780709 298.998438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#m85f4ce73ff\" x=\"299.251897\" y=\"284.4\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(296.070647 298.998438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#m9f9739c5d5\" x=\"43.78125\" y=\"283.845379\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_25\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(22.039063 287.644598) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#m9f9739c5d5\" x=\"43.78125\" y=\"262.865998\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_26\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(22.039063 266.665217) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_41\">\n", "      <g>\n", "       <use xlink:href=\"#m9f9739c5d5\" x=\"43.78125\" y=\"241.886618\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(30.41875 245.685836) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#m9f9739c5d5\" x=\"43.78125\" y=\"220.907237\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(30.41875 224.706456) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#m9f9739c5d5\" x=\"43.78125\" y=\"199.927856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(30.41875 203.727075) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#m9f9739c5d5\" x=\"43.78125\" y=\"178.948476\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(30.41875 182.747694) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 43.78125 284.4 \n", "L 43.78125 163.878261 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 322.78125 284.4 \n", "L 322.78125 163.878261 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 43.78125 284.4 \n", "L 322.78125 284.4 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 43.78125 163.878261 \n", "L 322.78125 163.878261 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_2\">\n", "    <g id=\"patch_13\">\n", "     <path d=\"M 50.78125 201.234511 \n", "L 134.434375 201.234511 \n", "Q 136.434375 201.234511 136.434375 199.234511 \n", "L 136.434375 170.878261 \n", "Q 136.434375 168.878261 134.434375 168.878261 \n", "L 50.78125 168.878261 \n", "Q 48.78125 168.878261 48.78125 170.878261 \n", "L 48.78125 199.234511 \n", "Q 48.78125 201.234511 50.78125 201.234511 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"PathCollection_3\">\n", "     <g>\n", "      <use xlink:href=\"#mf05861aebc\" x=\"62.78125\" y=\"177.851698\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_31\">\n", "     <!-- real -->\n", "     <g transform=\"translate(80.78125 180.476698) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-72\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"38.863281\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"100.386719\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"161.666016\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"PathCollection_4\">\n", "     <g>\n", "      <use xlink:href=\"#md18c3713bf\" x=\"62.78125\" y=\"192.529823\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_32\">\n", "     <!-- generated -->\n", "     <g transform=\"translate(80.78125 195.154823) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-67\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"188.378906\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"249.902344\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"291.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"352.294922\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"391.503906\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"453.027344\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb450bf7cdf\">\n", "   <rect x=\"43.78125\" y=\"7.2\" width=\"279\" height=\"120.521739\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pee3058d77f\">\n", "   <rect x=\"43.78125\" y=\"163.878261\" width=\"279\" height=\"120.521739\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 500x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lr_D, lr_G, latent_dim, num_epochs = 0.05, 0.005, 2, 20\n", "train(net_D, net_G, data_iter, num_epochs, lr_D, lr_G,\n", "      latent_dim, data[:100].detach().numpy())"]}, {"cell_type": "markdown", "id": "b4174981", "metadata": {"origin_pos": 33}, "source": ["## Summary\n", "\n", "* Generative adversarial networks (GANs) composes of two deep networks, the generator and the discriminator.\n", "* The generator generates the image as much closer to the true image as possible to fool the discriminator, via maximizing the cross-entropy loss, *i.e.*, $\\max \\log(D(\\mathbf{x'}))$.\n", "* The discriminator tries to distinguish the generated images from the true images, via minimizing the cross-entropy loss, *i.e.*, $\\min - y \\log D(\\mathbf{x}) - (1-y)\\log(1-D(\\mathbf{x}))$.\n", "\n", "## Exercises\n", "\n", "* Does an equilibrium exist where the generator wins, *i.e.* the discriminator ends up unable to distinguish the two distributions on finite samples?\n"]}, {"cell_type": "markdown", "id": "1b0dea6f", "metadata": {"origin_pos": 35, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1082)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
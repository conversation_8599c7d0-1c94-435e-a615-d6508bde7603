{"cells": [{"cell_type": "markdown", "id": "674999d4", "metadata": {"origin_pos": 1}, "source": ["# Q-Learning\n", ":label:`sec_qlearning`\n", "\n", "In the previous section, we discussed the Value Iteration algorithm which requires accessing the complete Markov decision process (MDP), e.g., the transition and reward functions. In this section, we will look at Q-Learning :cite:`<PERSON>.1992` which is an algorithm to learn the value function without necessarily knowing the MDP. This algorithm embodies the central idea behind reinforcement learning: it will enable the robot to obtain its own data.\n", "<!-- , instead of relying upon the expert. -->\n", "\n", "## The Q-Learning Algorithm\n", "\n", "Recall that value iteration for the action-value function in :ref:`sec_valueiter` corresponds to the update\n", "\n", "$$Q_{k+1}(s, a) = r(s, a) + \\gamma \\sum_{s' \\in \\mathcal{S}} P(s' \\mid s, a) \\max_{a' \\in \\mathcal{A}} Q_k (s', a'); \\ \\textrm{for all } s \\in \\mathcal{S} \\textrm{ and } a \\in \\mathcal{A}.$$\n", "\n", "As we discussed, implementing this algorithm requires knowing the MDP, specifically the transition function $P(s' \\mid s, a)$. The key idea behind Q-Learning is to replace the summation over all $s' \\in \\mathcal{S}$ in the above expression by a summation over the states visited by the robot. This allows us to subvert the need to know the transition function.\n", "\n", "## An Optimization Problem Underlying Q-Learning\n", "\n", "Let us imagine that the robot uses a policy $\\pi_e(a \\mid s)$ to take actions. Just like the previous chapter, it collects a dataset of $n$ trajectories of $T$ timesteps each $\\{ (s_t^i, a_t^i)_{t=0,\\ldots,T-1}\\}_{i=1,\\ldots, n}$. Recall that value iteration is really a set of constraints that ties together the action-value $Q^*(s, a)$ of different states and actions to each other. We can implement an approximate version of value iteration using the data that the robot has collected using $\\pi_e$ as\n", "\n", "$$\\hat{Q} = \\min_Q \\underbrace{\\frac{1}{nT} \\sum_{i=1}^n \\sum_{t=0}^{T-1} (Q(s_t^i, a_t^i) - r(s_t^i, a_t^i) - \\gamma \\max_{a'} Q(s_{t+1}^i, a'))^2}_{\\stackrel{\\textrm{def}}{=} \\ell(Q)}.$$\n", ":eqlabel:`q_learning_optimization_problem`\n", "\n", "Let us first observe the similarities and differences between this expression and value iteration above. If the robot's policy $\\pi_e$ were equal to the optimal policy $\\pi^*$, and if it collected an infinite amount of data, then this optimization problem would be identical to the optimization problem underlying value iteration. But while value iteration requires us to know $P(s' \\mid s, a)$, the optimization objective does not have this term. We have not cheated: as the robot uses the policy $\\pi_e$ to take an action $a_t^i$ at state $s_t^i$, the next state $s_{t+1}^i$ is a sample drawn from the transition function. So the optimization objective also has access to the transition function, but implicitly in terms of the data collected by the robot.\n", "\n", "The variables of our optimization problem are $Q(s, a)$ for all $s \\in \\mathcal{S}$ and $a \\in \\mathcal{A}$. We can minimize the objective using gradient descent. For every pair $(s_t^i, a_t^i)$ in our dataset, we can write\n", "\n", "$$\\begin{aligned}Q(s_t^i, a_t^i) &\\leftarrow Q(s_t^i, a_t^i) - \\alpha \\nabla_{Q(s_t^i,a_t^i)} \\ell(Q) \\\\&=(1 - \\alpha) Q(s_t^i,a_t^i) - \\alpha \\Big( r(s_t^i, a_t^i) + \\gamma \\max_{a'} Q(s_{t+1}^i, a') \\Big),\\end{aligned}$$\n", ":eqlabel:`q_learning`\n", "\n", "where $\\alpha$ is the learning rate. Typically in real problems, when the robot reaches the goal location, the trajectories end. The value of such a terminal state is zero because the robot does not take any further actions beyond this state. We should modify our update to handle such states as\n", "\n", "$$Q(s_t^i, a_t^i) =(1 - \\alpha) Q(s_t^i,a_t^i) - \\alpha \\Big( r(s_t^i, a_t^i) + \\gamma (1 - \\mathbb{1}_{s_{t+1}^i \\textrm{ is terminal}} )\\max_{a'} Q(s_{t+1}^i, a') \\Big).$$\n", "\n", "where $\\mathbb{1}_{s_{t+1}^i \\textrm{ is terminal}}$ is an indicator variable that is one if $s_{t+1}^i$ is a terminal state and zero otherwise. The value of state-action tuples $(s, a)$ that are not a part of the dataset is set to $-\\infty$. This algorithm is known as Q-Learning.\n", "\n", "Given the solution of these updates $\\hat{Q}$, which is an approximation of the optimal value function $Q^*$, we can obtain the optimal deterministic policy corresponding to this value function easily using\n", "\n", "$$\\hat{\\pi}(s) = \\mathrm{argmax}_{a} \\hat{Q}(s, a).$$\n", "\n", "There can be situations when there are multiple deterministic policies that correspond to the same optimal value function; such ties can be broken arbitrarily because they have the same value function.\n", "\n", "## Exploration in Q-Learning\n", "\n", "The policy used by the robot to collect data $\\pi_e$ is critical to ensure that Q-Learning works well. Afterall, we have replaced the expectation over $s'$ using the transition function $P(s' \\mid s, a)$ using the data collected by the robot. If the policy $\\pi_e$ does not reach diverse parts of the state-action space, then it is easy to imagine our estimate $\\hat{Q}$ will be a poor approximation of the optimal $Q^*$. It is also important to note that in such a situation, the estimate of $Q^*$ at *all states* $s \\in \\mathcal{S}$ will be bad, not just the ones visited by $\\pi_e$. This is because the Q-Learning objective (or value iteration) is a constraint that ties together the value of all state-action pairs. It is therefore critical to pick the correct policy $\\pi_e$ to collect data.\n", "\n", "We can mitigate this concern by picking a completely random policy $\\pi_e$ that samples actions uniformly randomly from $\\mathcal{A}$. Such a policy would visit all states, but it will take a large number of trajectories before it does so.\n", "\n", "We thus arrive at the second key idea in Q-Learning, namely exploration. Typical implementations of Q-Learning tie together the current estimate of $Q$ and the policy $\\pi_e$ to set\n", "\n", "$$\\pi_e(a \\mid s) = \\begin{cases}\\mathrm{argmax}_{a'} \\hat{Q}(s, a') & \\textrm{with prob. } 1-\\epsilon \\\\ \\textrm{uniform}(\\mathcal{A}) & \\textrm{with prob. } \\epsilon,\\end{cases}$$\n", ":eqlabel:`epsilon_greedy`\n", "\n", "where $\\epsilon$ is called the \"exploration parameter\" and is chosen by the user. The policy $\\pi_e$ is called an exploration policy. This particular $\\pi_e$ is called an $\\epsilon$-greedy exploration policy because it chooses the optimal action (under the current estimate $\\hat{Q}$) with probability $1-\\epsilon$ but explores randomly with the remainder probability $\\epsilon$. We can also use the so-called softmax exploration policy\n", "\n", "$$\\pi_e(a \\mid s) = \\frac{e^{\\hat{Q}(s, a)/T}}{\\sum_{a'} e^{\\hat{Q}(s, a')/T}};$$\n", "\n", "where the hyper-parameter $T$ is called temperature. A large value of $\\epsilon$ in $\\epsilon$-greedy policy functions similarly to a large value of temperature $T$ for the softmax policy.\n", "\n", "It is important to note that when we pick an exploration that depends upon the current estimate of the action-value function $\\hat{Q}$, we need to resolve the optimization problem periodically. Typical implementations of Q-Learning make one mini-batch update using a few state-action pairs in the collected dataset (typically the ones collected from the previous timestep of the robot) after taking every action using $\\pi_e$.\n", "\n", "## The \"Self-correcting\" Property of Q-Learning\n", "\n", "The dataset collected by the robot during Q-Learning grows with time. Both the exploration policy $\\pi_e$ and the estimate $\\hat{Q}$ evolve as the robot collects more data. This gives us a key insight into why Q-Learning works well. Consider a state $s$: if a particular action $a$ has a large value under the current estimate $\\hat{Q}(s,a)$, then both the $\\epsilon$-greedy and the softmax exploration policies have a larger probability of picking this action. If this action actually is *not* the ideal action, then the future states that arise from this action will have poor rewards. The next update of the Q-Learning objective will therefore reduce the value $\\hat{Q}(s,a)$, which will reduce the probability of picking this action the next time the robot visits state $s$. Bad actions, e.g., ones whose value is overestimated in $\\hat{Q}(s,a)$, are explored by the robot but their value is correct in the next update of the Q-Learning objective. Good actions, e.g., whose value $\\hat{Q}(s, a)$ is large, are explored more often by the robot and thereby reinforced. This property can be used to show that Q-Learning can converge to the optimal policy even if it begins with a random policy $\\pi_e$ :cite:`<PERSON>.1992`.\n", "\n", "This ability to not only collect new data but also collect the right kind of data is the central feature of reinforcement learning algorithms, and this is what distinguishes them from supervised learning. Q-Learning, using deep neural networks (which we will see in the DQN chapeter later), is responsible for the resurgence of reinforcement learning :cite:`mnih2013playing`.\n", "\n", "## Implementation of Q-Learning\n", "\n", "We now show how to implement Q-Learning on FrozenLake from [Open AI Gym](https://gym.openai.com). Note this is the same setup as we consider in :ref:`sec_valueiter` experiment.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "ec8fa6cc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:27.043298Z", "iopub.status.busy": "2023-08-18T19:42:27.042716Z", "iopub.status.idle": "2023-08-18T19:42:30.345982Z", "shell.execute_reply": "2023-08-18T19:42:30.345000Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import random\n", "import numpy as np\n", "from d2l import torch as d2l\n", "\n", "seed = 0  # Random number generator seed\n", "gamma = 0.95  # Discount factor\n", "num_iters = 256  # Number of iterations\n", "alpha   = 0.9  # Learing rate\n", "epsilon = 0.9  # Epsilon in epsilion gready algorithm\n", "random.seed(seed)  # Set the random seed\n", "np.random.seed(seed)\n", "\n", "# Now set up the environment\n", "env_info = d2l.make_env('FrozenLake-v1', seed=seed)"]}, {"cell_type": "markdown", "id": "170e1a9a", "metadata": {"origin_pos": 3}, "source": ["In the FrozenLake environment, the robot moves on a $4 \\times 4$ grid (these are the states) with actions that are \"up\" ($\\uparrow$), \"down\" ($\\rightarrow$), \"left\" ($\\leftarrow$), and \"right\" ($\\rightarrow$). The environment contains a number of holes (H) cells and frozen (F) cells as well as a goal cell (G), all of which are unknown to the robot. To keep the problem simple, we assume the robot has reliable actions, i.e. $P(s' \\mid s, a) = 1$ for all $s \\in \\mathcal{S}, a \\in \\mathcal{A}$. If the robot reaches the goal, the trial ends and the robot receives a reward of $1$ irrespective of the action; the reward at any other state is $0$ for all actions. The objective of the robot is to learn a policy that reaches the goal location (G) from a given start location (S) (this is $s_0$) to maximize the *return*.\n", "\n", "We first implement $\\epsilon$-greedy method as follows:\n"]}, {"cell_type": "code", "execution_count": 2, "id": "1c063932", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:30.350373Z", "iopub.status.busy": "2023-08-18T19:42:30.349697Z", "iopub.status.idle": "2023-08-18T19:42:30.354314Z", "shell.execute_reply": "2023-08-18T19:42:30.353514Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [], "source": ["def e_greedy(env, Q, s, epsilon):\n", "    if random.random() < epsilon:\n", "        return env.action_space.sample()\n", "\n", "    else:\n", "        return np.argmax(Q[s,:])"]}, {"cell_type": "markdown", "id": "0e325946", "metadata": {"origin_pos": 5}, "source": ["We are now ready to implement Q-learning:\n"]}, {"cell_type": "code", "execution_count": 3, "id": "34ffc68d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:30.357751Z", "iopub.status.busy": "2023-08-18T19:42:30.357180Z", "iopub.status.idle": "2023-08-18T19:42:33.084847Z", "shell.execute_reply": "2023-08-18T19:42:33.083917Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1500 with 12 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def q_learning(env_info, gamma, num_iters, alpha, epsilon):\n", "    env_desc = env_info['desc']  # 2D array specifying what each grid item means\n", "    env = env_info['env']  # 2D array specifying what each grid item means\n", "    num_states = env_info['num_states']\n", "    num_actions = env_info['num_actions']\n", "\n", "    Q  = np.zeros((num_states, num_actions))\n", "    V  = np.zeros((num_iters + 1, num_states))\n", "    pi = np.zeros((num_iters + 1, num_states))\n", "\n", "    for k in range(1, num_iters + 1):\n", "        # Reset environment\n", "        state, done = env.reset(), False\n", "        while not done:\n", "            # Select an action for a given state and acts in env based on selected action\n", "            action = e_greedy(env, Q, state, epsilon)\n", "            next_state, reward, done, _ = env.step(action)\n", "\n", "            # Q-update:\n", "            y = reward + gamma * np.max(Q[next_state,:])\n", "            Q[state, action] = Q[state, action] + alpha * (y - Q[state, action])\n", "\n", "            # Move to the next state\n", "            state = next_state\n", "        # Record max value and max action for visualization purpose only\n", "        for s in range(num_states):\n", "            V[k,s]  = np.max(Q[s,:])\n", "            pi[k,s] = np.argmax(Q[s,:])\n", "    d2l.show_Q_function_progress(env_desc, V[:-1], pi[:-1])\n", "\n", "q_learning(env_info=env_info, gamma=gamma, num_iters=num_iters, alpha=alpha, epsilon=epsilon)"]}, {"cell_type": "markdown", "id": "b0e9b93e", "metadata": {"origin_pos": 7}, "source": ["This result shows that Q-learning can find the optimal solution for this problem roughly after 250 iterations. However, when we compare this result with the Value Iteration algorithm's result (see :ref:`subsec_valueitercode`), we can see that the Value Iteration algorithm needs way fewer iterations to find the optimal solution for this problem. This happens because the Value Iteration algorithm has access to the full MDP whereas Q-learning does not.\n", "\n", "\n", "## Summary\n", "Q-learning is one of the most fundamental reinforcement-learning algorithms. It has been at the epicenter of the recent success of reinforcement learning, most notably in learning to play video games :cite:`mnih2013playing`. Implementing Q-learning does not require that we know the Markov decision process (MDP), e.g., the transition and reward functions, completely.\n", "\n", "## Exercises\n", "\n", "1. Try increasing the grid size to $8 \\times 8$. Compared with $4 \\times 4$ grid, how many iterations does it take to find the optimal value function?\n", "1. Run the Q-learning algorithm again with $\\gamma$ (i.e. \"gamma\" in the above code) when it equals to $0$, $0.5$, and $1$ and analyze its results.\n", "1. Run the Q-learning algorithm again with $\\epsilon$ (i.e. \"epsilon\" in the above code) when it equals to $0$, $0.5$, and $1$ and analyze its results.\n"]}, {"cell_type": "markdown", "id": "6e7bd27e", "metadata": {"origin_pos": 8, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/12103)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": ["\"setuptools==66\"", "\"wheel==0.38.4\"", "\"gym==0.21.0\""]}, "nbformat": 4, "nbformat_minor": 5}
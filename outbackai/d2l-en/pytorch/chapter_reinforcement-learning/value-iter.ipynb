{"cells": [{"cell_type": "markdown", "id": "b7ad4b75", "metadata": {"origin_pos": 1}, "source": ["# Value Iteration\n", ":label:`sec_valueiter`\n", "\n", "In this section we will discuss how to pick the best action for the robot at each state to maximize the *return* of the trajectory. We will describe an algorithm called Value Iteration and implement it for a simulated robot that travels over a frozen lake.\n", "\n", "## Stochastic Policy\n", "\n", "A stochastic policy denoted as $\\pi(a \\mid s)$ (policy for short) is a conditional distribution over the actions $a \\in \\mathcal{A}$ given the state $s \\in \\mathcal{S}$, $\\pi(a \\mid s) \\equiv P(a \\mid s)$. As an example, if the robot has four actions $\\mathcal{A}=$ {go left, go down, go right, go up}. The policy at a state $s \\in \\mathcal{S}$ for such a set of actions $\\mathcal{A}$ is a categorical distribution where the probabilities of the four actions could be $[0.4, 0.2, 0.1, 0.3]$; at some other state $s' \\in \\mathcal{S}$ the probabilities $\\pi(a \\mid s')$ of the same four actions could be $[0.1, 0.1, 0.2, 0.6]$. Note that we should have $\\sum_a \\pi(a \\mid s) = 1$ for any state $s$. A deterministic policy is a special case of a stochastic policy in that the distribution $\\pi(a \\mid s)$ only gives non-zero probability to one particular action, e.g., $[1, 0, 0, 0]$ for our example with four actions.\n", "\n", "To make the notation less cumbersome, we will often write $\\pi(s)$ as the conditional distribution instead of $\\pi(a \\mid s)$.\n", "\n", "## Value Function\n", "\n", "Imagine now that the robot starts at a state $s_0$ and at each time instant, it first samples an action from the policy $a_t \\sim \\pi(s_t)$ and takes this action to result in the next state $s_{t+1}$. The trajectory $\\tau = (s_0, a_0, r_0, s_1, a_1, r_1, \\ldots)$, can be different depending upon which particular action $a_t$ is sampled at intermediate instants. We define the average *return* $R(\\tau) = \\sum_{t=0}^\\infty \\gamma^t r(s_t, a_t)$ of all such trajectories\n", "$$V^\\pi(s_0) = E_{a_t \\sim \\pi(s_t)} \\Big[ R(\\tau) \\Big] = E_{a_t \\sim \\pi(s_t)} \\Big[ \\sum_{t=0}^\\infty \\gamma^t r(s_t, a_t) \\Big],$$\n", "\n", "where $s_{t+1} \\sim P(s_{t+1} \\mid s_t, a_t)$ is the next state of the robot and $r(s_t, a_t)$ is the instantaneous reward obtained by taking action $a_t$ in state $s_t$ at time $t$. This is called the \"value function\" for the policy $\\pi$. In simple words, the value of a state $s_0$ for a policy $\\pi$, denoted by $V^\\pi(s_0)$, is the expected $\\gamma$-discounted *return* obtained by the robot if it begins at state $s_0$ and takes actions from the policy $\\pi$ at each time instant.\n", "\n", "We next break down the trajectory into two stages (i) the first stage which corresponds to $s_0 \\to s_1$ upon taking the action $a_0$, and (ii) a second stage which is the trajectory $\\tau' = (s_1, a_1, r_1, \\ldots)$ thereafter. The key idea behind all algorithms in reinforcement learning is that the value of state $s_0$ can be written as the average reward obtained in the first stage and the value function averaged over all possible next states $s_1$. This is quite intuitive and arises from our Markov assumption: the average return from the current state is the sum of the average return from the next state and the average reward of going to the next state. Mathematically, we write the two stages as\n", "\n", "$$V^\\pi(s_0) = r(s_0, a_0) + \\gamma\\ E_{a_0 \\sim \\pi(s_0)} \\Big[ E_{s_1 \\sim P(s_1 \\mid s_0, a_0)} \\Big[ V^\\pi(s_1) \\Big] \\Big].$$\n", ":eqlabel:`eq_dynamic_programming`\n", "\n", "This decomposition is very powerful: it is the foundation of the principle of dynamic programming upon which all reinforcement learning algorithms are based. Notice that the second stage gets two expectations, one over the choices of the action $a_0$ taken in the first stage using the stochastic policy and another over the possible states $s_1$ obtained from the chosen action. We can write :eqref:`eq_dynamic_programming` using the transition probabilities in the Markov decision process (MDP) as\n", "\n", "$$V^\\pi(s) = \\sum_{a \\in \\mathcal{A}} \\pi(a \\mid s) \\Big[ r(s,  a) + \\gamma\\  \\sum_{s' \\in \\mathcal{S}} P(s' \\mid s, a) V^\\pi(s') \\Big];\\ \\textrm{for all } s \\in \\mathcal{S}.$$\n", ":eqlabel:`eq_dynamic_programming_val`\n", "\n", "An important thing to notice here is that the above identity holds for all states $s \\in \\mathcal{S}$ because we can think of any trajectory that begins at that state and break down the trajectory into two stages.\n", "\n", "## Action-Value Function\n", "\n", "In implementations, it is often useful to maintain a quantity called the \"action value\" function which is a closely related quantity to the value function. This is defined to be the average *return* of a trajectory that begins at $s_0$ but when the action of the first stage is fixed to be $a_0$\n", "\n", "$$Q^\\pi(s_0, a_0) = r(s_0, a_0) + E_{a_t \\sim \\pi(s_t)} \\Big[ \\sum_{t=1}^\\infty \\gamma^t r(s_t, a_t) \\Big],$$\n", "\n", "note that the summation inside the expectation is from $t=1,\\ldots, \\infty$ because the reward of the first stage is fixed in this case. We can again break down the trajectory into two parts and write\n", "\n", "$$Q^\\pi(s, a) = r(s, a) + \\gamma \\sum_{s' \\in \\mathcal{S}} P(s' \\mid s, a) \\sum_{a' \\in \\mathcal{A}} \\pi(a' \\mid s')\\ Q^\\pi(s', a');\\ \\textrm{ for all } s \\in \\mathcal{S}, a \\in \\mathcal{A}.$$\n", ":eqlabel:`eq_dynamic_programming_q`\n", "\n", "This version is the analog of :eqref:`eq_dynamic_programming_val` for the action value function.\n", "\n", "## Optimal Stochastic Policy\n", "\n", "Both the value function and the action-value function depend upon the policy that the robot chooses. We will next think of the \"optimal policy\" that achieves the maximal average *return*\n", "$$\\pi^* = \\underset{\\pi}{\\mathrm{argmax}} V^\\pi(s_0).$$\n", "\n", "Of all possible stochastic policies that the robot could have taken, the optimal policy $\\pi^*$  achieves the largest average discounted *return* for trajectories starting from state $s_0$. Let us denote the value function and the action-value function of the optimal policy as $V^* \\equiv V^{\\pi^*}$ and $Q^* \\equiv Q^{\\pi^*}$.\n", "\n", "Let us observe that for a deterministic policy where there is only one action that is possible under the policy at any given state. This gives us\n", "\n", "$$\\pi^*(s) = \\underset{a \\in \\mathcal{A}}{\\mathrm{argmax}} \\Big[ r(s, a) + \\gamma \\sum_{s' \\in \\mathcal{S}} P(s' \\mid s, a)\\ V^*(s') \\Big].$$\n", "\n", "A good mnemonic to remember this is that the optimal action at state $s$ (for a deterministic policy) is the one that maximizes the sum of reward $r(s, a)$ from the first stage and the average *return* of the trajectories starting from the next sate $s'$, averaged over all possible next states $s'$ from the second stage.\n", "\n", "## Principle of Dynamic Programming\n", "\n", "Our developement in the previous section in :eqref:`eq_dynamic_programming` or :eqref:`eq_dynamic_programming_q` can be turned into an algorithm to compute the optimal value function $V^*$ or the action-value function $Q^*$, respectively. Observe that\n", "$$ V^*(s) = \\sum_{a \\in \\mathcal{A}} \\pi^*(a \\mid s) \\Big[ r(s,  a) + \\gamma\\  \\sum_{s' \\in \\mathcal{S}} P(s' \\mid s, a) V^*(s') \\Big];\\ \\textrm{for all } s \\in \\mathcal{S}.$$\n", "\n", "For a deterministic optimal policy $\\pi^*$, since there is only one action that can be taken at state $s$, we can also write \n", "\n", "$$V^*(s) = \\mathrm{argmax}_{a \\in \\mathcal{A}} \\Big\\{ r(s,a) + \\gamma \\sum_{s' \\in \\mathcal{S}} P(s' \\mid s, a) V^*(s') \\Big\\}$$\n", "\n", "for all states $s \\in \\mathcal{S}$. This identity is called the \"principle of dynamic programming\" :cite:`<PERSON>manDPPaper,BellmanDPBook`. It was formulated by <PERSON> in 1950s and we can remember it as \"the remainder of an optimal trajectory is also optimal\".\n", "\n", "## Value Iteration\n", "\n", "We can turn the principle of dynamic programming into an algorithm for finding the optimal value function called value iteration. The key idea behind value iteration is to think of this identity as a set of constraints that tie together $V^*(s)$ at different states $s \\in \\mathcal{S}$. We initialize the value function to some arbitrary values $V_0(s)$ for all states $s \\in \\mathcal{S}$. At the $k^{\\textrm{th}}$ iteration, the Value Iteration algorithm updates the value function as\n", "\n", "$$V_{k+1}(s) = \\max_{a \\in \\mathcal{A}} \\Big\\{ r(s,  a) + \\gamma\\  \\sum_{s' \\in \\mathcal{S}} P(s' \\mid s, a) V_k(s') \\Big\\};\\ \\textrm{for all } s \\in \\mathcal{S}.$$\n", "\n", "It turns out that as $k \\to \\infty$ the value function estimated by the Value Iteration algorithm converges to the optimal value function irrespective of the initialization $V_0$,\n", "$$V^*(s) = \\lim_{k \\to \\infty} V_k(s);\\ \\textrm{for all states } s \\in \\mathcal{S}.$$\n", "\n", "The same Value Iteration algorithm can be equivalently written using the action-value function as\n", "$$Q_{k+1}(s, a) = r(s, a) + \\gamma \\max_{a' \\in \\mathcal{A}} \\sum_{s' \\in \\mathcal{S}} P(s' \\mid s, a) Q_k (s', a');\\ \\textrm{ for all } s \\in \\mathcal{S}, a \\in \\mathcal{A}.$$\n", "\n", "In this case we initialize $Q_0(s, a)$ to some arbitrary values for all $s \\in \\mathcal{S}$ and $a \\in \\mathcal{A}$. Again we have $Q^*(s, a) = \\lim_{k \\to \\infty} Q_k(s, a)$ for all $s \\in \\mathcal{S}$ and $a \\in \\mathcal{A}$.\n", "\n", "## Policy Evaluation\n", "\n", "Value Iteration enables us to compute the optimal value function, i.e., $V^{\\pi^*}$ of the optimal deterministic policy $\\pi^*$. We can also use similar iterative updates to compute the value function associated with any other, potentially stochastic, policy $\\pi$. We again initialize $V^\\pi_0(s)$ to some arbitrary values for all states $s \\in \\mathcal{S}$ and at the $k^{\\textrm{th}}$ iteration, perform the updates\n", "\n", "$$    V^\\pi_{k+1}(s) = \\sum_{a \\in \\mathcal{A}} \\pi(a \\mid s) \\Big[ r(s,  a) + \\gamma\\  \\sum_{s' \\in \\mathcal{S}} P(s' \\mid s, a) V^\\pi_k(s') \\Big];\\ \\textrm{for all } s \\in \\mathcal{S}.$$\n", "\n", "This algorithm is known as policy evaluation and is useful to compute the value function given the policy. Again, it turns out that as $k \\to \\infty$ these updates converge to the correct value function irrespective of the initialization $V_0$,\n", "\n", "$$V^\\pi(s) = \\lim_{k \\to \\infty} V^\\pi_k(s);\\ \\textrm{for all states } s \\in \\mathcal{S}.$$\n", "\n", "The algorithm for computing the action-value function $Q^\\pi(s, a)$ of a policy $\\pi$ is analogous.\n", "\n", "## Implementation of Value Iteration\n", ":label:`subsec_valueitercode`\n", "We next show how to implement Value Iteration for a navigation problem called FrozenLake from [Open AI Gym](https://gym.openai.com). We first need to setup the enviroment as shown in the following code.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "6dee5495", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:17.154507Z", "iopub.status.busy": "2023-08-18T19:28:17.154232Z", "iopub.status.idle": "2023-08-18T19:28:20.056569Z", "shell.execute_reply": "2023-08-18T19:28:20.055430Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import random\n", "import numpy as np\n", "from d2l import torch as d2l\n", "\n", "seed = 0  # Random number generator seed\n", "gamma = 0.95  # Discount factor\n", "num_iters = 10  # Number of iterations\n", "random.seed(seed)  # Set the random seed to ensure results can be reproduced\n", "np.random.seed(seed)\n", "\n", "# Now set up the environment\n", "env_info = d2l.make_env('FrozenLake-v1', seed=seed)"]}, {"cell_type": "markdown", "id": "5c112b04", "metadata": {"origin_pos": 3}, "source": ["In the FrozenLake environment, the robot moves on a $4 \\times 4$ grid (these are the states) with actions that are \"up\" ($\\uparrow$), \"down\" ($\\rightarrow$), \"left\" ($\\leftarrow$), and \"right\" ($\\rightarrow$). The environment contains a number of holes (H) cells and frozen (F) cells as well as a goal cell (G), all of which are unknown to the robot. To keep the problem simple, we assume the robot has reliable actions, i.e. $P(s' \\mid s, a) = 1$ for all $s \\in \\mathcal{S}, a \\in \\mathcal{A}$. If the robot reaches the goal, the trial ends and the robot receives a reward of $1$ irrespective of the action; the reward at any other state is $0$ for all actions. The objective of the robot is to learn a policy that reaches the goal location (G) from a given start location (S) (this is $s_0$) to maximize the *return*.\n", "\n", "The following function implements Value Iteration, where `env_info` contains MDP and environment related information and `gamma` is the discount factor:\n"]}, {"cell_type": "code", "execution_count": 2, "id": "09801b3a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:20.060787Z", "iopub.status.busy": "2023-08-18T19:28:20.060119Z", "iopub.status.idle": "2023-08-18T19:28:22.284244Z", "shell.execute_reply": "2023-08-18T19:28:22.283314Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1500 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def value_iteration(env_info, gamma, num_iters):\n", "    env_desc = env_info['desc']  # 2D array shows what each item means\n", "    prob_idx = env_info['trans_prob_idx']\n", "    nextstate_idx = env_info['nextstate_idx']\n", "    reward_idx = env_info['reward_idx']\n", "    num_states = env_info['num_states']\n", "    num_actions = env_info['num_actions']\n", "    mdp = env_info['mdp']\n", "\n", "    V  = np.zeros((num_iters + 1, num_states))\n", "    Q  = np.zeros((num_iters + 1, num_states, num_actions))\n", "    pi = np.zeros((num_iters + 1, num_states))\n", "\n", "    for k in range(1, num_iters + 1):\n", "        for s in range(num_states):\n", "            for a in range(num_actions):\n", "                # Calculate \\sum_{s'} p(s'\\mid s,a) [r + \\gamma v_k(s')]\n", "                for pxrds in mdp[(s,a)]:\n", "                    # mdp(s,a): [(p1,next1,r1,d1),(p2,next2,r2,d2),..]\n", "                    pr = pxrds[prob_idx]  # p(s'\\mid s,a)\n", "                    nextstate = pxrds[nextstate_idx]  # Next state\n", "                    reward = pxrds[reward_idx]  # Reward\n", "                    Q[k,s,a] += pr * (reward + gamma * V[k - 1, nextstate])\n", "            # Record max value and max action\n", "            V[k,s] = np.max(Q[k,s,:])\n", "            pi[k,s] = np.argmax(Q[k,s,:])\n", "    d2l.show_value_function_progress(env_desc, V[:-1], pi[:-1])\n", "\n", "value_iteration(env_info=env_info, gamma=gamma, num_iters=num_iters)"]}, {"cell_type": "markdown", "id": "d66b47e1", "metadata": {"origin_pos": 5}, "source": ["The above pictures show the policy (the arrow indicates the action) and value function (the change in color shows how the value function changes over time from the initial value shown by dark color to the optimal value shown by light colors.). As we see, Value Iteration finds the optimal value function after 10 iterations and the goal state (G) can be reached starting from any state as long as it is not an H cell. Another interesting aspect of the implementation is that in addition to finding the optimal value function, we also automatically found the optimal policy $\\pi^*$ corresponding to this value function.\n", "\n", "\n", "## Summary\n", "The main idea behind the Value Iteration algorithm is to use the principle of dynamic programming to find the optimal average return obtained from a given state. Note that implementing the Value Iteration algorithm requires that we know the Markov decision process (MDP), e.g., the transition and reward functions, completely.\n", "\n", "\n", "## Exercises\n", "\n", "1. Try increasing the grid size to $8 \\times 8$. Compared with $4 \\times 4$ grid, how many iterations does it take to find the optimal value function?\n", "1. What is the computational complexity of the Value Iteration algorithm?\n", "1. Run the Value Iteration algorithm again with $\\gamma$ (i.e. \"gamma\" in the above code) when it equals to $0$, $0.5$, and $1$ and analyze its results. \n", "1. How does the value of $\\gamma$ affect the number of iterations taken by Value Iteration to converge? What happens when $\\gamma=1$?\n"]}, {"cell_type": "markdown", "id": "df56d18c", "metadata": {"origin_pos": 6, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/12005)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": ["\"setuptools==66\"", "\"wheel==0.38.4\"", "\"gym==0.21.0\""]}, "nbformat": 4, "nbformat_minor": 5}
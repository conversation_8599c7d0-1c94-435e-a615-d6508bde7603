{"cells": [{"cell_type": "markdown", "id": "c8502f85", "metadata": {"origin_pos": 1}, "source": ["# Batch Normalization\n", ":label:`sec_batch_norm`\n", "\n", "Training deep neural networks is difficult.\n", "Getting them to converge in a reasonable amount of time can be tricky.\n", "In this section, we describe *batch normalization*, a popular and effective technique\n", "that consistently accelerates the convergence of deep networks :cite:`Ioffe.Szegedy.2015`.\n", "Together with residual blocks---covered later in :numref:`sec_resnet`---batch normalization\n", "has made it possible for practitioners to routinely train networks with over 100 layers.\n", "A secondary (serendipitous) benefit of batch normalization lies in its inherent regularization.\n"]}, {"cell_type": "code", "id": "f7b44765", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:58:59.890305Z", "iopub.status.busy": "2023-08-18T19:58:59.889455Z", "iopub.status.idle": "2023-08-18T19:59:03.088529Z", "shell.execute_reply": "2023-08-18T19:59:03.087539Z"}, "origin_pos": 3, "tab": ["pytorch"], "ExecuteTime": {"end_time": "2025-03-29T12:39:34.973833Z", "start_time": "2025-03-29T12:39:32.269839Z"}}, "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"], "outputs": [], "execution_count": 1}, {"cell_type": "markdown", "id": "85aa6aae", "metadata": {"origin_pos": 6}, "source": ["## Training Deep Networks\n", "\n", "When working with data, we often preprocess before training.\n", "Choices regarding data preprocessing often make an enormous difference in the final results.\n", "Recall our application of MLPs to predicting house prices (:numref:`sec_kaggle_house`).\n", "Our first step when working with real data\n", "was to standardize our input features to have\n", "zero mean $\\boldsymbol{\\mu} = 0$ and unit variance $\\boldsymbol{\\Sigma} = \\boldsymbol{1}$ across multiple observations :cite:`friedman1987exploratory`, frequently rescaling the latter so  that the diagonal is unity, i.e., $\\Sigma_{ii} = 1$.\n", "Yet another strategy is to rescale vectors to unit length, possibly zero mean *per observation*.\n", "This can work well, e.g., for spatial sensor data. These preprocessing techniques and many others, are\n", "beneficial for keeping the estimation problem well controlled. \n", "For a review of feature selection and extraction see the article of :citet:`guyon2008feature`, for example.\n", "Standardizing vectors also has the nice side-effect of constraining the function complexity of functions that act upon it. For instance, the celebrated radius-margin bound :cite:`Vapnik95` in support vector machines and the Perceptron Convergence Theorem :cite:`Novikoff62` rely on inputs of bounded norm. \n", "\n", "Intuitively, this standardization plays nicely with our optimizers\n", "since it puts the parameters *a priori* on a similar scale.\n", "As such, it is only natural to ask whether a corresponding normalization step *inside* a deep network\n", "might not be beneficial. While this is not quite the reasoning that led to the invention of batch normalization :cite:`Ioffe.Szegedy.2015`, it is a useful way of understanding it and its cousin, layer normalization :cite:`Ba.Kiros.Hinton.2016`, within a unified framework.\n", "\n", "Second, for a typical MLP or CNN, as we train,\n", "the variables \n", "in intermediate layers (e.g., affine transformation outputs in MLP)\n", "may take values with widely varying magnitudes:\n", "whether along the layers from input to output, across units in the same layer,\n", "and over time due to our updates to the model parameters.\n", "The inventors of batch normalization postulated informally\n", "that this drift in the distribution of such variables could hamper the convergence of the network.\n", "Intuitively, we might conjecture that if one\n", "layer has variable activations that are 100 times that of another layer,\n", "this might necessitate compensatory adjustments in the learning rates. Adaptive solvers\n", "such as AdaGrad :cite:<PERSON><PERSON><PERSON><PERSON><PERSON>.Singer.2011`, <PERSON> :cite:`Kingma.Ba.2014`, <PERSON>gi :cite:<PERSON><PERSON><PERSON><PERSON>.Reddi.Sachan.ea.2018`, or Distributed Shampoo :cite:`anil2020scalable` aim to address this from the viewpoint of optimization, e.g., by adding aspects of second-order methods. \n", "The alternative is to prevent the problem from occurring, simply by adaptive normalization.\n", "\n", "Third, deeper networks are complex and tend to be more liable to overfitting.\n", "This means that regularization becomes more critical. A common technique for regularization is noise\n", "injection. This has been known for a long time, e.g., with regard to noise injection for the\n", "inputs :cite:`<PERSON>.1995`. It also forms the basis of dropout in :numref:`sec_dropout`. As it turns out, quite serendipitously, batch normalization conveys all three benefits: preprocessing, numerical stability, and regularization.\n", "\n", "Batch normalization is applied to individual layers, or optionally, to all of them:\n", "In each training iteration,\n", "we first normalize the inputs (of batch normalization)\n", "by subtracting their mean and\n", "dividing by their standard deviation,\n", "where both are estimated based on the statistics of the current minibatch.\n", "Next, we apply a scale coefficient and an offset to recover the lost degrees\n", "of freedom. It is precisely due to this *normalization* based on *batch* statistics\n", "that *batch normalization* derives its name.\n", "\n", "Note that if we tried to apply batch normalization with minibatches of size 1,\n", "we would not be able to learn anything.\n", "That is because after subtracting the means,\n", "each hidden unit would take value 0.\n", "As you might guess, since we are devoting a whole section to batch normalization,\n", "with large enough minibatches the approach proves effective and stable.\n", "One takeaway here is that when applying batch normalization,\n", "the choice of batch size is\n", "even more significant than without batch normalization, or at least,\n", "suitable calibration is needed as we might adjust batch size.\n", "\n", "Denote by $\\mathcal{B}$ a minibatch and let $\\mathbf{x} \\in \\mathcal{B}$ be an input to \n", "batch normalization ($\\textrm{BN}$). In this case the batch normalization is defined as follows:\n", "\n", "$$\\textrm{BN}(\\mathbf{x}) = \\boldsymbol{\\gamma} \\odot \\frac{\\mathbf{x} - \\hat{\\boldsymbol{\\mu}}_\\mathcal{B}}{\\hat{\\boldsymbol{\\sigma}}_\\mathcal{B}} + \\boldsymbol{\\beta}.$$\n", ":eqlabel:`eq_batchnorm`\n", "\n", "In :eqref:`eq_batchnorm`,\n", "$\\hat{\\boldsymbol{\\mu}}_\\mathcal{B}$ is the  sample mean\n", "and $\\hat{\\boldsymbol{\\sigma}}_\\mathcal{B}$ is the sample standard deviation of the minibatch $\\mathcal{B}$.\n", "After applying standardization,\n", "the resulting minibatch\n", "has zero mean and unit variance.\n", "The choice of unit variance\n", "(rather than some other magic number) is arbitrary. We recover this degree of freedom\n", "by including an elementwise\n", "*scale parameter* $\\boldsymbol{\\gamma}$ and *shift parameter* $\\boldsymbol{\\beta}$\n", "that have the same shape as $\\mathbf{x}$. Both are parameters that\n", "need to be learned as part of model training.\n", "\n", "The variable magnitudes\n", "for intermediate layers cannot diverge during training\n", "since batch normalization actively centers and rescales them back\n", "to a given mean and size (via $\\hat{\\boldsymbol{\\mu}}_\\mathcal{B}$ and ${\\hat{\\boldsymbol{\\sigma}}_\\mathcal{B}}$).\n", "Practical experience confirms that, as alluded to when discussing feature rescaling, batch normalization seems to allow for more aggressive learning rates.\n", "We calculate $\\hat{\\boldsymbol{\\mu}}_\\mathcal{B}$ and ${\\hat{\\boldsymbol{\\sigma}}_\\mathcal{B}}$ in :eqref:`eq_batchnorm` as follows:\n", "\n", "$$\\hat{\\boldsymbol{\\mu}}_\\mathcal{B} = \\frac{1}{|\\mathcal{B}|} \\sum_{\\mathbf{x} \\in \\mathcal{B}} \\mathbf{x}\n", "\\textrm{ and }\n", "\\hat{\\boldsymbol{\\sigma}}_\\mathcal{B}^2 = \\frac{1}{|\\mathcal{B}|} \\sum_{\\mathbf{x} \\in \\mathcal{B}} (\\mathbf{x} - \\hat{\\boldsymbol{\\mu}}_{\\mathcal{B}})^2 + \\epsilon.$$\n", "\n", "Note that we add a small constant $\\epsilon > 0$\n", "to the variance estimate\n", "to ensure that we never attempt division by zero,\n", "even in cases where the empirical variance estimate might be very small or vanish.\n", "The estimates $\\hat{\\boldsymbol{\\mu}}_\\mathcal{B}$ and ${\\hat{\\boldsymbol{\\sigma}}_\\mathcal{B}}$ counteract the scaling issue\n", "by using noisy estimates of mean and variance.\n", "You might think that this noisiness should be a problem.\n", "On the contrary, it is actually beneficial.\n", "\n", "This turns out to be a recurring theme in deep learning.\n", "For reasons that are not yet well-characterized theoretically,\n", "various sources of noise in optimization\n", "often lead to faster training and less overfitting:\n", "this variation appears to act as a form of regularization.\n", ":citet:<PERSON><PERSON><PERSON><PERSON>Aziz<PERSON>ur.Smith.2018` and :citet:`<PERSON><PERSON><PERSON>Wang.Shao.ea.2018`\n", "related the properties of batch normalization to Bayesian priors and penalties, respectively. \n", "In particular, this sheds some light on the puzzle\n", "of why batch normalization works best for moderate minibatch sizes in the 50--100 range.\n", "This particular size of minibatch seems to inject just the \"right amount\" of noise per layer, both in terms of scale via $\\hat{\\boldsymbol{\\sigma}}$, and in terms of offset via $\\hat{\\boldsymbol{\\mu}}$: a\n", "larger minibatch regularizes less due to the more stable estimates, whereas tiny minibatches\n", "destroy useful signal due to high variance. Exploring this direction further, considering alternative types\n", "of preprocessing and filtering may yet lead to other effective types of regularization.\n", "\n", "Fixing a trained model, you might think\n", "that we would prefer using the entire dataset\n", "to estimate the mean and variance.\n", "Once training is complete, why would we want\n", "the same image to be classified differently,\n", "depending on the batch in which it happens to reside?\n", "During training, such exact calculation is infeasible\n", "because the intermediate variables\n", "for all data examples\n", "change every time we update our model.\n", "However, once the model is trained,\n", "we can calculate the means and variances\n", "of each layer's variables based on the entire dataset.\n", "Indeed this is standard practice for\n", "models employing batch normalization;\n", "thus batch normalization layers function differently\n", "in *training mode* (normalizing by minibatch statistics)\n", "than in *prediction mode* (normalizing by dataset statistics).\n", "In this form they closely resemble the behavior of dropout regularization of :numref:`sec_dropout`,\n", "where noise is only injected during training.\n", "\n", "\n", "## Batch Normalization Layers\n", "\n", "Batch normalization implementations for fully connected layers\n", "and convolutional layers are slightly different.\n", "One key difference between batch normalization and other layers\n", "is that because the former operates on a full minibatch at a time,\n", "we cannot just ignore the batch dimension\n", "as we did before when introducing other layers.\n", "\n", "### Fully Connected Layers\n", "\n", "When applying batch normalization to fully connected layers,\n", ":citet:`Ioffe.Szegedy.2015`, in their original paper inserted batch normalization after the affine transformation\n", "and *before* the nonlinear activation function. Later applications experimented with\n", "inserting batch normalization right *after* activation functions.\n", "Denoting the input to the fully connected layer by $\\mathbf{x}$,\n", "the affine transformation\n", "by $\\mathbf{W}\\mathbf{x} + \\mathbf{b}$ (with the weight parameter $\\mathbf{W}$ and the bias parameter $\\mathbf{b}$),\n", "and the activation function by $\\phi$,\n", "we can express the computation of a batch-normalization-enabled,\n", "fully connected layer output $\\mathbf{h}$ as follows:\n", "\n", "$$\\mathbf{h} = \\phi(\\textrm{BN}(\\mathbf{W}\\mathbf{x} + \\mathbf{b}) ).$$\n", "\n", "Recall that mean and variance are computed\n", "on the *same* minibatch\n", "on which the transformation is applied.\n", "\n", "### Convolutional Layers\n", "\n", "Similarly, with convolutional layers,\n", "we can apply batch normalization after the convolution\n", "but before the nonlinear activation function. The key difference from batch normalization\n", "in fully connected layers is that we apply the operation on a per-channel basis\n", "*across all locations*. This is compatible with our assumption of translation\n", "invariance that led to convolutions: we assumed that the specific location of a pattern\n", "within an image was not critical for the purpose of understanding.\n", "\n", "Assume that our minibatches contain $m$ examples\n", "and that for each channel,\n", "the output of the convolution has height $p$ and width $q$.\n", "For convolutional layers, we carry out each batch normalization\n", "over the $m \\cdot p \\cdot q$ elements per output channel simultaneously.\n", "Thus, we collect the values over all spatial locations\n", "when computing the mean and variance\n", "and consequently\n", "apply the same mean and variance\n", "within a given channel\n", "to normalize the value at each spatial location.\n", "Each channel has its own scale and shift parameters,\n", "both of which are scalars.\n", "\n", "### Layer Normalization\n", ":label:`subsec_layer-normalization-in-bn`\n", "\n", "Note that in the context of convolutions the batch normalization is well defined even for\n", "minibatches of size 1: after all, we have all the locations across an image to average. Consequently,\n", "mean and variance are well defined, even if it is just within a single observation. This consideration\n", "led :citet:`<PERSON><PERSON>Hinton.2016` to introduce the notion of *layer normalization*. It works just like\n", "a batch norm, only that it is applied to one observation at a time. Consequently both the offset and the scaling factor are scalars. For an $n$-dimensional vector $\\mathbf{x}$, layer norms are given by \n", "\n", "$$\\mathbf{x} \\rightarrow \\textrm{LN}(\\mathbf{x}) =  \\frac{\\mathbf{x} - \\hat{\\mu}}{\\hat\\sigma},$$\n", "\n", "where scaling and offset are applied coefficient-wise\n", "and given by \n", "\n", "$$\\hat{\\mu} \\stackrel{\\textrm{def}}{=} \\frac{1}{n} \\sum_{i=1}^n x_i \\textrm{ and }\n", "\\hat{\\sigma}^2 \\stackrel{\\textrm{def}}{=} \\frac{1}{n} \\sum_{i=1}^n (x_i - \\hat{\\mu})^2 + \\epsilon.$$\n", "\n", "As before we add a small offset $\\epsilon > 0$ to prevent division by zero. One of the major benefits of using layer normalization is that it prevents divergence. After all, ignoring $\\epsilon$, the output of the layer normalization is scale independent. That is, we have $\\textrm{LN}(\\mathbf{x}) \\approx \\textrm{LN}(\\alpha \\mathbf{x})$ for any choice of $\\alpha \\neq 0$. This becomes an equality for $|\\alpha| \\to \\infty$ (the approximate equality is due to the offset $\\epsilon$ for the variance). \n", "\n", "Another advantage of the layer normalization is that it does not depend on the minibatch size. It is also independent of whether we are in training or test regime. In other words, it is simply a deterministic transformation that standardizes the activations to a given scale. This can be very beneficial in preventing divergence in optimization. We skip further details and recommend that interested readers consult the original paper.\n", "\n", "### Batch Normalization During Prediction\n", "\n", "As we mentioned earlier, batch normalization typically behaves differently\n", "in training mode than in prediction mode.\n", "First, the noise in the sample mean and the sample variance\n", "arising from estimating each on minibatches\n", "is no longer desirable once we have trained the model.\n", "Second, we might not have the luxury\n", "of computing per-batch normalization statistics.\n", "For example,\n", "we might need to apply our model to make one prediction at a time.\n", "\n", "Typically, after training, we use the entire dataset\n", "to compute stable estimates of the variable statistics\n", "and then fix them at prediction time.\n", "Hence, batch normalization behaves differently during training than at test time.\n", "Recall that dropout also exhibits this characteristic.\n", "\n", "## (**Implementation from Scratch**)\n", "\n", "To see how batch normalization works in practice, we implement one from scratch below.\n"]}, {"cell_type": "code", "id": "9a79b8f2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:59:03.092523Z", "iopub.status.busy": "2023-08-18T19:59:03.092120Z", "iopub.status.idle": "2023-08-18T19:59:03.100348Z", "shell.execute_reply": "2023-08-18T19:59:03.099493Z"}, "origin_pos": 8, "tab": ["pytorch"], "ExecuteTime": {"end_time": "2025-03-29T12:39:34.999916Z", "start_time": "2025-03-29T12:39:34.996331Z"}}, "source": ["def batch_norm(X, gamma, beta, moving_mean, moving_var, eps, momentum):\n", "    # Use is_grad_enabled to determine whether we are in training mode\n", "    if not torch.is_grad_enabled():\n", "        # In prediction mode, use mean and variance obtained by moving average\n", "        X_hat = (X - moving_mean) / torch.sqrt(moving_var + eps)\n", "    else:\n", "        assert len(X.shape) in (2, 4)\n", "        if len(X.shape) == 2:\n", "            # When using a fully connected layer, calculate the mean and\n", "            # variance on the feature dimension\n", "            mean = X.mean(dim=0)\n", "            var = ((X - mean) ** 2).mean(dim=0)\n", "        else:\n", "            # When using a two-dimensional convolutional layer, calculate the\n", "            # mean and variance on the channel dimension (axis=1). Here we\n", "            # need to maintain the shape of X, so that the broadcasting\n", "            # operation can be carried out later\n", "            mean = X.mean(dim=(0, 2, 3), keepdim=True)\n", "            var = ((X - mean) ** 2).mean(dim=(0, 2, 3), keepdim=True)\n", "        # In training mode, the current mean and variance are used\n", "        X_hat = (X - mean) / torch.sqrt(var + eps)\n", "        # Update the mean and variance using moving average\n", "        moving_mean = (1.0 - momentum) * moving_mean + momentum * mean\n", "        moving_var = (1.0 - momentum) * moving_var + momentum * var\n", "    Y = gamma * X_hat + beta  # Scale and shift\n", "    return Y, moving_mean.data, moving_var.data"], "outputs": [], "execution_count": 2}, {"cell_type": "markdown", "id": "49370dea", "metadata": {"origin_pos": 11}, "source": ["We can now [**create a proper `BatchNorm` layer.**]\n", "Our layer will maintain proper parameters\n", "for scale `gamma` and shift `beta`,\n", "both of which will be updated in the course of training.\n", "Additionally, our layer will maintain\n", "moving averages of the means and variances\n", "for subsequent use during model prediction.\n", "\n", "Putting aside the algorithmic details,\n", "note the design pattern underlying our implementation of the layer.\n", "Typically, we define the mathematics in a separate function, say `batch_norm`.\n", "We then integrate this functionality into a custom layer,\n", "whose code mostly addresses bookkeeping matters,\n", "such as moving data to the right device context,\n", "allocating and initializing any required variables,\n", "keeping track of moving averages (here for mean and variance), and so on.\n", "This pattern enables a clean separation of mathematics from boilerplate code.\n", "Also note that for the sake of convenience\n", "we did not worry about automatically inferring the input shape here;\n", "thus we need to specify the number of features throughout.\n", "By now all modern deep learning frameworks offer automatic detection of size and shape in the\n", "high-level batch normalization APIs (in practice we will use this instead).\n"]}, {"cell_type": "code", "id": "8a591dd1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:59:03.103959Z", "iopub.status.busy": "2023-08-18T19:59:03.103597Z", "iopub.status.idle": "2023-08-18T19:59:03.113624Z", "shell.execute_reply": "2023-08-18T19:59:03.112645Z"}, "origin_pos": 13, "tab": ["pytorch"], "ExecuteTime": {"end_time": "2025-03-29T12:39:35.060852Z", "start_time": "2025-03-29T12:39:35.057501Z"}}, "source": ["class BatchNorm(nn.Module):\n", "    # num_features: the number of outputs for a fully connected layer or the\n", "    # number of output channels for a convolutional layer. num_dims: 2 for a\n", "    # fully connected layer and 4 for a convolutional layer\n", "    def __init__(self, num_features, num_dims):\n", "        super().__init__()\n", "        if num_dims == 2:\n", "            shape = (1, num_features)\n", "        else:\n", "            shape = (1, num_features, 1, 1)\n", "        # The scale parameter and the shift parameter (model parameters) are\n", "        # initialized to 1 and 0, respectively\n", "        self.gamma = nn.Parameter(torch.ones(shape))\n", "        self.beta = nn.Parameter(torch.zeros(shape))\n", "        # The variables that are not model parameters are initialized to 0 and\n", "        # 1\n", "        self.moving_mean = torch.zeros(shape)\n", "        self.moving_var = torch.ones(shape)\n", "\n", "    def forward(self, X):\n", "        # If X is not on the main memory, copy moving_mean and moving_var to\n", "        # the device where <PERSON> is located\n", "        if self.moving_mean.device != X.device:\n", "            self.moving_mean = self.moving_mean.to(X.device)\n", "            self.moving_var = self.moving_var.to(X.device)\n", "        # Save the updated moving_mean and moving_var\n", "        Y, self.moving_mean, self.moving_var = batch_norm(\n", "            X, self.gamma, self.beta, self.moving_mean,\n", "            self.moving_var, eps=1e-5, momentum=0.1)\n", "        return Y"], "outputs": [], "execution_count": 3}, {"cell_type": "markdown", "id": "7e8bc89f", "metadata": {"origin_pos": 16}, "source": ["We used `momentum` to govern the aggregation over past mean and variance estimates. This is somewhat of a misnomer as it has nothing whatsoever to do with the *momentum* term of optimization. Nonetheless, it is the commonly adopted name for this term and in deference to API naming convention we use the same variable name in our code.\n", "\n", "## [**LeNet with Batch Normalization**]\n", "\n", "To see how to apply `BatchNorm` in context,\n", "below we apply it to a traditional LeNet model (:numref:`sec_lenet`).\n", "Recall that batch normalization is applied\n", "after the convolutional layers or fully connected layers\n", "but before the corresponding activation functions.\n"]}, {"cell_type": "code", "id": "21c51c36", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:59:03.118112Z", "iopub.status.busy": "2023-08-18T19:59:03.117737Z", "iopub.status.idle": "2023-08-18T19:59:03.124711Z", "shell.execute_reply": "2023-08-18T19:59:03.123881Z"}, "origin_pos": 17, "tab": ["pytorch"], "ExecuteTime": {"end_time": "2025-03-29T12:39:35.075163Z", "start_time": "2025-03-29T12:39:35.072181Z"}}, "source": ["class BNLeNetScratch(d2l.Classifier):\n", "    def __init__(self, lr=0.1, num_classes=10):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        self.net = nn.Sequential(\n", "            nn.LazyConv2d(6, kernel_size=5), BatchNorm(6, num_dims=4),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>(), nn.AvgPool2d(kernel_size=2, stride=2),\n", "            nn.LazyConv2d(16, kernel_size=5), BatchNorm(16, num_dims=4),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>(), nn.AvgPool2d(kernel_size=2, stride=2),\n", "            nn.<PERSON><PERSON>(), nn.<PERSON><PERSON><PERSON>(120),\n", "            <PERSON><PERSON><PERSON>orm(120, num_dims=2), nn.<PERSON><PERSON><PERSON><PERSON>(), nn.<PERSON><PERSON><PERSON><PERSON><PERSON>(84),\n", "            BatchNorm(84, num_dims=2), nn.<PERSON><PERSON><PERSON><PERSON>(),\n", "            nn.<PERSON>zy<PERSON>inear(num_classes))"], "outputs": [], "execution_count": 4}, {"cell_type": "markdown", "id": "e842386f", "metadata": {"origin_pos": 21}, "source": ["As before, we will [**train our network on the Fashion-MNIST dataset**].\n", "This code is virtually identical to that when we first trained LeNet.\n"]}, {"cell_type": "code", "id": "064cdd64", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:59:03.127886Z", "iopub.status.busy": "2023-08-18T19:59:03.127595Z", "iopub.status.idle": "2023-08-18T20:00:16.870229Z", "shell.execute_reply": "2023-08-18T20:00:16.869283Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "source": ["trainer = d2l.Trainer(max_epochs=10, num_gpus=1)\n", "data = d2l.FashionMNIST(batch_size=128)\n", "model = BNLeNetScratch(lr=0.1)\n", "model.apply_init([next(iter(data.get_dataloader(True)))[0]], d2l.init_cnn)\n", "trainer.fit(model, data)"], "execution_count": 5, "outputs": []}, {"cell_type": "markdown", "id": "27eb3037", "metadata": {"origin_pos": 24}, "source": ["Let's [**have a look at the scale parameter `gamma`\n", "and the shift parameter `beta`**] learned\n", "from the first batch normalization layer.\n"]}, {"cell_type": "code", "id": "4969fdc2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:00:16.875213Z", "iopub.status.busy": "2023-08-18T20:00:16.874610Z", "iopub.status.idle": "2023-08-18T20:00:16.971921Z", "shell.execute_reply": "2023-08-18T20:00:16.970745Z"}, "origin_pos": 26, "tab": ["pytorch"], "ExecuteTime": {"end_time": "2025-03-29T12:41:06.551833Z", "start_time": "2025-03-29T12:41:06.478017Z"}}, "source": ["model.net[1].gamma.reshape((-1,)), model.net[1].beta.reshape((-1,))"], "outputs": [{"data": {"text/plain": ["(tensor([1.6737, 1.5783, 2.0114, 2.3043, 1.6973, 1.8389], device='mps:0',\n", "        grad_fn=<ViewBackward0>),\n", " tensor([-1.1571, -1.4371,  1.3936, -1.0429, -0.8580,  0.3876], device='mps:0',\n", "        grad_fn=<ViewBackward0>))"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "execution_count": 6}, {"cell_type": "markdown", "id": "6befcbce", "metadata": {"origin_pos": 29}, "source": ["## [**Concise Implementation**]\n", "\n", "Compared with the `BatchNorm` class,\n", "which we just defined ourselves,\n", "we can use the `BatchNorm` class defined in high-level APIs from the deep learning framework directly.\n", "The code looks virtually identical\n", "to our implementation above, except that we no longer need to provide additional arguments for it to get the dimensions right.\n"]}, {"cell_type": "code", "id": "ef2ab147", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:00:16.975625Z", "iopub.status.busy": "2023-08-18T20:00:16.975018Z", "iopub.status.idle": "2023-08-18T20:00:16.981373Z", "shell.execute_reply": "2023-08-18T20:00:16.980550Z"}, "origin_pos": 30, "tab": ["pytorch"], "ExecuteTime": {"end_time": "2025-03-29T12:41:06.566806Z", "start_time": "2025-03-29T12:41:06.563751Z"}}, "source": ["class BNLeNet(d2l.Classifier):\n", "    def __init__(self, lr=0.1, num_classes=10):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        self.net = nn.Sequential(\n", "            nn.LazyConv2d(6, kernel_size=5), nn.LazyBatchNorm2d(),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>(), nn.AvgPool2d(kernel_size=2, stride=2),\n", "            nn.LazyConv2d(16, kernel_size=5), nn.LazyBatchNorm2d(),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>(), nn.AvgPool2d(kernel_size=2, stride=2),\n", "            nn.<PERSON><PERSON>(), nn.<PERSON><PERSON><PERSON><PERSON>(120), nn.<PERSON><PERSON><PERSON>atchNorm1d(),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>(), nn.<PERSON><PERSON><PERSON><PERSON>(84), nn.<PERSON><PERSON><PERSON>atchNorm1d(),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>(), nn.<PERSON><PERSON>(num_classes))"], "outputs": [], "execution_count": 7}, {"cell_type": "markdown", "id": "75bb4f92", "metadata": {"origin_pos": 32}, "source": ["Below, we [**use the same hyperparameters to train our model.**]\n", "Note that as usual, the high-level API variant runs much faster\n", "because its code has been compiled to C++ or CUDA\n", "while our custom implementation must be interpreted by Python.\n"]}, {"cell_type": "code", "id": "0d6aaf49", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:00:16.984898Z", "iopub.status.busy": "2023-08-18T20:00:16.984364Z", "iopub.status.idle": "2023-08-18T20:01:21.082406Z", "shell.execute_reply": "2023-08-18T20:01:21.081474Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "source": ["trainer = d2l.Trainer(max_epochs=10, num_gpus=1)\n", "data = d2l.FashionMNIST(batch_size=128)\n", "model = BNLeNet(lr=0.1)\n", "model.apply_init([next(iter(data.get_dataloader(True)))[0]], d2l.init_cnn)\n", "trainer.fit(model, data)"], "execution_count": 8, "outputs": []}, {"cell_type": "markdown", "id": "353f1805", "metadata": {"origin_pos": 35}, "source": ["## Discussion\n", "\n", "Intuitively, batch normalization is thought\n", "to make the optimization landscape smoother.\n", "However, we must be careful to distinguish between\n", "speculative intuitions and true explanations\n", "for the phenomena that we observe when training deep models.\n", "Recall that we do not even know why simpler\n", "deep neural networks (MLPs and conventional CNNs)\n", "generalize well in the first place.\n", "Even with dropout and weight decay,\n", "they remain so flexible that their ability to generalize to unseen data\n", "likely needs significantly more refined learning-theoretic generalization guarantees.\n", "\n", "The original paper proposing batch normalization :cite:`Ioffe.Szegedy.2015`, in addition to introducing a powerful and useful tool,\n", "offered an explanation for why it works:\n", "by reducing *internal covariate shift*.\n", "Presumably by *internal covariate shift* they\n", "meant something like the intuition expressed above---the\n", "notion that the distribution of variable values changes\n", "over the course of training.\n", "However, there were two problems with this explanation:\n", "i) This drift is very different from *covariate shift*,\n", "rendering the name a misnomer. If anything, it is closer to concept drift. \n", "ii) The explanation offers an under-specified intuition\n", "but leaves the question of *why precisely this technique works*\n", "an open question wanting for a rigorous explanation.\n", "Throughout this book, we aim to convey the intuitions that practitioners\n", "use to guide their development of deep neural networks.\n", "However, we believe that it is important\n", "to separate these guiding intuitions\n", "from established scientific fact.\n", "Eventually, when you master this material\n", "and start writing your own research papers\n", "you will want to be clear to delineate\n", "between technical claims and hunches.\n", "\n", "Following the success of batch normalization,\n", "its explanation in terms of *internal covariate shift*\n", "has repeatedly surfaced in debates in the technical literature\n", "and broader discourse about how to present machine learning research.\n", "In a memorable speech given while accepting a Test of Time Award\n", "at the 2017 NeurIPS conference,\n", "<PERSON> used *internal covariate shift*\n", "as a focal point in an argument likening\n", "the modern practice of deep learning to alchemy.\n", "Subsequently, the example was revisited in detail\n", "in a position paper outlining\n", "troubling trends in machine learning :cite:`Lipton.Steinhardt.2018`.\n", "Other authors\n", "have proposed alternative explanations for the success of batch normalization,\n", "some :cite:`Santurkar.Tsipras.Ilyas.ea.2018`\n", "claiming that batch normalization's success comes despite exhibiting behavior\n", "that is in some ways opposite to those claimed in the original paper.\n", "\n", "\n", "We note that the *internal covariate shift*\n", "is no more worthy of criticism than any of\n", "thousands of similarly vague claims\n", "made every year in the technical machine learning literature.\n", "Likely, its resonance as a focal point of these debates\n", "owes to its broad recognizability for the target audience.\n", "Batch normalization has proven an indispensable method,\n", "applied in nearly all deployed image classifiers,\n", "earning the paper that introduced the technique\n", "tens of thousands of citations. We conjecture, though, that the guiding principles\n", "of regularization through noise injection, acceleration through rescaling and lastly preprocessing\n", "may well lead to further inventions of layers and techniques in the future.\n", "\n", "On a more practical note, there are a number of aspects worth remembering about batch normalization:\n", "\n", "* During model training, batch normalization continuously adjusts the intermediate output of\n", "  the network by utilizing the mean and standard deviation of the minibatch, so that the\n", "  values of the intermediate output in each layer throughout the neural network are more stable.\n", "* Batch normalization is slightly different for fully connected layers than for convolutional layers. In fact,\n", "  for convolutional layers, layer normalization can sometimes be used as an alternative.\n", "* Like a dropout layer, batch normalization layers have different behaviors\n", "  in training mode than in prediction mode.\n", "* Batch normalization is useful for regularization and improving convergence in optimization. By contrast,\n", "  the original motivation of reducing internal covariate shift seems not to be a valid explanation.\n", "* For more robust models that are less sensitive to input perturbations, consider removing batch normalization :cite:`wang2022removing`.\n", "\n", "## Exercises\n", "\n", "1. Should we remove the bias parameter from the fully connected layer or the convolutional layer before the batch normalization? Why?\n", "1. Compare the learning rates for LeNet with and without batch normalization.\n", "    1. Plot the increase in validation accuracy.\n", "    1. How large can you make the learning rate before the optimization fails in both cases?\n", "1. Do we need batch normalization in every layer? Experiment with it.\n", "1. Implement a \"lite\" version of batch normalization that only removes the mean, or alternatively one that\n", "   only removes the variance. How does it behave?\n", "1. Fix the parameters `beta` and `gamma`. Observe and analyze the results.\n", "1. Can you replace dropout by batch normalization? How does the behavior change?\n", "1. Research ideas: think of other normalization transforms that you can apply:\n", "    1. Can you apply the probability integral transform?\n", "    1. Can you use a full-rank covariance estimate? Why should you probably not do that? \n", "    1. Can you use other compact matrix variants (block-diagonal, low-displacement rank, Monarch, etc.)?\n", "    1. Does a sparsification compression act as a regularizer?\n", "    1. Are there other projections (e.g., convex cone, symmetry group-specific transforms) that you can use?\n"]}, {"cell_type": "markdown", "id": "3c9f5a14", "metadata": {"origin_pos": 37, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/84)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON> (aideep)", "language": "python", "name": "<PERSON><PERSON>"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "7ea1dde2", "metadata": {"origin_pos": 1}, "source": ["# Designing Convolution Network Architectures\n", ":label:`sec_cnn-design`\n", "\n", "The previous sections have taken us on a tour of modern network design for computer vision. Common to all the work we covered was that it greatly relied on the intuition of scientists. Many of the architectures are heavily informed by human creativity and to a much lesser extent by systematic exploration of the design space that deep networks offer. Nonetheless, this *network engineering* approach has been tremendously successful. \n", "\n", "Ever since AlexNet (:numref:`sec_alexnet`)\n", "beat conventional computer vision models on ImageNet,\n", "it has become popular to construct very deep networks\n", "by stacking blocks of convolutions, all designed according to the same pattern. \n", "In particular, $3 \\times 3$ convolutions were \n", "popularized by VGG networks (:numref:`sec_vgg`).\n", "NiN (:numref:`sec_nin`) showed that even $1 \\times 1$ convolutions could \n", "be beneficial by adding local nonlinearities. \n", "Moreover, NiN solved the problem of aggregating information at the head of a network \n", "by aggregating across all locations. \n", "GoogLeNet (:numref:`sec_googlenet`) added multiple branches of different convolution width, \n", "combining the advantages of VGG and NiN in its Inception block. \n", "ResNets (:numref:`sec_resnet`) \n", "changed the inductive bias towards the identity mapping (from $f(x) = 0$). This allowed for very deep networks. Almost a decade later, the ResNet design is still popular, a testament to its design. Lastly, ResNeXt (:numref:`subsec_resnext`) added grouped convolutions, offering a better trade-off between parameters and computation. A precursor to Transformers for vision, the Squeeze-and-Excitation Networks (SENets) allow for efficient information transfer between locations\n", ":cite:`<PERSON><PERSON>Shen.Sun.2018`. This was accomplished by computing a per-channel global attention function. \n", "\n", "Up to now we have omitted networks obtained via *neural architecture search* (NAS) :cite:`zoph2016neural,liu2018darts`. We chose to do so since their cost is usually enormous, relying on brute-force search, genetic algorithms, reinforcement learning, or some other form of hyperparameter optimization. Given a fixed search space,\n", "NAS uses a search strategy to automatically select\n", "an architecture based on the returned performance estimation.\n", "The outcome of NAS\n", "is a single network instance. EfficientNets are a notable outcome of this search :cite:`tan2019efficientnet`.\n", "\n", "In the following we discuss an idea that is quite different to the quest for the *single best network*. It is computationally relatively inexpensive, it leads to scientific insights on the way, and it is quite effective in terms of the quality of outcomes. Let's review the strategy by :citet:`Radosavovic.Kosaraju.Girshick.ea.2020` to *design network design spaces*. The strategy combines the strength of manual design and NAS. It accomplishes this by operating on *distributions of networks* and optimizing the distributions in a way to obtain good performance for entire families of networks. The outcome of it are *RegNets*, specifically RegNetX and RegNetY, plus a range of guiding principles for the design of performant CNNs.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d2d025e9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:41.106812Z", "iopub.status.busy": "2023-08-18T19:46:41.106523Z", "iopub.status.idle": "2023-08-18T19:46:44.212758Z", "shell.execute_reply": "2023-08-18T19:46:44.211435Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "a6e554d6", "metadata": {"origin_pos": 6}, "source": ["## The AnyNet Design Space\n", ":label:`subsec_the-anynet-design-space`\n", "\n", "The description below closely follows the reasoning in :citet:`<PERSON><PERSON><PERSON><PERSON><PERSON>.Kosaraju.Girshick.ea.2020` with some abbreviations to make it fit in the scope of the book. \n", "To begin, we need a template for the family of networks to explore. One of the commonalities of the designs in this chapter is that the networks consist of a *stem*, a *body* and a *head*. The stem performs initial image processing, often through convolutions with a larger window size. The body consists of multiple blocks, carrying out the bulk of the transformations needed to go from raw images to object representations. Lastly, the head converts this into the desired outputs, such as via a softmax regressor for multiclass classification. \n", "The body, in turn, consists of multiple stages, operating on the image at decreasing resolutions. In fact, both the stem and each subsequent stage quarter the spatial resolution. Lastly, each stage consists of one or more blocks. This pattern is common to all networks, from VGG to ResNeXt. Indeed, for the design of generic AnyNet networks, :citet:`Radosavovic.Kosaraju.Girshick.ea.2020` used the ResNeXt block of :numref:`fig_resnext_block`.\n", "\n", "\n", "![The AnyNet design space. The numbers $(\\mathit{c}, \\mathit{r})$ along each arrow indicate the number of channels $c$ and the resolution $\\mathit{r} \\times \\mathit{r}$ of the images at that point. From left to right: generic network structure composed of stem, body, and head; body composed of four stages; detailed structure of a stage; two alternative structures for blocks, one without downsampling and one that halves the resolution in each dimension. Design choices include depth $\\mathit{d_i}$, the number of output channels $\\mathit{c_i}$, the number of groups $\\mathit{g_i}$, and bottleneck ratio $\\mathit{k_i}$ for any stage $\\mathit{i}$.](../img/anynet.svg)\n", ":label:`fig_anynet_full`\n", "\n", "Let's review the structure outlined in :numref:`fig_anynet_full` in detail. As mentioned, an AnyNet consists of a stem, body, and head. The stem takes as its input RGB images (3 channels), using a $3 \\times 3$ convolution with a stride of $2$, followed by a batch norm, to halve the resolution from $r \\times r$ to $r/2 \\times r/2$. Moreover, it generates $c_0$ channels that serve as input to the body. \n", "\n", "Since the network is designed to work well with ImageNet images of shape $224 \\times 224 \\times 3$, the body serves to reduce this to $7 \\times 7 \\times c_4$ through 4 stages (recall that $224 / 2^{1+4} = 7$), each with an eventual stride of $2$. Lastly, the head employs an entirely standard design via global average pooling, similar to NiN (:numref:`sec_nin`), followed by a fully connected layer to emit an $n$-dimensional vector for $n$-class classification. \n", "\n", "Most of the relevant design decisions are inherent to the body of the network. It proceeds in stages, where each stage is composed of the same type of ResNeXt blocks as we discussed in :numref:`subsec_resnext`. The design there is again entirely generic: we begin with a block that halves the resolution by using a stride of $2$ (the rightmost in :numref:`fig_anynet_full`). To match this, the residual branch of the ResNeXt block needs to pass through a $1 \\times 1$ convolution. This block is followed by a variable number of additional ResNeXt blocks that leave both resolution and the number of channels unchanged. Note that a common design practice is to add a slight bottleneck in the design of convolutional blocks. \n", "As such, with bottleneck ratio $k_i \\geq 1$ we afford some number of channels, $c_i/k_i$,  within each block for stage $i$ (as the experiments show, this is not really effective and should be skipped). Lastly, since we are dealing with ResNeXt blocks, we also need to pick the number of groups $g_i$ for grouped convolutions at stage $i$. \n", "\n", "This seemingly generic design space provides us nonetheless with many parameters: we can set the block width (number of channels) $c_0, \\ldots c_4$, the depth (number of blocks) per stage $d_1, \\ldots d_4$, the bottleneck ratios $k_1, \\ldots k_4$, and the group widths (numbers of groups) $g_1, \\ldots g_4$. \n", "In total this adds up to 17 parameters, resulting in an unreasonably large number of configurations that would warrant exploring. We need some tools to reduce this huge design space effectively. This is where the conceptual beauty of design spaces comes in. Before we do so, let's implement the generic design first.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "0bcf1d35", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:44.217808Z", "iopub.status.busy": "2023-08-18T19:46:44.216883Z", "iopub.status.idle": "2023-08-18T19:46:44.224255Z", "shell.execute_reply": "2023-08-18T19:46:44.223047Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["class AnyNet(d2l.Classifier):\n", "    def stem(self, num_channels):\n", "        return nn.Sequential(\n", "            nn.LazyConv2d(num_channels, kernel_size=3, stride=2, padding=1),\n", "            nn.LazyBatchNorm2d(), nn.ReLU())"]}, {"cell_type": "markdown", "id": "e24d3006", "metadata": {"origin_pos": 11}, "source": ["Each stage consists of `depth` ResNeXt blocks,\n", "where `num_channels` specifies the block width.\n", "Note that the first block halves the height and width of input images.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c6faf93f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:44.228508Z", "iopub.status.busy": "2023-08-18T19:46:44.227739Z", "iopub.status.idle": "2023-08-18T19:46:44.236181Z", "shell.execute_reply": "2023-08-18T19:46:44.234611Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(AnyNet)\n", "def stage(self, depth, num_channels, groups, bot_mul):\n", "    blk = []\n", "    for i in range(depth):\n", "        if i == 0:\n", "            blk.append(d2l.ResNeXtBlock(num_channels, groups, bot_mul,\n", "                use_1x1conv=True, strides=2))\n", "        else:\n", "            blk.append(d2l.ResNeXtBlock(num_channels, groups, bot_mul))\n", "    return nn.Sequential(*blk)"]}, {"cell_type": "markdown", "id": "fb035054", "metadata": {"origin_pos": 16}, "source": ["Putting the network stem, body, and head together,\n", "we complete the implementation of AnyNet.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "18a44834", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:44.241780Z", "iopub.status.busy": "2023-08-18T19:46:44.240678Z", "iopub.status.idle": "2023-08-18T19:46:44.250216Z", "shell.execute_reply": "2023-08-18T19:46:44.248805Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(AnyNet)\n", "def __init__(self, arch, stem_channels, lr=0.1, num_classes=10):\n", "    super(AnyNet, self).__init__()\n", "    self.save_hyperparameters()\n", "    self.net = nn.Sequential(self.stem(stem_channels))\n", "    for i, s in enumerate(arch):\n", "        self.net.add_module(f'stage{i+1}', self.stage(*s))\n", "    self.net.add_module('head', nn.Sequential(\n", "        nn.AdaptiveAvgPool2d((1, 1)), nn.<PERSON><PERSON>(),\n", "        nn.<PERSON><PERSON><PERSON>inear(num_classes)))\n", "    self.net.apply(d2l.init_cnn)"]}, {"cell_type": "markdown", "id": "114b65ed", "metadata": {"origin_pos": 19}, "source": ["## Distributions and Parameters of Design Spaces\n", "\n", "As just discussed in :numref:`subsec_the-anynet-design-space`, parameters of a design space are hyperparameters of networks in that design space.\n", "Consider the problem of identifying good parameters in the AnyNet design space. We could try finding the *single best* parameter choice for a given amount of computation (e.g., FLOPs and compute time). If we allowed for even only *two* possible choices for each parameter, we would have to explore $2^{17} = 131072$ combinations to find the best solution. This is clearly infeasible because of its exorbitant cost. Even worse, we do not really learn anything from this exercise in terms of how one should design a network. Next time we add, say, an X-stage, or a shift operation, or similar, we would need to start from scratch. Even worse, due to the stochasticity in training (rounding, shuffling, bit errors), no two runs are likely to produce exactly the same results. A better strategy would be to try to determine general guidelines of how the choices of parameters should be related. For instance, the bottleneck ratio, the number of channels, blocks, groups, or their change between layers should ideally be governed by a collection of simple rules. The approach in :citet:`radosavovic2019network` relies on the following four assumptions:\n", "\n", "1. We assume that general design principles actually exist, so that many networks satisfying these requirements should offer good performance. Consequently, identifying a *distribution* over networks can be a sensible strategy. In other words, we assume that there are many good needles in the haystack.\n", "1. We need not train networks to convergence before we can assess whether a network is good. Instead, it is sufficient to use the intermediate results as reliable guidance for final accuracy. Using (approximate) proxies to optimize an objective is referred to as multi-fidelity optimization :cite:`forrester2007multi`. Consequently, design optimization is carried out, based on the accuracy achieved after only a few passes through the dataset, reducing the cost significantly. \n", "1. Results obtained at a smaller scale (for smaller networks) generalize to larger ones. Consequently, optimization is carried out for networks that are structurally similar, but with a smaller number of blocks, fewer channels, etc. Only in the end will we need to verify that the so-found networks also offer good performance at scale. \n", "1. Aspects of the design can be approximately factorized so that it is possible to infer their effect on the quality of the outcome somewhat independently. In other words, the optimization problem is moderately easy.\n", "\n", "These assumptions allow us to test many networks cheaply. In particular, we can *sample* uniformly from the space of configurations and evaluate their performance. Subsequently, we can evaluate the quality of the choice of parameters by reviewing the *distribution* of error/accuracy that can be achieved with said networks. Denote by $F(e)$ the cumulative distribution function (CDF) for errors committed by networks of a given design space, drawn using probability disribution $p$. That is, \n", "\n", "$$F(e, p) \\stackrel{\\textrm{def}}{=} P_{\\textrm{net} \\sim p} \\{e(\\textrm{net}) \\leq e\\}.$$\n", "\n", "Our goal is now to find a distribution $p$ over *networks* such that most networks have a very low error rate and where the support of $p$ is concise. Of course, this is computationally infeasible to perform accurately. We resort to a sample of networks $\\mathcal{Z} \\stackrel{\\textrm{def}}{=} \\{\\textrm{net}_1, \\ldots \\textrm{net}_n\\}$ (with errors $e_1, \\ldots, e_n$, respectively) from $p$ and use the empirical CDF $\\hat{F}(e, \\mathcal{Z})$ instead:\n", "\n", "$$\\hat{F}(e, \\mathcal{Z}) = \\frac{1}{n}\\sum_{i=1}^n \\mathbf{1}(e_i \\leq e).$$\n", "\n", "Whenever the CDF for one set of choices majorizes (or matches) another CDF it follows that its choice of parameters is superior (or indifferent). Accordingly \n", ":citet:`<PERSON><PERSON>av<PERSON><PERSON>.Kosaraju.Girshick.ea.2020` experimented with a shared network bottleneck ratio $k_i = k$ for all stages $i$ of the network. This gets rid of three of the four parameters governing the bottleneck ratio. To assess whether this (negatively) affects the performance one can draw networks from the constrained and from the unconstrained distribution and compare the corresonding CDFs. It turns out that this constraint does not affect the accuracy of the distribution of networks at all, as can be seen in the first panel of :numref:`fig_regnet-fig`. \n", "Likewise, we could choose to pick the same group width $g_i = g$ occurring at the various stages of the network. Again, this does not affect performance, as can be seen in the second panel of :numref:`fig_regnet-fig`.\n", "Both steps combined reduce the number of free parameters by six. \n", "\n", "![Comparing error empirical distribution functions of design spaces. $\\textrm{AnyNet}_\\mathit{A}$ is the original design space; $\\textrm{AnyNet}_\\mathit{B}$ ties the bottleneck ratios, $\\textrm{AnyNet}_\\mathit{C}$ also ties group widths, $\\textrm{AnyNet}_\\mathit{D}$ increases the network depth across stages. From left to right: (i) tying bottleneck ratios has no effect on performance; (ii) tying group widths has no effect on performance; (iii) increasing network widths (channels) across stages improves performance; (iv) increasing network depths across stages improves performance. Figure courtesy of :citet:`Radosavovic.Kosaraju.Girshick.ea.2020`.](../img/regnet-fig.png)\n", ":label:`fig_regnet-fig`\n", "\n", "Next we look for ways to reduce the multitude of potential choices for width and depth of the stages. It is a reasonable assumption that, as we go deeper, the number of channels should increase, i.e., $c_i \\geq c_{i-1}$ ($w_{i+1} \\geq w_i$ per their notation in :numref:`fig_regnet-fig`), yielding \n", "$\\textrm{AnyNetX}_D$. Likewise, it is equally reasonable to assume that as the stages progress, they should become deeper, i.e., $d_i \\geq d_{i-1}$, yielding $\\textrm{AnyNetX}_E$. This can be experimentally verified in the third and fourth panel of :numref:`fig_regnet-fig`, respectively.\n", "\n", "## RegNet\n", "\n", "The resulting $\\textrm{AnyNetX}_E$ design space consists of simple networks\n", "following easy-to-interpret design principles:\n", "\n", "* Share the bottleneck ratio $k_i = k$ for all stages $i$;\n", "* Share the group width $g_i = g$ for all stages $i$;\n", "* Increase network width across stages: $c_{i} \\leq c_{i+1}$;\n", "* Increase network depth across stages: $d_{i} \\leq d_{i+1}$.\n", "\n", "This leaves us with a final set of choices: how to pick the specific values for the above parameters of the eventual $\\textrm{AnyNetX}_E$ design space. By studying the best-performing networks from the distribution in $\\textrm{AnyNetX}_E$ one can observe the following: the width of the network ideally increases linearly with the block index across the network, i.e., $c_j \\approx c_0 + c_a j$, where $j$ is the block index and slope $c_a > 0$. Given that we get to choose a different block width only per stage, we arrive at a piecewise constant function, engineered to match this dependence. Furthermore, experiments also show that a bottleneck ratio of $k = 1$ performs best, i.e., we are advised not to use bottlenecks at all. \n", "\n", "We recommend the interested reader reviews further details in the design of specific networks for different amounts of computation by perusing :citet:`Radosavovic.Kosaraju.Girshick.ea.2020`. For instance, an effective 32-layer RegNetX variant is given by $k = 1$ (no bottleneck), $g = 16$ (group width is 16), $c_1 = 32$ and $c_2 = 80$ channels for the first and second stage, respectively, chosen to be $d_1=4$ and $d_2=6$ blocks deep. The astonishing insight from the design is that it still applies, even when investigating networks at a larger scale. Even better, it even holds for Squeeze-and-Excitation (SE) network designs (RegNetY) that have a global channel activation :cite:`Hu.Shen.Sun.2018`.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "dab5f41a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:44.255171Z", "iopub.status.busy": "2023-08-18T19:46:44.254345Z", "iopub.status.idle": "2023-08-18T19:46:44.262677Z", "shell.execute_reply": "2023-08-18T19:46:44.261273Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [], "source": ["class RegNetX32(AnyNet):\n", "    def __init__(self, lr=0.1, num_classes=10):\n", "        stem_channels, groups, bot_mul = 32, 16, 1\n", "        depths, channels = (4, 6), (32, 80)\n", "        super().__init__(\n", "            ((depths[0], channels[0], groups, bot_mul),\n", "             (depths[1], channels[1], groups, bot_mul)),\n", "            stem_channels, lr, num_classes)"]}, {"cell_type": "markdown", "id": "cd897988", "metadata": {"origin_pos": 22}, "source": ["We can see that each RegNetX stage progressively reduces resolution and increases output channels.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "75a56c8e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:44.267142Z", "iopub.status.busy": "2023-08-18T19:46:44.266080Z", "iopub.status.idle": "2023-08-18T19:46:44.365256Z", "shell.execute_reply": "2023-08-18T19:46:44.364396Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential output shape:\t torch.Size([1, 32, 48, 48])\n", "Sequential output shape:\t torch.Size([1, 32, 24, 24])\n", "Sequential output shape:\t torch.Size([1, 80, 12, 12])\n", "Sequential output shape:\t torch.Size([1, 10])\n"]}], "source": ["RegNetX32().layer_summary((1, 1, 96, 96))"]}, {"cell_type": "markdown", "id": "d568cc20", "metadata": {"origin_pos": 26}, "source": ["## Training\n", "\n", "Training the 32-layer RegNetX on the Fashion-MNIST dataset is just like before.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "91cbec9a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:44.368862Z", "iopub.status.busy": "2023-08-18T19:46:44.368269Z", "iopub.status.idle": "2023-08-18T19:50:01.782279Z", "shell.execute_reply": "2023-08-18T19:50:01.781241Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:50:01.651706</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"meb75d64702\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#meb75d64702\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#meb75d64702\" x=\"69.163125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(65.981875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#meb75d64702\" x=\"108.223125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(105.041875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#meb75d64702\" x=\"147.283125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(144.101875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#meb75d64702\" x=\"186.343125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(183.161875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#meb75d64702\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m8b6972250a\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8b6972250a\" x=\"30.103125\" y=\"133.279982\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 137.079201) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m8b6972250a\" x=\"30.103125\" y=\"100.083315\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 103.882533) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m8b6972250a\" x=\"30.103125\" y=\"66.886647\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 70.685866) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m8b6972250a\" x=\"30.103125\" y=\"33.68998\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 37.489198) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 34.954394 13.5 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 49.633125 76.040053 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_15\"/>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 49.633125 76.040053 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 49.633125 32.583643 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 49.633125 76.040053 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 49.633125 32.583643 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 49.633125 76.040053 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 49.633125 32.583643 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 49.633125 32.583643 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "L 171.372901 135.197646 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "L 171.372901 135.197646 \n", "L 181.11708 133.761175 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "L 171.372901 135.197646 \n", "L 181.11708 133.761175 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "L 186.343125 120.566709 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "L 171.372901 135.197646 \n", "L 181.11708 133.761175 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "L 186.343125 120.566709 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "L 186.343125 16.628757 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "L 171.372901 135.197646 \n", "L 181.11708 133.761175 \n", "L 190.861259 137.469738 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "L 186.343125 120.566709 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "L 186.343125 16.628757 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "L 171.372901 135.197646 \n", "L 181.11708 133.761175 \n", "L 190.861259 137.469738 \n", "L 200.605438 136.13151 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "L 186.343125 120.566709 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "L 186.343125 16.628757 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "L 171.372901 135.197646 \n", "L 181.11708 133.761175 \n", "L 190.861259 137.469738 \n", "L 200.605438 136.13151 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "L 186.343125 120.566709 \n", "L 205.873125 111.52118 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "L 186.343125 16.628757 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "L 171.372901 135.197646 \n", "L 181.11708 133.761175 \n", "L 190.861259 137.469738 \n", "L 200.605438 136.13151 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "L 186.343125 120.566709 \n", "L 205.873125 111.52118 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "L 186.343125 16.628757 \n", "L 205.873125 19.550537 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "L 171.372901 135.197646 \n", "L 181.11708 133.761175 \n", "L 190.861259 137.469738 \n", "L 200.605438 136.13151 \n", "L 210.349618 139.5 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "L 186.343125 120.566709 \n", "L 205.873125 111.52118 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "L 186.343125 16.628757 \n", "L 205.873125 19.550537 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "L 171.372901 135.197646 \n", "L 181.11708 133.761175 \n", "L 190.861259 137.469738 \n", "L 200.605438 136.13151 \n", "L 210.349618 139.5 \n", "L 220.093797 138.044948 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "L 186.343125 120.566709 \n", "L 205.873125 111.52118 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "L 186.343125 16.628757 \n", "L 205.873125 19.550537 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "L 171.372901 135.197646 \n", "L 181.11708 133.761175 \n", "L 190.861259 137.469738 \n", "L 200.605438 136.13151 \n", "L 210.349618 139.5 \n", "L 220.093797 138.044948 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "L 186.343125 120.566709 \n", "L 205.873125 111.52118 \n", "L 225.403125 121.405632 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "L 186.343125 16.628757 \n", "L 205.873125 19.550537 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 86.022264 \n", "L 54.442752 99.423775 \n", "L 64.186931 108.531663 \n", "L 73.93111 114.94409 \n", "L 83.675289 116.823251 \n", "L 93.419468 121.64301 \n", "L 103.163647 122.956381 \n", "L 112.907826 125.888846 \n", "L 122.652006 126.68361 \n", "L 132.396185 130.296898 \n", "L 142.140364 128.948212 \n", "L 151.884543 133.207583 \n", "L 161.628722 131.801861 \n", "L 171.372901 135.197646 \n", "L 181.11708 133.761175 \n", "L 190.861259 137.469738 \n", "L 200.605438 136.13151 \n", "L 210.349618 139.5 \n", "L 220.093797 138.044948 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 49.633125 76.040053 \n", "L 69.163125 97.73149 \n", "L 88.693125 116.581773 \n", "L 108.223125 95.187925 \n", "L 127.753125 119.542357 \n", "L 147.283125 107.687416 \n", "L 166.813125 115.583426 \n", "L 186.343125 120.566709 \n", "L 205.873125 111.52118 \n", "L 225.403125 121.405632 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 49.633125 32.583643 \n", "L 69.163125 24.195838 \n", "L 88.693125 18.680569 \n", "L 108.223125 25.574655 \n", "L 127.753125 16.71083 \n", "L 147.283125 21.618763 \n", "L 166.813125 18.729812 \n", "L 186.343125 16.628757 \n", "L 205.873125 19.550537 \n", "L 225.403125 15.775204 \n", "\" clip-path=\"url(#pdf2bea5bec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 100.434375 \n", "L 218.403125 100.434375 \n", "Q 220.403125 100.434375 220.403125 98.434375 \n", "L 220.403125 54.565625 \n", "Q 220.403125 52.565625 218.403125 52.565625 \n", "L 138.8125 52.565625 \n", "Q 136.8125 52.565625 136.8125 54.565625 \n", "L 136.8125 98.434375 \n", "Q 136.8125 100.434375 138.8125 100.434375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_127\">\n", "     <path d=\"M 140.8125 60.664063 \n", "L 150.8125 60.664063 \n", "L 160.8125 60.664063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 64.164063) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_128\">\n", "     <path d=\"M 140.8125 75.620313 \n", "L 150.8125 75.620313 \n", "L 160.8125 75.620313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 79.120313) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_129\">\n", "     <path d=\"M 140.8125 90.576563 \n", "L 150.8125 90.576563 \n", "L 160.8125 90.576563 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 94.076563) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pdf2bea5bec\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = RegNetX32(lr=0.05)\n", "trainer = d2l.Trainer(max_epochs=10, num_gpus=1)\n", "data = d2l.FashionMNIST(batch_size=128, resize=(96, 96))\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "f60c0281", "metadata": {"origin_pos": 29}, "source": ["## Discussion\n", "\n", "With desirable inductive biases (assumptions or preferences) like locality and translation invariance (:numref:`sec_why-conv`)\n", "for vision, CNNs have been the dominant architectures in this area. This remained the case from LeNet up until Transformers (:numref:`sec_transformer`) :cite:`Dosovitskiy.Beyer.Kolesnikov.ea.2021,touvron2021training` started surpassing CNNs in terms of accuracy. While much of the recent progress in terms of vision Transformers *can* be backported into CNNs :cite:`liu2022convnet`, it is only possible at a higher computational cost. Just as importantly, recent hardware optimizations (NVIDIA Ampere and Hopper) have only widened the gap in favor of Transformers. \n", "\n", "It is worth noting that Transformers have a significantly lower degree of inductive bias towards locality and translation invariance than CNNs. That learned structures prevailed is due, not least, to the availability of large image collections, such as LAION-400m and LAION-5B :cite:`schuhmann2022laion` with up to 5 billion images. Quite surprisingly, some of the more relevant work in this context even includes MLPs :cite:`tolstikhin2021mlp`. \n", "\n", "In sum, vision Transformers (:numref:`sec_vision-transformer`) by now lead in terms of \n", "state-of-the-art performance in large-scale image classification, \n", "showing that *scalability trumps inductive biases* :cite:`<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.Beyer.Kolesnikov.ea.2021`.\n", "This includes pretraining large-scale Transformers (:numref:`sec_large-pretraining-transformers`) with multi-head self-attention (:numref:`sec_multihead-attention`). We invite the readers to dive into these chapters for a much more detailed discussion.\n", "\n", "## Exercises\n", "\n", "1. Increase the number of stages to four. Can you design a deeper RegNetX that performs better?\n", "1. De-ResNeXt-ify RegNets by replacing the ResNeXt block with the ResNet block. How does your new model perform?\n", "1. Implement multiple instances of a \"VioNet\" family by *violating* the design principles of RegNetX. How do they perform? Which of ($d_i$, $c_i$, $g_i$, $b_i$) is the most important factor?\n", "1. Your goal is to design the \"perfect\" MLP. Can you use the design principles introduced above to find good architectures? Is it possible to extrapolate from small to large networks?\n"]}, {"cell_type": "markdown", "id": "cdaec6a5", "metadata": {"origin_pos": 31, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/7463)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
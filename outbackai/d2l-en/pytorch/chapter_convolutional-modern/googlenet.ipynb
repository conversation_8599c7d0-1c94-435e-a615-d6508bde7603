{"cells": [{"cell_type": "markdown", "id": "8979a666", "metadata": {"origin_pos": 1}, "source": ["# Multi-Branch Networks  (GoogLeNet)\n", ":label:`sec_googlenet`\n", "\n", "In 2014, *GoogLeNet*\n", "won the ImageNet Challenge :cite:`Szegedy.Liu.Jia.ea.2015`, using a structure\n", "that combined the strengths of NiN :cite:`<PERSON><PERSON>.2013`, repeated blocks :cite:`<PERSON>yan.Zisserman.2014`,\n", "and a cocktail of convolution kernels. It was arguably also the first network that exhibited a clear distinction among the stem (data ingest), body (data processing), and head (prediction) in a CNN. This design pattern has persisted ever since in the design of deep networks: the *stem* is given by the first two or three convolutions that operate on the image. They extract low-level features from the underlying images. This is followed by a *body* of convolutional blocks. Finally, the *head* maps the features obtained so far to the required classification, segmentation, detection, or tracking problem at hand.\n", "\n", "The key contribution in GoogLeNet was the design of the network body. It solved the problem of selecting\n", "convolution kernels in an ingenious way. While other works tried to identify which convolution, ranging from $1 \\times 1$ to $11 \\times 11$ would be best, it simply *concatenated* multi-branch convolutions.\n", "In what follows we introduce a slightly simplified version of GoogLeNet: the original design included a number of tricks for stabilizing training through intermediate loss functions, applied to multiple layers of the network. \n", "They are no longer necessary due to the availability of improved training algorithms.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "066f8e89", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:12:43.017176Z", "iopub.status.busy": "2023-08-18T20:12:43.016624Z", "iopub.status.idle": "2023-08-18T20:12:46.230431Z", "shell.execute_reply": "2023-08-18T20:12:46.228850Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "fd207136", "metadata": {"origin_pos": 6}, "source": ["## (**Inception Blocks**)\n", "\n", "The basic convolutional block in GoogLeNet is called an *Inception block*,\n", "stemming from the meme \"we need to go deeper\" from the movie *Inception*.\n", "\n", "![Structure of the Inception block.](../img/inception.svg)\n", ":label:`fig_inception`\n", "\n", "As depicted in :numref:`fig_inception`,\n", "the inception block consists of four parallel branches.\n", "The first three branches use convolutional layers\n", "with window sizes of $1\\times 1$, $3\\times 3$, and $5\\times 5$\n", "to extract information from different spatial sizes.\n", "The middle two branches also add a $1\\times 1$ convolution of the input\n", "to reduce the number of channels, reducing the model's complexity.\n", "The fourth branch uses a $3\\times 3$ max-pooling layer,\n", "followed by a $1\\times 1$ convolutional layer\n", "to change the number of channels.\n", "The four branches all use appropriate padding to give the input and output the same height and width.\n", "Finally, the outputs along each branch are concatenated\n", "along the channel dimension and comprise the block's output.\n", "The commonly-tuned hyperparameters of the Inception block\n", "are the number of output channels per layer, i.e., how to allocate capacity among convolutions of different size.\n"]}, {"cell_type": "code", "execution_count": null, "id": "7deda9e0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:12:46.235999Z", "iopub.status.busy": "2023-08-18T20:12:46.235477Z", "iopub.status.idle": "2023-08-18T20:12:46.245899Z", "shell.execute_reply": "2023-08-18T20:12:46.244271Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["class Inception(nn.Module):\n", "    # c1--c4 are the number of output channels for each branch\n", "    def __init__(self, c1, c2, c3, c4, **kwargs):\n", "        super(Inception, self).__init__(**kwargs)\n", "        # Branch 1\n", "        self.b1_1 = nn.LazyConv2d(c1, kernel_size=1)\n", "        # Branch 2\n", "        self.b2_1 = nn.LazyConv2d(c2[0], kernel_size=1)\n", "        self.b2_2 = nn.LazyConv2d(c2[1], kernel_size=3, padding=1)\n", "        # Branch 3\n", "        self.b3_1 = nn.LazyConv2d(c3[0], kernel_size=1)\n", "        self.b3_2 = nn.LazyConv2d(c3[1], kernel_size=5, padding=2)\n", "        # Branch 4\n", "        self.b4_1 = nn.MaxPool2d(kernel_size=3, stride=1, padding=1)\n", "        self.b4_2 = nn.LazyConv2d(c4, kernel_size=1)\n", "\n", "    def forward(self, x):\n", "        b1 = <PERSON><PERSON>relu(self.b1_1(x))\n", "        b2 = <PERSON><PERSON>relu(self.b2_2(<PERSON><PERSON>relu(self.b2_1(x))))\n", "        b3 = <PERSON><PERSON>relu(self.b3_2(<PERSON><PERSON>relu(self.b3_1(x))))\n", "        b4 = <PERSON><PERSON>relu(self.b4_2(self.b4_1(x)))\n", "        return torch.cat((b1, b2, b3, b4), dim=1)"]}, {"cell_type": "markdown", "id": "2a943308", "metadata": {"origin_pos": 11}, "source": ["To gain some intuition for why this network works so well,\n", "consider the combination of the filters.\n", "They explore the image in a variety of filter sizes.\n", "This means that details at different extents\n", "can be recognized efficiently by filters of different sizes.\n", "At the same time, we can allocate different amounts of parameters\n", "for different filters.\n", "\n", "\n", "## [**GoogLeNet Model**]\n", "\n", "As shown in :numref:`fig_inception_full`, GoogLeNet uses a stack of a total of 9 inception blocks, arranged into three groups with max-pooling in between,\n", "and global average pooling in its head to generate its estimates.\n", "Max-pooling between inception blocks reduces the dimensionality.\n", "At its stem, the first module is similar to AlexNet and LeNet.\n", "\n", "![The GoogLeNet architecture.](../img/inception-full-90.svg)\n", ":label:`fig_inception_full`\n", "\n", "We can now implement GoogLeNet piece by piece. Let's begin with the stem.\n", "The first module uses a 64-channel $7\\times 7$ convolutional layer.\n"]}, {"cell_type": "code", "execution_count": null, "id": "95b8f516", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:12:46.250221Z", "iopub.status.busy": "2023-08-18T20:12:46.249461Z", "iopub.status.idle": "2023-08-18T20:12:46.255915Z", "shell.execute_reply": "2023-08-18T20:12:46.254721Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["class GoogleNet(d2l.Classifier):\n", "    def b1(self):\n", "        return nn.Sequential(\n", "            nn.LazyConv2d(64, kernel_size=7, stride=2, padding=3),\n", "            nn.<PERSON><PERSON><PERSON>(), nn.<PERSON>Pool2d(kernel_size=3, stride=2, padding=1))"]}, {"cell_type": "markdown", "id": "762e20f9", "metadata": {"origin_pos": 14}, "source": ["The second module uses two convolutional layers:\n", "first, a 64-channel $1\\times 1$ convolutional layer,\n", "followed by a $3\\times 3$ convolutional layer that triples the number of channels. This corresponds to the second branch in the Inception block and concludes the design of the body. At this point we have 192 channels.\n"]}, {"cell_type": "code", "execution_count": null, "id": "f2f1ae36", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:12:46.260637Z", "iopub.status.busy": "2023-08-18T20:12:46.259867Z", "iopub.status.idle": "2023-08-18T20:12:46.265450Z", "shell.execute_reply": "2023-08-18T20:12:46.264324Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(GoogleNet)\n", "def b2(self):\n", "    return nn.Sequential(\n", "        nn.LazyConv2d(64, kernel_size=1), nn.ReLU(),\n", "        nn.LazyConv2d(192, kernel_size=3, padding=1), nn.ReLU(),\n", "        nn.MaxPool2d(kernel_size=3, stride=2, padding=1))"]}, {"cell_type": "markdown", "id": "dcc6272e", "metadata": {"origin_pos": 16}, "source": ["The third module connects two complete Inception blocks in series.\n", "The number of output channels of the first Inception block is\n", "$64+128+32+32=256$. This amounts to \n", "a ratio of the number of output channels\n", "among the four branches of $2:4:1:1$. To achieve this, we first reduce the input\n", "dimensions by $\\frac{1}{2}$ and by $\\frac{1}{12}$ in the second and third branch respectively\n", "to arrive at $96 = 192/2$ and $16 = 192/12$ channels respectively.\n", "\n", "The number of output channels of the second Inception block\n", "is increased to $128+192+96+64=480$, yielding a ratio of $128:192:96:64 = 4:6:3:2$. As before,\n", "we need to reduce the number of intermediate dimensions in the second and third channel. A\n", "scale of $\\frac{1}{2}$ and $\\frac{1}{8}$ respectively suffices, yielding $128$ and $32$ channels\n", "respectively. This is captured by the arguments of the following `Inception` block constructors.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "33a37805", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:12:46.269744Z", "iopub.status.busy": "2023-08-18T20:12:46.269267Z", "iopub.status.idle": "2023-08-18T20:12:46.276795Z", "shell.execute_reply": "2023-08-18T20:12:46.275528Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(GoogleNet)\n", "def b3(self):\n", "    return nn.Sequential(Inception(64, (96, 128), (16, 32), 32),\n", "                         Inception(128, (128, 192), (32, 96), 64),\n", "                         nn.MaxPool2d(kernel_size=3, stride=2, padding=1))"]}, {"cell_type": "markdown", "id": "112f5cd2", "metadata": {"origin_pos": 18}, "source": ["The fourth module is more complicated.\n", "It connects five Inception blocks in series,\n", "and they have $192+208+48+64=512$, $160+224+64+64=512$,\n", "$128+256+64+64=512$, $112+288+64+64=528$,\n", "and $256+320+128+128=832$ output channels, respectively.\n", "The number of channels assigned to these branches is similar\n", "to that in the third module:\n", "the second branch with the $3\\times 3$ convolutional layer\n", "outputs the largest number of channels,\n", "followed by the first branch with only the $1\\times 1$ convolutional layer,\n", "the third branch with the $5\\times 5$ convolutional layer,\n", "and the fourth branch with the $3\\times 3$ max-pooling layer.\n", "The second and third branches will first reduce\n", "the number of channels according to the ratio.\n", "These ratios are slightly different in different Inception blocks.\n"]}, {"cell_type": "code", "execution_count": null, "id": "2beb4b7a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:12:46.280409Z", "iopub.status.busy": "2023-08-18T20:12:46.280085Z", "iopub.status.idle": "2023-08-18T20:12:46.288926Z", "shell.execute_reply": "2023-08-18T20:12:46.287339Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(GoogleNet)\n", "def b4(self):\n", "    return nn.Sequential(Inception(192, (96, 208), (16, 48), 64),\n", "                         Inception(160, (112, 224), (24, 64), 64),\n", "                         Inception(128, (128, 256), (24, 64), 64),\n", "                         Inception(112, (144, 288), (32, 64), 64),\n", "                         Inception(256, (160, 320), (32, 128), 128),\n", "                         nn.MaxPool2d(kernel_size=3, stride=2, padding=1))"]}, {"cell_type": "markdown", "id": "703d40c6", "metadata": {"origin_pos": 20}, "source": ["The fifth module has two Inception blocks with $256+320+128+128=832$\n", "and $384+384+128+128=1024$ output channels.\n", "The number of channels assigned to each branch\n", "is the same as that in the third and fourth modules,\n", "but differs in specific values.\n", "It should be noted that the fifth block is followed by the output layer.\n", "This block uses the global average pooling layer\n", "to change the height and width of each channel to 1, just as in NiN.\n", "Finally, we turn the output into a two-dimensional array\n", "followed by a fully connected layer\n", "whose number of outputs is the number of label classes.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "8b836927", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:12:46.294877Z", "iopub.status.busy": "2023-08-18T20:12:46.293900Z", "iopub.status.idle": "2023-08-18T20:12:46.301477Z", "shell.execute_reply": "2023-08-18T20:12:46.300150Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(GoogleNet)\n", "def b5(self):\n", "    return nn.Sequential(Inception(256, (160, 320), (32, 128), 128),\n", "                         Inception(384, (192, 384), (48, 128), 128),\n", "                         nn.AdaptiveAvgPool2d((1,1)), nn.<PERSON><PERSON>())"]}, {"cell_type": "markdown", "id": "62f89c54", "metadata": {"origin_pos": 22}, "source": ["Now that we defined all blocks `b1` through `b5`, it is just a matter of assembling them all into a full network.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "fe47c47d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:12:46.306521Z", "iopub.status.busy": "2023-08-18T20:12:46.305540Z", "iopub.status.idle": "2023-08-18T20:12:46.313597Z", "shell.execute_reply": "2023-08-18T20:12:46.312191Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(GoogleNet)\n", "def __init__(self, lr=0.1, num_classes=10):\n", "    super(GoogleNet, self).__init__()\n", "    self.save_hyperparameters()\n", "    self.net = nn.Sequential(self.b1(), self.b2(), self.b3(), self.b4(),\n", "                             self.b5(), nn.<PERSON><PERSON>(num_classes))\n", "    self.net.apply(d2l.init_cnn)"]}, {"cell_type": "markdown", "id": "b94378d8", "metadata": {"origin_pos": 24}, "source": ["The GoogLeNet model is computationally complex. Note the large number of\n", "relatively arbitrary hyperparameters in terms of the number of channels chosen, the number of blocks prior to dimensionality reduction, the relative partitioning of capacity across channels, etc. Much of it is due to the \n", "fact that at the time when GoogLeNet was introduced, automatic tools for network definition or design exploration \n", "were not yet available. For instance, by now we take it for granted that a competent deep learning framework is capable of inferring dimensionalities of input tensors automatically. At the time, many such configurations had to be specified explicitly by the experimenter, thus often slowing down active experimentation. Moreover, the tools needed for automatic exploration were still in flux and initial experiments largely amounted to costly brute-force exploration, genetic algorithms, and similar strategies. \n", "\n", "For now the only modification we will carry out is to\n", "[**reduce the input height and width from 224 to 96\n", "to have a reasonable training time on Fashion-MNIST.**]\n", "This simplifies the computation. Let's have a look at the\n", "changes in the shape of the output between the various modules.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "83b695b7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:12:46.317969Z", "iopub.status.busy": "2023-08-18T20:12:46.316995Z", "iopub.status.idle": "2023-08-18T20:12:46.501717Z", "shell.execute_reply": "2023-08-18T20:12:46.500827Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential output shape:\t torch.Size([1, 64, 24, 24])\n", "Sequential output shape:\t torch.Size([1, 192, 12, 12])\n", "Sequential output shape:\t torch.Size([1, 480, 6, 6])\n", "Sequential output shape:\t torch.Size([1, 832, 3, 3])\n", "Sequential output shape:\t torch.Size([1, 1024])\n", "Linear output shape:\t torch.Size([1, 10])\n"]}], "source": ["model = GoogleNet().layer_summary((1, 1, 96, 96))"]}, {"cell_type": "markdown", "id": "2f3be870", "metadata": {"origin_pos": 27}, "source": ["## [**Training**]\n", "\n", "As before, we train our model using the Fashion-MNIST dataset.\n", " We transform it to $96 \\times 96$ pixel resolution\n", " before invoking the training procedure.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "d52cffee", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:12:46.505565Z", "iopub.status.busy": "2023-08-18T20:12:46.504978Z", "iopub.status.idle": "2023-08-18T20:16:06.908422Z", "shell.execute_reply": "2023-08-18T20:16:06.907202Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-29T08:28:15.837116</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m1438fb86c9\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1438fb86c9\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m1438fb86c9\" x=\"69.163125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(65.981875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m1438fb86c9\" x=\"108.223125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(105.041875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m1438fb86c9\" x=\"147.283125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(144.101875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m1438fb86c9\" x=\"186.343125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(183.161875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m1438fb86c9\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m26e38dd66b\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m26e38dd66b\" x=\"30.103125\" y=\"145.195524\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 148.994742) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m26e38dd66b\" x=\"30.103125\" y=\"116.599131\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 120.39835) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m26e38dd66b\" x=\"30.103125\" y=\"88.002738\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 91.801957) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m26e38dd66b\" x=\"30.103125\" y=\"59.406346\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 63.205565) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m26e38dd66b\" x=\"30.103125\" y=\"30.809953\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 34.609172) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 34.954394 13.5 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 49.633125 13.589114 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_16\"/>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 49.633125 13.589114 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 49.633125 139.5 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 49.633125 13.589114 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 49.633125 139.5 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 49.633125 13.589114 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 49.633125 139.5 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 49.633125 139.5 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "L 171.372901 107.232423 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "L 171.372901 107.232423 \n", "L 181.11708 108.96935 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "L 171.372901 107.232423 \n", "L 181.11708 108.96935 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "L 186.343125 101.674325 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "L 171.372901 107.232423 \n", "L 181.11708 108.96935 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "L 186.343125 101.674325 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "L 186.343125 104.591587 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "L 171.372901 107.232423 \n", "L 181.11708 108.96935 \n", "L 190.861259 111.293426 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "L 186.343125 101.674325 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "L 186.343125 104.591587 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "L 171.372901 107.232423 \n", "L 181.11708 108.96935 \n", "L 190.861259 111.293426 \n", "L 200.605438 113.721005 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "L 186.343125 101.674325 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "L 186.343125 104.591587 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "L 171.372901 107.232423 \n", "L 181.11708 108.96935 \n", "L 190.861259 111.293426 \n", "L 200.605438 113.721005 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "L 186.343125 101.674325 \n", "L 205.873125 108.510858 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "L 186.343125 104.591587 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "L 171.372901 107.232423 \n", "L 181.11708 108.96935 \n", "L 190.861259 111.293426 \n", "L 200.605438 113.721005 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "L 186.343125 101.674325 \n", "L 205.873125 108.510858 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "L 186.343125 104.591587 \n", "L 205.873125 102.069042 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "L 171.372901 107.232423 \n", "L 181.11708 108.96935 \n", "L 190.861259 111.293426 \n", "L 200.605438 113.721005 \n", "L 210.349618 107.647336 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "L 186.343125 101.674325 \n", "L 205.873125 108.510858 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "L 186.343125 104.591587 \n", "L 205.873125 102.069042 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "L 171.372901 107.232423 \n", "L 181.11708 108.96935 \n", "L 190.861259 111.293426 \n", "L 200.605438 113.721005 \n", "L 210.349618 107.647336 \n", "L 220.093797 116.022686 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "L 186.343125 101.674325 \n", "L 205.873125 108.510858 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "L 186.343125 104.591587 \n", "L 205.873125 102.069042 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "L 171.372901 107.232423 \n", "L 181.11708 108.96935 \n", "L 190.861259 111.293426 \n", "L 200.605438 113.721005 \n", "L 210.349618 107.647336 \n", "L 220.093797 116.022686 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "L 186.343125 101.674325 \n", "L 205.873125 108.510858 \n", "L 225.403125 115.960305 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "L 186.343125 104.591587 \n", "L 205.873125 102.069042 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.557746 \n", "L 54.442752 13.609981 \n", "L 64.186931 13.664495 \n", "L 73.93111 13.756022 \n", "L 83.675289 13.89398 \n", "L 93.419468 14.198125 \n", "L 103.163647 15.11949 \n", "L 112.907826 21.014363 \n", "L 122.652006 48.237717 \n", "L 132.396185 77.199544 \n", "L 142.140364 90.471724 \n", "L 151.884543 97.749914 \n", "L 161.628722 102.616876 \n", "L 171.372901 107.232423 \n", "L 181.11708 108.96935 \n", "L 190.861259 111.293426 \n", "L 200.605438 113.721005 \n", "L 210.349618 107.647336 \n", "L 220.093797 116.022686 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 49.633125 13.589114 \n", "L 69.163125 13.709903 \n", "L 88.693125 14.016182 \n", "L 108.223125 16.28951 \n", "L 127.753125 67.554452 \n", "L 147.283125 95.480994 \n", "L 166.813125 104.935945 \n", "L 186.343125 101.674325 \n", "L 205.873125 108.510858 \n", "L 225.403125 115.960305 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_127\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 128.793321 \n", "L 88.693125 131.31021 \n", "L 108.223125 126.389549 \n", "L 127.753125 120.38295 \n", "L 147.283125 107.968179 \n", "L 166.813125 103.381218 \n", "L 186.343125 104.591587 \n", "L 205.873125 102.069042 \n", "L 225.403125 98.794257 \n", "\" clip-path=\"url(#p5f2d98b306)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 60.06875 \n", "L 218.403125 60.06875 \n", "Q 220.403125 60.06875 220.403125 58.06875 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 138.8125 12.2 \n", "Q 136.8125 12.2 136.8125 14.2 \n", "L 136.8125 58.06875 \n", "Q 136.8125 60.06875 138.8125 60.06875 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_128\">\n", "     <path d=\"M 140.8125 20.298438 \n", "L 150.8125 20.298438 \n", "L 160.8125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_129\">\n", "     <path d=\"M 140.8125 35.254688 \n", "L 150.8125 35.254688 \n", "L 160.8125 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_130\">\n", "     <path d=\"M 140.8125 50.210938 \n", "L 150.8125 50.210938 \n", "L 160.8125 50.210938 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 53.710938) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p5f2d98b306\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = GoogleNet(lr=0.01)\n", "trainer = d2l.Trainer(max_epochs=10, num_gpus=1)\n", "data = d2l.FashionMNIST(batch_size=128, resize=(96, 96))\n", "model.apply_init([next(iter(data.get_dataloader(True)))[0]], d2l.init_cnn)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "14eaa685", "metadata": {"origin_pos": 30}, "source": ["## Discussion\n", "\n", "A key feature of GoogLeNet is that it is actually *cheaper* to compute than its predecessors\n", "while simultaneously providing improved accuracy. This marks the beginning of a much more deliberate\n", "network design that trades off the cost of evaluating a network with a reduction in errors. It also marks the beginning of experimentation at a block level with network design hyperparameters, even though it was entirely manual at the time. We will revisit this topic in :numref:`sec_cnn-design` when discussing strategies for network structure exploration. \n", "\n", "Over the following sections we will encounter a number of design choices (e.g., batch normalization, residual connections, and channel grouping) that allow us to improve networks significantly. For now, you can be proud to have implemented what is arguably the first truly modern CNN.\n", "\n", "## Exercises\n", "\n", "1. GoogLeNet was so successful that it went through a number of iterations, progressively improving speed and accuracy. Try to implement and run some of them. They include the following:\n", "    1. Add a batch normalization layer :cite:`Ioffe.Szegedy.2015`, as described later in :numref:`sec_batch_norm`.\n", "    1. Make adjustments to the Inception block (width, choice and order of convolutions), as described in :citet:`Szegedy.Vanhoucke.Ioffe.ea.2016`.\n", "    1. Use label smoothing for model regularization, as described in :citet:`Szegedy.Vanhoucke.Ioffe.ea.2016`.\n", "    1. Make further adjustments to the Inception block by adding residual connection :cite:`Szegedy.Ioffe.Vanhoucke.ea.2017`, as described later in :numref:`sec_resnet`.\n", "1. What is the minimum image size needed for GoogLeNet to work?\n", "1. Can you design a variant of GoogLeNet that works on Fashion-MNIST's native resolution of $28 \\times 28$ pixels? How would you need to change the stem, the body, and the head of the network, if anything at all?\n", "1. Compare the model parameter sizes of AlexNet, VGG, NiN, and GoogLeNet. How do the latter two network\n", "   architectures significantly reduce the model parameter size?\n", "1. Compare the amount of computation needed in GoogLeNet and AlexNet. How does this affect the design of an accelerator chip, e.g., in terms of memory size, memory bandwidth, cache size, the amount of computation, and the benefit of specialized operations?\n"]}, {"cell_type": "markdown", "id": "1f196dce", "metadata": {"origin_pos": 32, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/82)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON> (aideep)", "language": "python", "name": "<PERSON><PERSON>"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
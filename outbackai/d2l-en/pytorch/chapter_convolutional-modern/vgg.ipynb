{"cells": [{"cell_type": "markdown", "id": "36b56088", "metadata": {"origin_pos": 1}, "source": ["# Networks Using Blocks (VGG)\n", ":label:`sec_vgg`\n", "\n", "While AlexNet offered empirical evidence that deep CNNs\n", "can achieve good results, it did not provide a general template\n", "to guide subsequent researchers in designing new networks.\n", "In the following sections, we will introduce several heuristic concepts\n", "commonly used to design deep networks.\n", "\n", "Progress in this field mirrors that of VLSI (very large scale integration) \n", "in chip design\n", "where engineers moved from placing transistors\n", "to logical elements to logic blocks :cite:`Mead.1980`.\n", "Similarly, the design of neural network architectures\n", "has grown progressively more abstract,\n", "with researchers moving from thinking in terms of\n", "individual neurons to whole layers,\n", "and now to blocks, repeating patterns of layers. A decade later, this has now\n", "progressed to researchers using entire trained models to repurpose them for different, \n", "albeit related, tasks. Such large pretrained models are typically called \n", "*foundation models* :cite:`bommasani2021opportunities`. \n", "\n", "Back to network design. The idea of using blocks first emerged from the\n", "Visual Geometry Group (VGG) at Oxford University,\n", "in their eponymously-named *VGG* network :cite:`Simonyan.Zisserman.2014`.\n", "It is easy to implement these repeated structures in code\n", "with any modern deep learning framework by using loops and subroutines.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "89467a5c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:56:46.393555Z", "iopub.status.busy": "2023-08-18T19:56:46.392607Z", "iopub.status.idle": "2023-08-18T19:56:49.756697Z", "shell.execute_reply": "2023-08-18T19:56:49.755534Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "7c884009", "metadata": {"origin_pos": 6}, "source": ["## (**VGG Blocks**)\n", ":label:`subsec_vgg-blocks`\n", "\n", "The basic building block of CNNs\n", "is a sequence of the following:\n", "(i) a convolutional layer\n", "with padding to maintain the resolution,\n", "(ii) a nonlinearity such as a ReLU,\n", "(iii) a pooling layer such\n", "as max-pooling to reduce the resolution. One of the problems with \n", "this approach is that the spatial resolution decreases quite rapidly. In particular, \n", "this imposes a hard limit of $\\log_2 d$ convolutional layers on the network before all \n", "dimensions ($d$) are used up. For instance, in the case of ImageNet, it would be impossible to have \n", "more than 8 convolutional layers in this way. \n", "\n", "The key idea of :citet:`<PERSON><PERSON>.Zisserman.2014` was to use *multiple* convolutions in between downsampling\n", "via max-pooling in the form of a block. They were primarily interested in whether deep or \n", "wide networks perform better. For instance, the successive application of two $3 \\times 3$ convolutions\n", "touches the same pixels as a single $5 \\times 5$ convolution does. At the same time, the latter uses approximately \n", "as many parameters ($25 \\cdot c^2$) as three $3 \\times 3$ convolutions do ($3 \\cdot 9 \\cdot c^2$). \n", "In a rather detailed analysis they showed that deep and narrow networks significantly outperform their shallow counterparts. This set deep learning on a quest for ever deeper networks with over 100 layers for typical applications.\n", "Stacking $3 \\times 3$ convolutions\n", "has become a gold standard in later deep networks (a design decision only to be revisited recently by \n", ":citet:`liu2022convnet`). Consequently, fast implementations for small convolutions have become a staple on GPUs :cite:`lavin2016fast`. \n", "\n", "Back to VGG: a VGG block consists of a *sequence* of convolutions with $3\\times3$ kernels with padding of 1 \n", "(keeping height and width) followed by a $2 \\times 2$ max-pooling layer with stride of 2\n", "(halving height and width after each block).\n", "In the code below, we define a function called `vgg_block`\n", "to implement one VGG block.\n", "\n", "The function below takes two arguments,\n", "corresponding to the number of convolutional layers `num_convs`\n", "and the number of output channels `num_channels`.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "7a35971a", "metadata": {"attributes": {"classes": [], "id": "", "n": "3"}, "execution": {"iopub.execute_input": "2023-08-18T19:56:49.762934Z", "iopub.status.busy": "2023-08-18T19:56:49.761989Z", "iopub.status.idle": "2023-08-18T19:56:49.770418Z", "shell.execute_reply": "2023-08-18T19:56:49.769006Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["def vgg_block(num_convs, out_channels):\n", "    layers = []\n", "    for _ in range(num_convs):\n", "        layers.append(nn.LazyConv2d(out_channels, kernel_size=3, padding=1))\n", "        layers.append(nn.ReLU())\n", "    layers.append(nn.MaxPool2d(kernel_size=2,stride=2))\n", "    return nn.Sequential(*layers)"]}, {"cell_type": "markdown", "id": "6c267659", "metadata": {"origin_pos": 11}, "source": ["## [**VGG Network**]\n", ":label:`subsec_vgg-network`\n", "\n", "Like AlexNet and LeNet, \n", "the VGG Network can be partitioned into two parts:\n", "the first consisting mostly of convolutional and pooling layers\n", "and the second consisting of fully connected layers that are identical to those in AlexNet. \n", "The key difference is \n", "that the convolutional layers are grouped in nonlinear transformations that \n", "leave the dimensonality unchanged, followed by a resolution-reduction step, as \n", "depicted in :numref:`fig_vgg`. \n", "\n", "![From AlexNet to VGG. The key difference is that VGG consists of blocks of layers, whereas AlexNet's layers are all designed individually.](../img/vgg.svg)\n", ":width:`400px`\n", ":label:`fig_vgg`\n", "\n", "The convolutional part of the network connects several VGG blocks from :numref:`fig_vgg` (also defined in the `vgg_block` function)\n", "in succession. This grouping of convolutions is a pattern that has \n", "remained almost unchanged over the past decade, although the specific choice of \n", "operations has undergone considerable modifications. \n", "The variable `arch` consists of a list of tuples (one per block),\n", "where each contains two values: the number of convolutional layers\n", "and the number of output channels,\n", "which are precisely the arguments required to call\n", "the `vgg_block` function. As such, VGG defines a *family* of networks rather than just \n", "a specific manifestation. To build a specific network we simply iterate over `arch` to compose the blocks.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "35ed8ba4", "metadata": {"attributes": {"classes": [], "id": "", "n": "5"}, "execution": {"iopub.execute_input": "2023-08-18T19:56:49.775273Z", "iopub.status.busy": "2023-08-18T19:56:49.774832Z", "iopub.status.idle": "2023-08-18T19:56:49.783845Z", "shell.execute_reply": "2023-08-18T19:56:49.782642Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["class VGG(d2l.Classifier):\n", "    def __init__(self, arch, lr=0.1, num_classes=10):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        conv_blks = []\n", "        for (num_convs, out_channels) in arch:\n", "            conv_blks.append(vgg_block(num_convs, out_channels))\n", "        self.net = nn.Sequential(\n", "            *conv_blks, nn.<PERSON>(),\n", "            nn.<PERSON><PERSON><PERSON><PERSON><PERSON>(4096), nn.<PERSON><PERSON><PERSON>(), nn.<PERSON><PERSON>(0.5),\n", "            nn.<PERSON><PERSON><PERSON><PERSON><PERSON>(4096), nn.<PERSON><PERSON><PERSON>(), nn.<PERSON><PERSON>(0.5),\n", "            nn.<PERSON>zy<PERSON>inear(num_classes))\n", "        self.net.apply(d2l.init_cnn)"]}, {"cell_type": "markdown", "id": "f4413c3f", "metadata": {"origin_pos": 14}, "source": ["The original VGG network had five convolutional blocks,\n", "among which the first two have one convolutional layer each\n", "and the latter three contain two convolutional layers each.\n", "The first block has 64 output channels\n", "and each subsequent block doubles the number of output channels,\n", "until that number reaches 512.\n", "Since this network uses eight convolutional layers\n", "and three fully connected layers, it is often called VGG-11.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "fc110465", "metadata": {"attributes": {"classes": [], "id": "", "n": "6"}, "execution": {"iopub.execute_input": "2023-08-18T19:56:49.789248Z", "iopub.status.busy": "2023-08-18T19:56:49.788373Z", "iopub.status.idle": "2023-08-18T19:56:51.334656Z", "shell.execute_reply": "2023-08-18T19:56:51.333439Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential output shape:\t torch.Size([1, 64, 112, 112])\n", "Sequential output shape:\t torch.Size([1, 128, 56, 56])\n", "Sequential output shape:\t torch.Size([1, 256, 28, 28])\n", "Sequential output shape:\t torch.Size([1, 512, 14, 14])\n", "Sequential output shape:\t torch.Size([1, 512, 7, 7])\n", "Flatten output shape:\t torch.Size([1, 25088])\n", "Linear output shape:\t torch.Size([1, 4096])\n", "ReLU output shape:\t torch.Size([1, 4096])\n", "Dropout output shape:\t torch.Size([1, 4096])\n", "Linear output shape:\t torch.Size([1, 4096])\n", "ReLU output shape:\t torch.Size([1, 4096])\n", "Dropout output shape:\t torch.Size([1, 4096])\n", "Linear output shape:\t torch.Size([1, 10])\n"]}], "source": ["VGG(arch=((1, 64), (1, 128), (2, 256), (2, 512), (2, 512))).layer_summary(\n", "    (1, 1, 224, 224))"]}, {"cell_type": "markdown", "id": "cb49027b", "metadata": {"origin_pos": 18}, "source": ["As you can see, we halve height and width at each block,\n", "finally reaching a height and width of 7\n", "before flattening the representations\n", "for processing by the fully connected part of the network. \n", ":citet:`<PERSON><PERSON>.Zisserman.2014` described several other variants of VGG. \n", "In fact, it has become the norm to propose *families* of networks with \n", "different speed--accuracy trade-off when introducing a new architecture. \n", "\n", "## Training\n", "\n", "[**Since VGG-11 is computationally more demanding than AlexNet\n", "we construct a network with a smaller number of channels.**]\n", "This is more than sufficient for training on Fashion-MNIST.\n", "The [**model training**] process is similar to that of AlexNet in :numref:`sec_alexnet`. \n", "Again observe the close match between validation and training loss, \n", "suggesting only a small amount of overfitting.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ed532028", "metadata": {"attributes": {"classes": [], "id": "", "n": "8"}, "execution": {"iopub.execute_input": "2023-08-18T19:56:51.339409Z", "iopub.status.busy": "2023-08-18T19:56:51.338575Z", "iopub.status.idle": "2023-08-18T20:01:43.617050Z", "shell.execute_reply": "2023-08-18T20:01:43.615832Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-24T20:26:59.887410</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"maae37de746\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#maae37de746\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#maae37de746\" x=\"69.163125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(65.981875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#maae37de746\" x=\"108.223125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(105.041875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#maae37de746\" x=\"147.283125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(144.101875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#maae37de746\" x=\"186.343125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(183.161875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#maae37de746\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"md20bc75018\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md20bc75018\" x=\"30.103125\" y=\"126.442696\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 130.241915) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#md20bc75018\" x=\"30.103125\" y=\"94.788987\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 98.588206) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#md20bc75018\" x=\"30.103125\" y=\"63.135278\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 66.934497) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#md20bc75018\" x=\"30.103125\" y=\"31.48157\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 35.280788) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 34.954394 13.5 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 49.633125 114.989969 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_15\"/>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 49.633125 114.989969 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 49.633125 110.089949 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 49.633125 114.989969 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 49.633125 110.089949 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 49.633125 114.989969 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 49.633125 110.089949 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 49.633125 110.089949 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "L 171.372901 137.660315 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "L 171.372901 137.660315 \n", "L 181.11708 138.098178 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "L 171.372901 137.660315 \n", "L 181.11708 138.098178 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "L 186.343125 138.664091 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "L 171.372901 137.660315 \n", "L 181.11708 138.098178 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "L 186.343125 138.664091 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "L 186.343125 101.85723 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "L 171.372901 137.660315 \n", "L 181.11708 138.098178 \n", "L 190.861259 138.33179 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "L 186.343125 138.664091 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "L 186.343125 101.85723 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "L 171.372901 137.660315 \n", "L 181.11708 138.098178 \n", "L 190.861259 138.33179 \n", "L 200.605438 139.165115 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "L 186.343125 138.664091 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "L 186.343125 101.85723 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "L 171.372901 137.660315 \n", "L 181.11708 138.098178 \n", "L 190.861259 138.33179 \n", "L 200.605438 139.165115 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "L 186.343125 138.664091 \n", "L 205.873125 138.63883 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "L 186.343125 101.85723 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "L 171.372901 137.660315 \n", "L 181.11708 138.098178 \n", "L 190.861259 138.33179 \n", "L 200.605438 139.165115 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "L 186.343125 138.664091 \n", "L 205.873125 138.63883 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "L 186.343125 101.85723 \n", "L 205.873125 101.963661 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "L 171.372901 137.660315 \n", "L 181.11708 138.098178 \n", "L 190.861259 138.33179 \n", "L 200.605438 139.165115 \n", "L 210.349618 139.5 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "L 186.343125 138.664091 \n", "L 205.873125 138.63883 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "L 186.343125 101.85723 \n", "L 205.873125 101.963661 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "L 171.372901 137.660315 \n", "L 181.11708 138.098178 \n", "L 190.861259 138.33179 \n", "L 200.605438 139.165115 \n", "L 210.349618 139.5 \n", "L 220.093797 139.463059 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "L 186.343125 138.664091 \n", "L 205.873125 138.63883 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "L 186.343125 101.85723 \n", "L 205.873125 101.963661 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "L 171.372901 137.660315 \n", "L 181.11708 138.098178 \n", "L 190.861259 138.33179 \n", "L 200.605438 139.165115 \n", "L 210.349618 139.5 \n", "L 220.093797 139.463059 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "L 186.343125 138.664091 \n", "L 205.873125 138.63883 \n", "L 225.403125 139.154681 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "L 186.343125 101.85723 \n", "L 205.873125 101.963661 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 87.93922 \n", "L 54.442752 114.676 \n", "L 64.186931 121.878179 \n", "L 73.93111 126.292665 \n", "L 83.675289 128.912754 \n", "L 93.419468 130.481862 \n", "L 103.163647 132.000401 \n", "L 112.907826 133.480664 \n", "L 122.652006 133.831692 \n", "L 132.396185 134.680114 \n", "L 142.140364 135.910349 \n", "L 151.884543 136.50721 \n", "L 161.628722 136.809111 \n", "L 171.372901 137.660315 \n", "L 181.11708 138.098178 \n", "L 190.861259 138.33179 \n", "L 200.605438 139.165115 \n", "L 210.349618 139.5 \n", "L 220.093797 139.463059 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 49.633125 114.989969 \n", "L 69.163125 128.156215 \n", "L 88.693125 131.793416 \n", "L 108.223125 134.298562 \n", "L 127.753125 134.916663 \n", "L 147.283125 136.541905 \n", "L 166.813125 137.827173 \n", "L 186.343125 138.664091 \n", "L 205.873125 138.63883 \n", "L 225.403125 139.154681 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 49.633125 110.089949 \n", "L 69.163125 105.582301 \n", "L 88.693125 104.430346 \n", "L 108.223125 103.447429 \n", "L 127.753125 103.203264 \n", "L 147.283125 102.827627 \n", "L 166.813125 102.15148 \n", "L 186.343125 101.85723 \n", "L 205.873125 101.963661 \n", "L 225.403125 101.757061 \n", "\" clip-path=\"url(#p0770083776)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 60.06875 \n", "L 218.403125 60.06875 \n", "Q 220.403125 60.06875 220.403125 58.06875 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 138.8125 12.2 \n", "Q 136.8125 12.2 136.8125 14.2 \n", "L 136.8125 58.06875 \n", "Q 136.8125 60.06875 138.8125 60.06875 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_127\">\n", "     <path d=\"M 140.8125 20.298438 \n", "L 150.8125 20.298438 \n", "L 160.8125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_128\">\n", "     <path d=\"M 140.8125 35.254688 \n", "L 150.8125 35.254688 \n", "L 160.8125 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_129\">\n", "     <path d=\"M 140.8125 50.210938 \n", "L 150.8125 50.210938 \n", "L 160.8125 50.210938 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 53.710938) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p0770083776\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = VGG(arch=((1, 16), (1, 32), (2, 64), (2, 128), (2, 128)), lr=0.01)\n", "trainer = d2l.Trainer(max_epochs=10, num_gpus=1)\n", "data = d2l.FashionMNIST(batch_size=128, resize=(224, 224))\n", "model.apply_init([next(iter(data.get_dataloader(True)))[0]], d2l.init_cnn)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "157480ef", "metadata": {"origin_pos": 21}, "source": ["## Summary\n", "\n", "One might argue that VGG is the first truly modern convolutional neural network. While AlexNet introduced many of the components of what make deep learning effective at scale, it is VGG that arguably introduced key properties such as blocks of multiple convolutions and a preference for deep and narrow networks. It is also the first network that is actually an entire family of similarly parametrized models, giving the practitioner ample trade-off between complexity and speed. This is also the place where modern deep learning frameworks shine. It is no longer necessary to generate XML configuration files to specify a network but rather, to assemble said networks through simple Python code. \n", "\n", "More recently ParNet :cite:`Goyal.Bochkovskiy.Deng.ea.2021` demonstrated that it is possible to achieve competitive performance using a much more shallow architecture through a large number of parallel computations. This is an exciting development and there is hope that it will influence architecture designs in the future. For the remainder of the chapter, though, we will follow the path of scientific progress over the past decade. \n", "\n", "## Exercises\n", "\n", "\n", "1. Compared with AlexNet, VGG is much slower in terms of computation, and it also needs more GPU memory. \n", "    1. Compare the number of parameters needed for AlexNet and VGG.\n", "    1. Compare the number of floating point operations used in the convolutional layers and in the fully connected layers. \n", "    1. How could you reduce the computational cost created by the fully connected layers?\n", "1. When displaying the dimensions associated with the various layers of the network, we only see the information associated with eight blocks (plus some auxiliary transforms), even though the network has 11 layers. Where did the remaining three layers go?\n", "1. Use Table 1 in the VGG paper :cite:`<PERSON>yan.Zisserman.2014` to construct other common models, such as VGG-16 or VGG-19.\n", "1. Upsampling the resolution in Fashion-MNIST eight-fold from $28 \\times 28$ to $224 \\times 224$ dimensions is very wasteful. Try modifying the network architecture and resolution conversion, e.g., to 56 or to 84 dimensions for its input instead. Can you do so without reducing the accuracy of the network? Consult the VGG paper :cite:`<PERSON>yan.Zisserman.2014` for ideas on adding more nonlinearities prior to downsampling.\n"]}, {"cell_type": "markdown", "id": "dd565a2f", "metadata": {"origin_pos": 23, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/78)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON> (aideep)", "language": "python", "name": "<PERSON><PERSON>"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
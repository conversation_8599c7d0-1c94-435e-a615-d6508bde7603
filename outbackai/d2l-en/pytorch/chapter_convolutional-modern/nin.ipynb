{"cells": [{"cell_type": "markdown", "id": "4098e4aa", "metadata": {"origin_pos": 1}, "source": ["# Network in Network (NiN)\n", ":label:`sec_nin`\n", "\n", "LeNet, AlexNet, and VGG all share a common design pattern:\n", "extract features exploiting *spatial* structure\n", "via a sequence of convolutions and pooling layers\n", "and post-process the representations via fully connected layers.\n", "The improvements upon LeNet by AlexNet and VGG mainly lie\n", "in how these later networks widen and deepen these two modules.\n", "\n", "This design poses two major challenges.\n", "First, the fully connected layers at the end\n", "of the architecture consume tremendous numbers of parameters. For instance, even a simple\n", "model such as VGG-11 requires a monstrous matrix, occupying almost\n", "400MB of RAM in single precision (FP32). This is a significant impediment to computation, in particular on\n", "mobile and embedded devices. After all, even high-end mobile phones sport no more than 8GB of RAM. At the time VGG was invented, this was an order of magnitude less (the iPhone 4S had 512MB). As such, it would have been difficult to justify spending the majority of memory on an image classifier. \n", "\n", "Second, it is equally impossible to add fully connected layers\n", "earlier in the network to increase the degree of nonlinearity: doing so would destroy the\n", "spatial structure and require potentially even more memory.\n", "\n", "The *network in network* (*NiN*) blocks :cite:`Lin.Chen.Yan.2013` offer an alternative,\n", "capable of solving both problems in one simple strategy.\n", "They were proposed based on a very simple insight: (i) use $1 \\times 1$ convolutions to add\n", "local nonlinearities across the channel activations and (ii) use global average pooling to integrate\n", "across all locations in the last representation layer. Note that global average pooling would not\n", "be effective, were it not for the added nonlinearities. Let's dive into this in detail.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d11675e1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:01:36.760551Z", "iopub.status.busy": "2023-08-18T20:01:36.760234Z", "iopub.status.idle": "2023-08-18T20:01:39.896403Z", "shell.execute_reply": "2023-08-18T20:01:39.895388Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "95132206", "metadata": {"origin_pos": 6}, "source": ["## (**NiN <PERSON>s**)\n", "\n", "Recall :numref:`subsec_1x1`. In it we said that the inputs and outputs of convolutional layers\n", "consist of four-dimensional tensors with axes\n", "corresponding to the example, channel, height, and width.\n", "Also recall that the inputs and outputs of fully connected layers\n", "are typically two-dimensional tensors corresponding to the example and feature.\n", "The idea behind NiN is to apply a fully connected layer\n", "at each pixel location (for each height and width).\n", "The resulting $1 \\times 1$ convolution can be thought of as\n", "a fully connected layer acting independently on each pixel location.\n", "\n", ":numref:`fig_nin` illustrates the main structural\n", "differences between VGG and NiN, and their blocks.\n", "Note both the difference in the NiN blocks (the initial convolution is followed by $1 \\times 1$ convolutions, whereas VGG retains $3 \\times 3$ convolutions) and at the end where we no longer require a giant fully connected layer.\n", "\n", "![Comparing the architectures of VGG and NiN, and of their blocks.](../img/nin.svg)\n", ":width:`600px`\n", ":label:`fig_nin`\n"]}, {"cell_type": "code", "execution_count": 2, "id": "538cefc3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:01:39.901279Z", "iopub.status.busy": "2023-08-18T20:01:39.900852Z", "iopub.status.idle": "2023-08-18T20:01:39.905954Z", "shell.execute_reply": "2023-08-18T20:01:39.905166Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["def nin_block(out_channels, kernel_size, strides, padding):\n", "    return nn.Sequential(\n", "        nn.LazyConv2d(out_channels, kernel_size, strides, padding), nn.ReLU(),\n", "        nn.LazyConv2d(out_channels, kernel_size=1), nn.ReLU(),\n", "        nn.LazyConv2d(out_channels, kernel_size=1), nn.ReLU())"]}, {"cell_type": "markdown", "id": "5c944308", "metadata": {"origin_pos": 11}, "source": ["## [**NiN Model**]\n", "\n", "NiN uses the same initial convolution sizes as AlexNet (it was proposed shortly thereafter).\n", "The kernel sizes are $11\\times 11$, $5\\times 5$, and $3\\times 3$, respectively,\n", "and the numbers of output channels match those of AlexNet. Each NiN block is followed by a max-pooling layer\n", "with a stride of 2 and a window shape of $3\\times 3$.\n", "\n", "The second significant difference between NiN and both AlexNet and VGG\n", "is that NiN avoids fully connected layers altogether.\n", "Instead, NiN uses a NiN block with a number of output channels equal to the number of label classes, followed by a *global* average pooling layer,\n", "yielding a vector of logits.\n", "This design significantly reduces the number of required model parameters, albeit at the expense of a potential increase in training time.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "200542dc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:01:39.910042Z", "iopub.status.busy": "2023-08-18T20:01:39.909747Z", "iopub.status.idle": "2023-08-18T20:01:39.916497Z", "shell.execute_reply": "2023-08-18T20:01:39.915589Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["class NiN(d2l.Classifier):\n", "    def __init__(self, lr=0.1, num_classes=10):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        self.net = nn.Sequential(\n", "            nin_block(96, kernel_size=11, strides=4, padding=0),\n", "            nn.<PERSON><PERSON>ool2d(3, stride=2),\n", "            nin_block(256, kernel_size=5, strides=1, padding=2),\n", "            nn.<PERSON><PERSON>ool2d(3, stride=2),\n", "            nin_block(384, kernel_size=3, strides=1, padding=1),\n", "            nn.<PERSON><PERSON>ool2d(3, stride=2),\n", "            nn.Dropout(0.5),\n", "            nin_block(num_classes, kernel_size=3, strides=1, padding=1),\n", "            nn.AdaptiveAvgPool2d((1, 1)),\n", "            nn.<PERSON>())\n", "        self.net.apply(d2l.init_cnn)"]}, {"cell_type": "markdown", "id": "f7562f54", "metadata": {"origin_pos": 14}, "source": ["We create a data example to see [**the output shape of each block**].\n"]}, {"cell_type": "code", "execution_count": 4, "id": "c0a1fe2c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:01:39.919706Z", "iopub.status.busy": "2023-08-18T20:01:39.919415Z", "iopub.status.idle": "2023-08-18T20:01:40.016875Z", "shell.execute_reply": "2023-08-18T20:01:40.015009Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential output shape:\t torch.<PERSON><PERSON>([1, 96, 54, 54])\n", "MaxPool2d output shape:\t torch.Size([1, 96, 26, 26])\n", "Sequential output shape:\t torch.Size([1, 256, 26, 26])\n", "MaxPool2d output shape:\t torch.Size([1, 256, 12, 12])\n", "Sequential output shape:\t torch.Size([1, 384, 12, 12])\n", "MaxPool2d output shape:\t torch.Size([1, 384, 5, 5])\n", "Dropout output shape:\t torch.Size([1, 384, 5, 5])\n", "Sequential output shape:\t torch.Size([1, 10, 5, 5])\n", "AdaptiveAvgPool2d output shape:\t torch.Size([1, 10, 1, 1])\n", "Flatten output shape:\t torch.Size([1, 10])\n"]}], "source": ["NiN().layer_summary((1, 1, 224, 224))"]}, {"cell_type": "markdown", "id": "b4ac0209", "metadata": {"origin_pos": 18}, "source": ["## [**Training**]\n", "\n", "As before we use Fashion-MNIST to train the model using the same \n", "optimizer that we used for AlexNet and VGG.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "111948ca", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:01:40.021257Z", "iopub.status.busy": "2023-08-18T20:01:40.020900Z", "iopub.status.idle": "2023-08-18T20:05:45.250434Z", "shell.execute_reply": "2023-08-18T20:05:45.248575Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T20:05:45.077717</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m5841fb9f64\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5841fb9f64\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m5841fb9f64\" x=\"69.163125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(65.981875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m5841fb9f64\" x=\"108.223125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(105.041875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m5841fb9f64\" x=\"147.283125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(144.101875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m5841fb9f64\" x=\"186.343125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(183.161875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m5841fb9f64\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m5b729ac6c4\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5b729ac6c4\" x=\"30.103125\" y=\"121.030627\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 124.829846) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m5b729ac6c4\" x=\"30.103125\" y=\"91.272453\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 95.071672) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m5b729ac6c4\" x=\"30.103125\" y=\"61.514279\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 65.313498) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m5b729ac6c4\" x=\"30.103125\" y=\"31.756106\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 35.555324) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 34.954394 13.5 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 49.633125 14.300545 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_15\"/>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 49.633125 14.300545 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 49.633125 139.5 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 49.633125 14.300545 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 49.633125 139.5 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 49.633125 14.300545 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 49.633125 139.5 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 49.633125 139.5 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "L 171.372901 124.173214 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "L 171.372901 124.173214 \n", "L 181.11708 124.927889 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "L 171.372901 124.173214 \n", "L 181.11708 124.927889 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "L 186.343125 126.993604 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "L 171.372901 124.173214 \n", "L 181.11708 124.927889 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "L 186.343125 126.993604 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "L 186.343125 100.000968 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "L 171.372901 124.173214 \n", "L 181.11708 124.927889 \n", "L 190.861259 126.078432 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "L 186.343125 126.993604 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "L 186.343125 100.000968 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "L 171.372901 124.173214 \n", "L 181.11708 124.927889 \n", "L 190.861259 126.078432 \n", "L 200.605438 126.543974 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "L 186.343125 126.993604 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "L 186.343125 100.000968 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "L 171.372901 124.173214 \n", "L 181.11708 124.927889 \n", "L 190.861259 126.078432 \n", "L 200.605438 126.543974 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "L 186.343125 126.993604 \n", "L 205.873125 124.961804 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "L 186.343125 100.000968 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "L 171.372901 124.173214 \n", "L 181.11708 124.927889 \n", "L 190.861259 126.078432 \n", "L 200.605438 126.543974 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "L 186.343125 126.993604 \n", "L 205.873125 124.961804 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "L 186.343125 100.000968 \n", "L 205.873125 100.618968 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "L 171.372901 124.173214 \n", "L 181.11708 124.927889 \n", "L 190.861259 126.078432 \n", "L 200.605438 126.543974 \n", "L 210.349618 127.928331 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "L 186.343125 126.993604 \n", "L 205.873125 124.961804 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "L 186.343125 100.000968 \n", "L 205.873125 100.618968 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "L 171.372901 124.173214 \n", "L 181.11708 124.927889 \n", "L 190.861259 126.078432 \n", "L 200.605438 126.543974 \n", "L 210.349618 127.928331 \n", "L 220.093797 127.550269 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "L 186.343125 126.993604 \n", "L 205.873125 124.961804 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "L 186.343125 100.000968 \n", "L 205.873125 100.618968 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "L 171.372901 124.173214 \n", "L 181.11708 124.927889 \n", "L 190.861259 126.078432 \n", "L 200.605438 126.543974 \n", "L 210.349618 127.928331 \n", "L 220.093797 127.550269 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "L 186.343125 126.993604 \n", "L 205.873125 124.961804 \n", "L 225.403125 127.787915 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "L 186.343125 100.000968 \n", "L 205.873125 100.618968 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.868319 \n", "L 54.442752 19.067692 \n", "L 64.186931 47.147834 \n", "L 73.93111 69.062972 \n", "L 83.675289 80.369094 \n", "L 93.419468 99.440551 \n", "L 103.163647 105.321997 \n", "L 112.907826 110.679047 \n", "L 122.652006 113.826477 \n", "L 132.396185 115.921938 \n", "L 142.140364 118.836319 \n", "L 151.884543 121.032111 \n", "L 161.628722 122.657064 \n", "L 171.372901 124.173214 \n", "L 181.11708 124.927889 \n", "L 190.861259 126.078432 \n", "L 200.605438 126.543974 \n", "L 210.349618 127.928331 \n", "L 220.093797 127.550269 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 49.633125 14.300545 \n", "L 69.163125 62.966599 \n", "L 88.693125 96.722288 \n", "L 108.223125 109.790341 \n", "L 127.753125 74.840754 \n", "L 147.283125 119.861155 \n", "L 166.813125 120.95878 \n", "L 186.343125 126.993604 \n", "L 205.873125 124.961804 \n", "L 225.403125 127.787915 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 123.902856 \n", "L 88.693125 111.272112 \n", "L 108.223125 105.745426 \n", "L 127.753125 113.579312 \n", "L 147.283125 102.390568 \n", "L 166.813125 101.766683 \n", "L 186.343125 100.000968 \n", "L 205.873125 100.618968 \n", "L 225.403125 99.329997 \n", "\" clip-path=\"url(#pcbb690d5bc)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 60.06875 \n", "L 218.403125 60.06875 \n", "Q 220.403125 60.06875 220.403125 58.06875 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 138.8125 12.2 \n", "Q 136.8125 12.2 136.8125 14.2 \n", "L 136.8125 58.06875 \n", "Q 136.8125 60.06875 138.8125 60.06875 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_127\">\n", "     <path d=\"M 140.8125 20.298438 \n", "L 150.8125 20.298438 \n", "L 160.8125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_128\">\n", "     <path d=\"M 140.8125 35.254688 \n", "L 150.8125 35.254688 \n", "L 160.8125 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_129\">\n", "     <path d=\"M 140.8125 50.210938 \n", "L 150.8125 50.210938 \n", "L 160.8125 50.210938 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 53.710938) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pcbb690d5bc\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = NiN(lr=0.05)\n", "trainer = d2l.Trainer(max_epochs=10, num_gpus=1)\n", "data = d2l.FashionMNIST(batch_size=128, resize=(224, 224))\n", "model.apply_init([next(iter(data.get_dataloader(True)))[0]], d2l.init_cnn)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "182ce04f", "metadata": {"origin_pos": 21}, "source": ["## Summary\n", "\n", "NiN has dramatically fewer parameters than AlexNet and VGG. This stems primarily from the fact that it needs no giant fully connected layers. Instead, it uses global average pooling to aggregate across all image locations after the last stage of the network body. This obviates the need for expensive (learned) reduction operations and replaces them by a simple average. What surprised researchers at the time was the fact that this averaging operation did not harm accuracy. Note that averaging across a low-resolution representation (with many channels) also adds to the amount of translation invariance that the network can handle. \n", "\n", "Choosing fewer convolutions with wide kernels and replacing them by $1 \\times 1$ convolutions aids the quest for fewer parameters further. It can cater for a significant amount of nonlinearity across channels within any given location. Both $1 \\times 1$ convolutions and global average pooling significantly influenced subsequent CNN designs. \n", "\n", "## Exercises\n", "\n", "1. Why are there two $1\\times 1$ convolutional layers per NiN block? Increase their number to three. Reduce their number to one. What changes?\n", "1. What changes if you replace the $1 \\times 1$ convolutions by $3 \\times 3$ convolutions? \n", "1. What happens if you replace the global average pooling by a fully connected layer (speed, accuracy, number of parameters)?\n", "1. Calculate the resource usage for NiN.\n", "    1. What is the number of parameters?\n", "    1. What is the amount of computation?\n", "    1. What is the amount of memory needed during training?\n", "    1. What is the amount of memory needed during prediction?\n", "1. What are possible problems with reducing the $384 \\times 5 \\times 5$ representation to a $10 \\times 5 \\times 5$ representation in one step?\n", "1. Use the structural design decisions in VGG that led to VGG-11, VGG-16, and VGG-19 to design a family of NiN-like networks.\n"]}, {"cell_type": "markdown", "id": "b01b3354", "metadata": {"origin_pos": 23, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/80)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
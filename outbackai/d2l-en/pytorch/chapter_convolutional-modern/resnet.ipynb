{"cells": [{"cell_type": "markdown", "id": "e11b4915", "metadata": {"origin_pos": 1}, "source": ["# Residual Networks (ResNet) and ResNeXt\n", ":label:`sec_resnet`\n", "\n", "As we design ever deeper networks it becomes imperative to understand how adding layers can increase the complexity and expressiveness of the network.\n", "Even more important is the ability to design networks where adding layers makes networks strictly more expressive rather than just different.\n", "To make some progress we need a bit of mathematics.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "d6e5d075", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T13:07:49.880268Z", "start_time": "2025-03-29T13:07:49.877993Z"}, "execution": {"iopub.execute_input": "2023-08-18T19:50:50.915858Z", "iopub.status.busy": "2023-08-18T19:50:50.915085Z", "iopub.status.idle": "2023-08-18T19:50:53.897064Z", "shell.execute_reply": "2023-08-18T19:50:53.895755Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "f46d0878", "metadata": {"origin_pos": 6}, "source": ["## Function Classes\n", "\n", "Consider $\\mathcal{F}$, the class of functions that a specific network architecture (together with learning rates and other hyperparameter settings) can reach.\n", "That is, for all $f \\in \\mathcal{F}$ there exists some set of parameters (e.g., weights and biases) that can be obtained through training on a suitable dataset.\n", "Let's assume that $f^*$ is the \"truth\" function that we really would like to find.\n", "If it is in $\\mathcal{F}$, we are in good shape but typically we will not be quite so lucky.\n", "Instead, we will try to find some $f^*_\\mathcal{F}$ which is our best bet within $\\mathcal{F}$.\n", "For instance,\n", "given a dataset with features $\\mathbf{X}$\n", "and labels $\\mathbf{y}$,\n", "we might try finding it by solving the following optimization problem:\n", "\n", "$$f^*_\\mathcal{F} \\stackrel{\\textrm{def}}{=} \\mathop{\\mathrm{argmin}}_f L(\\mathbf{X}, \\mathbf{y}, f) \\textrm{ subject to } f \\in \\mathcal{F}.$$\n", "\n", "We know that regularization :cite:`tikhonov1977solutions,morozov2012methods` may control complexity of $\\mathcal{F}$\n", "and achieve consistency, so a larger size of training data\n", "generally leads to better $f^*_\\mathcal{F}$.\n", "It is only reasonable to assume that if we design a different and more powerful architecture $\\mathcal{F}'$ we should arrive at a better outcome. In other words, we would expect that $f^*_{\\mathcal{F}'}$ is \"better\" than $f^*_{\\mathcal{F}}$. However, if $\\mathcal{F} \\not\\subseteq \\mathcal{F}'$ there is no guarantee that this should even happen. In fact, $f^*_{\\mathcal{F}'}$ might well be worse.\n", "As illustrated by :numref:`fig_functionclasses`,\n", "for non-nested function classes, a larger function class does not always move closer to the \"truth\" function $f^*$. For instance,\n", "on the left of :numref:`fig_functionclasses`,\n", "though $\\mathcal{F}_3$ is closer to $f^*$ than $\\mathcal{F}_1$, $\\mathcal{F}_6$ moves away and there is no guarantee that further increasing the complexity can reduce the distance from $f^*$.\n", "With nested function classes\n", "where $\\mathcal{F}_1 \\subseteq \\cdots \\subseteq \\mathcal{F}_6$\n", "on the right of :numref:`fig_functionclasses`,\n", "we can avoid the aforementioned issue from the non-nested function classes.\n", "\n", "\n", "![For non-nested function classes, a larger (indicated by area) function class does not guarantee we will get closer to the \"truth\" function ($\\mathit{f}^*$). This does not happen in nested function classes.](../img/functionclasses.svg)\n", ":label:`fig_functionclasses`\n", "\n", "Thus,\n", "only if larger function classes contain the smaller ones are we guaranteed that increasing them strictly increases the expressive power of the network.\n", "For deep neural networks,\n", "if we can\n", "train the newly-added layer into an identity function $f(\\mathbf{x}) = \\mathbf{x}$, the new model will be as effective as the original model. As the new model may get a better solution to fit the training dataset, the added layer might make it easier to reduce training errors.\n", "\n", "This is the question that :citet:`<PERSON><PERSON>.Ren.ea.2016` considered when working on very deep computer vision models.\n", "At the heart of their proposed *residual network* (*ResNet*) is the idea that every additional layer should\n", "more easily\n", "contain the identity function as one of its elements.\n", "These considerations are rather profound but they led to a surprisingly simple\n", "solution, a *residual block*.\n", "With it, ResNet won the ImageNet Large Scale Visual Recognition Challenge in 2015. The design had a profound influence on how to\n", "build deep neural networks. For instance, residual blocks have been added to recurrent networks :cite:`prakash2016neural,kim2017residual`. Likewise, Transformers :cite:`Vaswani.Shazeer.Parmar.ea.2017` use them to stack many layers of networks efficiently. It is also used in graph neural networks :cite:`Kipf.Welling.2016` and, as a basic concept, it has been used extensively in computer vision :cite:`Redmon.Farhadi.2018,Ren.He.Girshick.ea.2015`. \n", "Note that residual networks are predated by highway networks :cite:`srivastava2015highway` that share some of the motivation, albeit without the elegant parametrization around the identity function.\n", "\n", "\n", "## (**Residual Blocks**)\n", ":label:`subsec_residual-blks`\n", "\n", "Let's focus on a local part of a neural network, as depicted in :numref:`fig_residual_block`. Denote the input by $\\mathbf{x}$.\n", "We assume that $f(\\mathbf{x})$, the desired underlying mapping we want to obtain by learning, is to be used as input to the activation function on the top.\n", "On the left,\n", "the portion within the dotted-line box\n", "must directly learn $f(\\mathbf{x})$.\n", "On the right,\n", "the portion within the dotted-line box\n", "needs to\n", "learn the *residual mapping* $g(\\mathbf{x}) = f(\\mathbf{x}) - \\mathbf{x}$,\n", "which is how the residual block derives its name.\n", "If the identity mapping $f(\\mathbf{x}) = \\mathbf{x}$ is the desired underlying mapping,\n", "the residual mapping amounts to $g(\\mathbf{x}) = 0$ and it is thus easier to learn:\n", "we only need to push the weights and biases\n", "of the\n", "upper weight layer (e.g., fully connected layer and convolutional layer)\n", "within the dotted-line box\n", "to zero.\n", "The right figure illustrates the *residual block* of ResNet,\n", "where the solid line carrying the layer input\n", "$\\mathbf{x}$ to the addition operator\n", "is called a *residual connection* (or *shortcut connection*).\n", "With residual blocks, inputs can\n", "forward propagate faster through the residual connections across layers.\n", "In fact,\n", "the residual block\n", "can be thought of as\n", "a special case of the multi-branch Inception block:\n", "it has two branches\n", "one of which is the identity mapping.\n", "\n", "![In a regular block (left), the portion within the dotted-line box must directly learn the mapping $\\mathit{f}(\\mathbf{x})$. In a residual block (right), the portion within the dotted-line box needs to learn the residual mapping $\\mathit{g}(\\mathbf{x}) = \\mathit{f}(\\mathbf{x}) - \\mathbf{x}$, making the identity mapping $\\mathit{f}(\\mathbf{x}) = \\mathbf{x}$ easier to learn.](../img/residual-block.svg)\n", ":label:`fig_residual_block`\n", "\n", "\n", "ResNet has VGG's full $3\\times 3$ convolutional layer design. The residual block has two $3\\times 3$ convolutional layers with the same number of output channels. Each convolutional layer is followed by a batch normalization layer and a ReLU activation function. Then, we skip these two convolution operations and add the input directly before the final ReLU activation function.\n", "This kind of design requires that the output of the two convolutional layers has to be of the same shape as the input, so that they can be added together. If we want to change the number of channels, we need to introduce an additional $1\\times 1$ convolutional layer to transform the input into the desired shape for the addition operation. Let's have a look at the code below.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "35fa7497", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T13:07:49.895671Z", "start_time": "2025-03-29T13:07:49.892174Z"}, "execution": {"iopub.execute_input": "2023-08-18T19:50:53.901535Z", "iopub.status.busy": "2023-08-18T19:50:53.900638Z", "iopub.status.idle": "2023-08-18T19:50:53.909065Z", "shell.execute_reply": "2023-08-18T19:50:53.907927Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["class Residual(nn.Mo<PERSON>le):  #@save\n", "    \"\"\"The Residual block of ResNet models.\"\"\"\n", "    def __init__(self, num_channels, use_1x1conv=False, strides=1):\n", "        super().__init__()\n", "        self.conv1 = nn.LazyConv2d(num_channels, kernel_size=3, padding=1,\n", "                                   stride=strides)\n", "        self.conv2 = nn.LazyConv2d(num_channels, kernel_size=3, padding=1)\n", "        if use_1x1conv:\n", "            self.conv3 = nn.LazyConv2d(num_channels, kernel_size=1,\n", "                                       stride=strides)\n", "        else:\n", "            self.conv3 = None\n", "        self.bn1 = nn.LazyBatchNorm2d()\n", "        self.bn2 = nn.LazyBatchNorm2d()\n", "\n", "    def forward(self, X):\n", "        Y = F.relu(self.bn1(self.conv1(X)))\n", "        Y = self.bn2(self.conv2(Y))\n", "        if self.conv3:\n", "            X = self.conv3(X)\n", "        Y += X\n", "        return <PERSON><PERSON>re<PERSON>(Y)"]}, {"cell_type": "markdown", "id": "d5a254f6", "metadata": {"origin_pos": 11}, "source": ["This code generates two types of networks: one where we add the input to the output before applying the ReLU nonlinearity whenever `use_1x1conv=False`; and one where we adjust channels and resolution by means of a $1 \\times 1$ convolution before adding. :numref:`fig_resnet_block` illustrates this.\n", "\n", "![ResNet block with and without $1 \\times 1$ convolution, which transforms the input into the desired shape for the addition operation.](../img/resnet-block.svg)\n", ":label:`fig_resnet_block`\n", "\n", "Now let's look at [**a situation where the input and output are of the same shape**], where $1 \\times 1$ convolution is not needed.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "2057b8bc", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T13:07:49.909550Z", "start_time": "2025-03-29T13:07:49.903945Z"}, "execution": {"iopub.execute_input": "2023-08-18T19:50:53.913050Z", "iopub.status.busy": "2023-08-18T19:50:53.912286Z", "iopub.status.idle": "2023-08-18T19:50:53.955152Z", "shell.execute_reply": "2023-08-18T19:50:53.953792Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([4, 3, 6, 6])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["blk = Residual(3)\n", "X = torch.randn(4, 3, 6, 6)\n", "blk(X).shape"]}, {"cell_type": "markdown", "id": "81b3b8b1", "metadata": {"origin_pos": 15}, "source": ["We also have the option to [**halve the output height and width while increasing the number of output channels**].\n", "In this case we use $1 \\times 1$ convolutions via `use_1x1conv=True`. This comes in handy at the beginning of each ResNet block to reduce the spatial dimensionality via `strides=2`.\n"]}, {"cell_type": "code", "execution_count": 14, "id": "341c1c55", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T13:07:49.929171Z", "start_time": "2025-03-29T13:07:49.923570Z"}, "execution": {"iopub.execute_input": "2023-08-18T19:50:53.958860Z", "iopub.status.busy": "2023-08-18T19:50:53.958579Z", "iopub.status.idle": "2023-08-18T19:50:53.983195Z", "shell.execute_reply": "2023-08-18T19:50:53.981643Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([4, 6, 3, 3])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["blk = Residual(6, use_1x1conv=True, strides=2)\n", "blk(X).shape"]}, {"cell_type": "markdown", "id": "735745a2", "metadata": {"origin_pos": 18}, "source": ["## [**ResNet Model**]\n", "\n", "The first two layers of ResNet are the same as those of the GoogLeNet we described before: the $7\\times 7$ convolutional layer with 64 output channels and a stride of 2 is followed by the $3\\times 3$ max-pooling layer with a stride of 2. The difference is the batch normalization layer added after each convolutional layer in ResNet.\n"]}, {"cell_type": "code", "execution_count": 15, "id": "393dd8de", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T13:07:49.947660Z", "start_time": "2025-03-29T13:07:49.944927Z"}, "execution": {"iopub.execute_input": "2023-08-18T19:50:53.987745Z", "iopub.status.busy": "2023-08-18T19:50:53.986818Z", "iopub.status.idle": "2023-08-18T19:50:53.994446Z", "shell.execute_reply": "2023-08-18T19:50:53.993110Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [], "source": ["class ResNet(d2l.Classifier):\n", "    def b1(self):\n", "        return nn.Sequential(\n", "            nn.LazyConv2d(64, kernel_size=7, stride=2, padding=3),\n", "            nn.LazyBatchNorm2d(), nn.ReLU(),\n", "            nn.MaxPool2d(kernel_size=3, stride=2, padding=1))"]}, {"cell_type": "markdown", "id": "537173c6", "metadata": {"origin_pos": 21}, "source": ["GoogLeNet uses four modules made up of Inception blocks.\n", "However, ResNet uses four modules made up of residual blocks, each of which uses several residual blocks with the same number of output channels.\n", "The number of channels in the first module is the same as the number of input channels. Since a max-pooling layer with a stride of 2 has already been used, it is not necessary to reduce the height and width. In the first residual block for each of the subsequent modules, the number of channels is doubled compared with that of the previous module, and the height and width are halved.\n"]}, {"cell_type": "code", "execution_count": 16, "id": "4d92b69c", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T13:07:49.966772Z", "start_time": "2025-03-29T13:07:49.963992Z"}, "execution": {"iopub.execute_input": "2023-08-18T19:50:53.998963Z", "iopub.status.busy": "2023-08-18T19:50:53.997879Z", "iopub.status.idle": "2023-08-18T19:50:54.005699Z", "shell.execute_reply": "2023-08-18T19:50:54.004419Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(ResNet)\n", "def block(self, num_residuals, num_channels, first_block=False):\n", "    blk = []\n", "    for i in range(num_residuals):\n", "        if i == 0 and not first_block:\n", "            blk.append(Residual(num_channels, use_1x1conv=True, strides=2))\n", "        else:\n", "            blk.append(Residual(num_channels))\n", "    return nn.Sequential(*blk)"]}, {"cell_type": "markdown", "id": "582781fd", "metadata": {"origin_pos": 26}, "source": ["Then, we add all the modules to ResNet. Here, two residual blocks are used for each module. Lastly, just like GoogLeNet, we add a global average pooling layer, followed by the fully connected layer output.\n"]}, {"cell_type": "code", "execution_count": 17, "id": "0019ee3f", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T13:07:49.984548Z", "start_time": "2025-03-29T13:07:49.981458Z"}, "execution": {"iopub.execute_input": "2023-08-18T19:50:54.010309Z", "iopub.status.busy": "2023-08-18T19:50:54.009135Z", "iopub.status.idle": "2023-08-18T19:50:54.017906Z", "shell.execute_reply": "2023-08-18T19:50:54.016848Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(ResNet)\n", "def __init__(self, arch, lr=0.1, num_classes=10):\n", "    super(ResNet, self).__init__()\n", "    self.save_hyperparameters()\n", "    self.net = nn.Sequential(self.b1())\n", "    for i, b in enumerate(arch):\n", "        self.net.add_module(f'b{i+2}', self.block(*b, first_block=(i==0)))\n", "    self.net.add_module('last', nn.Sequential(\n", "        nn.AdaptiveAvgPool2d((1, 1)), nn.<PERSON><PERSON>(),\n", "        nn.<PERSON><PERSON><PERSON>inear(num_classes)))\n", "    self.net.apply(d2l.init_cnn)"]}, {"cell_type": "markdown", "id": "91a9c2ac", "metadata": {"origin_pos": 29}, "source": ["There are four convolutional layers in each module (excluding the $1\\times 1$ convolutional layer). Together with the first $7\\times 7$ convolutional layer and the final fully connected layer, there are 18 layers in total. Therefore, this model is commonly known as ResNet-18.\n", "By configuring different numbers of channels and residual blocks in the module, we can create different ResNet models, such as the deeper 152-layer ResNet-152. Although the main architecture of ResNet is similar to that of GoogLeNet, ResNet's structure is simpler and easier to modify. All these factors have resulted in the rapid and widespread use of ResNet. :numref:`fig_resnet18` depicts the full ResNet-18.\n", "\n", "![The ResNet-18 architecture.](../img/resnet18-90.svg)\n", ":label:`fig_resnet18`\n", "\n", "Before training ResNet, let's [**observe how the input shape changes across different modules in ResNet**]. As in all the previous architectures, the resolution decreases while the number of channels increases up until the point where a global average pooling layer aggregates all features.\n"]}, {"cell_type": "code", "execution_count": 18, "id": "61e60e34", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T13:07:49.995352Z", "start_time": "2025-03-29T13:07:49.993050Z"}, "execution": {"iopub.execute_input": "2023-08-18T19:50:54.021691Z", "iopub.status.busy": "2023-08-18T19:50:54.021249Z", "iopub.status.idle": "2023-08-18T19:50:54.027632Z", "shell.execute_reply": "2023-08-18T19:50:54.026516Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [], "source": ["class ResNet18(ResNet):\n", "    def __init__(self, lr=0.1, num_classes=10):\n", "        super().__init__(((2, 64), (2, 128), (2, 256), (2, 512)),\n", "                       lr, num_classes)"]}, {"cell_type": "code", "execution_count": 19, "id": "f153f6ed", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T13:07:50.064310Z", "start_time": "2025-03-29T13:07:50.003642Z"}, "execution": {"iopub.execute_input": "2023-08-18T19:50:54.031902Z", "iopub.status.busy": "2023-08-18T19:50:54.030981Z", "iopub.status.idle": "2023-08-18T19:50:54.188619Z", "shell.execute_reply": "2023-08-18T19:50:54.187488Z"}, "origin_pos": 32, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential output shape:\t torch.Size([1, 64, 24, 24])\n", "Sequential output shape:\t torch.Size([1, 64, 24, 24])\n", "Sequential output shape:\t torch.Size([1, 128, 12, 12])\n", "Sequential output shape:\t torch.Size([1, 256, 6, 6])\n", "Sequential output shape:\t torch.Size([1, 512, 3, 3])\n", "Sequential output shape:\t torch.Size([1, 10])\n"]}], "source": ["ResNet18().layer_summary((1, 1, 96, 96))"]}, {"cell_type": "markdown", "id": "c04c33c9", "metadata": {"origin_pos": 35}, "source": ["## [**Training**]\n", "\n", "We train ResNet on the Fashion-MNIST dataset, just like before. ResNet is quite a powerful and flexible architecture. The plot capturing training and validation loss illustrates a significant gap between both graphs, with the training loss being considerably lower. For a network of this flexibility, more training data would offer distinct benefit in closing the gap and improving accuracy.\n"]}, {"cell_type": "code", "execution_count": 20, "id": "61b87bb9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:54.192632Z", "iopub.status.busy": "2023-08-18T19:50:54.191821Z", "iopub.status.idle": "2023-08-18T19:53:34.753784Z", "shell.execute_reply": "2023-08-18T19:53:34.752565Z"}, "origin_pos": 36, "tab": ["pytorch"]}, "outputs": [], "source": ["model = ResNet18(lr=0.01)\n", "trainer = d2l.Trainer(max_epochs=10, num_gpus=1)\n", "data = d2l.FashionMNIST(batch_size=128, resize=(96, 96))\n", "model.apply_init([next(iter(data.get_dataloader(True)))[0]], d2l.init_cnn)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "bc09eb06", "metadata": {"origin_pos": 38}, "source": ["## ResNeXt\n", ":label:`subsec_resnext`\n", "\n", "One of the challenges one encounters in the design of ResNet is the trade-off between nonlinearity and dimensionality within a given block. That is, we could add more nonlinearity by increasing the number of layers, or by increasing the width of the convolutions. An alternative strategy is to increase the number of channels that can carry information between blocks. Unfortunately, the latter comes with a quadratic penalty since the computational cost of ingesting $c_\\textrm{i}$ channels and emitting $c_\\textrm{o}$ channels is proportional to $\\mathcal{O}(c_\\textrm{i} \\cdot c_\\textrm{o})$ (see our discussion in :numref:`sec_channels`). \n", "\n", "We can take some inspiration from the Inception block of :numref:`fig_inception` which has information flowing through the block in separate groups. Applying the idea of multiple independent groups to the ResNet block of :numref:`fig_resnet_block` led to the design of ResNeXt :cite:`Xie.Girshick.Dollar.ea.2017`.\n", "Different from the smorgasbord of transformations in Inception, \n", "ResNeXt adopts the *same* transformation in all branches,\n", "thus minimizing the need for manual tuning of each branch. \n", "\n", "![The ResNeXt block. The use of grouped convolution with $\\mathit{g}$ groups is $\\mathit{g}$ times faster than a dense convolution. It is a bottleneck residual block when the number of intermediate channels $\\mathit{b}$ is less than $\\mathit{c}$.](../img/resnext-block.svg)\n", ":label:`fig_resnext_block`\n", "\n", "Breaking up a convolution from $c_\\textrm{i}$ to $c_\\textrm{o}$ channels into one of $g$ groups of size $c_\\textrm{i}/g$ generating $g$ outputs of size $c_\\textrm{o}/g$ is called, quite fittingly, a *grouped convolution*. The computational cost (proportionally) is reduced from $\\mathcal{O}(c_\\textrm{i} \\cdot c_\\textrm{o})$ to $\\mathcal{O}(g \\cdot (c_\\textrm{i}/g) \\cdot (c_\\textrm{o}/g)) = \\mathcal{O}(c_\\textrm{i} \\cdot c_\\textrm{o} / g)$, i.e., it is $g$ times faster. Even better, the number of parameters needed to generate the output is also reduced from a $c_\\textrm{i} \\times c_\\textrm{o}$ matrix to $g$ smaller matrices of size $(c_\\textrm{i}/g) \\times (c_\\textrm{o}/g)$, again a $g$ times reduction. In what follows we assume that both $c_\\textrm{i}$ and $c_\\textrm{o}$ are divisible by $g$. \n", "\n", "The only challenge in this design is that no information is exchanged between the $g$ groups. The ResNeXt block of \n", ":numref:`fig_resnext_block` amends this in two ways: the grouped convolution with a $3 \\times 3$ kernel is sandwiched in between two $1 \\times 1$ convolutions. The second one serves double duty in changing the number of channels back. The benefit is that we only pay the $\\mathcal{O}(c \\cdot b)$ cost for $1 \\times 1$ kernels and can make do with an $\\mathcal{O}(b^2 / g)$ cost for $3 \\times 3$ kernels. Similar to the residual block implementation in\n", ":numref:`subsec_residual-blks`, the residual connection is replaced (thus generalized) by a $1 \\times 1$ convolution.\n", "\n", "The right-hand figure in :numref:`fig_resnext_block` provides a much more concise summary of the resulting network block. It will also play a major role in the design of generic modern CNNs in :numref:`sec_cnn-design`. Note that the idea of grouped convolutions dates back to the implementation of AlexNet :cite:`Krizhevsky.Sutskever.Hinton.2012`. When distributing the network across two GPUs with limited memory, the implementation treated each GPU as its own channel with no ill effects. \n", "\n", "The following implementation of the `ResNeXtBlock` class takes as argument `groups` ($g$), with \n", "`bot_channels` ($b$) intermediate (bottleneck) channels. Lastly, when we need to reduce the height and width of the representation, we add a stride of $2$ by setting `use_1x1conv=True, strides=2`.\n"]}, {"cell_type": "code", "execution_count": 21, "id": "aa67ceaf", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T13:14:33.946020Z", "start_time": "2025-03-29T13:14:33.942609Z"}, "execution": {"iopub.execute_input": "2023-08-18T19:53:34.757842Z", "iopub.status.busy": "2023-08-18T19:53:34.757550Z", "iopub.status.idle": "2023-08-18T19:53:34.767922Z", "shell.execute_reply": "2023-08-18T19:53:34.766805Z"}, "origin_pos": 40, "tab": ["pytorch"]}, "outputs": [], "source": ["class ResNeXtBlock(nn.Module):  #@save\n", "    \"\"\"The ResNeXt block.\"\"\"\n", "    def __init__(self, num_channels, groups, bot_mul, use_1x1conv=False,\n", "                 strides=1):\n", "        super().__init__()\n", "        bot_channels = int(round(num_channels * bot_mul))\n", "        self.conv1 = nn.LazyConv2d(bot_channels, kernel_size=1, stride=1)\n", "        self.conv2 = nn.LazyConv2d(bot_channels, kernel_size=3,\n", "                                   stride=strides, padding=1,\n", "                                   groups=bot_channels//groups)\n", "        self.conv3 = nn.LazyConv2d(num_channels, kernel_size=1, stride=1)\n", "        self.bn1 = nn.LazyBatchNorm2d()\n", "        self.bn2 = nn.LazyBatchNorm2d()\n", "        self.bn3 = nn.LazyBatchNorm2d()\n", "        if use_1x1conv:\n", "            self.conv4 = nn.LazyConv2d(num_channels, kernel_size=1,\n", "                                       stride=strides)\n", "            self.bn4 = nn.LazyBatchNorm2d()\n", "        else:\n", "            self.conv4 = None\n", "\n", "    def forward(self, X):\n", "        Y = F.relu(self.bn1(self.conv1(X)))\n", "        Y = F.relu(self.bn2(self.conv2(Y)))\n", "        Y = self.bn3(self.conv3(Y))\n", "        if self.conv4:\n", "            X = self.bn4(self.conv4(X))\n", "        return <PERSON><PERSON>relu(Y + X)"]}, {"cell_type": "markdown", "id": "03b62951", "metadata": {"origin_pos": 43}, "source": ["Its use is entirely analogous to that of the `ResNetBlock` discussed previously. For instance, when using (`use_1x1conv=False, strides=1`), the input and output are of the same shape. Alternatively, setting `use_1x1conv=True, strides=2` halves the output height and width.\n"]}, {"cell_type": "code", "execution_count": 22, "id": "ec6906e4", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T13:14:34.005398Z", "start_time": "2025-03-29T13:14:33.972808Z"}, "execution": {"iopub.execute_input": "2023-08-18T19:53:34.771844Z", "iopub.status.busy": "2023-08-18T19:53:34.771565Z", "iopub.status.idle": "2023-08-18T19:53:34.803609Z", "shell.execute_reply": "2023-08-18T19:53:34.802407Z"}, "origin_pos": 44, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([4, 32, 96, 96])"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["blk = ResNeXtBlock(32, 16, 1)\n", "X = torch.randn(4, 32, 96, 96)\n", "blk(X).shape"]}, {"cell_type": "markdown", "id": "94a2283f", "metadata": {"origin_pos": 47}, "source": ["## Summary and Discussion\n", "\n", "Nested function classes are desirable since they allow us to obtain strictly *more powerful* rather than also subtly *different* function classes when adding capacity. One way of accomplishing this is by letting additional layers to simply pass through the input to the output. Residual connections allow for this. As a consequence, this changes the inductive bias from simple functions being of the form $f(\\mathbf{x}) = 0$ to simple functions looking like $f(\\mathbf{x}) = \\mathbf{x}$. \n", "\n", "\n", "The residual mapping can learn the identity function more easily, such as pushing parameters in the weight layer to zero. We can train an effective *deep* neural network by having residual blocks. Inputs can forward propagate faster through the residual connections across layers. As a consequence, we can thus train much deeper networks. For instance, the original ResNet paper :cite:`He<PERSON>Zhang.Ren.ea.2016` allowed for up to 152 layers. Another benefit of residual networks is that it allows us to add layers, initialized as the identity function, *during* the training process. After all, the default behavior of a layer is to let the data pass through unchanged. This can accelerate the training of very large networks in some cases. \n", "\n", "Prior to residual connections,\n", "bypassing paths with gating units were introduced\n", "to effectively train highway networks with over 100 layers\n", ":cite:`srivastava2015highway`.\n", "Using identity functions as bypassing paths,\n", "ResNet performed remarkably well\n", "on multiple computer vision tasks.\n", "Residual connections had a major influence on the design of subsequent deep neural networks, of either convolutional or sequential nature.\n", "As we will introduce later,\n", "the Transformer architecture :cite:`Vaswani.Shazeer.Parmar.ea.2017`\n", "adopts residual connections (together with other design choices) and is pervasive\n", "in areas as diverse as\n", "language, vision, speech, and reinforcement learning.\n", "\n", "ResNeXt is an example for how the design of convolutional neural networks has evolved over time: by being more frugal with computation and trading it off against the size of the activations (number of channels), it allows for faster and more accurate networks at lower cost. An alternative way of viewing grouped convolutions is to think of a block-diagonal matrix for the convolutional weights. Note that there are quite a few such \"tricks\" that lead to more efficient networks. For instance, ShiftNet :cite:`wu2018shift` mimicks the effects of a $3 \\times 3$ convolution, simply by adding shifted activations to the channels, offering increased function complexity, this time without any computational cost. \n", "\n", "A common feature of the designs we have discussed so far is that the network design is fairly manual, primarily relying on the ingenuity of the designer to find the \"right\" network hyperparameters. While clearly feasible, it is also very costly in terms of human time and there is no guarantee that the outcome is optimal in any sense. In :numref:`sec_cnn-design` we will discuss a number of strategies for obtaining high quality networks in a more automated fashion. In particular, we will review the notion of *network design spaces* that led to the RegNetX/Y models\n", ":cite:`<PERSON><PERSON><PERSON><PERSON><PERSON>.Kosaraju.Girshick.ea.2020`.\n", "\n", "## Exercises\n", "\n", "1. What are the major differences between the Inception block in :numref:`fig_inception` and the residual block? How do they compare in terms of computation, accuracy, and the classes of functions they can describe?\n", "1. Refer to Table 1 in the ResNet paper :cite:`<PERSON><PERSON>Zhang.Ren.ea.2016` to implement different variants of the network. \n", "1. For deeper networks, ResNet introduces a \"bottleneck\" architecture to reduce model complexity. Try to implement it.\n", "1. In subsequent versions of ResNet, the authors changed the \"convolution, batch normalization, and activation\" structure to the \"batch normalization, activation, and convolution\" structure. Make this improvement yourself. See Figure 1 in :citet:`He<PERSON>Zhang.Ren.ea.2016*1` for details.\n", "1. Why can't we just increase the complexity of functions without bound, even if the function classes are nested?\n"]}, {"cell_type": "markdown", "id": "af80c347", "metadata": {"origin_pos": 49, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/86)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "c3ed4012", "metadata": {"origin_pos": 1}, "source": ["# Deep Convolutional Neural Networks (AlexNet)\n", ":label:`sec_alexnet`\n", "\n", "\n", "Although CNNs were well known\n", "in the computer vision and machine learning communities\n", "following the introduction of LeNet :cite:`LeCun.Jackel.Bottou.ea.1995`,\n", "they did not immediately dominate the field.\n", "Although LeNet achieved good results on early small datasets,\n", "the performance and feasibility of training CNNs\n", "on larger, more realistic datasets had yet to be established.\n", "In fact, for much of the intervening time between the early 1990s\n", "and the watershed results of 2012 :cite:`<PERSON>rizhevsky.Sutskever.Hinton.2012`,\n", "neural networks were often surpassed by other machine learning methods,\n", "such as kernel methods :cite:`Scholkopf.Smola.2002`, ensemble methods :cite:`Freund.Schapire.ea.1996`,\n", "and structured estimation :cite:`Taskar.Guestrin.Koller.2004`.\n", "\n", "For computer vision, this comparison is perhaps not entirely accurate.\n", "That is, although the inputs to convolutional networks\n", "consist of raw or lightly-processed (e.g., by centering) pixel values, practitioners would never feed raw pixels into traditional models.\n", "Instead, typical computer vision pipelines\n", "consisted of manually engineering feature extraction pipelines, such as SIFT :cite:`Lowe.2004`, SURF :cite:`Bay.Tuytelaars.Van-Gool.2006`, and bags of visual words :cite:`<PERSON><PERSON>.Zisserman.2003`.\n", "Rather than *learning* the features, the features were *crafted*.\n", "Most of the progress came from having more clever ideas for feature extraction on the one hand and deep insight into geometry :cite:<PERSON><PERSON>.Z<PERSON>.2000` on the other. The learning algorithm was often considered an afterthought.\n", "\n", "Although some neural network accelerators were available in the 1990s,\n", "they were not yet sufficiently powerful to make\n", "deep multichannel, multilayer CNNs\n", "with a large number of parameters. For instance, NVIDIA's GeForce 256 from 1999\n", "was able to process at most 480 million floating-point operations, such as additions and multiplications, per second (MFLOPS), without any meaningful\n", "programming framework for operations beyond games. Today's accelerators are able to perform in excess of 1000 TFLOPs per device.\n", "Moreover, datasets were still relatively small: OCR on 60,000 low-resolution $28 \\times 28$ pixel images was considered a highly challenging task.\n", "Added to these obstacles, key tricks for training neural networks\n", "including parameter initialization heuristics :cite:`Glorot.Bengio.2010`,\n", "clever variants of stochastic gradient descent :cite:`Kingma.Ba.2014`,\n", "non-squashing activation functions :cite:`<PERSON>.Hinton.2010`,\n", "and effective regularization techniques :cite:`Srivastava.Hinton.Krizhevsky.ea.2014` were still missing.\n", "\n", "Thus, rather than training *end-to-end* (pixel to classification) systems,\n", "classical pipelines looked more like this:\n", "\n", "1. Obtain an interesting dataset. In the early days, these datasets required expensive sensors. For instance, the [Apple QuickTake 100](https://en.wikipedia.org/wiki/Apple_QuickTake) of 1994 sported a whopping 0.3 megapixel (VGA) resolution, capable of storing up to 8 images, all for the price of \\$1000.\n", "1. Preprocess the dataset with hand-crafted features based on some knowledge of optics, geometry, other analytic tools, and occasionally on the serendipitous discoveries by lucky graduate students.\n", "1. Feed the data through a standard set of feature extractors such as the SIFT (scale-invariant feature transform) :cite:`Lowe.2004`, the SURF (speeded up robust features) :cite:`Bay.Tuytelaars.Van-Gool.2006`, or any number of other hand-tuned pipelines. OpenCV still provides SIFT extractors to this day!\n", "1. Dump the resulting representations into your favorite classifier, likely a linear model or kernel method, to train a classifier.\n", "\n", "If you spoke to machine learning researchers,\n", "they would reply that machine learning was both important and beautiful.\n", "Elegant theories proved the properties of various classifiers :cite:`boucheron2005theory` and convex\n", "optimization :cite:<PERSON><PERSON>.2004` had become the mainstay for obtaining them.\n", "The field of machine learning was thriving, rigorous, and eminently useful. However,\n", "if you spoke to a computer vision researcher,\n", "you would hear a very different story.\n", "The dirty truth of image recognition, they would tell you,\n", "is that features, geometry :cite:<PERSON><PERSON>.Zisserman.2000,hartley2009global`, and engineering,\n", "rather than novel learning algorithms, drove progress.\n", "Computer vision researchers justifiably believed\n", "that a slightly bigger or cleaner dataset\n", "or a slightly improved feature-extraction pipeline\n", "mattered far more to the final accuracy than any learning algorithm.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "07938be6", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T07:27:55.469748Z", "start_time": "2025-03-29T07:27:53.914613Z"}, "attributes": {"classes": [], "id": "", "n": "3"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "3425d857", "metadata": {"origin_pos": 6}, "source": ["## Representation Learning\n", "\n", "Another way to cast the state of affairs is that\n", "the most important part of the pipeline was the representation.\n", "And up until 2012 the representation was calculated mostly mechanically.\n", "In fact, engineering a new set of feature functions, improving results, and writing up the method\n", "all featured prominently in papers.\n", "SIFT :cite:`Lowe.2004`,\n", "SURF :cite:`Bay.Tuytelaars.Van-Gool.2006`,\n", "HOG (histograms of oriented gradient) :cite:`Dalal.Triggs.2005`,\n", "bags of visual words :cite:<PERSON><PERSON><PERSON><PERSON>.2003`,\n", "and similar feature extractors ruled the roost.\n", "\n", "Another group of researchers,\n", "including <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,\n", "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>,\n", "had different plans.\n", "They believed that features themselves ought to be learned.\n", "Moreover, they believed that to be reasonably complex,\n", "the features ought to be hierarchically composed\n", "with multiple jointly learned layers, each with learnable parameters.\n", "In the case of an image, the lowest layers might come\n", "to detect edges, colors, and textures, by analogy with how the visual system in animals\n", "processes its input. In particular, the automatic design of visual features such as those obtained\n", "by sparse coding :cite:`olshausen1996emergence` remained an open challenge until the advent of modern CNNs.\n", "It was not until :citet:`Dean.Corrado.Monga.ea.2012,le2013building` that the idea of generating features\n", "from image data automatically gained significant traction.\n", "\n", "The first modern CNN :cite:<PERSON><PERSON><PERSON><PERSON><PERSON>.Sutskever.Hinton.2012`, named\n", "*AlexNet* after one of its inventors, <PERSON>, is largely an evolutionary improvement\n", "over LeNet. It achieved excellent performance in the 2012 ImageNet challenge.\n", "\n", "![Image filters learned by the first layer of AlexNet. Reproduction courtesy of :citet:<PERSON><PERSON><PERSON><PERSON><PERSON>.Sutskever.Hinton.2012`.](../img/filters.png)\n", ":width:`400px`\n", ":label:`fig_filters`\n", "\n", "Interestingly, in the lowest layers of the network,\n", "the model learned feature extractors that resembled some traditional filters.\n", ":numref:`fig_filters`\n", "shows lower-level image descriptors.\n", "Higher layers in the network might build upon these representations\n", "to represent larger structures, like eyes, noses, blades of grass, and so on.\n", "Even higher layers might represent whole objects\n", "like people, airplanes, dogs, or frisbees.\n", "Ultimately, the final hidden state learns a compact representation\n", "of the image that summarizes its contents\n", "such that data belonging to different categories can be easily separated.\n", "\n", "AlexNet (2012) and its precursor LeNet (1995) share many architectural elements. This begs the question: why did it take so long?\n", "A key difference was that, over the previous two decades, the amount of data and the computing power available had increased significantly. As such AlexNet was much larger: it was trained on much more data, and on much faster GPUs compared to the CPUs available in 1995.\n", "\n", "### Missing Ingredient: Data\n", "\n", "Deep models with many layers require large amounts of data\n", "in order to enter the regime\n", "where they significantly outperform traditional methods\n", "based on convex optimizations (e.g., linear and kernel methods).\n", "However, given the limited storage capacity of computers,\n", "the relative expense of (imaging) sensors,\n", "and the comparatively tighter research budgets in the 1990s,\n", "most research relied on tiny datasets.\n", "Numerous papers relied on the UCI collection of datasets,\n", "many of which contained only hundreds or (a few) thousands of images\n", "captured in low resolution and often with an artificially clean background.\n", "\n", "In 2009, the ImageNet dataset was released :cite:`Deng.Dong.Socher.ea.2009`,\n", "challenging researchers to learn models from 1 million examples,\n", "1000 each from 1000 distinct categories of objects. The categories themselves\n", "were based on the most popular noun nodes in WordNet :cite:`<PERSON>.1995`.\n", "The ImageNet team used Google Image Search to prefilter large candidate sets\n", "for each category and employed\n", "the Amazon Mechanical Turk crowdsourcing pipeline\n", "to confirm for each image whether it belonged to the associated category.\n", "This scale was unprecedented, exceeding others by over an order of magnitude\n", "(e.g., CIFAR-100 has 60,000 images). Another aspect was that the images were at\n", "relatively high resolution of $224 \\times 224$ pixels, unlike the 80 million-sized\n", "TinyImages dataset :cite:`Torralba.Fergus.Freeman.2008`, consisting of $32 \\times 32$ pixel thumbnails.\n", "This allowed for the formation of higher-level features.\n", "The associated competition, dubbed the ImageNet Large Scale Visual Recognition\n", "Challenge :cite:`russakovsky2015imagenet`,\n", "pushed computer vision and machine learning research forward,\n", "challenging researchers to identify which models performed best\n", "at a greater scale than academics had previously considered. The largest vision datasets, such as LAION-5B\n", ":cite:`schuhmann2022laion` contain billions of images with additional metadata.\n", "\n", "### Missing Ingredient: Hardware\n", "\n", "Deep learning models are voracious consumers of compute cycles.\n", "Training can take hundreds of epochs, and each iteration\n", "requires passing data through many layers of computationally expensive\n", "linear algebra operations.\n", "This is one of the main reasons why in the 1990s and early 2000s,\n", "simple algorithms based on the more-efficiently optimized\n", "convex objectives were preferred.\n", "\n", "*Graphical processing units* (GPUs) proved to be a game changer\n", "in making deep learning feasible.\n", "These chips had earlier been developed for accelerating\n", "graphics processing to benefit computer games.\n", "In particular, they were optimized for high throughput $4 \\times 4$\n", "matrix--vector products, which are needed for many computer graphics tasks.\n", "Fortunately, the math is strikingly similar\n", "to that required for calculating convolutional layers.\n", "Around that time, NVIDIA and ATI had begun optimizing GPUs\n", "for general computing operations :cite:`Fernando.2004`,\n", "going as far as to market them as *general-purpose GPUs* (GPGPUs).\n", "\n", "To provide some intuition, consider the cores of a modern microprocessor\n", "(CPU).\n", "Each of the cores is fairly powerful running at a high clock frequency\n", "and sporting large caches (up to several megabytes of L3).\n", "Each core is well-suited to executing a wide range of instructions,\n", "with branch predictors, a deep pipeline, specialized execution units,\n", "speculative execution,\n", "and many other bells and whistles\n", "that enable it to run a large variety of programs with sophisticated control flow.\n", "This apparent strength, however, is also its Achilles heel:\n", "general-purpose cores are very expensive to build. They excel at general-purpose\n", "code with lots of control flow.\n", "This requires lots of chip area, not just for the\n", "actual ALU (arithmetic logical unit) where computation happens, but also for\n", "all the aforementioned bells and whistles, plus\n", "memory interfaces, caching logic between cores,\n", "high-speed interconnects, and so on. CPUs are\n", "comparatively bad at any single task when compared with dedicated hardware.\n", "Modern laptops have 4--8 cores,\n", "and even high-end servers rarely exceed 64 cores per socket,\n", "simply because it is not cost-effective.\n", "\n", "By comparison, GPUs can consist of thousands of small processing elements (NIVIDA's latest Ampere chips have up to 6912 CUDA cores), often grouped into larger groups (NVIDIA calls them warps).\n", "The details differ somewhat between NVIDIA, AMD, ARM and other chip vendors. While each core is relatively weak,\n", "running at about 1GHz clock frequency,\n", "it is the total number of such cores that makes GPUs orders of magnitude faster than CPUs.\n", "For instance, NVIDIA's recent Ampere A100 GPU offers over 300 TFLOPs per chip for specialized 16-bit precision (BFLOAT16) matrix-matrix multiplications, and up to 20 TFLOPs for more general-purpose floating point operations (FP32).\n", "At the same time, floating point performance of CPUs rarely exceeds 1 TFLOPs. For instance, Amazon's Graviton 3  reaches 2 TFLOPs peak performance for 16-bit precision operations, a number similar to the GPU performance of Apple's M1 processor.\n", "\n", "There are many reasons why GPUs are much faster than CPUs in terms of FLOPs.\n", "First, power consumption tends to grow *quadratically* with clock frequency.\n", "Hence, for the power budget of a CPU core that runs four times faster (a typical number),\n", "you can use 16 GPU cores at $\\frac{1}{4}$ the speed,\n", "which yields $16 \\times \\frac{1}{4} = 4$ times the performance.\n", "Second, GPU cores are much simpler\n", "(in fact, for a long time they were not even *able*\n", "to execute general-purpose code),\n", "which makes them more energy efficient. For instance, (i) they tend not to support speculative evaluation, (ii) it typically is not possible to program each processing element individually, and (iii) the caches per core tend to be much smaller.\n", "Last, many operations in deep learning require high memory bandwidth.\n", "Again, GPUs shine here with buses that are at least 10 times as wide as many CPUs.\n", "\n", "Back to 2012. A major breakthrough came\n", "when <PERSON> and <PERSON><PERSON>ever\n", "implemented a deep CNN\n", "that could run on GPUs.\n", "They realized that the computational bottlenecks in CNNs,\n", "convolutions and matrix multiplications,\n", "are all operations that could be parallelized in hardware.\n", "Using two NVIDIA GTX 580s with 3GB of memory, either of which was capable of 1.5 TFLOPs (still a challenge for most CPUs a decade later),\n", "they implemented fast convolutions.\n", "The [cuda-convnet](https://code.google.com/archive/p/cuda-convnet/) code\n", "was good enough that for several years\n", "it was the industry standard and powered\n", "the first couple of years of the deep learning boom.\n", "\n", "## AlexNet\n", "\n", "AlexNet, which employed an 8-layer CNN,\n", "won the ImageNet Large Scale Visual Recognition Challenge 2012\n", "by a large margin :cite:<PERSON><PERSON><PERSON>ovsky.Deng.Huang.ea.2013`.\n", "This network showed, for the first time,\n", "that the features obtained by learning can transcend manually-designed features, breaking the previous paradigm in computer vision.\n", "\n", "The architectures of AlexNet and LeNet are strikingly similar,\n", "as :numref:`fig_alexnet` illustrates.\n", "Note that we provide a slightly streamlined version of AlexNet\n", "removing some of the design quirks that were needed in 2012\n", "to make the model fit on two small GPUs.\n", "\n", "![From LeNet (left) to AlexNet (right).](../img/alexnet.svg)\n", ":label:`fig_alexnet`\n", "\n", "There are also significant differences between AlexNet and LeNet.\n", "First, AlexNet is much deeper than the comparatively small LeNet-5.\n", "AlexNet consists of eight layers: five convolutional layers,\n", "two fully connected hidden layers, and one fully connected output layer.\n", "Second, AlexNet used the ReLU instead of the sigmoid\n", "as its activation function. Let's delve into the details below.\n", "\n", "### Architecture\n", "\n", "In AlexNet's first layer, the convolution window shape is $11\\times11$.\n", "Since the images in ImageNet are eight times taller and wider\n", "than the MNIST images,\n", "objects in ImageNet data tend to occupy more pixels with more visual detail.\n", "Consequently, a larger convolution window is needed to capture the object.\n", "The convolution window shape in the second layer\n", "is reduced to $5\\times5$, followed by $3\\times3$.\n", "In addition, after the first, second, and fifth convolutional layers,\n", "the network adds max-pooling layers\n", "with a window shape of $3\\times3$ and a stride of 2.\n", "Moreover, AlexNet has ten times more convolution channels than LeNet.\n", "\n", "After the final convolutional layer, there are two huge fully connected layers\n", "with 4096 outputs.\n", "These layers require nearly 1GB model parameters.\n", "Because of the limited memory in early GPUs,\n", "the original AlexNet used a dual data stream design,\n", "so that each of their two GPUs could be responsible\n", "for storing and computing only its half of the model.\n", "Fortunately, GPU memory is comparatively abundant now,\n", "so we rarely need to break up models across GPUs these days\n", "(our version of the AlexNet model deviates\n", "from the original paper in this aspect).\n", "\n", "### Activation Functions\n", "\n", "Furthermore, AlexNet changed the sigmoid activation function to a simpler ReLU activation function. On the one hand, the computation of the ReLU activation function is simpler. For example, it does not have the exponentiation operation found in the sigmoid activation function.\n", " On the other hand, the ReLU activation function makes model training easier when using different parameter initialization methods. This is because, when the output of the sigmoid activation function is very close to 0 or 1, the gradient of these regions is almost 0, so that backpropagation cannot continue to update some of the model parameters. By contrast, the gradient of the ReLU activation function in the positive interval is always 1 (:numref:`subsec_activation-functions`). Therefore, if the model parameters are not properly initialized, the sigmoid function may obtain a gradient of almost 0 in the positive interval, meaning that the model cannot be effectively trained.\n", "\n", "### Capacity Control and Preprocessing\n", "\n", "AlexNet controls the model complexity of the fully connected layer\n", "by dropout (:numref:`sec_dropout`),\n", "while LeNet only uses weight decay.\n", "To augment the data even further, the training loop of AlexNet\n", "added a great deal of image augmentation,\n", "such as flipping, clipping, and color changes.\n", "This makes the model more robust and the larger sample size effectively reduces overfitting.\n", "See :citet:`Buslaev.Iglovikov.Khvedchenya.ea.2020` for an in-depth review of such preprocessing steps.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "29feac8e", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T07:28:04.342526Z", "start_time": "2025-03-29T07:28:04.338728Z"}, "attributes": {"classes": [], "id": "", "n": "5"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["class AlexNet(d2l.Classifier):\n", "    def __init__(self, lr=0.1, num_classes=10):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        self.net = nn.Sequential(\n", "            nn.LazyConv2d(96, kernel_size=11, stride=4, padding=1),\n", "            nn.<PERSON><PERSON><PERSON>(), nn.MaxPool2d(kernel_size=3, stride=2),\n", "            nn.LazyConv2d(256, kernel_size=5, padding=2), nn.ReLU(),\n", "            nn.MaxPool2d(kernel_size=3, stride=2),\n", "            nn.LazyConv2d(384, kernel_size=3, padding=1), nn.ReLU(),\n", "            nn.LazyConv2d(384, kernel_size=3, padding=1), nn.ReLU(),\n", "            nn.LazyConv2d(256, kernel_size=3, padding=1), nn.ReLU(),\n", "            nn.<PERSON><PERSON>ool2d(kernel_size=3, stride=2), nn.<PERSON><PERSON>(),\n", "            nn.<PERSON><PERSON><PERSON><PERSON><PERSON>(4096), nn.<PERSON><PERSON><PERSON>(), nn.Dropout(p=0.5),\n", "            nn.<PERSON><PERSON><PERSON><PERSON><PERSON>(4096), nn.<PERSON><PERSON><PERSON>(),nn.Dropout(p=0.5),\n", "            nn.<PERSON>zy<PERSON>inear(num_classes))\n", "        self.net.apply(d2l.init_cnn)"]}, {"cell_type": "markdown", "id": "59e725f1", "metadata": {"origin_pos": 9}, "source": ["We [**construct a single-channel data example**] with both height and width of 224 (**to observe the output shape of each layer**). It matches the AlexNet architecture in :numref:`fig_alexnet`.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "3d5c2c0a", "metadata": {"ExecuteTime": {"end_time": "2025-03-29T07:28:08.046713Z", "start_time": "2025-03-29T07:28:07.879154Z"}, "attributes": {"classes": [], "id": "", "n": "6"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Conv2d output shape:\t torch.Size([1, 96, 54, 54])\n", "ReLU output shape:\t torch.Size([1, 96, 54, 54])\n", "MaxPool2d output shape:\t torch.Size([1, 96, 26, 26])\n", "Conv2d output shape:\t torch.Size([1, 256, 26, 26])\n", "ReLU output shape:\t torch.Size([1, 256, 26, 26])\n", "MaxPool2d output shape:\t torch.Size([1, 256, 12, 12])\n", "Conv2d output shape:\t torch.Size([1, 384, 12, 12])\n", "ReLU output shape:\t torch.Size([1, 384, 12, 12])\n", "Conv2d output shape:\t torch.Size([1, 384, 12, 12])\n", "ReLU output shape:\t torch.Size([1, 384, 12, 12])\n", "Conv2d output shape:\t torch.Size([1, 256, 12, 12])\n", "ReLU output shape:\t torch.Size([1, 256, 12, 12])\n", "MaxPool2d output shape:\t torch.Size([1, 256, 5, 5])\n", "Flatten output shape:\t torch.Size([1, 6400])\n", "Linear output shape:\t torch.Size([1, 4096])\n", "ReLU output shape:\t torch.Size([1, 4096])\n", "Dropout output shape:\t torch.Size([1, 4096])\n", "Linear output shape:\t torch.Size([1, 4096])\n", "ReLU output shape:\t torch.Size([1, 4096])\n", "Dropout output shape:\t torch.Size([1, 4096])\n", "Linear output shape:\t torch.Size([1, 10])\n"]}], "source": ["AlexNet().layer_summary((1, 1, 224, 224))"]}, {"cell_type": "markdown", "id": "193ba4c7", "metadata": {"origin_pos": 13}, "source": ["## Training\n", "\n", "Although AlexNet was trained on ImageNet in :citet:`<PERSON>riz<PERSON>vsky.Sutskever.Hinton.2012`,\n", "we use Fashion-MNIST here\n", "since training an ImageNet model to convergence could take hours or days\n", "even on a modern GPU.\n", "One of the problems with applying AlexNet directly on [**Fashion-MNIST**]\n", "is that its (**images have lower resolution**) ($28 \\times 28$ pixels)\n", "(**than ImageNet images.**)\n", "To make things work, (**we upsample them to $224 \\times 224$**).\n", "This is generally not a smart practice, as it simply increases the computational\n", "complexity without adding information. Nonetheless, we do it here to be faithful to the AlexNet architecture.\n", "We perform this resizing with the `resize` argument in the `d2l.FashionMNIST` constructor.\n", "\n", "Now, we can [**start training AlexNet.**]\n", "Compared to LeNet in :numref:`sec_lenet`,\n", "the main change here is the use of a smaller learning rate\n", "and much slower training due to the deeper and wider network,\n", "the higher image resolution, and the more costly convolutions.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "acd6a3e0", "metadata": {"attributes": {"classes": [], "id": "", "n": "8"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-29T21:01:06.271236</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m0be09bfce7\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m0be09bfce7\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m0be09bfce7\" x=\"69.163125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(65.981875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m0be09bfce7\" x=\"108.223125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(105.041875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m0be09bfce7\" x=\"147.283125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(144.101875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m0be09bfce7\" x=\"186.343125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(183.161875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m0be09bfce7\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"mc13ab036a7\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc13ab036a7\" x=\"30.103125\" y=\"122.67996\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 126.479179) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mc13ab036a7\" x=\"30.103125\" y=\"92.383516\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 96.182734) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mc13ab036a7\" x=\"30.103125\" y=\"62.087071\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 65.88629) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mc13ab036a7\" x=\"30.103125\" y=\"31.790626\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 35.589845) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 34.954394 13.5 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 49.633125 13.921695 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_15\"/>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 49.633125 13.921695 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 49.633125 139.5 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 49.633125 13.921695 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 49.633125 139.5 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 49.633125 13.921695 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 49.633125 139.5 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 49.633125 139.5 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "L 171.372901 121.717983 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "L 171.372901 121.717983 \n", "L 181.11708 122.841224 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "L 171.372901 121.717983 \n", "L 181.11708 122.841224 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "L 186.343125 123.817381 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "L 171.372901 121.717983 \n", "L 181.11708 122.841224 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "L 186.343125 123.817381 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "L 186.343125 103.043598 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "L 171.372901 121.717983 \n", "L 181.11708 122.841224 \n", "L 190.861259 123.623886 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "L 186.343125 123.817381 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "L 186.343125 103.043598 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "L 171.372901 121.717983 \n", "L 181.11708 122.841224 \n", "L 190.861259 123.623886 \n", "L 200.605438 124.890728 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "L 186.343125 123.817381 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "L 186.343125 103.043598 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "L 171.372901 121.717983 \n", "L 181.11708 122.841224 \n", "L 190.861259 123.623886 \n", "L 200.605438 124.890728 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "L 186.343125 123.817381 \n", "L 205.873125 125.105734 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "L 186.343125 103.043598 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "L 171.372901 121.717983 \n", "L 181.11708 122.841224 \n", "L 190.861259 123.623886 \n", "L 200.605438 124.890728 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "L 186.343125 123.817381 \n", "L 205.873125 125.105734 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "L 186.343125 103.043598 \n", "L 205.873125 102.540255 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "L 171.372901 121.717983 \n", "L 181.11708 122.841224 \n", "L 190.861259 123.623886 \n", "L 200.605438 124.890728 \n", "L 210.349618 125.548449 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "L 186.343125 123.817381 \n", "L 205.873125 125.105734 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "L 186.343125 103.043598 \n", "L 205.873125 102.540255 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "L 171.372901 121.717983 \n", "L 181.11708 122.841224 \n", "L 190.861259 123.623886 \n", "L 200.605438 124.890728 \n", "L 210.349618 125.548449 \n", "L 220.093797 125.892546 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "L 186.343125 123.817381 \n", "L 205.873125 125.105734 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "L 186.343125 103.043598 \n", "L 205.873125 102.540255 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "L 171.372901 121.717983 \n", "L 181.11708 122.841224 \n", "L 190.861259 123.623886 \n", "L 200.605438 124.890728 \n", "L 210.349618 125.548449 \n", "L 220.093797 125.892546 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "L 186.343125 123.817381 \n", "L 205.873125 125.105734 \n", "L 225.403125 126.596125 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "L 186.343125 103.043598 \n", "L 205.873125 102.540255 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 34.954394 13.5 \n", "L 44.698573 13.672378 \n", "L 54.442752 17.681442 \n", "L 64.186931 79.231329 \n", "L 73.93111 93.967408 \n", "L 83.675289 99.316054 \n", "L 93.419468 103.468154 \n", "L 103.163647 107.402459 \n", "L 112.907826 110.694639 \n", "L 122.652006 113.530073 \n", "L 132.396185 115.867812 \n", "L 142.140364 117.507984 \n", "L 151.884543 119.38412 \n", "L 161.628722 120.485681 \n", "L 171.372901 121.717983 \n", "L 181.11708 122.841224 \n", "L 190.861259 123.623886 \n", "L 200.605438 124.890728 \n", "L 210.349618 125.548449 \n", "L 220.093797 125.892546 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 49.633125 13.921695 \n", "L 69.163125 92.478422 \n", "L 88.693125 99.63334 \n", "L 108.223125 107.864806 \n", "L 127.753125 115.654446 \n", "L 147.283125 119.23484 \n", "L 166.813125 120.267755 \n", "L 186.343125 123.817381 \n", "L 205.873125 125.105734 \n", "L 225.403125 126.596125 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 49.633125 139.5 \n", "L 69.163125 116.669807 \n", "L 88.693125 113.51193 \n", "L 108.223125 109.85071 \n", "L 127.753125 106.093616 \n", "L 147.283125 105.009032 \n", "L 166.813125 104.823274 \n", "L 186.343125 103.043598 \n", "L 205.873125 102.540255 \n", "L 225.403125 102.234654 \n", "\" clip-path=\"url(#pc0b2576364)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 60.06875 \n", "L 218.403125 60.06875 \n", "Q 220.403125 60.06875 220.403125 58.06875 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 138.8125 12.2 \n", "Q 136.8125 12.2 136.8125 14.2 \n", "L 136.8125 58.06875 \n", "Q 136.8125 60.06875 138.8125 60.06875 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_127\">\n", "     <path d=\"M 140.8125 20.298438 \n", "L 150.8125 20.298438 \n", "L 160.8125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_128\">\n", "     <path d=\"M 140.8125 35.254688 \n", "L 150.8125 35.254688 \n", "L 160.8125 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_129\">\n", "     <path d=\"M 140.8125 50.210938 \n", "L 150.8125 50.210938 \n", "L 160.8125 50.210938 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 53.710938) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pc0b2576364\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = AlexNet(lr=0.01)\n", "data = d2l.FashionMNIST(batch_size=128, resize=(224, 224))\n", "trainer = d2l.Trainer(max_epochs=10, num_gpus=1)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "f9c33357", "metadata": {"origin_pos": 16}, "source": ["## Discussion\n", "\n", "AlexNet's structure bears a striking resemblance to LeNet, with a number of critical improvements, both for accuracy (dropout) and for ease of training (ReLU). What is equally striking is the amount of progress that has been made in terms of deep learning tooling. What was several months of work in 2012 can now be accomplished in a dozen lines of code using any modern framework.\n", "\n", "Reviewing the architecture, we see that AlexNet has an Achilles heel when it comes to efficiency: the last two hidden layers require matrices of size $6400 \\times 4096$ and $4096 \\times 4096$, respectively. This corresponds to 164 MB of memory and 81 MFLOPs of computation, both of which are a nontrivial outlay, especially on smaller devices, such as mobile phones. This is one of the reasons why AlexNet has been surpassed by much more effective architectures that we will cover in the following sections. Nonetheless, it is a key step from shallow to deep networks that are used nowadays. Note that even though the number of parameters exceeds by far the amount of training data in our experiments (the last two layers have more than 40 million parameters, trained on a datasets of 60 thousand images), there is hardly any overfitting: training and validation loss are virtually identical throughout training. This is due to the improved regularization, such as dropout, inherent in modern deep network designs.\n", "\n", "Although it seems that there are only a few more lines in AlexNet's implementation than in LeNet's, it took the academic community many years to embrace this conceptual change and take advantage of its excellent experimental results. This was also due to the lack of efficient computational tools. At the time neither DistBelief :cite:`Dean.Corrado.Monga.ea.2012` nor Caffe :cite:`Jia.Shelhamer.Donahue.ea.2014` existed, and Theano :cite:`Bergstra.Breuleux.Bastien.ea.2010` still lacked many distinguishing features. It was the availability of TensorFlow :cite:`Abadi.Barham.Chen.ea.2016` that dramatically changed the situation.\n", "\n", "## Exercises\n", "\n", "1. Following up on the discussion above, analyze the computational properties of AlexNet.\n", "    1. Compute the memory footprint for convolutions and fully connected layers, respectively. Which one dominates?\n", "    1. Calculate the computational cost for the convolutions and the fully connected layers.\n", "    1. How does the memory (read and write bandwidth, latency, size) affect computation? Is there any difference in its effects for training and inference?\n", "1. You are a chip designer and need to trade off computation and memory bandwidth. For example, a faster chip requires more power and possibly a larger chip area. More memory bandwidth requires more pins and control logic, thus also more area. How do you optimize?\n", "1. Why do engineers no longer report performance benchmarks on AlexNet?\n", "1. Try increasing the number of epochs when training AlexNet. Compared with LeNet, how do the results differ? Why?\n", "1. AlexNet may be too complex for the Fashion-MNIST dataset, in particular due to the low resolution of the initial images.\n", "    1. Try simplifying the model to make the training faster, while ensuring that the accuracy does not drop significantly.\n", "    1. Design a better model that works directly on $28 \\times 28$ images.\n", "1. Modify the batch size, and observe the changes in throughput (images/s), accuracy, and GPU memory.\n", "1. Apply dropout and ReLU to LeNet-5. Does it improve? Can you improve things further by preprocessing to take advantage of the invariances inherent in the images?\n", "1. Can you make AlexNet overfit? Which feature do you need to remove or change to break training?\n"]}, {"cell_type": "markdown", "id": "3e825ff7", "metadata": {"origin_pos": 18, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/76)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON> (aideep)", "language": "python", "name": "<PERSON><PERSON>"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
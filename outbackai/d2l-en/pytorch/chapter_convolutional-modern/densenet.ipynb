{"cells": [{"cell_type": "markdown", "id": "f122fb8e", "metadata": {"origin_pos": 1}, "source": ["# Densely Connected Networks (DenseNet)\n", ":label:`sec_densenet`\n", "\n", "ResNet significantly changed the view of how to parametrize the functions in deep networks. *DenseNet* (dense convolutional network) is to some extent the logical extension of this :cite:`<PERSON><PERSON>.<PERSON>-<PERSON>-Maaten.ea.2017`.\n", "DenseNet is characterized by both the connectivity pattern where\n", "each layer connects to all the preceding layers\n", "and the concatenation operation (rather than the addition operator in ResNet) to preserve and reuse features\n", "from earlier layers.\n", "To understand how to arrive at it, let's take a small detour to mathematics.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "898d7836", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:25.812288Z", "iopub.status.busy": "2023-08-18T19:47:25.811221Z", "iopub.status.idle": "2023-08-18T19:47:28.844635Z", "shell.execute_reply": "2023-08-18T19:47:28.843703Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "abbc7e4b", "metadata": {"origin_pos": 6}, "source": ["## From ResNet to DenseNet\n", "\n", "Recall the <PERSON> expansion for functions. At the point $x = 0$ it can be written as\n", "\n", "$$f(x) = f(0) + x \\cdot \\left[f'(0) + x \\cdot \\left[\\frac{f''(0)}{2!}  + x \\cdot \\left[\\frac{f'''(0)}{3!}  + \\cdots \\right]\\right]\\right].$$\n", "\n", "\n", "The key point is that it decomposes a function into terms of increasingly higher order. In a similar vein, ResNet decomposes functions into\n", "\n", "$$f(\\mathbf{x}) = \\mathbf{x} + g(\\mathbf{x}).$$\n", "\n", "That is, ResNet decomposes $f$ into a simple linear term and a more complex\n", "nonlinear one.\n", "What if we wanted to capture (not necessarily add) information beyond two terms?\n", "One such solution is DenseNet :cite:`<PERSON><PERSON>Der-Maaten.ea.2017`.\n", "\n", "![The main difference between ResNet (left) and DenseNet (right) in cross-layer connections: use of addition and use of concatenation. ](../img/densenet-block.svg)\n", ":label:`fig_densenet_block`\n", "\n", "As shown in :numref:`fig_densenet_block`, the key difference between ResNet and DenseNet is that in the latter case outputs are *concatenated* (denoted by $[,]$) rather than added.\n", "As a result, we perform a mapping from $\\mathbf{x}$ to its values after applying an increasingly complex sequence of functions:\n", "\n", "$$\\mathbf{x} \\to \\left[\n", "\\mathbf{x},\n", "f_1(\\mathbf{x}),\n", "f_2\\left(\\left[\\mathbf{x}, f_1\\left(\\mathbf{x}\\right)\\right]\\right), f_3\\left(\\left[\\mathbf{x}, f_1\\left(\\mathbf{x}\\right), f_2\\left(\\left[\\mathbf{x}, f_1\\left(\\mathbf{x}\\right)\\right]\\right)\\right]\\right), \\ldots\\right].$$\n", "\n", "In the end, all these functions are combined in MLP to reduce the number of features again. In terms of implementation this is quite simple:\n", "rather than adding terms, we concatenate them. The name DenseNet arises from the fact that the dependency graph between variables becomes quite dense. The final layer of such a chain is densely connected to all previous layers. The dense connections are shown in :numref:`fig_densenet`.\n", "\n", "![Dense connections in DenseNet. Note how the dimensionality increases with depth.](../img/densenet.svg)\n", ":label:`fig_densenet`\n", "\n", "The main components that comprise a DenseNet are *dense blocks* and *transition layers*. The former define how the inputs and outputs are concatenated, while the latter control the number of channels so that it is not too large, \n", "since the expansion $\\mathbf{x} \\to \\left[\\mathbf{x}, f_1(\\mathbf{x}),\n", "f_2\\left(\\left[\\mathbf{x}, f_1\\left(\\mathbf{x}\\right)\\right]\\right), \\ldots \\right]$ can be quite high-dimensional.\n", "\n", "\n", "## [**<PERSON><PERSON>s**]\n", "\n", "DenseNet uses the modified \"batch normalization, activation, and convolution\"\n", "structure of ResNet (see the exercise in :numref:`sec_resnet`).\n", "First, we implement this convolution block structure.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "7d95675e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:28.849074Z", "iopub.status.busy": "2023-08-18T19:47:28.848270Z", "iopub.status.idle": "2023-08-18T19:47:28.853093Z", "shell.execute_reply": "2023-08-18T19:47:28.852284Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["def conv_block(num_channels):\n", "    return nn.Sequential(\n", "        nn.LazyBatchNorm2d(), nn.ReLU(),\n", "        nn.LazyConv2d(num_channels, kernel_size=3, padding=1))"]}, {"cell_type": "markdown", "id": "b4e66d06", "metadata": {"origin_pos": 11}, "source": ["A *dense block* consists of multiple convolution blocks, each using the same number of output channels. In the forward propagation, however, we concatenate the input and output of each convolution block on the channel dimension. Lazy evaluation allows us to adjust the dimensionality automatically.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "805394e3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:28.856547Z", "iopub.status.busy": "2023-08-18T19:47:28.855981Z", "iopub.status.idle": "2023-08-18T19:47:28.862956Z", "shell.execute_reply": "2023-08-18T19:47:28.861783Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["class DenseBlock(nn.Module):\n", "    def __init__(self, num_convs, num_channels):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        layer = []\n", "        for i in range(num_convs):\n", "            layer.append(conv_block(num_channels))\n", "        self.net = nn.Sequential(*layer)\n", "\n", "    def forward(self, X):\n", "        for blk in self.net:\n", "            Y = blk(X)\n", "            # Concatenate input and output of each block along the channels\n", "            X = torch.cat((X, Y), dim=1)\n", "        return X"]}, {"cell_type": "markdown", "id": "b17dfe15", "metadata": {"origin_pos": 16}, "source": ["In the following example,\n", "we [**define a `DenseBlock` instance**] with two convolution blocks of 10 output channels.\n", "When using an input with three channels, we will get an output with  $3 + 10 + 10=23$ channels. The number of convolution block channels controls the growth in the number of output channels relative to the number of input channels. This is also referred to as the *growth rate*.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "e369c1ad", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:28.867108Z", "iopub.status.busy": "2023-08-18T19:47:28.866407Z", "iopub.status.idle": "2023-08-18T19:47:28.909936Z", "shell.execute_reply": "2023-08-18T19:47:28.908954Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([4, 23, 8, 8])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["blk = <PERSON><PERSON><PERSON>lock(2, 10)\n", "X = torch.randn(4, 3, 8, 8)\n", "Y = blk(X)\n", "Y.shape"]}, {"cell_type": "markdown", "id": "69c8df7b", "metadata": {"origin_pos": 19}, "source": ["## [**Transition Layers**]\n", "\n", "Since each dense block will increase the number of channels, adding too many of them will lead to an excessively complex model. A *transition layer* is used to control the complexity of the model. It reduces the number of channels by using a $1\\times 1$ convolution. Moreover, it halves the height and width via average pooling with a stride of 2.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "6160cc48", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:28.915937Z", "iopub.status.busy": "2023-08-18T19:47:28.914796Z", "iopub.status.idle": "2023-08-18T19:47:28.920281Z", "shell.execute_reply": "2023-08-18T19:47:28.919184Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [], "source": ["def transition_block(num_channels):\n", "    return nn.Sequential(\n", "        nn.LazyBatchNorm2d(), nn.ReLU(),\n", "        nn.LazyConv2d(num_channels, kernel_size=1),\n", "        nn.AvgPool2d(kernel_size=2, stride=2))"]}, {"cell_type": "markdown", "id": "280d3329", "metadata": {"origin_pos": 24}, "source": ["[**Apply a transition layer**] with 10 channels to the output of the dense block in the previous example.  This reduces the number of output channels to 10, and halves the height and width.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "fc0cacfc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:28.924597Z", "iopub.status.busy": "2023-08-18T19:47:28.924323Z", "iopub.status.idle": "2023-08-18T19:47:28.938373Z", "shell.execute_reply": "2023-08-18T19:47:28.937285Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([4, 10, 4, 4])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["blk = transition_block(10)\n", "blk(Y).shape"]}, {"cell_type": "markdown", "id": "b9bc8e5c", "metadata": {"origin_pos": 29}, "source": ["## [**DenseNet Model**]\n", "\n", "Next, we will construct a DenseNet model. DenseNet first uses the same single convolutional layer and max-pooling layer as in ResNet.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "79e0aaa1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:28.942331Z", "iopub.status.busy": "2023-08-18T19:47:28.941755Z", "iopub.status.idle": "2023-08-18T19:47:28.946845Z", "shell.execute_reply": "2023-08-18T19:47:28.946015Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [], "source": ["class DenseNet(d2l.Classifier):\n", "    def b1(self):\n", "        return nn.Sequential(\n", "            nn.LazyConv2d(64, kernel_size=7, stride=2, padding=3),\n", "            nn.LazyBatchNorm2d(), nn.ReLU(),\n", "            nn.MaxPool2d(kernel_size=3, stride=2, padding=1))"]}, {"cell_type": "markdown", "id": "6dccb085", "metadata": {"origin_pos": 32}, "source": ["Then, similar to the four modules made up of residual blocks that ResNet uses,\n", "DenseNet uses four dense blocks.\n", "As with ResNet, we can set the number of convolutional layers used in each dense block. Here, we set it to 4, consistent with the ResNet-18 model in :numref:`sec_resnet`. Furthermore, we set the number of channels (i.e., growth rate) for the convolutional layers in the dense block to 32, so 128 channels will be added to each dense block.\n", "\n", "In ResNet, the height and width are reduced between each module by a residual block with a stride of 2. Here, we use the transition layer to halve the height and width and halve the number of channels. Similar to ResNet, a global pooling layer and a fully connected layer are connected at the end to produce the output.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "58137883", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:28.950027Z", "iopub.status.busy": "2023-08-18T19:47:28.949746Z", "iopub.status.idle": "2023-08-18T19:47:28.958660Z", "shell.execute_reply": "2023-08-18T19:47:28.957444Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(DenseNet)\n", "def __init__(self, num_channels=64, growth_rate=32, arch=(4, 4, 4, 4),\n", "             lr=0.1, num_classes=10):\n", "    super(DenseNet, self).__init__()\n", "    self.save_hyperparameters()\n", "    self.net = nn.Sequential(self.b1())\n", "    for i, num_convs in enumerate(arch):\n", "        self.net.add_module(f'dense_blk{i+1}', DenseBlock(num_convs,\n", "                                                          growth_rate))\n", "        # The number of output channels in the previous dense block\n", "        num_channels += num_convs * growth_rate\n", "        # A transition layer that halves the number of channels is added\n", "        # between the dense blocks\n", "        if i != len(arch) - 1:\n", "            num_channels //= 2\n", "            self.net.add_module(f'tran_blk{i+1}', transition_block(\n", "                num_channels))\n", "    self.net.add_module('last', nn.Sequential(\n", "        nn.LazyBatchNorm2d(), nn.ReLU(),\n", "        nn.AdaptiveAvgPool2d((1, 1)), nn.<PERSON><PERSON>(),\n", "        nn.<PERSON><PERSON><PERSON>inear(num_classes)))\n", "    self.net.apply(d2l.init_cnn)"]}, {"cell_type": "markdown", "id": "a04e481f", "metadata": {"origin_pos": 35}, "source": ["## [**Training**]\n", "\n", "Since we are using a deeper network here, in this section, we will reduce the input height and width from 224 to 96 to simplify the computation.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "ef87c44e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:28.963624Z", "iopub.status.busy": "2023-08-18T19:47:28.962964Z", "iopub.status.idle": "2023-08-18T19:50:01.060105Z", "shell.execute_reply": "2023-08-18T19:50:01.059052Z"}, "origin_pos": 36, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:50:00.878365</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mdfcceab582\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mdfcceab582\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mdfcceab582\" x=\"69.163125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(65.981875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mdfcceab582\" x=\"108.223125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(105.041875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mdfcceab582\" x=\"147.283125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(144.101875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mdfcceab582\" x=\"186.343125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(183.161875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mdfcceab582\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"ma1f8b60a8f\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma1f8b60a8f\" x=\"30.103125\" y=\"131.965781\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 135.764999) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#ma1f8b60a8f\" x=\"30.103125\" y=\"98.590862\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 102.39008) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#ma1f8b60a8f\" x=\"30.103125\" y=\"65.215942\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 69.015161) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#ma1f8b60a8f\" x=\"30.103125\" y=\"31.841023\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 35.640242) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 34.954394 13.788255 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 49.633125 79.142305 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_15\"/>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 49.633125 79.142305 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 49.633125 30.56372 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 49.633125 79.142305 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 49.633125 30.56372 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 49.633125 79.142305 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 49.633125 30.56372 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 49.633125 30.56372 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "L 171.372901 135.196197 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "L 171.372901 135.196197 \n", "L 181.11708 135.027712 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "L 171.372901 135.196197 \n", "L 181.11708 135.027712 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "L 186.343125 123.284263 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "L 171.372901 135.196197 \n", "L 181.11708 135.027712 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "L 186.343125 123.284263 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "L 186.343125 13.5 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "L 171.372901 135.196197 \n", "L 181.11708 135.027712 \n", "L 190.861259 137.528855 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "L 186.343125 123.284263 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "L 186.343125 13.5 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "L 171.372901 135.196197 \n", "L 181.11708 135.027712 \n", "L 190.861259 137.528855 \n", "L 200.605438 137.370942 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "L 186.343125 123.284263 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "L 186.343125 13.5 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "L 171.372901 135.196197 \n", "L 181.11708 135.027712 \n", "L 190.861259 137.528855 \n", "L 200.605438 137.370942 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "L 186.343125 123.284263 \n", "L 205.873125 97.72245 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "L 186.343125 13.5 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "L 171.372901 135.196197 \n", "L 181.11708 135.027712 \n", "L 190.861259 137.528855 \n", "L 200.605438 137.370942 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "L 186.343125 123.284263 \n", "L 205.873125 97.72245 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "L 186.343125 13.5 \n", "L 205.873125 20.332089 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "L 171.372901 135.196197 \n", "L 181.11708 135.027712 \n", "L 190.861259 137.528855 \n", "L 200.605438 137.370942 \n", "L 210.349618 139.5 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "L 186.343125 123.284263 \n", "L 205.873125 97.72245 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "L 186.343125 13.5 \n", "L 205.873125 20.332089 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "L 171.372901 135.196197 \n", "L 181.11708 135.027712 \n", "L 190.861259 137.528855 \n", "L 200.605438 137.370942 \n", "L 210.349618 139.5 \n", "L 220.093797 139.124826 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "L 186.343125 123.284263 \n", "L 205.873125 97.72245 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "L 186.343125 13.5 \n", "L 205.873125 20.332089 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "L 171.372901 135.196197 \n", "L 181.11708 135.027712 \n", "L 190.861259 137.528855 \n", "L 200.605438 137.370942 \n", "L 210.349618 139.5 \n", "L 220.093797 139.124826 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "L 186.343125 123.284263 \n", "L 205.873125 97.72245 \n", "L 225.403125 114.867232 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "L 186.343125 13.5 \n", "L 205.873125 20.332089 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 34.954394 13.788255 \n", "L 44.698573 86.683025 \n", "L 54.442752 103.413249 \n", "L 64.186931 110.378811 \n", "L 73.93111 115.92628 \n", "L 83.675289 119.511265 \n", "L 93.419468 123.266421 \n", "L 103.163647 123.923916 \n", "L 112.907826 127.146956 \n", "L 122.652006 128.018217 \n", "L 132.396185 130.215122 \n", "L 142.140364 130.446515 \n", "L 151.884543 133.473473 \n", "L 161.628722 133.336258 \n", "L 171.372901 135.196197 \n", "L 181.11708 135.027712 \n", "L 190.861259 137.528855 \n", "L 200.605438 137.370942 \n", "L 210.349618 139.5 \n", "L 220.093797 139.124826 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 49.633125 79.142305 \n", "L 69.163125 102.189453 \n", "L 88.693125 105.782503 \n", "L 108.223125 116.66852 \n", "L 127.753125 108.084439 \n", "L 147.283125 122.133456 \n", "L 166.813125 104.82566 \n", "L 186.343125 123.284263 \n", "L 205.873125 97.72245 \n", "L 225.403125 114.867232 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 49.633125 30.56372 \n", "L 69.163125 20.068047 \n", "L 88.693125 20.315586 \n", "L 108.223125 15.69485 \n", "L 127.753125 18.863355 \n", "L 147.283125 13.797047 \n", "L 166.813125 21.09121 \n", "L 186.343125 13.5 \n", "L 205.873125 20.332089 \n", "L 225.403125 16.272442 \n", "\" clip-path=\"url(#pf87edcb7af)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 87.957813 100.434375 \n", "L 167.548438 100.434375 \n", "Q 169.548438 100.434375 169.548438 98.434375 \n", "L 169.548438 54.565625 \n", "Q 169.548438 52.565625 167.548438 52.565625 \n", "L 87.957813 52.565625 \n", "Q 85.957813 52.565625 85.957813 54.565625 \n", "L 85.957813 98.434375 \n", "Q 85.957813 100.434375 87.957813 100.434375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_127\">\n", "     <path d=\"M 89.957813 60.664063 \n", "L 99.957813 60.664063 \n", "L 109.957813 60.664063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(117.957813 64.164063) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_128\">\n", "     <path d=\"M 89.957813 75.620313 \n", "L 99.957813 75.620313 \n", "L 109.957813 75.620313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(117.957813 79.120313) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_129\">\n", "     <path d=\"M 89.957813 90.576563 \n", "L 99.957813 90.576563 \n", "L 109.957813 90.576563 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(117.957813 94.076563) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf87edcb7af\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = DenseNet(lr=0.01)\n", "trainer = d2l.Trainer(max_epochs=10, num_gpus=1)\n", "data = d2l.FashionMNIST(batch_size=128, resize=(96, 96))\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "bacabdce", "metadata": {"origin_pos": 38}, "source": ["## Summary and Discussion\n", "\n", "The main components that comprise DenseNet are dense blocks and transition layers. For the latter, we need to keep the dimensionality under control when composing the network by adding transition layers that shrink the number of channels again.\n", "In terms of cross-layer connections, in contrast to ResNet, where inputs and outputs are added together, DenseNet concatenates inputs and outputs on the channel dimension.\n", "Although these concatenation operations\n", "reuse features to achieve computational efficiency,\n", "unfortunately they lead to heavy GPU memory consumption.\n", "As a result,\n", "applying DenseNet may require more memory-efficient implementations that may increase training time :cite:`pleiss2017memory`.\n", "\n", "\n", "## Exercises\n", "\n", "1. Why do we use average pooling rather than max-pooling in the transition layer?\n", "1. One of the advantages mentioned in the DenseNet paper is that its model parameters are smaller than those of ResNet. Why is this the case?\n", "1. One problem for which DenseNet has been criticized is its high memory consumption.\n", "    1. Is this really the case? Try to change the input shape to $224\\times 224$ to compare the actual GPU memory consumption empirically.\n", "    1. Can you think of an alternative means of reducing the memory consumption? How would you need to change the framework?\n", "1. Implement the various DenseNet versions presented in Table 1 of the DenseNet paper :cite:`<PERSON><PERSON>.<PERSON>-<PERSON>-Maaten.ea.2017`.\n", "1. Design an MLP-based model by applying the DenseNet idea. Apply it to the housing price prediction task in :numref:`sec_kaggle_house`.\n"]}, {"cell_type": "markdown", "id": "a06d0ab9", "metadata": {"origin_pos": 40, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/88)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
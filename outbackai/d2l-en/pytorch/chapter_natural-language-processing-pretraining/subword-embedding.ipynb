{"cells": [{"cell_type": "markdown", "id": "3cb7c246", "metadata": {"origin_pos": 0}, "source": ["# Subword Embedding\n", ":label:`sec_fasttext`\n", "\n", "In English,\n", "words such as\n", "\"helps\", \"helped\", and \"helping\" are \n", "inflected forms of the same word \"help\".\n", "The relationship \n", "between \"dog\" and \"dogs\"\n", "is the same as \n", "that between \"cat\" and \"cats\",\n", "and \n", "the relationship \n", "between \"boy\" and \"boyfriend\"\n", "is the same as \n", "that between \"girl\" and \"girlfriend\".\n", "In other languages\n", "such as French and Spanish,\n", "many verbs have over 40 inflected forms,\n", "while in Finnish,\n", "a noun may have up to 15 cases.\n", "In linguistics,\n", "morphology studies word formation and word relationships.\n", "However,\n", "the internal structure of words\n", "was neither explored in word2vec\n", "nor in GloVe.\n", "\n", "## The fastText Model\n", "\n", "Recall how words are represented in word2vec.\n", "In both the skip-gram model\n", "and the continuous bag-of-words model,\n", "different inflected forms of the same word\n", "are directly represented by different vectors\n", "without shared parameters.\n", "To use morphological information,\n", "the *fastText* model\n", "proposed a *subword embedding* approach,\n", "where a subword is a character $n$-gram :cite:`<PERSON><PERSON><PERSON>.Grave.Joulin.ea.2017`.\n", "Instead of learning word-level vector representations,\n", "fastText can be considered as\n", "the subword-level skip-gram,\n", "where each *center word* is represented by the sum of \n", "its subword vectors.\n", "\n", "Let's illustrate how to obtain \n", "subwords for each center word in fastText\n", "using the word \"where\".\n", "First, add special characters “&lt;” and “&gt;” \n", "at the beginning and end of the word to distinguish prefixes and suffixes from other subwords. \n", "Then, extract character $n$-grams from the word.\n", "For example, when $n=3$,\n", "we obtain all subwords of length 3: \"&lt;wh\", \"whe\", \"her\", \"ere\", \"re&gt;\", and the special subword \"&lt;where&gt;\".\n", "\n", "\n", "In fastText, for any word $w$,\n", "denote by $\\mathcal{G}_w$\n", "the union of all its subwords of length between 3 and 6\n", "and its special subword.\n", "The vocabulary \n", "is the union of the subwords of all words.\n", "Letting $\\mathbf{z}_g$\n", "be the vector of subword $g$ in the dictionary,\n", "the vector $\\mathbf{v}_w$ for \n", "word $w$ as a center word\n", "in the skip-gram model\n", "is the sum of its subword vectors:\n", "\n", "$$\\mathbf{v}_w = \\sum_{g\\in\\mathcal{G}_w} \\mathbf{z}_g.$$\n", "\n", "The rest of fastText is the same as the skip-gram model. Compared with the skip-gram model, \n", "the vocabulary in fastText is larger,\n", "resulting in more model parameters. \n", "Besides, \n", "to calculate the representation of a word,\n", "all its subword vectors\n", "have to be summed,\n", "leading to higher computational complexity.\n", "However,\n", "thanks to shared parameters from subwords among words with similar structures,\n", "rare words and even out-of-vocabulary words\n", "may obtain better vector representations in fastText.\n", "\n", "\n", "\n", "## Byte Pair Encoding\n", ":label:`subsec_Byte_Pair_Encoding`\n", "\n", "In fastText, all the extracted subwords have to be of the specified lengths, such as $3$ to $6$, thus the vocabulary size cannot be predefined.\n", "To allow for variable-length subwords in a fixed-size vocabulary,\n", "we can apply a compression algorithm\n", "called *byte pair encoding* (BPE) to extract subwords :cite:`Sennrich.Haddow.Birch.2015`.\n", "\n", "Byte pair encoding performs a statistical analysis of the training dataset to discover common symbols within a word,\n", "such as consecutive characters of arbitrary length.\n", "Starting from symbols of length 1,\n", "byte pair encoding iteratively merges the most frequent pair of consecutive symbols to produce new longer symbols.\n", "Note that for efficiency, pairs crossing word boundaries are not considered.\n", "In the end, we can use such symbols as subwords to segment words.\n", "Byte pair encoding and its variants has been used for input representations in popular natural language processing pretraining models such as GPT-2 :cite:`Radford.Wu.Child.ea.2019` and RoBERTa :cite:`Liu.Ott.Goyal.ea.2019`.\n", "In the following, we will illustrate how byte pair encoding works.\n", "\n", "First, we initialize the vocabulary of symbols as all the English lowercase characters, a special end-of-word symbol `'_'`, and a special unknown symbol `'[UNK]'`.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d4071347", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:19.191865Z", "iopub.status.busy": "2023-08-18T19:41:19.191541Z", "iopub.status.idle": "2023-08-18T19:41:19.201332Z", "shell.execute_reply": "2023-08-18T19:41:19.200311Z"}, "origin_pos": 1, "tab": ["pytorch"]}, "outputs": [], "source": ["import collections\n", "\n", "symbols = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',\n", "           'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',\n", "           '_', '[UNK]']"]}, {"cell_type": "markdown", "id": "532f3025", "metadata": {"origin_pos": 2}, "source": ["Since we do not consider symbol pairs that cross boundaries of words,\n", "we only need a dictionary `raw_token_freqs` that maps words to their frequencies (number of occurrences)\n", "in a dataset.\n", "Note that the special symbol `'_'` is appended to each word so that\n", "we can easily recover a word sequence (e.g., \"a taller man\")\n", "from a sequence of output symbols ( e.g., \"a_ tall er_ man\").\n", "Since we start the merging process from a vocabulary of only single characters and special symbols, space is inserted between every pair of consecutive characters within each word (keys of the dictionary `token_freqs`).\n", "In other words, space is the delimiter between symbols within a word.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "39297a70", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:19.205282Z", "iopub.status.busy": "2023-08-18T19:41:19.204498Z", "iopub.status.idle": "2023-08-18T19:41:19.214291Z", "shell.execute_reply": "2023-08-18T19:41:19.213303Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["{'f a s t _': 4, 'f a s t e r _': 3, 't a l l _': 5, 't a l l e r _': 4}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["raw_token_freqs = {'fast_': 4, 'faster_': 3, 'tall_': 5, 'taller_': 4}\n", "token_freqs = {}\n", "for token, freq in raw_token_freqs.items():\n", "    token_freqs[' '.join(list(token))] = raw_token_freqs[token]\n", "token_freqs"]}, {"cell_type": "markdown", "id": "7daa6f08", "metadata": {"origin_pos": 4}, "source": ["We define the following `get_max_freq_pair` function that\n", "returns the most frequent pair of consecutive symbols within a word,\n", "where words come from keys of the input dictionary `token_freqs`.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "b17d5ac7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:19.218343Z", "iopub.status.busy": "2023-08-18T19:41:19.217648Z", "iopub.status.idle": "2023-08-18T19:41:19.224315Z", "shell.execute_reply": "2023-08-18T19:41:19.223245Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_max_freq_pair(token_freqs):\n", "    pairs = collections.defaultdict(int)\n", "    for token, freq in token_freqs.items():\n", "        symbols = token.split()\n", "        for i in range(len(symbols) - 1):\n", "            # Key of `pairs` is a tuple of two consecutive symbols\n", "            pairs[symbols[i], symbols[i + 1]] += freq\n", "    return max(pairs, key=pairs.get)  # Key of `pairs` with the max value"]}, {"cell_type": "markdown", "id": "c6a407bb", "metadata": {"origin_pos": 6}, "source": ["As a greedy approach based on frequency of consecutive symbols,\n", "byte pair encoding will use the following `merge_symbols` function to merge the most frequent pair of consecutive symbols to produce new symbols.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "b2a9f61d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:19.228421Z", "iopub.status.busy": "2023-08-18T19:41:19.227879Z", "iopub.status.idle": "2023-08-18T19:41:19.232827Z", "shell.execute_reply": "2023-08-18T19:41:19.231885Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["def merge_symbols(max_freq_pair, token_freqs, symbols):\n", "    symbols.append(''.join(max_freq_pair))\n", "    new_token_freqs = dict()\n", "    for token, freq in token_freqs.items():\n", "        new_token = token.replace(' '.join(max_freq_pair),\n", "                                  ''.join(max_freq_pair))\n", "        new_token_freqs[new_token] = token_freqs[token]\n", "    return new_token_freqs"]}, {"cell_type": "markdown", "id": "e766a10d", "metadata": {"origin_pos": 8}, "source": ["Now we iteratively perform the byte pair encoding algorithm over the keys of the dictionary `token_freqs`. In the first iteration, the most frequent pair of consecutive symbols are `'t'` and `'a'`, thus byte pair encoding merges them to produce a new symbol `'ta'`. In the second iteration, byte pair encoding continues to merge `'ta'` and `'l'` to result in another new symbol `'tal'`.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "31c4b2e1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:19.236575Z", "iopub.status.busy": "2023-08-18T19:41:19.235901Z", "iopub.status.idle": "2023-08-18T19:41:19.245956Z", "shell.execute_reply": "2023-08-18T19:41:19.244817Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["merge #1: ('t', 'a')\n", "merge #2: ('ta', 'l')\n", "merge #3: ('tal', 'l')\n", "merge #4: ('f', 'a')\n", "merge #5: ('fa', 's')\n", "merge #6: ('fas', 't')\n", "merge #7: ('e', 'r')\n", "merge #8: ('er', '_')\n", "merge #9: ('tall', '_')\n", "merge #10: ('fast', '_')\n"]}], "source": ["num_merges = 10\n", "for i in range(num_merges):\n", "    max_freq_pair = get_max_freq_pair(token_freqs)\n", "    token_freqs = merge_symbols(max_freq_pair, token_freqs, symbols)\n", "    print(f'merge #{i + 1}:', max_freq_pair)"]}, {"cell_type": "markdown", "id": "33be8e54", "metadata": {"origin_pos": 10}, "source": ["After 10 iterations of byte pair encoding, we can see that list `symbols` now contains 10 more symbols that are iteratively merged from other symbols.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "610ee437", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:19.249671Z", "iopub.status.busy": "2023-08-18T19:41:19.249141Z", "iopub.status.idle": "2023-08-18T19:41:19.253740Z", "shell.execute_reply": "2023-08-18T19:41:19.252701Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '_', '[UNK]', 'ta', 'tal', 'tall', 'fa', 'fas', 'fast', 'er', 'er_', 'tall_', 'fast_']\n"]}], "source": ["print(symbols)"]}, {"cell_type": "markdown", "id": "26c88860", "metadata": {"origin_pos": 12}, "source": ["For the same dataset specified in the keys of the dictionary `raw_token_freqs`,\n", "each word in the dataset is now segmented by subwords \"fast_\", \"fast\", \"er_\", \"tall_\", and \"tall\"\n", "as a result of the byte pair encoding algorithm.\n", "For instance, words \"faster_\" and \"taller_\" are segmented as \"fast er_\" and \"tall er_\", respectively.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "dede4390", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:19.257572Z", "iopub.status.busy": "2023-08-18T19:41:19.256827Z", "iopub.status.idle": "2023-08-18T19:41:19.262643Z", "shell.execute_reply": "2023-08-18T19:41:19.261492Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['fast_', 'fast er_', 'tall_', 'tall er_']\n"]}], "source": ["print(list(token_freqs.keys()))"]}, {"cell_type": "markdown", "id": "60999bd7", "metadata": {"origin_pos": 14}, "source": ["Note that the result of byte pair encoding depends on the dataset being used.\n", "We can also use the subwords learned from one dataset\n", "to segment words of another dataset.\n", "As a greedy approach, the following `segment_BPE` function tries to break words into the longest possible subwords from the input argument `symbols`.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "166b20c3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:19.266628Z", "iopub.status.busy": "2023-08-18T19:41:19.265711Z", "iopub.status.idle": "2023-08-18T19:41:19.272239Z", "shell.execute_reply": "2023-08-18T19:41:19.271387Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["def segment_BPE(tokens, symbols):\n", "    outputs = []\n", "    for token in tokens:\n", "        start, end = 0, len(token)\n", "        cur_output = []\n", "        # Segment token with the longest possible subwords from symbols\n", "        while start < len(token) and start < end:\n", "            if token[start: end] in symbols:\n", "                cur_output.append(token[start: end])\n", "                start = end\n", "                end = len(token)\n", "            else:\n", "                end -= 1\n", "        if start < len(token):\n", "            cur_output.append('[UNK]')\n", "        outputs.append(' '.join(cur_output))\n", "    return outputs"]}, {"cell_type": "markdown", "id": "e91df344", "metadata": {"origin_pos": 16}, "source": ["In the following, we use the subwords in list `symbols`, which is learned from the aforementioned dataset,\n", "to segment `tokens` that represent another dataset.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "e0dd3cac", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:19.275736Z", "iopub.status.busy": "2023-08-18T19:41:19.275209Z", "iopub.status.idle": "2023-08-18T19:41:19.280859Z", "shell.execute_reply": "2023-08-18T19:41:19.279709Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['tall e s t _', 'fa t t er_']\n"]}], "source": ["tokens = ['tallest_', 'fatter_']\n", "print(segment_BPE(tokens, symbols))"]}, {"cell_type": "markdown", "id": "5ee69b66", "metadata": {"origin_pos": 18}, "source": ["## Summary\n", "\n", "* The fastText model proposes a subword embedding approach. Based on the skip-gram model in word2vec, it represents a center word as the sum of its subword vectors.\n", "* Byte pair encoding performs a statistical analysis of the training dataset to discover common symbols within a word. As a greedy approach, byte pair encoding iteratively merges the most frequent pair of consecutive symbols.\n", "* Subword embedding may improve the quality of representations of rare words and out-of-dictionary words.\n", "\n", "## Exercises\n", "\n", "1. As an example, there are about $3\\times 10^8$ possible  $6$-grams in English. What is the issue when there are too many subwords? How to address the issue? Hint: refer to the end of Section 3.2 of the fastText paper :cite:`Bojanowski.Grave.Joulin.ea.2017`.\n", "1. How to design a subword embedding model based on the continuous bag-of-words model?\n", "1. To get a vocabulary of size $m$, how many merging operations are needed when the initial symbol vocabulary size is $n$?\n", "1. How to extend the idea of byte pair encoding to extract phrases?\n"]}, {"cell_type": "markdown", "id": "8a195613", "metadata": {"origin_pos": 20, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/4587)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "d2043d21", "metadata": {"origin_pos": 0}, "source": ["# Bidirectional Encoder Representations from Transformers (BERT)\n", ":label:`sec_bert`\n", "\n", "We have introduced several word embedding models for natural language understanding.\n", "After pretraining, the output can be thought of as a matrix\n", "where each row is a vector that represents a word of a predefined vocabulary.\n", "In fact, these word embedding models are all *context-independent*.\n", "Let's begin by illustrating this property.\n", "\n", "\n", "## From Context-Independent to Context-Sensitive\n", "\n", "Recall the experiments in :numref:`sec_word2vec_pretraining` and :numref:`sec_synonyms`.\n", "For instance, word2vec and GloVe both assign the same pretrained vector to the same word regardless of the context of the word (if any).\n", "Formally, a context-independent representation of any token $x$\n", "is a function $f(x)$ that only takes $x$ as its input.\n", "Given the abundance of polysemy and complex semantics in natural languages,\n", "context-independent representations have obvious limitations.\n", "For instance, the word \"crane\" in contexts\n", "\"a crane is flying\" and \"a crane driver came\" has completely different meanings;\n", "thus, the same word may be assigned different representations depending on contexts.\n", "\n", "This motivates the development of *context-sensitive* word representations,\n", "where representations of words depend on their contexts.\n", "Hence, a context-sensitive representation of token $x$ is a function $f(x, c(x))$\n", "depending on both $x$ and its context $c(x)$.\n", "Popular context-sensitive representations\n", "include TagLM (language-model-augmented sequence tagger) :cite:`Peters.Ammar.Bhagavatula.ea.2017`,\n", "CoVe (Context Vectors) :cite:`McCann.Bradbury.Xiong.ea.2017`,\n", "and ELMo (Embeddings from Language Models) :cite:`Peters.Neumann.Iyyer.ea.2018`.\n", "\n", "For example, by taking the entire sequence as input,\n", "ELMo is a function that assigns a representation to each word from the input sequence.\n", "Specifically, ELMo combines all the intermediate layer representations from pretrained bidirectional LSTM as the output representation.\n", "Then the ELMo representation will be added to a downstream task's existing supervised model\n", "as additional features, such as by concatenating ELMo representation and the original representation (e.g., GloVe) of tokens in the existing model.\n", "On the one hand,\n", "all the weights in the pretrained bidirectional LSTM model are frozen after ELMo representations are added.\n", "On the other hand,\n", "the existing supervised model is specifically customized for a given task.\n", "Leveraging different best models for different tasks at that time,\n", "adding ELMo improved the state of the art across six natural language processing tasks:\n", "sentiment analysis, natural language inference,\n", "semantic role labeling, coreference resolution,\n", "named entity recognition, and question answering.\n", "\n", "\n", "## From Task-Specific to Task-Agnostic\n", "\n", "Although ELMo has significantly improved solutions to a diverse set of natural language processing tasks,\n", "each solution still hinges on a *task-specific* architecture.\n", "However, it is practically non-trivial to craft a specific architecture for every natural language processing task.\n", "The GPT (Generative Pre-Training) model represents an effort in designing\n", "a general *task-agnostic* model for context-sensitive representations :cite:`<PERSON><PERSON>.Narasimhan.Salimans.ea.2018`.\n", "Built on a Transformer decoder,\n", "GPT pretrains a language model that will be used to represent text sequences.\n", "When applying GPT to a downstream task,\n", "the output of the language model will be fed into an added linear output layer\n", "to predict the label of the task.\n", "In sharp contrast to ELMo that freezes parameters of the pretrained model,\n", "GPT fine-tunes *all* the parameters in the pretrained Transformer decoder\n", "during supervised learning of the downstream task.\n", "GPT was evaluated on twelve tasks of natural language inference,\n", "question answering, sentence similarity, and classification,\n", "and improved the state of the art in nine of them with minimal changes\n", "to the model architecture.\n", "\n", "However, due to the autoregressive nature of language models,\n", "GPT only looks forward (left-to-right).\n", "In contexts \"i went to the bank to deposit cash\" and \"i went to the bank to sit down\",\n", "as \"bank\" is sensitive to the context to its left,\n", "GPT will return the same representation for \"bank\",\n", "though it has different meanings.\n", "\n", "\n", "## BERT: Combining the Best of Both Worlds\n", "\n", "As we have seen,\n", "ELMo encodes context bidirectionally but uses task-specific architectures;\n", "while GPT is task-agnostic but encodes context left-to-right.\n", "Combining the best of both worlds,\n", "BERT (Bidirectional Encoder Representations from Transformers)\n", "encodes context bidirectionally and requires minimal architecture changes\n", "for a wide range of natural language processing tasks :cite:`Devlin.Chang.Lee.ea.2018`.\n", "Using a pretrained Transformer encoder,\n", "BERT is able to represent any token based on its bidirectional context.\n", "During supervised learning of downstream tasks,\n", "BERT is similar to GPT in two aspects.\n", "First, BERT representations will be fed into an added output layer,\n", "with minimal changes to the model architecture depending on nature of tasks,\n", "such as predicting for every token vs. predicting for the entire sequence.\n", "Second,\n", "all the parameters of the pretrained Transformer encoder are fine-tuned,\n", "while the additional output layer will be trained from scratch.\n", ":numref:`fig_elmo-gpt-bert` depicts the differences among ELMo, GPT, and BERT.\n", "\n", "![A comparison of ELMo, GPT, and BERT.](../img/elmo-gpt-bert.svg)\n", ":label:`fig_elmo-gpt-bert`\n", "\n", "\n", "BERT further improved the state of the art on eleven natural language processing tasks\n", "under broad categories of (i) single text classification (e.g., sentiment analysis), (ii) text pair classification (e.g., natural language inference),\n", "(iii) question answering, (iv) text tagging (e.g., named entity recognition).\n", "All proposed in 2018,\n", "from context-sensitive ELMo to task-agnostic GPT and BERT,\n", "conceptually simple yet empirically powerful pretraining of deep representations for natural languages have revolutionized solutions to various natural language processing tasks.\n", "\n", "In the rest of this chapter,\n", "we will dive into the pretraining of BERT.\n", "When natural language processing applications are explained in :numref:`chap_nlp_app`,\n", "we will illustrate fine-tuning of BERT for downstream applications.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "ffa5c8df", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:51.331685Z", "iopub.status.busy": "2023-08-18T19:31:51.331049Z", "iopub.status.idle": "2023-08-18T19:31:54.812815Z", "shell.execute_reply": "2023-08-18T19:31:54.811897Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "c86eb35f", "metadata": {"origin_pos": 3}, "source": ["## [**Input Representation**]\n", ":label:`subsec_bert_input_rep`\n", "\n", "In natural language processing,\n", "some tasks (e.g., sentiment analysis) take single text as input,\n", "while in some other tasks (e.g., natural language inference),\n", "the input is a pair of text sequences.\n", "The BERT input sequence unambiguously represents both single text and text pairs.\n", "In the former,\n", "the BERT input sequence is the concatenation of\n", "the special classification token “&lt;cls&gt;”,\n", "tokens of a text sequence,\n", "and the special separation token “&lt;sep&gt;”.\n", "In the latter,\n", "the BERT input sequence is the concatenation of\n", "“&lt;cls&gt;”, tokens of the first text sequence,\n", "“&lt;sep&gt;”, tokens of the second text sequence, and “&lt;sep&gt;”.\n", "We will consistently distinguish the terminology \"BERT input sequence\"\n", "from other types of \"sequences\".\n", "For instance, one *BERT input sequence* may include either one *text sequence* or two *text sequences*.\n", "\n", "To distinguish text pairs,\n", "the learned segment embeddings $\\mathbf{e}_A$ and $\\mathbf{e}_B$\n", "are added to the token embeddings of the first sequence and the second sequence, respectively.\n", "For single text inputs, only $\\mathbf{e}_A$ is used.\n", "\n", "The following `get_tokens_and_segments` takes either one sentence or two sentences\n", "as input, then returns tokens of the BERT input sequence\n", "and their corresponding segment IDs.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "3c018a43", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:54.816440Z", "iopub.status.busy": "2023-08-18T19:31:54.816038Z", "iopub.status.idle": "2023-08-18T19:31:54.823105Z", "shell.execute_reply": "2023-08-18T19:31:54.822123Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def get_tokens_and_segments(tokens_a, tokens_b=None):\n", "    \"\"\"Get tokens of the BERT input sequence and their segment IDs.\"\"\"\n", "    tokens = ['<cls>'] + tokens_a + ['<sep>']\n", "    # 0 and 1 are marking segment A and B, respectively\n", "    segments = [0] * (len(tokens_a) + 2)\n", "    if tokens_b is not None:\n", "        tokens += tokens_b + ['<sep>']\n", "        segments += [1] * (len(tokens_b) + 1)\n", "    return tokens, segments"]}, {"cell_type": "markdown", "id": "948cea11", "metadata": {"origin_pos": 5}, "source": ["BERT chooses the Transformer encoder as its bidirectional architecture.\n", "Common in the Transformer encoder,\n", "positional embeddings are added at every position of the BERT input sequence.\n", "However, different from the original Transformer encoder,\n", "BERT uses *learnable* positional embeddings.\n", "To sum up, :numref:`fig_bert-input` shows that\n", "the embeddings of the BERT input sequence are the sum\n", "of the token embeddings, segment embeddings, and positional embeddings.\n", "\n", "![The embeddings of the BERT input sequence are the sum\n", "of the token embeddings, segment embeddings, and positional embeddings.](../img/bert-input.svg)\n", ":label:`fig_bert-input`\n", "\n", "The following [**`BERTEncoder` class**] is similar to the `TransformerEncoder` class\n", "as implemented in :numref:`sec_transformer`.\n", "Different from `TransformerEncoder`, `BERTEncoder` uses\n", "segment embeddings and learnable positional embeddings.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "a90baa53", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:54.828159Z", "iopub.status.busy": "2023-08-18T19:31:54.827512Z", "iopub.status.idle": "2023-08-18T19:31:54.835645Z", "shell.execute_reply": "2023-08-18T19:31:54.834698Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class BERTEncoder(nn.Module):\n", "    \"\"\"BERT encoder.\"\"\"\n", "    def __init__(self, vocab_size, num_hiddens, ffn_num_hiddens, num_heads,\n", "                 num_blks, dropout, max_len=1000, **kwargs):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.token_embedding = nn.Embedding(vocab_size, num_hiddens)\n", "        self.segment_embedding = nn.Embedding(2, num_hiddens)\n", "        self.blks = nn.Sequential()\n", "        for i in range(num_blks):\n", "            self.blks.add_module(f\"{i}\", d2l.TransformerEncoderBlock(\n", "                num_hiddens, ffn_num_hiddens, num_heads, dropout, True))\n", "        # In BERT, positional embeddings are learnable, thus we create a\n", "        # parameter of positional embeddings that are long enough\n", "        self.pos_embedding = nn.Parameter(torch.randn(1, max_len,\n", "                                                      num_hiddens))\n", "\n", "    def forward(self, tokens, segments, valid_lens):\n", "        # Shape of `X` remains unchanged in the following code snippet:\n", "        # (batch size, max sequence length, `num_hiddens`)\n", "        X = self.token_embedding(tokens) + self.segment_embedding(segments)\n", "        X = X + self.pos_embedding[:, :X.shape[1], :]\n", "        for blk in self.blks:\n", "            X = blk(X, valid_lens)\n", "        return X"]}, {"cell_type": "markdown", "id": "ab99ce42", "metadata": {"origin_pos": 8}, "source": ["Suppose that the vocabulary size is 10000.\n", "To demonstrate forward [**inference of `BERTEncoder`**],\n", "let's create an instance of it and initialize its parameters.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "7eb45fe4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:54.840002Z", "iopub.status.busy": "2023-08-18T19:31:54.839108Z", "iopub.status.idle": "2023-08-18T19:31:54.985512Z", "shell.execute_reply": "2023-08-18T19:31:54.984301Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["vocab_size, num_hiddens, ffn_num_hiddens, num_heads = 10000, 768, 1024, 4\n", "ffn_num_input, num_blks, dropout = 768, 2, 0.2\n", "encoder = BERTEncoder(vocab_size, num_hiddens, ffn_num_hiddens, num_heads,\n", "                      num_blks, dropout)"]}, {"cell_type": "markdown", "id": "7c52eb2f", "metadata": {"origin_pos": 11}, "source": ["We define `tokens` to be 2 BERT input sequences of length 8,\n", "where each token is an index of the vocabulary.\n", "The forward inference of `BERTEncoder` with the input `tokens`\n", "returns the encoded result where each token is represented by a vector\n", "whose length is predefined by the hyperparameter `num_hiddens`.\n", "This hyperparameter is usually referred to as the *hidden size*\n", "(number of hidden units) of the Transformer encoder.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "56903d71", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:54.990720Z", "iopub.status.busy": "2023-08-18T19:31:54.989911Z", "iopub.status.idle": "2023-08-18T19:31:55.148261Z", "shell.execute_reply": "2023-08-18T19:31:55.147176Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 8, 768])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["tokens = torch.randint(0, vocab_size, (2, 8))\n", "segments = torch.tensor([[0, 0, 0, 0, 1, 1, 1, 1], [0, 0, 0, 1, 1, 1, 1, 1]])\n", "encoded_X = encoder(tokens, segments, None)\n", "encoded_X.shape"]}, {"cell_type": "markdown", "id": "09881b5d", "metadata": {"origin_pos": 14}, "source": ["## Pretraining Tasks\n", ":label:`subsec_bert_pretraining_tasks`\n", "\n", "The forward inference of `BERTEncoder` gives the BERT representation\n", "of each token of the input text and the inserted\n", "special tokens “&lt;cls&gt;” and “&lt;seq&gt;”.\n", "Next, we will use these representations to compute the loss function\n", "for pretraining BERT.\n", "The pretraining is composed of the following two tasks:\n", "masked language modeling and next sentence prediction.\n", "\n", "### [**Masked Language Modeling**]\n", ":label:`subsec_mlm`\n", "\n", "As illustrated in :numref:`sec_language-model`,\n", "a language model predicts a token using the context on its left.\n", "To encode context bidirectionally for representing each token,\n", "BERT randomly masks tokens and uses tokens from the bidirectional context to\n", "predict the masked tokens in a self-supervised fashion.\n", "This task is referred to as a *masked language model*.\n", "\n", "In this pretraining task,\n", "15% of tokens will be selected at random as the masked tokens for prediction.\n", "To predict a masked token without cheating by using the label,\n", "one straightforward approach is to always replace it with a special “&lt;mask&gt;” token in the BERT input sequence.\n", "However, the artificial special token “&lt;mask&gt;” will never appear\n", "in fine-tuning.\n", "To avoid such a mismatch between pretraining and fine-tuning,\n", "if a token is masked for prediction (e.g., \"great\" is selected to be masked and predicted in \"this movie is great\"),\n", "in the input it will be replaced with:\n", "\n", "* a special “&lt;mask&gt;” token for 80% of the time (e.g., \"this movie is great\" becomes \"this movie is &lt;mask&gt;\");\n", "* a random token for 10% of the time (e.g., \"this movie is great\" becomes \"this movie is drink\");\n", "* the unchanged label token for 10% of the time (e.g., \"this movie is great\" becomes \"this movie is great\").\n", "\n", "Note that for 10% of 15% time a random token is inserted.\n", "This occasional noise encourages BERT to be less biased towards the masked token (especially when the label token remains unchanged) in its bidirectional context encoding.\n", "\n", "We implement the following `MaskLM` class to predict masked tokens\n", "in the masked language model task of BERT pretraining.\n", "The prediction uses a one-hidden-layer MLP (`self.mlp`).\n", "In forward inference, it takes two inputs:\n", "the encoded result of `BERTEncoder` and the token positions for prediction.\n", "The output is the prediction results at these positions.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "a8614e46", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:55.154295Z", "iopub.status.busy": "2023-08-18T19:31:55.153400Z", "iopub.status.idle": "2023-08-18T19:31:55.162155Z", "shell.execute_reply": "2023-08-18T19:31:55.161271Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class MaskLM(nn.Mo<PERSON>le):\n", "    \"\"\"The masked language model task of BERT.\"\"\"\n", "    def __init__(self, vocab_size, num_hiddens, **kwargs):\n", "        super(<PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.mlp = nn.Sequential(nn.LazyLinear(num_hiddens),\n", "                                 nn.ReLU(),\n", "                                 nn.<PERSON>er<PERSON><PERSON>(num_hiddens),\n", "                                 nn.LazyLinear(vocab_size))\n", "\n", "    def forward(self, X, pred_positions):\n", "        num_pred_positions = pred_positions.shape[1]\n", "        pred_positions = pred_positions.reshape(-1)\n", "        batch_size = X.shape[0]\n", "        batch_idx = torch.arange(0, batch_size)\n", "        # Suppose that `batch_size` = 2, `num_pred_positions` = 3, then\n", "        # `batch_idx` is `torch.tensor([0, 0, 0, 1, 1, 1])`\n", "        batch_idx = torch.repeat_interleave(batch_idx, num_pred_positions)\n", "        masked_X = X[batch_idx, pred_positions]\n", "        masked_X = masked_X.reshape((batch_size, num_pred_positions, -1))\n", "        mlm_Y_hat = self.mlp(masked_X)\n", "        return mlm_Y_hat"]}, {"cell_type": "markdown", "id": "bce1f5c9", "metadata": {"origin_pos": 17}, "source": ["To demonstrate [**the forward inference of `<PERSON><PERSON>`**],\n", "we create its instance `mlm` and initialize it.\n", "Recall that `encoded_X` from the forward inference of `BERTEncoder`\n", "represents 2 BERT input sequences.\n", "We define `mlm_positions` as the 3 indices to predict in either BERT input sequence of `encoded_X`.\n", "The forward inference of `mlm` returns prediction results `mlm_Y_hat`\n", "at all the masked positions `mlm_positions` of `encoded_X`.\n", "For each prediction, the size of the result is equal to the vocabulary size.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "6b3fc7d6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:55.166188Z", "iopub.status.busy": "2023-08-18T19:31:55.165836Z", "iopub.status.idle": "2023-08-18T19:31:55.273706Z", "shell.execute_reply": "2023-08-18T19:31:55.272680Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 3, 10000])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["mlm = MaskLM(vocab_size, num_hiddens)\n", "mlm_positions = torch.tensor([[1, 5, 2], [6, 1, 5]])\n", "mlm_Y_hat = mlm(encoded_X, mlm_positions)\n", "mlm_Y_hat.shape"]}, {"cell_type": "markdown", "id": "5cd88359", "metadata": {"origin_pos": 20}, "source": ["With the ground truth labels `mlm_Y` of the predicted tokens `mlm_Y_hat` under masks,\n", "we can calculate the cross-entropy loss of the masked language model task in BERT pretraining.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "8d85768b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:55.278830Z", "iopub.status.busy": "2023-08-18T19:31:55.278109Z", "iopub.status.idle": "2023-08-18T19:31:55.288546Z", "shell.execute_reply": "2023-08-18T19:31:55.287485Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([6])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["mlm_Y = torch.tensor([[7, 8, 9], [10, 20, 30]])\n", "loss = nn.CrossEntropyLoss(reduction='none')\n", "mlm_l = loss(mlm_Y_hat.reshape((-1, vocab_size)), mlm_Y.reshape(-1))\n", "mlm_l.shape"]}, {"cell_type": "markdown", "id": "c78b8c02", "metadata": {"origin_pos": 23}, "source": ["### [**Next Sentence Prediction**]\n", ":label:`subsec_nsp`\n", "\n", "Although masked language modeling is able to encode bidirectional context\n", "for representing words, it does not explicitly model the logical relationship\n", "between text pairs.\n", "To help understand the relationship between two text sequences,\n", "BERT considers a binary classification task, *next sentence prediction*, in its pretraining.\n", "When generating sentence pairs for pretraining,\n", "for half of the time they are indeed consecutive sentences with the label \"True\";\n", "while for the other half of the time the second sentence is randomly sampled from the corpus with the label \"False\".\n", "\n", "The following `NextSentencePred` class uses a one-hidden-layer MLP\n", "to predict whether the second sentence is the next sentence of the first\n", "in the BERT input sequence.\n", "Due to self-attention in the Transformer encoder,\n", "the BERT representation of the special token “&lt;cls&gt;”\n", "encodes both the two sentences from the input.\n", "Hence, the output layer (`self.output`) of the MLP classifier takes `X` as input,\n", "where `X` is the output of the MLP hidden layer whose input is the encoded “&lt;cls&gt;” token.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "c1951876", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:55.292806Z", "iopub.status.busy": "2023-08-18T19:31:55.291904Z", "iopub.status.idle": "2023-08-18T19:31:55.298328Z", "shell.execute_reply": "2023-08-18T19:31:55.297464Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class NextSentencePred(nn.Module):\n", "    \"\"\"The next sentence prediction task of BERT.\"\"\"\n", "    def __init__(self, **kwargs):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.output = nn.LazyLinear(2)\n", "\n", "    def forward(self, X):\n", "        # `X` shape: (batch size, `num_hiddens`)\n", "        return self.output(X)"]}, {"cell_type": "markdown", "id": "52f9f8eb", "metadata": {"origin_pos": 26}, "source": ["We can see that [**the forward inference of an `NextSentencePred`**] instance\n", "returns binary predictions for each BERT input sequence.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "aba0cce5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:55.302539Z", "iopub.status.busy": "2023-08-18T19:31:55.301869Z", "iopub.status.idle": "2023-08-18T19:31:55.310590Z", "shell.execute_reply": "2023-08-18T19:31:55.309427Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 2])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# PyTorch by default will not flatten the tensor as seen in mxnet where, if\n", "# flatten=True, all but the first axis of input data are collapsed together\n", "encoded_X = torch.flatten(encoded_X, start_dim=1)\n", "# input_shape for NSP: (batch size, `num_hiddens`)\n", "nsp = NextSentencePred()\n", "nsp_Y_hat = nsp(encoded_X)\n", "nsp_Y_hat.shape"]}, {"cell_type": "markdown", "id": "6aacce83", "metadata": {"origin_pos": 29}, "source": ["The cross-entropy loss of the 2 binary classifications can also be computed.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "ba504299", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:55.314489Z", "iopub.status.busy": "2023-08-18T19:31:55.313852Z", "iopub.status.idle": "2023-08-18T19:31:55.321256Z", "shell.execute_reply": "2023-08-18T19:31:55.320193Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["nsp_y = torch.tensor([0, 1])\n", "nsp_l = loss(nsp_Y_hat, nsp_y)\n", "nsp_l.shape"]}, {"cell_type": "markdown", "id": "21ac0df2", "metadata": {"origin_pos": 32}, "source": ["It is noteworthy that all the labels in both the aforementioned pretraining tasks\n", "can be trivially obtained from the pretraining corpus without manual labeling effort.\n", "The original BERT has been pretrained on the concatenation of BookCorpus :cite:`Zhu.Kiros.Zemel.ea.2015`\n", "and English Wikipedia.\n", "These two text corpora are huge:\n", "they have 800 million words and 2.5 billion words, respectively.\n", "\n", "\n", "## [**Putting It All Together**]\n", "\n", "When pretraining BERT, the final loss function is a linear combination of\n", "both the loss functions for masked language modeling and next sentence prediction.\n", "Now we can define the `BERTModel` class by instantiating the three classes\n", "`BERTEncoder`, `MaskLM`, and `NextSentencePred`.\n", "The forward inference returns the encoded BERT representations `encoded_X`,\n", "predictions of masked language modeling `mlm_Y_hat`,\n", "and next sentence predictions `nsp_Y_hat`.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "73c331cd", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:55.325106Z", "iopub.status.busy": "2023-08-18T19:31:55.324530Z", "iopub.status.idle": "2023-08-18T19:31:55.333301Z", "shell.execute_reply": "2023-08-18T19:31:55.332018Z"}, "origin_pos": 34, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class BERTModel(nn.Module):\n", "    \"\"\"The BERT model.\"\"\"\n", "    def __init__(self, vocab_size, num_hiddens, ffn_num_hiddens,\n", "                 num_heads, num_blks, dropout, max_len=1000):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.encoder = BERTEncoder(vocab_size, num_hiddens, ffn_num_hiddens,\n", "                                   num_heads, num_blks, dropout,\n", "                                   max_len=max_len)\n", "        self.hidden = nn.Sequential(nn.LazyLinear(num_hiddens),\n", "                                    nn.<PERSON>())\n", "        self.mlm = MaskLM(vocab_size, num_hiddens)\n", "        self.nsp = NextSentencePred()\n", "\n", "    def forward(self, tokens, segments, valid_lens=None, pred_positions=None):\n", "        encoded_X = self.encoder(tokens, segments, valid_lens)\n", "        if pred_positions is not None:\n", "            mlm_Y_hat = self.mlm(encoded_X, pred_positions)\n", "        else:\n", "            mlm_Y_hat = None\n", "        # The hidden layer of the MLP classifier for next sentence prediction.\n", "        # 0 is the index of the '<cls>' token\n", "        nsp_Y_hat = self.nsp(self.hidden(encoded_X[:, 0, :]))\n", "        return encoded_X, mlm_Y_hat, nsp_Y_hat"]}, {"cell_type": "markdown", "id": "be55112a", "metadata": {"origin_pos": 35}, "source": ["## Summary\n", "\n", "* Word embedding models such as word2vec and GloVe are context-independent. They assign the same pretrained vector to the same word regardless of the context of the word (if any). It is hard for them to handle well polysemy or complex semantics in natural languages.\n", "* For context-sensitive word representations such as ELMo and GPT, representations of words depend on their contexts.\n", "* ELMo encodes context bidirectionally but uses task-specific architectures (however, it is practically non-trivial to craft a specific architecture for every natural language processing task); while GPT is task-agnostic but encodes context left-to-right.\n", "* BERT combines the best of both worlds: it encodes context bidirectionally and requires minimal architecture changes for a wide range of natural language processing tasks.\n", "* The embeddings of the BERT input sequence are the sum of the token embeddings, segment embeddings, and positional embeddings.\n", "* Pretraining BERT is composed of two tasks: masked language modeling and next sentence prediction. The former is able to encode bidirectional context for representing words, while the latter explicitly models the logical relationship between text pairs.\n", "\n", "\n", "## Exercises\n", "\n", "1. All other things being equal, will a masked language model require more or fewer pretraining steps to converge than a left-to-right language model? Why?\n", "1. In the original implementation of BERT, the positionwise feed-forward network in `BERTEncoder` (via `d2l.TransformerEncoderBlock`) and the fully connected layer in `MaskLM` both use the Gaussian error linear unit (GELU) :cite:`Hendrycks.Gimpel.2016` as the activation function. Research into the difference between GELU and ReLU.\n"]}, {"cell_type": "markdown", "id": "9727cff7", "metadata": {"origin_pos": 37, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1490)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
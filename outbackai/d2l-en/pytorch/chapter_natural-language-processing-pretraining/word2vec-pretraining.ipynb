{"cells": [{"cell_type": "markdown", "id": "105653cd", "metadata": {"origin_pos": 0}, "source": ["# Pretraining word2vec\n", ":label:`sec_word2vec_pretraining`\n", "\n", "\n", "We go on to implement the skip-gram\n", "model defined in\n", ":numref:`sec_word2vec`.\n", "Then\n", "we will pretrain word2vec using negative sampling\n", "on the PTB dataset.\n", "First of all,\n", "let's obtain the data iterator\n", "and the vocabulary for this dataset\n", "by calling the `d2l.load_data_ptb`\n", "function, which was described in :numref:`sec_word2vec_data`\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c74744cb", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:56.397062Z", "iopub.status.busy": "2023-08-18T19:46:56.396303Z", "iopub.status.idle": "2023-08-18T19:47:13.849836Z", "shell.execute_reply": "2023-08-18T19:47:13.848880Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import math\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l\n", "\n", "batch_size, max_window_size, num_noise_words = 512, 5, 5\n", "data_iter, vocab = d2l.load_data_ptb(batch_size, max_window_size,\n", "                                     num_noise_words)"]}, {"cell_type": "markdown", "id": "edfdfb2d", "metadata": {"origin_pos": 3}, "source": ["## The Skip-Gram Model\n", "\n", "We implement the skip-gram model\n", "by using embedding layers and batch matrix multiplications.\n", "First, let's review\n", "how embedding layers work.\n", "\n", "\n", "### Embedding Layer\n", "\n", "As described in :numref:`sec_seq2seq`,\n", "an embedding layer\n", "maps a token's index to its feature vector.\n", "The weight of this layer\n", "is a matrix whose number of rows equals to\n", "the dictionary size (`input_dim`) and\n", "number of columns equals to\n", "the vector dimension for each token (`output_dim`).\n", "After a word embedding model is trained,\n", "this weight is what we need.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "f3bbd097", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:13.854261Z", "iopub.status.busy": "2023-08-18T19:47:13.853539Z", "iopub.status.idle": "2023-08-18T19:47:13.879890Z", "shell.execute_reply": "2023-08-18T19:47:13.878774Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameter embedding_weight (torch.Size([20, 4]), dtype=torch.float32)\n"]}], "source": ["embed = nn.Embedding(num_embeddings=20, embedding_dim=4)\n", "print(f'Parameter embedding_weight ({embed.weight.shape}, '\n", "      f'dtype={embed.weight.dtype})')"]}, {"cell_type": "markdown", "id": "801ec8da", "metadata": {"origin_pos": 6}, "source": ["The input of an embedding layer is the\n", "index of a token (word).\n", "For any token index $i$,\n", "its vector representation\n", "can be obtained from\n", "the $i^\\textrm{th}$ row of the weight matrix\n", "in the embedding layer.\n", "Since the vector dimension (`output_dim`)\n", "was set to 4,\n", "the embedding layer\n", "returns vectors with shape (2, 3, 4)\n", "for a minibatch of token indices with shape\n", "(2, 3).\n"]}, {"cell_type": "code", "execution_count": 3, "id": "5f462eec", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:13.884582Z", "iopub.status.busy": "2023-08-18T19:47:13.883631Z", "iopub.status.idle": "2023-08-18T19:47:13.896267Z", "shell.execute_reply": "2023-08-18T19:47:13.894870Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[[ 0.7606,  0.3872, -0.1864,  1.1732],\n", "         [ 1.5035,  2.3623, -1.7542, -1.4990],\n", "         [-1.2639, -1.5313,  2.1719,  0.4151]],\n", "\n", "        [[-1.9079,  0.2434,  1.5395,  1.2990],\n", "         [ 0.7470,  1.0129,  0.4039,  0.0591],\n", "         [-0.6293, -0.1814, -0.4782, -0.5289]]], grad_fn=<EmbeddingBackward0>)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["x = torch.tensor([[1, 2, 3], [4, 5, 6]])\n", "embed(x)"]}, {"cell_type": "markdown", "id": "61ffafa8", "metadata": {"origin_pos": 8}, "source": ["### Defining the Forward Propagation\n", "\n", "In the forward propagation,\n", "the input of the skip-gram model\n", "includes\n", "the center word indices `center`\n", "of shape (batch size, 1)\n", "and\n", "the concatenated context and noise word indices `contexts_and_negatives`\n", "of shape (batch size, `max_len`),\n", "where `max_len`\n", "is defined\n", "in :numref:`subsec_word2vec-minibatch-loading`.\n", "These two variables are first transformed from the\n", "token indices into vectors via the embedding layer,\n", "then their batch matrix multiplication\n", "(described in :numref:`subsec_batch_dot`)\n", "returns\n", "an output of shape (batch size, 1, `max_len`).\n", "Each element in the output is the dot product of\n", "a center word vector and a context or noise word vector.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "734187b9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:13.902028Z", "iopub.status.busy": "2023-08-18T19:47:13.900944Z", "iopub.status.idle": "2023-08-18T19:47:13.907239Z", "shell.execute_reply": "2023-08-18T19:47:13.906216Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["def skip_gram(center, contexts_and_negatives, embed_v, embed_u):\n", "    v = embed_v(center)\n", "    u = embed_u(contexts_and_negatives)\n", "    pred = torch.bmm(v, u.permute(0, 2, 1))\n", "    return pred"]}, {"cell_type": "markdown", "id": "5672ddf8", "metadata": {"origin_pos": 11}, "source": ["Let's print the output shape of this `skip_gram` function for some example inputs.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "265139ff", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:13.911481Z", "iopub.status.busy": "2023-08-18T19:47:13.910689Z", "iopub.status.idle": "2023-08-18T19:47:13.922107Z", "shell.execute_reply": "2023-08-18T19:47:13.920752Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 1, 4])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["skip_gram(torch.ones((2, 1), dtype=torch.long),\n", "          torch.ones((2, 4), dtype=torch.long), embed, embed).shape"]}, {"cell_type": "markdown", "id": "08a1ef11", "metadata": {"origin_pos": 14}, "source": ["## Training\n", "\n", "Before training the skip-gram model with negative sampling,\n", "let's first define its loss function.\n", "\n", "\n", "### Binary Cross-Entropy Loss\n", "\n", "According to the definition of the loss function\n", "for negative sampling in :numref:`subsec_negative-sampling`, \n", "we will use \n", "the binary cross-entropy loss.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "1f152a65", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:13.926069Z", "iopub.status.busy": "2023-08-18T19:47:13.925631Z", "iopub.status.idle": "2023-08-18T19:47:13.933662Z", "shell.execute_reply": "2023-08-18T19:47:13.932267Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["class SigmoidBCELoss(nn.Module):\n", "    # Binary cross-entropy loss with masking\n", "    def __init__(self):\n", "        super().__init__()\n", "\n", "    def forward(self, inputs, target, mask=None):\n", "        out = nn.functional.binary_cross_entropy_with_logits(\n", "            inputs, target, weight=mask, reduction=\"none\")\n", "        return out.mean(dim=1)\n", "\n", "loss = SigmoidBCELoss()"]}, {"cell_type": "markdown", "id": "ab567fa0", "metadata": {"origin_pos": 17}, "source": ["Recall our descriptions\n", "of the mask variable\n", "and the label variable in\n", ":numref:`subsec_word2vec-minibatch-loading`.\n", "The following\n", "calculates the \n", "binary cross-entropy loss\n", "for the given variables.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "41c67449", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:13.938563Z", "iopub.status.busy": "2023-08-18T19:47:13.937748Z", "iopub.status.idle": "2023-08-18T19:47:13.954225Z", "shell.execute_reply": "2023-08-18T19:47:13.952929Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0.9352, 1.8462])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pred = torch.tensor([[1.1, -2.2, 3.3, -4.4]] * 2)\n", "label = torch.tensor([[1.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0]])\n", "mask = torch.tensor([[1, 1, 1, 1], [1, 1, 0, 0]])\n", "loss(pred, label, mask) * mask.shape[1] / mask.sum(axis=1)"]}, {"cell_type": "markdown", "id": "7112ed9f", "metadata": {"origin_pos": 19}, "source": ["Below shows\n", "how the above results are calculated\n", "(in a less efficient way)\n", "using the\n", "sigmoid activation function\n", "in the binary cross-entropy loss.\n", "We can consider \n", "the two outputs as\n", "two normalized losses\n", "that are averaged over non-masked predictions.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "8bf49999", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:13.959559Z", "iopub.status.busy": "2023-08-18T19:47:13.958780Z", "iopub.status.idle": "2023-08-18T19:47:13.965184Z", "shell.execute_reply": "2023-08-18T19:47:13.964337Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9352\n", "1.8462\n"]}], "source": ["def sigmd(x):\n", "    return -math.log(1 / (1 + math.exp(-x)))\n", "\n", "print(f'{(sigmd(1.1) + sigmd(2.2) + sigmd(-3.3) + sigmd(4.4)) / 4:.4f}')\n", "print(f'{(sigmd(-1.1) + sigmd(-2.2)) / 2:.4f}')"]}, {"cell_type": "markdown", "id": "a8d4adb2", "metadata": {"origin_pos": 21}, "source": ["### Initializing Model Parameters\n", "\n", "We define two embedding layers\n", "for all the words in the vocabulary\n", "when they are used as center words\n", "and context words, respectively.\n", "The word vector dimension\n", "`embed_size` is set to 100.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "53bf9f52", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:13.970338Z", "iopub.status.busy": "2023-08-18T19:47:13.969732Z", "iopub.status.idle": "2023-08-18T19:47:13.986034Z", "shell.execute_reply": "2023-08-18T19:47:13.985180Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [], "source": ["embed_size = 100\n", "net = nn.Sequential(nn.Embedding(num_embeddings=len(vocab),\n", "                                 embedding_dim=embed_size),\n", "                    nn.Embedding(num_embeddings=len(vocab),\n", "                                 embedding_dim=embed_size))"]}, {"cell_type": "markdown", "id": "58b580f4", "metadata": {"origin_pos": 24}, "source": ["### Defining the Training Loop\n", "\n", "The training loop is defined below. Because of the existence of padding, the calculation of the loss function is slightly different compared to the previous training functions.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "4bc84232", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:13.989704Z", "iopub.status.busy": "2023-08-18T19:47:13.989147Z", "iopub.status.idle": "2023-08-18T19:47:13.997948Z", "shell.execute_reply": "2023-08-18T19:47:13.997164Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [], "source": ["def train(net, data_iter, lr, num_epochs, device=d2l.try_gpu()):\n", "    def init_weights(module):\n", "        if type(module) == nn.Embedding:\n", "            nn.init.xavier_uniform_(module.weight)\n", "    net.apply(init_weights)\n", "    net = net.to(device)\n", "    optimizer = torch.optim.Adam(net.parameters(), lr=lr)\n", "    animator = d2l.Animator(xlabel='epoch', ylabel='loss',\n", "                            xlim=[1, num_epochs])\n", "    # Sum of normalized losses, no. of normalized losses\n", "    metric = d2l.Accumulator(2)\n", "    for epoch in range(num_epochs):\n", "        timer, num_batches = d2l.Timer(), len(data_iter)\n", "        for i, batch in enumerate(data_iter):\n", "            optimizer.zero_grad()\n", "            center, context_negative, mask, label = [\n", "                data.to(device) for data in batch]\n", "\n", "            pred = skip_gram(center, context_negative, net[0], net[1])\n", "            l = (loss(pred.reshape(label.shape).float(), label.float(), mask)\n", "                     / mask.sum(axis=1) * mask.shape[1])\n", "            l.sum().backward()\n", "            optimizer.step()\n", "            metric.add(l.sum(), l.numel())\n", "            if (i + 1) % (num_batches // 5) == 0 or i == num_batches - 1:\n", "                animator.add(epoch + (i + 1) / num_batches,\n", "                             (metric[0] / metric[1],))\n", "    print(f'loss {metric[0] / metric[1]:.3f}, '\n", "          f'{metric[1] / timer.stop():.1f} tokens/sec on {str(device)}')"]}, {"cell_type": "markdown", "id": "33f9b060", "metadata": {"origin_pos": 27}, "source": ["Now we can train a skip-gram model using negative sampling.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "5e4a73b4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:14.001235Z", "iopub.status.busy": "2023-08-18T19:47:14.000704Z", "iopub.status.idle": "2023-08-18T19:47:53.656586Z", "shell.execute_reply": "2023-08-18T19:47:53.655104Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.410, 223485.0 tokens/sec on cuda:0\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"255.825pt\" height=\"183.35625pt\" viewBox=\"0 0 255.825 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:47:53.616066</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 255.825 183.35625 \n", "L 255.825 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 50.14375 145.8 \n", "L 245.44375 145.8 \n", "L 245.44375 7.2 \n", "L 50.14375 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 50.14375 145.8 \n", "L 50.14375 7.2 \n", "\" clip-path=\"url(#p8bceb1650b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m37a4bb8190\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m37a4bb8190\" x=\"50.14375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(46.9625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 98.96875 145.8 \n", "L 98.96875 7.2 \n", "\" clip-path=\"url(#p8bceb1650b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m37a4bb8190\" x=\"98.96875\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(95.7875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 147.79375 145.8 \n", "L 147.79375 7.2 \n", "\" clip-path=\"url(#p8bceb1650b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m37a4bb8190\" x=\"147.79375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(144.6125 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 196.61875 145.8 \n", "L 196.61875 7.2 \n", "\" clip-path=\"url(#p8bceb1650b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m37a4bb8190\" x=\"196.61875\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(193.4375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 245.44375 145.8 \n", "L 245.44375 7.2 \n", "\" clip-path=\"url(#p8bceb1650b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m37a4bb8190\" x=\"245.44375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(242.2625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(132.565625 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 50.14375 145.570215 \n", "L 245.44375 145.570215 \n", "\" clip-path=\"url(#p8bceb1650b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m24ac6a49c3\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m24ac6a49c3\" x=\"50.14375\" y=\"145.570215\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.40 -->\n", "      <g transform=\"translate(20.878125 149.369433) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 50.14375 115.045381 \n", "L 245.44375 115.045381 \n", "\" clip-path=\"url(#p8bceb1650b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m24ac6a49c3\" x=\"50.14375\" y=\"115.045381\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.45 -->\n", "      <g transform=\"translate(20.878125 118.8446) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 50.14375 84.520548 \n", "L 245.44375 84.520548 \n", "\" clip-path=\"url(#p8bceb1650b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m24ac6a49c3\" x=\"50.14375\" y=\"84.520548\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.50 -->\n", "      <g transform=\"translate(20.878125 88.319767) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 50.14375 53.995715 \n", "L 245.44375 53.995715 \n", "\" clip-path=\"url(#p8bceb1650b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m24ac6a49c3\" x=\"50.14375\" y=\"53.995715\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.55 -->\n", "      <g transform=\"translate(20.878125 57.794934) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 50.14375 23.470882 \n", "L 245.44375 23.470882 \n", "\" clip-path=\"url(#p8bceb1650b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m24ac6a49c3\" x=\"50.14375\" y=\"23.470882\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.60 -->\n", "      <g transform=\"translate(20.878125 27.2701) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798437 86.157813) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 11.025364 13.5 \n", "L 20.731979 60.650035 \n", "L 30.438593 79.577468 \n", "L 40.145207 89.734196 \n", "L 49.851822 96.22092 \n", "L 50.14375 96.377833 \n", "L 59.850364 101.19076 \n", "L 69.556979 104.893796 \n", "L 79.263593 107.935897 \n", "L 88.970207 110.571966 \n", "L 98.676822 112.87733 \n", "L 98.96875 112.939004 \n", "L 108.675364 115.405995 \n", "L 118.381979 117.593128 \n", "L 128.088593 119.590033 \n", "L 137.795207 121.404224 \n", "L 147.501822 123.078123 \n", "L 147.79375 123.125557 \n", "L 157.500364 125.19841 \n", "L 167.206979 127.089122 \n", "L 176.913593 128.791603 \n", "L 186.620207 130.342245 \n", "L 196.326822 131.761658 \n", "L 196.61875 131.803787 \n", "L 206.325364 133.671548 \n", "L 216.031979 135.334055 \n", "L 225.738593 136.846859 \n", "L 235.445207 138.20675 \n", "L 245.151822 139.465091 \n", "L 245.44375 139.5 \n", "\" clip-path=\"url(#p8bceb1650b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 50.14375 145.8 \n", "L 50.14375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 245.44375 145.8 \n", "L 245.44375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 50.14375 145.8 \n", "L 245.44375 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 50.14375 7.2 \n", "L 245.44375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p8bceb1650b\">\n", "   <rect x=\"50.14375\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lr, num_epochs = 0.002, 5\n", "train(net, data_iter, lr, num_epochs)"]}, {"cell_type": "markdown", "id": "c47dd8e2", "metadata": {"origin_pos": 29}, "source": ["## Applying Word Embeddings\n", ":label:`subsec_apply-word-embed`\n", "\n", "\n", "After training the word2vec model,\n", "we can use the cosine similarity\n", "of word vectors from the trained model\n", "to \n", "find words from the dictionary\n", "that are most semantically similar\n", "to an input word.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "81f4d03b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:47:53.660681Z", "iopub.status.busy": "2023-08-18T19:47:53.660377Z", "iopub.status.idle": "2023-08-18T19:47:53.728687Z", "shell.execute_reply": "2023-08-18T19:47:53.727729Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cosine sim=0.702: microprocessor\n", "cosine sim=0.649: mips\n", "cosine sim=0.643: intel\n"]}], "source": ["def get_similar_tokens(query_token, k, embed):\n", "    W = embed.weight.data\n", "    x = W[vocab[query_token]]\n", "    # Compute the cosine similarity. Add 1e-9 for numerical stability\n", "    cos = torch.mv(W, x) / torch.sqrt(torch.sum(W * W, dim=1) *\n", "                                      torch.sum(x * x) + 1e-9)\n", "    topk = torch.topk(cos, k=k+1)[1].cpu().numpy().astype('int32')\n", "    for i in topk[1:]:  # Remove the input words\n", "        print(f'cosine sim={float(cos[i]):.3f}: {vocab.to_tokens(i)}')\n", "\n", "get_similar_tokens('chip', 3, net[0])"]}, {"cell_type": "markdown", "id": "554c2d35", "metadata": {"origin_pos": 32}, "source": ["## Summary\n", "\n", "* We can train a skip-gram model with negative sampling using embedding layers and the binary cross-entropy loss.\n", "* Applications of word embeddings include finding semantically similar words for a given word based on the cosine similarity of word vectors.\n", "\n", "\n", "## Exercises\n", "\n", "1. Using the trained model, find semantically similar words for other input words. Can you improve the results by tuning hyperparameters?\n", "1. When a training corpus is huge, we often sample context words and noise words for the center words in the current minibatch *when updating model parameters*. In other words, the same center word may have different context words or noise words in different training epochs. What are the benefits of this method? Try to implement this training method.\n"]}, {"cell_type": "markdown", "id": "7742f77d", "metadata": {"origin_pos": 34, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1335)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
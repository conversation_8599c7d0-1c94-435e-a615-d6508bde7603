{"cells": [{"cell_type": "markdown", "id": "15193e65", "metadata": {"origin_pos": 0}, "source": ["# Natural Language Processing: Pretraining\n", ":label:`chap_nlp_pretrain`\n", "\n", "\n", "Humans need to communicate.\n", "Out of this basic need of the human condition, a vast amount of written text has been generated on an everyday basis.\n", "Given rich text in social media, chat apps, emails, product reviews, news articles,  research papers, and books, it becomes vital to enable computers to understand them to offer assistance or make decisions based on human languages.\n", "\n", "*Natural language processing* studies interactions between computers and humans using natural languages.\n", "In practice, it is very common to use natural language processing techniques to process and analyze text (human natural language) data, such as language models in :numref:`sec_language-model` and machine translation models in :numref:`sec_machine_translation`.\n", "\n", "To understand text, we can begin by learning\n", "its representations.\n", "Leveraging the existing text sequences\n", "from large corpora,\n", "*self-supervised learning*\n", "has been extensively\n", "used to pretrain text representations,\n", "such as by predicting some hidden part of the text\n", "using some other part of their surrounding text.\n", "In this way,\n", "models learn through supervision\n", "from *massive* text data\n", "without *expensive* labeling efforts!\n", "\n", "\n", "As we will see in this chapter,\n", "when treating each word or subword as an individual token,\n", "the representation of each token can be pretrained\n", "using word2vec, GloVe, or subword embedding models\n", "on large corpora.\n", "After pretraining, representation of each token can be a vector,\n", "however, it remains the same no matter what the context is.\n", "For instance, the vector representation of \"bank\" is the same\n", "in both\n", "\"go to the bank to deposit some money\"\n", "and\n", "\"go to the bank to sit down\".\n", "Thus, many more recent pretraining models adapt representation of the same token\n", "to different contexts.\n", "Among them is BERT, a much deeper self-supervised model based on the Transformer encoder.\n", "In this chapter, we will focus on how to pretrain such representations for text,\n", "as highlighted in :numref:`fig_nlp-map-pretrain`.\n", "\n", "![Pretrained text representations can be fed to various deep learning architectures for different downstream natural language processing applications. This chapter focuses on the upstream text representation pretraining.](../img/nlp-map-pretrain.svg)\n", ":label:`fig_nlp-map-pretrain`\n", "\n", "\n", "For sight of the big picture,\n", ":numref:`fig_nlp-map-pretrain` shows that\n", "the pretrained text representations can be fed to\n", "a variety of deep learning architectures for different downstream natural language processing applications.\n", "We will cover them in :numref:`chap_nlp_app`.\n", "\n", ":begin_tab:toc\n", " - [word2vec](word2vec.ipynb)\n", " - [approx-training](approx-training.ipynb)\n", " - [word-embedding-dataset](word-embedding-dataset.ipynb)\n", " - [word2vec-pretraining](word2vec-pretraining.ipynb)\n", " - [glove](glove.ipynb)\n", " - [subword-embedding](subword-embedding.ipynb)\n", " - [similarity-analogy](similarity-analogy.ipynb)\n", " - [bert](bert.ipynb)\n", " - [bert-dataset](bert-dataset.ipynb)\n", " - [bert-pretraining](bert-pretraining.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "da7817f3", "metadata": {"origin_pos": 0}, "source": ["# Word Embedding (word2vec)\n", ":label:`sec_word2vec`\n", "\n", "\n", "Natural language is a complex system used to express meanings.\n", "In this system, words are the basic unit of the meaning.\n", "As the name implies,\n", "*word vectors* are vectors used to represent words,\n", "and can also be considered as feature vectors or representations of words.\n", "The technique of mapping words to real vectors\n", "is called *word embedding*.\n", "In recent years,\n", "word embedding has gradually become\n", "the basic knowledge of natural language processing.\n", "\n", "\n", "## One-Hot Vectors Are a Bad Choice\n", "\n", "We used one-hot vectors to represent words (characters are words) in :numref:`sec_rnn-scratch`.\n", "Suppose that the number of different words in the dictionary (the dictionary size) is $N$,\n", "and each word corresponds to\n", "a different integer (index) from $0$ to $N-1$.\n", "To obtain the one-hot vector representation\n", "for any word with index $i$,\n", "we create a length-$N$ vector with all 0s\n", "and set the element at position $i$ to 1.\n", "In this way, each word is represented as a vector of length $N$, and it\n", "can be used directly by neural networks.\n", "\n", "\n", "Although one-hot word vectors are easy to construct,\n", "they are usually not a good choice.\n", "A main reason is that one-hot word vectors cannot accurately express the similarity between different words, such as the *cosine similarity* that we often use.\n", "For vectors $\\mathbf{x}, \\mathbf{y} \\in \\mathbb{R}^d$, their cosine similarity is the cosine of the angle between them:\n", "\n", "\n", "$$\\frac{\\mathbf{x}^\\top \\mathbf{y}}{\\|\\mathbf{x}\\| \\|\\mathbf{y}\\|} \\in [-1, 1].$$\n", "\n", "\n", "Since the cosine similarity between one-hot vectors of any two different words is 0,\n", "one-hot vectors cannot encode similarities among words.\n", "\n", "\n", "## Self-Supervised word2vec\n", "\n", "The [word2vec](https://code.google.com/archive/p/word2vec/) tool was proposed to address the above issue.\n", "It maps each word to a fixed-length vector, and  these vectors can better express the similarity and analogy relationship among different words.\n", "The word2vec tool contains two models, namely *skip-gram* :cite:`<PERSON><PERSON><PERSON>.Sutskever.Chen.ea.2013`  and *continuous bag of words* (CBOW) :cite:`<PERSON><PERSON>lov.Chen.Corrado.ea.2013`.\n", "For semantically meaningful representations,\n", "their training relies on\n", "conditional probabilities\n", "that can be viewed as predicting\n", "some words using some of their surrounding words\n", "in corpora.\n", "Since supervision comes from the data without labels,\n", "both skip-gram and continuous bag of words\n", "are self-supervised models.\n", "\n", "In the following, we will introduce these two models and their training methods.\n", "\n", "\n", "## The Skip-Gram Model\n", ":label:`subsec_skip-gram`\n", "\n", "The *skip-gram* model assumes that a word can be used to generate its surrounding words in a text sequence.\n", "Take the text sequence \"the\", \"man\", \"loves\", \"his\", \"son\" as an example.\n", "Let's choose \"loves\" as the *center word* and set the context window size to 2.\n", "As shown in :numref:`fig_skip_gram`,\n", "given the center word \"loves\",\n", "the skip-gram model considers\n", "the conditional probability for generating the *context words*: \"the\", \"man\", \"his\", and \"son\",\n", "which are no more than 2 words away from the center word:\n", "\n", "$$P(\\textrm{\"the\"},\\textrm{\"man\"},\\textrm{\"his\"},\\textrm{\"son\"}\\mid\\textrm{\"loves\"}).$$\n", "\n", "Assume that\n", "the context words are independently generated\n", "given the center word (i.e., conditional independence).\n", "In this case, the above conditional probability\n", "can be rewritten as\n", "\n", "$$P(\\textrm{\"the\"}\\mid\\textrm{\"loves\"})\\cdot P(\\textrm{\"man\"}\\mid\\textrm{\"loves\"})\\cdot P(\\textrm{\"his\"}\\mid\\textrm{\"loves\"})\\cdot P(\\textrm{\"son\"}\\mid\\textrm{\"loves\"}).$$\n", "\n", "![The skip-gram model considers the conditional probability of generating the surrounding context words given a center word.](../img/skip-gram.svg)\n", ":label:`fig_skip_gram`\n", "\n", "In the skip-gram model, each word\n", "has two $d$-dimensional-vector representations\n", "for calculating conditional probabilities.\n", "More concretely,\n", "for any word with index $i$ in the dictionary,\n", "denote by $\\mathbf{v}_i\\in\\mathbb{R}^d$\n", "and $\\mathbf{u}_i\\in\\mathbb{R}^d$\n", "its two vectors\n", "when used as a *center* word and a *context* word, respectively.\n", "The conditional probability of generating any\n", "context word $w_o$ (with index $o$ in the dictionary) given the center word $w_c$ (with index $c$ in the dictionary) can be modeled by\n", "a softmax operation on vector dot products:\n", "\n", "\n", "$$P(w_o \\mid w_c) = \\frac{\\exp(\\mathbf{u}_o^\\top \\mathbf{v}_c)}{ \\sum_{i \\in \\mathcal{V}} \\exp(\\mathbf{u}_i^\\top \\mathbf{v}_c)},$$\n", ":eqlabel:`eq_skip-gram-softmax`\n", "\n", "where the vocabulary index set $\\mathcal{V} = \\{0, 1, \\ldots, |\\mathcal{V}|-1\\}$.\n", "Given a text sequence of length $T$, where the word at time step $t$ is denoted as $w^{(t)}$.\n", "Assume that\n", "context words are independently generated\n", "given any center word.\n", "For context window size $m$,\n", "the likelihood function of the skip-gram model\n", "is the probability of generating all context words\n", "given any center word:\n", "\n", "\n", "$$ \\prod_{t=1}^{T} \\prod_{-m \\leq j \\leq m,\\ j \\neq 0} P(w^{(t+j)} \\mid w^{(t)}),$$\n", "\n", "where any time step that is less than $1$ or greater than $T$ can be omitted.\n", "\n", "### Training\n", "\n", "The skip-gram model parameters are the center word vector and context word vector for each word in the vocabulary.\n", "In training, we learn the model parameters by maximizing the likelihood function (i.e., maximum likelihood estimation). This is equivalent to minimizing the following loss function:\n", "\n", "$$ - \\sum_{t=1}^{T} \\sum_{-m \\leq j \\leq m,\\ j \\neq 0} \\textrm{log}\\, P(w^{(t+j)} \\mid w^{(t)}).$$\n", "\n", "When using stochastic gradient descent to minimize the loss,\n", "in each iteration\n", "we can\n", "randomly sample a shorter subsequence to calculate the (stochastic) gradient for this subsequence to update the model parameters.\n", "To calculate this (stochastic) gradient,\n", "we need to obtain\n", "the gradients of\n", "the log conditional probability with respect to the center word vector and the context word vector.\n", "In general, according to :eqref:`eq_skip-gram-softmax`\n", "the log conditional probability\n", "involving any pair of the center word $w_c$ and\n", "the context word $w_o$ is\n", "\n", "\n", "$$\\log P(w_o \\mid w_c) =\\mathbf{u}_o^\\top \\mathbf{v}_c - \\log\\left(\\sum_{i \\in \\mathcal{V}} \\exp(\\mathbf{u}_i^\\top \\mathbf{v}_c)\\right).$$\n", ":eqlabel:`eq_skip-gram-log`\n", "\n", "Through differentiation, we can obtain its gradient\n", "with respect to the center word vector $\\mathbf{v}_c$ as\n", "\n", "$$\\begin{aligned}\\frac{\\partial \\textrm{log}\\, P(w_o \\mid w_c)}{\\partial \\mathbf{v}_c}&= \\mathbf{u}_o - \\frac{\\sum_{j \\in \\mathcal{V}} \\exp(\\mathbf{u}_j^\\top \\mathbf{v}_c)\\mathbf{u}_j}{\\sum_{i \\in \\mathcal{V}} \\exp(\\mathbf{u}_i^\\top \\mathbf{v}_c)}\\\\&= \\mathbf{u}_o - \\sum_{j \\in \\mathcal{V}} \\left(\\frac{\\exp(\\mathbf{u}_j^\\top \\mathbf{v}_c)}{ \\sum_{i \\in \\mathcal{V}} \\exp(\\mathbf{u}_i^\\top \\mathbf{v}_c)}\\right) \\mathbf{u}_j\\\\&= \\mathbf{u}_o - \\sum_{j \\in \\mathcal{V}} P(w_j \\mid w_c) \\mathbf{u}_j.\\end{aligned}$$\n", ":eqlabel:`eq_skip-gram-grad`\n", "\n", "\n", "Note that the calculation in :eqref:`eq_skip-gram-grad` requires the conditional probabilities of all words in the dictionary with $w_c$ as the center word.\n", "The gradients for the other word vectors can be obtained in the same way.\n", "\n", "\n", "After training, for any word with index $i$ in the dictionary, we obtain both word vectors\n", "$\\mathbf{v}_i$ (as the center word) and $\\mathbf{u}_i$ (as the context word).\n", "In natural language processing applications, the center word vectors of the skip-gram model are typically\n", "used as the word representations.\n", "\n", "\n", "## The Continuous Bag of Words (CBOW) Model\n", "\n", "\n", "The *continuous bag of words* (CBOW) model is similar to the skip-gram model.\n", "The major difference\n", "from the skip-gram model is that\n", "the continuous bag of words model\n", "assumes that a center word is generated\n", "based on its surrounding context words in the text sequence.\n", "For example,\n", "in the same text sequence \"the\", \"man\", \"loves\", \"his\", and \"son\", with \"loves\" as the center word and the context window size being 2,\n", "the continuous bag of words model\n", "considers\n", "the conditional probability of generating the center word \"loves\" based on the context words \"the\", \"man\", \"his\" and \"son\" (as shown in :numref:`fig_cbow`), which is\n", "\n", "$$P(\\textrm{\"loves\"}\\mid\\textrm{\"the\"},\\textrm{\"man\"},\\textrm{\"his\"},\\textrm{\"son\"}).$$\n", "\n", "![The continuous bag of words model considers the conditional probability of generating the center word given its surrounding context words.](../img/cbow.svg)\n", ":label:`fig_cbow`\n", "\n", "\n", "Since there are multiple context words\n", "in the continuous bag of words model,\n", "these context word vectors are averaged\n", "in the calculation of the conditional probability.\n", "Specifically,\n", "for any word with index $i$ in the dictionary,\n", "denote by $\\mathbf{v}_i\\in\\mathbb{R}^d$\n", "and $\\mathbf{u}_i\\in\\mathbb{R}^d$\n", "its two vectors\n", "when used as a *context* word and a *center* word\n", "(meanings are switched in the skip-gram model), respectively.\n", "The conditional probability of generating any\n", "center word $w_c$ (with index $c$ in the dictionary) given its surrounding context words $w_{o_1}, \\ldots, w_{o_{2m}}$ (with index $o_1, \\ldots, o_{2m}$ in the dictionary) can be modeled by\n", "\n", "\n", "\n", "$$P(w_c \\mid w_{o_1}, \\ldots, w_{o_{2m}}) = \\frac{\\exp\\left(\\frac{1}{2m}\\mathbf{u}_c^\\top (\\mathbf{v}_{o_1} + \\ldots + \\mathbf{v}_{o_{2m}}) \\right)}{ \\sum_{i \\in \\mathcal{V}} \\exp\\left(\\frac{1}{2m}\\mathbf{u}_i^\\top (\\mathbf{v}_{o_1} + \\ldots + \\mathbf{v}_{o_{2m}}) \\right)}.$$\n", ":eqlabel:`fig_cbow-full`\n", "\n", "\n", "For brevity, let $\\mathcal{W}_o= \\{w_{o_1}, \\ldots, w_{o_{2m}}\\}$ and $\\bar{\\mathbf{v}}_o = \\left(\\mathbf{v}_{o_1} + \\ldots + \\mathbf{v}_{o_{2m}} \\right)/(2m)$. Then :eqref:`fig_cbow-full` can be simplified as\n", "\n", "$$P(w_c \\mid \\mathcal{W}_o) = \\frac{\\exp\\left(\\mathbf{u}_c^\\top \\bar{\\mathbf{v}}_o\\right)}{\\sum_{i \\in \\mathcal{V}} \\exp\\left(\\mathbf{u}_i^\\top \\bar{\\mathbf{v}}_o\\right)}.$$\n", "\n", "Given a text sequence of length $T$, where the word at time step $t$ is denoted as $w^{(t)}$.\n", "For context window size $m$,\n", "the likelihood function of the continuous bag of words model\n", "is the probability of generating all center words\n", "given their context words:\n", "\n", "\n", "$$ \\prod_{t=1}^{T}  P(w^{(t)} \\mid  w^{(t-m)}, \\ldots, w^{(t-1)}, w^{(t+1)}, \\ldots, w^{(t+m)}).$$\n", "\n", "### Training\n", "\n", "Training continuous bag of words models\n", "is almost the same as\n", "training skip-gram models.\n", "The maximum likelihood estimation of the\n", "continuous bag of words model is equivalent to minimizing the following loss function:\n", "\n", "\n", "\n", "$$  -\\sum_{t=1}^T  \\textrm{log}\\, P(w^{(t)} \\mid  w^{(t-m)}, \\ldots, w^{(t-1)}, w^{(t+1)}, \\ldots, w^{(t+m)}).$$\n", "\n", "Notice that\n", "\n", "$$\\log\\,P(w_c \\mid \\mathcal{W}_o) = \\mathbf{u}_c^\\top \\bar{\\mathbf{v}}_o - \\log\\,\\left(\\sum_{i \\in \\mathcal{V}} \\exp\\left(\\mathbf{u}_i^\\top \\bar{\\mathbf{v}}_o\\right)\\right).$$\n", "\n", "Through differentiation, we can obtain its gradient\n", "with respect to any context word vector $\\mathbf{v}_{o_i}$($i = 1, \\ldots, 2m$)\n", "as\n", "\n", "\n", "$$\\frac{\\partial \\log\\, P(w_c \\mid \\mathcal{W}_o)}{\\partial \\mathbf{v}_{o_i}} = \\frac{1}{2m} \\left(\\mathbf{u}_c - \\sum_{j \\in \\mathcal{V}} \\frac{\\exp(\\mathbf{u}_j^\\top \\bar{\\mathbf{v}}_o)\\mathbf{u}_j}{ \\sum_{i \\in \\mathcal{V}} \\exp(\\mathbf{u}_i^\\top \\bar{\\mathbf{v}}_o)} \\right) = \\frac{1}{2m}\\left(\\mathbf{u}_c - \\sum_{j \\in \\mathcal{V}} P(w_j \\mid \\mathcal{W}_o) \\mathbf{u}_j \\right).$$\n", ":eqlabel:`eq_cbow-gradient`\n", "\n", "\n", "The gradients for the other word vectors can be obtained in the same way.\n", "Unlike the skip-gram model,\n", "the continuous bag of words model\n", "typically\n", "uses context word vectors as the word representations.\n", "\n", "\n", "\n", "\n", "## Summary\n", "\n", "* Word vectors are vectors used to represent words, and can also be considered as feature vectors or representations of words. The technique of mapping words to real vectors is called word embedding.\n", "* The word2vec tool contains both the skip-gram  and continuous bag of words models.\n", "* The skip-gram model assumes that a word can be used to generate its surrounding words in a text sequence; while the continuous bag of words model assumes that a center word is generated based on its surrounding context words.\n", "\n", "\n", "\n", "## Exercises\n", "\n", "1. What is the computational complexity for calculating each gradient? What could be the issue if the dictionary size is huge?\n", "1. Some fixed phrases in English consist of multiple words, such as \"new york\". How to train their word vectors? Hint: see Section 4 in the word2vec paper :cite:`Mi<PERSON>lov.Sutskever.Chen.ea.2013`.\n", "1. Let's reflect on the word2vec design by taking the skip-gram model as an example. What is the relationship between the dot product of two word vectors in the skip-gram model and the cosine similarity? For a pair of words with similar semantics, why may the cosine similarity of their word vectors (trained by the skip-gram model) be high?\n", "\n", "[Discussions](https://discuss.d2l.ai/t/381)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
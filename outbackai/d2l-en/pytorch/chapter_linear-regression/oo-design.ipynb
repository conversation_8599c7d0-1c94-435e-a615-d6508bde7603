{"cells": [{"cell_type": "markdown", "id": "9f09fc0f", "metadata": {"origin_pos": 1}, "source": ["# Object-Oriented Design for Implementation\n", ":label:`sec_oo-design`\n", "\n", "In our introduction to linear regression,\n", "we walked through various components\n", "including\n", "the data, the model, the loss function,\n", "and the optimization algorithm.\n", "Indeed,\n", "linear regression is\n", "one of the simplest machine learning models.\n", "Training it,\n", "however, uses many of the same components that other models in this book require.\n", "Therefore, \n", "before diving into the implementation details\n", "it is worth \n", "designing some of the APIs\n", "that we use throughout. \n", "Treating components in deep learning\n", "as objects,\n", "we can start by\n", "defining classes for these objects\n", "and their interactions.\n", "This object-oriented design\n", "for implementation\n", "will greatly\n", "streamline the presentation and you might even want to use it in your projects.\n", "\n", "\n", "Inspired by open-source libraries such as [PyTorch Lightning](https://www.pytorchlightning.ai/),\n", "at a high level\n", "we wish to have three classes: \n", "(i) `Module` contains models, losses, and optimization methods; \n", "(ii) `DataModule` provides data loaders for training and validation; \n", "(iii) both classes are combined using the `Trainer` class, which allows us to\n", "train models on a variety of hardware platforms. \n", "Most code in this book adapts `Module` and `DataModule`. We will touch upon the `Trainer` class only when we discuss GPUs, CPUs, parallel training, and optimization algorithms.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "4f04a51a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:19.443072Z", "iopub.status.busy": "2023-08-18T19:26:19.442526Z", "iopub.status.idle": "2023-08-18T19:26:22.383415Z", "shell.execute_reply": "2023-08-18T19:26:22.382436Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import time\n", "import numpy as np\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "c805c409", "metadata": {"origin_pos": 6}, "source": ["## Utilities\n", ":label:`oo-design-utilities`\n", "\n", "We need a few utilities to simplify object-oriented programming in Jupyter notebooks. One of the challenges is that class definitions tend to be fairly long blocks of code. Notebook readability demands short code fragments, interspersed with explanations, a requirement incompatible with the style of programming common for Python libraries. The first\n", "utility function allows us to register functions as methods in a class *after* the class has been created. In fact, we can do so *even after* we have created instances of the class! It allows us to split the implementation of a class into multiple code blocks.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "5403292c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:22.389019Z", "iopub.status.busy": "2023-08-18T19:26:22.388325Z", "iopub.status.idle": "2023-08-18T19:26:22.393121Z", "shell.execute_reply": "2023-08-18T19:26:22.392315Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["def add_to_class(Class):  #@save\n", "    \"\"\"Register functions as methods in created class.\"\"\"\n", "    def wrapper(obj):\n", "        setattr(Class, obj.__name__, obj)\n", "    return wrapper"]}, {"cell_type": "markdown", "id": "7d8c1f96", "metadata": {"origin_pos": 8}, "source": ["Let's have a quick look at how to use it. We plan to implement a class `A` with a method `do`. Instead of having code for both `A` and `do` in the same code block, we can first declare the class `A` and create an instance `a`.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "e04e01bb", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:22.396585Z", "iopub.status.busy": "2023-08-18T19:26:22.395941Z", "iopub.status.idle": "2023-08-18T19:26:22.400066Z", "shell.execute_reply": "2023-08-18T19:26:22.399246Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["class A:\n", "    def __init__(self):\n", "        self.b = 1\n", "\n", "a = A()"]}, {"cell_type": "markdown", "id": "d2db5aae", "metadata": {"origin_pos": 10}, "source": ["Next we define the method `do` as we normally would, but not in class `A`'s scope. Instead, we decorate this method by `add_to_class` with class `A` as its argument. In doing so, the method is able to access the member variables of `A` just as we would expect had it been included as part of `A`'s definition. Let's see what happens when we invoke it for the instance `a`.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "d2133566", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:22.403442Z", "iopub.status.busy": "2023-08-18T19:26:22.402774Z", "iopub.status.idle": "2023-08-18T19:26:22.407704Z", "shell.execute_reply": "2023-08-18T19:26:22.406859Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Class attribute \"b\" is 1\n"]}], "source": ["@add_to_class(A)\n", "def do(self):\n", "    print('Class attribute \"b\" is', self.b)\n", "\n", "a.do()"]}, {"cell_type": "markdown", "id": "0b09869a", "metadata": {"origin_pos": 12}, "source": ["The second one is a utility class that saves all arguments in a class's `__init__` method as class attributes. This allows us to extend constructor call signatures implicitly without additional code.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b2316a5a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:22.410976Z", "iopub.status.busy": "2023-08-18T19:26:22.410447Z", "iopub.status.idle": "2023-08-18T19:26:22.414594Z", "shell.execute_reply": "2023-08-18T19:26:22.413768Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["class HyperParameters:  #@save\n", "    \"\"\"The base class of hyperparameters.\"\"\"\n", "    def save_hyperparameters(self, ignore=[]):\n", "        raise NotImplemented"]}, {"cell_type": "markdown", "id": "f4c0374c", "metadata": {"origin_pos": 14}, "source": ["We defer its implementation into :numref:`sec_utils`. To use it, we define our class that inherits from `HyperParameters` and calls `save_hyperparameters` in the `__init__` method.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "e3f26d79", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:22.417917Z", "iopub.status.busy": "2023-08-18T19:26:22.417395Z", "iopub.status.idle": "2023-08-18T19:26:22.423055Z", "shell.execute_reply": "2023-08-18T19:26:22.422251Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["self.a = 1 self.b = 2\n", "There is no self.c = True\n"]}], "source": ["# Call the fully implemented HyperParameters class saved in d2l\n", "class B(d2l.HyperParameters):\n", "    def __init__(self, a, b, c):\n", "        self.save_hyperparameters(ignore=['c'])\n", "        print('self.a =', self.a, 'self.b =', self.b)\n", "        print('There is no self.c =', not hasattr(self, 'c'))\n", "\n", "b = B(a=1, b=2, c=3)"]}, {"cell_type": "markdown", "id": "f66f84be", "metadata": {"origin_pos": 16}, "source": ["The final utility allows us to plot experiment progress interactively while it is going on. In deference to the much more powerful (and complex) [TensorBoard](https://www.tensorflow.org/tensorboard) we name it `ProgressBoard`. The  implementation is deferred to :numref:`sec_utils`. For now, let's simply see it in action.\n", "\n", "The `draw` method plots a point `(x, y)` in the figure, with `label` specified in the legend. The optional `every_n` smooths the line by only showing $1/n$ points in the figure. Their values are averaged from the $n$ neighbor points in the original figure.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "c7b69b94", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:22.426329Z", "iopub.status.busy": "2023-08-18T19:26:22.425774Z", "iopub.status.idle": "2023-08-18T19:26:22.431284Z", "shell.execute_reply": "2023-08-18T19:26:22.430467Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["class ProgressBoard(d2l.HyperParameters):  #@save\n", "    \"\"\"The board that plots data points in animation.\"\"\"\n", "    def __init__(self, xlabel=None, ylabel=None, xlim=None,\n", "                 ylim=None, xscale='linear', yscale='linear',\n", "                 ls=['-', '--', '-.', ':'], colors=['C0', 'C1', 'C2', 'C3'],\n", "                 fig=None, axes=None, figsize=(3.5, 2.5), display=True):\n", "        self.save_hyperparameters()\n", "\n", "    def draw(self, x, y, label, every_n=1):\n", "        raise NotImplemented"]}, {"cell_type": "markdown", "id": "1b87a713", "metadata": {"origin_pos": 18}, "source": ["In the following example, we draw `sin` and `cos` with a different smoothness. If you run this code block, you will see the lines grow in animation.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "acda0f92", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:22.434595Z", "iopub.status.busy": "2023-08-18T19:26:22.434058Z", "iopub.status.idle": "2023-08-18T19:26:38.822758Z", "shell.execute_reply": "2023-08-18T19:26:38.821895Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"241.185572pt\" height=\"183.35625pt\" viewBox=\"0 0 241.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:26:38.654609</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 183.35625 \n", "L 241.**********.35625 \n", "L 241.185572 0 \n", "L -0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 38.**********.8 \n", "L 233.**********.8 \n", "L 233.782813 7.2 \n", "L 38.482813 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"me77baef64d\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me77baef64d\" x=\"46.454241\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(43.272991 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#me77baef64d\" x=\"82.688007\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(79.506757 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#me77baef64d\" x=\"118.921774\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(115.740524 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#me77baef64d\" x=\"155.15554\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(151.97429 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#me77baef64d\" x=\"191.389306\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(188.208056 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#me77baef64d\" x=\"227.623072\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(221.260572 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- x -->\n", "     <g transform=\"translate(133.173438 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"me48fc91a1e\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me48fc91a1e\" x=\"38.482813\" y=\"139.701599\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(7.2 143.500818) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#me48fc91a1e\" x=\"38.482813\" y=\"108.131347\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(7.2 111.930566) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#me48fc91a1e\" x=\"38.482813\" y=\"76.561095\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(15.579688 80.360314) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#me48fc91a1e\" x=\"38.482813\" y=\"44.990843\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(15.579688 48.790062) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#me48fc91a1e\" x=\"38.482813\" y=\"13.420591\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(15.579688 17.21981) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 47.360085 73.409329 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\"/>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\"/>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\"/>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\"/>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_21\"/>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 54.606838 22.023199 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 54.606838 22.023199 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 54.606838 22.023199 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 54.606838 22.023199 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 54.606838 22.023199 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 54.606838 22.023199 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "L 192.29515 14.70714 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "L 192.29515 14.70714 \n", "L 195.918527 18.380202 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "L 192.29515 14.70714 \n", "L 195.918527 18.380202 \n", "L 199.541903 24.372753 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "L 192.29515 14.70714 \n", "L 195.918527 18.380202 \n", "L 199.541903 24.372753 \n", "L 203.16528 32.445888 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "L 192.29515 14.70714 \n", "L 195.918527 18.380202 \n", "L 199.541903 24.372753 \n", "L 203.16528 32.445888 \n", "L 206.788657 42.277757 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "L 192.29515 14.70714 \n", "L 195.918527 18.380202 \n", "L 199.541903 24.372753 \n", "L 203.16528 32.445888 \n", "L 206.788657 42.277757 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "L 199.541903 110.560813 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "L 192.29515 14.70714 \n", "L 195.918527 18.380202 \n", "L 199.541903 24.372753 \n", "L 203.16528 32.445888 \n", "L 206.788657 42.277757 \n", "L 210.412033 53.476395 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "L 199.541903 110.560813 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "L 192.29515 14.70714 \n", "L 195.918527 18.380202 \n", "L 199.541903 24.372753 \n", "L 203.16528 32.445888 \n", "L 206.788657 42.277757 \n", "L 210.412033 53.476395 \n", "L 214.03541 65.595347 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "L 199.541903 110.560813 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "L 192.29515 14.70714 \n", "L 195.918527 18.380202 \n", "L 199.541903 24.372753 \n", "L 203.16528 32.445888 \n", "L 206.788657 42.277757 \n", "L 210.412033 53.476395 \n", "L 214.03541 65.595347 \n", "L 217.658787 78.151469 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "L 199.541903 110.560813 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "L 192.29515 14.70714 \n", "L 195.918527 18.380202 \n", "L 199.541903 24.372753 \n", "L 203.16528 32.445888 \n", "L 206.788657 42.277757 \n", "L 210.412033 53.476395 \n", "L 214.03541 65.595347 \n", "L 217.658787 78.151469 \n", "L 221.282163 90.644187 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_127\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "L 199.541903 110.560813 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_128\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "L 192.29515 14.70714 \n", "L 195.918527 18.380202 \n", "L 199.541903 24.372753 \n", "L 203.16528 32.445888 \n", "L 206.788657 42.277757 \n", "L 210.412033 53.476395 \n", "L 214.03541 65.595347 \n", "L 217.658787 78.151469 \n", "L 221.282163 90.644187 \n", "L 224.90554 102.575457 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_129\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "L 199.541903 110.560813 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_130\">\n", "    <path d=\"M 47.360085 73.409329 \n", "L 50.983462 60.959407 \n", "L 54.606838 49.131475 \n", "L 58.230215 38.397075 \n", "L 61.853592 29.184154 \n", "L 65.476968 21.860003 \n", "L 69.100345 16.716611 \n", "L 72.723722 13.959031 \n", "L 76.347098 13.697196 \n", "L 79.970475 15.941548 \n", "L 83.593851 20.602609 \n", "L 87.217228 27.494558 \n", "L 90.840605 36.342636 \n", "L 94.463981 46.794096 \n", "L 98.087358 58.432273 \n", "L 101.710735 70.793189 \n", "L 105.334111 83.384053 \n", "L 108.957488 95.702907 \n", "L 112.580864 107.258638 \n", "L 116.204241 117.590554 \n", "L 119.827618 126.286755 \n", "L 123.450994 133.000552 \n", "L 127.074371 137.464285 \n", "L 130.697748 139.5 \n", "L 134.321124 139.02654 \n", "L 137.944501 136.062779 \n", "L 141.567877 130.726874 \n", "L 145.191254 123.231551 \n", "L 148.814631 113.875623 \n", "L 152.438007 103.032084 \n", "L 156.061384 91.133229 \n", "L 159.684761 78.653429 \n", "L 163.308137 66.090215 \n", "L 166.931514 53.944442 \n", "L 170.55489 42.700323 \n", "L 174.178267 32.806126 \n", "L 177.801644 24.656302 \n", "L 181.42502 18.575758 \n", "L 185.048397 14.806907 \n", "L 188.671774 13.5 \n", "L 192.29515 14.70714 \n", "L 195.918527 18.380202 \n", "L 199.541903 24.372753 \n", "L 203.16528 32.445888 \n", "L 206.788657 42.277757 \n", "L 210.412033 53.476395 \n", "L 214.03541 65.595347 \n", "L 217.658787 78.151469 \n", "L 221.282163 90.644187 \n", "L 224.90554 102.575457 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_131\">\n", "    <path d=\"M 54.606838 22.023199 \n", "L 72.723722 69.262535 \n", "L 90.840605 123.212134 \n", "L 108.957488 134.270983 \n", "L 127.074371 92.271627 \n", "L 145.191254 35.828081 \n", "L 163.308137 16.83428 \n", "L 181.42502 52.753037 \n", "L 199.541903 110.560813 \n", "L 217.658787 137.109405 \n", "\" clip-path=\"url(#p6f3318fa44)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 38.**********.8 \n", "L 38.482813 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 233.**********.8 \n", "L 233.782813 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 38.**********.8 \n", "L 233.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 38.482813 7.2 \n", "L 233.782813 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 177.95625 140.8 \n", "L 226.782813 140.8 \n", "Q 228.782813 140.8 228.782813 138.8 \n", "L 228.782813 110.44375 \n", "Q 228.782813 108.44375 226.782813 108.44375 \n", "L 177.95625 108.44375 \n", "Q 175.95625 108.44375 175.95625 110.44375 \n", "L 175.95625 138.8 \n", "Q 175.95625 140.8 177.95625 140.8 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_132\">\n", "     <path d=\"M 179.95625 116.542188 \n", "L 189.95625 116.542188 \n", "L 199.95625 116.542188 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- sin -->\n", "     <g transform=\"translate(207.95625 120.042188) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-73\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"52.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"79.882812\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_133\">\n", "     <path d=\"M 179.95625 131.220313 \n", "L 189.95625 131.220313 \n", "L 199.95625 131.220313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- cos -->\n", "     <g transform=\"translate(207.95625 134.720313) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-63\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"54.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"116.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p6f3318fa44\">\n", "   <rect x=\"38.482813\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["board = d2l.ProgressBoard('x')\n", "for x in np.arange(0, 10, 0.1):\n", "    board.draw(x, np.sin(x), 'sin', every_n=2)\n", "    board.draw(x, np.cos(x), 'cos', every_n=10)"]}, {"cell_type": "markdown", "id": "27746ca1", "metadata": {"origin_pos": 20}, "source": ["## Models\n", ":label:`subsec_oo-design-models`\n", "\n", "The `Module` class is the base class of all models we will implement. At the very least we need three methods. The first, `__init__`, stores the learnable parameters, the `training_step` method accepts a data batch to return the loss value, and finally, `configure_optimizers` returns the optimization method, or a list of them, that is used to update the learnable parameters. Optionally we can define `validation_step` to report the evaluation measures.\n", "Sometimes we put the code for computing the output into a separate `forward` method to make it more reusable.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "62a305b5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:38.826860Z", "iopub.status.busy": "2023-08-18T19:26:38.826284Z", "iopub.status.idle": "2023-08-18T19:26:38.835993Z", "shell.execute_reply": "2023-08-18T19:26:38.835101Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["class Module(nn.<PERSON><PERSON><PERSON>, d2l.HyperParameters):  #@save\n", "    \"\"\"The base class of models.\"\"\"\n", "    def __init__(self, plot_train_per_epoch=2, plot_valid_per_epoch=1):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        self.board = ProgressBoard()\n", "\n", "    def loss(self, y_hat, y):\n", "        raise NotImplementedError\n", "\n", "    def forward(self, X):\n", "        assert hasattr(self, 'net'), 'Neural network is defined'\n", "        return self.net(X)\n", "\n", "    def plot(self, key, value, train):\n", "        \"\"\"Plot a point in animation.\"\"\"\n", "        assert hasattr(self, 'trainer'), 'Trainer is not inited'\n", "        self.board.xlabel = 'epoch'\n", "        if train:\n", "            x = self.trainer.train_batch_idx / \\\n", "                self.trainer.num_train_batches\n", "            n = self.trainer.num_train_batches / \\\n", "                self.plot_train_per_epoch\n", "        else:\n", "            x = self.trainer.epoch + 1\n", "            n = self.trainer.num_val_batches / \\\n", "                self.plot_valid_per_epoch\n", "        self.board.draw(x, value.to(d2l.cpu()).detach().numpy(),\n", "                        ('train_' if train else 'val_') + key,\n", "                        every_n=int(n))\n", "\n", "    def training_step(self, batch):\n", "        l = self.loss(self(*batch[:-1]), batch[-1])\n", "        self.plot('loss', l, train=True)\n", "        return l\n", "\n", "    def validation_step(self, batch):\n", "        l = self.loss(self(*batch[:-1]), batch[-1])\n", "        self.plot('loss', l, train=False)\n", "\n", "    def configure_optimizers(self):\n", "        raise NotImplementedError"]}, {"cell_type": "markdown", "id": "68b2d5aa", "metadata": {"origin_pos": 25, "tab": ["pytorch"]}, "source": ["You may notice that `Module` is a subclass of `nn.Module`, the base class of neural networks in PyTorch.\n", "It provides convenient features for handling neural networks. For example, if we define a `forward` method, such as `forward(self, X)`, then for an instance `a` we can invoke this method by `a(X)`. This works since it calls the `forward` method in the built-in `__call__` method. You can find more details and examples about `nn.Module` in :numref:`sec_model_construction`.\n"]}, {"cell_type": "markdown", "id": "b263de76", "metadata": {"origin_pos": 28}, "source": ["##  Data\n", ":label:`oo-design-data`\n", "\n", "The `DataModule` class is the base class for data. Quite frequently the `__init__` method is used to prepare the data. This includes downloading and preprocessing if needed. The `train_dataloader` returns the data loader for the training dataset. A data loader is a (Python) generator that yields a data batch each time it is used. This batch is then fed into the `training_step` method of `Module` to compute the loss. There is an optional `val_dataloader` to return the validation dataset loader. It behaves in the same manner, except that it yields data batches for the `validation_step` method in `Module`.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "14e2b695", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:38.839261Z", "iopub.status.busy": "2023-08-18T19:26:38.838718Z", "iopub.status.idle": "2023-08-18T19:26:38.843828Z", "shell.execute_reply": "2023-08-18T19:26:38.843014Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [], "source": ["class DataModule(d2l.HyperParameters):  #@save\n", "    \"\"\"The base class of data.\"\"\"\n", "    def __init__(self, root='../data', num_workers=4):\n", "        self.save_hyperparameters()\n", "\n", "    def get_dataloader(self, train):\n", "        raise NotImplementedError\n", "\n", "    def train_dataloader(self):\n", "        return self.get_dataloader(train=True)\n", "\n", "    def val_dataloader(self):\n", "        return self.get_dataloader(train=False)"]}, {"cell_type": "markdown", "id": "80b834f8", "metadata": {"origin_pos": 30}, "source": ["## Training\n", ":label:`oo-design-training`\n"]}, {"cell_type": "markdown", "id": "19097c4d", "metadata": {"origin_pos": 31, "tab": ["pytorch"]}, "source": ["The `Trainer` class trains the learnable parameters in the `Module` class with data specified in `DataModule`. The key method is `fit`, which accepts two arguments: `model`, an instance of `Module`, and `data`, an instance of `DataModule`. It then iterates over the entire dataset `max_epochs` times to train the model. As before, we will defer the implementation of this method to later chapters.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "798ecd6d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:38.847255Z", "iopub.status.busy": "2023-08-18T19:26:38.846633Z", "iopub.status.idle": "2023-08-18T19:26:38.853801Z", "shell.execute_reply": "2023-08-18T19:26:38.852995Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [], "source": ["class Trainer(d2l.HyperParameters):  #@save\n", "    \"\"\"The base class for training models with data.\"\"\"\n", "    def __init__(self, max_epochs, num_gpus=0, gradient_clip_val=0):\n", "        self.save_hyperparameters()\n", "        assert num_gpus == 0, 'No GPU support yet'\n", "\n", "    def prepare_data(self, data):\n", "        self.train_dataloader = data.train_dataloader()\n", "        self.val_dataloader = data.val_dataloader()\n", "        self.num_train_batches = len(self.train_dataloader)\n", "        self.num_val_batches = (len(self.val_dataloader)\n", "                                if self.val_dataloader is not None else 0)\n", "\n", "    def prepare_model(self, model):\n", "        model.trainer = self\n", "        model.board.xlim = [0, self.max_epochs]\n", "        self.model = model\n", "\n", "    def fit(self, model, data):\n", "        self.prepare_data(data)\n", "        self.prepare_model(model)\n", "        self.optim = model.configure_optimizers()\n", "        self.epoch = 0\n", "        self.train_batch_idx = 0\n", "        self.val_batch_idx = 0\n", "        for self.epoch in range(self.max_epochs):\n", "            self.fit_epoch()\n", "\n", "    def fit_epoch(self):\n", "        raise NotImplementedError"]}, {"cell_type": "markdown", "id": "792b845a", "metadata": {"origin_pos": 34}, "source": ["## Summary\n", "\n", "To highlight the object-oriented design\n", "for our future deep learning implementation,\n", "the above classes simply show how their objects \n", "store data and interact with each other.\n", "We will keep enriching implementations of these classes,\n", "such as via `@add_to_class`,\n", "in the rest of the book.\n", "Moreover,\n", "these fully implemented classes\n", "are saved in the [D2L library](https://github.com/d2l-ai/d2l-en/tree/master/d2l),\n", "a *lightweight toolkit* that makes structured modeling for deep learning easy. \n", "In particular, it facilitates reusing many components between projects without changing much at all. For instance, we can replace just the optimizer, just the model, just the dataset, etc.;\n", "this degree of modularity pays dividends throughout the book in terms of conciseness and simplicity (this is why we added it) and it can do the same for your own projects. \n", "\n", "\n", "## Exercises\n", "\n", "1. Locate full implementations of the above classes that are saved in the [D2L library](https://github.com/d2l-ai/d2l-en/tree/master/d2l). We strongly recommend that you look at the implementation in detail once you have gained some more familiarity with deep learning modeling.\n", "1. Remove the `save_hyperparameters` statement in the `B` class. Can you still print `self.a` and `self.b`? Optional: if you have dived into the full implementation of the `HyperParameters` class, can you explain why?\n"]}, {"cell_type": "markdown", "id": "8f75e484", "metadata": {"origin_pos": 36, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/6646)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "d6fb47f3", "metadata": {"origin_pos": 1}, "source": ["# Concise Implementation of Linear Regression\n", ":label:`sec_linear_concise`\n", "\n", "Deep learning has witnessed a sort of Cambrian explosion\n", "over the past decade.\n", "The sheer number of techniques, applications and algorithms by far surpasses the\n", "progress of previous decades. \n", "This is due to a fortuitous combination of multiple factors,\n", "one of which is the powerful free tools\n", "offered by a number of open-source deep learning frameworks.\n", "Theano :cite:`Bergstra.Breuleux.Bastien.ea.2010`,\n", "DistBelief :cite:`<PERSON>.Corrado.Monga.ea.2012`,\n", "and Caffe :cite:`<PERSON><PERSON>.Shelhamer.Donahue.ea.2014`\n", "arguably represent the\n", "first generation of such models \n", "that found widespread adoption.\n", "In contrast to earlier (seminal) works like\n", "SN2 (Simulateur Neuristique) :cite:`Bottou.Le-Cun.1988`,\n", "which provided a Lisp-like programming experience,\n", "modern frameworks offer automatic differentiation\n", "and the convenience of Python.\n", "These frameworks allow us to automate and modularize\n", "the repetitive work of implementing gradient-based learning algorithms.\n", "\n", "In :numref:`sec_linear_scratch`, we relied only on\n", "(i) tensors for data storage and linear algebra;\n", "and (ii) automatic differentiation for calculating gradients.\n", "In practice, because data iterators, loss functions, optimizers,\n", "and neural network layers\n", "are so common, modern libraries implement these components for us as well.\n", "In this section, (**we will show you how to implement\n", "the linear regression model**) from :numref:`sec_linear_scratch`\n", "(**concisely by using high-level APIs**) of deep learning frameworks.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "af5e3ef6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:10.338416Z", "iopub.status.busy": "2023-08-18T19:26:10.337902Z", "iopub.status.idle": "2023-08-18T19:26:13.242568Z", "shell.execute_reply": "2023-08-18T19:26:13.241256Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import numpy as np\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "cf76423a", "metadata": {"origin_pos": 6}, "source": ["## Defining the Model\n", "\n", "When we implemented linear regression from scratch\n", "in :numref:`sec_linear_scratch`,\n", "we defined our model parameters explicitly\n", "and coded up the calculations to produce output\n", "using basic linear algebra operations.\n", "You *should* know how to do this.\n", "But once your models get more complex,\n", "and once you have to do this nearly every day,\n", "you will be glad of the assistance.\n", "The situation is similar to coding up your own blog from scratch.\n", "Doing it once or twice is rewarding and instructive,\n", "but you would be a lousy web developer\n", "if you spent a month reinventing the wheel.\n", "\n", "For standard operations,\n", "we can [**use a framework's predefined layers,**]\n", "which allow us to focus\n", "on the layers used to construct the model\n", "rather than worrying about their implementation.\n", "Recall the architecture of a single-layer network\n", "as described in :numref:`fig_single_neuron`.\n", "The layer is called *fully connected*,\n", "since each of its inputs is connected\n", "to each of its outputs\n", "by means of a matrix--vector multiplication.\n"]}, {"cell_type": "markdown", "id": "39abf439", "metadata": {"origin_pos": 8, "tab": ["pytorch"]}, "source": ["In PyTorch, the fully connected layer is defined in `Linear` and `LazyLinear` classes (available since version 1.8.0). \n", "The latter\n", "allows users to specify *merely*\n", "the output dimension,\n", "while the former\n", "additionally asks for\n", "how many inputs go into this layer.\n", "Specifying input shapes is inconvenient and may require nontrivial calculations\n", "(such as in convolutional layers).\n", "Thus, for simplicity, we will use such \"lazy\" layers\n", "whenever we can.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "5a75de1f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:13.246965Z", "iopub.status.busy": "2023-08-18T19:26:13.246279Z", "iopub.status.idle": "2023-08-18T19:26:13.251883Z", "shell.execute_reply": "2023-08-18T19:26:13.251011Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["class LinearRegression(d2l.Module):  #@save\n", "    \"\"\"The linear regression model implemented with high-level APIs.\"\"\"\n", "    def __init__(self, lr):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        self.net = nn.LazyLinear(1)\n", "        self.net.weight.data.normal_(0, 0.01)\n", "        self.net.bias.data.fill_(0)"]}, {"cell_type": "markdown", "id": "33660a46", "metadata": {"origin_pos": 12}, "source": ["In the `forward` method we just invoke the built-in `__call__` method of the predefined layers to compute the outputs.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "ea3983b1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:13.255283Z", "iopub.status.busy": "2023-08-18T19:26:13.254649Z", "iopub.status.idle": "2023-08-18T19:26:13.258693Z", "shell.execute_reply": "2023-08-18T19:26:13.257894Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(LinearRegression)  #@save\n", "def forward(self, X):\n", "    return self.net(X)"]}, {"cell_type": "markdown", "id": "66748a30", "metadata": {"origin_pos": 15}, "source": ["## Defining the Loss Function\n"]}, {"cell_type": "markdown", "id": "1cdf7456", "metadata": {"origin_pos": 17, "tab": ["pytorch"]}, "source": ["[**The `MSELoss` class computes the mean squared error (without the $1/2$ factor in :eqref:`eq_mse`).**]\n", "By default, `MSELoss` returns the average loss over examples.\n", "It is faster (and easier to use) than implementing our own.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "08279ee2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:13.262204Z", "iopub.status.busy": "2023-08-18T19:26:13.261534Z", "iopub.status.idle": "2023-08-18T19:26:13.265876Z", "shell.execute_reply": "2023-08-18T19:26:13.265042Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(LinearRegression)  #@save\n", "def loss(self, y_hat, y):\n", "    fn = nn.MS<PERSON><PERSON>()\n", "    return fn(y_hat, y)"]}, {"cell_type": "markdown", "id": "9e523527", "metadata": {"origin_pos": 21}, "source": ["## Defining the Optimization Algorithm\n"]}, {"cell_type": "markdown", "id": "9ea96bb2", "metadata": {"origin_pos": 23, "tab": ["pytorch"]}, "source": ["Minibatch SGD is a standard tool\n", "for optimizing neural networks\n", "and thus PyTorch supports it alongside a number of\n", "variations on this algorithm in the `optim` module.\n", "When we (**instantiate an `SGD` instance,**)\n", "we specify the parameters to optimize over,\n", "obtainable from our model via `self.parameters()`,\n", "and the learning rate (`self.lr`)\n", "required by our optimization algorithm.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "a6f8dcb8", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:13.269377Z", "iopub.status.busy": "2023-08-18T19:26:13.268734Z", "iopub.status.idle": "2023-08-18T19:26:13.272985Z", "shell.execute_reply": "2023-08-18T19:26:13.272152Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(LinearRegression)  #@save\n", "def configure_optimizers(self):\n", "    return torch.optim.SGD(self.parameters(), self.lr)"]}, {"cell_type": "markdown", "id": "cf9ff29e", "metadata": {"origin_pos": 26}, "source": ["## Training\n", "\n", "You might have noticed that expressing our model through\n", "high-level APIs of a deep learning framework\n", "requires fewer lines of code.\n", "We did not have to allocate parameters individually,\n", "define our loss function, or implement minibatch SGD.\n", "Once we start working with much more complex models,\n", "the advantages of the high-level API will grow considerably.\n", "\n", "Now that we have all the basic pieces in place,\n", "[**the training loop itself is the same\n", "as the one we implemented from scratch.**]\n", "So we just call the `fit` method (introduced in :numref:`oo-design-training`),\n", "which relies on the implementation of the `fit_epoch` method\n", "in :numref:`sec_linear_scratch`,\n", "to train our model.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "084d36cb", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:13.276169Z", "iopub.status.busy": "2023-08-18T19:26:13.275889Z", "iopub.status.idle": "2023-08-18T19:26:15.234839Z", "shell.execute_reply": "2023-08-18T19:26:15.233918Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"237.376562pt\" height=\"183.35625pt\" viewBox=\"0 0 237.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:26:15.181789</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 237.**********.35625 \n", "L 237.376562 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 26.925 145.8 \n", "L 222.225 145.8 \n", "L 222.225 7.2 \n", "L 26.925 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mf4440c482d\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf4440c482d\" x=\"26.925\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(18.973438 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mf4440c482d\" x=\"59.475\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(51.523438 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mf4440c482d\" x=\"92.025\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(84.073438 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mf4440c482d\" x=\"124.575\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(116.623437 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mf4440c482d\" x=\"157.125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(149.173438 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mf4440c482d\" x=\"189.675\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(181.723438 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mf4440c482d\" x=\"222.225\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 3.0 -->\n", "      <g transform=\"translate(214.273438 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(109.346875 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_8\">\n", "      <defs>\n", "       <path id=\"m7cca32c1c7\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7cca32c1c7\" x=\"26.925\" y=\"139.502946\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(13.5625 143.302165) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m7cca32c1c7\" x=\"26.925\" y=\"101.347926\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(13.5625 105.147144) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m7cca32c1c7\" x=\"26.925\" y=\"63.192905\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(7.2 66.992124) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m7cca32c1c7\" x=\"26.925\" y=\"25.037885\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(7.2 28.837104) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 42.182813 13.5 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 121.94626 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 121.94626 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 92.025 134.27043 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 121.94626 \n", "L 107.282813 137.15812 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 92.025 134.27043 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 121.94626 \n", "L 107.282813 137.15812 \n", "L 139.832812 139.198632 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 92.025 134.27043 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 121.94626 \n", "L 107.282813 137.15812 \n", "L 139.832812 139.198632 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 92.025 134.27043 \n", "L 157.125 139.398599 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 121.94626 \n", "L 107.282813 137.15812 \n", "L 139.832812 139.198632 \n", "L 172.382812 139.456533 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 92.025 134.27043 \n", "L 157.125 139.398599 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 121.94626 \n", "L 107.282813 137.15812 \n", "L 139.832812 139.198632 \n", "L 172.382812 139.456533 \n", "L 204.932812 139.495446 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 92.025 134.27043 \n", "L 157.125 139.398599 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 121.94626 \n", "L 107.282813 137.15812 \n", "L 139.832812 139.198632 \n", "L 172.382812 139.456533 \n", "L 204.932812 139.495446 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 92.025 134.27043 \n", "L 157.125 139.398599 \n", "L 222.225 139.5 \n", "\" clip-path=\"url(#p0d5f6e1ccf)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 26.925 145.8 \n", "L 26.925 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 222.225 145.8 \n", "L 222.225 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 26.925 145.8 \n", "L 222.225 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 26.925 7.2 \n", "L 222.225 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 135.634375 45.1125 \n", "L 215.225 45.1125 \n", "Q 217.225 45.1125 217.225 43.1125 \n", "L 217.225 14.2 \n", "Q 217.225 12.2 215.225 12.2 \n", "L 135.634375 12.2 \n", "Q 133.634375 12.2 133.634375 14.2 \n", "L 133.634375 43.1125 \n", "Q 133.634375 45.1125 135.634375 45.1125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 137.634375 20.298438 \n", "L 147.634375 20.298438 \n", "L 157.634375 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(165.634375 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_29\">\n", "     <path d=\"M 137.634375 35.254688 \n", "L 147.634375 35.254688 \n", "L 157.634375 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(165.634375 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p0d5f6e1ccf\">\n", "   <rect x=\"26.925\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = LinearRegression(lr=0.03)\n", "data = d2l.SyntheticRegressionData(w=torch.tensor([2, -3.4]), b=4.2)\n", "trainer = d2l.Trainer(max_epochs=3)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "bf3e3b53", "metadata": {"origin_pos": 28}, "source": ["Below, we\n", "[**compare the model parameters learned\n", "by training on finite data\n", "and the actual parameters**]\n", "that generated our dataset.\n", "To access parameters,\n", "we access the weights and bias\n", "of the layer that we need.\n", "As in our implementation from scratch,\n", "note that our estimated parameters\n", "are close to their true counterparts.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "9ff8ee0f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:15.238470Z", "iopub.status.busy": "2023-08-18T19:26:15.237858Z", "iopub.status.idle": "2023-08-18T19:26:15.242681Z", "shell.execute_reply": "2023-08-18T19:26:15.241815Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(LinearRegression)  #@save\n", "def get_w_b(self):\n", "    return (self.net.weight.data, self.net.bias.data)\n", "w, b = model.get_w_b()"]}, {"cell_type": "code", "execution_count": 8, "id": "2e5bd7b1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:15.246141Z", "iopub.status.busy": "2023-08-18T19:26:15.245569Z", "iopub.status.idle": "2023-08-18T19:26:15.252296Z", "shell.execute_reply": "2023-08-18T19:26:15.251312Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["error in estimating w: tensor([ 0.0094, -0.0030])\n", "error in estimating b: tensor([0.0137])\n"]}], "source": ["print(f'error in estimating w: {data.w - w.reshape(data.w.shape)}')\n", "print(f'error in estimating b: {data.b - b}')"]}, {"cell_type": "markdown", "id": "bc2e436c", "metadata": {"origin_pos": 32}, "source": ["## Summary\n", "\n", "This section contains the first\n", "implementation of a deep network (in this book)\n", "to tap into the conveniences afforded\n", "by modern deep learning frameworks,\n", "such as MXNet :cite:`Chen.Li.Li.ea.2015`, \n", "JAX :cite:<PERSON><PERSON><PERSON><PERSON>.2018`, \n", "PyTorch :cite:`Paszke.Gross.Massa.ea.2019`, \n", "and Tensorflow :cite:`<PERSON><PERSON><PERSON><PERSON>Barham.Chen.ea.2016`.\n", "We used framework defaults for loading data, defining a layer,\n", "a loss function, an optimizer and a training loop.\n", "Whenever the framework provides all necessary features,\n", "it is generally a good idea to use them,\n", "since the library implementations of these components\n", "tend to be heavily optimized for performance\n", "and properly tested for reliability.\n", "At the same time, try not to forget\n", "that these modules *can* be implemented directly.\n", "This is especially important for aspiring researchers\n", "who wish to live on the leading edge of model development,\n", "where you will be inventing new components\n", "that cannot possibly exist in any current library.\n"]}, {"cell_type": "markdown", "id": "102e9024", "metadata": {"origin_pos": 34, "tab": ["pytorch"]}, "source": ["In PyTorch, the `data` module provides tools for data processing,\n", "the `nn` module defines a large number of neural network layers and common loss functions.\n", "We can initialize the parameters by replacing their values\n", "with methods ending with `_`.\n", "Note that we need to specify the input dimensions of the network.\n", "While this is trivial for now, it can have significant knock-on effects\n", "when we want to design complex networks with many layers.\n", "Careful considerations of how to parametrize these networks\n", "is needed to allow portability.\n"]}, {"cell_type": "markdown", "id": "5a5895fe", "metadata": {"origin_pos": 36}, "source": ["## Exercises\n", "\n", "1. How would you need to change the learning rate if you replace the aggregate loss over the minibatch\n", "   with an average over the loss on the minibatch?\n", "1. Review the framework documentation to see which loss functions are provided. In particular,\n", "   replace the squared loss with <PERSON><PERSON>'s robust loss function. That is, use the loss function\n", "   $$l(y,y') = \\begin{cases}|y-y'| -\\frac{\\sigma}{2} & \\textrm{ if } |y-y'| > \\sigma \\\\ \\frac{1}{2 \\sigma} (y-y')^2 & \\textrm{ otherwise}\\end{cases}$$\n", "1. How do you access the gradient of the weights of the model?\n", "1. What is the effect on the solution if you change the learning rate and the number of epochs? Does it keep on improving?\n", "1. How does the solution change as you vary the amount of data generated?\n", "    1. Plot the estimation error for $\\hat{\\mathbf{w}} - \\mathbf{w}$ and $\\hat{b} - b$ as a function of the amount of data. Hint: increase the amount of data logarithmically rather than linearly, i.e., 5, 10, 20, 50, ..., 10,000 rather than 1000, 2000, ..., 10,000.\n", "    2. Why is the suggestion in the hint appropriate?\n"]}, {"cell_type": "markdown", "id": "9467a79a", "metadata": {"origin_pos": 38, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/45)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
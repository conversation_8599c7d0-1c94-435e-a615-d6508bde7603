{"cells": [{"cell_type": "markdown", "id": "3aa97d87", "metadata": {"origin_pos": 1}, "source": ["# Linear Regression Implementation from Scratch\n", ":label:`sec_linear_scratch`\n", "\n", "We are now ready to work through \n", "a fully functioning implementation \n", "of linear regression. \n", "In this section, \n", "(**we will implement the entire method from scratch,\n", "including (i) the model; (ii) the loss function;\n", "(iii) a minibatch stochastic gradient descent optimizer;\n", "and (iv) the training function \n", "that stitches all of these pieces together.**)\n", "Finally, we will run our synthetic data generator\n", "from :numref:`sec_synthetic-regression-data`\n", "and apply our model\n", "on the resulting dataset. \n", "While modern deep learning frameworks \n", "can automate nearly all of this work,\n", "implementing things from scratch is the only way\n", "to make sure that you really know what you are doing.\n", "Moreover, when it is time to customize models,\n", "defining our own layers or loss functions,\n", "understanding how things work under the hood will prove handy.\n", "In this section, we will rely only \n", "on tensors and automatic differentiation.\n", "Later, we will introduce a more concise implementation,\n", "taking advantage of the bells and whistles of deep learning frameworks \n", "while retaining the structure of what follows below.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "e05d8aba", "metadata": {"attributes": {"classes": [], "id": "", "n": "3"}, "execution": {"iopub.execute_input": "2023-08-18T19:42:51.015606Z", "iopub.status.busy": "2023-08-18T19:42:51.014819Z", "iopub.status.idle": "2023-08-18T19:42:54.292743Z", "shell.execute_reply": "2023-08-18T19:42:54.291780Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "caf49f7e", "metadata": {"origin_pos": 6}, "source": ["## Defining the Model\n", "\n", "[**Before we can begin optimizing our model's parameters**] by minibatch SGD,\n", "(**we need to have some parameters in the first place.**)\n", "In the following we initialize weights by drawing\n", "random numbers from a normal distribution with mean 0\n", "and a standard deviation of 0.01. \n", "The magic number 0.01 often works well in practice, \n", "but you can specify a different value \n", "through the argument `sigma`.\n", "Moreover we set the bias to 0.\n", "Note that for object-oriented design\n", "we add the code to the `__init__` method of a subclass of `d2l.Module` (introduced in :numref:`subsec_oo-design-models`).\n"]}, {"cell_type": "code", "execution_count": 2, "id": "d007e745", "metadata": {"attributes": {"classes": [], "id": "", "n": "6"}, "execution": {"iopub.execute_input": "2023-08-18T19:42:54.297196Z", "iopub.status.busy": "2023-08-18T19:42:54.296343Z", "iopub.status.idle": "2023-08-18T19:42:54.302370Z", "shell.execute_reply": "2023-08-18T19:42:54.301603Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["class LinearRegressionScratch(d2l.Module):  #@save\n", "    \"\"\"The linear regression model implemented from scratch.\"\"\"\n", "    def __init__(self, num_inputs, lr, sigma=0.01):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        self.w = torch.normal(0, sigma, (num_inputs, 1), requires_grad=True)\n", "        self.b = torch.zeros(1, requires_grad=True)"]}, {"cell_type": "markdown", "id": "71097a3f", "metadata": {"origin_pos": 9}, "source": ["Next we must [**define our model,\n", "relating its input and parameters to its output.**]\n", "Using the same notation as :eqref:`eq_linreg-y-vec`\n", "for our linear model we simply take the matrix--vector product\n", "of the input features $\\mathbf{X}$ \n", "and the model weights $\\mathbf{w}$,\n", "and add the offset $b$ to each example.\n", "The product $\\mathbf{Xw}$ is a vector and $b$ is a scalar.\n", "Because of the broadcasting mechanism \n", "(see :numref:`subsec_broadcasting`),\n", "when we add a vector and a scalar,\n", "the scalar is added to each component of the vector.\n", "The resulting `forward` method \n", "is registered in the `LinearRegressionScratch` class\n", "via `add_to_class` (introduced in :numref:`oo-design-utilities`).\n"]}, {"cell_type": "code", "execution_count": 3, "id": "1306d051", "metadata": {"attributes": {"classes": [], "id": "", "n": "8"}, "execution": {"iopub.execute_input": "2023-08-18T19:42:54.305721Z", "iopub.status.busy": "2023-08-18T19:42:54.305204Z", "iopub.status.idle": "2023-08-18T19:42:54.309765Z", "shell.execute_reply": "2023-08-18T19:42:54.308692Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(LinearRegressionScratch)  #@save\n", "def forward(self, X):\n", "    return torch.matmul(X, self.w) + self.b"]}, {"cell_type": "markdown", "id": "e939c258", "metadata": {"origin_pos": 11}, "source": ["## Defining the Loss Function\n", "\n", "Since [**updating our model requires taking\n", "the gradient of our loss function,**]\n", "we ought to (**define the loss function first.**)\n", "Here we use the squared loss function\n", "in :eqref:`eq_mse`.\n", "In the implementation, we need to transform the true value `y`\n", "into the predicted value's shape `y_hat`.\n", "The result returned by the following method\n", "will also have the same shape as `y_hat`. \n", "We also return the averaged loss value\n", "among all examples in the minibatch.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6509851d", "metadata": {"attributes": {"classes": [], "id": "", "n": "9"}, "execution": {"iopub.execute_input": "2023-08-18T19:42:54.313880Z", "iopub.status.busy": "2023-08-18T19:42:54.313169Z", "iopub.status.idle": "2023-08-18T19:42:54.318867Z", "shell.execute_reply": "2023-08-18T19:42:54.317836Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(LinearRegressionScratch)  #@save\n", "def loss(self, y_hat, y):\n", "    l = (y_hat - y) ** 2 / 2\n", "    return l.mean()"]}, {"cell_type": "markdown", "id": "4285b751", "metadata": {"origin_pos": 14}, "source": ["## Defining the Optimization Algorithm\n", "\n", "As discussed in :numref:`sec_linear_regression`,\n", "linear regression has a closed-form solution.\n", "However, our goal here is to illustrate \n", "how to train more general neural networks,\n", "and that requires that we teach you \n", "how to use minibatch SGD.\n", "Hence we will take this opportunity\n", "to introduce your first working example of SGD.\n", "At each step, using a minibatch \n", "randomly drawn from our dataset,\n", "we estimate the gradient of the loss\n", "with respect to the parameters.\n", "Next, we update the parameters\n", "in the direction that may reduce the loss.\n", "\n", "The following code applies the update, \n", "given a set of parameters, a learning rate `lr`.\n", "Since our loss is computed as an average over the minibatch, \n", "we do not need to adjust the learning rate against the batch size. \n", "In later chapters we will investigate \n", "how learning rates should be adjusted\n", "for very large minibatches as they arise \n", "in distributed large-scale learning.\n", "For now, we can ignore this dependency.\n"]}, {"cell_type": "markdown", "id": "fb3bc263", "metadata": {"origin_pos": 16, "tab": ["pytorch"]}, "source": ["We define our `SGD` class,\n", "a subclass of `d2l.HyperParameters` (introduced in :numref:`oo-design-utilities`),\n", "to have a similar API \n", "as the built-in SGD optimizer.\n", "We update the parameters in the `step` method.\n", "The `zero_grad` method sets all gradients to 0,\n", "which must be run before a backpropagation step.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ee40ef54", "metadata": {"attributes": {"classes": [], "id": "", "n": "11"}, "execution": {"iopub.execute_input": "2023-08-18T19:42:54.322951Z", "iopub.status.busy": "2023-08-18T19:42:54.322264Z", "iopub.status.idle": "2023-08-18T19:42:54.329600Z", "shell.execute_reply": "2023-08-18T19:42:54.328587Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [], "source": ["class SGD(d2l.HyperParameters):  #@save\n", "    \"\"\"Minibatch stochastic gradient descent.\"\"\"\n", "    def __init__(self, params, lr):\n", "        self.save_hyperparameters()\n", "\n", "    def step(self):\n", "        for param in self.params:\n", "            param -= self.lr * param.grad\n", "\n", "    def zero_grad(self):\n", "        for param in self.params:\n", "            if param.grad is not None:\n", "                param.grad.zero_()"]}, {"cell_type": "markdown", "id": "00c390bf", "metadata": {"origin_pos": 21}, "source": ["We next define the `configure_optimizers` method, which returns an instance of the `SGD` class.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "8a5a3a40", "metadata": {"attributes": {"classes": [], "id": "", "n": "14"}, "execution": {"iopub.execute_input": "2023-08-18T19:42:54.333602Z", "iopub.status.busy": "2023-08-18T19:42:54.332931Z", "iopub.status.idle": "2023-08-18T19:42:54.338188Z", "shell.execute_reply": "2023-08-18T19:42:54.336975Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(LinearRegressionScratch)  #@save\n", "def configure_optimizers(self):\n", "    return SGD([self.w, self.b], self.lr)"]}, {"cell_type": "markdown", "id": "a965a93a", "metadata": {"origin_pos": 23}, "source": ["## Training\n", "\n", "Now that we have all of the parts in place\n", "(parameters, loss function, model, and optimizer),\n", "we are ready to [**implement the main training loop.**]\n", "It is crucial that you understand this code fully\n", "since you will employ similar training loops\n", "for every other deep learning model\n", "covered in this book.\n", "In each *epoch*, we iterate through \n", "the entire training dataset, \n", "passing once through every example\n", "(assuming that the number of examples \n", "is divisible by the batch size). \n", "In each *iteration*, we grab a minibatch of training examples,\n", "and compute its loss through the model's `training_step` method. \n", "Then we compute the gradients with respect to each parameter. \n", "Finally, we will call the optimization algorithm\n", "to update the model parameters. \n", "In summary, we will execute the following loop:\n", "\n", "* Initialize parameters $(\\mathbf{w}, b)$\n", "* Repeat until done\n", "    * Compute gradient $\\mathbf{g} \\leftarrow \\partial_{(\\mathbf{w},b)} \\frac{1}{|\\mathcal{B}|} \\sum_{i \\in \\mathcal{B}} l(\\mathbf{x}^{(i)}, y^{(i)}, \\mathbf{w}, b)$\n", "    * Update parameters $(\\mathbf{w}, b) \\leftarrow (\\mathbf{w}, b) - \\eta \\mathbf{g}$\n", " \n", "Recall that the synthetic regression dataset \n", "that we generated in :numref:``sec_synthetic-regression-data`` \n", "does not provide a validation dataset. \n", "In most cases, however, \n", "we will want a validation dataset \n", "to measure our model quality. \n", "Here we pass the validation dataloader \n", "once in each epoch to measure the model performance.\n", "Following our object-oriented design,\n", "the `prepare_batch` and `fit_epoch` methods\n", "are registered in the `d2l.Trainer` class\n", "(introduced in :numref:`oo-design-training`).\n"]}, {"cell_type": "code", "execution_count": 7, "id": "0c422c5b", "metadata": {"attributes": {"classes": [], "id": "", "n": "15"}, "execution": {"iopub.execute_input": "2023-08-18T19:42:54.342007Z", "iopub.status.busy": "2023-08-18T19:42:54.341174Z", "iopub.status.idle": "2023-08-18T19:42:54.345995Z", "shell.execute_reply": "2023-08-18T19:42:54.344948Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(d2l.Trainer)  #@save\n", "def prepare_batch(self, batch):\n", "    return batch"]}, {"cell_type": "code", "execution_count": 8, "id": "9f43b679", "metadata": {"attributes": {"classes": [], "id": "", "n": "16"}, "execution": {"iopub.execute_input": "2023-08-18T19:42:54.349687Z", "iopub.status.busy": "2023-08-18T19:42:54.348979Z", "iopub.status.idle": "2023-08-18T19:42:54.355255Z", "shell.execute_reply": "2023-08-18T19:42:54.354485Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(d2l.Trainer)  #@save\n", "def fit_epoch(self):\n", "    self.model.train()\n", "    for batch in self.train_dataloader:\n", "        loss = self.model.training_step(self.prepare_batch(batch))\n", "        self.optim.zero_grad()\n", "        with torch.no_grad():\n", "            loss.backward()\n", "            if self.gradient_clip_val > 0:  # To be discussed later\n", "                self.clip_gradients(self.gradient_clip_val, self.model)\n", "            self.optim.step()\n", "        self.train_batch_idx += 1\n", "    if self.val_dataloader is None:\n", "        return\n", "    self.model.eval()\n", "    for batch in self.val_dataloader:\n", "        with torch.no_grad():\n", "            self.model.validation_step(self.prepare_batch(batch))\n", "        self.val_batch_idx += 1"]}, {"cell_type": "markdown", "id": "b9fc0166", "metadata": {"origin_pos": 29}, "source": ["We are almost ready to train the model,\n", "but first we need some training data.\n", "Here we use the `SyntheticRegressionData` class \n", "and pass in some ground truth parameters.\n", "Then we train our model with \n", "the learning rate `lr=0.03` \n", "and set `max_epochs=3`. \n", "Note that in general, both the number of epochs \n", "and the learning rate are hyperparameters.\n", "In general, setting hyperparameters is tricky\n", "and we will usually want to use a three-way split,\n", "one set for training, \n", "a second for hyperparameter selection,\n", "and the third reserved for the final evaluation.\n", "We elide these details for now but will revise them\n", "later.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "d45852e3", "metadata": {"attributes": {"classes": [], "id": "", "n": "20"}, "execution": {"iopub.execute_input": "2023-08-18T19:42:54.359835Z", "iopub.status.busy": "2023-08-18T19:42:54.359070Z", "iopub.status.idle": "2023-08-18T19:42:56.328769Z", "shell.execute_reply": "2023-08-18T19:42:56.327907Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"237.376562pt\" height=\"183.35625pt\" viewBox=\"0 0 237.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:42:56.275496</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 237.**********.35625 \n", "L 237.376562 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 26.925 145.8 \n", "L 222.225 145.8 \n", "L 222.225 7.2 \n", "L 26.925 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m2a814b1784\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m2a814b1784\" x=\"26.925\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(18.973438 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m2a814b1784\" x=\"59.475\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(51.523438 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m2a814b1784\" x=\"92.025\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(84.073438 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m2a814b1784\" x=\"124.575\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(116.623437 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m2a814b1784\" x=\"157.125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(149.173438 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m2a814b1784\" x=\"189.675\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(181.723438 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m2a814b1784\" x=\"222.225\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 3.0 -->\n", "      <g transform=\"translate(214.273438 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(109.346875 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_8\">\n", "      <defs>\n", "       <path id=\"mfe3164249b\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mfe3164249b\" x=\"26.925\" y=\"140.010717\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(13.5625 143.809935) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mfe3164249b\" x=\"26.925\" y=\"116.488726\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(13.5625 120.287945) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mfe3164249b\" x=\"26.925\" y=\"92.966736\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(13.5625 96.765954) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#mfe3164249b\" x=\"26.925\" y=\"69.444745\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(13.5625 73.243964) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mfe3164249b\" x=\"26.925\" y=\"45.922755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(13.5625 49.721974) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#mfe3164249b\" x=\"26.925\" y=\"22.400764\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(7.2 26.199983) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 42.182813 13.5 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 88.078244 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 88.078244 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 92.025 114.022841 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 88.078244 \n", "L 107.282813 121.6734 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 92.025 114.022841 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 88.078244 \n", "L 107.282813 121.6734 \n", "L 139.832812 134.329444 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 92.025 114.022841 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 88.078244 \n", "L 107.282813 121.6734 \n", "L 139.832812 134.329444 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 92.025 114.022841 \n", "L 157.125 136.314918 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 88.078244 \n", "L 107.282813 121.6734 \n", "L 139.832812 134.329444 \n", "L 172.382812 137.535891 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 92.025 114.022841 \n", "L 157.125 136.314918 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 88.078244 \n", "L 107.282813 121.6734 \n", "L 139.832812 134.329444 \n", "L 172.382812 137.535891 \n", "L 204.932812 139.067789 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 92.025 114.022841 \n", "L 157.125 136.314918 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 42.182813 13.5 \n", "L 74.732812 88.078244 \n", "L 107.282813 121.6734 \n", "L 139.832812 134.329444 \n", "L 172.382812 137.535891 \n", "L 204.932812 139.067789 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 92.025 114.022841 \n", "L 157.125 136.314918 \n", "L 222.225 139.5 \n", "\" clip-path=\"url(#pf7350e1209)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 26.925 145.8 \n", "L 26.925 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 222.225 145.8 \n", "L 222.225 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 26.925 145.8 \n", "L 222.225 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 26.925 7.2 \n", "L 222.225 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 135.634375 45.1125 \n", "L 215.225 45.1125 \n", "Q 217.225 45.1125 217.225 43.1125 \n", "L 217.225 14.2 \n", "Q 217.225 12.2 215.225 12.2 \n", "L 135.634375 12.2 \n", "Q 133.634375 12.2 133.634375 14.2 \n", "L 133.634375 43.1125 \n", "Q 133.634375 45.1125 135.634375 45.1125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 137.634375 20.298438 \n", "L 147.634375 20.298438 \n", "L 157.634375 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(165.634375 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_31\">\n", "     <path d=\"M 137.634375 35.254688 \n", "L 147.634375 35.254688 \n", "L 157.634375 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(165.634375 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf7350e1209\">\n", "   <rect x=\"26.925\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = LinearRegressionScratch(2, lr=0.03)\n", "data = d2l.SyntheticRegressionData(w=torch.tensor([2, -3.4]), b=4.2)\n", "trainer = d2l.Trainer(max_epochs=3)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "07628bb6", "metadata": {"origin_pos": 31}, "source": ["Because we synthesized the dataset ourselves,\n", "we know precisely what the true parameters are.\n", "Thus, we can [**evaluate our success in training\n", "by comparing the true parameters\n", "with those that we learned**] through our training loop.\n", "Indeed they turn out to be very close to each other.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "5a72b404", "metadata": {"attributes": {"classes": [], "id": "", "n": "21"}, "execution": {"iopub.execute_input": "2023-08-18T19:42:56.334422Z", "iopub.status.busy": "2023-08-18T19:42:56.333858Z", "iopub.status.idle": "2023-08-18T19:42:56.340281Z", "shell.execute_reply": "2023-08-18T19:42:56.339444Z"}, "origin_pos": 32, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["error in estimating w: tensor([ 0.1408, -0.1493])\n", "error in estimating b: tensor([0.2130])\n"]}], "source": ["with torch.no_grad():\n", "    print(f'error in estimating w: {data.w - model.w.reshape(data.w.shape)}')\n", "    print(f'error in estimating b: {data.b - model.b}')"]}, {"cell_type": "markdown", "id": "39644e44", "metadata": {"origin_pos": 35}, "source": ["We should not take the ability to exactly recover \n", "the ground truth parameters for granted.\n", "In general, for deep models unique solutions\n", "for the parameters do not exist,\n", "and even for linear models,\n", "exactly recovering the parameters\n", "is only possible when no feature \n", "is linearly dependent on the others.\n", "However, in machine learning, \n", "we are often less concerned\n", "with recovering true underlying parameters,\n", "but rather with parameters \n", "that lead to highly accurate prediction :cite:`Vapnik.1992`.\n", "Fortunately, even on difficult optimization problems,\n", "stochastic gradient descent can often find remarkably good solutions,\n", "owing partly to the fact that, for deep networks,\n", "there exist many configurations of the parameters\n", "that lead to highly accurate prediction.\n", "\n", "\n", "## Summary\n", "\n", "In this section, we took a significant step \n", "towards designing deep learning systems \n", "by implementing a fully functional \n", "neural network model and training loop.\n", "In this process, we built a data loader, \n", "a model, a loss function, an optimization procedure,\n", "and a visualization and monitoring tool. \n", "We did this by composing a Python object \n", "that contains all relevant components for training a model. \n", "While this is not yet a professional-grade implementation\n", "it is perfectly functional and code like this \n", "could already help you to solve small problems quickly.\n", "In the coming sections, we will see how to do this\n", "both *more concisely* (avoiding boilerplate code)\n", "and *more efficiently* (using our GPUs to their full potential).\n", "\n", "\n", "\n", "## Exercises\n", "\n", "1. What would happen if we were to initialize the weights to zero. Would the algorithm still work? What if we\n", "   initialized the parameters with variance $1000$ rather than $0.01$?\n", "1. Assume that you are [<PERSON>](https://en.wikipedia.org/wiki/<PERSON>_<PERSON>) trying to come up\n", "   with a model for resistance that relates voltage and current. Can you use automatic\n", "   differentiation to learn the parameters of your model?\n", "1. Can you use [<PERSON><PERSON>'s Law](https://en.wikipedia.org/wiki/Planck%27s_law) to determine the temperature of an object\n", "   using spectral energy density? For reference, the spectral density $B$ of radiation emanating from a black body is\n", "   $B(\\lambda, T) = \\frac{2 hc^2}{\\lambda^5} \\cdot \\left(\\exp \\frac{h c}{\\lambda k T} - 1\\right)^{-1}$. Here\n", "   $\\lambda$ is the wavelength, $T$ is the temperature, $c$ is the speed of light, $h$ is <PERSON><PERSON>'s constant, and $k$ is the\n", "   Boltzmann constant. You measure the energy for different wavelengths $\\lambda$ and you now need to fit the spectral\n", "   density curve to <PERSON><PERSON>'s law.\n", "1. What are the problems you might encounter if you wanted to compute the second derivatives of the loss? How would\n", "   you fix them?\n", "1. Why is the `reshape` method needed in the `loss` function?\n", "1. Experiment using different learning rates to find out how quickly the loss function value drops. Can you reduce the\n", "   error by increasing the number of epochs of training?\n", "1. If the number of examples cannot be divided by the batch size, what happens to `data_iter` at the end of an epoch?\n", "1. Try implementing a different loss function, such as the absolute value loss `(y_hat - d2l.reshape(y, y_hat.shape)).abs().sum()`.\n", "    1. Check what happens for regular data.\n", "    1. Check whether there is a difference in behavior if you actively perturb some entries, such as $y_5 = 10000$, of $\\mathbf{y}$.\n", "    1. Can you think of a cheap solution for combining the best aspects of squared loss and absolute value loss?\n", "       Hint: how can you avoid really large gradient values?\n", "1. Why do we need to reshuffle the dataset? Can you design a case where a maliciously constructed dataset would break the optimization algorithm otherwise?\n"]}, {"cell_type": "markdown", "id": "d533576d", "metadata": {"origin_pos": 37, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/43)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
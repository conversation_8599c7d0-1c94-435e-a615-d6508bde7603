{"cells": [{"cell_type": "markdown", "id": "f27fb3d8", "metadata": {"origin_pos": 1}, "source": ["# Weight Decay\n", ":label:`sec_weight_decay`\n", "\n", "Now that we have characterized the problem of overfitting,\n", "we can introduce our first *regularization* technique.\n", "Recall that we can always mitigate overfitting\n", "by collecting more training data.\n", "However, that can be costly, time consuming,\n", "or entirely out of our control,\n", "making it impossible in the short run.\n", "For now, we can assume that we already have\n", "as much high-quality data as our resources permit\n", "and focus the tools at our disposal\n", "when the dataset is taken as a given.\n", "\n", "Recall that in our polynomial regression example\n", "(:numref:`subsec_polynomial-curve-fitting`)\n", "we could limit our model's capacity\n", "by tweaking the degree\n", "of the fitted polynomial.\n", "Indeed, limiting the number of features\n", "is a popular technique for mitigating overfitting.\n", "However, simply tossing aside features\n", "can be too blunt an instrument.\n", "Sticking with the polynomial regression\n", "example, consider what might happen\n", "with high-dimensional input.\n", "The natural extensions of polynomials\n", "to multivariate data are called *monomials*,\n", "which are simply products of powers of variables.\n", "The degree of a monomial is the sum of the powers.\n", "For example, $x_1^2 x_2$, and $x_3 x_5^2$\n", "are both monomials of degree 3.\n", "\n", "Note that the number of terms with degree $d$\n", "blows up rapidly as $d$ grows larger.\n", "Given $k$ variables, the number of monomials\n", "of degree $d$ is ${k - 1 + d} \\choose {k - 1}$.\n", "Even small changes in degree, say from $2$ to $3$,\n", "dramatically increase the complexity of our model.\n", "Thus we often need a more fine-grained tool\n", "for adjusting function complexity.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "e143c528", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:33.457379Z", "iopub.status.busy": "2023-08-18T19:45:33.456856Z", "iopub.status.idle": "2023-08-18T19:45:36.417497Z", "shell.execute_reply": "2023-08-18T19:45:36.416229Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "8be4ac63", "metadata": {"origin_pos": 6}, "source": ["## Norms and Weight Decay\n", "\n", "(**Rather than directly manipulating the number of parameters,\n", "*weight decay*, operates by restricting the values \n", "that the parameters can take.**)\n", "More commonly called $\\ell_2$ regularization\n", "outside of deep learning circles\n", "when optimized by minibatch stochastic gradient descent,\n", "weight decay might be the most widely used technique\n", "for regularizing parametric machine learning models.\n", "The technique is motivated by the basic intuition\n", "that among all functions $f$,\n", "the function $f = 0$\n", "(assigning the value $0$ to all inputs)\n", "is in some sense the *simplest*,\n", "and that we can measure the complexity\n", "of a function by the distance of its parameters from zero.\n", "But how precisely should we measure\n", "the distance between a function and zero?\n", "There is no single right answer.\n", "In fact, entire branches of mathematics,\n", "including parts of functional analysis\n", "and the theory of Banach spaces,\n", "are devoted to addressing such issues.\n", "\n", "One simple interpretation might be\n", "to measure the complexity of a linear function\n", "$f(\\mathbf{x}) = \\mathbf{w}^\\top \\mathbf{x}$\n", "by some norm of its weight vector, e.g., $\\| \\mathbf{w} \\|^2$.\n", "Recall that we introduced the $\\ell_2$ norm and $\\ell_1$ norm,\n", "which are special cases of the more general $\\ell_p$ norm,\n", "in :numref:`subsec_lin-algebra-norms`.\n", "The most common method for ensuring a small weight vector\n", "is to add its norm as a penalty term\n", "to the problem of minimizing the loss.\n", "Thus we replace our original objective,\n", "*minimizing the prediction loss on the training labels*,\n", "with new objective,\n", "*minimizing the sum of the prediction loss and the penalty term*.\n", "Now, if our weight vector grows too large,\n", "our learning algorithm might focus\n", "on minimizing the weight norm $\\| \\mathbf{w} \\|^2$\n", "rather than minimizing the training error.\n", "That is exactly what we want.\n", "To illustrate things in code,\n", "we revive our previous example\n", "from :numref:`sec_linear_regression` for linear regression.\n", "There, our loss was given by\n", "\n", "$$L(\\mathbf{w}, b) = \\frac{1}{n}\\sum_{i=1}^n \\frac{1}{2}\\left(\\mathbf{w}^\\top \\mathbf{x}^{(i)} + b - y^{(i)}\\right)^2.$$\n", "\n", "Recall that $\\mathbf{x}^{(i)}$ are the features,\n", "$y^{(i)}$ is the label for any data example $i$, and $(\\mathbf{w}, b)$\n", "are the weight and bias parameters, respectively.\n", "To penalize the size of the weight vector,\n", "we must somehow add $\\| \\mathbf{w} \\|^2$ to the loss function,\n", "but how should the model trade off the\n", "standard loss for this new additive penalty?\n", "In practice, we characterize this trade-off\n", "via the *regularization constant* $\\lambda$,\n", "a nonnegative hyperparameter\n", "that we fit using validation data:\n", "\n", "$$L(\\mathbf{w}, b) + \\frac{\\lambda}{2} \\|\\mathbf{w}\\|^2.$$\n", "\n", "\n", "For $\\lambda = 0$, we recover our original loss function.\n", "For $\\lambda > 0$, we restrict the size of $\\| \\mathbf{w} \\|$.\n", "We divide by $2$ by convention:\n", "when we take the derivative of a quadratic function,\n", "the $2$ and $1/2$ cancel out, ensuring that the expression\n", "for the update looks nice and simple.\n", "The astute reader might wonder why we work with the squared\n", "norm and not the standard norm (i.e., the Euclidean distance).\n", "We do this for computational convenience.\n", "By squaring the $\\ell_2$ norm, we remove the square root,\n", "leaving the sum of squares of\n", "each component of the weight vector.\n", "This makes the derivative of the penalty easy to compute: \n", "the sum of derivatives equals the derivative of the sum.\n", "\n", "\n", "Moreover, you might ask why we work with the $\\ell_2$ norm\n", "in the first place and not, say, the $\\ell_1$ norm.\n", "In fact, other choices are valid and\n", "popular throughout statistics.\n", "While $\\ell_2$-regularized linear models constitute\n", "the classic *ridge regression* algorithm,\n", "$\\ell_1$-regularized linear regression\n", "is a similarly fundamental method in statistics, \n", "popularly known as *lasso regression*.\n", "One reason to work with the $\\ell_2$ norm\n", "is that it places an outsize penalty\n", "on large components of the weight vector.\n", "This biases our learning algorithm\n", "towards models that distribute weight evenly\n", "across a larger number of features.\n", "In practice, this might make them more robust\n", "to measurement error in a single variable.\n", "By contrast, $\\ell_1$ penalties lead to models\n", "that concentrate weights on a small set of features\n", "by clearing the other weights to zero.\n", "This gives us an effective method for *feature selection*,\n", "which may be desirable for other reasons.\n", "For example, if our model only relies on a few features,\n", "then we may not need to collect, store, or transmit data\n", "for the other (dropped) features. \n", "\n", "Using the same notation in :eqref:`eq_linreg_batch_update`,\n", "minibatch stochastic gradient descent updates\n", "for $\\ell_2$-regularized regression as follows:\n", "\n", "$$\\begin{aligned}\n", "\\mathbf{w} & \\leftarrow \\left(1- \\eta\\lambda \\right) \\mathbf{w} - \\frac{\\eta}{|\\mathcal{B}|} \\sum_{i \\in \\mathcal{B}} \\mathbf{x}^{(i)} \\left(\\mathbf{w}^\\top \\mathbf{x}^{(i)} + b - y^{(i)}\\right).\n", "\\end{aligned}$$\n", "\n", "As before, we update $\\mathbf{w}$ based on the amount\n", "by which our estimate differs from the observation.\n", "However, we also shrink the size of $\\mathbf{w}$ towards zero.\n", "That is why the method is sometimes called \"weight decay\":\n", "given the penalty term alone,\n", "our optimization algorithm *decays*\n", "the weight at each step of training.\n", "In contrast to feature selection,\n", "weight decay offers us a mechanism for continuously adjusting the complexity of a function.\n", "Smaller values of $\\lambda$ correspond\n", "to less constrained $\\mathbf{w}$,\n", "whereas larger values of $\\lambda$\n", "constrain $\\mathbf{w}$ more considerably.\n", "Whether we include a corresponding bias penalty $b^2$ \n", "can vary across implementations, \n", "and may vary across layers of a neural network.\n", "Often, we do not regularize the bias term.\n", "Besides,\n", "although $\\ell_2$ regularization may not be equivalent to weight decay for other optimization algorithms,\n", "the idea of regularization through\n", "shrinking the size of weights\n", "still holds true.\n", "\n", "## High-Dimensional Linear Regression\n", "\n", "We can illustrate the benefits of weight decay \n", "through a simple synthetic example.\n", "\n", "First, we [**generate some data as before**]:\n", "\n", "(**$$y = 0.05 + \\sum_{i = 1}^d 0.01 x_i + \\epsilon \\textrm{ where }\n", "\\epsilon \\sim \\mathcal{N}(0, 0.01^2).$$**)\n", "\n", "In this synthetic dataset, our label is given \n", "by an underlying linear function of our inputs,\n", "corrupted by Gaussian noise \n", "with zero mean and standard deviation 0.01.\n", "For illustrative purposes, \n", "we can make the effects of overfitting pronounced,\n", "by increasing the dimensionality of our problem to $d = 200$\n", "and working with a small training set with only 20 examples.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "c254bc8e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:36.422127Z", "iopub.status.busy": "2023-08-18T19:45:36.421372Z", "iopub.status.idle": "2023-08-18T19:45:36.428080Z", "shell.execute_reply": "2023-08-18T19:45:36.427182Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["class Data(d2l.DataModule):\n", "    def __init__(self, num_train, num_val, num_inputs, batch_size):\n", "        self.save_hyperparameters()\n", "        n = num_train + num_val\n", "        self.X = torch.randn(n, num_inputs)\n", "        noise = torch.randn(n, 1) * 0.01\n", "        w, b = torch.ones((num_inputs, 1)) * 0.01, 0.05\n", "        self.y = torch.matmul(self.X, w) + b + noise\n", "\n", "    def get_dataloader(self, train):\n", "        i = slice(0, self.num_train) if train else slice(self.num_train, None)\n", "        return self.get_tensorloader([self.X, self.y], train, i)"]}, {"cell_type": "markdown", "id": "53fd0785", "metadata": {"origin_pos": 8}, "source": ["## Implementation from Scratch\n", "\n", "Now, let's try implementing weight decay from scratch.\n", "Since minibatch stochastic gradient descent\n", "is our optimizer,\n", "we just need to add the squared $\\ell_2$ penalty\n", "to the original loss function.\n", "\n", "### (**Defining $\\ell_2$ Norm Penalty**)\n", "\n", "Perhaps the most convenient way of implementing this penalty\n", "is to square all terms in place and sum them.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "0c593dfb", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:36.431661Z", "iopub.status.busy": "2023-08-18T19:45:36.430940Z", "iopub.status.idle": "2023-08-18T19:45:36.434890Z", "shell.execute_reply": "2023-08-18T19:45:36.434104Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["def l2_penalty(w):\n", "    return (w ** 2).sum() / 2"]}, {"cell_type": "markdown", "id": "11f<PERSON>dd<PERSON>", "metadata": {"origin_pos": 10}, "source": ["### Defining the Model\n", "\n", "In the final model,\n", "the linear regression and the squared loss have not changed since :numref:`sec_linear_scratch`,\n", "so we will just define a subclass of `d2l.LinearRegressionScratch`. The only change here is that our loss now includes the penalty term.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "0cc93ca2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:36.438193Z", "iopub.status.busy": "2023-08-18T19:45:36.437628Z", "iopub.status.idle": "2023-08-18T19:45:36.442519Z", "shell.execute_reply": "2023-08-18T19:45:36.441749Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [], "source": ["class WeightDecayScratch(d2l.LinearRegressionScratch):\n", "    def __init__(self, num_inputs, lambd, lr, sigma=0.01):\n", "        super().__init__(num_inputs, lr, sigma)\n", "        self.save_hyperparameters()\n", "\n", "    def loss(self, y_hat, y):\n", "        return (super().loss(y_hat, y) +\n", "                self.lambd * l2_penalty(self.w))"]}, {"cell_type": "markdown", "id": "c9d78212", "metadata": {"origin_pos": 13}, "source": ["The following code fits our model on the training set with 20 examples and evaluates it on the validation set with 100 examples.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "11c12f35", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:36.445796Z", "iopub.status.busy": "2023-08-18T19:45:36.445315Z", "iopub.status.idle": "2023-08-18T19:45:36.475713Z", "shell.execute_reply": "2023-08-18T19:45:36.474732Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["data = Data(num_train=20, num_val=100, num_inputs=200, batch_size=5)\n", "trainer = d2l.Trainer(max_epochs=10)\n", "\n", "def train_scratch(lambd):\n", "    model = WeightDecayScratch(num_inputs=200, lambd=lambd, lr=0.01)\n", "    model.board.yscale='log'\n", "    trainer.fit(model, data)\n", "    print('L2 norm of w:', float(l2_penalty(model.w)))"]}, {"cell_type": "markdown", "id": "891546e7", "metadata": {"origin_pos": 15}, "source": ["### [**Training without Regularization**]\n", "\n", "We now run this code with `lambd = 0`,\n", "disabling weight decay.\n", "Note that we overfit badly,\n", "decreasing the training error but not the\n", "validation error---a textbook case of overfitting.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "e63ee5d3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:36.479194Z", "iopub.status.busy": "2023-08-18T19:45:36.478880Z", "iopub.status.idle": "2023-08-18T19:45:47.229507Z", "shell.execute_reply": "2023-08-18T19:45:47.228580Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["L2 norm of w: 0.009948714636266232\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"246.5625pt\" height=\"183.35625pt\" viewBox=\"0 0 246.5625 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:47.082133</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 246.5625 183.35625 \n", "L 246.5625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 37.7 145.8 \n", "L 233 145.8 \n", "L 233 7.2 \n", "L 37.7 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"ma78f1bcc84\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma78f1bcc84\" x=\"37.7\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(34.51875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#ma78f1bcc84\" x=\"76.76\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(73.57875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#ma78f1bcc84\" x=\"115.82\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(112.63875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#ma78f1bcc84\" x=\"154.88\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(151.69875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#ma78f1bcc84\" x=\"193.94\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(190.75875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#ma78f1bcc84\" x=\"233\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(226.6375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(120.121875 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"ma56eaefdca\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma56eaefdca\" x=\"37.7\" y=\"121.391811\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{10^{-4}}$ -->\n", "      <g transform=\"translate(7.2 125.191029) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(186.855469 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#ma56eaefdca\" x=\"37.7\" y=\"77.393907\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- $\\mathdefault{10^{-3}}$ -->\n", "      <g transform=\"translate(7.2 81.193125) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(186.855469 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#ma56eaefdca\" x=\"37.7\" y=\"33.396002\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- $\\mathdefault{10^{-2}}$ -->\n", "      <g transform=\"translate(7.2 37.195221) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(186.855469 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m996bfc37ac\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"144.397379\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"138.900337\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"134.636499\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"131.152691\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"128.207172\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"125.655648\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"123.405044\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"108.147122\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"100.399475\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"94.902433\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"90.638595\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"87.154787\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"84.209268\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"81.657744\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"79.40714\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"64.149218\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"56.401571\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"50.904529\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"46.640691\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"43.156882\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"40.211364\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"37.65984\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"35.409236\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"20.151314\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m996bfc37ac\" x=\"37.7\" y=\"12.403667\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 40.14125 13.5 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 57.23 17.818095 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 57.23 17.818095 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 57.23 17.818095 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "L 157.32125 103.00531 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "L 157.32125 103.00531 \n", "L 167.08625 100.43102 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "L 157.32125 103.00531 \n", "L 167.08625 100.43102 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "L 174.41 18.493861 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "L 157.32125 103.00531 \n", "L 167.08625 100.43102 \n", "L 176.85125 117.776564 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "L 174.41 18.493861 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "L 157.32125 103.00531 \n", "L 167.08625 100.43102 \n", "L 176.85125 117.776564 \n", "L 186.61625 112.029163 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "L 174.41 18.493861 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "L 157.32125 103.00531 \n", "L 167.08625 100.43102 \n", "L 176.85125 117.776564 \n", "L 186.61625 112.029163 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "L 174.41 18.493861 \n", "L 193.94 18.47841 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "L 157.32125 103.00531 \n", "L 167.08625 100.43102 \n", "L 176.85125 117.776564 \n", "L 186.61625 112.029163 \n", "L 196.38125 126.862017 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "L 174.41 18.493861 \n", "L 193.94 18.47841 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "L 157.32125 103.00531 \n", "L 167.08625 100.43102 \n", "L 176.85125 117.776564 \n", "L 186.61625 112.029163 \n", "L 196.38125 126.862017 \n", "L 206.14625 125.820993 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "L 174.41 18.493861 \n", "L 193.94 18.47841 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "L 157.32125 103.00531 \n", "L 167.08625 100.43102 \n", "L 176.85125 117.776564 \n", "L 186.61625 112.029163 \n", "L 196.38125 126.862017 \n", "L 206.14625 125.820993 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "L 174.41 18.493861 \n", "L 193.94 18.47841 \n", "L 213.47 18.464532 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "L 157.32125 103.00531 \n", "L 167.08625 100.43102 \n", "L 176.85125 117.776564 \n", "L 186.61625 112.029163 \n", "L 196.38125 126.862017 \n", "L 206.14625 125.820993 \n", "L 215.91125 139.5 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "L 174.41 18.493861 \n", "L 193.94 18.47841 \n", "L 213.47 18.464532 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "L 157.32125 103.00531 \n", "L 167.08625 100.43102 \n", "L 176.85125 117.776564 \n", "L 186.61625 112.029163 \n", "L 196.38125 126.862017 \n", "L 206.14625 125.820993 \n", "L 215.91125 139.5 \n", "L 225.67625 137.161207 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "L 174.41 18.493861 \n", "L 193.94 18.47841 \n", "L 213.47 18.464532 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 19.185278 \n", "L 59.67125 24.470284 \n", "L 69.43625 48.080434 \n", "L 79.20125 42.991598 \n", "L 88.96625 53.337218 \n", "L 98.73125 80.193556 \n", "L 108.49625 54.316744 \n", "L 118.26125 84.052369 \n", "L 128.02625 72.010788 \n", "L 137.79125 93.662136 \n", "L 147.55625 86.602651 \n", "L 157.32125 103.00531 \n", "L 167.08625 100.43102 \n", "L 176.85125 117.776564 \n", "L 186.61625 112.029163 \n", "L 196.38125 126.862017 \n", "L 206.14625 125.820993 \n", "L 215.91125 139.5 \n", "L 225.67625 137.161207 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 57.23 17.818095 \n", "L 76.76 18.301645 \n", "L 96.29 18.47633 \n", "L 115.82 18.513472 \n", "L 135.35 18.519778 \n", "L 154.88 18.508321 \n", "L 174.41 18.493861 \n", "L 193.94 18.47841 \n", "L 213.47 18.464532 \n", "L 233 18.452934 \n", "\" clip-path=\"url(#p228dad7d4a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 37.7 145.8 \n", "L 37.7 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 233 145.8 \n", "L 233 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 37.7 145.8 \n", "L 233 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 37.7 7.2 \n", "L 233 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 44.7 140.8 \n", "L 124.290625 140.8 \n", "Q 126.290625 140.8 126.290625 138.8 \n", "L 126.290625 109.8875 \n", "Q 126.290625 107.8875 124.290625 107.8875 \n", "L 44.7 107.8875 \n", "Q 42.7 107.8875 42.7 109.8875 \n", "L 42.7 138.8 \n", "Q 42.7 140.8 44.7 140.8 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_93\">\n", "     <path d=\"M 46.7 115.985938 \n", "L 56.7 115.985938 \n", "L 66.7 115.985938 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(74.7 119.485938) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_94\">\n", "     <path d=\"M 46.7 130.942188 \n", "L 56.7 130.942188 \n", "L 66.7 130.942188 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(74.7 134.442188) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p228dad7d4a\">\n", "   <rect x=\"37.7\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["train_scratch(0)"]}, {"cell_type": "markdown", "id": "92cf33eb", "metadata": {"origin_pos": 17}, "source": ["### [**Using Weight Decay**]\n", "\n", "Below, we run with substantial weight decay.\n", "Note that the training error increases\n", "but the validation error decreases.\n", "This is precisely the effect\n", "we expect from regularization.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "8b217c4a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:47.233242Z", "iopub.status.busy": "2023-08-18T19:45:47.232914Z", "iopub.status.idle": "2023-08-18T19:45:57.189155Z", "shell.execute_reply": "2023-08-18T19:45:57.188025Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["L2 norm of w: 0.0017270983662456274\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"246.5625pt\" height=\"183.35625pt\" viewBox=\"0 0 246.5625 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:57.099881</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 246.5625 183.35625 \n", "L 246.5625 -0 \n", "L 0 -0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 37.7 145.8 \n", "L 233 145.8 \n", "L 233 7.2 \n", "L 37.7 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m8a930cb59b\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8a930cb59b\" x=\"37.7\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(34.51875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m8a930cb59b\" x=\"76.76\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(73.57875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m8a930cb59b\" x=\"115.82\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(112.63875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m8a930cb59b\" x=\"154.88\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(151.69875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m8a930cb59b\" x=\"193.94\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(190.75875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m8a930cb59b\" x=\"233\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(226.6375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(120.121875 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m4c92352054\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m4c92352054\" x=\"37.7\" y=\"114.04803\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{10^{-2}}$ -->\n", "      <g transform=\"translate(7.2 117.847249) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(186.855469 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <defs>\n", "       <path id=\"mf9533ad652\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf9533ad652\" x=\"37.7\" y=\"141.899573\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mf9533ad652\" x=\"37.7\" y=\"133.494877\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mf9533ad652\" x=\"37.7\" y=\"126.214398\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#mf9533ad652\" x=\"37.7\" y=\"119.79256\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mf9533ad652\" x=\"37.7\" y=\"76.255842\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#mf9533ad652\" x=\"37.7\" y=\"54.148829\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mf9533ad652\" x=\"37.7\" y=\"38.463654\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#mf9533ad652\" x=\"37.7\" y=\"26.297286\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mf9533ad652\" x=\"37.7\" y=\"16.356641\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#mf9533ad652\" x=\"37.7\" y=\"7.951944\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 40.14125 13.5 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 57.23 29.1899 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 57.23 29.1899 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 57.23 29.1899 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "L 157.32125 113.4713 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "L 157.32125 113.4713 \n", "L 167.08625 116.092754 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "L 157.32125 113.4713 \n", "L 167.08625 116.092754 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "L 174.41 73.729097 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "L 157.32125 113.4713 \n", "L 167.08625 116.092754 \n", "L 176.85125 123.357701 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "L 174.41 73.729097 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "L 157.32125 113.4713 \n", "L 167.08625 116.092754 \n", "L 176.85125 123.357701 \n", "L 186.61625 123.032432 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "L 174.41 73.729097 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "L 157.32125 113.4713 \n", "L 167.08625 116.092754 \n", "L 176.85125 123.357701 \n", "L 186.61625 123.032432 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "L 174.41 73.729097 \n", "L 193.94 78.542133 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "L 157.32125 113.4713 \n", "L 167.08625 116.092754 \n", "L 176.85125 123.357701 \n", "L 186.61625 123.032432 \n", "L 196.38125 128.811306 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "L 174.41 73.729097 \n", "L 193.94 78.542133 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "L 157.32125 113.4713 \n", "L 167.08625 116.092754 \n", "L 176.85125 123.357701 \n", "L 186.61625 123.032432 \n", "L 196.38125 128.811306 \n", "L 206.14625 133.353198 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "L 174.41 73.729097 \n", "L 193.94 78.542133 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "L 157.32125 113.4713 \n", "L 167.08625 116.092754 \n", "L 176.85125 123.357701 \n", "L 186.61625 123.032432 \n", "L 196.38125 128.811306 \n", "L 206.14625 133.353198 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "L 174.41 73.729097 \n", "L 193.94 78.542133 \n", "L 213.47 82.76226 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "L 157.32125 113.4713 \n", "L 167.08625 116.092754 \n", "L 176.85125 123.357701 \n", "L 186.61625 123.032432 \n", "L 196.38125 128.811306 \n", "L 206.14625 133.353198 \n", "L 215.91125 139.5 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "L 174.41 73.729097 \n", "L 193.94 78.542133 \n", "L 213.47 82.76226 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "L 157.32125 113.4713 \n", "L 167.08625 116.092754 \n", "L 176.85125 123.357701 \n", "L 186.61625 123.032432 \n", "L 196.38125 128.811306 \n", "L 206.14625 133.353198 \n", "L 215.91125 139.5 \n", "L 225.67625 135.70902 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "L 174.41 73.729097 \n", "L 193.94 78.542133 \n", "L 213.47 82.76226 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 40.14125 13.5 \n", "L 49.90625 40.379863 \n", "L 59.67125 47.603958 \n", "L 69.43625 60.444566 \n", "L 79.20125 68.422117 \n", "L 88.96625 73.424552 \n", "L 98.73125 83.240038 \n", "L 108.49625 85.302261 \n", "L 118.26125 93.019822 \n", "L 128.02625 97.995407 \n", "L 137.79125 103.767732 \n", "L 147.55625 107.588641 \n", "L 157.32125 113.4713 \n", "L 167.08625 116.092754 \n", "L 176.85125 123.357701 \n", "L 186.61625 123.032432 \n", "L 196.38125 128.811306 \n", "L 206.14625 133.353198 \n", "L 215.91125 139.5 \n", "L 225.67625 135.70902 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 57.23 29.1899 \n", "L 76.76 39.051237 \n", "L 96.29 47.570004 \n", "L 115.82 55.304014 \n", "L 135.35 62.17482 \n", "L 154.88 68.27847 \n", "L 174.41 73.729097 \n", "L 193.94 78.542133 \n", "L 213.47 82.76226 \n", "L 233 86.176535 \n", "\" clip-path=\"url(#p18846bc135)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 37.7 145.8 \n", "L 37.7 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 233 145.8 \n", "L 233 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 37.7 145.8 \n", "L 233 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 37.7 7.2 \n", "L 233 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 146.409375 45.1125 \n", "L 226 45.1125 \n", "Q 228 45.1125 228 43.1125 \n", "L 228 14.2 \n", "Q 228 12.2 226 12.2 \n", "L 146.409375 12.2 \n", "Q 144.409375 12.2 144.409375 14.2 \n", "L 144.409375 43.1125 \n", "Q 144.409375 45.1125 146.409375 45.1125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_76\">\n", "     <path d=\"M 148.409375 20.298438 \n", "L 158.409375 20.298438 \n", "L 168.409375 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(176.409375 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_77\">\n", "     <path d=\"M 148.409375 35.254688 \n", "L 158.409375 35.254688 \n", "L 168.409375 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(176.409375 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p18846bc135\">\n", "   <rect x=\"37.7\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["train_scratch(3)"]}, {"cell_type": "markdown", "id": "9f439ad4", "metadata": {"origin_pos": 19}, "source": ["## [**Concise Implementation**]\n", "\n", "Because weight decay is ubiquitous\n", "in neural network optimization,\n", "the deep learning framework makes it especially convenient,\n", "integrating weight decay into the optimization algorithm itself\n", "for easy use in combination with any loss function.\n", "Moreover, this integration serves a computational benefit,\n", "allowing implementation tricks to add weight decay to the algorithm,\n", "without any additional computational overhead.\n", "Since the weight decay portion of the update\n", "depends only on the current value of each parameter,\n", "the optimizer must touch each parameter once anyway.\n"]}, {"cell_type": "markdown", "id": "6010d707", "metadata": {"origin_pos": 21, "tab": ["pytorch"]}, "source": ["Below, we specify\n", "the weight decay hyperparameter directly\n", "through `weight_decay` when instantiating our optimizer.\n", "By default, PyTorch decays both\n", "weights and biases simultaneously, but\n", "we can configure the optimizer to handle different parameters\n", "according to different policies.\n", "Here, we only set `weight_decay` for\n", "the weights (the `net.weight` parameters), hence the \n", "bias (the `net.bias` parameter) will not decay.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "05a15008", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:57.194017Z", "iopub.status.busy": "2023-08-18T19:45:57.193045Z", "iopub.status.idle": "2023-08-18T19:45:57.200339Z", "shell.execute_reply": "2023-08-18T19:45:57.199168Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [], "source": ["class WeightDecay(d2l.LinearRegression):\n", "    def __init__(self, wd, lr):\n", "        super().__init__(lr)\n", "        self.save_hyperparameters()\n", "        self.wd = wd\n", "\n", "    def configure_optimizers(self):\n", "        return torch.optim.SGD([\n", "            {'params': self.net.weight, 'weight_decay': self.wd},\n", "            {'params': self.net.bias}], lr=self.lr)"]}, {"cell_type": "markdown", "id": "0bc680da", "metadata": {"origin_pos": 27}, "source": ["[**The plot looks similar to that when\n", "we implemented weight decay from scratch**].\n", "However, this version runs faster\n", "and is easier to implement,\n", "benefits that will become more\n", "pronounced as you address larger problems\n", "and this work becomes more routine.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "f9557057", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:57.205597Z", "iopub.status.busy": "2023-08-18T19:45:57.204597Z", "iopub.status.idle": "2023-08-18T19:46:08.781099Z", "shell.execute_reply": "2023-08-18T19:46:08.780176Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["L2 norm of w: 0.013779522851109505\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"246.5625pt\" height=\"183.35625pt\" viewBox=\"0 0 246.5625 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:46:08.644610</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 246.5625 183.35625 \n", "L 246.5625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 37.7 145.8 \n", "L 233 145.8 \n", "L 233 7.2 \n", "L 37.7 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m9b8de61b57\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9b8de61b57\" x=\"37.7\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(34.51875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m9b8de61b57\" x=\"76.76\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(73.57875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m9b8de61b57\" x=\"115.82\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(112.63875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m9b8de61b57\" x=\"154.88\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(151.69875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m9b8de61b57\" x=\"193.94\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(190.75875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m9b8de61b57\" x=\"233\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(226.6375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(120.121875 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m3657ba27ed\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3657ba27ed\" x=\"37.7\" y=\"111.468413\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{10^{-3}}$ -->\n", "      <g transform=\"translate(7.2 115.267632) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(186.855469 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m3657ba27ed\" x=\"37.7\" y=\"70.598675\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- $\\mathdefault{10^{-2}}$ -->\n", "      <g transform=\"translate(7.2 74.397894) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(186.855469 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m3657ba27ed\" x=\"37.7\" y=\"29.728938\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- $\\mathdefault{10^{-1}}$ -->\n", "      <g transform=\"translate(7.2 33.528156) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.855469 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"mee1636e5c4\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"140.035134\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"132.83833\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"127.732117\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"123.77143\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"120.535313\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"117.799216\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"115.4291\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"113.33851\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"99.165396\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"91.968593\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"86.862379\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"82.901692\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"79.665576\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"76.929478\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"74.559362\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"72.468772\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"58.295658\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"51.098855\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"45.992641\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"42.031955\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"38.795838\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"36.05974\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"33.689625\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"31.599034\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"17.425921\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_29\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#mee1636e5c4\" x=\"37.7\" y=\"10.229117\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 40.14125 20.38018 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 57.23 15.304868 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 57.23 15.304868 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 57.23 15.304868 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "L 157.32125 120.463791 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "L 157.32125 120.463791 \n", "L 167.08625 116.790906 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "L 157.32125 120.463791 \n", "L 167.08625 116.790906 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "L 174.41 35.802846 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "L 157.32125 120.463791 \n", "L 167.08625 116.790906 \n", "L 176.85125 125.009284 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "L 174.41 35.802846 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "L 157.32125 120.463791 \n", "L 167.08625 116.790906 \n", "L 176.85125 125.009284 \n", "L 186.61625 110.328803 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "L 174.41 35.802846 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "L 157.32125 120.463791 \n", "L 167.08625 116.790906 \n", "L 176.85125 125.009284 \n", "L 186.61625 110.328803 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "L 174.41 35.802846 \n", "L 193.94 38.571138 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "L 157.32125 120.463791 \n", "L 167.08625 116.790906 \n", "L 176.85125 125.009284 \n", "L 186.61625 110.328803 \n", "L 196.38125 121.239401 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "L 174.41 35.802846 \n", "L 193.94 38.571138 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "L 157.32125 120.463791 \n", "L 167.08625 116.790906 \n", "L 176.85125 125.009284 \n", "L 186.61625 110.328803 \n", "L 196.38125 121.239401 \n", "L 206.14625 118.730655 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "L 174.41 35.802846 \n", "L 193.94 38.571138 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "L 157.32125 120.463791 \n", "L 167.08625 116.790906 \n", "L 176.85125 125.009284 \n", "L 186.61625 110.328803 \n", "L 196.38125 121.239401 \n", "L 206.14625 118.730655 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "L 174.41 35.802846 \n", "L 193.94 38.571138 \n", "L 213.47 41.036512 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "L 157.32125 120.463791 \n", "L 167.08625 116.790906 \n", "L 176.85125 125.009284 \n", "L 186.61625 110.328803 \n", "L 196.38125 121.239401 \n", "L 206.14625 118.730655 \n", "L 215.91125 117.230306 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "L 174.41 35.802846 \n", "L 193.94 38.571138 \n", "L 213.47 41.036512 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "L 157.32125 120.463791 \n", "L 167.08625 116.790906 \n", "L 176.85125 125.009284 \n", "L 186.61625 110.328803 \n", "L 196.38125 121.239401 \n", "L 206.14625 118.730655 \n", "L 215.91125 117.230306 \n", "L 225.67625 119.332287 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "L 174.41 35.802846 \n", "L 193.94 38.571138 \n", "L 213.47 41.036512 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 40.14125 20.38018 \n", "L 49.90625 13.5 \n", "L 59.67125 69.827476 \n", "L 69.43625 67.326057 \n", "L 79.20125 106.758073 \n", "L 88.96625 103.25024 \n", "L 98.73125 109.142402 \n", "L 108.49625 128.90553 \n", "L 118.26125 117.357531 \n", "L 128.02625 110.122092 \n", "L 137.79125 139.5 \n", "L 147.55625 103.233962 \n", "L 157.32125 120.463791 \n", "L 167.08625 116.790906 \n", "L 176.85125 125.009284 \n", "L 186.61625 110.328803 \n", "L 196.38125 121.239401 \n", "L 206.14625 118.730655 \n", "L 215.91125 117.230306 \n", "L 225.67625 119.332287 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 57.23 15.304868 \n", "L 76.76 19.137755 \n", "L 96.29 22.828058 \n", "L 115.82 26.341884 \n", "L 135.35 29.658651 \n", "L 154.88 32.824469 \n", "L 174.41 35.802846 \n", "L 193.94 38.571138 \n", "L 213.47 41.036512 \n", "L 233 43.315623 \n", "\" clip-path=\"url(#p513240ecfe)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 37.7 145.8 \n", "L 37.7 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 233 145.8 \n", "L 233 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 37.7 145.8 \n", "L 233 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 37.7 7.2 \n", "L 233 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 146.409375 92.95625 \n", "L 226 92.95625 \n", "Q 228 92.95625 228 90.95625 \n", "L 228 62.04375 \n", "Q 228 60.04375 226 60.04375 \n", "L 146.409375 60.04375 \n", "Q 144.409375 60.04375 144.409375 62.04375 \n", "L 144.409375 90.95625 \n", "Q 144.409375 92.95625 146.409375 92.95625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_94\">\n", "     <path d=\"M 148.409375 68.142188 \n", "L 158.409375 68.142188 \n", "L 168.409375 68.142188 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(176.409375 71.642188) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_95\">\n", "     <path d=\"M 148.409375 83.098438 \n", "L 158.409375 83.098438 \n", "L 168.409375 83.098438 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(176.409375 86.598438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p513240ecfe\">\n", "   <rect x=\"37.7\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = WeightDecay(wd=3, lr=0.01)\n", "model.board.yscale='log'\n", "trainer.fit(model, data)\n", "\n", "print('L2 norm of w:', float(l2_penalty(model.get_w_b()[0])))"]}, {"cell_type": "markdown", "id": "e6b6d3ae", "metadata": {"origin_pos": 29}, "source": ["So far, we have touched upon one notion of\n", "what constitutes a simple linear function.\n", "However, even for simple nonlinear functions, the situation can be much more complex. To see this, the concept of [reproducing kernel Hilbert space (RKHS)](https://en.wikipedia.org/wiki/Reproducing_kernel_Hilbert_space)\n", "allows one to apply tools introduced\n", "for linear functions in a nonlinear context.\n", "Unfortunately, RKHS-based algorithms\n", "tend to scale poorly to large, high-dimensional data.\n", "In this book we will often adopt the common heuristic\n", "whereby weight decay is applied\n", "to all layers of a deep network.\n", "\n", "## Summary\n", "\n", "Regularization is a common method for dealing with overfitting. Classical regularization techniques add a penalty term to the loss function (when training) to reduce the complexity of the learned model.\n", "One particular choice for keeping the model simple is using an $\\ell_2$ penalty. This leads to weight decay in the update steps of the minibatch stochastic gradient descent algorithm.\n", "In practice, the weight decay functionality is provided in optimizers from deep learning frameworks.\n", "Different sets of parameters can have different update behaviors within the same training loop.\n", "\n", "\n", "\n", "## Exercises\n", "\n", "1. Experiment with the value of $\\lambda$ in the estimation problem in this section. Plot training and validation accuracy as a function of $\\lambda$. What do you observe?\n", "1. Use a validation set to find the optimal value of $\\lambda$. Is it really the optimal value? Does this matter?\n", "1. What would the update equations look like if instead of $\\|\\mathbf{w}\\|^2$ we used $\\sum_i |w_i|$ as our penalty of choice ($\\ell_1$ regularization)?\n", "1. We know that $\\|\\mathbf{w}\\|^2 = \\mathbf{w}^\\top \\mathbf{w}$. Can you find a similar equation for matrices (see the Frobenius norm in :numref:`subsec_lin-algebra-norms`)?\n", "1. Review the relationship between training error and generalization error. In addition to weight decay, increased training, and the use of a model of suitable complexity, what other ways might help us deal with overfitting?\n", "1. In Bayesian statistics we use the product of prior and likelihood to arrive at a posterior via $P(w \\mid x) \\propto P(x \\mid w) P(w)$. How can you identify $P(w)$ with regularization?\n"]}, {"cell_type": "markdown", "id": "60c2e223", "metadata": {"origin_pos": 31, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/99)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "0a968153", "metadata": {"origin_pos": 0}, "source": ["# Linear Neural Networks for Regression\n", ":label:`chap_regression`\n", "\n", "Before we worry about making our neural networks deep,\n", "it will be helpful to implement some shallow ones,\n", "for which the inputs connect directly to the outputs.\n", "This will prove important for a few reasons.\n", "First, rather than getting distracted by complicated architectures,\n", "we can focus on the basics of neural network training,\n", "including parametrizing the output layer, handling data,\n", "specifying a loss function, and training the model.\n", "Second, this class of shallow networks happens\n", "to comprise the set of linear models,\n", "which subsumes many classical methods of statistical prediction,\n", "including linear and softmax regression.\n", "Understanding these classical tools is pivotal\n", "because they are widely used in many contexts\n", "and we will often need to use them as baselines\n", "when justifying the use of fancier architectures.\n", "This chapter will focus narrowly on linear regression\n", "and the next one will extend our modeling repertoire\n", "by developing linear neural networks for classification.\n", "\n", ":begin_tab:toc\n", " - [linear-regression](linear-regression.ipynb)\n", " - [oo-design](oo-design.ipynb)\n", " - [synthetic-regression-data](synthetic-regression-data.ipynb)\n", " - [linear-regression-scratch](linear-regression-scratch.ipynb)\n", " - [linear-regression-concise](linear-regression-concise.ipynb)\n", " - [generalization](generalization.ipynb)\n", " - [weight-decay](weight-decay.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
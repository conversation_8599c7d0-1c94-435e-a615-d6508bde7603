{"cells": [{"cell_type": "markdown", "id": "558852bf", "metadata": {"origin_pos": 0}, "source": ["# Naive <PERSON>\n", ":label:`sec_naive_bayes`\n", "\n", "Throughout the previous sections, we learned about the theory of probability and random variables.  To put this theory to work, let's introduce the *naive Bayes* classifier.  This uses nothing but probabilistic fundamentals to allow us to perform classification of digits.\n", "\n", "Learning is all about making assumptions. If we want to classify a new data example that we have never seen before we have to make some assumptions about which data examples are similar to each other. The naive <PERSON>es classifier, a popular and remarkably clear algorithm, assumes all features are independent from each other to simplify the computation. In this section, we will apply this model to recognize characters in images.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c6444050", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:11.189874Z", "iopub.status.busy": "2023-08-18T20:14:11.189459Z", "iopub.status.idle": "2023-08-18T20:14:14.273916Z", "shell.execute_reply": "2023-08-18T20:14:14.272653Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import math\n", "import torch\n", "import torchvision\n", "from d2l import torch as d2l\n", "\n", "d2l.use_svg_display()"]}, {"cell_type": "markdown", "id": "e484af4c", "metadata": {"origin_pos": 4}, "source": ["## Optical Character Recognition\n", "\n", "MNIST :cite:`LeCun.Bottou.Bengio.ea.1998` is one of widely used datasets. It contains 60,000 images for training and 10,000 images for validation. Each image contains a handwritten digit from 0 to 9. The task is classifying each image into the corresponding digit.\n", "\n", "Gluon provides a `MNIST` class in the `data.vision` module to\n", "automatically retrieve the dataset from the Internet.\n", "Subsequently, Gluon will use the already-downloaded local copy.\n", "We specify whether we are requesting the training set or the test set\n", "by setting the value of the parameter `train` to `True` or `False`, respectively.\n", "Each image is a grayscale image with both width and height of $28$ with shape ($28$,$28$,$1$). We use a customized transformation to remove the last channel dimension. In addition, the dataset represents each pixel by an unsigned $8$-bit integer.  We quantize them into binary features to simplify the problem.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "032f03cd", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:14.278062Z", "iopub.status.busy": "2023-08-18T20:14:14.277624Z", "iopub.status.idle": "2023-08-18T20:14:15.371537Z", "shell.execute_reply": "2023-08-18T20:14:15.370525Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading http://yann.lecun.com/exdb/mnist/train-images-idx3-ubyte.gz\n", "Downloading http://yann.lecun.com/exdb/mnist/train-images-idx3-ubyte.gz to ./temp/MNIST/raw/train-images-idx3-ubyte.gz\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\r", "  0%|          | 0/9912422 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\r", "100%|██████████| 9912422/9912422 [00:00<00:00, 115752065.81it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting ./temp/MNIST/raw/train-images-idx3-ubyte.gz to ./temp/MNIST/raw\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Downloading http://yann.lecun.com/exdb/mnist/train-labels-idx1-ubyte.gz\n", "Downloading http://yann.lecun.com/exdb/mnist/train-labels-idx1-ubyte.gz to ./temp/MNIST/raw/train-labels-idx1-ubyte.gz\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\r", "  0%|          | 0/28881 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\r", "100%|██████████| 28881/28881 [00:00<00:00, 5234904.66it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting ./temp/MNIST/raw/train-labels-idx1-ubyte.gz to ./temp/MNIST/raw\n", "\n", "Downloading http://yann.lecun.com/exdb/mnist/t10k-images-idx3-ubyte.gz\n", "Downloading http://yann.lecun.com/exdb/mnist/t10k-images-idx3-ubyte.gz to ./temp/MNIST/raw/t10k-images-idx3-ubyte.gz\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\r", "  0%|          | 0/1648877 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\r", "100%|██████████| 1648877/1648877 [00:00<00:00, 43715298.68it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting ./temp/MNIST/raw/t10k-images-idx3-ubyte.gz to ./temp/MNIST/raw\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Downloading http://yann.lecun.com/exdb/mnist/t10k-labels-idx1-ubyte.gz\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloading http://yann.lecun.com/exdb/mnist/t10k-labels-idx1-ubyte.gz to ./temp/MNIST/raw/t10k-labels-idx1-ubyte.gz\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\r", "  0%|          | 0/4542 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\r", "100%|██████████| 4542/4542 [00:00<00:00, 21501725.47it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting ./temp/MNIST/raw/t10k-labels-idx1-ubyte.gz to ./temp/MNIST/raw\n", "\n"]}], "source": ["data_transform = torchvision.transforms.Compose([\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    lambda x: torch.floor(x * 255 / 128).squeeze(dim=0)\n", "])\n", "\n", "mnist_train = torchvision.datasets.MNIST(\n", "    root='./temp', train=True, transform=data_transform, download=True)\n", "mnist_test = torchvision.datasets.MNIST(\n", "    root='./temp', train=False, transform=data_transform, download=True)"]}, {"cell_type": "markdown", "id": "5d720912", "metadata": {"origin_pos": 8}, "source": ["We can access a particular example, which contains the image and the corresponding label.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "5ff4cf09", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:15.375165Z", "iopub.status.busy": "2023-08-18T20:14:15.374880Z", "iopub.status.idle": "2023-08-18T20:14:15.382550Z", "shell.execute_reply": "2023-08-18T20:14:15.381767Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(<PERSON><PERSON><PERSON><PERSON>([28, 28]), 4)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["image, label = mnist_train[2]\n", "image.shape, label"]}, {"cell_type": "markdown", "id": "fe837626", "metadata": {"origin_pos": 12}, "source": ["Our example, stored here in the variable `image`, corresponds to an image with a height and width of $28$ pixels.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "0d35b3b7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:15.386872Z", "iopub.status.busy": "2023-08-18T20:14:15.386295Z", "iopub.status.idle": "2023-08-18T20:14:15.391562Z", "shell.execute_reply": "2023-08-18T20:14:15.390744Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(torch.<PERSON><PERSON>([28, 28]), torch.float32)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["image.shape, image.dtype"]}, {"cell_type": "markdown", "id": "2b1b613c", "metadata": {"origin_pos": 14}, "source": ["Our code stores the label of each image as a scalar. Its type is a $32$-bit integer.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "47bdbe66", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:15.396205Z", "iopub.status.busy": "2023-08-18T20:14:15.395656Z", "iopub.status.idle": "2023-08-18T20:14:15.400704Z", "shell.execute_reply": "2023-08-18T20:14:15.399890Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(4, int)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["label, type(label)"]}, {"cell_type": "markdown", "id": "669a9356", "metadata": {"origin_pos": 18}, "source": ["We can also access multiple examples at the same time.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "82c26c39", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:15.405695Z", "iopub.status.busy": "2023-08-18T20:14:15.404827Z", "iopub.status.idle": "2023-08-18T20:14:15.422934Z", "shell.execute_reply": "2023-08-18T20:14:15.422116Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(torch.<PERSON><PERSON>([28, 28, 28]), torch.<PERSON><PERSON>([28]))"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["images = torch.stack([mnist_train[i][0] for i in range(10, 38)], dim=0)\n", "labels = torch.tensor([mnist_train[i][1] for i in range(10, 38)])\n", "images.shape, labels.shape"]}, {"cell_type": "markdown", "id": "6670bdea", "metadata": {"origin_pos": 22}, "source": ["Let's visualize these examples.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "b9d9e537", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:15.426373Z", "iopub.status.busy": "2023-08-18T20:14:15.425827Z", "iopub.status.idle": "2023-08-18T20:14:16.153047Z", "shell.execute_reply": "2023-08-18T20:14:16.152119Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"767.7pt\" height=\"176.186038pt\" viewBox=\"0 0 767.7 176.186038\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T20:14:16.064598</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 176.186038 \n", "L 767.7 176.186038 \n", "L 767.7 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 7.2 78.266038 \n", "L 78.266038 78.266038 \n", "L 78.266038 7.2 \n", "L 7.2 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p659120f5a4)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABs0lEQVR4nO3c4UnDUBhAUStO4RQOIXQBl3ULp3AN/WkoEprSvJyk9/yXFC7f915t9fR++vh5CuF56xeQP8WAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIC9bv4A5n99fN/3c+fXtrq9jlCYDUgxIMSBDzozp7r/c57eeC9c+779nqpoMSDEgw6+2a6ylJc+UV1aTASkGpBiQVc6MJefCPXb4kufJ194mA1IMyCprau5d9hprYcS7+hGaDEgxIMWADPl1iHR9lDUZkGJATo/wZ2TXXnW3XqdNBqQYkGJAigEpBqQYkGJAigEpBqQYEPrvM6b2+undEk0GpBiQYkDoM+MRzompJgNSDAi9pkbY+tO9qSYDUgxIMSD0mTG3z4947W0yIMWAHOJLbPdaWVtfc5sMSDEgxYDQV9s5RzknppoMSDEgu1lTR3zHfanJgBQDUgzIbs6MtUj/pa3JgBQDQq+pEdfZrVfTVJMBKQakGBD6zFiDdEZcajIgxYDQa+rW703Jq2hOkwEpBqQYEPrMmLPXc2FOkwEpBqQYkGJAigEpBqQYkGJAigEpBqQYkGJAigH5BZW8RK5xPK9xAAAAAElFTkSuQmCC\" id=\"imagebf2139d95a\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"7.2\" y=\"-6.986038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 7.2 78.266038 \n", "L 7.2 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 78.266038 78.266038 \n", "L 78.266038 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 7.2 78.266038 \n", "L 78.266038 78.266038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 7.2 7.2 \n", "L 78.266038 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 92.479245 78.266038 \n", "L 163.545283 78.266038 \n", "L 163.545283 7.2 \n", "L 92.479245 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p0888f54591)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABlklEQVR4nO3b4UnDUBhAUSNO4RQOIXQBl3ULp3AN/WkoGNqavN7Ec/4KqXD5vheedXqd3r4eSHi89y/ADzFCxAgRI0SMEDFCxAgRI0SMEDFCxAgRI0SMEDFCxAgRI0SMEDFCxAgRI0SMEDFCxAgRI+Rp9Ae+f36s8pzT88sqzykxGSFihAxZU2utpt+eeZSVZTJCxAgRI2STM2PpjLh1vy898/xnez1DTEaIGCHTiH8jG722rCn+TIwQMUKGXIesscO3uFKpMRkhYoQM/+PSNS5dTXt9lT1nMkLECBEjJH1mLDnKOTFnMkLECBEjZLdnhm+HsCkxQtJrar5+3NoylBghYoSkz4y5pdfXa86T8muwyQgRI2TIl9hGu/U1+N4rzGSEiBEiRsghz4xzW1+lrHXWmIwQMUL+xZq61Iib4aWVZjJCxAgRI2Q3t7YjXPOKusX5YjJCxAixpm60xQ2vyQgRI0SMEDFCxAgRI0SMEDFCxAgRI0SMEDFCxAgRI0SMEDFCxAgRI0SMEDFCxAgRI0SMEDFCvgEFcDxhhcQ72wAAAABJRU5ErkJggg==\" id=\"image7f718973e9\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"92.479245\" y=\"-6.986038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 92.479245 78.266038 \n", "L 92.479245 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 163.545283 78.266038 \n", "L 163.545283 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 92.479245 78.266038 \n", "L 163.545283 78.266038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 92.479245 7.2 \n", "L 163.545283 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 177.758491 78.266038 \n", "L 248.824528 78.266038 \n", "L 248.824528 7.2 \n", "L 177.758491 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p18587be927)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABvUlEQVR4nO3c0U0CQRRAUTFWYRUWYWIDNmsXVmEb+ulK4oZFduYg9/wZo5LcvJlhBA7Ph9fPuxDuZz+AfCsGpBiQYkCKASkGpBiQYkCKASkG5GH2A1h6+3j/8fXL49Ov39ti+XtkTQakGJDD6Fvbvyw3e5u9nDUZkGJAigGhjrazrR2tR2gyIMWADFmmTj3O7rUsnHucXv7ciCWryYAUA1IMCHUdMmJdvtR1zB6PtcmAFAMy/Bn47JvR478v3SI3GZBiQIoBKQakGJBiQPrn0pn2uNFtMiDFgBQDcnN7Rre2OUkxIP9ymZJuYrdoMiDFgBQDQu8Z0trfi9huTDEgxYBQe8aMPWL2q1WWmgxIMSDUMrXXC8ykpWhNkwEpBqQYEGrPOHapT9W5Fk0GpBiQ4W8ju5Rzly35mNtkQIoBKQbkaveMNVv2E2kPaTIgxYDQz8BHGP3JOWuaDEgxIMWAbNoz/uPN6ex9YqnJgBQDcnNHW2lZOtZkQIoBKQZk055x6no7+wgs7wtrmgxIMSC7HG2vdZmYrcmAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAfAE+j0yNBmtKkAAAAABJRU5ErkJggg==\" id=\"imagec1b4837903\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"177.758491\" y=\"-6.986038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 177.758491 78.266038 \n", "L 177.758491 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 248.824528 78.266038 \n", "L 248.824528 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 177.758491 78.266038 \n", "L 248.824528 78.266038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 177.758491 7.2 \n", "L 248.824528 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 263.037736 78.266038 \n", "L 334.103774 78.266038 \n", "L 334.103774 7.2 \n", "L 263.037736 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p6479833d72)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABvElEQVR4nO3cwU3DQBRAQYKogiooAokGaJYuqII24Ijlg4Vjxzsxb25crEhPf3e1ibm8Xt6/H0J4HP0B8qsYkGJAigEpBqQYkGJAigEpBqQYkGJAigEpBqQYkGJAigEpBqQYkGJAigF5Gv0BPr4+Nz/j7fll8zMETQakGJBiQA7fM/bYI7Y8U95fmgxIMSDDj7ZTS0vILZY3TZMBKQakGJBD9gxpvZ9/Fumo22RAigGhjrbXWrPUzJep6d+jl6wmA1IMSDEgl6NfPV465u51HbJlD9njmddqMiDFgNzNMrXmOUtGLIV/1WRAigEpBuTwPWNuj6Plllvha5/bnnFyxYAUA3LKK/Q1e4j0LWSTASkG5BTL1NwtjsHd2v4zxYAUA3LKPWPJmmPw0b8WaTIgxYAMv7WdGv1N22hNBqQYkGJAigEpBqQYEOpoOyf9DvYITQakGJBiQOg9Y+o/XJU0GZBiQO5mmZo747LVZECKASkGpBiQYkCKAbnbo+3UrV4jO1qTASkGpBiQU+wZc/I7GEuaDEgxIKd8JeBe/6d6kwEpBqQYkFPuGUtGH1+XNBmQYkCKASkGpBiQYkB+ABOPZMGsNy38AAAAAElFTkSuQmCC\" id=\"image00b3a545b6\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"263.037736\" y=\"-6.986038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 263.037736 78.266038 \n", "L 263.037736 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 334.103774 78.266038 \n", "L 334.103774 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 263.037736 78.266038 \n", "L 334.103774 78.266038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 263.037736 7.2 \n", "L 334.103774 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 348.316981 78.266038 \n", "L 419.383019 78.266038 \n", "L 419.383019 7.2 \n", "L 348.316981 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pc505bee36d)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABQElEQVR4nO3csQkCMRiAUU+cwikcQnABl3ULp3ANLcUr0ijcF3mvO64JfPwhabKcl+tzR8J+6wXwJkaIGCFihIgRIkaIGCFihIgRIkbIYesF/MLtcf/4vhxPm6zjWyYjRIyQabep9dY0+jfLtmUyQsQIESNEjBAxQsQImfZoOzLLUXbNZISIESJGiBghYoSIESJGiBghYoSIESJGiBghYoSIESJGiBghYoSIESJGiBghYoSIESJGiBghYoSIESJGiBghYoSIESJGiBghYoSIESJGiBghYoSIESJGiBghYoSIESJGiBghYoSIESJGiBghYoRM8xLb6FHhWV9eWzMZIWKEiBEiRogYIWKEiBEiRogYIdPcwP/llj1iMkLECBEjRIwQMULECBEjRIwQMULECBEjRIwQMULECBEjRIwQMUJeRU0O+dfELlEAAAAASUVORK5CYII=\" id=\"imagee182b45138\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"348.316981\" y=\"-6.986038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 348.316981 78.266038 \n", "L 348.316981 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 419.383019 78.266038 \n", "L 419.383019 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 348.316981 78.266038 \n", "L 419.383019 78.266038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 348.316981 7.2 \n", "L 419.383019 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 433.596226 78.266038 \n", "L 504.662264 78.266038 \n", "L 504.662264 7.2 \n", "L 433.596226 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p14878a8b23)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABr0lEQVR4nO3by03DQBRAUQdRBVVQBBIN0CxdUAVtwA4sLwwBx3Oc3LNCYmPp6s2MPzk9nV4+phDuRl9AvhUDUgzI/egLeH1/+/r7+eFx2HUImgxIMSC7L1PzZemn/93astVkQIoBKQbkNPpxyNoeMncL+0eTASkGpBiQYkCKASkGZPjRdu63x9xpus6jbpMBKQaEWqaWbu3uvMmAFANSDEgxIMWAFANCH23XXOPdepMBKQakGJBiQIoBKQZk+E8C9nCUnx00GZBiQIoBOeyeMV/7z3k0ImsyIMWAHPap7ZqjPtFtMiDFgBQDssuesbaG77FmH+VjuCYDUgxIMSAX2TP++nhC2j+maf89pMmAFAMy/Gi75hLLhHQtS00GpBiQYkDoN31H+apjK00GpBiQ3d/0XeLjgf8sYdJRt8mAFANSDMjuR9vlWrvFHtJHbNlcMSDD78C3OCK2TGVzxYAUAzJ8z9jCOfuO9PhjqcmAFANyFcvUOeSXVE0GpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKAfkEUYJVv0Jq0uEAAAAASUVORK5CYII=\" id=\"imageb83fead2a2\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"433.596226\" y=\"-6.986038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 433.596226 78.266038 \n", "L 433.596226 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 504.662264 78.266038 \n", "L 504.662264 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 433.596226 78.266038 \n", "L 504.662264 78.266038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 433.596226 7.2 \n", "L 504.662264 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 518.875472 78.266038 \n", "L 589.941509 78.266038 \n", "L 589.941509 7.2 \n", "L 518.875472 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd9e2e30acf)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABr0lEQVR4nO3cy03DQBRAUYKogiooAokGaJYuqII2YJdYXlhR8HiOpXu2EQnS1XvjfC/vl8/fpxCeZ/8DuSkGpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKAXk54kG+fr53v8+P17fd73O2JgNSDMiQNbXXWlquovV9bj3GWVdYkwEpBqQYkEMubZce3efrv9s6M9a3neUMaTIgxYAcvqb2snXZu7a8XV5ZTQakGJBiQC4jvp8x4uWQUY8vnSFNBqQYkCFram3Em0sjzF5ZTQakGJBiQA45Mx4lnzUjzpcmA1IMCL2mRpi9+rbWW5MBKQakGJDTvtM3Qi+H5KoYkGJAOjMWZn/4rcmAFANSDEgxIMWAFANSDEgxIMWA9Ax8oVdtc1UMSDEg08+Ms3zf7ghNBqQYkOlramnUmzuzP7h2ryYDUgxIMSDUmbF2xGWvdDndZECKAZm+pu79dZyzXJ7+R5MBKQakGJDpZ8bS1mXm7F/qOUKTASkGhFpTW+T1spcmA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA/IHwSFJh1AkXvsAAAAASUVORK5CYII=\" id=\"image0d33a6f1d2\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"518.875472\" y=\"-6.986038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 518.875472 78.266038 \n", "L 518.875472 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 589.941509 78.266038 \n", "L 589.941509 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 518.875472 78.266038 \n", "L 589.941509 78.266038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 518.875472 7.2 \n", "L 589.941509 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 604.154717 78.266038 \n", "L 675.220755 78.266038 \n", "L 675.220755 7.2 \n", "L 604.154717 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p9929738859)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABvElEQVR4nO3cwU3DQBRAQYKogiooAokGaJYuqII24IhlCROH4J3Yb65BxNLT3107gdPz6fXzLoT70ReQb8WAFANSDEgxIMWAFANSDEgxIMWAPIy+gLeP9x9fe3l82uw6BE0GpBiQzZeppWVpzc/ucQlrMiDFgBQDssmesWafOLImA1IMyPA78Kml4+p8qdvjsbfJgBQDUgwItWcsme8DezwuNxmQYkA2WaamS8wel5draTIgxYAUA3Ia/fcZ13is8ds+dCuPR5oMSDEgw+/Al469RzsGNxmQYkCKASkGpBiQYkCGH22njvAB0pImA1IMSDEgw5/anuta+4f8BLfJgBQDUgwIdZ8x9x/3GfPfKe0hTQakGBB6mTrXXh6jNBmQYkCKAdnFnjG3Zg+Zvjb6mNtkQIoBoZ7arjmSXrqkbPEel2oyIMWAFANys3vGkjVr/bnvucX+0WRAigGh7sDX/FedJT21zZ8VA1IMCLVnyLb4IkOTASkG5GaWqREf/Gx9RG4yIMWAFANSDEgxIMWA3MzRdoS+kHBgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgX3iuV5SwXYC2AAAAAElFTkSuQmCC\" id=\"image57260c0422\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"604.154717\" y=\"-6.986038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 604.154717 78.266038 \n", "L 604.154717 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 675.220755 78.266038 \n", "L 675.220755 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 604.154717 78.266038 \n", "L 675.220755 78.266038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 604.154717 7.2 \n", "L 675.220755 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 689.433962 78.266038 \n", "L 760.5 78.266038 \n", "L 760.5 7.2 \n", "L 689.433962 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p341f54120b)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABrklEQVR4nO3c203DQBBAUYKogiooAokGaJYuqII24A8sfzgvJ3ts7inAinQ1s060yuH18P79EMLj6A+QP8WAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIE+jP8CpPr4+V3nO2/PLKs+5hSYDUgwIvaamq+ma9TJ9znzdSWuryYAUA1IMyEG6xHaPfb70ijz6/GgyIMWA0K+2tzBfRdJrb5MBKQakGJB/d2bMTc+FtX4ZvlSTASkGpBiQYkCKASkGpBiQYkCKAdnFN/Brfm0d/a17qsmAFANSDMguzoy1dCEhv4oBKQaEPjNOvfi8dOPjmNHnxFSTASkGhFpT16ybpedsRZMBKQakGBDqzFgy+lLyPTQZkGJA6DUl3YO9hyYDUgxIMSD0mbFkrb+ykDQZkGJAigEpBqQYkGJAqH/VOYf87ziXajIgxYBsdk1NHftFdytrq8mAFANSDMguzoy5rb72NhmQYkB2uabmTr3MMHqFNRmQYkCKAdnshYRzbOUyXJMBKQbkX6ypqdGvr0uaDEgxIMWAFANSDEgxIMWAFANSDEgxID9nsk2N7T4qhwAAAABJRU5ErkJggg==\" id=\"image2913d3d44d\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"689.433962\" y=\"-6.986038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 689.433962 78.266038 \n", "L 689.433962 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 760.5 78.266038 \n", "L 760.5 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_45\">\n", "    <path d=\"M 689.433962 78.266038 \n", "L 760.5 78.266038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_46\">\n", "    <path d=\"M 689.433962 7.2 \n", "L 760.5 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_10\">\n", "   <g id=\"patch_47\">\n", "    <path d=\"M 7.2 168.986038 \n", "L 78.266038 168.986038 \n", "L 78.266038 97.92 \n", "L 7.2 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p30d8075c0a)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABpUlEQVR4nO3b3UkDQRhAUSNWYRUWIdiAzdqFVdiGPmYIuGZVds9k73kVTODyzcz+5PR8ev28C+F+7y+Qs2JAigF52PoD3z7ev/3by+PTZt9D1GRAigE57X20bdk6azIgxYAUA7L50XaNcT85wv7RZECKAdl9mRqXn6Vj7hE0GZBiQIoB2f12yJKj3SppMiDFgOx+tP2tW7w6bzIgxYAUA0LvGUe7VdJkQIoBoa/Al9zi1XmTASkGpBiQafeM0U/H3ln2kCYDUgxIMSDFgBQDUgxIMSDFgBQDQj9c+i+zvLzQZECKASkGhN4zjvASwqjJgBQDUgxIMSDFgBQDQh9tl6y5rTEekeWX35oMSDEgxYBMu2essbQXLO0nW+8hTQakGJBpl6lZnt6t0WRAigEpBoTeM6796fGa32fITw+bDEgxIPQyNbo8vq5ZbuSladRkQIoBKQZkmj3j0rV3Yv/yf7bWZECKAZl2mVoiLT1rNBmQYkCKASkGpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKASkG5AsMTkpFs7CrPQAAAABJRU5ErkJggg==\" id=\"imagefe4fcc4450\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"7.2\" y=\"-97.706038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_48\">\n", "    <path d=\"M 7.2 168.986038 \n", "L 7.2 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_49\">\n", "    <path d=\"M 78.266038 168.986038 \n", "L 78.266038 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_50\">\n", "    <path d=\"M 7.2 168.986038 \n", "L 78.266038 168.986038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_51\">\n", "    <path d=\"M 7.2 97.92 \n", "L 78.266038 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_11\">\n", "   <g id=\"patch_52\">\n", "    <path d=\"M 92.479245 168.986038 \n", "L 163.545283 168.986038 \n", "L 163.545283 97.92 \n", "L 92.479245 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pe2ec73d30a)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABqElEQVR4nO3ay00DQRBAQYyIgigIAokESJYsiII04MjKEsbfmVrvqysHjJ66e228e929fz+E8Dj7BeRXMSDFgBQDUgxIMSDFgBQDUgxIMSDFgBQDUgzI0+wXIPv4+vzzZ2/PL1f/fU0GpBiQYkC6GQuHbsQITQakGJDNr6nZq2mpyYAUA1IMyOZuhnQj9jUZkGJANremznWLT2n3NRmQYkCKARlyM459nByxl2VNBqQYEOrRdvQXADRNBqQYkGJAqJtxyCWftq7l3jQZkGJAVrOmLiH/Q2mpyYAUA1IMyE1uxlp29H9GPxI3GZBiQIoBmf4+49y9fC93aanJgBQDcrU1NXptnLLe1rLSmgxIMSDFgEx/tB1heV/k+9FkQIoBGb6m1vLlgBmaDEgxIMWAnH0z5EfEtWoyIMWAnLSmzl1NPc4ep8mAFANSDMhNPg6Rb8T+a5Me0ZsMSDEgV1tT8mpaiyYDUgxIMSAn3Yx7vwuz/74mA1IMSDEgxYAUA1IMyCa+a3vI7MfZpSYDUgxIMSDFgBQDUgxIMSDFgBQDUgxIMSDFgBQD8gM5ujMXxW18/AAAAABJRU5ErkJggg==\" id=\"image7430201f7a\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"92.479245\" y=\"-97.706038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_53\">\n", "    <path d=\"M 92.479245 168.986038 \n", "L 92.479245 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_54\">\n", "    <path d=\"M 163.545283 168.986038 \n", "L 163.545283 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_55\">\n", "    <path d=\"M 92.479245 168.986038 \n", "L 163.545283 168.986038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_56\">\n", "    <path d=\"M 92.479245 97.92 \n", "L 163.545283 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_12\">\n", "   <g id=\"patch_57\">\n", "    <path d=\"M 177.758491 168.986038 \n", "L 248.824528 168.986038 \n", "L 248.824528 97.92 \n", "L 177.758491 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p6458f4442f)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAAB2ElEQVR4nO3c4U3rMBRAYYI6BVMwBBILsGy3YArWgJ9YFk1S99r3GJ/v9ytEOrp2nhO6vW0f309CeM6+AP0yBogxQIwBYgwQY4AYA8QYIMYAMQaIMUCMAWIMkEv2BZSuX5+n/+37y2u368jiZIAYA8QYIMP3jHv2haifM8v+4mSAGANkyDIVtTRF/H7ykuVkgBgDxBggXfaMqD1ib31v/R3150h7iJMBYgyQLepd29ZlY8QyQb62kpMBYgwQY4AMP7XNuJVsvUUefYziZIAYAwT1QkKGcvk5u2TVn4viZIAYA8QYIOkvJJBOTetrGf2E0skAMQZI2DJ19hZRtzkZIMYAMQZIl1vb7FvEWTkZIMYAMQZI+hH6LC8lj+BkgBgDZMgy1fo07dbPOEI+Gd7jZIAYA8QYIOkvsZ09KnlkH5jl9tnJADEGSPr/wFufEPpnZOrKGCDGAEnfM0qrPyF0MkCMAYJapmqrvRjnZIAYA8QYIOg9o9TjG3Ye/Ww0JwPEGCDTLFN7jk5eSUvRHicDxBggxgD5F3vGkYhjFb9VZzHGAFlimSrd8wDLL4xcmDFAjAGy3J5R8yU2/ckYIMYAMQaIMUCMAWIMEGOAGAPEGCDGADEGiDFAfgAz1myMdIfRhgAAAABJRU5ErkJggg==\" id=\"imagebe04470227\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"177.758491\" y=\"-97.706038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_58\">\n", "    <path d=\"M 177.758491 168.986038 \n", "L 177.758491 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_59\">\n", "    <path d=\"M 248.824528 168.986038 \n", "L 248.824528 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_60\">\n", "    <path d=\"M 177.758491 168.986038 \n", "L 248.824528 168.986038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_61\">\n", "    <path d=\"M 177.758491 97.92 \n", "L 248.824528 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_13\">\n", "   <g id=\"patch_62\">\n", "    <path d=\"M 263.037736 168.986038 \n", "L 334.103774 168.986038 \n", "L 334.103774 97.92 \n", "L 263.037736 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p22f3ab91b9)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABuUlEQVR4nO3by03EMBRAUQZRBVVQBBIN0CxdUAVtwJLgRSazGHyS3LNDYhHp6tnOZy6vl/fvhxAeZ19AfhUDUgxIMSDFgBQD8jT7ApY+vj7//P32/DLlOmZpMiDFgBQDQu0Zo+Uecob9o8mAFANCLVPjUjQedY+uyYAUA1IMCLVnrDnDo5ImA1IMCL1MLZeiMxxzmwxIMSDFgNB7xpojPtFtMiDFgFz2+q3t2lF3r8tWkwEpBqQYkGJAigEpBoS+A996fB3/b68vopoMSDEgxYBQe8Ytb/OO+OavyYAUA0ItU6OtR9JrS9ZeXkQ1GZBiQIoBofeMrW75XYf8qKTJgBQDUgwI9XXIvb742ProZPb+0WRAigGhjrb3+unxXn7n0WRAigEpBoTaM9b8x2OM2Y9KmgxIMSDUHfiaGXfnLVMnVgxIMSC7Odqe4VFJkwEpBmQ3y9ToluVl9kujrZoMSDEgxYDsds9Yunbs7cPn3KwYkEMsU6O1ZWv2XfaaJgNSDEgxIIfcM0bycXapyYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgP8qlYyNdxJ5CAAAAAElFTkSuQmCC\" id=\"image096363a2cb\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"263.037736\" y=\"-97.706038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_63\">\n", "    <path d=\"M 263.037736 168.986038 \n", "L 263.037736 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_64\">\n", "    <path d=\"M 334.103774 168.986038 \n", "L 334.103774 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_65\">\n", "    <path d=\"M 263.037736 168.986038 \n", "L 334.103774 168.986038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_66\">\n", "    <path d=\"M 263.037736 97.92 \n", "L 334.103774 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_14\">\n", "   <g id=\"patch_67\">\n", "    <path d=\"M 348.316981 168.986038 \n", "L 419.383019 168.986038 \n", "L 419.383019 97.92 \n", "L 348.316981 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pabfced9361)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABjElEQVR4nO3ay03DQBRAUYKogiooAokGaJYuqII2YEeskbBCIsVn7Hu22US6evOxfXo9vX8/hPC49R/IWTEgxYAUA1IMSDEgxYAUA1IMSDEgT1v/gY+vzz9/e3t+udv/EDQZkGJAigEpBqQYkGJAigEpBqQYkM1v4GuWt/Mj3MabDEgxIMWAnKSP2Nae4I72uIc0GZBiQIoBKQakGJBiQOjHIWv2+KikyYAUA0LdwEf/uZEvzbpsNRmQYkCKAaGPtsu1/9r9YyZNBqQYEHqZuta4pM1y1G0yIMWAFAMyzZ4xrvt7POo2GZBiQIoBmWbPuMUsbwWbDEgxIPSbvkvdcsyVlq0mA1IMSDEgu9gzRrN+VdJkQIoBKQakGJBiQIoB2eXRdjTLUbfJgBQDcoiXS7N8s9tkQIoBKQbkEEfbNZfuIfc45jYZkGJAigEpBqQYkGJADvE4ZI30qKTJgBQDcvhlaqnvpvKrGJBiQIoBKQakGJBiQIoBKQakGJBiQIoBKQakGJBiQIoBKQbkB+IxOY0LpAcaAAAAAElFTkSuQmCC\" id=\"image3dc322bce1\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"348.316981\" y=\"-97.706038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_68\">\n", "    <path d=\"M 348.316981 168.986038 \n", "L 348.316981 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_69\">\n", "    <path d=\"M 419.383019 168.986038 \n", "L 419.383019 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_70\">\n", "    <path d=\"M 348.316981 168.986038 \n", "L 419.383019 168.986038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_71\">\n", "    <path d=\"M 348.316981 97.92 \n", "L 419.383019 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_15\">\n", "   <g id=\"patch_72\">\n", "    <path d=\"M 433.596226 168.986038 \n", "L 504.662264 168.986038 \n", "L 504.662264 97.92 \n", "L 433.596226 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p57f2ab45a3)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABo0lEQVR4nO3c0UkDQRRAUSNWYRUWIdiAzdqFVdiGfhoWXDYhO3M23vMrksDlvRnX6On19P79EMLj7DeQX8WAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIE8jXuTj6/PPr709v4x4C4fQZECKARmypta0wn41GZBiQIoBmX5mrFk7T0YbcX41GZBiQE6zP2srraJL7LG2mgxIMSDFgEy/2o64Mp6fS5e83rXfd60mA1IMyPQ1tYdbXZdHPzVuMiDFgBQDUgxIMSDFgBQDUgxIMSDFgNzl45A1y0cl0gflmgxIMSDFgPy7M0M6I5aaDEgxINP/jGwreb3cSpMBKQakGJAhZ8bavt96nsiPMW6lyYAUAzL9J/Ct62a5po76dx1rmgxIMSDFgEw/M7Zani2dGdlVMSDFgBQDUgxIMSCHudounV917+Wa22RAigGZ/l919nDJ2pJ+SdVkQIoBKQakGJBiQIoBKQakGJBiQIoBOexT22tJjz+WmgxIMSB3uabkVbSmyYAUA1IMSDEgxYAUA1IMSDEgxYAUA/IDXLY3W2up93MAAAAASUVORK5CYII=\" id=\"image02da88ea55\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"433.596226\" y=\"-97.706038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_73\">\n", "    <path d=\"M 433.596226 168.986038 \n", "L 433.596226 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_74\">\n", "    <path d=\"M 504.662264 168.986038 \n", "L 504.662264 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_75\">\n", "    <path d=\"M 433.596226 168.986038 \n", "L 504.662264 168.986038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_76\">\n", "    <path d=\"M 433.596226 97.92 \n", "L 504.662264 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_16\">\n", "   <g id=\"patch_77\">\n", "    <path d=\"M 518.875472 168.986038 \n", "L 589.941509 168.986038 \n", "L 589.941509 97.92 \n", "L 518.875472 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p3e401c34d3)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABvUlEQVR4nO3cwU3DQBBAUYKogiooAokGaJYuqII24BgrBysJ8fpt8t8ZIaOv2bHskMP74fP3KYTnvS8gR8WAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIC97X8Car5/vs37u4/Vt0+sYpcmAFANSDMjwnXHuHtjqd8r7pcmAFAMy5Jja4mi61rXXMuJ4azIgxYAUA7LJzpB2xK2s/U232idNBqQYEPqp7bnjfy/HYpMBKQakGJDDiP/PkB9BLN1q91x73U0GpBiQIcfU0ixH1h6aDEgxIMWADN8Zp9ohR00GpBiQ3Y+ppUc/spoMSDEgxYBQO+PUFm/w5P3SZECKASkGhN4Za+5xnzQZkGJA6A+xrVkeKX2ILTdXDEgxINPe2q6Z9ba3yYAUAzLtre2a0yNlllvfJgNSDEgxIHd5a3sJ6RMpTQakGJBpvvxr7xc/IzQZkGJAigGZ5nHILI80/qPJgBQDMvyYmvWJai+XHkwxIMWA7H5ru3YW771PRj+CaTIgxYDsfkyt2eILI+Wnv00GpBiQYkDonXEueQ9cosmAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWA/AFh2lcwvwls8gAAAABJRU5ErkJggg==\" id=\"image9a6e71a047\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"518.875472\" y=\"-97.706038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_78\">\n", "    <path d=\"M 518.875472 168.986038 \n", "L 518.875472 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_79\">\n", "    <path d=\"M 589.941509 168.986038 \n", "L 589.941509 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_80\">\n", "    <path d=\"M 518.875472 168.986038 \n", "L 589.941509 168.986038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_81\">\n", "    <path d=\"M 518.875472 97.92 \n", "L 589.941509 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_17\">\n", "   <g id=\"patch_82\">\n", "    <path d=\"M 604.154717 168.986038 \n", "L 675.220755 168.986038 \n", "L 675.220755 97.92 \n", "L 604.154717 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p4eb178f23e)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABhElEQVR4nO3cwU3DQBRAQRtRBVVQBBIN0Gy6oAragCOWJZyIIO/YvLnmEEtPf3djJ5lf5rfPKYSH0ReQb8WAFANSDEgxIMWAPI6+gN+6fLz/+Nrr0/Nu1/GXmgxIMSDFgBQDUgxIMSDzGe7abh1zp+k4R90mA1IMSDEgxYAUA1IMSDEgxYAUA1IMyGGf9C2tb3dcuz2iajIgxYCcYpm6ZrlsyXdwmwxIMSDFgBQDUgxIMSCnPNoe9RN5kwEpBqQYkGJAigEpBuQwR9ujHE/v0WRAigEpBoTaM/bYF9bvIT35azIgxYAUAzJ8z7h1n7hnbd96D+mbI00GpBiQ4cvUlj2WjdFL01KTASkGpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKAaH+MHKPLwvI/6HeZECKASkGhHrSN3rNHq3JgBQDQi1Te5B/ltxkQIoBKQbk3+0Za9JxusmAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIF/DQjAzlnZktQAAAABJRU5ErkJggg==\" id=\"image6d6e4c9d02\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"604.154717\" y=\"-97.706038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_83\">\n", "    <path d=\"M 604.154717 168.986038 \n", "L 604.154717 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_84\">\n", "    <path d=\"M 675.220755 168.986038 \n", "L 675.220755 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_85\">\n", "    <path d=\"M 604.154717 168.986038 \n", "L 675.220755 168.986038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_86\">\n", "    <path d=\"M 604.154717 97.92 \n", "L 675.220755 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_18\">\n", "   <g id=\"patch_87\">\n", "    <path d=\"M 689.433962 168.986038 \n", "L 760.5 168.986038 \n", "L 760.5 97.92 \n", "L 689.433962 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pa9765d09c4)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABsklEQVR4nO3d0U3DQBQAQYKogiooAokGaJYuqII24A+sfBhQfOdNPFOAQVq9d47BcHo+vX7ekXC/9zfADzFCxAgRI0SMEDFCxAgRI0SMEDFCHmZ8kbeP94uv8fL4dPE16kxGiBghm62pLVbRjOuX153JCBEjRIyQKbe2JWtnz97nickIESPkcGtqzfkKm722TEaIGCFihEw/M0bs4VGPYpbXnXF+mIwQMUI2W1N7fnr97WuPfqK8FZMRIkaIGCGHeByyPFPK54fJCBEjRIwQMULECBEj5BC3tuXb2SWTESJGyE2uqWtZS+dMRogYIWKEpM+MvXe/X2I7MDFCxAhJnxmzeT+Db2KEWFML3s/gmxghYoScrvWPDM9+VOL9jIMRI+Rq19R/jFhpI9aWyQgRI0SMkKt9HLL2WvCM294RryWbjBAxQoasqRF/3HHtmnv/4sJWTEaIGCFihKRvbW/lLPgrkxEiRsiQNbXHJ+LR/HDpYMQIESNkyq3tFvvW/89gKjFC0p/Al8rrZSsmI0SMEDFCxAgRI0SMEDFCxAgRI0SMEDFCxAgRI0SMEDFCxAgRI+QLR4hMjm0nSnYAAAAASUVORK5CYII=\" id=\"image8ac49d7011\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"689.433962\" y=\"-97.706038\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_88\">\n", "    <path d=\"M 689.433962 168.986038 \n", "L 689.433962 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_89\">\n", "    <path d=\"M 760.5 168.986038 \n", "L 760.5 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_90\">\n", "    <path d=\"M 689.433962 168.986038 \n", "L 760.5 168.986038 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_91\">\n", "    <path d=\"M 689.433962 97.92 \n", "L 760.5 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p659120f5a4\">\n", "   <rect x=\"7.2\" y=\"7.2\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p0888f54591\">\n", "   <rect x=\"92.479245\" y=\"7.2\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p18587be927\">\n", "   <rect x=\"177.758491\" y=\"7.2\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6479833d72\">\n", "   <rect x=\"263.037736\" y=\"7.2\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pc505bee36d\">\n", "   <rect x=\"348.316981\" y=\"7.2\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p14878a8b23\">\n", "   <rect x=\"433.596226\" y=\"7.2\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pd9e2e30acf\">\n", "   <rect x=\"518.875472\" y=\"7.2\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p9929738859\">\n", "   <rect x=\"604.154717\" y=\"7.2\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p341f54120b\">\n", "   <rect x=\"689.433962\" y=\"7.2\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p30d8075c0a\">\n", "   <rect x=\"7.2\" y=\"97.92\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pe2ec73d30a\">\n", "   <rect x=\"92.479245\" y=\"97.92\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6458f4442f\">\n", "   <rect x=\"177.758491\" y=\"97.92\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p22f3ab91b9\">\n", "   <rect x=\"263.037736\" y=\"97.92\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pabfced9361\">\n", "   <rect x=\"348.316981\" y=\"97.92\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p57f2ab45a3\">\n", "   <rect x=\"433.596226\" y=\"97.92\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p3e401c34d3\">\n", "   <rect x=\"518.875472\" y=\"97.92\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p4eb178f23e\">\n", "   <rect x=\"604.154717\" y=\"97.92\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pa9765d09c4\">\n", "   <rect x=\"689.433962\" y=\"97.92\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1350x300 with 18 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.show_images(images, 2, 9);"]}, {"cell_type": "markdown", "id": "2148344b", "metadata": {"origin_pos": 24}, "source": ["## The Probabilistic Model for Classification\n", "\n", "In a classification task, we map an example into a category. Here an example is a grayscale $28\\times 28$ image, and a category is a digit. (Refer to :numref:`sec_softmax` for a more detailed explanation.)\n", "One natural way to express the classification task is via the probabilistic question: what is the most likely label given the features (i.e., image pixels)? Denote by $\\mathbf x\\in\\mathbb R^d$ the features of the example and $y\\in\\mathbb R$ the label. Here features are image pixels, where we can reshape a $2$-dimensional image to a vector so that $d=28^2=784$, and labels are digits.\n", "The probability of the label given the features is $p(y  \\mid  \\mathbf{x})$. If we are able to compute these probabilities, which are $p(y  \\mid  \\mathbf{x})$ for $y=0, \\ldots,9$ in our example, then the classifier will output the prediction $\\hat{y}$ given by the expression:\n", "\n", "$$\\hat{y} = \\mathrm{argmax} \\> p(y  \\mid  \\mathbf{x}).$$\n", "\n", "Unfortunately, this requires that we estimate $p(y  \\mid  \\mathbf{x})$ for every value of $\\mathbf{x} = x_1, ..., x_d$. Imagine that each feature could take one of $2$ values. For example, the feature $x_1 = 1$ might signify that the word apple appears in a given document and $x_1 = 0$ would signify that it does not. If we had $30$ such binary features, that would mean that we need to be prepared to classify any of $2^{30}$ (over 1 billion!) possible values of the input vector $\\mathbf{x}$.\n", "\n", "Moreover, where is the learning? If we need to see every single possible example in order to predict the corresponding label then we are not really learning a pattern but just memorizing the dataset.\n", "\n", "## The Naive Bayes Classifier\n", "\n", "Fortunately, by making some assumptions about conditional independence, we can introduce some inductive bias and build a model capable of generalizing from a comparatively modest selection of training examples. To begin, let's use <PERSON><PERSON> theorem, to express the classifier as\n", "\n", "$$\\hat{y} = \\mathrm{argmax}_y \\> p(y  \\mid  \\mathbf{x}) = \\mathrm{argmax}_y \\> \\frac{p( \\mathbf{x}  \\mid  y) p(y)}{p(\\mathbf{x})}.$$\n", "\n", "Note that the denominator is the normalizing term $p(\\mathbf{x})$ which does not depend on the value of the label $y$. As a result, we only need to worry about comparing the numerator across different values of $y$. Even if calculating the denominator turned out to be intractable, we could get away with ignoring it, so long as we could evaluate the numerator. Fortunately, even if we wanted to recover the normalizing constant, we could.  We can always recover the normalization term since $\\sum_y p(y  \\mid  \\mathbf{x}) = 1$.\n", "\n", "Now, let's focus on $p( \\mathbf{x}  \\mid  y)$. Using the chain rule of probability, we can express the term $p( \\mathbf{x}  \\mid  y)$ as\n", "\n", "$$p(x_1  \\mid y) \\cdot p(x_2  \\mid  x_1, y) \\cdot ... \\cdot p( x_d  \\mid  x_1, ..., x_{d-1}, y).$$\n", "\n", "By itself, this expression does not get us any further. We still must estimate roughly $2^d$ parameters. However, if we assume that *the features are conditionally independent of each other, given the label*, then suddenly we are in much better shape, as this term simplifies to $\\prod_i p(x_i  \\mid  y)$, giving us the predictor\n", "\n", "$$\\hat{y} = \\mathrm{argmax}_y \\> \\prod_{i=1}^d p(x_i  \\mid  y) p(y).$$\n", "\n", "If we can estimate $p(x_i=1  \\mid  y)$ for every $i$ and $y$, and save its value in $P_{xy}[i, y]$, here $P_{xy}$ is a $d\\times n$ matrix with $n$ being the number of classes and $y\\in\\{1, \\ldots, n\\}$, then we can also use this to estimate $p(x_i = 0 \\mid y)$, i.e.,\n", "\n", "$$\n", "p(x_i = t_i \\mid y) =\n", "\\begin{cases}\n", "    P_{xy}[i, y] & \\textrm{for } t_i=1 ;\\\\\n", "    1 - P_{xy}[i, y] & \\textrm{for } t_i = 0 .\n", "\\end{cases}\n", "$$\n", "\n", "In addition, we estimate $p(y)$ for every $y$ and save it in $P_y[y]$, with $P_y$ a $n$-length vector. Then, for any new example $\\mathbf t = (t_1, t_2, \\ldots, t_d)$, we could compute\n", "\n", "$$\\begin{aligned}\\hat{y} &= \\mathrm{argmax}_ y \\ p(y)\\prod_{i=1}^d   p(x_t = t_i \\mid y) \\\\ &= \\mathrm{argmax}_y \\ P_y[y]\\prod_{i=1}^d \\ P_{xy}[i, y]^{t_i}\\, \\left(1 - P_{xy}[i, y]\\right)^{1-t_i}\\end{aligned}$$\n", ":eqlabel:`eq_naive_bayes_estimation`\n", "\n", "for any $y$. So our assumption of conditional independence has taken the complexity of our model from an exponential dependence on the number of features $\\mathcal{O}(2^dn)$ to a linear dependence, which is $\\mathcal{O}(dn)$.\n", "\n", "\n", "## Training\n", "\n", "The problem now is that we do not know $P_{xy}$ and $P_y$. So we need to estimate their values given some training data first. This is *training* the model. Estimating $P_y$ is not too hard. Since we are only dealing with $10$ classes, we may count the number of occurrences $n_y$ for each of the digits and divide it by the total amount of data $n$. For instance, if digit 8 occurs $n_8 = 5,800$ times and we have a total of $n = 60,000$ images, the probability estimate is $p(y=8) = 0.0967$.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1b0597f6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:16.157521Z", "iopub.status.busy": "2023-08-18T20:14:16.156927Z", "iopub.status.idle": "2023-08-18T20:14:40.019976Z", "shell.execute_reply": "2023-08-18T20:14:40.018702Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0.0987, 0.1124, 0.0993, 0.1022, 0.0974, 0.0904, 0.0986, 0.1044, 0.0975,\n", "        0.0992])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.stack([mnist_train[i][0] for i in range(len(mnist_train))], dim=0)\n", "Y = torch.tensor([mnist_train[i][1] for i in range(len(mnist_train))])\n", "\n", "n_y = torch.zeros(10)\n", "for y in range(10):\n", "    n_y[y] = (Y == y).sum()\n", "P_y = n_y / n_y.sum()\n", "P_y"]}, {"cell_type": "markdown", "id": "d08f748d", "metadata": {"origin_pos": 28}, "source": ["Now on to slightly more difficult things $P_{xy}$. Since we picked black and white images, $p(x_i  \\mid  y)$ denotes the probability that pixel $i$ is switched on for class $y$. Just like before we can go and count the number of times $n_{iy}$ such that an event occurs and divide it by the total number of occurrences of $y$, i.e., $n_y$. But there is something slightly troubling: certain pixels may never be black (e.g., for well cropped images the corner pixels might always be white). A convenient way for statisticians to deal with this problem is to add pseudo counts to all occurrences. Hence, rather than $n_{iy}$ we use $n_{iy}+1$ and instead of $n_y$ we use $n_{y}+2$ (since there are two possible values pixel $i$ can take - it can either be black or white). This is also called *Laplace Smoothing*.  It may seem ad-hoc, however it can be motivated from a Bayesian point-of-view by a Beta-binomial model.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "4696714a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:40.023892Z", "iopub.status.busy": "2023-08-18T20:14:40.022984Z", "iopub.status.idle": "2023-08-18T20:14:40.423692Z", "shell.execute_reply": "2023-08-18T20:14:40.422375Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"432.9pt\" height=\"177.275172pt\" viewBox=\"0 0 432.9 177.275172\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T20:14:40.370364</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 177.275172 \n", "L 432.9 177.275172 \n", "L 432.9 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 7.2 79.355172 \n", "L 79.355172 79.355172 \n", "L 79.355172 7.2 \n", "L 7.2 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pb9dbc7dfec)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGUAAABlCAYAAABUfC3PAAAIf0lEQVR4nO1dWW9bVRC+m7d77fjaThonTdo0bVOVpawVi0ACCQnU5/4K/gH/hzdeEE9VJR4KEkItZe0CpEmTNE2I4y12HO/38nbnfCcQJSaBoZrvaUZj38XjMzPnO3PuNT8wr4eGgBWs//oCBPshTmEIcQpDiFMYQpzCEOIUhhCnMIQ4hSHEKQwhTmEI58SObNmRaMbwNJbrki2bAVuQ9UAfevFIDi0TbOaQGCK71cVzNPZAD3dbJO+18Zwd/K4RBor877NQMlIYQpzCEMcWvsxEAnQ7n4vk/nwRbOXLqUiuX8LjxOZ2Qb94aiuSXacHtpWdfCRvbfpgc5fyoI89ppCUWcXwFXtSAT2o70Ry2MbPhoOBcdKQkcIQ4hSGEKcwxMg5xXTwq3bxFOjNl6ciefsV/Gzm6nYkfzL/NdiueY9An3LSkTxUS1XDMGoBxfvvF3ywfXHlVdC/XFmg7z3AMjz3awr07CPKh/b6NtiCWp3krl5KH0/5LCOFIcQpDCFOYYiRc4qVRjqkNzcOeuV5OrSaQwzDMD4+fyuSr3mPwZYwbdAf9ogu2RhiLhiGNDeKm0OwXc/fAf2S+0ckf557CWzLEziP6uToPIV0DK9vWdG3tHxzTDlGRgpDiFMYYvSSOIOhpDmDNMveBaJErk1hmVt0iMa40ToLthuVF0H/eWuajtnEczgJojwmfKRnXipsgL6ghK+PivfBdsvpg34/NhPJoZMEW8Gk0j8ZYHgKSxjOQj2cHRIyUhhCnMIQ4hSGGDmnhCmM7+1T6N/8RCOS55JlsC12qQT9bB3pkM0fsDz1fyc5u4sxfJCka2hMpsF248wE6N/Nz0bym5MrYHvZXwe9O08/y6PhFNisPq2EjncKYHO0FcxhpUpKgCX7QZCRwhDiFIYYfeXRRn8OkGg18kkayjFttr3Yodn/kw1cISws4nEKv1Cpa1eaYAuVa/ALGL6acy7o1SqFs5uXscx9feYJ6JfGSpG8M4ufrTXo2uNNvOl8zQfdatL1Bh0JX/9riFMYQpzCEKPnlCGuAobYJ2dYJpWv/RCZ326gnLaH/4tEE8teq6400ZWrYAuHFKftah1sfjUHemyXytdyFymiu+Ys6K+dphyzkEPq5PYZyiPNGuax9FM8bryk5LVOxzgsZKQwhDiFIcQpDDE6dd9GSiGG/dRGo0P1vZ5TCjHKE5aLHYc9T7skB7+rIuwR5a53Llp91D1lFXCYQAqmlMHc8FuK6PkXxjfBdnaiFslLRZwL7U7HQS8sK6uzFcyHB0FGCkOIUxhidJZYY0TjO1jK1ho0tMt9LBUnY8QgnxpvgK1RROpikKcQ4JSRmTbUlb0h0hhBG0tQSymZ02vY9NHWKJry5Fgkt3xs/p5IEu3zeALP0c1hOAs95V5Mbc5wQFOFjBSGEKcwhDiFIUbPKXtYA7sljOnVLSqJl89io95Urh7JV7Suk5uzuJrXOk3HyZYwN1lKTtnXCKd3mig5xqph50uqirnArtLPUuui7eIY0S4ZD3NKL4OfDRLYyHdYyEhhCHEKQ4hTGGL0nKLRGskSbth0Nyn+39/GDpV5j7pb5lPY6VI8h/OC2sZkJCfquHScUmgWawfnOyoFYxiGYdjKvv4B5j+njcsQdod+lv4QaZ64RfftJnBjbFf7NU1lLnKUVm8ZKQwhTmGI0cOXxsI6Zew08Z4SlVF6Oga2B3kKZ+/kl8D22jg2xt24TN+t7CIFUwiUZusVvBWz2TL+DmEcS9XAQQpEJbVtC0NbQglfGnFiGIGmD0Z7nIiMFIYQpzCEOIUhRu9m0fa0hw3MKZl16iZprWIueKDsMZxx62CbT2H3yKtn1yL5zt48ntMiKj8fRyonuYmUh9WlEnng4/V0fCx7+1kqmSdTeF+2kjh6Wrmsr75ae0TD6OnmIMhIYQhxCkP8g/ClsbDa0+ZiJWJiM6u4YritrPT9mJ0BW66IMeCqvxLJg0v4H/oxdoZsHp7DW8cyPNGgANJ38TjNc1jcZqYpZM26NbDtDuk8tR1cwfTrWtnbxRn/YSEjhSHEKQwhTmGIY3tcYdjD+GnViLVNr2Ps7Sol6ZMsNsb9lMSc8lZ++S9lwzCMXJzy2B0fm7TLM9ihYjfoVgMXC1RvGsve92Zo33/Wxlz5W4NY66CEG4rcbWSfwyM0dauQkcIQ4hSGEKcwxPHlFL1DUaFdYhsYe8cyRJ33xpBG/8WbBl197O1bPuaUd5VN9s+nsStmbRZXKVsDml+kbMx/z7n4Xd+mvHavjfOoh39QTvHW8D+dLOFyQdiWnPLMQJzCECf2AgK1RA7r2NSQWqV9HLkk7k0MYsjg3rbmIjlxHlc73/cfRvLbLm7A/zCNISpmUhnsmkiHNAP8b37TJjb6q60LYAuXqLzPrmgNGNt4n8Oe0CzPDMQpDCFOYYjjyyk6la+UyIHWDG5t0/4/z9b2NIZIuVcDyjG3+gtga1+kcjoo3APb1eQq6Eklj1QDvO1v27ii+enaG5H89P4k2MYpjRnpZe1ZMRWk+fVpwmEhI4UhxCkMcXLv5FL7aLXGvaBJq5KWFvbS2mqd06KSuVHBLdF3S/RGnJ/OnQbbuXHsSc4nKIRu7mGIfLyhNV0sEgNR/BVD0Ngilb3m+hbY9DAtD4t+hiBOYQhxCkOYH5jX//0XGSp7yk0HWWJTa742lXdCGvks2PpF2gPTKWC+6Wbw/xYoh3U6eMvJGuaNeJXyWmxrB2xhlcreoKW9RK0/Gq2iQ0YKQ4hTGEKcwhAnN085CDCHwTi8Ly6rtb/2eCZ7iXKTZ+L/y7P2bekhaHvs9zWrK/Z9r9o8wpO4R4WMFIYQpzDEfxO+jgKVqgi1ZrejbPr4H0FGCkOIUxhCnMIQ4hSGEKcwhDiFIcQpDCFOYQhxCkOIUxjiT6RDl/9BBRhWAAAAAElFTkSuQmCC\" id=\"image8740f55c5f\" transform=\"scale(1 -1) translate(0 -72.72)\" x=\"7.2\" y=\"-6.635172\" width=\"72.72\" height=\"72.72\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 7.2 79.355172 \n", "L 7.2 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 79.355172 79.355172 \n", "L 79.355172 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 7.2 79.355172 \n", "L 79.355172 79.355172 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 7.2 7.2 \n", "L 79.355172 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 93.786207 79.355172 \n", "L 165.941379 79.355172 \n", "L 165.941379 7.2 \n", "L 93.786207 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pfd395ff8b4)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGUAAABlCAYAAABUfC3PAAAFfUlEQVR4nO2du3McRRDGZ3dv7y0h6YTLbwUULhsCIgeOISMhIOA/4f8hosgoioSYiCIDl6sgwMZWSUbo4Xvo3jMErtrpr690IKO7/Sj6F02rT7t79e10z87O9CUfJZ8GZ1CRln0BxiImCiEmCiEmCiEmCiEmCiGVsi/gykgStMN/d6RvPYUQE4UQE4WQ1eUUEeOTSo6uarSTRh19OX7WVcQl6jyRiXtK5ZQwHKHd6xdtPxrjcfzcMWE9hRAThZCrC19phqYMURsb+NlrO0VzdKMNrlEHw9esEcNSWHILJSoCNY7xD82n3aKdvTgAn+8PwA5z8b8lDK2tpxBiohBiohCyuiFxGvVOMtR+Xo+nnW7gJQx38bOTzdj21SWnm6CdBMxxteNa0c4bDfysGiIHL/OIxwOvIcdYTyHERCHERCHk6nJKwNgrx/phNgNfOop2OlMxWs3Az9rRP2vhZ0Ma7fyVyltqtkYeN3iVJ/RHU/FsVMIMjPUUQkwUQq4wfKnQMo0hSs/Ypt04rVHtNsGXjXAoG0TY8ZsYBrNajC2zWc0hGAfTiYhDKpwGfe1+ybBXzkavaHhsPYUQE4UQE4WQ1U2ziCFyGKtpjF6vaOdHmFPquziXMpiIHNOYgu/6TpyOfz7u4Dky9bZzInLcBI/j5pcY99o0y/8TE4WQFYav2M31E70fDIt2enwGvuYhhrPuXrRDhk/iD3efFe3RDL/KtPo22MlYhK8phq+FIbCcnbA3j4ZzJgolJgohpSzwDrMY08OrLvjyA8wpzZdxsd5ggpf7Qev3ol27iXnr6w3MKXLYG+b6beL63y4uw3oKISYKISYKIeVsGhIx26spj+TPE7A3n24V7aMjXAzeqcRF259t/wC+L289wnNmYrqGLIdorKcQYqIQUv6eRxVK/Pk52Pmzo6LderEHvmmIl/9A7Wu5e+8lHrfd+leXuU6spxBiohBiohBSfk5RBPUW0J+eFe3tX26B7+fh7aL9SasPvo9v/gT2N7c/LNqtx/i19auFsrGeQoiJQoiJQghdTtHIeN98jnnj2/33i/bnncfge6++D/YX78avuvHjDvj8AT7TlL2v3noKISYKIXzhK8H7JJHlRM5xUd8fT+LbxScPcLa5leAmyMGeWAx+Zxd8abcHtu+LMGmrWQznTBRKTBRC+HLKMlR8bx7Ge+qrs4fge9T+Fey0E/NR/y6umNk63MbTTGI+0ovT14H1FEJMFELowpfcLu2cg3Iiuvpd3o3h7Lv9++Cr38Ehcr0e7cF1DF/ta2+BnYkqevOpmkFew9O+9RRCTBRCTBRC6HKK3sAjp1l0TqmI7flHh5gXvm+8A7b38X9luSrnnBvewFJT7ZN4rFSVnfKyJsCK8ov1FEJMFEJMFELocsrS5xTlS6cx/2Sn+FV+a+O++uk4+vMa5q3xJtaDaezEsrxZD8vjpmK1jR+vZqG49RRCTBRC6MKXBiq1pngPpWIGpKoq443qOMyFY3oMg/M6hp3pVixTlZ1ihfFEzBonau+k3Mv5+g9vFs6spxBiohBiohBCn1NgdYuK0dkk2hUcubr89OL7rYL7kqAkonPO+VxUW22pH93pxnyzUPl7rs75hqVaracQYqIQUn74+rtho9yvot4C5r1o14/x/somKiZJ3wjPWe2jnY4vLi0lZ61Dupp72noKISYKISYKIeXnFMXCnkcxrZGenIFPDlbzLu6T91Wc+U3EG81Evd2UP4jgnHPJMC7GS/o4fvaDaMtFe6//sPyHDf4p1lMIMVEIMVEIocspC78uIXLKXC+2Pjm98DCXudsWMoGVKzQ0JgohfOHrMpBVtLsqrKcQYqIQYqIQYqIQYqIQYqIQYqIQYqIQYqIQYqIQ8hfVtqWCczmJGQAAAABJRU5ErkJggg==\" id=\"imaged5472fb4f8\" transform=\"scale(1 -1) translate(0 -72.72)\" x=\"93.786207\" y=\"-6.635172\" width=\"72.72\" height=\"72.72\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 93.786207 79.355172 \n", "L 93.786207 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 165.941379 79.355172 \n", "L 165.941379 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 93.786207 79.355172 \n", "L 165.941379 79.355172 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 93.786207 7.2 \n", "L 165.941379 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 180.372414 79.355172 \n", "L 252.527586 79.355172 \n", "L 252.527586 7.2 \n", "L 180.372414 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd49e97e2af)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image0f61067888\" transform=\"scale(1 -1) translate(0 -72.72)\" x=\"180.372414\" y=\"-6.635172\" width=\"72.72\" height=\"72.72\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 180.372414 79.355172 \n", "L 180.372414 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 252.527586 79.355172 \n", "L 252.527586 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 180.372414 79.355172 \n", "L 252.527586 79.355172 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 180.372414 7.2 \n", "L 252.527586 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 266.958621 79.355172 \n", "L 339.113793 79.355172 \n", "L 339.113793 7.2 \n", "L 266.958621 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p8d032bb53b)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image22522f40fb\" transform=\"scale(1 -1) translate(0 -72.72)\" x=\"266.958621\" y=\"-6.635172\" width=\"72.72\" height=\"72.72\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 266.958621 79.355172 \n", "L 266.958621 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 339.113793 79.355172 \n", "L 339.113793 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 266.958621 79.355172 \n", "L 339.113793 79.355172 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 266.958621 7.2 \n", "L 339.113793 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 353.544828 79.355172 \n", "L 425.7 79.355172 \n", "L 425.7 7.2 \n", "L 353.544828 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd7535fd25f)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image5e3c4adb6f\" transform=\"scale(1 -1) translate(0 -72.72)\" x=\"353.544828\" y=\"-6.635172\" width=\"72.72\" height=\"72.72\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 353.544828 79.355172 \n", "L 353.544828 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 425.7 79.355172 \n", "L 425.7 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 353.544828 79.355172 \n", "L 425.7 79.355172 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 353.544828 7.2 \n", "L 425.7 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 7.2 170.075172 \n", "L 79.**********.075172 \n", "L 79.355172 97.92 \n", "L 7.2 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p6cd9564d25)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagec7150c6134\" transform=\"scale(1 -1) translate(0 -72.72)\" x=\"7.2\" y=\"-97.355172\" width=\"72.72\" height=\"72.72\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 7.2 170.075172 \n", "L 7.2 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 79.**********.075172 \n", "L 79.355172 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 7.2 170.075172 \n", "L 79.**********.075172 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 7.2 97.92 \n", "L 79.355172 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 93.786207 170.075172 \n", "L 165.941379 170.075172 \n", "L 165.941379 97.92 \n", "L 93.786207 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p7ef1014e47)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGUAAABlCAYAAABUfC3PAAAHw0lEQVR4nO1dTW8bZRB+d73e9Tqx43wnbQgUVbSiFFWCS5G4ceYE/4CfwG/iyAUOCAmJCxcEqlSEEC0tLW1SYzuOP9efu8sBaed9xkrURHYyreY5zWTs3deZfWfmfd7ZXecT57PUKETBvewBKGahThEIdYpAqFMEQp0iEOoUgVCnCIQ6RSDUKQKhThEIdYpAqFMEQp0iEN5lD8A4DtNdppLd8dhwczmSXXZ9xTGoqa2fZjPGmPRyiXOdKQKhThEIdYpAXExOcXOoFoJMdkrLYEu310AfXillcn8HhztZsvJNgnkgaKO+dDjOZP/fLo6v0cIxdMmeTqdos/PPgnKPzhSBUKcIhDpFIBaWU+w1hbu8hMarO5nYvrUKptqHuG5564PnmfzFlV/BdqfwNJNbcRFsP3Rvgf7tY9Lj3zfAtnEf81jpL8op7osG2NJ2J5OT8QRsJmHrnXNCZ4pAqFMEwplbMx6jS9xlKnWdvR2wtd5fz+Tqx3j6T+9iiPpy88dM3vOwfJ6kFC5GKYaSKMVQ8mTqZ/JXzbtg++aP26Av/xJm8sb9EdgKj+uZnNSPwJYMhgb/cL5wpjNFINQpAqFOEYi5lcSOlwfdXaISdbhbBlvrHboWrt18DrbPV38GPbHk7yM8x099KnMPhxWwlb0B6DeK1Uz+qPQQbMEtpFK+du9kci2Ppfamt5XJId92qGH5nEQRKWegZHSmCIQ6RSDmF75yzL9FKiuHGxh2hptUKl4v18HWTULQv+vvZfL3jXfB9uBok753jGHGDBkzXaKS+founvO9yiHot/cOMvle5xrY/A79Fq9bAVt+iOWzzTCn47FB48nhTGeKQKhTBEKdIhDzY4lZN0kaUOydFLF0TJco1gYulqO/DfdAv9fez+RHTWR3J5OTh59vYU7JP6PPPqriOQ7eXgH96ko7kwvrWFoPtmgndLgdgM1rIg3k9qkkjif4O016MgWjM0Ug1CkCoU4RiMV1s9gUBC/JLX2U4BAakxLoT7u0M9nt4RomLNK6YHu7DbajAOP76LCQye4Ec1y/gWucA0vmy4l4if4wKmHeKi5jjnEDq2vHplyMMWliToTOFIFQpwjE4sJXTPMzN8YY4FgUSHdSABsvkaMR7RjGHaRr+jGFoSvlDtj2949BP1yjsrfextCWm2IYmk5dS0abHfhSNJnEx39nzqaenJe//nWmCIQ6RSDUKQIxv5ySYI3nWLSC30Nbvk3B+EWEu5IVH2mNvEd0RK6HQTx3RPrDdAtsN/eroN+o1DJ5I+yDrdrHMrw7oFJ21MEyN+xRVsmN8He5E0adwLLglBqYQWeKQKhTBEKdIhBzyykp5yOs5mevj7HWb9FpDxoVsO0Wcb2xUqCuw6McnqNYpWsqbuJ658/mPugPt+k4Nj1jzOxaZNChY+XruDbyLTYn6ODvcgfYpWlvAaeJdrO80lCnCMQcS2I2Pa1OjnwXOzkKDaJO+nUMO/+s4v0qayGxq0mF797RcVYfoK30DJngaIuY4PEKssIp+y8sWdHN5/dO1ihkFWrY0O10sNRO7O6WMzR760wRCHWKQKhTBGJ+OYXRCOmEykM3wpwSHltxuY5DqK5jZ0l+iz4bljGGj9Yopzh/Y+wvP8B75UtP6DyTkg+2uMCuTetQ+QhzldemPOG2evi1HuaUdISl98tCZ4pAqFMEYn4rel4SWyyxM8Bp7LfJVqzianq0iqxsPaRdwjDAMNjcpeNEW/hTCjXGKFdpJzL3go3VY1uIdmMhY7+NVeamQwynSR8Z7plHVr0kdKYIhDpFINQpArG4kthiSJ0R5gLPol3CI2RhB3WM733r3sl0AxvachU6TvdNpE4KLdSXu1b8bzTZWJHddawdwxn228oT6eSUx079/2VzHuhMEQh1ikCoUwRiYR2SqdUhyekG14rvfgvXJfZuojHGTJdpiJGPNL8XUAwf7mB8b7fwp3k9om9C/kio4xaO3V6LxGydYuXOmbWZPlrq9YU6RSDmWBKzqWyXyLzkjCh85TsYksImlsTjGg0x9pHdnW5YZXeRNYZfZfc8RvRdd7oONgygxpiW1bzBQi+Wwac03xmjJfHrBHWKQKhTBGJxJbFVLvKnYNv0t10eG2NMcIRDKhathrsQr6FBniiapITnmJYx3vfeoBzjxJhFVhzMMf5z67PHeC+liSx6nlNLCd/BVOr+tYE6RSAu5AUEnD11rHBml8fGGON1kTUuWjuIcYAlcWLdUzhkLzlIAwwt4zXS+2N2H2OC4axsKpnsszIXNL4ryV9WkNr3p2gv8SsNdYpAqFMEYnE5xS4XGZsKrLGPOcTpY47Je3TdFH28hhJ4cRrmickK+2yexjAt4nhGFcwbQyuveYwGykWkp2xHleeU80JnikCoUwRCnSIQl7JOseGwNyk4/O0SlozR3Rg3pkdNeQP8KSOWU+ICHZc9/sXku5hjvIjyoTNla5HTXuJ5hvsaT4POFIFQpwjEAktieyozNtWa9fxFMA6nZKyyM9fDZrzQuk07OMSn5qXBKU3bLCS5Y4xnjnUefs+JPd6Zp3Jr48TrC3WKQKhTBOJi3h080+liNUmzOJxOWaMce0rpieCPAzzDo5xmMoG+j17BoU4RiIsJX2fBeUPHOZsUJEJnikCoUwRCnSIQ/wEGiJ4mC5IsDAAAAABJRU5ErkJggg==\" id=\"image6d29a9d8ea\" transform=\"scale(1 -1) translate(0 -72.72)\" x=\"93.786207\" y=\"-97.355172\" width=\"72.72\" height=\"72.72\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 93.786207 170.075172 \n", "L 93.786207 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 165.941379 170.075172 \n", "L 165.941379 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 93.786207 170.075172 \n", "L 165.941379 170.075172 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 93.786207 97.92 \n", "L 165.941379 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 180.372414 170.075172 \n", "L 252.527586 170.075172 \n", "L 252.527586 97.92 \n", "L 180.372414 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pc7b8470464)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image38fa76fcbe\" transform=\"scale(1 -1) translate(0 -72.72)\" x=\"180.372414\" y=\"-97.355172\" width=\"72.72\" height=\"72.72\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 180.372414 170.075172 \n", "L 180.372414 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 252.527586 170.075172 \n", "L 252.527586 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 180.372414 170.075172 \n", "L 252.527586 170.075172 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 180.372414 97.92 \n", "L 252.527586 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 266.958621 170.075172 \n", "L 339.113793 170.075172 \n", "L 339.113793 97.92 \n", "L 266.958621 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pabc0335d1b)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagedfc299b362\" transform=\"scale(1 -1) translate(0 -72.72)\" x=\"266.958621\" y=\"-97.355172\" width=\"72.72\" height=\"72.72\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 266.958621 170.075172 \n", "L 266.958621 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 339.113793 170.075172 \n", "L 339.113793 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_45\">\n", "    <path d=\"M 266.958621 170.075172 \n", "L 339.113793 170.075172 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_46\">\n", "    <path d=\"M 266.958621 97.92 \n", "L 339.113793 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_10\">\n", "   <g id=\"patch_47\">\n", "    <path d=\"M 353.544828 170.075172 \n", "L 425.7 170.075172 \n", "L 425.7 97.92 \n", "L 353.544828 97.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#peab2047cb2)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image27691e3aa6\" transform=\"scale(1 -1) translate(0 -72.72)\" x=\"353.544828\" y=\"-97.355172\" width=\"72.72\" height=\"72.72\"/>\n", "   </g>\n", "   <g id=\"patch_48\">\n", "    <path d=\"M 353.544828 170.075172 \n", "L 353.544828 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_49\">\n", "    <path d=\"M 425.7 170.075172 \n", "L 425.7 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_50\">\n", "    <path d=\"M 353.544828 170.075172 \n", "L 425.7 170.075172 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_51\">\n", "    <path d=\"M 353.544828 97.92 \n", "L 425.7 97.92 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb9dbc7dfec\">\n", "   <rect x=\"7.2\" y=\"7.2\" width=\"72.155172\" height=\"72.155172\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pfd395ff8b4\">\n", "   <rect x=\"93.786207\" y=\"7.2\" width=\"72.155172\" height=\"72.155172\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pd49e97e2af\">\n", "   <rect x=\"180.372414\" y=\"7.2\" width=\"72.155172\" height=\"72.155172\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p8d032bb53b\">\n", "   <rect x=\"266.958621\" y=\"7.2\" width=\"72.155172\" height=\"72.155172\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pd7535fd25f\">\n", "   <rect x=\"353.544828\" y=\"7.2\" width=\"72.155172\" height=\"72.155172\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6cd9564d25\">\n", "   <rect x=\"7.2\" y=\"97.92\" width=\"72.155172\" height=\"72.155172\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p7ef1014e47\">\n", "   <rect x=\"93.786207\" y=\"97.92\" width=\"72.155172\" height=\"72.155172\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pc7b8470464\">\n", "   <rect x=\"180.372414\" y=\"97.92\" width=\"72.155172\" height=\"72.155172\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pabc0335d1b\">\n", "   <rect x=\"266.958621\" y=\"97.92\" width=\"72.155172\" height=\"72.155172\"/>\n", "  </clipPath>\n", "  <clipPath id=\"peab2047cb2\">\n", "   <rect x=\"353.544828\" y=\"97.92\" width=\"72.155172\" height=\"72.155172\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 750x300 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["n_x = torch.zeros((10, 28, 28))\n", "for y in range(10):\n", "    n_x[y] = torch.tensor(X.numpy()[Y.numpy() == y].sum(axis=0))\n", "P_xy = (n_x + 1) / (n_y + 2).reshape(10, 1, 1)\n", "\n", "d2l.show_images(P_xy, 2, 5);"]}, {"cell_type": "markdown", "id": "2a4aa654", "metadata": {"origin_pos": 32}, "source": ["By visualizing these $10\\times 28\\times 28$ probabilities (for each pixel for each class) we could get some mean looking digits.\n", "\n", "Now we can use :eqref:`eq_naive_bayes_estimation` to predict a new image. Given $\\mathbf x$, the following functions computes $p(\\mathbf x \\mid y)p(y)$ for every $y$.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "37b33d70", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:40.427262Z", "iopub.status.busy": "2023-08-18T20:14:40.426782Z", "iopub.status.idle": "2023-08-18T20:14:40.436411Z", "shell.execute_reply": "2023-08-18T20:14:40.435177Z"}, "origin_pos": 34, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0., 0., 0., 0., 0., 0., 0., 0., 0., 0.])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["def bayes_pred(x):\n", "    x = x.unsqueeze(0)  # (28, 28) -> (1, 28, 28)\n", "    p_xy = P_xy * x + (1 - P_xy)*(1 - x)\n", "    p_xy = p_xy.reshape(10, -1).prod(dim=1)  # p(x|y)\n", "    return p_xy * P_y\n", "\n", "image, label = mnist_test[0]\n", "bayes_pred(image)"]}, {"cell_type": "markdown", "id": "7536ade9", "metadata": {"origin_pos": 36}, "source": ["This went horribly wrong! To find out why, let's look at the per pixel probabilities. They are typically numbers between $0.001$ and $1$. We are multiplying $784$ of them. At this point it is worth mentioning that we are calculating these numbers on a computer, hence with a fixed range for the exponent. What happens is that we experience *numerical underflow*, i.e., multiplying all the small numbers leads to something even smaller until it is rounded down to zero.  We discussed this as a theoretical issue in :numref:`sec_maximum_likelihood`, but we see the phenomena clearly here in practice.\n", "\n", "As discussed in that section, we fix this by use the fact that $\\log a b = \\log a + \\log b$, i.e., we switch to summing logarithms.\n", "Even if both $a$ and $b$ are small numbers, the logarithm values should be in a proper range.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "036f36f6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:40.440411Z", "iopub.status.busy": "2023-08-18T20:14:40.439398Z", "iopub.status.idle": "2023-08-18T20:14:40.445727Z", "shell.execute_reply": "2023-08-18T20:14:40.444466Z"}, "origin_pos": 38, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["underflow: 0.0\n", "logarithm is normal: -1805.2267129073316\n"]}], "source": ["a = 0.1\n", "print('underflow:', a**784)\n", "print('logarithm is normal:', 784*math.log(a))"]}, {"cell_type": "markdown", "id": "96581a1d", "metadata": {"origin_pos": 40}, "source": ["Since the logarithm is an increasing function, we can rewrite :eqref:`eq_naive_bayes_estimation` as\n", "\n", "$$ \\hat{y} = \\mathrm{argmax}_y \\ \\log P_y[y] + \\sum_{i=1}^d \\Big[t_i\\log P_{xy}[x_i, y] + (1-t_i) \\log (1 - P_{xy}[x_i, y]) \\Big].$$\n", "\n", "We can implement the following stable version:\n"]}, {"cell_type": "code", "execution_count": 12, "id": "9b194d84", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:40.449044Z", "iopub.status.busy": "2023-08-18T20:14:40.448769Z", "iopub.status.idle": "2023-08-18T20:14:40.462160Z", "shell.execute_reply": "2023-08-18T20:14:40.461101Z"}, "origin_pos": 42, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([-268.9725, -301.7044, -245.1951, -218.8738, -193.4570, -206.0909,\n", "        -292.5226, -114.6257, -220.3313, -163.1784])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["log_P_xy = torch.log(P_xy)\n", "log_P_xy_neg = torch.log(1 - P_xy)\n", "log_P_y = torch.log(P_y)\n", "\n", "def bayes_pred_stable(x):\n", "    x = x.unsqueeze(0)  # (28, 28) -> (1, 28, 28)\n", "    p_xy = log_P_xy * x + log_P_xy_neg * (1 - x)\n", "    p_xy = p_xy.reshape(10, -1).sum(axis=1)  # p(x|y)\n", "    return p_xy + log_P_y\n", "\n", "py = bayes_pred_stable(image)\n", "py"]}, {"cell_type": "markdown", "id": "20d26b56", "metadata": {"origin_pos": 44}, "source": ["We may now check if the prediction is correct.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "1802d629", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:40.466773Z", "iopub.status.busy": "2023-08-18T20:14:40.466141Z", "iopub.status.idle": "2023-08-18T20:14:40.473208Z", "shell.execute_reply": "2023-08-18T20:14:40.471922Z"}, "origin_pos": 46, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor(True)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["py.argmax(dim=0) == label"]}, {"cell_type": "markdown", "id": "bfc3c761", "metadata": {"origin_pos": 48}, "source": ["If we now predict a few validation examples, we can see the Bayes\n", "classifier works pretty well.\n"]}, {"cell_type": "code", "execution_count": 14, "id": "7531d206", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:40.478617Z", "iopub.status.busy": "2023-08-18T20:14:40.477472Z", "iopub.status.idle": "2023-08-18T20:14:41.447119Z", "shell.execute_reply": "2023-08-18T20:14:41.446186Z"}, "origin_pos": 50, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"767.7pt\" height=\"191.304163pt\" viewBox=\"0 0 767.7 191.304163\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T20:14:41.342264</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 191.304163 \n", "L 767.7 191.304163 \n", "L 767.7 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 7.2 93.384163 \n", "L 78.266038 93.384163 \n", "L 78.266038 22.318125 \n", "L 7.2 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pe133959d7c)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABkklEQVR4nO3bwU2FQBRAUTFWYRUWYWIDNmsXVmEbupSQiIj5cIB7tm5Ibt4MzB+H5+H18y6E+70fIN+KASkG5GHvB5jz9vH+499eHp82e46tNBmQYkDoZWrOeAk7y5LVZECKASkGpBiQYkCKASkGpBiQYkCKAaGPQ8bHHHMnuGfRZECKARmOeiHhjD88NRmQYkCKAaFfbdea7idH2UOaDEgxIId9tR37y9e5vGQ1GZBiQIoBOcWr7XQfOOoJb5MBKQbkFK+2vznKCW+TASkGpBiQYkCKASkG5BRf4P8h/TtakwEpBqQYkEvsGUe5s9tkQIoBKQbkEnvGUntffmsyIMWAXG6Zki8vNBmQYkCKAbnE7ZCl9r5F0mRAigEpBqQYkGJAigGhjkOko4mpLU50mwxIMSA3Wabk5WatvsAvphiQYkBusmes/TVt74vHe2syIMWAbPIFfvXlZ6kmA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYB8ARSNPGXdfVhFAAAAAElFTkSuQmCC\" id=\"imageb46ef581cc\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"7.2\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 7.2 93.384163 \n", "L 7.2 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 78.266038 93.384163 \n", "L 78.266038 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 7.2 93.384163 \n", "L 78.266038 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 7.2 22.318125 \n", "L 78.266038 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_1\">\n", "    <!-- 7 -->\n", "    <g transform=\"translate(38.915519 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-37\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 92.479245 93.384163 \n", "L 163.545283 93.384163 \n", "L 163.545283 22.318125 \n", "L 92.479245 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p3f524da8ce)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABsklEQVR4nO3cwU3DQBRAQYKogiooAikN0CxdUAVtwJHIhyg4tnfsvCkgsvT0d9eOk9P76ePnKYTn0ReQP8WAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIC+jL2Cuz++vRT7n/Pq2yOcsocmAFAOy+TK11PKylDWuZ+7S12RAigEpBmS3R1vZtX3o2n7SZECKAaGXKenueGqNI3GTASkGpBgQes+QrbGfNRmQYkDoZery+Cgfc5fSZECKASkG5DT6p8dzHysccQ9pMiDFgAw/2l4uN9rLCltrMiDFgBQDMnzPmOuIj0qaDEgxIMWAUHvGdO1/tPuOJgNSDAi1TM01Xc72etRtMiDFgBQDMvybvlvdc8zdyx7SZECKAdnN0fYR7s6bDEgxIMWA7OZoe81/9g/5mNtkQIoBKQakGJBiQIoBof6Jbe6x8yiPSpoMSDEg1FPbLV4skF9eaDIgxYAUA0LtGVPSHwBvocmAFAOy+TJ1lLvlNTQZkGJAigEZfrS99ajZu7bZVDEgw5epW+1lqblHkwEpBqQYkGJAigEpBqQYkGJAigEpBuQX+h9HZ6MUo/IAAAAASUVORK5CYII=\" id=\"image39f3b8bd97\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"92.479245\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 92.479245 93.384163 \n", "L 92.479245 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 163.545283 93.384163 \n", "L 163.545283 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 92.479245 93.384163 \n", "L 163.545283 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 92.479245 22.318125 \n", "L 163.545283 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_2\">\n", "    <!-- 2 -->\n", "    <g transform=\"translate(124.194764 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-32\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 177.758491 93.384163 \n", "L 248.824528 93.384163 \n", "L 248.824528 22.318125 \n", "L 177.758491 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#paf7a73611a)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABbElEQVR4nO3cwU3DQBBA0TiiCqpIEZFogGbpgipogxxjWcLAxXm7+e+ai6WvsScr2ct1ef8+hXB+9AXkrhiQYkCKASkGpBiQYkCKASkGpBiQYkCKASkG5OXRF7Dn4+vzx9/eXi+HXcdRmgxIMSDFgBQDUgxIMSD0artnvfbOsuY2GZBiQIoBoZ8Z62fB3tHILJoMSDEgxYAUA1IMSDEg9Gr7V9u1d9TjkSYDUgzIMLep7a1nxn/kTQakGJBiQIoBKQakGJBiQIoBKQakGJBlhq/q/HY0MsopbpMBKQakGJBiQIoBKQakGJBiQIoBKQZkiuOQrVE/c9FkQIoBKQakGJBiQIoBKQakGJBiQIZ5P+M/Rn2Xo8mAFANSDMiUz4w98mvKTQakGJBiQIoBKQakGJCnWG1H+fBkkwEpBqQYkGJAigEpBuQpVts16ZR2q8mAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWA3ADgrSQvBhh21gAAAABJRU5ErkJggg==\" id=\"imageb7afb25d56\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"177.758491\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 177.758491 93.384163 \n", "L 177.758491 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 248.824528 93.384163 \n", "L 248.824528 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 177.758491 93.384163 \n", "L 248.824528 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 177.758491 22.318125 \n", "L 248.824528 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_3\">\n", "    <!-- 1 -->\n", "    <g transform=\"translate(209.474009 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-31\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 263.037736 93.384163 \n", "L 334.103774 93.384163 \n", "L 334.103774 22.318125 \n", "L 263.037736 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pfb0625c05e)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABx0lEQVR4nO3cwU0DMRBAURZRBVVQBFIaoNl0QRW0AUcsH1axMp75k/x3jkKkr7GNYXN8Hl+/L0J4rf4A+mcMEGOAGAPEGCDGADEGiDFAjAFiDBBjgBgDxBggb9Uf4Mz15/um113eP7Z+jixOBogxQIwBUr5n3Lov3PMeXfYUJwPEGCDpy1TEshT5M0lLmJMBYgwQY4CUH22rkfYTJwPEGCApy1TE7WvFkTibkwFiDBBjgLQ52q4cM6P2l/F9Mo65TgaIMUDaLFMr5iWly7HYyQAxBogxQB5yz5hFXLPMr9tx1HUyQIwB8hTL1Jlxuak+AjsZIMYAMQZIyp5BWpej7LjRdTJAjAFiDBDU7xkZVw5RvA55cMYAQS1Ts+x/CKjmZIAYA8QYIOg9Y7TrOQrSV2I4GSDGADmqvwt9xy1u1HMePkb2xIwBYgyQNkfbFV3/muhkgBgDpHyZIv2zQvXNsJMBYgwQY4CU7xmjjGfxqveFM04GiDFAUMvULOrYS16aRk4GiDFAjAGC3jNGXb+CYoWTAWIMEGOAGAPEGCDGAGlztJ11ueJY4WSAGAPEGCDGADEGiDFAjAFiDBBjgBgDxBggxgAxBsgfi0lVrN1wLAsAAAAASUVORK5CYII=\" id=\"image7349c0f1cc\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"263.037736\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 263.037736 93.384163 \n", "L 263.037736 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 334.103774 93.384163 \n", "L 334.103774 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 263.037736 93.384163 \n", "L 334.103774 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 263.037736 22.318125 \n", "L 334.103774 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- 0 -->\n", "    <g transform=\"translate(294.753255 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-30\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 348.316981 93.384163 \n", "L 419.383019 93.384163 \n", "L 419.383019 22.318125 \n", "L 348.316981 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pb13ae2d142)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABoUlEQVR4nO3cwU3DMBiA0RYxBVMwBBILsCxbMAVrwK1EOQSKwH7g7516QpE+/baTppwfzk9vpxBuZl9APhQDUgxIMSDFgBQDUgxIMSDFgBQDcjv7AkZ4fn25fH68u592HZ9pMiDFgCyxTG1tl6zTyVq2mgxIMSDFgCy3Z+xJx94mA1IMyBLL1Hb52R9tJU0GpBiQYkCW2DPkfWKryYAUA7LEMtXRNlcrBqQYkCX2DHmf2GoyIMWADF+m/sqSMUOTASkGpBiQIXvGV/eJ774Q8F/2oSYDUgzI9Dvwn3hX6Zq/IS9pTQakGJBiQKbvGZLZv91oMiDFgBQDUgxIMSDFgCx3tN0fV6XHI00GpBiQYkDOo//J8NEaPeOnv7/9LeQ1mgxIMSDLHW33pN9uNBmQYkCKARm+Zxw9jpj9TdtsTQakGJDhd+BH5Lvz7sAXUwxIMSDFgBQDUgwI9dR29ssCPbXNRTEgxYBQe8Zss58SNxmQYkDoZWr2sjFakwEpBqQYkGJAigEpBqQYkGJAigEpBqQYkGJAigEpBqQYkGJAigF5BzKDRoyi0gUnAAAAAElFTkSuQmCC\" id=\"imagecf684eff7d\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"348.316981\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 348.316981 93.384163 \n", "L 348.316981 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 419.383019 93.384163 \n", "L 419.383019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 348.316981 93.384163 \n", "L 419.383019 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 348.316981 22.318125 \n", "L 419.383019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- 4 -->\n", "    <g transform=\"translate(380.0325 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-34\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 433.596226 93.384163 \n", "L 504.662264 93.384163 \n", "L 504.662264 22.318125 \n", "L 433.596226 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd8ac015241)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABc0lEQVR4nO3cwU3DQBBAUYyogiooAikNpFm6oAragCOOpSTOAe3z8t8tysXS12ysSZzlfTl/P4XwPPoC8qsYkGJAigEpBqQYkGJAigEpBqQYkJfRF7DXx9fnxevT69uQ6/hLTQakGBD6mNoeTdfem+XIajIgxYAUA1IMSDEgxYAUA1IMSDEgxYDQ65D1muPWamQWTQakGBD6mNprli+emgxIMSDFgBQDUgxIMSDFgBQDUgxIMSDLUR89fmSLe5T1SJMBKQakGJBiQIoBKQakGJBiQIoBKQbksOuQtVlWI00GpBiQYkCKASkGpBiQYkCKASkGZIrnMx4hP8vRZECKASkGZIrPjO25f9THlJsMSDEgxYAUA1IMSDEgU/wg4Z69t7qjVyNNBqQYkGJAigEpBqQYkCm2tvfc+uPJ0beza00GpBiQf3FMrUnH0laTASkGpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKAfkBaq0oxCrlGDEAAAAASUVORK5CYII=\" id=\"image7dcf741c9a\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"433.596226\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 433.596226 93.384163 \n", "L 433.596226 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 504.662264 93.384163 \n", "L 504.662264 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 433.596226 93.384163 \n", "L 504.662264 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 433.596226 22.318125 \n", "L 504.662264 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- 1 -->\n", "    <g transform=\"translate(465.311745 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-31\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 518.875472 93.384163 \n", "L 589.941509 93.384163 \n", "L 589.941509 22.318125 \n", "L 518.875472 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p011cf127be)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABvUlEQVR4nO3cy43CQBAAURsRBVEQBBIJkCxZEAVpwBHLh8HjT3eNXe+22p+lUs94By/9rX98OiGcsi9AP8YAMQaIMUCMAWIMEGOAGAPEGCDGADlnX8Aanu9X8fP3yzXkOpZyMkCMAdLsMvVvaZr6taQlzMkAMQaIMUCa3TNqDPeF8f4x/Dh7/3AyQIwBcohlaih7KSpxMkCMAWIMkGb3jNLtaqucDBBjgBgDxBggxgAxBkizt7Y1SCezJU4GiDFAdrFMjZeeVv8idzJAjAFiDBBjgBgDxBgg/RHeIcFnbVXNGCDGAEk/Dtni6KJmHyCd6DoZIMYAMQZIyJ4RfaTtEboWMwbIJsvUWsvE3FvNub9//H3Rt7pOBogxQIwBkn4cssW6XPqZ5NteJwPEGCDpy1S0miUs+kTXyQAxBogxQDbZM/byIHI0JwPEGCDhD7G18kBZ18Vfq5MBYgwQY4CgjkOyX2nL5mSAGAMk/f8zpv51nr1kRdzmOhkgxgAxBkj6rW0rb/zoK30HYwyQ9GVqKPv2NZuTAWIMEGOAGAPEGCDGADEGiDFAjAFiDBBjgBgDxBggxgAxBogxQIwB8gXxbFVDZFuq/wAAAABJRU5ErkJggg==\" id=\"image4d15bd59ac\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"518.875472\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 518.875472 93.384163 \n", "L 518.875472 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 589.941509 93.384163 \n", "L 589.941509 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 518.875472 93.384163 \n", "L 589.941509 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 518.875472 22.318125 \n", "L 589.941509 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- 4 -->\n", "    <g transform=\"translate(550.590991 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-34\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 604.154717 93.384163 \n", "L 675.220755 93.384163 \n", "L 675.220755 22.318125 \n", "L 604.154717 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p1aced902f4)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABtElEQVR4nO3cy03DQBRAUYKogiooAokGaJYuqII2YAeWF6M48uc43LNiQxTp6r0ZHODyenn/fgjh8eg3kD/FgBQDUgxIMSDFgDwd/QbO6uPr8/frt+eXVV6zyYAUA1IMSGfGlaZnxFaaDEgxIMWAFANSDEgxIF1tB0bX2bUegUw1GZBiQIoBKQakGJBiQLraTux9lZ1rMiDFgPz7NXX0appqMiDFgBQDUgxIMSDFgNBX2y2unXv8/tOtmgxIMSDFgFBnxpJ9vuTvI6593b0ff8w1GZBiQA5fU2tcNeevcfS6uVWTASkGpBiQw8+MkdHuH501S84h6XxpMiDFgBQDsvuZsdYj7Omulx+LL9FkQIoB2X1Nza+SW/x3mrNqMiDFgBQDQj0OuZdH4bdqMiDFgFBram6Ln6zl1ddkQIoBKQaEPjPWIp8TU00GpBiQw9fUFh8SnWUtzTUZkGJAigE5/MyYGn0KuOT7zqrJgBQDQq2puXv83aiRJgNSDEgxIPSZMXUv19eRJgNSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWA/ADPBESC9PqksAAAAABJRU5ErkJggg==\" id=\"image7623fef9db\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"604.154717\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 604.154717 93.384163 \n", "L 604.154717 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 675.220755 93.384163 \n", "L 675.220755 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 604.154717 93.384163 \n", "L 675.220755 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 604.154717 22.318125 \n", "L 675.220755 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_8\">\n", "    <!-- 9 -->\n", "    <g transform=\"translate(635.870236 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-39\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 689.433962 93.384163 \n", "L 760.5 93.384163 \n", "L 760.5 22.318125 \n", "L 689.433962 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p5783b3cd7c)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABqklEQVR4nO3cy00DMRRAUYKogiooAokGaJYuqII2YMcnq8woYx+Te3ZICA26erZjJTk9n14/70K4n/0A+VEMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA/Iw+wGO8Pbx/ufnl8enKc+xVZMBKQakGJBl94zzfWHv70r7SZMBKQZkmWVqy7K09+/OXrKaDEgxIMWALLNnjDD7GqXJgBQD8i+XqfPl5ahj8bU1GZBiQIoBofeMvWv9KnvEuSYDUgzIkGVqlWWjW9t8KwakGJCr7Rmr7AuyJgNSDAj9Cnz2UXO0JgNSDEgxIMP3jFvbB7ZoMiDFgBQDUgxIMSDFgFztaPv7yNoN7j5NBqQYkGJAhl+HzP4MhKzJgBQDchrxXeiXHnVvfclqMiDFgBQDMuRo21XJZZoMSDEg09+QMPqjYvLxucmAFANSDMj0Nz6PXsNHH623/H9NBqQYkGJAigEpBqQYkOlH2722HBlXuSluMiDFgCy7TG1xxKv8I75DvcmAFANSDMhN7BlHOGIfajIgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgX8VFQBl3llO1AAAAAElFTkSuQmCC\" id=\"image04d4b72692\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"689.433962\" y=\"-22.104163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 689.433962 93.384163 \n", "L 689.433962 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 760.5 93.384163 \n", "L 760.5 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_45\">\n", "    <path d=\"M 689.433962 93.384163 \n", "L 760.5 93.384163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_46\">\n", "    <path d=\"M 689.433962 22.318125 \n", "L 760.5 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_9\">\n", "    <!-- 4 -->\n", "    <g transform=\"translate(721.149481 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-34\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_10\">\n", "   <g id=\"patch_47\">\n", "    <path d=\"M 7.2 184.104163 \n", "L 78.266038 184.104163 \n", "L 78.266038 113.038125 \n", "L 7.2 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p9d5ace9503)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABu0lEQVR4nO3bUS4EQRRAUSNWYRUWIbEBm7ULq7ANPrUOE92i6vTMPX/iwyQ371Vp7fR4en6/CeF29gfIp2JAigG5m/0Bll7eXr98/XT/MOVzzNJkQIoBmb6m1qvpp+9dw8pqMiDFgBQDMv3M+K1ruPY2GZBiQKavqeW6OXfNvQZNBqQYkGJApp8Ze13io5ImA1IMyEl+IWHvVfeoa6vJgBQDUgxIMSDFgBQDQl9tl/7yRPcoV90mA1IMSDEghzkz1i7xUUmTASkG5LB/XLrEFxmaDEgxIMWAHPbM2Et+Z7fJgBQDMn1Nzb6Wnvv5o1dYkwEpBqQYkCFnxuxzYa/R7/M2GZBiQIoBmf57xtJ6L0tnzYjHKE0GpBgQak2tjbhO7l2F/3HtbTIgxYAUAzLkzPjtmxwjHmdvuaKOvlo3GZBiQOir7dKMFTZakwEpBqQYEOr/M2bv7C16anvhigGhrrbSb8MzNBmQYkCKAaHOjHO2XCW3nC/9f0a+VQzIYdbUFtLq2aLJgBQDUgxIMSDFgBQDUgxIMSDFgBQDUgxIMSDFgBQDUgxIMSDFgBQDUgxIMSDFgBQDUgxIMSAfi6RbJ3I8aEoAAAAASUVORK5CYII=\" id=\"image4d6ebd4888\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"7.2\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_48\">\n", "    <path d=\"M 7.2 184.104163 \n", "L 7.2 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_49\">\n", "    <path d=\"M 78.266038 184.104163 \n", "L 78.266038 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_50\">\n", "    <path d=\"M 7.2 184.104163 \n", "L 78.266038 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_51\">\n", "    <path d=\"M 7.2 113.038125 \n", "L 78.266038 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_10\">\n", "    <!-- 9 -->\n", "    <g transform=\"translate(38.915519 107.038125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-39\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_11\">\n", "   <g id=\"patch_52\">\n", "    <path d=\"M 92.479245 184.104163 \n", "L 163.545283 184.104163 \n", "L 163.545283 113.038125 \n", "L 92.479245 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p3c3f0b213d)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABqElEQVR4nO3c0U0CQRRAUTFWYRUWYWIDNmsXVmEb+umEjw2YZebg3vNrDCQ37w0ssKfX0/v3QwiPq59AfhUDUgxIMSDFgBQDUgxIMSDFgBQDUgxIMSDFgDytfgIfX583f4y355ebP8YemgxIMSDFgEw/M2acEdc8pnSeNBmQYkCmrKkVq+lS0gprMiDFgBQDsvxyyIy9LJ9ZoyYDUgzI8jU1w9Yq3Fph499mrNMmA1IMSDEghzgztoxnwaXnx/n/7aXJgBQDcvg1NTpfPbPfuTcZkGJAigHpzPijW1wqaTIgxYC0pjZc+u58L00GpBiQYkCKASkGpBiQYkCKASkGpBiQKZdDti4rzP6imKzJgBQDUgxIMSDFgBQD0id9G/oS24EVA1IMyPQzY+vLxTN+AyFrMiDFgPTSdnDNS9l+RvbPFQNSDAh9Zsz4FFC6406TASkGhF5To9XrpLvqHEwxIMWALD8zZv9u7hrdZPjAigFZvqZGe62Fe/2QqsmAFANSDAh1ZuzlXs6Ic00GpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKASkG5AejjknxMTTQFgAAAABJRU5ErkJggg==\" id=\"image4fbabdef3c\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"92.479245\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_53\">\n", "    <path d=\"M 92.479245 184.104163 \n", "L 92.479245 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_54\">\n", "    <path d=\"M 163.545283 184.104163 \n", "L 163.545283 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_55\">\n", "    <path d=\"M 92.479245 184.104163 \n", "L 163.545283 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_56\">\n", "    <path d=\"M 92.479245 113.038125 \n", "L 163.545283 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <!-- 0 -->\n", "    <g transform=\"translate(124.194764 107.038125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-30\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_12\">\n", "   <g id=\"patch_57\">\n", "    <path d=\"M 177.758491 184.104163 \n", "L 248.824528 184.104163 \n", "L 248.824528 113.038125 \n", "L 177.758491 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p5866d6fa1e)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAAByUlEQVR4nO3cwa3CMBAAUYKogiooAokGaJYuqII24IjlwyoJ9nqM5t0/QRqtbSX5LNfl/j4I4Tj6C+jLGCDGADEGiDFAjAFiDBBjgBgDxBggxgAxBogxQIwBYgwQY4AYA+Q0+gtEHq9nk8+5nS9NPqc3JwPEGCDDl6lWS9Hea5CWMCcDxBggxgBJ3zO27BFr1/Nf9p3yb0fvH04GiDFAlox3bdcuIxnLRI9lshUnA8QYIMYAGX47ZO+6vHftj65Xf2b2sdfJADEGyPBlaq2MO6/155TXrK/fY9lyMkCMAWIMEPSekfEUMFLuCxnHXicDxBggxgAxBogxQIwBgj7aRkfLUsatigxOBogxQIwBgt4z9pp1D3EyQIwBkvISW6nVE7vsfzHLeNLoZIAYA8QYINMebbe8jBYZ/TSx5GSAGANk2mUqEr2M1usaLTgZIMYAMQYIas/odbfVu7bazBgg6ctUxrFzVk4GiDFAjAFiDBBjgBgDxBggxgAxBogxQNJfYovM8mPAvTgZIMYAQT1cisz6mv8WTgaIMUCMAYI62tZIP06cwckAMQYI+mj7L8vPWk4GiDFAjAFiDBBjgBgDxBggxgAxBogxQD5UcmXWYUskPwAAAABJRU5ErkJggg==\" id=\"image206cb88566\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"177.758491\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_58\">\n", "    <path d=\"M 177.758491 184.104163 \n", "L 177.758491 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_59\">\n", "    <path d=\"M 248.824528 184.104163 \n", "L 248.824528 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_60\">\n", "    <path d=\"M 177.758491 184.104163 \n", "L 248.824528 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_61\">\n", "    <path d=\"M 177.758491 113.038125 \n", "L 248.824528 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <!-- 6 -->\n", "    <g transform=\"translate(209.474009 107.038125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-36\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_13\">\n", "   <g id=\"patch_62\">\n", "    <path d=\"M 263.037736 184.104163 \n", "L 334.103774 184.104163 \n", "L 334.103774 113.038125 \n", "L 263.037736 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p8596fbea8f)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABpUlEQVR4nO3cy03EQBBAQRYRBVEQBBIJkCxZEAVpwBHjg2G/U2Ne3Thh6anb9mBxeD68ft6FcD/6AvKtGJBiQB5GX8AlvH28//j55fFpyHWcq8mAFAMy7Zpar6Y9aDIgxYAUA1IMSDEgxYAc9nBq+9tj7ixv5E0GpBiQYkCKASkGpBiQYkCKASkGpBiQaf/Sd4zlcYl8NNJkQIoB2cWaWq+eWT9WaDIgxYAUA7KLe8Yx5O9ymwxIMSDFgBQDUgxIMSDDH22vcXQx6/FIkwEpBuTm39rKK2P023iTASkGpBiQ4Y+2W07d4fJ9aUuTASkGpBgQ+p5xqo5DcrZiQHa5ptaWa2trZY3+WKHJgBQDUgzI8H9X8dfHzmvt763f3z3jHysGZPiaWjrmTflSK2T0mlxqMiDFgBQDQt0z1m69z0c/5jYZkGJA6FPbU09bZ9VkQIoBKQaEvmcsXesjg9EfOy81GZBiQKZZU2vSermUJgNSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAfAGTN0vYtGK4+QAAAABJRU5ErkJggg==\" id=\"imagec9f16853f2\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"263.037736\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_63\">\n", "    <path d=\"M 263.037736 184.104163 \n", "L 263.037736 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_64\">\n", "    <path d=\"M 334.103774 184.104163 \n", "L 334.103774 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_65\">\n", "    <path d=\"M 263.037736 184.104163 \n", "L 334.103774 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_66\">\n", "    <path d=\"M 263.037736 113.038125 \n", "L 334.103774 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_13\">\n", "    <!-- 9 -->\n", "    <g transform=\"translate(294.753255 107.038125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-39\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_14\">\n", "   <g id=\"patch_67\">\n", "    <path d=\"M 348.316981 184.104163 \n", "L 419.383019 184.104163 \n", "L 419.383019 113.038125 \n", "L 348.316981 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p01fe41870a)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABt0lEQVR4nO3cwU3DQBRAwQRRBVVQBBIN0CxdUAVtwBHjgyHG7E6UN1cUxdLTXy/rJOen88vHKYS72ReQL8WAFANSDEgxIMWAFANSDEgxIMWA3M++gL1e3992ve754fHQ6zhSkwEpBoRepn67FK2Xnq3Xbf1t9hLWZECKASkG5Cw96btku7p3fR/xHns1GZBiQKZvbUdvNS/ZBo/WZECKASkGZPo9Q7a8n4zY5jYZkGJAhi9T0lbydPq+/My+tiYDUgxIMSBD7hl7n9jdmiYDUgxI/4EvbJ3orpfa/1hSmwxIMSDFgBQDUgxIMSDFgBQDUgxIMSBDjkOkp2myJgNSDEgxINQR+ohjalmTASkGpBiQYkCKASkGhNraro3+fsTso5omA1IMyPBlSvvq7+z3X2oyIMWAFANCb22XZp/o9tXjG1MMyNX++NfST0vItfwGbpMBKQakGBBqa7v3qEQ60viLJgNSDAi1TK3d2md0mwxIMSDFgND3jKWto4mj7iezP9vbZECKAbmaZWrL7OXlKE0GpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKASkGpBiQYkCKASkG5BMfSlWKt4vqnQAAAABJRU5ErkJggg==\" id=\"image4eeefad75f\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"348.316981\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_68\">\n", "    <path d=\"M 348.316981 184.104163 \n", "L 348.316981 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_69\">\n", "    <path d=\"M 419.383019 184.104163 \n", "L 419.383019 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_70\">\n", "    <path d=\"M 348.316981 184.104163 \n", "L 419.383019 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_71\">\n", "    <path d=\"M 348.316981 113.038125 \n", "L 419.383019 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <!-- 0 -->\n", "    <g transform=\"translate(380.0325 107.038125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-30\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_15\">\n", "   <g id=\"patch_72\">\n", "    <path d=\"M 433.596226 184.104163 \n", "L 504.662264 184.104163 \n", "L 504.662264 113.038125 \n", "L 433.596226 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pfb244210de)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABXElEQVR4nO3awUnEUBRAUSNWYRUWMTAN2KxdWIVt6HI+gQkG0Zxv7tnOJnB5eX+SLJfl9fMhhMejLyA3xYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgT0dfwHe9fbzf/e36/PJn1/GbmgxIMSDFgEyzM7as98msO6TJgBQD8i9uU2uzHoObDEgxIMWATLMz1vf6rb0wqyYDUgxIMSDFgBQDUgxIMSDFgBQDMs0/8J+Qn9SOmgxIMSDFgBQDUgxIMSDFgBQDUgxIMSDFgBQDUgxIMSCneIQ+fvAmP05vMiDFgBQDUgxIMSDFgBQDUgxIMSDFgBQDUgxIMSDFgBQDUgzIKV4uyS+URk0GpBiQYkCKASkGpBiQUxxt+24quxUDUgzIKXaGvCdGTQakGJBiQIoBKQakGJBpj7azHFf3aDIgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgX8OSFrYMX0SPAAAAAElFTkSuQmCC\" id=\"imagefd9eb90ad7\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"433.596226\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_73\">\n", "    <path d=\"M 433.596226 184.104163 \n", "L 433.596226 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_74\">\n", "    <path d=\"M 504.662264 184.104163 \n", "L 504.662264 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_75\">\n", "    <path d=\"M 433.596226 184.104163 \n", "L 504.662264 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_76\">\n", "    <path d=\"M 433.596226 113.038125 \n", "L 504.662264 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_15\">\n", "    <!-- 1 -->\n", "    <g transform=\"translate(465.311745 107.038125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-31\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_16\">\n", "   <g id=\"patch_77\">\n", "    <path d=\"M 518.875472 184.104163 \n", "L 589.941509 184.104163 \n", "L 589.941509 113.038125 \n", "L 518.875472 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pa2cf69b8ff)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABnklEQVR4nO3cwU0DMRRAQRZRBVVQBFIaoFm6oAragCOrHCyyJPaIvLlyyEpP37YSL9vr9vb1EMLj6gfIj2JAigEpBqQYkGJAigEpBqQYkGJAigEpBqQYkKfVD/D++TH1807PL1M/7xJNBqQYkGJApu8Zs/eIa33+jL2myYAUA7L8aLu3+tg5WsLO/3aLZ20yIMWAFAOydYntd0b7ybX2jyYDUgxIy9RBt1i2mgxIMSDFgBQDUgxIMSDFgBQDUgxIMSDUL32yvrW9M8WAtEwNzFia9poMSDEgxYC0Z+zM3iPONRmQYkCKAaHez5ixLl/yfsbsi9hNBqQYEOpoe3QJ+8uraavfCdlrMiDFgBQDsvzi84xXkaV9YaTJgBQDsvxou19CZrzeK2syIMWAFANSDEgxIMWALD/arv5nYJImA1IMSDEg1IWEe9dkQIoBWX60HVl9x2q2JgNSDEgxIHdxIWFP3muaDEgxIMuXqaP+432rJgNSDEgxIPTXISPyEfWoJgNSDEgxIMWAFANSDEgxIMWAFANSDEgxIMWAFAPyDetvSNqnkTjLAAAAAElFTkSuQmCC\" id=\"image52c2e6a12c\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"518.875472\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_78\">\n", "    <path d=\"M 518.875472 184.104163 \n", "L 518.875472 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_79\">\n", "    <path d=\"M 589.941509 184.104163 \n", "L 589.941509 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_80\">\n", "    <path d=\"M 518.875472 184.104163 \n", "L 589.941509 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_81\">\n", "    <path d=\"M 518.875472 113.038125 \n", "L 589.941509 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_16\">\n", "    <!-- 3 -->\n", "    <g transform=\"translate(550.590991 107.038125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-33\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_17\">\n", "   <g id=\"patch_82\">\n", "    <path d=\"M 604.154717 184.104163 \n", "L 675.220755 184.104163 \n", "L 675.220755 113.038125 \n", "L 604.154717 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p2254b5c673)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABrUlEQVR4nO3dwU3DUBQAQYKogiooAikN0CxdUAVtwBHLh8iOif842TmDsLR63y/GiNP76ePnKYTn0ReQP8WAFANSDEgxIMWAvIy+gP/w+f21+GvPr283u46tmgxIMSDFgBQDUgxIMSB3sdrO19U1q66kyYAUA1IMSDEgxYAUA3IXq+0a87VXeorbZECKASkGpBiQYkCKAbnL1faoT3GbDEgxIMWAFANSDEgxIMNX21usnWuexE5//ugnuE0GpBiQYkB2v2dc+7cUa77vKI8/5poMSDEguxxTl46NpevklrXzKMdWkwEpBqQYkOGPQ/awdEUe/YJbkwEpBqQYkGJAigEpBuQhVtsp+QW3JgNSDEgxIMPvGdLbGXN7X1uTASkGZJdj6toXCx5NkwEpBqQYkOGr7dSI37RJ97MmA1IMyGn0v/lZejTc6iU26VN/kwEpBqQYkOH3jKk9VkvpHjHXZECKAaE+gV86QrYcYfLRNNVkQIoBKQaEumdccpRzf4smA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYAUA1IMSDEgxYD8Ak6+SqiodFgyAAAAAElFTkSuQmCC\" id=\"image3d4b982ca4\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"604.154717\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_83\">\n", "    <path d=\"M 604.154717 184.104163 \n", "L 604.154717 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_84\">\n", "    <path d=\"M 675.220755 184.104163 \n", "L 675.220755 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_85\">\n", "    <path d=\"M 604.154717 184.104163 \n", "L 675.220755 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_86\">\n", "    <path d=\"M 604.154717 113.038125 \n", "L 675.220755 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_17\">\n", "    <!-- 9 -->\n", "    <g transform=\"translate(635.870236 107.038125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-39\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_18\">\n", "   <g id=\"patch_87\">\n", "    <path d=\"M 689.433962 184.104163 \n", "L 760.5 184.104163 \n", "L 760.5 113.038125 \n", "L 689.433962 113.038125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p853dcc8bdc)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAABo0lEQVR4nO3cwU3DQBBAUYKogiooAokGaJYuqII24EgUCSuJjP12/d+VS6SvGa83wOn19P79EMLj3h8gv4oBKQbkae8PsOTj6/PPn709v2z2ObbSZECKAaHX1JLzFTbLymoyIMWAFAMy7DPj3OUReNRnSJMBKQaEXlPn62bpbXwWTQakGJBiQE6jftN3yzNklKNukwEpBoQ+2q5llBveJgNSDEgxIMM+M2a8KmkyIMWADPsGvmTUt/MmA1IMSDEgwx5t1yJdlTQZkGJAplxTl+tmlDf0JgNSDEgxIFNehyyRr0qaDEgxIIdbU5euXVtbrKwmA1IMSDEgxYAUA1IMSDEgU16h30L6ZbgmA1IMSDEgxYAUA1IMyOGPttceZ7f4zz1NBqQYkEOsqTXerPum72CKASkG5F+eGfceA/e+Nd1bkwEpBmS1NbW0YqT1s/efii1pMiDFgBQDMux1iLz779VkQIoBWW1Nzbg2ttZkQIoBKQakGJBiQIoBKQakGJBiQIoBKQakGJBiQIoBKQakGJBiQIoBKQakGJBiQIoBKQakGJAfOOhER2UJh9UAAAAASUVORK5CYII=\" id=\"imagea828c2e6a1\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"689.433962\" y=\"-112.824163\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_88\">\n", "    <path d=\"M 689.433962 184.104163 \n", "L 689.433962 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_89\">\n", "    <path d=\"M 760.5 184.104163 \n", "L 760.5 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_90\">\n", "    <path d=\"M 689.433962 184.104163 \n", "L 760.5 184.104163 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_91\">\n", "    <path d=\"M 689.433962 113.038125 \n", "L 760.5 113.038125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- 7 -->\n", "    <g transform=\"translate(721.149481 107.038125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-37\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pe133959d7c\">\n", "   <rect x=\"7.2\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p3f524da8ce\">\n", "   <rect x=\"92.479245\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"paf7a73611a\">\n", "   <rect x=\"177.758491\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pfb0625c05e\">\n", "   <rect x=\"263.037736\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pb13ae2d142\">\n", "   <rect x=\"348.316981\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pd8ac015241\">\n", "   <rect x=\"433.596226\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p011cf127be\">\n", "   <rect x=\"518.875472\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p1aced902f4\">\n", "   <rect x=\"604.154717\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p5783b3cd7c\">\n", "   <rect x=\"689.433962\" y=\"22.318125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p9d5ace9503\">\n", "   <rect x=\"7.2\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p3c3f0b213d\">\n", "   <rect x=\"92.479245\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p5866d6fa1e\">\n", "   <rect x=\"177.758491\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p8596fbea8f\">\n", "   <rect x=\"263.037736\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p01fe41870a\">\n", "   <rect x=\"348.316981\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pfb244210de\">\n", "   <rect x=\"433.596226\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pa2cf69b8ff\">\n", "   <rect x=\"518.875472\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p2254b5c673\">\n", "   <rect x=\"604.154717\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p853dcc8bdc\">\n", "   <rect x=\"689.433962\" y=\"113.038125\" width=\"71.066038\" height=\"71.066038\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1350x300 with 18 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def predict(X):\n", "    return [bayes_pred_stable(x).argmax(dim=0).type(torch.int32).item()\n", "            for x in X]\n", "\n", "X = torch.stack([mnist_test[i][0] for i in range(18)], dim=0)\n", "y = torch.tensor([mnist_test[i][1] for i in range(18)])\n", "preds = predict(X)\n", "d2l.show_images(X, 2, 9, titles=[str(d) for d in preds]);"]}, {"cell_type": "markdown", "id": "69798bb5", "metadata": {"origin_pos": 52}, "source": ["Finally, let's compute the overall accuracy of the classifier.\n"]}, {"cell_type": "code", "execution_count": 15, "id": "a53803ee", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:14:41.452176Z", "iopub.status.busy": "2023-08-18T20:14:41.451579Z", "iopub.status.idle": "2023-08-18T20:14:46.019472Z", "shell.execute_reply": "2023-08-18T20:14:46.018486Z"}, "origin_pos": 54, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["0.8427"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.stack([mnist_test[i][0] for i in range(len(mnist_test))], dim=0)\n", "y = torch.tensor([mnist_test[i][1] for i in range(len(mnist_test))])\n", "preds = torch.tensor(predict(X), dtype=torch.int32)\n", "float((preds == y).sum()) / len(y)  # Validation accuracy"]}, {"cell_type": "markdown", "id": "141abe1d", "metadata": {"origin_pos": 56}, "source": ["Modern deep networks achieve error rates of less than $0.01$. The relatively poor performance is due to the incorrect statistical assumptions that we made in our model: we assumed that each and every pixel are *independently* generated, depending only on the label. This is clearly not how humans write digits, and this wrong assumption led to the downfall of our overly naive (Bayes) classifier.\n", "\n", "## Summary\n", "* Using <PERSON><PERSON>' rule, a classifier can be made by assuming all observed features are independent.\n", "* This classifier can be trained on a dataset by counting the number of occurrences of combinations of labels and pixel values.\n", "* This classifier was the gold standard for decades for tasks such as spam detection.\n", "\n", "## Exercises\n", "1. Consider the dataset $[[0,0], [0,1], [1,0], [1,1]]$ with labels given by the XOR of the two elements $[0,1,1,0]$.  What are the probabilities for a Naive <PERSON>es classifier built on this dataset.  Does it successfully classify our points?  If not, what assumptions are violated?\n", "1. Suppose that we did not use Laplace smoothing when estimating probabilities and a data example arrived at testing time which contained a value never observed in training.  What would the model output?\n", "1. The naive <PERSON><PERSON> classifier is a specific example of a Bayesian network, where the dependence of random variables are encoded with a graph structure.  While the full theory is beyond the scope of this section (see :citet:`Koller.Friedman.2009` for full details), explain why allowing explicit dependence between the two input variables in the XOR model allows for the creation of a successful classifier.\n"]}, {"cell_type": "markdown", "id": "3b834883", "metadata": {"origin_pos": 58, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1100)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
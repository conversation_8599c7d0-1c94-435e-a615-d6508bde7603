{"cells": [{"cell_type": "markdown", "id": "602cd834", "metadata": {"origin_pos": 0}, "source": ["# Information Theory\n", ":label:`sec_information_theory`\n", "\n", "The universe is overflowing with information. Information provides a common language across disciplinary rifts: from <PERSON>'s Sonnet to researchers' paper on Cornell ArXiv, from <PERSON>'s printing Starry Night to <PERSON>'s music Symphony No. 5, from the first programming language Plankalkül to the state-of-the-art machine learning algorithms. Everything must follow the rules of information theory, no matter the format. With information theory, we can measure and compare how much information is present in different signals. In this section, we will investigate the fundamental concepts of information theory and applications of information theory in machine learning.\n", "\n", "Before we get started, let's outline the relationship between machine learning and information theory. Machine learning aims to extract interesting signals from data and make critical predictions.  On the other hand, information theory studies encoding, decoding, transmitting, and manipulating information. As a result, information theory provides fundamental language for discussing the information processing in machine learned systems. For example, many machine learning applications use the cross-entropy loss as described in :numref:`sec_softmax`.  This loss can be directly derived from information theoretic considerations.\n", "\n", "\n", "## Information\n", "\n", "Let's start with the \"soul\" of information theory: information. *Information* can be encoded in anything with a particular sequence of one or more encoding formats. Suppose that we task ourselves with trying to define a notion of information.  What could be our starting point?\n", "\n", "Consider the following thought experiment.  We have a friend with a deck of cards.  They will shuffle the deck, flip over some cards, and tell us statements about the cards.  We will try to assess the information content of each statement.\n", "\n", "First, they flip over a card and tell us, \"I see a card.\"  This provides us with no information at all.  We were already certain that this was the case so we hope the information should be zero.\n", "\n", "Next, they flip over a card and say, \"I see a heart.\"  This provides us some information, but in reality there are only $4$ different suits that were possible, each equally likely, so we are not surprised by this outcome.  We hope that whatever the measure of information, this event should have low information content.\n", "\n", "Next, they flip over a card and say, \"This is the $3$ of spades.\"  This is more information.  Indeed there were $52$ equally likely possible outcomes, and our friend told us which one it was.  This should be a medium amount of information.\n", "\n", "Let's take this to the logical extreme.  Suppose that finally they flip over every card from the deck and read off the entire sequence of the shuffled deck.  There are $52!$ different orders to the deck, again all equally likely, so we need a lot of information to know which one it is.\n", "\n", "Any notion of information we develop must conform to this intuition.  Indeed, in the next sections we will learn how to compute that these events have $0\\textrm{ bits}$, $2\\textrm{ bits}$, $~5.7\\textrm{ bits}$, and $~225.6\\textrm{ bits}$ of information respectively.\n", "\n", "If we read through these thought experiments, we see a natural idea.  As a starting point, rather than caring about the knowledge, we may build off the idea that information represents the degree of surprise or the abstract possibility of the event. For example, if we want to describe an unusual event, we need a lot information. For a common event, we may not need much information.\n", "\n", "In 1948, <PERSON> published *A Mathematical Theory of Communication* :cite:<PERSON><PERSON>.1948` establishing the theory of information.  In his article, <PERSON> introduced the concept of information entropy for the first time. We will begin our journey here.\n", "\n", "\n", "### Self-information\n", "\n", "Since information embodies the abstract possibility of an event, how do we map the possibility to the number of bits? <PERSON> introduced the terminology *bit* as the unit of information, which was originally created by <PERSON>. So what is a \"bit\" and why do we use it to measure information? Historically, an antique transmitter can only send or receive two types of code: $0$ and $1$.  Indeed, binary encoding is still in common use on all modern digital computers. In this way, any information is encoded by a series of $0$ and $1$. And hence, a series of binary digits of length $n$ contains $n$ bits of information.\n", "\n", "Now, suppose that for any series of codes, each $0$ or $1$ occurs with a probability of $\\frac{1}{2}$. Hence, an event $X$ with a series of codes of length $n$, occurs with a probability of $\\frac{1}{2^n}$. At the same time, as we mentioned before, this series contains $n$ bits of information. So, can we generalize to a mathematical function which can transfer the probability $p$ to the number of bits? <PERSON> gave the answer by defining *self-information*\n", "\n", "$$I(X) = - \\log_2 (p),$$\n", "\n", "as the *bits* of information we have received for this event $X$. Note that we will always use base-2 logarithms in this section. For the sake of simplicity, the rest of this section will omit the subscript 2 in the logarithm notation, i.e., $\\log(.)$ always refers to $\\log_2(.)$. For example, the code \"0010\" has a self-information\n", "\n", "$$I(\\textrm{\"0010\"}) = - \\log (p(\\textrm{\"0010\"})) = - \\log \\left( \\frac{1}{2^4} \\right) = 4 \\textrm{ bits}.$$\n", "\n", "We can calculate self information as shown below. Before that, let's first import all the necessary packages in this section.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "37a675a0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:33.218476Z", "iopub.status.busy": "2023-08-18T19:28:33.217762Z", "iopub.status.idle": "2023-08-18T19:28:34.943195Z", "shell.execute_reply": "2023-08-18T19:28:34.942344Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["6.0"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "from torch.nn import NLLLoss\n", "\n", "\n", "def nansum(x):\n", "    # Define nansum, as pytorch does not offer it inbuilt.\n", "    return x[~torch.isnan(x)].sum()\n", "\n", "def self_information(p):\n", "    return -torch.log2(torch.tensor(p)).item()\n", "\n", "self_information(1 / 64)"]}, {"cell_type": "markdown", "id": "65280edf", "metadata": {"origin_pos": 4}, "source": ["## Entropy\n", "\n", "As self-information only measures the information of a single discrete event, we need a more generalized measure for any random variable of either discrete or continuous distribution.\n", "\n", "\n", "### Motivating Entropy\n", "\n", "Let's try to get specific about what we want.  This will be an informal statement of what are known as the *axioms of Shannon entropy*.  It will turn out that the following collection of common-sense statements force us to a unique definition of information.  A formal version of these axioms, along with several others may be found in :citet:`Csiszar.2008`.\n", "\n", "1.  The information we gain by observing a random variable does not depend on what we call the elements, or the presence of additional elements which have probability zero.\n", "2.  The information we gain by observing two random variables is no more than the sum of the information we gain by observing them separately.  If they are independent, then it is exactly the sum.\n", "3.  The information gained when observing (nearly) certain events is (nearly) zero.\n", "\n", "While proving this fact is beyond the scope of our text, it is important to know that this uniquely determines the form that entropy must take.  The only ambiguity that these allow is in the choice of fundamental units, which is most often normalized by making the choice we saw before that the information provided by a single fair coin flip is one bit.\n", "\n", "### Definition\n", "\n", "For any random variable $X$ that follows a probability distribution $P$ with a probability density function (p.d.f.) or a probability mass function (p.m.f.) $p(x)$, we measure the expected amount of information through *entropy* (or *Shannon entropy*)\n", "\n", "$$H(X) = - E_{x \\sim P} [\\log p(x)].$$\n", ":eqlabel:`eq_ent_def`\n", "\n", "To be specific, if $X$ is discrete, $$H(X) = - \\sum_i p_i \\log p_i \\textrm{, where } p_i = P(X_i).$$\n", "\n", "Otherwise, if $X$ is continuous, we also refer entropy as *differential entropy*\n", "\n", "$$H(X) = - \\int_x p(x) \\log p(x) \\; dx.$$\n", "\n", "We can define entropy as below.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "f1ec60ff", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:34.946659Z", "iopub.status.busy": "2023-08-18T19:28:34.946277Z", "iopub.status.idle": "2023-08-18T19:28:34.953992Z", "shell.execute_reply": "2023-08-18T19:28:34.953211Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor(1.6855)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["def entropy(p):\n", "    entropy = - p * torch.log2(p)\n", "    # Operator `nansum` will sum up the non-nan number\n", "    out = nansum(entropy)\n", "    return out\n", "\n", "entropy(torch.tensor([0.1, 0.5, 0.1, 0.3]))"]}, {"cell_type": "markdown", "id": "f45c350c", "metadata": {"origin_pos": 8}, "source": ["### Interpretations\n", "\n", "You may be curious: in the entropy definition :eqref:`eq_ent_def`, why do we use an expectation of a negative logarithm? Here are some intuitions.\n", "\n", "First, why do we use a *logarithm* function $\\log$? Suppose that $p(x) = f_1(x) f_2(x) \\ldots, f_n(x)$, where each component function $f_i(x)$ is independent from each other. This means that each $f_i(x)$ contributes independently to the total information obtained from $p(x)$. As discussed above, we want the entropy formula to be additive over independent random variables. Luckily, $\\log$ can naturally turn a product of probability distributions to a summation of the individual terms.\n", "\n", "Next, why do we use a *negative* $\\log$? Intuitively, more frequent events should contain less information than less common events, since we often gain more information from an unusual case than from an ordinary one. However, $\\log$ is monotonically increasing with the probabilities, and indeed negative for all values in $[0, 1]$.  We need to construct a monotonically decreasing relationship between the probability of events and their entropy, which will ideally be always positive (for nothing we observe should force us to forget what we have known). Hence, we add a negative sign in front of $\\log$ function.\n", "\n", "Last, where does the *expectation* function come from? Consider a random variable $X$. We can interpret the self-information ($-\\log(p)$) as the amount of *surprise* we have at seeing a particular outcome.  Indeed, as the probability approaches zero, the surprise becomes infinite.  Similarly, we can interpret the entropy as the average amount of surprise from observing $X$. For example, imagine that a slot machine system emits statistical independently symbols ${s_1, \\ldots, s_k}$ with probabilities ${p_1, \\ldots, p_k}$ respectively. Then the entropy of this system equals to the average self-information from observing each output, i.e.,\n", "\n", "$$H(S) = \\sum_i {p_i \\cdot I(s_i)} = - \\sum_i {p_i \\cdot \\log p_i}.$$\n", "\n", "\n", "\n", "### Properties of Entropy\n", "\n", "By the above examples and interpretations, we can derive the following properties of entropy :eqref:`eq_ent_def`. Here, we refer to $X$ as an event and $P$ as the probability distribution of $X$.\n", "\n", "* $H(X) \\geq 0$ for all discrete $X$ (entropy can be negative for continuous $X$).\n", "\n", "* If $X \\sim P$ with a p.d.f. or a p.m.f. $p(x)$, and we try to estimate $P$ by a new probability distribution $Q$ with a p.d.f. or a p.m.f. $q(x)$, then $$H(X) = - E_{x \\sim P} [\\log p(x)] \\leq  - E_{x \\sim P} [\\log q(x)], \\textrm{ with equality if and only if } P = Q.$$  Alternatively, $H(X)$ gives a lower bound of the average number of bits needed to encode symbols drawn from $P$.\n", "\n", "* If $X \\sim P$, then $x$ conveys the maximum amount of information if it spreads evenly among all possible outcomes. Specifically, if the probability distribution $P$ is discrete with $k$-class $\\{p_1, \\ldots, p_k \\}$, then $$H(X) \\leq \\log(k), \\textrm{ with equality if and only if } p_i = \\frac{1}{k}, \\forall i.$$ If $P$ is a continuous random variable, then the story becomes much more complicated.  However, if we additionally impose that $P$ is supported on a finite interval (with all values between $0$ and $1$), then $P$ has the highest entropy if it is the uniform distribution on that interval.\n", "\n", "\n", "## Mutual Information\n", "\n", "Previously we defined entropy of a single random variable $X$, how about the entropy of a pair random variables $(X, Y)$?  We can think of these techniques as trying to answer the following type of question, \"What information is contained in $X$ and $Y$ together compared to each separately?  Is there redundant information, or is it all unique?\"\n", "\n", "For the following discussion, we always use $(X, Y)$ as a pair of random variables that follows a joint probability distribution $P$ with a p.d.f. or a p.m.f. $p_{X, Y}(x, y)$, while $X$ and $Y$ follow probability distribution $p_X(x)$ and $p_Y(y)$, respectively.\n", "\n", "\n", "### Joint Entropy\n", "\n", "Similar to entropy of a single random variable :eqref:`eq_ent_def`, we define the *joint entropy* $H(X, Y)$ of a pair random variables $(X, Y)$ as\n", "\n", "$$H(X, Y) = -E_{(x, y) \\sim P} [\\log p_{X, Y}(x, y)]. $$\n", ":eqlabel:`eq_joint_ent_def`\n", "\n", "Precisely, on the one hand, if $(X, Y)$ is a pair of discrete random variables, then\n", "\n", "$$H(X, Y) = - \\sum_{x} \\sum_{y} p_{X, Y}(x, y) \\log p_{X, Y}(x, y).$$\n", "\n", "On the other hand, if $(X, Y)$ is a pair of continuous random variables, then we define the *differential joint entropy* as\n", "\n", "$$H(X, Y) = - \\int_{x, y} p_{X, Y}(x, y) \\ \\log p_{X, Y}(x, y) \\;dx \\;dy.$$\n", "\n", "We can think of :eqref:`eq_joint_ent_def` as telling us the total randomness in the pair of random variables.  As a pair of extremes, if $X = Y$ are two identical random variables, then the information in the pair is exactly the information in one and we have $H(X, Y) = H(X) = H(Y)$.  On the other extreme, if $X$ and $Y$ are independent then $H(X, Y) = H(X) + H(Y)$.  Indeed we will always have that the information contained in a pair of random variables is no smaller than the entropy of either random variable and no more than the sum of both.\n", "\n", "$$\n", "H(X), H(Y) \\le H(X, Y) \\le H(X) + H(Y).\n", "$$\n", "\n", "Let's implement joint entropy from scratch.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "ed80345a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:34.957196Z", "iopub.status.busy": "2023-08-18T19:28:34.956886Z", "iopub.status.idle": "2023-08-18T19:28:34.963853Z", "shell.execute_reply": "2023-08-18T19:28:34.962981Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor(1.6855)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["def joint_entropy(p_xy):\n", "    joint_ent = -p_xy * torch.log2(p_xy)\n", "    # Operator `nansum` will sum up the non-nan number\n", "    out = nansum(joint_ent)\n", "    return out\n", "\n", "joint_entropy(torch.tensor([[0.1, 0.5], [0.1, 0.3]]))"]}, {"cell_type": "markdown", "id": "3520d2e5", "metadata": {"origin_pos": 12}, "source": ["Notice that this is the same *code* as before, but now we interpret it differently as working on the joint distribution of the two random variables.\n", "\n", "\n", "### Conditional Entropy\n", "\n", "The joint entropy defined above the amount of information contained in a pair of random variables.  This is useful, but oftentimes it is not what we care about.  Consider the setting of machine learning.  Let's take $X$ to be the random variable (or vector of random variables) that describes the pixel values of an image, and $Y$ to be the random variable which is the class label.  $X$ should contain substantial information---a natural image is a complex thing.  However, the information contained in $Y$ once the image has been show should be low.  Indeed, the image of a digit should already contain the information about what digit it is unless the digit is illegible.  Thus, to continue to extend our vocabulary of information theory, we need to be able to reason about the information content in a random variable conditional on another.\n", "\n", "In the probability theory, we saw the definition of the *conditional probability* to measure the relationship between variables. We now want to analogously define the *conditional entropy* $H(Y \\mid X)$.  We can write this as\n", "\n", "$$ H(Y \\mid X) = - E_{(x, y) \\sim P} [\\log p(y \\mid x)],$$\n", ":eqlabel:`eq_cond_ent_def`\n", "\n", "where $p(y \\mid x) = \\frac{p_{X, Y}(x, y)}{p_X(x)}$ is the conditional probability. Specifically, if $(X, Y)$ is a pair of discrete random variables, then\n", "\n", "$$H(Y \\mid X) = - \\sum_{x} \\sum_{y} p(x, y) \\log p(y \\mid x).$$\n", "\n", "If $(X, Y)$ is a pair of continuous random variables, then the *differential conditional entropy* is similarly defined as\n", "\n", "$$H(Y \\mid X) = - \\int_x \\int_y p(x, y) \\ \\log p(y \\mid x) \\;dx \\;dy.$$\n", "\n", "\n", "It is now natural to ask, how does the *conditional entropy* $H(Y \\mid X)$ relate to the entropy $H(X)$ and the joint entropy $H(X, Y)$?  Using the definitions above, we can express this cleanly:\n", "\n", "$$H(Y \\mid X) = H(X, Y) - H(X).$$\n", "\n", "This has an intuitive interpretation: the information in $Y$ given $X$ ($H(Y \\mid X)$) is the same as the information in both $X$ and $Y$ together ($H(X, Y)$) minus the information already contained in $X$.  This gives us the information in $Y$ which is not also represented in $X$.\n", "\n", "Now, let's implement conditional entropy :eqref:`eq_cond_ent_def` from scratch.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "229bfa9a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:34.966982Z", "iopub.status.busy": "2023-08-18T19:28:34.966671Z", "iopub.status.idle": "2023-08-18T19:28:34.973923Z", "shell.execute_reply": "2023-08-18T19:28:34.973136Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor(0.8635)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["def conditional_entropy(p_xy, p_x):\n", "    p_y_given_x = p_xy/p_x\n", "    cond_ent = -p_xy * torch.log2(p_y_given_x)\n", "    # Operator `nansum` will sum up the non-nan number\n", "    out = nansum(cond_ent)\n", "    return out\n", "\n", "conditional_entropy(torch.tensor([[0.1, 0.5], [0.2, 0.3]]),\n", "                    torch.tensor([0.2, 0.8]))"]}, {"cell_type": "markdown", "id": "2bcf1456", "metadata": {"origin_pos": 16}, "source": ["### Mutual Information\n", "\n", "Given the previous setting of random variables $(X, Y)$, you may wonder: \"Now that we know how much information is contained in $Y$ but not in $X$, can we similarly ask how much information is shared between $X$ and $Y$?\" The answer will be the *mutual information* of $(X, Y)$, which we will write as $I(X, Y)$.\n", "\n", "Rather than diving straight into the formal definition, let's practice our intuition by first trying to derive an expression for the mutual information entirely based on terms we have constructed before.  We wish to find the information shared between two random variables.  One way we could try to do this is to start with all the information contained in both $X$ and $Y$ together, and then we take off the parts that are not shared.  The information contained in both $X$ and $Y$ together is written as $H(X, Y)$.  We want to subtract from this the information contained in $X$ but not in $Y$, and the information contained in $Y$ but not in $X$.  As we saw in the previous section, this is given by $H(X \\mid Y)$ and $H(Y \\mid X)$ respectively.  Thus, we have that the mutual information should be\n", "\n", "$$\n", "I(X, Y) = H(X, Y) - H(Y \\mid X) - H(X \\mid Y).\n", "$$\n", "\n", "Indeed, this is a valid definition for the mutual information.  If we expand out the definitions of these terms and combine them, a little algebra shows that this is the same as\n", "\n", "$$I(X, Y) = E_{x} E_{y} \\left\\{ p_{X, Y}(x, y) \\log\\frac{p_{X, Y}(x, y)}{p_X(x) p_Y(y)} \\right\\}. $$\n", ":eqlabel:`eq_mut_ent_def`\n", "\n", "\n", "We can summarize all of these relationships in image :numref:`fig_mutual_information`.  It is an excellent test of intuition to see why the following statements are all also equivalent to $I(X, Y)$.\n", "\n", "* $H(X) - H(X \\mid Y)$\n", "* $H(Y) - H(Y \\mid X)$\n", "* $H(X) + H(Y) - H(X, Y)$\n", "\n", "![Mutual information's relationship with joint entropy and conditional entropy.](../img/mutual-information.svg)\n", ":label:`fig_mutual_information`\n", "\n", "\n", "In many ways we can think of the mutual information :eqref:`eq_mut_ent_def` as principled extension of correlation coefficient we saw in :numref:`sec_random_variables`.  This allows us to ask not only for linear relationships between variables, but for the maximum information shared between the two random variables of any kind.\n", "\n", "Now, let's implement mutual information from scratch.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ecdd8475", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:34.977061Z", "iopub.status.busy": "2023-08-18T19:28:34.976754Z", "iopub.status.idle": "2023-08-18T19:28:34.984386Z", "shell.execute_reply": "2023-08-18T19:28:34.983587Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor(0.7195)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["def mutual_information(p_xy, p_x, p_y):\n", "    p = p_xy / (p_x * p_y)\n", "    mutual = p_xy * torch.log2(p)\n", "    # Operator `nansum` will sum up the non-nan number\n", "    out = nansum(mutual)\n", "    return out\n", "\n", "mutual_information(torch.tensor([[0.1, 0.5], [0.1, 0.3]]),\n", "                   torch.tensor([0.2, 0.8]), torch.tensor([[0.75, 0.25]]))"]}, {"cell_type": "markdown", "id": "b3c1a6ff", "metadata": {"origin_pos": 20}, "source": ["### Properties of Mutual Information\n", "\n", "Rather than memorizing the definition of mutual information :eqref:`eq_mut_ent_def`, you only need to keep in mind its notable properties:\n", "\n", "* Mutual information is symmetric, i.e., $I(X, Y) = I(Y, X)$.\n", "* Mutual information is non-negative, i.e., $I(X, Y) \\geq 0$.\n", "* $I(X, Y) = 0$ if and only if $X$ and $Y$ are independent. For example, if $X$ and $Y$ are independent, then knowing $Y$ does not give any information about $X$ and vice versa, so their mutual information is zero.\n", "* Alternatively, if $X$ is an invertible function of $Y$, then $Y$ and $X$ share all information and $$I(X, Y) = H(Y) = H(X).$$\n", "\n", "### Pointwise Mutual Information\n", "\n", "When we worked with entropy at the beginning of this chapter, we were able to provide an interpretation of $-\\log(p_X(x))$ as how *surprised* we were with the particular outcome.  We may give a similar interpretation to the logarithmic term in the mutual information, which is often referred to as the *pointwise mutual information*:\n", "\n", "$$\\textrm{pmi}(x, y) = \\log\\frac{p_{X, Y}(x, y)}{p_X(x) p_Y(y)}.$$\n", ":eqlabel:`eq_pmi_def`\n", "\n", "We can think of :eqref:`eq_pmi_def` as measuring how much more or less likely the specific combination of outcomes $x$ and $y$ are compared to what we would expect for independent random outcomes.  If it is large and positive, then these two specific outcomes occur much more frequently than they would compared to random chance (*note*: the denominator is $p_X(x) p_Y(y)$ which is the probability of the two outcomes were independent), whereas if it is large and negative it represents the two outcomes happening far less than we would expect by random chance.\n", "\n", "This allows us to interpret the mutual information :eqref:`eq_mut_ent_def` as the average amount that we were surprised to see two outcomes occurring together compared to what we would expect if they were independent.\n", "\n", "### Applications of Mutual Information\n", "\n", "Mutual information may be a little abstract in it pure definition, so how does it related to machine learning? In natural language processing, one of the most difficult problems is the *ambiguity resolution*, or the issue of the meaning of a word being unclear from context. For example, recently a headline in the news reported that \"Amazon is on fire\". You may wonder whether the company Amazon has a building on fire, or the Amazon rain forest is on fire.\n", "\n", "In this case, mutual information can help us resolve this ambiguity. We first find the group of words that each has a relatively large mutual information with the company Amazon, such as e-commerce, technology, and online. Second, we find another group of words that each has a relatively large mutual information with the Amazon rain forest, such as rain, forest, and tropical. When we need to disambiguate \"Amazon\", we can compare which group has more occurrence in the context of the word Amazon.  In this case the article would go on to describe the forest, and make the context clear.\n", "\n", "\n", "## <PERSON><PERSON><PERSON>–<PERSON><PERSON>r Divergence\n", "\n", "As what we have discussed in :numref:`sec_linear-algebra`, we can use norms to measure distance between two points in space of any dimensionality.  We would like to be able to do a similar task with probability distributions.  There are many ways to go about this, but information theory provides one of the nicest.  We now explore the *<PERSON>–Leibler (KL) divergence*, which provides a way to measure if two distributions are close together or not.\n", "\n", "\n", "### Definition\n", "\n", "Given a random variable $X$ that follows the probability distribution $P$ with a p.d.f. or a p.m.f. $p(x)$, and we estimate $P$ by another probability distribution $Q$ with a p.d.f. or a p.m.f. $q(x)$. Then the *<PERSON>back–Leibler (KL) divergence* (or *relative entropy*) between $P$ and $Q$ is\n", "\n", "$$D_{\\textrm{KL}}(P\\|Q) = E_{x \\sim P} \\left[ \\log \\frac{p(x)}{q(x)} \\right].$$\n", ":eqlabel:`eq_kl_def`\n", "\n", "As with the pointwise mutual information :eqref:`eq_pmi_def`, we can again provide an interpretation of the logarithmic term:  $-\\log \\frac{q(x)}{p(x)} = -\\log(q(x)) - (-\\log(p(x)))$ will be large and positive if we see $x$ far more often under $P$ than we would expect for $Q$, and large and negative if we see the outcome far less than expected.  In this way, we can interpret it as our *relative* surprise at observing the outcome compared to how surprised we would be observing it from our reference distribution.\n", "\n", "Let's implement the KL divergence from Scratch.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "e93a8e8a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:34.987451Z", "iopub.status.busy": "2023-08-18T19:28:34.987098Z", "iopub.status.idle": "2023-08-18T19:28:34.991409Z", "shell.execute_reply": "2023-08-18T19:28:34.990553Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["def kl_divergence(p, q):\n", "    kl = p * torch.log2(p / q)\n", "    out = nansum(kl)\n", "    return out.abs().item()"]}, {"cell_type": "markdown", "id": "ea4151e0", "metadata": {"origin_pos": 24}, "source": ["### KL Divergence Properties\n", "\n", "Let's take a look at some properties of the KL divergence :eqref:`eq_kl_def`.\n", "\n", "* KL divergence is non-symmetric, i.e., there are $P,Q$ such that $$D_{\\textrm{KL}}(P\\|Q) \\neq D_{\\textrm{KL}}(Q\\|P).$$\n", "* KL divergence is non-negative, i.e., $$D_{\\textrm{KL}}(P\\|Q) \\geq 0.$$ Note that the equality holds only when $P = Q$.\n", "* If there exists an $x$ such that $p(x) > 0$ and $q(x) = 0$, then $D_{\\textrm{KL}}(P\\|Q) = \\infty$.\n", "* There is a close relationship between KL divergence and mutual information. Besides the relationship shown in :numref:`fig_mutual_information`, $I(X, Y)$ is also numerically equivalent with the following terms:\n", "    1. $D_{\\textrm{KL}}(P(X, Y)  \\ \\| \\ P(X)P(Y))$;\n", "    1. $E_Y \\{ D_{\\textrm{KL}}(P(X \\mid Y) \\ \\| \\ P(X)) \\}$;\n", "    1. $E_X \\{ D_{\\textrm{KL}}(P(Y \\mid X) \\ \\| \\ P(Y)) \\}$.\n", "\n", "  For the first term, we interpret mutual information as the KL divergence between $P(X, Y)$ and the product of $P(X)$ and $P(Y)$, and thus is a measure of how different the joint distribution is from the distribution if they were independent. For the second term, mutual information tells us the average reduction in uncertainty about $Y$ that results from learning the value of the $X$'s distribution. Similarly to the third term.\n", "\n", "\n", "### Example\n", "\n", "Let's go through a toy example to see the non-symmetry explicitly.\n", "\n", "First, let's generate and sort three tensors of length $10,000$: an objective tensor $p$ which follows a normal distribution $N(0, 1)$, and two candidate tensors $q_1$ and $q_2$ which follow normal distributions $N(-1, 1)$ and $N(1, 1)$ respectively.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "9a145bd1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:34.994445Z", "iopub.status.busy": "2023-08-18T19:28:34.994138Z", "iopub.status.idle": "2023-08-18T19:28:35.003905Z", "shell.execute_reply": "2023-08-18T19:28:35.003053Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [], "source": ["torch.manual_seed(1)\n", "\n", "tensor_len = 10000\n", "p = torch.normal(0, 1, (tensor_len, ))\n", "q1 = torch.normal(-1, 1, (tensor_len, ))\n", "q2 = torch.normal(1, 1, (tensor_len, ))\n", "\n", "p = torch.sort(p)[0]\n", "q1 = torch.sort(q1)[0]\n", "q2 = torch.sort(q2)[0]"]}, {"cell_type": "markdown", "id": "db854162", "metadata": {"origin_pos": 28}, "source": ["Since $q_1$ and $q_2$ are symmetric with respect to the y-axis (i.e., $x=0$), we expect a similar value of KL divergence between $D_{\\textrm{KL}}(p\\|q_1)$ and $D_{\\textrm{KL}}(p\\|q_2)$. As you can see below, there is only a less than 3% off between $D_{\\textrm{KL}}(p\\|q_1)$ and $D_{\\textrm{KL}}(p\\|q_2)$.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "23314c60", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:35.007040Z", "iopub.status.busy": "2023-08-18T19:28:35.006732Z", "iopub.status.idle": "2023-08-18T19:28:35.014738Z", "shell.execute_reply": "2023-08-18T19:28:35.013942Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(8582.0341796875, 8828.3095703125, 2.8290698237936858)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["kl_pq1 = kl_divergence(p, q1)\n", "kl_pq2 = kl_divergence(p, q2)\n", "similar_percentage = abs(kl_pq1 - kl_pq2) / ((kl_pq1 + kl_pq2) / 2) * 100\n", "\n", "kl_pq1, kl_pq2, similar_percentage"]}, {"cell_type": "markdown", "id": "defbbf97", "metadata": {"origin_pos": 30}, "source": ["In contrast, you may find that $D_{\\textrm{KL}}(q_2 \\|p)$ and $D_{\\textrm{KL}}(p \\| q_2)$ are off a lot, with around 40% off as shown below.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "30b79c00", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:35.017956Z", "iopub.status.busy": "2023-08-18T19:28:35.017648Z", "iopub.status.idle": "2023-08-18T19:28:35.024032Z", "shell.execute_reply": "2023-08-18T19:28:35.023213Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(14130.125, 46.18621024399691)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["kl_q2p = kl_divergence(q2, p)\n", "differ_percentage = abs(kl_q2p - kl_pq2) / ((kl_q2p + kl_pq2) / 2) * 100\n", "\n", "kl_q2p, differ_percentage"]}, {"cell_type": "markdown", "id": "e0b1be30", "metadata": {"origin_pos": 32}, "source": ["## Cross-Entropy\n", "\n", "If you are curious about applications of information theory in deep learning, here is a quick example. We define the true distribution $P$ with probability distribution $p(x)$, and the estimated distribution $Q$ with probability distribution $q(x)$, and we will use them in the rest of this section.\n", "\n", "Say we need to solve a binary classification problem based on given $n$ data examples {$x_1, \\ldots, x_n$}. Assume that we encode $1$ and $0$ as the positive and negative class label $y_i$ respectively, and our neural network is parametrized by $\\theta$. If we aim to find a best $\\theta$ so that $\\hat{y}_i= p_{\\theta}(y_i \\mid x_i)$, it is natural to apply the maximum log-likelihood approach as was seen in :numref:`sec_maximum_likelihood`. To be specific, for true labels $y_i$ and predictions $\\hat{y}_i= p_{\\theta}(y_i \\mid x_i)$, the probability to be classified as positive is $\\pi_i= p_{\\theta}(y_i = 1 \\mid x_i)$. Hence, the log-likelihood function would be\n", "\n", "$$\n", "\\begin{aligned}\n", "l(\\theta) &= \\log L(\\theta) \\\\\n", "  &= \\log \\prod_{i=1}^n \\pi_i^{y_i} (1 - \\pi_i)^{1 - y_i} \\\\\n", "  &= \\sum_{i=1}^n y_i \\log(\\pi_i) + (1 - y_i) \\log (1 - \\pi_i). \\\\\n", "\\end{aligned}\n", "$$\n", "\n", "Maximizing the log-likelihood function $l(\\theta)$ is identical to minimizing $- l(\\theta)$, and hence we can find the best $\\theta$ from here. To generalize the above loss to any distributions, we also called $-l(\\theta)$ the *cross-entropy loss* $\\textrm{CE}(y, \\hat{y})$, where $y$ follows the true distribution $P$ and $\\hat{y}$ follows the estimated distribution $Q$.\n", "\n", "This was all derived by working from the maximum likelihood point of view.  However, if we look closely we can see that terms like $\\log(\\pi_i)$ have entered into our computation which is a solid indication that we can understand the expression from an information theoretic point of view.\n", "\n", "\n", "### Formal Definition\n", "\n", "Like KL divergence, for a random variable $X$, we can also measure the divergence between the estimating distribution $Q$ and the true distribution $P$ via *cross-entropy*,\n", "\n", "$$\\textrm{CE}(P, Q) = - E_{x \\sim P} [\\log(q(x))].$$\n", ":eqlabel:`eq_ce_def`\n", "\n", "By using properties of entropy discussed above, we can also interpret it as the summation of the entropy $H(P)$ and the KL divergence between $P$ and $Q$, i.e.,\n", "\n", "$$\\textrm{CE} (P, Q) = H(P) + D_{\\textrm{KL}}(P\\|Q).$$\n", "\n", "\n", "We can implement the cross-entropy loss as below.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "eb9a24ef", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:35.027038Z", "iopub.status.busy": "2023-08-18T19:28:35.026732Z", "iopub.status.idle": "2023-08-18T19:28:35.030992Z", "shell.execute_reply": "2023-08-18T19:28:35.030178Z"}, "origin_pos": 34, "tab": ["pytorch"]}, "outputs": [], "source": ["def cross_entropy(y_hat, y):\n", "    ce = -torch.log(y_hat[range(len(y_hat)), y])\n", "    return ce.mean()"]}, {"cell_type": "markdown", "id": "12653882", "metadata": {"origin_pos": 36}, "source": ["Now define two tensors for the labels and predictions, and calculate the cross-entropy loss of them.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "28e29cd4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:35.034082Z", "iopub.status.busy": "2023-08-18T19:28:35.033774Z", "iopub.status.idle": "2023-08-18T19:28:35.042898Z", "shell.execute_reply": "2023-08-18T19:28:35.042120Z"}, "origin_pos": 38, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor(0.9486)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["labels = torch.tensor([0, 2])\n", "preds = torch.tensor([[0.3, 0.6, 0.1], [0.2, 0.3, 0.5]])\n", "\n", "cross_entropy(preds, labels)"]}, {"cell_type": "markdown", "id": "0db4840f", "metadata": {"origin_pos": 40}, "source": ["### Properties\n", "\n", "As alluded in the beginning of this section, cross-entropy :eqref:`eq_ce_def` can be used to define a loss function in the optimization problem. It turns out that the following are equivalent:\n", "\n", "1. Maximizing predictive probability of $Q$ for distribution $P$, (i.e., $E_{x\n", "\\sim P} [\\log (q(x))]$);\n", "1. Minimizing cross-entropy $\\textrm{CE} (P, Q)$;\n", "1. Minimizing the KL divergence $D_{\\textrm{KL}}(P\\|Q)$.\n", "\n", "The definition of cross-entropy indirectly proves the equivalent relationship between objective 2 and objective 3, as long as the entropy of true data $H(P)$ is constant.\n", "\n", "\n", "### Cross-Entropy as An Objective Function of Multi-class Classification\n", "\n", "If we dive deep into the classification objective function with cross-entropy loss $\\textrm{CE}$, we will find minimizing $\\textrm{CE}$ is equivalent to maximizing the log-likelihood function $L$.\n", "\n", "To begin with, suppose that we are given a dataset with $n$ examples, and it can be classified into $k$-classes. For each data example $i$, we represent any $k$-class label $\\mathbf{y}_i = (y_{i1}, \\ldots, y_{ik})$ by *one-hot encoding*. To be specific, if the  example $i$ belongs to class $j$, then we set the $j$-th entry to $1$, and all other components to $0$, i.e.,\n", "\n", "$$ y_{ij} = \\begin{cases}1 & j \\in J; \\\\ 0 &\\textrm{otherwise.}\\end{cases}$$\n", "\n", "For instance, if a multi-class classification problem contains three classes $A$, $B$, and $C$, then the labels $\\mathbf{y}_i$ can be encoded in {$A: (1, 0, 0); B: (0, 1, 0); C: (0, 0, 1)$}.\n", "\n", "\n", "Assume that our neural network is parametrized by $\\theta$. For true label vectors $\\mathbf{y}_i$ and predictions $$\\hat{\\mathbf{y}}_i= p_{\\theta}(\\mathbf{y}_i \\mid \\mathbf{x}_i) = \\sum_{j=1}^k y_{ij} p_{\\theta} (y_{ij}  \\mid  \\mathbf{x}_i).$$\n", "\n", "Hence, the *cross-entropy loss* would be\n", "\n", "$$\n", "\\textrm{CE}(\\mathbf{y}, \\hat{\\mathbf{y}}) = - \\sum_{i=1}^n \\mathbf{y}_i \\log \\hat{\\mathbf{y}}_i\n", " = - \\sum_{i=1}^n \\sum_{j=1}^k y_{ij} \\log{p_{\\theta} (y_{ij}  \\mid  \\mathbf{x}_i)}.\\\\\n", "$$\n", "\n", "On the other side, we can also approach the problem through maximum likelihood estimation. To begin with, let's quickly introduce a $k$-class multinoulli distribution. It is an extension of the <PERSON><PERSON><PERSON> distribution from binary class to multi-class. If a random variable $\\mathbf{z} = (z_{1}, \\ldots, z_{k})$ follows a $k$-class *multinoulli distribution* with probabilities $\\mathbf{p} =$ ($p_{1}, \\ldots, p_{k}$), i.e., $$p(\\mathbf{z}) = p(z_1, \\ldots, z_k) = \\textrm{Multi} (p_1, \\ldots, p_k), \\textrm{ where } \\sum_{i=1}^k p_i = 1,$$ then the joint probability mass function(p.m.f.) of $\\mathbf{z}$ is\n", "$$\\mathbf{p}^\\mathbf{z} = \\prod_{j=1}^k p_{j}^{z_{j}}.$$\n", "\n", "\n", "It can be seen that the label of each data example, $\\mathbf{y}_i$, is following a $k$-class multinoulli distribution with probabilities $\\boldsymbol{\\pi} =$ ($\\pi_{1}, \\ldots, \\pi_{k}$). Therefore, the joint p.m.f. of each data example $\\mathbf{y}_i$ is  $\\mathbf{\\pi}^{\\mathbf{y}_i} = \\prod_{j=1}^k \\pi_{j}^{y_{ij}}.$\n", "Hence, the log-likelihood function would be\n", "\n", "$$\n", "\\begin{aligned}\n", "l(\\theta)\n", " = \\log L(\\theta)\n", " = \\log \\prod_{i=1}^n \\boldsymbol{\\pi}^{\\mathbf{y}_i}\n", " = \\log \\prod_{i=1}^n \\prod_{j=1}^k \\pi_{j}^{y_{ij}}\n", " = \\sum_{i=1}^n \\sum_{j=1}^k y_{ij} \\log{\\pi_{j}}.\\\\\n", "\\end{aligned}\n", "$$\n", "\n", "Since in maximum likelihood estimation, we maximizing the objective function $l(\\theta)$ by having $\\pi_{j} = p_{\\theta} (y_{ij}  \\mid  \\mathbf{x}_i)$. Therefore, for any multi-class classification, maximizing the above log-likelihood function $l(\\theta)$ is equivalent to minimizing the CE loss $\\textrm{CE}(y, \\hat{y})$.\n", "\n", "\n", "To test the above proof, let's apply the built-in measure `NegativeLogLikelihood`. Using the same `labels` and `preds` as in the earlier example, we will get the same numerical loss as the previous example up to the 5 decimal place.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "83ee05a7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:35.046022Z", "iopub.status.busy": "2023-08-18T19:28:35.045713Z", "iopub.status.idle": "2023-08-18T19:28:35.052187Z", "shell.execute_reply": "2023-08-18T19:28:35.051409Z"}, "origin_pos": 42, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor(0.9486)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Implementation of cross-entropy loss in PyTorch combines `nn.LogSoftmax()`\n", "# and `nn.<PERSON>()`\n", "nll_loss = NLLLoss()\n", "loss = nll_loss(torch.log(preds), labels)\n", "loss"]}, {"cell_type": "markdown", "id": "31ee8331", "metadata": {"origin_pos": 44}, "source": ["## Summary\n", "\n", "* Information theory is a field of study about encoding, decoding, transmitting, and manipulating information.\n", "* Entropy is the unit to measure how much information is presented in different signals.\n", "* KL divergence can also measure the divergence between two distributions.\n", "* Cross-entropy can be viewed as an objective function of multi-class classification. Minimizing cross-entropy loss is equivalent to maximizing the log-likelihood function.\n", "\n", "\n", "## Exercises\n", "\n", "1. Verify that the card examples from the first section indeed have the claimed entropy.\n", "1. Show that the KL divergence $D(p\\|q)$ is nonnegative for all distributions $p$ and $q$. Hint: use <PERSON>'s inequality, i.e., use the fact that $-\\log x$ is a convex function.\n", "1. Let's compute the entropy from a few data sources:\n", "    * Assume that you are watching the output generated by a monkey at a typewriter. The monkey presses any of the $44$ keys of the typewriter at random (you can assume that it has not discovered any special keys or the shift key yet). How many bits of randomness per character do you observe?\n", "    * Being unhappy with the monkey, you replaced it by a drunk typesetter. It is able to generate words, albeit not coherently. Instead, it picks a random word out of a vocabulary of $2,000$ words. Let's assume that the average length of a word is $4.5$ letters in English. How many bits of randomness per character do you observe now?\n", "    * Still being unhappy with the result, you replace the typesetter by a high quality language model. The language model can currently obtain a perplexity as low as $15$ points per word. The character *perplexity* of a language model is defined as the inverse of the geometric mean of a set of probabilities, each probability is corresponding to a character in the word. To be specific, if the length of a given word is $l$, then  $\\textrm{PPL}(\\textrm{word}) = \\left[\\prod_i p(\\textrm{character}_i)\\right]^{ -\\frac{1}{l}} = \\exp \\left[ - \\frac{1}{l} \\sum_i{\\log p(\\textrm{character}_i)} \\right].$  Assume that the test word has 4.5 letters, how many bits of randomness per character do you observe now?\n", "1. Explain intuitively why $I(X, Y) = H(X) - H(X \\mid Y)$.  Then, show this is true by expressing both sides as an expectation with respect to the joint distribution.\n", "1. What is the KL Divergence between the two Gaussian distributions $\\mathcal{N}(\\mu_1, \\sigma_1^2)$ and $\\mathcal{N}(\\mu_2, \\sigma_2^2)$?\n"]}, {"cell_type": "markdown", "id": "b3cf9eb2", "metadata": {"origin_pos": 46, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1104)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "c3310065", "metadata": {"origin_pos": 0}, "source": ["# Eigendecompositions\n", ":label:`sec_eigendecompositions`\n", "\n", "Eigenvalues are often one of the most useful notions\n", "we will encounter when studying linear algebra,\n", "however, as a beginner, it is easy to overlook their importance.\n", "Below, we introduce eigendecomposition and\n", "try to convey some sense of just why it is so important.\n", "\n", "Suppose that we have a matrix $A$ with the following entries:\n", "\n", "$$\n", "\\mathbf{A} = \\begin{bmatrix}\n", "2 & 0 \\\\\n", "0 & -1\n", "\\end{bmatrix}.\n", "$$\n", "\n", "If we apply $A$ to any vector $\\mathbf{v} = [x, y]^\\top$,\n", "we obtain a vector $\\mathbf{A}\\mathbf{v} = [2x, -y]^\\top$.\n", "This has an intuitive interpretation:\n", "stretch the vector to be twice as wide in the $x$-direction,\n", "and then flip it in the $y$-direction.\n", "\n", "However, there are *some* vectors for which something remains unchanged.\n", "Namely $[1, 0]^\\top$ gets sent to $[2, 0]^\\top$\n", "and $[0, 1]^\\top$ gets sent to $[0, -1]^\\top$.\n", "These vectors are still in the same line,\n", "and the only modification is that the matrix stretches them\n", "by a factor of $2$ and $-1$ respectively.\n", "We call such vectors *eigenvectors*\n", "and the factor they are stretched by *eigenvalues*.\n", "\n", "In general, if we can find a number $\\lambda$\n", "and a vector $\\mathbf{v}$ such that\n", "\n", "$$\n", "\\mathbf{A}\\mathbf{v} = \\lambda \\mathbf{v}.\n", "$$\n", "\n", "We say that $\\mathbf{v}$ is an eigenvector for $A$ and $\\lambda$ is an eigenvalue.\n", "\n", "## Finding Eigenvalues\n", "Let's figure out how to find them. By subtracting off the $\\lambda \\mathbf{v}$ from both sides,\n", "and then factoring out the vector,\n", "we see the above is equivalent to:\n", "\n", "$$(\\mathbf{A} - \\lambda \\mathbf{I})\\mathbf{v} = 0.$$\n", ":eqlabel:`eq_eigvalue_der`\n", "\n", "For :eqref:`eq_eigvalue_der` to happen, we see that $(\\mathbf{A} - \\lambda \\mathbf{I})$\n", "must compress some direction down to zero,\n", "hence it is not invertible, and thus the determinant is zero.\n", "Thus, we can find the *eigenvalues*\n", "by finding for what $\\lambda$ is $\\det(\\mathbf{A}-\\lambda \\mathbf{I}) = 0$.\n", "Once we find the eigenvalues, we can solve\n", "$\\mathbf{A}\\mathbf{v} = \\lambda \\mathbf{v}$\n", "to find the associated *eigenvector(s)*.\n", "\n", "### An Example\n", "Let's see this with a more challenging matrix\n", "\n", "$$\n", "\\mathbf{A} = \\begin{bmatrix}\n", "2 & 1\\\\\n", "2 & 3\n", "\\end{bmatrix}.\n", "$$\n", "\n", "If we consider $\\det(\\mathbf{A}-\\lambda \\mathbf{I}) = 0$,\n", "we see this is equivalent to the polynomial equation\n", "$0 = (2-\\lambda)(3-\\lambda)-2 = (4-\\lambda)(1-\\lambda)$.\n", "Thus, two eigenvalues are $4$ and $1$.\n", "To find the associated vectors, we then need to solve\n", "\n", "$$\n", "\\begin{bmatrix}\n", "2 & 1\\\\\n", "2 & 3\n", "\\end{bmatrix}\\begin{bmatrix}x \\\\ y\\end{bmatrix} = \\begin{bmatrix}x \\\\ y\\end{bmatrix}  \\; \\textrm{and} \\;\n", "\\begin{bmatrix}\n", "2 & 1\\\\\n", "2 & 3\n", "\\end{bmatrix}\\begin{bmatrix}x \\\\ y\\end{bmatrix}  = \\begin{bmatrix}4x \\\\ 4y\\end{bmatrix} .\n", "$$\n", "\n", "We can solve this with the vectors $[1, -1]^\\top$ and $[1, 2]^\\top$ respectively.\n", "\n", "We can check this in code using the built-in `numpy.linalg.eig` routine.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "89ca18bb", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:42.865720Z", "iopub.status.busy": "2023-08-18T19:36:42.865350Z", "iopub.status.idle": "2023-08-18T19:36:46.401039Z", "shell.execute_reply": "2023-08-18T19:36:46.399879Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.return_types.linalg_eig(\n", "eigenvalues=tensor([1.+0.j, 4.+0.j], dtype=torch.complex128),\n", "eigenvectors=tensor([[-0.7071+0.j, -0.4472+0.j],\n", "        [ 0.7071+0.j, -0.8944+0.j]], dtype=torch.complex128))"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["%matplotlib inline\n", "import torch\n", "from IPython import display\n", "from d2l import torch as d2l\n", "\n", "torch.linalg.eig(torch.tensor([[2, 1], [2, 3]], dtype=torch.float64))"]}, {"cell_type": "markdown", "id": "5a3c034b", "metadata": {"origin_pos": 4}, "source": ["Note that `numpy` normalizes the eigenvectors to be of length one,\n", "whereas we took ours to be of arbitrary length.\n", "Additionally, the choice of sign is arbitrary.\n", "However, the vectors computed are parallel\n", "to the ones we found by hand with the same eigenvalues.\n", "\n", "## Decomposing Matrices\n", "Let's continue the previous example one step further.  Let\n", "\n", "$$\n", "\\mathbf{W} = \\begin{bmatrix}\n", "1 & 1 \\\\\n", "-1 & 2\n", "\\end{bmatrix},\n", "$$\n", "\n", "be the matrix where the columns are the eigenvectors of the matrix $\\mathbf{A}$. Let\n", "\n", "$$\n", "\\boldsymbol{\\Sigma} = \\begin{bmatrix}\n", "1 & 0 \\\\\n", "0 & 4\n", "\\end{bmatrix},\n", "$$\n", "\n", "be the matrix with the associated eigenvalues on the diagonal.\n", "Then the definition of eigenvalues and eigenvectors tells us that\n", "\n", "$$\n", "\\mathbf{A}\\mathbf{W} =\\mathbf{W} \\boldsymbol{\\Sigma} .\n", "$$\n", "\n", "The matrix $W$ is invertible, so we may multiply both sides by $W^{-1}$ on the right,\n", "we see that we may write\n", "\n", "$$\\mathbf{A} = \\mathbf{W} \\boldsymbol{\\Sigma} \\mathbf{W}^{-1}.$$\n", ":eqlabel:`eq_eig_decomp`\n", "\n", "In the next section we will see some nice consequences of this,\n", "but for now we need only know that such a decomposition\n", "will exist as long as we can find a full collection\n", "of linearly independent eigenvectors (so that $W$ is invertible).\n", "\n", "## Operations on Eigendecompositions\n", "One nice thing about eigendecompositions :eqref:`eq_eig_decomp` is that\n", "we can write many operations we usually encounter cleanly\n", "in terms of the eigendecomposition. As a first example, consider:\n", "\n", "$$\n", "\\mathbf{A}^n = \\overbrace{\\mathbf{A}\\cdots \\mathbf{A}}^{\\textrm{$n$ times}} = \\overbrace{(\\mathbf{W}\\boldsymbol{\\Sigma} \\mathbf{W}^{-1})\\cdots(\\mathbf{W}\\boldsymbol{\\Sigma} \\mathbf{W}^{-1})}^{\\textrm{$n$ times}} =  \\mathbf{W}\\overbrace{\\boldsymbol{\\Sigma}\\cdots\\boldsymbol{\\Sigma}}^{\\textrm{$n$ times}}\\mathbf{W}^{-1} = \\mathbf{W}\\boldsymbol{\\Sigma}^n \\mathbf{W}^{-1}.\n", "$$\n", "\n", "This tells us that for any positive power of a matrix,\n", "the eigendecomposition is obtained by just raising the eigenvalues to the same power.\n", "The same can be shown for negative powers,\n", "so if we want to invert a matrix we need only consider\n", "\n", "$$\n", "\\mathbf{A}^{-1} = \\mathbf{W}\\boldsymbol{\\Sigma}^{-1} \\mathbf{W}^{-1},\n", "$$\n", "\n", "or in other words, just invert each eigenvalue.\n", "This will work as long as each eigenvalue is non-zero,\n", "so we see that invertible is the same as having no zero eigenvalues.\n", "\n", "Indeed, additional work can show that if $\\lambda_1, \\ldots, \\lambda_n$\n", "are the eigenvalues of a matrix, then the determinant of that matrix is\n", "\n", "$$\n", "\\det(\\mathbf{A}) = \\lambda_1 \\cdots \\lambda_n,\n", "$$\n", "\n", "or the product of all the eigenvalues.\n", "This makes sense intuitively because whatever stretching $\\mathbf{W}$ does,\n", "$W^{-1}$ undoes it, so in the end the only stretching that happens is\n", "by multiplication by the diagonal matrix $\\boldsymbol{\\Sigma}$,\n", "which stretches volumes by the product of the diagonal elements.\n", "\n", "Finally, recall that the rank was the maximum number\n", "of linearly independent columns of your matrix.\n", "By examining the eigendecomposition closely,\n", "we can see that the rank is the same\n", "as the number of non-zero eigenvalues of $\\mathbf{A}$.\n", "\n", "The examples could continue, but hopefully the point is clear:\n", "eigendecomposition can simplify many linear-algebraic computations\n", "and is a fundamental operation underlying many numerical algorithms\n", "and much of the analysis that we do in linear algebra.\n", "\n", "## Eigendecompositions of Symmetric Matrices\n", "It is not always possible to find enough linearly independent eigenvectors\n", "for the above process to work. For instance the matrix\n", "\n", "$$\n", "\\mathbf{A} = \\begin{bmatrix}\n", "1 & 1 \\\\\n", "0 & 1\n", "\\end{bmatrix},\n", "$$\n", "\n", "has only a single eigenvector, namely $(1, 0)^\\top$.\n", "To handle such matrices, we require more advanced techniques\n", "than we can cover (such as the Jordan Normal Form, or Singular Value Decomposition).\n", "We will often need to restrict our attention to those matrices\n", "where we can guarantee the existence of a full set of eigenvectors.\n", "\n", "The most commonly encountered family are the *symmetric matrices*,\n", "which are those matrices where $\\mathbf{A} = \\mathbf{A}^\\top$.\n", "In this case, we may take $W$ to be an *orthogonal matrix*—a matrix whose columns are all length one vectors that are at right angles to one another, where\n", "$\\mathbf{W}^\\top = \\mathbf{W}^{-1}$—and all the eigenvalues will be real.\n", "Thus, in this special case, we can write :eqref:`eq_eig_decomp` as\n", "\n", "$$\n", "\\mathbf{A} = \\mathbf{W}\\boldsymbol{\\Sigma}\\mathbf{W}^\\top .\n", "$$\n", "\n", "## Gershgorin Circle Theorem\n", "Eigenvalues are often difficult to reason with intuitively.\n", "If presented an arbitrary matrix, there is little that can be said\n", "about what the eigenvalues are without computing them.\n", "There is, however, one theorem that can make it easy to approximate well\n", "if the largest values are on the diagonal.\n", "\n", "Let $\\mathbf{A} = (a_{ij})$ be any square matrix ($n\\times n$).\n", "We will define $r_i = \\sum_{j \\neq i} |a_{ij}|$.\n", "Let $\\mathcal{D}_i$ represent the disc in the complex plane\n", "with center $a_{ii}$ radius $r_i$.\n", "Then, every eigenvalue of $\\mathbf{A}$ is contained in one of the $\\mathcal{D}_i$.\n", "\n", "This can be a bit to unpack, so let's look at an example.\n", "Consider the matrix:\n", "\n", "$$\n", "\\mathbf{A} = \\begin{bmatrix}\n", "1.0 & 0.1 & 0.1 & 0.1 \\\\\n", "0.1 & 3.0 & 0.2 & 0.3 \\\\\n", "0.1 & 0.2 & 5.0 & 0.5 \\\\\n", "0.1 & 0.3 & 0.5 & 9.0\n", "\\end{bmatrix}.\n", "$$\n", "\n", "We have $r_1 = 0.3$, $r_2 = 0.6$, $r_3 = 0.8$ and $r_4 = 0.9$.\n", "The matrix is symmetric, so all eigenvalues are real.\n", "This means that all of our eigenvalues will be in one of the ranges of\n", "\n", "$$[a_{11}-r_1, a_{11}+r_1] = [0.7, 1.3], $$\n", "\n", "$$[a_{22}-r_2, a_{22}+r_2] = [2.4, 3.6], $$\n", "\n", "$$[a_{33}-r_3, a_{33}+r_3] = [4.2, 5.8], $$\n", "\n", "$$[a_{44}-r_4, a_{44}+r_4] = [8.1, 9.9]. $$\n", "\n", "\n", "Performing the numerical computation shows\n", "that the eigenvalues are approximately $0.99$, $2.97$, $4.95$, $9.08$,\n", "all comfortably inside the ranges provided.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "573606d1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:46.406032Z", "iopub.status.busy": "2023-08-18T19:36:46.405197Z", "iopub.status.idle": "2023-08-18T19:36:46.419609Z", "shell.execute_reply": "2023-08-18T19:36:46.418478Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0.9923+0.j, 9.0803+0.j, 4.9539+0.j, 2.9734+0.j])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["A = torch.tensor([[1.0, 0.1, 0.1, 0.1],\n", "              [0.1, 3.0, 0.2, 0.3],\n", "              [0.1, 0.2, 5.0, 0.5],\n", "              [0.1, 0.3, 0.5, 9.0]])\n", "\n", "v, _ = torch.linalg.eig(A)\n", "v"]}, {"cell_type": "markdown", "id": "f6f54eb0", "metadata": {"origin_pos": 8}, "source": ["In this way, eigenvalues can be approximated,\n", "and the approximations will be fairly accurate\n", "in the case that the diagonal is\n", "significantly larger than all the other elements.\n", "\n", "It is a small thing, but with a complex\n", "and subtle topic like eigendecomposition,\n", "it is good to get any intuitive grasp we can.\n", "\n", "## A Useful Application: The Growth of Iterated Maps\n", "\n", "Now that we understand what eigenvectors are in principle,\n", "let's see how they can be used to provide a deep understanding\n", "of a problem central to neural network behavior: proper weight initialization.\n", "\n", "### Eigenvectors as Long Term Behavior\n", "\n", "The full mathematical investigation of the initialization\n", "of deep neural networks is beyond the scope of the text,\n", "but we can see a toy version here to understand\n", "how eigenvalues can help us see how these models work.\n", "As we know, neural networks operate by interspersing layers\n", "of linear transformations with non-linear operations.\n", "For simplicity here, we will assume that there is no non-linearity,\n", "and that the transformation is a single repeated matrix operation $A$,\n", "so that the output of our model is\n", "\n", "$$\n", "\\mathbf{v}_{out} = \\mathbf{A}\\cdot \\mathbf{A}\\cdots \\mathbf{A} \\mathbf{v}_{in} = \\mathbf{A}^N \\mathbf{v}_{in}.\n", "$$\n", "\n", "When these models are initialized, $A$ is taken to be\n", "a random matrix with Gaussian entries, so let's make one of those.\n", "To be concrete, we start with a mean zero, variance one Gaussian distributed $5 \\times 5$ matrix.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "8877e2b5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:46.423825Z", "iopub.status.busy": "2023-08-18T19:36:46.423224Z", "iopub.status.idle": "2023-08-18T19:36:46.436261Z", "shell.execute_reply": "2023-08-18T19:36:46.433303Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.2996,  0.2424,  0.2832, -0.2329,  0.6712],\n", "        [ 0.7818, -1.7903, -1.7484,  0.1735, -0.1182],\n", "        [-1.7446, -0.4695,  0.4573,  0.5177, -0.2771],\n", "        [-0.6641,  0.6551,  0.2616, -1.5265, -0.3311],\n", "        [-0.6378,  0.1072,  0.7096,  0.3009, -0.2869]], dtype=torch.float64)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.manual_seed(42)\n", "\n", "k = 5\n", "A = torch.randn(k, k, dtype=torch.float64)\n", "A"]}, {"cell_type": "markdown", "id": "9280ca4a", "metadata": {"origin_pos": 12}, "source": ["### Behavior on Random Data\n", "For simplicity in our toy model,\n", "we will assume that the data vector we feed in $\\mathbf{v}_{in}$\n", "is a random five dimensional Gaussian vector.\n", "Let's think about what we want to have happen.\n", "For context, lets think of a generic ML problem,\n", "where we are trying to turn input data, like an image, into a prediction,\n", "like the probability the image is a picture of a cat.\n", "If repeated application of $\\mathbf{A}$\n", "stretches a random vector out to be very long,\n", "then small changes in input will be amplified\n", "into large changes in output---tiny modifications of the input image\n", "would lead to vastly different predictions.\n", "This does not seem right!\n", "\n", "On the flip side, if $\\mathbf{A}$ shrinks random vectors to be shorter,\n", "then after running through many layers, the vector will essentially shrink to nothing,\n", "and the output will not depend on the input. This is also clearly not right either!\n", "\n", "We need to walk the narrow line between growth and decay\n", "to make sure that our output changes depending on our input, but not much!\n", "\n", "Let's see what happens when we repeatedly multiply our matrix $\\mathbf{A}$\n", "against a random input vector, and keep track of the norm.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "fc46648a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:46.440717Z", "iopub.status.busy": "2023-08-18T19:36:46.439912Z", "iopub.status.idle": "2023-08-18T19:36:46.692198Z", "shell.execute_reply": "2023-08-18T19:36:46.690992Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"239.200491pt\" height=\"193.954688pt\" viewBox=\"0 0 239.**********.954688\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:36:46.644791</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 193.954688 \n", "L 239.**********.954688 \n", "L 239.200491 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 34.**********.398438 \n", "L 229.**********.398438 \n", "L 229.540625 17.798438 \n", "L 34.240625 17.798438 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 43.117898 156.398438 \n", "L 43.117898 17.798438 \n", "\" clip-path=\"url(#p5b1b994c72)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m92119768f2\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m92119768f2\" x=\"43.117898\" y=\"156.398438\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(39.936648 170.996875) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 78.985666 156.398438 \n", "L 78.985666 17.798438 \n", "\" clip-path=\"url(#p5b1b994c72)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m92119768f2\" x=\"78.985666\" y=\"156.398438\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(72.623166 170.996875) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 114.853435 156.398438 \n", "L 114.853435 17.798438 \n", "\" clip-path=\"url(#p5b1b994c72)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m92119768f2\" x=\"114.853435\" y=\"156.398438\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(108.490935 170.996875) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 150.721204 156.398438 \n", "L 150.721204 17.798438 \n", "\" clip-path=\"url(#p5b1b994c72)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m92119768f2\" x=\"150.721204\" y=\"156.398438\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(144.358704 170.996875) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 186.588972 156.398438 \n", "L 186.588972 17.798438 \n", "\" clip-path=\"url(#p5b1b994c72)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m92119768f2\" x=\"186.588972\" y=\"156.398438\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(180.226472 170.996875) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 222.456741 156.398438 \n", "L 222.456741 17.798438 \n", "\" clip-path=\"url(#p5b1b994c72)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m92119768f2\" x=\"222.456741\" y=\"156.398438\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(212.912991 170.996875) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- Iteration -->\n", "     <g transform=\"translate(110.682031 184.675) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-49\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-49\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"29.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"68.701172\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"130.224609\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"171.337891\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"232.617188\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"271.826172\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"299.609375\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"360.791016\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 34.240625 150.098437 \n", "L 229.540625 150.098437 \n", "\" clip-path=\"url(#p5b1b994c72)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"m313c45d2d3\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m313c45d2d3\" x=\"34.240625\" y=\"150.098437\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 153.897656) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 34.240625 117.696298 \n", "L 229.540625 117.696298 \n", "\" clip-path=\"url(#p5b1b994c72)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m313c45d2d3\" x=\"34.240625\" y=\"117.696298\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(20.878125 121.495517) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 34.240625 85.294158 \n", "L 229.540625 85.294158 \n", "\" clip-path=\"url(#p5b1b994c72)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m313c45d2d3\" x=\"34.240625\" y=\"85.294158\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(20.878125 89.093377) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 34.240625 52.892019 \n", "L 229.540625 52.892019 \n", "\" clip-path=\"url(#p5b1b994c72)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m313c45d2d3\" x=\"34.240625\" y=\"52.892019\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(20.878125 56.691237) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 34.240625 20.489879 \n", "L 229.540625 20.489879 \n", "\" clip-path=\"url(#p5b1b994c72)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m313c45d2d3\" x=\"34.240625\" y=\"20.489879\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(20.878125 24.289098) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Value -->\n", "     <g transform=\"translate(14.798438 100.829688) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-56\" d=\"M 1831 0 \n", "L 50 4666 \n", "L 709 4666 \n", "L 2188 738 \n", "L 3669 4666 \n", "L 4325 4666 \n", "L 2547 0 \n", "L 1831 0 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-56\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"60.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"121.9375\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"149.720703\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"213.099609\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- 1e38 -->\n", "     <g transform=\"translate(34.240625 14.798438) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-33\" x=\"125.146484\"/>\n", "      <use xlink:href=\"#DejaVuSans-38\" x=\"188.769531\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 43.117898 150.098437 \n", "L 44.911286 150.098437 \n", "L 46.704675 150.098437 \n", "L 48.498063 150.098437 \n", "L 50.291451 150.098437 \n", "L 52.08484 150.098437 \n", "L 53.878228 150.098437 \n", "L 55.671617 150.098437 \n", "L 57.465005 150.098437 \n", "L 59.258394 150.098437 \n", "L 61.051782 150.098437 \n", "L 62.84517 150.098437 \n", "L 64.638559 150.098437 \n", "L 66.431947 150.098437 \n", "L 68.225336 150.098437 \n", "L 70.018724 150.098437 \n", "L 71.812113 150.098437 \n", "L 73.605501 150.098437 \n", "L 75.398889 150.098437 \n", "L 77.192278 150.098437 \n", "L 78.985666 150.098437 \n", "L 80.779055 150.098437 \n", "L 82.572443 150.098437 \n", "L 84.365832 150.098437 \n", "L 86.15922 150.098437 \n", "L 87.952608 150.098437 \n", "L 89.745997 150.098437 \n", "L 91.539385 150.098437 \n", "L 93.332774 150.098437 \n", "L 95.126162 150.098437 \n", "L 96.919551 150.098437 \n", "L 98.712939 150.098437 \n", "L 100.506327 150.098437 \n", "L 102.299716 150.098437 \n", "L 104.093104 150.098437 \n", "L 105.886493 150.098437 \n", "L 107.679881 150.098437 \n", "L 109.47327 150.098437 \n", "L 111.266658 150.098437 \n", "L 113.060046 150.098437 \n", "L 114.853435 150.098437 \n", "L 116.646823 150.098437 \n", "L 118.440212 150.098437 \n", "L 120.2336 150.098437 \n", "L 122.026989 150.098437 \n", "L 123.820377 150.098437 \n", "L 125.613765 150.098437 \n", "L 127.407154 150.098437 \n", "L 129.200542 150.098437 \n", "L 130.993931 150.098437 \n", "L 132.787319 150.098437 \n", "L 134.580708 150.098437 \n", "L 136.374096 150.098437 \n", "L 138.167485 150.098437 \n", "L 139.960873 150.098437 \n", "L 141.754261 150.098437 \n", "L 143.54765 150.098437 \n", "L 145.341038 150.098437 \n", "L 147.134427 150.098437 \n", "L 148.927815 150.098437 \n", "L 150.721204 150.098437 \n", "L 152.514592 150.098437 \n", "L 154.30798 150.098437 \n", "L 156.101369 150.098437 \n", "L 157.894757 150.098437 \n", "L 159.688146 150.098437 \n", "L 161.481534 150.098437 \n", "L 163.274923 150.098437 \n", "L 165.068311 150.098437 \n", "L 166.861699 150.098437 \n", "L 168.655088 150.098437 \n", "L 170.448476 150.098437 \n", "L 172.241865 150.098437 \n", "L 174.035253 150.098437 \n", "L 175.828642 150.098437 \n", "L 177.62203 150.098437 \n", "L 179.415418 150.098437 \n", "L 181.208807 150.098437 \n", "L 183.002195 150.098437 \n", "L 184.795584 150.098435 \n", "L 186.588972 150.098433 \n", "L 188.382361 150.098425 \n", "L 190.175749 150.098408 \n", "L 191.969137 150.098364 \n", "L 193.762526 150.098258 \n", "L 195.555914 150.097997 \n", "L 197.349303 150.097357 \n", "L 199.142691 150.095786 \n", "L 200.93608 150.091933 \n", "L 202.729468 150.082479 \n", "L 204.522856 150.059288 \n", "L 206.316245 150.002396 \n", "L 208.109633 149.862825 \n", "L 209.903022 149.520424 \n", "L 211.69641 148.680436 \n", "L 213.489799 146.61975 \n", "L 215.283187 141.564407 \n", "L 217.076575 129.162474 \n", "L 218.869964 98.737641 \n", "L 220.663352 24.098438 \n", "\" clip-path=\"url(#p5b1b994c72)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 34.**********.398438 \n", "L 34.240625 17.798438 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 229.**********.398438 \n", "L 229.540625 17.798438 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 34.**********.398438 \n", "L 229.**********.398438 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.240625 17.798438 \n", "L 229.540625 17.798438 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p5b1b994c72\">\n", "   <rect x=\"34.240625\" y=\"17.798438\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calculate the sequence of norms after repeatedly applying `A`\n", "v_in = torch.randn(k, 1, dtype=torch.float64)\n", "\n", "norm_list = [torch.norm(v_in).item()]\n", "for i in range(1, 100):\n", "    v_in = A @ v_in\n", "    norm_list.append(torch.norm(v_in).item())\n", "\n", "d2l.plot(torch.arange(0, 100), norm_list, 'Iteration', 'Value')"]}, {"cell_type": "markdown", "id": "3a575dda", "metadata": {"origin_pos": 16}, "source": ["The norm is growing uncontrollably!\n", "Indeed if we take the list of quotients, we will see a pattern.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "c62eef21", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:46.697008Z", "iopub.status.busy": "2023-08-18T19:36:46.696261Z", "iopub.status.idle": "2023-08-18T19:36:46.960835Z", "shell.execute_reply": "2023-08-18T19:36:46.959907Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"248.759416pt\" height=\"183.35625pt\" viewBox=\"0 0 248.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:36:46.912072</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 248.**********.35625 \n", "L 248.759416 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 43.78125 145.8 \n", "L 239.08125 145.8 \n", "L 239.08125 7.2 \n", "L 43.78125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 50.846834 145.8 \n", "L 50.846834 7.2 \n", "\" clip-path=\"url(#p9764404c5d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"me0aaab5e47\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me0aaab5e47\" x=\"50.846834\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(47.665584 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 87.080601 145.8 \n", "L 87.080601 7.2 \n", "\" clip-path=\"url(#p9764404c5d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#me0aaab5e47\" x=\"87.080601\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(80.718101 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 123.314367 145.8 \n", "L 123.314367 7.2 \n", "\" clip-path=\"url(#p9764404c5d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#me0aaab5e47\" x=\"123.314367\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(116.951867 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 159.548133 145.8 \n", "L 159.548133 7.2 \n", "\" clip-path=\"url(#p9764404c5d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#me0aaab5e47\" x=\"159.548133\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(153.185633 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 195.781899 145.8 \n", "L 195.781899 7.2 \n", "\" clip-path=\"url(#p9764404c5d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#me0aaab5e47\" x=\"195.781899\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(189.419399 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 232.015666 145.8 \n", "L 232.015666 7.2 \n", "\" clip-path=\"url(#p9764404c5d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#me0aaab5e47\" x=\"232.015666\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(222.471916 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- Iteration -->\n", "     <g transform=\"translate(120.222656 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-49\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-49\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"29.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"68.701172\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"130.224609\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"171.337891\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"232.617188\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"271.826172\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"299.609375\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"360.791016\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 43.78125 130.753131 \n", "L 239.08125 130.753131 \n", "\" clip-path=\"url(#p9764404c5d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"mb2cdea8630\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb2cdea8630\" x=\"43.78125\" y=\"130.753131\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.6 -->\n", "      <g transform=\"translate(20.878125 134.55235) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 43.78125 103.286464 \n", "L 239.08125 103.286464 \n", "\" clip-path=\"url(#p9764404c5d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mb2cdea8630\" x=\"43.78125\" y=\"103.286464\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1.8 -->\n", "      <g transform=\"translate(20.878125 107.085683) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 43.78125 75.819797 \n", "L 239.08125 75.819797 \n", "\" clip-path=\"url(#p9764404c5d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mb2cdea8630\" x=\"43.78125\" y=\"75.819797\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(20.878125 79.619016) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 43.78125 48.35313 \n", "L 239.08125 48.35313 \n", "\" clip-path=\"url(#p9764404c5d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mb2cdea8630\" x=\"43.78125\" y=\"48.35313\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2.2 -->\n", "      <g transform=\"translate(20.878125 52.152349) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 43.78125 20.886463 \n", "L 239.08125 20.886463 \n", "\" clip-path=\"url(#p9764404c5d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#mb2cdea8630\" x=\"43.78125\" y=\"20.886463\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 2.4 -->\n", "      <g transform=\"translate(20.878125 24.685682) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Ratio -->\n", "     <g transform=\"translate(14.798437 89.334375) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-52\" d=\"M 2841 2188 \n", "Q 3044 2119 3236 1894 \n", "Q 3428 1669 3622 1275 \n", "L 4263 0 \n", "L 3584 0 \n", "L 2988 1197 \n", "Q 2756 1666 2539 1819 \n", "Q 2322 1972 1947 1972 \n", "L 1259 1972 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "L 2053 4666 \n", "Q 2853 4666 3247 4331 \n", "Q 3641 3997 3641 3322 \n", "Q 3641 2881 3436 2590 \n", "Q 3231 2300 2841 2188 \n", "z\n", "M 1259 4147 \n", "L 1259 2491 \n", "L 2053 2491 \n", "Q 2509 2491 2742 2702 \n", "Q 2975 2913 2975 3322 \n", "Q 2975 3731 2742 3939 \n", "Q 2509 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-52\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"67.232422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"128.511719\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"167.720703\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"195.503906\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 52.658523 139.5 \n", "L 54.470211 107.813054 \n", "L 56.281899 78.442169 \n", "L 58.093588 27.552428 \n", "L 59.905276 28.699157 \n", "L 61.716964 13.672877 \n", "L 63.528653 14.703432 \n", "L 65.340341 15.08475 \n", "L 67.152029 13.5 \n", "L 68.963718 13.69925 \n", "L 70.775406 13.730143 \n", "L 72.587094 13.570856 \n", "L 74.398782 13.590028 \n", "L 76.210471 13.591762 \n", "L 78.022159 13.575737 \n", "L 79.833847 13.577441 \n", "L 81.645536 13.577463 \n", "L 83.457224 13.575845 \n", "L 85.268912 13.57599 \n", "L 87.080601 13.575976 \n", "L 88.892289 13.575812 \n", "L 90.703977 13.575824 \n", "L 92.515666 13.575821 \n", "L 94.327354 13.575804 \n", "L 96.139042 13.575805 \n", "L 97.950731 13.575805 \n", "L 99.762419 13.575803 \n", "L 101.574107 13.575803 \n", "L 103.385795 13.575803 \n", "L 105.197484 13.575803 \n", "L 107.009172 13.575803 \n", "L 108.82086 13.575803 \n", "L 110.632549 13.575803 \n", "L 112.444237 13.575803 \n", "L 114.255925 13.575803 \n", "L 116.067614 13.575803 \n", "L 117.879302 13.575803 \n", "L 119.69099 13.575803 \n", "L 121.502679 13.575803 \n", "L 123.314367 13.575803 \n", "L 125.126055 13.575803 \n", "L 126.937744 13.575803 \n", "L 128.749432 13.575803 \n", "L 130.56112 13.575803 \n", "L 132.372808 13.575803 \n", "L 134.184497 13.575803 \n", "L 135.996185 13.575803 \n", "L 137.807873 13.575803 \n", "L 139.619562 13.575803 \n", "L 141.43125 13.575803 \n", "L 143.242938 13.575803 \n", "L 145.054627 13.575803 \n", "L 146.866315 13.575803 \n", "L 148.678003 13.575803 \n", "L 150.489692 13.575803 \n", "L 152.30138 13.575803 \n", "L 154.113068 13.575803 \n", "L 155.924756 13.575803 \n", "L 157.736445 13.575803 \n", "L 159.548133 13.575803 \n", "L 161.359821 13.575803 \n", "L 163.17151 13.575803 \n", "L 164.983198 13.575803 \n", "L 166.794886 13.575803 \n", "L 168.606575 13.575803 \n", "L 170.418263 13.575803 \n", "L 172.229951 13.575803 \n", "L 174.04164 13.575803 \n", "L 175.853328 13.575803 \n", "L 177.665016 13.575803 \n", "L 179.476705 13.575803 \n", "L 181.288393 13.575803 \n", "L 183.100081 13.575803 \n", "L 184.911769 13.575803 \n", "L 186.723458 13.575803 \n", "L 188.535146 13.575803 \n", "L 190.346834 13.575803 \n", "L 192.158523 13.575803 \n", "L 193.970211 13.575803 \n", "L 195.781899 13.575803 \n", "L 197.593588 13.575803 \n", "L 199.405276 13.575803 \n", "L 201.216964 13.575803 \n", "L 203.028653 13.575803 \n", "L 204.840341 13.575803 \n", "L 206.652029 13.575803 \n", "L 208.463718 13.575803 \n", "L 210.275406 13.575803 \n", "L 212.087094 13.575803 \n", "L 213.898782 13.575803 \n", "L 215.710471 13.575803 \n", "L 217.522159 13.575803 \n", "L 219.333847 13.575803 \n", "L 221.145536 13.575803 \n", "L 222.957224 13.575803 \n", "L 224.768912 13.575803 \n", "L 226.580601 13.575803 \n", "L 228.392289 13.575803 \n", "L 230.203977 13.575803 \n", "\" clip-path=\"url(#p9764404c5d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 43.78125 145.8 \n", "L 43.78125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 239.08125 145.8 \n", "L 239.08125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 43.78125 145.8 \n", "L 239.08125 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 43.78125 7.2 \n", "L 239.08125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p9764404c5d\">\n", "   <rect x=\"43.78125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Compute the scaling factor of the norms\n", "norm_ratio_list = []\n", "for i in range(1, 100):\n", "    norm_ratio_list.append(norm_list[i]/norm_list[i - 1])\n", "\n", "d2l.plot(torch.arange(1, 100), norm_ratio_list, 'Iteration', 'Ratio')"]}, {"cell_type": "markdown", "id": "b6088454", "metadata": {"origin_pos": 20}, "source": ["If we look at the last portion of the above computation,\n", "we see that the random vector is stretched by a factor of `1.974459321485[...]`,\n", "where the portion at the end shifts a little,\n", "but the stretching factor is stable.\n", "\n", "### Relating Back to Eigenvectors\n", "\n", "We have seen that eigenvectors and eigenvalues correspond\n", "to the amount something is stretched,\n", "but that was for specific vectors, and specific stretches.\n", "Let's take a look at what they are for $\\mathbf{A}$.\n", "A bit of a caveat here: it turns out that to see them all,\n", "we will need to go to complex numbers.\n", "You can think of these as stretches and rotations.\n", "By taking the norm of the complex number\n", "(square root of the sums of squares of real and imaginary parts)\n", "we can measure that stretching factor. Let's also sort them.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "d9744b0c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:46.964699Z", "iopub.status.busy": "2023-08-18T19:36:46.964385Z", "iopub.status.idle": "2023-08-18T19:36:46.974480Z", "shell.execute_reply": "2023-08-18T19:36:46.973455Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["norms of eigenvalues: [tensor(0.3490), tensor(1.1296), tensor(1.1296), tensor(1.1828), tensor(2.4532)]\n"]}], "source": ["# Compute the eigenvalues\n", "eigs = torch.linalg.eig(A).eigenvalues.tolist()\n", "norm_eigs = [torch.abs(torch.tensor(x)) for x in eigs]\n", "norm_eigs.sort()\n", "print(f'norms of eigenvalues: {norm_eigs}')"]}, {"cell_type": "markdown", "id": "8a7e18b1", "metadata": {"origin_pos": 24}, "source": ["### An Observation\n", "\n", "We see something a bit unexpected happening here:\n", "that number we identified before for the\n", "long term stretching of our matrix $\\mathbf{A}$\n", "applied to a random vector is *exactly*\n", "(accurate to thirteen decimal places!)\n", "the largest eigenvalue of $\\mathbf{A}$.\n", "This is clearly not a coincidence!\n", "\n", "But, if we now think about what is happening geometrically,\n", "this starts to make sense. Consider a random vector.\n", "This random vector points a little in every direction,\n", "so in particular, it points at least a little bit\n", "in the same direction as the eigenvector of $\\mathbf{A}$\n", "associated with the largest eigenvalue.\n", "This is so important that it is called\n", "the *principle eigenvalue* and *principle eigenvector*.\n", "After applying $\\mathbf{A}$, our random vector\n", "gets stretched in every possible direction,\n", "as is associated with every possible eigenvector,\n", "but it is stretched most of all in the direction\n", "associated with this principle eigenvector.\n", "What this means is that after apply in $A$,\n", "our random vector is longer, and points in a direction\n", "closer to being aligned with the principle eigenvector.\n", "After applying the matrix many times,\n", "the alignment with the principle eigenvector becomes closer and closer until,\n", "for all practical purposes, our random vector has been transformed\n", "into the principle eigenvector!\n", "Indeed this algorithm is the basis\n", "for what is known as the *power iteration*\n", "for finding the largest eigenvalue and eigenvector of a matrix. For details see, for example, :cite:`Golub.Van-Loan.1996`.\n", "\n", "### Fixing the Normalization\n", "\n", "Now, from above discussions, we concluded\n", "that we do not want a random vector to be stretched or squished at all,\n", "we would like random vectors to stay about the same size throughout the entire process.\n", "To do so, we now rescale our matrix by this principle eigenvalue\n", "so that the largest eigenvalue is instead now just one.\n", "Let's see what happens in this case.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "d67c7bc4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:46.979667Z", "iopub.status.busy": "2023-08-18T19:36:46.977983Z", "iopub.status.idle": "2023-08-18T19:36:47.253730Z", "shell.execute_reply": "2023-08-18T19:36:47.252638Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"248.741116pt\" height=\"183.35625pt\" viewBox=\"0 0 248.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:36:47.200404</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 248.**********.35625 \n", "L 248.741116 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 43.78125 145.8 \n", "L 239.08125 145.8 \n", "L 239.08125 7.2 \n", "L 43.78125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 52.658523 145.8 \n", "L 52.658523 7.2 \n", "\" clip-path=\"url(#p439a392e1a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m9ff9bc4eee\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9ff9bc4eee\" x=\"52.658523\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(49.477273 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 88.526291 145.8 \n", "L 88.526291 7.2 \n", "\" clip-path=\"url(#p439a392e1a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m9ff9bc4eee\" x=\"88.526291\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(82.163791 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 124.39406 145.8 \n", "L 124.39406 7.2 \n", "\" clip-path=\"url(#p439a392e1a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m9ff9bc4eee\" x=\"124.39406\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(118.03156 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 160.261829 145.8 \n", "L 160.261829 7.2 \n", "\" clip-path=\"url(#p439a392e1a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m9ff9bc4eee\" x=\"160.261829\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(153.899329 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 196.129597 145.8 \n", "L 196.129597 7.2 \n", "\" clip-path=\"url(#p439a392e1a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m9ff9bc4eee\" x=\"196.129597\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(189.767097 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 231.997366 145.8 \n", "L 231.997366 7.2 \n", "\" clip-path=\"url(#p439a392e1a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m9ff9bc4eee\" x=\"231.997366\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(222.453616 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- Iteration -->\n", "     <g transform=\"translate(120.222656 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-49\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-49\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"29.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"68.701172\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"130.224609\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"171.337891\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"232.617188\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"271.826172\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"299.609375\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"360.791016\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 43.78125 123.894946 \n", "L 239.08125 123.894946 \n", "\" clip-path=\"url(#p439a392e1a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"medffab094e\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#medffab094e\" x=\"43.78125\" y=\"123.894946\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 2.1 -->\n", "      <g transform=\"translate(20.878125 127.694165) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 43.78125 93.43637 \n", "L 239.08125 93.43637 \n", "\" clip-path=\"url(#p439a392e1a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#medffab094e\" x=\"43.78125\" y=\"93.43637\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.2 -->\n", "      <g transform=\"translate(20.878125 97.235589) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 43.78125 62.977794 \n", "L 239.08125 62.977794 \n", "\" clip-path=\"url(#p439a392e1a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#medffab094e\" x=\"43.78125\" y=\"62.977794\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 2.3 -->\n", "      <g transform=\"translate(20.878125 66.777013) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 43.78125 32.519218 \n", "L 239.08125 32.519218 \n", "\" clip-path=\"url(#p439a392e1a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#medffab094e\" x=\"43.78125\" y=\"32.519218\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2.4 -->\n", "      <g transform=\"translate(20.878125 36.318436) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- Value -->\n", "     <g transform=\"translate(14.798437 90.23125) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-56\" d=\"M 1831 0 \n", "L 50 4666 \n", "L 709 4666 \n", "L 2188 738 \n", "L 3669 4666 \n", "L 4325 4666 \n", "L 2547 0 \n", "L 1831 0 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-56\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"60.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"121.9375\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"149.720703\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"213.099609\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 52.658523 62.036527 \n", "L 54.451911 13.5 \n", "L 56.2453 139.5 \n", "L 58.038688 126.740779 \n", "L 59.832076 121.921025 \n", "L 61.625465 132.934703 \n", "L 63.418853 129.984174 \n", "L 65.212242 129.553445 \n", "L 67.00563 130.609525 \n", "L 68.799019 130.305278 \n", "L 70.592407 130.267843 \n", "L 72.385795 130.371306 \n", "L 74.179184 130.341495 \n", "L 75.972572 130.33834 \n", "L 77.765961 130.348517 \n", "L 79.559349 130.345626 \n", "L 81.352738 130.345379 \n", "L 83.146126 130.34639 \n", "L 84.939514 130.346119 \n", "L 86.732903 130.346109 \n", "L 88.526291 130.346218 \n", "L 90.31968 130.346202 \n", "L 92.113068 130.346211 \n", "L 93.906457 130.346231 \n", "L 95.699845 130.346239 \n", "L 97.493233 130.346249 \n", "L 99.286622 130.34626 \n", "L 101.08001 130.34627 \n", "L 102.873399 130.346281 \n", "L 104.666787 130.346291 \n", "L 106.460176 130.346302 \n", "L 108.253564 130.346312 \n", "L 110.046952 130.346322 \n", "L 111.840341 130.346333 \n", "L 113.633729 130.346343 \n", "L 115.427118 130.346354 \n", "L 117.220506 130.346364 \n", "L 119.013895 130.346374 \n", "L 120.807283 130.346385 \n", "L 122.600671 130.346395 \n", "L 124.39406 130.346405 \n", "L 126.187448 130.346416 \n", "L 127.980837 130.346426 \n", "L 129.774225 130.346437 \n", "L 131.567614 130.346447 \n", "L 133.361002 130.346457 \n", "L 135.15439 130.346468 \n", "L 136.947779 130.346478 \n", "L 138.741167 130.346489 \n", "L 140.534556 130.346499 \n", "L 142.327944 130.346509 \n", "L 144.121333 130.34652 \n", "L 145.914721 130.34653 \n", "L 147.70811 130.346541 \n", "L 149.501498 130.346551 \n", "L 151.294886 130.346561 \n", "L 153.088275 130.346572 \n", "L 154.881663 130.346582 \n", "L 156.675052 130.346592 \n", "L 158.46844 130.346603 \n", "L 160.261829 130.346613 \n", "L 162.055217 130.346624 \n", "L 163.848605 130.346634 \n", "L 165.641994 130.346644 \n", "L 167.435382 130.346655 \n", "L 169.228771 130.346665 \n", "L 171.022159 130.346676 \n", "L 172.815548 130.346686 \n", "L 174.608936 130.346696 \n", "L 176.402324 130.346707 \n", "L 178.195713 130.346717 \n", "L 179.989101 130.346728 \n", "L 181.78249 130.346738 \n", "L 183.575878 130.346748 \n", "L 185.369267 130.346759 \n", "L 187.162655 130.346769 \n", "L 188.956043 130.34678 \n", "L 190.749432 130.34679 \n", "L 192.54282 130.3468 \n", "L 194.336209 130.346811 \n", "L 196.129597 130.346821 \n", "L 197.922986 130.346831 \n", "L 199.716374 130.346842 \n", "L 201.509762 130.346852 \n", "L 203.303151 130.346863 \n", "L 205.096539 130.346873 \n", "L 206.889928 130.346883 \n", "L 208.683316 130.346894 \n", "L 210.476705 130.346904 \n", "L 212.270093 130.346915 \n", "L 214.063481 130.346925 \n", "L 215.85687 130.346935 \n", "L 217.650258 130.346946 \n", "L 219.443647 130.346956 \n", "L 221.237035 130.346967 \n", "L 223.030424 130.346977 \n", "L 224.823812 130.346987 \n", "L 226.6172 130.346998 \n", "L 228.410589 130.347008 \n", "L 230.203977 130.347018 \n", "\" clip-path=\"url(#p439a392e1a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 43.78125 145.8 \n", "L 43.78125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 239.08125 145.8 \n", "L 239.08125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 43.78125 145.8 \n", "L 239.08125 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 43.78125 7.2 \n", "L 239.08125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p439a392e1a\">\n", "   <rect x=\"43.78125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Rescale the matrix `A`\n", "A /= norm_eigs[-1]\n", "\n", "# Do the same experiment again\n", "v_in = torch.randn(k, 1, dtype=torch.float64)\n", "\n", "norm_list = [torch.norm(v_in).item()]\n", "for i in range(1, 100):\n", "    v_in = A @ v_in\n", "    norm_list.append(torch.norm(v_in).item())\n", "\n", "d2l.plot(torch.arange(0, 100), norm_list, 'Iteration', 'Value')"]}, {"cell_type": "markdown", "id": "02ea7957", "metadata": {"origin_pos": 28}, "source": ["We can also plot the ratio between consecutive norms as before and see that indeed it stabilizes.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "9bb79e3d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:47.259182Z", "iopub.status.busy": "2023-08-18T19:36:47.257830Z", "iopub.status.idle": "2023-08-18T19:36:47.540436Z", "shell.execute_reply": "2023-08-18T19:36:47.539269Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"255.121916pt\" height=\"183.35625pt\" viewBox=\"0 0 255.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:36:47.480696</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 255.**********.35625 \n", "L 255.121916 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 50.14375 145.8 \n", "L 245.44375 145.8 \n", "L 245.44375 7.2 \n", "L 50.14375 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 57.209334 145.8 \n", "L 57.209334 7.2 \n", "\" clip-path=\"url(#pf7d3f9ac01)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mcc8f78a1b5\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mcc8f78a1b5\" x=\"57.209334\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(54.028084 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 93.443101 145.8 \n", "L 93.443101 7.2 \n", "\" clip-path=\"url(#pf7d3f9ac01)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mcc8f78a1b5\" x=\"93.443101\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(87.080601 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 129.676867 145.8 \n", "L 129.676867 7.2 \n", "\" clip-path=\"url(#pf7d3f9ac01)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mcc8f78a1b5\" x=\"129.676867\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(123.314367 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 165.910633 145.8 \n", "L 165.910633 7.2 \n", "\" clip-path=\"url(#pf7d3f9ac01)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mcc8f78a1b5\" x=\"165.910633\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(159.548133 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 202.144399 145.8 \n", "L 202.144399 7.2 \n", "\" clip-path=\"url(#pf7d3f9ac01)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mcc8f78a1b5\" x=\"202.144399\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(195.781899 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 238.378166 145.8 \n", "L 238.378166 7.2 \n", "\" clip-path=\"url(#pf7d3f9ac01)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mcc8f78a1b5\" x=\"238.378166\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(228.834416 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- Iteration -->\n", "     <g transform=\"translate(126.585156 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-49\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-49\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"29.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"68.701172\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"130.224609\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"171.337891\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"232.617188\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"271.826172\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"299.609375\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"360.791016\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 50.14375 129.94083 \n", "L 245.44375 129.94083 \n", "\" clip-path=\"url(#pf7d3f9ac01)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"m5115878c8e\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5115878c8e\" x=\"50.14375\" y=\"129.94083\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.85 -->\n", "      <g transform=\"translate(20.878125 133.740049) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 50.14375 103.379303 \n", "L 245.44375 103.379303 \n", "\" clip-path=\"url(#pf7d3f9ac01)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m5115878c8e\" x=\"50.14375\" y=\"103.379303\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.90 -->\n", "      <g transform=\"translate(20.878125 107.178522) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 50.14375 76.817775 \n", "L 245.44375 76.817775 \n", "\" clip-path=\"url(#pf7d3f9ac01)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m5115878c8e\" x=\"50.14375\" y=\"76.817775\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.95 -->\n", "      <g transform=\"translate(20.878125 80.616994) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 50.14375 50.256248 \n", "L 245.44375 50.256248 \n", "\" clip-path=\"url(#pf7d3f9ac01)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m5115878c8e\" x=\"50.14375\" y=\"50.256248\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.00 -->\n", "      <g transform=\"translate(20.878125 54.055467) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 50.14375 23.69472 \n", "L 245.44375 23.69472 \n", "\" clip-path=\"url(#pf7d3f9ac01)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m5115878c8e\" x=\"50.14375\" y=\"23.69472\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.05 -->\n", "      <g transform=\"translate(20.878125 27.493939) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Ratio -->\n", "     <g transform=\"translate(14.798437 89.334375) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-52\" d=\"M 2841 2188 \n", "Q 3044 2119 3236 1894 \n", "Q 3428 1669 3622 1275 \n", "L 4263 0 \n", "L 3584 0 \n", "L 2988 1197 \n", "Q 2756 1666 2539 1819 \n", "Q 2322 1972 1947 1972 \n", "L 1259 1972 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "L 2053 4666 \n", "Q 2853 4666 3247 4331 \n", "Q 3641 3997 3641 3322 \n", "Q 3641 2881 3436 2590 \n", "Q 3231 2300 2841 2188 \n", "z\n", "M 1259 4147 \n", "L 1259 2491 \n", "L 2053 2491 \n", "Q 2509 2491 2742 2702 \n", "Q 2975 2913 2975 3322 \n", "Q 2975 3731 2742 3939 \n", "Q 2509 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-52\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"67.232422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"128.511719\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"167.720703\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"195.503906\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 59.021023 13.5 \n", "L 60.832711 139.5 \n", "L 62.644399 39.394363 \n", "L 64.456088 46.235419 \n", "L 66.267776 59.375271 \n", "L 68.079464 47.770624 \n", "L 69.891153 49.895077 \n", "L 71.702841 51.14118 \n", "L 73.514529 50.000881 \n", "L 75.326218 50.224842 \n", "L 77.137906 50.343042 \n", "L 78.949594 50.231235 \n", "L 80.761282 50.253602 \n", "L 82.572971 50.264786 \n", "L 84.384659 50.253822 \n", "L 86.196347 50.256041 \n", "L 88.008036 50.257096 \n", "L 89.819724 50.256021 \n", "L 91.631412 50.25624 \n", "L 93.443101 50.256339 \n", "L 95.254789 50.256234 \n", "L 97.066477 50.256256 \n", "L 98.878166 50.256265 \n", "L 100.689854 50.256254 \n", "L 102.501542 50.256257 \n", "L 104.313231 50.256257 \n", "L 106.124919 50.256256 \n", "L 107.936607 50.256257 \n", "L 109.748295 50.256257 \n", "L 111.559984 50.256257 \n", "L 113.371672 50.256257 \n", "L 115.18336 50.256257 \n", "L 116.995049 50.256257 \n", "L 118.806737 50.256257 \n", "L 120.618425 50.256257 \n", "L 122.430114 50.256257 \n", "L 124.241802 50.256257 \n", "L 126.05349 50.256257 \n", "L 127.865179 50.256257 \n", "L 129.676867 50.256257 \n", "L 131.488555 50.256257 \n", "L 133.300244 50.256257 \n", "L 135.111932 50.256257 \n", "L 136.92362 50.256257 \n", "L 138.735308 50.256257 \n", "L 140.546997 50.256257 \n", "L 142.358685 50.256257 \n", "L 144.170373 50.256257 \n", "L 145.982062 50.256257 \n", "L 147.79375 50.256257 \n", "L 149.605438 50.256257 \n", "L 151.417127 50.256257 \n", "L 153.228815 50.256257 \n", "L 155.040503 50.256257 \n", "L 156.852192 50.256257 \n", "L 158.66388 50.256257 \n", "L 160.475568 50.256257 \n", "L 162.287256 50.256257 \n", "L 164.098945 50.256257 \n", "L 165.910633 50.256257 \n", "L 167.722321 50.256257 \n", "L 169.53401 50.256257 \n", "L 171.345698 50.256257 \n", "L 173.157386 50.256257 \n", "L 174.969075 50.256257 \n", "L 176.780763 50.256257 \n", "L 178.592451 50.256257 \n", "L 180.40414 50.256257 \n", "L 182.215828 50.256257 \n", "L 184.027516 50.256257 \n", "L 185.839205 50.256257 \n", "L 187.650893 50.256257 \n", "L 189.462581 50.256257 \n", "L 191.274269 50.256257 \n", "L 193.085958 50.256257 \n", "L 194.897646 50.256257 \n", "L 196.709334 50.256257 \n", "L 198.521023 50.256257 \n", "L 200.332711 50.256257 \n", "L 202.144399 50.256257 \n", "L 203.956088 50.256257 \n", "L 205.767776 50.256257 \n", "L 207.579464 50.256257 \n", "L 209.391153 50.256257 \n", "L 211.202841 50.256257 \n", "L 213.014529 50.256257 \n", "L 214.826218 50.256257 \n", "L 216.637906 50.256257 \n", "L 218.449594 50.256257 \n", "L 220.261282 50.256257 \n", "L 222.072971 50.256257 \n", "L 223.884659 50.256257 \n", "L 225.696347 50.256257 \n", "L 227.508036 50.256257 \n", "L 229.319724 50.256257 \n", "L 231.131412 50.256257 \n", "L 232.943101 50.256257 \n", "L 234.754789 50.256257 \n", "L 236.566477 50.256257 \n", "\" clip-path=\"url(#pf7d3f9ac01)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 50.14375 145.8 \n", "L 50.14375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 245.44375 145.8 \n", "L 245.44375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 50.14375 145.8 \n", "L 245.44375 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 50.14375 7.2 \n", "L 245.44375 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf7d3f9ac01\">\n", "   <rect x=\"50.14375\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Also plot the ratio\n", "norm_ratio_list = []\n", "for i in range(1, 100):\n", "    norm_ratio_list.append(norm_list[i]/norm_list[i-1])\n", "\n", "d2l.plot(torch.arange(1, 100), norm_ratio_list, 'Iteration', 'Ratio')"]}, {"cell_type": "markdown", "id": "e5424ed2", "metadata": {"origin_pos": 32}, "source": ["## Discussion\n", "\n", "We now see exactly what we hoped for!\n", "After normalizing the matrices by the principal eigenvalue,\n", "we see that the random data does not explode as before,\n", "but rather eventually equilibrates to a specific value.\n", "It would be nice to be able to do these things from first principles,\n", "and it turns out that if we look deeply at the mathematics of it,\n", "we can see that the largest eigenvalue\n", "of a large random matrix with independent mean zero,\n", "variance one Gaussian entries is on average about $\\sqrt{n}$,\n", "or in our case $\\sqrt{5} \\approx 2.2$,\n", "due to a fascinating fact known as the *circular law* :cite:`Ginibre.1965`.\n", "The relationship between the eigenvalues (and a related object called singular values) of random matrices has been shown to have deep connections to proper initialization of neural networks as was discussed in :citet:`Pennington.Schoenholz.Ganguli.2017` and subsequent works.\n", "\n", "## Summary\n", "* Eigenvectors are vectors which are stretched by a matrix without changing direction.\n", "* Eigenvalues are the amount that the eigenvectors are stretched by the application of the matrix.\n", "* The eigendecomposition of a matrix can allow for many operations to be reduced to operations on the eigenvalues.\n", "* The Gershgorin Circle Theorem can provide approximate values for the eigenvalues of a matrix.\n", "* The behavior of iterated matrix powers depends primarily on the size of the largest eigenvalue.  This understanding has many applications in the theory of neural network initialization.\n", "\n", "## Exercises\n", "1. What are the eigenvalues and eigenvectors of\n", "$$\n", "\\mathbf{A} = \\begin{bmatrix}\n", "2 & 1 \\\\\n", "1 & 2\n", "\\end{bmatrix}?\n", "$$\n", "1.  What are the eigenvalues and eigenvectors of the following matrix, and what is strange about this example compared to the previous one?\n", "$$\n", "\\mathbf{A} = \\begin{bmatrix}\n", "2 & 1 \\\\\n", "0 & 2\n", "\\end{bmatrix}.\n", "$$\n", "1. Without computing the eigenvalues, is it possible that the smallest eigenvalue of the following matrix is less that $0.5$? *Note*: this problem can be done in your head.\n", "$$\n", "\\mathbf{A} = \\begin{bmatrix}\n", "3.0 & 0.1 & 0.3 & 1.0 \\\\\n", "0.1 & 1.0 & 0.1 & 0.2 \\\\\n", "0.3 & 0.1 & 5.0 & 0.0 \\\\\n", "1.0 & 0.2 & 0.0 & 1.8\n", "\\end{bmatrix}.\n", "$$\n"]}, {"cell_type": "markdown", "id": "e0f40af6", "metadata": {"origin_pos": 34, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1086)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "eb43f686", "metadata": {"origin_pos": 0}, "source": ["# Integral Calculus\n", ":label:`sec_integral_calculus`\n", "\n", "Differentiation only makes up half of the content of a traditional calculus education.  The other pillar, integration, starts out seeming a rather disjoint question, \"What is the area underneath this curve?\"  While seemingly unrelated, integration is tightly intertwined with the differentiation via what is known as the *fundamental theorem of calculus*.\n", "\n", "At the level of machine learning we discuss in this book, we will not need a deep understanding of integration. However, we will provide a brief introduction to lay the groundwork for any further applications we will encounter later on.\n", "\n", "## Geometric Interpretation\n", "Suppose that we have a function $f(x)$.  For simplicity, let's assume that $f(x)$ is non-negative (never takes a value less than zero).  What we want to try and understand is: what is the area contained between $f(x)$ and the $x$-axis?\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f1905b21", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:26.075515Z", "iopub.status.busy": "2023-08-18T19:28:26.074947Z", "iopub.status.idle": "2023-08-18T19:28:29.142397Z", "shell.execute_reply": "2023-08-18T19:28:29.141546Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"232.603125pt\" height=\"169.678125pt\" viewBox=\"0 0 232.**********.678125\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:28:29.107216</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 169.678125 \n", "L 232.**********.678125 \n", "L 232.603125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"PolyCollection_1\">\n", "    <path d=\"M 38.980398 139.5 \n", "L 38.980398 137.192229 \n", "L 39.425373 137.098288 \n", "L 39.870349 137.001022 \n", "L 40.315325 136.900337 \n", "L 40.7603 136.796136 \n", "L 41.205276 136.688321 \n", "L 41.650252 136.576792 \n", "L 42.095233 136.461445 \n", "L 42.540208 136.34218 \n", "L 42.985184 136.218889 \n", "L 43.43016 136.091467 \n", "L 43.875135 135.959804 \n", "L 44.320111 135.823792 \n", "L 44.765086 135.683317 \n", "L 45.210062 135.538266 \n", "L 45.655043 135.388523 \n", "L 46.100013 135.233978 \n", "L 46.544989 135.074506 \n", "L 46.989965 134.909992 \n", "L 47.43494 134.740314 \n", "L 47.879916 134.565351 \n", "L 48.324892 134.384979 \n", "L 48.769867 134.199073 \n", "L 49.214848 134.007509 \n", "L 49.659824 133.810161 \n", "L 50.1048 133.606902 \n", "L 50.549775 133.397602 \n", "L 50.994751 133.182132 \n", "L 51.439727 132.960362 \n", "L 51.884702 132.732161 \n", "L 52.329678 132.497398 \n", "L 52.774659 132.255938 \n", "L 53.219634 132.007653 \n", "L 53.66461 131.752408 \n", "L 54.109586 131.490071 \n", "L 54.554561 131.220504 \n", "L 54.999537 130.943579 \n", "L 55.444513 130.659159 \n", "L 55.889488 130.367112 \n", "L 56.334469 130.067301 \n", "L 56.77944 129.759604 \n", "L 57.224415 129.443875 \n", "L 57.669391 129.119991 \n", "L 58.114367 128.787817 \n", "L 58.559342 128.447223 \n", "L 59.004318 128.09808 \n", "L 59.449294 127.740261 \n", "L 59.894275 127.373635 \n", "L 60.33925 126.998085 \n", "L 60.784226 126.613477 \n", "L 61.229202 126.219698 \n", "L 61.674177 125.81662 \n", "L 62.119153 125.404131 \n", "L 62.564128 124.982109 \n", "L 63.009104 124.550441 \n", "L 63.454085 124.109011 \n", "L 63.899055 123.657728 \n", "L 64.344031 123.196469 \n", "L 64.789007 122.725132 \n", "L 65.233982 122.243626 \n", "L 65.678958 121.751844 \n", "L 66.123934 121.249698 \n", "L 66.568909 120.7371 \n", "L 67.01389 120.213951 \n", "L 67.458866 119.680191 \n", "L 67.903842 119.135733 \n", "L 68.348817 118.580497 \n", "L 68.793793 118.014426 \n", "L 69.238769 117.437447 \n", "L 69.683744 116.849505 \n", "L 70.12872 116.250543 \n", "L 70.573701 115.640511 \n", "L 71.018676 115.019376 \n", "L 71.463652 114.387091 \n", "L 71.908628 113.743632 \n", "L 72.353603 113.088965 \n", "L 72.798579 112.423075 \n", "L 73.243555 111.745945 \n", "L 73.68853 111.057577 \n", "L 74.133511 110.35795 \n", "L 74.578482 109.647104 \n", "L 75.023457 108.925028 \n", "L 75.468433 108.191746 \n", "L 75.913409 107.447292 \n", "L 76.358384 106.691696 \n", "L 76.80336 105.925003 \n", "L 77.248336 105.147264 \n", "L 77.693317 104.358529 \n", "L 78.138292 103.558886 \n", "L 78.583268 102.748394 \n", "L 79.028243 101.927144 \n", "L 79.473219 101.095226 \n", "L 79.918195 100.252733 \n", "L 80.36317 99.399779 \n", "L 80.808146 98.536487 \n", "L 81.253127 97.662962 \n", "L 81.698103 96.77937 \n", "L 82.143078 95.885838 \n", "L 82.588054 94.982517 \n", "L 83.03303 94.069579 \n", "L 83.478008 93.147186 \n", "L 83.922984 92.215524 \n", "L 84.367959 91.274787 \n", "L 84.812935 90.32517 \n", "L 85.257911 89.366877 \n", "L 85.702886 88.400133 \n", "L 86.147862 87.42516 \n", "L 86.592837 86.442197 \n", "L 87.037816 85.451487 \n", "L 87.482791 84.453297 \n", "L 87.927767 83.447879 \n", "L 88.372743 82.435513 \n", "L 88.817718 81.416483 \n", "L 89.262694 80.391076 \n", "L 89.70767 79.359597 \n", "L 90.152645 78.322354 \n", "L 90.597624 77.279662 \n", "L 91.042599 76.231856 \n", "L 91.487575 75.179274 \n", "L 91.932551 74.122253 \n", "L 92.377526 73.061154 \n", "L 92.822502 71.996337 \n", "L 93.267478 70.928164 \n", "L 93.712453 69.857023 \n", "L 94.157432 68.783292 \n", "L 94.602407 67.707361 \n", "L 95.047383 66.629649 \n", "L 95.492358 65.55054 \n", "L 95.937334 64.470463 \n", "L 96.38231 63.38983 \n", "L 96.827285 62.309077 \n", "L 97.272261 61.228632 \n", "L 97.717239 60.14893 \n", "L 98.162215 59.07043 \n", "L 98.607191 57.993574 \n", "L 99.052166 56.918814 \n", "L 99.497145 55.846615 \n", "L 99.94212 54.777443 \n", "L 100.387096 53.711762 \n", "L 100.832072 52.650055 \n", "L 101.27705 51.592779 \n", "L 101.722026 50.540436 \n", "L 102.167001 49.493494 \n", "L 102.611977 48.452447 \n", "L 103.056952 47.417776 \n", "L 103.501928 46.38997 \n", "L 103.946904 45.369524 \n", "L 104.391879 44.356918 \n", "L 104.836858 43.352648 \n", "L 105.281833 42.357211 \n", "L 105.726809 41.371101 \n", "L 106.171785 40.394799 \n", "L 106.616762 39.428802 \n", "L 107.061737 38.473596 \n", "L 107.506714 37.529664 \n", "L 107.95169 36.5975 \n", "L 108.396666 35.677585 \n", "L 108.841643 34.770384 \n", "L 109.286618 33.876394 \n", "L 109.731594 32.996065 \n", "L 110.17657 32.129886 \n", "L 110.621545 31.278298 \n", "L 111.066522 30.441776 \n", "L 111.511498 29.620763 \n", "L 111.956473 28.815709 \n", "L 112.40145 28.027056 \n", "L 112.846426 27.255235 \n", "L 113.291402 26.500671 \n", "L 113.736379 25.76378 \n", "L 114.181354 25.04498 \n", "L 114.626331 24.34467 \n", "L 115.071307 23.663248 \n", "L 115.516283 23.00109 \n", "L 115.96126 22.358579 \n", "L 116.406235 21.736082 \n", "L 116.851211 21.133953 \n", "L 117.296187 20.552537 \n", "L 117.741164 19.992165 \n", "L 118.186139 19.453174 \n", "L 118.631116 18.935857 \n", "L 119.076092 18.44053 \n", "L 119.521067 17.967479 \n", "L 119.966044 17.516973 \n", "L 120.411019 17.089283 \n", "L 120.855996 16.684664 \n", "L 121.300972 16.303342 \n", "L 121.745948 15.945557 \n", "L 122.190924 15.611504 \n", "L 122.6359 15.301401 \n", "L 123.080876 15.015413 \n", "L 123.525852 14.75372 \n", "L 123.970828 14.516481 \n", "L 124.415804 14.303823 \n", "L 124.86078 14.115888 \n", "L 125.305756 13.952782 \n", "L 125.750733 13.814609 \n", "L 126.195709 13.701438 \n", "L 126.640685 13.613351 \n", "L 127.085661 13.550393 \n", "L 127.530637 13.512602 \n", "L 127.975613 13.5 \n", "L 128.420589 13.512602 \n", "L 128.865565 13.550393 \n", "L 129.310541 13.613351 \n", "L 129.755517 13.701438 \n", "L 130.200493 13.814609 \n", "L 130.645469 13.952782 \n", "L 131.090445 14.115888 \n", "L 131.535421 14.303823 \n", "L 131.980397 14.516481 \n", "L 132.425374 14.75372 \n", "L 132.87035 15.015413 \n", "L 133.315326 15.301401 \n", "L 133.760302 15.611504 \n", "L 134.205278 15.945557 \n", "L 134.650254 16.303342 \n", "L 135.09523 16.684664 \n", "L 135.540206 17.089283 \n", "L 135.985182 17.516973 \n", "L 136.430158 17.967479 \n", "L 136.875134 18.44053 \n", "L 137.32011 18.935857 \n", "L 137.765086 19.453174 \n", "L 138.210062 19.992165 \n", "L 138.655038 20.552537 \n", "L 139.100015 21.133953 \n", "L 139.54499 21.736082 \n", "L 139.989966 22.358579 \n", "L 140.434943 23.00109 \n", "L 140.879919 23.663248 \n", "L 141.324894 24.34467 \n", "L 141.769871 25.04498 \n", "L 142.214847 25.76378 \n", "L 142.659823 26.500664 \n", "L 143.1048 27.255235 \n", "L 143.549775 28.027056 \n", "L 143.994751 28.815709 \n", "L 144.439728 29.620763 \n", "L 144.884703 30.441776 \n", "L 145.329679 31.278298 \n", "L 145.774656 32.129886 \n", "L 146.219632 32.996065 \n", "L 146.664609 33.876394 \n", "L 147.109584 34.770384 \n", "L 147.55456 35.677585 \n", "L 147.999537 36.597507 \n", "L 148.444513 37.529671 \n", "L 148.889488 38.473596 \n", "L 149.334464 39.428802 \n", "L 149.77944 40.394799 \n", "L 150.224417 41.371101 \n", "L 150.669392 42.357211 \n", "L 151.114368 43.352648 \n", "L 151.559344 44.35691 \n", "L 152.004319 45.369516 \n", "L 152.449298 46.38997 \n", "L 152.894273 47.417776 \n", "L 153.339249 48.452447 \n", "L 153.784224 49.493494 \n", "L 154.2292 50.540436 \n", "L 154.674178 51.592786 \n", "L 155.119154 52.650055 \n", "L 155.56413 53.711762 \n", "L 156.009105 54.777443 \n", "L 156.454081 55.846615 \n", "L 156.899057 56.918807 \n", "L 157.344032 57.993567 \n", "L 157.789008 59.070422 \n", "L 158.233986 60.14893 \n", "L 158.678962 61.228632 \n", "L 159.123938 62.309069 \n", "L 159.568913 63.38983 \n", "L 160.013892 64.470463 \n", "L 160.458867 65.55054 \n", "L 160.903843 66.629649 \n", "L 161.348818 67.707361 \n", "L 161.793797 68.783292 \n", "L 162.238772 69.857023 \n", "L 162.683748 70.928164 \n", "L 163.128724 71.996337 \n", "L 163.573699 73.061154 \n", "L 164.018675 74.122253 \n", "L 164.463651 75.179274 \n", "L 164.908626 76.231856 \n", "L 165.353605 77.27967 \n", "L 165.79858 78.322354 \n", "L 166.243556 79.359597 \n", "L 166.688532 80.391076 \n", "L 167.133507 81.416483 \n", "L 167.578483 82.435513 \n", "L 168.023459 83.447879 \n", "L 168.468434 84.453297 \n", "L 168.913413 85.451495 \n", "L 169.358388 86.442197 \n", "L 169.803364 87.42516 \n", "L 170.248339 88.400133 \n", "L 170.693315 89.366877 \n", "L 171.138291 90.32517 \n", "L 171.583266 91.274787 \n", "L 172.028242 92.215524 \n", "L 172.47322 93.147189 \n", "L 172.918196 94.069579 \n", "L 173.363172 94.982517 \n", "L 173.808147 95.885838 \n", "L 174.253123 96.77937 \n", "L 174.698099 97.662962 \n", "L 175.143074 98.536476 \n", "L 175.58805 99.399771 \n", "L 176.033026 100.252722 \n", "L 176.478001 101.095215 \n", "L 176.922977 101.927137 \n", "L 177.367958 102.748394 \n", "L 177.812933 103.558886 \n", "L 178.257909 104.358529 \n", "L 178.702885 105.147256 \n", "L 179.14786 105.924996 \n", "L 179.592836 106.691684 \n", "L 180.037812 107.447284 \n", "L 180.482787 108.191739 \n", "L 180.927768 108.925028 \n", "L 181.372744 109.647104 \n", "L 181.81772 110.357962 \n", "L 182.262695 111.057577 \n", "L 182.707671 111.745945 \n", "L 183.152647 112.423075 \n", "L 183.597622 113.088965 \n", "L 184.042598 113.743632 \n", "L 184.487579 114.387101 \n", "L 184.932549 115.019376 \n", "L 185.377525 115.640511 \n", "L 185.822501 116.250536 \n", "L 186.267476 116.849497 \n", "L 186.712452 117.43744 \n", "L 187.157427 118.014417 \n", "L 187.602403 118.580494 \n", "L 188.047384 119.135733 \n", "L 188.49236 119.680191 \n", "L 188.937335 120.213951 \n", "L 189.382311 120.737092 \n", "L 189.827287 121.249692 \n", "L 190.272262 121.751838 \n", "L 190.717238 122.24362 \n", "L 191.162214 122.725128 \n", "L 191.607195 123.196469 \n", "L 192.05217 123.657728 \n", "L 192.497146 124.109019 \n", "L 192.942122 124.550441 \n", "L 193.387097 124.982109 \n", "L 193.832073 125.404131 \n", "L 194.277048 125.81662 \n", "L 194.722024 126.219698 \n", "L 195.167005 126.613483 \n", "L 195.611975 126.998085 \n", "L 196.056951 127.373635 \n", "L 196.501927 127.740258 \n", "L 196.946902 128.098077 \n", "L 197.391878 128.447217 \n", "L 197.836854 128.787813 \n", "L 198.281829 129.119985 \n", "L 198.72681 129.443875 \n", "L 199.171786 129.759604 \n", "L 199.616762 130.067306 \n", "L 200.061737 130.367112 \n", "L 200.506713 130.659159 \n", "L 200.951689 130.943579 \n", "L 201.396664 131.220504 \n", "L 201.84164 131.490071 \n", "L 202.286621 131.75241 \n", "L 202.731591 132.007653 \n", "L 203.176567 132.255938 \n", "L 203.621542 132.497396 \n", "L 204.066518 132.732159 \n", "L 204.511494 132.96036 \n", "L 204.956469 133.182129 \n", "L 205.401445 133.397599 \n", "L 205.846426 133.606902 \n", "L 206.291402 133.810161 \n", "L 206.736377 134.007509 \n", "L 207.181353 134.199071 \n", "L 207.626329 134.384976 \n", "L 208.071304 134.565348 \n", "L 208.51628 134.740312 \n", "L 208.961256 134.909991 \n", "L 209.406237 135.074506 \n", "L 209.851212 135.233978 \n", "L 210.296188 135.388525 \n", "L 210.741164 135.538266 \n", "L 211.186139 135.683317 \n", "L 211.631115 135.823792 \n", "L 212.07609 135.959804 \n", "L 212.521066 136.091467 \n", "L 212.966047 136.218891 \n", "L 213.411017 136.34218 \n", "L 213.855993 136.461445 \n", "L 214.300969 136.576791 \n", "L 214.745944 136.68832 \n", "L 215.19092 136.796135 \n", "L 215.635896 136.900336 \n", "L 216.080871 137.001021 \n", "L 216.525852 137.098288 \n", "L 216.525852 139.5 \n", "L 216.525852 139.5 \n", "L 216.080871 139.5 \n", "L 215.635896 139.5 \n", "L 215.19092 139.5 \n", "L 214.745944 139.5 \n", "L 214.300969 139.5 \n", "L 213.855993 139.5 \n", "L 213.411017 139.5 \n", "L 212.966047 139.5 \n", "L 212.521066 139.5 \n", "L 212.07609 139.5 \n", "L 211.631115 139.5 \n", "L 211.186139 139.5 \n", "L 210.741164 139.5 \n", "L 210.296188 139.5 \n", "L 209.851212 139.5 \n", "L 209.406237 139.5 \n", "L 208.961256 139.5 \n", "L 208.51628 139.5 \n", "L 208.071304 139.5 \n", "L 207.626329 139.5 \n", "L 207.181353 139.5 \n", "L 206.736377 139.5 \n", "L 206.291402 139.5 \n", "L 205.846426 139.5 \n", "L 205.401445 139.5 \n", "L 204.956469 139.5 \n", "L 204.511494 139.5 \n", "L 204.066518 139.5 \n", "L 203.621542 139.5 \n", "L 203.176567 139.5 \n", "L 202.731591 139.5 \n", "L 202.286621 139.5 \n", "L 201.84164 139.5 \n", "L 201.396664 139.5 \n", "L 200.951689 139.5 \n", "L 200.506713 139.5 \n", "L 200.061737 139.5 \n", "L 199.616762 139.5 \n", "L 199.171786 139.5 \n", "L 198.72681 139.5 \n", "L 198.281829 139.5 \n", "L 197.836854 139.5 \n", "L 197.391878 139.5 \n", "L 196.946902 139.5 \n", "L 196.501927 139.5 \n", "L 196.056951 139.5 \n", "L 195.611975 139.5 \n", "L 195.167005 139.5 \n", "L 194.722024 139.5 \n", "L 194.277048 139.5 \n", "L 193.832073 139.5 \n", "L 193.387097 139.5 \n", "L 192.942122 139.5 \n", "L 192.497146 139.5 \n", "L 192.05217 139.5 \n", "L 191.607195 139.5 \n", "L 191.162214 139.5 \n", "L 190.717238 139.5 \n", "L 190.272262 139.5 \n", "L 189.827287 139.5 \n", "L 189.382311 139.5 \n", "L 188.937335 139.5 \n", "L 188.49236 139.5 \n", "L 188.047384 139.5 \n", "L 187.602403 139.5 \n", "L 187.157427 139.5 \n", "L 186.712452 139.5 \n", "L 186.267476 139.5 \n", "L 185.822501 139.5 \n", "L 185.377525 139.5 \n", "L 184.932549 139.5 \n", "L 184.487579 139.5 \n", "L 184.042598 139.5 \n", "L 183.597622 139.5 \n", "L 183.152647 139.5 \n", "L 182.707671 139.5 \n", "L 182.262695 139.5 \n", "L 181.81772 139.5 \n", "L 181.372744 139.5 \n", "L 180.927768 139.5 \n", "L 180.482787 139.5 \n", "L 180.037812 139.5 \n", "L 179.592836 139.5 \n", "L 179.14786 139.5 \n", "L 178.702885 139.5 \n", "L 178.257909 139.5 \n", "L 177.812933 139.5 \n", "L 177.367958 139.5 \n", "L 176.922977 139.5 \n", "L 176.478001 139.5 \n", "L 176.033026 139.5 \n", "L 175.58805 139.5 \n", "L 175.143074 139.5 \n", "L 174.698099 139.5 \n", "L 174.253123 139.5 \n", "L 173.808147 139.5 \n", "L 173.363172 139.5 \n", "L 172.918196 139.5 \n", "L 172.47322 139.5 \n", "L 172.028242 139.5 \n", "L 171.583266 139.5 \n", "L 171.138291 139.5 \n", "L 170.693315 139.5 \n", "L 170.248339 139.5 \n", "L 169.803364 139.5 \n", "L 169.358388 139.5 \n", "L 168.913413 139.5 \n", "L 168.468434 139.5 \n", "L 168.023459 139.5 \n", "L 167.578483 139.5 \n", "L 167.133507 139.5 \n", "L 166.688532 139.5 \n", "L 166.243556 139.5 \n", "L 165.79858 139.5 \n", "L 165.353605 139.5 \n", "L 164.908626 139.5 \n", "L 164.463651 139.5 \n", "L 164.018675 139.5 \n", "L 163.573699 139.5 \n", "L 163.128724 139.5 \n", "L 162.683748 139.5 \n", "L 162.238772 139.5 \n", "L 161.793797 139.5 \n", "L 161.348818 139.5 \n", "L 160.903843 139.5 \n", "L 160.458867 139.5 \n", "L 160.013892 139.5 \n", "L 159.568913 139.5 \n", "L 159.123938 139.5 \n", "L 158.678962 139.5 \n", "L 158.233986 139.5 \n", "L 157.789008 139.5 \n", "L 157.344032 139.5 \n", "L 156.899057 139.5 \n", "L 156.454081 139.5 \n", "L 156.009105 139.5 \n", "L 155.56413 139.5 \n", "L 155.119154 139.5 \n", "L 154.674178 139.5 \n", "L 154.2292 139.5 \n", "L 153.784224 139.5 \n", "L 153.339249 139.5 \n", "L 152.894273 139.5 \n", "L 152.449298 139.5 \n", "L 152.004319 139.5 \n", "L 151.559344 139.5 \n", "L 151.114368 139.5 \n", "L 150.669392 139.5 \n", "L 150.224417 139.5 \n", "L 149.77944 139.5 \n", "L 149.334464 139.5 \n", "L 148.889488 139.5 \n", "L 148.444513 139.5 \n", "L 147.999537 139.5 \n", "L 147.55456 139.5 \n", "L 147.109584 139.5 \n", "L 146.664609 139.5 \n", "L 146.219632 139.5 \n", "L 145.774656 139.5 \n", "L 145.329679 139.5 \n", "L 144.884703 139.5 \n", "L 144.439728 139.5 \n", "L 143.994751 139.5 \n", "L 143.549775 139.5 \n", "L 143.1048 139.5 \n", "L 142.659823 139.5 \n", "L 142.214847 139.5 \n", "L 141.769871 139.5 \n", "L 141.324894 139.5 \n", "L 140.879919 139.5 \n", "L 140.434943 139.5 \n", "L 139.989966 139.5 \n", "L 139.54499 139.5 \n", "L 139.100015 139.5 \n", "L 138.655038 139.5 \n", "L 138.210062 139.5 \n", "L 137.765086 139.5 \n", "L 137.32011 139.5 \n", "L 136.875134 139.5 \n", "L 136.430158 139.5 \n", "L 135.985182 139.5 \n", "L 135.540206 139.5 \n", "L 135.09523 139.5 \n", "L 134.650254 139.5 \n", "L 134.205278 139.5 \n", "L 133.760302 139.5 \n", "L 133.315326 139.5 \n", "L 132.87035 139.5 \n", "L 132.425374 139.5 \n", "L 131.980397 139.5 \n", "L 131.535421 139.5 \n", "L 131.090445 139.5 \n", "L 130.645469 139.5 \n", "L 130.200493 139.5 \n", "L 129.755517 139.5 \n", "L 129.310541 139.5 \n", "L 128.865565 139.5 \n", "L 128.420589 139.5 \n", "L 127.975613 139.5 \n", "L 127.530637 139.5 \n", "L 127.085661 139.5 \n", "L 126.640685 139.5 \n", "L 126.195709 139.5 \n", "L 125.750733 139.5 \n", "L 125.305756 139.5 \n", "L 124.86078 139.5 \n", "L 124.415804 139.5 \n", "L 123.970828 139.5 \n", "L 123.525852 139.5 \n", "L 123.080876 139.5 \n", "L 122.6359 139.5 \n", "L 122.190924 139.5 \n", "L 121.745948 139.5 \n", "L 121.300972 139.5 \n", "L 120.855996 139.5 \n", "L 120.411019 139.5 \n", "L 119.966044 139.5 \n", "L 119.521067 139.5 \n", "L 119.076092 139.5 \n", "L 118.631116 139.5 \n", "L 118.186139 139.5 \n", "L 117.741164 139.5 \n", "L 117.296187 139.5 \n", "L 116.851211 139.5 \n", "L 116.406235 139.5 \n", "L 115.96126 139.5 \n", "L 115.516283 139.5 \n", "L 115.071307 139.5 \n", "L 114.626331 139.5 \n", "L 114.181354 139.5 \n", "L 113.736379 139.5 \n", "L 113.291402 139.5 \n", "L 112.846426 139.5 \n", "L 112.40145 139.5 \n", "L 111.956473 139.5 \n", "L 111.511498 139.5 \n", "L 111.066522 139.5 \n", "L 110.621545 139.5 \n", "L 110.17657 139.5 \n", "L 109.731594 139.5 \n", "L 109.286618 139.5 \n", "L 108.841643 139.5 \n", "L 108.396666 139.5 \n", "L 107.95169 139.5 \n", "L 107.506714 139.5 \n", "L 107.061737 139.5 \n", "L 106.616762 139.5 \n", "L 106.171785 139.5 \n", "L 105.726809 139.5 \n", "L 105.281833 139.5 \n", "L 104.836858 139.5 \n", "L 104.391879 139.5 \n", "L 103.946904 139.5 \n", "L 103.501928 139.5 \n", "L 103.056952 139.5 \n", "L 102.611977 139.5 \n", "L 102.167001 139.5 \n", "L 101.722026 139.5 \n", "L 101.27705 139.5 \n", "L 100.832072 139.5 \n", "L 100.387096 139.5 \n", "L 99.94212 139.5 \n", "L 99.497145 139.5 \n", "L 99.052166 139.5 \n", "L 98.607191 139.5 \n", "L 98.162215 139.5 \n", "L 97.717239 139.5 \n", "L 97.272261 139.5 \n", "L 96.827285 139.5 \n", "L 96.38231 139.5 \n", "L 95.937334 139.5 \n", "L 95.492358 139.5 \n", "L 95.047383 139.5 \n", "L 94.602407 139.5 \n", "L 94.157432 139.5 \n", "L 93.712453 139.5 \n", "L 93.267478 139.5 \n", "L 92.822502 139.5 \n", "L 92.377526 139.5 \n", "L 91.932551 139.5 \n", "L 91.487575 139.5 \n", "L 91.042599 139.5 \n", "L 90.597624 139.5 \n", "L 90.152645 139.5 \n", "L 89.70767 139.5 \n", "L 89.262694 139.5 \n", "L 88.817718 139.5 \n", "L 88.372743 139.5 \n", "L 87.927767 139.5 \n", "L 87.482791 139.5 \n", "L 87.037816 139.5 \n", "L 86.592837 139.5 \n", "L 86.147862 139.5 \n", "L 85.702886 139.5 \n", "L 85.257911 139.5 \n", "L 84.812935 139.5 \n", "L 84.367959 139.5 \n", "L 83.922984 139.5 \n", "L 83.478008 139.5 \n", "L 83.03303 139.5 \n", "L 82.588054 139.5 \n", "L 82.143078 139.5 \n", "L 81.698103 139.5 \n", "L 81.253127 139.5 \n", "L 80.808146 139.5 \n", "L 80.36317 139.5 \n", "L 79.918195 139.5 \n", "L 79.473219 139.5 \n", "L 79.028243 139.5 \n", "L 78.583268 139.5 \n", "L 78.138292 139.5 \n", "L 77.693317 139.5 \n", "L 77.248336 139.5 \n", "L 76.80336 139.5 \n", "L 76.358384 139.5 \n", "L 75.913409 139.5 \n", "L 75.468433 139.5 \n", "L 75.023457 139.5 \n", "L 74.578482 139.5 \n", "L 74.133511 139.5 \n", "L 73.68853 139.5 \n", "L 73.243555 139.5 \n", "L 72.798579 139.5 \n", "L 72.353603 139.5 \n", "L 71.908628 139.5 \n", "L 71.463652 139.5 \n", "L 71.018676 139.5 \n", "L 70.573701 139.5 \n", "L 70.12872 139.5 \n", "L 69.683744 139.5 \n", "L 69.238769 139.5 \n", "L 68.793793 139.5 \n", "L 68.348817 139.5 \n", "L 67.903842 139.5 \n", "L 67.458866 139.5 \n", "L 67.01389 139.5 \n", "L 66.568909 139.5 \n", "L 66.123934 139.5 \n", "L 65.678958 139.5 \n", "L 65.233982 139.5 \n", "L 64.789007 139.5 \n", "L 64.344031 139.5 \n", "L 63.899055 139.5 \n", "L 63.454085 139.5 \n", "L 63.009104 139.5 \n", "L 62.564128 139.5 \n", "L 62.119153 139.5 \n", "L 61.674177 139.5 \n", "L 61.229202 139.5 \n", "L 60.784226 139.5 \n", "L 60.33925 139.5 \n", "L 59.894275 139.5 \n", "L 59.449294 139.5 \n", "L 59.004318 139.5 \n", "L 58.559342 139.5 \n", "L 58.114367 139.5 \n", "L 57.669391 139.5 \n", "L 57.224415 139.5 \n", "L 56.77944 139.5 \n", "L 56.334469 139.5 \n", "L 55.889488 139.5 \n", "L 55.444513 139.5 \n", "L 54.999537 139.5 \n", "L 54.554561 139.5 \n", "L 54.109586 139.5 \n", "L 53.66461 139.5 \n", "L 53.219634 139.5 \n", "L 52.774659 139.5 \n", "L 52.329678 139.5 \n", "L 51.884702 139.5 \n", "L 51.439727 139.5 \n", "L 50.994751 139.5 \n", "L 50.549775 139.5 \n", "L 50.1048 139.5 \n", "L 49.659824 139.5 \n", "L 49.214848 139.5 \n", "L 48.769867 139.5 \n", "L 48.324892 139.5 \n", "L 47.879916 139.5 \n", "L 47.43494 139.5 \n", "L 46.989965 139.5 \n", "L 46.544989 139.5 \n", "L 46.100013 139.5 \n", "L 45.655043 139.5 \n", "L 45.210062 139.5 \n", "L 44.765086 139.5 \n", "L 44.320111 139.5 \n", "L 43.875135 139.5 \n", "L 43.43016 139.5 \n", "L 42.985184 139.5 \n", "L 42.540208 139.5 \n", "L 42.095233 139.5 \n", "L 41.650252 139.5 \n", "L 41.205276 139.5 \n", "L 40.7603 139.5 \n", "L 40.315325 139.5 \n", "L 39.870349 139.5 \n", "L 39.425373 139.5 \n", "L 38.980398 139.5 \n", "z\n", "\" clip-path=\"url(#pdfb410c2b7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mc6cbb9b269\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc6cbb9b269\" x=\"38.980398\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(31.609304 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mc6cbb9b269\" x=\"83.478005\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(76.106912 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mc6cbb9b269\" x=\"127.975613\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(124.794363 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mc6cbb9b269\" x=\"172.47322\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(169.29197 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mc6cbb9b269\" x=\"216.970828\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(213.789578 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"ma78928ee9b\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma78928ee9b\" x=\"30.103125\" y=\"139.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 143.299219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#ma78928ee9b\" x=\"30.103125\" y=\"114.3\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 118.099219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#ma78928ee9b\" x=\"30.103125\" y=\"89.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 92.899219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#ma78928ee9b\" x=\"30.103125\" y=\"63.9\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 67.699219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#ma78928ee9b\" x=\"30.103125\" y=\"38.7\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.499219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#ma78928ee9b\" x=\"30.103125\" y=\"13.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 17.299219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 38.980398 137.192229 \n", "L 42.540208 136.34218 \n", "L 45.655043 135.388523 \n", "L 48.769867 134.199073 \n", "L 51.439727 132.960362 \n", "L 54.109586 131.490071 \n", "L 56.77944 129.759604 \n", "L 59.449294 127.740261 \n", "L 62.119153 125.404131 \n", "L 64.789007 122.725132 \n", "L 67.458866 119.680191 \n", "L 70.12872 116.250543 \n", "L 72.798579 112.423075 \n", "L 75.468433 108.191746 \n", "L 78.138292 103.558886 \n", "L 81.253127 97.662962 \n", "L 84.367959 91.274787 \n", "L 87.927767 83.447879 \n", "L 92.377526 73.061154 \n", "L 105.726809 41.371101 \n", "L 108.841643 34.770384 \n", "L 111.511498 29.620763 \n", "L 113.736379 25.76378 \n", "L 115.96126 22.358579 \n", "L 117.741164 19.992165 \n", "L 119.521067 17.967479 \n", "L 121.300972 16.303342 \n", "L 122.6359 15.301401 \n", "L 123.970828 14.516481 \n", "L 125.305756 13.952782 \n", "L 126.640685 13.613351 \n", "L 127.975613 13.5 \n", "L 129.310541 13.613351 \n", "L 130.645469 13.952782 \n", "L 131.980397 14.516481 \n", "L 133.315326 15.301401 \n", "L 134.650254 16.303342 \n", "L 136.430158 17.967479 \n", "L 138.210062 19.992165 \n", "L 139.989966 22.358579 \n", "L 142.214847 25.76378 \n", "L 144.439728 29.620763 \n", "L 147.109584 34.770384 \n", "L 150.224417 41.371101 \n", "L 153.784224 49.493494 \n", "L 159.123938 62.309069 \n", "L 166.688532 80.391076 \n", "L 170.693315 89.366877 \n", "L 174.253123 96.77937 \n", "L 177.367958 102.748394 \n", "L 180.482787 108.191739 \n", "L 183.152647 112.423075 \n", "L 185.822501 116.250536 \n", "L 188.49236 119.680191 \n", "L 191.162214 122.725128 \n", "L 193.832073 125.404131 \n", "L 196.501927 127.740258 \n", "L 199.171786 129.759604 \n", "L 201.84164 131.490071 \n", "L 204.511494 132.96036 \n", "L 207.181353 134.199071 \n", "L 210.296188 135.388525 \n", "L 213.411017 136.34218 \n", "L 216.525852 137.098288 \n", "L 216.525852 137.098288 \n", "\" clip-path=\"url(#pdfb410c2b7)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pdfb410c2b7\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "import torch\n", "from IPython import display\n", "from mpl_toolkits import mplot3d\n", "from d2l import torch as d2l\n", "\n", "x = torch.arange(-2, 2, 0.01)\n", "f = torch.exp(-x**2)\n", "\n", "d2l.set_figsize()\n", "d2l.plt.plot(x, f, color='black')\n", "d2l.plt.fill_between(x.tolist(), f.tolist())\n", "d2l.plt.show()"]}, {"cell_type": "markdown", "id": "03fcf056", "metadata": {"origin_pos": 4}, "source": ["In most cases, this area will be infinite or undefined (consider the area under $f(x) = x^{2}$), so people will often talk about the area between a pair of ends, say $a$ and $b$.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "b68f6242", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:29.146790Z", "iopub.status.busy": "2023-08-18T19:28:29.146114Z", "iopub.status.idle": "2023-08-18T19:28:29.298482Z", "shell.execute_reply": "2023-08-18T19:28:29.297428Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"232.603125pt\" height=\"169.678125pt\" viewBox=\"0 0 232.**********.678125\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:28:29.263731</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 169.678125 \n", "L 232.**********.678125 \n", "L 232.603125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"PolyCollection_1\">\n", "    <path d=\"M 61.229202 139.5 \n", "L 61.229202 126.219698 \n", "L 61.674177 125.81662 \n", "L 62.119153 125.404131 \n", "L 62.564128 124.982109 \n", "L 63.009104 124.550441 \n", "L 63.454085 124.109011 \n", "L 63.899055 123.657728 \n", "L 64.344031 123.196469 \n", "L 64.789007 122.725132 \n", "L 65.233982 122.243626 \n", "L 65.678958 121.751844 \n", "L 66.123934 121.249698 \n", "L 66.568909 120.7371 \n", "L 67.01389 120.213951 \n", "L 67.458866 119.680191 \n", "L 67.903842 119.135733 \n", "L 68.348817 118.580497 \n", "L 68.793793 118.014426 \n", "L 69.238769 117.437447 \n", "L 69.683744 116.849505 \n", "L 70.12872 116.250543 \n", "L 70.573701 115.640511 \n", "L 71.018676 115.019376 \n", "L 71.463652 114.387091 \n", "L 71.908628 113.743632 \n", "L 72.353603 113.088965 \n", "L 72.798579 112.423075 \n", "L 73.243555 111.745945 \n", "L 73.68853 111.057577 \n", "L 74.133511 110.35795 \n", "L 74.578482 109.647104 \n", "L 75.023457 108.925028 \n", "L 75.468433 108.191746 \n", "L 75.913409 107.447292 \n", "L 76.358384 106.691696 \n", "L 76.80336 105.925003 \n", "L 77.248336 105.147264 \n", "L 77.693317 104.358529 \n", "L 78.138292 103.558886 \n", "L 78.583268 102.748394 \n", "L 79.028243 101.927144 \n", "L 79.473219 101.095226 \n", "L 79.918195 100.252733 \n", "L 80.36317 99.399779 \n", "L 80.808146 98.536487 \n", "L 81.253127 97.662962 \n", "L 81.698103 96.77937 \n", "L 82.143078 95.885838 \n", "L 82.588054 94.982517 \n", "L 83.03303 94.069579 \n", "L 83.478008 93.147186 \n", "L 83.922984 92.215524 \n", "L 84.367959 91.274787 \n", "L 84.812935 90.32517 \n", "L 85.257911 89.366877 \n", "L 85.702886 88.400133 \n", "L 86.147862 87.42516 \n", "L 86.592837 86.442197 \n", "L 87.037816 85.451487 \n", "L 87.482791 84.453297 \n", "L 87.927767 83.447879 \n", "L 88.372743 82.435513 \n", "L 88.817718 81.416483 \n", "L 89.262694 80.391076 \n", "L 89.70767 79.359597 \n", "L 90.152645 78.322354 \n", "L 90.597624 77.279662 \n", "L 91.042599 76.231856 \n", "L 91.487575 75.179274 \n", "L 91.932551 74.122253 \n", "L 92.377526 73.061154 \n", "L 92.822502 71.996337 \n", "L 93.267478 70.928164 \n", "L 93.712453 69.857023 \n", "L 94.157432 68.783292 \n", "L 94.602407 67.707361 \n", "L 95.047383 66.629649 \n", "L 95.492358 65.55054 \n", "L 95.937334 64.470463 \n", "L 96.38231 63.38983 \n", "L 96.827285 62.309077 \n", "L 97.272261 61.228632 \n", "L 97.717239 60.14893 \n", "L 98.162215 59.07043 \n", "L 98.607191 57.993574 \n", "L 99.052166 56.918814 \n", "L 99.497145 55.846615 \n", "L 99.94212 54.777443 \n", "L 100.387096 53.711762 \n", "L 100.832072 52.650055 \n", "L 101.27705 51.592779 \n", "L 101.722026 50.540436 \n", "L 102.167001 49.493494 \n", "L 102.611977 48.452447 \n", "L 103.056952 47.417776 \n", "L 103.501928 46.38997 \n", "L 103.946904 45.369524 \n", "L 104.391879 44.356918 \n", "L 104.836858 43.352648 \n", "L 105.281833 42.357211 \n", "L 105.726809 41.371101 \n", "L 106.171785 40.394799 \n", "L 106.616762 39.428802 \n", "L 107.061737 38.473596 \n", "L 107.506714 37.529664 \n", "L 107.95169 36.5975 \n", "L 108.396666 35.677585 \n", "L 108.841643 34.770384 \n", "L 109.286618 33.876394 \n", "L 109.731594 32.996065 \n", "L 110.17657 32.129886 \n", "L 110.621545 31.278298 \n", "L 111.066522 30.441776 \n", "L 111.511498 29.620763 \n", "L 111.956473 28.815709 \n", "L 112.40145 28.027056 \n", "L 112.846426 27.255235 \n", "L 113.291402 26.500671 \n", "L 113.736379 25.76378 \n", "L 114.181354 25.04498 \n", "L 114.626331 24.34467 \n", "L 115.071307 23.663248 \n", "L 115.516283 23.00109 \n", "L 115.96126 22.358579 \n", "L 116.406235 21.736082 \n", "L 116.851211 21.133953 \n", "L 117.296187 20.552537 \n", "L 117.741164 19.992165 \n", "L 118.186139 19.453174 \n", "L 118.631116 18.935857 \n", "L 119.076092 18.44053 \n", "L 119.521067 17.967479 \n", "L 119.966044 17.516973 \n", "L 120.411019 17.089283 \n", "L 120.855996 16.684664 \n", "L 121.300972 16.303342 \n", "L 121.745948 15.945557 \n", "L 122.190924 15.611504 \n", "L 122.6359 15.301401 \n", "L 123.080876 15.015413 \n", "L 123.525852 14.75372 \n", "L 123.970828 14.516481 \n", "L 124.415804 14.303823 \n", "L 124.86078 14.115888 \n", "L 125.305756 13.952782 \n", "L 125.750733 13.814609 \n", "L 126.195709 13.701438 \n", "L 126.640685 13.613351 \n", "L 127.085661 13.550393 \n", "L 127.530637 13.512602 \n", "L 127.975613 13.5 \n", "L 128.420589 13.512602 \n", "L 128.865565 13.550393 \n", "L 129.310541 13.613351 \n", "L 129.755517 13.701438 \n", "L 130.200493 13.814609 \n", "L 130.645469 13.952782 \n", "L 131.090445 14.115888 \n", "L 131.535421 14.303823 \n", "L 131.980397 14.516481 \n", "L 132.425374 14.75372 \n", "L 132.87035 15.015413 \n", "L 133.315326 15.301401 \n", "L 133.760302 15.611504 \n", "L 134.205278 15.945557 \n", "L 134.650254 16.303342 \n", "L 135.09523 16.684664 \n", "L 135.540206 17.089283 \n", "L 135.985182 17.516973 \n", "L 136.430158 17.967479 \n", "L 136.875134 18.44053 \n", "L 137.32011 18.935857 \n", "L 137.765086 19.453174 \n", "L 138.210062 19.992165 \n", "L 138.655038 20.552537 \n", "L 139.100015 21.133953 \n", "L 139.54499 21.736082 \n", "L 139.989966 22.358579 \n", "L 140.434943 23.00109 \n", "L 140.879919 23.663248 \n", "L 141.324894 24.34467 \n", "L 141.769871 25.04498 \n", "L 142.214847 25.76378 \n", "L 142.659823 26.500664 \n", "L 143.1048 27.255235 \n", "L 143.549775 28.027056 \n", "L 143.994751 28.815709 \n", "L 144.439728 29.620763 \n", "L 144.884703 30.441776 \n", "L 145.329679 31.278298 \n", "L 145.774656 32.129886 \n", "L 146.219632 32.996065 \n", "L 146.664609 33.876394 \n", "L 147.109584 34.770384 \n", "L 147.55456 35.677585 \n", "L 147.999537 36.597507 \n", "L 148.444513 37.529671 \n", "L 148.889488 38.473596 \n", "L 149.334464 39.428802 \n", "L 149.77944 40.394799 \n", "L 149.77944 139.5 \n", "L 149.77944 139.5 \n", "L 149.334464 139.5 \n", "L 148.889488 139.5 \n", "L 148.444513 139.5 \n", "L 147.999537 139.5 \n", "L 147.55456 139.5 \n", "L 147.109584 139.5 \n", "L 146.664609 139.5 \n", "L 146.219632 139.5 \n", "L 145.774656 139.5 \n", "L 145.329679 139.5 \n", "L 144.884703 139.5 \n", "L 144.439728 139.5 \n", "L 143.994751 139.5 \n", "L 143.549775 139.5 \n", "L 143.1048 139.5 \n", "L 142.659823 139.5 \n", "L 142.214847 139.5 \n", "L 141.769871 139.5 \n", "L 141.324894 139.5 \n", "L 140.879919 139.5 \n", "L 140.434943 139.5 \n", "L 139.989966 139.5 \n", "L 139.54499 139.5 \n", "L 139.100015 139.5 \n", "L 138.655038 139.5 \n", "L 138.210062 139.5 \n", "L 137.765086 139.5 \n", "L 137.32011 139.5 \n", "L 136.875134 139.5 \n", "L 136.430158 139.5 \n", "L 135.985182 139.5 \n", "L 135.540206 139.5 \n", "L 135.09523 139.5 \n", "L 134.650254 139.5 \n", "L 134.205278 139.5 \n", "L 133.760302 139.5 \n", "L 133.315326 139.5 \n", "L 132.87035 139.5 \n", "L 132.425374 139.5 \n", "L 131.980397 139.5 \n", "L 131.535421 139.5 \n", "L 131.090445 139.5 \n", "L 130.645469 139.5 \n", "L 130.200493 139.5 \n", "L 129.755517 139.5 \n", "L 129.310541 139.5 \n", "L 128.865565 139.5 \n", "L 128.420589 139.5 \n", "L 127.975613 139.5 \n", "L 127.530637 139.5 \n", "L 127.085661 139.5 \n", "L 126.640685 139.5 \n", "L 126.195709 139.5 \n", "L 125.750733 139.5 \n", "L 125.305756 139.5 \n", "L 124.86078 139.5 \n", "L 124.415804 139.5 \n", "L 123.970828 139.5 \n", "L 123.525852 139.5 \n", "L 123.080876 139.5 \n", "L 122.6359 139.5 \n", "L 122.190924 139.5 \n", "L 121.745948 139.5 \n", "L 121.300972 139.5 \n", "L 120.855996 139.5 \n", "L 120.411019 139.5 \n", "L 119.966044 139.5 \n", "L 119.521067 139.5 \n", "L 119.076092 139.5 \n", "L 118.631116 139.5 \n", "L 118.186139 139.5 \n", "L 117.741164 139.5 \n", "L 117.296187 139.5 \n", "L 116.851211 139.5 \n", "L 116.406235 139.5 \n", "L 115.96126 139.5 \n", "L 115.516283 139.5 \n", "L 115.071307 139.5 \n", "L 114.626331 139.5 \n", "L 114.181354 139.5 \n", "L 113.736379 139.5 \n", "L 113.291402 139.5 \n", "L 112.846426 139.5 \n", "L 112.40145 139.5 \n", "L 111.956473 139.5 \n", "L 111.511498 139.5 \n", "L 111.066522 139.5 \n", "L 110.621545 139.5 \n", "L 110.17657 139.5 \n", "L 109.731594 139.5 \n", "L 109.286618 139.5 \n", "L 108.841643 139.5 \n", "L 108.396666 139.5 \n", "L 107.95169 139.5 \n", "L 107.506714 139.5 \n", "L 107.061737 139.5 \n", "L 106.616762 139.5 \n", "L 106.171785 139.5 \n", "L 105.726809 139.5 \n", "L 105.281833 139.5 \n", "L 104.836858 139.5 \n", "L 104.391879 139.5 \n", "L 103.946904 139.5 \n", "L 103.501928 139.5 \n", "L 103.056952 139.5 \n", "L 102.611977 139.5 \n", "L 102.167001 139.5 \n", "L 101.722026 139.5 \n", "L 101.27705 139.5 \n", "L 100.832072 139.5 \n", "L 100.387096 139.5 \n", "L 99.94212 139.5 \n", "L 99.497145 139.5 \n", "L 99.052166 139.5 \n", "L 98.607191 139.5 \n", "L 98.162215 139.5 \n", "L 97.717239 139.5 \n", "L 97.272261 139.5 \n", "L 96.827285 139.5 \n", "L 96.38231 139.5 \n", "L 95.937334 139.5 \n", "L 95.492358 139.5 \n", "L 95.047383 139.5 \n", "L 94.602407 139.5 \n", "L 94.157432 139.5 \n", "L 93.712453 139.5 \n", "L 93.267478 139.5 \n", "L 92.822502 139.5 \n", "L 92.377526 139.5 \n", "L 91.932551 139.5 \n", "L 91.487575 139.5 \n", "L 91.042599 139.5 \n", "L 90.597624 139.5 \n", "L 90.152645 139.5 \n", "L 89.70767 139.5 \n", "L 89.262694 139.5 \n", "L 88.817718 139.5 \n", "L 88.372743 139.5 \n", "L 87.927767 139.5 \n", "L 87.482791 139.5 \n", "L 87.037816 139.5 \n", "L 86.592837 139.5 \n", "L 86.147862 139.5 \n", "L 85.702886 139.5 \n", "L 85.257911 139.5 \n", "L 84.812935 139.5 \n", "L 84.367959 139.5 \n", "L 83.922984 139.5 \n", "L 83.478008 139.5 \n", "L 83.03303 139.5 \n", "L 82.588054 139.5 \n", "L 82.143078 139.5 \n", "L 81.698103 139.5 \n", "L 81.253127 139.5 \n", "L 80.808146 139.5 \n", "L 80.36317 139.5 \n", "L 79.918195 139.5 \n", "L 79.473219 139.5 \n", "L 79.028243 139.5 \n", "L 78.583268 139.5 \n", "L 78.138292 139.5 \n", "L 77.693317 139.5 \n", "L 77.248336 139.5 \n", "L 76.80336 139.5 \n", "L 76.358384 139.5 \n", "L 75.913409 139.5 \n", "L 75.468433 139.5 \n", "L 75.023457 139.5 \n", "L 74.578482 139.5 \n", "L 74.133511 139.5 \n", "L 73.68853 139.5 \n", "L 73.243555 139.5 \n", "L 72.798579 139.5 \n", "L 72.353603 139.5 \n", "L 71.908628 139.5 \n", "L 71.463652 139.5 \n", "L 71.018676 139.5 \n", "L 70.573701 139.5 \n", "L 70.12872 139.5 \n", "L 69.683744 139.5 \n", "L 69.238769 139.5 \n", "L 68.793793 139.5 \n", "L 68.348817 139.5 \n", "L 67.903842 139.5 \n", "L 67.458866 139.5 \n", "L 67.01389 139.5 \n", "L 66.568909 139.5 \n", "L 66.123934 139.5 \n", "L 65.678958 139.5 \n", "L 65.233982 139.5 \n", "L 64.789007 139.5 \n", "L 64.344031 139.5 \n", "L 63.899055 139.5 \n", "L 63.454085 139.5 \n", "L 63.009104 139.5 \n", "L 62.564128 139.5 \n", "L 62.119153 139.5 \n", "L 61.674177 139.5 \n", "L 61.229202 139.5 \n", "z\n", "\" clip-path=\"url(#pca9a642199)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m3b8323df5c\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3b8323df5c\" x=\"38.980398\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(31.609304 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m3b8323df5c\" x=\"83.478005\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(76.106912 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m3b8323df5c\" x=\"127.975613\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(124.794363 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m3b8323df5c\" x=\"172.47322\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(169.29197 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m3b8323df5c\" x=\"216.970828\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(213.789578 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m06e5e2fa1d\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m06e5e2fa1d\" x=\"30.103125\" y=\"139.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 143.299219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m06e5e2fa1d\" x=\"30.103125\" y=\"114.3\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 118.099219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m06e5e2fa1d\" x=\"30.103125\" y=\"89.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 92.899219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m06e5e2fa1d\" x=\"30.103125\" y=\"63.9\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 67.699219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m06e5e2fa1d\" x=\"30.103125\" y=\"38.7\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.499219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m06e5e2fa1d\" x=\"30.103125\" y=\"13.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 17.299219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 38.980398 137.192229 \n", "L 42.540208 136.34218 \n", "L 45.655043 135.388523 \n", "L 48.769867 134.199073 \n", "L 51.439727 132.960362 \n", "L 54.109586 131.490071 \n", "L 56.77944 129.759604 \n", "L 59.449294 127.740261 \n", "L 62.119153 125.404131 \n", "L 64.789007 122.725132 \n", "L 67.458866 119.680191 \n", "L 70.12872 116.250543 \n", "L 72.798579 112.423075 \n", "L 75.468433 108.191746 \n", "L 78.138292 103.558886 \n", "L 81.253127 97.662962 \n", "L 84.367959 91.274787 \n", "L 87.927767 83.447879 \n", "L 92.377526 73.061154 \n", "L 105.726809 41.371101 \n", "L 108.841643 34.770384 \n", "L 111.511498 29.620763 \n", "L 113.736379 25.76378 \n", "L 115.96126 22.358579 \n", "L 117.741164 19.992165 \n", "L 119.521067 17.967479 \n", "L 121.300972 16.303342 \n", "L 122.6359 15.301401 \n", "L 123.970828 14.516481 \n", "L 125.305756 13.952782 \n", "L 126.640685 13.613351 \n", "L 127.975613 13.5 \n", "L 129.310541 13.613351 \n", "L 130.645469 13.952782 \n", "L 131.980397 14.516481 \n", "L 133.315326 15.301401 \n", "L 134.650254 16.303342 \n", "L 136.430158 17.967479 \n", "L 138.210062 19.992165 \n", "L 139.989966 22.358579 \n", "L 142.214847 25.76378 \n", "L 144.439728 29.620763 \n", "L 147.109584 34.770384 \n", "L 150.224417 41.371101 \n", "L 153.784224 49.493494 \n", "L 159.123938 62.309069 \n", "L 166.688532 80.391076 \n", "L 170.693315 89.366877 \n", "L 174.253123 96.77937 \n", "L 177.367958 102.748394 \n", "L 180.482787 108.191739 \n", "L 183.152647 112.423075 \n", "L 185.822501 116.250536 \n", "L 188.49236 119.680191 \n", "L 191.162214 122.725128 \n", "L 193.832073 125.404131 \n", "L 196.501927 127.740258 \n", "L 199.171786 129.759604 \n", "L 201.84164 131.490071 \n", "L 204.511494 132.96036 \n", "L 207.181353 134.199071 \n", "L 210.296188 135.388525 \n", "L 213.411017 136.34218 \n", "L 216.525852 137.098288 \n", "L 216.525852 137.098288 \n", "\" clip-path=\"url(#pca9a642199)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pca9a642199\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x = torch.arange(-2, 2, 0.01)\n", "f = torch.exp(-x**2)\n", "\n", "d2l.set_figsize()\n", "d2l.plt.plot(x, f, color='black')\n", "d2l.plt.fill_between(x.tolist()[50:250], f.tolist()[50:250])\n", "d2l.plt.show()"]}, {"cell_type": "markdown", "id": "0e5f6d49", "metadata": {"origin_pos": 8}, "source": ["We will denote this area by the integral symbol below:\n", "\n", "$$\n", "\\textrm{Area}(\\mathcal{A}) = \\int_a^b f(x) \\;dx.\n", "$$\n", "\n", "The inner variable is a dummy variable, much like the index of a sum in a $\\sum$, and so this can be equivalently written with any inner value we like:\n", "\n", "$$\n", "\\int_a^b f(x) \\;dx = \\int_a^b f(z) \\;dz.\n", "$$\n", "\n", "There is a traditional way to try and understand how we might try to approximate such integrals: we can imagine taking the region in-between $a$ and $b$ and chopping it into $N$ vertical slices.  If $N$ is large, we can approximate the area of each slice by a rectangle, and then add up the areas to get the total area under the curve.  Let's take a look at an example doing this in code.  We will see how to get the true value in a later section.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "fab0969f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:29.302241Z", "iopub.status.busy": "2023-08-18T19:28:29.301624Z", "iopub.status.idle": "2023-08-18T19:28:29.553483Z", "shell.execute_reply": "2023-08-18T19:28:29.552650Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"232.603125pt\" height=\"173.477344pt\" viewBox=\"0 0 232.**********.477344\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:28:29.503048</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 173.477344 \n", "L 232.**********.477344 \n", "L 232.603125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 38.980398 149.599219 \n", "L 43.419034 149.599219 \n", "L 43.419034 149.599219 \n", "L 38.980398 149.599219 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 43.419034 149.599219 \n", "L 47.857671 149.599219 \n", "L 47.857671 142.686501 \n", "L 43.419034 142.686501 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 47.857671 149.599219 \n", "L 52.296307 149.599219 \n", "L 52.296307 135.876446 \n", "L 47.857671 135.876446 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.296307 149.599219 \n", "L 56.734943 149.599219 \n", "L 56.734943 129.266701 \n", "L 52.296307 129.266701 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 56.734943 149.599219 \n", "L 61.17358 149.599219 \n", "L 61.17358 122.945372 \n", "L 56.734943 122.945372 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 61.17358 149.599219 \n", "L 65.612217 149.599219 \n", "L 65.612217 116.987454 \n", "L 61.17358 116.987454 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 65.612217 149.599219 \n", "L 70.050854 149.599219 \n", "L 70.050854 111.452429 \n", "L 65.612217 111.452429 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 70.050852 149.599219 \n", "L 74.489489 149.599219 \n", "L 74.489489 106.383181 \n", "L 70.050852 106.383181 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 74.489489 149.599219 \n", "L 78.928127 149.599219 \n", "L 78.928127 101.806114 \n", "L 74.489489 101.806114 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 78.928127 149.599219 \n", "L 83.366761 149.599219 \n", "L 83.366761 97.732271 \n", "L 78.928127 97.732271 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 83.366761 149.599219 \n", "L 87.805399 149.599219 \n", "L 87.805399 94.159218 \n", "L 83.366761 94.159218 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 87.805399 149.599219 \n", "L 92.244036 149.599219 \n", "L 92.244036 91.073307 \n", "L 87.805399 91.073307 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 92.244036 149.599219 \n", "L 96.682674 149.599219 \n", "L 96.682674 88.452159 \n", "L 92.244036 88.452159 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 96.682668 149.599219 \n", "L 101.121306 149.599219 \n", "L 101.121306 86.267055 \n", "L 96.682668 86.267055 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 101.121306 149.599219 \n", "L 105.559943 149.599219 \n", "L 105.559943 84.485125 \n", "L 101.121306 84.485125 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 105.559943 149.599219 \n", "L 109.998581 149.599219 \n", "L 109.998581 83.07122 \n", "L 105.559943 83.07122 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 109.998581 149.599219 \n", "L 114.437218 149.599219 \n", "L 114.437218 81.989465 \n", "L 109.998581 81.989465 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 114.437218 149.599219 \n", "L 118.875855 149.599219 \n", "L 118.875855 81.204444 \n", "L 114.437218 81.204444 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 118.875855 149.599219 \n", "L 123.314493 149.599219 \n", "L 123.314493 80.682092 \n", "L 118.875855 80.682092 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 123.314488 149.599219 \n", "L 127.753125 149.599219 \n", "L 127.753125 80.390282 \n", "L 123.314488 80.390282 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 127.753125 149.599219 \n", "L 132.191757 149.599219 \n", "L 132.191757 80.299219 \n", "L 127.753125 80.299219 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 132.191757 149.599219 \n", "L 136.630389 149.599219 \n", "L 136.630389 80.381624 \n", "L 132.191757 80.381624 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 136.6304 149.599219 \n", "L 141.069032 149.599219 \n", "L 141.069032 80.612793 \n", "L 136.6304 80.612793 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 141.069032 149.599219 \n", "L 145.507664 149.599219 \n", "L 145.507664 80.970586 \n", "L 141.069032 80.970586 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 145.507675 149.599219 \n", "L 149.946307 149.599219 \n", "L 149.946307 81.435283 \n", "L 145.507675 81.435283 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 149.946307 149.599219 \n", "L 154.384939 149.599219 \n", "L 154.384939 81.989461 \n", "L 149.946307 81.989461 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 154.38495 149.599219 \n", "L 158.823582 149.599219 \n", "L 158.823582 82.617804 \n", "L 154.38495 82.617804 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 158.823582 149.599219 \n", "L 163.262214 149.599219 \n", "L 163.262214 83.306929 \n", "L 158.823582 83.306929 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 163.262224 149.599219 \n", "L 167.700857 149.599219 \n", "L 167.700857 84.045167 \n", "L 163.262224 84.045167 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 167.700857 149.599219 \n", "L 172.139489 149.599219 \n", "L 172.139489 84.822426 \n", "L 167.700857 84.822426 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 172.139489 149.599219 \n", "L 176.578121 149.599219 \n", "L 176.578121 85.629988 \n", "L 172.139489 85.629988 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 176.578131 149.599219 \n", "L 181.016763 149.599219 \n", "L 181.016763 86.46035 \n", "L 176.578131 86.46035 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 181.016763 149.599219 \n", "L 185.455396 149.599219 \n", "L 185.455396 87.307086 \n", "L 181.016763 87.307086 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 185.455396 149.599219 \n", "L 189.894028 149.599219 \n", "L 189.894028 88.164698 \n", "L 185.455396 88.164698 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 189.894038 149.599219 \n", "L 194.33267 149.599219 \n", "L 194.33267 89.028523 \n", "L 189.894038 89.028523 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 194.33267 149.599219 \n", "L 198.771303 149.599219 \n", "L 198.771303 89.894603 \n", "L 194.33267 89.894603 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 198.771303 149.599219 \n", "L 203.209935 149.599219 \n", "L 203.209935 90.759596 \n", "L 198.771303 90.759596 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 203.209945 149.599219 \n", "L 207.648577 149.599219 \n", "L 207.648577 91.620703 \n", "L 203.209945 91.620703 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 207.648577 149.599219 \n", "L 212.08721 149.599219 \n", "L 212.08721 92.475572 \n", "L 207.648577 92.475572 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 212.08722 149.599219 \n", "L 216.525852 149.599219 \n", "L 216.525852 93.32228 \n", "L 212.08722 93.32228 \n", "z\n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"meaaad7e2c8\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#meaaad7e2c8\" x=\"38.980398\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(31.028835 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#meaaad7e2c8\" x=\"83.366761\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(75.415199 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#meaaad7e2c8\" x=\"127.753125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(119.801563 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#meaaad7e2c8\" x=\"172.139489\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(164.187926 164.197656) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#meaaad7e2c8\" x=\"216.525852\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(208.57429 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m9ef49a9dbc\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9ef49a9dbc\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 153.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m9ef49a9dbc\" x=\"30.103125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 125.678438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m9ef49a9dbc\" x=\"30.103125\" y=\"94.159219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 97.958438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m9ef49a9dbc\" x=\"30.103125\" y=\"66.439219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 70.238438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m9ef49a9dbc\" x=\"30.103125\" y=\"38.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.518438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m9ef49a9dbc\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 38.980398 149.599219 \n", "L 43.419034 142.686501 \n", "L 47.857671 135.876446 \n", "L 52.296307 129.266701 \n", "L 56.734943 122.945372 \n", "L 61.17358 116.987454 \n", "L 65.612217 111.452429 \n", "L 70.050852 106.383181 \n", "L 74.489489 101.806114 \n", "L 78.928127 97.732271 \n", "L 83.366761 94.159218 \n", "L 87.805399 91.073307 \n", "L 92.244036 88.452159 \n", "L 96.682668 86.267055 \n", "L 101.121306 84.485125 \n", "L 105.559943 83.07122 \n", "L 109.998581 81.989465 \n", "L 114.437218 81.204444 \n", "L 118.875855 80.682092 \n", "L 123.314488 80.390282 \n", "L 127.753125 80.299219 \n", "L 132.191757 80.381624 \n", "L 136.6304 80.612793 \n", "L 141.069032 80.970586 \n", "L 145.507675 81.435283 \n", "L 149.946307 81.989461 \n", "L 154.38495 82.617804 \n", "L 158.823582 83.306929 \n", "L 163.262224 84.045167 \n", "L 167.700857 84.822426 \n", "L 172.139489 85.629988 \n", "L 176.578131 86.46035 \n", "L 181.016763 87.307086 \n", "L 185.455396 88.164698 \n", "L 189.894038 89.028523 \n", "L 194.33267 89.894603 \n", "L 198.771303 90.759596 \n", "L 203.209945 91.620703 \n", "L 207.648577 92.475572 \n", "L 212.08722 93.32228 \n", "\" clip-path=\"url(#p6a4da5a8a7)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_45\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_46\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p6a4da5a8a7\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'approximation: 0.7944855690002441, truth: tensor([0.8047])'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["epsilon = 0.05\n", "a = 0\n", "b = 2\n", "\n", "x = torch.arange(a, b, epsilon)\n", "f = x / (1 + x**2)\n", "\n", "approx = torch.sum(epsilon*f)\n", "true = torch.log(torch.tensor([5.])) / 2\n", "\n", "d2l.set_figsize()\n", "d2l.plt.bar(x, f, width=epsilon, align='edge')\n", "d2l.plt.plot(x, f, color='black')\n", "d2l.plt.ylim([0, 1])\n", "d2l.plt.show()\n", "\n", "f'approximation: {approx}, truth: {true}'"]}, {"cell_type": "markdown", "id": "7b8d2140", "metadata": {"origin_pos": 12}, "source": ["The issue is that while it can be done numerically, we can do this approach analytically for only the simplest functions like\n", "\n", "$$\n", "\\int_a^b x \\;dx.\n", "$$\n", "\n", "Anything somewhat more complex like our example from the code above\n", "\n", "$$\n", "\\int_a^b \\frac{x}{1+x^{2}} \\;dx.\n", "$$\n", "\n", "is beyond what we can solve with such a direct method.\n", "\n", "We will instead take a different approach.  We will work intuitively with the notion of the area, and learn the main computational tool used to find integrals: the *fundamental theorem of calculus*.   This will be the basis for our study of integration.\n", "\n", "## The Fundamental Theorem of Calculus\n", "\n", "To dive deeper into the theory of integration, let's introduce a function\n", "\n", "$$\n", "F(x) = \\int_0^x f(y) dy.\n", "$$\n", "\n", "This function measures the area between $0$ and $x$ depending on how we change $x$.  Notice that this is everything we need since\n", "\n", "$$\n", "\\int_a^b f(x) \\;dx = F(b) - F(a).\n", "$$\n", "\n", "This is a mathematical encoding of the fact that we can measure the area out to the far end-point and then subtract off the area to the near end point as indicated in :numref:`fig_area-subtract`.\n", "\n", "![Visualizing why we may reduce the problem of computing the area under a curve between two points to computing the area to the left of a point.](../img/sub-area.svg)\n", ":label:`fig_area-subtract`\n", "\n", "Thus, we can figure out what the integral over any interval is by figuring out what $F(x)$ is.\n", "\n", "To do so, let's consider an experiment.  As we often do in calculus, let's imagine what happens when we shift the value by a tiny bit.  From the comment above, we know that\n", "\n", "$$\n", "F(x+\\epsilon) - F(x) = \\int_x^{x+\\epsilon} f(y) \\; dy.\n", "$$\n", "\n", "This tells us that the function changes by the area under a tiny sliver of a function.\n", "\n", "This is the point at which we make an approximation.  If we look at a tiny sliver of area like this, it looks like this area is close to the rectangular area with height the value of $f(x)$ and the base width $\\epsilon$.  Indeed, one can show that as $\\epsilon \\rightarrow 0$ this approximation becomes better and better.  Thus we can conclude:\n", "\n", "$$\n", "F(x+\\epsilon) - F(x) \\approx \\epsilon f(x).\n", "$$\n", "\n", "However, we can now notice: this is exactly the pattern we expect if we were computing the derivative of $F$!  Thus we see the following rather surprising fact:\n", "\n", "$$\n", "\\frac{dF}{dx}(x) = f(x).\n", "$$\n", "\n", "This is the *fundamental theorem of calculus*.  We may write it in expanded form as\n", "$$\\frac{d}{dx}\\int_0^x  f(y) \\; dy = f(x).$$\n", ":eqlabel:`eq_ftc`\n", "\n", "It takes the concept of finding areas (*a priori* rather hard), and reduces it to a statement derivatives (something much more completely understood).  One last comment that we must make is that this does not tell us exactly what $F(x)$ is.  Indeed $F(x) + C$ for any $C$ has the same derivative.  This is a fact-of-life in the theory of integration.  Thankfully, notice that when working with definite integrals, the constants drop out, and thus are irrelevant to the outcome.\n", "\n", "$$\n", "\\int_a^b f(x) \\; dx = (F(b) + C) - (F(a) + C) = F(b) - F(a).\n", "$$\n", "\n", "This may seem like abstract non-sense, but let's take a moment to appreciate that it has given us a whole new perspective on computing integrals.  Our goal is no-longer to do some sort of chop-and-sum process to try and recover the area, rather we need only find a function whose derivative is the function we have!  This is incredible since we can now list many rather difficult integrals by just reversing the table from :numref:`sec_derivative_table`.  For instance, we know that the derivative of $x^{n}$ is $nx^{n-1}$.  Thus, we can say using the fundamental theorem :eqref:`eq_ftc` that\n", "\n", "$$\n", "\\int_0^{x} ny^{n-1} \\; dy = x^n - 0^n = x^n.\n", "$$\n", "\n", "Similarly, we know that the derivative of $e^{x}$ is itself, so that means\n", "\n", "$$\n", "\\int_0^{x} e^{x} \\; dx = e^{x} - e^{0} = e^x - 1.\n", "$$\n", "\n", "In this way, we can develop the entire theory of integration leveraging ideas from differential calculus freely.  Every integration rule derives from this one fact.\n", "\n", "## Change of Variables\n", ":label:`subsec_integral_example`\n", "\n", "Just as with differentiation, there are a number of rules which make the computation of integrals more tractable.  In fact, every rule of differential calculus (like the product rule, sum rule, and chain rule) has a corresponding rule for integral calculus (integration by parts, linearity of integration, and the change of variables formula respectively).  In this section, we will dive into what is arguably the most important from the list: the change of variables formula.\n", "\n", "First, suppose that we have a function which is itself an integral:\n", "\n", "$$\n", "F(x) = \\int_0^x f(y) \\; dy.\n", "$$\n", "\n", "Let's suppose that we want to know how this function looks when we compose it with another to obtain $F(u(x))$.  By the chain rule, we know\n", "\n", "$$\n", "\\frac{d}{dx}F(u(x)) = \\frac{dF}{du}(u(x))\\cdot \\frac{du}{dx}.\n", "$$\n", "\n", "We can turn this into a statement about integration by using the fundamental theorem :eqref:`eq_ftc` as above.  This gives\n", "\n", "$$\n", "F(u(x)) - F(u(0)) = \\int_0^x \\frac{dF}{du}(u(y))\\cdot \\frac{du}{dy} \\;dy.\n", "$$\n", "\n", "Recalling that $F$ is itself an integral gives that the left hand side may be rewritten to be\n", "\n", "$$\n", "\\int_{u(0)}^{u(x)} f(y) \\; dy = \\int_0^x \\frac{dF}{du}(u(y))\\cdot \\frac{du}{dy} \\;dy.\n", "$$\n", "\n", "Similarly, recalling that $F$ is an integral allows us to recognize that $\\frac{dF}{dx} = f$ using the fundamental theorem :eqref:`eq_ftc`, and thus we may conclude\n", "\n", "$$\\int_{u(0)}^{u(x)} f(y) \\; dy = \\int_0^x f(u(y))\\cdot \\frac{du}{dy} \\;dy.$$\n", ":eqlabel:`eq_change_var`\n", "\n", "This is the *change of variables* formula.\n", "\n", "For a more intuitive derivation, consider what happens when we take an integral of $f(u(x))$ between $x$ and $x+\\epsilon$. For a small $\\epsilon$, this integral is approximately $\\epsilon f(u(x))$, the area of the associated rectangle.  Now, let's compare this with the integral of $f(y)$ from $u(x)$ to $u(x+\\epsilon)$.  We know that $u(x+\\epsilon) \\approx u(x) + \\epsilon \\frac{du}{dx}(x)$, so the area of this rectangle is approximately $\\epsilon \\frac{du}{dx}(x)f(u(x))$.  Thus, to make the area of these two rectangles to agree, we need to multiply the first one by $\\frac{du}{dx}(x)$ as is illustrated in :numref:`fig_rect-transform`.\n", "\n", "![Visualizing the transformation of a single thin rectangle under the change of variables.](../img/rect-trans.svg)\n", ":label:`fig_rect-transform`\n", "\n", "This tells us that\n", "\n", "$$\n", "\\int_x^{x+\\epsilon} f(u(y))\\frac{du}{dy}(y)\\;dy = \\int_{u(x)}^{u(x+\\epsilon)} f(y) \\; dy.\n", "$$\n", "\n", "This is the change of variables formula expressed for a single small rectangle.\n", "\n", "If $u(x)$ and $f(x)$ are properly chosen, this can allow for the computation of incredibly complex integrals.  For instance, if we even chose $f(y) = 1$ and $u(x) = e^{-x^{2}}$ (which means $\\frac{du}{dx}(x) = -2xe^{-x^{2}}$), this can show for instance that\n", "\n", "$$\n", "e^{-1} - 1 = \\int_{e^{-0}}^{e^{-1}} 1 \\; dy = -2\\int_0^{1} ye^{-y^2}\\;dy,\n", "$$\n", "\n", "and thus by rearranging that\n", "\n", "$$\n", "\\int_0^{1} ye^{-y^2}\\; dy = \\frac{1-e^{-1}}{2}.\n", "$$\n", "\n", "## A Comment on Sign Conventions\n", "\n", "Keen-eyed readers will observe something strange about the computations above.  Namely, computations like\n", "\n", "$$\n", "\\int_{e^{-0}}^{e^{-1}} 1 \\; dy = e^{-1} -1 < 0,\n", "$$\n", "\n", "can produce negative numbers.  When thinking about areas, it can be strange to see a negative value, and so it is worth digging into what the convention is.\n", "\n", "Mathematicians take the notion of signed areas.  This manifests itself in two ways.  First, if we consider a function $f(x)$ which is sometimes less than zero, then the area will also be negative.  So for instance\n", "\n", "$$\n", "\\int_0^{1} (-1)\\;dx = -1.\n", "$$\n", "\n", "Similarly, integrals which progress from right to left, rather than left to right are also taken to be negative areas\n", "\n", "$$\n", "\\int_0^{-1} 1\\; dx = -1.\n", "$$\n", "\n", "The standard area (from left to right of a positive function) is always positive.  Anything obtained by flipping it (say flipping over the $x$-axis to get the integral of a negative number, or flipping over the $y$-axis to get an integral in the wrong order) will produce a negative area.  And indeed, flipping twice will give a pair of negative signs that cancel out to have positive area\n", "\n", "$$\n", "\\int_0^{-1} (-1)\\;dx =  1.\n", "$$\n", "\n", "If this discussion sounds familiar, it is!  In :numref:`sec_geometry-linear-algebraic-ops` we discussed how the determinant represented the signed area in much the same way.\n", "\n", "## Multiple Integrals\n", "In some cases, we will need to work in higher dimensions.  For instance, suppose that we have a function of two variables, like $f(x, y)$ and we want to know the volume under $f$ when $x$ ranges over $[a, b]$ and $y$ ranges over $[c, d]$.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "b774c013", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:29.557192Z", "iopub.status.busy": "2023-08-18T19:28:29.556602Z", "iopub.status.idle": "2023-08-18T19:28:29.844282Z", "shell.execute_reply": "2023-08-18T19:28:29.843336Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"163.45257pt\" height=\"156.400786pt\" viewBox=\"0 0 163.45257 156.400786\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:28:29.786346</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 156.400786 \n", "L 163.45257 156.400786 \n", "L 163.45257 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"patch_2\">\n", "   <path d=\"M 7.2 145.8 \n", "L 145.8 145.8 \n", "L 145.8 7.2 \n", "L 7.2 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"pane3d_1\">\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 27.**********.476933 \n", "L 65.746483 73.471623 \n", "L 65.294849 27.015534 \n", "L 25.804101 56.674423 \n", "\" style=\"fill: #f2f2f2; opacity: 0.5; stroke: #f2f2f2; stroke-linejoin: miter\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"pane3d_2\">\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 65.746483 73.471623 \n", "L 127.452778 91.388092 \n", "L 129.283933 43.59906 \n", "L 65.294849 27.015534 \n", "\" style=\"fill: #e6e6e6; opacity: 0.5; stroke: #e6e6e6; stroke-linejoin: miter\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"pane3d_3\">\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 27.**********.476933 \n", "L 92.543212 126.21441 \n", "L 127.452778 91.388092 \n", "L 65.746483 73.471623 \n", "\" style=\"fill: #ececec; opacity: 0.5; stroke: #ececec; stroke-linejoin: miter\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axis3d_1\">\n", "   <g id=\"line2d_1\">\n", "    <path d=\"M 27.**********.476933 \n", "L 92.543212 126.21441 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_1\">\n", "    <!-- x -->\n", "    <g transform=\"translate(42.940071 147.693651) scale(0.1 -0.1)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-78\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"Line3DCollection_1\">\n", "    <path d=\"M 28.995517 105.874257 \n", "L 66.932342 73.815938 \n", "L 66.522787 27.333769 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "    <path d=\"M 44.059364 110.695848 \n", "L 81.311741 77.991008 \n", "L 81.417999 31.194039 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "    <path d=\"M 59.439711 115.618742 \n", "L 95.971967 82.247615 \n", "L 96.614736 35.132452 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "    <path d=\"M 75.146636 120.646167 \n", "L 110.921328 86.588173 \n", "L 112.122247 39.151407 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "    <path d=\"M 91.190654 125.781488 \n", "L 126.168463 91.01519 \n", "L 127.950165 43.253399 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "   </g>\n", "   <g id=\"xtick_1\">\n", "    <g id=\"line2d_2\">\n", "     <path d=\"M 29.321354 105.598911 \n", "L 28.34268 106.425934 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_2\">\n", "     <!-- −2 -->\n", "     <g transform=\"translate(15.3872 126.526523) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-2212\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"xtick_2\">\n", "    <g id=\"line2d_3\">\n", "     <path d=\"M 44.379551 110.414748 \n", "L 43.417837 111.259061 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- −1 -->\n", "     <g transform=\"translate(30.476381 131.513626) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-2212\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"xtick_3\">\n", "    <g id=\"line2d_4\">\n", "     <path d=\"M 59.753936 115.331707 \n", "L 58.810116 116.193859 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- 0 -->\n", "     <g transform=\"translate(50.074362 136.606146) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-30\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"xtick_4\">\n", "    <g id=\"line2d_5\">\n", "     <path d=\"M 75.454573 120.353007 \n", "L 74.529628 121.233568 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- 1 -->\n", "     <g transform=\"translate(65.811678 141.807462) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"xtick_5\">\n", "    <g id=\"line2d_6\">\n", "     <path d=\"M 91.491959 125.482004 \n", "L 90.58692 126.38157 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- 2 -->\n", "     <g transform=\"translate(81.888835 147.121099) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-32\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axis3d_2\">\n", "   <g id=\"line2d_7\">\n", "    <path d=\"M 127.452778 91.388092 \n", "L 92.543212 126.21441 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- y -->\n", "    <g transform=\"translate(130.155124 134.245064) scale(0.1 -0.1)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-79\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"Line3DCollection_2\">\n", "    <path d=\"M 26.653462 56.036525 \n", "L 28.569187 104.790355 \n", "L 93.294356 125.465057 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "    <path d=\"M 36.645621 48.532074 \n", "L 38.16423 96.707342 \n", "L 102.130027 116.650456 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "    <path d=\"M 46.281942 41.294871 \n", "L 47.429799 88.901883 \n", "L 110.649228 108.151571 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "    <path d=\"M 55.581099 34.310889 \n", "L 56.382576 81.359924 \n", "L 118.868662 99.951737 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "    <path d=\"M 64.560484 27.567067 \n", "L 65.038138 74.068343 \n", "L 126.803878 92.035445 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "   </g>\n", "   <g id=\"xtick_6\">\n", "    <g id=\"line2d_8\">\n", "     <path d=\"M 92.753612 125.292331 \n", "L 94.377006 125.81088 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- −2 -->\n", "     <g transform=\"translate(96.613394 144.200802) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-2212\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"xtick_7\">\n", "    <g id=\"line2d_9\">\n", "     <path d=\"M 101.59604 116.483971 \n", "L 103.199126 116.983777 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- −1 -->\n", "     <g transform=\"translate(105.262226 135.174659) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-2212\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"xtick_8\">\n", "    <g id=\"line2d_10\">\n", "     <path d=\"M 110.121866 107.990994 \n", "L 111.705044 108.473056 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- 0 -->\n", "     <g transform=\"translate(117.790838 126.472106) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-30\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"xtick_9\">\n", "    <g id=\"line2d_11\">\n", "     <path d=\"M 118.347793 99.79676 \n", "L 119.91146 100.262007 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- 1 -->\n", "     <g transform=\"translate(125.835923 118.076048) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"xtick_10\">\n", "    <g id=\"line2d_12\">\n", "     <path d=\"M 126.289371 91.885779 \n", "L 127.833918 92.335074 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- 2 -->\n", "     <g transform=\"translate(133.602571 109.970574) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-32\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axis3d_3\">\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 127.452778 91.388092 \n", "L 129.283933 43.59906 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"Line3DCollection_3\">\n", "    <path d=\"M 127.488109 90.466025 \n", "L 65.737756 72.573967 \n", "L 27.716594 104.536392 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "    <path d=\"M 127.916087 79.296776 \n", "L 65.632086 61.704502 \n", "L 27.261211 93.139986 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "    <path d=\"M 128.351588 67.931162 \n", "L 65.524633 50.651699 \n", "L 26.797565 81.536821 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "    <path d=\"M 128.794814 56.363961 \n", "L 65.415353 39.410878 \n", "L 26.325432 69.721221 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "    <path d=\"M 129.245972 44.589762 \n", "L 65.304198 27.977202 \n", "L 25.844574 57.687295 \n", "\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8\"/>\n", "   </g>\n", "   <g id=\"xtick_11\">\n", "    <g id=\"line2d_14\">\n", "     <path d=\"M 126.973746 90.31699 \n", "L 128.517863 90.764395 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- 0.00 -->\n", "     <g transform=\"translate(131.691705 95.901632) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-30\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"xtick_12\">\n", "    <g id=\"line2d_15\">\n", "     <path d=\"M 127.397099 79.150186 \n", "L 128.955107 79.590249 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- 0.25 -->\n", "     <g transform=\"translate(132.250588 84.767971) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-30\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"xtick_13\">\n", "    <g id=\"line2d_16\">\n", "     <path d=\"M 127.827893 67.787129 \n", "L 129.400043 68.219521 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- 0.50 -->\n", "     <g transform=\"translate(132.819248 73.439533) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-30\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"xtick_14\">\n", "    <g id=\"line2d_17\">\n", "     <path d=\"M 128.266325 56.222598 \n", "L 129.852876 56.646977 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- 0.75 -->\n", "     <g transform=\"translate(133.397944 61.911161) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-30\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"xtick_15\">\n", "    <g id=\"line2d_18\">\n", "     <path d=\"M 128.7126 44.451188 \n", "L 130.313818 44.867196 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- 1.00 -->\n", "     <g transform=\"translate(133.986945 50.177516) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"Line3DCollection_4\">\n", "    <path d=\"M 29.77277 104.231186 \n", "L 30.16279 103.899621 \n", "L 30.552244 103.568145 \n", "L 30.941129 103.236718 \n", "L 31.329447 102.905292 \n", "L 31.717197 102.573817 \n", "L 32.104379 102.242244 \n", "L 32.490992 101.91052 \n", "L 32.877037 101.578589 \n", "L 33.262515 101.246396 \n", "L 33.647422 100.913886 \n", "L 34.031761 100.581004 \n", "L 34.415533 100.247693 \n", "L 34.798735 99.913904 \n", "L 35.181369 99.579585 \n", "L 35.563436 99.244691 \n", "L 35.944936 98.909182 \n", "L 36.325869 98.573024 \n", "L 36.70624 98.236188 \n", "L 37.086046 97.898658 \n", "L 37.465292 97.560423 \n", "L 37.843979 97.221486 \n", "L 38.22211 96.881862 \n", "L 38.599687 96.541576 \n", "L 38.976716 96.200668 \n", "L 39.353197 95.859195 \n", "L 39.729136 95.517226 \n", "L 40.104539 95.174844 \n", "L 40.479409 94.832152 \n", "L 40.853751 94.489264 \n", "L 41.227572 94.146311 \n", "L 41.600875 93.803437 \n", "L 41.973669 93.460799 \n", "L 42.345958 93.118566 \n", "L 42.717749 92.776921 \n", "L 43.089048 92.436048 \n", "L 43.45986 92.096144 \n", "L 43.830192 91.757408 \n", "L 44.20005 91.42004 \n", "L 44.569439 91.084242 \n", "L 44.938365 90.75021 \n", "L 45.306831 90.418136 \n", "L 45.674843 90.088201 \n", "L 46.042404 89.760577 \n", "L 46.409518 89.435421 \n", "L 46.776187 89.112874 \n", "L 47.142413 88.793057 \n", "L 47.508199 88.476074 \n", "L 47.873544 88.162005 \n", "L 48.238449 87.850905 \n", "L 48.602915 87.542808 \n", "L 48.966938 87.237723 \n", "L 49.330519 86.935633 \n", "L 49.693654 86.636495 \n", "L 50.056342 86.340245 \n", "L 50.418579 86.046795 \n", "L 50.780363 85.756034 \n", "L 51.141688 85.467834 \n", "L 51.502553 85.182046 \n", "L 51.862952 84.898509 \n", "L 52.222881 84.617044 \n", "L 52.582337 84.337468 \n", "L 52.941315 84.059582 \n", "L 53.299812 83.783189 \n", "L 53.657823 83.508085 \n", "L 54.015345 83.234069 \n", "L 54.372375 82.960939 \n", "L 54.72891 82.688502 \n", "L 55.084948 82.416568 \n", "L 55.440486 82.14496 \n", "L 55.795522 81.873511 \n", "L 56.150056 81.602063 \n", "L 56.504084 81.330475 \n", "L 56.857609 81.058618 \n", "L 57.210628 80.78638 \n", "L 57.563142 80.513664 \n", "L 57.915152 80.240387 \n", "L 58.266659 79.966479 \n", "L 58.617662 79.691892 \n", "L 58.968164 79.416585 \n", "L 59.318167 79.140533 \n", "L 59.667671 78.863726 \n", "L 60.016678 78.586162 \n", "L 60.365193 78.30785 \n", "L 60.713215 78.028811 \n", "L 61.060747 77.749072 \n", "L 61.407793 77.468665 \n", "L 61.754353 77.187635 \n", "L 62.100431 76.906023 \n", "L 62.446029 76.623879 \n", "L 62.791148 76.341255 \n", "L 63.135791 76.058204 \n", "L 63.479962 75.774778 \n", "L 63.823661 75.491034 \n", "L 64.16689 75.207023 \n", "L 64.509653 74.922798 \n", "L 64.851951 74.638409 \n", "L 65.193785 74.353907 \n", "L 65.535157 74.069336 \n", "L 65.87607 73.78474 \n", "L 66.216524 73.500162 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 31.563363 104.793092 \n", "L 31.952538 104.459174 \n", "L 32.341141 104.125122 \n", "L 32.729169 103.790871 \n", "L 33.116621 103.456347 \n", "L 33.503498 103.121471 \n", "L 33.889797 102.786164 \n", "L 34.275519 102.450342 \n", "L 34.660661 102.113918 \n", "L 35.045226 101.776804 \n", "L 35.429209 101.438913 \n", "L 35.812611 101.100157 \n", "L 36.195433 100.760447 \n", "L 36.577673 100.419704 \n", "L 36.95933 100.077847 \n", "L 37.340408 99.734803 \n", "L 37.720904 99.390511 \n", "L 38.10082 99.044915 \n", "L 38.480159 98.697972 \n", "L 38.85892 98.349653 \n", "L 39.237108 97.999946 \n", "L 39.614725 97.648853 \n", "L 39.991774 97.296397 \n", "L 40.368259 96.942622 \n", "L 40.744186 96.587591 \n", "L 41.119558 96.231394 \n", "L 41.494382 95.874141 \n", "L 41.868665 95.515969 \n", "L 42.242413 95.157037 \n", "L 42.615632 94.797532 \n", "L 42.988332 94.43766 \n", "L 43.36052 94.077653 \n", "L 43.732206 93.717762 \n", "L 44.103396 93.358258 \n", "L 44.474099 92.999429 \n", "L 44.844326 92.641575 \n", "L 45.214083 92.285007 \n", "L 45.583381 91.930044 \n", "L 45.952226 91.577006 \n", "L 46.320626 91.226214 \n", "L 46.688589 90.877985 \n", "L 47.056122 90.532621 \n", "L 47.423229 90.190416 \n", "L 47.789918 89.851643 \n", "L 48.15619 89.516553 \n", "L 48.522051 89.185371 \n", "L 48.887502 88.858294 \n", "L 49.252546 88.535485 \n", "L 49.617182 88.217072 \n", "L 49.981411 87.903148 \n", "L 50.345232 87.593763 \n", "L 50.70864 87.288933 \n", "L 51.071634 86.988631 \n", "L 51.434211 86.692791 \n", "L 51.796364 86.40131 \n", "L 52.15809 86.114047 \n", "L 52.519382 85.830828 \n", "L 52.880234 85.551446 \n", "L 53.240641 85.275666 \n", "L 53.600595 85.00323 \n", "L 53.960089 84.733855 \n", "L 54.319117 84.467245 \n", "L 54.677672 84.203089 \n", "L 55.035748 83.941068 \n", "L 55.393339 83.680859 \n", "L 55.750438 83.422141 \n", "L 56.107041 83.164595 \n", "L 56.463143 82.907909 \n", "L 56.818739 82.651786 \n", "L 57.173826 82.395939 \n", "L 57.5284 82.140104 \n", "L 57.882459 81.88403 \n", "L 58.236001 81.627493 \n", "L 58.589025 81.370288 \n", "L 58.94153 81.112236 \n", "L 59.293515 80.853183 \n", "L 59.644982 80.592996 \n", "L 59.995931 80.331568 \n", "L 60.346362 80.068819 \n", "L 60.696277 79.804685 \n", "L 61.04568 79.539129 \n", "L 61.394571 79.272133 \n", "L 61.742953 79.003694 \n", "L 62.090831 78.73383 \n", "L 62.438204 78.462573 \n", "L 62.785077 78.189966 \n", "L 63.131454 77.916064 \n", "L 63.477336 77.640933 \n", "L 63.822727 77.364645 \n", "L 64.167631 77.087277 \n", "L 64.51205 76.808914 \n", "L 64.855986 76.529639 \n", "L 65.199445 76.249537 \n", "L 65.542427 75.968698 \n", "L 65.884935 75.687206 \n", "L 66.226972 75.405145 \n", "L 66.568541 75.122595 \n", "L 66.909643 74.839636 \n", "L 67.250282 74.556341 \n", "L 67.590459 74.27278 \n", "L 67.930175 73.989021 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 33.35826 105.352154 \n", "L 33.746566 105.01515 \n", "L 34.13429 104.677686 \n", "L 34.521429 104.339656 \n", "L 34.907982 104.000947 \n", "L 35.293949 103.66144 \n", "L 35.679325 103.32101 \n", "L 36.06411 102.979529 \n", "L 36.448302 102.636863 \n", "L 36.8319 102.292876 \n", "L 37.214901 101.947433 \n", "L 37.597304 101.600397 \n", "L 37.979109 101.251634 \n", "L 38.360313 100.901018 \n", "L 38.740917 100.548428 \n", "L 39.120922 100.19375 \n", "L 39.500325 99.836888 \n", "L 39.87913 99.477757 \n", "L 40.257338 99.11629 \n", "L 40.634951 98.752445 \n", "L 41.011972 98.3862 \n", "L 41.388405 98.017561 \n", "L 41.764255 97.646563 \n", "L 42.139526 97.273274 \n", "L 42.514227 96.897794 \n", "L 42.888363 96.520259 \n", "L 43.261943 96.140844 \n", "L 43.634976 95.759759 \n", "L 44.007471 95.377252 \n", "L 44.379439 94.993611 \n", "L 44.750891 94.609158 \n", "L 45.121837 94.224251 \n", "L 45.492291 93.839282 \n", "L 45.862263 93.454669 \n", "L 46.231767 93.070859 \n", "L 46.600814 92.688319 \n", "L 46.969417 92.307531 \n", "L 47.337588 91.92899 \n", "L 47.705338 91.553195 \n", "L 48.072677 91.180642 \n", "L 48.439615 90.811821 \n", "L 48.806162 90.447207 \n", "L 49.172326 90.087253 \n", "L 49.538113 89.732382 \n", "L 49.903529 89.382986 \n", "L 50.268578 89.039415 \n", "L 50.633263 88.701975 \n", "L 50.997586 88.370918 \n", "L 51.361547 88.046445 \n", "L 51.725144 87.728699 \n", "L 52.088376 87.41776 \n", "L 52.451236 87.113653 \n", "L 52.813721 86.816335 \n", "L 53.175824 86.525705 \n", "L 53.537537 86.241604 \n", "L 53.898853 85.963813 \n", "L 54.259762 85.692062 \n", "L 54.620255 85.426031 \n", "L 54.980321 85.165357 \n", "L 55.339951 84.909636 \n", "L 55.699133 84.658434 \n", "L 56.057858 84.411289 \n", "L 56.416116 84.167721 \n", "L 56.773896 83.927234 \n", "L 57.131189 83.689329 \n", "L 57.487987 83.453508 \n", "L 57.844281 83.219274 \n", "L 58.200063 82.986149 \n", "L 58.555328 82.753669 \n", "L 58.91007 82.521393 \n", "L 59.264283 82.288908 \n", "L 59.617963 82.05583 \n", "L 59.971108 81.821809 \n", "L 60.323716 81.58653 \n", "L 60.675785 81.349715 \n", "L 61.027314 81.111125 \n", "L 61.378304 80.870557 \n", "L 61.728756 80.627846 \n", "L 62.078671 80.382866 \n", "L 62.42805 80.135522 \n", "L 62.776899 79.885755 \n", "L 63.125217 79.633536 \n", "L 63.473009 79.378867 \n", "L 63.820281 79.121771 \n", "L 64.167032 78.862299 \n", "L 64.51327 78.600519 \n", "L 64.858998 78.336519 \n", "L 65.204219 78.070399 \n", "L 65.548939 77.802271 \n", "L 65.893161 77.532258 \n", "L 66.236888 77.260487 \n", "L 66.580125 76.98709 \n", "L 66.922877 76.712202 \n", "L 67.265145 76.435958 \n", "L 67.606935 76.15849 \n", "L 67.948248 75.87993 \n", "L 68.289089 75.600401 \n", "L 68.62946 75.320028 \n", "L 68.969363 75.038926 \n", "L 69.308803 74.757201 \n", "L 69.647779 74.474959 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 35.157449 105.906999 \n", "L 35.544855 105.565943 \n", "L 35.931668 105.223958 \n", "L 36.317882 104.880885 \n", "L 36.703496 104.536556 \n", "L 37.088509 104.190789 \n", "L 37.472913 103.8434 \n", "L 37.856708 103.494194 \n", "L 38.239891 103.142971 \n", "L 38.62246 102.789526 \n", "L 39.00441 102.433657 \n", "L 39.385739 102.075158 \n", "L 39.766447 101.713828 \n", "L 40.146529 101.349477 \n", "L 40.525986 100.981922 \n", "L 40.904819 100.610995 \n", "L 41.283025 100.236548 \n", "L 41.660607 99.858455 \n", "L 42.037568 99.476616 \n", "L 42.413908 99.090966 \n", "L 42.789633 98.701474 \n", "L 43.164749 98.308146 \n", "L 43.53926 97.911039 \n", "L 43.913176 97.510251 \n", "L 44.286505 97.105935 \n", "L 44.659256 96.698297 \n", "L 45.031441 96.287597 \n", "L 45.403072 95.874152 \n", "L 45.774164 95.458338 \n", "L 46.144729 95.040588 \n", "L 46.514784 94.621386 \n", "L 46.884343 94.201273 \n", "L 47.253425 93.780837 \n", "L 47.622046 93.36071 \n", "L 47.990221 92.941567 \n", "L 48.35797 92.524109 \n", "L 48.725308 92.109066 \n", "L 49.092251 91.697183 \n", "L 49.458816 91.289212 \n", "L 49.825016 90.885904 \n", "L 50.190865 90.487998 \n", "L 50.556375 90.096208 \n", "L 50.921557 89.71122 \n", "L 51.286421 89.333674 \n", "L 51.650972 88.96416 \n", "L 52.015216 88.603208 \n", "L 52.379156 88.251277 \n", "L 52.742795 87.908753 \n", "L 53.10613 87.575937 \n", "L 53.469159 87.253044 \n", "L 53.831878 86.940199 \n", "L 54.194279 86.637437 \n", "L 54.556354 86.344694 \n", "L 54.918094 86.061819 \n", "L 55.279486 85.788571 \n", "L 55.640519 85.524622 \n", "L 56.001179 85.269563 \n", "L 56.361452 85.022913 \n", "L 56.721322 84.784122 \n", "L 57.080775 84.552583 \n", "L 57.439795 84.327641 \n", "L 57.798366 84.108599 \n", "L 58.156475 83.894733 \n", "L 58.514107 83.685298 \n", "L 58.871249 83.479541 \n", "L 59.227886 83.276708 \n", "L 59.584008 83.076055 \n", "L 59.939604 82.87686 \n", "L 60.294665 82.678424 \n", "L 60.649182 82.480084 \n", "L 61.003148 82.281218 \n", "L 61.356558 82.081247 \n", "L 61.709407 81.879647 \n", "L 62.061692 81.675943 \n", "L 62.413412 81.469717 \n", "L 62.764564 81.260611 \n", "L 63.11515 81.048319 \n", "L 63.465171 80.832594 \n", "L 63.814628 80.613246 \n", "L 64.163525 80.390135 \n", "L 64.511866 80.163172 \n", "L 64.859653 79.932315 \n", "L 65.206892 79.697565 \n", "L 65.553589 79.458961 \n", "L 65.899747 79.216579 \n", "L 66.245372 78.970523 \n", "L 66.59047 78.720924 \n", "L 66.935047 78.467935 \n", "L 67.279106 78.211726 \n", "L 67.622657 77.952479 \n", "L 67.965701 77.69039 \n", "L 68.308244 77.425658 \n", "L 68.650293 77.158483 \n", "L 68.991851 76.889071 \n", "L 69.332922 76.617623 \n", "L 69.673511 76.344334 \n", "L 70.013623 76.069394 \n", "L 70.35326 75.792988 \n", "L 70.692426 75.51529 \n", "L 71.031124 75.236463 \n", "L 71.369358 74.956664 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 36.960921 106.45613 \n", "L 37.347394 106.109798 \n", "L 37.733258 105.76189 \n", "L 38.118507 105.412175 \n", "L 38.503137 105.060405 \n", "L 38.887145 104.706318 \n", "L 39.270523 104.349643 \n", "L 39.653269 103.990095 \n", "L 40.035377 103.627382 \n", "L 40.416845 103.261207 \n", "L 40.797665 102.891271 \n", "L 41.177837 102.517275 \n", "L 41.557356 102.138926 \n", "L 41.93622 101.755944 \n", "L 42.314426 101.368062 \n", "L 42.691976 100.975034 \n", "L 43.068866 100.576645 \n", "L 43.445101 100.17271 \n", "L 43.820682 99.763081 \n", "L 44.195612 99.347664 \n", "L 44.569899 98.926412 \n", "L 44.943548 98.499335 \n", "L 45.316568 98.066514 \n", "L 45.68897 97.628093 \n", "L 46.060766 97.184295 \n", "L 46.431969 96.735421 \n", "L 46.802594 96.281851 \n", "L 47.17266 95.824049 \n", "L 47.542183 95.362563 \n", "L 47.911183 94.898027 \n", "L 48.279682 94.43115 \n", "L 48.647701 93.962724 \n", "L 49.015262 93.493609 \n", "L 49.382389 93.024734 \n", "L 49.749104 92.557081 \n", "L 50.115431 92.091684 \n", "L 50.48139 91.629609 \n", "L 50.847006 91.171949 \n", "L 51.212299 90.719809 \n", "L 51.577287 90.274287 \n", "L 51.941988 89.83647 \n", "L 52.306419 89.407405 \n", "L 52.670593 88.988098 \n", "L 53.034522 88.57949 \n", "L 53.398214 88.182447 \n", "L 53.761676 87.797747 \n", "L 54.124911 87.426065 \n", "L 54.487919 87.067967 \n", "L 54.850699 86.723897 \n", "L 55.213245 86.39417 \n", "L 55.57555 86.07897 \n", "L 55.937604 85.778346 \n", "L 56.299393 85.492211 \n", "L 56.660903 85.220338 \n", "L 57.022118 84.962376 \n", "L 57.383019 84.717842 \n", "L 57.743587 84.486138 \n", "L 58.103801 84.266557 \n", "L 58.463641 84.058292 \n", "L 58.823084 83.860454 \n", "L 59.18211 83.67208 \n", "L 59.540696 83.492149 \n", "L 59.898822 83.3196 \n", "L 60.256468 83.153339 \n", "L 60.613616 82.992263 \n", "L 60.970246 82.835269 \n", "L 61.326344 82.681266 \n", "L 61.681895 82.529196 \n", "L 62.036885 82.378036 \n", "L 62.391304 82.226816 \n", "L 62.745142 82.074625 \n", "L 63.098391 81.920618 \n", "L 63.451046 81.764027 \n", "L 63.803102 81.604159 \n", "L 64.154557 81.440404 \n", "L 64.50541 81.272236 \n", "L 64.855662 81.099212 \n", "L 65.205314 80.920972 \n", "L 65.554369 80.737237 \n", "L 65.902831 80.547805 \n", "L 66.250706 80.352547 \n", "L 66.597998 80.151403 \n", "L 66.944714 79.944375 \n", "L 67.290862 79.731523 \n", "L 67.636446 79.512957 \n", "L 67.981475 79.288832 \n", "L 68.325958 79.05934 \n", "L 68.669899 78.824705 \n", "L 69.013307 78.585178 \n", "L 69.35619 78.341026 \n", "L 69.698553 78.092534 \n", "L 70.040404 77.839994 \n", "L 70.381749 77.583702 \n", "L 70.722594 77.323958 \n", "L 71.062944 77.061056 \n", "L 71.402805 76.795283 \n", "L 71.742184 76.526917 \n", "L 72.081082 76.256228 \n", "L 72.419504 75.983471 \n", "L 72.757457 75.708887 \n", "L 73.09494 75.432703 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 38.768676 106.998073 \n", "L 39.154181 106.644987 \n", "L 39.539058 106.289463 \n", "L 39.923297 105.931174 \n", "L 40.306895 105.569767 \n", "L 40.689845 105.204871 \n", "L 41.072139 104.8361 \n", "L 41.453771 104.46305 \n", "L 41.834735 104.085307 \n", "L 42.215026 103.702447 \n", "L 42.594636 103.314046 \n", "L 42.973561 102.919679 \n", "L 43.351797 102.518931 \n", "L 43.729339 102.111401 \n", "L 44.106186 101.696711 \n", "L 44.482336 101.274511 \n", "L 44.857788 100.844493 \n", "L 45.232545 100.406393 \n", "L 45.606612 99.960004 \n", "L 45.979991 99.505186 \n", "L 46.352691 99.041873 \n", "L 46.724722 98.570079 \n", "L 47.096094 98.089917 \n", "L 47.466822 97.601591 \n", "L 47.836923 97.105415 \n", "L 48.206414 96.601816 \n", "L 48.575315 96.091332 \n", "L 48.94365 95.574624 \n", "L 49.311443 95.052469 \n", "L 49.678719 94.525768 \n", "L 50.045508 93.995528 \n", "L 50.411837 93.462876 \n", "L 50.777737 92.929036 \n", "L 51.143237 92.395328 \n", "L 51.508368 91.863154 \n", "L 51.873161 91.333982 \n", "L 52.237644 90.809336 \n", "L 52.601846 90.290772 \n", "L 52.965795 89.779862 \n", "L 53.329515 89.278179 \n", "L 53.69303 88.787266 \n", "L 54.056358 88.308624 \n", "L 54.419518 87.843685 \n", "L 54.782525 87.393795 \n", "L 55.145387 86.96019 \n", "L 55.508112 86.543981 \n", "L 55.870703 86.146133 \n", "L 56.23316 85.767454 \n", "L 56.595478 85.408578 \n", "L 56.95765 85.069959 \n", "L 57.319663 84.751858 \n", "L 57.681502 84.454345 \n", "L 58.043149 84.177295 \n", "L 58.404584 83.920388 \n", "L 58.765782 83.683118 \n", "L 59.126719 83.464799 \n", "L 59.487367 83.264574 \n", "L 59.847699 83.081435 \n", "L 60.207685 82.914231 \n", "L 60.567296 82.761691 \n", "L 60.926502 82.622443 \n", "L 61.285275 82.49503 \n", "L 61.643586 82.377935 \n", "L 62.00141 82.269604 \n", "L 62.358721 82.16846 \n", "L 62.715494 82.072932 \n", "L 63.071711 81.981469 \n", "L 63.427349 81.892561 \n", "L 63.782394 81.804753 \n", "L 64.136831 81.716665 \n", "L 64.490647 81.627 \n", "L 64.843833 81.534561 \n", "L 65.196381 81.43825 \n", "L 65.548287 81.337088 \n", "L 65.899548 81.230208 \n", "L 66.250163 81.116864 \n", "L 66.600133 80.996427 \n", "L 66.949463 80.868388 \n", "L 67.298154 80.732353 \n", "L 67.646213 80.588036 \n", "L 67.993648 80.435257 \n", "L 68.340465 80.273932 \n", "L 68.686673 80.104067 \n", "L 69.032283 79.925748 \n", "L 69.3773 79.739133 \n", "L 69.721737 79.544445 \n", "L 70.065604 79.341957 \n", "L 70.408908 79.131991 \n", "L 70.75166 78.914902 \n", "L 71.09387 78.691072 \n", "L 71.435545 78.460909 \n", "L 71.776694 78.224826 \n", "L 72.117327 77.983247 \n", "L 72.457449 77.736597 \n", "L 72.797069 77.485293 \n", "L 73.136192 77.229746 \n", "L 73.474826 76.970353 \n", "L 73.812975 76.707495 \n", "L 74.150644 76.441536 \n", "L 74.48784 76.172819 \n", "L 74.824565 75.901666 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 40.580733 107.531589 \n", "L 40.965234 107.170057 \n", "L 41.349083 106.80498 \n", "L 41.732271 106.435906 \n", "L 42.114789 106.062347 \n", "L 42.496629 105.683793 \n", "L 42.877782 105.29971 \n", "L 43.258239 104.909539 \n", "L 43.637991 104.51271 \n", "L 44.017033 104.108636 \n", "L 44.395353 103.696732 \n", "L 44.772946 103.276411 \n", "L 45.149808 102.847098 \n", "L 45.525931 102.40824 \n", "L 45.901313 101.959311 \n", "L 46.275955 101.49983 \n", "L 46.649854 101.029367 \n", "L 47.023012 100.547558 \n", "L 47.395438 100.054113 \n", "L 47.767134 99.548838 \n", "L 48.138112 99.031638 \n", "L 48.508385 98.502531 \n", "L 48.877967 97.961668 \n", "L 49.246877 97.40933 \n", "L 49.615137 96.845947 \n", "L 49.98277 96.272106 \n", "L 50.349802 95.688549 \n", "L 50.716264 95.096184 \n", "L 51.082188 94.496087 \n", "L 51.447606 93.889496 \n", "L 51.812556 93.277806 \n", "L 52.177074 92.662573 \n", "L 52.541199 92.045489 \n", "L 52.90497 91.42838 \n", "L 53.268425 90.813187 \n", "L 53.631604 90.201942 \n", "L 53.994544 89.596756 \n", "L 54.357281 88.999785 \n", "L 54.719849 88.413213 \n", "L 55.08228 87.839217 \n", "L 55.444601 87.27994 \n", "L 55.806838 86.737466 \n", "L 56.169012 86.213783 \n", "L 56.531139 85.71076 \n", "L 56.893231 85.230116 \n", "L 57.255296 84.773393 \n", "L 57.617335 84.341936 \n", "L 57.979346 83.936866 \n", "L 58.341322 83.559066 \n", "L 58.703249 83.209167 \n", "L 59.065112 82.887531 \n", "L 59.426888 82.594259 \n", "L 59.788552 82.329174 \n", "L 60.150075 82.091833 \n", "L 60.511427 81.881531 \n", "L 60.872572 81.697316 \n", "L 61.233475 81.537998 \n", "L 61.594097 81.402174 \n", "L 61.954401 81.288246 \n", "L 62.314347 81.194449 \n", "L 62.673897 81.118876 \n", "L 63.033015 81.059508 \n", "L 63.391662 81.014237 \n", "L 63.749807 80.980909 \n", "L 64.107415 80.957338 \n", "L 64.464456 80.941346 \n", "L 64.820906 80.930784 \n", "L 65.176737 80.92356 \n", "L 65.531931 80.917663 \n", "L 65.88647 80.911183 \n", "L 66.240337 80.902327 \n", "L 66.593524 80.88944 \n", "L 66.94602 80.871007 \n", "L 67.297821 80.845677 \n", "L 67.648924 80.812254 \n", "L 67.999329 80.76971 \n", "L 68.349038 80.717179 \n", "L 68.698058 80.653962 \n", "L 69.046392 80.579517 \n", "L 69.39405 80.493455 \n", "L 69.741042 80.395528 \n", "L 70.087377 80.285625 \n", "L 70.433066 80.163756 \n", "L 70.778122 80.030045 \n", "L 71.122557 79.884713 \n", "L 71.466383 79.728069 \n", "L 71.809613 79.560494 \n", "L 72.152259 79.382435 \n", "L 72.494333 79.194382 \n", "L 72.835847 78.996868 \n", "L 73.176812 78.790453 \n", "L 73.517238 78.575713 \n", "L 73.857137 78.353233 \n", "L 74.196515 78.123599 \n", "L 74.535383 77.887391 \n", "L 74.873749 77.645175 \n", "L 75.21162 77.3975 \n", "L 75.549002 77.144894 \n", "L 75.885902 76.887858 \n", "L 76.222326 76.626867 \n", "L 76.558278 76.362367 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 42.397128 108.055936 \n", "L 42.780594 107.68414 \n", "L 43.163382 107.307426 \n", "L 43.545479 106.925187 \n", "L 43.926876 106.536773 \n", "L 44.307562 106.141494 \n", "L 44.687524 105.738636 \n", "L 45.066753 105.32745 \n", "L 45.445236 104.907168 \n", "L 45.822965 104.477005 \n", "L 46.199929 104.036173 \n", "L 46.57612 103.583883 \n", "L 46.951531 103.119362 \n", "L 47.326156 102.641868 \n", "L 47.69999 102.150693 \n", "L 48.073034 101.645186 \n", "L 48.445286 101.12477 \n", "L 48.816751 100.588952 \n", "L 49.187435 100.037339 \n", "L 49.557346 99.469668 \n", "L 49.926499 98.885804 \n", "L 50.294909 98.28577 \n", "L 50.662595 97.669761 \n", "L 51.029581 97.038151 \n", "L 51.395896 96.39151 \n", "L 51.761568 95.730621 \n", "L 52.126632 95.056479 \n", "L 52.491125 94.370295 \n", "L 52.855089 93.673512 \n", "L 53.218565 92.967788 \n", "L 53.5816 92.254999 \n", "L 53.944239 91.537228 \n", "L 54.306531 90.816754 \n", "L 54.668524 90.09603 \n", "L 55.030267 89.377667 \n", "L 55.391809 88.664401 \n", "L 55.753193 87.959077 \n", "L 56.114467 87.2646 \n", "L 56.475671 86.583912 \n", "L 56.836843 85.919954 \n", "L 57.198016 85.275619 \n", "L 57.559221 84.653718 \n", "L 57.920482 84.05694 \n", "L 58.281817 83.487809 \n", "L 58.64324 82.948651 \n", "L 59.004756 82.441551 \n", "L 59.366366 81.968328 \n", "L 59.728064 81.530498 \n", "L 60.089839 81.129258 \n", "L 60.451671 80.765461 \n", "L 60.813539 80.4396 \n", "L 61.175411 80.151808 \n", "L 61.537254 79.901848 \n", "L 61.89903 79.689119 \n", "L 62.260697 79.51267 \n", "L 62.62221 79.371208 \n", "L 62.983523 79.263127 \n", "L 63.344587 79.186527 \n", "L 63.705352 79.139251 \n", "L 64.065771 79.118911 \n", "L 64.425793 79.12293 \n", "L 64.785373 79.148581 \n", "L 65.144464 79.19302 \n", "L 65.503025 79.25334 \n", "L 65.861015 79.326592 \n", "L 66.218397 79.409839 \n", "L 66.575141 79.500189 \n", "L 66.931215 79.594824 \n", "L 67.286597 79.691037 \n", "L 67.641264 79.78626 \n", "L 67.9952 79.878085 \n", "L 68.348394 79.964285 \n", "L 68.700836 80.042833 \n", "L 69.052521 80.111912 \n", "L 69.403448 80.169922 \n", "L 69.753617 80.215486 \n", "L 70.103035 80.24745 \n", "L 70.451709 80.264878 \n", "L 70.799646 80.267048 \n", "L 71.146859 80.253444 \n", "L 71.493361 80.223739 \n", "L 71.839165 80.177789 \n", "L 72.184285 80.115615 \n", "L 72.528738 80.037386 \n", "L 72.872539 79.943404 \n", "L 73.215704 79.834088 \n", "L 73.558249 79.709953 \n", "L 73.900189 79.571601 \n", "L 74.241538 79.419695 \n", "L 74.582313 79.254952 \n", "L 74.922524 79.078124 \n", "L 75.262186 78.889988 \n", "L 75.601312 78.691328 \n", "L 75.939911 78.482935 \n", "L 76.277994 78.265586 \n", "L 76.615571 78.040042 \n", "L 76.95265 77.80704 \n", "L 77.289239 77.56729 \n", "L 77.625344 77.321464 \n", "L 77.960973 77.070201 \n", "L 78.29613 76.814098 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 44.217925 108.571163 \n", "L 44.600333 108.187289 \n", "L 44.982034 107.796863 \n", "L 45.363014 107.39909 \n", "L 45.743261 106.993122 \n", "L 46.122761 106.578063 \n", "L 46.501499 106.152979 \n", "L 46.879462 105.716894 \n", "L 47.256637 105.268805 \n", "L 47.633014 104.807688 \n", "L 48.008579 104.332513 \n", "L 48.383322 103.842252 \n", "L 48.757236 103.335893 \n", "L 49.130313 102.812464 \n", "L 49.502549 102.27104 \n", "L 49.873944 101.710768 \n", "L 50.244498 101.130892 \n", "L 50.614216 100.53076 \n", "L 50.983107 99.909857 \n", "L 51.351183 99.267832 \n", "L 51.718459 98.604502 \n", "L 52.084958 97.919889 \n", "L 52.450702 97.214239 \n", "L 52.815723 96.488032 \n", "L 53.180054 95.742004 \n", "L 53.543732 94.977168 \n", "L 53.9068 94.194812 \n", "L 54.269305 93.396515 \n", "L 54.631295 92.584148 \n", "L 54.992824 91.759875 \n", "L 55.353946 90.92614 \n", "L 55.714719 90.08566 \n", "L 56.0752 89.24141 \n", "L 56.43545 88.396599 \n", "L 56.795525 87.55464 \n", "L 57.155483 86.719116 \n", "L 57.51538 85.89375 \n", "L 57.875269 85.08235 \n", "L 58.235197 84.288779 \n", "L 58.59521 83.516892 \n", "L 58.955347 82.770488 \n", "L 59.31564 82.053261 \n", "L 59.676118 81.368747 \n", "L 60.036799 80.720265 \n", "L 60.397696 80.110872 \n", "L 60.758814 79.543316 \n", "L 61.120151 79.019989 \n", "L 61.481695 78.542891 \n", "L 61.843429 78.113594 \n", "L 62.205326 77.733225 \n", "L 62.567356 77.402433 \n", "L 62.929479 77.121392 \n", "L 63.29165 76.88979 \n", "L 63.653821 76.706838 \n", "L 64.015939 76.571279 \n", "L 64.377946 76.481415 \n", "L 64.739784 76.435127 \n", "L 65.101393 76.429919 \n", "L 65.462712 76.462947 \n", "L 65.82368 76.531073 \n", "L 66.184238 76.630911 \n", "L 66.544329 76.758874 \n", "L 66.903898 76.911229 \n", "L 67.262896 77.084157 \n", "L 67.621275 77.273794 \n", "L 67.978991 77.476286 \n", "L 68.336008 77.687845 \n", "L 68.692292 77.904782 \n", "L 69.047816 78.123555 \n", "L 69.402557 78.340807 \n", "L 69.756497 78.553393 \n", "L 70.109624 78.758404 \n", "L 70.46193 78.9532 \n", "L 70.813412 79.135411 \n", "L 71.164069 79.302957 \n", "L 71.513907 79.454045 \n", "L 71.862933 79.58718 \n", "L 72.211159 79.701148 \n", "L 72.558597 79.795015 \n", "L 72.905261 79.868116 \n", "L 73.251172 79.920037 \n", "L 73.596343 79.950597 \n", "L 73.940796 79.959829 \n", "L 74.284551 79.947963 \n", "L 74.627626 79.915401 \n", "L 74.970041 79.862693 \n", "L 75.311817 79.79052 \n", "L 75.652969 79.699668 \n", "L 75.993518 79.591009 \n", "L 76.333481 79.46548 \n", "L 76.672873 79.324066 \n", "L 77.011709 79.167781 \n", "L 77.350005 78.997654 \n", "L 77.68777 78.814713 \n", "L 78.025019 78.619975 \n", "L 78.361761 78.414433 \n", "L 78.698007 78.19905 \n", "L 79.033764 77.974751 \n", "L 79.36904 77.742415 \n", "L 79.703843 77.502872 \n", "L 80.038177 77.256904 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 46.043216 109.078366 \n", "L 46.424554 108.680788 \n", "L 46.805157 108.274785 \n", "L 47.185008 107.85935 \n", "L 47.564092 107.433408 \n", "L 47.942395 106.995825 \n", "L 48.319897 106.545415 \n", "L 48.696584 106.080941 \n", "L 49.072442 105.601133 \n", "L 49.447457 105.104689 \n", "L 49.821613 104.590304 \n", "L 50.194901 104.056671 \n", "L 50.567313 103.502505 \n", "L 50.938839 102.92657 \n", "L 51.309477 102.327688 \n", "L 51.679226 101.704774 \n", "L 52.048087 101.056859 \n", "L 52.416068 100.383113 \n", "L 52.783181 99.682873 \n", "L 53.149439 98.955683 \n", "L 53.514864 98.201302 \n", "L 53.879483 97.419746 \n", "L 54.243323 96.611315 \n", "L 54.606423 95.776605 \n", "L 54.968824 94.916536 \n", "L 55.330569 94.032382 \n", "L 55.691711 93.125764 \n", "L 56.052306 92.198674 \n", "L 56.412411 91.253477 \n", "L 56.77209 90.292908 \n", "L 57.131408 89.320064 \n", "L 57.490431 88.33839 \n", "L 57.84923 87.351662 \n", "L 58.207872 86.363955 \n", "L 58.566424 85.379608 \n", "L 58.924955 84.403182 \n", "L 59.283527 83.439416 \n", "L 59.642201 82.493167 \n", "L 60.001031 81.569358 \n", "L 60.360067 80.672911 \n", "L 60.719354 79.808679 \n", "L 61.078925 78.981381 \n", "L 61.438811 78.195538 \n", "L 61.79903 77.455398 \n", "L 62.159592 76.764875 \n", "L 62.520499 76.127484 \n", "L 62.881744 75.546294 \n", "L 63.24331 75.023863 \n", "L 63.605169 74.56221 \n", "L 63.967289 74.162778 \n", "L 64.329628 73.826399 \n", "L 64.692133 73.553297 \n", "L 65.054751 73.343071 \n", "L 65.417419 73.19471 \n", "L 65.780073 73.106603 \n", "L 66.142641 73.07657 \n", "L 66.505054 73.101902 \n", "L 66.867237 73.179395 \n", "L 67.229119 73.305414 \n", "L 67.590627 73.475941 \n", "L 67.951693 73.686643 \n", "L 68.312249 73.932936 \n", "L 68.672232 74.210053 \n", "L 69.031585 74.513115 \n", "L 69.390253 74.837192 \n", "L 69.74819 75.177374 \n", "L 70.105353 75.52883 \n", "L 70.461707 75.886866 \n", "L 70.817224 76.246979 \n", "L 71.171879 76.6049 \n", "L 71.525655 76.956637 \n", "L 71.878543 77.298501 \n", "L 72.230535 77.627144 \n", "L 72.581632 77.939568 \n", "L 72.931839 78.233142 \n", "L 73.281162 78.505606 \n", "L 73.629616 78.755069 \n", "L 73.977215 78.98001 \n", "L 74.323977 79.179254 \n", "L 74.669923 79.351973 \n", "L 75.015076 79.497653 \n", "L 75.359456 79.616077 \n", "L 75.703089 79.7073 \n", "L 76.046 79.771624 \n", "L 76.38821 79.809567 \n", "L 76.729744 79.821836 \n", "L 77.070627 79.809303 \n", "L 77.410878 79.77297 \n", "L 77.750519 79.713948 \n", "L 78.08957 79.633431 \n", "L 78.42805 79.532671 \n", "L 78.765975 79.412956 \n", "L 79.103361 79.275593 \n", "L 79.440222 79.121888 \n", "L 79.776571 78.95313 \n", "L 80.11242 78.770582 \n", "L 80.447779 78.575462 \n", "L 80.782656 78.368943 \n", "L 81.117059 78.15214 \n", "L 81.450996 77.926107 \n", "L 81.784472 77.691832 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 47.873117 109.579862 \n", "L 48.253389 109.167349 \n", "L 48.632901 108.744358 \n", "L 49.011632 108.309649 \n", "L 49.389565 107.8619 \n", "L 49.766684 107.399713 \n", "L 50.142968 106.921628 \n", "L 50.5184 106.42612 \n", "L 50.892965 105.911624 \n", "L 51.266647 105.376535 \n", "L 51.639431 104.819242 \n", "L 52.011305 104.238132 \n", "L 52.382261 103.63162 \n", "L 52.752289 102.998176 \n", "L 53.121389 102.336344 \n", "L 53.48956 101.644778 \n", "L 53.856805 100.922278 \n", "L 54.223135 100.167808 \n", "L 54.588565 99.380541 \n", "L 54.953112 98.559901 \n", "L 55.316802 97.705577 \n", "L 55.679667 96.817574 \n", "L 56.041742 95.896244 \n", "L 56.40307 94.942303 \n", "L 56.763701 93.956874 \n", "L 57.123686 92.941503 \n", "L 57.483087 91.898175 \n", "L 57.841967 90.829331 \n", "L 58.200395 89.73787 \n", "L 58.558441 88.627157 \n", "L 58.916182 87.500998 \n", "L 59.273694 86.363644 \n", "L 59.631055 85.219746 \n", "L 59.988343 84.074338 \n", "L 60.345633 82.93278 \n", "L 60.703 81.800712 \n", "L 61.060515 80.684008 \n", "L 61.418243 79.588679 \n", "L 61.776245 78.520831 \n", "L 62.134573 77.486572 \n", "L 62.493273 76.491931 \n", "L 62.852381 75.542775 \n", "L 63.211925 74.644723 \n", "L 63.571921 73.803059 \n", "L 63.932377 73.022659 \n", "L 64.293287 72.307903 \n", "L 64.654638 71.662608 \n", "L 65.016404 71.089967 \n", "L 65.37855 70.5925 \n", "L 65.74103 70.172002 \n", "L 66.103792 69.82951 \n", "L 66.466772 69.565309 \n", "L 66.829901 69.378892 \n", "L 67.193106 69.269 \n", "L 67.556308 69.233626 \n", "L 67.919424 69.27005 \n", "L 68.282369 69.374894 \n", "L 68.64506 69.54417 \n", "L 69.007412 69.773348 \n", "L 69.369342 70.057428 \n", "L 69.730773 70.391019 \n", "L 70.09163 70.768422 \n", "L 70.451841 71.183717 \n", "L 70.811344 71.630841 \n", "L 71.170082 72.103681 \n", "L 71.528002 72.596149 \n", "L 71.885062 73.102266 \n", "L 72.241226 73.616219 \n", "L 72.596466 74.13244 \n", "L 72.95076 74.64566 \n", "L 73.304094 75.150946 \n", "L 73.65646 75.643759 \n", "L 74.007857 76.119972 \n", "L 74.35829 76.5759 \n", "L 74.707768 77.008308 \n", "L 75.056306 77.414428 \n", "L 75.403921 77.791946 \n", "L 75.750636 78.138999 \n", "L 76.096475 78.454164 \n", "L 76.441463 78.736433 \n", "L 76.785629 78.985191 \n", "L 77.129001 79.200188 \n", "L 77.471607 79.381507 \n", "L 77.813478 79.529536 \n", "L 78.154641 79.644924 \n", "L 78.495124 79.728555 \n", "L 78.834954 79.781513 \n", "L 79.174155 79.805043 \n", "L 79.512752 79.80052 \n", "L 79.850767 79.769421 \n", "L 80.188218 79.713294 \n", "L 80.525126 79.63373 \n", "L 80.861508 79.532342 \n", "L 81.197377 79.410741 \n", "L 81.532747 79.270518 \n", "L 81.867631 79.113226 \n", "L 82.202039 78.94037 \n", "L 82.535978 78.753395 \n", "L 82.869456 78.553673 \n", "L 83.202482 78.342501 \n", "L 83.535058 78.121097 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 49.707756 110.079219 \n", "L 50.086986 109.65115 \n", "L 50.465433 109.210458 \n", "L 50.843076 108.755662 \n", "L 51.219896 108.285178 \n", "L 51.595874 107.797335 \n", "L 51.970989 107.290384 \n", "L 52.345224 106.762502 \n", "L 52.71856 106.211809 \n", "L 53.090984 105.636388 \n", "L 53.462477 105.034304 \n", "L 53.83303 104.403623 \n", "L 54.202634 103.742444 \n", "L 54.57128 103.048927 \n", "L 54.938969 102.321323 \n", "L 55.305703 101.558011 \n", "L 55.671487 100.75754 \n", "L 56.036334 99.918659 \n", "L 56.400263 99.040366 \n", "L 56.763297 98.121949 \n", "L 57.125465 97.163023 \n", "L 57.486805 96.163571 \n", "L 57.847358 95.123993 \n", "L 58.207174 94.04513 \n", "L 58.56631 92.928298 \n", "L 58.924825 91.775331 \n", "L 59.282788 90.588587 \n", "L 59.64027 89.370965 \n", "L 59.997349 88.125926 \n", "L 60.354103 86.857487 \n", "L 60.710617 85.570198 \n", "L 61.066975 84.269145 \n", "L 61.423263 82.959903 \n", "L 61.779564 81.64851 \n", "L 62.135962 80.341404 \n", "L 62.492536 79.045367 \n", "L 62.84936 77.767457 \n", "L 63.206505 76.514924 \n", "L 63.564033 75.295123 \n", "L 63.921996 74.115418 \n", "L 64.28044 72.983093 \n", "L 64.639399 71.905234 \n", "L 64.998896 70.888628 \n", "L 65.358945 69.939674 \n", "L 65.719543 69.064273 \n", "L 66.080681 68.267721 \n", "L 66.442334 67.55465 \n", "L 66.804465 66.92893 \n", "L 67.16703 66.393608 \n", "L 67.52997 65.950859 \n", "L 67.893219 65.601951 \n", "L 68.256702 65.347219 \n", "L 68.620337 65.186057 \n", "L 68.984036 65.116929 \n", "L 69.347708 65.137404 \n", "L 69.711257 65.244188 \n", "L 70.074588 65.433189 \n", "L 70.437606 65.699568 \n", "L 70.800217 66.037848 \n", "L 71.16233 66.441978 \n", "L 71.52386 66.905433 \n", "L 71.884725 67.421331 \n", "L 72.244851 67.98252 \n", "L 72.604173 68.581682 \n", "L 72.96263 69.211448 \n", "L 73.320171 69.864484 \n", "L 73.676756 70.533588 \n", "L 74.03235 71.211772 \n", "L 74.386928 71.892346 \n", "L 74.740474 72.568983 \n", "L 75.092978 73.235772 \n", "L 75.444439 73.887273 \n", "L 75.794862 74.518548 \n", "L 76.14426 75.125197 \n", "L 76.492648 75.703362 \n", "L 76.840048 76.249738 \n", "L 77.186486 76.761579 \n", "L 77.531992 77.236678 \n", "L 77.876595 77.673345 \n", "L 78.220328 78.070401 \n", "L 78.563228 78.427131 \n", "L 78.905325 78.743255 \n", "L 79.246656 79.018893 \n", "L 79.587255 79.254525 \n", "L 79.927154 79.450947 \n", "L 80.266383 79.609234 \n", "L 80.604975 79.73069 \n", "L 80.942955 79.816822 \n", "L 81.280349 79.869284 \n", "L 81.617183 79.889851 \n", "L 81.953475 79.880383 \n", "L 82.289247 79.842789 \n", "L 82.624516 79.779005 \n", "L 82.959296 79.69096 \n", "L 83.2936 79.580562 \n", "L 83.62744 79.449671 \n", "L 83.960826 79.300089 \n", "L 84.293765 79.133544 \n", "L 84.626263 78.951681 \n", "L 84.958328 78.756051 \n", "L 85.28996 78.54811 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 51.547266 110.581097 \n", "L 51.925495 110.137645 \n", "L 52.302925 109.679454 \n", "L 52.679534 109.204798 \n", "L 53.055303 108.711838 \n", "L 53.430212 108.198631 \n", "L 53.804241 107.663142 \n", "L 54.17737 107.10325 \n", "L 54.549582 106.516769 \n", "L 54.920863 105.901465 \n", "L 55.291196 105.255086 \n", "L 55.660571 104.575378 \n", "L 56.028982 103.860124 \n", "L 56.396422 103.107175 \n", "L 56.762892 102.31449 \n", "L 57.128398 101.480171 \n", "L 57.492948 100.602517 \n", "L 57.856558 99.68006 \n", "L 58.219253 98.711615 \n", "L 58.581057 97.696335 \n", "L 58.942006 96.633751 \n", "L 59.302145 95.523821 \n", "L 59.661518 94.366988 \n", "L 60.020183 93.164201 \n", "L 60.378201 91.916971 \n", "L 60.73564 90.627403 \n", "L 61.092575 89.298219 \n", "L 61.449083 87.932769 \n", "L 61.805249 86.535065 \n", "L 62.161157 85.109758 \n", "L 62.516899 83.662142 \n", "L 62.872563 82.198126 \n", "L 63.22824 80.724206 \n", "L 63.584018 79.247413 \n", "L 63.939983 77.775262 \n", "L 64.296217 76.315673 \n", "L 64.652795 74.876894 \n", "L 65.009788 73.467407 \n", "L 65.367255 72.095826 \n", "L 65.725248 70.770786 \n", "L 66.083807 69.500817 \n", "L 66.442961 68.294242 \n", "L 66.802727 67.159038 \n", "L 67.16311 66.102715 \n", "L 67.524099 65.132211 \n", "L 67.885673 64.253764 \n", "L 68.247797 63.472817 \n", "L 68.610422 62.79393 \n", "L 68.973489 62.220691 \n", "L 69.336929 61.755661 \n", "L 69.700662 61.400332 \n", "L 70.064599 61.155098 \n", "L 70.428646 61.019244 \n", "L 70.792702 60.990964 \n", "L 71.156664 61.06739 \n", "L 71.520427 61.244649 \n", "L 71.883885 61.517913 \n", "L 72.246935 61.8815 \n", "L 72.609476 62.328961 \n", "L 72.971412 62.853178 \n", "L 73.332653 63.446497 \n", "L 73.693116 64.10083 \n", "L 74.052727 64.807782 \n", "L 74.411418 65.558782 \n", "L 74.769134 66.345193 \n", "L 75.125826 67.158429 \n", "L 75.481458 67.990067 \n", "L 75.836001 68.831945 \n", "L 76.189438 69.676254 \n", "L 76.541759 70.515609 \n", "L 76.892964 71.34313 \n", "L 77.243059 72.152484 \n", "L 77.592058 72.937935 \n", "L 77.939983 73.694368 \n", "L 78.28686 74.417317 \n", "L 78.632717 75.102953 \n", "L 78.97759 75.748107 \n", "L 79.321517 76.350231 \n", "L 79.664534 76.907392 \n", "L 80.006682 77.418237 \n", "L 80.348004 77.881962 \n", "L 80.688536 78.298264 \n", "L 81.028321 78.667308 \n", "L 81.367396 78.989671 \n", "L 81.705797 79.266298 \n", "L 82.043559 79.498455 \n", "L 82.380715 79.687677 \n", "L 82.717292 79.835725 \n", "L 83.05332 79.944536 \n", "L 83.388823 80.016184 \n", "L 83.723822 80.052837 \n", "L 84.058336 80.056724 \n", "L 84.392382 80.030098 \n", "L 84.725974 79.975208 \n", "L 85.059124 79.894273 \n", "L 85.391842 79.789459 \n", "L 85.724137 79.662863 \n", "L 86.056013 79.516493 \n", "L 86.387476 79.352262 \n", "L 86.718531 79.171974 \n", "L 87.049177 78.977323 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 53.391771 111.090881 \n", "L 53.769054 110.633145 \n", "L 54.145532 110.158709 \n", "L 54.521182 109.665628 \n", "L 54.895985 109.151824 \n", "L 55.269922 108.6151 \n", "L 55.642972 108.053158 \n", "L 56.015118 107.463601 \n", "L 56.386343 106.843958 \n", "L 56.756633 106.191702 \n", "L 57.125974 105.504285 \n", "L 57.494358 104.779156 \n", "L 57.86178 104.013801 \n", "L 58.228237 103.205789 \n", "L 58.593732 102.3528 \n", "L 58.958274 101.45268 \n", "L 59.321876 100.503492 \n", "L 59.684557 99.503563 \n", "L 60.046345 98.451533 \n", "L 60.407271 97.346427 \n", "L 60.767376 96.187692 \n", "L 61.126706 94.975257 \n", "L 61.485314 93.709596 \n", "L 61.843262 92.391758 \n", "L 62.200617 91.023421 \n", "L 62.55745 89.606942 \n", "L 62.913841 88.145365 \n", "L 63.269874 86.642466 \n", "L 63.625636 85.102748 \n", "L 63.981216 83.531461 \n", "L 64.336707 81.934574 \n", "L 64.6922 80.318768 \n", "L 65.047789 78.691382 \n", "L 65.403561 77.060383 \n", "L 65.759602 75.434283 \n", "L 66.115992 73.822065 \n", "L 66.472803 72.233095 \n", "L 66.830101 70.677002 \n", "L 67.187942 69.163585 \n", "L 67.54637 67.702666 \n", "L 67.905417 66.303967 \n", "L 68.265104 64.97696 \n", "L 68.625438 63.730744 \n", "L 68.986411 62.57389 \n", "L 69.348002 61.514313 \n", "L 69.710177 60.559134 \n", "L 70.072887 59.714574 \n", "L 70.436071 58.98584 \n", "L 70.799657 58.377033 \n", "L 71.16356 57.891084 \n", "L 71.52769 57.529697 \n", "L 71.891943 57.293321 \n", "L 72.256215 57.18114 \n", "L 72.620394 57.191086 \n", "L 72.984368 57.319882 \n", "L 73.348023 57.563096 \n", "L 73.711248 57.915217 \n", "L 74.073934 58.36975 \n", "L 74.435976 58.919334 \n", "L 74.797278 59.555851 \n", "L 75.157749 60.270564 \n", "L 75.517308 61.054253 \n", "L 75.875884 61.897363 \n", "L 76.233415 62.790131 \n", "L 76.589852 63.722734 \n", "L 76.945152 64.685408 \n", "L 77.299289 65.668593 \n", "L 77.652242 66.663026 \n", "L 78.004006 67.65985 \n", "L 78.35458 68.650708 \n", "L 78.703974 69.627806 \n", "L 79.052207 70.583989 \n", "L 79.399304 71.512777 \n", "L 79.745296 72.408407 \n", "L 80.090219 73.265838 \n", "L 80.434114 74.080776 \n", "L 80.777024 74.849651 \n", "L 81.118996 75.569617 \n", "L 81.460074 76.238512 \n", "L 81.800307 76.854836 \n", "L 82.139742 77.417706 \n", "L 82.478424 77.926805 \n", "L 82.816398 78.382341 \n", "L 83.153705 78.784991 \n", "L 83.490385 79.13584 \n", "L 83.826475 79.436337 \n", "L 84.162009 79.688233 \n", "L 84.497017 79.89353 \n", "L 84.831525 80.054428 \n", "L 85.165561 80.173279 \n", "L 85.499141 80.252543 \n", "L 85.832286 80.294741 \n", "L 86.165011 80.302425 \n", "L 86.497327 80.278138 \n", "L 86.829244 80.22439 \n", "L 87.160771 80.143631 \n", "L 87.491913 80.038229 \n", "L 87.822673 79.910455 \n", "L 88.153054 79.762468 \n", "L 88.483057 79.596307 \n", "L 88.81268 79.413884 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 55.241359 111.614145 \n", "L 55.617765 111.144176 \n", "L 55.993368 110.655849 \n", "L 56.368147 110.147026 \n", "L 56.742084 109.61543 \n", "L 57.115162 109.058651 \n", "L 57.48736 108.474165 \n", "L 57.858664 107.85934 \n", "L 58.229059 107.211464 \n", "L 58.598533 106.52776 \n", "L 58.967075 105.805428 \n", "L 59.334679 105.041665 \n", "L 59.701345 104.233705 \n", "L 60.06707 103.378873 \n", "L 60.431864 102.474612 \n", "L 60.795739 101.518547 \n", "L 61.15871 100.508543 \n", "L 61.520803 99.442742 \n", "L 61.882049 98.319639 \n", "L 62.242482 97.138144 \n", "L 62.602149 95.897629 \n", "L 62.9611 94.597993 \n", "L 63.319392 93.239734 \n", "L 63.67709 91.823978 \n", "L 64.034265 90.352543 \n", "L 64.390992 88.827993 \n", "L 64.747353 87.253645 \n", "L 65.103433 85.633623 \n", "L 65.459321 83.972855 \n", "L 65.815107 82.277088 \n", "L 66.170883 80.552867 \n", "L 66.526739 78.807523 \n", "L 66.882766 77.049119 \n", "L 67.239047 75.286416 \n", "L 67.595664 73.528776 \n", "L 67.952692 71.786089 \n", "L 68.310194 70.068677 \n", "L 68.66823 68.387162 \n", "L 69.026845 66.752349 \n", "L 69.386072 65.175089 \n", "L 69.745934 63.666119 \n", "L 70.106437 62.235917 \n", "L 70.467576 60.894536 \n", "L 70.82933 59.651466 \n", "L 71.191664 58.515467 \n", "L 71.554528 57.494431 \n", "L 71.91786 56.595241 \n", "L 72.281585 55.823672 \n", "L 72.645618 55.184271 \n", "L 73.009861 54.680285 \n", "L 73.37421 54.313598 \n", "L 73.738554 54.084715 \n", "L 74.102776 53.992722 \n", "L 74.466759 54.035333 \n", "L 74.830383 54.208913 \n", "L 75.19353 54.50855 \n", "L 75.556085 54.928133 \n", "L 75.91794 55.460474 \n", "L 76.278991 56.097415 \n", "L 76.639143 56.829972 \n", "L 76.998311 57.648475 \n", "L 77.35642 58.542736 \n", "L 77.713407 59.502191 \n", "L 78.069218 60.516056 \n", "L 78.423814 61.573483 \n", "L 78.777166 62.66371 \n", "L 79.129258 63.776193 \n", "L 79.480082 64.900726 \n", "L 79.829645 66.027562 \n", "L 80.17796 67.147511 \n", "L 80.52505 68.252006 \n", "L 80.870947 69.33319 \n", "L 81.215688 70.383955 \n", "L 81.559316 71.397981 \n", "L 81.901879 72.369757 \n", "L 82.243427 73.294584 \n", "L 82.584014 74.168566 \n", "L 82.923695 74.988603 \n", "L 83.262523 75.752347 \n", "L 83.600551 76.458175 \n", "L 83.937835 77.105142 \n", "L 84.274422 77.692925 \n", "L 84.610361 78.221773 \n", "L 84.945698 78.69245 \n", "L 85.280473 79.106166 \n", "L 85.614724 79.464526 \n", "L 85.948487 79.76947 \n", "L 86.281789 80.023205 \n", "L 86.614659 80.228155 \n", "L 86.947119 80.386913 \n", "L 87.279186 80.502185 \n", "L 87.610878 80.576743 \n", "L 87.942207 80.613392 \n", "L 88.273182 80.614928 \n", "L 88.60381 80.584107 \n", "L 88.934095 80.523618 \n", "L 89.26404 80.436062 \n", "L 89.593645 80.323932 \n", "L 89.92291 80.189596 \n", "L 90.251832 80.035293 \n", "L 90.580408 79.863118 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 57.096086 112.156012 \n", "L 57.471686 111.676738 \n", "L 57.846496 111.177874 \n", "L 58.220497 110.657144 \n", "L 58.593673 110.112117 \n", "L 58.966008 109.540226 \n", "L 59.337487 108.93878 \n", "L 59.708095 108.304973 \n", "L 60.077823 107.635911 \n", "L 60.446661 106.928632 \n", "L 60.814601 106.180151 \n", "L 61.181642 105.387472 \n", "L 61.547786 104.547644 \n", "L 61.913037 103.657808 \n", "L 62.277405 102.715232 \n", "L 62.640909 101.717375 \n", "L 63.003568 100.661948 \n", "L 63.365411 99.546962 \n", "L 63.726474 98.370797 \n", "L 64.086794 97.132277 \n", "L 64.446422 95.830715 \n", "L 64.805411 94.465985 \n", "L 65.16382 93.038601 \n", "L 65.521717 91.549741 \n", "L 65.879175 90.001327 \n", "L 66.236268 88.39607 \n", "L 66.59308 86.737495 \n", "L 66.949695 85.029975 \n", "L 67.306199 83.278754 \n", "L 67.662679 81.489949 \n", "L 68.019224 79.670529 \n", "L 68.375919 77.828311 \n", "L 68.732848 75.971899 \n", "L 69.090087 74.110636 \n", "L 69.447709 72.25453 \n", "L 69.805777 70.414141 \n", "L 70.164348 68.600515 \n", "L 70.523465 66.82501 \n", "L 70.883162 65.099192 \n", "L 71.243458 63.434686 \n", "L 71.604361 61.842985 \n", "L 71.965863 60.335323 \n", "L 72.327943 58.922486 \n", "L 72.690564 57.614645 \n", "L 73.053676 56.421198 \n", "L 73.417215 55.350619 \n", "L 73.781105 54.4103 \n", "L 74.145255 53.606436 \n", "L 74.509568 52.943911 \n", "L 74.873936 52.426213 \n", "L 75.238245 52.055365 \n", "L 75.602374 51.831909 \n", "L 75.966202 51.754864 \n", "L 76.329606 51.821771 \n", "L 76.692464 52.028728 \n", "L 77.054657 52.370458 \n", "L 77.416073 52.840401 \n", "L 77.776606 53.430842 \n", "L 78.136158 54.13302 \n", "L 78.494641 54.937299 \n", "L 78.851979 55.833314 \n", "L 79.208109 56.810137 \n", "L 79.562976 57.856442 \n", "L 79.916544 58.960689 \n", "L 80.268784 60.111259 \n", "L 80.619682 61.296632 \n", "L 80.969237 62.505535 \n", "L 81.317457 63.727053 \n", "L 81.664363 64.950778 \n", "L 82.009985 66.166889 \n", "L 82.354358 67.366251 \n", "L 82.697529 68.540483 \n", "L 83.039547 69.682008 \n", "L 83.380469 70.784101 \n", "L 83.720353 71.840894 \n", "L 84.059261 72.847392 \n", "L 84.397253 73.799463 \n", "L 84.734395 74.693817 \n", "L 85.070746 75.52797 \n", "L 85.406365 76.300212 \n", "L 85.741312 77.009556 \n", "L 86.075637 77.655675 \n", "L 86.409394 78.238853 \n", "L 86.742629 78.759921 \n", "L 87.075381 79.220182 \n", "L 87.407691 79.621363 \n", "L 87.739592 79.96554 \n", "L 88.071111 80.255077 \n", "L 88.402273 80.49257 \n", "L 88.733099 80.680787 \n", "L 89.063602 80.822618 \n", "L 89.393798 80.921024 \n", "L 89.723695 80.979 \n", "L 90.053297 80.999526 \n", "L 90.382608 80.985545 \n", "L 90.711629 80.939923 \n", "L 91.040359 80.865434 \n", "L 91.368793 80.764734 \n", "L 91.696928 80.640351 \n", "L 92.024758 80.494668 \n", "L 92.352276 80.32992 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 58.955955 112.720497 \n", "L 59.330817 112.235536 \n", "L 59.704912 111.730282 \n", "L 60.078222 111.202378 \n", "L 60.450735 110.649309 \n", "L 60.822439 110.068414 \n", "L 61.193321 109.456909 \n", "L 61.563371 108.811887 \n", "L 61.932581 108.130351 \n", "L 62.300949 107.409233 \n", "L 62.668469 106.64544 \n", "L 63.035144 105.835869 \n", "L 63.40098 104.97746 \n", "L 63.765987 104.067249 \n", "L 64.130178 103.102404 \n", "L 64.493576 102.080288 \n", "L 64.856205 100.998525 \n", "L 65.218096 99.855047 \n", "L 65.57929 98.64817 \n", "L 65.939827 97.376667 \n", "L 66.299758 96.039815 \n", "L 66.659142 94.637476 \n", "L 67.018036 93.170168 \n", "L 67.376508 91.639102 \n", "L 67.734633 90.046255 \n", "L 68.092482 88.394423 \n", "L 68.450135 86.687245 \n", "L 68.807675 84.929239 \n", "L 69.165181 83.125829 \n", "L 69.522736 81.283341 \n", "L 69.88042 79.40899 \n", "L 70.23831 77.51087 \n", "L 70.596478 75.597888 \n", "L 70.954993 73.679731 \n", "L 71.313912 71.766764 \n", "L 71.673289 69.869951 \n", "L 72.033161 68.000738 \n", "L 72.393561 66.170912 \n", "L 72.754505 64.392478 \n", "L 73.115996 62.677501 \n", "L 73.478025 61.037919 \n", "L 73.840568 59.4854 \n", "L 74.203587 58.031145 \n", "L 74.567031 56.685728 \n", "L 74.930831 55.458916 \n", "L 75.294911 54.359513 \n", "L 75.65918 53.395205 \n", "L 76.023536 52.572434 \n", "L 76.387872 51.896279 \n", "L 76.752069 51.370363 \n", "L 77.116008 50.996798 \n", "L 77.479563 50.776138 \n", "L 77.84261 50.70737 \n", "L 78.205024 50.787932 \n", "L 78.566686 51.013767 \n", "L 78.927481 51.379386 \n", "L 79.287302 51.877972 \n", "L 79.64605 52.501498 \n", "L 80.003637 53.240864 \n", "L 80.359988 54.086056 \n", "L 80.715038 55.026301 \n", "L 81.068737 56.050248 \n", "L 81.421048 57.146134 \n", "L 81.771948 58.301977 \n", "L 82.121425 59.505716 \n", "L 82.469482 60.745397 \n", "L 82.816134 62.009324 \n", "L 83.161407 63.286183 \n", "L 83.505336 64.565174 \n", "L 83.847966 65.836126 \n", "L 84.189348 67.08957 \n", "L 84.529543 68.31683 \n", "L 84.868613 69.510062 \n", "L 85.206626 70.662304 \n", "L 85.543651 71.76749 \n", "L 85.879758 72.820454 \n", "L 86.215017 73.816929 \n", "L 86.5495 74.753519 \n", "L 86.88327 75.627665 \n", "L 87.216393 76.437605 \n", "L 87.54893 77.182333 \n", "L 87.880936 77.861518 \n", "L 88.212464 78.475466 \n", "L 88.543559 79.025045 \n", "L 88.874263 79.511616 \n", "L 89.204613 79.936974 \n", "L 89.53464 80.303275 \n", "L 89.864368 80.612974 \n", "L 90.193821 80.868763 \n", "L 90.523015 81.073512 \n", "L 90.85196 81.230217 \n", "L 91.180667 81.341948 \n", "L 91.50914 81.411806 \n", "L 91.83738 81.44288 \n", "L 92.165385 81.438217 \n", "L 92.493153 81.400787 \n", "L 92.820679 81.333461 \n", "L 93.147953 81.23899 \n", "L 93.474969 81.119991 \n", "L 93.801717 80.978932 \n", "L 94.128185 80.818126 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 60.820926 113.309976 \n", "L 61.195107 112.823352 \n", "L 61.568551 112.31632 \n", "L 61.941243 111.786509 \n", "L 62.313175 111.231391 \n", "L 62.684339 110.64829 \n", "L 63.054724 110.034404 \n", "L 63.424327 109.386811 \n", "L 63.793143 108.702497 \n", "L 64.161174 107.978377 \n", "L 64.528418 107.211339 \n", "L 64.894884 106.398261 \n", "L 65.260581 105.536068 \n", "L 65.625523 104.621777 \n", "L 65.98973 103.652537 \n", "L 66.353225 102.625699 \n", "L 66.716038 101.53887 \n", "L 67.078202 100.38997 \n", "L 67.43976 99.177303 \n", "L 67.800755 97.899634 \n", "L 68.161239 96.556234 \n", "L 68.52127 95.146962 \n", "L 68.880906 93.672338 \n", "L 69.240214 92.133578 \n", "L 69.599263 90.532667 \n", "L 69.958123 88.872415 \n", "L 70.316869 87.15648 \n", "L 70.675576 85.389405 \n", "L 71.034318 83.576643 \n", "L 71.393166 81.724553 \n", "L 71.752192 79.840393 \n", "L 72.111461 77.932299 \n", "L 72.471033 76.009236 \n", "L 72.830962 74.080939 \n", "L 73.191292 72.157843 \n", "L 73.552059 70.250969 \n", "L 73.913287 68.371835 \n", "L 74.274989 66.532298 \n", "L 74.637166 64.74444 \n", "L 74.999803 63.020391 \n", "L 75.362874 61.372174 \n", "L 75.726339 59.81152 \n", "L 76.090142 58.349709 \n", "L 76.454217 56.997374 \n", "L 76.818483 55.764347 \n", "L 77.182849 54.659488 \n", "L 77.547213 53.690531 \n", "L 77.911465 52.863963 \n", "L 78.275488 52.184887 \n", "L 78.63916 51.65696 \n", "L 79.002358 51.282301 \n", "L 79.364953 51.061466 \n", "L 79.726823 50.993439 \n", "L 80.087848 51.075644 \n", "L 80.447913 51.303989 \n", "L 80.806911 51.672959 \n", "L 81.164744 52.175685 \n", "L 81.521324 52.804095 \n", "L 81.876578 53.54903 \n", "L 82.230442 54.400407 \n", "L 82.582869 55.347392 \n", "L 82.933824 56.378559 \n", "L 83.283286 57.482078 \n", "L 83.631249 58.645882 \n", "L 83.977721 59.857847 \n", "L 84.32272 61.105942 \n", "L 84.666278 62.3784 \n", "L 85.008437 63.663839 \n", "L 85.349248 64.951402 \n", "L 85.688772 66.230849 \n", "L 86.027072 67.492663 \n", "L 86.364222 68.728115 \n", "L 86.700295 69.929318 \n", "L 87.035371 71.089272 \n", "L 87.369525 72.201876 \n", "L 87.702837 73.261939 \n", "L 88.035382 74.265169 \n", "L 88.367237 75.208154 \n", "L 88.69847 76.088323 \n", "L 89.029148 76.903906 \n", "L 89.359336 77.653891 \n", "L 89.689087 78.337951 \n", "L 90.018454 78.956392 \n", "L 90.347483 79.510091 \n", "L 90.676212 80.000418 \n", "L 91.004676 80.429178 \n", "L 91.332903 80.798542 \n", "L 91.660915 81.11098 \n", "L 91.988729 81.369199 \n", "L 92.31636 81.576087 \n", "L 92.643813 81.734658 \n", "L 92.971093 81.848 \n", "L 93.298201 81.919231 \n", "L 93.625133 81.951457 \n", "L 93.951884 81.947745 \n", "L 94.278445 81.911079 \n", "L 94.60481 81.844348 \n", "L 94.930963 81.75032 \n", "L 95.256894 81.631625 \n", "L 95.58259 81.490744 \n", "L 95.908037 81.330005 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 62.690915 113.924853 \n", "L 63.064455 113.440658 \n", "L 63.437293 112.936538 \n", "L 63.809418 112.410177 \n", "L 64.180825 111.859105 \n", "L 64.551511 111.280709 \n", "L 64.921469 110.672253 \n", "L 65.290698 110.030882 \n", "L 65.659201 109.353652 \n", "L 66.026982 108.637551 \n", "L 66.394044 107.87954 \n", "L 66.7604 107.076572 \n", "L 67.126065 106.225644 \n", "L 67.491054 105.323846 \n", "L 67.855391 104.368396 \n", "L 68.219104 103.356709 \n", "L 68.582223 102.286452 \n", "L 68.944786 101.155598 \n", "L 69.306836 99.962496 \n", "L 69.668416 98.705946 \n", "L 70.029578 97.385242 \n", "L 70.390379 96.000255 \n", "L 70.750875 94.551495 \n", "L 71.111128 93.040162 \n", "L 71.471205 91.468201 \n", "L 71.831169 89.838365 \n", "L 72.191088 88.154234 \n", "L 72.551028 86.42025 \n", "L 72.911053 84.641742 \n", "L 73.271224 82.824933 \n", "L 73.631602 80.976908 \n", "L 73.992236 79.105615 \n", "L 74.353175 77.21981 \n", "L 74.714456 75.329 \n", "L 75.076107 73.443365 \n", "L 75.438148 71.573664 \n", "L 75.800587 69.731128 \n", "L 76.163419 67.927332 \n", "L 76.526627 66.174054 \n", "L 76.890181 64.483127 \n", "L 77.254039 62.866263 \n", "L 77.618144 61.334906 \n", "L 77.982428 59.90004 \n", "L 78.34681 58.572037 \n", "L 78.711199 57.36047 \n", "L 79.075492 56.273975 \n", "L 79.439582 55.320086 \n", "L 79.80335 54.505115 \n", "L 80.166677 53.834044 \n", "L 80.529439 53.310425 \n", "L 80.891512 52.936326 \n", "L 81.252773 52.712289 \n", "L 81.613102 52.637322 \n", "L 81.972387 52.708915 \n", "L 82.330521 52.923091 \n", "L 82.687409 53.274467 \n", "L 83.042965 53.756362 \n", "L 83.397115 54.360906 \n", "L 83.749799 55.079175 \n", "L 84.100971 55.901353 \n", "L 84.450599 56.816869 \n", "L 84.798666 57.814596 \n", "L 85.145167 58.882995 \n", "L 85.490114 60.010308 \n", "L 85.833531 61.184708 \n", "L 86.175452 62.394464 \n", "L 86.515925 63.628092 \n", "L 86.855007 64.87449 \n", "L 87.192763 66.123058 \n", "L 87.529264 67.363807 \n", "L 87.864589 68.587442 \n", "L 88.198819 69.785435 \n", "L 88.532037 70.950083 \n", "L 88.864329 72.074548 \n", "L 89.19578 73.152863 \n", "L 89.526472 74.179953 \n", "L 89.856487 75.151621 \n", "L 90.185902 76.064527 \n", "L 90.514788 76.916149 \n", "L 90.843213 77.704755 \n", "L 91.171241 78.429346 \n", "L 91.498924 79.089596 \n", "L 91.826315 79.685798 \n", "L 92.153456 80.218802 \n", "L 92.480382 80.68994 \n", "L 92.807125 81.10097 \n", "L 93.13371 81.454008 \n", "L 93.460154 81.751463 \n", "L 93.786471 81.995975 \n", "L 94.11267 82.190364 \n", "L 94.438753 82.33757 \n", "L 94.764719 82.440608 \n", "L 95.090567 82.502522 \n", "L 95.416287 82.526346 \n", "L 95.741871 82.515073 \n", "L 96.067306 82.471619 \n", "L 96.39258 82.398806 \n", "L 96.717676 82.299334 \n", "L 97.042579 82.175776 \n", "L 97.367276 82.030553 \n", "L 97.691746 81.865942 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 64.565817 114.563504 \n", "L 64.938736 114.08555 \n", "L 65.310989 113.588712 \n", "L 65.682571 113.070792 \n", "L 66.053479 112.529445 \n", "L 66.423714 111.962193 \n", "L 66.793274 111.366441 \n", "L 67.162163 110.739479 \n", "L 67.530385 110.078518 \n", "L 67.89795 109.380701 \n", "L 68.264865 108.643144 \n", "L 68.631146 107.862964 \n", "L 68.996811 107.037315 \n", "L 69.36188 106.163439 \n", "L 69.726378 105.238702 \n", "L 70.090336 104.260662 \n", "L 70.453785 103.227112 \n", "L 70.816765 102.13614 \n", "L 71.179316 100.98619 \n", "L 71.541484 99.776135 \n", "L 71.903318 98.505318 \n", "L 72.264871 97.173632 \n", "L 72.626196 95.781577 \n", "L 72.987351 94.330307 \n", "L 73.348395 92.82168 \n", "L 73.709385 91.258325 \n", "L 74.07038 89.643649 \n", "L 74.431436 87.981886 \n", "L 74.792606 86.278098 \n", "L 75.153941 84.538196 \n", "L 75.515486 82.768908 \n", "L 75.877279 80.977775 \n", "L 76.239352 79.1731 \n", "L 76.601728 77.363886 \n", "L 76.964419 75.559784 \n", "L 77.32743 73.770978 \n", "L 77.690751 72.008098 \n", "L 78.054364 70.282091 \n", "L 78.418235 68.60409 \n", "L 78.782321 66.985285 \n", "L 79.146564 65.436739 \n", "L 79.510896 63.969263 \n", "L 79.875238 62.593225 \n", "L 80.239499 61.318415 \n", "L 80.60358 60.153867 \n", "L 80.967373 59.107729 \n", "L 81.330765 58.187104 \n", "L 81.693637 57.397948 \n", "L 82.05587 56.744959 \n", "L 82.417342 56.231484 \n", "L 82.777933 55.85947 \n", "L 83.137526 55.629436 \n", "L 83.49601 55.54044 \n", "L 83.853283 55.590119 \n", "L 84.20925 55.774723 \n", "L 84.563828 56.089178 \n", "L 84.916944 56.527179 \n", "L 85.26854 57.081307 \n", "L 85.618572 57.743141 \n", "L 85.967009 58.503417 \n", "L 86.313836 59.352157 \n", "L 86.65905 60.278855 \n", "L 87.002664 61.272612 \n", "L 87.344705 62.322317 \n", "L 87.68521 63.416785 \n", "L 88.024228 64.544926 \n", "L 88.361821 65.695878 \n", "L 88.698056 66.859129 \n", "L 89.033009 68.024645 \n", "L 89.366763 69.182954 \n", "L 89.699401 70.325252 \n", "L 90.031015 71.443451 \n", "L 90.361692 72.53024 \n", "L 90.691523 73.579126 \n", "L 91.020597 74.584437 \n", "L 91.348998 75.541352 \n", "L 91.676809 76.445869 \n", "L 92.004108 77.294809 \n", "L 92.330965 78.085761 \n", "L 92.657449 78.817069 \n", "L 92.983619 79.48777 \n", "L 93.309527 80.097537 \n", "L 93.63522 80.646636 \n", "L 93.96074 81.135859 \n", "L 94.286118 81.566459 \n", "L 94.61138 81.940093 \n", "L 94.936549 82.258759 \n", "L 95.261636 82.524736 \n", "L 95.586651 82.740519 \n", "L 95.9116 82.908778 \n", "L 96.23648 83.032299 \n", "L 96.561288 83.113938 \n", "L 96.886016 83.15658 \n", "L 97.210652 83.163102 \n", "L 97.535184 83.136342 \n", "L 97.859596 83.079064 \n", "L 98.183873 82.993945 \n", "L 98.507995 82.883549 \n", "L 98.831945 82.750313 \n", "L 99.155706 82.596537 \n", "L 99.479257 82.424381 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 66.445517 115.222506 \n", "L 66.817813 114.754017 \n", "L 67.189479 114.268156 \n", "L 67.560514 113.762895 \n", "L 67.930918 113.236075 \n", "L 68.300696 112.685411 \n", "L 68.669849 112.108511 \n", "L 69.038385 111.50288 \n", "L 69.406312 110.865949 \n", "L 69.773642 110.195086 \n", "L 70.140386 109.487641 \n", "L 70.506562 108.740955 \n", "L 70.872192 107.952414 \n", "L 71.237297 107.119481 \n", "L 71.601904 106.239742 \n", "L 71.966045 105.310949 \n", "L 72.329751 104.331085 \n", "L 72.69306 103.298399 \n", "L 73.056015 102.211471 \n", "L 73.418657 101.06928 \n", "L 73.781031 99.87124 \n", "L 74.143189 98.617269 \n", "L 74.505177 97.307849 \n", "L 74.867047 95.944061 \n", "L 75.228852 94.527644 \n", "L 75.590638 93.061037 \n", "L 75.952456 91.5474 \n", "L 76.314353 89.99065 \n", "L 76.67637 88.395466 \n", "L 77.038545 86.767308 \n", "L 77.40091 85.112379 \n", "L 77.763491 83.437631 \n", "L 78.126306 81.750702 \n", "L 78.489363 80.059889 \n", "L 78.85266 78.374056 \n", "L 79.216189 76.702557 \n", "L 79.579925 75.055158 \n", "L 79.943838 73.441901 \n", "L 80.307882 71.873 \n", "L 80.672001 70.358703 \n", "L 81.036129 68.909147 \n", "L 81.400189 67.534231 \n", "L 81.764093 66.24344 \n", "L 82.127748 65.045729 \n", "L 82.491049 63.949363 \n", "L 82.853887 62.961781 \n", "L 83.21615 62.089479 \n", "L 83.577721 61.337898 \n", "L 83.938485 60.711323 \n", "L 84.298325 60.212816 \n", "L 84.657131 59.844157 \n", "L 85.014793 59.605812 \n", "L 85.371214 59.496929 \n", "L 85.7263 59.515349 \n", "L 86.079972 59.657644 \n", "L 86.432158 59.919184 \n", "L 86.782803 60.294209 \n", "L 87.131862 60.775936 \n", "L 87.479306 61.356673 \n", "L 87.825118 62.027945 \n", "L 88.169298 62.780625 \n", "L 88.511857 63.6051 \n", "L 88.852822 64.491385 \n", "L 89.19223 65.429303 \n", "L 89.530131 66.408603 \n", "L 89.866586 67.419111 \n", "L 90.201663 68.450859 \n", "L 90.535439 69.494188 \n", "L 90.867997 70.539879 \n", "L 91.199424 71.579229 \n", "L 91.52981 72.604123 \n", "L 91.859249 73.607118 \n", "L 92.187831 74.581472 \n", "L 92.515648 75.521195 \n", "L 92.842789 76.421049 \n", "L 93.169337 77.276574 \n", "L 93.495376 78.084066 \n", "L 93.82098 78.840573 \n", "L 94.146218 79.543858 \n", "L 94.471154 80.192368 \n", "L 94.795846 80.785198 \n", "L 95.120341 81.322026 \n", "L 95.444683 81.803078 \n", "L 95.768908 82.229067 \n", "L 96.093044 82.601132 \n", "L 96.417113 82.920784 \n", "L 96.741133 83.189854 \n", "L 97.065113 83.410428 \n", "L 97.389058 83.5848 \n", "L 97.71297 83.71542 \n", "L 98.036842 83.80485 \n", "L 98.360668 83.855717 \n", "L 98.684438 83.870675 \n", "L 99.008136 83.852373 \n", "L 99.331747 83.803424 \n", "L 99.655254 83.726374 \n", "L 99.97864 83.623689 \n", "L 100.301882 83.497731 \n", "L 100.624962 83.350747 \n", "L 100.94786 83.184856 \n", "L 101.270555 83.002048 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 68.329911 115.897104 \n", "L 68.701562 115.440488 \n", "L 69.072616 114.968361 \n", "L 69.443075 114.47891 \n", "L 69.812943 113.970201 \n", "L 70.182226 113.440186 \n", "L 70.55093 112.886727 \n", "L 70.919063 112.307591 \n", "L 71.286637 111.700478 \n", "L 71.653667 111.063035 \n", "L 72.020166 110.392894 \n", "L 72.386154 109.687679 \n", "L 72.751653 108.945054 \n", "L 73.116685 108.162759 \n", "L 73.481279 107.338638 \n", "L 73.845466 106.470693 \n", "L 74.209277 105.557128 \n", "L 74.572748 104.59639 \n", "L 74.935918 103.587226 \n", "L 75.298825 102.528739 \n", "L 75.661513 101.420423 \n", "L 76.024026 100.262227 \n", "L 76.386405 99.054606 \n", "L 76.748695 97.798549 \n", "L 77.11094 96.495633 \n", "L 77.473181 95.148068 \n", "L 77.835456 93.758702 \n", "L 78.197802 92.331057 \n", "L 78.560251 90.869343 \n", "L 78.922829 89.378451 \n", "L 79.285556 87.863946 \n", "L 79.648446 86.332047 \n", "L 80.011505 84.789589 \n", "L 80.374728 83.243981 \n", "L 80.738105 81.703135 \n", "L 81.101612 80.175396 \n", "L 81.465218 78.669467 \n", "L 81.82888 77.194288 \n", "L 82.192546 75.758945 \n", "L 82.556151 74.372553 \n", "L 82.919625 73.04412 \n", "L 83.282886 71.782431 \n", "L 83.645844 70.595909 \n", "L 84.008403 69.492492 \n", "L 84.370461 68.479508 \n", "L 84.731912 67.563553 \n", "L 85.092647 66.750377 \n", "L 85.452557 66.044802 \n", "L 85.811532 65.450623 \n", "L 86.169466 64.970544 \n", "L 86.52626 64.606146 \n", "L 86.881815 64.357841 \n", "L 87.236045 64.224875 \n", "L 87.588871 64.205334 \n", "L 87.940227 64.29619 \n", "L 88.290056 64.493341 \n", "L 88.638314 64.791686 \n", "L 88.984971 65.185218 \n", "L 89.330012 65.667117 \n", "L 89.673432 66.229867 \n", "L 90.015241 66.865378 \n", "L 90.355464 67.565109 \n", "L 90.694135 68.320198 \n", "L 91.031302 69.121598 \n", "L 91.367022 69.960191 \n", "L 91.70136 70.826923 \n", "L 92.034391 71.712915 \n", "L 92.366196 72.609561 \n", "L 92.696861 73.508629 \n", "L 93.026474 74.402351 \n", "L 93.355127 75.283476 \n", "L 93.68291 76.145341 \n", "L 94.009916 76.981914 \n", "L 94.336233 77.787817 \n", "L 94.661947 78.558357 \n", "L 94.987139 79.289517 \n", "L 95.311888 79.977967 \n", "L 95.636265 80.621041 \n", "L 95.960334 81.216713 \n", "L 96.284154 81.763571 \n", "L 96.607779 82.260781 \n", "L 96.931252 82.708033 \n", "L 97.254612 83.105509 \n", "L 97.57789 83.453828 \n", "L 97.901109 83.753991 \n", "L 98.22429 84.007336 \n", "L 98.547444 84.215487 \n", "L 98.870577 84.3803 \n", "L 99.193691 84.503817 \n", "L 99.516785 84.588225 \n", "L 99.83985 84.63581 \n", "L 100.162877 84.648917 \n", "L 100.485854 84.629918 \n", "L 100.808762 84.58118 \n", "L 101.131587 84.505042 \n", "L 101.454308 84.40378 \n", "L 101.776908 84.279601 \n", "L 102.099363 84.134619 \n", "L 102.421654 83.970846 \n", "L 102.743763 83.790179 \n", "L 103.065665 83.594399 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 70.218917 116.581831 \n", "L 70.589886 116.138557 \n", "L 70.960287 115.681849 \n", "L 71.330123 115.21013 \n", "L 71.699401 114.721716 \n", "L 72.068128 114.214828 \n", "L 72.436312 113.687606 \n", "L 72.803965 113.138108 \n", "L 73.171099 112.564337 \n", "L 73.53773 111.964248 \n", "L 73.903873 111.335785 \n", "L 74.269548 110.676886 \n", "L 74.634778 109.985526 \n", "L 74.999585 109.259746 \n", "L 75.363997 108.497679 \n", "L 75.728042 107.697598 \n", "L 76.091751 106.857953 \n", "L 76.455158 105.97741 \n", "L 76.818297 105.054892 \n", "L 77.181204 104.089636 \n", "L 77.543915 103.081224 \n", "L 77.90647 102.02963 \n", "L 78.268904 100.935271 \n", "L 78.631255 99.799029 \n", "L 78.993559 98.622298 \n", "L 79.355847 97.407021 \n", "L 79.71815 96.155696 \n", "L 80.080496 94.8714 \n", "L 80.442906 93.557811 \n", "L 80.805396 92.219197 \n", "L 81.167977 90.860396 \n", "L 81.530652 89.48682 \n", "L 81.893418 88.104406 \n", "L 82.256263 86.719581 \n", "L 82.619164 85.339206 \n", "L 82.982093 83.970507 \n", "L 83.345011 82.621015 \n", "L 83.707869 81.298456 \n", "L 84.070611 80.01068 \n", "L 84.43317 78.765556 \n", "L 84.795473 77.570853 \n", "L 85.157439 76.434135 \n", "L 85.51898 75.362664 \n", "L 85.880003 74.363269 \n", "L 86.240411 73.442258 \n", "L 86.600104 72.605301 \n", "L 86.958982 71.857338 \n", "L 87.316944 71.202514 \n", "L 87.673892 70.644086 \n", "L 88.029731 70.184385 \n", "L 88.384374 69.824759 \n", "L 88.737734 69.56557 \n", "L 89.08974 69.406165 \n", "L 89.440325 69.344906 \n", "L 89.789437 69.379187 \n", "L 90.13703 69.505489 \n", "L 90.483075 69.719425 \n", "L 90.827553 70.015834 \n", "L 91.170458 70.388854 \n", "L 91.511796 70.832013 \n", "L 91.851586 71.338348 \n", "L 92.189859 71.900497 \n", "L 92.526657 72.510827 \n", "L 92.862031 73.161531 \n", "L 93.196042 73.844736 \n", "L 93.528757 74.552619 \n", "L 93.860254 75.277502 \n", "L 94.190611 76.011934 \n", "L 94.519915 76.748787 \n", "L 94.84825 77.481319 \n", "L 95.175704 78.20324 \n", "L 95.502368 78.90876 \n", "L 95.828324 79.592625 \n", "L 96.15366 80.250159 \n", "L 96.478455 80.877262 \n", "L 96.802785 81.470426 \n", "L 97.126721 82.026737 \n", "L 97.45033 82.543855 \n", "L 97.773671 83.019997 \n", "L 98.096795 83.453913 \n", "L 98.419752 83.844853 \n", "L 98.742579 84.19253 \n", "L 99.06531 84.497081 \n", "L 99.387973 84.759022 \n", "L 99.710585 84.97921 \n", "L 100.033163 85.158794 \n", "L 100.355716 85.299169 \n", "L 100.678246 85.401941 \n", "L 101.000754 85.468875 \n", "L 101.323234 85.501864 \n", "L 101.645678 85.502888 \n", "L 101.968073 85.473982 \n", "L 102.290408 85.417206 \n", "L 102.612663 85.334615 \n", "L 102.934822 85.228238 \n", "L 103.256865 85.100056 \n", "L 103.578774 84.951985 \n", "L 103.900527 84.785866 \n", "L 104.222104 84.603446 \n", "L 104.543487 84.406377 \n", "L 104.864652 84.196207 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 72.112485 117.271168 \n", "L 72.482726 116.841758 \n", "L 72.852421 116.401067 \n", "L 73.221575 115.947762 \n", "L 73.590196 115.480419 \n", "L 73.958292 114.997535 \n", "L 74.325873 114.497539 \n", "L 74.692951 113.978791 \n", "L 75.059539 113.439604 \n", "L 75.425653 112.878253 \n", "L 75.791309 112.293003 \n", "L 76.156527 111.682117 \n", "L 76.521328 111.043886 \n", "L 76.885733 110.376664 \n", "L 77.249769 109.67888 \n", "L 77.613463 108.949083 \n", "L 77.97684 108.185975 \n", "L 78.339932 107.388439 \n", "L 78.702771 106.555582 \n", "L 79.065385 105.686772 \n", "L 79.427808 104.781673 \n", "L 79.790073 103.840281 \n", "L 80.152207 102.862966 \n", "L 80.514243 101.850495 \n", "L 80.876208 100.804062 \n", "L 81.238128 99.725328 \n", "L 81.600024 98.616421 \n", "L 81.961916 97.479956 \n", "L 82.323818 96.319054 \n", "L 82.685736 95.137329 \n", "L 83.047676 93.938874 \n", "L 83.409631 92.728262 \n", "L 83.771593 91.5105 \n", "L 84.133542 90.291009 \n", "L 84.495451 89.075564 \n", "L 84.857289 87.870248 \n", "L 85.219011 86.681383 \n", "L 85.58057 85.515464 \n", "L 85.941906 84.379076 \n", "L 86.302955 83.278811 \n", "L 86.663645 82.221178 \n", "L 87.0239 81.212512 \n", "L 87.383637 80.258885 \n", "L 87.74277 79.366007 \n", "L 88.10121 78.539149 \n", "L 88.458868 77.783039 \n", "L 88.815652 77.10181 \n", "L 89.171475 76.498918 \n", "L 89.526249 75.977078 \n", "L 89.879894 75.538233 \n", "L 90.232333 75.183511 \n", "L 90.583497 74.913212 \n", "L 90.933323 74.726793 \n", "L 91.281761 74.622887 \n", "L 91.628767 74.599321 \n", "L 91.97431 74.653159 \n", "L 92.31837 74.780746 \n", "L 92.660936 74.977762 \n", "L 93.002012 75.23932 \n", "L 93.34161 75.56001 \n", "L 93.679755 75.934008 \n", "L 94.01648 76.355158 \n", "L 94.351831 76.817065 \n", "L 94.68586 77.313187 \n", "L 95.018628 77.836925 \n", "L 95.3502 78.381713 \n", "L 95.680651 78.941097 \n", "L 96.010056 79.508818 \n", "L 96.338496 80.078874 \n", "L 96.666051 80.645586 \n", "L 96.992803 81.203649 \n", "L 97.318834 81.748173 \n", "L 97.644222 82.27472 \n", "L 97.969045 82.779332 \n", "L 98.293375 83.258533 \n", "L 98.617281 83.709345 \n", "L 98.940828 84.129291 \n", "L 99.264074 84.516375 \n", "L 99.58707 84.86907 \n", "L 99.909864 85.186302 \n", "L 100.232498 85.467419 \n", "L 100.555003 85.712157 \n", "L 100.877409 85.920616 \n", "L 101.199738 86.093216 \n", "L 101.522005 86.230665 \n", "L 101.844223 86.33392 \n", "L 102.166397 86.404149 \n", "L 102.488529 86.442698 \n", "L 102.810615 86.45105 \n", "L 103.132651 86.430796 \n", "L 103.454626 86.383605 \n", "L 103.776528 86.31119 \n", "L 104.098344 86.215288 \n", "L 104.420055 86.097632 \n", "L 104.741647 85.959936 \n", "L 105.063099 85.803871 \n", "L 105.384394 85.631057 \n", "L 105.705512 85.443046 \n", "L 106.026433 85.241316 \n", "L 106.34714 85.027263 \n", "L 106.667613 84.802196 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 74.0106 117.960131 \n", "L 74.380063 117.544254 \n", "L 74.748996 117.119199 \n", "L 75.117404 116.683871 \n", "L 75.485296 116.237103 \n", "L 75.852683 115.777659 \n", "L 76.219571 115.30425 \n", "L 76.585975 114.81553 \n", "L 76.951907 114.310115 \n", "L 77.317383 113.786591 \n", "L 77.682418 113.243534 \n", "L 78.04703 112.679523 \n", "L 78.41124 112.093158 \n", "L 78.775066 111.483092 \n", "L 79.138533 110.848042 \n", "L 79.501665 110.186824 \n", "L 79.864484 109.498381 \n", "L 80.227018 108.781806 \n", "L 80.589294 108.036373 \n", "L 80.951336 107.261581 \n", "L 81.313173 106.457164 \n", "L 81.674831 105.623133 \n", "L 82.036332 104.759808 \n", "L 82.397702 103.867832 \n", "L 82.758964 102.948201 \n", "L 83.120133 102.002295 \n", "L 83.481227 101.031871 \n", "L 83.842258 100.039093 \n", "L 84.203233 99.026528 \n", "L 84.564154 97.997151 \n", "L 84.925019 96.954329 \n", "L 85.285819 95.90181 \n", "L 85.646539 94.843705 \n", "L 86.007158 93.784451 \n", "L 86.367646 92.728775 \n", "L 86.727971 91.681647 \n", "L 87.088088 90.648235 \n", "L 87.44795 89.633833 \n", "L 87.807502 88.64381 \n", "L 88.166684 87.683542 \n", "L 88.525429 86.758322 \n", "L 88.883667 85.873309 \n", "L 89.241325 85.033433 \n", "L 89.598325 84.243334 \n", "L 89.954589 83.507294 \n", "L 90.310039 82.829152 \n", "L 90.664596 82.212259 \n", "L 91.018185 81.659414 \n", "L 91.370731 81.172825 \n", "L 91.722167 80.754065 \n", "L 92.072431 80.404045 \n", "L 92.421463 80.123012 \n", "L 92.769217 79.910523 \n", "L 93.115651 79.765472 \n", "L 93.460733 79.686101 \n", "L 93.804441 79.670024 \n", "L 94.146763 79.714276 \n", "L 94.487696 79.815359 \n", "L 94.827248 79.9693 \n", "L 95.165436 80.171706 \n", "L 95.502287 80.417843 \n", "L 95.837836 80.702711 \n", "L 96.172127 81.0211 \n", "L 96.50521 81.367687 \n", "L 96.837143 81.737093 \n", "L 97.167987 82.123959 \n", "L 97.497811 82.523023 \n", "L 97.826683 82.929166 \n", "L 98.154677 83.337483 \n", "L 98.481865 83.743324 \n", "L 98.80832 84.142342 \n", "L 99.134116 84.530524 \n", "L 99.459322 84.904228 \n", "L 99.784007 85.260196 \n", "L 100.108234 85.595568 \n", "L 100.432063 85.90789 \n", "L 100.75555 86.195111 \n", "L 101.078747 86.455581 \n", "L 101.401697 86.688032 \n", "L 101.724441 86.891564 \n", "L 102.047014 87.065627 \n", "L 102.369442 87.209992 \n", "L 102.69175 87.324722 \n", "L 103.013957 87.410154 \n", "L 103.336074 87.466853 \n", "L 103.658109 87.495595 \n", "L 103.980068 87.497331 \n", "L 104.301949 87.473156 \n", "L 104.623749 87.424281 \n", "L 104.945462 87.352006 \n", "L 105.267076 87.257696 \n", "L 105.588582 87.142754 \n", "L 105.909965 87.008602 \n", "L 106.23121 86.856662 \n", "L 106.552301 86.688339 \n", "L 106.873221 86.505003 \n", "L 107.193955 86.307982 \n", "L 107.514481 86.098553 \n", "L 107.834785 85.877928 \n", "L 108.15485 85.647252 \n", "L 108.474658 85.4076 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 75.913282 118.644712 \n", "L 76.281918 118.241346 \n", "L 76.650034 117.830758 \n", "L 77.017635 117.412074 \n", "L 77.384731 116.984361 \n", "L 77.751331 116.546635 \n", "L 78.117442 116.097866 \n", "L 78.483079 115.636981 \n", "L 78.848252 115.162876 \n", "L 79.212977 114.674425 \n", "L 79.577266 114.170493 \n", "L 79.941136 113.649949 \n", "L 80.304607 113.111679 \n", "L 80.667694 112.554612 \n", "L 81.030418 111.977729 \n", "L 81.3928 111.380091 \n", "L 81.754861 110.760859 \n", "L 82.116621 110.119318 \n", "L 82.478106 109.454898 \n", "L 82.839334 108.767205 \n", "L 83.200327 108.05604 \n", "L 83.561109 107.32142 \n", "L 83.921696 106.563612 \n", "L 84.282108 105.783139 \n", "L 84.642361 104.98081 \n", "L 85.002467 104.157732 \n", "L 85.362437 103.315319 \n", "L 85.722279 102.455304 \n", "L 86.081995 101.579745 \n", "L 86.441583 100.691018 \n", "L 86.801039 99.791812 \n", "L 87.160349 98.885117 \n", "L 87.519497 97.974206 \n", "L 87.878463 97.062613 \n", "L 88.237216 96.154097 \n", "L 88.595724 95.252606 \n", "L 88.953947 94.36224 \n", "L 89.311841 93.487203 \n", "L 89.669357 92.63175 \n", "L 90.02644 91.800136 \n", "L 90.383032 90.996551 \n", "L 90.739072 90.225074 \n", "L 91.094494 89.489607 \n", "L 91.449235 88.793812 \n", "L 91.803226 88.14107 \n", "L 92.156402 87.53441 \n", "L 92.508696 86.976479 \n", "L 92.860046 86.469484 \n", "L 93.210392 86.015164 \n", "L 93.559678 85.61476 \n", "L 93.907853 85.268991 \n", "L 94.254872 84.97805 \n", "L 94.600698 84.741589 \n", "L 94.945299 84.558738 \n", "L 95.288653 84.428105 \n", "L 95.630745 84.347814 \n", "L 95.971568 84.315529 \n", "L 96.311125 84.328482 \n", "L 96.649427 84.383539 \n", "L 96.986493 84.477232 \n", "L 97.322348 84.605823 \n", "L 97.657027 84.765352 \n", "L 97.990571 84.9517 \n", "L 98.323025 85.160653 \n", "L 98.654442 85.387948 \n", "L 98.984877 85.629341 \n", "L 99.31439 85.880649 \n", "L 99.643042 86.137811 \n", "L 99.970896 86.396927 \n", "L 100.298017 86.654297 \n", "L 100.624467 86.90646 \n", "L 100.950309 87.150217 \n", "L 101.275603 87.382662 \n", "L 101.600408 87.601192 \n", "L 101.924777 87.80352 \n", "L 102.248762 87.987678 \n", "L 102.57241 88.152023 \n", "L 102.895765 88.295225 \n", "L 103.218861 88.416265 \n", "L 103.541734 88.514411 \n", "L 103.864412 88.589213 \n", "L 104.186917 88.640477 \n", "L 104.509268 88.668245 \n", "L 104.831481 88.672772 \n", "L 105.153564 88.654504 \n", "L 105.475523 88.61405 \n", "L 105.797362 88.55216 \n", "L 106.119078 88.469703 \n", "L 106.440667 88.367639 \n", "L 106.762124 88.247002 \n", "L 107.083439 88.108876 \n", "L 107.4046 87.954378 \n", "L 107.725598 87.784639 \n", "L 108.046416 87.600793 \n", "L 108.367041 87.403957 \n", "L 108.687459 87.195226 \n", "L 109.007655 86.975657 \n", "L 109.327611 86.746269 \n", "L 109.647315 86.508028 \n", "L 109.966752 86.261848 \n", "L 110.285907 86.008587 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 77.820572 119.32211 \n", "L 78.188337 118.929757 \n", "L 78.555588 118.531919 \n", "L 78.922328 118.127917 \n", "L 79.288567 117.717028 \n", "L 79.654314 117.298489 \n", "L 80.019577 116.871501 \n", "L 80.384365 116.435233 \n", "L 80.748691 115.98883 \n", "L 81.112568 115.531418 \n", "L 81.476007 115.062121 \n", "L 81.839023 114.580061 \n", "L 82.201631 114.084377 \n", "L 82.563846 113.574241 \n", "L 82.925686 113.048867 \n", "L 83.287167 112.507528 \n", "L 83.648307 111.949582 \n", "L 84.009124 111.374476 \n", "L 84.369636 110.781774 \n", "L 84.72986 110.17118 \n", "L 85.089814 109.542542 \n", "L 85.449514 108.895881 \n", "L 85.808973 108.231413 \n", "L 86.168207 107.549551 \n", "L 86.527228 106.850926 \n", "L 86.886043 106.136408 \n", "L 87.244659 105.407098 \n", "L 87.60308 104.664345 \n", "L 87.961306 103.909748 \n", "L 88.319333 103.145154 \n", "L 88.677154 102.372648 \n", "L 89.034756 101.594547 \n", "L 89.392122 100.813387 \n", "L 89.749233 100.031899 \n", "L 90.10606 99.25299 \n", "L 90.462576 98.47971 \n", "L 90.818744 97.715224 \n", "L 91.174526 96.962772 \n", "L 91.529879 96.225635 \n", "L 91.884756 95.50709 \n", "L 92.239109 94.810363 \n", "L 92.592884 94.13859 \n", "L 92.94603 93.494772 \n", "L 93.298491 92.881725 \n", "L 93.650212 92.302045 \n", "L 94.001139 91.758058 \n", "L 94.351219 91.251793 \n", "L 94.700401 90.784948 \n", "L 95.048637 90.358855 \n", "L 95.395883 89.974465 \n", "L 95.742101 89.63233 \n", "L 96.087255 89.332598 \n", "L 96.431316 89.075004 \n", "L 96.774262 88.858877 \n", "L 97.116078 88.683152 \n", "L 97.456754 88.546389 \n", "L 97.796288 88.446792 \n", "L 98.134686 88.38224 \n", "L 98.471959 88.350325 \n", "L 98.808126 88.348382 \n", "L 99.143211 88.373536 \n", "L 99.477246 88.422743 \n", "L 99.810266 88.492833 \n", "L 100.142312 88.580561 \n", "L 100.473429 88.682642 \n", "L 100.803664 88.795803 \n", "L 101.13307 88.916818 \n", "L 101.461696 89.042551 \n", "L 101.789598 89.169987 \n", "L 102.116828 89.296268 \n", "L 102.44344 89.418711 \n", "L 102.769486 89.534841 \n", "L 103.095016 89.642404 \n", "L 103.420078 89.739378 \n", "L 103.744717 89.823987 \n", "L 104.068974 89.8947 \n", "L 104.39289 89.950237 \n", "L 104.716498 89.98956 \n", "L 105.039829 90.011869 \n", "L 105.362909 90.01659 \n", "L 105.685762 90.003367 \n", "L 106.008405 89.972041 \n", "L 106.330854 89.922637 \n", "L 106.653121 89.855347 \n", "L 106.975211 89.77051 \n", "L 107.297129 89.668591 \n", "L 107.618877 89.550169 \n", "L 107.940453 89.415911 \n", "L 108.261852 89.266559 \n", "L 108.583071 89.10291 \n", "L 108.904098 88.925803 \n", "L 109.224925 88.736101 \n", "L 109.545544 88.534679 \n", "L 109.86594 88.322415 \n", "L 110.186102 88.100175 \n", "L 110.506019 87.868804 \n", "L 110.825677 87.629125 \n", "L 111.145062 87.381924 \n", "L 111.464164 87.127951 \n", "L 111.78297 86.867915 \n", "L 112.101467 86.602481 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 79.732531 119.990783 \n", "L 80.099388 119.607679 \n", "L 80.465733 119.220568 \n", "L 80.831568 118.828941 \n", "L 81.196902 118.432252 \n", "L 81.561743 118.029924 \n", "L 81.926098 117.621358 \n", "L 82.289975 117.205927 \n", "L 82.653386 116.782986 \n", "L 83.016341 116.351877 \n", "L 83.378849 115.911941 \n", "L 83.740923 115.462518 \n", "L 84.102577 115.002959 \n", "L 84.463822 114.532644 \n", "L 84.824672 114.050981 \n", "L 85.185143 113.557424 \n", "L 85.545247 113.051493 \n", "L 85.904998 112.532775 \n", "L 86.264413 112.000943 \n", "L 86.623503 111.455779 \n", "L 86.982281 110.897172 \n", "L 87.340763 110.325143 \n", "L 87.698956 109.739859 \n", "L 88.056871 109.141635 \n", "L 88.414519 108.530953 \n", "L 88.771902 107.90847 \n", "L 89.129026 107.275022 \n", "L 89.485893 106.631627 \n", "L 89.8425 105.979495 \n", "L 90.198842 105.320016 \n", "L 90.554912 104.654763 \n", "L 90.910697 103.985481 \n", "L 91.266184 103.314077 \n", "L 91.621353 102.642606 \n", "L 91.976182 101.973253 \n", "L 92.330644 101.308306 \n", "L 92.684711 100.650142 \n", "L 93.03835 100.001192 \n", "L 93.391525 99.363912 \n", "L 93.744198 98.740759 \n", "L 94.096329 98.134149 \n", "L 94.447875 97.546429 \n", "L 94.798794 96.979842 \n", "L 95.149043 96.436495 \n", "L 95.498576 95.918326 \n", "L 95.847351 95.427077 \n", "L 96.195327 94.964261 \n", "L 96.542465 94.531146 \n", "L 96.888726 94.128727 \n", "L 97.234077 93.757713 \n", "L 97.57849 93.418517 \n", "L 97.921937 93.11125 \n", "L 98.264398 92.835711 \n", "L 98.605857 92.591402 \n", "L 98.946304 92.377526 \n", "L 99.285734 92.193009 \n", "L 99.624148 92.036508 \n", "L 99.961551 91.906439 \n", "L 100.297956 91.801001 \n", "L 100.63338 91.718202 \n", "L 100.967844 91.655893 \n", "L 101.301376 91.611794 \n", "L 101.634005 91.583534 \n", "L 101.965767 91.568681 \n", "L 102.296698 91.564772 \n", "L 102.626838 91.569355 \n", "L 102.956229 91.580008 \n", "L 103.284914 91.594377 \n", "L 103.612937 91.610197 \n", "L 103.94034 91.625318 \n", "L 104.267168 91.637724 \n", "L 104.593462 91.64555 \n", "L 104.919263 91.647097 \n", "L 105.244609 91.640841 \n", "L 105.569537 91.625441 \n", "L 105.89408 91.599741 \n", "L 106.218268 91.562771 \n", "L 106.54213 91.513745 \n", "L 106.865689 91.452056 \n", "L 107.188966 91.377266 \n", "L 107.51198 91.289101 \n", "L 107.834744 91.187436 \n", "L 108.157269 91.072287 \n", "L 108.479567 90.943792 \n", "L 108.801639 90.802202 \n", "L 109.12349 90.647867 \n", "L 109.445121 90.481216 \n", "L 109.766529 90.302751 \n", "L 110.087711 90.113027 \n", "L 110.408662 89.912643 \n", "L 110.729375 89.702229 \n", "L 111.049843 89.482434 \n", "L 111.370057 89.253915 \n", "L 111.690006 89.017333 \n", "L 112.009682 88.77334 \n", "L 112.329075 88.522573 \n", "L 112.648175 88.265648 \n", "L 112.966969 88.00316 \n", "L 113.285449 87.735674 \n", "L 113.603607 87.463721 \n", "L 113.92143 87.187805 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 81.649223 120.650313 \n", "L 82.015144 120.274622 \n", "L 82.380552 119.896137 \n", "L 82.745449 119.514485 \n", "L 83.109842 119.129265 \n", "L 83.473738 118.740056 \n", "L 83.837142 118.346418 \n", "L 84.200063 117.947892 \n", "L 84.562509 117.544006 \n", "L 84.92449 117.134278 \n", "L 85.286012 116.718227 \n", "L 85.647086 116.295369 \n", "L 86.007724 115.86523 \n", "L 86.367934 115.427356 \n", "L 86.727729 114.981315 \n", "L 87.087119 114.526709 \n", "L 87.446115 114.063188 \n", "L 87.804728 113.590451 \n", "L 88.16297 113.10826 \n", "L 88.520851 112.616457 \n", "L 88.878381 112.114966 \n", "L 89.23557 111.603802 \n", "L 89.592425 111.08309 \n", "L 89.948953 110.553065 \n", "L 90.305162 110.01408 \n", "L 90.661054 109.466622 \n", "L 91.016631 108.911302 \n", "L 91.371894 108.348869 \n", "L 91.72684 107.780209 \n", "L 92.081463 107.206342 \n", "L 92.435759 106.628419 \n", "L 92.789715 106.047717 \n", "L 93.143319 105.465631 \n", "L 93.496556 104.883663 \n", "L 93.849406 104.303408 \n", "L 94.201849 103.726537 \n", "L 94.553861 103.154786 \n", "L 94.905416 102.589925 \n", "L 95.256485 102.033747 \n", "L 95.607039 101.488041 \n", "L 95.957044 100.954568 \n", "L 96.30647 100.435035 \n", "L 96.655281 99.931076 \n", "L 97.003445 99.444222 \n", "L 97.350927 98.975885 \n", "L 97.697695 98.527329 \n", "L 98.043718 98.099656 \n", "L 98.388965 97.693786 \n", "L 98.733408 97.310443 \n", "L 99.077023 96.950141 \n", "L 99.419789 96.61318 \n", "L 99.761685 96.29964 \n", "L 100.102698 96.009377 \n", "L 100.442817 95.742027 \n", "L 100.782036 95.497011 \n", "L 101.120352 95.273547 \n", "L 101.457768 95.07066 \n", "L 101.794289 94.887198 \n", "L 102.129928 94.721851 \n", "L 102.464699 94.573169 \n", "L 102.79862 94.439588 \n", "L 103.131713 94.319448 \n", "L 103.464004 94.211022 \n", "L 103.79552 94.112537 \n", "L 104.126292 94.0222 \n", "L 104.456351 93.938219 \n", "L 104.78573 93.85883 \n", "L 105.114464 93.782313 \n", "L 105.442588 93.707013 \n", "L 105.770135 93.631359 \n", "L 106.097139 93.553877 \n", "L 106.423635 93.473201 \n", "L 106.749652 93.388089 \n", "L 107.075222 93.297423 \n", "L 107.400372 93.20022 \n", "L 107.725128 93.095631 \n", "L 108.049515 92.982943 \n", "L 108.373554 92.861579 \n", "L 108.697262 92.73109 \n", "L 109.020656 92.591153 \n", "L 109.343751 92.441564 \n", "L 109.666556 92.282229 \n", "L 109.98908 92.113156 \n", "L 110.311331 91.934443 \n", "L 110.633311 91.746273 \n", "L 110.955022 91.548897 \n", "L 111.276466 91.342629 \n", "L 111.59764 91.127833 \n", "L 111.918541 90.904916 \n", "L 112.239166 90.674314 \n", "L 112.559509 90.436486 \n", "L 112.879563 90.191907 \n", "L 113.199323 89.941058 \n", "L 113.51878 89.684421 \n", "L 113.837927 89.422472 \n", "L 114.156756 89.155678 \n", "L 114.475261 88.88449 \n", "L 114.793431 88.609343 \n", "L 115.11126 88.33065 \n", "L 115.428741 88.048799 \n", "L 115.745864 87.76416 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 83.570713 121.301182 \n", "L 83.935679 120.93115 \n", "L 84.300128 120.559281 \n", "L 84.664063 120.18531 \n", "L 85.027489 119.808952 \n", "L 85.390413 119.429906 \n", "L 85.75284 119.047859 \n", "L 86.114775 118.662484 \n", "L 86.476225 118.273447 \n", "L 86.837199 117.880402 \n", "L 87.197702 117.483009 \n", "L 87.557743 117.080922 \n", "L 87.91733 116.673805 \n", "L 88.27647 116.261336 \n", "L 88.635173 115.843207 \n", "L 88.993448 115.419135 \n", "L 89.351302 114.988873 \n", "L 89.708744 114.552206 \n", "L 90.065784 114.108967 \n", "L 90.422428 113.659045 \n", "L 90.778684 113.202384 \n", "L 91.13456 112.738999 \n", "L 91.49006 112.268981 \n", "L 91.84519 111.792495 \n", "L 92.199956 111.309796 \n", "L 92.554357 110.82123 \n", "L 92.908395 110.327235 \n", "L 93.262072 109.828343 \n", "L 93.615384 109.325186 \n", "L 93.968325 108.818489 \n", "L 94.320892 108.309072 \n", "L 94.673075 107.797842 \n", "L 95.024864 107.28579 \n", "L 95.376246 106.773984 \n", "L 95.727207 106.263556 \n", "L 96.077731 105.755693 \n", "L 96.427798 105.251625 \n", "L 96.77739 104.752609 \n", "L 97.126483 104.259915 \n", "L 97.475056 103.774811 \n", "L 97.823084 103.298545 \n", "L 98.170542 102.832326 \n", "L 98.517405 102.37731 \n", "L 98.863648 101.934582 \n", "L 99.209246 101.50514 \n", "L 99.554175 101.089878 \n", "L 99.898411 100.689577 \n", "L 100.241933 100.304886 \n", "L 100.584721 99.936317 \n", "L 100.926756 99.584234 \n", "L 101.268025 99.248848 \n", "L 101.608512 98.930217 \n", "L 101.948209 98.628237 \n", "L 102.287108 98.342651 \n", "L 102.625206 98.073049 \n", "L 102.962504 97.818878 \n", "L 103.299002 97.579449 \n", "L 103.634708 97.353945 \n", "L 103.969632 97.141438 \n", "L 104.303784 96.940903 \n", "L 104.637181 96.751232 \n", "L 104.96984 96.571248 \n", "L 105.301781 96.399729 \n", "L 105.633026 96.235416 \n", "L 105.963598 96.077038 \n", "L 106.293521 95.923327 \n", "L 106.622823 95.773026 \n", "L 106.951529 95.624918 \n", "L 107.279665 95.477825 \n", "L 107.607259 95.330633 \n", "L 107.934335 95.182292 \n", "L 108.260919 95.031832 \n", "L 108.587035 94.878369 \n", "L 108.912706 94.721106 \n", "L 109.237953 94.559342 \n", "L 109.562796 94.392474 \n", "L 109.887252 94.219993 \n", "L 110.211339 94.041484 \n", "L 110.535069 93.856628 \n", "L 110.858455 93.665194 \n", "L 111.181508 93.467032 \n", "L 111.504234 93.262076 \n", "L 111.826641 93.050328 \n", "L 112.148734 92.831857 \n", "L 112.470515 92.606792 \n", "L 112.791984 92.37531 \n", "L 113.113145 92.137631 \n", "L 113.433993 91.894017 \n", "L 113.754528 91.644753 \n", "L 114.074747 91.390148 \n", "L 114.394644 91.13053 \n", "L 114.714216 90.866235 \n", "L 115.033458 90.597604 \n", "L 115.352363 90.324981 \n", "L 115.670927 90.048703 \n", "L 115.989143 89.769102 \n", "L 116.307007 89.486499 \n", "L 116.624511 89.201202 \n", "L 116.941651 88.913505 \n", "L 117.258421 88.623685 \n", "L 117.574815 88.332004 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 85.497062 121.944479 \n", "L 85.861057 121.578538 \n", "L 86.224533 121.211489 \n", "L 86.58749 120.84315 \n", "L 86.949935 120.473323 \n", "L 87.311872 120.101798 \n", "L 87.673303 119.728361 \n", "L 88.034236 119.352785 \n", "L 88.394675 118.974837 \n", "L 88.754626 118.59428 \n", "L 89.114095 118.210876 \n", "L 89.473086 117.824389 \n", "L 89.831609 117.434583 \n", "L 90.189667 117.041239 \n", "L 90.547268 116.644141 \n", "L 90.904419 116.243095 \n", "L 91.261126 115.83793 \n", "L 91.617395 115.428499 \n", "L 91.973233 115.014684 \n", "L 92.328646 114.596411 \n", "L 92.683639 114.173642 \n", "L 93.038218 113.746386 \n", "L 93.392386 113.314706 \n", "L 93.746148 112.87872 \n", "L 94.099506 112.438601 \n", "L 94.452462 111.994592 \n", "L 94.805016 111.546993 \n", "L 95.157168 111.096174 \n", "L 95.508917 110.642571 \n", "L 95.860257 110.186687 \n", "L 96.211186 109.729087 \n", "L 96.561696 109.270398 \n", "L 96.91178 108.811305 \n", "L 97.261428 108.352544 \n", "L 97.610629 107.894898 \n", "L 97.959371 107.439184 \n", "L 98.30764 106.986253 \n", "L 98.655423 106.536969 \n", "L 99.002701 106.092208 \n", "L 99.34946 105.652845 \n", "L 99.69568 105.219739 \n", "L 100.041344 104.793723 \n", "L 100.386435 104.375594 \n", "L 100.730933 103.966096 \n", "L 101.074821 103.56592 \n", "L 101.418082 103.175679 \n", "L 101.760699 102.795909 \n", "L 102.102657 102.427059 \n", "L 102.443942 102.06948 \n", "L 102.784542 101.723422 \n", "L 103.124446 101.38903 \n", "L 103.463644 101.066344 \n", "L 103.802132 100.755292 \n", "L 104.139905 100.455696 \n", "L 104.476961 100.167275 \n", "L 104.813301 99.889648 \n", "L 105.148927 99.622338 \n", "L 105.483846 99.364784 \n", "L 105.818065 99.116349 \n", "L 106.151594 98.876323 \n", "L 106.484444 98.643943 \n", "L 106.816629 98.4184 \n", "L 107.148165 98.19885 \n", "L 107.479068 97.984426 \n", "L 107.809356 97.77425 \n", "L 108.139047 97.567447 \n", "L 108.468162 97.363152 \n", "L 108.796719 97.160522 \n", "L 109.124739 96.958746 \n", "L 109.452242 96.757052 \n", "L 109.779245 96.554716 \n", "L 110.105768 96.351067 \n", "L 110.431829 96.145493 \n", "L 110.757445 95.937444 \n", "L 111.082632 95.726434 \n", "L 111.407403 95.512045 \n", "L 111.731773 95.293924 \n", "L 112.055753 95.071783 \n", "L 112.379352 94.845401 \n", "L 112.702581 94.614617 \n", "L 113.025447 94.379325 \n", "L 113.347955 94.139481 \n", "L 113.67011 93.895083 \n", "L 113.991917 93.646178 \n", "L 114.313376 93.392854 \n", "L 114.63449 93.135233 \n", "L 114.955258 92.873466 \n", "L 115.275679 92.607734 \n", "L 115.595752 92.338232 \n", "L 115.915475 92.065174 \n", "L 116.234844 91.788786 \n", "L 116.553857 91.5093 \n", "L 116.872511 91.22695 \n", "L 117.190801 90.941975 \n", "L 117.508722 90.654607 \n", "L 117.826272 90.365076 \n", "L 118.143447 90.073601 \n", "L 118.460241 89.780397 \n", "L 118.776651 89.485666 \n", "L 119.092674 89.1896 \n", "L 119.408306 88.892378 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 87.428319 122.581628 \n", "L 87.791335 122.218453 \n", "L 88.153828 121.854706 \n", "L 88.515799 121.490267 \n", "L 88.877253 121.125001 \n", "L 89.238194 120.758768 \n", "L 89.598623 120.391422 \n", "L 89.958546 120.022811 \n", "L 90.317967 119.652779 \n", "L 90.676891 119.281164 \n", "L 91.035321 118.90781 \n", "L 91.393263 118.532554 \n", "L 91.750721 118.15524 \n", "L 92.1077 117.775719 \n", "L 92.464205 117.393847 \n", "L 92.820242 117.009492 \n", "L 93.175814 116.62254 \n", "L 93.530927 116.232892 \n", "L 93.885587 115.840469 \n", "L 94.239795 115.445222 \n", "L 94.593558 115.047123 \n", "L 94.94688 114.64618 \n", "L 95.299763 114.242436 \n", "L 95.652209 113.835968 \n", "L 96.004222 113.426894 \n", "L 96.355801 113.015377 \n", "L 96.706947 112.601618 \n", "L 97.057662 112.185866 \n", "L 97.407942 111.768414 \n", "L 97.757785 111.349599 \n", "L 98.107188 110.929801 \n", "L 98.456146 110.50944 \n", "L 98.804654 110.088976 \n", "L 99.152705 109.668902 \n", "L 99.500291 109.249745 \n", "L 99.847404 108.832051 \n", "L 100.194034 108.416391 \n", "L 100.54017 108.003344 \n", "L 100.885801 107.593498 \n", "L 101.230915 107.187438 \n", "L 101.575501 106.78574 \n", "L 101.919545 106.388962 \n", "L 102.263034 105.997637 \n", "L 102.605958 105.612264 \n", "L 102.948302 105.233305 \n", "L 103.290055 104.86117 \n", "L 103.631206 104.496218 \n", "L 103.971745 104.138749 \n", "L 104.311662 103.788998 \n", "L 104.650949 103.447132 \n", "L 104.9896 103.113248 \n", "L 105.327608 102.787371 \n", "L 105.66497 102.469455 \n", "L 106.001684 102.159378 \n", "L 106.337749 101.856953 \n", "L 106.673167 101.561924 \n", "L 107.007939 101.273972 \n", "L 107.342071 100.99272 \n", "L 107.675569 100.717741 \n", "L 108.008441 100.44856 \n", "L 108.340695 100.184665 \n", "L 108.672341 99.925513 \n", "L 109.003392 99.67054 \n", "L 109.33386 99.419161 \n", "L 109.663757 99.170791 \n", "L 109.993098 98.92484 \n", "L 110.321896 98.680729 \n", "L 110.650166 98.437892 \n", "L 110.977923 98.195784 \n", "L 111.30518 97.953887 \n", "L 111.631952 97.711714 \n", "L 111.958252 97.468815 \n", "L 112.284092 97.224778 \n", "L 112.609487 96.979232 \n", "L 112.934445 96.731852 \n", "L 113.258979 96.482356 \n", "L 113.583097 96.230506 \n", "L 113.906808 95.976107 \n", "L 114.23012 95.719012 \n", "L 114.553038 95.45911 \n", "L 114.87557 95.19633 \n", "L 115.197717 94.930641 \n", "L 115.519485 94.662043 \n", "L 115.840876 94.390564 \n", "L 116.161892 94.116265 \n", "L 116.482532 93.839226 \n", "L 116.802799 93.559548 \n", "L 117.122691 93.277351 \n", "L 117.442207 92.992768 \n", "L 117.761347 92.705941 \n", "L 118.080107 92.417021 \n", "L 118.398486 92.126163 \n", "L 118.716483 91.833525 \n", "L 119.034093 91.539267 \n", "L 119.351315 91.243544 \n", "L 119.668145 90.94651 \n", "L 119.984583 90.648312 \n", "L 120.300624 90.349095 \n", "L 120.616266 90.048993 \n", "L 120.931507 89.748135 \n", "L 121.246344 89.446642 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 89.364526 123.214151 \n", "L 89.726556 122.852674 \n", "L 90.088061 122.49101 \n", "L 90.449041 122.129078 \n", "L 90.809499 121.76679 \n", "L 91.169441 121.404053 \n", "L 91.528865 121.040774 \n", "L 91.887778 120.676851 \n", "L 92.246181 120.312183 \n", "L 92.60408 119.946664 \n", "L 92.961475 119.580191 \n", "L 93.318372 119.212658 \n", "L 93.674776 118.843962 \n", "L 94.030687 118.474006 \n", "L 94.38611 118.102696 \n", "L 94.741052 117.729944 \n", "L 95.095512 117.355678 \n", "L 95.449497 116.979831 \n", "L 95.80301 116.602351 \n", "L 96.156053 116.223207 \n", "L 96.508629 115.842381 \n", "L 96.860744 115.459876 \n", "L 97.212396 115.075721 \n", "L 97.56359 114.689966 \n", "L 97.914327 114.302688 \n", "L 98.264607 113.913993 \n", "L 98.614432 113.524012 \n", "L 98.963801 113.132908 \n", "L 99.312713 112.74087 \n", "L 99.661166 112.348121 \n", "L 100.00916 111.954904 \n", "L 100.356689 111.561497 \n", "L 100.703752 111.168198 \n", "L 101.050342 110.775329 \n", "L 101.396456 110.383232 \n", "L 101.742087 109.992264 \n", "L 102.087228 109.602796 \n", "L 102.431873 109.215206 \n", "L 102.776014 108.829876 \n", "L 103.119643 108.447188 \n", "L 103.462752 108.067517 \n", "L 103.805333 107.691225 \n", "L 104.147377 107.318659 \n", "L 104.488877 106.950146 \n", "L 104.829823 106.585983 \n", "L 105.170208 106.22644 \n", "L 105.510025 105.871749 \n", "L 105.849267 105.522105 \n", "L 106.187929 105.17766 \n", "L 106.526004 104.838524 \n", "L 106.863491 104.504758 \n", "L 107.200383 104.176379 \n", "L 107.53668 103.853357 \n", "L 107.872381 103.535612 \n", "L 108.207485 103.223022 \n", "L 108.541995 102.915418 \n", "L 108.875913 102.612595 \n", "L 109.209242 102.314305 \n", "L 109.541988 102.02027 \n", "L 109.874155 101.73018 \n", "L 110.205751 101.443702 \n", "L 110.536784 101.160482 \n", "L 110.867261 100.88015 \n", "L 111.197192 100.602326 \n", "L 111.526586 100.326628 \n", "L 111.855453 100.052673 \n", "L 112.183803 99.780079 \n", "L 112.511647 99.50848 \n", "L 112.838994 99.237518 \n", "L 113.165856 98.966857 \n", "L 113.492241 98.696177 \n", "L 113.818159 98.425185 \n", "L 114.14362 98.153611 \n", "L 114.468632 97.881214 \n", "L 114.793203 97.607779 \n", "L 115.117341 97.333124 \n", "L 115.441051 97.057092 \n", "L 115.764343 96.779554 \n", "L 116.087218 96.500416 \n", "L 116.409683 96.219603 \n", "L 116.731742 95.937069 \n", "L 117.053396 95.652794 \n", "L 117.37465 95.366775 \n", "L 117.695506 95.079032 \n", "L 118.015964 94.789604 \n", "L 118.336025 94.498543 \n", "L 118.655691 94.205913 \n", "L 118.974959 93.911794 \n", "L 119.293831 93.616271 \n", "L 119.612307 93.319435 \n", "L 119.930383 93.021387 \n", "L 120.24806 92.722227 \n", "L 120.565336 92.422057 \n", "L 120.88221 92.12098 \n", "L 121.198679 91.8191 \n", "L 121.514743 91.516514 \n", "L 121.8304 91.21332 \n", "L 122.145649 90.909611 \n", "L 122.460487 90.605474 \n", "L 122.774914 90.300994 \n", "L 123.088928 89.996249 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 91.305715 123.843491 \n", "L 91.666756 123.482894 \n", "L 92.027269 123.122373 \n", "L 92.387255 122.761877 \n", "L 92.746716 122.40135 \n", "L 93.105657 122.040731 \n", "L 93.464077 121.679963 \n", "L 93.82198 121.318979 \n", "L 94.179369 120.957715 \n", "L 94.536247 120.596103 \n", "L 94.892615 120.234078 \n", "L 95.248477 119.871572 \n", "L 95.603837 119.50852 \n", "L 95.958696 119.144859 \n", "L 96.313057 118.780529 \n", "L 96.666925 118.415475 \n", "L 97.020299 118.049649 \n", "L 97.373185 117.683011 \n", "L 97.725586 117.315525 \n", "L 98.077502 116.947172 \n", "L 98.428937 116.57794 \n", "L 98.779894 116.207831 \n", "L 99.130373 115.836864 \n", "L 99.480376 115.465069 \n", "L 99.829907 115.092494 \n", "L 100.178963 114.719207 \n", "L 100.527546 114.345291 \n", "L 100.875657 113.970848 \n", "L 101.223296 113.595998 \n", "L 101.57046 113.220882 \n", "L 101.917151 112.845654 \n", "L 102.263364 112.47049 \n", "L 102.609098 112.095578 \n", "L 102.954351 111.721122 \n", "L 103.299118 111.347338 \n", "L 103.643398 110.974453 \n", "L 103.987184 110.602701 \n", "L 104.330474 110.232321 \n", "L 104.673261 109.863555 \n", "L 105.015542 109.496645 \n", "L 105.357311 109.131827 \n", "L 105.698563 108.769331 \n", "L 106.039292 108.409377 \n", "L 106.379494 108.052169 \n", "L 106.719162 107.697898 \n", "L 107.058294 107.346733 \n", "L 107.396884 106.99882 \n", "L 107.734927 106.654283 \n", "L 108.072422 106.313218 \n", "L 108.409364 105.975694 \n", "L 108.745752 105.641748 \n", "L 109.081583 105.311393 \n", "L 109.416857 104.984608 \n", "L 109.751573 104.661342 \n", "L 110.085733 104.341517 \n", "L 110.419337 104.025027 \n", "L 110.752388 103.71174 \n", "L 111.084889 103.4015 \n", "L 111.416844 103.09413 \n", "L 111.748256 102.789433 \n", "L 112.07913 102.487199 \n", "L 112.409473 102.187202 \n", "L 112.73929 101.889207 \n", "L 113.068588 101.592975 \n", "L 113.397374 101.298261 \n", "L 113.725653 101.004823 \n", "L 114.053434 100.71242 \n", "L 114.380724 100.420818 \n", "L 114.70753 100.129789 \n", "L 115.033859 99.839121 \n", "L 115.359718 99.548611 \n", "L 115.685113 99.258072 \n", "L 116.010052 98.967333 \n", "L 116.33454 98.676239 \n", "L 116.658582 98.384656 \n", "L 116.982185 98.092466 \n", "L 117.305352 97.79957 \n", "L 117.628089 97.505886 \n", "L 117.950399 97.211353 \n", "L 118.272284 96.915925 \n", "L 118.59375 96.619569 \n", "L 118.914797 96.322275 \n", "L 119.235427 96.024039 \n", "L 119.555644 95.724874 \n", "L 119.875446 95.424804 \n", "L 120.194835 95.123861 \n", "L 120.513813 94.822087 \n", "L 120.832378 94.519531 \n", "L 121.150531 94.216246 \n", "L 121.468273 93.91229 \n", "L 121.785601 93.607728 \n", "L 122.102516 93.302621 \n", "L 122.419018 92.997033 \n", "L 122.735105 92.691032 \n", "L 123.050776 92.384681 \n", "L 123.366032 92.078042 \n", "L 123.680872 91.771176 \n", "L 123.995293 91.464143 \n", "L 124.309297 91.156998 \n", "L 124.622882 90.849792 \n", "L 124.936047 90.542577 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 91.953889 124.052788 \n", "L 92.3146 123.692341 \n", "L 92.674782 123.332036 \n", "L 93.034435 122.971831 \n", "L 93.393564 122.611677 \n", "L 93.752171 122.251523 \n", "L 94.110257 121.891318 \n", "L 94.467824 121.531007 \n", "L 94.824876 121.170534 \n", "L 95.181415 120.809841 \n", "L 95.537442 120.448872 \n", "L 95.892962 120.087569 \n", "L 96.247977 119.725876 \n", "L 96.602488 119.36374 \n", "L 96.956498 119.001109 \n", "L 97.310013 118.637936 \n", "L 97.663031 118.274181 \n", "L 98.015557 117.909807 \n", "L 98.367595 117.544785 \n", "L 98.719144 117.179098 \n", "L 99.070208 116.812737 \n", "L 99.420789 116.445701 \n", "L 99.770888 116.078008 \n", "L 100.120507 115.709683 \n", "L 100.469649 115.340767 \n", "L 100.818312 114.971317 \n", "L 101.166498 114.601405 \n", "L 101.514207 114.231118 \n", "L 101.86144 113.860557 \n", "L 102.208195 113.489842 \n", "L 102.554471 113.119107 \n", "L 102.900268 112.748499 \n", "L 103.245582 112.37818 \n", "L 103.590413 112.008323 \n", "L 103.934756 111.639114 \n", "L 104.27861 111.270744 \n", "L 104.621971 110.903415 \n", "L 104.964834 110.537329 \n", "L 105.307197 110.172694 \n", "L 105.649054 109.809715 \n", "L 105.990402 109.448594 \n", "L 106.331235 109.089528 \n", "L 106.67155 108.732702 \n", "L 107.011343 108.378293 \n", "L 107.350609 108.026461 \n", "L 107.689343 107.677351 \n", "L 108.027543 107.331089 \n", "L 108.365205 106.98778 \n", "L 108.702326 106.647505 \n", "L 109.038904 106.310322 \n", "L 109.374937 105.976265 \n", "L 109.710424 105.645342 \n", "L 110.045363 105.317537 \n", "L 110.379755 104.992805 \n", "L 110.713602 104.671081 \n", "L 111.046903 104.352273 \n", "L 111.379662 104.036268 \n", "L 111.71188 103.722934 \n", "L 112.043562 103.412119 \n", "L 112.374711 103.103655 \n", "L 112.705332 102.797362 \n", "L 113.035429 102.493047 \n", "L 113.365009 102.190511 \n", "L 113.694076 101.889547 \n", "L 114.022638 101.589948 \n", "L 114.350698 101.291507 \n", "L 114.678266 100.994017 \n", "L 115.005346 100.697279 \n", "L 115.331945 100.401099 \n", "L 115.65807 100.105295 \n", "L 115.983726 99.809693 \n", "L 116.30892 99.514133 \n", "L 116.633656 99.218471 \n", "L 116.957942 98.922573 \n", "L 117.281781 98.626324 \n", "L 117.605179 98.329625 \n", "L 117.928138 98.032389 \n", "L 118.250665 97.734547 \n", "L 118.572761 97.436047 \n", "L 118.89443 97.136849 \n", "L 119.215675 96.836926 \n", "L 119.536497 96.536267 \n", "L 119.856898 96.234871 \n", "L 120.176882 95.932746 \n", "L 120.496446 95.629915 \n", "L 120.815594 95.326404 \n", "L 121.134326 95.022248 \n", "L 121.452641 94.71749 \n", "L 121.77054 94.412175 \n", "L 122.088024 94.106353 \n", "L 122.40509 93.800076 \n", "L 122.72174 93.4934 \n", "L 123.037974 93.186377 \n", "L 123.353789 92.879066 \n", "L 123.669186 92.57152 \n", "L 123.984164 92.263793 \n", "L 124.298724 91.955936 \n", "L 124.612863 91.647999 \n", "L 124.926583 91.340031 \n", "L 125.239882 91.032074 \n", "L 125.55276 90.724173 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 29.77277 104.231186 \n", "L 30.369155 104.418727 \n", "L 30.96602 104.606044 \n", "L 31.563363 104.793092 \n", "L 32.161184 104.979826 \n", "L 32.759484 105.166197 \n", "L 33.35826 105.352154 \n", "L 33.957513 105.537643 \n", "L 34.557242 105.72261 \n", "L 35.157449 105.906999 \n", "L 35.75813 106.090752 \n", "L 36.359287 106.273814 \n", "L 36.960921 106.45613 \n", "L 37.563029 106.637645 \n", "L 38.165613 106.818308 \n", "L 38.768676 106.998073 \n", "L 39.372215 107.176898 \n", "L 39.976233 107.354745 \n", "L 40.580733 107.531589 \n", "L 41.185712 107.707406 \n", "L 41.791176 107.882188 \n", "L 42.397128 108.055936 \n", "L 43.003566 108.228661 \n", "L 43.610497 108.40039 \n", "L 44.217925 108.571163 \n", "L 44.825851 108.741034 \n", "L 45.434279 108.910073 \n", "L 46.043216 109.078366 \n", "L 46.652665 109.246014 \n", "L 47.26263 109.413135 \n", "L 47.873117 109.579862 \n", "L 48.484129 109.746342 \n", "L 49.095674 109.912736 \n", "L 49.707756 110.079219 \n", "L 50.320378 110.245976 \n", "L 50.933547 110.413202 \n", "L 51.547266 110.581097 \n", "L 52.161541 110.749869 \n", "L 52.776375 110.919728 \n", "L 53.391771 111.090881 \n", "L 54.007732 111.263536 \n", "L 54.624261 111.437893 \n", "L 55.241359 111.614145 \n", "L 55.85903 111.792474 \n", "L 56.477272 111.973046 \n", "L 57.096086 112.156012 \n", "L 57.715472 112.341505 \n", "L 58.335429 112.529637 \n", "L 58.955955 112.720497 \n", "L 59.577048 112.91415 \n", "L 60.198707 113.110639 \n", "L 60.820926 113.309976 \n", "L 61.443703 113.512153 \n", "L 62.067034 113.717132 \n", "L 62.690915 113.924853 \n", "L 63.315342 114.135231 \n", "L 63.940311 114.348158 \n", "L 64.565817 114.563504 \n", "L 65.191856 114.781123 \n", "L 65.818424 115.00085 \n", "L 66.445517 115.222506 \n", "L 67.073131 115.445901 \n", "L 67.701264 115.670835 \n", "L 68.329911 115.897104 \n", "L 68.959071 116.124498 \n", "L 69.588739 116.352809 \n", "L 70.218917 116.581831 \n", "L 70.8496 116.811359 \n", "L 71.48079 117.041201 \n", "L 72.112485 117.271168 \n", "L 72.744684 117.501085 \n", "L 73.37739 117.73079 \n", "L 74.0106 117.960131 \n", "L 74.644319 118.188977 \n", "L 75.278546 118.417205 \n", "L 75.913282 118.644712 \n", "L 76.54853 118.871411 \n", "L 77.184294 119.097229 \n", "L 77.820572 119.32211 \n", "L 78.457369 119.546012 \n", "L 79.094689 119.768908 \n", "L 79.732531 119.990783 \n", "L 80.370898 120.211634 \n", "L 81.009796 120.431472 \n", "L 81.649223 120.650313 \n", "L 82.289183 120.868188 \n", "L 82.92968 121.08513 \n", "L 83.570713 121.301182 \n", "L 84.212286 121.516389 \n", "L 84.854403 121.730804 \n", "L 85.497062 121.944479 \n", "L 86.140266 122.15747 \n", "L 86.784019 122.369835 \n", "L 87.428319 122.581628 \n", "L 88.073169 122.792909 \n", "L 88.71857 123.003731 \n", "L 89.364526 123.214151 \n", "L 90.011033 123.424217 \n", "L 90.658095 123.633981 \n", "L 91.305715 123.843491 \n", "L 91.953889 124.052788 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 30.941129 103.236718 \n", "L 31.536671 103.42191 \n", "L 32.132686 103.606651 \n", "L 32.729169 103.790871 \n", "L 33.326121 103.974498 \n", "L 33.923542 104.157454 \n", "L 34.521429 104.339656 \n", "L 35.119782 104.52102 \n", "L 35.718599 104.70146 \n", "L 36.317882 104.880885 \n", "L 36.917627 105.059206 \n", "L 37.517835 105.236331 \n", "L 38.118507 105.412175 \n", "L 38.71964 105.586649 \n", "L 39.321236 105.759673 \n", "L 39.923297 105.931174 \n", "L 40.525821 106.101082 \n", "L 41.128811 106.269341 \n", "L 41.732271 106.435906 \n", "L 42.336199 106.600743 \n", "L 42.9406 106.763836 \n", "L 43.545479 106.925187 \n", "L 44.150837 107.084816 \n", "L 44.75668 107.242762 \n", "L 45.363014 107.39909 \n", "L 45.969842 107.553885 \n", "L 46.577171 107.70726 \n", "L 47.185008 107.85935 \n", "L 47.79336 108.010316 \n", "L 48.402231 108.160345 \n", "L 49.011632 108.309649 \n", "L 49.621567 108.458462 \n", "L 50.232047 108.60704 \n", "L 50.843076 108.755662 \n", "L 51.454662 108.90462 \n", "L 52.066813 109.054225 \n", "L 52.679534 109.204798 \n", "L 53.292833 109.356667 \n", "L 53.906714 109.510166 \n", "L 54.521182 109.665628 \n", "L 55.136242 109.823381 \n", "L 55.751896 109.983745 \n", "L 56.368147 110.147026 \n", "L 56.984998 110.313513 \n", "L 57.602448 110.483472 \n", "L 58.220497 110.657144 \n", "L 58.839144 110.834739 \n", "L 59.458386 111.016437 \n", "L 60.078222 111.202378 \n", "L 60.698646 111.392667 \n", "L 61.319656 111.58737 \n", "L 61.941243 111.786509 \n", "L 62.563403 111.990069 \n", "L 63.186131 112.197991 \n", "L 63.809418 112.410177 \n", "L 64.433259 112.626493 \n", "L 65.057645 112.846766 \n", "L 65.682571 113.070792 \n", "L 66.308029 113.298333 \n", "L 66.934012 113.529129 \n", "L 67.560514 113.762895 \n", "L 68.187528 113.999326 \n", "L 68.81505 114.238108 \n", "L 69.443075 114.47891 \n", "L 70.071598 114.721403 \n", "L 70.700614 114.965253 \n", "L 71.330123 115.21013 \n", "L 71.960119 115.455712 \n", "L 72.590604 115.701688 \n", "L 73.221575 115.947762 \n", "L 73.853031 116.193653 \n", "L 74.484975 116.439102 \n", "L 75.117404 116.683871 \n", "L 75.750323 116.927746 \n", "L 76.383733 117.170535 \n", "L 77.017635 117.412074 \n", "L 77.652033 117.652223 \n", "L 78.28693 117.890868 \n", "L 78.922328 118.127917 \n", "L 79.558231 118.363305 \n", "L 80.194644 118.596987 \n", "L 80.831568 118.828941 \n", "L 81.469007 119.059163 \n", "L 82.106968 119.287668 \n", "L 82.745449 119.514485 \n", "L 83.384456 119.739659 \n", "L 84.023994 119.963245 \n", "L 84.664063 120.18531 \n", "L 85.304666 120.405928 \n", "L 85.945809 120.62518 \n", "L 86.58749 120.84315 \n", "L 87.229714 121.059928 \n", "L 87.872484 121.275604 \n", "L 88.515799 121.490267 \n", "L 89.159662 121.704009 \n", "L 89.804075 121.916917 \n", "L 90.449041 122.129078 \n", "L 91.094557 122.340572 \n", "L 91.740628 122.55148 \n", "L 92.387255 122.761877 \n", "L 93.034435 122.971831 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 32.104379 102.242244 \n", "L 32.699058 102.42437 \n", "L 33.294199 102.605712 \n", "L 33.889797 102.786164 \n", "L 34.485852 102.965612 \n", "L 35.082363 103.143937 \n", "L 35.679325 103.32101 \n", "L 36.276738 103.496703 \n", "L 36.874601 103.670879 \n", "L 37.472913 103.8434 \n", "L 38.07167 104.014127 \n", "L 38.670873 104.18292 \n", "L 39.270523 104.349643 \n", "L 39.870616 104.514162 \n", "L 40.471154 104.676353 \n", "L 41.072139 104.8361 \n", "L 41.673569 104.993298 \n", "L 42.275449 105.147858 \n", "L 42.877782 105.29971 \n", "L 43.480568 105.448802 \n", "L 44.083813 105.595111 \n", "L 44.687524 105.738636 \n", "L 45.291703 105.879408 \n", "L 45.896358 106.01749 \n", "L 46.501499 106.152979 \n", "L 47.107128 106.286009 \n", "L 47.713258 106.41675 \n", "L 48.319897 106.545415 \n", "L 48.927055 106.67225 \n", "L 49.534741 106.797546 \n", "L 50.142968 106.921628 \n", "L 50.751743 107.044858 \n", "L 51.361081 107.167634 \n", "L 51.970989 107.290384 \n", "L 52.581478 107.413564 \n", "L 53.192559 107.537651 \n", "L 53.804241 107.663142 \n", "L 54.416532 107.790547 \n", "L 55.029441 107.920381 \n", "L 55.642972 108.053158 \n", "L 56.257134 108.189389 \n", "L 56.871928 108.329566 \n", "L 57.48736 108.474165 \n", "L 58.103431 108.623632 \n", "L 58.72014 108.77838 \n", "L 59.337487 108.93878 \n", "L 59.955469 109.105161 \n", "L 60.574082 109.277797 \n", "L 61.193321 109.456909 \n", "L 61.813179 109.642658 \n", "L 62.433651 109.835144 \n", "L 63.054724 110.034404 \n", "L 63.676392 110.240412 \n", "L 64.298644 110.453079 \n", "L 64.921469 110.672253 \n", "L 65.544856 110.897724 \n", "L 66.168795 111.129226 \n", "L 66.793274 111.366441 \n", "L 67.418284 111.609003 \n", "L 68.043812 111.856506 \n", "L 68.669849 112.108511 \n", "L 69.296387 112.364545 \n", "L 69.923416 112.62412 \n", "L 70.55093 112.886727 \n", "L 71.178921 113.151854 \n", "L 71.807383 113.418983 \n", "L 72.436312 113.687606 \n", "L 73.065705 113.957223 \n", "L 73.695559 114.227354 \n", "L 74.325873 114.497539 \n", "L 74.956645 114.767346 \n", "L 75.587878 115.036372 \n", "L 76.219571 115.30425 \n", "L 76.851728 115.570648 \n", "L 77.484351 115.835272 \n", "L 78.117442 116.097866 \n", "L 78.751007 116.358214 \n", "L 79.385052 116.616139 \n", "L 80.019577 116.871501 \n", "L 80.654589 117.124198 \n", "L 81.290096 117.374163 \n", "L 81.926098 117.621358 \n", "L 82.562603 117.865781 \n", "L 83.199617 118.107452 \n", "L 83.837142 118.346418 \n", "L 84.475184 118.582747 \n", "L 85.11375 118.816527 \n", "L 85.75284 119.047859 \n", "L 86.392459 119.276857 \n", "L 87.032614 119.503648 \n", "L 87.673303 119.728361 \n", "L 88.314533 119.951135 \n", "L 88.956306 120.172109 \n", "L 89.598623 120.391422 \n", "L 90.241487 120.609213 \n", "L 90.8849 120.82562 \n", "L 91.528865 121.040774 \n", "L 92.173381 121.254802 \n", "L 92.818451 121.467826 \n", "L 93.464077 121.679963 \n", "L 94.110257 121.891318 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 33.262515 101.246396 \n", "L 33.856306 101.424507 \n", "L 34.450545 101.601364 \n", "L 35.045226 101.776804 \n", "L 35.640347 101.950658 \n", "L 36.235907 102.122746 \n", "L 36.8319 102.292876 \n", "L 37.428324 102.460852 \n", "L 38.025178 102.626471 \n", "L 38.62246 102.789526 \n", "L 39.220165 102.949806 \n", "L 39.818293 103.107101 \n", "L 40.416845 103.261207 \n", "L 41.015816 103.411923 \n", "L 41.615209 103.559061 \n", "L 42.215026 103.702447 \n", "L 42.815266 103.841922 \n", "L 43.415933 103.977354 \n", "L 44.017033 104.108636 \n", "L 44.618566 104.235691 \n", "L 45.22054 104.35848 \n", "L 45.822965 104.477005 \n", "L 46.425845 104.59131 \n", "L 47.029191 104.701489 \n", "L 47.633014 104.807688 \n", "L 48.237324 104.910107 \n", "L 48.842133 105.009002 \n", "L 49.447457 105.104689 \n", "L 50.053307 105.197542 \n", "L 50.659699 105.287994 \n", "L 51.266647 105.376535 \n", "L 51.874167 105.463708 \n", "L 52.482274 105.550112 \n", "L 53.090984 105.636388 \n", "L 53.700309 105.723221 \n", "L 54.310265 105.811332 \n", "L 54.920863 105.901465 \n", "L 55.532118 105.994388 \n", "L 56.144038 106.090875 \n", "L 56.756633 106.191702 \n", "L 57.36991 106.297634 \n", "L 57.983876 106.409415 \n", "L 58.598533 106.52776 \n", "L 59.213884 106.65334 \n", "L 59.829927 106.786777 \n", "L 60.446661 106.928632 \n", "L 61.06408 107.079397 \n", "L 61.682179 107.239487 \n", "L 62.300949 107.409233 \n", "L 62.920379 107.588879 \n", "L 63.540459 107.778575 \n", "L 64.161174 107.978377 \n", "L 64.782509 108.188247 \n", "L 65.40445 108.408047 \n", "L 66.026982 108.637551 \n", "L 66.650086 108.876442 \n", "L 67.273748 109.124318 \n", "L 67.89795 109.380701 \n", "L 68.522677 109.645039 \n", "L 69.147912 109.916723 \n", "L 69.773642 110.195086 \n", "L 70.399852 110.479423 \n", "L 71.026531 110.768994 \n", "L 71.653667 111.063035 \n", "L 72.281251 111.360774 \n", "L 72.909274 111.661434 \n", "L 73.53773 111.964248 \n", "L 74.166613 112.268466 \n", "L 74.795922 112.573365 \n", "L 75.425653 112.878253 \n", "L 76.055806 113.182481 \n", "L 76.686382 113.485445 \n", "L 77.317383 113.786591 \n", "L 77.948813 114.085421 \n", "L 78.580676 114.381494 \n", "L 79.212977 114.674425 \n", "L 79.845721 114.96389 \n", "L 80.478917 115.249625 \n", "L 81.112568 115.531418 \n", "L 81.746684 115.809118 \n", "L 82.381274 116.082622 \n", "L 83.016341 116.351877 \n", "L 83.651894 116.616876 \n", "L 84.287942 116.877654 \n", "L 84.92449 117.134278 \n", "L 85.561544 117.386854 \n", "L 86.199113 117.635511 \n", "L 86.837199 117.880402 \n", "L 87.47581 118.1217 \n", "L 88.114952 118.359594 \n", "L 88.754626 118.59428 \n", "L 89.394838 118.825963 \n", "L 90.035593 119.054855 \n", "L 90.676891 119.281164 \n", "L 91.318737 119.5051 \n", "L 91.961131 119.726867 \n", "L 92.60408 119.946664 \n", "L 93.24758 120.164682 \n", "L 93.891634 120.381104 \n", "L 94.536247 120.596103 \n", "L 95.181415 120.809841 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 34.415533 100.247693 \n", "L 35.008406 100.420592 \n", "L 35.601708 100.591589 \n", "L 36.195433 100.760447 \n", "L 36.789577 100.926919 \n", "L 37.384139 101.09074 \n", "L 37.979109 101.251634 \n", "L 38.574486 101.409312 \n", "L 39.170266 101.563478 \n", "L 39.766447 101.713828 \n", "L 40.363022 101.860056 \n", "L 40.959992 102.001855 \n", "L 41.557356 102.138926 \n", "L 42.155111 102.270976 \n", "L 42.753256 102.397729 \n", "L 43.351797 102.518931 \n", "L 43.950732 102.63435 \n", "L 44.550067 102.743791 \n", "L 45.149808 102.847098 \n", "L 45.749958 102.944157 \n", "L 46.350529 103.034912 \n", "L 46.951531 103.119362 \n", "L 47.552973 103.197573 \n", "L 48.154869 103.269679 \n", "L 48.757236 103.335893 \n", "L 49.360086 103.396505 \n", "L 49.963439 103.451889 \n", "L 50.567313 103.502505 \n", "L 51.171728 103.548897 \n", "L 51.776703 103.591697 \n", "L 52.382261 103.63162 \n", "L 52.98842 103.66946 \n", "L 53.595205 103.706088 \n", "L 54.202634 103.742444 \n", "L 54.810726 103.779527 \n", "L 55.419504 103.81839 \n", "L 56.028982 103.860124 \n", "L 56.639179 103.905847 \n", "L 57.250108 103.956695 \n", "L 57.86178 104.013801 \n", "L 58.474206 104.078286 \n", "L 59.087393 104.151239 \n", "L 59.701345 104.233705 \n", "L 60.316063 104.32667 \n", "L 60.931545 104.431042 \n", "L 61.547786 104.547644 \n", "L 62.16478 104.677196 \n", "L 62.782516 104.820306 \n", "L 63.40098 104.97746 \n", "L 64.020158 105.149014 \n", "L 64.640032 105.33519 \n", "L 65.260581 105.536068 \n", "L 65.881785 105.751592 \n", "L 66.50362 105.981561 \n", "L 67.126065 106.225644 \n", "L 67.749094 106.483375 \n", "L 68.372684 106.754166 \n", "L 68.996811 107.037315 \n", "L 69.621454 107.332016 \n", "L 70.246587 107.637373 \n", "L 70.872192 107.952414 \n", "L 71.49825 108.276101 \n", "L 72.124742 108.607353 \n", "L 72.751653 108.945054 \n", "L 73.37897 109.288071 \n", "L 74.006681 109.635269 \n", "L 74.634778 109.985526 \n", "L 75.263253 110.337745 \n", "L 75.892104 110.690868 \n", "L 76.521328 111.043886 \n", "L 77.150923 111.39585 \n", "L 77.780893 111.745876 \n", "L 78.41124 112.093158 \n", "L 79.04197 112.436968 \n", "L 79.67309 112.776662 \n", "L 80.304607 113.111679 \n", "L 80.936529 113.441549 \n", "L 81.568868 113.765883 \n", "L 82.201631 114.084377 \n", "L 82.83483 114.396807 \n", "L 83.468476 114.703027 \n", "L 84.102577 115.002959 \n", "L 84.737145 115.296595 \n", "L 85.372192 115.583985 \n", "L 86.007724 115.86523 \n", "L 86.643752 116.140482 \n", "L 87.280286 116.409933 \n", "L 87.91733 116.673805 \n", "L 88.554894 116.932353 \n", "L 89.192986 117.185849 \n", "L 89.831609 117.434583 \n", "L 90.470769 117.678856 \n", "L 91.110473 117.918973 \n", "L 91.750721 118.15524 \n", "L 92.391519 118.387963 \n", "L 93.032869 118.61744 \n", "L 93.674776 118.843962 \n", "L 94.317237 119.067807 \n", "L 94.960256 119.289242 \n", "L 95.603837 119.50852 \n", "L 96.247977 119.725876 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 35.563436 99.244691 \n", "L 36.155356 99.410935 \n", "L 36.747683 99.574417 \n", "L 37.340408 99.734803 \n", "L 37.933527 99.89174 \n", "L 38.527034 100.044853 \n", "L 39.120922 100.19375 \n", "L 39.715185 100.338022 \n", "L 40.309818 100.477247 \n", "L 40.904819 100.610995 \n", "L 41.50018 100.73883 \n", "L 42.095899 100.860319 \n", "L 42.691976 100.975034 \n", "L 43.288406 101.082562 \n", "L 43.885191 101.182508 \n", "L 44.482336 101.274511 \n", "L 45.07984 101.358242 \n", "L 45.67771 101.433424 \n", "L 46.275955 101.49983 \n", "L 46.874582 101.557302 \n", "L 47.473603 101.605754 \n", "L 48.073034 101.645186 \n", "L 48.672888 101.675687 \n", "L 49.273184 101.697449 \n", "L 49.873944 101.710768 \n", "L 50.475188 101.716057 \n", "L 51.076939 101.713842 \n", "L 51.679226 101.704774 \n", "L 52.282073 101.689624 \n", "L 52.885507 101.669289 \n", "L 53.48956 101.644778 \n", "L 54.094257 101.617222 \n", "L 54.699629 101.587855 \n", "L 55.305703 101.558011 \n", "L 55.912505 101.52911 \n", "L 56.520063 101.502646 \n", "L 57.128398 101.480171 \n", "L 57.737534 101.463276 \n", "L 58.347487 101.453574 \n", "L 58.958274 101.45268 \n", "L 59.569907 101.462186 \n", "L 60.182394 101.483646 \n", "L 60.795739 101.518547 \n", "L 61.409943 101.568292 \n", "L 62.025003 101.634178 \n", "L 62.640909 101.717375 \n", "L 63.257652 101.81891 \n", "L 63.875214 101.93965 \n", "L 64.493576 102.080288 \n", "L 65.112716 102.241331 \n", "L 65.73261 102.423097 \n", "L 66.353225 102.625699 \n", "L 66.974534 102.849056 \n", "L 67.596505 103.092884 \n", "L 68.219104 103.356709 \n", "L 68.842298 103.639866 \n", "L 69.466053 103.941517 \n", "L 70.090336 104.260662 \n", "L 70.715116 104.596151 \n", "L 71.340362 104.946709 \n", "L 71.966045 105.310949 \n", "L 72.592138 105.687396 \n", "L 73.218619 106.074506 \n", "L 73.845466 106.470693 \n", "L 74.472662 106.874344 \n", "L 75.100191 107.283843 \n", "L 75.728042 107.697598 \n", "L 76.356207 108.114047 \n", "L 76.984682 108.531686 \n", "L 77.613463 108.949083 \n", "L 78.24255 109.364888 \n", "L 78.87195 109.777851 \n", "L 79.501665 110.186824 \n", "L 80.131705 110.590777 \n", "L 80.76208 110.988797 \n", "L 81.3928 111.380091 \n", "L 82.023879 111.763988 \n", "L 82.655331 112.139945 \n", "L 83.287167 112.507528 \n", "L 83.919405 112.866423 \n", "L 84.55206 113.216425 \n", "L 85.185143 113.557424 \n", "L 85.818672 113.88941 \n", "L 86.45266 114.212457 \n", "L 87.087119 114.526709 \n", "L 87.722062 114.832384 \n", "L 88.357503 115.129753 \n", "L 88.993448 115.419135 \n", "L 89.62991 115.70089 \n", "L 90.266899 115.975406 \n", "L 90.904419 116.243095 \n", "L 91.542479 116.504383 \n", "L 92.181086 116.759703 \n", "L 92.820242 117.009492 \n", "L 93.459952 117.254181 \n", "L 94.100221 117.494194 \n", "L 94.741052 117.729944 \n", "L 95.382444 117.961825 \n", "L 96.024401 118.190216 \n", "L 96.666925 118.415475 \n", "L 97.310013 118.637936 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 36.70624 98.236188 \n", "L 37.29717 98.394133 \n", "L 37.88848 98.548214 \n", "L 38.480159 98.697972 \n", "L 39.0722 98.842918 \n", "L 39.664597 98.982538 \n", "L 40.257338 99.11629 \n", "L 40.850418 99.24361 \n", "L 41.44383 99.363916 \n", "L 42.037568 99.476616 \n", "L 42.631624 99.581108 \n", "L 43.225996 99.676793 \n", "L 43.820682 99.763081 \n", "L 44.415678 99.839401 \n", "L 45.010987 99.905209 \n", "L 45.606612 99.960004 \n", "L 46.202556 100.003331 \n", "L 46.798827 100.034805 \n", "L 47.395438 100.054113 \n", "L 47.992397 100.061032 \n", "L 48.589723 100.055443 \n", "L 49.187435 100.037339 \n", "L 49.785552 100.006843 \n", "L 50.3841 99.964214 \n", "L 50.983107 99.909857 \n", "L 51.582601 99.844338 \n", "L 52.182613 99.768378 \n", "L 52.783181 99.682873 \n", "L 53.384337 99.588884 \n", "L 53.986119 99.487642 \n", "L 54.588565 99.380541 \n", "L 55.191712 99.269136 \n", "L 55.7956 99.155131 \n", "L 56.400263 99.040366 \n", "L 57.005738 98.926803 \n", "L 57.612058 98.816504 \n", "L 58.219253 98.711615 \n", "L 58.827351 98.614334 \n", "L 59.436376 98.526895 \n", "L 60.046345 98.451533 \n", "L 60.657275 98.390449 \n", "L 61.269175 98.345798 \n", "L 61.882049 98.319639 \n", "L 62.495896 98.313916 \n", "L 63.110708 98.330428 \n", "L 63.726474 98.370797 \n", "L 64.343176 98.436449 \n", "L 64.96079 98.528587 \n", "L 65.57929 98.64817 \n", "L 66.198642 98.795906 \n", "L 66.818813 98.972229 \n", "L 67.43976 99.177303 \n", "L 68.061442 99.411012 \n", "L 68.683816 99.672963 \n", "L 69.306836 99.962496 \n", "L 69.930456 100.278692 \n", "L 70.554631 100.620387 \n", "L 71.179316 100.98619 \n", "L 71.804469 101.374511 \n", "L 72.430048 101.783579 \n", "L 73.056015 102.211471 \n", "L 73.682336 102.656145 \n", "L 74.308979 103.115462 \n", "L 74.935918 103.587226 \n", "L 75.563129 104.069203 \n", "L 76.190593 104.559161 \n", "L 76.818297 105.054892 \n", "L 77.446231 105.554241 \n", "L 78.074389 106.055131 \n", "L 78.702771 106.555582 \n", "L 79.331377 107.053731 \n", "L 79.960216 107.547856 \n", "L 80.589294 108.036373 \n", "L 81.218626 108.517863 \n", "L 81.848225 108.991068 \n", "L 82.478106 109.454898 \n", "L 83.108288 109.908432 \n", "L 83.738793 110.350922 \n", "L 84.369636 110.781774 \n", "L 85.00084 111.20056 \n", "L 85.632427 111.606998 \n", "L 86.264413 112.000943 \n", "L 86.89682 112.382383 \n", "L 87.529667 112.75142 \n", "L 88.16297 113.10826 \n", "L 88.796747 113.453202 \n", "L 89.431015 113.786624 \n", "L 90.065784 114.108967 \n", "L 90.701069 114.420728 \n", "L 91.336884 114.722444 \n", "L 91.973233 115.014684 \n", "L 92.610129 115.298035 \n", "L 93.247579 115.573097 \n", "L 93.885587 115.840469 \n", "L 94.524158 116.100749 \n", "L 95.163298 116.354521 \n", "L 95.80301 116.602351 \n", "L 96.443293 116.844786 \n", "L 97.084151 117.082346 \n", "L 97.725586 117.315525 \n", "L 98.367595 117.544785 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 37.843979 97.221486 \n", "L 38.433884 97.369372 \n", "L 39.024137 97.512032 \n", "L 39.614725 97.648853 \n", "L 40.205639 97.779181 \n", "L 40.796871 97.902326 \n", "L 41.388405 98.017561 \n", "L 41.980236 98.124131 \n", "L 42.572352 98.221259 \n", "L 43.164749 98.308146 \n", "L 43.757416 98.383988 \n", "L 44.35035 98.447982 \n", "L 44.943548 98.499335 \n", "L 45.537008 98.537279 \n", "L 46.13073 98.561086 \n", "L 46.724722 98.570079 \n", "L 47.318987 98.56365 \n", "L 47.913536 98.541274 \n", "L 48.508385 98.502531 \n", "L 49.103548 98.447118 \n", "L 49.699047 98.374868 \n", "L 50.294909 98.28577 \n", "L 50.891158 98.179981 \n", "L 51.487829 98.057841 \n", "L 52.084958 97.919889 \n", "L 52.682581 97.766874 \n", "L 53.28074 97.599764 \n", "L 53.879483 97.419746 \n", "L 54.478853 97.228242 \n", "L 55.078897 97.026895 \n", "L 55.679667 96.817574 \n", "L 56.281209 96.602359 \n", "L 56.883573 96.383535 \n", "L 57.486805 96.163571 \n", "L 58.09095 95.945098 \n", "L 58.696051 95.730887 \n", "L 59.302145 95.523821 \n", "L 59.909267 95.326859 \n", "L 60.517447 95.143 \n", "L 61.126706 94.975257 \n", "L 61.737062 94.826596 \n", "L 62.348526 94.699914 \n", "L 62.9611 94.597993 \n", "L 63.574782 94.523453 \n", "L 64.189558 94.47872 \n", "L 64.805411 94.465985 \n", "L 65.422315 94.487172 \n", "L 66.040238 94.543901 \n", "L 66.659142 94.637476 \n", "L 67.278981 94.768846 \n", "L 67.899709 94.938602 \n", "L 68.52127 95.146962 \n", "L 69.143608 95.393771 \n", "L 69.766664 95.678499 \n", "L 70.390379 96.000255 \n", "L 71.014691 96.357793 \n", "L 71.639541 96.749544 \n", "L 72.264871 97.173632 \n", "L 72.890623 97.627909 \n", "L 73.516746 98.109985 \n", "L 74.143189 98.617269 \n", "L 74.769909 99.147003 \n", "L 75.396866 99.696309 \n", "L 76.024026 100.262227 \n", "L 76.651361 100.84175 \n", "L 77.278847 101.431873 \n", "L 77.90647 102.02963 \n", "L 78.534217 102.632121 \n", "L 79.162085 103.236557 \n", "L 79.790073 103.840281 \n", "L 80.418185 104.440795 \n", "L 81.046434 105.035784 \n", "L 81.674831 105.623133 \n", "L 82.303395 106.200939 \n", "L 82.932147 106.767521 \n", "L 83.561109 107.32142 \n", "L 84.190306 107.861412 \n", "L 84.819766 108.386495 \n", "L 85.449514 108.895881 \n", "L 86.079577 109.389002 \n", "L 86.709986 109.865485 \n", "L 87.340763 110.325143 \n", "L 87.971936 110.767963 \n", "L 88.603532 111.19409 \n", "L 89.23557 111.603802 \n", "L 89.868074 111.997503 \n", "L 90.501066 112.375704 \n", "L 91.13456 112.738999 \n", "L 91.768573 113.088056 \n", "L 92.403123 113.423598 \n", "L 93.038218 113.746386 \n", "L 93.67387 114.057209 \n", "L 94.31009 114.356873 \n", "L 94.94688 114.64618 \n", "L 95.58425 114.925932 \n", "L 96.222203 115.19691 \n", "L 96.860744 115.459876 \n", "L 97.499871 115.715559 \n", "L 98.139587 115.964657 \n", "L 98.779894 116.207831 \n", "L 99.420789 116.445701 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 38.976716 96.200668 \n", "L 39.565564 96.336751 \n", "L 40.154726 96.465992 \n", "L 40.744186 96.587591 \n", "L 41.333931 96.700698 \n", "L 41.92395 96.804414 \n", "L 42.514227 96.897794 \n", "L 43.104752 96.979854 \n", "L 43.695514 97.04958 \n", "L 44.286505 97.105935 \n", "L 44.877713 97.147869 \n", "L 45.469134 97.174333 \n", "L 46.060766 97.184295 \n", "L 46.652605 97.176754 \n", "L 47.244655 97.150755 \n", "L 47.836923 97.105415 \n", "L 48.429415 97.039937 \n", "L 49.022147 96.953632 \n", "L 49.615137 96.845947 \n", "L 50.208406 96.716482 \n", "L 50.801981 96.56501 \n", "L 51.395896 96.39151 \n", "L 51.990184 96.196176 \n", "L 52.584888 95.979443 \n", "L 53.180054 95.742004 \n", "L 53.775728 95.484824 \n", "L 54.371966 95.209154 \n", "L 54.968824 94.916536 \n", "L 55.566358 94.60881 \n", "L 56.16463 94.288113 \n", "L 56.763701 93.956874 \n", "L 57.363631 93.6178 \n", "L 57.964481 93.273869 \n", "L 58.56631 92.928298 \n", "L 59.169172 92.584521 \n", "L 59.773121 92.246156 \n", "L 60.378201 91.916971 \n", "L 60.984456 91.600832 \n", "L 61.591919 91.301669 \n", "L 62.200617 91.023421 \n", "L 62.810568 90.769977 \n", "L 63.421784 90.545135 \n", "L 64.034265 90.352543 \n", "L 64.648005 90.195639 \n", "L 65.262983 90.077608 \n", "L 65.879175 90.001327 \n", "L 66.496544 89.969323 \n", "L 67.115048 89.983731 \n", "L 67.734633 90.046255 \n", "L 68.355241 90.15815 \n", "L 68.976808 90.320192 \n", "L 69.599263 90.532667 \n", "L 70.222532 90.79537 \n", "L 70.846538 91.107607 \n", "L 71.471205 91.468201 \n", "L 72.096455 91.875522 \n", "L 72.72221 92.327502 \n", "L 73.348395 92.82168 \n", "L 73.974941 93.355231 \n", "L 74.60178 93.925019 \n", "L 75.228852 94.527644 \n", "L 75.856099 95.159489 \n", "L 76.483476 95.816778 \n", "L 77.11094 96.495633 \n", "L 77.738459 97.192119 \n", "L 78.366003 97.902301 \n", "L 78.993559 98.622298 \n", "L 79.621112 99.348324 \n", "L 80.248661 100.076735 \n", "L 80.876208 100.804062 \n", "L 81.503763 101.52705 \n", "L 82.131342 102.242681 \n", "L 82.758964 102.948201 \n", "L 83.386655 103.641138 \n", "L 84.014444 104.319304 \n", "L 84.642361 104.98081 \n", "L 85.27044 105.624066 \n", "L 85.898718 106.247776 \n", "L 86.527228 106.850926 \n", "L 87.156007 107.432782 \n", "L 87.785094 107.99287 \n", "L 88.414519 108.530953 \n", "L 89.044317 109.047023 \n", "L 89.674523 109.541276 \n", "L 90.305162 110.01408 \n", "L 90.936264 110.465971 \n", "L 91.567856 110.897617 \n", "L 92.199956 111.309796 \n", "L 92.832586 111.703382 \n", "L 93.465766 112.079319 \n", "L 94.099506 112.438601 \n", "L 94.733822 112.782259 \n", "L 95.368726 113.11134 \n", "L 96.004222 113.426894 \n", "L 96.640317 113.729962 \n", "L 97.277017 114.021563 \n", "L 97.914327 114.302688 \n", "L 98.552244 114.574287 \n", "L 99.19077 114.837269 \n", "L 99.829907 115.092494 \n", "L 100.469649 115.340767 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 40.104539 95.174844 \n", "L 40.692311 95.297577 \n", "L 41.280359 95.411622 \n", "L 41.868665 95.515969 \n", "L 42.457213 95.609541 \n", "L 43.045989 95.691203 \n", "L 43.634976 95.759759 \n", "L 44.22416 95.813963 \n", "L 44.813529 95.852532 \n", "L 45.403072 95.874152 \n", "L 45.992778 95.877494 \n", "L 46.582641 95.86123 \n", "L 47.17266 95.824049 \n", "L 47.76283 95.76468 \n", "L 48.353157 95.681914 \n", "L 48.94365 95.574624 \n", "L 49.534318 95.441797 \n", "L 50.125182 95.282554 \n", "L 50.716264 95.096184 \n", "L 51.307591 94.882173 \n", "L 51.899198 94.640224 \n", "L 52.491125 94.370295 \n", "L 53.083417 94.072622 \n", "L 53.676124 93.747741 \n", "L 54.269305 93.396515 \n", "L 54.863017 93.020154 \n", "L 55.457327 92.620227 \n", "L 56.052306 92.198674 \n", "L 56.648023 91.757815 \n", "L 57.244551 91.300344 \n", "L 57.841967 90.829331 \n", "L 58.440343 90.348199 \n", "L 59.039754 89.860718 \n", "L 59.64027 89.370965 \n", "L 60.241956 88.883295 \n", "L 60.844876 88.402302 \n", "L 61.449083 87.932769 \n", "L 62.054628 87.479617 \n", "L 62.661548 87.047845 \n", "L 63.269874 86.642466 \n", "L 63.879626 86.26844 \n", "L 64.490813 85.930608 \n", "L 65.103433 85.633623 \n", "L 65.717473 85.381876 \n", "L 66.332905 85.179435 \n", "L 66.949695 85.029975 \n", "L 67.567793 84.936728 \n", "L 68.187142 84.902422 \n", "L 68.807675 84.929239 \n", "L 69.429314 85.018785 \n", "L 70.051979 85.172051 \n", "L 70.675576 85.389405 \n", "L 71.300015 85.670584 \n", "L 71.925198 86.014701 \n", "L 72.551028 86.42025 \n", "L 73.177407 86.885142 \n", "L 73.80424 87.406738 \n", "L 74.431436 87.981886 \n", "L 75.058906 88.606982 \n", "L 75.68657 89.278017 \n", "L 76.314353 89.99065 \n", "L 76.942189 90.740265 \n", "L 77.570021 91.522049 \n", "L 78.197802 92.331057 \n", "L 78.825493 93.162277 \n", "L 79.453063 94.010703 \n", "L 80.080496 94.8714 \n", "L 80.707781 95.739554 \n", "L 81.334919 96.610539 \n", "L 81.961916 97.479956 \n", "L 82.588789 98.343679 \n", "L 83.21556 99.197885 \n", "L 83.842258 100.039093 \n", "L 84.468918 100.864174 \n", "L 85.095578 101.670373 \n", "L 85.722279 102.455304 \n", "L 86.349065 103.216969 \n", "L 86.975985 103.95374 \n", "L 87.60308 104.664345 \n", "L 88.230399 105.347869 \n", "L 88.85799 106.003725 \n", "L 89.485893 106.631627 \n", "L 90.114151 107.231576 \n", "L 90.742807 107.803831 \n", "L 91.371894 108.348869 \n", "L 92.001446 108.867374 \n", "L 92.631498 109.360202 \n", "L 93.262072 109.828343 \n", "L 93.893195 110.272909 \n", "L 94.524889 110.695099 \n", "L 95.157168 111.096174 \n", "L 95.790049 111.477441 \n", "L 96.423545 111.84023 \n", "L 97.057662 112.185866 \n", "L 97.692407 112.515671 \n", "L 98.327785 112.830934 \n", "L 98.963801 113.132908 \n", "L 99.600451 113.422796 \n", "L 100.237736 113.701747 \n", "L 100.875657 113.970848 \n", "L 101.514207 114.231118 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 41.227572 94.146311 \n", "L 41.814261 94.254544 \n", "L 42.401188 94.352074 \n", "L 42.988332 94.43766 \n", "L 43.575676 94.509979 \n", "L 44.163202 94.567635 \n", "L 44.750891 94.609158 \n", "L 45.338726 94.633016 \n", "L 45.926694 94.637631 \n", "L 46.514784 94.621386 \n", "L 47.102981 94.582645 \n", "L 47.691281 94.519771 \n", "L 48.279682 94.43115 \n", "L 48.868182 94.315216 \n", "L 49.456786 94.170472 \n", "L 50.045508 93.995528 \n", "L 50.63436 93.789129 \n", "L 51.223366 93.550184 \n", "L 51.812556 93.277806 \n", "L 52.401962 92.971351 \n", "L 52.991626 92.630437 \n", "L 53.5816 92.254999 \n", "L 54.171935 91.845308 \n", "L 54.762694 91.402007 \n", "L 55.353946 90.92614 \n", "L 55.945762 90.419176 \n", "L 56.538222 89.883026 \n", "L 57.131408 89.320064 \n", "L 57.725405 88.733125 \n", "L 58.3203 88.125515 \n", "L 58.916182 87.500998 \n", "L 59.513139 86.863787 \n", "L 60.111256 86.218512 \n", "L 60.710617 85.570198 \n", "L 61.311298 84.924213 \n", "L 61.913371 84.286223 \n", "L 62.516899 83.662142 \n", "L 63.121938 83.058046 \n", "L 63.72853 82.480122 \n", "L 64.336707 81.934574 \n", "L 64.946489 81.427553 \n", "L 65.557884 80.96506 \n", "L 66.170883 80.552867 \n", "L 66.785466 80.196425 \n", "L 67.401596 79.90079 \n", "L 68.019224 79.670529 \n", "L 68.63829 79.509659 \n", "L 69.258717 79.421572 \n", "L 69.88042 79.40899 \n", "L 70.503304 79.473906 \n", "L 71.127266 79.617551 \n", "L 71.752192 79.840393 \n", "L 72.377969 80.142096 \n", "L 73.004479 80.521557 \n", "L 73.631602 80.976908 \n", "L 74.25922 81.505545 \n", "L 74.887218 82.104183 \n", "L 75.515486 82.768908 \n", "L 76.143921 83.495238 \n", "L 76.772424 84.278192 \n", "L 77.40091 85.112379 \n", "L 78.029303 85.992074 \n", "L 78.657536 86.911307 \n", "L 79.285556 87.863946 \n", "L 79.913321 88.843789 \n", "L 80.5408 89.844638 \n", "L 81.167977 90.860396 \n", "L 81.794843 91.885113 \n", "L 82.421405 92.91308 \n", "L 83.047676 93.938874 \n", "L 83.673678 94.95741 \n", "L 84.299447 95.963989 \n", "L 84.925019 96.954329 \n", "L 85.550442 97.92459 \n", "L 86.175764 98.871388 \n", "L 86.801039 99.791812 \n", "L 87.426323 100.683413 \n", "L 88.051677 101.544205 \n", "L 88.677154 102.372648 \n", "L 89.302815 103.167629 \n", "L 89.928717 103.928445 \n", "L 90.554912 104.654763 \n", "L 91.181452 105.346597 \n", "L 91.808387 106.004282 \n", "L 92.435759 106.628419 \n", "L 93.06361 107.219861 \n", "L 93.691978 107.779669 \n", "L 94.320892 108.309072 \n", "L 94.950383 108.809442 \n", "L 95.580475 109.282261 \n", "L 96.211186 109.729087 \n", "L 96.842534 110.151528 \n", "L 97.474532 110.551221 \n", "L 98.107188 110.929801 \n", "L 98.740508 111.288891 \n", "L 99.374498 111.630078 \n", "L 100.00916 111.954904 \n", "L 100.64449 112.264847 \n", "L 101.280487 112.561318 \n", "L 101.917151 112.845654 \n", "L 102.554471 113.119107 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 42.345958 93.118566 \n", "L 42.931577 93.211758 \n", "L 43.517397 93.292149 \n", "L 44.103396 93.358258 \n", "L 44.689553 93.408509 \n", "L 45.27585 93.441231 \n", "L 45.862263 93.454669 \n", "L 46.448777 93.446994 \n", "L 47.035375 93.41632 \n", "L 47.622046 93.36071 \n", "L 48.208774 93.278211 \n", "L 48.795556 93.166863 \n", "L 49.382389 93.024734 \n", "L 49.969273 92.849943 \n", "L 50.556218 92.640698 \n", "L 51.143237 92.395328 \n", "L 51.730349 92.112321 \n", "L 52.317581 91.790363 \n", "L 52.90497 91.42838 \n", "L 53.492555 91.025581 \n", "L 54.080386 90.581499 \n", "L 54.668524 90.09603 \n", "L 55.25703 89.56948 \n", "L 55.845978 89.002596 \n", "L 56.43545 88.396599 \n", "L 57.025527 87.753227 \n", "L 57.616302 87.074742 \n", "L 58.207872 86.363955 \n", "L 58.800335 85.624242 \n", "L 59.393791 84.859541 \n", "L 59.988343 84.074338 \n", "L 60.584091 83.273662 \n", "L 61.181133 82.463048 \n", "L 61.779564 81.64851 \n", "L 62.37947 80.836478 \n", "L 62.980931 80.033748 \n", "L 63.584018 79.247413 \n", "L 64.18879 78.484778 \n", "L 64.795294 77.75328 \n", "L 65.403561 77.060383 \n", "L 66.013611 76.413498 \n", "L 66.625444 75.819857 \n", "L 67.239047 75.286416 \n", "L 67.85439 74.819762 \n", "L 68.471424 74.425997 \n", "L 69.090087 74.110636 \n", "L 69.7103 73.878539 \n", "L 70.331971 73.733808 \n", "L 70.954993 73.679731 \n", "L 71.579251 73.718717 \n", "L 72.204619 73.852268 \n", "L 72.830962 74.080939 \n", "L 73.458143 74.404333 \n", "L 74.086022 74.821107 \n", "L 74.714456 75.329 \n", "L 75.343308 75.92487 \n", "L 75.972441 76.604745 \n", "L 76.601728 77.363886 \n", "L 77.231049 78.196882 \n", "L 77.860294 79.097721 \n", "L 78.489363 80.059889 \n", "L 79.11817 81.076484 \n", "L 79.746645 82.140311 \n", "L 80.374728 83.243981 \n", "L 81.002377 84.38003 \n", "L 81.629559 85.541007 \n", "L 82.256263 86.719581 \n", "L 82.882484 87.908614 \n", "L 83.508236 89.101255 \n", "L 84.133542 90.291009 \n", "L 84.758433 91.471788 \n", "L 85.382955 92.637974 \n", "L 86.007158 93.784451 \n", "L 86.631101 94.906643 \n", "L 87.254847 96.00052 \n", "L 87.878463 97.062613 \n", "L 88.502018 98.09002 \n", "L 89.125585 99.080391 \n", "L 89.749233 100.031899 \n", "L 90.373032 100.943244 \n", "L 90.997051 101.813606 \n", "L 91.621353 102.642606 \n", "L 92.246001 103.430286 \n", "L 92.871052 104.177059 \n", "L 93.496556 104.883663 \n", "L 94.122562 105.551132 \n", "L 94.749114 106.180745 \n", "L 95.376246 106.773984 \n", "L 96.003992 107.332496 \n", "L 96.63238 107.858061 \n", "L 97.261428 108.352544 \n", "L 97.891157 108.817874 \n", "L 98.521581 109.256008 \n", "L 99.152705 109.668902 \n", "L 99.784539 110.058499 \n", "L 100.417084 110.426694 \n", "L 101.050342 110.775329 \n", "L 101.684308 111.10617 \n", "L 102.318979 111.420902 \n", "L 102.954351 111.721122 \n", "L 103.590413 112.008323 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 43.45986 92.096144 \n", "L 44.044439 92.174534 \n", "L 44.629189 92.238061 \n", "L 45.214083 92.285007 \n", "L 45.7991 92.313543 \n", "L 46.38422 92.32173 \n", "L 46.969417 92.307531 \n", "L 47.554675 92.268825 \n", "L 48.139976 92.203419 \n", "L 48.725308 92.109066 \n", "L 49.310656 91.983494 \n", "L 49.896017 91.824424 \n", "L 50.48139 91.629609 \n", "L 51.066777 91.396859 \n", "L 51.652188 91.124085 \n", "L 52.237644 90.809336 \n", "L 52.823164 90.450846 \n", "L 53.408783 90.047074 \n", "L 53.994544 89.596756 \n", "L 54.580491 89.098957 \n", "L 55.166685 88.553111 \n", "L 55.753193 87.959077 \n", "L 56.340088 87.317186 \n", "L 56.927453 86.628278 \n", "L 57.51538 85.89375 \n", "L 58.103964 85.115587 \n", "L 58.69331 84.296396 \n", "L 59.283527 83.439416 \n", "L 59.874724 82.548549 \n", "L 60.467015 81.628345 \n", "L 61.060515 80.684008 \n", "L 61.655333 79.721367 \n", "L 62.251581 78.746855 \n", "L 62.84936 77.767457 \n", "L 63.448766 76.790662 \n", "L 64.049887 75.824385 \n", "L 64.652795 74.876894 \n", "L 65.257556 73.956717 \n", "L 65.864214 73.07254 \n", "L 66.472803 72.233095 \n", "L 67.083335 71.447039 \n", "L 67.695806 70.722848 \n", "L 68.310194 70.068677 \n", "L 68.926458 69.492238 \n", "L 69.544535 69.000691 \n", "L 70.164348 68.600515 \n", "L 70.7858 68.297408 \n", "L 71.40878 68.096196 \n", "L 72.033161 68.000738 \n", "L 72.658806 68.013869 \n", "L 73.285568 68.13735 \n", "L 73.913287 68.371835 \n", "L 74.541805 68.716857 \n", "L 75.170959 69.170841 \n", "L 75.800587 69.731128 \n", "L 76.430529 70.394029 \n", "L 77.060632 71.154871 \n", "L 77.690751 72.008098 \n", "L 78.320753 72.947355 \n", "L 78.950513 73.965584 \n", "L 79.579925 75.055158 \n", "L 80.208895 76.207986 \n", "L 80.837345 77.415634 \n", "L 81.465218 78.669467 \n", "L 82.092468 79.960754 \n", "L 82.719068 81.28079 \n", "L 83.345011 82.621015 \n", "L 83.970299 83.973106 \n", "L 84.594955 85.329085 \n", "L 85.219011 86.681383 \n", "L 85.842513 88.022927 \n", "L 86.465517 89.347189 \n", "L 87.088088 90.648235 \n", "L 87.710297 91.920757 \n", "L 88.332224 93.160096 \n", "L 88.953947 94.36224 \n", "L 89.575551 95.523845 \n", "L 90.197124 96.642202 \n", "L 90.818744 97.715224 \n", "L 91.440496 98.741419 \n", "L 92.062461 99.719864 \n", "L 92.684711 100.650142 \n", "L 93.307318 101.532326 \n", "L 93.930349 102.366915 \n", "L 94.553861 103.154786 \n", "L 95.177909 103.897156 \n", "L 95.802542 104.595525 \n", "L 96.427798 105.251625 \n", "L 97.053714 105.867381 \n", "L 97.680322 106.444866 \n", "L 98.30764 106.986253 \n", "L 98.93569 107.493785 \n", "L 99.564486 107.96974 \n", "L 100.194034 108.416391 \n", "L 100.824339 108.835993 \n", "L 101.455404 109.230749 \n", "L 102.087228 109.602796 \n", "L 102.719802 109.954184 \n", "L 103.353123 110.286869 \n", "L 103.987184 110.602701 \n", "L 104.621971 110.903415 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 44.569439 91.084242 \n", "L 45.153032 91.148971 \n", "L 45.73677 91.19694 \n", "L 46.320626 91.226214 \n", "L 46.904578 91.234731 \n", "L 47.488603 91.220305 \n", "L 48.072677 91.180642 \n", "L 48.656781 91.113346 \n", "L 49.240898 91.015945 \n", "L 49.825016 90.885904 \n", "L 50.409121 90.720657 \n", "L 50.993211 90.517632 \n", "L 51.577287 90.274287 \n", "L 52.161351 89.988148 \n", "L 52.74542 89.656848 \n", "L 53.329515 89.278179 \n", "L 53.913663 88.850133 \n", "L 54.497901 88.370961 \n", "L 55.08228 87.839217 \n", "L 55.66685 87.253828 \n", "L 56.251679 86.614135 \n", "L 56.836843 85.919954 \n", "L 57.422422 85.171635 \n", "L 58.00851 84.370099 \n", "L 58.59521 83.516892 \n", "L 59.182626 82.614231 \n", "L 59.770871 81.665025 \n", "L 60.360067 80.672911 \n", "L 60.950334 79.642261 \n", "L 61.541794 78.578198 \n", "L 62.134573 77.486572 \n", "L 62.728788 76.373959 \n", "L 63.32456 75.247609 \n", "L 63.921996 74.115418 \n", "L 64.521197 72.985844 \n", "L 65.122256 71.867846 \n", "L 65.725248 70.770786 \n", "L 66.330237 69.704324 \n", "L 66.937269 68.678314 \n", "L 67.54637 67.702666 \n", "L 68.157549 66.787221 \n", "L 68.770795 65.941603 \n", "L 69.386072 65.175089 \n", "L 70.003329 64.496461 \n", "L 70.622489 63.913867 \n", "L 71.243458 63.434686 \n", "L 71.866122 63.065416 \n", "L 72.49035 62.811553 \n", "L 73.115996 62.677501 \n", "L 73.742901 62.666494 \n", "L 74.370896 62.780544 \n", "L 74.999803 63.020391 \n", "L 75.629441 63.38551 \n", "L 76.259628 63.874099 \n", "L 76.890181 64.483127 \n", "L 77.520926 65.208384 \n", "L 78.151692 66.044549 \n", "L 78.782321 66.985285 \n", "L 79.412667 68.023352 \n", "L 80.042599 69.15072 \n", "L 80.672001 70.358703 \n", "L 81.300776 71.638094 \n", "L 81.928846 72.979317 \n", "L 82.556151 74.372553 \n", "L 83.182652 75.807894 \n", "L 83.808325 77.275461 \n", "L 84.43317 78.765556 \n", "L 85.0572 80.268755 \n", "L 85.680447 81.776023 \n", "L 86.302955 83.278811 \n", "L 86.924782 84.769122 \n", "L 87.545999 86.239597 \n", "L 88.166684 87.683542 \n", "L 88.786924 89.094986 \n", "L 89.406812 90.468682 \n", "L 90.02644 91.800136 \n", "L 90.645909 93.085589 \n", "L 91.265316 94.322018 \n", "L 91.884756 95.50709 \n", "L 92.504324 96.639153 \n", "L 93.124111 97.71719 \n", "L 93.744198 98.740759 \n", "L 94.364667 99.709962 \n", "L 94.985593 100.625387 \n", "L 95.607039 101.488041 \n", "L 96.229064 102.299315 \n", "L 96.851723 103.060916 \n", "L 97.475056 103.774811 \n", "L 98.099104 104.443181 \n", "L 98.723898 105.068373 \n", "L 99.34946 105.652845 \n", "L 99.975808 106.199132 \n", "L 100.602959 106.709808 \n", "L 101.230915 107.187438 \n", "L 101.859682 107.634567 \n", "L 102.489259 108.05368 \n", "L 103.119643 108.447188 \n", "L 103.750824 108.817401 \n", "L 104.382793 109.166525 \n", "L 105.015542 109.496645 \n", "L 105.649054 109.809715 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 45.674843 90.088201 \n", "L 46.257518 90.141333 \n", "L 46.840323 90.176111 \n", "L 47.423229 90.190416 \n", "L 48.006214 90.18199 \n", "L 48.589254 90.14844 \n", "L 49.172326 90.087253 \n", "L 49.75541 89.995805 \n", "L 50.338491 89.871386 \n", "L 50.921557 89.71122 \n", "L 51.504597 89.512492 \n", "L 52.087607 89.272381 \n", "L 52.670593 88.988098 \n", "L 53.25356 88.656926 \n", "L 53.836526 88.276266 \n", "L 54.419518 87.843685 \n", "L 55.002566 87.356979 \n", "L 55.585712 86.814209 \n", "L 56.169012 86.213783 \n", "L 56.752523 85.554504 \n", "L 57.336319 84.835631 \n", "L 57.920482 84.05694 \n", "L 58.505101 83.218792 \n", "L 59.090276 82.322171 \n", "L 59.676118 81.368747 \n", "L 60.262738 80.360926 \n", "L 60.85026 79.301869 \n", "L 61.438811 78.195538 \n", "L 62.028519 77.046708 \n", "L 62.619513 75.860968 \n", "L 63.211925 74.644723 \n", "L 63.805879 73.405168 \n", "L 64.401498 72.15025 \n", "L 64.998896 70.888628 \n", "L 65.598174 69.629584 \n", "L 66.199427 68.382956 \n", "L 66.802727 67.159038 \n", "L 67.408139 65.968456 \n", "L 68.015702 64.822052 \n", "L 68.625438 63.730744 \n", "L 69.237347 62.705376 \n", "L 69.851408 61.756564 \n", "L 70.467576 60.894536 \n", "L 71.085785 60.128989 \n", "L 71.705944 59.46892 \n", "L 72.327943 58.922486 \n", "L 72.951652 58.496859 \n", "L 73.576921 58.198123 \n", "L 74.203587 58.031145 \n", "L 74.831472 57.999502 \n", "L 75.46039 58.105414 \n", "L 76.090142 58.349709 \n", "L 76.720531 58.731796 \n", "L 77.351359 59.249691 \n", "L 77.982428 59.90004 \n", "L 78.61355 60.678188 \n", "L 79.244542 61.578248 \n", "L 79.875238 62.593225 \n", "L 80.505483 63.715115 \n", "L 81.135141 64.935051 \n", "L 81.764093 66.24344 \n", "L 82.392243 67.630132 \n", "L 83.019511 69.084561 \n", "L 83.645844 70.595909 \n", "L 84.271205 72.153254 \n", "L 84.895582 73.74573 \n", "L 85.51898 75.362664 \n", "L 86.141424 76.993692 \n", "L 86.762957 78.628894 \n", "L 87.383637 80.258885 \n", "L 88.003534 81.874894 \n", "L 88.622733 83.468852 \n", "L 89.241325 85.033433 \n", "L 89.859412 86.562101 \n", "L 90.477099 88.049129 \n", "L 91.094494 89.489607 \n", "L 91.711708 90.87944 \n", "L 92.328852 92.215338 \n", "L 92.94603 93.494772 \n", "L 93.563347 94.715957 \n", "L 94.180906 95.877801 \n", "L 94.798794 96.979842 \n", "L 95.417101 98.022216 \n", "L 96.035908 99.005586 \n", "L 96.655281 99.931076 \n", "L 97.275286 100.800222 \n", "L 97.89598 101.614912 \n", "L 98.517405 102.37731 \n", "L 99.139603 103.089817 \n", "L 99.762606 103.755012 \n", "L 100.386435 104.375594 \n", "L 101.011109 104.954345 \n", "L 101.636641 105.494088 \n", "L 102.263034 105.997637 \n", "L 102.890291 106.46778 \n", "L 103.518407 106.907241 \n", "L 104.147377 107.318659 \n", "L 104.777189 107.704567 \n", "L 105.407832 108.067379 \n", "L 106.039292 108.409377 \n", "L 106.67155 108.732702 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 46.776187 89.112874 \n", "L 47.358027 89.157314 \n", "L 47.939992 89.18223 \n", "L 48.522051 89.185371 \n", "L 49.104183 89.164333 \n", "L 49.686367 89.116573 \n", "L 50.268578 89.039415 \n", "L 50.850799 88.93007 \n", "L 51.433015 88.785653 \n", "L 52.015216 88.603208 \n", "L 52.597392 88.379739 \n", "L 53.179543 88.112243 \n", "L 53.761676 87.797747 \n", "L 54.343799 87.433356 \n", "L 54.925935 87.016299 \n", "L 55.508112 86.543981 \n", "L 56.090364 86.014043 \n", "L 56.672739 85.424418 \n", "L 57.255296 84.773393 \n", "L 57.838097 84.059686 \n", "L 58.421221 83.282489 \n", "L 59.004756 82.441551 \n", "L 59.588793 81.537235 \n", "L 60.173441 80.57057 \n", "L 60.758814 79.543316 \n", "L 61.345029 78.458009 \n", "L 61.932214 77.318 \n", "L 62.520499 76.127484 \n", "L 63.110018 74.891526 \n", "L 63.700903 73.616065 \n", "L 64.293287 72.307903 \n", "L 64.887298 70.974693 \n", "L 65.483058 69.624895 \n", "L 66.080681 68.267721 \n", "L 66.680267 66.913071 \n", "L 67.281908 65.571423 \n", "L 67.885673 64.253764 \n", "L 68.491621 62.971431 \n", "L 69.099784 61.735998 \n", "L 69.710177 60.559134 \n", "L 70.32279 59.452417 \n", "L 70.937593 58.427202 \n", "L 71.554528 57.494431 \n", "L 72.173516 56.664473 \n", "L 72.794453 55.946958 \n", "L 73.417215 55.350619 \n", "L 74.041658 54.883142 \n", "L 74.667616 54.551034 \n", "L 75.294911 54.359513 \n", "L 75.92335 54.312411 \n", "L 76.552733 54.412107 \n", "L 77.182849 54.659488 \n", "L 77.813487 55.053922 \n", "L 78.444437 55.593282 \n", "L 79.075492 56.273975 \n", "L 79.706456 57.091013 \n", "L 80.337141 58.038096 \n", "L 80.967373 59.107729 \n", "L 81.596996 60.291343 \n", "L 82.225873 61.579446 \n", "L 82.853887 62.961781 \n", "L 83.480943 64.427486 \n", "L 84.106967 65.965261 \n", "L 84.731912 67.563553 \n", "L 85.355749 69.21069 \n", "L 85.978474 70.895068 \n", "L 86.600104 72.605301 \n", "L 87.220673 74.330326 \n", "L 87.840238 76.059575 \n", "L 88.458868 77.783039 \n", "L 89.076645 79.491384 \n", "L 89.693668 81.176019 \n", "L 90.310039 82.829152 \n", "L 90.925874 84.44384 \n", "L 91.541288 86.013995 \n", "L 92.156402 87.53441 \n", "L 92.771335 89.000747 \n", "L 93.38621 90.40952 \n", "L 94.001139 91.758058 \n", "L 94.616235 93.044479 \n", "L 95.231608 94.267643 \n", "L 95.847351 95.427077 \n", "L 96.463559 96.522939 \n", "L 97.080317 97.555954 \n", "L 97.697695 98.527329 \n", "L 98.315761 99.438714 \n", "L 98.934573 100.292126 \n", "L 99.554175 101.089878 \n", "L 100.174608 101.834533 \n", "L 100.795904 102.528841 \n", "L 101.418082 103.175679 \n", "L 102.041161 103.778011 \n", "L 102.665152 104.338842 \n", "L 103.290055 104.86117 \n", "L 103.915869 105.347961 \n", "L 104.54259 105.802115 \n", "L 105.170208 106.22644 \n", "L 105.798707 106.623629 \n", "L 106.428075 106.996251 \n", "L 107.058294 107.346733 \n", "L 107.689343 107.677351 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 47.873544 88.162005 \n", "L 48.454638 88.201307 \n", "L 49.03586 88.220439 \n", "L 49.617182 88.217072 \n", "L 50.198585 88.188727 \n", "L 50.780047 88.132772 \n", "L 51.361547 88.046445 \n", "L 51.943068 87.926863 \n", "L 52.524599 87.771046 \n", "L 53.10613 87.575937 \n", "L 53.687654 87.338442 \n", "L 54.269174 87.055452 \n", "L 54.850699 86.723897 \n", "L 55.43224 86.340781 \n", "L 56.013823 85.903236 \n", "L 56.595478 85.408578 \n", "L 57.177244 84.854365 \n", "L 57.759172 84.238452 \n", "L 58.341322 83.559066 \n", "L 58.92376 82.814871 \n", "L 59.506569 82.005026 \n", "L 60.089839 81.129258 \n", "L 60.673665 80.187938 \n", "L 61.258156 79.182114 \n", "L 61.843429 78.113594 \n", "L 62.429602 76.984991 \n", "L 63.016805 75.799755 \n", "L 63.605169 74.56221 \n", "L 64.194827 73.277585 \n", "L 64.78591 71.952006 \n", "L 65.37855 70.5925 \n", "L 65.972872 69.206975 \n", "L 66.568996 67.804168 \n", "L 67.16703 66.393608 \n", "L 67.767071 64.985522 \n", "L 68.369202 63.590763 \n", "L 68.973489 62.220691 \n", "L 69.579981 60.887039 \n", "L 70.188702 59.601793 \n", "L 70.799657 58.377033 \n", "L 71.412827 57.224755 \n", "L 72.02817 56.156721 \n", "L 72.645618 55.184271 \n", "L 73.265081 54.318153 \n", "L 73.886442 53.568349 \n", "L 74.509568 52.943911 \n", "L 75.1343 52.45281 \n", "L 75.760465 52.101795 \n", "L 76.387872 51.896279 \n", "L 77.016319 51.840233 \n", "L 77.645598 51.936131 \n", "L 78.275488 52.184887 \n", "L 78.905774 52.585851 \n", "L 79.536241 53.136811 \n", "L 80.166677 53.834044 \n", "L 80.796884 54.672376 \n", "L 81.426672 55.645272 \n", "L 82.05587 56.744959 \n", "L 82.684324 57.962555 \n", "L 83.3119 59.288221 \n", "L 83.938485 60.711323 \n", "L 84.563991 62.220606 \n", "L 85.188354 63.804362 \n", "L 85.811532 65.450623 \n", "L 86.433507 67.147303 \n", "L 87.054284 68.882386 \n", "L 87.673892 70.644086 \n", "L 88.292375 72.420965 \n", "L 88.909801 74.202076 \n", "L 89.526249 75.977078 \n", "L 90.141814 77.736315 \n", "L 90.756603 79.470915 \n", "L 91.370731 81.172825 \n", "L 91.984321 82.834872 \n", "L 92.597499 84.450777 \n", "L 93.210392 86.015164 \n", "L 93.823129 87.523559 \n", "L 94.435838 88.972373 \n", "L 95.048637 90.358855 \n", "L 95.661645 91.681071 \n", "L 96.274975 92.937855 \n", "L 96.888726 94.128727 \n", "L 97.502993 95.253861 \n", "L 98.117863 96.314014 \n", "L 98.733408 97.310443 \n", "L 99.349697 98.244858 \n", "L 99.966786 99.119351 \n", "L 100.584721 99.936317 \n", "L 101.20354 100.698409 \n", "L 101.823275 101.408471 \n", "L 102.443942 102.06948 \n", "L 103.065559 102.684502 \n", "L 103.688133 103.256642 \n", "L 104.311662 103.788998 \n", "L 104.936144 104.284639 \n", "L 105.56157 104.746559 \n", "L 106.187929 105.17766 \n", "L 106.815202 105.580725 \n", "L 107.443372 105.958409 \n", "L 108.072422 106.313218 \n", "L 108.702326 106.647505 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 48.966938 87.237723 \n", "L 49.547371 87.275814 \n", "L 50.127947 87.293662 \n", "L 50.70864 87.288933 \n", "L 51.289432 87.259136 \n", "L 51.870304 87.201634 \n", "L 52.451236 87.113653 \n", "L 53.032216 86.992299 \n", "L 53.613232 86.834583 \n", "L 54.194279 86.637437 \n", "L 54.775352 86.397754 \n", "L 55.356456 86.112419 \n", "L 55.937604 85.778346 \n", "L 56.518808 85.392532 \n", "L 57.100096 84.952096 \n", "L 57.681502 84.454345 \n", "L 58.263066 83.896828 \n", "L 58.84484 83.277393 \n", "L 59.426888 82.594259 \n", "L 60.009276 81.846085 \n", "L 60.592086 81.032025 \n", "L 61.175411 80.151808 \n", "L 61.759344 79.205801 \n", "L 62.343995 78.195058 \n", "L 62.929479 77.121392 \n", "L 63.515912 75.987422 \n", "L 64.10342 74.79661 \n", "L 64.692133 73.553297 \n", "L 65.282178 72.262726 \n", "L 65.873682 70.931047 \n", "L 66.466772 69.565309 \n", "L 67.061565 68.173446 \n", "L 67.658175 66.764231 \n", "L 68.256702 65.347219 \n", "L 68.857235 63.932684 \n", "L 69.459849 62.531509 \n", "L 70.064599 61.155098 \n", "L 70.671525 59.815227 \n", "L 71.280641 58.523929 \n", "L 71.891943 57.293321 \n", "L 72.505401 56.135451 \n", "L 73.120964 55.06212 \n", "L 73.738554 54.084715 \n", "L 74.358071 53.214019 \n", "L 74.979393 52.460054 \n", "L 75.602374 51.831909 \n", "L 76.226852 51.33758 \n", "L 76.852647 50.983848 \n", "L 77.479563 50.776138 \n", "L 78.107396 50.718446 \n", "L 78.735932 50.81325 \n", "L 79.364953 51.061466 \n", "L 79.994241 51.462444 \n", "L 80.623584 52.013967 \n", "L 81.252773 52.712289 \n", "L 81.881612 53.552223 \n", "L 82.509918 54.527205 \n", "L 83.137526 55.629436 \n", "L 83.764288 56.849998 \n", "L 84.390078 58.179013 \n", "L 85.014793 59.605812 \n", "L 85.638354 61.119093 \n", "L 86.260705 62.70711 \n", "L 86.881815 64.357841 \n", "L 87.501676 66.059164 \n", "L 88.120303 67.799018 \n", "L 88.737734 69.56557 \n", "L 89.354024 71.34734 \n", "L 89.969248 73.13335 \n", "L 90.583497 74.913212 \n", "L 91.19687 76.677244 \n", "L 91.809486 78.416537 \n", "L 92.421463 80.123012 \n", "L 93.032933 81.789472 \n", "L 93.644025 83.409616 \n", "L 94.254872 84.97805 \n", "L 94.865607 86.490286 \n", "L 95.476361 87.942721 \n", "L 96.087255 89.332598 \n", "L 96.698409 90.657978 \n", "L 97.309936 91.917688 \n", "L 97.921937 93.11125 \n", "L 98.534507 94.23884 \n", "L 99.147733 95.301215 \n", "L 99.761685 96.29964 \n", "L 100.376431 97.235832 \n", "L 100.992026 98.111889 \n", "L 101.608512 98.930217 \n", "L 102.225927 99.693477 \n", "L 102.844299 100.404523 \n", "L 103.463644 101.066344 \n", "L 104.083975 101.682016 \n", "L 104.705298 102.254655 \n", "L 105.327608 102.787371 \n", "L 105.9509 103.283243 \n", "L 106.575163 103.745274 \n", "L 107.200383 104.176379 \n", "L 107.826539 104.57935 \n", "L 108.453612 104.956849 \n", "L 109.081583 105.311393 \n", "L 109.710424 105.645342 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 50.056342 86.340245 \n", "L 50.636191 86.38109 \n", "L 51.216206 86.402204 \n", "L 51.796364 86.40131 \n", "L 52.376649 86.375982 \n", "L 52.957046 86.323647 \n", "L 53.537537 86.241604 \n", "L 54.118112 86.127032 \n", "L 54.698763 85.977017 \n", "L 55.279486 85.788571 \n", "L 55.86028 85.558668 \n", "L 56.441153 85.284271 \n", "L 57.022118 84.962376 \n", "L 57.60319 84.590057 \n", "L 58.1844 84.164512 \n", "L 58.765782 83.683118 \n", "L 59.347377 83.143491 \n", "L 59.929237 82.543538 \n", "L 60.511427 81.881531 \n", "L 61.094011 81.156168 \n", "L 61.677072 80.366633 \n", "L 62.260697 79.51267 \n", "L 62.844979 78.594641 \n", "L 63.430022 77.613586 \n", "L 64.015939 76.571279 \n", "L 64.602839 75.470282 \n", "L 65.190843 74.313977 \n", "L 65.780073 73.106603 \n", "L 66.370648 71.853273 \n", "L 66.962687 70.55999 \n", "L 67.556308 69.233626 \n", "L 68.151618 67.881913 \n", "L 68.748722 66.513401 \n", "L 69.347708 65.137404 \n", "L 69.948654 63.76392 \n", "L 70.551624 62.40355 \n", "L 71.156664 61.06739 \n", "L 71.763801 59.766909 \n", "L 72.373041 58.513809 \n", "L 72.984368 57.319882 \n", "L 73.597745 56.196839 \n", "L 74.213111 55.156163 \n", "L 74.830383 54.208913 \n", "L 75.449454 53.365586 \n", "L 76.070197 52.635913 \n", "L 76.692464 52.028728 \n", "L 77.316091 51.551805 \n", "L 77.940896 51.211725 \n", "L 78.566686 51.013767 \n", "L 79.193259 50.961805 \n", "L 79.820406 51.058249 \n", "L 80.447913 51.303989 \n", "L 81.075571 51.698393 \n", "L 81.703173 52.239304 \n", "L 82.330521 52.923091 \n", "L 82.957429 53.744705 \n", "L 83.583724 54.697777 \n", "L 84.20925 55.774723 \n", "L 84.833873 56.966877 \n", "L 85.457478 58.264645 \n", "L 86.079972 59.657644 \n", "L 86.701287 61.134896 \n", "L 87.321379 62.684974 \n", "L 87.940227 64.29619 \n", "L 88.557833 65.95675 \n", "L 89.174221 67.654923 \n", "L 89.789437 69.379187 \n", "L 90.403542 71.118373 \n", "L 91.016621 72.861787 \n", "L 91.628767 74.599321 \n", "L 92.240087 76.321537 \n", "L 92.850701 78.019755 \n", "L 93.460733 79.686101 \n", "L 94.070313 81.31356 \n", "L 94.679576 82.895981 \n", "L 95.288653 84.428105 \n", "L 95.897677 85.905551 \n", "L 96.506778 87.324799 \n", "L 97.116078 88.683152 \n", "L 97.725693 89.978713 \n", "L 98.335736 91.210329 \n", "L 98.946304 92.377526 \n", "L 99.55749 93.480469 \n", "L 100.169378 94.519889 \n", "L 100.782036 95.497011 \n", "L 101.395526 96.413501 \n", "L 102.009904 97.271401 \n", "L 102.625206 98.073049 \n", "L 103.241468 98.821036 \n", "L 103.858716 99.518142 \n", "L 104.476961 100.167275 \n", "L 105.096214 100.771432 \n", "L 105.71648 101.333648 \n", "L 106.337749 101.856953 \n", "L 106.960016 102.344346 \n", "L 107.583266 102.798756 \n", "L 108.207485 103.223022 \n", "L 108.83265 103.619863 \n", "L 109.458739 103.991877 \n", "L 110.085733 104.341517 \n", "L 110.713602 104.671081 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 51.141688 85.467834 \n", "L 51.721014 85.515104 \n", "L 52.300536 85.543694 \n", "L 52.880234 85.551446 \n", "L 53.460096 85.536063 \n", "L 54.040107 85.495111 \n", "L 54.620255 85.426031 \n", "L 55.20053 85.326155 \n", "L 55.780929 85.192724 \n", "L 56.361452 85.022913 \n", "L 56.942097 84.813856 \n", "L 57.522875 84.562684 \n", "L 58.103801 84.266557 \n", "L 58.684893 83.922709 \n", "L 59.266181 83.528493 \n", "L 59.847699 83.081435 \n", "L 60.429488 82.579285 \n", "L 61.011601 82.020074 \n", "L 61.594097 81.402174 \n", "L 62.177041 80.724368 \n", "L 62.760509 79.985895 \n", "L 63.344587 79.186527 \n", "L 63.92936 78.326627 \n", "L 64.514928 77.407193 \n", "L 65.101393 76.429919 \n", "L 65.688859 75.397249 \n", "L 66.277436 74.312397 \n", "L 66.867237 73.179395 \n", "L 67.45837 72.003091 \n", "L 68.050942 70.789179 \n", "L 68.64506 69.54417 \n", "L 69.240818 68.275386 \n", "L 69.838308 66.990918 \n", "L 70.437606 65.699568 \n", "L 71.038779 64.410795 \n", "L 71.641877 63.134613 \n", "L 72.246935 61.8815 \n", "L 72.85397 60.662279 \n", "L 73.462977 59.487987 \n", "L 74.073934 58.36975 \n", "L 74.686794 57.318607 \n", "L 75.301492 56.345375 \n", "L 75.91794 55.460474 \n", "L 76.53603 54.673784 \n", "L 77.155634 53.994468 \n", "L 77.776606 53.430842 \n", "L 78.398785 52.990213 \n", "L 79.021995 52.678776 \n", "L 79.64605 52.501498 \n", "L 80.270755 52.46202 \n", "L 80.895915 52.562604 \n", "L 81.521324 52.804095 \n", "L 82.146788 53.185891 \n", "L 82.772113 53.705965 \n", "L 83.397115 54.360906 \n", "L 84.021622 55.145963 \n", "L 84.645478 56.055139 \n", "L 85.26854 57.081307 \n", "L 85.890689 58.216307 \n", "L 86.511822 59.451108 \n", "L 87.131862 60.775936 \n", "L 87.750752 62.180449 \n", "L 88.368459 63.653881 \n", "L 88.984971 65.185218 \n", "L 89.600301 66.763333 \n", "L 90.214477 68.377161 \n", "L 90.827553 70.015834 \n", "L 91.439596 71.668802 \n", "L 92.050691 73.325965 \n", "L 92.660936 74.977762 \n", "L 93.270438 76.615277 \n", "L 93.879318 78.230294 \n", "L 94.487696 79.815359 \n", "L 95.095704 81.363826 \n", "L 95.70347 82.869862 \n", "L 96.311125 84.328482 \n", "L 96.918797 85.735518 \n", "L 97.526612 87.087628 \n", "L 98.134686 88.38224 \n", "L 98.743132 89.617544 \n", "L 99.352056 90.792432 \n", "L 99.961551 91.906439 \n", "L 100.571705 92.959705 \n", "L 101.182597 93.95291 \n", "L 101.794289 94.887198 \n", "L 102.406842 95.764139 \n", "L 103.020303 96.585652 \n", "L 103.634708 97.353945 \n", "L 104.250088 98.071461 \n", "L 104.866464 98.740824 \n", "L 105.483846 99.364784 \n", "L 106.102243 99.946175 \n", "L 106.721654 100.487864 \n", "L 107.342071 100.99272 \n", "L 107.963485 101.463579 \n", "L 108.585881 101.903212 \n", "L 109.209242 102.314305 \n", "L 109.833545 102.699436 \n", "L 110.458768 103.06106 \n", "L 111.084889 103.4015 \n", "L 111.71188 103.722934 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 52.222881 84.617044 \n", "L 52.801725 84.673822 \n", "L 53.3808 84.71342 \n", "L 53.960089 84.733855 \n", "L 54.53958 84.733014 \n", "L 55.119266 84.708658 \n", "L 55.699133 84.658434 \n", "L 56.279178 84.579891 \n", "L 56.859398 84.470492 \n", "L 57.439795 84.327641 \n", "L 58.02037 84.148708 \n", "L 58.601136 83.931056 \n", "L 59.18211 83.67208 \n", "L 59.763308 83.369243 \n", "L 60.34476 83.02012 \n", "L 60.926502 82.622443 \n", "L 61.508571 82.174156 \n", "L 62.091017 81.673461 \n", "L 62.673897 81.118876 \n", "L 63.257271 80.509298 \n", "L 63.841211 79.844047 \n", "L 64.425793 79.12293 \n", "L 65.011098 78.346302 \n", "L 65.597215 77.515102 \n", "L 66.184238 76.630911 \n", "L 66.772258 75.695995 \n", "L 67.361377 74.713331 \n", "L 67.951693 73.686643 \n", "L 68.543302 72.620405 \n", "L 69.136298 71.519866 \n", "L 69.730773 70.391019 \n", "L 70.32681 69.240601 \n", "L 70.924484 68.076041 \n", "L 71.52386 66.905433 \n", "L 72.124989 65.737449 \n", "L 72.727913 64.581268 \n", "L 73.332653 63.446497 \n", "L 73.939217 62.343042 \n", "L 74.547593 61.281008 \n", "L 75.157749 60.270564 \n", "L 75.769637 59.321799 \n", "L 76.383187 58.444599 \n", "L 76.998311 57.648475 \n", "L 77.614903 56.942442 \n", "L 78.232839 56.334863 \n", "L 78.851979 55.833314 \n", "L 79.472171 55.444462 \n", "L 80.093248 55.173951 \n", "L 80.715038 55.026301 \n", "L 81.33736 55.004833 \n", "L 81.960033 55.111609 \n", "L 82.582869 55.347392 \n", "L 83.20569 55.711638 \n", "L 83.828322 56.202502 \n", "L 84.450599 56.816869 \n", "L 85.072368 57.550416 \n", "L 85.693487 58.39768 \n", "L 86.313836 59.352157 \n", "L 86.933307 60.406415 \n", "L 87.551816 61.55221 \n", "L 88.169298 62.780625 \n", "L 88.785708 64.082222 \n", "L 89.401023 65.447161 \n", "L 90.015241 66.865378 \n", "L 90.628381 68.326703 \n", "L 91.240477 69.821009 \n", "L 91.851586 71.338348 \n", "L 92.461777 72.86905 \n", "L 93.071134 74.403858 \n", "L 93.679755 75.934008 \n", "L 94.287743 77.451303 \n", "L 94.895215 78.948201 \n", "L 95.502287 80.417843 \n", "L 96.109085 81.854116 \n", "L 96.715731 83.251643 \n", "L 97.322348 84.605823 \n", "L 97.929056 85.91281 \n", "L 98.535975 87.169508 \n", "L 99.143211 88.373536 \n", "L 99.750871 89.523203 \n", "L 100.359054 90.617472 \n", "L 100.967844 91.655893 \n", "L 101.577323 92.638577 \n", "L 102.187563 93.56613 \n", "L 102.79862 94.439588 \n", "L 103.410549 95.260378 \n", "L 104.023392 96.030254 \n", "L 104.637181 96.751232 \n", "L 105.251942 97.42555 \n", "L 105.867694 98.055613 \n", "L 106.484444 98.643943 \n", "L 107.102196 99.193139 \n", "L 107.72095 99.705835 \n", "L 108.340695 100.184665 \n", "L 108.96142 100.632234 \n", "L 109.583111 101.05109 \n", "L 110.205751 101.443702 \n", "L 110.829315 101.812439 \n", "L 111.453782 102.159559 \n", "L 112.07913 102.487199 \n", "L 112.705332 102.797362 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 53.299812 83.783189 \n", "L 53.878195 83.851751 \n", "L 54.456846 83.904968 \n", "L 55.035748 83.941068 \n", "L 55.614895 83.958162 \n", "L 56.194281 83.954251 \n", "L 56.773896 83.927234 \n", "L 57.353738 83.87492 \n", "L 57.933807 83.795048 \n", "L 58.514107 83.685298 \n", "L 59.094642 83.543326 \n", "L 59.675423 83.36678 \n", "L 60.256468 83.153339 \n", "L 60.837795 82.900744 \n", "L 61.419431 82.606837 \n", "L 62.00141 82.269604 \n", "L 62.583767 81.887218 \n", "L 63.166549 81.458089 \n", "L 63.749807 80.980909 \n", "L 64.333595 80.45471 \n", "L 64.917977 79.878904 \n", "L 65.503025 79.25334 \n", "L 66.088807 78.578358 \n", "L 66.675403 77.854818 \n", "L 67.262896 77.084157 \n", "L 67.851366 76.268421 \n", "L 68.440901 75.410294 \n", "L 69.031585 74.513115 \n", "L 69.623501 73.580905 \n", "L 70.216728 72.618358 \n", "L 70.811344 71.630841 \n", "L 71.407417 70.624369 \n", "L 72.00501 69.605578 \n", "L 72.604173 68.581682 \n", "L 73.204945 67.560407 \n", "L 73.807356 66.549926 \n", "L 74.411418 65.558782 \n", "L 75.017131 64.595779 \n", "L 75.624475 63.669887 \n", "L 76.233415 62.790131 \n", "L 76.843902 61.965458 \n", "L 77.455864 61.204624 \n", "L 78.069218 60.516056 \n", "L 78.683863 59.907728 \n", "L 79.299681 59.387039 \n", "L 79.916544 58.960689 \n", "L 80.53431 58.63457 \n", "L 81.152831 58.413667 \n", "L 81.771948 58.301977 \n", "L 82.391499 58.302425 \n", "L 83.011321 58.416837 \n", "L 83.631249 58.645882 \n", "L 84.251125 58.989084 \n", "L 84.870795 59.444807 \n", "L 85.490114 60.010308 \n", "L 86.10895 60.681765 \n", "L 86.727182 61.45435 \n", "L 87.344705 62.322317 \n", "L 87.961431 63.279092 \n", "L 88.57729 64.317383 \n", "L 89.19223 65.429303 \n", "L 89.806218 66.606489 \n", "L 90.41924 67.840228 \n", "L 91.031302 69.121598 \n", "L 91.642426 70.441573 \n", "L 92.25265 71.791164 \n", "L 92.862031 73.161531 \n", "L 93.470636 74.544079 \n", "L 94.078549 75.930566 \n", "L 94.68586 77.313187 \n", "L 95.292668 78.684634 \n", "L 95.899082 80.038175 \n", "L 96.50521 81.367687 \n", "L 97.111168 82.667696 \n", "L 97.717069 83.933395 \n", "L 98.323025 85.160653 \n", "L 98.929147 86.346019 \n", "L 99.535544 87.486705 \n", "L 100.142312 88.580561 \n", "L 100.749548 89.626054 \n", "L 101.357341 90.622235 \n", "L 101.965767 91.568681 \n", "L 102.574898 92.465467 \n", "L 103.184799 93.313117 \n", "L 103.79552 94.112537 \n", "L 104.407108 94.864987 \n", "L 105.019602 95.572016 \n", "L 105.633026 96.235416 \n", "L 106.247403 96.857173 \n", "L 106.862749 97.439428 \n", "L 107.479068 97.984426 \n", "L 108.096362 98.494481 \n", "L 108.71463 98.971944 \n", "L 109.33386 99.419161 \n", "L 109.95404 99.83846 \n", "L 110.575157 100.232113 \n", "L 111.197192 100.602326 \n", "L 111.820122 100.951214 \n", "L 112.443928 101.280794 \n", "L 113.068588 101.592975 \n", "L 113.694076 101.889547 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 54.372375 82.960939 \n", "L 54.9503 83.042649 \n", "L 55.528526 83.111047 \n", "L 56.107041 83.164595 \n", "L 56.685842 83.201653 \n", "L 57.264924 83.220487 \n", "L 57.844281 83.219274 \n", "L 58.423912 83.196114 \n", "L 59.003819 83.149044 \n", "L 59.584008 83.076055 \n", "L 60.164483 82.975116 \n", "L 60.745256 82.844189 \n", "L 61.326344 82.681266 \n", "L 61.907765 82.484394 \n", "L 62.489543 82.251707 \n", "L 63.071711 81.981469 \n", "L 63.654298 81.672106 \n", "L 64.237348 81.32225 \n", "L 64.820906 80.930784 \n", "L 65.405018 80.496881 \n", "L 65.989743 80.020053 \n", "L 66.575141 79.500189 \n", "L 67.161271 78.937604 \n", "L 67.748203 78.333066 \n", "L 68.336008 77.687845 \n", "L 68.924752 77.003738 \n", "L 69.51451 76.283095 \n", "L 70.105353 75.52883 \n", "L 70.69735 74.744449 \n", "L 71.290565 73.934042 \n", "L 71.885062 73.102266 \n", "L 72.480895 72.254347 \n", "L 73.078114 71.396039 \n", "L 73.676756 70.533588 \n", "L 74.27685 69.67368 \n", "L 74.878416 68.82338 \n", "L 75.481458 67.990067 \n", "L 76.085968 67.181336 \n", "L 76.691924 66.404919 \n", "L 77.299289 65.668593 \n", "L 77.908012 64.98006 \n", "L 78.518028 64.346844 \n", "L 79.129258 63.776193 \n", "L 79.741608 63.274956 \n", "L 80.354973 62.849488 \n", "L 80.969237 62.505535 \n", "L 81.584275 62.248147 \n", "L 82.199953 62.081604 \n", "L 82.816134 62.009324 \n", "L 83.432677 62.033822 \n", "L 84.049439 62.156654 \n", "L 84.666278 62.3784 \n", "L 85.283058 62.698646 \n", "L 85.899648 63.115996 \n", "L 86.515925 63.628092 \n", "L 87.131778 64.231664 \n", "L 87.747105 64.922567 \n", "L 88.361821 65.695878 \n", "L 88.975854 66.545956 \n", "L 89.589148 67.466541 \n", "L 90.201663 68.450859 \n", "L 90.813377 69.491716 \n", "L 91.424283 70.581629 \n", "L 92.034391 71.712915 \n", "L 92.643727 72.877801 \n", "L 93.25233 74.068538 \n", "L 93.860254 75.277502 \n", "L 94.467563 76.497269 \n", "L 95.074334 77.72072 \n", "L 95.680651 78.941097 \n", "L 96.286604 80.152078 \n", "L 96.892291 81.347826 \n", "L 97.497811 82.523023 \n", "L 98.103267 83.672919 \n", "L 98.70876 84.793328 \n", "L 99.31439 85.880649 \n", "L 99.920256 86.931872 \n", "L 100.526454 87.944558 \n", "L 101.13307 88.916818 \n", "L 101.740187 89.847301 \n", "L 102.347885 90.73516 \n", "L 102.956229 91.580008 \n", "L 103.565283 92.381891 \n", "L 104.175103 93.141241 \n", "L 104.78573 93.85883 \n", "L 105.397207 94.535735 \n", "L 106.009564 95.173286 \n", "L 106.622823 95.773026 \n", "L 107.237003 96.336669 \n", "L 107.852115 96.866065 \n", "L 108.468162 97.363152 \n", "L 109.085145 97.829935 \n", "L 109.70306 98.26845 \n", "L 110.321896 98.680729 \n", "L 110.941642 99.06879 \n", "L 111.562283 99.434602 \n", "L 112.183803 99.780079 \n", "L 112.806181 100.107056 \n", "L 113.429398 100.417284 \n", "L 114.053434 100.71242 \n", "L 114.678266 100.994017 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 55.440486 82.14496 \n", "L 56.017936 82.240267 \n", "L 56.595719 82.324359 \n", "L 57.173826 82.395939 \n", "L 57.752255 82.453626 \n", "L 58.331005 82.495956 \n", "L 58.91007 82.521393 \n", "L 59.489452 82.528335 \n", "L 60.069154 82.515129 \n", "L 60.649182 82.480084 \n", "L 61.229541 82.421488 \n", "L 61.810243 82.337629 \n", "L 62.391304 82.226816 \n", "L 62.97274 82.08741 \n", "L 63.554573 81.917845 \n", "L 64.136831 81.716665 \n", "L 64.719542 81.482553 \n", "L 65.302741 81.214369 \n", "L 65.88647 80.911183 \n", "L 66.470767 80.572313 \n", "L 67.055681 80.197365 \n", "L 67.641264 79.78626 \n", "L 68.227566 79.339284 \n", "L 68.814644 78.857104 \n", "L 69.402557 78.340807 \n", "L 69.991361 77.79193 \n", "L 70.581115 77.212469 \n", "L 71.171879 76.6049 \n", "L 71.763707 75.972192 \n", "L 72.356651 75.3178 \n", "L 72.95076 74.64566 \n", "L 73.546077 73.960178 \n", "L 74.142639 73.2662 \n", "L 74.740474 72.568983 \n", "L 75.3396 71.874148 \n", "L 75.940029 71.187628 \n", "L 76.541759 70.515609 \n", "L 77.14478 69.86446 \n", "L 77.749065 69.24066 \n", "L 78.35458 68.650708 \n", "L 78.961276 68.101047 \n", "L 79.569093 67.597965 \n", "L 80.17796 67.147511 \n", "L 80.787794 66.7554 \n", "L 81.398502 66.426932 \n", "L 82.009985 66.166889 \n", "L 82.622133 65.979486 \n", "L 83.234832 65.868284 \n", "L 83.847966 65.836126 \n", "L 84.461413 65.885106 \n", "L 85.075055 66.01652 \n", "L 85.688772 66.230849 \n", "L 86.30245 66.527748 \n", "L 86.915981 66.906055 \n", "L 87.529264 67.363807 \n", "L 88.142208 67.898283 \n", "L 88.75473 68.506036 \n", "L 89.366763 69.182954 \n", "L 89.978249 69.924346 \n", "L 90.589146 70.724988 \n", "L 91.199424 71.579229 \n", "L 91.80907 72.481068 \n", "L 92.418082 73.42425 \n", "L 93.026474 74.402351 \n", "L 93.634273 75.408872 \n", "L 94.241514 76.437326 \n", "L 94.84825 77.481319 \n", "L 95.454537 78.53463 \n", "L 96.060447 79.591277 \n", "L 96.666051 80.645586 \n", "L 97.271432 81.692235 \n", "L 97.876674 82.726308 \n", "L 98.481865 83.743324 \n", "L 99.087094 84.73927 \n", "L 99.692449 85.710611 \n", "L 100.298017 86.654297 \n", "L 100.903882 87.567773 \n", "L 101.510129 88.448968 \n", "L 102.116828 89.296268 \n", "L 102.724054 90.108517 \n", "L 103.331873 90.884983 \n", "L 103.94034 91.625318 \n", "L 104.54951 92.329546 \n", "L 105.15943 92.998015 \n", "L 105.770135 93.631359 \n", "L 106.381658 94.230473 \n", "L 106.994028 94.796468 \n", "L 107.607259 95.330633 \n", "L 108.221367 95.834404 \n", "L 108.836361 96.309334 \n", "L 109.452242 96.757052 \n", "L 110.069009 97.179241 \n", "L 110.686659 97.577616 \n", "L 111.30518 97.953887 \n", "L 111.924562 98.309751 \n", "L 112.544792 98.646871 \n", "L 113.165856 98.966857 \n", "L 113.787732 99.271256 \n", "L 114.410406 99.561545 \n", "L 115.033859 99.839121 \n", "L 115.65807 100.105295 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 56.504084 81.330475 \n", "L 57.081031 81.439007 \n", "L 57.658339 81.538368 \n", "L 58.236001 81.627493 \n", "L 58.814018 81.705251 \n", "L 59.392388 81.770442 \n", "L 59.971108 81.821809 \n", "L 60.550182 81.858039 \n", "L 61.129613 81.87778 \n", "L 61.709407 81.879647 \n", "L 62.289569 81.862241 \n", "L 62.87011 81.824161 \n", "L 63.451046 81.764027 \n", "L 64.032388 81.6805 \n", "L 64.614159 81.572303 \n", "L 65.196381 81.43825 \n", "L 65.779077 81.277272 \n", "L 66.362279 81.088441 \n", "L 66.94602 80.871007 \n", "L 67.530332 80.624427 \n", "L 68.115256 80.348384 \n", "L 68.700836 80.042833 \n", "L 69.287109 79.70802 \n", "L 69.874125 79.344506 \n", "L 70.46193 78.9532 \n", "L 71.050568 78.535372 \n", "L 71.640088 78.092673 \n", "L 72.230535 77.627144 \n", "L 72.821953 77.141226 \n", "L 73.41438 76.637759 \n", "L 74.007857 76.119972 \n", "L 74.602412 75.591474 \n", "L 75.198074 75.056234 \n", "L 75.794862 74.518548 \n", "L 76.392787 73.983008 \n", "L 76.991854 73.454456 \n", "L 77.592058 72.937935 \n", "L 78.193386 72.43863 \n", "L 78.795812 71.961814 \n", "L 79.399304 71.512777 \n", "L 80.003817 71.096749 \n", "L 80.609299 70.71884 \n", "L 81.215688 70.383955 \n", "L 81.822912 70.096727 \n", "L 82.430894 69.861452 \n", "L 83.039547 69.682008 \n", "L 83.648782 69.561804 \n", "L 84.258503 69.50372 \n", "L 84.868613 69.510062 \n", "L 85.479014 69.582521 \n", "L 86.089607 69.722142 \n", "L 86.700295 69.929318 \n", "L 87.310987 70.203767 \n", "L 87.921594 70.54455 \n", "L 88.532037 70.950083 \n", "L 89.142241 71.418157 \n", "L 89.752144 71.945985 \n", "L 90.361692 72.53024 \n", "L 90.970842 73.167121 \n", "L 91.579561 73.852392 \n", "L 92.187831 74.581472 \n", "L 92.795642 75.349496 \n", "L 93.402999 76.151381 \n", "L 94.009916 76.981914 \n", "L 94.616418 77.835806 \n", "L 95.222538 78.707776 \n", "L 95.828324 79.592625 \n", "L 96.433826 80.485276 \n", "L 97.039104 81.380854 \n", "L 97.644222 82.27472 \n", "L 98.249249 83.162529 \n", "L 98.854258 84.040255 \n", "L 99.459322 84.904228 \n", "L 100.064519 85.751157 \n", "L 100.669922 86.578137 \n", "L 101.275603 87.382662 \n", "L 101.881634 88.162627 \n", "L 102.488085 88.916319 \n", "L 103.095016 89.642404 \n", "L 103.702487 90.339919 \n", "L 104.310554 91.008249 \n", "L 104.919263 91.647097 \n", "L 105.528658 92.256464 \n", "L 106.138778 92.836626 \n", "L 106.749652 93.388089 \n", "L 107.361306 93.911575 \n", "L 107.973763 94.407986 \n", "L 108.587035 94.878369 \n", "L 109.201133 95.323893 \n", "L 109.816065 95.745825 \n", "L 110.431829 96.145493 \n", "L 111.048426 96.524275 \n", "L 111.66585 96.88357 \n", "L 112.284092 97.224778 \n", "L 112.903143 97.549287 \n", "L 113.522989 97.858458 \n", "L 114.14362 98.153611 \n", "L 114.765016 98.436012 \n", "L 115.387165 98.706871 \n", "L 116.010052 98.967333 \n", "L 116.633656 99.218471 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 57.563142 80.513664 \n", "L 58.13955 80.6344 \n", "L 58.716343 80.747855 \n", "L 59.293515 80.853183 \n", "L 59.871068 80.949481 \n", "L 60.449002 81.035796 \n", "L 61.027314 81.111125 \n", "L 61.606009 81.174425 \n", "L 62.18509 81.224621 \n", "L 62.764564 81.260611 \n", "L 63.344434 81.281282 \n", "L 63.924712 81.285522 \n", "L 64.50541 81.272236 \n", "L 65.086539 81.240359 \n", "L 65.668117 81.188882 \n", "L 66.250163 81.116864 \n", "L 66.832696 81.023458 \n", "L 67.415742 80.907937 \n", "L 67.999329 80.76971 \n", "L 68.583481 80.608349 \n", "L 69.168233 80.423617 \n", "L 69.753617 80.215486 \n", "L 70.339666 79.984162 \n", "L 70.926417 79.730104 \n", "L 71.513907 79.454045 \n", "L 72.10217 79.157008 \n", "L 72.691243 78.840318 \n", "L 73.281162 78.505606 \n", "L 73.871959 78.154825 \n", "L 74.463664 77.790241 \n", "L 75.056306 77.414428 \n", "L 75.649905 77.03026 \n", "L 76.244481 76.640891 \n", "L 76.840048 76.249738 \n", "L 77.43661 75.860445 \n", "L 78.034169 75.476852 \n", "L 78.632717 75.102953 \n", "L 79.23224 74.742859 \n", "L 79.832716 74.400739 \n", "L 80.434114 74.080776 \n", "L 81.036397 73.787099 \n", "L 81.639518 73.523744 \n", "L 82.243427 73.294584 \n", "L 82.848065 73.10327 \n", "L 83.453366 72.953188 \n", "L 84.059261 72.847392 \n", "L 84.665676 72.788569 \n", "L 85.272534 72.778986 \n", "L 85.879758 72.820454 \n", "L 86.487268 72.914306 \n", "L 87.094987 73.061366 \n", "L 87.702837 73.261939 \n", "L 88.310745 73.515805 \n", "L 88.918645 73.822227 \n", "L 89.526472 74.179953 \n", "L 90.134171 74.587249 \n", "L 90.741693 75.041923 \n", "L 91.348998 75.541352 \n", "L 91.956054 76.082539 \n", "L 92.562838 76.662149 \n", "L 93.169337 77.276574 \n", "L 93.775549 77.921968 \n", "L 94.381478 78.594323 \n", "L 94.987139 79.289517 \n", "L 95.592556 80.003374 \n", "L 96.197758 80.731718 \n", "L 96.802785 81.470426 \n", "L 97.40768 82.21548 \n", "L 98.012494 82.963013 \n", "L 98.617281 83.709345 \n", "L 99.222098 84.451022 \n", "L 99.827005 85.184844 \n", "L 100.432063 85.90789 \n", "L 101.037335 86.617536 \n", "L 101.642882 87.311467 \n", "L 102.248762 87.987678 \n", "L 102.855035 88.644486 \n", "L 103.461756 89.280517 \n", "L 104.068974 89.8947 \n", "L 104.676739 90.486257 \n", "L 105.285096 91.054687 \n", "L 105.89408 91.599741 \n", "L 106.503727 92.121413 \n", "L 107.11407 92.619911 \n", "L 107.725128 93.095631 \n", "L 108.336925 93.549138 \n", "L 108.949479 93.981143 \n", "L 109.562796 94.392474 \n", "L 110.176887 94.784058 \n", "L 110.791757 95.156897 \n", "L 111.407403 95.512045 \n", "L 112.023826 95.850596 \n", "L 112.641021 96.173662 \n", "L 113.258979 96.482356 \n", "L 113.877691 96.777781 \n", "L 114.497148 97.061021 \n", "L 115.117341 97.333124 \n", "L 115.738252 97.595099 \n", "L 116.35987 97.847909 \n", "L 116.982185 98.092466 \n", "L 117.605179 98.329625 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 58.617662 79.691892 \n", "L 59.193493 79.823359 \n", "L 59.769728 79.94922 \n", "L 60.346362 80.068819 \n", "L 60.923396 80.181457 \n", "L 61.500833 80.286398 \n", "L 62.078671 80.382866 \n", "L 62.656913 80.470054 \n", "L 63.235563 80.547131 \n", "L 63.814628 80.613246 \n", "L 64.394111 80.667539 \n", "L 64.974021 80.709151 \n", "L 65.554369 80.737237 \n", "L 66.135164 80.750976 \n", "L 66.71642 80.749589 \n", "L 67.298154 80.732353 \n", "L 67.88038 80.698618 \n", "L 68.463118 80.647823 \n", "L 69.046392 80.579517 \n", "L 69.630221 80.493379 \n", "L 70.21463 80.389227 \n", "L 70.799646 80.267048 \n", "L 71.385294 80.12701 \n", "L 71.971601 79.969474 \n", "L 72.558597 79.795015 \n", "L 73.146306 79.604431 \n", "L 73.734757 79.398755 \n", "L 74.323977 79.179254 \n", "L 74.913989 78.947446 \n", "L 75.504815 78.705084 \n", "L 76.096475 78.454164 \n", "L 76.688983 78.196909 \n", "L 77.282354 77.935757 \n", "L 77.876595 77.673345 \n", "L 78.471707 77.412488 \n", "L 79.06769 77.156143 \n", "L 79.664534 76.907392 \n", "L 80.262227 76.669396 \n", "L 80.860749 76.445364 \n", "L 81.460074 76.238512 \n", "L 82.06017 76.052015 \n", "L 82.661 75.888969 \n", "L 83.262523 75.752347 \n", "L 83.86469 75.644952 \n", "L 84.467449 75.56938 \n", "L 85.070746 75.52797 \n", "L 85.674522 75.522781 \n", "L 86.278717 75.55555 \n", "L 86.88327 75.627665 \n", "L 87.488119 75.740144 \n", "L 88.093206 75.893619 \n", "L 88.69847 76.088323 \n", "L 89.303855 76.324089 \n", "L 89.90931 76.600349 \n", "L 90.514788 76.916149 \n", "L 91.120246 77.27016 \n", "L 91.725648 77.6607 \n", "L 92.330965 78.085761 \n", "L 92.936175 78.543049 \n", "L 93.541262 79.030005 \n", "L 94.146218 79.543858 \n", "L 94.751043 80.081661 \n", "L 95.355744 80.640334 \n", "L 95.960334 81.216713 \n", "L 96.564833 81.807585 \n", "L 97.169268 82.409737 \n", "L 97.773671 83.019997 \n", "L 98.378077 83.63527 \n", "L 98.982529 84.252574 \n", "L 99.58707 84.86907 \n", "L 100.191745 85.482091 \n", "L 100.796605 86.089165 \n", "L 101.401697 86.688032 \n", "L 102.007072 87.276661 \n", "L 102.612778 87.853257 \n", "L 103.218861 88.416265 \n", "L 103.825369 88.964377 \n", "L 104.432345 89.496524 \n", "L 105.039829 90.011869 \n", "L 105.647858 90.509803 \n", "L 106.256469 90.989932 \n", "L 106.865689 91.452056 \n", "L 107.475546 91.896163 \n", "L 108.086065 92.322408 \n", "L 108.697262 92.73109 \n", "L 109.309154 93.122642 \n", "L 109.921755 93.49761 \n", "L 110.535069 93.856628 \n", "L 111.149106 94.200411 \n", "L 111.763868 94.529731 \n", "L 112.379352 94.845401 \n", "L 112.995559 95.148265 \n", "L 113.612484 95.439183 \n", "L 114.23012 95.719012 \n", "L 114.848459 95.988607 \n", "L 115.467495 96.248804 \n", "L 116.087218 96.500416 \n", "L 116.707616 96.744221 \n", "L 117.328679 96.980965 \n", "L 117.950399 97.211353 \n", "L 118.572761 97.436047 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 59.667671 78.863726 \n", "L 60.242888 79.004211 \n", "L 60.818523 79.140511 \n", "L 61.394571 79.272133 \n", "L 61.971035 79.398552 \n", "L 62.547918 79.519214 \n", "L 63.125217 79.633536 \n", "L 63.702937 79.740913 \n", "L 64.28108 79.840719 \n", "L 64.859653 79.932315 \n", "L 65.438658 80.015055 \n", "L 66.018104 80.088296 \n", "L 66.597998 80.151403 \n", "L 67.178348 80.20376 \n", "L 67.759166 80.244783 \n", "L 68.340465 80.273932 \n", "L 68.922255 80.29072 \n", "L 69.504553 80.29473 \n", "L 70.087377 80.285625 \n", "L 70.670739 80.263165 \n", "L 71.254662 80.227223 \n", "L 71.839165 80.177789 \n", "L 72.424264 80.114996 \n", "L 73.009983 80.039119 \n", "L 73.596343 79.950597 \n", "L 74.183361 79.850032 \n", "L 74.771059 79.738206 \n", "L 75.359456 79.616077 \n", "L 75.948569 79.484786 \n", "L 76.538412 79.345656 \n", "L 77.129001 79.200188 \n", "L 77.720344 79.050052 \n", "L 78.312451 78.897081 \n", "L 78.905325 78.743255 \n", "L 79.498967 78.590682 \n", "L 80.093373 78.441583 \n", "L 80.688536 78.298264 \n", "L 81.284445 78.163095 \n", "L 81.881081 78.038473 \n", "L 82.478424 77.926805 \n", "L 83.076449 77.830465 \n", "L 83.675126 77.751764 \n", "L 84.274422 77.692925 \n", "L 84.8743 77.656038 \n", "L 85.474719 77.643039 \n", "L 86.075637 77.655675 \n", "L 86.677011 77.695481 \n", "L 87.278792 77.76375 \n", "L 87.880936 77.861518 \n", "L 88.483397 77.989543 \n", "L 89.086129 78.148296 \n", "L 89.689087 78.337951 \n", "L 90.292231 78.558382 \n", "L 90.895521 78.809168 \n", "L 91.498924 79.089596 \n", "L 92.102409 79.398676 \n", "L 92.70595 79.735155 \n", "L 93.309527 80.097537 \n", "L 93.913123 80.484107 \n", "L 94.516729 80.892959 \n", "L 95.120341 81.322026 \n", "L 95.72396 81.769104 \n", "L 96.327593 82.231895 \n", "L 96.931252 82.708033 \n", "L 97.534955 83.195115 \n", "L 98.138721 83.690739 \n", "L 98.742579 84.19253 \n", "L 99.346556 84.698169 \n", "L 99.950686 85.205421 \n", "L 100.555003 85.712157 \n", "L 101.159542 86.216376 \n", "L 101.764343 86.71622 \n", "L 102.369442 87.209992 \n", "L 102.97488 87.696163 \n", "L 103.580693 88.173382 \n", "L 104.186917 88.640477 \n", "L 104.793588 89.096461 \n", "L 105.400742 89.540527 \n", "L 106.008405 89.972041 \n", "L 106.61661 90.39054 \n", "L 107.225383 90.795723 \n", "L 107.834744 91.187436 \n", "L 108.444715 91.565665 \n", "L 109.055315 91.930523 \n", "L 109.666556 92.282229 \n", "L 110.27845 92.621108 \n", "L 110.891009 92.947565 \n", "L 111.504234 93.262076 \n", "L 112.118133 93.565174 \n", "L 112.732708 93.857439 \n", "L 113.347955 94.139481 \n", "L 113.963875 94.411932 \n", "L 114.580465 94.675437 \n", "L 115.197717 94.930641 \n", "L 115.815627 95.178186 \n", "L 116.434189 95.4187 \n", "L 117.053396 95.652794 \n", "L 117.673237 95.881052 \n", "L 118.293707 96.104035 \n", "L 118.914797 96.322275 \n", "L 119.536497 96.536267 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 60.713215 78.028811 \n", "L 61.287785 78.176542 \n", "L 61.862783 78.32125 \n", "L 62.438204 78.462573 \n", "L 63.014052 78.600129 \n", "L 63.590328 78.733513 \n", "L 64.167032 78.862299 \n", "L 64.744168 78.986044 \n", "L 65.321738 79.104293 \n", "L 65.899747 79.216579 \n", "L 66.478196 79.32243 \n", "L 67.057094 79.421377 \n", "L 67.636446 79.512957 \n", "L 68.216258 79.596721 \n", "L 68.796539 79.672245 \n", "L 69.3773 79.739133 \n", "L 69.958548 79.797033 \n", "L 70.540296 79.845641 \n", "L 71.122557 79.884713 \n", "L 71.705341 79.914078 \n", "L 72.288663 79.933642 \n", "L 72.872539 79.943404 \n", "L 73.456981 79.943462 \n", "L 74.042004 79.93402 \n", "L 74.627626 79.915401 \n", "L 75.213857 79.888049 \n", "L 75.800714 79.852537 \n", "L 76.38821 79.809567 \n", "L 76.976356 79.759975 \n", "L 77.565163 79.704728 \n", "L 78.154641 79.644924 \n", "L 78.744796 79.581782 \n", "L 79.335633 79.516642 \n", "L 79.927154 79.450947 \n", "L 80.519357 79.386236 \n", "L 81.112241 79.324125 \n", "L 81.705797 79.266298 \n", "L 82.300016 79.214476 \n", "L 82.894885 79.170407 \n", "L 83.490385 79.13584 \n", "L 84.086499 79.112501 \n", "L 84.683204 79.102073 \n", "L 85.280473 79.106166 \n", "L 85.87828 79.126301 \n", "L 86.476593 79.163884 \n", "L 87.075381 79.220182 \n", "L 87.674612 79.296309 \n", "L 88.274251 79.393203 \n", "L 88.874263 79.511616 \n", "L 89.474616 79.652098 \n", "L 90.075277 79.81499 \n", "L 90.676212 80.000418 \n", "L 91.277392 80.208291 \n", "L 91.87879 80.438304 \n", "L 92.480382 80.68994 \n", "L 93.082145 80.96248 \n", "L 93.684062 81.255014 \n", "L 94.286118 81.566459 \n", "L 94.888304 81.89557 \n", "L 95.490613 82.240961 \n", "L 96.093044 82.601132 \n", "L 96.695598 82.974482 \n", "L 97.298283 83.359343 \n", "L 97.901109 83.753991 \n", "L 98.50409 84.156681 \n", "L 99.107241 84.565663 \n", "L 99.710585 84.97921 \n", "L 100.314143 85.395633 \n", "L 100.917941 85.813301 \n", "L 101.522005 86.230665 \n", "L 102.126362 86.646263 \n", "L 102.731043 87.058738 \n", "L 103.336074 87.466853 \n", "L 103.941486 87.869489 \n", "L 104.547307 88.265659 \n", "L 105.153564 88.654504 \n", "L 105.760283 89.035301 \n", "L 106.367492 89.407458 \n", "L 106.975211 89.77051 \n", "L 107.583462 90.124115 \n", "L 108.192267 90.468051 \n", "L 108.801639 90.802202 \n", "L 109.411595 91.126557 \n", "L 110.02215 91.441194 \n", "L 110.633311 91.746273 \n", "L 111.245087 92.042027 \n", "L 111.857488 92.328752 \n", "L 112.470515 92.606792 \n", "L 113.08417 92.876537 \n", "L 113.698459 93.13841 \n", "L 114.313376 93.392854 \n", "L 114.928923 93.64033 \n", "L 115.545097 93.881311 \n", "L 116.161892 94.116265 \n", "L 116.779304 94.345661 \n", "L 117.397329 94.569959 \n", "L 118.015964 94.789604 \n", "L 118.635197 95.005024 \n", "L 119.255026 95.216628 \n", "L 119.875446 95.424804 \n", "L 120.496446 95.629915 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 61.754353 77.187635 \n", "L 62.328248 77.340929 \n", "L 62.902577 77.492114 \n", "L 63.477336 77.640933 \n", "L 64.052528 77.787114 \n", "L 64.628157 77.93037 \n", "L 65.204219 78.070399 \n", "L 65.78072 78.206886 \n", "L 66.357661 78.339508 \n", "L 66.935047 78.467935 \n", "L 67.512878 78.591831 \n", "L 68.09116 78.710864 \n", "L 68.669899 78.824705 \n", "L 69.249098 78.933037 \n", "L 69.828765 79.035559 \n", "L 70.408908 79.131991 \n", "L 70.989531 79.222083 \n", "L 71.570645 79.305621 \n", "L 72.152259 79.382435 \n", "L 72.73438 79.452401 \n", "L 73.317019 79.515456 \n", "L 73.900189 79.571601 \n", "L 74.483895 79.620906 \n", "L 75.068151 79.663518 \n", "L 75.652969 79.699668 \n", "L 76.238355 79.729673 \n", "L 76.824322 79.75394 \n", "L 77.410878 79.77297 \n", "L 77.998031 79.787357 \n", "L 78.585787 79.797788 \n", "L 79.174155 79.805043 \n", "L 79.763137 79.809988 \n", "L 80.352737 79.813573 \n", "L 80.942955 79.816822 \n", "L 81.533789 79.820826 \n", "L 82.125237 79.82673 \n", "L 82.717292 79.835725 \n", "L 83.309948 79.849033 \n", "L 83.903194 79.867889 \n", "L 84.497017 79.89353 \n", "L 85.091402 79.927179 \n", "L 85.686332 79.970024 \n", "L 86.281789 80.023205 \n", "L 86.877753 80.087795 \n", "L 87.474201 80.164787 \n", "L 88.071111 80.255077 \n", "L 88.668458 80.359452 \n", "L 89.266219 80.478575 \n", "L 89.864368 80.612974 \n", "L 90.462883 80.76304 \n", "L 91.06174 80.929012 \n", "L 91.660915 81.11098 \n", "L 92.260388 81.308881 \n", "L 92.86014 81.522496 \n", "L 93.460154 81.751463 \n", "L 94.060416 81.995274 \n", "L 94.660913 82.253287 \n", "L 95.261636 82.524736 \n", "L 95.862579 82.808741 \n", "L 96.463738 83.104326 \n", "L 97.065113 83.410428 \n", "L 97.666707 83.725918 \n", "L 98.268525 84.049614 \n", "L 98.870577 84.3803 \n", "L 99.472872 84.716739 \n", "L 100.075423 85.057694 \n", "L 100.678246 85.401941 \n", "L 101.281359 85.748284 \n", "L 101.88478 86.09557 \n", "L 102.488529 86.442698 \n", "L 103.092625 86.788633 \n", "L 103.697092 87.132412 \n", "L 104.301949 87.473156 \n", "L 104.907219 87.810072 \n", "L 105.512922 88.142458 \n", "L 106.119078 88.469703 \n", "L 106.725706 88.791293 \n", "L 107.332826 89.106806 \n", "L 107.940453 89.415911 \n", "L 108.548603 89.718364 \n", "L 109.157292 90.014005 \n", "L 109.766529 90.302751 \n", "L 110.376326 90.584592 \n", "L 110.986695 90.859582 \n", "L 111.59764 91.127833 \n", "L 112.209168 91.389512 \n", "L 112.821286 91.644825 \n", "L 113.433993 91.894017 \n", "L 114.047294 92.137364 \n", "L 114.661191 92.375165 \n", "L 115.275679 92.607734 \n", "L 115.890759 92.835398 \n", "L 116.506432 93.058493 \n", "L 117.122691 93.277351 \n", "L 117.739534 93.492305 \n", "L 118.356957 93.703681 \n", "L 118.974959 93.911794 \n", "L 119.593532 94.116949 \n", "L 120.212672 94.319436 \n", "L 120.832378 94.519531 \n", "L 121.452641 94.71749 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 62.791148 76.341255 \n", "L 63.364345 76.498616 \n", "L 63.93798 76.654562 \n", "L 64.51205 76.808914 \n", "L 65.086557 76.961486 \n", "L 65.661503 77.11208 \n", "L 66.236888 77.260487 \n", "L 66.812714 77.40649 \n", "L 67.388984 77.549867 \n", "L 67.965701 77.69039 \n", "L 68.542865 77.827828 \n", "L 69.12048 77.961951 \n", "L 69.698553 78.092534 \n", "L 70.277084 78.219356 \n", "L 70.856079 78.342212 \n", "L 71.435545 78.460909 \n", "L 72.015484 78.575274 \n", "L 72.595904 78.685162 \n", "L 73.176812 78.790453 \n", "L 73.758212 78.891065 \n", "L 74.340113 78.986955 \n", "L 74.922524 79.078124 \n", "L 75.505448 79.164621 \n", "L 76.088895 79.246548 \n", "L 76.672873 79.324066 \n", "L 77.257386 79.397394 \n", "L 77.842443 79.466813 \n", "L 78.42805 79.532671 \n", "L 79.014212 79.595376 \n", "L 79.600933 79.655404 \n", "L 80.188218 79.713294 \n", "L 80.776069 79.769641 \n", "L 81.364488 79.825102 \n", "L 81.953475 79.880383 \n", "L 82.543028 79.936234 \n", "L 83.133146 79.993445 \n", "L 83.723822 80.052837 \n", "L 84.315052 80.11525 \n", "L 84.906828 80.181535 \n", "L 85.499141 80.252543 \n", "L 86.09198 80.329115 \n", "L 86.685333 80.412067 \n", "L 87.279186 80.502185 \n", "L 87.873526 80.600204 \n", "L 88.468337 80.706811 \n", "L 89.063602 80.822618 \n", "L 89.659306 80.948166 \n", "L 90.255431 81.083911 \n", "L 90.85196 81.230217 \n", "L 91.448877 81.38735 \n", "L 92.046167 81.555477 \n", "L 92.643813 81.734658 \n", "L 93.2418 81.924851 \n", "L 93.840117 82.125904 \n", "L 94.438753 82.33757 \n", "L 95.037696 82.5595 \n", "L 95.636941 82.791252 \n", "L 96.23648 83.032299 \n", "L 96.836311 83.282039 \n", "L 97.436432 83.5398 \n", "L 98.036842 83.80485 \n", "L 98.637545 84.076414 \n", "L 99.238546 84.35368 \n", "L 99.83985 84.63581 \n", "L 100.441467 84.921955 \n", "L 101.043405 85.211261 \n", "L 101.645678 85.502888 \n", "L 102.248295 85.796011 \n", "L 102.851274 86.089836 \n", "L 103.454626 86.383605 \n", "L 104.058366 86.676604 \n", "L 104.662512 86.96817 \n", "L 105.267076 87.257696 \n", "L 105.872076 87.544635 \n", "L 106.477526 87.828502 \n", "L 107.083439 88.108876 \n", "L 107.68983 88.385402 \n", "L 108.296713 88.657788 \n", "L 108.904098 88.925803 \n", "L 109.511996 89.18928 \n", "L 110.12042 89.448108 \n", "L 110.729375 89.702229 \n", "L 111.33887 89.951634 \n", "L 111.948914 90.196361 \n", "L 112.559509 90.436486 \n", "L 113.170659 90.672123 \n", "L 113.782371 90.903415 \n", "L 114.394644 91.13053 \n", "L 115.00748 91.353658 \n", "L 115.620881 91.573004 \n", "L 116.234844 91.788786 \n", "L 116.84937 92.001229 \n", "L 117.46446 92.210564 \n", "L 118.080107 92.417021 \n", "L 118.696311 92.620828 \n", "L 119.31307 92.822211 \n", "L 119.930383 93.021387 \n", "L 120.548243 93.218567 \n", "L 121.16665 93.41395 \n", "L 121.785601 93.607728 \n", "L 122.40509 93.800076 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 63.823661 75.491034 \n", "L 64.396145 75.651202 \n", "L 64.969067 75.810463 \n", "L 65.542427 75.968698 \n", "L 66.116225 76.125782 \n", "L 66.690465 76.281582 \n", "L 67.265145 76.435958 \n", "L 67.840268 76.588765 \n", "L 68.415835 76.739853 \n", "L 68.991851 76.889071 \n", "L 69.568313 77.036263 \n", "L 70.145226 77.181276 \n", "L 70.722594 77.323958 \n", "L 71.300417 77.464163 \n", "L 71.878701 77.601752 \n", "L 72.457449 77.736597 \n", "L 73.036664 77.868582 \n", "L 73.61635 77.997608 \n", "L 74.196515 78.123599 \n", "L 74.777159 78.246498 \n", "L 75.358289 78.366276 \n", "L 75.939911 78.482935 \n", "L 76.522028 78.596508 \n", "L 77.104645 78.707065 \n", "L 77.68777 78.814713 \n", "L 78.271404 78.919599 \n", "L 78.855553 79.021913 \n", "L 79.440222 79.121888 \n", "L 80.025414 79.219798 \n", "L 80.611131 79.315962 \n", "L 81.197377 79.410741 \n", "L 81.784152 79.504535 \n", "L 82.371459 79.597784 \n", "L 82.959296 79.69096 \n", "L 83.547662 79.784567 \n", "L 84.136556 79.879133 \n", "L 84.725974 79.975208 \n", "L 85.315913 80.073354 \n", "L 85.906366 80.174141 \n", "L 86.497327 80.278138 \n", "L 87.088789 80.385907 \n", "L 87.680744 80.497996 \n", "L 88.273182 80.614928 \n", "L 88.866094 80.737197 \n", "L 89.459469 80.865259 \n", "L 90.053297 80.999526 \n", "L 90.647565 81.140359 \n", "L 91.242263 81.288063 \n", "L 91.83738 81.44288 \n", "L 92.432904 81.604989 \n", "L 93.028825 81.774502 \n", "L 93.625133 81.951457 \n", "L 94.221818 82.135828 \n", "L 94.818872 82.327513 \n", "L 95.416287 82.526346 \n", "L 96.014059 82.732095 \n", "L 96.612182 82.944464 \n", "L 97.210652 83.163102 \n", "L 97.809468 83.387606 \n", "L 98.408629 83.617525 \n", "L 99.008136 83.852373 \n", "L 99.607991 84.09163 \n", "L 100.208198 84.334752 \n", "L 100.808762 84.58118 \n", "L 101.40969 84.830346 \n", "L 102.010986 85.081678 \n", "L 102.612663 85.334615 \n", "L 103.214726 85.588603 \n", "L 103.817187 85.843111 \n", "L 104.420055 86.097632 \n", "L 105.023341 86.351688 \n", "L 105.627056 86.604833 \n", "L 106.23121 86.856662 \n", "L 106.835815 87.106808 \n", "L 107.440881 87.354946 \n", "L 108.046416 87.600793 \n", "L 108.652431 87.84411 \n", "L 109.258938 88.084703 \n", "L 109.86594 88.322415 \n", "L 110.473447 88.557135 \n", "L 111.081469 88.788788 \n", "L 111.690006 89.017333 \n", "L 112.299068 89.242766 \n", "L 112.908659 89.465112 \n", "L 113.51878 89.684421 \n", "L 114.129436 89.900768 \n", "L 114.740631 90.114251 \n", "L 115.352363 90.324981 \n", "L 115.964635 90.533085 \n", "L 116.577448 90.738701 \n", "L 117.190801 90.941975 \n", "L 117.804692 91.143058 \n", "L 118.419125 91.342104 \n", "L 119.034093 91.539267 \n", "L 119.649597 91.734701 \n", "L 120.265636 91.928557 \n", "L 120.88221 92.12098 \n", "L 121.499312 92.312112 \n", "L 122.116944 92.502087 \n", "L 122.735105 92.691032 \n", "L 123.353789 92.879066 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 64.851951 74.638409 \n", "L 65.423709 74.800377 \n", "L 65.995906 74.961798 \n", "L 66.568541 75.122595 \n", "L 67.141616 75.282686 \n", "L 67.715132 75.441985 \n", "L 68.289089 75.600401 \n", "L 68.863489 75.75784 \n", "L 69.438332 75.914204 \n", "L 70.013623 76.069394 \n", "L 70.58936 76.223309 \n", "L 71.165545 76.375849 \n", "L 71.742184 76.526917 \n", "L 72.319274 76.676415 \n", "L 72.89682 76.824255 \n", "L 73.474826 76.970353 \n", "L 74.053291 77.114633 \n", "L 74.632221 77.257032 \n", "L 75.21162 77.3975 \n", "L 75.791488 77.535999 \n", "L 76.371829 77.672512 \n", "L 76.95265 77.80704 \n", "L 77.53395 77.939605 \n", "L 78.115734 78.070251 \n", "L 78.698007 78.19905 \n", "L 79.28077 78.326098 \n", "L 79.864026 78.451518 \n", "L 80.447779 78.575462 \n", "L 81.032031 78.698109 \n", "L 81.616783 78.819667 \n", "L 82.202039 78.94037 \n", "L 82.787797 79.060481 \n", "L 83.37406 79.180284 \n", "L 83.960826 79.300089 \n", "L 84.548095 79.420221 \n", "L 85.135867 79.541027 \n", "L 85.724137 79.662863 \n", "L 86.312904 79.786095 \n", "L 86.902165 79.911093 \n", "L 87.491913 80.038229 \n", "L 88.082146 80.167868 \n", "L 88.672857 80.300365 \n", "L 89.26404 80.436062 \n", "L 89.85569 80.57528 \n", "L 90.447798 80.718314 \n", "L 91.040359 80.865434 \n", "L 91.633364 81.016872 \n", "L 92.226806 81.172829 \n", "L 92.820679 81.333461 \n", "L 93.414975 81.498884 \n", "L 94.009688 81.669171 \n", "L 94.60481 81.844348 \n", "L 95.200335 82.024397 \n", "L 95.79626 82.209252 \n", "L 96.39258 82.398806 \n", "L 96.98929 82.592906 \n", "L 97.586388 82.791362 \n", "L 98.183873 82.993945 \n", "L 98.781743 83.200393 \n", "L 99.379998 83.410413 \n", "L 99.97864 83.623689 \n", "L 100.57767 83.839882 \n", "L 101.177091 84.058641 \n", "L 101.776908 84.279601 \n", "L 102.377124 84.502391 \n", "L 102.977743 84.726641 \n", "L 103.578774 84.951985 \n", "L 104.180221 85.178064 \n", "L 104.782092 85.404531 \n", "L 105.384394 85.631057 \n", "L 105.987133 85.857329 \n", "L 106.590318 86.08306 \n", "L 107.193955 86.307982 \n", "L 107.798052 86.53186 \n", "L 108.402617 86.754479 \n", "L 109.007655 86.975657 \n", "L 109.613173 87.195238 \n", "L 110.219179 87.413094 \n", "L 110.825677 87.629125 \n", "L 111.432672 87.843255 \n", "L 112.040171 88.055439 \n", "L 112.648175 88.265648 \n", "L 113.256689 88.473879 \n", "L 113.865718 88.680149 \n", "L 114.475261 88.88449 \n", "L 115.085322 89.086952 \n", "L 115.695905 89.287597 \n", "L 116.307007 89.486499 \n", "L 116.918631 89.683739 \n", "L 117.530779 89.879408 \n", "L 118.143447 90.073601 \n", "L 118.756637 90.266415 \n", "L 119.370351 90.457952 \n", "L 119.984583 90.648312 \n", "L 120.599336 90.837595 \n", "L 121.214608 91.025899 \n", "L 121.8304 91.21332 \n", "L 122.446708 91.399948 \n", "L 123.063531 91.585873 \n", "L 123.680872 91.771176 \n", "L 124.298724 91.955936 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 65.87607 73.78474 \n", "L 66.447094 73.947736 \n", "L 67.018558 74.110433 \n", "L 67.590459 74.27278 \n", "L 68.162799 74.434728 \n", "L 68.735581 74.596221 \n", "L 69.308803 74.757201 \n", "L 69.882466 74.91761 \n", "L 70.456572 75.077384 \n", "L 71.031124 75.236463 \n", "L 71.606121 75.394782 \n", "L 72.181564 75.552277 \n", "L 72.757457 75.708887 \n", "L 73.333798 75.864551 \n", "L 73.910592 76.019212 \n", "L 74.48784 76.172819 \n", "L 75.065543 76.325322 \n", "L 75.643704 76.476682 \n", "L 76.222326 76.626867 \n", "L 76.801409 76.775854 \n", "L 77.380957 76.923631 \n", "L 77.960973 77.070201 \n", "L 78.541457 77.215575 \n", "L 79.122412 77.359784 \n", "L 79.703843 77.502872 \n", "L 80.285749 77.6449 \n", "L 80.868132 77.785946 \n", "L 81.450996 77.926107 \n", "L 82.034342 78.065496 \n", "L 82.61817 78.204244 \n", "L 83.202482 78.342501 \n", "L 83.787278 78.480431 \n", "L 84.372561 78.618216 \n", "L 84.958328 78.756051 \n", "L 85.544579 78.894142 \n", "L 86.131314 79.032708 \n", "L 86.718531 79.171974 \n", "L 87.306229 79.312174 \n", "L 87.894405 79.45354 \n", "L 88.483057 79.596307 \n", "L 89.072181 79.740706 \n", "L 89.661774 79.886963 \n", "L 90.251832 80.035293 \n", "L 90.842352 80.185898 \n", "L 91.433329 80.338966 \n", "L 92.024758 80.494668 \n", "L 92.616636 80.65315 \n", "L 93.208957 80.814537 \n", "L 93.801717 80.978932 \n", "L 94.394911 81.146405 \n", "L 94.988538 81.317004 \n", "L 95.58259 81.490744 \n", "L 96.177066 81.667614 \n", "L 96.771962 81.847573 \n", "L 97.367276 82.030553 \n", "L 97.963005 82.216459 \n", "L 98.559149 82.405168 \n", "L 99.155706 82.596537 \n", "L 99.752677 82.790401 \n", "L 100.350061 82.986574 \n", "L 100.94786 83.184856 \n", "L 101.546075 83.385034 \n", "L 102.144708 83.586885 \n", "L 102.743763 83.790179 \n", "L 103.343242 83.994681 \n", "L 103.943148 84.200157 \n", "L 104.543487 84.406377 \n", "L 105.144261 84.613112 \n", "L 105.745478 84.820143 \n", "L 106.34714 85.027263 \n", "L 106.949252 85.234274 \n", "L 107.551821 85.440992 \n", "L 108.15485 85.647252 \n", "L 108.758346 85.8529 \n", "L 109.362312 86.057805 \n", "L 109.966752 86.261848 \n", "L 110.571672 86.464931 \n", "L 111.177078 86.666975 \n", "L 111.78297 86.867915 \n", "L 112.389353 87.067704 \n", "L 112.996232 87.266312 \n", "L 113.603607 87.463721 \n", "L 114.211482 87.65993 \n", "L 114.81986 87.85495 \n", "L 115.428741 88.048799 \n", "L 116.038126 88.241511 \n", "L 116.648021 88.433124 \n", "L 117.258421 88.623685 \n", "L 117.869329 88.813246 \n", "L 118.480748 89.001865 \n", "L 119.092674 89.1896 \n", "L 119.705109 89.376514 \n", "L 120.318055 89.562671 \n", "L 120.931507 89.748135 \n", "L 121.545468 89.932969 \n", "L 122.159936 90.117235 \n", "L 122.774914 90.300994 \n", "L 123.390397 90.484303 \n", "L 124.006386 90.667218 \n", "L 124.622882 90.849792 \n", "L 125.239882 91.032074 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 66.216524 73.500162 \n", "L 66.787303 73.663366 \n", "L 67.358521 73.826333 \n", "L 67.930175 73.989021 \n", "L 68.502269 74.151386 \n", "L 69.074805 74.313382 \n", "L 69.647779 74.474959 \n", "L 70.221195 74.636067 \n", "L 70.795054 74.796653 \n", "L 71.369358 74.956664 \n", "L 71.944105 75.116045 \n", "L 72.519298 75.274742 \n", "L 73.09494 75.432703 \n", "L 73.67103 75.589876 \n", "L 74.247571 75.746211 \n", "L 74.824565 75.901666 \n", "L 75.402012 76.056198 \n", "L 75.979915 76.209774 \n", "L 76.558278 76.362367 \n", "L 77.137098 76.513956 \n", "L 77.716381 76.664533 \n", "L 78.29613 76.814098 \n", "L 78.876343 76.962662 \n", "L 79.457024 77.110251 \n", "L 80.038177 77.256904 \n", "L 80.619801 77.40267 \n", "L 81.201898 77.547619 \n", "L 81.784472 77.691832 \n", "L 82.367522 77.835405 \n", "L 82.95105 77.978451 \n", "L 83.535058 78.121097 \n", "L 84.119545 78.263484 \n", "L 84.704513 78.405766 \n", "L 85.28996 78.54811 \n", "L 85.875887 78.690693 \n", "L 86.462294 78.8337 \n", "L 87.049177 78.977323 \n", "L 87.636538 79.121762 \n", "L 88.224373 79.267216 \n", "L 88.81268 79.413884 \n", "L 89.401457 79.561964 \n", "L 89.990701 79.711647 \n", "L 90.580408 79.863118 \n", "L 91.170575 80.01655 \n", "L 91.761199 80.172103 \n", "L 92.352276 80.32992 \n", "L 92.943801 80.490129 \n", "L 93.535772 80.652836 \n", "L 94.128185 80.818126 \n", "L 94.721035 80.986063 \n", "L 95.314321 81.156685 \n", "L 95.908037 81.330005 \n", "L 96.502182 81.506016 \n", "L 97.096752 81.684681 \n", "L 97.691746 81.865942 \n", "L 98.287162 82.049717 \n", "L 98.882999 82.235905 \n", "L 99.479257 82.424381 \n", "L 100.075936 82.615003 \n", "L 100.673034 82.807616 \n", "L 101.270555 83.002048 \n", "L 101.868499 83.198117 \n", "L 102.466868 83.395633 \n", "L 103.065665 83.594399 \n", "L 103.664893 83.794217 \n", "L 104.264554 83.994885 \n", "L 104.864652 84.196207 \n", "L 105.465192 84.397989 \n", "L 106.066177 84.600045 \n", "L 106.667613 84.802196 \n", "L 107.269501 85.004274 \n", "L 107.871848 85.206123 \n", "L 108.474658 85.4076 \n", "L 109.077935 85.608577 \n", "L 109.681684 85.80894 \n", "L 110.285907 86.008587 \n", "L 110.890609 86.207437 \n", "L 111.495796 86.40542 \n", "L 112.101467 86.602481 \n", "L 112.707628 86.798581 \n", "L 113.314282 86.993694 \n", "L 113.92143 87.187805 \n", "L 114.529075 87.380912 \n", "L 115.13722 87.573025 \n", "L 115.745864 87.76416 \n", "L 116.355011 87.954345 \n", "L 116.964662 88.143613 \n", "L 117.574815 88.332004 \n", "L 118.185473 88.519562 \n", "L 118.796637 88.706337 \n", "L 119.408306 88.892378 \n", "L 120.020479 89.07774 \n", "L 120.63316 89.262477 \n", "L 121.246344 89.446642 \n", "L 121.860033 89.630291 \n", "L 122.474227 89.813476 \n", "L 123.088928 89.996249 \n", "L 123.70413 90.178659 \n", "L 124.319836 90.360753 \n", "L 124.936047 90.542577 \n", "L 125.55276 90.724173 \n", "\" clip-path=\"url(#pfabc09b28a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pfabc09b28a\">\n", "   <rect x=\"7.2\" y=\"7.2\" width=\"138.6\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Construct grid and compute function\n", "x, y = torch.meshgrid(torch.linspace(-2, 2, 101), torch.linspace(-2, 2, 101))\n", "z = torch.exp(- x**2 - y**2)\n", "\n", "# Plot function\n", "ax = d2l.plt.figure().add_subplot(111, projection='3d')\n", "ax.plot_wireframe(x, y, z)\n", "d2l.plt.xlabel('x')\n", "d2l.plt.ylabel('y')\n", "d2l.plt.xticks([-2, -1, 0, 1, 2])\n", "d2l.plt.yticks([-2, -1, 0, 1, 2])\n", "d2l.set_figsize()\n", "ax.set_xlim(-2, 2)\n", "ax.set_ylim(-2, 2)\n", "ax.set_zlim(0, 1)\n", "ax.dist = 12"]}, {"cell_type": "markdown", "id": "aabe72ab", "metadata": {"origin_pos": 16}, "source": ["We write this as\n", "\n", "$$\n", "\\int_{[a, b]\\times[c, d]} f(x, y)\\;dx\\;dy.\n", "$$\n", "\n", "Suppose that we wish to compute this integral.  My claim is that we can do this by iteratively computing first the integral in $x$ and then shifting to the integral in $y$, that is to say\n", "\n", "$$\n", "\\int_{[a, b]\\times[c, d]} f(x, y)\\;dx\\;dy = \\int_c^{d} \\left(\\int_a^{b} f(x, y) \\;dx\\right) \\; dy.\n", "$$\n", "\n", "Let's see why this is.\n", "\n", "Consider the figure above where we have split the function into $\\epsilon \\times \\epsilon$ squares which we will index with integer coordinates $i, j$.  In this case, our integral is approximately\n", "\n", "$$\n", "\\sum_{i, j} \\epsilon^{2} f(\\epsilon i, \\epsilon j).\n", "$$\n", "\n", "Once we discretize the problem, we may add up the values on these squares in whatever order we like, and not worry about changing the values.  This is illustrated in :numref:`fig_sum-order`.  In particular, we can say that\n", "\n", "$$\n", " \\sum _ {j} \\epsilon \\left(\\sum_{i} \\epsilon f(\\epsilon i, \\epsilon j)\\right).\n", "$$\n", "\n", "![Illustrating how to decompose a sum over many squares as a sum over first the columns (1), then adding the column sums together (2).](../img/sum-order.svg)\n", ":label:`fig_sum-order`\n", "\n", "The sum on the inside is precisely the discretization of the integral\n", "\n", "$$\n", "G(\\epsilon j) = \\int _a^{b} f(x, \\epsilon j) \\; dx.\n", "$$\n", "\n", "Finally, notice that if we combine these two expressions we get\n", "\n", "$$\n", "\\sum _ {j} \\epsilon G(\\epsilon j) \\approx \\int _ {c}^{d} G(y) \\; dy = \\int _ {[a, b]\\times[c, d]} f(x, y)\\;dx\\;dy.\n", "$$\n", "\n", "Thus putting it all together, we have that\n", "\n", "$$\n", "\\int _ {[a, b]\\times[c, d]} f(x, y)\\;dx\\;dy = \\int _ c^{d} \\left(\\int _ a^{b} f(x, y) \\;dx\\right) \\; dy.\n", "$$\n", "\n", "Notice that, once discretized, all we did was rearrange the order in which we added a list of numbers.  This may make it seem like it is nothing, however this result (called *<PERSON><PERSON><PERSON>'s Theorem*) is not always true!  For the type of mathematics encountered when doing machine learning (continuous functions), there is no concern, however it is possible to create examples where it fails (for example the function $f(x, y) = xy(x^2-y^2)/(x^2+y^2)^3$ over the rectangle $[0,2]\\times[0,1]$).\n", "\n", "Note that the choice to do the integral in $x$ first, and then the integral in $y$ was arbitrary.  We could have equally well chosen to do $y$ first and then $x$ to see\n", "\n", "$$\n", "\\int _ {[a, b]\\times[c, d]} f(x, y)\\;dx\\;dy = \\int _ a^{b} \\left(\\int _ c^{d} f(x, y) \\;dy\\right) \\; dx.\n", "$$\n", "\n", "Often times, we will condense down to vector notation, and say that for $U = [a, b]\\times [c, d]$ this is\n", "\n", "$$\n", "\\int _ U f(\\mathbf{x})\\;d\\mathbf{x}.\n", "$$\n", "\n", "## Change of Variables in Multiple Integrals\n", "As with single variables in :eqref:`eq_change_var`, the ability to change variables inside a higher dimensional integral is a key tool.  Let's summarize the result without derivation.\n", "\n", "We need a function that reparametrizes our domain of integration.  We can take this to be $\\phi : \\mathbb{R}^n \\rightarrow \\mathbb{R}^n$, that is any function which takes in $n$ real variables and returns another $n$.  To keep the expressions clean, we will assume that $\\phi$ is *injective* which is to say it never folds over itself ($\\phi(\\mathbf{x}) = \\phi(\\mathbf{y}) \\implies \\mathbf{x} = \\mathbf{y}$).\n", "\n", "In this case, we can say that\n", "\n", "$$\n", "\\int _ {\\phi(U)} f(\\mathbf{x})\\;d\\mathbf{x} = \\int _ {U} f(\\phi(\\mathbf{x})) \\left|\\det(D\\phi(\\mathbf{x}))\\right|\\;d\\mathbf{x}.\n", "$$\n", "\n", "where $D\\phi$ is the *Jacobian* of $\\phi$, which is the matrix of partial derivatives of $\\boldsymbol{\\phi} = (\\phi_1(x_1, \\ldots, x_n), \\ldots, \\phi_n(x_1, \\ldots, x_n))$,\n", "\n", "$$\n", "D\\boldsymbol{\\phi} = \\begin{bmatrix}\n", "\\frac{\\partial \\phi _ 1}{\\partial x _ 1} & \\cdots & \\frac{\\partial \\phi _ 1}{\\partial x _ n} \\\\\n", "\\vdots & \\ddots & \\vdots \\\\\n", "\\frac{\\partial \\phi _ n}{\\partial x _ 1} & \\cdots & \\frac{\\partial \\phi _ n}{\\partial x _ n}\n", "\\end{bmatrix}.\n", "$$\n", "\n", "Looking closely, we see that this is similar to the single variable chain rule :eqref:`eq_change_var`, except we have replaced the term $\\frac{du}{dx}(x)$ with $\\left|\\det(D\\phi(\\mathbf{x}))\\right|$.  Let's see how we can to interpret this term.  Recall that the $\\frac{du}{dx}(x)$ term existed to say how much we stretched our $x$-axis by applying $u$.  The same process in higher dimensions is to determine how much we stretch the area (or volume, or hyper-volume) of a little square (or little *hyper-cube*) by applying $\\boldsymbol{\\phi}$.  If $\\boldsymbol{\\phi}$ was the multiplication by a matrix, then we know how the determinant already gives the answer.\n", "\n", "With some work, one can show that the *Jacobian* provides the best approximation to a multivariable function $\\boldsymbol{\\phi}$ at a point by a matrix in the same way we could approximate by lines or planes with derivatives and gradients. Thus the determinant of the Jacobian exactly mirrors the scaling factor we identified in one dimension.\n", "\n", "It takes some work to fill in the details to this, so do not worry if they are not clear now.  Let's see at least one example we will make use of later on.  Consider the integral\n", "\n", "$$\n", "\\int _ {-\\infty}^{\\infty} \\int _ {-\\infty}^{\\infty} e^{-x^{2}-y^{2}} \\;dx\\;dy.\n", "$$\n", "\n", "Playing with this integral directly will get us no-where, but if we change variables, we can make significant progress.  If we let $\\boldsymbol{\\phi}(r, \\theta) = (r \\cos(\\theta),  r\\sin(\\theta))$ (which is to say that $x = r \\cos(\\theta)$, $y = r \\sin(\\theta)$), then we can apply the change of variable formula to see that this is the same thing as\n", "\n", "$$\n", "\\int _ 0^\\infty \\int_0 ^ {2\\pi} e^{-r^{2}} \\left|\\det(D\\mathbf{\\phi}(\\mathbf{x}))\\right|\\;d\\theta\\;dr,\n", "$$\n", "\n", "where\n", "\n", "$$\n", "\\left|\\det(D\\mathbf{\\phi}(\\mathbf{x}))\\right| = \\left|\\det\\begin{bmatrix}\n", "\\cos(\\theta) & -r\\sin(\\theta) \\\\\n", "\\sin(\\theta) & r\\cos(\\theta)\n", "\\end{bmatrix}\\right| = r(\\cos^{2}(\\theta) + \\sin^{2}(\\theta)) = r.\n", "$$\n", "\n", "Thus, the integral is\n", "\n", "$$\n", "\\int _ 0^\\infty \\int _ 0 ^ {2\\pi} re^{-r^{2}} \\;d\\theta\\;dr = 2\\pi\\int _ 0^\\infty re^{-r^{2}} \\;dr = \\pi,\n", "$$\n", "\n", "where the final equality follows by the same computation that we used in section :numref:`subsec_integral_example`.\n", "\n", "We will meet this integral again when we study continuous random variables in :numref:`sec_random_variables`.\n", "\n", "## Summary\n", "\n", "* The theory of integration allows us to answer questions about areas or volumes.\n", "* The fundamental theorem of calculus allows us to leverage knowledge about derivatives to compute areas via the observation that the derivative of the area up to some point is given by the value of the function being integrated.\n", "* Integrals in higher dimensions can be computed by iterating single variable integrals.\n", "\n", "## Exercises\n", "1. What is $\\int_1^2 \\frac{1}{x} \\;dx$?\n", "2. Use the change of variables formula to integrate $\\int_0^{\\sqrt{\\pi}}x\\sin(x^2)\\;dx$.\n", "3. What is $\\int_{[0,1]^2} xy \\;dx\\;dy$?\n", "4. Use the change of variables formula to compute $\\int_0^2\\int_0^1xy(x^2-y^2)/(x^2+y^2)^3\\;dy\\;dx$ and $\\int_0^1\\int_0^2f(x, y) = xy(x^2-y^2)/(x^2+y^2)^3\\;dx\\;dy$ to see they are different.\n"]}, {"cell_type": "markdown", "id": "638081bd", "metadata": {"origin_pos": 18, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1092)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "b1c651b9", "metadata": {"origin_pos": 0}, "source": ["# Geometry and Linear Algebraic Operations\n", ":label:`sec_geometry-linear-algebraic-ops`\n", "\n", "In :numref:`sec_linear-algebra`, we encountered the basics of linear algebra\n", "and saw how it could be used to express common operations for transforming our data.\n", "Linear algebra is one of the key mathematical pillars\n", "underlying much of the work that we do in deep learning\n", "and in machine learning more broadly.\n", "While :numref:`sec_linear-algebra` contained enough machinery\n", "to communicate the mechanics of modern deep learning models,\n", "there is a lot more to the subject.\n", "In this section, we will go deeper,\n", "highlighting some geometric interpretations of linear algebra operations,\n", "and introducing a few fundamental concepts, including of eigenvalues and eigenvectors.\n", "\n", "## Geometry of Vectors\n", "First, we need to discuss the two common geometric interpretations of vectors,\n", "as either points or directions in space.\n", "Fundamentally, a vector is a list of numbers such as the Python list below.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "cacc74a0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:00.057600Z", "iopub.status.busy": "2023-08-18T19:30:00.057050Z", "iopub.status.idle": "2023-08-18T19:30:00.065399Z", "shell.execute_reply": "2023-08-18T19:30:00.064455Z"}, "origin_pos": 1, "tab": ["pytorch"]}, "outputs": [], "source": ["v = [1, 7, 0, 1]"]}, {"cell_type": "markdown", "id": "112fc890", "metadata": {"origin_pos": 2}, "source": ["Mathematicians most often write this as either a *column* or *row* vector, which is to say either as\n", "\n", "$$\n", "\\mathbf{x} = \\begin{bmatrix}1\\\\7\\\\0\\\\1\\end{bmatrix},\n", "$$\n", "\n", "or\n", "\n", "$$\n", "\\mathbf{x}^\\top = \\begin{bmatrix}1 & 7 & 0 & 1\\end{bmatrix}.\n", "$$\n", "\n", "These often have different interpretations,\n", "where data examples are column vectors\n", "and weights used to form weighted sums are row vectors.\n", "However, it can be beneficial to be flexible.\n", "As we have described in :numref:`sec_linear-algebra`,\n", "though a single vector's default orientation is a column vector,\n", "for any matrix representing a tabular dataset,\n", "treating each data example as a row vector\n", "in the matrix\n", "is more conventional.\n", "\n", "Given a vector, the first interpretation\n", "that we should give it is as a point in space.\n", "In two or three dimensions, we can visualize these points\n", "by using the components of the vectors to define\n", "the location of the points in space compared\n", "to a fixed reference called the *origin*.  This can be seen in :numref:`fig_grid`.\n", "\n", "![An illustration of visualizing vectors as points in the plane.  The first component of the vector gives the $\\mathit{x}$-coordinate, the second component gives the $\\mathit{y}$-coordinate.  Higher dimensions are analogous, although much harder to visualize.](../img/grid-points.svg)\n", ":label:`fig_grid`\n", "\n", "This geometric point of view allows us to consider the problem on a more abstract level.\n", "No longer faced with some insurmountable seeming problem\n", "like classifying pictures as either cats or dogs,\n", "we can start considering tasks abstractly\n", "as collections of points in space and picturing the task\n", "as discovering how to separate two distinct clusters of points.\n", "\n", "In parallel, there is a second point of view\n", "that people often take of vectors: as directions in space.\n", "Not only can we think of the vector $\\mathbf{v} = [3,2]^\\top$\n", "as the location $3$ units to the right and $2$ units up from the origin,\n", "we can also think of it as the direction itself\n", "to take $3$ steps to the right and $2$ steps up.\n", "In this way, we consider all the vectors in figure :numref:`fig_arrow` the same.\n", "\n", "![Any vector can be visualized as an arrow in the plane.  In this case, every vector drawn is a representation of the vector $(3,2)^\\top$.](../img/par-vec.svg)\n", ":label:`fig_arrow`\n", "\n", "One of the benefits of this shift is that\n", "we can make visual sense of the act of vector addition.\n", "In particular, we follow the directions given by one vector,\n", "and then follow the directions given by the other, as is seen in :numref:`fig_add-vec`.\n", "\n", "![We can visualize vector addition by first following one vector, and then another.](../img/vec-add.svg)\n", ":label:`fig_add-vec`\n", "\n", "Vector subtraction has a similar interpretation.\n", "By considering the identity that $\\mathbf{u} = \\mathbf{v} + (\\mathbf{u}-\\mathbf{v})$,\n", "we see that the vector $\\mathbf{u}-\\mathbf{v}$ is the direction\n", "that takes us from the point $\\mathbf{v}$ to the point $\\mathbf{u}$.\n", "\n", "\n", "## Dot Products and Angles\n", "As we saw in :numref:`sec_linear-algebra`,\n", "if we take two column vectors $\\mathbf{u}$ and $\\mathbf{v}$,\n", "we can form their dot product by computing:\n", "\n", "$$\\mathbf{u}^\\top\\mathbf{v} = \\sum_i u_i\\cdot v_i.$$\n", ":eqlabel:`eq_dot_def`\n", "\n", "Because :eqref:`eq_dot_def` is symmetric, we will mirror the notation\n", "of classical multiplication and write\n", "\n", "$$\n", "\\mathbf{u}\\cdot\\mathbf{v} = \\mathbf{u}^\\top\\mathbf{v} = \\mathbf{v}^\\top\\mathbf{u},\n", "$$\n", "\n", "to highlight the fact that exchanging the order of the vectors will yield the same answer.\n", "\n", "The dot product :eqref:`eq_dot_def` also admits a geometric interpretation: it is closely related to the angle between two vectors.  Consider the angle shown in :numref:`fig_angle`.\n", "\n", "![Between any two vectors in the plane there is a well defined angle $\\theta$.  We will see this angle is intimately tied to the dot product.](../img/vec-angle.svg)\n", ":label:`fig_angle`\n", "\n", "To start, let's consider two specific vectors:\n", "\n", "$$\n", "\\mathbf{v} = (r,0) \\; \\textrm{and} \\; \\mathbf{w} = (s\\cos(\\theta), s \\sin(\\theta)).\n", "$$\n", "\n", "The vector $\\mathbf{v}$ is length $r$ and runs parallel to the $x$-axis,\n", "and the vector $\\mathbf{w}$ is of length $s$ and at angle $\\theta$ with the $x$-axis.\n", "If we compute the dot product of these two vectors, we see that\n", "\n", "$$\n", "\\mathbf{v}\\cdot\\mathbf{w} = rs\\cos(\\theta) = \\|\\mathbf{v}\\|\\|\\mathbf{w}\\|\\cos(\\theta).\n", "$$\n", "\n", "With some simple algebraic manipulation, we can rearrange terms to obtain\n", "\n", "$$\n", "\\theta = \\arccos\\left(\\frac{\\mathbf{v}\\cdot\\mathbf{w}}{\\|\\mathbf{v}\\|\\|\\mathbf{w}\\|}\\right).\n", "$$\n", "\n", "In short, for these two specific vectors,\n", "the dot product combined with the norms tell us the angle between the two vectors. This same fact is true in general. We will not derive the expression here, however,\n", "if we consider writing $\\|\\mathbf{v} - \\mathbf{w}\\|^2$ in two ways:\n", "one with the dot product, and the other geometrically using the law of cosines,\n", "we can obtain the full relationship.\n", "Indeed, for any two vectors $\\mathbf{v}$ and $\\mathbf{w}$,\n", "the angle between the two vectors is\n", "\n", "$$\\theta = \\arccos\\left(\\frac{\\mathbf{v}\\cdot\\mathbf{w}}{\\|\\mathbf{v}\\|\\|\\mathbf{w}\\|}\\right).$$\n", ":eqlabel:`eq_angle_forumla`\n", "\n", "This is a nice result since nothing in the computation references two-dimensions.\n", "Indeed, we can use this in three or three million dimensions without issue.\n", "\n", "As a simple example, let's see how to compute the angle between a pair of vectors:\n"]}, {"cell_type": "code", "execution_count": 2, "id": "c68a6de0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:00.068681Z", "iopub.status.busy": "2023-08-18T19:30:00.068407Z", "iopub.status.idle": "2023-08-18T19:30:03.242521Z", "shell.execute_reply": "2023-08-18T19:30:03.241359Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor(0.4190)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["%matplotlib inline\n", "import torch\n", "import torchvision\n", "from IPython import display\n", "from torchvision import transforms\n", "from d2l import torch as d2l\n", "\n", "\n", "def angle(v, w):\n", "    return torch.acos(v.dot(w) / (torch.norm(v) * torch.norm(w)))\n", "\n", "angle(torch.tensor([0, 1, 2], dtype=torch.float32), torch.tensor([2.0, 3, 4]))"]}, {"cell_type": "markdown", "id": "41060004", "metadata": {"origin_pos": 6}, "source": ["We will not use it right now, but it is useful to know\n", "that we will refer to vectors for which the angle is $\\pi/2$\n", "(or equivalently $90^{\\circ}$) as being *orthogonal*.\n", "By examining the equation above, we see that this happens when $\\theta = \\pi/2$,\n", "which is the same thing as $\\cos(\\theta) = 0$.\n", "The only way this can happen is if the dot product itself is zero,\n", "and two vectors are orthogonal if and only if $\\mathbf{v}\\cdot\\mathbf{w} = 0$.\n", "This will prove to be a helpful formula when understanding objects geometrically.\n", "\n", "It is reasonable to ask: why is computing the angle useful?\n", "The answer comes in the kind of invariance we expect data to have.\n", "Consider an image, and a duplicate image,\n", "where every pixel value is the same but $10\\%$ the brightness.\n", "The values of the individual pixels are in general far from the original values.\n", "Thus, if one computed the distance between the original image and the darker one,\n", "the distance can be large.\n", "However, for most ML applications, the *content* is the same---it is still\n", "an image of a cat as far as a cat/dog classifier is concerned.\n", "However, if we consider the angle, it is not hard to see\n", "that for any vector $\\mathbf{v}$, the angle\n", "between $\\mathbf{v}$ and $0.1\\cdot\\mathbf{v}$ is zero.\n", "This corresponds to the fact that scaling vectors\n", "keeps the same direction and just changes the length.\n", "The angle considers the darker image identical.\n", "\n", "Examples like this are everywhere.\n", "In text, we might want the topic being discussed\n", "to not change if we write twice as long of document that says the same thing.\n", "For some encoding (such as counting the number of occurrences of words in some vocabulary), this corresponds to a doubling of the vector encoding the document,\n", "so again we can use the angle.\n", "\n", "### Cosine Similarity\n", "In ML contexts where the angle is employed\n", "to measure the closeness of two vectors,\n", "practitioners adopt the term *cosine similarity*\n", "to refer to the portion\n", "$$\n", "\\cos(\\theta) = \\frac{\\mathbf{v}\\cdot\\mathbf{w}}{\\|\\mathbf{v}\\|\\|\\mathbf{w}\\|}.\n", "$$\n", "\n", "The cosine takes a maximum value of $1$\n", "when the two vectors point in the same direction,\n", "a minimum value of $-1$ when they point in opposite directions,\n", "and a value of $0$ when the two vectors are orthogonal.\n", "Note that if the components of high-dimensional vectors\n", "are sampled randomly with mean $0$,\n", "their cosine will nearly always be close to $0$.\n", "\n", "\n", "## Hyperplanes\n", "\n", "In addition to working with vectors, another key object\n", "that you must understand to go far in linear algebra\n", "is the *hyperplane*, a generalization to higher dimensions\n", "of a line (two dimensions) or of a plane (three dimensions).\n", "In an $d$-dimensional vector space, a hyperplane has $d-1$ dimensions\n", "and divides the space into two half-spaces.\n", "\n", "Let's start with an example.\n", "Suppose that we have a column vector $\\mathbf{w}=[2,1]^\\top$. We want to know, \"what are the points $\\mathbf{v}$ with $\\mathbf{w}\\cdot\\mathbf{v} = 1$?\"\n", "By recalling the connection between dot products and angles above :eqref:`eq_angle_forumla`,\n", "we can see that this is equivalent to\n", "$$\n", "\\|\\mathbf{v}\\|\\|\\mathbf{w}\\|\\cos(\\theta) = 1 \\; \\iff \\; \\|\\mathbf{v}\\|\\cos(\\theta) = \\frac{1}{\\|\\mathbf{w}\\|} = \\frac{1}{\\sqrt{5}}.\n", "$$\n", "\n", "![Recalling trigonometry, we see the formula $\\|\\mathbf{v}\\|\\cos(\\theta)$ is the length of the projection of the vector $\\mathbf{v}$ onto the direction of $\\mathbf{w}$](../img/proj-vec.svg)\n", ":label:`fig_vector-project`\n", "\n", "If we consider the geometric meaning of this expression,\n", "we see that this is equivalent to saying\n", "that the length of the projection of $\\mathbf{v}$\n", "onto the direction of $\\mathbf{w}$ is exactly $1/\\|\\mathbf{w}\\|$, as is shown in :numref:`fig_vector-project`.\n", "The set of all points where this is true is a line\n", "at right angles to the vector $\\mathbf{w}$.\n", "If we wanted, we could find the equation for this line\n", "and see that it is $2x + y = 1$ or equivalently $y = 1 - 2x$.\n", "\n", "If we now look at what happens when we ask about the set of points with\n", "$\\mathbf{w}\\cdot\\mathbf{v} > 1$ or $\\mathbf{w}\\cdot\\mathbf{v} < 1$,\n", "we can see that these are cases where the projections\n", "are longer or shorter than $1/\\|\\mathbf{w}\\|$, respectively.\n", "Thus, those two inequalities define either side of the line.\n", "In this way, we have found a way to cut our space into two halves,\n", "where all the points on one side have dot product below a threshold,\n", "and the other side above as we see in :numref:`fig_space-division`.\n", "\n", "![If we now consider the inequality version of the expression, we see that our hyperplane (in this case: just a line) separates the space into two halves.](../img/space-division.svg)\n", ":label:`fig_space-division`\n", "\n", "The story in higher dimension is much the same.\n", "If we now take $\\mathbf{w} = [1,2,3]^\\top$\n", "and ask about the points in three dimensions with $\\mathbf{w}\\cdot\\mathbf{v} = 1$,\n", "we obtain a plane at right angles to the given vector $\\mathbf{w}$.\n", "The two inequalities again define the two sides of the plane as is shown in :numref:`fig_higher-division`.\n", "\n", "![Hyperplanes in any dimension separate the space into two halves.](../img/space-division-3d.svg)\n", ":label:`fig_higher-division`\n", "\n", "While our ability to visualize runs out at this point,\n", "nothing stops us from doing this in tens, hundreds, or billions of dimensions.\n", "This occurs often when thinking about machine learned models.\n", "For instance, we can understand linear classification models\n", "like those from :numref:`sec_softmax`,\n", "as methods to find hyperplanes that separate the different target classes.\n", "In this context, such hyperplanes are often referred to as *decision planes*.\n", "The majority of deep learned classification models end\n", "with a linear layer fed into a softmax,\n", "so one can interpret the role of the deep neural network\n", "to be to find a non-linear embedding such that the target classes\n", "can be separated cleanly by hyperplanes.\n", "\n", "To give a hand-built example, notice that we can produce a reasonable model\n", "to classify tiny images of t-shirts and trousers from the Fashion-MNIST dataset\n", "(seen in :numref:`sec_fashion_mnist`)\n", "by just taking the vector between their means to define the decision plane\n", "and eyeball a crude threshold.  First we will load the data and compute the averages.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6181ed73", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:03.247622Z", "iopub.status.busy": "2023-08-18T19:30:03.246701Z", "iopub.status.idle": "2023-08-18T19:30:28.056683Z", "shell.execute_reply": "2023-08-18T19:30:28.055314Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["# Load in the dataset\n", "trans = []\n", "trans.append(transforms.ToTensor())\n", "trans = transforms.Compose(trans)\n", "train = torchvision.datasets.FashionMNIST(root=\"../data\", transform=trans,\n", "                                          train=True, download=True)\n", "test = torchvision.datasets.FashionMNIST(root=\"../data\", transform=trans,\n", "                                         train=False, download=True)\n", "\n", "X_train_0 = torch.stack(\n", "    [x[0] * 256 for x in train if x[1] == 0]).type(torch.float32)\n", "X_train_1 = torch.stack(\n", "    [x[0] * 256 for x in train if x[1] == 1]).type(torch.float32)\n", "X_test = torch.stack(\n", "    [x[0] * 256 for x in test if x[1] == 0 or x[1] == 1]).type(torch.float32)\n", "y_test = torch.stack([torch.tensor(x[1]) for x in test\n", "                      if x[1] == 0 or x[1] == 1]).type(torch.float32)\n", "\n", "# Compute averages\n", "ave_0 = torch.mean(X_train_0, axis=0)\n", "ave_1 = torch.mean(X_train_1, axis=0)"]}, {"cell_type": "markdown", "id": "269eb648", "metadata": {"origin_pos": 10}, "source": ["It can be informative to examine these averages in detail, so let's plot what they look like.  In this case, we see that the average indeed resembles a blurry image of a t-shirt.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "027037c8", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:28.063623Z", "iopub.status.busy": "2023-08-18T19:30:28.061532Z", "iopub.status.idle": "2023-08-18T19:30:28.243347Z", "shell.execute_reply": "2023-08-18T19:30:28.242495Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"172.725pt\" height=\"171.002344pt\" viewBox=\"0 0 172.725 171.002344\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:30:28.191114</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 171.002344 \n", "L 172.725 171.002344 \n", "L 172.725 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 26.925 147.124219 \n", "L 165.525 147.124219 \n", "L 165.525 8.524219 \n", "L 26.925 8.524219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p092a295790)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAMEAAADBCAYAAAB2QtScAAAIUUlEQVR4nO3d3UpUbRjG8WVpfhaC9oFYUAlFQbQRtNNmGx1Sh9IBdCjtFG1EBAUhlYhRUVla6qj5nsBcl3kzz5Dv9f9tzs1as2bGywU39/OskYODg4NuiNTbucvo9Xqytra2JmsvXryQtU+fPsnaxsaGrG1ubsra1taWrO3u7vZ93X3u0dFRWRsbG5O1qakpWZudnZW1ubk5WVtcXJS1GzdulN7PfT5lZGTkyMcc5sTAzwgcM4QA8QgB4hECxCMEiEcIEO/oPaq/4Np+qra3tyeP+f79u6w9fvxY1h49eiRrrg26vb0ta/v7+7LmqNaea/lV24HuONc+dbWbN2/K2sOHD2Xt7t27sqau8+TJk/IY97dV/b64EyAeIUA8QoB4hADxCAHiEQLEK7dIhzl86lpfbppyYmJC1twEo3s/97n//PkjaydO9P9/U22RVmvVz+3ap66l6aj3a9EGdbgTIB4hQDxCgHiEAPEIAeIRAsRrMkXqVFqrqr3Ydb5l5iY+3dSq49qgFe763ed2Bt2y7Dq9UUDX1X8f9bdQbYNWW6vcCRCPECAeIUA8QoB4hADxCAHiDX2hfYVrwblatQ36r2gxTdli+rc6mfqv4E6AeIQA8QgB4hECxCMEiGe7Qy06CZVzuoEwt464hUF3O1qsI65yv41by33q1ClZqwzQVf/u2IYRKCIEiEcIEI8QIB4hQDxCgHhNtmGstLjcMW5IbnJyUtaq2ylWVVqa1TXGw/5srg06Pj4+8PcbJu4EiEcIEI8QIB4hQDxCgHiEAPH+mYd5V9fTzszMyJpb+9rr9WTNtSar7Uc1Cdvigd3VtqvbYrJFi5QpUuAfQQgQjxAgHiFAPEKAeIQA8Y7Fk2qqLdLqE1uqT4gZ5jmH3Vp107puEb4z6HZ5FXcCxCMEiEcIEI8QIB4hQDxCgHhDb5EOmmuRuunGra0tWWvRflQt0mEvmK9OyFandVt8hkHjToB4hADxCAHiEQLEIwSIRwgQ71gstHdaLAAvL9gu7B3a4pFM1SnM6hRpdVp30HvWVnEnQDxCgHiEAPEIAeIRAsQjBIjX5HFNgz6fq7n2nGuRVh8PNei9SKtaTJ+6a3QtUmfQbdDq57YTvn93WcD/FyFAPEKAeIQA8QgB4hECxDsWU6TV1pebMK2qPmW+MhFa/U6c6p6o1e9ymBPFVdwJEI8QIB4hQDxCgHiEAPEIAeIdixape9J6i0f7uDaim7Sstk8rxwx70tLV9vf3Zc39dpXNB1hoDzRACBCPECAeIUA8QoB4hADxmiy0r7Q79/b25DE7Ozuy1uv1ZM217hzX6qzWVGu1RavTcd+zs7u7O/CaUp10reJOgHiEAPEIAeIRAsQjBIjXpDvkhqZUp8c9XHtzc1PWPn/+LGuuEzLMB3YfVlMqw2etuN/n69evsuauc3p6+sjHVIcbHe4EiEcIEI8QIB4hQDxCgHiEAPGatEh///4ta6ql+eXLF3nMhw8fZG15eVnW3OBdtdVWXYeratUn5rTgPrdrRT99+lTWLl26JGsLCwt9Xz937pw8Zn5+XtZGR2t/ztwJEI8QIB4hQDxCgHiEAPEIAeLZnpJr37n1u67d+fz5876vuzboyspK6b22t7dlrdp+rE6KVrYcdNyEaXVttbuWjx8/ypqbMHW/q2qf3rp1Sx4zNTUla9Wn6XAnQDxCgHiEAPEIAeIRAsQjBIhXniJ1Lbr19XVZU1Ofq6ur8pj379/L2tramqy5LQDdxKFrdbrjKgvEq0+Acce5aVD3u7njXCvaLbR376dqc3Nz8pilpSVZKz/gvHQU8D9CCBCPECAeIUA8QoB4hADxyi3S6kJ1tQjf7Tf669cvWXNPqnHX4SYOXa2636VqrZbbeg2mYKuTqa4VvbGxIWszMzN9X3ffSXUTBIc7AeIRAsQjBIhHCBCPECAeIUA82yKtTiqeOXNG1lSLzi2Kd06fPi1rrnXnrr/ahqu0Twd9vsNUJ0zHxsZK53S/gXqc1uTkpDxmYmJC1miRAkWEAPEIAeIRAsQjBIhHCBCvyRSpa5G6mlLdf9JNMLqpyGqr0LXoVDuwsn/pYap7yLprcd/z+Ph46Zzquzx79mzpOmiRAkWEAPEIAeIRAsQjBIhHCBCvPEXqWl9qAXXXdd3Fixf7vv7y5Ut5jJtEdO1MtwhfLfg/7Jyu5qjWZItF5dX2r9tntbppgTvn7Oxs39fPnz9fOh8tUqCIECAeIUA8QoB4hADxCAHiladIXTvKTRVeu3at7+vPnj2Tx6gJzK7rup2dHVlrsVDdtR/dtVT2HHXX71qF1Tau+02rGwJMT0/L2tWrV/u+Pj8/X3qvKu4EiEcIEI8QIB4hQDxCgHjl7pA9qelcXLlype/rqmvUdf4pNtXrcF0G18lxnRc36Ke0eJh3dZCsOiTntk28cOGCrN2+fbvv625NeYs12dwJEI8QIB4hQDxCgHiEAPEIAeI1GaBzbSy1rvTBgwfyGLdW+M2bN7LmBu9c+/Hnz5+y5obk3EPHK9swus+tvseu8y1GN9zonvrjts9cWFiQtfv378va9evX+77utrpsgTsB4hECxCMEiEcIEI8QIB4hQLyRg8ri10NUTunamT9+/JC1169fy9qTJ09k7dWrV7K2vLwsa2/fvpU111pV7Uc3ufnt2zdZc9Osly9flrWlpaVS7c6dO7J27949WVtcXJQ19WDuFhOyDncCxCMEiEcIEI8QIB4hQDxCgHhNWqQV1cuobou4vr4uaysrK7K2uroqa3Nzc0euuSlSN5X67t07WXOL4tXkZtf5aVA3mVp9wHmLdmcFdwLEIwSIRwgQjxAgHiFAPEKAeP8BRNS7BeiP034AAAAASUVORK5CYII=\" id=\"image09286d7aa5\" transform=\"scale(1 -1) translate(0 -138.96)\" x=\"26.925\" y=\"-8.164219\" width=\"138.96\" height=\"138.96\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mac3d2c90d8\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mac3d2c90d8\" x=\"29.4\" y=\"147.124219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.21875 161.722656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mac3d2c90d8\" x=\"78.9\" y=\"147.124219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(72.5375 161.722656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mac3d2c90d8\" x=\"128.4\" y=\"147.124219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(122.0375 161.722656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"mb4bd03e6c6\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb4bd03e6c6\" x=\"26.925\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(13.5625 14.798437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mb4bd03e6c6\" x=\"26.925\" y=\"35.749219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(13.5625 39.548437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mb4bd03e6c6\" x=\"26.925\" y=\"60.499219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(7.2 64.298437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mb4bd03e6c6\" x=\"26.925\" y=\"85.249219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(7.2 89.048437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mb4bd03e6c6\" x=\"26.925\" y=\"109.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(7.2 113.798437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mb4bd03e6c6\" x=\"26.925\" y=\"134.749219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 25 -->\n", "      <g transform=\"translate(7.2 138.548437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 26.925 147.124219 \n", "L 26.925 8.524219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 165.525 147.124219 \n", "L 165.525 8.524219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 26.925 147.124219 \n", "L 165.525 147.124219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 26.925 8.524219 \n", "L 165.525 8.524219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p092a295790\">\n", "   <rect x=\"26.925\" y=\"8.524219\" width=\"138.6\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot average t-shirt\n", "d2l.set_figsize()\n", "d2l.plt.imshow(ave_0.reshape(28, 28).tolist(), cmap='Greys')\n", "d2l.plt.show()"]}, {"cell_type": "markdown", "id": "5ed0be84", "metadata": {"origin_pos": 13}, "source": ["In the second case, we again see that the average resembles a blurry image of trousers.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "909256ed", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:28.247745Z", "iopub.status.busy": "2023-08-18T19:30:28.246995Z", "iopub.status.idle": "2023-08-18T19:30:28.422205Z", "shell.execute_reply": "2023-08-18T19:30:28.421098Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"172.725pt\" height=\"171.002344pt\" viewBox=\"0 0 172.725 171.002344\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:30:28.379952</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 171.002344 \n", "L 172.725 171.002344 \n", "L 172.725 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 26.925 147.124219 \n", "L 165.525 147.124219 \n", "L 165.525 8.524219 \n", "L 26.925 8.524219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p540c0124e8)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image1a2ad3002d\" transform=\"scale(1 -1) translate(0 -138.96)\" x=\"26.925\" y=\"-8.164219\" width=\"138.96\" height=\"138.96\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mc35d0f60bf\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc35d0f60bf\" x=\"29.4\" y=\"147.124219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.21875 161.722656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mc35d0f60bf\" x=\"78.9\" y=\"147.124219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(72.5375 161.722656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mc35d0f60bf\" x=\"128.4\" y=\"147.124219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(122.0375 161.722656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"ma192f08d88\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma192f08d88\" x=\"26.925\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(13.5625 14.798437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#ma192f08d88\" x=\"26.925\" y=\"35.749219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(13.5625 39.548437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#ma192f08d88\" x=\"26.925\" y=\"60.499219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(7.2 64.298437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#ma192f08d88\" x=\"26.925\" y=\"85.249219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(7.2 89.048437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#ma192f08d88\" x=\"26.925\" y=\"109.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(7.2 113.798437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#ma192f08d88\" x=\"26.925\" y=\"134.749219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 25 -->\n", "      <g transform=\"translate(7.2 138.548437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 26.925 147.124219 \n", "L 26.925 8.524219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 165.525 147.124219 \n", "L 165.525 8.524219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 26.925 147.124219 \n", "L 165.525 147.124219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 26.925 8.524219 \n", "L 165.525 8.524219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p540c0124e8\">\n", "   <rect x=\"26.925\" y=\"8.524219\" width=\"138.6\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot average trousers\n", "d2l.plt.imshow(ave_1.reshape(28, 28).tolist(), cmap='Greys')\n", "d2l.plt.show()"]}, {"cell_type": "markdown", "id": "8cae38cb", "metadata": {"origin_pos": 16}, "source": ["In a fully machine learned solution, we would learn the threshold from the dataset.  In this case, I simply eyeballed a threshold that looked good on the training data by hand.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "608ae3a7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:28.426733Z", "iopub.status.busy": "2023-08-18T19:30:28.425880Z", "iopub.status.idle": "2023-08-18T19:30:28.442336Z", "shell.execute_reply": "2023-08-18T19:30:28.440892Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor(0.7870, dtype=torch.float64)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Print test set accuracy with eyeballed threshold\n", "w = (ave_1 - ave_0).T\n", "# '@' is Matrix Multiplication operator in pytorch.\n", "predictions = X_test.reshape(2000, -1) @ (w.flatten()) > -1500000\n", "\n", "# Accuracy\n", "torch.mean((predictions.type(y_test.dtype) == y_test).float(), dtype=torch.float64)"]}, {"cell_type": "markdown", "id": "21d75c35", "metadata": {"origin_pos": 20}, "source": ["## Geometry of Linear Transformations\n", "\n", "Through :numref:`sec_linear-algebra` and the above discussions,\n", "we have a solid understanding of the geometry of vectors, lengths, and angles.\n", "However, there is one important object we have omitted discussing,\n", "and that is a geometric understanding of linear transformations represented by matrices.  Fully internalizing what matrices can do to transform data\n", "between two potentially different high dimensional spaces takes significant practice,\n", "and is beyond the scope of this appendix.\n", "However, we can start building up intuition in two dimensions.\n", "\n", "Suppose that we have some matrix:\n", "\n", "$$\n", "\\mathbf{A} = \\begin{bmatrix}\n", "a & b \\\\ c & d\n", "\\end{bmatrix}.\n", "$$\n", "\n", "If we want to apply this to an arbitrary vector\n", "$\\mathbf{v} = [x, y]^\\top$,\n", "we multiply and see that\n", "\n", "$$\n", "\\begin{aligned}\n", "\\mathbf{A}\\mathbf{v} & = \\begin{bmatrix}a & b \\\\ c & d\\end{bmatrix}\\begin{bmatrix}x \\\\ y\\end{bmatrix} \\\\\n", "& = \\begin{bmatrix}ax+by\\\\ cx+dy\\end{bmatrix} \\\\\n", "& = x\\begin{bmatrix}a \\\\ c\\end{bmatrix} + y\\begin{bmatrix}b \\\\d\\end{bmatrix} \\\\\n", "& = x\\left\\{\\mathbf{A}\\begin{bmatrix}1\\\\0\\end{bmatrix}\\right\\} + y\\left\\{\\mathbf{A}\\begin{bmatrix}0\\\\1\\end{bmatrix}\\right\\}.\n", "\\end{aligned}\n", "$$\n", "\n", "This may seem like an odd computation,\n", "where something clear became somewhat impenetrable.\n", "However, it tells us that we can write the way\n", "that a matrix transforms *any* vector\n", "in terms of how it transforms *two specific vectors*:\n", "$[1,0]^\\top$ and $[0,1]^\\top$.\n", "This is worth considering for a moment.\n", "We have essentially reduced an infinite problem\n", "(what happens to any pair of real numbers)\n", "to a finite one (what happens to these specific vectors).\n", "These vectors are an example a *basis*,\n", "where we can write any vector in our space\n", "as a weighted sum of these *basis vectors*.\n", "\n", "Let's draw what happens when we use the specific matrix\n", "\n", "$$\n", "\\mathbf{A} = \\begin{bmatrix}\n", "1 & 2 \\\\\n", "-1 & 3\n", "\\end{bmatrix}.\n", "$$\n", "\n", "If we look at the specific vector $\\mathbf{v} = [2, -1]^\\top$,\n", "we see this is $2\\cdot[1,0]^\\top + -1\\cdot[0,1]^\\top$,\n", "and thus we know that the matrix $A$ will send this to\n", "$2(\\mathbf{A}[1,0]^\\top) + -1(\\mathbf{A}[0,1])^\\top = 2[1, -1]^\\top - [2,3]^\\top = [0, -5]^\\top$.\n", "If we follow this logic through carefully,\n", "say by considering the grid of all integer pairs of points,\n", "we see that what happens is that the matrix multiplication\n", "can skew, rotate, and scale the grid,\n", "but the grid structure must remain as you see in :numref:`fig_grid-transform`.\n", "\n", "![The matrix $\\mathbf{A}$ acting on the given basis vectors.  Notice how the entire grid is transported along with it.](../img/grid-transform.svg)\n", ":label:`fig_grid-transform`\n", "\n", "This is the most important intuitive point\n", "to internalize about linear transformations represented by matrices.\n", "Matrices are incapable of distorting some parts of space differently than others.\n", "All they can do is take the original coordinates on our space\n", "and skew, rotate, and scale them.\n", "\n", "Some distortions can be severe.  For instance the matrix\n", "\n", "$$\n", "\\mathbf{B} = \\begin{bmatrix}\n", "2 & -1 \\\\ 4 & -2\n", "\\end{bmatrix},\n", "$$\n", "\n", "compresses the entire two-dimensional plane down to a single line.\n", "Identifying and working with such transformations are the topic of a later section,\n", "but geometrically we can see that this is fundamentally different\n", "from the types of transformations we saw above.\n", "For instance, the result from matrix $\\mathbf{A}$ can be \"bent back\" to the original grid.  The results from matrix $\\mathbf{B}$ cannot\n", "because we will never know where the vector $[1,2]^\\top$ came from---was\n", "it $[1,1]^\\top$ or $[0, -1]^\\top$?\n", "\n", "While this picture was for a $2\\times2$ matrix,\n", "nothing prevents us from taking the lessons learned into higher dimensions.\n", "If we take similar basis vectors like $[1,0, \\ldots,0]$\n", "and see where our matrix sends them,\n", "we can start to get a feeling for how the matrix multiplication\n", "distorts the entire space in whatever dimension space we are dealing with.\n", "\n", "## Linear Dependence\n", "\n", "Consider again the matrix\n", "\n", "$$\n", "\\mathbf{B} = \\begin{bmatrix}\n", "2 & -1 \\\\ 4 & -2\n", "\\end{bmatrix}.\n", "$$\n", "\n", "This compresses the entire plane down to live on the single line $y = 2x$.\n", "The question now arises: is there some way we can detect this\n", "just looking at the matrix itself?\n", "The answer is that indeed we can.\n", "Let's take $\\mathbf{b}_1 = [2,4]^\\top$ and $\\mathbf{b}_2 = [-1, -2]^\\top$\n", "be the two columns of $\\mathbf{B}$.\n", "Remember that we can write everything transformed by the matrix $\\mathbf{B}$\n", "as a weighted sum of the columns of the matrix:\n", "like $a_1\\mathbf{b}_1 + a_2\\mathbf{b}_2$.\n", "We call this a *linear combination*.\n", "The fact that $\\mathbf{b}_1 = -2\\cdot\\mathbf{b}_2$\n", "means that we can write any linear combination of those two columns\n", "entirely in terms of say $\\mathbf{b}_2$ since\n", "\n", "$$\n", "a_1\\mathbf{b}_1 + a_2\\mathbf{b}_2 = -2a_1\\mathbf{b}_2 + a_2\\mathbf{b}_2 = (a_2-2a_1)\\mathbf{b}_2.\n", "$$\n", "\n", "This means that one of the columns is, in a sense, redundant\n", "because it does not define a unique direction in space.\n", "This should not surprise us too much\n", "since we already saw that this matrix\n", "collapses the entire plane down into a single line.\n", "Moreover, we see that the linear dependence\n", "$\\mathbf{b}_1 = -2\\cdot\\mathbf{b}_2$ captures this.\n", "To make this more symmetrical between the two vectors, we will write this as\n", "\n", "$$\n", "\\mathbf{b}_1  + 2\\cdot\\mathbf{b}_2 = 0.\n", "$$\n", "\n", "In general, we will say that a collection of vectors\n", "$\\mathbf{v}_1, \\ldots, \\mathbf{v}_k$ are *linearly dependent*\n", "if there exist coefficients $a_1, \\ldots, a_k$ *not all equal to zero* so that\n", "\n", "$$\n", "\\sum_{i=1}^k a_i\\mathbf{v_i} = 0.\n", "$$\n", "\n", "In this case, we can solve for one of the vectors\n", "in terms of some combination of the others,\n", "and effectively render it redundant.\n", "Thus, a linear dependence in the columns of a matrix\n", "is a witness to the fact that our matrix\n", "is compressing the space down to some lower dimension.\n", "If there is no linear dependence we say the vectors are *linearly independent*.\n", "If the columns of a matrix are linearly independent,\n", "no compression occurs and the operation can be undone.\n", "\n", "## Rank\n", "\n", "If we have a general $n\\times m$ matrix,\n", "it is reasonable to ask what dimension space the matrix maps into.\n", "A concept known as the *rank* will be our answer.\n", "In the previous section, we noted that a linear dependence\n", "bears witness to compression of space into a lower dimension\n", "and so we will be able to use this to define the notion of rank.\n", "In particular, the rank of a matrix $\\mathbf{A}$\n", "is the largest number of linearly independent columns\n", "amongst all subsets of columns. For example, the matrix\n", "\n", "$$\n", "\\mathbf{B} = \\begin{bmatrix}\n", "2 & 4 \\\\ -1 & -2\n", "\\end{bmatrix},\n", "$$\n", "\n", "has $\\textrm{rank}(B)=1$, since the two columns are linearly dependent,\n", "but either column by itself is not linearly dependent.\n", "For a more challenging example, we can consider\n", "\n", "$$\n", "\\mathbf{C} = \\begin{bmatrix}\n", "1& 3 & 0 & -1 & 0 \\\\\n", "-1 & 0 & 1 & 1 & -1 \\\\\n", "0 & 3 & 1 & 0 & -1 \\\\\n", "2 & 3 & -1 & -2 & 1\n", "\\end{bmatrix},\n", "$$\n", "\n", "and show that $\\mathbf{C}$ has rank two since, for instance,\n", "the first two columns are linearly independent,\n", "however any of the four collections of three columns are dependent.\n", "\n", "This procedure, as described, is very inefficient.\n", "It requires looking at every subset of the columns of our given matrix,\n", "and thus is potentially exponential in the number of columns.\n", "Later we will see a more computationally efficient way\n", "to compute the rank of a matrix, but for now,\n", "this is sufficient to see that the concept\n", "is well defined and understand the meaning.\n", "\n", "## Invertibility\n", "\n", "We have seen above that multiplication by a matrix with linearly dependent columns\n", "cannot be undone, i.e., there is no inverse operation that can always recover the input.  However, multiplication by a full-rank matrix\n", "(i.e., some $\\mathbf{A}$ that is $n \\times n$ matrix with rank $n$),\n", "we should always be able to undo it.  Consider the matrix\n", "\n", "$$\n", "\\mathbf{I} = \\begin{bmatrix}\n", "1 & 0 & \\cdots & 0 \\\\\n", "0 & 1 & \\cdots & 0 \\\\\n", "\\vdots & \\vdots & \\ddots & \\vdots \\\\\n", "0 & 0 & \\cdots & 1\n", "\\end{bmatrix}.\n", "$$\n", "\n", "which is the matrix with ones along the diagonal, and zeros elsewhere.\n", "We call this the *identity* matrix.\n", "It is the matrix which leaves our data unchanged when applied.\n", "To find a matrix which undoes what our matrix $\\mathbf{A}$ has done,\n", "we want to find a matrix $\\mathbf{A}^{-1}$ such that\n", "\n", "$$\n", "\\mathbf{A}^{-1}\\mathbf{A} = \\mathbf{A}\\mathbf{A}^{-1} =  \\mathbf{I}.\n", "$$\n", "\n", "If we look at this as a system, we have $n \\times n$ unknowns\n", "(the entries of $\\mathbf{A}^{-1}$) and $n \\times n$ equations\n", "(the equality that needs to hold between every entry of the product $\\mathbf{A}^{-1}\\mathbf{A}$ and every entry of $\\mathbf{I}$)\n", "so we should generically expect a solution to exist.\n", "Indeed, in the next section we will see a quantity called the *determinant*,\n", "which has the property that as long as the determinant is not zero, we can find a solution.  We call such a matrix $\\mathbf{A}^{-1}$ the *inverse* matrix.\n", "As an example, if $\\mathbf{A}$ is the general $2 \\times 2$ matrix\n", "\n", "$$\n", "\\mathbf{A} = \\begin{bmatrix}\n", "a & b \\\\\n", "c & d\n", "\\end{bmatrix},\n", "$$\n", "\n", "then we can see that the inverse is\n", "\n", "$$\n", " \\frac{1}{ad-bc}  \\begin{bmatrix}\n", "d & -b \\\\\n", "-c & a\n", "\\end{bmatrix}.\n", "$$\n", "\n", "We can test to see this by seeing that multiplying\n", "by the inverse given by the formula above works in practice.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "ce241194", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:28.446491Z", "iopub.status.busy": "2023-08-18T19:30:28.445780Z", "iopub.status.idle": "2023-08-18T19:30:28.455103Z", "shell.execute_reply": "2023-08-18T19:30:28.453835Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[1., 0.],\n", "        [0., 1.]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["M = torch.tensor([[1, 2], [1, 4]], dtype=torch.float32)\n", "M_inv = torch.tensor([[2, -1], [-0.5, 0.5]])\n", "M_inv @ M"]}, {"cell_type": "markdown", "id": "e135d68f", "metadata": {"origin_pos": 24}, "source": ["### Numerical Issues\n", "While the inverse of a matrix is useful in theory,\n", "we must say that most of the time we do not wish\n", "to *use* the matrix inverse to solve a problem in practice.\n", "In general, there are far more numerically stable algorithms\n", "for solving linear equations like\n", "\n", "$$\n", "\\mathbf{A}\\mathbf{x} = \\mathbf{b},\n", "$$\n", "\n", "than computing the inverse and multiplying to get\n", "\n", "$$\n", "\\mathbf{x} = \\mathbf{A}^{-1}\\mathbf{b}.\n", "$$\n", "\n", "Just as division by a small number can lead to numerical instability,\n", "so can inversion of a matrix which is close to having low rank.\n", "\n", "Moreover, it is common that the matrix $\\mathbf{A}$ is *sparse*,\n", "which is to say that it contains only a small number of non-zero values.\n", "If we were to explore examples, we would see\n", "that this does not mean the inverse is sparse.\n", "Even if $\\mathbf{A}$ was a $1$ million by $1$ million matrix\n", "with only $5$ million non-zero entries\n", "(and thus we need only store those $5$ million),\n", "the inverse will typically have almost every entry non-negative,\n", "requiring us to store all $1\\textrm{M}^2$ entries---that is $1$ trillion entries!\n", "\n", "While we do not have time to dive all the way into the thorny numerical issues\n", "frequently encountered when working with linear algebra,\n", "we want to provide you with some intuition about when to proceed with caution,\n", "and generally avoiding inversion in practice is a good rule of thumb.\n", "\n", "## Determinant\n", "The geometric view of linear algebra gives an intuitive way\n", "to interpret a fundamental quantity known as the *determinant*.\n", "Consider the grid image from before, but now with a highlighted region (:numref:`fig_grid-filled`).\n", "\n", "![The matrix $\\mathbf{A}$ again distorting the grid.  This time, I want to draw particular attention to what happens to the highlighted square.](../img/grid-transform-filled.svg)\n", ":label:`fig_grid-filled`\n", "\n", "Look at the highlighted square.  This is a square with edges given\n", "by $(0, 1)$ and $(1, 0)$ and thus it has area one.\n", "After $\\mathbf{A}$ transforms this square,\n", "we see that it becomes a parallelogram.\n", "There is no reason this parallelogram should have the same area\n", "that we started with, and indeed in the specific case shown here of\n", "\n", "$$\n", "\\mathbf{A} = \\begin{bmatrix}\n", "1 & 2 \\\\\n", "-1 & 3\n", "\\end{bmatrix},\n", "$$\n", "\n", "it is an exercise in coordinate geometry to compute\n", "the area of this parallelogram and obtain that the area is $5$.\n", "\n", "In general, if we have a matrix\n", "\n", "$$\n", "\\mathbf{A} = \\begin{bmatrix}\n", "a & b \\\\\n", "c & d\n", "\\end{bmatrix},\n", "$$\n", "\n", "we can see with some computation that the area\n", "of the resulting parallelogram is $ad-bc$.\n", "This area is referred to as the *determinant*.\n", "\n", "Let's check this quickly with some example code.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "42a61cd7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:28.460420Z", "iopub.status.busy": "2023-08-18T19:30:28.459512Z", "iopub.status.idle": "2023-08-18T19:30:28.468439Z", "shell.execute_reply": "2023-08-18T19:30:28.467020Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor(5.)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.det(torch.tensor([[1, -1], [2, 3]], dtype=torch.float32))"]}, {"cell_type": "markdown", "id": "e6d888e4", "metadata": {"origin_pos": 28}, "source": ["The eagle-eyed amongst us will notice\n", "that this expression can be zero or even negative.\n", "For the negative term, this is a matter of convention\n", "taken generally in mathematics:\n", "if the matrix flips the figure,\n", "we say the area is negated.\n", "Let's see now that when the determinant is zero, we learn more.\n", "\n", "Let's consider\n", "\n", "$$\n", "\\mathbf{B} = \\begin{bmatrix}\n", "2 & 4 \\\\ -1 & -2\n", "\\end{bmatrix}.\n", "$$\n", "\n", "If we compute the determinant of this matrix,\n", "we get $2\\cdot(-2 ) - 4\\cdot(-1) = 0$.\n", "Given our understanding above, this makes sense.\n", "$\\mathbf{B}$ compresses the square from the original image\n", "down to a line segment, which has zero area.\n", "And indeed, being compressed into a lower dimensional space\n", "is the only way to have zero area after the transformation.\n", "Thus we see the following result is true:\n", "a matrix $A$ is invertible if and only if\n", "the determinant is not equal to zero.\n", "\n", "As a final comment, imagine that we have any figure drawn on the plane.\n", "Thinking like computer scientists, we can decompose\n", "that figure into a collection of little squares\n", "so that the area of the figure is in essence\n", "just the number of squares in the decomposition.\n", "If we now transform that figure by a matrix,\n", "we send each of these squares to parallelograms,\n", "each one of which has area given by the determinant.\n", "We see that for any figure, the determinant gives the (signed) number\n", "that a matrix scales the area of any figure.\n", "\n", "Computing determinants for larger matrices can be laborious,\n", "but the  intuition is the same.\n", "The determinant remains the factor\n", "that $n\\times n$ matrices scale $n$-dimensional volumes.\n", "\n", "## Tensors and Common Linear Algebra Operations\n", "\n", "In :numref:`sec_linear-algebra` the concept of tensors was introduced.\n", "In this section, we will dive more deeply into tensor contractions\n", "(the tensor equivalent of matrix multiplication),\n", "and see how it can provide a unified view\n", "on a number of matrix and vector operations.\n", "\n", "With matrices and vectors we knew how to multiply them to transform data.\n", "We need to have a similar definition for tensors if they are to be useful to us.\n", "Think about matrix multiplication:\n", "\n", "$$\n", "\\mathbf{C} = \\mathbf{A}\\mathbf{B},\n", "$$\n", "\n", "or equivalently\n", "\n", "$$ c_{i, j} = \\sum_{k} a_{i, k}b_{k, j}.$$\n", "\n", "This pattern is one we can repeat for tensors.\n", "For tensors, there is no one case of what\n", "to sum over that can be universally chosen,\n", "so we need specify exactly which indices we want to sum over.\n", "For instance we could consider\n", "\n", "$$\n", "y_{il} = \\sum_{jk} x_{ijkl}a_{jk}.\n", "$$\n", "\n", "Such a transformation is called a *tensor contraction*.\n", "It can represent a far more flexible family of transformations\n", "that matrix multiplication alone.\n", "\n", "As a often-used notational simplification,\n", "we can notice that the sum is over exactly those indices\n", "that occur more than once in the expression,\n", "thus people often work with *Einstein notation*,\n", "where the summation is implicitly taken over all repeated indices.\n", "This gives the compact expression:\n", "\n", "$$\n", "y_{il} = x_{ijkl}a_{jk}.\n", "$$\n", "\n", "### Common Examples from Linear Algebra\n", "\n", "Let's see how many of the linear algebraic definitions\n", "we have seen before can be expressed in this compressed tensor notation:\n", "\n", "* $\\mathbf{v} \\cdot \\mathbf{w} = \\sum_i v_iw_i$\n", "* $\\|\\mathbf{v}\\|_2^{2} = \\sum_i v_iv_i$\n", "* $(\\mathbf{A}\\mathbf{v})_i = \\sum_j a_{ij}v_j$\n", "* $(\\mathbf{A}\\mathbf{B})_{ik} = \\sum_j a_{ij}b_{jk}$\n", "* $\\textrm{tr}(\\mathbf{A}) = \\sum_i a_{ii}$\n", "\n", "In this way, we can replace a myriad of specialized notations with short tensor expressions.\n", "\n", "### Expressing in Code\n", "Tensors may flexibly be operated on in code as well.\n", "As seen in :numref:`sec_linear-algebra`,\n", "we can create tensors as is shown below.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "60092e5c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:28.472962Z", "iopub.status.busy": "2023-08-18T19:30:28.471989Z", "iopub.status.idle": "2023-08-18T19:30:28.482336Z", "shell.execute_reply": "2023-08-18T19:30:28.481119Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(torch.<PERSON><PERSON>([2, 2]), torch.<PERSON><PERSON>([2, 2, 3]), torch.<PERSON><PERSON>([2]))"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Define tensors\n", "B = torch.tensor([[[1, 2, 3], [4, 5, 6]], [[7, 8, 9], [10, 11, 12]]])\n", "A = torch.tensor([[1, 2], [3, 4]])\n", "v = torch.tensor([1, 2])\n", "\n", "# Print out the shapes\n", "A.shape, B.shape, v.shape"]}, {"cell_type": "markdown", "id": "7fdcbd57", "metadata": {"origin_pos": 32}, "source": ["Einstein summation has been implemented directly.\n", "The indices that occurs in the Einstein summation can be passed as a string,\n", "followed by the tensors that are being acted upon.\n", "For instance, to implement matrix multiplication,\n", "we can consider the Einstein summation seen above\n", "($\\mathbf{A}\\mathbf{v} = a_{ij}v_j$)\n", "and strip out the indices themselves to get the implementation:\n"]}, {"cell_type": "code", "execution_count": 10, "id": "0b47b53c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:28.487310Z", "iopub.status.busy": "2023-08-18T19:30:28.486600Z", "iopub.status.idle": "2023-08-18T19:30:28.493572Z", "shell.execute_reply": "2023-08-18T19:30:28.492583Z"}, "origin_pos": 34, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(tensor([ 5, 11]), tensor([ 5, 11]))"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Reimplement matrix multiplication\n", "torch.einsum(\"ij, j -> i\", A, v), A@v"]}, {"cell_type": "markdown", "id": "9a9cb807", "metadata": {"origin_pos": 36}, "source": ["This is a highly flexible notation.\n", "For instance if we want to compute\n", "what would be traditionally written as\n", "\n", "$$\n", "c_{kl} = \\sum_{ij} \\mathbf{b}_{ijk}\\mathbf{a}_{il}v_j.\n", "$$\n", "\n", "it can be implemented via Einstein summation as:\n"]}, {"cell_type": "code", "execution_count": 11, "id": "ba403495", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:28.498150Z", "iopub.status.busy": "2023-08-18T19:30:28.497621Z", "iopub.status.idle": "2023-08-18T19:30:28.507270Z", "shell.execute_reply": "2023-08-18T19:30:28.506042Z"}, "origin_pos": 38, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[ 90, 126],\n", "        [102, 144],\n", "        [114, 162]])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.einsum(\"ijk, il, j -> kl\", B, A, v)"]}, {"cell_type": "markdown", "id": "5bcfabad", "metadata": {"origin_pos": 40}, "source": ["This notation is readable and efficient for humans,\n", "however bulky if for whatever reason\n", "we need to generate a tensor contraction programmatically.\n", "For this reason, `einsum` provides an alternative notation\n", "by providing integer indices for each tensor.\n", "For example, the same tensor contraction can also be written as:\n"]}, {"cell_type": "code", "execution_count": 12, "id": "aa6b3ca6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:28.511952Z", "iopub.status.busy": "2023-08-18T19:30:28.511061Z", "iopub.status.idle": "2023-08-18T19:30:28.516610Z", "shell.execute_reply": "2023-08-18T19:30:28.515414Z"}, "origin_pos": 42, "tab": ["pytorch"]}, "outputs": [], "source": ["# PyTorch does not support this type of notation."]}, {"cell_type": "markdown", "id": "a12c501b", "metadata": {"origin_pos": 44}, "source": ["Either notation allows for concise and efficient representation of tensor contractions in code.\n", "\n", "## Summary\n", "* Vectors can be interpreted geometrically as either points or directions in space.\n", "* Dot products define the notion of angle to arbitrarily high-dimensional spaces.\n", "* Hyperplanes are high-dimensional generalizations of lines and planes.  They can be used to define decision planes that are often used as the last step in a classification task.\n", "* Matrix multiplication can be geometrically interpreted as uniform distortions of the underlying coordinates. They represent a very restricted, but mathematically clean, way to transform vectors.\n", "* Linear dependence is a way to tell when a collection of vectors are in a lower dimensional space than we would expect (say you have $3$ vectors living in a $2$-dimensional space). The rank of a matrix is the size of the largest subset of its columns that are linearly independent.\n", "* When a matrix's inverse is defined, matrix inversion allows us to find another matrix that undoes the action of the first. Matrix inversion is useful in theory, but requires care in practice owing to numerical instability.\n", "* Determinants allow us to measure how much a matrix expands or contracts a space. A nonzero determinant implies an invertible (non-singular) matrix and a zero-valued determinant means that the matrix is non-invertible (singular).\n", "* Tensor contractions and Einstein summation provide for a neat and clean notation for expressing many of the computations that are seen in machine learning.\n", "\n", "## Exercises\n", "1. What is the angle between\n", "$$\n", "\\vec v_1 = \\begin{bmatrix}\n", "1 \\\\ 0 \\\\ -1 \\\\ 2\n", "\\end{bmatrix}, \\qquad \\vec v_2 = \\begin{bmatrix}\n", "3 \\\\ 1 \\\\ 0 \\\\ 1\n", "\\end{bmatrix}?\n", "$$\n", "2. True or false: $\\begin{bmatrix}1 & 2\\\\0&1\\end{bmatrix}$ and $\\begin{bmatrix}1 & -2\\\\0&1\\end{bmatrix}$ are inverses of one another?\n", "3. Suppose that we draw a shape in the plane with area $100\\textrm{m}^2$.  What is the area after transforming the figure by the matrix\n", "$$\n", "\\begin{bmatrix}\n", "2 & 3\\\\\n", "1 & 2\n", "\\end{bmatrix}.\n", "$$\n", "4. Which of the following sets of vectors are linearly independent?\n", " * $\\left\\{\\begin{pmatrix}1\\\\0\\\\-1\\end{pmatrix}, \\begin{pmatrix}2\\\\1\\\\-1\\end{pmatrix}, \\begin{pmatrix}3\\\\1\\\\1\\end{pmatrix}\\right\\}$\n", " * $\\left\\{\\begin{pmatrix}3\\\\1\\\\1\\end{pmatrix}, \\begin{pmatrix}1\\\\1\\\\1\\end{pmatrix}, \\begin{pmatrix}0\\\\0\\\\0\\end{pmatrix}\\right\\}$\n", " * $\\left\\{\\begin{pmatrix}1\\\\1\\\\0\\end{pmatrix}, \\begin{pmatrix}0\\\\1\\\\-1\\end{pmatrix}, \\begin{pmatrix}1\\\\0\\\\1\\end{pmatrix}\\right\\}$\n", "5. Suppose that you have a matrix written as $A = \\begin{bmatrix}c\\\\d\\end{bmatrix}\\cdot\\begin{bmatrix}a & b\\end{bmatrix}$ for some choice of values $a, b, c$, and $d$.  True or false: the determinant of such a matrix is always $0$?\n", "6. The vectors $e_1 = \\begin{bmatrix}1\\\\0\\end{bmatrix}$ and $e_2 = \\begin{bmatrix}0\\\\1\\end{bmatrix}$ are orthogonal.  What is the condition on a matrix $A$ so that $Ae_1$ and $Ae_2$ are orthogonal?\n", "7. How can you write $\\textrm{tr}(\\mathbf{A}^4)$ in Einstein notation for an arbitrary matrix $A$?\n"]}, {"cell_type": "markdown", "id": "7d98bab2", "metadata": {"origin_pos": 46, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1084)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
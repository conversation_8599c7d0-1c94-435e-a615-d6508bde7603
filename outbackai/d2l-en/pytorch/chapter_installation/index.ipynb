{"cells": [{"cell_type": "markdown", "id": "b2c31cd7", "metadata": {"origin_pos": 0}, "source": ["# Installation\n", ":label:`chap_installation`\n", "\n", "In order to get up and running,\n", "we will need an environment for running Python,\n", "the Jupyter Notebook, the relevant libraries,\n", "and the code needed to run the book itself.\n", "\n", "## Installing Miniconda\n", "\n", "Your simplest option is to install\n", "[Miniconda](https://conda.io/en/latest/miniconda.html).\n", "Note that the Python 3.x version is required.\n", "You can skip the following steps\n", "if your machine already has conda installed.\n", "\n", "Visit the Miniconda website and determine\n", "the appropriate version for your system\n", "based on your Python 3.x version and machine architecture.\n", "Suppose that your Python version is 3.9\n", "(our tested version).\n", "If you are using macOS,\n", "you would download the bash script\n", "whose name contains the strings \"MacOSX\",\n", "navigate to the download location,\n", "and execute the installation as follows\n", "(taking Intel Macs as an example):\n", "\n", "```bash\n", "# The file name is subject to changes\n", "sh Miniconda3-py39_4.12.0-MacOSX-x86_64.sh -b\n", "```\n", "\n", "A Linux user\n", "would download the file\n", "whose name contains the strings \"Linux\"\n", "and execute the following at the download location:\n", "\n", "```bash\n", "# The file name is subject to changes\n", "sh Miniconda3-py39_4.12.0-Linux-x86_64.sh -b\n", "```\n", "\n", "A Windows user would download and install Miniconda by following its [online instructions](https://conda.io/en/latest/miniconda.html).\n", "On Windows, you may search for `cmd` to open the Command Prompt (command-line interpreter) for running commands.\n", "\n", "Next, initialize the shell so we can run `conda` directly.\n", "\n", "```bash\n", "~/miniconda3/bin/conda init\n", "```\n", "\n", "Then close and reopen your current shell.\n", "You should be able to create\n", "a new environment as follows:\n", "\n", "```bash\n", "conda create --name d2l python=3.9 -y\n", "```\n", "\n", "Now we can activate the `d2l` environment:\n", "\n", "```bash\n", "conda activate d2l\n", "```\n", "\n", "## Installing the Deep Learning Framework and the `d2l` Package\n", "\n", "Before installing any deep learning framework,\n", "please first check whether or not\n", "you have proper GPUs on your machine\n", "(the GPUs that power the display\n", "on a standard laptop are not relevant for our purposes).\n", "For example,\n", "if your computer has NVIDIA GPUs and has installed [CUDA](https://developer.nvidia.com/cuda-downloads),\n", "then you are all set.\n", "If your machine does not house any GPU,\n", "there is no need to worry just yet.\n", "Your CPU provides more than enough horsepower\n", "to get you through the first few chapters.\n", "Just remember that you will want to access GPUs\n", "before running larger models.\n"]}, {"cell_type": "markdown", "id": "f2302a86", "metadata": {"origin_pos": 2, "tab": ["pytorch"]}, "source": ["You can install PyTorch (the specified versions are tested at the time of writing) with either CPU or GPU support as follows:\n", "\n", "```bash\n", "pip install torch==2.0.0 torchvision==0.15.1\n", "```\n"]}, {"cell_type": "markdown", "id": "7f91263d", "metadata": {"origin_pos": 5}, "source": ["Our next step is to install\n", "the `d2l` package that we developed\n", "in order to encapsulate\n", "frequently used functions and classes\n", "found throughout this book:\n", "\n", "```bash\n", "pip install d2l==1.0.3\n", "```\n", "\n", "## Downloading and Running the Code\n", "\n", "Next, you will want to download the notebooks\n", "so that you can run each of the book's code blocks.\n", "Simply click on the \"Notebooks\" tab at the top\n", "of any HTML page on [the D2L.ai website](https://d2l.ai/)\n", "to download the code and then unzip it.\n", "Alternatively, you can fetch the notebooks\n", "from the command line as follows:\n"]}, {"cell_type": "markdown", "id": "461f11df", "metadata": {"origin_pos": 7, "tab": ["pytorch"]}, "source": ["```bash\n", "mkdir d2l-en && cd d2l-en\n", "curl https://d2l.ai/d2l-en-1.0.3.zip -o d2l-en.zip\n", "unzip d2l-en.zip && rm d2l-en.zip\n", "cd pytorch\n", "```\n"]}, {"cell_type": "markdown", "id": "75f2fcaf", "metadata": {"origin_pos": 10}, "source": ["If you do not already have `unzip` installed, first run `sudo apt-get install unzip`.\n", "Now we can start the Jupyter Notebook server by running:\n", "\n", "```bash\n", "jupyter notebook\n", "```\n", "\n", "At this point, you can open http://localhost:8888\n", "(it may have already opened automatically) in your web browser.\n", "Then we can run the code for each section of the book.\n", "Whenever you open a new command line window,\n", "you will need to execute `conda activate d2l`\n", "to activate the runtime environment\n", "before running the D2L notebooks,\n", "or updating your packages\n", "(either the deep learning framework\n", "or the `d2l` package).\n", "To exit the environment,\n", "run `conda deactivate`.\n"]}, {"cell_type": "markdown", "id": "cedf31bd", "metadata": {"origin_pos": 12, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/24)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
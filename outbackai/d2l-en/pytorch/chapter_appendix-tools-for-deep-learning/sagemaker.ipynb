{"cells": [{"cell_type": "markdown", "id": "f2c0e562", "metadata": {"origin_pos": 0}, "source": ["# Using Amazon SageMaker\n", ":label:`sec_sagemaker`\n", "\n", "Deep learning applications\n", "may demand so much computational resource\n", "that easily goes beyond\n", "what your local machine can offer.\n", "Cloud computing services\n", "allow you to \n", "run GPU-intensive code of this book\n", "more easily\n", "using more powerful computers.\n", "This section will introduce \n", "how to use Amazon SageMaker\n", "to run the code of this book.\n", "\n", "## Signing Up\n", "\n", "First, we need to sign up an account at https://aws.amazon.com/.\n", "For additional security,\n", "using two-factor authentication \n", "is encouraged.\n", "It is also a good idea to\n", "set up detailed billing and spending alerts to\n", "avoid any surprise,\n", "e.g., \n", "when forgetting to stop running instances.\n", "After logging into your AWS account, \n", "go to your [console](http://console.aws.amazon.com/) and search for \"Amazon SageMaker\" (see :numref:`fig_sagemaker`), \n", "then click it to open the SageMaker panel.\n", "\n", "![Search for and open the SageMaker panel.](../img/sagemaker.png)\n", ":width:`300px`\n", ":label:`fig_sagemaker`\n", "\n", "## Creating a SageMaker Instance\n", "\n", "Next, let's create a notebook instance as described in :numref:`fig_sagemaker-create`.\n", "\n", "![Create a SageMaker instance.](../img/sagemaker-create.png)\n", ":width:`400px`\n", ":label:`fig_sagemaker-create`\n", "\n", "SageMaker provides multiple [instance types](https://aws.amazon.com/sagemaker/pricing/instance-types/) with varying computational power and prices.\n", "When creating a notebook instance,\n", "we can specify its name and type.\n", "In :numref:`fig_sagemaker-create-2`, we choose `ml.p3.2xlarge`: with one Tesla V100 GPU and an 8-core CPU, this instance is powerful enough for most of the book.\n", "\n", "![Choose the instance type.](../img/sagemaker-create-2.png)\n", ":width:`400px`\n", ":label:`fig_sagemaker-create-2`\n"]}, {"cell_type": "markdown", "id": "92dfeb02", "metadata": {"origin_pos": 2, "tab": ["pytorch"]}, "source": ["The entire book in the ipynb format for running with SageMaker is available at https://github.com/d2l-ai/d2l-pytorch-sagemaker. We can specify this GitHub repository URL (:numref:`fig_sagemaker-create-3`) to allow SageMaker to clone it when creating the instance.\n"]}, {"cell_type": "markdown", "id": "35a9483d", "metadata": {"origin_pos": 4}, "source": ["![Specify the GitHub repository.](../img/sagemaker-create-3.png)\n", ":width:`400px`\n", ":label:`fig_sagemaker-create-3`\n", "\n", "## Running and Stopping an Instance\n", "\n", "Creating an instance\n", "may take a few minutes.\n", "When it is ready,\n", "click on the \"Open Jupyter\" link next to it (:numref:`fig_sagemaker-open`) so you can\n", "edit and run all the <PERSON><PERSON><PERSON> notebooks\n", "of this book on this instance\n", "(similar to steps in :numref:`sec_jupyter`).\n", "\n", "![Open Jupyter on the created SageMaker instance.](../img/sagemaker-open.png)\n", ":width:`400px`\n", ":label:`fig_sagemaker-open`\n", "\n", "\n", "After finishing your work,\n", "do not forget to stop the instance to avoid \n", "being charged further (:numref:`fig_sagemaker-stop`).\n", "\n", "![Stop a SageMaker instance.](../img/sagemaker-stop.png)\n", ":width:`300px`\n", ":label:`fig_sagemaker-stop`\n", "\n", "## Updating Notebooks\n"]}, {"cell_type": "markdown", "id": "f85f5541", "metadata": {"origin_pos": 6, "tab": ["pytorch"]}, "source": ["Notebooks of this open-source book will be regularly updated in the [d2l-ai/d2l-pytorch-sagemaker](https://github.com/d2l-ai/d2l-pytorch-sagemaker) repository\n", "on GitHub.\n", "To update to the latest version,\n", "you may open a terminal on the SageMaker instance (:numref:`fig_sagemaker-terminal`).\n"]}, {"cell_type": "markdown", "id": "b510e4f3", "metadata": {"origin_pos": 8}, "source": ["![Open a terminal on the SageMaker instance.](../img/sagemaker-terminal.png)\n", ":width:`300px`\n", ":label:`fig_sagemaker-terminal`\n", "\n", "You may wish to commit your local changes before pulling updates from the remote repository. \n", "Otherwise, simply discard all your local changes\n", "with the following commands in the terminal:\n"]}, {"cell_type": "markdown", "id": "480c9c70", "metadata": {"origin_pos": 10, "tab": ["pytorch"]}, "source": ["```bash\n", "cd SageMaker/d2l-pytorch-sagemaker/\n", "git reset --hard\n", "git pull\n", "```\n"]}, {"cell_type": "markdown", "id": "a6b57240", "metadata": {"origin_pos": 12}, "source": ["## Summary\n", "\n", "* We can create a notebook instance using Amazon SageMaker to run GPU-intensive code of this book.\n", "* We can update notebooks via the terminal on the Amazon SageMaker instance.\n", "\n", "\n", "## Exercises\n", "\n", "\n", "1. Edit and run any section that requires a GPU using Amazon SageMaker.\n", "1. Open a terminal to access the local directory that hosts all the notebooks of this book.\n", "\n", "\n", "[Discussions](https://discuss.d2l.ai/t/422)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
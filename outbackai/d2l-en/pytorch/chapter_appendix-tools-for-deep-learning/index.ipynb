{"cells": [{"cell_type": "markdown", "id": "488fe751", "metadata": {"origin_pos": 0}, "source": ["# Appendix: Tools for Deep Learning\n", ":label:`chap_appendix_tools`\n", "\n", "\n", "To get the most out of *Dive into Deep Learning*,\n", "we will talk you through different tools\n", "in this appendix,\n", "such as\n", "for running and contributing to this\n", "interactive open-source book.\n", "\n", ":begin_tab:toc\n", " - [jupyter](jupyter.ipynb)\n", " - [sagemaker](sagemaker.ipynb)\n", " - [aws](aws.ipynb)\n", " - [colab](colab.ipynb)\n", " - [selecting-servers-gpus](selecting-servers-gpus.ipynb)\n", " - [contributing](contributing.ipynb)\n", " - [utils](utils.ipynb)\n", " - [d2l](d2l.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
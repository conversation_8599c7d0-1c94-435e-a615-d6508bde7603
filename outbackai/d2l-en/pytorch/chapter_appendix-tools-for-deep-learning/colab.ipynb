{"cells": [{"cell_type": "markdown", "id": "35c5f97a", "metadata": {"origin_pos": 0}, "source": ["# Using Google Colab\n", ":label:`sec_colab`\n", "\n", "We introduced how to run this book on AWS in :numref:`sec_sagemaker` and :numref:`sec_aws`. Another option is running this book on [Google Colab](https://colab.research.google.com/)\n", "if you have a Google account.\n", "\n", "To run the code of a section on Colab, simply click the `Colab` button as shown in :numref:`fig_colab`. \n", "\n", "![Run the code of a section on Colab](../img/colab.png)\n", ":width:`300px`\n", ":label:`fig_colab`\n", "\n", "\n", "If it is your first time to run a code cell,\n", "you will receive a warning message as shown in :numref:`fig_colab2`.\n", "Just click \"RUN ANYWAY\" to ignore it.\n", "\n", "![Ignore the warning message by clicking \"RUN ANYWAY\".](../img/colab-2.png)\n", ":width:`300px`\n", ":label:`fig_colab2`\n", "\n", "Next, <PERSON><PERSON> will connect you to an instance to run the code of this section.\n", "Specifically, if a GPU is needed, \n", "Colab will be automatically requested \n", "for connecting to a GPU instance.\n", "\n", "\n", "## Summary\n", "\n", "* You can use Google Colab to run each section's code in this book.\n", "* Colab will be requested to connect to a GPU instance if a GPU is needed in any section of this book.\n", "\n", "\n", "## Exercises\n", "\n", "1. Open any section of this book using Google Colab.\n", "1. Edit and run any section that requires a GPU using Google Colab.\n", "\n", "\n", "[Discussions](https://discuss.d2l.ai/t/424)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
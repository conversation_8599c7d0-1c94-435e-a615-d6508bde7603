{"cells": [{"cell_type": "markdown", "id": "4811b4ba", "metadata": {"origin_pos": 0}, "source": ["# Using Jupyter Notebooks\n", ":label:`sec_jupyter`\n", "\n", "\n", "This section describes how to edit and run the code\n", "in each section of this book\n", "using the Jupyter Notebook. Make sure you have\n", "installed Jupy<PERSON> and downloaded the\n", "code as described in\n", ":ref:`chap_installation`.\n", "If you want to know more about <PERSON><PERSON><PERSON> see the excellent tutorial in\n", "their [documentation](https://jupyter.readthedocs.io/en/latest/).\n", "\n", "\n", "## Editing and Running the Code Locally\n", "\n", "Suppose that the local path of the book's code is `xx/yy/d2l-en/`. Use the shell to change the directory to this path (`cd xx/yy/d2l-en`) and run the command `jupyter notebook`. If your browser does not do this automatically, open http://localhost:8888 and you will see the interface of <PERSON><PERSON><PERSON> and all the folders containing the code of the book, as shown in :numref:`fig_jupyter00`.\n", "\n", "![The folders containing the code of this book.](../img/jupyter00.png)\n", ":width:`600px`\n", ":label:`fig_jupyter00`\n", "\n", "\n", "You can access the notebook files by clicking on the folder displayed on the webpage.\n", "They usually have the suffix \".ipynb\".\n", "For the sake of brevity, we create a temporary \"test.ipynb\" file.\n", "The content displayed after you click it is\n", "shown in :numref:`fig_jupyter01`.\n", "This notebook includes a markdown cell and a code cell. The content in the markdown cell includes \"This Is a Title\" and \"This is text.\".\n", "The code cell contains two lines of Python code.\n", "\n", "![Markdown and code cells in the \"text.ipynb\" file.](../img/jupyter01.png)\n", ":width:`600px`\n", ":label:`fig_jupyter01`\n", "\n", "\n", "Double click on the markdown cell to enter edit mode.\n", "Add a new text string \"Hello world.\" at the end of the cell, as shown in :numref:`fig_jupyter02`.\n", "\n", "![Edit the markdown cell.](../img/jupyter02.png)\n", ":width:`600px`\n", ":label:`fig_jupyter02`\n", "\n", "\n", "As demonstrated in :numref:`fig_jupyter03`,\n", "click \"Cell\" $\\rightarrow$ \"Run Cells\" in the menu bar to run the edited cell.\n", "\n", "![Run the cell.](../img/jupyter03.png)\n", ":width:`600px`\n", ":label:`fig_jupyter03`\n", "\n", "After running, the markdown cell is shown in :numref:`fig_jupyter04`.\n", "\n", "![The markdown cell after running.](../img/jupyter04.png)\n", ":width:`600px`\n", ":label:`fig_jupyter04`\n", "\n", "\n", "Next, click on the code cell. Multiply the elements by 2 after the last line of code, as shown in :numref:`fig_jupyter05`.\n", "\n", "![Edit the code cell.](../img/jupyter05.png)\n", ":width:`600px`\n", ":label:`fig_jupyter05`\n", "\n", "\n", "You can also run the cell with a shortcut (\"Ctrl + Enter\" by default) and obtain the output result from :numref:`fig_jupyter06`.\n", "\n", "![Run the code cell to obtain the output.](../img/jupyter06.png)\n", ":width:`600px`\n", ":label:`fig_jupyter06`\n", "\n", "\n", "When a notebook contains more cells, we can click \"Kernel\" $\\rightarrow$ \"Restart & Run All\" in the menu bar to run all the cells in the entire notebook. By clicking \"Help\" $\\rightarrow$ \"Edit Keyboard Shortcuts\" in the menu bar, you can edit the shortcuts according to your preferences.\n", "\n", "## Advanced Options\n", "\n", "Beyond local editing two things are quite important: editing the notebooks in the markdown format and running Ju<PERSON><PERSON> remotely.\n", "The latter matters when we want to run the code on a faster server.\n", "The former matters since <PERSON><PERSON><PERSON>'s native ipynb format stores a lot of auxiliary data that is\n", "irrelevant to the content,\n", "mostly related to how and where the code is run.\n", "This is confusing for <PERSON><PERSON>, making\n", "reviewing contributions very difficult.\n", "Fortunately there is an alternative---native editing in the markdown format.\n", "\n", "### Markdown Files in Jupyter\n", "\n", "If you wish to contribute to the content of this book, you need to modify the\n", "source file (md file, not ipynb file) on GitHub.\n", "Using the notedown plugin we\n", "can modify notebooks in the md format directly in Jupyter.\n", "\n", "\n", "First, install the notedown plugin, run the Jupyter Notebook, and load the plugin:\n", "\n", "```\n", "pip install d2l-notedown  # You may need to uninstall the original notedown.\n", "jupyter notebook --NotebookApp.contents_manager_class='notedown.NotedownContentsManager'\n", "```\n", "\n", "You may also turn on the notedown plugin by default whenever you run the Jupyter Notebook.\n", "First, generate a Jupyter Notebook configuration file (if it has already been generated, you can skip this step).\n", "\n", "```\n", "jupyter notebook --generate-config\n", "```\n", "\n", "Then, add the following line to the end of the Jupyter Notebook configuration file (for Linux or macOS, usually in the path `~/.jupyter/jupyter_notebook_config.py`):\n", "\n", "```\n", "c.NotebookApp.contents_manager_class = 'notedown.NotedownContentsManager'\n", "```\n", "\n", "After that, you only need to run the `jupyter notebook` command to turn on the notedown plugin by default.\n", "\n", "### Running Jupyter Notebooks on a Remote Server\n", "\n", "Sometimes, you may want to run Jupyter notebooks on a remote server and access it through a browser on your local computer. If Linux or macOS is installed on your local machine (Windows can also support this function through third-party software such as PuTTY), you can use port forwarding:\n", "\n", "```\n", "ssh myserver -L 8888:localhost:8888\n", "```\n", "\n", "The above string `myserver` is the address of the remote server.\n", "Then we can use http://localhost:8888 to access the remote server `myserver` that runs Jupyter notebooks. We will detail on how to run Jupyter notebooks on AWS instances\n", "later in this appendix.\n", "\n", "### Timing\n", "\n", "We can use the `ExecuteTime` plugin to time the execution of each code cell in Jupyter notebooks.\n", "Use the following commands to install the plugin:\n", "\n", "```\n", "pip install jupyter_contrib_nbextensions\n", "jupyter contrib nbextension install --user\n", "jupyter nbextension enable execute_time/ExecuteTime\n", "```\n", "\n", "## Summary\n", "\n", "* Using the Jupyter Notebook tool, we can edit, run, and contribute to each section of the book.\n", "* We can run Jupyter notebooks on remote servers using port forwarding.\n", "\n", "\n", "## Exercises\n", "\n", "1. Edit and run the code in this book with the Jupyter Notebook on your local machine.\n", "1. Edit and run the code in this book with the Jupyter Notebook *remotely* via port forwarding.\n", "1. Compare the running time of the operations $\\mathbf{A}^\\top \\mathbf{B}$ and $\\mathbf{A} \\mathbf{B}$ for two square matrices in $\\mathbb{R}^{1024 \\times 1024}$. Which one is faster?\n", "\n", "\n", "[Discussions](https://discuss.d2l.ai/t/421)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "1dcb4584", "metadata": {"origin_pos": 0}, "source": ["# Using AWS EC2 Instances\n", ":label:`sec_aws`\n", "\n", "In this section, we will show you how to install all libraries on a raw Linux machine. Recall that in :numref:`sec_sagemaker` we discussed how to use Amazon SageMaker, while building an instance by yourself costs less on AWS. The walkthrough includes three steps:\n", "\n", "1. Request for a GPU Linux instance from AWS EC2.\n", "1. Install CUDA (or use an Amazon Machine Image with preinstalled CUDA).\n", "1. Install the deep learning framework and other libraries for running the code of the book.\n", "\n", "This process applies to other instances (and other clouds), too, albeit with some minor modifications. Before going forward, you need to create an AWS account, see :numref:`sec_sagemaker` for more details.\n", "\n", "\n", "## Creating and Running an EC2 Instance\n", "\n", "After logging into your AWS account, click \"EC2\" (:numref:`fig_aws`) to go to the EC2 panel.\n", "\n", "![Open the EC2 console.](../img/aws.png)\n", ":width:`400px`\n", ":label:`fig_aws`\n", "\n", ":numref:`fig_ec2` shows the EC2 panel.\n", "\n", "![The EC2 panel.](../img/ec2.png)\n", ":width:`700px`\n", ":label:`fig_ec2`\n", "\n", "### Presetting Location\n", "Select a nearby data center to reduce latency, e.g., \"Oregon\" (marked by the red box in the top-right of :numref:`fig_ec2`). If you are located in China,\n", "you can select a nearby Asia Pacific region, such as Seoul or Tokyo. Please note\n", "that some data centers may not have GPU instances.\n", "\n", "\n", "### Increasing Limits\n", "\n", "Before choosing an instance, check if there are quantity\n", "restrictions by clicking the \"Limits\" label in the bar on the left as shown in\n", ":numref:`fig_ec2`. \n", ":numref:`fig_limits` shows an example of such a\n", "limitation. The account currently cannot open \"p2.xlarge\" instances according to the region. If\n", "you need to open one or more instances, click on the \"Request limit increase\" link to\n", "apply for a higher instance quota.\n", "Generally, it takes one business day to\n", "process an application.\n", "\n", "![Instance quantity restrictions.](../img/limits.png)\n", ":width:`700px`\n", ":label:`fig_limits`\n", "\n", "\n", "### Launching an Instance\n", "\n", "Next, click the \"Launch Instance\" button marked by the red box in :numref:`fig_ec2` to launch your instance.\n", "\n", "We begin by selecting a suitable Amazon Machine Image (AMI). Select an Ubuntu instance (:numref:`fig_ubuntu`).\n", "\n", "\n", "![Choose an AMI.](../img/ubuntu-new.png)\n", ":width:`700px`\n", ":label:`fig_ubuntu`\n", "\n", "EC2 provides many different instance configurations to choose from. This can sometimes feel overwhelming to a beginner. :numref:`tab_ec2` lists different suitable machines.\n", "\n", ":Different EC2 instance types\n", ":label:`tab_ec2`\n", "\n", "| Name | GPU         | Notes                         |\n", "|------|-------------|-------------------------------|\n", "| g2   | Grid K520   | ancient                       |\n", "| p2   | Kepler K80  | old but often cheap as spot   |\n", "| g3   | Maxwell M60 | good trade-off                |\n", "| p3   | Volta V100  | high performance for FP16     |\n", "| p4   | Ampere A100 | high performance for large-scale training |\n", "| g4   | Turing T4   | inference optimized FP16/INT8 |\n", "\n", "\n", "All these servers come in multiple flavors indicating the number of GPUs used. For example, a p2.xlarge has 1 GPU and a p2.16xlarge has 16 GPUs and more memory. For more details, see the [AWS EC2 documentation](https://aws.amazon.com/ec2/instance-types/) or a [summary page](https://www.ec2instances.info). For the purpose of illustration, a p2.xlarge will suffice (marked in the red box of :numref:`fig_p2x`).\n", "\n", "![Choose an instance.](../img/p2x.png)\n", ":width:`700px`\n", ":label:`fig_p2x`\n", "\n", "Note that you should use a GPU-enabled instance with suitable drivers and a GPU-enabled deep learning framework. Otherwise you will not see any benefit from using GPUs.\n", "\n", "We go on to select the key pair used to access\n", "the instance. If you do not have a key pair, click \"Create new key pair\" in :numref:`fig_keypair` to generate a key pair. Subsequently,\n", "you can select the\n", "previously generated key pair. \n", "Make sure that you download the key pair and store it in a safe location if you\n", "generated a new one. This is your only way to SSH into the server.\n", "\n", "![Select a key pair.](../img/keypair.png)\n", ":width:`500px`\n", ":label:`fig_keypair`\n", "\n", "In this example, we will keep the default configurations for \"Network settings\" (click the \"Edit\" button to configure items such as the subnet and security groups). We just increase the default hard disk size to 64 GB (:numref:`fig_disk`). Note that CUDA by itself already takes up 4 GB.\n", "\n", "![Modify the hard disk size.](../img/disk.png)\n", ":width:`700px`\n", ":label:`fig_disk`\n", "\n", "\n", "Click \"Launch Instance\" to launch the created\n", "instance. Click the\n", "instance ID shown in :numref:`fig_launching` to view the status of this instance.\n", "\n", "![Click the instance ID.](../img/launching.png)\n", ":width:`700px`\n", ":label:`fig_launching`\n", "\n", "### Connecting to the Instance\n", "\n", "As shown in :numref:`fig_connect`, after the instance state turns green, right-click the instance and select `Connect` to view the instance access method.\n", "\n", "![View the instance access method.](../img/connect.png)\n", ":width:`700px`\n", ":label:`fig_connect`\n", "\n", "If this is a new key, it must not be publicly viewable for SSH to work. Go to the folder where you store `D2L_key.pem` and \n", "execute the following command \n", "to make the key not publicly viewable:\n", "\n", "```bash\n", "chmod 400 D2L_key.pem\n", "```\n", "\n", "![View instance access and startup method.](../img/chmod.png)\n", ":width:`400px`\n", ":label:`fig_chmod`\n", "\n", "\n", "Now, copy the SSH command in the lower red box of :numref:`fig_chmod` and paste onto the command line:\n", "\n", "```bash\n", "ssh -i \"D2L_key.pem\" <EMAIL>\n", "```\n", "\n", "When the command line prompts \"Are you sure you want to continue connecting (yes/no)\", enter \"yes\" and press En<PERSON> to log into the instance.\n", "\n", "Your server is ready now.\n", "\n", "\n", "## Installing CUDA\n", "\n", "Before installing CUDA, be sure to update the instance with the latest drivers.\n", "\n", "```bash\n", "sudo apt-get update && sudo apt-get install -y build-essential git libgfortran3\n", "```\n", "\n", "Here we download CUDA 12.1. Visit NVIDIA's [official repository](https://developer.nvidia.com/cuda-toolkit-archive) to find the download link as shown in :numref:`fig_cuda`.\n", "\n", "![Find the CUDA 12.1 download address.](../img/cuda121.png)\n", ":width:`500px`\n", ":label:`fig_cuda`\n", "\n", "Copy the instructions and paste them onto the terminal to install CUDA 12.1.\n", "\n", "```bash\n", "# The link and file name are subject to changes\n", "wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-ubuntu2204.pin\n", "sudo mv cuda-ubuntu2204.pin /etc/apt/preferences.d/cuda-repository-pin-600\n", "wget https://developer.download.nvidia.com/compute/cuda/12.1.0/local_installers/cuda-repo-ubuntu2204-12-1-local_12.1.0-530.30.02-1_amd64.deb\n", "sudo dpkg -i cuda-repo-ubuntu2204-12-1-local_12.1.0-530.30.02-1_amd64.deb\n", "sudo cp /var/cuda-repo-ubuntu2204-12-1-local/cuda-*-keyring.gpg /usr/share/keyrings/\n", "sudo apt-get update\n", "sudo apt-get -y install cuda\n", "```\n", "\n", "After installing the program, run the following command to view the GPUs:\n", "\n", "```bash\n", "nvidia-smi\n", "```\n", "\n", "Finally, add CUDA to the library path to help other libraries find it, such as appending the following lines to the end of `~/.bashrc`.\n", "\n", "```bash\n", "export PATH=\"/usr/local/cuda-12.1/bin:$PATH\"\n", "export LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/usr/local/cuda-12.1/lib64\n", "```\n", "\n", "## Installing Libraries for Running the Code\n", "\n", "To run the code of this book,\n", "just follow steps in :ref:`chap_installation`\n", "for Linux users on the EC2 instance\n", "and use the following tips \n", "for working on a remote Linux server:\n", "\n", "* To download the bash script on the Miniconda installation page, right click the download link and select \"Copy Link Address\", then execute `wget [copied link address]`.\n", "* After running `~/miniconda3/bin/conda init`, you may execute `source ~/.bashrc` instead of closing and reopening your current shell.\n", "\n", "\n", "## Running the Jupyter Notebook remotely\n", "\n", "To run the Jupyter Notebook remotely you need to use SSH port forwarding. After all, the server in the cloud does not have a monitor or keyboard. For this, log into your server from your desktop (or laptop) as follows:\n", "\n", "```\n", "# This command must be run in the local command line\n", "ssh -i \"/path/to/key.pem\" <EMAIL> -L 8889:localhost:8888\n", "```\n", "\n", "Next, go to the location \n", "of the downloaded code of this book\n", "on the EC2 instance,\n", "then run:\n", "\n", "```\n", "conda activate d2l\n", "jupyter notebook\n", "```\n", "\n", ":numref:`fig_jupyter` shows the possible output after you run the Jupyter Notebook. The last row is the URL for port 8888.\n", "\n", "![Output after running the Jupyter Notebook. The last row is the URL for port 8888.](../img/jupyter.png)\n", ":width:`700px`\n", ":label:`fig_jupyter`\n", "\n", "Since you used port forwarding to port 8889,\n", "copy the last row in the red box of :numref:`fig_jupyter`,\n", "replace \"8888\" with \"8889\" in the URL,\n", "and open it in your local browser.\n", "\n", "\n", "## Closing Unused Instances\n", "\n", "As cloud services are billed by the time of use, you should close instances that are not being used. Note that there are alternatives:\n", "\n", "* \"Stopping\" an instance means that you will be able to start it again. This is akin to switching off the power for your regular server. However, stopped instances will still be billed a small amount for the hard disk space retained. \n", "* \"Terminating\" an instance will delete all data associated with it. This includes the disk, hence you cannot start it again. Only do this if you know that you will not need it in the future.\n", "\n", "If you want to use the instance as a template for many more instances,\n", "right-click on the example in :numref:`fig_connect` and select \"Image\" $\\rightarrow$\n", "\"Create\" to create an image of the instance. Once this is complete, select\n", "\"Instance State\" $\\rightarrow$ \"Terminate\" to terminate the instance. The next\n", "time you want to use this instance, you can follow the steps in this section \n", "to create an instance based on\n", "the saved image. The only difference is that, in \"1. Choose AMI\" shown in\n", ":numref:`fig_ubuntu`, you must use the \"My AMIs\" option on the left to select your saved\n", "image. The created instance will retain the information stored on the image hard\n", "disk. For example, you will not have to reinstall CUDA and other runtime\n", "environments.\n", "\n", "\n", "## Summary\n", "\n", "* We can launch and stop instances on demand without having to buy and build our own computer.\n", "* We need to install CUDA before using the GPU-enabled deep learning framework.\n", "* We can use port forwarding to run the Jupyter Notebook on a remote server.\n", "\n", "\n", "## Exercises\n", "\n", "1. The cloud offers convenience, but it does not come cheap. Find out how to launch [spot instances](https://aws.amazon.com/ec2/spot/) to see how to reduce costs.\n", "1. Experiment with different GPU servers. How fast are they?\n", "1. Experiment with multi-GPU servers. How well can you scale things up?\n", "\n", "\n", "[Discussions](https://discuss.d2l.ai/t/423)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
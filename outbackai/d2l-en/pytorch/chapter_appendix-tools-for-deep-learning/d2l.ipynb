{"cells": [{"cell_type": "markdown", "id": "cef0fc9c", "metadata": {"origin_pos": 0}, "source": ["# The `d2l` API Document\n", ":label:`sec_d2l`\n", "\n", "This section displays classes and functions (sorted alphabetically) in the `d2l` package, showing where they are defined in the book so you can find more detailed implementations and explanations. \n", "See also the source code on the [GitHub repository](https://github.com/d2l-ai/d2l-en/tree/master/d2l).\n"]}, {"cell_type": "markdown", "id": "08dfb40f", "metadata": {"origin_pos": 1, "tab": ["pytorch"]}, "source": ["```eval_rst\n", "\n", ".. currentmodule:: d2l.torch\n", "\n", "```\n"]}, {"cell_type": "markdown", "id": "26d0ff5f", "metadata": {"origin_pos": 4}, "source": ["## Classes\n", "\n", "```eval_rst \n", ".. autoclass:: AdditiveAttention\n", "   :members:\n", "   \n", ".. autoclass:: AddNorm\n", "   :members:\n", "\n", ".. autoclass:: AttentionDecoder\n", "   :members: \n", "\n", ".. autoclass:: Classifier\n", "   :members: \n", "   \n", ".. autoclass:: DataModule\n", "   :members: \n", "   \n", ".. autoclass:: <PERSON><PERSON><PERSON>\n", "   :members: \n", "   \n", ".. autoclass:: DotProductAttention\n", "   :members:\n", "   \n", ".. autoclass:: Encoder\n", "   :members:\n", "   \n", ".. autoclass:: EncoderDecoder\n", "   :members:\n", "   \n", ".. autoclass:: FashionMNIST\n", "   :members: \n", "   \n", ".. autoclass:: GRU\n", "   :members: \n", "   \n", ".. autoclass:: HyperParameters\n", "   :members: \n", "   \n", ".. autoclass:: LeNet\n", "   :members: \n", "   \n", ".. autoclass:: LinearRegression\n", "   :members: \n", "   \n", ".. autoclass:: LinearRegressionScratch\n", "   :members: \n", "   \n", ".. autoclass:: <PERSON><PERSON><PERSON>\n", "   :members: \n", "   \n", ".. autoclass:: MTFraEng\n", "   :members: \n", "   \n", ".. autoclass:: MultiHeadAttention\n", "   :members:\n", "   \n", ".. autoclass:: PositionalEncoding\n", "   :members:\n", "   \n", ".. autoclass:: PositionWiseFFN\n", "   :members:\n", "   \n", ".. autoclass:: ProgressBoard\n", "   :members: \n", "   \n", ".. autoclass:: Residual\n", "   :members: \n", "   \n", ".. autoclass:: ResNeXtBlock\n", "   :members:\n", "   \n", ".. autoclass:: RNN\n", "   :members: \n", "   \n", ".. autoclass:: RNNLM\n", "   :members:\n", "   \n", ".. autoclass:: RNNLMScratch\n", "   :members:\n", "   \n", ".. autoclass:: R<PERSON><PERSON><PERSON><PERSON>\n", "   :members: \n", "   \n", ".. autoclass:: Seq2Seq\n", "   :members:  \n", "   \n", ".. autoclass:: Seq2SeqEncoder\n", "   :members:\n", "   \n", ".. autoclass:: SGD\n", "   :members: \n", "   \n", ".. autoclass:: SoftmaxRegression\n", "   :members: \n", "\n", ".. autoclass:: SyntheticRegressionData\n", "   :members: \n", "\n", ".. autoclass:: TimeMachine\n", "   :members: \n", "\n", ".. autoclass:: Trainer\n", "   :members: \n", "\n", ".. autoclass:: TransformerEncoder \n", "   :members:\n", "\n", ".. autoclass:: TransformerEncoderBlock\n", "   :members:\n", "\n", ".. autoclass:: Vocab\n", "   :members:\n", "```\n", "\n", "## Functions\n", "\n", "```eval_rst \n", ".. autofunction:: add_to_class\n", "\n", ".. autofunction:: bleu\n", "\n", ".. autofunction:: check_len\n", "\n", ".. autofunction:: check_shape\n", "\n", ".. autofunction:: corr2d\n", "\n", ".. autofunction:: cpu\n", "\n", ".. autofunction:: gpu\n", "\n", ".. autofunction:: init_cnn\n", "\n", ".. autofunction:: init_seq2seq\n", "\n", ".. autofunction:: masked_softmax\n", "\n", ".. autofunction:: num_gpus\n", "\n", ".. autofunction:: plot\n", "\n", ".. autofunction:: set_axes\n", "\n", ".. autofunction:: set_figsize\n", "\n", ".. autofunction:: show_heatmaps\n", "\n", ".. autofunction:: show_list_len_pair_hist\n", "\n", ".. autofunction:: try_all_gpus\n", "\n", ".. autofunction:: try_gpu\n", "\n", ".. autofunction:: use_svg_display\n", "```\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "fad736db", "metadata": {"origin_pos": 0}, "source": ["# Automatic Parallelism\n", ":label:`sec_auto_para`\n", "\n", "\n", "Deep learning frameworks (e.g., MXNet and PyTorch) automatically construct computational graphs at the backend. Using a\n", "computational graph, the system is aware of all the dependencies,\n", "and can selectively execute multiple non-interdependent tasks in parallel to\n", "improve speed. For instance, :numref:`fig_asyncgraph` in :numref:`sec_async` initializes two variables independently. Consequently the system can choose to execute them in parallel.\n", "\n", "\n", "Typically, a single operator will use all the computational resources on all CPUs or on a single GPU. For example, the `dot` operator will use all cores (and threads) on all CPUs, even if there are multiple CPU processors on a single machine. The same applies to a single GPU. Hence parallelization is not quite so useful for single-device computers. With multiple devices things matter more. While parallelization is typically most relevant between multiple GPUs, adding the local CPU will increase performance slightly. For example, see :citet:`Hadjis.Zhang.Mitliagkas.ea.2016` that focuses on training computer vision models combining a GPU and a CPU. With the convenience of an automatically parallelizing framework we can accomplish the same goal in a few lines of Python code. More broadly, our discussion of automatic parallel computation focuses on parallel computation using both CPUs and GPUs, as well as the parallelization of computation and communication.\n", "\n", "Note that we need at least two GPUs to run the experiments in this section.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d0d9e57d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:29.485059Z", "iopub.status.busy": "2023-08-18T19:25:29.484473Z", "iopub.status.idle": "2023-08-18T19:25:35.448809Z", "shell.execute_reply": "2023-08-18T19:25:35.447820Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "b92102a2", "metadata": {"origin_pos": 3}, "source": ["## Parallel Computation on GPUs\n", "\n", "Let's start by defining a reference workload to test: the `run` function below performs 10 matrix-matrix multiplications on the device of our choice using data allocated into two variables: `x_gpu1` and `x_gpu2`.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "759a409e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:35.454178Z", "iopub.status.busy": "2023-08-18T19:25:35.453378Z", "iopub.status.idle": "2023-08-18T19:25:35.770458Z", "shell.execute_reply": "2023-08-18T19:25:35.769570Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [], "source": ["devices = d2l.try_all_gpus()\n", "def run(x):\n", "    return [x.mm(x) for _ in range(50)]\n", "\n", "x_gpu1 = torch.rand(size=(4000, 4000), device=devices[0])\n", "x_gpu2 = torch.rand(size=(4000, 4000), device=devices[1])"]}, {"cell_type": "markdown", "id": "8d9fa484", "metadata": {"origin_pos": 7, "tab": ["pytorch"]}, "source": ["Now we apply the function to the data. To ensure that caching does not play a role in the results we warm up the devices by performing a single pass on either of them prior to measuring. `torch.cuda.synchronize()` waits for all kernels in all streams on a CUDA device to complete. It takes in a `device` argument, the device for which we need to synchronize. It uses the current device, given by `current_device()`, if the device argument is `None` (default).\n"]}, {"cell_type": "code", "execution_count": 3, "id": "d27257b0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:35.774318Z", "iopub.status.busy": "2023-08-18T19:25:35.773750Z", "iopub.status.idle": "2023-08-18T19:25:37.238089Z", "shell.execute_reply": "2023-08-18T19:25:37.237230Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU1 time: 0.4660 sec\n"]}, {"name": "stdout", "output_type": "stream", "text": ["GPU2 time: 0.4510 sec\n"]}], "source": ["run(x_gpu1)\n", "run(x_gpu2)  # Warm-up all devices\n", "torch.cuda.synchronize(devices[0])\n", "torch.cuda.synchronize(devices[1])\n", "\n", "with d2l.<PERSON>('GPU1 time'):\n", "    run(x_gpu1)\n", "    torch.cuda.synchronize(devices[0])\n", "\n", "with d2l.<PERSON>('GPU2 time'):\n", "    run(x_gpu2)\n", "    torch.cuda.synchronize(devices[1])"]}, {"cell_type": "markdown", "id": "f2369a21", "metadata": {"origin_pos": 11, "tab": ["pytorch"]}, "source": ["If we remove the `synchronize` statement between both tasks the system is free to parallelize computation on both devices automatically.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "99564cb7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:37.243856Z", "iopub.status.busy": "2023-08-18T19:25:37.243251Z", "iopub.status.idle": "2023-08-18T19:25:37.714436Z", "shell.execute_reply": "2023-08-18T19:25:37.713587Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU1 & GPU2: 0.4659 sec"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["with d2l.<PERSON>('GPU1 & GPU2'):\n", "    run(x_gpu1)\n", "    run(x_gpu2)\n", "    torch.cuda.synchronize()"]}, {"cell_type": "markdown", "id": "450c6d55", "metadata": {"origin_pos": 14}, "source": ["In the above case the total execution time is less than the sum of its parts, since the deep learning framework automatically schedules computation on both GPU devices without the need for sophisticated code on behalf of the user.\n", "\n", "\n", "\n", "## Parallel Computation and Communication\n", "\n", "In many cases we need to move data between different devices, say between the CPU and GPU, or between different GPUs.\n", "For instance,\n", "this occurs when we want to perform distributed optimization where we need to aggregate the gradients over multiple accelerator cards. Let's simulate this by computing on the GPU and then copying the results back to the CPU.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "7c782f25", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:37.718751Z", "iopub.status.busy": "2023-08-18T19:25:37.718134Z", "iopub.status.idle": "2023-08-18T19:25:40.502157Z", "shell.execute_reply": "2023-08-18T19:25:40.501308Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Run on GPU1: 0.4656 sec\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Copy to CPU: 2.3125 sec\n"]}], "source": ["def copy_to_cpu(x, non_blocking=False):\n", "    return [y.to('cpu', non_blocking=non_blocking) for y in x]\n", "\n", "with d2l.<PERSON>('Run on GPU1'):\n", "    y = run(x_gpu1)\n", "    torch.cuda.synchronize()\n", "\n", "with d2l.<PERSON>('Copy to CPU'):\n", "    y_cpu = copy_to_cpu(y)\n", "    torch.cuda.synchronize()"]}, {"cell_type": "markdown", "id": "8a7299b0", "metadata": {"origin_pos": 18, "tab": ["pytorch"]}, "source": ["This is somewhat inefficient. Note that we could already start copying parts of `y` to the CPU while the remainder of the list is still being computed. This situation occurs, e.g., when we compute the (backprop) gradient on a minibatch. The gradients of some of the parameters will be available earlier than that of others. Hence it works to our advantage to start using PCI-Express bus bandwidth while the GPU is still running. In PyTorch, several functions such as `to()` and `copy_()` admit an explicit `non_blocking` argument, which lets the caller bypass synchronization when it is unnecessary. Setting `non_blocking=True` allows us to simulate this scenario.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "3bb3f920", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:40.505685Z", "iopub.status.busy": "2023-08-18T19:25:40.505115Z", "iopub.status.idle": "2023-08-18T19:25:42.200667Z", "shell.execute_reply": "2023-08-18T19:25:42.199739Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Run on GPU1 and copy to CPU: 1.6907 sec\n"]}], "source": ["with d2l.<PERSON>('Run on GPU1 and copy to CPU'):\n", "    y = run(x_gpu1)\n", "    y_cpu = copy_to_cpu(y, True)\n", "    torch.cuda.synchronize()"]}, {"cell_type": "markdown", "id": "91b6c2a3", "metadata": {"origin_pos": 21}, "source": ["The total time required for both operations is (as expected) less than the sum of their parts.\n", "Note that this task is different from parallel computation as it uses a different resource: the bus between the CPU and GPUs. In fact, we could compute on both devices and communicate, all at the same time. As noted above, there is a dependency between computation and communication: `y[i]` must be computed before it can be copied to the CPU. Fortunately, the system can copy `y[i-1]` while computing `y[i]` to reduce the total running time.\n", "\n", "We conclude with an illustration of the computational graph and its dependencies for a simple two-layer MLP when training on a CPU and two GPUs, as depicted in :numref:`fig_twogpu`. It would be quite painful to schedule the parallel program resulting from this manually. This is where it is advantageous to have a graph-based computing backend for optimization.\n", "\n", "![The computational graph and its dependencies of a two-layer MLP on a CPU and two GPUs.](../img/twogpu.svg)\n", ":label:`fig_twogpu`\n", "\n", "\n", "## Summary\n", "\n", "* Modern systems have a variety of devices, such as multiple GPUs and CPUs. They can be used in parallel, asynchronously.\n", "* Modern systems also have a variety of resources for communication, such as PCI Express, storage (typically solid-state drives or via networks), and network bandwidth. They can be used in parallel for peak efficiency.\n", "* The backend can improve performance through automatic parallel computation and communication.\n", "\n", "## Exercises\n", "\n", "1. Eight operations were performed in the `run` function defined in this section. There are no dependencies between them. Design an experiment to see if the deep learning framework will automatically execute them in parallel.\n", "1. When the workload of an individual operator is sufficiently small, parallelization can help even on a single CPU or GPU. Design an experiment to verify this.\n", "1. Design an experiment that uses parallel computation on CPUs, GPUs, and communication between both devices.\n", "1. Use a debugger such as NVIDIA's [Nsight](https://developer.nvidia.com/nsight-compute-2019_5) to verify that your code is efficient.\n", "1. Designing computation tasks that include more complex data dependencies, and run experiments to see if you can obtain the correct results while improving performance.\n"]}, {"cell_type": "markdown", "id": "f43b15b3", "metadata": {"origin_pos": 23, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1681)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
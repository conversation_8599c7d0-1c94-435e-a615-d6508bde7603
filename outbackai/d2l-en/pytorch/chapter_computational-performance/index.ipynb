{"cells": [{"cell_type": "markdown", "id": "89c47c6d", "metadata": {"origin_pos": 0}, "source": ["# Computational Performance\n", ":label:`chap_performance`\n", "\n", "In deep learning, \n", "datasets and models are usually large,\n", "which involves heavy computation.\n", "Therefore, computational performance matters a lot.\n", "This chapter will focus on the major factors that affect computational performance:\n", "imperative programming, symbolic programming, asynchronous computing, automatic parallelism, and multi-GPU computation.\n", "By studying this chapter, you may further improve computational performance of those models implemented in the previous chapters,\n", "for example, by reducing training time without affecting accuracy.\n", "\n", ":begin_tab:toc\n", " - [hybridize](hybridize.ipynb)\n", " - [async-computation](async-computation.ipynb)\n", " - [auto-parallelism](auto-parallelism.ipynb)\n", " - [hardware](hardware.ipynb)\n", " - [multiple-gpus](multiple-gpus.ipynb)\n", " - [multiple-gpus-concise](multiple-gpus-concise.ipynb)\n", " - [parameterserver](parameterserver.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
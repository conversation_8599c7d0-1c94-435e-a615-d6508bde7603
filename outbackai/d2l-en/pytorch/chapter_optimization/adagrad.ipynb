{"cells": [{"cell_type": "markdown", "id": "1c8748ea", "metadata": {"origin_pos": 0}, "source": ["# Adagrad\n", ":label:`sec_adagrad`\n", "\n", "Let's begin by considering learning problems with features that occur infrequently.\n", "\n", "\n", "## Sparse Features and Learning Rates\n", "\n", "Imagine that we are training a language model. To get good accuracy we typically want to decrease the learning rate as we keep on training, usually at a rate of $\\mathcal{O}(t^{-\\frac{1}{2}})$ or slower. Now consider a model training on sparse features, i.e., features that occur only infrequently. This is common for natural language, e.g., it is a lot less likely that we will see the word *preconditioning* than *learning*. However, it is also common in other areas such as computational advertising and personalized collaborative filtering. After all, there are many things that are of interest only for a small number of people.\n", "\n", "Parameters associated with infrequent features only receive meaningful updates whenever these features occur. Given a decreasing learning rate we might end up in a situation where the parameters for common features converge rather quickly to their optimal values, whereas for infrequent features we are still short of observing them sufficiently frequently before their optimal values can be determined. In other words, the learning rate either decreases too slowly for frequent features or too quickly for infrequent ones.\n", "\n", "A possible hack to redress this issue would be to count the number of times we see a particular feature and to use this as a clock for adjusting learning rates. That is, rather than choosing a learning rate of the form $\\eta = \\frac{\\eta_0}{\\sqrt{t + c}}$ we could use $\\eta_i = \\frac{\\eta_0}{\\sqrt{s(i, t) + c}}$. Here $s(i, t)$ counts the number of nonzeros for feature $i$ that we have observed up to time $t$. This is actually quite easy to implement at no meaningful overhead. However, it fails whenever we do not quite have sparsity but rather just data where the gradients are often very small and only rarely large. After all, it is unclear where one would draw the line between something that qualifies as an observed feature or not.\n", "\n", "Adagrad by :citet:<PERSON><PERSON><PERSON><PERSON>Hazan.Singer.2011` addresses this by replacing the rather crude counter $s(i, t)$ by an aggregate of the squares of previously observed gradients. In particular, it uses $s(i, t+1) = s(i, t) + \\left(\\partial_i f(\\mathbf{x})\\right)^2$ as a means to adjust the learning rate. This has two benefits: first, we no longer need to decide just when a gradient is large enough. Second, it scales automatically with the magnitude of the gradients. Coordinates that routinely correspond to large gradients are scaled down significantly, whereas others with small gradients receive a much more gentle treatment. In practice this leads to a very effective optimization procedure for computational advertising and related problems. But this hides some of the additional benefits inherent in Adagrad that are best understood in the context of preconditioning.\n", "\n", "\n", "## Preconditioning\n", "\n", "Convex optimization problems are good for analyzing the characteristics of algorithms. After all, for most nonconvex problems it is difficult to derive meaningful theoretical guarantees, but *intuition* and *insight* often carry over.  Let's look at the problem of minimizing $f(\\mathbf{x}) = \\frac{1}{2} \\mathbf{x}^\\top \\mathbf{Q} \\mathbf{x} + \\mathbf{c}^\\top \\mathbf{x} + b$.\n", "\n", "As we saw in :numref:`sec_momentum`, it is possible to rewrite this problem in terms of its eigendecomposition $\\mathbf{Q} = \\mathbf{U}^\\top \\boldsymbol{\\Lambda} \\mathbf{U}$ to arrive at a much simplified problem where each coordinate can be solved individually:\n", "\n", "$$f(\\mathbf{x}) = \\bar{f}(\\bar{\\mathbf{x}}) = \\frac{1}{2} \\bar{\\mathbf{x}}^\\top \\boldsymbol{\\Lambda} \\bar{\\mathbf{x}} + \\bar{\\mathbf{c}}^\\top \\bar{\\mathbf{x}} + b.$$\n", "\n", "Here we used $\\bar{\\mathbf{x}} = \\mathbf{U} \\mathbf{x}$ and consequently $\\bar{\\mathbf{c}} = \\mathbf{U} \\mathbf{c}$. The modified problem has as its minimizer $\\bar{\\mathbf{x}} = -\\boldsymbol{\\Lambda}^{-1} \\bar{\\mathbf{c}}$ and minimum value $-\\frac{1}{2} \\bar{\\mathbf{c}}^\\top \\boldsymbol{\\Lambda}^{-1} \\bar{\\mathbf{c}} + b$. This is much easier to compute since $\\boldsymbol{\\Lambda}$ is a diagonal matrix containing the eigenvalues of $\\mathbf{Q}$.\n", "\n", "If we perturb $\\mathbf{c}$ slightly we would hope to find only slight changes in the minimizer of $f$. Unfortunately this is not the case. While slight changes in $\\mathbf{c}$ lead to equally slight changes in $\\bar{\\mathbf{c}}$, this is not the case for the minimizer of $f$ (and of $\\bar{f}$ respectively). Whenever the eigenvalues $\\boldsymbol{\\Lambda}_i$ are large we will see only small changes in $\\bar{x}_i$ and in the minimum of $\\bar{f}$. Conversely, for small $\\boldsymbol{\\Lambda}_i$ changes in $\\bar{x}_i$ can be dramatic. The ratio between the largest and the smallest eigenvalue is called the condition number of an optimization problem.\n", "\n", "$$\\kappa = \\frac{\\boldsymbol{\\Lambda}_1}{\\boldsymbol{\\Lambda}_d}.$$\n", "\n", "If the condition number $\\kappa$ is large, it is difficult to solve the optimization problem accurately. We need to ensure that we are careful in getting a large dynamic range of values right. Our analysis leads to an obvious, albeit somewhat naive question: couldn't we simply \"fix\" the problem by distorting the space such that all eigenvalues are $1$. In theory this is quite easy: we only need the eigenvalues and eigenvectors of $\\mathbf{Q}$ to rescale the problem from $\\mathbf{x}$ to one in $\\mathbf{z} \\stackrel{\\textrm{def}}{=} \\boldsymbol{\\Lambda}^{\\frac{1}{2}} \\mathbf{U} \\mathbf{x}$. In the new coordinate system $\\mathbf{x}^\\top \\mathbf{Q} \\mathbf{x}$ could be simplified to $\\|\\mathbf{z}\\|^2$. Alas, this is a rather impractical suggestion. Computing eigenvalues and eigenvectors is in general *much more* expensive than solving the actual  problem.\n", "\n", "While computing eigenvalues exactly might be expensive, guessing them and computing them even somewhat approximately may already be a lot better than not doing anything at all. In particular, we could use the diagonal entries of $\\mathbf{Q}$ and rescale it accordingly. This is *much* cheaper than computing eigenvalues.\n", "\n", "$$\\tilde{\\mathbf{Q}} = \\textrm{diag}^{-\\frac{1}{2}}(\\mathbf{Q}) \\mathbf{Q} \\textrm{diag}^{-\\frac{1}{2}}(\\mathbf{Q}).$$\n", "\n", "In this case we have $\\tilde{\\mathbf{Q}}_{ij} = \\mathbf{Q}_{ij} / \\sqrt{\\mathbf{Q}_{ii} \\mathbf{Q}_{jj}}$ and specifically $\\tilde{\\mathbf{Q}}_{ii} = 1$ for all $i$. In most cases this simplifies the condition number considerably. For instance, the cases we discussed previously, this would entirely eliminate the problem at hand since the problem is axis aligned.\n", "\n", "Unfortunately we face yet another problem: in deep learning we typically do not even have access to the second derivative of the objective function: for $\\mathbf{x} \\in \\mathbb{R}^d$ the second derivative even on a minibatch may require $\\mathcal{O}(d^2)$ space and work to compute, thus making it practically infeasible. The ingenious idea of Adagrad is to use a proxy for that elusive diagonal of the Hessian that is both relatively cheap to compute and effective---the magnitude of the gradient itself.\n", "\n", "In order to see why this works, let's look at $\\bar{f}(\\bar{\\mathbf{x}})$. We have that\n", "\n", "$$\\partial_{\\bar{\\mathbf{x}}} \\bar{f}(\\bar{\\mathbf{x}}) = \\boldsymbol{\\Lambda} \\bar{\\mathbf{x}} + \\bar{\\mathbf{c}} = \\boldsymbol{\\Lambda} \\left(\\bar{\\mathbf{x}} - \\bar{\\mathbf{x}}_0\\right),$$\n", "\n", "where $\\bar{\\mathbf{x}}_0$ is the minimizer of $\\bar{f}$. Hence the magnitude of the gradient depends both on $\\boldsymbol{\\Lambda}$ and the distance from optimality. If $\\bar{\\mathbf{x}} - \\bar{\\mathbf{x}}_0$ did not change, this would be all that is needed. After all, in this case the magnitude of the gradient $\\partial_{\\bar{\\mathbf{x}}} \\bar{f}(\\bar{\\mathbf{x}})$ suffices. Since AdaGrad is a stochastic gradient descent algorithm, we will see gradients with nonzero variance even at optimality. As a result we can safely use the variance of the gradients as a cheap proxy for the scale of the Hessian. A thorough analysis is beyond the scope of this section (it would be several pages). We refer the reader to :cite:`Duchi.Hazan.Singer.2011` for details.\n", "\n", "## The Algorithm\n", "\n", "Let's formalize the discussion from above. We use the variable $\\mathbf{s}_t$ to accumulate past gradient variance as follows.\n", "\n", "$$\\begin{aligned}\n", "    \\mathbf{g}_t & = \\partial_{\\mathbf{w}} l(y_t, f(\\mathbf{x}_t, \\mathbf{w})), \\\\\n", "    \\mathbf{s}_t & = \\mathbf{s}_{t-1} + \\mathbf{g}_t^2, \\\\\n", "    \\mathbf{w}_t & = \\mathbf{w}_{t-1} - \\frac{\\eta}{\\sqrt{\\mathbf{s}_t + \\epsilon}} \\cdot \\mathbf{g}_t.\n", "\\end{aligned}$$\n", "\n", "Here the operation are applied coordinate wise. That is, $\\mathbf{v}^2$ has entries $v_i^2$. Likewise $\\frac{1}{\\sqrt{v}}$ has entries $\\frac{1}{\\sqrt{v_i}}$ and $\\mathbf{u} \\cdot \\mathbf{v}$ has entries $u_i v_i$. As before $\\eta$ is the learning rate and $\\epsilon$ is an additive constant that ensures that we do not divide by $0$. Last, we initialize $\\mathbf{s}_0 = \\mathbf{0}$.\n", "\n", "Just like in the case of momentum we need to keep track of an auxiliary variable, in this case to allow for an individual learning rate per coordinate. This does not increase the cost of Adagrad significantly relative to SGD, simply since the main cost is typically to compute $l(y_t, f(\\mathbf{x}_t, \\mathbf{w}))$ and its derivative.\n", "\n", "Note that accumulating squared gradients in $\\mathbf{s}_t$ means that $\\mathbf{s}_t$ grows essentially at linear rate (somewhat slower than linearly in practice, since the gradients initially diminish). This leads to an $\\mathcal{O}(t^{-\\frac{1}{2}})$ learning rate, albeit adjusted on a per coordinate basis. For convex problems this is perfectly adequate. In deep learning, though, we might want to decrease the learning rate rather more slowly. This led to a number of Adagrad variants that we will discuss in the subsequent chapters. For now let's see how it behaves in a quadratic convex problem. We use the same problem as before:\n", "\n", "$$f(\\mathbf{x}) = 0.1 x_1^2 + 2 x_2^2.$$\n", "\n", "We are going to implement Adagrad using the same learning rate previously, i.e., $\\eta = 0.4$. As we can see, the iterative trajectory of the independent variable is smoother. However, due to the cumulative effect of $\\boldsymbol{s}_t$, the learning rate continuously decays, so the independent variable does not move as much during later stages of iteration.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "97c9f0d0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:27.512736Z", "iopub.status.busy": "2023-08-18T19:39:27.512091Z", "iopub.status.idle": "2023-08-18T19:39:30.836232Z", "shell.execute_reply": "2023-08-18T19:39:30.834662Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import math\n", "import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "code", "execution_count": 2, "id": "dad856dd", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:30.840637Z", "iopub.status.busy": "2023-08-18T19:39:30.840198Z", "iopub.status.idle": "2023-08-18T19:39:31.045574Z", "shell.execute_reply": "2023-08-18T19:39:31.044609Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 20, x1: -2.382563, x2: -0.158591\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"245.120313pt\" height=\"183.35625pt\" viewBox=\"0 0 245.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:39:31.008533</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 245.**********.35625 \n", "L 245.120313 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "L 237.920313 7.2 \n", "L 42.620312 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"me6a4c52a42\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me6a4c52a42\" x=\"88.39375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(81.022656 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#me6a4c52a42\" x=\"149.425\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(142.053907 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#me6a4c52a42\" x=\"210.456251\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(207.275001 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- x1 -->\n", "     <g transform=\"translate(134.129687 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"ma902cf0f87\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma902cf0f87\" x=\"42.620312\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −3 -->\n", "      <g transform=\"translate(20.878125 149.599219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#ma902cf0f87\" x=\"42.620312\" y=\"110.261538\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(20.878125 114.060757) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#ma902cf0f87\" x=\"42.620312\" y=\"74.723076\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(20.878125 78.522295) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#ma902cf0f87\" x=\"42.620312\" y=\"39.184615\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 42.983833) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- x2 -->\n", "     <g transform=\"translate(14.798437 82.640625) rotate(-90) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_8\">\n", "    <path d=\"M 57.878125 110.261538 \n", "L 70.084369 96.046154 \n", "L 78.348688 87.165873 \n", "L 84.908015 80.537052 \n", "L 90.459611 75.236107 \n", "L 95.325825 70.841109 \n", "L 99.686585 67.116708 \n", "L 103.654327 63.914925 \n", "L 107.304852 61.134891 \n", "L 110.692126 58.703726 \n", "L 113.856191 56.566407 \n", "L 116.827734 54.679956 \n", "L 119.630892 53.009887 \n", "L 122.285063 51.527936 \n", "L 124.806116 50.210531 \n", "L 127.207235 49.037745 \n", "L 129.499519 47.992541 \n", "L 131.692415 47.060222 \n", "L 133.79405 46.228018 \n", "L 135.811476 45.48477 \n", "L 137.750858 44.820679 \n", "\" clip-path=\"url(#pdae8947845)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m4802aa6532\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pdae8947845)\">\n", "     <use xlink:href=\"#m4802aa6532\" x=\"57.878125\" y=\"110.261538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"70.084369\" y=\"96.046154\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"78.348688\" y=\"87.165873\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"84.908015\" y=\"80.537052\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"90.459611\" y=\"75.236107\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"95.325825\" y=\"70.841109\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"99.686585\" y=\"67.116708\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"103.654327\" y=\"63.914925\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"107.304852\" y=\"61.134891\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"110.692126\" y=\"58.703726\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"113.856191\" y=\"56.566407\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"116.827734\" y=\"54.679956\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"119.630892\" y=\"53.009887\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"122.285063\" y=\"51.527936\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"124.806116\" y=\"50.210531\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"127.207235\" y=\"49.037745\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"129.499519\" y=\"47.992541\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"131.692415\" y=\"47.060222\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"133.79405\" y=\"46.228018\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"135.811476\" y=\"45.48477\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4802aa6532\" x=\"137.750858\" y=\"44.820679\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"PathCollection_1\"/>\n", "   <g id=\"PathCollection_2\">\n", "    <path d=\"M 97.100868 7.2 \n", "L 94.496869 7.868961 \n", "L 91.44531 8.673801 \n", "L 88.393757 9.499545 \n", "L 85.342191 10.346198 \n", "L 83.908323 10.753845 \n", "L 82.290631 11.275074 \n", "L 79.239071 12.281998 \n", "L 76.187512 13.312611 \n", "L 73.307377 14.307692 \n", "L 73.135938 14.376037 \n", "L 70.084378 15.61988 \n", "L 67.032818 16.891062 \n", "L 64.752171 17.861537 \n", "L 63.981244 18.249232 \n", "L 60.929685 19.816153 \n", "L 57.878125 21.415384 \n", "L 57.878125 21.415384 \n", "L 54.826565 23.409482 \n", "L 52.48604 24.96923 \n", "L 51.775006 25.578453 \n", "L 48.723432 28.243853 \n", "L 48.409729 28.523076 \n", "L 45.671872 31.93477 \n", "L 45.55989 32.076922 \n", "L 43.880133 35.630769 \n", "L 43.320214 39.184616 \n", "L 43.880133 42.738462 \n", "L 45.55989 46.292308 \n", "L 45.671872 46.43446 \n", "L 48.409729 49.846154 \n", "L 48.723432 50.125377 \n", "L 51.775006 52.790777 \n", "L 52.486042 53.400001 \n", "L 54.826565 54.959745 \n", "L 57.878125 56.953846 \n", "L 57.878125 56.953846 \n", "L 60.929685 58.553076 \n", "L 63.981244 60.119998 \n", "L 64.752171 60.507693 \n", "L 67.032818 61.478167 \n", "L 70.084378 62.749349 \n", "L 73.135938 63.993193 \n", "L 73.307377 64.061538 \n", "L 76.187512 65.056618 \n", "L 79.239071 66.087232 \n", "L 82.290631 67.094156 \n", "L 83.908319 67.615382 \n", "L 85.342191 68.02303 \n", "L 88.393757 68.869685 \n", "L 91.44531 69.695427 \n", "L 94.496869 70.500269 \n", "L 97.100868 71.169229 \n", "L 97.548436 71.272105 \n", "L 100.599996 71.954818 \n", "L 103.651563 72.618826 \n", "L 106.703122 73.264128 \n", "L 109.754682 73.890726 \n", "L 112.806249 74.498621 \n", "L 113.968755 74.723076 \n", "L 115.857816 75.053077 \n", "L 118.909375 75.56923 \n", "L 121.960942 76.068463 \n", "L 125.012502 76.55077 \n", "L 128.064069 77.016155 \n", "L 131.115628 77.464616 \n", "L 134.167188 77.896154 \n", "L 136.969651 78.276924 \n", "L 137.218755 78.307826 \n", "L 140.270314 78.670936 \n", "L 143.321874 79.018595 \n", "L 146.373441 79.350803 \n", "L 149.425 79.667558 \n", "L 152.476564 79.968862 \n", "L 155.528127 80.254716 \n", "L 158.57969 80.525117 \n", "L 161.631253 80.780066 \n", "L 164.682813 81.019566 \n", "L 167.734376 81.243611 \n", "L 170.785939 81.452208 \n", "L 173.837499 81.645351 \n", "L 176.889062 81.823043 \n", "L 177.034329 81.830766 \n", "L 179.940626 81.972923 \n", "L 182.992189 82.107968 \n", "L 186.04375 82.228801 \n", "L 189.095313 82.335415 \n", "L 192.146877 82.427815 \n", "L 195.198438 82.505999 \n", "L 198.250001 82.569969 \n", "L 201.301564 82.619722 \n", "L 204.353126 82.655262 \n", "L 207.404689 82.676585 \n", "L 210.456251 82.683692 \n", "L 213.507813 82.676585 \n", "L 216.559376 82.655262 \n", "L 219.610939 82.619722 \n", "L 222.662501 82.569969 \n", "L 225.714063 82.505999 \n", "L 228.765626 82.427815 \n", "L 231.817188 82.335415 \n", "L 234.868751 82.228801 \n", "L 237.920313 82.107968 \n", "\" clip-path=\"url(#pdae8947845)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_3\">\n", "    <path d=\"M 42.620312 82.505999 \n", "L 45.671872 83.280739 \n", "L 48.723432 84.041258 \n", "L 51.775006 84.78757 \n", "L 54.263642 85.384613 \n", "L 54.826565 85.509659 \n", "L 57.878125 86.174359 \n", "L 60.929685 86.825897 \n", "L 63.981244 87.464272 \n", "L 67.032818 88.089488 \n", "L 70.084378 88.701539 \n", "L 71.29158 88.93846 \n", "L 73.135938 89.275465 \n", "L 76.187512 89.820797 \n", "L 79.239071 90.353873 \n", "L 82.290631 90.874696 \n", "L 85.342191 91.383263 \n", "L 88.393757 91.879576 \n", "L 91.44531 92.363634 \n", "L 92.277553 92.492307 \n", "L 94.496869 92.813298 \n", "L 97.548436 93.243201 \n", "L 100.599996 93.661637 \n", "L 103.651563 94.06861 \n", "L 106.703122 94.464117 \n", "L 109.754682 94.848161 \n", "L 112.806249 95.220742 \n", "L 115.857816 95.58186 \n", "L 118.909375 95.931512 \n", "L 119.943828 96.046154 \n", "L 121.960942 96.256152 \n", "L 125.012502 96.563076 \n", "L 128.064069 96.859229 \n", "L 131.115628 97.144614 \n", "L 134.167188 97.41923 \n", "L 137.218755 97.683078 \n", "L 140.270314 97.936154 \n", "L 143.321874 98.17846 \n", "L 146.373441 98.41 \n", "L 149.425 98.630769 \n", "L 152.476564 98.840769 \n", "L 155.528127 99.040001 \n", "L 158.57969 99.228461 \n", "L 161.631253 99.406154 \n", "L 164.682813 99.573078 \n", "L 165.20896 99.600001 \n", "L 167.734376 99.721846 \n", "L 170.785939 99.858923 \n", "L 173.837499 99.985846 \n", "L 176.889062 100.102617 \n", "L 179.940626 100.209232 \n", "L 182.992189 100.305693 \n", "L 186.04375 100.392 \n", "L 189.095313 100.468155 \n", "L 192.146877 100.534155 \n", "L 195.198438 100.59 \n", "L 198.250001 100.635694 \n", "L 201.301564 100.671232 \n", "L 204.353126 100.696615 \n", "L 207.404689 100.711847 \n", "L 210.456251 100.716923 \n", "L 213.507813 100.711847 \n", "L 216.559376 100.696615 \n", "L 219.610939 100.671232 \n", "L 222.662501 100.635694 \n", "L 225.714063 100.59 \n", "L 228.765626 100.534155 \n", "L 231.817188 100.468155 \n", "L 234.868751 100.392 \n", "L 237.920313 100.305693 \n", "\" clip-path=\"url(#pdae8947845)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_4\">\n", "    <path d=\"M 42.620312 100.589999 \n", "L 45.671872 101.143384 \n", "L 48.723432 101.686614 \n", "L 51.775006 102.219694 \n", "L 54.826565 102.742616 \n", "L 57.27387 103.153848 \n", "L 57.878125 103.249896 \n", "L 60.929685 103.725343 \n", "L 63.981244 104.191185 \n", "L 67.032818 104.647423 \n", "L 70.084378 105.094054 \n", "L 73.135938 105.53108 \n", "L 76.187512 105.958505 \n", "L 79.239071 106.376322 \n", "L 81.716227 106.707695 \n", "L 82.290631 106.780594 \n", "L 85.342191 107.158756 \n", "L 88.393757 107.527811 \n", "L 91.44531 107.88775 \n", "L 94.496869 108.23858 \n", "L 97.548436 108.580295 \n", "L 100.599996 108.912898 \n", "L 103.651563 109.236389 \n", "L 106.703122 109.550769 \n", "L 109.754682 109.856036 \n", "L 112.806249 110.152188 \n", "L 113.968764 110.261538 \n", "L 115.857816 110.430561 \n", "L 118.909375 110.694936 \n", "L 121.960942 110.950639 \n", "L 125.012502 111.197671 \n", "L 128.064069 111.43604 \n", "L 131.115628 111.665742 \n", "L 134.167188 111.886772 \n", "L 137.218755 112.099135 \n", "L 140.270314 112.302831 \n", "L 143.321874 112.497859 \n", "L 146.373441 112.68422 \n", "L 149.425 112.861914 \n", "L 152.476564 113.030936 \n", "L 155.528127 113.191291 \n", "L 158.57969 113.342983 \n", "L 161.631253 113.486004 \n", "L 164.682813 113.620353 \n", "L 167.734376 113.746039 \n", "L 169.542874 113.81539 \n", "L 170.785939 113.86084 \n", "L 173.837499 113.964148 \n", "L 176.889062 114.059192 \n", "L 179.940626 114.145972 \n", "L 182.992189 114.224487 \n", "L 186.04375 114.294739 \n", "L 189.095313 114.356726 \n", "L 192.146877 114.410445 \n", "L 195.198438 114.455904 \n", "L 198.250001 114.493094 \n", "L 201.301564 114.522021 \n", "L 204.353126 114.542683 \n", "L 207.404689 114.555078 \n", "L 210.456251 114.559212 \n", "L 213.507813 114.555078 \n", "L 216.559376 114.542683 \n", "L 219.610939 114.522021 \n", "L 222.662501 114.493094 \n", "L 225.714063 114.455904 \n", "L 228.765626 114.410445 \n", "L 231.817188 114.356726 \n", "L 234.868751 114.294739 \n", "L 237.920313 114.224487 \n", "\" clip-path=\"url(#pdae8947845)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_5\">\n", "    <path d=\"M 42.620312 114.455904 \n", "L 45.671872 114.90633 \n", "L 48.723432 115.348496 \n", "L 51.775006 115.782398 \n", "L 54.826565 116.208033 \n", "L 57.878125 116.625402 \n", "L 60.929685 117.034508 \n", "L 63.477901 117.369232 \n", "L 63.981244 117.432411 \n", "L 67.032818 117.80754 \n", "L 70.084378 118.174768 \n", "L 73.135938 118.534104 \n", "L 76.187512 118.885538 \n", "L 79.239071 119.229076 \n", "L 82.290631 119.564717 \n", "L 85.342191 119.892461 \n", "L 88.393757 120.212309 \n", "L 91.44531 120.524255 \n", "L 94.496869 120.828305 \n", "L 95.47338 120.923075 \n", "L 97.548436 121.115889 \n", "L 100.599996 121.391879 \n", "L 103.651563 121.660309 \n", "L 106.703122 121.921177 \n", "L 109.754682 122.174485 \n", "L 112.806249 122.420228 \n", "L 115.857816 122.658411 \n", "L 118.909375 122.889036 \n", "L 121.960942 123.112097 \n", "L 125.012502 123.327593 \n", "L 128.064069 123.535532 \n", "L 131.115628 123.735911 \n", "L 134.167188 123.928724 \n", "L 137.218755 124.113977 \n", "L 140.270314 124.29167 \n", "L 143.321874 124.461801 \n", "L 143.605786 124.476926 \n", "L 146.373441 124.618353 \n", "L 149.425 124.767036 \n", "L 152.476564 124.908463 \n", "L 155.528127 125.042637 \n", "L 158.57969 125.169563 \n", "L 161.631253 125.289234 \n", "L 164.682813 125.401648 \n", "L 167.734376 125.506814 \n", "L 170.785939 125.604728 \n", "L 173.837499 125.695385 \n", "L 176.889062 125.778791 \n", "L 179.940626 125.854945 \n", "L 182.992189 125.923846 \n", "L 186.04375 125.985495 \n", "L 189.095313 126.039892 \n", "L 192.146877 126.087033 \n", "L 195.198438 126.126925 \n", "L 198.250001 126.159562 \n", "L 201.301564 126.184946 \n", "L 204.353126 126.203079 \n", "L 207.404689 126.213955 \n", "L 210.456251 126.217583 \n", "L 213.507813 126.213955 \n", "L 216.559376 126.203079 \n", "L 219.610939 126.184946 \n", "L 222.662501 126.159562 \n", "L 225.714063 126.126925 \n", "L 228.765626 126.087033 \n", "L 231.817188 126.039892 \n", "L 234.868751 125.985495 \n", "L 237.920313 125.923846 \n", "\" clip-path=\"url(#pdae8947845)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_6\">\n", "    <path d=\"M 42.620312 126.126925 \n", "L 45.671872 126.522197 \n", "L 48.723432 126.91022 \n", "L 51.775006 127.290991 \n", "L 54.826565 127.664506 \n", "L 57.878125 128.030769 \n", "L 57.878125 128.030769 \n", "L 60.929685 128.375701 \n", "L 63.981244 128.713666 \n", "L 67.032818 129.044662 \n", "L 70.084378 129.368688 \n", "L 73.135938 129.685749 \n", "L 76.187512 129.995838 \n", "L 79.239071 130.29896 \n", "L 82.290631 130.595114 \n", "L 85.342191 130.884301 \n", "L 88.393757 131.166519 \n", "L 91.44531 131.441767 \n", "L 93.070112 131.584612 \n", "L 94.496869 131.705313 \n", "L 97.548436 131.956763 \n", "L 100.599996 132.201509 \n", "L 103.651563 132.43955 \n", "L 106.703122 132.670885 \n", "L 109.754682 132.895516 \n", "L 112.806249 133.113439 \n", "L 115.857816 133.324659 \n", "L 118.909375 133.529174 \n", "L 121.960942 133.726982 \n", "L 125.012502 133.918082 \n", "L 128.064069 134.102481 \n", "L 131.115628 134.280175 \n", "L 134.167188 134.45116 \n", "L 137.218755 134.615441 \n", "L 140.270314 134.773017 \n", "L 143.321874 134.923888 \n", "L 146.373441 135.068054 \n", "L 147.936485 135.138463 \n", "L 149.425 135.203077 \n", "L 152.476564 135.329076 \n", "L 155.528127 135.448613 \n", "L 158.57969 135.561693 \n", "L 161.631253 135.668308 \n", "L 164.682813 135.768459 \n", "L 167.734376 135.862153 \n", "L 170.785939 135.949385 \n", "L 173.837499 136.030153 \n", "L 176.889062 136.10446 \n", "L 179.940626 136.172306 \n", "L 182.992189 136.233691 \n", "L 186.04375 136.288614 \n", "L 189.095313 136.337077 \n", "L 192.146877 136.379075 \n", "L 195.198438 136.414616 \n", "L 198.250001 136.443692 \n", "L 201.301564 136.466308 \n", "L 204.353126 136.482462 \n", "L 207.404689 136.492152 \n", "L 210.456251 136.495384 \n", "L 213.507813 136.492152 \n", "L 216.559376 136.482462 \n", "L 219.610939 136.466308 \n", "L 222.662501 136.443692 \n", "L 225.714063 136.414616 \n", "L 228.765626 136.379075 \n", "L 231.817188 136.337077 \n", "L 234.868751 136.288614 \n", "L 237.920313 136.233691 \n", "\" clip-path=\"url(#pdae8947845)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_7\">\n", "    <path d=\"M 42.620312 136.414613 \n", "L 45.671872 136.766771 \n", "L 48.723432 137.112458 \n", "L 51.775006 137.451694 \n", "L 54.826565 137.784465 \n", "L 57.878125 138.110766 \n", "L 60.929685 138.430616 \n", "L 63.477871 138.692306 \n", "L 63.981244 138.742187 \n", "L 67.032818 139.038339 \n", "L 70.084378 139.328259 \n", "L 73.135938 139.611942 \n", "L 76.187512 139.889394 \n", "L 79.239071 140.160608 \n", "L 82.290631 140.425588 \n", "L 85.342191 140.684331 \n", "L 88.393757 140.936841 \n", "L 91.44531 141.183116 \n", "L 94.496869 141.423157 \n", "L 97.548436 141.656963 \n", "L 100.599996 141.884534 \n", "L 103.651563 142.105868 \n", "L 105.641791 142.246157 \n", "L 106.703122 142.318434 \n", "L 109.754682 142.520218 \n", "L 112.806249 142.715983 \n", "L 115.857816 142.905721 \n", "L 118.909375 143.089439 \n", "L 121.960942 143.267132 \n", "L 125.012502 143.438798 \n", "L 128.064069 143.604445 \n", "L 131.115628 143.764065 \n", "L 134.167188 143.917665 \n", "L 137.218755 144.06524 \n", "L 140.270314 144.206794 \n", "L 143.321874 144.342322 \n", "L 146.373441 144.471825 \n", "L 149.425 144.595307 \n", "L 152.476564 144.712763 \n", "L 155.528127 144.8242 \n", "L 158.57969 144.92961 \n", "L 161.631253 145.028994 \n", "L 164.682813 145.122358 \n", "L 167.734376 145.209702 \n", "L 170.785939 145.291015 \n", "L 173.837499 145.366313 \n", "L 176.889062 145.435579 \n", "L 179.940626 145.498825 \n", "L 182.992189 145.556051 \n", "L 186.04375 145.607251 \n", "L 189.095313 145.652426 \n", "L 192.146877 145.69158 \n", "L 195.198438 145.724708 \n", "L 198.250001 145.75181 \n", "L 201.301564 145.772892 \n", "L 204.353126 145.787954 \n", "L 207.404689 145.79699 \n", "L 210.456251 145.8 \n", "\" clip-path=\"url(#pdae8947845)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 210.456251 145.8 \n", "L 213.507813 145.79699 \n", "L 216.559376 145.787954 \n", "L 219.610939 145.772892 \n", "L 222.662501 145.75181 \n", "L 225.714063 145.724708 \n", "L 228.765626 145.69158 \n", "L 231.817188 145.652426 \n", "L 234.868751 145.607251 \n", "L 237.**********.556051 \n", "\" clip-path=\"url(#pdae8947845)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_8\">\n", "    <path d=\"M 42.**********.724708 \n", "L 43.320206 145.8 \n", "\" clip-path=\"url(#pdae8947845)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_9\"/>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.8 \n", "L 42.620312 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.8 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 7.2 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pdae8947845\">\n", "   <rect x=\"42.620312\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def adagrad_2d(x1, x2, s1, s2):\n", "    eps = 1e-6\n", "    g1, g2 = 0.2 * x1, 4 * x2\n", "    s1 += g1 ** 2\n", "    s2 += g2 ** 2\n", "    x1 -= eta / math.sqrt(s1 + eps) * g1\n", "    x2 -= eta / math.sqrt(s2 + eps) * g2\n", "    return x1, x2, s1, s2\n", "\n", "def f_2d(x1, x2):\n", "    return 0.1 * x1 ** 2 + 2 * x2 ** 2\n", "\n", "eta = 0.4\n", "d2l.show_trace_2d(f_2d, d2l.train_2d(adagrad_2d))"]}, {"cell_type": "markdown", "id": "3b97f88a", "metadata": {"origin_pos": 5}, "source": ["As we increase the learning rate to $2$ we see much better behavior. This already indicates that the decrease in learning rate might be rather aggressive, even in the noise-free case and we need to ensure that parameters converge appropriately.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "168b7aa7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:31.049192Z", "iopub.status.busy": "2023-08-18T19:39:31.048894Z", "iopub.status.idle": "2023-08-18T19:39:31.285604Z", "shell.execute_reply": "2023-08-18T19:39:31.284736Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 20, x1: -0.002295, x2: -0.000000\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"245.120313pt\" height=\"183.35625pt\" viewBox=\"0 0 245.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:39:31.245627</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 245.**********.35625 \n", "L 245.120313 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "L 237.920313 7.2 \n", "L 42.620312 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m73c90916d3\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m73c90916d3\" x=\"88.39375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(81.022656 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m73c90916d3\" x=\"149.425\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(142.053907 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m73c90916d3\" x=\"210.456251\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(207.275001 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- x1 -->\n", "     <g transform=\"translate(134.129687 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m6df9065cb1\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m6df9065cb1\" x=\"42.620312\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −3 -->\n", "      <g transform=\"translate(20.878125 149.599219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m6df9065cb1\" x=\"42.620312\" y=\"110.261538\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(20.878125 114.060757) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m6df9065cb1\" x=\"42.620312\" y=\"74.723076\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(20.878125 78.522295) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m6df9065cb1\" x=\"42.620312\" y=\"39.184615\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 42.983833) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- x2 -->\n", "     <g transform=\"translate(14.798437 82.640625) rotate(-90) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_8\">\n", "    <path d=\"M 57.878125 110.261538 \n", "L 118.909345 39.184615 \n", "L 150.30966 39.184615 \n", "L 169.853409 39.184615 \n", "L 182.748839 39.184615 \n", "L 191.45862 39.184615 \n", "L 197.40211 39.184615 \n", "L 201.47704 39.184615 \n", "L 204.277004 39.184615 \n", "L 206.202904 39.184615 \n", "L 207.528241 39.184615 \n", "L 208.440503 39.184615 \n", "L 209.068504 39.184615 \n", "L 209.500842 39.184615 \n", "L 209.798486 39.184615 \n", "L 210.003402 39.184615 \n", "L 210.144479 39.184615 \n", "L 210.241606 39.184615 \n", "L 210.308475 39.184615 \n", "L 210.354512 39.184615 \n", "L 210.386207 39.184615 \n", "\" clip-path=\"url(#p88cf590792)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m8717df0d3e\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p88cf590792)\">\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"57.878125\" y=\"110.261538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"118.909345\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"150.30966\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"169.853409\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"182.748839\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"191.45862\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"197.40211\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"201.47704\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"204.277004\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"206.202904\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"207.528241\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"208.440503\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"209.068504\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"209.500842\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"209.798486\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"210.003402\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"210.144479\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"210.241606\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"210.308475\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"210.354512\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m8717df0d3e\" x=\"210.386207\" y=\"39.184615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"PathCollection_1\"/>\n", "   <g id=\"PathCollection_2\">\n", "    <path d=\"M 97.100868 7.2 \n", "L 94.496869 7.868961 \n", "L 91.44531 8.673801 \n", "L 88.393757 9.499545 \n", "L 85.342191 10.346198 \n", "L 83.908323 10.753845 \n", "L 82.290631 11.275074 \n", "L 79.239071 12.281998 \n", "L 76.187512 13.312611 \n", "L 73.307377 14.307692 \n", "L 73.135938 14.376037 \n", "L 70.084378 15.61988 \n", "L 67.032818 16.891062 \n", "L 64.752171 17.861537 \n", "L 63.981244 18.249232 \n", "L 60.929685 19.816153 \n", "L 57.878125 21.415384 \n", "L 57.878125 21.415384 \n", "L 54.826565 23.409482 \n", "L 52.48604 24.96923 \n", "L 51.775006 25.578453 \n", "L 48.723432 28.243853 \n", "L 48.409729 28.523076 \n", "L 45.671872 31.93477 \n", "L 45.55989 32.076922 \n", "L 43.880133 35.630769 \n", "L 43.320214 39.184616 \n", "L 43.880133 42.738462 \n", "L 45.55989 46.292308 \n", "L 45.671872 46.43446 \n", "L 48.409729 49.846154 \n", "L 48.723432 50.125377 \n", "L 51.775006 52.790777 \n", "L 52.486042 53.400001 \n", "L 54.826565 54.959745 \n", "L 57.878125 56.953846 \n", "L 57.878125 56.953846 \n", "L 60.929685 58.553076 \n", "L 63.981244 60.119998 \n", "L 64.752171 60.507693 \n", "L 67.032818 61.478167 \n", "L 70.084378 62.749349 \n", "L 73.135938 63.993193 \n", "L 73.307377 64.061538 \n", "L 76.187512 65.056618 \n", "L 79.239071 66.087232 \n", "L 82.290631 67.094156 \n", "L 83.908319 67.615382 \n", "L 85.342191 68.02303 \n", "L 88.393757 68.869685 \n", "L 91.44531 69.695427 \n", "L 94.496869 70.500269 \n", "L 97.100868 71.169229 \n", "L 97.548436 71.272105 \n", "L 100.599996 71.954818 \n", "L 103.651563 72.618826 \n", "L 106.703122 73.264128 \n", "L 109.754682 73.890726 \n", "L 112.806249 74.498621 \n", "L 113.968755 74.723076 \n", "L 115.857816 75.053077 \n", "L 118.909375 75.56923 \n", "L 121.960942 76.068463 \n", "L 125.012502 76.55077 \n", "L 128.064069 77.016155 \n", "L 131.115628 77.464616 \n", "L 134.167188 77.896154 \n", "L 136.969651 78.276924 \n", "L 137.218755 78.307826 \n", "L 140.270314 78.670936 \n", "L 143.321874 79.018595 \n", "L 146.373441 79.350803 \n", "L 149.425 79.667558 \n", "L 152.476564 79.968862 \n", "L 155.528127 80.254716 \n", "L 158.57969 80.525117 \n", "L 161.631253 80.780066 \n", "L 164.682813 81.019566 \n", "L 167.734376 81.243611 \n", "L 170.785939 81.452208 \n", "L 173.837499 81.645351 \n", "L 176.889062 81.823043 \n", "L 177.034329 81.830766 \n", "L 179.940626 81.972923 \n", "L 182.992189 82.107968 \n", "L 186.04375 82.228801 \n", "L 189.095313 82.335415 \n", "L 192.146877 82.427815 \n", "L 195.198438 82.505999 \n", "L 198.250001 82.569969 \n", "L 201.301564 82.619722 \n", "L 204.353126 82.655262 \n", "L 207.404689 82.676585 \n", "L 210.456251 82.683692 \n", "L 213.507813 82.676585 \n", "L 216.559376 82.655262 \n", "L 219.610939 82.619722 \n", "L 222.662501 82.569969 \n", "L 225.714063 82.505999 \n", "L 228.765626 82.427815 \n", "L 231.817188 82.335415 \n", "L 234.868751 82.228801 \n", "L 237.920313 82.107968 \n", "\" clip-path=\"url(#p88cf590792)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_3\">\n", "    <path d=\"M 42.620312 82.505999 \n", "L 45.671872 83.280739 \n", "L 48.723432 84.041258 \n", "L 51.775006 84.78757 \n", "L 54.263642 85.384613 \n", "L 54.826565 85.509659 \n", "L 57.878125 86.174359 \n", "L 60.929685 86.825897 \n", "L 63.981244 87.464272 \n", "L 67.032818 88.089488 \n", "L 70.084378 88.701539 \n", "L 71.29158 88.93846 \n", "L 73.135938 89.275465 \n", "L 76.187512 89.820797 \n", "L 79.239071 90.353873 \n", "L 82.290631 90.874696 \n", "L 85.342191 91.383263 \n", "L 88.393757 91.879576 \n", "L 91.44531 92.363634 \n", "L 92.277553 92.492307 \n", "L 94.496869 92.813298 \n", "L 97.548436 93.243201 \n", "L 100.599996 93.661637 \n", "L 103.651563 94.06861 \n", "L 106.703122 94.464117 \n", "L 109.754682 94.848161 \n", "L 112.806249 95.220742 \n", "L 115.857816 95.58186 \n", "L 118.909375 95.931512 \n", "L 119.943828 96.046154 \n", "L 121.960942 96.256152 \n", "L 125.012502 96.563076 \n", "L 128.064069 96.859229 \n", "L 131.115628 97.144614 \n", "L 134.167188 97.41923 \n", "L 137.218755 97.683078 \n", "L 140.270314 97.936154 \n", "L 143.321874 98.17846 \n", "L 146.373441 98.41 \n", "L 149.425 98.630769 \n", "L 152.476564 98.840769 \n", "L 155.528127 99.040001 \n", "L 158.57969 99.228461 \n", "L 161.631253 99.406154 \n", "L 164.682813 99.573078 \n", "L 165.20896 99.600001 \n", "L 167.734376 99.721846 \n", "L 170.785939 99.858923 \n", "L 173.837499 99.985846 \n", "L 176.889062 100.102617 \n", "L 179.940626 100.209232 \n", "L 182.992189 100.305693 \n", "L 186.04375 100.392 \n", "L 189.095313 100.468155 \n", "L 192.146877 100.534155 \n", "L 195.198438 100.59 \n", "L 198.250001 100.635694 \n", "L 201.301564 100.671232 \n", "L 204.353126 100.696615 \n", "L 207.404689 100.711847 \n", "L 210.456251 100.716923 \n", "L 213.507813 100.711847 \n", "L 216.559376 100.696615 \n", "L 219.610939 100.671232 \n", "L 222.662501 100.635694 \n", "L 225.714063 100.59 \n", "L 228.765626 100.534155 \n", "L 231.817188 100.468155 \n", "L 234.868751 100.392 \n", "L 237.920313 100.305693 \n", "\" clip-path=\"url(#p88cf590792)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_4\">\n", "    <path d=\"M 42.620312 100.589999 \n", "L 45.671872 101.143384 \n", "L 48.723432 101.686614 \n", "L 51.775006 102.219694 \n", "L 54.826565 102.742616 \n", "L 57.27387 103.153848 \n", "L 57.878125 103.249896 \n", "L 60.929685 103.725343 \n", "L 63.981244 104.191185 \n", "L 67.032818 104.647423 \n", "L 70.084378 105.094054 \n", "L 73.135938 105.53108 \n", "L 76.187512 105.958505 \n", "L 79.239071 106.376322 \n", "L 81.716227 106.707695 \n", "L 82.290631 106.780594 \n", "L 85.342191 107.158756 \n", "L 88.393757 107.527811 \n", "L 91.44531 107.88775 \n", "L 94.496869 108.23858 \n", "L 97.548436 108.580295 \n", "L 100.599996 108.912898 \n", "L 103.651563 109.236389 \n", "L 106.703122 109.550769 \n", "L 109.754682 109.856036 \n", "L 112.806249 110.152188 \n", "L 113.968764 110.261538 \n", "L 115.857816 110.430561 \n", "L 118.909375 110.694936 \n", "L 121.960942 110.950639 \n", "L 125.012502 111.197671 \n", "L 128.064069 111.43604 \n", "L 131.115628 111.665742 \n", "L 134.167188 111.886772 \n", "L 137.218755 112.099135 \n", "L 140.270314 112.302831 \n", "L 143.321874 112.497859 \n", "L 146.373441 112.68422 \n", "L 149.425 112.861914 \n", "L 152.476564 113.030936 \n", "L 155.528127 113.191291 \n", "L 158.57969 113.342983 \n", "L 161.631253 113.486004 \n", "L 164.682813 113.620353 \n", "L 167.734376 113.746039 \n", "L 169.542874 113.81539 \n", "L 170.785939 113.86084 \n", "L 173.837499 113.964148 \n", "L 176.889062 114.059192 \n", "L 179.940626 114.145972 \n", "L 182.992189 114.224487 \n", "L 186.04375 114.294739 \n", "L 189.095313 114.356726 \n", "L 192.146877 114.410445 \n", "L 195.198438 114.455904 \n", "L 198.250001 114.493094 \n", "L 201.301564 114.522021 \n", "L 204.353126 114.542683 \n", "L 207.404689 114.555078 \n", "L 210.456251 114.559212 \n", "L 213.507813 114.555078 \n", "L 216.559376 114.542683 \n", "L 219.610939 114.522021 \n", "L 222.662501 114.493094 \n", "L 225.714063 114.455904 \n", "L 228.765626 114.410445 \n", "L 231.817188 114.356726 \n", "L 234.868751 114.294739 \n", "L 237.920313 114.224487 \n", "\" clip-path=\"url(#p88cf590792)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_5\">\n", "    <path d=\"M 42.620312 114.455904 \n", "L 45.671872 114.90633 \n", "L 48.723432 115.348496 \n", "L 51.775006 115.782398 \n", "L 54.826565 116.208033 \n", "L 57.878125 116.625402 \n", "L 60.929685 117.034508 \n", "L 63.477901 117.369232 \n", "L 63.981244 117.432411 \n", "L 67.032818 117.80754 \n", "L 70.084378 118.174768 \n", "L 73.135938 118.534104 \n", "L 76.187512 118.885538 \n", "L 79.239071 119.229076 \n", "L 82.290631 119.564717 \n", "L 85.342191 119.892461 \n", "L 88.393757 120.212309 \n", "L 91.44531 120.524255 \n", "L 94.496869 120.828305 \n", "L 95.47338 120.923075 \n", "L 97.548436 121.115889 \n", "L 100.599996 121.391879 \n", "L 103.651563 121.660309 \n", "L 106.703122 121.921177 \n", "L 109.754682 122.174485 \n", "L 112.806249 122.420228 \n", "L 115.857816 122.658411 \n", "L 118.909375 122.889036 \n", "L 121.960942 123.112097 \n", "L 125.012502 123.327593 \n", "L 128.064069 123.535532 \n", "L 131.115628 123.735911 \n", "L 134.167188 123.928724 \n", "L 137.218755 124.113977 \n", "L 140.270314 124.29167 \n", "L 143.321874 124.461801 \n", "L 143.605786 124.476926 \n", "L 146.373441 124.618353 \n", "L 149.425 124.767036 \n", "L 152.476564 124.908463 \n", "L 155.528127 125.042637 \n", "L 158.57969 125.169563 \n", "L 161.631253 125.289234 \n", "L 164.682813 125.401648 \n", "L 167.734376 125.506814 \n", "L 170.785939 125.604728 \n", "L 173.837499 125.695385 \n", "L 176.889062 125.778791 \n", "L 179.940626 125.854945 \n", "L 182.992189 125.923846 \n", "L 186.04375 125.985495 \n", "L 189.095313 126.039892 \n", "L 192.146877 126.087033 \n", "L 195.198438 126.126925 \n", "L 198.250001 126.159562 \n", "L 201.301564 126.184946 \n", "L 204.353126 126.203079 \n", "L 207.404689 126.213955 \n", "L 210.456251 126.217583 \n", "L 213.507813 126.213955 \n", "L 216.559376 126.203079 \n", "L 219.610939 126.184946 \n", "L 222.662501 126.159562 \n", "L 225.714063 126.126925 \n", "L 228.765626 126.087033 \n", "L 231.817188 126.039892 \n", "L 234.868751 125.985495 \n", "L 237.920313 125.923846 \n", "\" clip-path=\"url(#p88cf590792)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_6\">\n", "    <path d=\"M 42.620312 126.126925 \n", "L 45.671872 126.522197 \n", "L 48.723432 126.91022 \n", "L 51.775006 127.290991 \n", "L 54.826565 127.664506 \n", "L 57.878125 128.030769 \n", "L 57.878125 128.030769 \n", "L 60.929685 128.375701 \n", "L 63.981244 128.713666 \n", "L 67.032818 129.044662 \n", "L 70.084378 129.368688 \n", "L 73.135938 129.685749 \n", "L 76.187512 129.995838 \n", "L 79.239071 130.29896 \n", "L 82.290631 130.595114 \n", "L 85.342191 130.884301 \n", "L 88.393757 131.166519 \n", "L 91.44531 131.441767 \n", "L 93.070112 131.584612 \n", "L 94.496869 131.705313 \n", "L 97.548436 131.956763 \n", "L 100.599996 132.201509 \n", "L 103.651563 132.43955 \n", "L 106.703122 132.670885 \n", "L 109.754682 132.895516 \n", "L 112.806249 133.113439 \n", "L 115.857816 133.324659 \n", "L 118.909375 133.529174 \n", "L 121.960942 133.726982 \n", "L 125.012502 133.918082 \n", "L 128.064069 134.102481 \n", "L 131.115628 134.280175 \n", "L 134.167188 134.45116 \n", "L 137.218755 134.615441 \n", "L 140.270314 134.773017 \n", "L 143.321874 134.923888 \n", "L 146.373441 135.068054 \n", "L 147.936485 135.138463 \n", "L 149.425 135.203077 \n", "L 152.476564 135.329076 \n", "L 155.528127 135.448613 \n", "L 158.57969 135.561693 \n", "L 161.631253 135.668308 \n", "L 164.682813 135.768459 \n", "L 167.734376 135.862153 \n", "L 170.785939 135.949385 \n", "L 173.837499 136.030153 \n", "L 176.889062 136.10446 \n", "L 179.940626 136.172306 \n", "L 182.992189 136.233691 \n", "L 186.04375 136.288614 \n", "L 189.095313 136.337077 \n", "L 192.146877 136.379075 \n", "L 195.198438 136.414616 \n", "L 198.250001 136.443692 \n", "L 201.301564 136.466308 \n", "L 204.353126 136.482462 \n", "L 207.404689 136.492152 \n", "L 210.456251 136.495384 \n", "L 213.507813 136.492152 \n", "L 216.559376 136.482462 \n", "L 219.610939 136.466308 \n", "L 222.662501 136.443692 \n", "L 225.714063 136.414616 \n", "L 228.765626 136.379075 \n", "L 231.817188 136.337077 \n", "L 234.868751 136.288614 \n", "L 237.920313 136.233691 \n", "\" clip-path=\"url(#p88cf590792)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_7\">\n", "    <path d=\"M 42.620312 136.414613 \n", "L 45.671872 136.766771 \n", "L 48.723432 137.112458 \n", "L 51.775006 137.451694 \n", "L 54.826565 137.784465 \n", "L 57.878125 138.110766 \n", "L 60.929685 138.430616 \n", "L 63.477871 138.692306 \n", "L 63.981244 138.742187 \n", "L 67.032818 139.038339 \n", "L 70.084378 139.328259 \n", "L 73.135938 139.611942 \n", "L 76.187512 139.889394 \n", "L 79.239071 140.160608 \n", "L 82.290631 140.425588 \n", "L 85.342191 140.684331 \n", "L 88.393757 140.936841 \n", "L 91.44531 141.183116 \n", "L 94.496869 141.423157 \n", "L 97.548436 141.656963 \n", "L 100.599996 141.884534 \n", "L 103.651563 142.105868 \n", "L 105.641791 142.246157 \n", "L 106.703122 142.318434 \n", "L 109.754682 142.520218 \n", "L 112.806249 142.715983 \n", "L 115.857816 142.905721 \n", "L 118.909375 143.089439 \n", "L 121.960942 143.267132 \n", "L 125.012502 143.438798 \n", "L 128.064069 143.604445 \n", "L 131.115628 143.764065 \n", "L 134.167188 143.917665 \n", "L 137.218755 144.06524 \n", "L 140.270314 144.206794 \n", "L 143.321874 144.342322 \n", "L 146.373441 144.471825 \n", "L 149.425 144.595307 \n", "L 152.476564 144.712763 \n", "L 155.528127 144.8242 \n", "L 158.57969 144.92961 \n", "L 161.631253 145.028994 \n", "L 164.682813 145.122358 \n", "L 167.734376 145.209702 \n", "L 170.785939 145.291015 \n", "L 173.837499 145.366313 \n", "L 176.889062 145.435579 \n", "L 179.940626 145.498825 \n", "L 182.992189 145.556051 \n", "L 186.04375 145.607251 \n", "L 189.095313 145.652426 \n", "L 192.146877 145.69158 \n", "L 195.198438 145.724708 \n", "L 198.250001 145.75181 \n", "L 201.301564 145.772892 \n", "L 204.353126 145.787954 \n", "L 207.404689 145.79699 \n", "L 210.456251 145.8 \n", "\" clip-path=\"url(#p88cf590792)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 210.456251 145.8 \n", "L 213.507813 145.79699 \n", "L 216.559376 145.787954 \n", "L 219.610939 145.772892 \n", "L 222.662501 145.75181 \n", "L 225.714063 145.724708 \n", "L 228.765626 145.69158 \n", "L 231.817188 145.652426 \n", "L 234.868751 145.607251 \n", "L 237.**********.556051 \n", "\" clip-path=\"url(#p88cf590792)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_8\">\n", "    <path d=\"M 42.**********.724708 \n", "L 43.320206 145.8 \n", "\" clip-path=\"url(#p88cf590792)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_9\"/>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.8 \n", "L 42.620312 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.8 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 7.2 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p88cf590792\">\n", "   <rect x=\"42.620312\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["eta = 2\n", "d2l.show_trace_2d(f_2d, d2l.train_2d(adagrad_2d))"]}, {"cell_type": "markdown", "id": "1ecadc52", "metadata": {"origin_pos": 7}, "source": ["## Implementation from Scratch\n", "\n", "Just like the momentum method, Adagrad needs to maintain a state variable of the same shape as the parameters.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6adec53c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:31.289088Z", "iopub.status.busy": "2023-08-18T19:39:31.288789Z", "iopub.status.idle": "2023-08-18T19:39:31.294582Z", "shell.execute_reply": "2023-08-18T19:39:31.293717Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["def init_adagrad_states(feature_dim):\n", "    s_w = torch.zeros((feature_dim, 1))\n", "    s_b = torch.zeros(1)\n", "    return (s_w, s_b)\n", "\n", "def adagrad(params, states, hyperparams):\n", "    eps = 1e-6\n", "    for p, s in zip(params, states):\n", "        with torch.no_grad():\n", "            s[:] += torch.square(p.grad)\n", "            p[:] -= hyperparams['lr'] * p.grad / torch.sqrt(s + eps)\n", "        p.grad.data.zero_()"]}, {"cell_type": "markdown", "id": "687d5df2", "metadata": {"origin_pos": 11}, "source": ["Compared to the experiment in :numref:`sec_minibatch_sgd` we use a\n", "larger learning rate to train the model.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "48c42e3a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:31.297779Z", "iopub.status.busy": "2023-08-18T19:39:31.297488Z", "iopub.status.idle": "2023-08-18T19:39:35.623907Z", "shell.execute_reply": "2023-08-18T19:39:35.622834Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss: 0.243, 0.162 sec/epoch\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"266.957813pt\" height=\"187.155469pt\" viewBox=\"0 0 266.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:39:35.574028</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 187.155469 \n", "L 266.**********.155469 \n", "L 266.957813 -0 \n", "L -0 -0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "L 56.50625 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 56.50625 149.599219 \n", "L 56.50625 10.999219 \n", "\" clip-path=\"url(#p9eb013b0d2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"md6580864c2\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md6580864c2\" x=\"56.50625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(48.554688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 105.33125 149.599219 \n", "L 105.33125 10.999219 \n", "\" clip-path=\"url(#p9eb013b0d2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#md6580864c2\" x=\"105.33125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(97.379688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 154.15625 149.599219 \n", "L 154.15625 10.999219 \n", "\" clip-path=\"url(#p9eb013b0d2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#md6580864c2\" x=\"154.15625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(146.204688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 202.98125 149.599219 \n", "L 202.98125 10.999219 \n", "\" clip-path=\"url(#p9eb013b0d2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#md6580864c2\" x=\"202.98125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(195.029688 164.197656) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#p9eb013b0d2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#md6580864c2\" x=\"251.80625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(243.854688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(138.928125 177.875781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 56.50625 144.26845 \n", "L 251.80625 144.26845 \n", "\" clip-path=\"url(#p9eb013b0d2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m7b58b5a940\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7b58b5a940\" x=\"56.50625\" y=\"144.26845\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.225 -->\n", "      <g transform=\"translate(20.878125 148.067668) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 56.50625 117.614603 \n", "L 251.80625 117.614603 \n", "\" clip-path=\"url(#p9eb013b0d2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m7b58b5a940\" x=\"56.50625\" y=\"117.614603\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.250 -->\n", "      <g transform=\"translate(20.878125 121.413822) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 56.50625 90.960757 \n", "L 251.80625 90.960757 \n", "\" clip-path=\"url(#p9eb013b0d2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m7b58b5a940\" x=\"56.50625\" y=\"90.960757\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.275 -->\n", "      <g transform=\"translate(20.878125 94.759976) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 56.50625 64.306911 \n", "L 251.80625 64.306911 \n", "\" clip-path=\"url(#p9eb013b0d2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m7b58b5a940\" x=\"56.50625\" y=\"64.306911\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.300 -->\n", "      <g transform=\"translate(20.878125 68.10613) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 56.50625 37.653065 \n", "L 251.80625 37.653065 \n", "\" clip-path=\"url(#p9eb013b0d2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m7b58b5a940\" x=\"56.50625\" y=\"37.653065\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.325 -->\n", "      <g transform=\"translate(20.878125 41.452284) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#p9eb013b0d2)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m7b58b5a940\" x=\"56.50625\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.350 -->\n", "      <g transform=\"translate(20.878125 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798438 89.957031) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 69.52625 88.545438 \n", "L 82.54625 118.000928 \n", "L 95.56625 120.454707 \n", "L 108.58625 123.300977 \n", "L 121.60625 120.771561 \n", "L 134.62625 124.078727 \n", "L 147.64625 123.704617 \n", "L 160.66625 124.16329 \n", "L 173.68625 123.780123 \n", "L 186.70625 123.896423 \n", "L 199.72625 123.784577 \n", "L 212.74625 124.932087 \n", "L 225.76625 124.002558 \n", "L 238.78625 124.658106 \n", "L 251.80625 125.259667 \n", "\" clip-path=\"url(#p9eb013b0d2)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 56.50625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 251.80625 149.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p9eb013b0d2\">\n", "   <rect x=\"56.50625\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data_iter, feature_dim = d2l.get_data_ch11(batch_size=10)\n", "d2l.train_ch11(adagrad, init_adagrad_states(feature_dim),\n", "               {'lr': 0.1}, data_iter, feature_dim);"]}, {"cell_type": "markdown", "id": "2b5b4460", "metadata": {"origin_pos": 13}, "source": ["## Concise Implementation\n", "\n", "Using the `Trainer` instance of the algorithm `adagrad`, we can invoke the Adagrad algorithm in Gluon.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "34cdbb4e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:35.628162Z", "iopub.status.busy": "2023-08-18T19:39:35.627593Z", "iopub.status.idle": "2023-08-18T19:39:43.618383Z", "shell.execute_reply": "2023-08-18T19:39:43.617210Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss: 0.242, 0.129 sec/epoch\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"262.1875pt\" height=\"187.155469pt\" viewBox=\"0 0 262.1875 187.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:39:43.566572</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 187.155469 \n", "L 262.1875 187.155469 \n", "L 262.1875 -0 \n", "L -0 -0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "L 56.50625 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 56.50625 149.599219 \n", "L 56.50625 10.999219 \n", "\" clip-path=\"url(#p0df997cc9d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m2e0724ef94\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m2e0724ef94\" x=\"56.50625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(53.325 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 105.33125 149.599219 \n", "L 105.33125 10.999219 \n", "\" clip-path=\"url(#p0df997cc9d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m2e0724ef94\" x=\"105.33125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(102.15 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 154.15625 149.599219 \n", "L 154.15625 10.999219 \n", "\" clip-path=\"url(#p0df997cc9d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m2e0724ef94\" x=\"154.15625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(150.975 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 202.98125 149.599219 \n", "L 202.98125 10.999219 \n", "\" clip-path=\"url(#p0df997cc9d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m2e0724ef94\" x=\"202.98125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(199.8 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#p0df997cc9d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m2e0724ef94\" x=\"251.80625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(248.625 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(138.928125 177.875781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 56.50625 144.26845 \n", "L 251.80625 144.26845 \n", "\" clip-path=\"url(#p0df997cc9d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"me882252677\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me882252677\" x=\"56.50625\" y=\"144.26845\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.225 -->\n", "      <g transform=\"translate(20.878125 148.067668) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 56.50625 117.614603 \n", "L 251.80625 117.614603 \n", "\" clip-path=\"url(#p0df997cc9d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#me882252677\" x=\"56.50625\" y=\"117.614603\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.250 -->\n", "      <g transform=\"translate(20.878125 121.413822) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 56.50625 90.960757 \n", "L 251.80625 90.960757 \n", "\" clip-path=\"url(#p0df997cc9d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#me882252677\" x=\"56.50625\" y=\"90.960757\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.275 -->\n", "      <g transform=\"translate(20.878125 94.759976) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 56.50625 64.306911 \n", "L 251.80625 64.306911 \n", "\" clip-path=\"url(#p0df997cc9d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#me882252677\" x=\"56.50625\" y=\"64.306911\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.300 -->\n", "      <g transform=\"translate(20.878125 68.10613) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 56.50625 37.653065 \n", "L 251.80625 37.653065 \n", "\" clip-path=\"url(#p0df997cc9d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#me882252677\" x=\"56.50625\" y=\"37.653065\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.325 -->\n", "      <g transform=\"translate(20.878125 41.452284) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#p0df997cc9d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#me882252677\" x=\"56.50625\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.350 -->\n", "      <g transform=\"translate(20.878125 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798438 89.957031) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 63.01625 99.44302 \n", "L 69.52625 117.715646 \n", "L 76.03625 120.328707 \n", "L 82.54625 116.526325 \n", "L 89.05625 121.61914 \n", "L 95.56625 124.257027 \n", "L 102.07625 124.417119 \n", "L 108.58625 124.960032 \n", "L 115.09625 123.40329 \n", "L 121.60625 123.743213 \n", "L 128.11625 125.153119 \n", "L 134.62625 123.6103 \n", "L 141.13625 124.265943 \n", "L 147.64625 125.367768 \n", "L 154.15625 126.070983 \n", "L 160.66625 125.904817 \n", "L 167.17625 124.762387 \n", "L 173.68625 123.982012 \n", "L 180.19625 125.093935 \n", "L 186.70625 125.155748 \n", "L 193.21625 124.321487 \n", "L 199.72625 125.917498 \n", "L 206.23625 124.227003 \n", "L 212.74625 125.691929 \n", "L 219.25625 126.260231 \n", "L 225.76625 126.053445 \n", "L 232.27625 125.974543 \n", "L 238.78625 125.834246 \n", "L 245.29625 126.393743 \n", "L 251.80625 125.716271 \n", "\" clip-path=\"url(#p0df997cc9d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 56.50625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 251.80625 149.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p0df997cc9d\">\n", "   <rect x=\"56.50625\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer = torch.optim.Adagrad\n", "d2l.train_concise_ch11(trainer, {'lr': 0.1}, data_iter)"]}, {"cell_type": "markdown", "id": "ea6c53db", "metadata": {"origin_pos": 17}, "source": ["## Summary\n", "\n", "* Adagrad decreases the learning rate dynamically on a per-coordinate basis.\n", "* It uses the magnitude of the gradient as a means of adjusting how quickly progress is achieved - coordinates with large gradients are compensated with a smaller learning rate.\n", "* Computing the exact second derivative is typically infeasible in deep learning problems due to memory and computational constraints. The gradient can be a useful proxy.\n", "* If the optimization problem has a rather uneven structure Adagrad can help mitigate the distortion.\n", "* Adagrad is particularly effective for sparse features where the learning rate needs to decrease more slowly for infrequently occurring terms.\n", "* On deep learning problems Adagrad can sometimes be too aggressive in reducing learning rates. We will discuss strategies for mitigating this in the context of :numref:`sec_adam`.\n", "\n", "## Exercises\n", "\n", "1. Prove that for an orthogonal matrix $\\mathbf{U}$ and a vector $\\mathbf{c}$ the following holds: $\\|\\mathbf{c} - \\mathbf{\\delta}\\|_2 = \\|\\mathbf{U} \\mathbf{c} - \\mathbf{U} \\mathbf{\\delta}\\|_2$. Why does this mean that the magnitude of perturbations does not change after an orthogonal change of variables?\n", "1. Try out Adagrad for $f(\\mathbf{x}) = 0.1 x_1^2 + 2 x_2^2$ and also for the objective function was rotated by 45 degrees, i.e., $f(\\mathbf{x}) = 0.1 (x_1 + x_2)^2 + 2 (x_1 - x_2)^2$. Does it behave differently?\n", "1. Prove [<PERSON><PERSON><PERSON><PERSON><PERSON>'s circle theorem](https://en.wikipedia.org/wiki/G<PERSON><PERSON><PERSON><PERSON>_circle_theorem) which states that eigenvalues $\\lambda_i$ of a matrix $\\mathbf{M}$ satisfy $|\\lambda_i - \\mathbf{M}_{jj}| \\leq \\sum_{k \\neq j} |\\mathbf{M}_{jk}|$ for at least one choice of $j$.\n", "1. What does <PERSON><PERSON><PERSON><PERSON><PERSON>'s theorem tell us about the eigenvalues of the diagonally preconditioned matrix $\\textrm{diag}^{-\\frac{1}{2}}(\\mathbf{M}) \\mathbf{M} \\textrm{diag}^{-\\frac{1}{2}}(\\mathbf{M})$?\n", "1. Try out Adagrad for a proper deep network, such as :numref:`sec_lenet` when applied to Fashion-MNIST.\n", "1. How would you need to modify Adagrad to achieve a less aggressive decay in learning rate?\n"]}, {"cell_type": "markdown", "id": "78744440", "metadata": {"origin_pos": 19, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1072)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
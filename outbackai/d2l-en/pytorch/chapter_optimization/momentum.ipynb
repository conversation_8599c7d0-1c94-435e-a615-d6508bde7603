{"cells": [{"cell_type": "markdown", "id": "d9255644", "metadata": {"origin_pos": 0}, "source": ["# Momentum\n", ":label:`sec_momentum`\n", "\n", "In :numref:`sec_sgd` we reviewed what happens when performing stochastic gradient descent, i.e., when performing optimization where only a noisy variant of the gradient is available. In particular, we noticed that for noisy gradients we need to be extra cautious when it comes to choosing the learning rate in the face of noise. If we decrease it too rapidly, convergence stalls. If we are too lenient, we fail to converge to a good enough solution since noise keeps on driving us away from optimality.\n", "\n", "## Basics\n", "\n", "In this section, we will explore more effective optimization algorithms, especially for certain types of optimization problems that are common in practice.\n", "\n", "\n", "### Leaky Averages\n", "\n", "The previous section saw us discussing minibatch SGD as a means for accelerating computation. It also had the nice side-effect that averaging gradients reduced the amount of variance. The minibatch stochastic gradient descent can be calculated by:\n", "\n", "$$\\mathbf{g}_{t, t-1} = \\partial_{\\mathbf{w}} \\frac{1}{|\\mathcal{B}_t|} \\sum_{i \\in \\mathcal{B}_t} f(\\mathbf{x}_{i}, \\mathbf{w}_{t-1}) = \\frac{1}{|\\mathcal{B}_t|} \\sum_{i \\in \\mathcal{B}_t} \\mathbf{h}_{i, t-1}.\n", "$$\n", "\n", "To keep the notation simple, here we used $\\mathbf{h}_{i, t-1} = \\partial_{\\mathbf{w}} f(\\mathbf{x}_i, \\mathbf{w}_{t-1})$ as the stochastic gradient descent for sample $i$ using the weights updated at time $t-1$.\n", "It would be nice if we could benefit from the effect of variance reduction even beyond averaging gradients on a minibatch. One option to accomplish this task is to replace the gradient computation by a \"leaky average\":\n", "\n", "$$\\mathbf{v}_t = \\beta \\mathbf{v}_{t-1} + \\mathbf{g}_{t, t-1}$$\n", "\n", "for some $\\beta \\in (0, 1)$. This effectively replaces the instantaneous gradient by one that is been averaged over multiple *past* gradients. $\\mathbf{v}$ is called *velocity*. It accumulates past gradients similar to how a heavy ball rolling down the objective function landscape integrates over past forces. To see what is happening in more detail let's expand $\\mathbf{v}_t$ recursively into\n", "\n", "$$\\begin{aligned}\n", "\\mathbf{v}_t = \\beta^2 \\mathbf{v}_{t-2} + \\beta \\mathbf{g}_{t-1, t-2} + \\mathbf{g}_{t, t-1}\n", "= \\ldots, = \\sum_{\\tau = 0}^{t-1} \\beta^{\\tau} \\mathbf{g}_{t-\\tau, t-\\tau-1}.\n", "\\end{aligned}$$\n", "\n", "Large $\\beta$ amounts to a long-range average, whereas small $\\beta$ amounts to only a slight correction relative to a gradient method. The new gradient replacement no longer points into the direction of steepest descent on a particular instance any longer but rather in the direction of a weighted average of past gradients. This allows us to realize most of the benefits of averaging over a batch without the cost of actually computing the gradients on it. We will revisit this averaging procedure in more detail later.\n", "\n", "The above reasoning formed the basis for what is now known as *accelerated* gradient methods, such as gradients with momentum. They enjoy the additional benefit of being much more effective in cases where the optimization problem is ill-conditioned (i.e., where there are some directions where progress is much slower than in others, resembling a narrow canyon). Furthermore, they allow us to average over subsequent gradients to obtain more stable directions of descent. Indeed, the aspect of acceleration even for noise-free convex problems is one of the key reasons why momentum works and why it works so well.\n", "\n", "As one would expect, due to its efficacy momentum is a well-studied subject in optimization for deep learning and beyond. See e.g., the beautiful [expository article](https://distill.pub/2017/momentum/) by :citet:`Goh.2017` for an in-depth analysis and interactive animation. It was proposed by :citet:`Polyak.1964`. :citet:`Nesterov.2018` has a detailed theoretical discussion in the context of convex optimization. Momentum in deep learning has been known to be beneficial for a long time. See e.g., the discussion by :citet:`Sutskever.Martens.Dahl.ea.2013` for details.\n", "\n", "### An Ill-conditioned Problem\n", "\n", "To get a better understanding of the geometric properties of the momentum method we revisit gradient descent, albeit with a significantly less pleasant objective function. Recall that in :numref:`sec_gd` we used $f(\\mathbf{x}) = x_1^2 + 2 x_2^2$, i.e., a moderately distorted ellipsoid objective. We distort this function further by stretching it out in the $x_1$ direction via\n", "\n", "$$f(\\mathbf{x}) = 0.1 x_1^2 + 2 x_2^2.$$\n", "\n", "As before $f$ has its minimum at $(0, 0)$. This function is *very* flat in the direction of $x_1$. Let's see what happens when we perform gradient descent as before on this new function. We pick a learning rate of $0.4$.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "0ac4ffad", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:05.158887Z", "iopub.status.busy": "2023-08-18T19:45:05.158160Z", "iopub.status.idle": "2023-08-18T19:45:08.357402Z", "shell.execute_reply": "2023-08-18T19:45:08.356489Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 20, x1: -0.943467, x2: -0.000073\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"245.120313pt\" height=\"183.35625pt\" viewBox=\"0 0 245.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:08.318669</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 245.**********.35625 \n", "L 245.120313 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "L 237.920313 7.2 \n", "L 42.620312 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m1e3ea6110b\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1e3ea6110b\" x=\"88.39375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(81.022656 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m1e3ea6110b\" x=\"149.425\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(142.053907 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m1e3ea6110b\" x=\"210.456251\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(207.275001 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- x1 -->\n", "     <g transform=\"translate(134.129687 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"ma5225d55c0\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma5225d55c0\" x=\"42.620312\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −3 -->\n", "      <g transform=\"translate(20.878125 149.599219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#ma5225d55c0\" x=\"42.620312\" y=\"114.371429\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(20.878125 118.170647) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#ma5225d55c0\" x=\"42.620312\" y=\"82.942857\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(20.878125 86.742076) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#ma5225d55c0\" x=\"42.620312\" y=\"51.514286\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 55.313504) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#ma5225d55c0\" x=\"42.620312\" y=\"20.085714\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(29.257812 23.884933) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- x2 -->\n", "     <g transform=\"translate(14.798437 82.640625) rotate(-90) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_9\">\n", "    <path d=\"M 57.878125 114.371429 \n", "L 70.084375 13.8 \n", "L 81.314125 74.142857 \n", "L 91.645495 37.937143 \n", "L 101.150356 59.660571 \n", "L 109.894827 46.626514 \n", "L 117.939741 54.446949 \n", "L 125.341062 49.754688 \n", "L 132.150277 52.570044 \n", "L 138.414755 50.880831 \n", "L 144.178075 51.894359 \n", "L 149.480329 51.286242 \n", "L 154.358402 51.651112 \n", "L 158.84623 51.43219 \n", "L 162.975032 51.563543 \n", "L 166.773529 51.484731 \n", "L 170.268147 51.532018 \n", "L 173.483195 51.503646 \n", "L 176.44104 51.520669 \n", "L 179.162257 51.510455 \n", "L 181.665776 51.516584 \n", "\" clip-path=\"url(#p82fbb3c7c3)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m540b4b92f1\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p82fbb3c7c3)\">\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"57.878125\" y=\"114.371429\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"70.084375\" y=\"13.8\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"81.314125\" y=\"74.142857\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"91.645495\" y=\"37.937143\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"101.150356\" y=\"59.660571\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"109.894827\" y=\"46.626514\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"117.939741\" y=\"54.446949\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"125.341062\" y=\"49.754688\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"132.150277\" y=\"52.570044\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"138.414755\" y=\"50.880831\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"144.178075\" y=\"51.894359\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"149.480329\" y=\"51.286242\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"154.358402\" y=\"51.651112\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"158.84623\" y=\"51.43219\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"162.975032\" y=\"51.563543\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"166.773529\" y=\"51.484731\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"170.268147\" y=\"51.532018\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"173.483195\" y=\"51.503646\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"176.44104\" y=\"51.520669\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"179.162257\" y=\"51.510455\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m540b4b92f1\" x=\"181.665776\" y=\"51.516584\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"PathCollection_1\"/>\n", "   <g id=\"PathCollection_2\">\n", "    <path d=\"M 97.100868 23.228572 \n", "L 94.496869 23.82017 \n", "L 91.44531 24.531934 \n", "L 88.393757 25.262183 \n", "L 85.342191 26.010924 \n", "L 83.908323 26.371428 \n", "L 82.290631 26.832379 \n", "L 79.239071 27.722856 \n", "L 76.187512 28.634283 \n", "L 73.307377 29.514286 \n", "L 73.135938 29.574727 \n", "L 70.084378 30.674725 \n", "L 67.032818 31.798899 \n", "L 64.752171 32.657142 \n", "L 63.981244 33.000002 \n", "L 60.929685 34.385714 \n", "L 57.878125 35.8 \n", "L 57.878125 35.8 \n", "L 54.826565 37.563488 \n", "L 52.48604 38.942857 \n", "L 51.775006 39.481626 \n", "L 48.723432 41.838782 \n", "L 48.409729 42.085714 \n", "L 45.671872 45.102859 \n", "L 45.55989 45.228571 \n", "L 43.880133 48.371429 \n", "L 43.320214 51.514286 \n", "L 43.880133 54.657144 \n", "L 45.55989 57.800001 \n", "L 45.671872 57.925713 \n", "L 48.409729 60.942858 \n", "L 48.723432 61.189789 \n", "L 51.775006 63.546946 \n", "L 52.486042 64.085715 \n", "L 54.826565 65.465081 \n", "L 57.878125 67.228571 \n", "L 57.878125 67.228571 \n", "L 60.929685 68.642857 \n", "L 63.981244 70.02857 \n", "L 64.752171 70.371429 \n", "L 67.032818 71.229672 \n", "L 70.084378 72.353847 \n", "L 73.135938 73.453844 \n", "L 73.307377 73.514285 \n", "L 76.187512 74.394289 \n", "L 79.239071 75.305716 \n", "L 82.290631 76.196193 \n", "L 83.908319 76.657141 \n", "L 85.342191 77.017646 \n", "L 88.393757 77.766388 \n", "L 91.44531 78.496637 \n", "L 94.496869 79.208402 \n", "L 97.100868 79.799999 \n", "L 97.548436 79.890978 \n", "L 100.599996 80.494737 \n", "L 103.651563 81.081956 \n", "L 106.703122 81.652631 \n", "L 109.754682 82.206765 \n", "L 112.806249 82.74436 \n", "L 113.968755 82.942857 \n", "L 115.857816 83.234694 \n", "L 118.909375 83.691156 \n", "L 121.960942 84.132654 \n", "L 125.012502 84.559184 \n", "L 128.064069 84.97075 \n", "L 131.115628 85.367348 \n", "L 134.167188 85.74898 \n", "L 136.969651 86.085715 \n", "L 137.218755 86.113044 \n", "L 140.270314 86.434161 \n", "L 143.321874 86.741615 \n", "L 146.373441 87.035404 \n", "L 149.425 87.315528 \n", "L 152.476564 87.581987 \n", "L 155.528127 87.834784 \n", "L 158.57969 88.073913 \n", "L 161.631253 88.299379 \n", "L 164.682813 88.511181 \n", "L 167.734376 88.709316 \n", "L 170.785939 88.89379 \n", "L 173.837499 89.064596 \n", "L 176.889062 89.221739 \n", "L 177.034329 89.228569 \n", "L 179.940626 89.354286 \n", "L 182.992189 89.473714 \n", "L 186.04375 89.580572 \n", "L 189.095313 89.674857 \n", "L 192.146877 89.756572 \n", "L 195.198438 89.825714 \n", "L 198.250001 89.882286 \n", "L 201.301564 89.926286 \n", "L 204.353126 89.957715 \n", "L 207.404689 89.976572 \n", "L 210.456251 89.982857 \n", "L 213.507813 89.976572 \n", "L 216.559376 89.957715 \n", "L 219.610939 89.926286 \n", "L 222.662501 89.882286 \n", "L 225.714063 89.825714 \n", "L 228.765626 89.756572 \n", "L 231.817188 89.674857 \n", "L 234.868751 89.580572 \n", "L 237.920313 89.473714 \n", "\" clip-path=\"url(#p82fbb3c7c3)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_3\">\n", "    <path d=\"M 42.620312 89.825714 \n", "L 45.671872 90.510858 \n", "L 48.723432 91.183426 \n", "L 51.775006 91.843429 \n", "L 54.263642 92.371427 \n", "L 54.826565 92.482012 \n", "L 57.878125 93.069841 \n", "L 60.929685 93.646031 \n", "L 63.981244 94.210581 \n", "L 67.032818 94.763493 \n", "L 70.084378 95.304763 \n", "L 71.29158 95.514285 \n", "L 73.135938 95.812316 \n", "L 76.187512 96.294583 \n", "L 79.239071 96.766011 \n", "L 82.290631 97.226602 \n", "L 85.342191 97.676356 \n", "L 88.393757 98.115272 \n", "L 91.44531 98.54335 \n", "L 92.277553 98.657143 \n", "L 94.496869 98.941012 \n", "L 97.548436 99.321198 \n", "L 100.599996 99.691244 \n", "L 103.651563 100.051152 \n", "L 106.703122 100.40092 \n", "L 109.754682 100.740551 \n", "L 112.806249 101.070044 \n", "L 115.857816 101.3894 \n", "L 118.909375 101.698616 \n", "L 119.943828 101.800001 \n", "L 121.960942 101.985713 \n", "L 125.012502 102.257142 \n", "L 128.064069 102.519047 \n", "L 131.115628 102.771427 \n", "L 134.167188 103.014285 \n", "L 137.218755 103.24762 \n", "L 140.270314 103.471429 \n", "L 143.321874 103.685713 \n", "L 146.373441 103.890476 \n", "L 149.425 104.085714 \n", "L 152.476564 104.271428 \n", "L 155.528127 104.44762 \n", "L 158.57969 104.614286 \n", "L 161.631253 104.771429 \n", "L 164.682813 104.919049 \n", "L 165.20896 104.942859 \n", "L 167.734376 105.050612 \n", "L 170.785939 105.171837 \n", "L 173.837499 105.284082 \n", "L 176.889062 105.387348 \n", "L 179.940626 105.481634 \n", "L 182.992189 105.56694 \n", "L 186.04375 105.643265 \n", "L 189.095313 105.710614 \n", "L 192.146877 105.768981 \n", "L 195.198438 105.818367 \n", "L 198.250001 105.858777 \n", "L 201.301564 105.890205 \n", "L 204.353126 105.912653 \n", "L 207.404689 105.926123 \n", "L 210.456251 105.930613 \n", "L 213.507813 105.926123 \n", "L 216.559376 105.912653 \n", "L 219.610939 105.890205 \n", "L 222.662501 105.858777 \n", "L 225.714063 105.818367 \n", "L 228.765626 105.768981 \n", "L 231.817188 105.710614 \n", "L 234.868751 105.643265 \n", "L 237.920313 105.56694 \n", "\" clip-path=\"url(#p82fbb3c7c3)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_4\">\n", "    <path d=\"M 42.620312 105.818367 \n", "L 45.671872 106.307755 \n", "L 48.723432 106.788163 \n", "L 51.775006 107.259594 \n", "L 54.826565 107.722041 \n", "L 57.27387 108.085717 \n", "L 57.878125 108.170656 \n", "L 60.929685 108.59112 \n", "L 63.981244 109.003089 \n", "L 67.032818 109.406564 \n", "L 70.084378 109.801545 \n", "L 73.135938 110.18803 \n", "L 76.187512 110.566025 \n", "L 79.239071 110.935523 \n", "L 81.716227 111.228574 \n", "L 82.290631 111.293042 \n", "L 85.342191 111.627472 \n", "L 88.393757 111.953847 \n", "L 91.44531 112.27216 \n", "L 94.496869 112.582418 \n", "L 97.548436 112.884615 \n", "L 100.599996 113.178753 \n", "L 103.651563 113.464834 \n", "L 106.703122 113.742857 \n", "L 109.754682 114.012821 \n", "L 112.806249 114.274725 \n", "L 113.968764 114.371429 \n", "L 115.857816 114.520904 \n", "L 118.909375 114.754705 \n", "L 121.960942 114.980837 \n", "L 125.012502 115.199301 \n", "L 128.064069 115.410104 \n", "L 131.115628 115.613241 \n", "L 134.167188 115.80871 \n", "L 137.218755 115.996514 \n", "L 140.270314 116.176653 \n", "L 143.321874 116.349127 \n", "L 146.373441 116.513936 \n", "L 149.425 116.67108 \n", "L 152.476564 116.820556 \n", "L 155.528127 116.962367 \n", "L 158.57969 117.096516 \n", "L 161.631253 117.222997 \n", "L 164.682813 117.341809 \n", "L 167.734376 117.45296 \n", "L 169.542874 117.51429 \n", "L 170.785939 117.554485 \n", "L 173.837499 117.645846 \n", "L 176.889062 117.729898 \n", "L 179.940626 117.806642 \n", "L 182.992189 117.876077 \n", "L 186.04375 117.938204 \n", "L 189.095313 117.993023 \n", "L 192.146877 118.04053 \n", "L 195.198438 118.080731 \n", "L 198.250001 118.113621 \n", "L 201.301564 118.139202 \n", "L 204.353126 118.157475 \n", "L 207.404689 118.168436 \n", "L 210.456251 118.172092 \n", "L 213.507813 118.168436 \n", "L 216.559376 118.157475 \n", "L 219.610939 118.139202 \n", "L 222.662501 118.113621 \n", "L 225.714063 118.080731 \n", "L 228.765626 118.04053 \n", "L 231.817188 117.993023 \n", "L 234.868751 117.938204 \n", "L 237.920313 117.876077 \n", "\" clip-path=\"url(#p82fbb3c7c3)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_5\">\n", "    <path d=\"M 42.620312 118.080731 \n", "L 45.671872 118.479068 \n", "L 48.723432 118.870099 \n", "L 51.775006 119.253822 \n", "L 54.826565 119.630233 \n", "L 57.878125 119.999336 \n", "L 60.929685 120.36113 \n", "L 63.477901 120.657144 \n", "L 63.981244 120.713017 \n", "L 67.032818 121.044763 \n", "L 70.084378 121.369523 \n", "L 73.135938 121.687303 \n", "L 76.187512 121.998095 \n", "L 79.239071 122.301904 \n", "L 82.290631 122.59873 \n", "L 85.342191 122.888572 \n", "L 88.393757 123.17143 \n", "L 91.44531 123.447301 \n", "L 94.496869 123.716188 \n", "L 95.47338 123.799999 \n", "L 97.548436 123.970514 \n", "L 100.599996 124.214587 \n", "L 103.651563 124.451974 \n", "L 106.703122 124.682674 \n", "L 109.754682 124.906687 \n", "L 112.806249 125.124011 \n", "L 115.857816 125.334649 \n", "L 118.909375 125.538603 \n", "L 121.960942 125.735868 \n", "L 125.012502 125.926443 \n", "L 128.064069 126.110335 \n", "L 131.115628 126.28754 \n", "L 134.167188 126.458056 \n", "L 137.218755 126.621885 \n", "L 140.270314 126.779028 \n", "L 143.321874 126.929484 \n", "L 143.605786 126.94286 \n", "L 146.373441 127.067932 \n", "L 149.425 127.19942 \n", "L 152.476564 127.324491 \n", "L 155.528127 127.443149 \n", "L 158.57969 127.555396 \n", "L 161.631253 127.661227 \n", "L 164.682813 127.760641 \n", "L 167.734376 127.853645 \n", "L 170.785939 127.940235 \n", "L 173.837499 128.020409 \n", "L 176.889062 128.094169 \n", "L 179.940626 128.161516 \n", "L 182.992189 128.222449 \n", "L 186.04375 128.276968 \n", "L 189.095313 128.325074 \n", "L 192.146877 128.366764 \n", "L 195.198438 128.402043 \n", "L 198.250001 128.430905 \n", "L 201.301564 128.453354 \n", "L 204.353126 128.469389 \n", "L 207.404689 128.479008 \n", "L 210.456251 128.482217 \n", "L 213.507813 128.479008 \n", "L 216.559376 128.469389 \n", "L 219.610939 128.453354 \n", "L 222.662501 128.430905 \n", "L 225.714063 128.402043 \n", "L 228.765626 128.366764 \n", "L 231.817188 128.325074 \n", "L 234.868751 128.276968 \n", "L 237.920313 128.222449 \n", "\" clip-path=\"url(#p82fbb3c7c3)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_6\">\n", "    <path d=\"M 42.620312 128.402043 \n", "L 45.671872 128.751603 \n", "L 48.723432 129.094753 \n", "L 51.775006 129.431489 \n", "L 54.826565 129.761808 \n", "L 57.878125 130.085714 \n", "L 57.878125 130.085714 \n", "L 60.929685 130.390756 \n", "L 63.981244 130.689636 \n", "L 67.032818 130.982354 \n", "L 70.084378 131.268908 \n", "L 73.135938 131.549302 \n", "L 76.187512 131.823531 \n", "L 79.239071 132.091598 \n", "L 82.290631 132.353503 \n", "L 85.342191 132.609246 \n", "L 88.393757 132.858827 \n", "L 91.44531 133.102243 \n", "L 93.070112 133.228568 \n", "L 94.496869 133.335311 \n", "L 97.548436 133.557682 \n", "L 100.599996 133.774123 \n", "L 103.651563 133.984636 \n", "L 106.703122 134.189218 \n", "L 109.754682 134.387872 \n", "L 112.806249 134.580593 \n", "L 115.857816 134.767386 \n", "L 118.909375 134.948249 \n", "L 121.960942 135.123182 \n", "L 125.012502 135.292182 \n", "L 128.064069 135.455255 \n", "L 131.115628 135.612399 \n", "L 134.167188 135.763611 \n", "L 137.218755 135.908893 \n", "L 140.270314 136.048246 \n", "L 143.321874 136.18167 \n", "L 146.373441 136.309163 \n", "L 147.936485 136.37143 \n", "L 149.425 136.428571 \n", "L 152.476564 136.539999 \n", "L 155.528127 136.645712 \n", "L 158.57969 136.745715 \n", "L 161.631253 136.84 \n", "L 164.682813 136.92857 \n", "L 167.734376 137.011428 \n", "L 170.785939 137.088572 \n", "L 173.837499 137.159999 \n", "L 176.889062 137.225713 \n", "L 179.940626 137.285713 \n", "L 182.992189 137.339998 \n", "L 186.04375 137.38857 \n", "L 189.095313 137.431429 \n", "L 192.146877 137.46857 \n", "L 195.198438 137.500001 \n", "L 198.250001 137.525714 \n", "L 201.301564 137.545714 \n", "L 204.353126 137.56 \n", "L 207.404689 137.56857 \n", "L 210.456251 137.571428 \n", "L 213.507813 137.56857 \n", "L 216.559376 137.56 \n", "L 219.610939 137.545714 \n", "L 222.662501 137.525714 \n", "L 225.714063 137.500001 \n", "L 228.765626 137.46857 \n", "L 231.817188 137.431429 \n", "L 234.868751 137.38857 \n", "L 237.920313 137.339998 \n", "\" clip-path=\"url(#p82fbb3c7c3)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_7\">\n", "    <path d=\"M 42.620312 137.499998 \n", "L 45.671872 137.81143 \n", "L 48.723432 138.11714 \n", "L 51.775006 138.417144 \n", "L 54.826565 138.711432 \n", "L 57.878125 138.999997 \n", "L 60.929685 139.282857 \n", "L 63.477871 139.514284 \n", "L 63.981244 139.558396 \n", "L 67.032818 139.8203 \n", "L 70.084378 140.076692 \n", "L 73.135938 140.327568 \n", "L 76.187512 140.572933 \n", "L 79.239071 140.812783 \n", "L 82.290631 141.047119 \n", "L 85.342191 141.275939 \n", "L 88.393757 141.499247 \n", "L 91.44531 141.717041 \n", "L 94.496869 141.929322 \n", "L 97.548436 142.13609 \n", "L 100.599996 142.337343 \n", "L 103.651563 142.53308 \n", "L 105.641791 142.657146 \n", "L 106.703122 142.721064 \n", "L 109.754682 142.899513 \n", "L 112.806249 143.072638 \n", "L 115.857816 143.240434 \n", "L 118.909375 143.402906 \n", "L 121.960942 143.560049 \n", "L 125.012502 143.711863 \n", "L 128.064069 143.858352 \n", "L 131.115628 143.999513 \n", "L 134.167188 144.13535 \n", "L 137.218755 144.265858 \n", "L 140.270314 144.391042 \n", "L 143.321874 144.510897 \n", "L 146.373441 144.625423 \n", "L 149.425 144.734625 \n", "L 152.476564 144.838498 \n", "L 155.528127 144.937047 \n", "L 158.57969 145.030267 \n", "L 161.631253 145.118158 \n", "L 164.682813 145.200725 \n", "L 167.734376 145.277968 \n", "L 170.785939 145.349877 \n", "L 173.837499 145.416467 \n", "L 176.889062 145.477723 \n", "L 179.940626 145.533655 \n", "L 182.992189 145.584263 \n", "L 186.04375 145.629542 \n", "L 189.095313 145.669492 \n", "L 192.146877 145.704118 \n", "L 195.198438 145.733415 \n", "L 198.250001 145.757383 \n", "L 201.301564 145.776027 \n", "L 204.353126 145.789347 \n", "L 207.404689 145.797338 \n", "L 210.456251 145.8 \n", "\" clip-path=\"url(#p82fbb3c7c3)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 210.456251 145.8 \n", "L 213.507813 145.797338 \n", "L 216.559376 145.789347 \n", "L 219.610939 145.776027 \n", "L 222.662501 145.757383 \n", "L 225.714063 145.733415 \n", "L 228.765626 145.704118 \n", "L 231.817188 145.669492 \n", "L 234.868751 145.629542 \n", "L 237.**********.584263 \n", "\" clip-path=\"url(#p82fbb3c7c3)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_8\">\n", "    <path d=\"M 42.**********.733415 \n", "L 43.320206 145.8 \n", "\" clip-path=\"url(#p82fbb3c7c3)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_9\"/>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.8 \n", "L 42.620312 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.8 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 7.2 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p82fbb3c7c3\">\n", "   <rect x=\"42.620312\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "import torch\n", "from d2l import torch as d2l\n", "\n", "eta = 0.4\n", "def f_2d(x1, x2):\n", "    return 0.1 * x1 ** 2 + 2 * x2 ** 2\n", "def gd_2d(x1, x2, s1, s2):\n", "    return (x1 - eta * 0.2 * x1, x2 - eta * 4 * x2, 0, 0)\n", "\n", "d2l.show_trace_2d(f_2d, d2l.train_2d(gd_2d))"]}, {"cell_type": "markdown", "id": "380cd13c", "metadata": {"origin_pos": 4}, "source": ["By construction, the gradient in the $x_2$ direction is *much* higher and changes much more rapidly than in the horizontal $x_1$ direction. Thus we are stuck between two undesirable choices: if we pick a small learning rate we ensure that the solution does not diverge in the $x_2$ direction but we are saddled with slow convergence in the $x_1$ direction. Conversely, with a large learning rate we progress rapidly in the $x_1$ direction but diverge in $x_2$. The example below illustrates what happens even after a slight increase in learning rate from $0.4$ to $0.6$. Convergence in the $x_1$ direction improves but the overall solution quality is much worse.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "335281b1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:08.363841Z", "iopub.status.busy": "2023-08-18T19:45:08.362814Z", "iopub.status.idle": "2023-08-18T19:45:08.532368Z", "shell.execute_reply": "2023-08-18T19:45:08.531477Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 20, x1: -0.387814, x2: -1673.365109\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"264.207812pt\" height=\"183.35625pt\" viewBox=\"0 0 264.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:08.498642</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 264.**********.35625 \n", "L 264.207812 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 61.**********.8 \n", "L 257.**********.8 \n", "L 257.007812 7.2 \n", "L 61.707813 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mf89e274a42\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf89e274a42\" x=\"107.48125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(100.110156 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mf89e274a42\" x=\"168.5125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(161.141407 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mf89e274a42\" x=\"229.543751\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(226.362501 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- x1 -->\n", "     <g transform=\"translate(153.217188 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m8eff047007\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8eff047007\" x=\"61.707813\" y=\"109.923469\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −1000 -->\n", "      <g transform=\"translate(20.878125 113.722687) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"274.658203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m8eff047007\" x=\"61.707813\" y=\"66\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(48.345313 69.799219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m8eff047007\" x=\"61.707813\" y=\"22.076531\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(29.257812 25.87575) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- x2 -->\n", "     <g transform=\"translate(14.798438 82.640625) rotate(-90) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_7\">\n", "    <path d=\"M 76.965625 66.087847 \n", "L 95.275 65.877014 \n", "L 111.38725 66.17218 \n", "L 125.56603 65.758948 \n", "L 138.043357 66.337473 \n", "L 149.023404 65.527538 \n", "L 158.685846 66.661447 \n", "L 167.188794 65.073975 \n", "L 174.671389 67.296435 \n", "L 181.256072 64.18499 \n", "L 187.050594 68.541014 \n", "L 192.149773 62.442581 \n", "L 196.63705 70.980387 \n", "L 200.585854 59.027459 \n", "L 204.060802 75.761558 \n", "L 207.118755 52.333819 \n", "L 209.809755 85.132653 \n", "L 212.177834 39.214286 \n", "L 214.261744 103.5 \n", "L 216.095585 13.5 \n", "L 217.709365 139.5 \n", "\" clip-path=\"url(#p295cd96f25)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m88cc60fb71\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p295cd96f25)\">\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"76.965625\" y=\"66.087847\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"95.275\" y=\"65.877014\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"111.38725\" y=\"66.17218\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"125.56603\" y=\"65.758948\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"138.043357\" y=\"66.337473\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"149.023404\" y=\"65.527538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"158.685846\" y=\"66.661447\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"167.188794\" y=\"65.073975\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"174.671389\" y=\"67.296435\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"181.256072\" y=\"64.18499\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"187.050594\" y=\"68.541014\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"192.149773\" y=\"62.442581\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"196.63705\" y=\"70.980387\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"200.585854\" y=\"59.027459\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"204.060802\" y=\"75.761558\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"207.118755\" y=\"52.333819\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"209.809755\" y=\"85.132653\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"212.177834\" y=\"39.214286\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"214.261744\" y=\"103.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"216.095585\" y=\"13.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m88cc60fb71\" x=\"217.709365\" y=\"139.5\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"PathCollection_1\"/>\n", "   <g id=\"PathCollection_2\">\n", "    <path d=\"M 116.188368 65.960469 \n", "L 113.584369 65.961296 \n", "L 110.53281 65.96229 \n", "L 107.481257 65.963311 \n", "L 104.429691 65.964357 \n", "L 102.995823 65.964861 \n", "L 101.378131 65.965505 \n", "L 98.326571 65.96675 \n", "L 95.275012 65.968024 \n", "L 92.394877 65.969254 \n", "L 92.223438 65.969338 \n", "L 89.171878 65.970875 \n", "L 86.120318 65.972446 \n", "L 83.839671 65.973646 \n", "L 83.068744 65.974125 \n", "L 80.017185 65.976062 \n", "L 76.965625 65.978038 \n", "L 76.965625 65.978038 \n", "L 73.914065 65.980503 \n", "L 71.57354 65.982431 \n", "L 70.862506 65.983184 \n", "L 67.810932 65.986478 \n", "L 67.497229 65.986823 \n", "L 64.759372 65.99104 \n", "L 64.64739 65.991215 \n", "L 62.967633 65.995608 \n", "L 62.407714 66 \n", "L 62.967633 66.004392 \n", "L 64.64739 66.008785 \n", "L 64.759372 66.00896 \n", "L 67.497229 66.013177 \n", "L 67.810932 66.013522 \n", "L 70.862506 66.016816 \n", "L 71.573542 66.017569 \n", "L 73.914065 66.019497 \n", "L 76.965625 66.021962 \n", "L 76.965625 66.021962 \n", "L 80.017185 66.023938 \n", "L 83.068744 66.025875 \n", "L 83.839671 66.026354 \n", "L 86.120318 66.027554 \n", "L 89.171878 66.029125 \n", "L 92.223438 66.030662 \n", "L 92.394877 66.030746 \n", "L 95.275012 66.031976 \n", "L 98.326571 66.03325 \n", "L 101.378131 66.034495 \n", "L 102.995819 66.035139 \n", "L 104.429691 66.035643 \n", "L 107.481257 66.036689 \n", "L 110.53281 66.03771 \n", "L 113.584369 66.038704 \n", "L 116.188368 66.039531 \n", "L 116.635936 66.039658 \n", "L 119.687496 66.040502 \n", "L 122.739063 66.041323 \n", "L 125.790622 66.04212 \n", "L 128.842182 66.042895 \n", "L 131.893749 66.043646 \n", "L 133.056255 66.043923 \n", "L 134.945316 66.044331 \n", "L 137.996875 66.044969 \n", "L 141.048442 66.045586 \n", "L 144.100002 66.046182 \n", "L 147.151569 66.046758 \n", "L 150.203128 66.047312 \n", "L 153.254688 66.047845 \n", "L 156.057151 66.048316 \n", "L 156.306255 66.048354 \n", "L 159.357814 66.048803 \n", "L 162.409374 66.049232 \n", "L 165.460941 66.049643 \n", "L 168.5125 66.050035 \n", "L 171.564064 66.050407 \n", "L 174.615627 66.05076 \n", "L 177.66719 66.051094 \n", "L 180.718753 66.05141 \n", "L 183.770313 66.051706 \n", "L 186.821876 66.051982 \n", "L 189.873439 66.05224 \n", "L 192.924999 66.052479 \n", "L 195.976562 66.052699 \n", "L 196.121829 66.052708 \n", "L 199.028126 66.052884 \n", "L 202.079689 66.053051 \n", "L 205.13125 66.0532 \n", "L 208.182813 66.053332 \n", "L 211.234377 66.053446 \n", "L 214.285938 66.053543 \n", "L 217.337501 66.053622 \n", "L 220.389064 66.053683 \n", "L 223.440626 66.053727 \n", "L 226.492189 66.053754 \n", "L 229.543751 66.053762 \n", "L 232.595313 66.053754 \n", "L 235.646876 66.053727 \n", "L 238.698439 66.053683 \n", "L 241.750001 66.053622 \n", "L 244.801563 66.053543 \n", "L 247.853126 66.053446 \n", "L 250.904688 66.053332 \n", "L 253.956251 66.0532 \n", "L 257.007812 66.053051 \n", "\" clip-path=\"url(#p295cd96f25)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_3\">\n", "    <path d=\"M 61.707812 66.053543 \n", "L 64.759372 66.0545 \n", "L 67.810932 66.05544 \n", "L 70.862506 66.056363 \n", "L 73.351142 66.057101 \n", "L 73.914065 66.057255 \n", "L 76.965625 66.058077 \n", "L 80.017185 66.058882 \n", "L 83.068744 66.059671 \n", "L 86.120318 66.060444 \n", "L 89.171878 66.0612 \n", "L 90.37908 66.061493 \n", "L 92.223438 66.061909 \n", "L 95.275012 66.062583 \n", "L 98.326571 66.063242 \n", "L 101.378131 66.063886 \n", "L 104.429691 66.064514 \n", "L 107.481257 66.065128 \n", "L 110.53281 66.065726 \n", "L 111.365053 66.065885 \n", "L 113.584369 66.066282 \n", "L 116.635936 66.066813 \n", "L 119.687496 66.06733 \n", "L 122.739063 66.067833 \n", "L 125.790622 66.068322 \n", "L 128.842182 66.068797 \n", "L 131.893749 66.069257 \n", "L 134.945316 66.069704 \n", "L 137.996875 66.070136 \n", "L 139.031328 66.070278 \n", "L 141.048442 66.070537 \n", "L 144.100002 66.070916 \n", "L 147.151569 66.071282 \n", "L 150.203128 66.071635 \n", "L 153.254688 66.071975 \n", "L 156.306255 66.072301 \n", "L 159.357814 66.072613 \n", "L 162.409374 66.072913 \n", "L 165.460941 66.073199 \n", "L 168.5125 66.073472 \n", "L 171.564064 66.073732 \n", "L 174.615627 66.073978 \n", "L 177.66719 66.074211 \n", "L 180.718753 66.07443 \n", "L 183.770313 66.074637 \n", "L 184.29646 66.07467 \n", "L 186.821876 66.07482 \n", "L 189.873439 66.07499 \n", "L 192.924999 66.075147 \n", "L 195.976562 66.075291 \n", "L 199.028126 66.075423 \n", "L 202.079689 66.075542 \n", "L 205.13125 66.075649 \n", "L 208.182813 66.075743 \n", "L 211.234377 66.075824 \n", "L 214.285938 66.075893 \n", "L 217.337501 66.07595 \n", "L 220.389064 66.075994 \n", "L 223.440626 66.076025 \n", "L 226.492189 66.076044 \n", "L 229.543751 66.07605 \n", "L 232.595313 66.076044 \n", "L 235.646876 66.076025 \n", "L 238.698439 66.075994 \n", "L 241.750001 66.07595 \n", "L 244.801563 66.075893 \n", "L 247.853126 66.075824 \n", "L 250.904688 66.075743 \n", "L 253.956251 66.075649 \n", "L 257.007812 66.075542 \n", "\" clip-path=\"url(#p295cd96f25)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_4\">\n", "    <path d=\"M 61.707812 66.075893 \n", "L 64.759372 66.076577 \n", "L 67.810932 66.077249 \n", "L 70.862506 66.077908 \n", "L 73.914065 66.078554 \n", "L 76.36137 66.079062 \n", "L 76.965625 66.079181 \n", "L 80.017185 66.079769 \n", "L 83.068744 66.080344 \n", "L 86.120318 66.080908 \n", "L 89.171878 66.08146 \n", "L 92.223438 66.082 \n", "L 95.275012 66.082529 \n", "L 98.326571 66.083045 \n", "L 100.803727 66.083455 \n", "L 101.378131 66.083545 \n", "L 104.429691 66.084012 \n", "L 107.481257 66.084468 \n", "L 110.53281 66.084913 \n", "L 113.584369 66.085347 \n", "L 116.635936 66.085769 \n", "L 119.687496 66.08618 \n", "L 122.739063 66.08658 \n", "L 125.790622 66.086968 \n", "L 128.842182 66.087346 \n", "L 131.893749 66.087712 \n", "L 133.056264 66.087847 \n", "L 134.945316 66.088056 \n", "L 137.996875 66.088383 \n", "L 141.048442 66.088699 \n", "L 144.100002 66.089004 \n", "L 147.151569 66.089299 \n", "L 150.203128 66.089582 \n", "L 153.254688 66.089856 \n", "L 156.306255 66.090118 \n", "L 159.357814 66.09037 \n", "L 162.409374 66.090611 \n", "L 165.460941 66.090841 \n", "L 168.5125 66.091061 \n", "L 171.564064 66.09127 \n", "L 174.615627 66.091468 \n", "L 177.66719 66.091655 \n", "L 180.718753 66.091832 \n", "L 183.770313 66.091998 \n", "L 186.821876 66.092154 \n", "L 188.630374 66.092239 \n", "L 189.873439 66.092295 \n", "L 192.924999 66.092423 \n", "L 195.976562 66.092541 \n", "L 199.028126 66.092648 \n", "L 202.079689 66.092745 \n", "L 205.13125 66.092832 \n", "L 208.182813 66.092908 \n", "L 211.234377 66.092975 \n", "L 214.285938 66.093031 \n", "L 217.337501 66.093077 \n", "L 220.389064 66.093113 \n", "L 223.440626 66.093138 \n", "L 226.492189 66.093154 \n", "L 229.543751 66.093159 \n", "L 232.595313 66.093154 \n", "L 235.646876 66.093138 \n", "L 238.698439 66.093113 \n", "L 241.750001 66.093077 \n", "L 244.801563 66.093031 \n", "L 247.853126 66.092975 \n", "L 250.904688 66.092908 \n", "L 253.956251 66.092832 \n", "L 257.007812 66.092745 \n", "\" clip-path=\"url(#p295cd96f25)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_5\">\n", "    <path d=\"M 61.707812 66.093031 \n", "L 64.759372 66.093588 \n", "L 67.810932 66.094134 \n", "L 70.862506 66.09467 \n", "L 73.914065 66.095196 \n", "L 76.965625 66.095712 \n", "L 80.017185 66.096218 \n", "L 82.565401 66.096632 \n", "L 83.068744 66.09671 \n", "L 86.120318 66.097173 \n", "L 89.171878 66.097627 \n", "L 92.223438 66.098071 \n", "L 95.275012 66.098506 \n", "L 98.326571 66.09893 \n", "L 101.378131 66.099345 \n", "L 104.429691 66.09975 \n", "L 107.481257 66.100146 \n", "L 110.53281 66.100531 \n", "L 113.584369 66.100907 \n", "L 114.56088 66.101024 \n", "L 116.635936 66.101262 \n", "L 119.687496 66.101603 \n", "L 122.739063 66.101935 \n", "L 125.790622 66.102258 \n", "L 128.842182 66.102571 \n", "L 131.893749 66.102874 \n", "L 134.945316 66.103169 \n", "L 137.996875 66.103454 \n", "L 141.048442 66.103729 \n", "L 144.100002 66.103996 \n", "L 147.151569 66.104253 \n", "L 150.203128 66.1045 \n", "L 153.254688 66.104739 \n", "L 156.306255 66.104968 \n", "L 159.357814 66.105187 \n", "L 162.409374 66.105398 \n", "L 162.693286 66.105416 \n", "L 165.460941 66.105591 \n", "L 168.5125 66.105775 \n", "L 171.564064 66.10595 \n", "L 174.615627 66.106116 \n", "L 177.66719 66.106272 \n", "L 180.718753 66.10642 \n", "L 183.770313 66.106559 \n", "L 186.821876 66.106689 \n", "L 189.873439 66.10681 \n", "L 192.924999 66.106922 \n", "L 195.976562 66.107025 \n", "L 199.028126 66.107119 \n", "L 202.079689 66.107205 \n", "L 205.13125 66.107281 \n", "L 208.182813 66.107348 \n", "L 211.234377 66.107406 \n", "L 214.285938 66.107456 \n", "L 217.337501 66.107496 \n", "L 220.389064 66.107527 \n", "L 223.440626 66.10755 \n", "L 226.492189 66.107563 \n", "L 229.543751 66.107568 \n", "L 232.595313 66.107563 \n", "L 235.646876 66.10755 \n", "L 238.698439 66.107527 \n", "L 241.750001 66.107496 \n", "L 244.801563 66.107456 \n", "L 247.853126 66.107406 \n", "L 250.904688 66.107348 \n", "L 253.956251 66.107281 \n", "L 257.007812 66.107205 \n", "\" clip-path=\"url(#p295cd96f25)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_6\">\n", "    <path d=\"M 61.707812 66.107456 \n", "L 64.759372 66.107944 \n", "L 67.810932 66.108424 \n", "L 70.862506 66.108894 \n", "L 73.914065 66.109356 \n", "L 76.965625 66.109809 \n", "L 76.965625 66.109809 \n", "L 80.017185 66.110235 \n", "L 83.068744 66.110653 \n", "L 86.120318 66.111062 \n", "L 89.171878 66.111462 \n", "L 92.223438 66.111854 \n", "L 95.275012 66.112237 \n", "L 98.326571 66.112612 \n", "L 101.378131 66.112978 \n", "L 104.429691 66.113335 \n", "L 107.481257 66.113684 \n", "L 110.53281 66.114024 \n", "L 112.157612 66.114201 \n", "L 113.584369 66.11435 \n", "L 116.635936 66.114661 \n", "L 119.687496 66.114963 \n", "L 122.739063 66.115258 \n", "L 125.790622 66.115544 \n", "L 128.842182 66.115821 \n", "L 131.893749 66.116091 \n", "L 134.945316 66.116352 \n", "L 137.996875 66.116604 \n", "L 141.048442 66.116849 \n", "L 144.100002 66.117085 \n", "L 147.151569 66.117313 \n", "L 150.203128 66.117533 \n", "L 153.254688 66.117744 \n", "L 156.306255 66.117947 \n", "L 159.357814 66.118142 \n", "L 162.409374 66.118328 \n", "L 165.460941 66.118506 \n", "L 167.023985 66.118593 \n", "L 168.5125 66.118673 \n", "L 171.564064 66.118829 \n", "L 174.615627 66.118977 \n", "L 177.66719 66.119116 \n", "L 180.718753 66.119248 \n", "L 183.770313 66.119372 \n", "L 186.821876 66.119488 \n", "L 189.873439 66.119596 \n", "L 192.924999 66.119695 \n", "L 195.976562 66.119787 \n", "L 199.028126 66.119871 \n", "L 202.079689 66.119947 \n", "L 205.13125 66.120015 \n", "L 208.182813 66.120075 \n", "L 211.234377 66.120127 \n", "L 214.285938 66.120171 \n", "L 217.337501 66.120207 \n", "L 220.389064 66.120235 \n", "L 223.440626 66.120254 \n", "L 226.492189 66.120266 \n", "L 229.543751 66.12027 \n", "L 232.595313 66.120266 \n", "L 235.646876 66.120254 \n", "L 238.698439 66.120235 \n", "L 241.750001 66.120207 \n", "L 244.801563 66.120171 \n", "L 247.853126 66.120127 \n", "L 250.904688 66.120075 \n", "L 253.956251 66.120015 \n", "L 257.007812 66.119947 \n", "\" clip-path=\"url(#p295cd96f25)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_7\">\n", "    <path d=\"M 61.707812 66.120171 \n", "L 64.759372 66.120606 \n", "L 67.810932 66.121033 \n", "L 70.862506 66.121452 \n", "L 73.914065 66.121864 \n", "L 76.965625 66.122267 \n", "L 80.017185 66.122662 \n", "L 82.565371 66.122986 \n", "L 83.068744 66.123047 \n", "L 86.120318 66.123413 \n", "L 89.171878 66.123772 \n", "L 92.223438 66.124122 \n", "L 95.275012 66.124465 \n", "L 98.326571 66.1248 \n", "L 101.378131 66.125128 \n", "L 104.429691 66.125448 \n", "L 107.481257 66.12576 \n", "L 110.53281 66.126064 \n", "L 113.584369 66.126361 \n", "L 116.635936 66.12665 \n", "L 119.687496 66.126931 \n", "L 122.739063 66.127205 \n", "L 124.729291 66.127378 \n", "L 125.790622 66.127467 \n", "L 128.842182 66.127717 \n", "L 131.893749 66.127959 \n", "L 134.945316 66.128193 \n", "L 137.996875 66.12842 \n", "L 141.048442 66.12864 \n", "L 144.100002 66.128852 \n", "L 147.151569 66.129057 \n", "L 150.203128 66.129254 \n", "L 153.254688 66.129444 \n", "L 156.306255 66.129626 \n", "L 159.357814 66.129801 \n", "L 162.409374 66.129969 \n", "L 165.460941 66.130129 \n", "L 168.5125 66.130281 \n", "L 171.564064 66.130427 \n", "L 174.615627 66.130564 \n", "L 177.66719 66.130695 \n", "L 180.718753 66.130817 \n", "L 183.770313 66.130933 \n", "L 186.821876 66.131041 \n", "L 189.873439 66.131141 \n", "L 192.924999 66.131234 \n", "L 195.976562 66.13132 \n", "L 199.028126 66.131398 \n", "L 202.079689 66.131469 \n", "L 205.13125 66.131532 \n", "L 208.182813 66.131588 \n", "L 211.234377 66.131636 \n", "L 214.285938 66.131677 \n", "L 217.337501 66.131711 \n", "L 220.389064 66.131737 \n", "L 223.440626 66.131756 \n", "L 226.492189 66.131767 \n", "L 229.543751 66.13177 \n", "\" clip-path=\"url(#p295cd96f25)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 229.543751 66.13177 \n", "L 232.595313 66.131767 \n", "L 235.646876 66.131756 \n", "L 238.698439 66.131737 \n", "L 241.750001 66.131711 \n", "L 244.801563 66.131677 \n", "L 247.853126 66.131636 \n", "L 250.904688 66.131588 \n", "L 253.956251 66.131532 \n", "L 257.007812 66.131469 \n", "\" clip-path=\"url(#p295cd96f25)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_8\">\n", "    <path d=\"M 61.707812 66.131677 \n", "L 62.407706 66.13177 \n", "\" clip-path=\"url(#p295cd96f25)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_9\"/>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 61.**********.8 \n", "L 61.707813 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 257.**********.8 \n", "L 257.007812 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 61.707812 145.8 \n", "L 257.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 61.707812 7.2 \n", "L 257.007812 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p295cd96f25\">\n", "   <rect x=\"61.707813\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["eta = 0.6\n", "d2l.show_trace_2d(f_2d, d2l.train_2d(gd_2d))"]}, {"cell_type": "markdown", "id": "0a70adbf", "metadata": {"origin_pos": 6}, "source": ["### The Momentum Method\n", "\n", "The momentum method allows us to solve the gradient descent problem described\n", "above. Looking at the optimization trace above we might intuit that averaging gradients over the past would work well. After all, in the $x_1$ direction this will aggregate well-aligned gradients, thus increasing the distance we cover with every step. Conversely, in the $x_2$ direction where gradients oscillate, an aggregate gradient will reduce step size due to oscillations that cancel each other out.\n", "Using $\\mathbf{v}_t$ instead of the gradient $\\mathbf{g}_t$ yields the following update equations:\n", "\n", "$$\n", "\\begin{aligned}\n", "\\mathbf{v}_t &\\leftarrow \\beta \\mathbf{v}_{t-1} + \\mathbf{g}_{t, t-1}, \\\\\n", "\\mathbf{x}_t &\\leftarrow \\mathbf{x}_{t-1} - \\eta_t \\mathbf{v}_t.\n", "\\end{aligned}\n", "$$\n", "\n", "Note that for $\\beta = 0$ we recover regular gradient descent. Before delving deeper into the mathematical properties let's have a quick look at how the algorithm behaves in practice.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "0590e23d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:08.538390Z", "iopub.status.busy": "2023-08-18T19:45:08.537792Z", "iopub.status.idle": "2023-08-18T19:45:08.709045Z", "shell.execute_reply": "2023-08-18T19:45:08.707851Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 20, x1: 0.007188, x2: 0.002553\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"245.120313pt\" height=\"183.35625pt\" viewBox=\"0 0 245.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:08.674866</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 245.**********.35625 \n", "L 245.120313 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "L 237.920313 7.2 \n", "L 42.620312 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mb71a66c8d5\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb71a66c8d5\" x=\"88.39375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(81.022656 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mb71a66c8d5\" x=\"149.425\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(142.053907 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mb71a66c8d5\" x=\"210.456251\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(207.275001 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- x1 -->\n", "     <g transform=\"translate(134.129687 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m722a692f38\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m722a692f38\" x=\"42.620312\" y=\"123.041379\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(20.878125 126.840598) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m722a692f38\" x=\"42.620312\" y=\"77.524138\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 81.323357) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m722a692f38\" x=\"42.620312\" y=\"32.006897\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(29.257812 35.806115) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- x2 -->\n", "     <g transform=\"translate(14.798437 82.640625) rotate(-90) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_7\">\n", "    <path d=\"M 57.878125 123.041379 \n", "L 76.1875 13.8 \n", "L 101.454438 112.117241 \n", "L 127.168124 78.252414 \n", "L 150.019542 59.572138 \n", "L 168.697657 93.3168 \n", "L 183.047745 72.286742 \n", "L 193.51181 74.341463 \n", "L 200.777175 83.007243 \n", "L 205.571347 74.180681 \n", "L 208.554621 77.791697 \n", "L 210.274454 78.955064 \n", "L 211.156186 76.102525 \n", "L 211.51306 78.088126 \n", "L 211.564679 77.727355 \n", "L 211.457478 77.059249 \n", "L 211.28373 77.84093 \n", "L 211.097558 77.47147 \n", "L 210.927516 77.413143 \n", "L 210.785942 77.650367 \n", "L 210.675593 77.466029 \n", "\" clip-path=\"url(#p71ef5c3f4b)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"mb9e62ded42\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p71ef5c3f4b)\">\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"57.878125\" y=\"123.041379\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"76.1875\" y=\"13.8\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"101.454438\" y=\"112.117241\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"127.168124\" y=\"78.252414\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"150.019542\" y=\"59.572138\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"168.697657\" y=\"93.3168\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"183.047745\" y=\"72.286742\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"193.51181\" y=\"74.341463\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"200.777175\" y=\"83.007243\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"205.571347\" y=\"74.180681\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"208.554621\" y=\"77.791697\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"210.274454\" y=\"78.955064\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"211.156186\" y=\"76.102525\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"211.51306\" y=\"78.088126\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"211.564679\" y=\"77.727355\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"211.457478\" y=\"77.059249\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"211.28373\" y=\"77.84093\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"211.097558\" y=\"77.47147\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"210.927516\" y=\"77.413143\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"210.785942\" y=\"77.650367\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb9e62ded42\" x=\"210.675593\" y=\"77.466029\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"PathCollection_1\"/>\n", "   <g id=\"PathCollection_2\">\n", "    <path d=\"M 97.100868 57.04138 \n", "L 94.496869 57.469778 \n", "L 91.44531 57.985193 \n", "L 88.393757 58.513995 \n", "L 85.342191 59.056186 \n", "L 83.908323 59.317241 \n", "L 82.290631 59.651033 \n", "L 79.239071 60.295861 \n", "L 76.187512 60.95586 \n", "L 73.307377 61.593104 \n", "L 73.135938 61.636871 \n", "L 70.084378 62.433421 \n", "L 67.032818 63.247479 \n", "L 64.752171 63.868965 \n", "L 63.981244 64.117243 \n", "L 60.929685 65.12069 \n", "L 57.878125 66.144828 \n", "L 57.878125 66.144828 \n", "L 54.826565 67.421836 \n", "L 52.48604 68.42069 \n", "L 51.775006 68.810833 \n", "L 48.723432 70.517739 \n", "L 48.409729 70.696551 \n", "L 45.671872 72.881381 \n", "L 45.55989 72.972414 \n", "L 43.880133 75.248276 \n", "L 43.320214 77.524138 \n", "L 43.880133 79.800001 \n", "L 45.55989 82.075862 \n", "L 45.671872 82.166896 \n", "L 48.409729 84.351724 \n", "L 48.723432 84.530537 \n", "L 51.775006 86.237444 \n", "L 52.486042 86.627587 \n", "L 54.826565 87.626438 \n", "L 57.878125 88.903448 \n", "L 57.878125 88.903448 \n", "L 60.929685 89.927586 \n", "L 63.981244 90.931033 \n", "L 64.752171 91.179311 \n", "L 67.032818 91.800797 \n", "L 70.084378 92.614854 \n", "L 73.135938 93.411405 \n", "L 73.307377 93.455172 \n", "L 76.187512 94.092416 \n", "L 79.239071 94.752415 \n", "L 82.290631 95.397243 \n", "L 83.908319 95.731033 \n", "L 85.342191 95.992089 \n", "L 88.393757 96.534281 \n", "L 91.44531 97.063082 \n", "L 94.496869 97.578498 \n", "L 97.100868 98.006896 \n", "L 97.548436 98.072777 \n", "L 100.599996 98.509982 \n", "L 103.651563 98.935209 \n", "L 106.703122 99.348457 \n", "L 109.754682 99.749726 \n", "L 112.806249 100.139019 \n", "L 113.968755 100.282759 \n", "L 115.857816 100.494089 \n", "L 118.909375 100.82463 \n", "L 121.960942 101.144336 \n", "L 125.012502 101.453202 \n", "L 128.064069 101.751232 \n", "L 131.115628 102.038424 \n", "L 134.167188 102.314778 \n", "L 136.969651 102.558621 \n", "L 137.218755 102.578411 \n", "L 140.270314 102.810944 \n", "L 143.321874 103.033584 \n", "L 146.373441 103.246327 \n", "L 149.425 103.449175 \n", "L 152.476564 103.642129 \n", "L 155.528127 103.825188 \n", "L 158.57969 103.998351 \n", "L 161.631253 104.161619 \n", "L 164.682813 104.314993 \n", "L 167.734376 104.45847 \n", "L 170.785939 104.592055 \n", "L 173.837499 104.715742 \n", "L 176.889062 104.829535 \n", "L 177.034329 104.834481 \n", "L 179.940626 104.925518 \n", "L 182.992189 105.012 \n", "L 186.04375 105.08938 \n", "L 189.095313 105.157655 \n", "L 192.146877 105.216828 \n", "L 195.198438 105.266896 \n", "L 198.250001 105.307862 \n", "L 201.301564 105.339724 \n", "L 204.353126 105.362483 \n", "L 207.404689 105.376138 \n", "L 210.456251 105.38069 \n", "L 213.507813 105.376138 \n", "L 216.559376 105.362483 \n", "L 219.610939 105.339724 \n", "L 222.662501 105.307862 \n", "L 225.714063 105.266896 \n", "L 228.765626 105.216828 \n", "L 231.817188 105.157655 \n", "L 234.868751 105.08938 \n", "L 237.920313 105.012 \n", "\" clip-path=\"url(#p71ef5c3f4b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_3\">\n", "    <path d=\"M 42.620312 105.266896 \n", "L 45.671872 105.763035 \n", "L 48.723432 106.250067 \n", "L 51.775006 106.728 \n", "L 54.263642 107.110344 \n", "L 54.826565 107.190423 \n", "L 57.878125 107.616092 \n", "L 60.929685 108.033333 \n", "L 63.981244 108.442145 \n", "L 67.032818 108.842529 \n", "L 70.084378 109.234483 \n", "L 71.29158 109.386206 \n", "L 73.135938 109.602022 \n", "L 76.187512 109.95125 \n", "L 79.239071 110.292629 \n", "L 82.290631 110.62616 \n", "L 85.342191 110.951844 \n", "L 88.393757 111.26968 \n", "L 91.44531 111.579667 \n", "L 92.277553 111.662069 \n", "L 94.496869 111.86763 \n", "L 97.548436 112.142937 \n", "L 100.599996 112.410901 \n", "L 103.651563 112.671524 \n", "L 106.703122 112.924804 \n", "L 109.754682 113.170744 \n", "L 112.806249 113.409342 \n", "L 115.857816 113.6406 \n", "L 118.909375 113.864515 \n", "L 119.943828 113.937932 \n", "L 121.960942 114.072413 \n", "L 125.012502 114.268965 \n", "L 128.064069 114.45862 \n", "L 131.115628 114.641379 \n", "L 134.167188 114.817241 \n", "L 137.218755 114.986208 \n", "L 140.270314 115.148276 \n", "L 143.321874 115.303447 \n", "L 146.373441 115.451724 \n", "L 149.425 115.593103 \n", "L 152.476564 115.727586 \n", "L 155.528127 115.855173 \n", "L 158.57969 115.975862 \n", "L 161.631253 116.089655 \n", "L 164.682813 116.196552 \n", "L 165.20896 116.213794 \n", "L 167.734376 116.291823 \n", "L 170.785939 116.379606 \n", "L 173.837499 116.460887 \n", "L 176.889062 116.535666 \n", "L 179.940626 116.603942 \n", "L 182.992189 116.665715 \n", "L 186.04375 116.720985 \n", "L 189.095313 116.769755 \n", "L 192.146877 116.812021 \n", "L 195.198438 116.847783 \n", "L 198.250001 116.877045 \n", "L 201.301564 116.899804 \n", "L 204.353126 116.916059 \n", "L 207.404689 116.925813 \n", "L 210.456251 116.929064 \n", "L 213.507813 116.925813 \n", "L 216.559376 116.916059 \n", "L 219.610939 116.899804 \n", "L 222.662501 116.877045 \n", "L 225.714063 116.847783 \n", "L 228.765626 116.812021 \n", "L 231.817188 116.769755 \n", "L 234.868751 116.720985 \n", "L 237.920313 116.665715 \n", "\" clip-path=\"url(#p71ef5c3f4b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_4\">\n", "    <path d=\"M 42.620312 116.847783 \n", "L 45.671872 117.202167 \n", "L 48.723432 117.550049 \n", "L 51.775006 117.89143 \n", "L 54.826565 118.226306 \n", "L 57.27387 118.489657 \n", "L 57.878125 118.551165 \n", "L 60.929685 118.855639 \n", "L 63.981244 119.153961 \n", "L 67.032818 119.446133 \n", "L 70.084378 119.732153 \n", "L 73.135938 120.012022 \n", "L 76.187512 120.285742 \n", "L 79.239071 120.55331 \n", "L 81.716227 120.765519 \n", "L 82.290631 120.812203 \n", "L 85.342191 121.054376 \n", "L 88.393757 121.290717 \n", "L 91.44531 121.521219 \n", "L 94.496869 121.745889 \n", "L 97.548436 121.964721 \n", "L 100.599996 122.177718 \n", "L 103.651563 122.38488 \n", "L 106.703122 122.586207 \n", "L 109.754682 122.781698 \n", "L 112.806249 122.971352 \n", "L 113.968764 123.041379 \n", "L 115.857816 123.14962 \n", "L 118.909375 123.318924 \n", "L 121.960942 123.482675 \n", "L 125.012502 123.640873 \n", "L 128.064069 123.793523 \n", "L 131.115628 123.940623 \n", "L 134.167188 124.082169 \n", "L 137.218755 124.218165 \n", "L 140.270314 124.348611 \n", "L 143.321874 124.473506 \n", "L 146.373441 124.59285 \n", "L 149.425 124.706644 \n", "L 152.476564 124.814885 \n", "L 155.528127 124.917576 \n", "L 158.57969 125.014718 \n", "L 161.631253 125.106308 \n", "L 164.682813 125.192344 \n", "L 167.734376 125.272833 \n", "L 169.542874 125.317245 \n", "L 170.785939 125.346351 \n", "L 173.837499 125.412509 \n", "L 176.889062 125.473374 \n", "L 179.940626 125.528948 \n", "L 182.992189 125.579228 \n", "L 186.04375 125.624217 \n", "L 189.095313 125.663913 \n", "L 192.146877 125.698315 \n", "L 195.198438 125.727426 \n", "L 198.250001 125.751243 \n", "L 201.301564 125.769767 \n", "L 204.353126 125.782999 \n", "L 207.404689 125.790936 \n", "L 210.456251 125.793584 \n", "L 213.507813 125.790936 \n", "L 216.559376 125.782999 \n", "L 219.610939 125.769767 \n", "L 222.662501 125.751243 \n", "L 225.714063 125.727426 \n", "L 228.765626 125.698315 \n", "L 231.817188 125.663913 \n", "L 234.868751 125.624217 \n", "L 237.920313 125.579228 \n", "\" clip-path=\"url(#p71ef5c3f4b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_5\">\n", "    <path d=\"M 42.620312 125.727426 \n", "L 45.671872 126.015877 \n", "L 48.723432 126.299037 \n", "L 51.775006 126.576906 \n", "L 54.826565 126.849479 \n", "L 57.878125 127.11676 \n", "L 60.929685 127.378749 \n", "L 63.477901 127.593105 \n", "L 63.981244 127.633564 \n", "L 67.032818 127.873794 \n", "L 70.084378 128.108965 \n", "L 73.135938 128.339081 \n", "L 76.187512 128.564138 \n", "L 79.239071 128.784138 \n", "L 82.290631 128.99908 \n", "L 85.342191 129.208966 \n", "L 88.393757 129.413794 \n", "L 91.44531 129.613563 \n", "L 94.496869 129.808274 \n", "L 95.47338 129.868964 \n", "L 97.548436 129.992441 \n", "L 100.599996 130.169184 \n", "L 103.651563 130.341084 \n", "L 106.703122 130.508143 \n", "L 109.754682 130.67036 \n", "L 112.806249 130.827732 \n", "L 115.857816 130.980263 \n", "L 118.909375 131.127954 \n", "L 121.960942 131.270801 \n", "L 125.012502 131.408804 \n", "L 128.064069 131.541967 \n", "L 131.115628 131.670288 \n", "L 134.167188 131.793765 \n", "L 137.218755 131.912399 \n", "L 140.270314 132.026192 \n", "L 143.321874 132.135144 \n", "L 143.605786 132.14483 \n", "L 146.373441 132.235399 \n", "L 149.425 132.330614 \n", "L 152.476564 132.421183 \n", "L 155.528127 132.507108 \n", "L 158.57969 132.58839 \n", "L 161.631253 132.665026 \n", "L 164.682813 132.737016 \n", "L 167.734376 132.804364 \n", "L 170.785939 132.867067 \n", "L 173.837499 132.925124 \n", "L 176.889062 132.978536 \n", "L 179.940626 133.027305 \n", "L 182.992189 133.071428 \n", "L 186.04375 133.110908 \n", "L 189.095313 133.145744 \n", "L 192.146877 133.175932 \n", "L 195.198438 133.201479 \n", "L 198.250001 133.22238 \n", "L 201.301564 133.238636 \n", "L 204.353126 133.250248 \n", "L 207.404689 133.257213 \n", "L 210.456251 133.259536 \n", "L 213.507813 133.257213 \n", "L 216.559376 133.250248 \n", "L 219.610939 133.238636 \n", "L 222.662501 133.22238 \n", "L 225.714063 133.201479 \n", "L 228.765626 133.175932 \n", "L 231.817188 133.145744 \n", "L 234.868751 133.110908 \n", "L 237.920313 133.071428 \n", "\" clip-path=\"url(#p71ef5c3f4b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_6\">\n", "    <path d=\"M 42.620312 133.201479 \n", "L 45.671872 133.454609 \n", "L 48.723432 133.703097 \n", "L 51.775006 133.94694 \n", "L 54.826565 134.186137 \n", "L 57.878125 134.42069 \n", "L 57.878125 134.42069 \n", "L 60.929685 134.641582 \n", "L 63.981244 134.858013 \n", "L 67.032818 135.069981 \n", "L 70.084378 135.277485 \n", "L 73.135938 135.480529 \n", "L 76.187512 135.679108 \n", "L 79.239071 135.873226 \n", "L 82.290631 136.062881 \n", "L 85.342191 136.248074 \n", "L 88.393757 136.428805 \n", "L 91.44531 136.605072 \n", "L 93.070112 136.69655 \n", "L 94.496869 136.773846 \n", "L 97.548436 136.934873 \n", "L 100.599996 137.091607 \n", "L 103.651563 137.244047 \n", "L 106.703122 137.392193 \n", "L 109.754682 137.536045 \n", "L 112.806249 137.675602 \n", "L 115.857816 137.810865 \n", "L 118.909375 137.941836 \n", "L 121.960942 138.068511 \n", "L 125.012502 138.19089 \n", "L 128.064069 138.308978 \n", "L 131.115628 138.422772 \n", "L 134.167188 138.53227 \n", "L 137.218755 138.637475 \n", "L 140.270314 138.738385 \n", "L 143.321874 138.835002 \n", "L 146.373441 138.927325 \n", "L 147.936485 138.972415 \n", "L 149.425 139.013793 \n", "L 152.476564 139.094482 \n", "L 155.528127 139.171033 \n", "L 158.57969 139.243449 \n", "L 161.631253 139.311724 \n", "L 164.682813 139.375861 \n", "L 167.734376 139.435861 \n", "L 170.785939 139.491724 \n", "L 173.837499 139.543448 \n", "L 176.889062 139.591033 \n", "L 179.940626 139.634482 \n", "L 182.992189 139.673792 \n", "L 186.04375 139.708965 \n", "L 189.095313 139.74 \n", "L 192.146877 139.766896 \n", "L 195.198438 139.789656 \n", "L 198.250001 139.808276 \n", "L 201.301564 139.822759 \n", "L 204.353126 139.833104 \n", "L 207.404689 139.839309 \n", "L 210.456251 139.841379 \n", "L 213.507813 139.839309 \n", "L 216.559376 139.833104 \n", "L 219.610939 139.822759 \n", "L 222.662501 139.808276 \n", "L 225.714063 139.789656 \n", "L 228.765626 139.766896 \n", "L 231.817188 139.74 \n", "L 234.868751 139.708965 \n", "L 237.920313 139.673792 \n", "\" clip-path=\"url(#p71ef5c3f4b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_7\">\n", "    <path d=\"M 42.620312 139.789654 \n", "L 45.671872 140.015173 \n", "L 48.723432 140.23655 \n", "L 51.775006 140.453794 \n", "L 54.826565 140.666899 \n", "L 57.878125 140.87586 \n", "L 60.929685 141.08069 \n", "L 63.477871 141.248275 \n", "L 63.981244 141.280218 \n", "L 67.032818 141.469872 \n", "L 70.084378 141.655535 \n", "L 73.135938 141.837204 \n", "L 76.187512 142.014883 \n", "L 79.239071 142.188567 \n", "L 82.290631 142.358258 \n", "L 85.342191 142.523956 \n", "L 88.393757 142.685662 \n", "L 91.44531 142.843375 \n", "L 94.496869 142.997095 \n", "L 97.548436 143.146824 \n", "L 100.599996 143.292559 \n", "L 103.651563 143.4343 \n", "L 105.641791 143.52414 \n", "L 106.703122 143.570425 \n", "L 109.754682 143.699647 \n", "L 112.806249 143.825013 \n", "L 115.857816 143.946521 \n", "L 118.909375 144.064173 \n", "L 121.960942 144.177966 \n", "L 125.012502 144.2879 \n", "L 128.064069 144.393979 \n", "L 131.115628 144.496199 \n", "L 134.167188 144.594564 \n", "L 137.218755 144.68907 \n", "L 140.270314 144.77972 \n", "L 143.321874 144.866512 \n", "L 146.373441 144.949444 \n", "L 149.425 145.028522 \n", "L 152.476564 145.10374 \n", "L 155.528127 145.175103 \n", "L 158.57969 145.242607 \n", "L 161.631253 145.306252 \n", "L 164.682813 145.366042 \n", "L 167.734376 145.421977 \n", "L 170.785939 145.474049 \n", "L 173.837499 145.522269 \n", "L 176.889062 145.566627 \n", "L 179.940626 145.60713 \n", "L 182.992189 145.643777 \n", "L 186.04375 145.676565 \n", "L 189.095313 145.705494 \n", "L 192.146877 145.730568 \n", "L 195.198438 145.751783 \n", "L 198.250001 145.769139 \n", "L 201.301564 145.78264 \n", "L 204.353126 145.792286 \n", "L 207.404689 145.798072 \n", "L 210.456251 145.8 \n", "\" clip-path=\"url(#p71ef5c3f4b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 210.456251 145.8 \n", "L 213.507813 145.798072 \n", "L 216.559376 145.792286 \n", "L 219.610939 145.78264 \n", "L 222.662501 145.769139 \n", "L 225.714063 145.751783 \n", "L 228.765626 145.730568 \n", "L 231.817188 145.705494 \n", "L 234.868751 145.676565 \n", "L 237.**********.643777 \n", "\" clip-path=\"url(#p71ef5c3f4b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_8\">\n", "    <path d=\"M 42.**********.751783 \n", "L 43.320206 145.8 \n", "\" clip-path=\"url(#p71ef5c3f4b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_9\"/>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.8 \n", "L 42.620312 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.8 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 7.2 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p71ef5c3f4b\">\n", "   <rect x=\"42.620312\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def momentum_2d(x1, x2, v1, v2):\n", "    v1 = beta * v1 + 0.2 * x1\n", "    v2 = beta * v2 + 4 * x2\n", "    return x1 - eta * v1, x2 - eta * v2, v1, v2\n", "\n", "eta, beta = 0.6, 0.5\n", "d2l.show_trace_2d(f_2d, d2l.train_2d(momentum_2d))"]}, {"cell_type": "markdown", "id": "24e6b17e", "metadata": {"origin_pos": 8}, "source": ["As we can see, even with the same learning rate that we used before, momentum still converges well. Let's see what happens when we decrease the momentum parameter. Halving it to $\\beta = 0.25$ leads to a trajectory that barely converges at all. Nonetheless, it is a lot better than without momentum (when the solution diverges).\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6793baa9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:08.714805Z", "iopub.status.busy": "2023-08-18T19:45:08.713835Z", "iopub.status.idle": "2023-08-18T19:45:08.885652Z", "shell.execute_reply": "2023-08-18T19:45:08.884783Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 20, x1: -0.126340, x2: -0.186632\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"245.120313pt\" height=\"183.35625pt\" viewBox=\"0 0 245.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:08.852094</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 245.**********.35625 \n", "L 245.120313 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "L 237.920313 7.2 \n", "L 42.620312 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m5f03d7ac7a\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5f03d7ac7a\" x=\"88.39375\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(81.022656 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m5f03d7ac7a\" x=\"149.425\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(142.053907 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m5f03d7ac7a\" x=\"210.456251\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(207.275001 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- x1 -->\n", "     <g transform=\"translate(134.129687 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m86fbf94b7f\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m86fbf94b7f\" x=\"42.620312\" y=\"123.041379\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(20.878125 126.840598) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m86fbf94b7f\" x=\"42.620312\" y=\"77.524138\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(29.257812 81.323357) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m86fbf94b7f\" x=\"42.620312\" y=\"32.006897\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(29.257812 35.806115) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- x2 -->\n", "     <g transform=\"translate(14.798437 82.640625) rotate(-90) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_7\">\n", "    <path d=\"M 57.878125 123.041379 \n", "L 76.1875 13.8 \n", "L 96.877094 139.427586 \n", "L 115.678991 22.266207 \n", "L 131.752737 125.594897 \n", "L 145.215595 36.057248 \n", "L 156.410188 113.193371 \n", "L 165.694364 46.871242 \n", "L 173.386834 103.85766 \n", "L 179.758282 54.903812 \n", "L 185.0349 96.954133 \n", "L 189.404616 60.834725 \n", "L 193.023242 91.859464 \n", "L 196.019859 65.210867 \n", "L 198.50138 88.100569 \n", "L 200.556345 68.439561 \n", "L 202.258075 85.327294 \n", "L 203.667289 70.821652 \n", "L 204.834267 83.281207 \n", "L 205.80065 72.57913 \n", "L 206.600918 81.77163 \n", "\" clip-path=\"url(#p533604f081)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"mb99172b101\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p533604f081)\">\n", "     <use xlink:href=\"#mb99172b101\" x=\"57.878125\" y=\"123.041379\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"76.1875\" y=\"13.8\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"96.877094\" y=\"139.427586\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"115.678991\" y=\"22.266207\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"131.752737\" y=\"125.594897\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"145.215595\" y=\"36.057248\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"156.410188\" y=\"113.193371\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"165.694364\" y=\"46.871242\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"173.386834\" y=\"103.85766\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"179.758282\" y=\"54.903812\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"185.0349\" y=\"96.954133\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"189.404616\" y=\"60.834725\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"193.023242\" y=\"91.859464\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"196.019859\" y=\"65.210867\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"198.50138\" y=\"88.100569\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"200.556345\" y=\"68.439561\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"202.258075\" y=\"85.327294\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"203.667289\" y=\"70.821652\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"204.834267\" y=\"83.281207\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"205.80065\" y=\"72.57913\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#mb99172b101\" x=\"206.600918\" y=\"81.77163\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"PathCollection_1\"/>\n", "   <g id=\"PathCollection_2\">\n", "    <path d=\"M 97.100868 57.04138 \n", "L 94.496869 57.469778 \n", "L 91.44531 57.985193 \n", "L 88.393757 58.513995 \n", "L 85.342191 59.056186 \n", "L 83.908323 59.317241 \n", "L 82.290631 59.651033 \n", "L 79.239071 60.295861 \n", "L 76.187512 60.95586 \n", "L 73.307377 61.593104 \n", "L 73.135938 61.636871 \n", "L 70.084378 62.433421 \n", "L 67.032818 63.247479 \n", "L 64.752171 63.868965 \n", "L 63.981244 64.117243 \n", "L 60.929685 65.12069 \n", "L 57.878125 66.144828 \n", "L 57.878125 66.144828 \n", "L 54.826565 67.421836 \n", "L 52.48604 68.42069 \n", "L 51.775006 68.810833 \n", "L 48.723432 70.517739 \n", "L 48.409729 70.696551 \n", "L 45.671872 72.881381 \n", "L 45.55989 72.972414 \n", "L 43.880133 75.248276 \n", "L 43.320214 77.524138 \n", "L 43.880133 79.800001 \n", "L 45.55989 82.075862 \n", "L 45.671872 82.166896 \n", "L 48.409729 84.351724 \n", "L 48.723432 84.530537 \n", "L 51.775006 86.237444 \n", "L 52.486042 86.627587 \n", "L 54.826565 87.626438 \n", "L 57.878125 88.903448 \n", "L 57.878125 88.903448 \n", "L 60.929685 89.927586 \n", "L 63.981244 90.931033 \n", "L 64.752171 91.179311 \n", "L 67.032818 91.800797 \n", "L 70.084378 92.614854 \n", "L 73.135938 93.411405 \n", "L 73.307377 93.455172 \n", "L 76.187512 94.092416 \n", "L 79.239071 94.752415 \n", "L 82.290631 95.397243 \n", "L 83.908319 95.731033 \n", "L 85.342191 95.992089 \n", "L 88.393757 96.534281 \n", "L 91.44531 97.063082 \n", "L 94.496869 97.578498 \n", "L 97.100868 98.006896 \n", "L 97.548436 98.072777 \n", "L 100.599996 98.509982 \n", "L 103.651563 98.935209 \n", "L 106.703122 99.348457 \n", "L 109.754682 99.749726 \n", "L 112.806249 100.139019 \n", "L 113.968755 100.282759 \n", "L 115.857816 100.494089 \n", "L 118.909375 100.82463 \n", "L 121.960942 101.144336 \n", "L 125.012502 101.453202 \n", "L 128.064069 101.751232 \n", "L 131.115628 102.038424 \n", "L 134.167188 102.314778 \n", "L 136.969651 102.558621 \n", "L 137.218755 102.578411 \n", "L 140.270314 102.810944 \n", "L 143.321874 103.033584 \n", "L 146.373441 103.246327 \n", "L 149.425 103.449175 \n", "L 152.476564 103.642129 \n", "L 155.528127 103.825188 \n", "L 158.57969 103.998351 \n", "L 161.631253 104.161619 \n", "L 164.682813 104.314993 \n", "L 167.734376 104.45847 \n", "L 170.785939 104.592055 \n", "L 173.837499 104.715742 \n", "L 176.889062 104.829535 \n", "L 177.034329 104.834481 \n", "L 179.940626 104.925518 \n", "L 182.992189 105.012 \n", "L 186.04375 105.08938 \n", "L 189.095313 105.157655 \n", "L 192.146877 105.216828 \n", "L 195.198438 105.266896 \n", "L 198.250001 105.307862 \n", "L 201.301564 105.339724 \n", "L 204.353126 105.362483 \n", "L 207.404689 105.376138 \n", "L 210.456251 105.38069 \n", "L 213.507813 105.376138 \n", "L 216.559376 105.362483 \n", "L 219.610939 105.339724 \n", "L 222.662501 105.307862 \n", "L 225.714063 105.266896 \n", "L 228.765626 105.216828 \n", "L 231.817188 105.157655 \n", "L 234.868751 105.08938 \n", "L 237.920313 105.012 \n", "\" clip-path=\"url(#p533604f081)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_3\">\n", "    <path d=\"M 42.620312 105.266896 \n", "L 45.671872 105.763035 \n", "L 48.723432 106.250067 \n", "L 51.775006 106.728 \n", "L 54.263642 107.110344 \n", "L 54.826565 107.190423 \n", "L 57.878125 107.616092 \n", "L 60.929685 108.033333 \n", "L 63.981244 108.442145 \n", "L 67.032818 108.842529 \n", "L 70.084378 109.234483 \n", "L 71.29158 109.386206 \n", "L 73.135938 109.602022 \n", "L 76.187512 109.95125 \n", "L 79.239071 110.292629 \n", "L 82.290631 110.62616 \n", "L 85.342191 110.951844 \n", "L 88.393757 111.26968 \n", "L 91.44531 111.579667 \n", "L 92.277553 111.662069 \n", "L 94.496869 111.86763 \n", "L 97.548436 112.142937 \n", "L 100.599996 112.410901 \n", "L 103.651563 112.671524 \n", "L 106.703122 112.924804 \n", "L 109.754682 113.170744 \n", "L 112.806249 113.409342 \n", "L 115.857816 113.6406 \n", "L 118.909375 113.864515 \n", "L 119.943828 113.937932 \n", "L 121.960942 114.072413 \n", "L 125.012502 114.268965 \n", "L 128.064069 114.45862 \n", "L 131.115628 114.641379 \n", "L 134.167188 114.817241 \n", "L 137.218755 114.986208 \n", "L 140.270314 115.148276 \n", "L 143.321874 115.303447 \n", "L 146.373441 115.451724 \n", "L 149.425 115.593103 \n", "L 152.476564 115.727586 \n", "L 155.528127 115.855173 \n", "L 158.57969 115.975862 \n", "L 161.631253 116.089655 \n", "L 164.682813 116.196552 \n", "L 165.20896 116.213794 \n", "L 167.734376 116.291823 \n", "L 170.785939 116.379606 \n", "L 173.837499 116.460887 \n", "L 176.889062 116.535666 \n", "L 179.940626 116.603942 \n", "L 182.992189 116.665715 \n", "L 186.04375 116.720985 \n", "L 189.095313 116.769755 \n", "L 192.146877 116.812021 \n", "L 195.198438 116.847783 \n", "L 198.250001 116.877045 \n", "L 201.301564 116.899804 \n", "L 204.353126 116.916059 \n", "L 207.404689 116.925813 \n", "L 210.456251 116.929064 \n", "L 213.507813 116.925813 \n", "L 216.559376 116.916059 \n", "L 219.610939 116.899804 \n", "L 222.662501 116.877045 \n", "L 225.714063 116.847783 \n", "L 228.765626 116.812021 \n", "L 231.817188 116.769755 \n", "L 234.868751 116.720985 \n", "L 237.920313 116.665715 \n", "\" clip-path=\"url(#p533604f081)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_4\">\n", "    <path d=\"M 42.620312 116.847783 \n", "L 45.671872 117.202167 \n", "L 48.723432 117.550049 \n", "L 51.775006 117.89143 \n", "L 54.826565 118.226306 \n", "L 57.27387 118.489657 \n", "L 57.878125 118.551165 \n", "L 60.929685 118.855639 \n", "L 63.981244 119.153961 \n", "L 67.032818 119.446133 \n", "L 70.084378 119.732153 \n", "L 73.135938 120.012022 \n", "L 76.187512 120.285742 \n", "L 79.239071 120.55331 \n", "L 81.716227 120.765519 \n", "L 82.290631 120.812203 \n", "L 85.342191 121.054376 \n", "L 88.393757 121.290717 \n", "L 91.44531 121.521219 \n", "L 94.496869 121.745889 \n", "L 97.548436 121.964721 \n", "L 100.599996 122.177718 \n", "L 103.651563 122.38488 \n", "L 106.703122 122.586207 \n", "L 109.754682 122.781698 \n", "L 112.806249 122.971352 \n", "L 113.968764 123.041379 \n", "L 115.857816 123.14962 \n", "L 118.909375 123.318924 \n", "L 121.960942 123.482675 \n", "L 125.012502 123.640873 \n", "L 128.064069 123.793523 \n", "L 131.115628 123.940623 \n", "L 134.167188 124.082169 \n", "L 137.218755 124.218165 \n", "L 140.270314 124.348611 \n", "L 143.321874 124.473506 \n", "L 146.373441 124.59285 \n", "L 149.425 124.706644 \n", "L 152.476564 124.814885 \n", "L 155.528127 124.917576 \n", "L 158.57969 125.014718 \n", "L 161.631253 125.106308 \n", "L 164.682813 125.192344 \n", "L 167.734376 125.272833 \n", "L 169.542874 125.317245 \n", "L 170.785939 125.346351 \n", "L 173.837499 125.412509 \n", "L 176.889062 125.473374 \n", "L 179.940626 125.528948 \n", "L 182.992189 125.579228 \n", "L 186.04375 125.624217 \n", "L 189.095313 125.663913 \n", "L 192.146877 125.698315 \n", "L 195.198438 125.727426 \n", "L 198.250001 125.751243 \n", "L 201.301564 125.769767 \n", "L 204.353126 125.782999 \n", "L 207.404689 125.790936 \n", "L 210.456251 125.793584 \n", "L 213.507813 125.790936 \n", "L 216.559376 125.782999 \n", "L 219.610939 125.769767 \n", "L 222.662501 125.751243 \n", "L 225.714063 125.727426 \n", "L 228.765626 125.698315 \n", "L 231.817188 125.663913 \n", "L 234.868751 125.624217 \n", "L 237.920313 125.579228 \n", "\" clip-path=\"url(#p533604f081)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_5\">\n", "    <path d=\"M 42.620312 125.727426 \n", "L 45.671872 126.015877 \n", "L 48.723432 126.299037 \n", "L 51.775006 126.576906 \n", "L 54.826565 126.849479 \n", "L 57.878125 127.11676 \n", "L 60.929685 127.378749 \n", "L 63.477901 127.593105 \n", "L 63.981244 127.633564 \n", "L 67.032818 127.873794 \n", "L 70.084378 128.108965 \n", "L 73.135938 128.339081 \n", "L 76.187512 128.564138 \n", "L 79.239071 128.784138 \n", "L 82.290631 128.99908 \n", "L 85.342191 129.208966 \n", "L 88.393757 129.413794 \n", "L 91.44531 129.613563 \n", "L 94.496869 129.808274 \n", "L 95.47338 129.868964 \n", "L 97.548436 129.992441 \n", "L 100.599996 130.169184 \n", "L 103.651563 130.341084 \n", "L 106.703122 130.508143 \n", "L 109.754682 130.67036 \n", "L 112.806249 130.827732 \n", "L 115.857816 130.980263 \n", "L 118.909375 131.127954 \n", "L 121.960942 131.270801 \n", "L 125.012502 131.408804 \n", "L 128.064069 131.541967 \n", "L 131.115628 131.670288 \n", "L 134.167188 131.793765 \n", "L 137.218755 131.912399 \n", "L 140.270314 132.026192 \n", "L 143.321874 132.135144 \n", "L 143.605786 132.14483 \n", "L 146.373441 132.235399 \n", "L 149.425 132.330614 \n", "L 152.476564 132.421183 \n", "L 155.528127 132.507108 \n", "L 158.57969 132.58839 \n", "L 161.631253 132.665026 \n", "L 164.682813 132.737016 \n", "L 167.734376 132.804364 \n", "L 170.785939 132.867067 \n", "L 173.837499 132.925124 \n", "L 176.889062 132.978536 \n", "L 179.940626 133.027305 \n", "L 182.992189 133.071428 \n", "L 186.04375 133.110908 \n", "L 189.095313 133.145744 \n", "L 192.146877 133.175932 \n", "L 195.198438 133.201479 \n", "L 198.250001 133.22238 \n", "L 201.301564 133.238636 \n", "L 204.353126 133.250248 \n", "L 207.404689 133.257213 \n", "L 210.456251 133.259536 \n", "L 213.507813 133.257213 \n", "L 216.559376 133.250248 \n", "L 219.610939 133.238636 \n", "L 222.662501 133.22238 \n", "L 225.714063 133.201479 \n", "L 228.765626 133.175932 \n", "L 231.817188 133.145744 \n", "L 234.868751 133.110908 \n", "L 237.920313 133.071428 \n", "\" clip-path=\"url(#p533604f081)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_6\">\n", "    <path d=\"M 42.620312 133.201479 \n", "L 45.671872 133.454609 \n", "L 48.723432 133.703097 \n", "L 51.775006 133.94694 \n", "L 54.826565 134.186137 \n", "L 57.878125 134.42069 \n", "L 57.878125 134.42069 \n", "L 60.929685 134.641582 \n", "L 63.981244 134.858013 \n", "L 67.032818 135.069981 \n", "L 70.084378 135.277485 \n", "L 73.135938 135.480529 \n", "L 76.187512 135.679108 \n", "L 79.239071 135.873226 \n", "L 82.290631 136.062881 \n", "L 85.342191 136.248074 \n", "L 88.393757 136.428805 \n", "L 91.44531 136.605072 \n", "L 93.070112 136.69655 \n", "L 94.496869 136.773846 \n", "L 97.548436 136.934873 \n", "L 100.599996 137.091607 \n", "L 103.651563 137.244047 \n", "L 106.703122 137.392193 \n", "L 109.754682 137.536045 \n", "L 112.806249 137.675602 \n", "L 115.857816 137.810865 \n", "L 118.909375 137.941836 \n", "L 121.960942 138.068511 \n", "L 125.012502 138.19089 \n", "L 128.064069 138.308978 \n", "L 131.115628 138.422772 \n", "L 134.167188 138.53227 \n", "L 137.218755 138.637475 \n", "L 140.270314 138.738385 \n", "L 143.321874 138.835002 \n", "L 146.373441 138.927325 \n", "L 147.936485 138.972415 \n", "L 149.425 139.013793 \n", "L 152.476564 139.094482 \n", "L 155.528127 139.171033 \n", "L 158.57969 139.243449 \n", "L 161.631253 139.311724 \n", "L 164.682813 139.375861 \n", "L 167.734376 139.435861 \n", "L 170.785939 139.491724 \n", "L 173.837499 139.543448 \n", "L 176.889062 139.591033 \n", "L 179.940626 139.634482 \n", "L 182.992189 139.673792 \n", "L 186.04375 139.708965 \n", "L 189.095313 139.74 \n", "L 192.146877 139.766896 \n", "L 195.198438 139.789656 \n", "L 198.250001 139.808276 \n", "L 201.301564 139.822759 \n", "L 204.353126 139.833104 \n", "L 207.404689 139.839309 \n", "L 210.456251 139.841379 \n", "L 213.507813 139.839309 \n", "L 216.559376 139.833104 \n", "L 219.610939 139.822759 \n", "L 222.662501 139.808276 \n", "L 225.714063 139.789656 \n", "L 228.765626 139.766896 \n", "L 231.817188 139.74 \n", "L 234.868751 139.708965 \n", "L 237.920313 139.673792 \n", "\" clip-path=\"url(#p533604f081)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_7\">\n", "    <path d=\"M 42.620312 139.789654 \n", "L 45.671872 140.015173 \n", "L 48.723432 140.23655 \n", "L 51.775006 140.453794 \n", "L 54.826565 140.666899 \n", "L 57.878125 140.87586 \n", "L 60.929685 141.08069 \n", "L 63.477871 141.248275 \n", "L 63.981244 141.280218 \n", "L 67.032818 141.469872 \n", "L 70.084378 141.655535 \n", "L 73.135938 141.837204 \n", "L 76.187512 142.014883 \n", "L 79.239071 142.188567 \n", "L 82.290631 142.358258 \n", "L 85.342191 142.523956 \n", "L 88.393757 142.685662 \n", "L 91.44531 142.843375 \n", "L 94.496869 142.997095 \n", "L 97.548436 143.146824 \n", "L 100.599996 143.292559 \n", "L 103.651563 143.4343 \n", "L 105.641791 143.52414 \n", "L 106.703122 143.570425 \n", "L 109.754682 143.699647 \n", "L 112.806249 143.825013 \n", "L 115.857816 143.946521 \n", "L 118.909375 144.064173 \n", "L 121.960942 144.177966 \n", "L 125.012502 144.2879 \n", "L 128.064069 144.393979 \n", "L 131.115628 144.496199 \n", "L 134.167188 144.594564 \n", "L 137.218755 144.68907 \n", "L 140.270314 144.77972 \n", "L 143.321874 144.866512 \n", "L 146.373441 144.949444 \n", "L 149.425 145.028522 \n", "L 152.476564 145.10374 \n", "L 155.528127 145.175103 \n", "L 158.57969 145.242607 \n", "L 161.631253 145.306252 \n", "L 164.682813 145.366042 \n", "L 167.734376 145.421977 \n", "L 170.785939 145.474049 \n", "L 173.837499 145.522269 \n", "L 176.889062 145.566627 \n", "L 179.940626 145.60713 \n", "L 182.992189 145.643777 \n", "L 186.04375 145.676565 \n", "L 189.095313 145.705494 \n", "L 192.146877 145.730568 \n", "L 195.198438 145.751783 \n", "L 198.250001 145.769139 \n", "L 201.301564 145.78264 \n", "L 204.353126 145.792286 \n", "L 207.404689 145.798072 \n", "L 210.456251 145.8 \n", "\" clip-path=\"url(#p533604f081)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 210.456251 145.8 \n", "L 213.507813 145.798072 \n", "L 216.559376 145.792286 \n", "L 219.610939 145.78264 \n", "L 222.662501 145.769139 \n", "L 225.714063 145.751783 \n", "L 228.765626 145.730568 \n", "L 231.817188 145.705494 \n", "L 234.868751 145.676565 \n", "L 237.**********.643777 \n", "\" clip-path=\"url(#p533604f081)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_8\">\n", "    <path d=\"M 42.**********.751783 \n", "L 43.320206 145.8 \n", "\" clip-path=\"url(#p533604f081)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"PathCollection_9\"/>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.**********.8 \n", "L 42.620312 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 237.**********.8 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 42.**********.8 \n", "L 237.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 42.620312 7.2 \n", "L 237.920313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p533604f081\">\n", "   <rect x=\"42.620312\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["eta, beta = 0.6, 0.25\n", "d2l.show_trace_2d(f_2d, d2l.train_2d(momentum_2d))"]}, {"cell_type": "markdown", "id": "5438e8db", "metadata": {"origin_pos": 10}, "source": ["Note that we can combine momentum with stochastic gradient descent and in particular, minibatch stochastic gradient descent. The only change is that in that case we replace the gradients $\\mathbf{g}_{t, t-1}$ with $\\mathbf{g}_t$. Last, for convenience we initialize $\\mathbf{v}_0 = 0$ at time $t=0$. Let's look at what leaky averaging actually does to the updates.\n", "\n", "### Effective Sample Weight\n", "\n", "Recall that $\\mathbf{v}_t = \\sum_{\\tau = 0}^{t-1} \\beta^{\\tau} \\mathbf{g}_{t-\\tau, t-\\tau-1}$. In the limit the terms add up to $\\sum_{\\tau=0}^\\infty \\beta^\\tau = \\frac{1}{1-\\beta}$. In other words, rather than taking a step of size $\\eta$ in gradient descent or stochastic gradient descent we take a step of size $\\frac{\\eta}{1-\\beta}$ while at the same time, dealing with a potentially much better behaved descent direction. These are two benefits in one. To illustrate how weighting behaves for different choices of $\\beta$ consider the diagram below.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "acefb6aa", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:08.891483Z", "iopub.status.busy": "2023-08-18T19:45:08.890980Z", "iopub.status.idle": "2023-08-18T19:45:09.270074Z", "shell.execute_reply": "2023-08-18T19:45:09.269168Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"234.6408pt\" height=\"183.35625pt\" viewBox=\"0 0 234.6408 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:09.213398</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 234.6408 183.35625 \n", "L 234.6408 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m6bb7f042ca\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m6bb7f042ca\" x=\"38.980398\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(35.799148 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m6bb7f042ca\" x=\"84.504873\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(78.142373 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m6bb7f042ca\" x=\"130.029349\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(123.666849 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m6bb7f042ca\" x=\"175.553824\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(169.191324 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m6bb7f042ca\" x=\"221.0783\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(214.7158 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- time -->\n", "     <g transform=\"translate(116.457031 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m21c5c5c860\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m21c5c5c860\" x=\"30.103125\" y=\"139.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 143.299219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m21c5c5c860\" x=\"30.103125\" y=\"114.3\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 118.099219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m21c5c5c860\" x=\"30.103125\" y=\"89.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 92.899219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m21c5c5c860\" x=\"30.103125\" y=\"63.9\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 67.699219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m21c5c5c860\" x=\"30.103125\" y=\"38.7\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.499219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m21c5c5c860\" x=\"30.103125\" y=\"13.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 17.299219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 38.980398 13.5 \n", "L 43.532845 19.8 \n", "L 48.085293 25.785 \n", "L 52.63774 31.47075 \n", "L 57.190188 36.872213 \n", "L 61.742635 42.003602 \n", "L 66.295083 46.878422 \n", "L 70.847531 51.509501 \n", "L 75.399978 55.909026 \n", "L 79.952426 60.088574 \n", "L 84.504873 64.059146 \n", "L 89.057321 67.831188 \n", "L 93.609768 71.414629 \n", "L 98.162216 74.818898 \n", "L 102.714663 78.052953 \n", "L 107.267111 81.125305 \n", "L 111.819559 84.04404 \n", "L 116.372006 86.816838 \n", "L 120.924454 89.450996 \n", "L 125.476901 91.953446 \n", "L 130.029349 94.330774 \n", "L 134.581796 96.589235 \n", "L 139.134244 98.734773 \n", "L 143.686691 100.773035 \n", "L 148.239139 102.709383 \n", "L 152.791587 104.548914 \n", "L 157.344034 106.296468 \n", "L 161.896482 107.956645 \n", "L 166.448929 109.533812 \n", "L 171.001377 111.032122 \n", "L 175.553824 112.455516 \n", "L 180.106272 113.80774 \n", "L 184.658719 115.092353 \n", "L 189.211167 116.312735 \n", "L 193.763615 117.472099 \n", "L 198.316062 118.573494 \n", "L 202.86851 119.619819 \n", "L 207.420957 120.613828 \n", "L 211.973405 121.558137 \n", "L 216.525852 122.45523 \n", "\" clip-path=\"url(#pdeecc3d0a7)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 38.980398 13.5 \n", "L 43.532845 26.1 \n", "L 48.085293 37.44 \n", "L 52.63774 47.646 \n", "L 57.190188 56.8314 \n", "L 61.742635 65.09826 \n", "L 66.295083 72.538434 \n", "L 70.847531 79.234591 \n", "L 75.399978 85.261132 \n", "L 79.952426 90.685018 \n", "L 84.504873 95.566517 \n", "L 89.057321 99.959865 \n", "L 93.609768 103.913878 \n", "L 98.162216 107.472491 \n", "L 102.714663 110.675242 \n", "L 107.267111 113.557717 \n", "L 111.819559 116.151946 \n", "L 116.372006 118.486751 \n", "L 120.924454 120.588076 \n", "L 125.476901 122.479268 \n", "L 130.029349 124.181342 \n", "L 134.581796 125.713207 \n", "L 139.134244 127.091887 \n", "L 143.686691 128.332698 \n", "L 148.239139 129.449428 \n", "L 152.791587 130.454485 \n", "L 157.344034 131.359037 \n", "L 161.896482 132.173133 \n", "L 166.448929 132.90582 \n", "L 171.001377 133.565238 \n", "L 175.553824 134.158714 \n", "L 180.106272 134.692843 \n", "L 184.658719 135.173558 \n", "L 189.211167 135.606203 \n", "L 193.763615 135.995582 \n", "L 198.316062 136.346024 \n", "L 202.86851 136.661422 \n", "L 207.420957 136.945279 \n", "L 211.973405 137.200752 \n", "L 216.525852 137.430676 \n", "\" clip-path=\"url(#pdeecc3d0a7)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 38.980398 13.5 \n", "L 43.532845 63.9 \n", "L 48.085293 94.14 \n", "L 52.63774 112.284 \n", "L 57.190188 123.1704 \n", "L 61.742635 129.70224 \n", "L 66.295083 133.621344 \n", "L 70.847531 135.972806 \n", "L 75.399978 137.383684 \n", "L 79.952426 138.23021 \n", "L 84.504873 138.738126 \n", "L 89.057321 139.042876 \n", "L 93.609768 139.225725 \n", "L 98.162216 139.335435 \n", "L 102.714663 139.401261 \n", "L 107.267111 139.440757 \n", "L 111.819559 139.464454 \n", "L 116.372006 139.478672 \n", "L 120.924454 139.487203 \n", "L 125.476901 139.492322 \n", "L 130.029349 139.495393 \n", "L 134.581796 139.497236 \n", "L 139.134244 139.498342 \n", "L 143.686691 139.499005 \n", "L 148.239139 139.499403 \n", "L 152.791587 139.499642 \n", "L 157.344034 139.499785 \n", "L 161.896482 139.499871 \n", "L 166.448929 139.499923 \n", "L 171.001377 139.499954 \n", "L 175.553824 139.499972 \n", "L 180.106272 139.499983 \n", "L 184.658719 139.49999 \n", "L 189.211167 139.499994 \n", "L 193.763615 139.499996 \n", "L 198.316062 139.499998 \n", "L 202.86851 139.499999 \n", "L 207.420957 139.499999 \n", "L 211.973405 139.5 \n", "L 216.525852 139.5 \n", "\" clip-path=\"url(#pdeecc3d0a7)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 38.980398 13.5 \n", "L 43.532845 139.5 \n", "L 48.085293 139.5 \n", "L 52.63774 139.5 \n", "L 57.190188 139.5 \n", "L 61.742635 139.5 \n", "L 66.295083 139.5 \n", "L 70.847531 139.5 \n", "L 75.399978 139.5 \n", "L 79.952426 139.5 \n", "L 84.504873 139.5 \n", "L 89.057321 139.5 \n", "L 93.609768 139.5 \n", "L 98.162216 139.5 \n", "L 102.714663 139.5 \n", "L 107.267111 139.5 \n", "L 111.819559 139.5 \n", "L 116.372006 139.5 \n", "L 120.924454 139.5 \n", "L 125.476901 139.5 \n", "L 130.029349 139.5 \n", "L 134.581796 139.5 \n", "L 139.134244 139.5 \n", "L 143.686691 139.5 \n", "L 148.239139 139.5 \n", "L 152.791587 139.5 \n", "L 157.344034 139.5 \n", "L 161.896482 139.5 \n", "L 166.448929 139.5 \n", "L 171.001377 139.5 \n", "L 175.553824 139.5 \n", "L 180.106272 139.5 \n", "L 184.658719 139.5 \n", "L 189.211167 139.5 \n", "L 193.763615 139.5 \n", "L 198.316062 139.5 \n", "L 202.86851 139.5 \n", "L 207.420957 139.5 \n", "L 211.973405 139.5 \n", "L 216.525852 139.5 \n", "\" clip-path=\"url(#pdeecc3d0a7)\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 126.851562 73.9125 \n", "L 218.403125 73.9125 \n", "Q 220.403125 73.9125 220.403125 71.9125 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 126.851562 12.2 \n", "Q 124.851562 12.2 124.851562 14.2 \n", "L 124.851562 71.9125 \n", "Q 124.851562 73.9125 126.851562 73.9125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_16\">\n", "     <path d=\"M 128.851562 20.298438 \n", "L 138.851562 20.298438 \n", "L 148.851562 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- beta = 0.95 -->\n", "     <g transform=\"translate(156.851562 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-62\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"164.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"225.488281\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"257.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"341.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"372.851562\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"436.474609\"/>\n", "      <use xlink:href=\"#DejaVuSans-39\" x=\"468.261719\"/>\n", "      <use xlink:href=\"#DejaVuSans-35\" x=\"531.884766\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_17\">\n", "     <path d=\"M 128.851562 34.976562 \n", "L 138.851562 34.976562 \n", "L 148.851562 34.976562 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- beta = 0.90 -->\n", "     <g transform=\"translate(156.851562 38.476562) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-62\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"164.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"225.488281\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"257.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"341.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"372.851562\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"436.474609\"/>\n", "      <use xlink:href=\"#DejaVuSans-39\" x=\"468.261719\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"531.884766\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_18\">\n", "     <path d=\"M 128.851562 49.654687 \n", "L 138.851562 49.654687 \n", "L 148.851562 49.654687 \n", "\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- beta = 0.60 -->\n", "     <g transform=\"translate(156.851562 53.154687) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-62\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"164.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"225.488281\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"257.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"341.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"372.851562\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"436.474609\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"468.261719\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"531.884766\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_19\">\n", "     <path d=\"M 128.851562 64.332813 \n", "L 138.851562 64.332813 \n", "L 148.851562 64.332813 \n", "\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- beta = 0.00 -->\n", "     <g transform=\"translate(156.851562 67.832813) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-62\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"164.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"225.488281\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"257.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"341.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"372.851562\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"436.474609\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"468.261719\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"531.884766\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pdeecc3d0a7\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.set_figsize()\n", "betas = [0.95, 0.9, 0.6, 0]\n", "for beta in betas:\n", "    x = torch.arange(40).detach().numpy()\n", "    d2l.plt.plot(x, beta ** x, label=f'beta = {beta:.2f}')\n", "d2l.plt.xlabel('time')\n", "d2l.plt.legend();"]}, {"cell_type": "markdown", "id": "03114925", "metadata": {"origin_pos": 12}, "source": ["## Practical Experiments\n", "\n", "Let's see how momentum works in practice, i.e., when used within the context of a proper optimizer. For this we need a somewhat more scalable implementation.\n", "\n", "### Implementation from <PERSON><PERSON><PERSON>\n", "\n", "Compared with (minibatch) stochastic gradient descent the momentum method needs to maintain a set of  auxiliary variables, i.e., velocity. It has the same shape as the gradients (and variables of the optimization problem). In the implementation below we call these variables `states`.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "44104422", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:09.275760Z", "iopub.status.busy": "2023-08-18T19:45:09.275138Z", "iopub.status.idle": "2023-08-18T19:45:09.279518Z", "shell.execute_reply": "2023-08-18T19:45:09.278671Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["def init_momentum_states(feature_dim):\n", "    v_w = torch.zeros((feature_dim, 1))\n", "    v_b = torch.zeros(1)\n", "    return (v_w, v_b)"]}, {"cell_type": "code", "execution_count": 7, "id": "d2047404", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:09.282865Z", "iopub.status.busy": "2023-08-18T19:45:09.282307Z", "iopub.status.idle": "2023-08-18T19:45:09.287089Z", "shell.execute_reply": "2023-08-18T19:45:09.286303Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["def sgd_momentum(params, states, hyperparams):\n", "    for p, v in zip(params, states):\n", "        with torch.no_grad():\n", "            v[:] = hyperparams['momentum'] * v + p.grad\n", "            p[:] -= hyperparams['lr'] * v\n", "        p.grad.data.zero_()"]}, {"cell_type": "markdown", "id": "666911e6", "metadata": {"origin_pos": 18}, "source": ["Let's see how this works in practice.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "96a60c31", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:09.290520Z", "iopub.status.busy": "2023-08-18T19:45:09.289967Z", "iopub.status.idle": "2023-08-18T19:45:12.881025Z", "shell.execute_reply": "2023-08-18T19:45:12.880074Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss: 0.245, 0.153 sec/epoch\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"266.957813pt\" height=\"187.155469pt\" viewBox=\"0 0 266.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:12.839972</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 187.155469 \n", "L 266.**********.155469 \n", "L 266.957813 -0 \n", "L -0 -0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "L 56.50625 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 56.50625 149.599219 \n", "L 56.50625 10.999219 \n", "\" clip-path=\"url(#pddb65a747e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m7afc77bf45\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7afc77bf45\" x=\"56.50625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(48.554688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 105.33125 149.599219 \n", "L 105.33125 10.999219 \n", "\" clip-path=\"url(#pddb65a747e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m7afc77bf45\" x=\"105.33125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(97.379688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 154.15625 149.599219 \n", "L 154.15625 10.999219 \n", "\" clip-path=\"url(#pddb65a747e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m7afc77bf45\" x=\"154.15625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(146.204688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 202.98125 149.599219 \n", "L 202.98125 10.999219 \n", "\" clip-path=\"url(#pddb65a747e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m7afc77bf45\" x=\"202.98125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(195.029688 164.197656) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#pddb65a747e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m7afc77bf45\" x=\"251.80625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(243.854688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(138.928125 177.875781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 56.50625 144.26845 \n", "L 251.80625 144.26845 \n", "\" clip-path=\"url(#pddb65a747e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m8e44d410ce\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8e44d410ce\" x=\"56.50625\" y=\"144.26845\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.225 -->\n", "      <g transform=\"translate(20.878125 148.067668) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 56.50625 117.614603 \n", "L 251.80625 117.614603 \n", "\" clip-path=\"url(#pddb65a747e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m8e44d410ce\" x=\"56.50625\" y=\"117.614603\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.250 -->\n", "      <g transform=\"translate(20.878125 121.413822) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 56.50625 90.960757 \n", "L 251.80625 90.960757 \n", "\" clip-path=\"url(#pddb65a747e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m8e44d410ce\" x=\"56.50625\" y=\"90.960757\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.275 -->\n", "      <g transform=\"translate(20.878125 94.759976) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 56.50625 64.306911 \n", "L 251.80625 64.306911 \n", "\" clip-path=\"url(#pddb65a747e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m8e44d410ce\" x=\"56.50625\" y=\"64.306911\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.300 -->\n", "      <g transform=\"translate(20.878125 68.10613) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 56.50625 37.653065 \n", "L 251.80625 37.653065 \n", "\" clip-path=\"url(#pddb65a747e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m8e44d410ce\" x=\"56.50625\" y=\"37.653065\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.325 -->\n", "      <g transform=\"translate(20.878125 41.452284) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#pddb65a747e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m8e44d410ce\" x=\"56.50625\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.350 -->\n", "      <g transform=\"translate(20.878125 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798438 89.957031) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 69.52625 25.012429 \n", "L 82.54625 82.823373 \n", "L 95.56625 103.558186 \n", "L 108.58625 117.510241 \n", "L 121.60625 121.109614 \n", "L 134.62625 121.838591 \n", "L 147.64625 124.522832 \n", "L 160.66625 122.486092 \n", "L 173.68625 121.20391 \n", "L 186.70625 123.080648 \n", "L 199.72625 122.072658 \n", "L 212.74625 120.695692 \n", "L 225.76625 124.783814 \n", "L 238.78625 126.05304 \n", "L 251.80625 123.155259 \n", "\" clip-path=\"url(#pddb65a747e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 56.50625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 251.80625 149.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pddb65a747e\">\n", "   <rect x=\"56.50625\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def train_momentum(lr, momentum, num_epochs=2):\n", "    d2l.train_ch11(sgd_momentum, init_momentum_states(feature_dim),\n", "                   {'lr': lr, 'momentum': momentum}, data_iter,\n", "                   feature_dim, num_epochs)\n", "\n", "data_iter, feature_dim = d2l.get_data_ch11(batch_size=10)\n", "train_momentum(0.02, 0.5)"]}, {"cell_type": "markdown", "id": "ccd9b902", "metadata": {"origin_pos": 20}, "source": ["When we increase the momentum hyperparameter `momentum` to 0.9, it amounts to a significantly larger effective sample size of $\\frac{1}{1 - 0.9} = 10$. We reduce the learning rate slightly to $0.01$ to keep matters under control.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "0cf3eaeb", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:12.884542Z", "iopub.status.busy": "2023-08-18T19:45:12.883961Z", "iopub.status.idle": "2023-08-18T19:45:16.285627Z", "shell.execute_reply": "2023-08-18T19:45:16.284745Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss: 0.248, 0.109 sec/epoch\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"266.957813pt\" height=\"187.155469pt\" viewBox=\"0 0 266.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:16.243714</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 187.155469 \n", "L 266.**********.155469 \n", "L 266.957813 -0 \n", "L -0 -0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "L 56.50625 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 56.50625 149.599219 \n", "L 56.50625 10.999219 \n", "\" clip-path=\"url(#pf263c6bd24)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"md62292505d\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md62292505d\" x=\"56.50625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(48.554688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 105.33125 149.599219 \n", "L 105.33125 10.999219 \n", "\" clip-path=\"url(#pf263c6bd24)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#md62292505d\" x=\"105.33125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(97.379688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 154.15625 149.599219 \n", "L 154.15625 10.999219 \n", "\" clip-path=\"url(#pf263c6bd24)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#md62292505d\" x=\"154.15625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(146.204688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 202.98125 149.599219 \n", "L 202.98125 10.999219 \n", "\" clip-path=\"url(#pf263c6bd24)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#md62292505d\" x=\"202.98125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(195.029688 164.197656) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#pf263c6bd24)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#md62292505d\" x=\"251.80625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(243.854688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(138.928125 177.875781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 56.50625 144.26845 \n", "L 251.80625 144.26845 \n", "\" clip-path=\"url(#pf263c6bd24)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"meebf7d6a51\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#meebf7d6a51\" x=\"56.50625\" y=\"144.26845\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.225 -->\n", "      <g transform=\"translate(20.878125 148.067668) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 56.50625 117.614603 \n", "L 251.80625 117.614603 \n", "\" clip-path=\"url(#pf263c6bd24)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#meebf7d6a51\" x=\"56.50625\" y=\"117.614603\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.250 -->\n", "      <g transform=\"translate(20.878125 121.413822) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 56.50625 90.960757 \n", "L 251.80625 90.960757 \n", "\" clip-path=\"url(#pf263c6bd24)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#meebf7d6a51\" x=\"56.50625\" y=\"90.960757\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.275 -->\n", "      <g transform=\"translate(20.878125 94.759976) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 56.50625 64.306911 \n", "L 251.80625 64.306911 \n", "\" clip-path=\"url(#pf263c6bd24)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#meebf7d6a51\" x=\"56.50625\" y=\"64.306911\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.300 -->\n", "      <g transform=\"translate(20.878125 68.10613) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 56.50625 37.653065 \n", "L 251.80625 37.653065 \n", "\" clip-path=\"url(#pf263c6bd24)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#meebf7d6a51\" x=\"56.50625\" y=\"37.653065\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.325 -->\n", "      <g transform=\"translate(20.878125 41.452284) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#pf263c6bd24)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#meebf7d6a51\" x=\"56.50625\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.350 -->\n", "      <g transform=\"translate(20.878125 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798438 89.957031) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 69.52625 79.078263 \n", "L 82.54625 108.055849 \n", "L 95.56625 114.637514 \n", "L 108.58625 121.032025 \n", "L 121.60625 124.252254 \n", "L 134.62625 101.416903 \n", "L 147.64625 105.853428 \n", "L 160.66625 113.703431 \n", "L 173.68625 119.999853 \n", "L 186.70625 122.973677 \n", "L 199.72625 120.432535 \n", "L 212.74625 118.548794 \n", "L 225.76625 121.096427 \n", "L 238.78625 121.253703 \n", "L 251.80625 120.085506 \n", "\" clip-path=\"url(#pf263c6bd24)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 56.50625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 251.80625 149.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf263c6bd24\">\n", "   <rect x=\"56.50625\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["train_momentum(0.01, 0.9)"]}, {"cell_type": "markdown", "id": "d8a811ad", "metadata": {"origin_pos": 22}, "source": ["Reducing the learning rate further addresses any issue of non-smooth optimization problems. Setting it to $0.005$ yields good convergence properties.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "611c410e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:16.288851Z", "iopub.status.busy": "2023-08-18T19:45:16.288569Z", "iopub.status.idle": "2023-08-18T19:45:19.660179Z", "shell.execute_reply": "2023-08-18T19:45:19.659234Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss: 0.243, 0.107 sec/epoch\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"266.957813pt\" height=\"187.155469pt\" viewBox=\"0 0 266.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:19.619363</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 187.155469 \n", "L 266.**********.155469 \n", "L 266.957813 -0 \n", "L -0 -0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "L 56.50625 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 56.50625 149.599219 \n", "L 56.50625 10.999219 \n", "\" clip-path=\"url(#p24e0720c09)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mb7fa137537\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb7fa137537\" x=\"56.50625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(48.554688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 105.33125 149.599219 \n", "L 105.33125 10.999219 \n", "\" clip-path=\"url(#p24e0720c09)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mb7fa137537\" x=\"105.33125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(97.379688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 154.15625 149.599219 \n", "L 154.15625 10.999219 \n", "\" clip-path=\"url(#p24e0720c09)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mb7fa137537\" x=\"154.15625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(146.204688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 202.98125 149.599219 \n", "L 202.98125 10.999219 \n", "\" clip-path=\"url(#p24e0720c09)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mb7fa137537\" x=\"202.98125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(195.029688 164.197656) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#p24e0720c09)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mb7fa137537\" x=\"251.80625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(243.854688 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(138.928125 177.875781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 56.50625 144.26845 \n", "L 251.80625 144.26845 \n", "\" clip-path=\"url(#p24e0720c09)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m8ca9ce98e8\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8ca9ce98e8\" x=\"56.50625\" y=\"144.26845\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.225 -->\n", "      <g transform=\"translate(20.878125 148.067668) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 56.50625 117.614603 \n", "L 251.80625 117.614603 \n", "\" clip-path=\"url(#p24e0720c09)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m8ca9ce98e8\" x=\"56.50625\" y=\"117.614603\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.250 -->\n", "      <g transform=\"translate(20.878125 121.413822) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 56.50625 90.960757 \n", "L 251.80625 90.960757 \n", "\" clip-path=\"url(#p24e0720c09)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m8ca9ce98e8\" x=\"56.50625\" y=\"90.960757\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.275 -->\n", "      <g transform=\"translate(20.878125 94.759976) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 56.50625 64.306911 \n", "L 251.80625 64.306911 \n", "\" clip-path=\"url(#p24e0720c09)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m8ca9ce98e8\" x=\"56.50625\" y=\"64.306911\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.300 -->\n", "      <g transform=\"translate(20.878125 68.10613) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 56.50625 37.653065 \n", "L 251.80625 37.653065 \n", "\" clip-path=\"url(#p24e0720c09)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m8ca9ce98e8\" x=\"56.50625\" y=\"37.653065\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.325 -->\n", "      <g transform=\"translate(20.878125 41.452284) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#p24e0720c09)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m8ca9ce98e8\" x=\"56.50625\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.350 -->\n", "      <g transform=\"translate(20.878125 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798438 89.957031) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 72.024072 -1 \n", "L 82.54625 75.272806 \n", "L 95.56625 115.568341 \n", "L 108.58625 116.472774 \n", "L 121.60625 119.580689 \n", "L 134.62625 123.91349 \n", "L 147.64625 123.842329 \n", "L 160.66625 115.989911 \n", "L 173.68625 117.866301 \n", "L 186.70625 122.105894 \n", "L 199.72625 122.295561 \n", "L 212.74625 120.765698 \n", "L 225.76625 121.744283 \n", "L 238.78625 124.54451 \n", "L 251.80625 124.888187 \n", "\" clip-path=\"url(#p24e0720c09)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 56.50625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 251.80625 149.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p24e0720c09\">\n", "   <rect x=\"56.50625\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["train_momentum(0.005, 0.9)"]}, {"cell_type": "markdown", "id": "6986283b", "metadata": {"origin_pos": 24}, "source": ["### Concise Implementation\n", "\n", "There is very little to do in Gluon since the standard `sgd` solver already had momentum built in. Setting matching parameters yields a very similar trajectory.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "0801b6b7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:19.663901Z", "iopub.status.busy": "2023-08-18T19:45:19.663266Z", "iopub.status.idle": "2023-08-18T19:45:26.333136Z", "shell.execute_reply": "2023-08-18T19:45:26.332278Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss: 0.250, 0.108 sec/epoch\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"262.1875pt\" height=\"187.155469pt\" viewBox=\"0 0 262.1875 187.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:26.292591</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 187.155469 \n", "L 262.1875 187.155469 \n", "L 262.1875 -0 \n", "L -0 -0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "L 56.50625 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 56.50625 149.599219 \n", "L 56.50625 10.999219 \n", "\" clip-path=\"url(#p09da2dcc1b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mf2de940fde\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf2de940fde\" x=\"56.50625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(53.325 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 105.33125 149.599219 \n", "L 105.33125 10.999219 \n", "\" clip-path=\"url(#p09da2dcc1b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mf2de940fde\" x=\"105.33125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(102.15 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 154.15625 149.599219 \n", "L 154.15625 10.999219 \n", "\" clip-path=\"url(#p09da2dcc1b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mf2de940fde\" x=\"154.15625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(150.975 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 202.98125 149.599219 \n", "L 202.98125 10.999219 \n", "\" clip-path=\"url(#p09da2dcc1b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mf2de940fde\" x=\"202.98125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(199.8 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#p09da2dcc1b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mf2de940fde\" x=\"251.80625\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(248.625 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(138.928125 177.875781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 56.50625 144.26845 \n", "L 251.80625 144.26845 \n", "\" clip-path=\"url(#p09da2dcc1b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m1f3f859874\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1f3f859874\" x=\"56.50625\" y=\"144.26845\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.225 -->\n", "      <g transform=\"translate(20.878125 148.067668) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 56.50625 117.614603 \n", "L 251.80625 117.614603 \n", "\" clip-path=\"url(#p09da2dcc1b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m1f3f859874\" x=\"56.50625\" y=\"117.614603\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.250 -->\n", "      <g transform=\"translate(20.878125 121.413822) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 56.50625 90.960757 \n", "L 251.80625 90.960757 \n", "\" clip-path=\"url(#p09da2dcc1b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m1f3f859874\" x=\"56.50625\" y=\"90.960757\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.275 -->\n", "      <g transform=\"translate(20.878125 94.759976) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 56.50625 64.306911 \n", "L 251.80625 64.306911 \n", "\" clip-path=\"url(#p09da2dcc1b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m1f3f859874\" x=\"56.50625\" y=\"64.306911\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.300 -->\n", "      <g transform=\"translate(20.878125 68.10613) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 56.50625 37.653065 \n", "L 251.80625 37.653065 \n", "\" clip-path=\"url(#p09da2dcc1b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m1f3f859874\" x=\"56.50625\" y=\"37.653065\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.325 -->\n", "      <g transform=\"translate(20.878125 41.452284) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" clip-path=\"url(#p09da2dcc1b)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m1f3f859874\" x=\"56.50625\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.350 -->\n", "      <g transform=\"translate(20.878125 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- loss -->\n", "     <g transform=\"translate(14.798438 89.957031) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"88.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"141.064453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 63.01625 65.325329 \n", "L 69.52625 107.988025 \n", "L 76.03625 116.497685 \n", "L 82.54625 119.809408 \n", "L 89.05625 123.055827 \n", "L 95.56625 125.196714 \n", "L 102.07625 124.313919 \n", "L 108.58625 118.623081 \n", "L 115.09625 124.121013 \n", "L 121.60625 121.248501 \n", "L 128.11625 118.428429 \n", "L 134.62625 117.094324 \n", "L 141.13625 123.629377 \n", "L 147.64625 124.697916 \n", "L 154.15625 111.888675 \n", "L 160.66625 104.324583 \n", "L 167.17625 120.02569 \n", "L 173.68625 121.701334 \n", "L 180.19625 124.101802 \n", "L 186.70625 123.533863 \n", "L 193.21625 118.185064 \n", "L 199.72625 118.029031 \n", "L 206.23625 123.097421 \n", "L 212.74625 120.473398 \n", "L 219.25625 117.524268 \n", "L 225.76625 114.633807 \n", "L 232.27625 122.427015 \n", "L 238.78625 123.345936 \n", "L 245.29625 125.543647 \n", "L 251.80625 117.393493 \n", "\" clip-path=\"url(#p09da2dcc1b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 56.50625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 251.80625 149.599219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 56.50625 149.599219 \n", "L 251.80625 149.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 56.50625 10.999219 \n", "L 251.80625 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p09da2dcc1b\">\n", "   <rect x=\"56.50625\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer = torch.optim.SGD\n", "d2l.train_concise_ch11(trainer, {'lr': 0.005, 'momentum': 0.9}, data_iter)"]}, {"cell_type": "markdown", "id": "175977b3", "metadata": {"origin_pos": 28}, "source": ["## Theoretical Analysis\n", "\n", "So far the 2D example of $f(x) = 0.1 x_1^2 + 2 x_2^2$ seemed rather contrived. We will now see that this is actually quite representative of the types of problem one might encounter, at least in the case of minimizing convex quadratic objective functions.\n", "\n", "### Quadratic Convex Functions\n", "\n", "Consider the function\n", "\n", "$$h(\\mathbf{x}) = \\frac{1}{2} \\mathbf{x}^\\top \\mathbf{Q} \\mathbf{x} + \\mathbf{x}^\\top \\mathbf{c} + b.$$\n", "\n", "This is a general quadratic function. For positive definite matrices $\\mathbf{Q} \\succ 0$, i.e., for matrices with positive eigenvalues this has a minimizer at $\\mathbf{x}^* = -\\mathbf{Q}^{-1} \\mathbf{c}$ with minimum value $b - \\frac{1}{2} \\mathbf{c}^\\top \\mathbf{Q}^{-1} \\mathbf{c}$. Hence we can rewrite $h$ as\n", "\n", "$$h(\\mathbf{x}) = \\frac{1}{2} (\\mathbf{x} - \\mathbf{Q}^{-1} \\mathbf{c})^\\top \\mathbf{Q} (\\mathbf{x} - \\mathbf{Q}^{-1} \\mathbf{c}) + b - \\frac{1}{2} \\mathbf{c}^\\top \\mathbf{Q}^{-1} \\mathbf{c}.$$\n", "\n", "The gradient is given by $\\partial_{\\mathbf{x}} h(\\mathbf{x}) = \\mathbf{Q} (\\mathbf{x} - \\mathbf{Q}^{-1} \\mathbf{c})$. That is, it is given by the distance between $\\mathbf{x}$ and the minimizer, multiplied by $\\mathbf{Q}$. Consequently also the velocity  is a linear combination of terms $\\mathbf{Q} (\\mathbf{x}_t - \\mathbf{Q}^{-1} \\mathbf{c})$.\n", "\n", "Since $\\mathbf{Q}$ is positive definite it can be decomposed into its eigensystem via $\\mathbf{Q} = \\mathbf{O}^\\top \\boldsymbol{\\Lambda} \\mathbf{O}$ for an orthogonal (rotation) matrix $\\mathbf{O}$ and a diagonal matrix $\\boldsymbol{\\Lambda}$ of positive eigenvalues. This allows us to perform a change of variables from $\\mathbf{x}$ to $\\mathbf{z} \\stackrel{\\textrm{def}}{=} \\mathbf{O} (\\mathbf{x} - \\mathbf{Q}^{-1} \\mathbf{c})$ to obtain a much simplified expression:\n", "\n", "$$h(\\mathbf{z}) = \\frac{1}{2} \\mathbf{z}^\\top \\boldsymbol{\\Lambda} \\mathbf{z} + b'.$$\n", "\n", "Here $b' = b - \\frac{1}{2} \\mathbf{c}^\\top \\mathbf{Q}^{-1} \\mathbf{c}$. Since $\\mathbf{O}$ is only an orthogonal matrix this does not perturb the gradients in a meaningful way. Expressed in terms of $\\mathbf{z}$ gradient descent becomes\n", "\n", "$$\\mathbf{z}_t = \\mathbf{z}_{t-1} - \\boldsymbol{\\Lambda} \\mathbf{z}_{t-1} = (\\mathbf{I} - \\boldsymbol{\\Lambda}) \\mathbf{z}_{t-1}.$$\n", "\n", "The important fact in this expression is that gradient descent *does not mix* between different eigenspaces. That is, when expressed in terms of the eigensystem of $\\mathbf{Q}$ the optimization problem proceeds in a coordinate-wise manner. This also holds for\n", "\n", "$$\\begin{aligned}\n", "\\mathbf{v}_t & = \\beta \\mathbf{v}_{t-1} + \\boldsymbol{\\Lambda} \\mathbf{z}_{t-1} \\\\\n", "\\mathbf{z}_t & = \\mathbf{z}_{t-1} - \\eta \\left(\\beta \\mathbf{v}_{t-1} + \\boldsymbol{\\Lambda} \\mathbf{z}_{t-1}\\right) \\\\\n", "    & = (\\mathbf{I} - \\eta \\boldsymbol{\\Lambda}) \\mathbf{z}_{t-1} - \\eta \\beta \\mathbf{v}_{t-1}.\n", "\\end{aligned}$$\n", "\n", "In doing this we just proved the following theorem: gradient descent with and without momentum for a convex quadratic function decomposes into coordinate-wise optimization in the direction of the eigenvectors of the quadratic matrix.\n", "\n", "### Scalar Functions\n", "\n", "Given the above result let's see what happens when we minimize the function $f(x) = \\frac{\\lambda}{2} x^2$. For gradient descent we have\n", "\n", "$$x_{t+1} = x_t - \\eta \\lambda x_t = (1 - \\eta \\lambda) x_t.$$\n", "\n", "Whenever $|1 - \\eta \\lambda| < 1$ this optimization converges at an exponential rate since after $t$ steps we have $x_t = (1 - \\eta \\lambda)^t x_0$. This shows how the rate of convergence improves initially as we increase the learning rate $\\eta$ until $\\eta \\lambda = 1$. Beyond that things diverge and for $\\eta \\lambda > 2$ the optimization problem diverges.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "41934226", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:26.339502Z", "iopub.status.busy": "2023-08-18T19:45:26.339087Z", "iopub.status.idle": "2023-08-18T19:45:26.615834Z", "shell.execute_reply": "2023-08-18T19:45:26.614946Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"386.845312pt\" height=\"266.51625pt\" viewBox=\"0 0 386.**********.51625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:26.552441</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 266.51625 \n", "L 386.**********.51625 \n", "L 386.845312 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 44.**********.96 \n", "L 379.**********.96 \n", "L 379.645313 7.2 \n", "L 44.845313 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m56947cac9d\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m56947cac9d\" x=\"60.063494\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(52.111932 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m56947cac9d\" x=\"100.111341\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(92.159779 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m56947cac9d\" x=\"140.159188\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 5.0 -->\n", "      <g transform=\"translate(132.207626 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m56947cac9d\" x=\"180.207035\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 7.5 -->\n", "      <g transform=\"translate(172.255472 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m56947cac9d\" x=\"220.254882\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10.0 -->\n", "      <g transform=\"translate(209.122069 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m56947cac9d\" x=\"260.302729\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 12.5 -->\n", "      <g transform=\"translate(249.169916 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m56947cac9d\" x=\"300.350576\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 15.0 -->\n", "      <g transform=\"translate(289.217763 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m56947cac9d\" x=\"340.398423\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 17.5 -->\n", "      <g transform=\"translate(329.26561 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- time -->\n", "     <g transform=\"translate(200.949219 257.236562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <defs>\n", "       <path id=\"m1958a99fbb\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1958a99fbb\" x=\"44.845313\" y=\"202.964211\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- −0.75 -->\n", "      <g transform=\"translate(7.2 206.763429) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"179.199219\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"242.822266\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m1958a99fbb\" x=\"44.845313\" y=\"176.437895\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- −0.50 -->\n", "      <g transform=\"translate(7.2 180.237113) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"242.822266\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m1958a99fbb\" x=\"44.845313\" y=\"149.911579\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- −0.25 -->\n", "      <g transform=\"translate(7.2 153.710798) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"179.199219\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"242.822266\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m1958a99fbb\" x=\"44.845313\" y=\"123.385263\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(15.579688 127.184482) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m1958a99fbb\" x=\"44.845313\" y=\"96.858947\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0.25 -->\n", "      <g transform=\"translate(15.579688 100.658166) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m1958a99fbb\" x=\"44.845313\" y=\"70.332632\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0.50 -->\n", "      <g transform=\"translate(15.579688 74.13185) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m1958a99fbb\" x=\"44.845313\" y=\"43.806316\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 0.75 -->\n", "      <g transform=\"translate(15.579688 47.605535) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m1958a99fbb\" x=\"44.845313\" y=\"17.28\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 1.00 -->\n", "      <g transform=\"translate(15.579688 21.079219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 60.063494 17.28 \n", "L 76.082633 18.341053 \n", "L 92.101772 19.391495 \n", "L 108.120911 20.431432 \n", "L 124.140049 21.460971 \n", "L 140.159188 22.480214 \n", "L 156.178327 23.489264 \n", "L 172.197466 24.488224 \n", "L 188.216604 25.477195 \n", "L 204.235743 26.456275 \n", "L 220.254882 27.425565 \n", "L 236.274021 28.385162 \n", "L 252.293159 29.335163 \n", "L 268.312298 30.275664 \n", "L 284.331437 31.20676 \n", "L 300.350576 32.128545 \n", "L 316.369714 33.041112 \n", "L 332.388853 33.944554 \n", "L 348.407992 34.838961 \n", "L 364.427131 35.724424 \n", "\" clip-path=\"url(#pd68dba7f87)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 60.063494 17.28 \n", "L 76.082633 27.890526 \n", "L 92.101772 37.44 \n", "L 108.120911 46.034526 \n", "L 124.140049 53.7696 \n", "L 140.159188 60.731166 \n", "L 156.178327 66.996576 \n", "L 172.197466 72.635445 \n", "L 188.216604 77.710427 \n", "L 204.235743 82.27791 \n", "L 220.254882 86.388646 \n", "L 236.274021 90.088307 \n", "L 252.293159 93.418003 \n", "L 268.312298 96.414729 \n", "L 284.331437 99.111782 \n", "L 300.350576 101.53913 \n", "L 316.369714 103.723744 \n", "L 332.388853 105.689896 \n", "L 348.407992 107.459432 \n", "L 364.427131 109.052015 \n", "\" clip-path=\"url(#pd68dba7f87)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 60.063494 17.28 \n", "L 76.082633 123.385263 \n", "L 92.101772 123.385263 \n", "L 108.120911 123.385263 \n", "L 124.140049 123.385263 \n", "L 140.159188 123.385263 \n", "L 156.178327 123.385263 \n", "L 172.197466 123.385263 \n", "L 188.216604 123.385263 \n", "L 204.235743 123.385263 \n", "L 220.254882 123.385263 \n", "L 236.274021 123.385263 \n", "L 252.293159 123.385263 \n", "L 268.312298 123.385263 \n", "L 284.331437 123.385263 \n", "L 300.350576 123.385263 \n", "L 316.369714 123.385263 \n", "L 332.388853 123.385263 \n", "L 348.407992 123.385263 \n", "L 364.427131 123.385263 \n", "\" clip-path=\"url(#pd68dba7f87)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 60.063494 17.28 \n", "L 76.082633 218.88 \n", "L 92.101772 37.44 \n", "L 108.120911 200.736 \n", "L 124.140049 53.7696 \n", "L 140.159188 186.03936 \n", "L 156.178327 66.996576 \n", "L 172.197466 174.135082 \n", "L 188.216604 77.710427 \n", "L 204.235743 164.492616 \n", "L 220.254882 86.388646 \n", "L 236.274021 156.682219 \n", "L 252.293159 93.418003 \n", "L 268.312298 150.355797 \n", "L 284.331437 99.111782 \n", "L 300.350576 145.231396 \n", "L 316.369714 103.723744 \n", "L 332.388853 141.080631 \n", "L 348.407992 107.459432 \n", "L 364.427131 137.718511 \n", "\" clip-path=\"url(#pd68dba7f87)\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 44.**********.96 \n", "L 44.845313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 379.**********.96 \n", "L 379.645313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 44.845312 228.96 \n", "L 379.**********.96 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 44.845312 7.2 \n", "L 379.645313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 259.809375 223.96 \n", "L 372.645313 223.96 \n", "Q 374.645313 223.96 374.645313 221.96 \n", "L 374.645313 164.2475 \n", "Q 374.645313 162.2475 372.645313 162.2475 \n", "L 259.809375 162.2475 \n", "Q 257.809375 162.2475 257.809375 164.2475 \n", "L 257.809375 221.96 \n", "Q 257.809375 223.96 259.809375 223.96 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_21\">\n", "     <path d=\"M 261.809375 170.345937 \n", "L 271.809375 170.345937 \n", "L 281.809375 170.345937 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- lambda = 0.10 -->\n", "     <g transform=\"translate(289.809375 173.845937) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"89.0625\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"186.474609\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"249.951172\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"374.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"406.494141\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"490.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"522.070312\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"585.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"617.480469\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"681.103516\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_22\">\n", "     <path d=\"M 261.809375 185.024062 \n", "L 271.809375 185.024062 \n", "L 281.809375 185.024062 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- lambda = 1.00 -->\n", "     <g transform=\"translate(289.809375 188.524062) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"89.0625\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"186.474609\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"249.951172\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"374.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"406.494141\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"490.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"522.070312\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"585.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"617.480469\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"681.103516\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_23\">\n", "     <path d=\"M 261.809375 199.702187 \n", "L 271.809375 199.702187 \n", "L 281.809375 199.702187 \n", "\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_20\">\n", "     <!-- lambda = 10.00 -->\n", "     <g transform=\"translate(289.809375 203.202187) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"89.0625\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"186.474609\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"249.951172\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"374.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"406.494141\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"490.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"522.070312\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"585.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"649.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"681.103516\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"744.726562\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_24\">\n", "     <path d=\"M 261.809375 214.380312 \n", "L 271.809375 214.380312 \n", "L 281.809375 214.380312 \n", "\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- lambda = 19.00 -->\n", "     <g transform=\"translate(289.809375 217.880312) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"89.0625\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"186.474609\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"249.951172\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"374.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-3d\" x=\"406.494141\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"490.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"522.070312\"/>\n", "      <use xlink:href=\"#DejaVuSans-39\" x=\"585.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"649.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"681.103516\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"744.726562\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pd68dba7f87\">\n", "   <rect x=\"44.845313\" y=\"7.2\" width=\"334.8\" height=\"221.76\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lambdas = [0.1, 1, 10, 19]\n", "eta = 0.1\n", "d2l.set_figsize((6, 4))\n", "for lam in lambdas:\n", "    t = torch.arange(20).detach().numpy()\n", "    d2l.plt.plot(t, (1 - eta * lam) ** t, label=f'lambda = {lam:.2f}')\n", "d2l.plt.xlabel('time')\n", "d2l.plt.legend();"]}, {"cell_type": "markdown", "id": "6e7b867c", "metadata": {"origin_pos": 30}, "source": ["To analyze convergence in the case of momentum we begin by rewriting the update equations in terms of two scalars: one for $x$ and one for velocity $v$. This yields:\n", "\n", "$$\n", "\\begin{bmatrix} v_{t+1} \\\\ x_{t+1} \\end{bmatrix} =\n", "\\begin{bmatrix} \\beta & \\lambda \\\\ -\\eta \\beta & (1 - \\eta \\lambda) \\end{bmatrix}\n", "\\begin{bmatrix} v_{t} \\\\ x_{t} \\end{bmatrix} = \\mathbf{R}(\\beta, \\eta, \\lambda) \\begin{bmatrix} v_{t} \\\\ x_{t} \\end{bmatrix}.\n", "$$\n", "\n", "We used $\\mathbf{R}$ to denote the $2 \\times 2$ governing convergence behavior. After $t$ steps the initial choice $[v_0, x_0]$ becomes $\\mathbf{R}(\\beta, \\eta, \\lambda)^t [v_0, x_0]$. Hence, it is up to the eigenvalues of $\\mathbf{R}$ to determine the speed of convergence. See the [Distill post](https://distill.pub/2017/momentum/) of :citet:`Goh.2017` for a great animation and :citet:`Flammarion.Bach.2015` for a detailed analysis. One can show that $0 < \\eta \\lambda < 2 + 2 \\beta$ velocity converges. This is a larger range of feasible parameters when compared to $0 < \\eta \\lambda < 2$ for gradient descent. It also suggests that in general large values of $\\beta$ are desirable. Further details require a fair amount of technical detail and we suggest that the interested reader consult the original publications.\n", "\n", "## Summary\n", "\n", "* Momentum replaces gradients with a leaky average over past gradients. This accelerates convergence significantly.\n", "* It is desirable for both noise-free gradient descent and (noisy) stochastic gradient descent.\n", "* Momentum prevents stalling of the optimization process that is much more likely to occur for stochastic gradient descent.\n", "* The effective number of gradients is given by $\\frac{1}{1-\\beta}$ due to exponentiated downweighting of past data.\n", "* In the case of convex quadratic problems this can be analyzed explicitly in detail.\n", "* Implementation is quite straightforward but it requires us to store an additional state vector (velocity $\\mathbf{v}$).\n", "\n", "## Exercises\n", "\n", "1. Use other combinations of momentum hyperparameters and learning rates and observe and analyze the different experimental results.\n", "1. Try out gradient descent and momentum for a quadratic problem where you have multiple eigenvalues, i.e., $f(x) = \\frac{1}{2} \\sum_i \\lambda_i x_i^2$, e.g., $\\lambda_i = 2^{-i}$. Plot how the values of $x$ decrease for the initialization $x_i = 1$.\n", "1. Derive minimum value and minimizer for $h(\\mathbf{x}) = \\frac{1}{2} \\mathbf{x}^\\top \\mathbf{Q} \\mathbf{x} + \\mathbf{x}^\\top \\mathbf{c} + b$.\n", "1. What changes when we perform stochastic gradient descent with momentum? What happens when we use minibatch stochastic gradient descent with momentum? Experiment with the parameters?\n"]}, {"cell_type": "markdown", "id": "bb48c694", "metadata": {"origin_pos": 32, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1070)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "2b9b78fd", "metadata": {"origin_pos": 0}, "source": ["# Optimization Algorithms\n", ":label:`chap_optimization`\n", "\n", "If you read the book in sequence up to this point you already used a number of optimization algorithms to train deep learning models.\n", "They were the tools that allowed us to continue updating model parameters and to minimize the value of the loss function, as evaluated on the training set. Indeed, anyone content with treating optimization as a black box device to minimize objective functions in a simple setting might well content oneself with the knowledge that there exists an array of incantations of such a procedure (with names such as \"<PERSON><PERSON><PERSON>\" and \"<PERSON>\").\n", "\n", "To do well, however, some deeper knowledge is required.\n", "Optimization algorithms are important for deep learning.\n", "On the one hand, training a complex deep learning model can take hours, days, or even weeks.\n", "The performance of the optimization algorithm directly affects the model's training efficiency.\n", "On the other hand, understanding the principles of different optimization algorithms and the role of their hyperparameters\n", "will enable us to tune the hyperparameters in a targeted manner to improve the performance of deep learning models.\n", "\n", "In this chapter, we explore common deep learning optimization algorithms in depth.\n", "Almost all optimization problems arising in deep learning are *nonconvex*.\n", "Nonetheless, the design and analysis of algorithms in the context of *convex* problems have proven to be very instructive.\n", "It is for that reason that this chapter includes a primer on convex optimization and the proof for a very simple stochastic gradient descent algorithm on a convex objective function.\n", "\n", ":begin_tab:toc\n", " - [optimization-intro](optimization-intro.ipynb)\n", " - [convexity](convexity.ipynb)\n", " - [gd](gd.ipynb)\n", " - [sgd](sgd.ipynb)\n", " - [minibatch-sgd](minibatch-sgd.ipynb)\n", " - [momentum](momentum.ipynb)\n", " - [adagrad](adagrad.ipynb)\n", " - [rmsprop](rmsprop.ipynb)\n", " - [adadelta](adadelta.ipynb)\n", " - [adam](adam.ipynb)\n", " - [lr-scheduler](lr-scheduler.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
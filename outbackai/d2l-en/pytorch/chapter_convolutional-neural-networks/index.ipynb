{"cells": [{"cell_type": "markdown", "id": "5951640a", "metadata": {"origin_pos": 0}, "source": ["# Convolutional Neural Networks\n", ":label:`chap_cnn`\n", "\n", "Image data is represented as a two-dimensional grid of pixels, be the image\n", "monochromatic or in color. Accordingly each pixel corresponds to one\n", "or multiple numerical values respectively. So far we have ignored this rich\n", "structure and treated images as vectors of numbers by *flattening* them, irrespective of the spatial relation between pixels. This\n", "deeply unsatisfying approach was necessary in order to feed the\n", "resulting one-dimensional vectors through a fully connected MLP.\n", "\n", "Because these networks are invariant to the order of the features, we\n", "could get similar results regardless of whether we preserve an order\n", "corresponding to the spatial structure of the pixels or if we permute\n", "the columns of our design matrix before fitting the MLP's parameters.\n", "Ideally, we would leverage our prior knowledge that nearby pixels\n", "are typically related to each other, to build efficient models for\n", "learning from image data.\n", "\n", "This chapter introduces *convolutional neural networks* (CNNs)\n", ":cite:`<PERSON><PERSON><PERSON>.Jackel.Bottou.ea.1995`, a powerful family of neural networks that\n", "are designed for precisely this purpose.\n", "CNN-based architectures are\n", "now ubiquitous in the field of computer vision.\n", "For instance, on the Imagnet collection\n", ":cite:`<PERSON>g<PERSON><PERSON>.<PERSON>cher.ea.2009` it was only the use of convolutional neural\n", "networks, in short Convnets, that provided significant performance\n", "improvements :cite:<PERSON><PERSON><PERSON><PERSON><PERSON>.Sutskever.Hinton.2012`.\n", "\n", "Modern CNNs, as they are called colloquially, owe their design to\n", "inspirations from biology, group theory, and a healthy dose of\n", "experimental tinkering.  In addition to their sample efficiency in\n", "achieving accurate models, CNNs tend to be computationally efficient,\n", "both because they require fewer parameters than fully connected\n", "architectures and because convolutions are easy to parallelize across\n", "GPU cores :cite:`<PERSON><PERSON><PERSON>r.Woolley.Vandermersch.ea.2014`.  Consequently, practitioners often\n", "apply CNNs whenever possible, and increasingly they have emerged as\n", "credible competitors even on tasks with a one-dimensional sequence\n", "structure, such as audio :cite:`<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Mohamed.Jiang.ea.2014`, text\n", ":cite:`Kalchbrenner.Grefenstette.Blunsom.2014`, and time series analysis\n", ":cite:`LeCun.Bengio.ea.1995`, where recurrent neural networks are\n", "conventionally used.  Some clever adaptations of CNNs have also\n", "brought them to bear on graph-structured data :cite:`Kipf.Welling.2016` and\n", "in recommender systems.\n", "\n", "First, we will dive more deeply into the motivation for convolutional\n", "neural networks. This is followed by a walk through the basic operations\n", "that comprise the backbone of all convolutional networks.\n", "These include the convolutional layers themselves,\n", "nitty-gritty details including padding and stride,\n", "the pooling layers used to aggregate information\n", "across adjacent spatial regions,\n", "the use of multiple channels  at each layer,\n", "and a careful discussion of the structure of modern architectures.\n", "We will conclude the chapter with a full working example of LeNet,\n", "the first convolutional network successfully deployed,\n", "long before the rise of modern deep learning.\n", "In the next chapter, we will dive into full implementations\n", "of some popular and comparatively recent CNN architectures\n", "whose designs represent most of the techniques\n", "commonly used by modern practitioners.\n", "\n", ":begin_tab:toc\n", " - [why-conv](why-conv.ipynb)\n", " - [conv-layer](conv-layer.ipynb)\n", " - [padding-and-strides](padding-and-strides.ipynb)\n", " - [channels](channels.ipynb)\n", " - [pooling](pooling.ipynb)\n", " - [lenet](lenet.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
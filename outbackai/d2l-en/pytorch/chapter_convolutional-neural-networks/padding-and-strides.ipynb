{"cells": [{"cell_type": "markdown", "id": "1ca8e5cd", "metadata": {"origin_pos": 1}, "source": ["# Padding and Stride\n", ":label:`sec_padding`\n", "\n", "Recall the example of a convolution in :numref:`fig_correlation`. \n", "The input had both a height and width of 3\n", "and the convolution kernel had both a height and width of 2,\n", "yielding an output representation with dimension $2\\times2$.\n", "Assuming that the input shape is $n_\\textrm{h}\\times n_\\textrm{w}$\n", "and the convolution kernel shape is $k_\\textrm{h}\\times k_\\textrm{w}$,\n", "the output shape will be $(n_\\textrm{h}-k_\\textrm{h}+1) \\times (n_\\textrm{w}-k_\\textrm{w}+1)$: \n", "we can only shift the convolution kernel so far until it runs out\n", "of pixels to apply the convolution to. \n", "\n", "In the following we will explore a number of techniques, \n", "including padding and strided convolutions,\n", "that offer more control over the size of the output. \n", "As motivation, note that since kernels generally\n", "have width and height greater than $1$,\n", "after applying many successive convolutions,\n", "we tend to wind up with outputs that are\n", "considerably smaller than our input.\n", "If we start with a $240 \\times 240$ pixel image,\n", "ten layers of $5 \\times 5$ convolutions\n", "reduce the image to $200 \\times 200$ pixels,\n", "slicing off $30 \\%$ of the image and with it\n", "obliterating any interesting information\n", "on the boundaries of the original image.\n", "*Padding* is the most popular tool for handling this issue.\n", "In other cases, we may want to reduce the dimensionality drastically,\n", "e.g., if we find the original input resolution to be unwieldy.\n", "*Strided convolutions* are a popular technique that can help in these instances.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "49dab7e8", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:14.385177Z", "iopub.status.busy": "2023-08-18T19:43:14.384720Z", "iopub.status.idle": "2023-08-18T19:43:16.173429Z", "shell.execute_reply": "2023-08-18T19:43:16.172321Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn"]}, {"cell_type": "markdown", "id": "4bf1f4a4", "metadata": {"origin_pos": 6}, "source": ["## Padding\n", "\n", "As described above, one tricky issue when applying convolutional layers\n", "is that we tend to lose pixels on the perimeter of our image. Consider :numref:`img_conv_reuse` that depicts the pixel utilization as a function of the convolution kernel size and the position within the image. The pixels in the corners are hardly used at all. \n", "\n", "![Pixel utilization for convolutions of size $1 \\times 1$, $2 \\times 2$, and $3 \\times 3$ respectively.](../img/conv-reuse.svg)\n", ":label:`img_conv_reuse`\n", "\n", "Since we typically use small kernels,\n", "for any given convolution\n", "we might only lose a few pixels\n", "but this can add up as we apply\n", "many successive convolutional layers.\n", "One straightforward solution to this problem\n", "is to add extra pixels of filler around the boundary of our input image,\n", "thus increasing the effective size of the image.\n", "Typically, we set the values of the extra pixels to zero.\n", "In :numref:`img_conv_pad`, we pad a $3 \\times 3$ input,\n", "increasing its size to $5 \\times 5$.\n", "The corresponding output then increases to a $4 \\times 4$ matrix.\n", "The shaded portions are the first output element as well as the input and kernel tensor elements used for the output computation: $0\\times0+0\\times1+0\\times2+0\\times3=0$.\n", "\n", "![Two-dimensional cross-correlation with padding.](../img/conv-pad.svg)\n", ":label:`img_conv_pad`\n", "\n", "In general, if we add a total of $p_\\textrm{h}$ rows of padding\n", "(roughly half on top and half on bottom)\n", "and a total of $p_\\textrm{w}$ columns of padding\n", "(roughly half on the left and half on the right),\n", "the output shape will be\n", "\n", "$$(n_\\textrm{h}-k_\\textrm{h}+p_\\textrm{h}+1)\\times(n_\\textrm{w}-k_\\textrm{w}+p_\\textrm{w}+1).$$\n", "\n", "This means that the height and width of the output\n", "will increase by $p_\\textrm{h}$ and $p_\\textrm{w}$, respectively.\n", "\n", "In many cases, we will want to set $p_\\textrm{h}=k_\\textrm{h}-1$ and $p_\\textrm{w}=k_\\textrm{w}-1$\n", "to give the input and output the same height and width.\n", "This will make it easier to predict the output shape of each layer\n", "when constructing the network.\n", "Assuming that $k_\\textrm{h}$ is odd here,\n", "we will pad $p_\\textrm{h}/2$ rows on both sides of the height.\n", "If $k_\\textrm{h}$ is even, one possibility is to\n", "pad $\\lceil p_\\textrm{h}/2\\rceil$ rows on the top of the input\n", "and $\\lfloor p_\\textrm{h}/2\\rfloor$ rows on the bottom.\n", "We will pad both sides of the width in the same way.\n", "\n", "CNNs commonly use convolution kernels\n", "with odd height and width values, such as 1, 3, 5, or 7.\n", "Choosing odd kernel sizes has the benefit\n", "that we can preserve the dimensionality\n", "while padding with the same number of rows on top and bottom,\n", "and the same number of columns on left and right.\n", "\n", "Moreover, this practice of using odd kernels\n", "and padding to precisely preserve dimensionality\n", "offers a clerical benefit.\n", "For any two-dimensional tensor `X`,\n", "when the kernel's size is odd\n", "and the number of padding rows and columns\n", "on all sides are the same,\n", "thereby producing an output with the same height and width as the input,\n", "we know that the output `Y[i, j]` is calculated\n", "by cross-correlation of the input and convolution kernel\n", "with the window centered on `X[i, j]`.\n", "\n", "In the following example, we create a two-dimensional convolutional layer\n", "with a height and width of 3\n", "and (**apply 1 pixel of padding on all sides.**)\n", "Given an input with a height and width of 8,\n", "we find that the height and width of the output is also 8.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "b5e8917d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:16.177580Z", "iopub.status.busy": "2023-08-18T19:43:16.176879Z", "iopub.status.idle": "2023-08-18T19:43:16.211812Z", "shell.execute_reply": "2023-08-18T19:43:16.210995Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([8, 8])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# We define a helper function to calculate convolutions. It initializes the\n", "# convolutional layer weights and performs corresponding dimensionality\n", "# elevations and reductions on the input and output\n", "def comp_conv2d(conv2d, X):\n", "    # (1, 1) indicates that batch size and the number of channels are both 1\n", "    X = X.reshape((1, 1) + X.shape)\n", "    Y = conv2d(X)\n", "    # Strip the first two dimensions: examples and channels\n", "    return Y.reshape(Y.shape[2:])\n", "\n", "# 1 row and column is padded on either side, so a total of 2 rows or columns\n", "# are added\n", "conv2d = nn.LazyConv2d(1, kernel_size=3, padding=1)\n", "X = torch.rand(size=(8, 8))\n", "comp_conv2d(conv2d, X).shape"]}, {"cell_type": "markdown", "id": "dbbcc821", "metadata": {"origin_pos": 11}, "source": ["When the height and width of the convolution kernel are different,\n", "we can make the output and input have the same height and width\n", "by [**setting different padding numbers for height and width.**]\n"]}, {"cell_type": "code", "execution_count": 3, "id": "1aa91aee", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:16.215455Z", "iopub.status.busy": "2023-08-18T19:43:16.214828Z", "iopub.status.idle": "2023-08-18T19:43:16.221907Z", "shell.execute_reply": "2023-08-18T19:43:16.221110Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([8, 8])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# We use a convolution kernel with height 5 and width 3. The padding on either\n", "# side of the height and width are 2 and 1, respectively\n", "conv2d = nn.LazyConv2d(1, kernel_size=(5, 3), padding=(2, 1))\n", "comp_conv2d(conv2d, X).shape"]}, {"cell_type": "markdown", "id": "8cad6e56", "metadata": {"origin_pos": 16}, "source": ["## Stride\n", "\n", "When computing the cross-correlation,\n", "we start with the convolution window\n", "at the upper-left corner of the input tensor,\n", "and then slide it over all locations both down and to the right.\n", "In the previous examples, we defaulted to sliding one element at a time.\n", "However, sometimes, either for computational efficiency\n", "or because we wish to downsample,\n", "we move our window more than one element at a time,\n", "skipping the intermediate locations. This is particularly useful if the convolution \n", "kernel is large since it captures a large area of the underlying image.\n", "\n", "We refer to the number of rows and columns traversed per slide as *stride*.\n", "So far, we have used strides of 1, both for height and width.\n", "Sometimes, we may want to use a larger stride.\n", ":numref:`img_conv_stride` shows a two-dimensional cross-correlation operation\n", "with a stride of 3 vertically and 2 horizontally.\n", "The shaded portions are the output elements as well as the input and kernel tensor elements used for the output computation: $0\\times0+0\\times1+1\\times2+2\\times3=8$, $0\\times0+6\\times1+0\\times2+0\\times3=6$.\n", "We can see that when the second element of the first column is generated,\n", "the convolution window slides down three rows.\n", "The convolution window slides two columns to the right\n", "when the second element of the first row is generated.\n", "When the convolution window continues to slide two columns to the right on the input,\n", "there is no output because the input element cannot fill the window\n", "(unless we add another column of padding).\n", "\n", "![Cross-correlation with strides of 3 and 2 for height and width, respectively.](../img/conv-stride.svg)\n", ":label:`img_conv_stride`\n", "\n", "In general, when the stride for the height is $s_\\textrm{h}$\n", "and the stride for the width is $s_\\textrm{w}$, the output shape is\n", "\n", "$$\\lfloor(n_\\textrm{h}-k_\\textrm{h}+p_\\textrm{h}+s_\\textrm{h})/s_\\textrm{h}\\rfloor \\times \\lfloor(n_\\textrm{w}-k_\\textrm{w}+p_\\textrm{w}+s_\\textrm{w})/s_\\textrm{w}\\rfloor.$$\n", "\n", "If we set $p_\\textrm{h}=k_\\textrm{h}-1$ and $p_\\textrm{w}=k_\\textrm{w}-1$,\n", "then the output shape can be simplified to\n", "$\\lfloor(n_\\textrm{h}+s_\\textrm{h}-1)/s_\\textrm{h}\\rfloor \\times \\lfloor(n_\\textrm{w}+s_\\textrm{w}-1)/s_\\textrm{w}\\rfloor$.\n", "Going a step further, if the input height and width\n", "are divisible by the strides on the height and width,\n", "then the output shape will be $(n_\\textrm{h}/s_\\textrm{h}) \\times (n_\\textrm{w}/s_\\textrm{w})$.\n", "\n", "Below, we [**set the strides on both the height and width to 2**],\n", "thus halving the input height and width.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "cc9ed33d", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:16.225546Z", "iopub.status.busy": "2023-08-18T19:43:16.225010Z", "iopub.status.idle": "2023-08-18T19:43:16.232355Z", "shell.execute_reply": "2023-08-18T19:43:16.231524Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([4, 4])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["conv2d = nn.LazyConv2d(1, kernel_size=3, padding=1, stride=2)\n", "comp_conv2d(conv2d, X).shape"]}, {"cell_type": "markdown", "id": "fe567d48", "metadata": {"origin_pos": 21}, "source": ["Let's look at (**a slightly more complicated example**).\n"]}, {"cell_type": "code", "execution_count": 5, "id": "530a0750", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:16.235915Z", "iopub.status.busy": "2023-08-18T19:43:16.235234Z", "iopub.status.idle": "2023-08-18T19:43:16.243134Z", "shell.execute_reply": "2023-08-18T19:43:16.242281Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 2])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["conv2d = nn.LazyConv2d(1, kernel_size=(3, 5), padding=(0, 1), stride=(3, 4))\n", "comp_conv2d(conv2d, X).shape"]}, {"cell_type": "markdown", "id": "d1bac70f", "metadata": {"origin_pos": 26}, "source": ["## Summary and Discussion\n", "\n", "Padding can increase the height and width of the output. This is often used to give the output the same height and width as the input to avoid undesirable shrinkage of the output. Moreover, it ensures that all pixels are used equally frequently. Typically we pick symmetric padding on both sides of the input height and width. In this case we refer to $(p_\\textrm{h}, p_\\textrm{w})$ padding. Most commonly we set $p_\\textrm{h} = p_\\textrm{w}$, in which case we simply state that we choose padding $p$. \n", "\n", "A similar convention applies to strides. When horizontal stride $s_\\textrm{h}$ and vertical stride $s_\\textrm{w}$ match, we simply talk about stride $s$. The stride can reduce the resolution of the output, for example reducing the height and width of the output to only $1/n$ of the height and width of the input for $n > 1$. By default, the padding is 0 and the stride is 1. \n", "\n", "So far all padding that we discussed simply extended images with zeros. This has significant computational benefit since it is trivial to accomplish. Moreover, operators can be engineered to take advantage of this padding implicitly without the need to allocate additional memory. At the same time, it allows CNNs to encode implicit position information within an image, simply by learning where the \"whitespace\" is. There are many alternatives to zero-padding. :citet:`Alsallakh.Kokhlikyan.Miglani.ea.2020` provided an extensive overview of those (albeit without a clear case for when to use nonzero paddings unless artifacts occur). \n", "\n", "\n", "## Exercises\n", "\n", "1. Given the final code example in this section with kernel size $(3, 5)$, padding $(0, 1)$, and stride $(3, 4)$, \n", "   calculate the output shape to check if it is consistent with the experimental result.\n", "1. For audio signals, what does a stride of 2 correspond to?\n", "1. Implement mirror padding, i.e., padding where the border values are simply mirrored to extend tensors. \n", "1. What are the computational benefits of a stride larger than 1?\n", "1. What might be statistical benefits of a stride larger than 1?\n", "1. How would you implement a stride of $\\frac{1}{2}$? What does it correspond to? When would this be useful?\n"]}, {"cell_type": "markdown", "id": "49d246dc", "metadata": {"origin_pos": 28, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/68)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "f3986c79", "metadata": {"origin_pos": 0}, "source": ["# From Fully Connected Layers to Convolutions\n", ":label:`sec_why-conv`\n", "\n", "To this day,\n", "the models that we have discussed so far\n", "remain appropriate options\n", "when we are dealing with tabular data.\n", "By tabular, we mean that the data consist\n", "of rows corresponding to examples\n", "and columns corresponding to features.\n", "With tabular data, we might anticipate\n", "that the patterns we seek could involve\n", "interactions among the features,\n", "but we do not assume any structure *a priori*\n", "concerning how the features interact.\n", "\n", "Sometimes, we truly lack the knowledge to be able to guide the construction of fancier architectures.\n", "In these cases, an MLP\n", "may be the best that we can do.\n", "However, for high-dimensional perceptual data,\n", "such structureless networks can grow unwieldy.\n", "\n", "For instance, let's return to our running example\n", "of distinguishing cats from dogs.\n", "Say that we do a thorough job in data collection,\n", "collecting an annotated dataset of one-megapixel photographs.\n", "This means that each input to the network has one million dimensions.\n", "Even an aggressive reduction to one thousand hidden dimensions\n", "would require a fully connected layer\n", "characterized by $10^6 \\times 10^3 = 10^9$ parameters.\n", "Unless we have lots of GPUs, a talent\n", "for distributed optimization,\n", "and an extraordinary amount of patience,\n", "learning the parameters of this network\n", "may turn out to be infeasible.\n", "\n", "A careful reader might object to this argument\n", "on the basis that one megapixel resolution may not be necessary.\n", "However, while we might be able\n", "to get away with one hundred thousand pixels,\n", "our hidden layer of size 1000 grossly underestimates\n", "the number of hidden units that it takes\n", "to learn good representations of images,\n", "so a practical system will still require billions of parameters.\n", "Moreover, learning a classifier by fitting so many parameters\n", "might require collecting an enormous dataset.\n", "And yet today both humans and computers are able\n", "to distinguish cats from dogs quite well,\n", "seemingly contradicting these intuitions.\n", "That is because images exhibit rich structure\n", "that can be exploited by humans\n", "and machine learning models alike.\n", "Convolutional neural networks (CNNs) are one creative way\n", "that machine learning has embraced for exploiting\n", "some of the known structure in natural images.\n", "\n", "\n", "## Invariance\n", "\n", "Imagine that we want to detect an object in an image.\n", "It seems reasonable that whatever method\n", "we use to recognize objects should not be overly concerned\n", "with the precise location of the object in the image.\n", "Ideally, our system should exploit this knowledge.\n", "Pigs usually do not fly and planes usually do not swim.\n", "Nonetheless, we should still recognize\n", "a pig were one to appear at the top of the image.\n", "We can draw some inspiration here\n", "from the children's game \"Where's Waldo\"\n", "(which itself has inspired many real-life imitations, such as that depicted in :numref:`img_waldo`).\n", "The game consists of a number of chaotic scenes\n", "bursting with activities.\n", "<PERSON><PERSON> shows up somewhere in each,\n", "typically lurking in some unlikely location.\n", "The reader's goal is to locate him.\n", "Despite his characteristic outfit,\n", "this can be surprisingly difficult,\n", "due to the large number of distractions.\n", "However, *what <PERSON><PERSON> looks like*\n", "does not depend upon *where <PERSON><PERSON> is located*.\n", "We could sweep the image with a <PERSON>aldo detector\n", "that could assign a score to each patch,\n", "indicating the likelihood that the patch contains <PERSON><PERSON>. \n", "In fact, many object detection and segmentation algorithms \n", "are based on this approach :cite:`Long.Shelhamer.Darrell.2015`. \n", "CNNs systematize this idea of *spatial invariance*,\n", "exploiting it to learn useful representations\n", "with fewer parameters.\n", "\n", "![Can you find <PERSON><PERSON> (image courtesy of <PERSON> (Infomatique))?](../img/waldo-football.jpg)\n", ":width:`400px`\n", ":label:`img_waldo`\n", "\n", "We can now make these intuitions more concrete \n", "by enumerating a few desiderata to guide our design\n", "of a neural network architecture suitable for computer vision:\n", "\n", "1. In the earliest layers, our network\n", "   should respond similarly to the same patch,\n", "   regardless of where it appears in the image. This principle is called *translation invariance* (or *translation equivariance*).\n", "1. The earliest layers of the network should focus on local regions,\n", "   without regard for the contents of the image in distant regions. This is the *locality* principle.\n", "   Eventually, these local representations can be aggregated\n", "   to make predictions at the whole image level.\n", "1. As we proceed, deeper layers should be able to capture longer-range features of the \n", "   image, in a way similar to higher level vision in nature. \n", "\n", "Let's see how this translates into mathematics.\n", "\n", "\n", "## Constraining the MLP\n", "\n", "To start off, we can consider an MLP\n", "with two-dimensional images $\\mathbf{X}$ as inputs\n", "and their immediate hidden representations\n", "$\\mathbf{H}$ similarly represented as matrices (they are two-dimensional tensors in code), where both $\\mathbf{X}$ and $\\mathbf{H}$ have the same shape.\n", "Let that sink in.\n", "We now imagine that not only the inputs but\n", "also the hidden representations possess spatial structure.\n", "\n", "Let $[\\mathbf{X}]_{i, j}$ and $[\\mathbf{H}]_{i, j}$ denote the pixel\n", "at location $(i,j)$\n", "in the input image and hidden representation, respectively.\n", "Consequently, to have each of the hidden units\n", "receive input from each of the input pixels,\n", "we would switch from using weight matrices\n", "(as we did previously in MLPs)\n", "to representing our parameters\n", "as fourth-order weight tensors $\\mathsf{W}$.\n", "Suppose that $\\mathbf{U}$ contains biases,\n", "we could formally express the fully connected layer as\n", "\n", "$$\\begin{aligned} \\left[\\mathbf{H}\\right]_{i, j} &= [\\mathbf{U}]_{i, j} + \\sum_k \\sum_l[\\mathsf{W}]_{i, j, k, l}  [\\mathbf{X}]_{k, l}\\\\ &=  [\\mathbf{U}]_{i, j} +\n", "\\sum_a \\sum_b [\\mathsf{V}]_{i, j, a, b}  [\\mathbf{X}]_{i+a, j+b}.\\end{aligned}$$\n", "\n", "The switch from $\\mathsf{W}$ to $\\mathsf{V}$ is entirely cosmetic for now\n", "since there is a one-to-one correspondence\n", "between coefficients in both fourth-order tensors.\n", "We simply re-index the subscripts $(k, l)$\n", "such that $k = i+a$ and $l = j+b$.\n", "In other words, we set $[\\mathsf{V}]_{i, j, a, b} = [\\mathsf{W}]_{i, j, i+a, j+b}$.\n", "The indices $a$ and $b$ run over both positive and negative offsets,\n", "covering the entire image.\n", "For any given location ($i$, $j$) in the hidden representation $[\\mathbf{H}]_{i, j}$,\n", "we compute its value by summing over pixels in $x$,\n", "centered around $(i, j)$ and weighted by $[\\mathsf{V}]_{i, j, a, b}$. Before we carry on, let's consider the total number of parameters required for a *single* layer in this parametrization: a $1000 \\times 1000$ image (1 megapixel) is mapped to a $1000 \\times 1000$ hidden representation. This requires $10^{12}$ parameters, far beyond what computers currently can handle.  \n", "\n", "### Translation Invariance\n", "\n", "Now let's invoke the first principle\n", "established above: translation invariance :cite:`Zhang.ea.1988`.\n", "This implies that a shift in the input $\\mathbf{X}$\n", "should simply lead to a shift in the hidden representation $\\mathbf{H}$.\n", "This is only possible if $\\mathsf{V}$ and $\\mathbf{U}$ do not actually depend on $(i, j)$. As such,\n", "we have $[\\mathsf{V}]_{i, j, a, b} = [\\mathbf{V}]_{a, b}$ and $\\mathbf{U}$ is a constant, say $u$.\n", "As a result, we can simplify the definition for $\\mathbf{H}$:\n", "\n", "$$[\\mathbf{H}]_{i, j} = u + \\sum_a\\sum_b [\\mathbf{V}]_{a, b}  [\\mathbf{X}]_{i+a, j+b}.$$\n", "\n", "\n", "This is a *convolution*!\n", "We are effectively weighting pixels at $(i+a, j+b)$\n", "in the vicinity of location $(i, j)$ with coefficients $[\\mathbf{V}]_{a, b}$\n", "to obtain the value $[\\mathbf{H}]_{i, j}$.\n", "Note that $[\\mathbf{V}]_{a, b}$ needs many fewer coefficients than $[\\mathsf{V}]_{i, j, a, b}$ since it\n", "no longer depends on the location within the image. Consequently, the number of parameters required is no longer $10^{12}$ but a much more reasonable $4 \\times 10^6$: we still have the dependency on $a, b \\in (-1000, 1000)$. In short, we have made significant progress. Time-delay neural networks (TDNNs) are some of the first examples to exploit this idea :cite:`Waibel.Hanazawa.Hinton.ea.1989`.\n", "\n", "###  Locality\n", "\n", "Now let's invoke the second principle: locality.\n", "As motivated above, we believe that we should not have\n", "to look very far away from location $(i, j)$\n", "in order to glean relevant information\n", "to assess what is going on at $[\\mathbf{H}]_{i, j}$.\n", "This means that outside some range $|a|> \\Delta$ or $|b| > \\Delta$,\n", "we should set $[\\mathbf{V}]_{a, b} = 0$.\n", "Equivalently, we can rewrite $[\\mathbf{H}]_{i, j}$ as\n", "\n", "$$[\\mathbf{H}]_{i, j} = u + \\sum_{a = -\\Delta}^{\\Delta} \\sum_{b = -\\Delta}^{\\Delta} [\\mathbf{V}]_{a, b}  [\\mathbf{X}]_{i+a, j+b}.$$\n", ":eqlabel:`eq_conv-layer`\n", "\n", "This reduces the number of parameters from $4 \\times 10^6$ to $4 \\Delta^2$, where $\\Delta$ is typically smaller than $10$. As such, we reduced the number of parameters by another four orders of magnitude. Note that :eqref:`eq_conv-layer`, is what is called, in a nutshell, a *convolutional layer*. \n", "*Convolutional neural networks* (CNNs)\n", "are a special family of neural networks that contain convolutional layers.\n", "In the deep learning research community,\n", "$\\mathbf{V}$ is referred to as a *convolution kernel*,\n", "a *filter*, or simply the layer's *weights* that are learnable parameters.\n", "\n", "While previously, we might have required billions of parameters\n", "to represent just a single layer in an image-processing network,\n", "we now typically need just a few hundred, without\n", "altering the dimensionality of either\n", "the inputs or the hidden representations.\n", "The price paid for this drastic reduction in parameters\n", "is that our features are now translation invariant\n", "and that our layer can only incorporate local information,\n", "when determining the value of each hidden activation.\n", "All learning depends on imposing inductive bias.\n", "When that bias agrees with reality,\n", "we get sample-efficient models\n", "that generalize well to unseen data.\n", "But of course, if those biases do not agree with reality,\n", "e.g., if images turned out not to be translation invariant,\n", "our models might struggle even to fit our training data.\n", "\n", "This dramatic reduction in parameters brings us to our last desideratum, \n", "namely that deeper layers should represent larger and more complex aspects \n", "of an image. This can be achieved by interleaving nonlinearities and convolutional \n", "layers repeatedly. \n", "\n", "## Convolutions\n", "\n", "Let's briefly review why :eqref:`eq_conv-layer` is called a convolution. \n", "In mathematics, the *convolution* between two functions :cite:`<PERSON><PERSON><PERSON>.1973`,\n", "say $f, g: \\mathbb{R}^d \\to \\mathbb{R}$ is defined as\n", "\n", "$$(f * g)(\\mathbf{x}) = \\int f(\\mathbf{z}) g(\\mathbf{x}-\\mathbf{z}) d\\mathbf{z}.$$\n", "\n", "That is, we measure the overlap between $f$ and $g$\n", "when one function is \"flipped\" and shifted by $\\mathbf{x}$.\n", "Whenever we have discrete objects, the integral turns into a sum.\n", "For instance, for vectors from\n", "the set of square-summable infinite-dimensional vectors\n", "with index running over $\\mathbb{Z}$ we obtain the following definition:\n", "\n", "$$(f * g)(i) = \\sum_a f(a) g(i-a).$$\n", "\n", "For two-dimensional tensors, we have a corresponding sum\n", "with indices $(a, b)$ for $f$ and $(i-a, j-b)$ for $g$, respectively:\n", "\n", "$$(f * g)(i, j) = \\sum_a\\sum_b f(a, b) g(i-a, j-b).$$\n", ":eqlabel:`eq_2d-conv-discrete`\n", "\n", "This looks similar to :eqref:`eq_conv-layer`, with one major difference.\n", "Rather than using $(i+a, j+b)$, we are using the difference instead.\n", "Note, though, that this distinction is mostly cosmetic\n", "since we can always match the notation between\n", ":eqref:`eq_conv-layer` and :eqref:`eq_2d-conv-discrete`.\n", "Our original definition in :eqref:`eq_conv-layer` more properly\n", "describes a *cross-correlation*.\n", "We will come back to this in the following section.\n", "\n", "\n", "## Channels\n", ":label:`subsec_why-conv-channels`\n", "\n", "Returning to our <PERSON>aldo detector, let's see what this looks like.\n", "The convolutional layer picks windows of a given size\n", "and weighs intensities according to the filter $\\mathsf{V}$, as demonstrated in :numref:`fig_waldo_mask`.\n", "We might aim to learn a model so that\n", "wherever the \"waldoness\" is highest,\n", "we should find a peak in the hidden layer representations.\n", "\n", "![<PERSON><PERSON><PERSON> (image courtesy of <PERSON> (Infomatique)).](../img/waldo-mask.jpg)\n", ":width:`400px`\n", ":label:`fig_waldo_mask`\n", "\n", "There is just one problem with this approach.\n", "So far, we blissfully ignored that images consist\n", "of three channels: red, green, and blue. \n", "In sum, images are not two-dimensional objects\n", "but rather third-order tensors,\n", "characterized by a height, width, and channel,\n", "e.g., with shape $1024 \\times 1024 \\times 3$ pixels. \n", "While the first two of these axes concern spatial relationships,\n", "the third can be regarded as assigning\n", "a multidimensional representation to each pixel location.\n", "We thus index $\\mathsf{X}$ as $[\\mathsf{X}]_{i, j, k}$.\n", "The convolutional filter has to adapt accordingly.\n", "Instead of $[\\mathbf{V}]_{a,b}$, we now have $[\\mathsf{V}]_{a,b,c}$.\n", "\n", "Moreover, just as our input consists of a third-order tensor,\n", "it turns out to be a good idea to similarly formulate\n", "our hidden representations as third-order tensors $\\mathsf{H}$.\n", "In other words, rather than just having a single hidden representation\n", "corresponding to each spatial location,\n", "we want an entire vector of hidden representations\n", "corresponding to each spatial location.\n", "We could think of the hidden representations as comprising\n", "a number of two-dimensional grids stacked on top of each other.\n", "As in the inputs, these are sometimes called *channels*.\n", "They are also sometimes called *feature maps*,\n", "as each provides a spatialized set\n", "of learned features for the subsequent layer.\n", "Intuitively, you might imagine that at lower layers that are closer to inputs,\n", "some channels could become specialized to recognize edges while\n", "others could recognize textures.\n", "\n", "To support multiple channels in both inputs ($\\mathsf{X}$) and hidden representations ($\\mathsf{H}$),\n", "we can add a fourth coordinate to $\\mathsf{V}$: $[\\mathsf{V}]_{a, b, c, d}$.\n", "Putting everything together we have:\n", "\n", "$$[\\mathsf{H}]_{i,j,d} = \\sum_{a = -\\Delta}^{\\Delta} \\sum_{b = -\\Delta}^{\\Delta} \\sum_c [\\mathsf{V}]_{a, b, c, d} [\\mathsf{X}]_{i+a, j+b, c},$$\n", ":eqlabel:`eq_conv-layer-channels`\n", "\n", "where $d$ indexes the output channels in the hidden representations $\\mathsf{H}$. The subsequent convolutional layer will go on to take a third-order tensor, $\\mathsf{H}$, as input.\n", "We take\n", ":eqref:`eq_conv-layer-channels`,\n", "because of its generality, as\n", "the definition of a convolutional layer for multiple channels, where $\\mathsf{V}$ is a kernel or filter of the layer.\n", "\n", "There are still many operations that we need to address.\n", "For instance, we need to figure out how to combine all the hidden representations\n", "to a single output, e.g., whether there is a Waldo *anywhere* in the image.\n", "We also need to decide how to compute things efficiently,\n", "how to combine multiple layers,\n", "appropriate activation functions,\n", "and how to make reasonable design choices\n", "to yield networks that are effective in practice.\n", "We turn to these issues in the remainder of the chapter.\n", "\n", "## Summary and Discussion\n", "\n", "In this section we derived the structure of convolutional neural networks from first principles. While it is unclear whether this was the route taken to the invention of CNNs, it is satisfying to know that they are the *right* choice when applying reasonable principles to how image processing and computer vision algorithms should operate, at least at lower levels. In particular, translation invariance in images implies that all patches of an image will be treated in the same manner. Locality means that only a small neighborhood of pixels will be used to compute the corresponding hidden representations. Some of the earliest references to CNNs are in the form of the Neocognitron :cite:`Fukushima.1982`. \n", "\n", "A second principle that we encountered in our reasoning is how to reduce the number of parameters in a function class without limiting its expressive power, at least, whenever certain assumptions on the model hold. We saw a dramatic reduction of complexity as a result of this restriction, turning computationally and statistically infeasible problems into tractable models. \n", "\n", "Adding channels allowed us to bring back some of the complexity that was lost due to the restrictions imposed on the convolutional kernel by locality and translation invariance. Note that it is quite natural to add channels other than just red, green, and blue. Many satellite \n", "images, in particular for agriculture and meteorology, have tens to hundreds of channels, \n", "generating hyperspectral images instead. They report data on many different wavelengths. In the following we will see how to use convolutions effectively to manipulate the dimensionality of the images they operate on, how to move from location-based to channel-based representations, and how to deal with large numbers of categories efficiently. \n", "\n", "## Exercises\n", "\n", "1. Assume that the size of the convolution kernel is $\\Delta = 0$.\n", "   Show that in this case the convolution kernel\n", "   implements an MLP independently for each set of channels. This leads to the Network in Network \n", "   architectures :cite:<PERSON><PERSON><PERSON>.<PERSON>.2013`. \n", "1. Audio data is often represented as a one-dimensional sequence. \n", "    1. When might you want to impose locality and translation invariance for audio? \n", "    1. Derive the convolution operations for audio.\n", "    1. Can you treat audio using the same tools as computer vision? Hint: use the spectrogram.\n", "1. Why might translation invariance not be a good idea after all? Give an example. \n", "1. Do you think that convolutional layers might also be applicable for text data?\n", "   Which problems might you encounter with language?\n", "1. What happens with convolutions when an object is at the boundary of an image?\n", "1. Prove that the convolution is symmetric, i.e., $f * g = g * f$.\n", "\n", "[Discussions](https://discuss.d2l.ai/t/64)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
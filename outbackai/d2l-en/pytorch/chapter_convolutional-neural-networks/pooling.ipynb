{"cells": [{"cell_type": "markdown", "id": "57249b96", "metadata": {"origin_pos": 1}, "source": ["# Pooling\n", ":label:`sec_pooling`\n", "\n", "In many cases our ultimate task asks some global question about the image,\n", "e.g., *does it contain a cat?* Consequently, the units of our final layer \n", "should be sensitive to the entire input.\n", "By gradually aggregating information, yielding coarser and coarser maps,\n", "we accomplish this goal of ultimately learning a global representation,\n", "while keeping all of the advantages of convolutional layers at the intermediate layers of processing.\n", "The deeper we go in the network,\n", "the larger the receptive field (relative to the input)\n", "to which each hidden node is sensitive. Reducing spatial resolution \n", "accelerates this process, \n", "since the convolution kernels cover a larger effective area. \n", "\n", "Moreover, when detecting lower-level features, such as edges\n", "(as discussed in :numref:`sec_conv_layer`),\n", "we often want our representations to be somewhat invariant to translation.\n", "For instance, if we take the image `X`\n", "with a sharp delineation between black and white\n", "and shift the whole image by one pixel to the right,\n", "i.e., `Z[i, j] = X[i, j + 1]`,\n", "then the output for the new image `Z` might be vastly different.\n", "The edge will have shifted by one pixel.\n", "In reality, objects hardly ever occur exactly at the same place.\n", "In fact, even with a tripod and a stationary object,\n", "vibration of the camera due to the movement of the shutter\n", "might shift everything by a pixel or so\n", "(high-end cameras are loaded with special features to address this problem).\n", "\n", "This section introduces *pooling layers*,\n", "which serve the dual purposes of\n", "mitigating the sensitivity of convolutional layers to location\n", "and of spatially downsampling representations.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "1d620804", "metadata": {"origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "b3e7dfcb", "metadata": {"origin_pos": 5}, "source": ["## Maximum Pooling and Average Pooling\n", "\n", "Like convolutional layers, *pooling* operators\n", "consist of a fixed-shape window that is slid over\n", "all regions in the input according to its stride,\n", "computing a single output for each location traversed\n", "by the fixed-shape window (sometimes known as the *pooling window*).\n", "However, unlike the cross-correlation computation\n", "of the inputs and kernels in the convolutional layer,\n", "the pooling layer contains no parameters (there is no *kernel*).\n", "Instead, pooling operators are deterministic,\n", "typically calculating either the maximum or the average value\n", "of the elements in the pooling window.\n", "These operations are called *maximum pooling* (*max-pooling* for short)\n", "and *average pooling*, respectively.\n", "\n", "*Average pooling* is essentially as old as CNNs. The idea is akin to \n", "downsampling an image. Rather than just taking the value of every second (or third) \n", "pixel for the lower resolution image, we can average over adjacent pixels to obtain \n", "an image with better signal-to-noise ratio since we are combining the information \n", "from multiple adjacent pixels. *Max-pooling* was introduced in \n", ":citet:`Riesenhuber.Poggio.1999` in the context of cognitive neuroscience to describe \n", "how information aggregation might be aggregated hierarchically for the purpose \n", "of object recognition; there already was an earlier version in speech recognition :cite:`<PERSON><PERSON><PERSON>.Sakamoto.Akabane.ea.1990`. In almost all cases, max-pooling, as it is also referred to, \n", "is preferable to average pooling. \n", "\n", "In both cases, as with the cross-correlation operator,\n", "we can think of the pooling window\n", "as starting from the upper-left of the input tensor\n", "and sliding across it from left to right and top to bottom.\n", "At each location that the pooling window hits,\n", "it computes the maximum or average\n", "value of the input subtensor in the window,\n", "depending on whether max or average pooling is employed.\n", "\n", "\n", "![Max-pooling with a pooling window shape of $2\\times 2$. The shaded portions are the first output element as well as the input tensor elements used for the output computation: $\\max(0, 1, 3, 4)=4$.](../img/pooling.svg)\n", ":label:`fig_pooling`\n", "\n", "The output tensor in :numref:`fig_pooling`  has a height of 2 and a width of 2.\n", "The four elements are derived from the maximum value in each pooling window:\n", "\n", "$$\n", "\\max(0, 1, 3, 4)=4,\\\\\n", "\\max(1, 2, 4, 5)=5,\\\\\n", "\\max(3, 4, 6, 7)=7,\\\\\n", "\\max(4, 5, 7, 8)=8.\\\\\n", "$$\n", "\n", "More generally, we can define a $p \\times q$ pooling layer by aggregating over \n", "a region of said size. Returning to the problem of edge detection, \n", "we use the output of the convolutional layer\n", "as input for $2\\times 2$ max-pooling.\n", "Denote by `X` the input of the convolutional layer input and `Y` the pooling layer output. \n", "Regardless of whether or not the values of `X[i, j]`, `X[i, j + 1]`, \n", "`X[i+1, j]` and `X[i+1, j + 1]` are different,\n", "the pooling layer always outputs `Y[i, j] = 1`.\n", "That is to say, using the $2\\times 2$ max-pooling layer,\n", "we can still detect if the pattern recognized by the convolutional layer\n", "moves no more than one element in height or width.\n", "\n", "In the code below, we (**implement the forward propagation\n", "of the pooling layer**) in the `pool2d` function.\n", "This function is similar to the `corr2d` function\n", "in :numref:`sec_conv_layer`.\n", "However, no kernel is needed, computing the output\n", "as either the maximum or the average of each region in the input.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "b6b758a3", "metadata": {"origin_pos": 6, "tab": ["pytorch"]}, "outputs": [], "source": ["def pool2d(X, pool_size, mode='max'):\n", "    p_h, p_w = pool_size\n", "    Y = torch.zeros((X.shape[0] - p_h + 1, X.shape[1] - p_w + 1))\n", "    for i in range(Y.shape[0]):\n", "        for j in range(Y.shape[1]):\n", "            if mode == 'max':\n", "                Y[i, j] = X[i: i + p_h, j: j + p_w].max()\n", "            elif mode == 'avg':\n", "                Y[i, j] = X[i: i + p_h, j: j + p_w].mean()\n", "    return Y"]}, {"cell_type": "markdown", "id": "4ff6470c", "metadata": {"origin_pos": 9}, "source": ["We can construct the input tensor `X` in :numref:`fig_pooling` to [**validate the output of the two-dimensional max-pooling layer**].\n"]}, {"cell_type": "code", "execution_count": 11, "id": "8fcb17f0", "metadata": {"origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[4., 5.],\n", "        [7., 8.]])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.tensor([[0.0, 1.0, 2.0], [3.0, 4.0, 5.0], [6.0, 7.0, 8.0]])\n", "pool2d(X, (2, 2))"]}, {"cell_type": "markdown", "id": "2660d3f7", "metadata": {"origin_pos": 11}, "source": ["Also, we can experiment with (**the average pooling layer**).\n"]}, {"cell_type": "code", "execution_count": 12, "id": "db997aec", "metadata": {"origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[2., 3.],\n", "        [5., 6.]])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["pool2d(X, (2, 2), 'avg')"]}, {"cell_type": "markdown", "id": "12a5812d", "metadata": {"origin_pos": 13}, "source": ["## [**Padding and Stride**]\n", "\n", "As with convolutional layers, pooling layers\n", "change the output shape.\n", "And as before, we can adjust the operation to achieve a desired output shape\n", "by padding the input and adjusting the stride.\n", "We can demonstrate the use of padding and strides\n", "in pooling layers via the built-in two-dimensional max-pooling layer from the deep learning framework.\n", "We first construct an input tensor `X` whose shape has four dimensions,\n", "where the number of examples (batch size) and number of channels are both 1.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "163fc8d6", "metadata": {"origin_pos": 15, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[[[ 0.,  1.,  2.,  3.],\n", "          [ 4.,  5.,  6.,  7.],\n", "          [ 8.,  9., 10., 11.],\n", "          [12., 13., 14., 15.]]]])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.arange(16, dtype=torch.float32).reshape((1, 1, 4, 4))\n", "X"]}, {"cell_type": "markdown", "id": "613a9612", "metadata": {"origin_pos": 17}, "source": ["Since pooling aggregates information from an area, (**deep learning frameworks default to matching pooling window sizes and stride.**) For instance, if we use a pooling window of shape `(3, 3)`\n", "we get a stride shape of `(3, 3)` by default.\n"]}, {"cell_type": "code", "execution_count": 14, "id": "bc286034", "metadata": {"origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[[[10.]]]])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["pool2d = nn.MaxPool2d(3)\n", "# Pooling has no model parameters, hence it needs no initialization\n", "pool2d(X)"]}, {"cell_type": "markdown", "id": "02120224", "metadata": {"origin_pos": 22}, "source": ["Needless to say, [**the stride and padding can be manually specified**] to override framework defaults if required.\n"]}, {"cell_type": "code", "execution_count": 15, "id": "ca0c78a7", "metadata": {"origin_pos": 24, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[[[ 5.,  7.],\n", "          [13., 15.]]]])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["pool2d = nn.MaxPool2d(3, padding=1, stride=2)\n", "pool2d(X)"]}, {"cell_type": "markdown", "id": "c4474180", "metadata": {"origin_pos": 27}, "source": ["Of course, we can specify an arbitrary rectangular pooling window with arbitrary height and width respectively, as the example below shows.\n"]}, {"cell_type": "code", "execution_count": 16, "id": "69b31fea", "metadata": {"origin_pos": 29, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[[[ 5.,  7.],\n", "          [13., 15.]]]])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["pool2d = nn.MaxPool2d((2, 3), stride=(2, 3), padding=(0, 1))\n", "pool2d(X)"]}, {"cell_type": "markdown", "id": "61da4755", "metadata": {"origin_pos": 32}, "source": ["## Multiple Channels\n", "\n", "When processing multi-channel input data,\n", "[**the pooling layer pools each input channel separately**],\n", "rather than summing the inputs up over channels\n", "as in a convolutional layer.\n", "This means that the number of output channels for the pooling layer\n", "is the same as the number of input channels.\n", "Below, we will concatenate tensors `X` and `X + 1`\n", "on the channel dimension to construct an input with two channels.\n"]}, {"cell_type": "code", "execution_count": 17, "id": "e54e620e", "metadata": {"origin_pos": 34, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[[[ 0.,  1.,  2.,  3.],\n", "          [ 4.,  5.,  6.,  7.],\n", "          [ 8.,  9., 10., 11.],\n", "          [12., 13., 14., 15.]],\n", "\n", "         [[ 1.,  2.,  3.,  4.],\n", "          [ 5.,  6.,  7.,  8.],\n", "          [ 9., 10., 11., 12.],\n", "          [13., 14., 15., 16.]]]])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.cat((X, X + 1), 1)\n", "X"]}, {"cell_type": "markdown", "id": "d3283101", "metadata": {"origin_pos": 36}, "source": ["As we can see, the number of output channels is still two after pooling.\n"]}, {"cell_type": "code", "execution_count": 18, "id": "87e7f759", "metadata": {"origin_pos": 38, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[[[ 5.,  7.],\n", "          [13., 15.]],\n", "\n", "         [[ 6.,  8.],\n", "          [14., 16.]]]])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["pool2d = nn.MaxPool2d(3, padding=1, stride=2)\n", "pool2d(X)"]}, {"cell_type": "markdown", "id": "0fd9d3ec", "metadata": {"origin_pos": 42}, "source": ["## Summary\n", "\n", "Pooling is an exceedingly simple operation. It does exactly what its name indicates, aggregate results over a window of values. All convolution semantics, such as strides and padding apply in the same way as they did previously. Note that pooling is indifferent to channels, i.e., it leaves the number of channels unchanged and it applies to each channel separately. Lastly, of the two popular pooling choices, max-pooling is preferable to average pooling, as it confers some degree of invariance to output. A popular choice is to pick a pooling window size of $2 \\times 2$ to quarter the spatial resolution of output. \n", "\n", "Note that there are many more ways of reducing resolution beyond pooling. For instance, in stochastic pooling :cite:<PERSON><PERSON><PERSON><PERSON>.Fergus.2013` and fractional max-pooling :cite:`Graham.2014` aggregation is combined with randomization. This can slightly improve the accuracy in some cases. Lastly, as we will see later with the attention mechanism, there are more refined ways of aggregating over outputs, e.g., by using the alignment between a query and representation vectors. \n", "\n", "\n", "## Exercises\n", "\n", "1. Implement average pooling through a convolution. \n", "1. Prove that max-pooling cannot be implemented through a convolution alone. \n", "1. Max-pooling can be accomplished using ReLU operations, i.e., $\\textrm{ReLU}(x) = \\max(0, x)$.\n", "    1. Express $\\max (a, b)$ by using only ReLU operations.\n", "    1. Use this to implement max-pooling by means of convolutions and ReLU layers. \n", "    1. How many channels and layers do you need for a $2 \\times 2$ convolution? How many for a $3 \\times 3$ convolution?\n", "1. What is the computational cost of the pooling layer? Assume that the input to the pooling layer is of size $c\\times h\\times w$, the pooling window has a shape of $p_\\textrm{h}\\times p_\\textrm{w}$ with a padding of $(p_\\textrm{h}, p_\\textrm{w})$ and a stride of $(s_\\textrm{h}, s_\\textrm{w})$.\n", "1. Why do you expect max-pooling and average pooling to work differently?\n", "1. Do we need a separate minimum pooling layer? Can you replace it with another operation?\n", "1. We could use the softmax operation for pooling. Why might it not be so popular?\n"]}, {"cell_type": "markdown", "id": "8a37fa6a", "metadata": {"origin_pos": 44, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/72)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "4582bdfd", "metadata": {"origin_pos": 1}, "source": ["# Convolutions for Images\n", ":label:`sec_conv_layer`\n", "\n", "Now that we understand how convolutional layers work in theory,\n", "we are ready to see how they work in practice.\n", "Building on our motivation of convolutional neural networks\n", "as efficient architectures for exploring structure in image data,\n", "we stick with images as our running example.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "07444b49", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:01.908966Z", "iopub.status.busy": "2023-08-18T19:41:01.908168Z", "iopub.status.idle": "2023-08-18T19:41:05.382412Z", "shell.execute_reply": "2023-08-18T19:41:05.381355Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "ad7d3200", "metadata": {"origin_pos": 6}, "source": ["## The Cross-Correlation Operation\n", "\n", "Recall that strictly speaking, convolutional layers\n", "are a  misnomer, since the operations they express\n", "are more accurately described as cross-correlations.\n", "Based on our descriptions of convolutional layers in :numref:`sec_why-conv`,\n", "in such a layer, an input tensor\n", "and a kernel tensor are combined\n", "to produce an output tensor through a (**cross-correlation operation.**)\n", "\n", "Let's ignore channels for now and see how this works\n", "with two-dimensional data and hidden representations.\n", "In :numref:`fig_correlation`,\n", "the input is a two-dimensional tensor\n", "with a height of 3 and width of 3.\n", "We mark the shape of the tensor as $3 \\times 3$ or ($3$, $3$).\n", "The height and width of the kernel are both 2.\n", "The shape of the *kernel window* (or *convolution window*)\n", "is given by the height and width of the kernel\n", "(here it is $2 \\times 2$).\n", "\n", "![Two-dimensional cross-correlation operation. The shaded portions are the first output element as well as the input and kernel tensor elements used for the output computation: $0\\times0+1\\times1+3\\times2+4\\times3=19$.](../img/correlation.svg)\n", ":label:`fig_correlation`\n", "\n", "In the two-dimensional cross-correlation operation,\n", "we begin with the convolution window positioned\n", "at the upper-left corner of the input tensor\n", "and slide it across the input tensor,\n", "both from left to right and top to bottom.\n", "When the convolution window slides to a certain position,\n", "the input subtensor contained in that window\n", "and the kernel tensor are multiplied elementwise\n", "and the resulting tensor is summed up\n", "yielding a single scalar value.\n", "This result gives the value of the output tensor\n", "at the corresponding location.\n", "Here, the output tensor has a height of 2 and width of 2\n", "and the four elements are derived from\n", "the two-dimensional cross-correlation operation:\n", "\n", "$$\n", "0\\times0+1\\times1+3\\times2+4\\times3=19,\\\\\n", "1\\times0+2\\times1+4\\times2+5\\times3=25,\\\\\n", "3\\times0+4\\times1+6\\times2+7\\times3=37,\\\\\n", "4\\times0+5\\times1+7\\times2+8\\times3=43.\n", "$$\n", "\n", "Note that along each axis, the output size\n", "is slightly smaller than the input size.\n", "Because the kernel has width and height greater than $1$,\n", "we can only properly compute the cross-correlation\n", "for locations where the kernel fits wholly within the image,\n", "the output size is given by the input size $n_\\textrm{h} \\times n_\\textrm{w}$\n", "minus the size of the convolution kernel $k_\\textrm{h} \\times k_\\textrm{w}$\n", "via\n", "\n", "$$(n_\\textrm{h}-k_\\textrm{h}+1) \\times (n_\\textrm{w}-k_\\textrm{w}+1).$$\n", "\n", "This is the case since we need enough space\n", "to \"shift\" the convolution kernel across the image.\n", "Later we will see how to keep the size unchanged\n", "by padding the image with zeros around its boundary\n", "so that there is enough space to shift the kernel.\n", "Next, we implement this process in the `corr2d` function,\n", "which accepts an input tensor `X` and a kernel tensor `K`\n", "and returns an output tensor `Y`.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "5e550b4f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:05.386933Z", "iopub.status.busy": "2023-08-18T19:41:05.386003Z", "iopub.status.idle": "2023-08-18T19:41:05.393401Z", "shell.execute_reply": "2023-08-18T19:41:05.392355Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["def corr2d(X, K):  #@save\n", "    \"\"\"Compute 2D cross-correlation.\"\"\"\n", "    h, w = K.shape\n", "    Y = torch.zeros((X.shape[0] - h + 1, X.shape[1] - w + 1))\n", "    for i in range(Y.shape[0]):\n", "        for j in range(Y.shape[1]):\n", "            Y[i, j] = (X[i:i + h, j:j + w] * K).sum()\n", "    return Y"]}, {"cell_type": "markdown", "id": "ce6cd64e", "metadata": {"origin_pos": 11}, "source": ["We can construct the input tensor `X` and the kernel tensor `K`\n", "from :numref:`fig_correlation`\n", "to [**validate the output of the above implementation**]\n", "of the two-dimensional cross-correlation operation.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "7845059c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:05.397828Z", "iopub.status.busy": "2023-08-18T19:41:05.397122Z", "iopub.status.idle": "2023-08-18T19:41:05.427898Z", "shell.execute_reply": "2023-08-18T19:41:05.426544Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[19., 25.],\n", "        [37., 43.]])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.tensor([[0.0, 1.0, 2.0], [3.0, 4.0, 5.0], [6.0, 7.0, 8.0]])\n", "K = torch.tensor([[0.0, 1.0], [2.0, 3.0]])\n", "corr2d(X, K)"]}, {"cell_type": "markdown", "id": "2edc81c6", "metadata": {"origin_pos": 13}, "source": ["## Convolutional Layers\n", "\n", "A convolutional layer cross-correlates the input and kernel\n", "and adds a scalar bias to produce an output.\n", "The two parameters of a convolutional layer\n", "are the kernel and the scalar bias.\n", "When training models based on convolutional layers,\n", "we typically initialize the kernels randomly,\n", "just as we would with a fully connected layer.\n", "\n", "We are now ready to [**implement a two-dimensional convolutional layer**]\n", "based on the `corr2d` function defined above.\n", "In the `__init__` constructor method,\n", "we declare `weight` and `bias` as the two model parameters.\n", "The forward propagation method\n", "calls the `corr2d` function and adds the bias.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "74d09a7c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:05.432542Z", "iopub.status.busy": "2023-08-18T19:41:05.431776Z", "iopub.status.idle": "2023-08-18T19:41:05.444669Z", "shell.execute_reply": "2023-08-18T19:41:05.443731Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["class Conv2D(nn.Module):\n", "    def __init__(self, kernel_size):\n", "        super().__init__()\n", "        self.weight = nn.Parameter(torch.rand(kernel_size))\n", "        self.bias = nn.Parameter(torch.zeros(1))\n", "\n", "    def forward(self, x):\n", "        return corr2d(x, self.weight) + self.bias"]}, {"cell_type": "markdown", "id": "1bddb4e9", "metadata": {"origin_pos": 18}, "source": ["In\n", "$h \\times w$ convolution\n", "or an $h \\times w$ convolution kernel,\n", "the height and width of the convolution kernel are $h$ and $w$, respectively.\n", "We also refer to\n", "a convolutional layer with an $h \\times w$\n", "convolution kernel simply as an $h \\times w$ convolutional layer.\n", "\n", "\n", "## Object Edge Detection in Images\n", "\n", "Let's take a moment to parse [**a simple application of a convolutional layer:\n", "detecting the edge of an object in an image**]\n", "by finding the location of the pixel change.\n", "First, we construct an \"image\" of $6\\times 8$ pixels.\n", "The middle four columns are black ($0$) and the rest are white ($1$).\n"]}, {"cell_type": "code", "execution_count": 5, "id": "3b5cdda0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:05.448555Z", "iopub.status.busy": "2023-08-18T19:41:05.447775Z", "iopub.status.idle": "2023-08-18T19:41:05.456977Z", "shell.execute_reply": "2023-08-18T19:41:05.455824Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[1., 1., 0., 0., 0., 0., 1., 1.],\n", "        [1., 1., 0., 0., 0., 0., 1., 1.],\n", "        [1., 1., 0., 0., 0., 0., 1., 1.],\n", "        [1., 1., 0., 0., 0., 0., 1., 1.],\n", "        [1., 1., 0., 0., 0., 0., 1., 1.],\n", "        [1., 1., 0., 0., 0., 0., 1., 1.]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.ones((6, 8))\n", "X[:, 2:6] = 0\n", "X"]}, {"cell_type": "markdown", "id": "ef65b584", "metadata": {"origin_pos": 22}, "source": ["Next, we construct a kernel `K` with a height of 1 and a width of 2.\n", "When we perform the cross-correlation operation with the input,\n", "if the horizontally adjacent elements are the same,\n", "the output is 0. Otherwise, the output is nonzero.\n", "Note that this kernel is a special case of a finite difference operator. At location $(i,j)$ it computes $x_{i,j} - x_{(i+1),j}$, i.e., it computes the difference between the values of horizontally adjacent pixels. This is a discrete approximation of the first derivative in the horizontal direction. After all, for a function $f(i,j)$ its derivative $-\\partial_i f(i,j) = \\lim_{\\epsilon \\to 0} \\frac{f(i,j) - f(i+\\epsilon,j)}{\\epsilon}$. Let's see how this works in practice.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "c64588b6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:05.463518Z", "iopub.status.busy": "2023-08-18T19:41:05.462855Z", "iopub.status.idle": "2023-08-18T19:41:05.467300Z", "shell.execute_reply": "2023-08-18T19:41:05.466485Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [], "source": ["K = torch.tensor([[1.0, -1.0]])"]}, {"cell_type": "markdown", "id": "14945056", "metadata": {"origin_pos": 24}, "source": ["We are ready to perform the cross-correlation operation\n", "with arguments `X` (our input) and `K` (our kernel).\n", "As you can see, [**we detect $1$ for the edge from white to black\n", "and $-1$ for the edge from black to white.**]\n", "All other outputs take value $0$.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "7287547f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:05.472001Z", "iopub.status.busy": "2023-08-18T19:41:05.471414Z", "iopub.status.idle": "2023-08-18T19:41:05.478644Z", "shell.execute_reply": "2023-08-18T19:41:05.477751Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.,  1.,  0.,  0.,  0., -1.,  0.],\n", "        [ 0.,  1.,  0.,  0.,  0., -1.,  0.],\n", "        [ 0.,  1.,  0.,  0.,  0., -1.,  0.],\n", "        [ 0.,  1.,  0.,  0.,  0., -1.,  0.],\n", "        [ 0.,  1.,  0.,  0.,  0., -1.,  0.],\n", "        [ 0.,  1.,  0.,  0.,  0., -1.,  0.]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["Y = corr2d(X, K)\n", "Y"]}, {"cell_type": "markdown", "id": "68f25916", "metadata": {"origin_pos": 26}, "source": ["We can now apply the kernel to the transposed image.\n", "As expected, it vanishes. [**The kernel `K` only detects vertical edges.**]\n"]}, {"cell_type": "code", "execution_count": 8, "id": "c5803e8e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:05.482194Z", "iopub.status.busy": "2023-08-18T19:41:05.481611Z", "iopub.status.idle": "2023-08-18T19:41:05.489355Z", "shell.execute_reply": "2023-08-18T19:41:05.488493Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[0., 0., 0., 0., 0.],\n", "        [0., 0., 0., 0., 0.],\n", "        [0., 0., 0., 0., 0.],\n", "        [0., 0., 0., 0., 0.],\n", "        [0., 0., 0., 0., 0.],\n", "        [0., 0., 0., 0., 0.],\n", "        [0., 0., 0., 0., 0.],\n", "        [0., 0., 0., 0., 0.]])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["corr2d(X.t(), K)"]}, {"cell_type": "markdown", "id": "231b5888", "metadata": {"origin_pos": 28}, "source": ["## Learning a Kernel\n", "\n", "Designing an edge detector by finite differences `[1, -1]` is neat\n", "if we know this is precisely what we are looking for.\n", "However, as we look at larger kernels,\n", "and consider successive layers of convolutions,\n", "it might be impossible to specify\n", "precisely what each filter should be doing manually.\n", "\n", "Now let's see whether we can [**learn the kernel that generated `Y` from `X`**]\n", "by looking at the input--output pairs only.\n", "We first construct a convolutional layer\n", "and initialize its kernel as a random tensor.\n", "Next, in each iteration, we will use the squared error\n", "to compare `Y` with the output of the convolutional layer.\n", "We can then calculate the gradient to update the kernel.\n", "For the sake of simplicity,\n", "in the following\n", "we use the built-in class\n", "for two-dimensional convolutional layers\n", "and ignore the bias.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "cc5935f0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:05.493184Z", "iopub.status.busy": "2023-08-18T19:41:05.492373Z", "iopub.status.idle": "2023-08-18T19:41:05.588165Z", "shell.execute_reply": "2023-08-18T19:41:05.586875Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["epoch 2, loss 12.757\n", "epoch 4, loss 2.695\n", "epoch 6, loss 0.679\n", "epoch 8, loss 0.207\n", "epoch 10, loss 0.073\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/aideep/lib/python3.9/site-packages/torch/nn/modules/lazy.py:180: UserWarning: Lazy modules are a new feature under heavy development so changes to the API or functionality can happen at any moment.\n", "  warnings.warn('Lazy modules are a new feature under heavy development '\n"]}], "source": ["# Construct a two-dimensional convolutional layer with 1 output channel and a\n", "# kernel of shape (1, 2). For the sake of simplicity, we ignore the bias here\n", "conv2d = nn.LazyConv2d(1, kernel_size=(1, 2), bias=False)\n", "\n", "# The two-dimensional convolutional layer uses four-dimensional input and\n", "# output in the format of (example, channel, height, width), where the batch\n", "# size (number of examples in the batch) and the number of channels are both 1\n", "X = X.reshape((1, 1, 6, 8))\n", "Y = Y.reshape((1, 1, 6, 7))\n", "lr = 3e-2  # Learning rate\n", "\n", "for i in range(10):\n", "    Y_hat = conv2d(X)\n", "    l = (Y_hat - Y) ** 2\n", "    conv2d.zero_grad()\n", "    l.sum().backward()\n", "    # Update the kernel\n", "    conv2d.weight.data[:] -= lr * conv2d.weight.grad\n", "    if (i + 1) % 2 == 0:\n", "        print(f'epoch {i + 1}, loss {l.sum():.3f}')"]}, {"cell_type": "markdown", "id": "be06b7e7", "metadata": {"origin_pos": 33}, "source": ["Note that the error has dropped to a small value after 10 iterations. Now we will [**take a look at the kernel tensor we learned.**]\n"]}, {"cell_type": "code", "execution_count": 10, "id": "4ab76f3e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:05.593720Z", "iopub.status.busy": "2023-08-18T19:41:05.592926Z", "iopub.status.idle": "2023-08-18T19:41:05.601680Z", "shell.execute_reply": "2023-08-18T19:41:05.600494Z"}, "origin_pos": 35, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.9569, -1.0094]])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["conv2d.weight.data.reshape((1, 2))"]}, {"cell_type": "markdown", "id": "fc14c622", "metadata": {"origin_pos": 38}, "source": ["Indeed, the learned kernel tensor is remarkably close\n", "to the kernel tensor `K` we defined earlier.\n", "\n", "## Cross-Correlation and Convolution\n", "\n", "Recall our observation from :numref:`sec_why-conv` of the correspondence\n", "between the cross-correlation and convolution operations.\n", "Here let's continue to consider two-dimensional convolutional layers.\n", "What if such layers\n", "perform strict convolution operations\n", "as defined in :eqref:`eq_2d-conv-discrete`\n", "instead of cross-correlations?\n", "In order to obtain the output of the strict *convolution* operation, we only need to flip the two-dimensional kernel tensor both horizontally and vertically, and then perform the *cross-correlation* operation with the input tensor.\n", "\n", "It is noteworthy that since kernels are learned from data in deep learning,\n", "the outputs of convolutional layers remain unaffected\n", "no matter such layers\n", "perform\n", "either the strict convolution operations\n", "or the cross-correlation operations.\n", "\n", "To illustrate this, suppose that a convolutional layer performs *cross-correlation* and learns the kernel in :numref:`fig_correlation`, which is here denoted as the matrix $\\mathbf{K}$.\n", "Assuming that other conditions remain unchanged,\n", "when this layer instead performs strict *convolution*,\n", "the learned kernel $\\mathbf{K}'$ will be the same as $\\mathbf{K}$\n", "after $\\mathbf{K}'$ is\n", "flipped both horizontally and vertically.\n", "That is to say,\n", "when the convolutional layer\n", "performs strict *convolution*\n", "for the input in :numref:`fig_correlation`\n", "and $\\mathbf{K}'$,\n", "the same output in :numref:`fig_correlation`\n", "(cross-correlation of the input and $\\mathbf{K}$)\n", "will be obtained.\n", "\n", "In keeping with standard terminology in deep learning literature,\n", "we will continue to refer to the cross-correlation operation\n", "as a convolution even though, strictly-speaking, it is slightly different.\n", "Furthermore,\n", "we use the term *element* to refer to\n", "an entry (or component) of any tensor representing a layer representation or a convolution kernel.\n", "\n", "\n", "## Feature Map and Receptive Field\n", "\n", "As described in :numref:`subsec_why-conv-channels`,\n", "the convolutional layer output in\n", ":numref:`fig_correlation`\n", "is sometimes called a *feature map*,\n", "as it can be regarded as\n", "the learned representations (features)\n", "in the spatial dimensions (e.g., width and height)\n", "to the subsequent layer.\n", "In CNNs,\n", "for any element $x$ of some layer,\n", "its *receptive field* refers to\n", "all the elements (from all the previous layers)\n", "that may affect the calculation of $x$\n", "during the forward propagation.\n", "Note that the receptive field\n", "may be larger than the actual size of the input.\n", "\n", "Let's continue to use :numref:`fig_correlation` to explain the receptive field.\n", "Given the $2 \\times 2$ convolution kernel,\n", "the receptive field of the shaded output element (of value $19$)\n", "is\n", "the four elements in the shaded portion of the input.\n", "Now let's denote the $2 \\times 2$\n", "output as $\\mathbf{Y}$\n", "and consider a deeper CNN\n", "with an additional $2 \\times 2$ convolutional layer that takes $\\mathbf{Y}$\n", "as its input, outputting\n", "a single element $z$.\n", "In this case,\n", "the receptive field of $z$\n", "on $\\mathbf{Y}$ includes all the four elements of $\\mathbf{Y}$,\n", "while\n", "the receptive field\n", "on the input includes all the nine input elements.\n", "Thus,\n", "when any element in a feature map\n", "needs a larger receptive field\n", "to detect input features over a broader area,\n", "we can build a deeper network.\n", "\n", "\n", "Receptive fields derive their name from neurophysiology.\n", "A series of experiments on a range of animals using different stimuli\n", ":cite:`<PERSON><PERSON><PERSON>Wiesel.1959,<PERSON><PERSON>.Wiesel.1962,<PERSON><PERSON><PERSON>Wiesel.1968` explored the response of what is called the visual\n", "cortex on said stimuli. By and large they found that lower levels respond to edges and related\n", "shapes. Later on, :citet:`Field.1987` illustrated this effect on natural\n", "images with, what can only be called, convolutional kernels.\n", "We reprint a key figure in :numref:`field_visual` to illustrate the striking similarities.\n", "\n", "![Figure and caption taken from :citet:`Field.1987`: An example of coding with six different channels. (Left) Examples of the six types of sensor associated with each channel. (Right) Convolution of the image in (Middle) with the six sensors shown in (Left). The response of the individual sensors is determined by sampling these filtered images at a distance proportional to the size of the sensor (shown with dots). This diagram shows the response of only the even symmetric sensors.](../img/field-visual.png)\n", ":label:`field_visual`\n", "\n", "As it turns out, this relation even holds for the features computed by deeper layers of networks trained on image classification tasks, as demonstrated in, for example, :citet:`Kuzovkin.Vicente.Petton.ea.2018`. Suffice it to say, convolutions have proven to be an incredibly powerful tool for computer vision, both in biology and in code. As such, it is not surprising (in hindsight) that they heralded the recent success in deep learning.\n", "\n", "## Summary\n", "\n", "The core computation required for a convolutional layer is a cross-correlation operation. We saw that a simple nested for-loop is all that is required to compute its value. If we have multiple input and multiple output channels, we are  performing a matrix--matrix operation between channels. As can be seen, the computation is straightforward and, most importantly, highly *local*. This affords significant hardware optimization and many recent results in computer vision are only possible because of that. After all, it means that chip designers can invest in fast computation rather than memory when it comes to optimizing for convolutions. While this may not lead to optimal designs for other applications, it does open the door to ubiquitous and affordable computer vision.\n", "\n", "In terms of convolutions themselves, they can be used for many purposes, for example detecting edges and lines, blurring images, or sharpening them. Most importantly, it is not necessary that the statistician (or engineer) invents suitable filters. Instead, we can simply *learn* them from data. This replaces feature engineering heuristics by evidence-based statistics. Lastly, and quite delightfully, these filters are not just advantageous for building deep networks but they also correspond to receptive fields and feature maps in the brain. This gives us confidence that we are on the right track.\n", "\n", "## Exercises\n", "\n", "1. Construct an image `X` with diagonal edges.\n", "    1. What happens if you apply the kernel `K` in this section to it?\n", "    1. What happens if you transpose `X`?\n", "    1. What happens if you transpose `K`?\n", "1. Design some kernels manually.\n", "    1. Given a directional vector $\\mathbf{v} = (v_1, v_2)$, derive an edge-detection kernel that detects\n", "       edges orthogonal to $\\mathbf{v}$, i.e., edges in the direction $(v_2, -v_1)$.\n", "    1. Derive a finite difference operator for the second derivative. What is the minimum\n", "       size of the convolutional kernel associated with it? Which structures in images respond most strongly to it?\n", "    1. How would you design a blur kernel? Why might you want to use such a kernel?\n", "    1. What is the minimum size of a kernel to obtain a derivative of order $d$?\n", "1. When you try to automatically find the gradient for the `Conv2D` class we created, what kind of error message do you see?\n", "1. How do you represent a cross-correlation operation as a matrix multiplication by changing the input and kernel tensors?\n"]}, {"cell_type": "markdown", "id": "db5fe222", "metadata": {"origin_pos": 40, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/66)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON> (aideep)", "language": "python", "name": "<PERSON><PERSON>"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
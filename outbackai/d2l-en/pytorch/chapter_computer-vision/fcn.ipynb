{"cells": [{"cell_type": "markdown", "id": "3c358c3f", "metadata": {"origin_pos": 0}, "source": ["# Fully Convolutional Networks\n", ":label:`sec_fcn`\n", "\n", "As discussed in :numref:`sec_semantic_segmentation`,\n", "semantic segmentation\n", "classifies images in pixel level.\n", "A fully convolutional network (FCN)\n", "uses a convolutional neural network to\n", "transform image pixels to pixel classes :cite:`Long.Shelhamer.Darrell.2015`.\n", "Unlike the CNNs that we encountered earlier\n", "for image classification \n", "or object detection,\n", "a fully convolutional network\n", "transforms \n", "the height and width of intermediate feature maps\n", "back to those of the input image:\n", "this is achieved by\n", "the transposed convolutional layer\n", "introduced in :numref:`sec_transposed_conv`.\n", "As a result,\n", "the classification output\n", "and the input image \n", "have a one-to-one correspondence \n", "in pixel level:\n", "the channel dimension at any output pixel \n", "holds the classification results\n", "for the input pixel at the same spatial position.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "805a6df5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:22.296389Z", "iopub.status.busy": "2023-08-18T19:34:22.296055Z", "iopub.status.idle": "2023-08-18T19:34:25.836644Z", "shell.execute_reply": "2023-08-18T19:34:25.835421Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "import torchvision\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "05a33edc", "metadata": {"origin_pos": 3}, "source": ["## The Model\n", "\n", "Here we describe the basic design of the fully convolutional network model. \n", "As shown in :numref:`fig_fcn`,\n", "this model first uses a CNN to extract image features,\n", "then transforms the number of channels into\n", "the number of classes\n", "via a $1\\times 1$ convolutional layer,\n", "and finally transforms the height and width of\n", "the feature maps\n", "to those\n", "of the input image via\n", "the transposed convolution introduced in :numref:`sec_transposed_conv`. \n", "As a result,\n", "the model output has the same height and width as the input image,\n", "where the output channel contains the predicted classes\n", "for the input pixel at the same spatial position.\n", "\n", "\n", "![Fully convolutional network.](../img/fcn.svg)\n", ":label:`fig_fcn`\n", "\n", "Below, we [**use a ResNet-18 model pretrained on the ImageNet dataset to extract image features**]\n", "and denote the model instance as `pretrained_net`.\n", "The last few layers of this model\n", "include a global average pooling layer\n", "and a fully connected layer:\n", "they are not needed\n", "in the fully convolutional network.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "a2a9a1c6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:25.842583Z", "iopub.status.busy": "2023-08-18T19:34:25.841424Z", "iopub.status.idle": "2023-08-18T19:34:27.191787Z", "shell.execute_reply": "2023-08-18T19:34:27.190380Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/aideep/lib/python3.9/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/aideep/lib/python3.9/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet18_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet18_Weights.DEFAULT` to get the most up-to-date weights.\n", "  warnings.warn(msg)\n", "Downloading: \"https://download.pytorch.org/models/resnet18-f37072fd.pth\" to /Users/<USER>/.cache/torch/hub/checkpoints/resnet18-f37072fd.pth\n", "100.0%\n"]}, {"data": {"text/plain": ["[Sequential(\n", "   (0): BasicBlock(\n", "     (conv1): Conv2d(256, 512, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)\n", "     (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "     (relu): ReLU(inplace=True)\n", "     (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "     (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "     (downsample): Sequential(\n", "       (0): Conv2d(256, 512, kernel_size=(1, 1), stride=(2, 2), bias=False)\n", "       (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "     )\n", "   )\n", "   (1): BasicBlock(\n", "     (conv1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "     (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "     (relu): ReLU(inplace=True)\n", "     (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)\n", "     (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "   )\n", " ),\n", " AdaptiveAvgPool2d(output_size=(1, 1)),\n", " Linear(in_features=512, out_features=1000, bias=True)]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["pretrained_net = torchvision.models.resnet18(pretrained=True)\n", "list(pretrained_net.children())[-3:]"]}, {"cell_type": "markdown", "id": "ef9bca9e", "metadata": {"origin_pos": 6}, "source": ["Next, we [**create the fully convolutional network instance `net`**].\n", "It copies all the pretrained layers in the ResNet-18\n", "except for the final global average pooling layer\n", "and the fully connected layer that are closest\n", "to the output.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "be7ad27f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:27.197675Z", "iopub.status.busy": "2023-08-18T19:34:27.196065Z", "iopub.status.idle": "2023-08-18T19:34:27.204322Z", "shell.execute_reply": "2023-08-18T19:34:27.203107Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["net = nn.Sequential(*list(pretrained_net.children())[:-2])"]}, {"cell_type": "markdown", "id": "446c3798", "metadata": {"origin_pos": 9}, "source": ["Given an input with height and width of 320 and 480 respectively,\n", "the forward propagation of `net`\n", "reduces the input height and width to 1/32 of the original, namely 10 and 15.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "5bb828bc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:27.208600Z", "iopub.status.busy": "2023-08-18T19:34:27.207953Z", "iopub.status.idle": "2023-08-18T19:34:27.301540Z", "shell.execute_reply": "2023-08-18T19:34:27.300587Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([1, 512, 10, 15])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.rand(size=(1, 3, 320, 480))\n", "net(X).shape"]}, {"cell_type": "markdown", "id": "f7c2da65", "metadata": {"origin_pos": 12}, "source": ["Next, we [**use a $1\\times 1$ convolutional layer to transform the number of output channels into the number of classes (21) of the Pascal VOC2012 dataset.**]\n", "Finally, we need to (**increase the height and width of the feature maps by 32 times**) to change them back to the height and width of the input image. \n", "Recall how to calculate \n", "the output shape of a convolutional layer in :numref:`sec_padding`. \n", "Since $(320-64+16\\times2+32)/32=10$ and $(480-64+16\\times2+32)/32=15$, we construct a transposed convolutional layer with stride of $32$, \n", "setting\n", "the height and width of the kernel\n", "to $64$, the padding to $16$.\n", "In general,\n", "we can see that\n", "for stride $s$,\n", "padding $s/2$ (assuming $s/2$ is an integer),\n", "and the height and width of the kernel $2s$, \n", "the transposed convolution will increase\n", "the height and width of the input by $s$ times.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "a6fefe2f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:27.305909Z", "iopub.status.busy": "2023-08-18T19:34:27.304968Z", "iopub.status.idle": "2023-08-18T19:34:27.332225Z", "shell.execute_reply": "2023-08-18T19:34:27.331098Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["num_classes = 21\n", "net.add_module('final_conv', nn.Conv2d(512, num_classes, kernel_size=1))\n", "net.add_module('transpose_conv', nn.ConvTranspose2d(num_classes, num_classes,\n", "                                    kernel_size=64, padding=16, stride=32))"]}, {"cell_type": "markdown", "id": "9c402b62", "metadata": {"origin_pos": 15}, "source": ["## [**Initializing Transposed Convolutional Layers**]\n", "\n", "\n", "We already know that\n", "transposed convolutional layers can increase\n", "the height and width of\n", "feature maps.\n", "In image processing, we may need to scale up\n", "an image, i.e., *upsampling*.\n", "*Bilinear interpolation*\n", "is one of the commonly used upsampling techniques.\n", "It is also often used for initializing transposed convolutional layers.\n", "\n", "To explain bilinear interpolation,\n", "say that \n", "given an input image\n", "we want to \n", "calculate each pixel \n", "of the upsampled output image.\n", "In order to calculate the pixel of the output image\n", "at coordinate $(x, y)$, \n", "first map $(x, y)$ to coordinate $(x', y')$ on the input image, for example, according to the ratio of the input size to the output size. \n", "Note that the mapped $x'$ and $y'$ are real numbers. \n", "Then, find the four pixels closest to coordinate\n", "$(x', y')$ on the input image. \n", "Finally, the pixel of the output image at coordinate $(x, y)$ is calculated based on these four closest pixels\n", "on the input image and their relative distance from $(x', y')$. \n", "\n", "Upsampling of bilinear interpolation\n", "can be implemented by the transposed convolutional layer \n", "with the kernel constructed by the following `bilinear_kernel` function. \n", "Due to space limitations, we only provide the implementation of the `bilinear_kernel` function below\n", "without discussions on its algorithm design.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "8acfd5fc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:27.337253Z", "iopub.status.busy": "2023-08-18T19:34:27.336271Z", "iopub.status.idle": "2023-08-18T19:34:27.347187Z", "shell.execute_reply": "2023-08-18T19:34:27.346126Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["def bilinear_kernel(in_channels, out_channels, kernel_size):\n", "    factor = (kernel_size + 1) // 2\n", "    if kernel_size % 2 == 1:\n", "        center = factor - 1\n", "    else:\n", "        center = factor - 0.5\n", "    og = (torch.arange(kernel_size).reshape(-1, 1),\n", "          torch.arange(kernel_size).reshape(1, -1))\n", "    filt = (1 - torch.abs(og[0] - center) / factor) * \\\n", "           (1 - torch.abs(og[1] - center) / factor)\n", "    weight = torch.zeros((in_channels, out_channels,\n", "                          kernel_size, kernel_size))\n", "    weight[range(in_channels), range(out_channels), :, :] = filt\n", "    return weight"]}, {"cell_type": "markdown", "id": "7c68424b", "metadata": {"origin_pos": 18}, "source": ["Let's [**experiment with upsampling of bilinear interpolation**] \n", "that is implemented by a transposed convolutional layer. \n", "We construct a transposed convolutional layer that \n", "doubles the height and weight,\n", "and initialize its kernel with the `bilinear_kernel` function.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "98efd2f9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:27.350768Z", "iopub.status.busy": "2023-08-18T19:34:27.350438Z", "iopub.status.idle": "2023-08-18T19:34:27.357663Z", "shell.execute_reply": "2023-08-18T19:34:27.356565Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [], "source": ["conv_trans = nn.ConvTranspose2d(3, 3, kernel_size=4, padding=1, stride=2,\n", "                                bias=False)\n", "conv_trans.weight.data.copy_(bilinear_kernel(3, 3, 4));"]}, {"cell_type": "markdown", "id": "4ce526ac", "metadata": {"origin_pos": 21}, "source": ["Read the image `X` and assign the upsampling output to `Y`. In order to print the image, we need to adjust the position of the channel dimension.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "7d8d6903", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:27.361307Z", "iopub.status.busy": "2023-08-18T19:34:27.360856Z", "iopub.status.idle": "2023-08-18T19:34:27.475914Z", "shell.execute_reply": "2023-08-18T19:34:27.472680Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [], "source": ["img = torchvision.transforms.ToTensor()(d2l.Image.open('../img/catdog.jpg'))\n", "X = img.unsqueeze(0)\n", "Y = conv_trans(X)\n", "out_img = Y[0].permute(1, 2, 0).detach()"]}, {"cell_type": "markdown", "id": "ddcbc658", "metadata": {"origin_pos": 24}, "source": ["As we can see, the transposed convolutional layer increases both the height and width of the image by a factor of two.\n", "Except for the different scales in coordinates,\n", "the image scaled up by bilinear interpolation and the original image printed in :numref:`sec_bbox` look the same.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "a5c3366a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:27.481229Z", "iopub.status.busy": "2023-08-18T19:34:27.480557Z", "iopub.status.idle": "2023-08-18T19:34:28.367719Z", "shell.execute_reply": "2023-08-18T19:34:28.366579Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["input image shape: torch.<PERSON>ze([561, 728, 3])\n", "output image shape: torch.Size([1122, 1456, 3])\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"226.708824pt\" height=\"173.415579pt\" viewBox=\"0 0 226.**********.415579\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-27T22:20:41.984354</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 173.415579 \n", "L 226.**********.415579 \n", "L 226.708824 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 39.65 149.537454 \n", "L 219.**********.537454 \n", "L 219.508824 10.937454 \n", "L 39.65 10.937454 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p8fa03e93bc)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAPoAAADBCAYAAADmUa1TAADcGElEQVR4nOz9d5NtWXreif2W3e64NNeWaQc0uuEIkiI1MwpNSAoqqOCMPsfow2kipAghJsQIjUZDcgjCESBMo015d1264/bey+qPtc/JvFW3Go1BVTVA3FWRlXnPOZnH7PWu1z3P84qnT5/mtm15vV6v1+s/z7Xf79Fd19F13S/7tbxer9fr9TUtIQTyl/0iXq/X6/X6+tdrQ3+9Xq9/AOu1ob9er9c/gPXa0F+v1+sfwHpt6K/X6/UPYL029Nfr9foHsF4b+uv1ev0DWK8N/fV6vf4BLP3LfgHfxMo5/2IPFOKLN/0N/tYXHvtzn0r8wq9LvOJ1vV6v199k/YMwdLg10CxebYACgfic4Qle/dhXGeiXmeIrn+uOkf91xv7ayF+vr2L9gzF0KEaX82R84vP3fdHgZP5yA/7CH/556/PP9YtGGK/X6/UVrb+3hv43NZbD448OcrL24rXzK//e4WC4u77Mw4pXPPZg4K96qbev58uPksN9X/ZeX3v71+sXXX/nDf3nGfTnjSXnLzHYO7eL4+8JxCGMz58P08tP6c7fuGtUOecvGFl+hffPGdIrbPHzr/HnGexrI3+9vor1d97Q4dWb/SXjnXLeu3lvSun4PaVEjBHvPX4cccNISgmlFEopjDEYY1BKIZUqbB8hyEKAFEgpj89z+H74uvvaxCs8+jFV+Pxdrw319foG198pQ/+Cp7t7+/G+BHeMJ2dICFLO5BSIMeCGkd1uz2a95ubmmpv1mvVmw8XFFc+fPuPixXP63Q5rFOfnZ2hjqOqWrm1pmgZb11R1S902LBYLTk5OWMxn1FVFXddIrVBSlQNAiGM4MB0DL6UH6aVUYXpf4m78II/1gVvTL4fI4T1/4fz4a0L6V63XB8s/7PV3ytBfWunWQ8ucj8G0yA5SIiNASFIGnwxJSFLoGfc3XD695Kd/9R4/+/Ff8cHH7/H84hnPL9fs94n94HHDnkYFzlcN6rtv8+mTJ9zsIkaClIIgLNdDoq5bWmN4+63H/Mp3v8PpyZIHD+6zOl3xq7/6a7Tzeaniq4yc/rv19hIhBTIdoozbgl8xuslYJ/sT5Y7b+w/GjCSLlyOKz0czr4349frr1t85Q0+pZMYi52MofLgNIKJJlLBc5HJfyoEYIsOw4+lnT/jj3/9j/vD3/5iP3v8QZWHbb7m+2SNUi8gZqwTf/d73WHWG+/dOIY5occOya+iainXv0Jc7Brdhtx15YRL77Q3/6c/+nPPzc1aLjv/u//bf8Ru//ZuYukJpg5QK8ZKhfzHkP77Hu8aZb799PjUoj70NZl5l1K+qF9xdrw+C1wt+iYb+8/LunDMyf/G2DHghS5UsR0QO5BiJYc/Q7/mLP/8Lfvd3/1/8we//PuubNfeWKxarGUbCYlYTgsbHHoxkt9vz4PwRJ8sFcXfJ/uaGezPL6bLFp5Y3752wi5JPnrzg8voCKTOLecvTp58ybGZcvnjCbvuIOs/QukXp6mjch++Hn+8a2yGvf5XxHQ6Gu/enO33/z/+dv27dfa7/tS29b+KQ+NtiCb7KduV/rofiL8XQ716YlNJxIx48961XvwN0yZmUMyknRI7I5Mm+J7qe/W7D7//hH/E//A//I+++/yGb7Zb5omVhMy0D805h6xUxWXbrLdfbDc57XEgs5x2+tTy3hsooFq1Fa4tLgq2c8eP3P0aIzP/md34D7wb+zf/8bxhHxW5zTQp7SAZiTSKBLK/zrsHeNXa4NdZfdHMnIchT/V9KeZvOyJ+PXr67Ye9GRL/oRv5F6wCvei//aw+WL+tG/CKtyFf9/t90/edq5PB1GfqX9KRffkj+0q9SMS/h+6F6nicjzzmS/IAfdwzrCy6efsLzyxf80X/4Pa5evKCpWoSQSJmZK8+vvPmIR28+op2fkHPFxdWG//Cnf8H1mBmd53S1gs2c9+wN0lQobZnPO3wSXGygR3F6esobD8+4v6i5+eQd3n8S6Hcb+v013WyGyKUj/yrQzeEge1VYfncJIW4PuJdaeemYo9+9//hzueHOHzr+7xdo433u9+48/q9NCabn/TzCTwhxp1D6CgThK1KPL3vs3ddQDo8vfTnTL3z+BX7xeX7ea/myx33ZY/8+ra/Ro0eAUjQ7bDy4xaBmICVIiXioQucIqXjsnAQxQYqBnCIpeEJwuNzj1zv6iw+4+OzHPP/0BcuzR/yjX/0u/U3P08sdL0yNyor7p5q37nd86/EK23QMURPpaVoYExgy1hhs00KYDhHpaeaapWm4yY77i4bT1YL7ZyvuN5l//P1HNIuBldwQb67I975HEhIjSoFQ5IxICXImiwmQMxXX5PT98O+7m+quh35ps4l0/Pw4/B4ghCztO3gptIc7xv+qzXnwklLfFv8O1wUgHry/mPqFgnLOZHJKCEoHJJcXjRCKNOESym23V/3QLRECRJYTRilPh+IEVDq+6Vd0F+6+t9tnePkxdz+qfChwvhpP8fn1izzm50U2f5+M/2sx9M83i2CyaSBlECnfMehIQEBOJe9OgRwDKWWCD/hxILoR7wbGccfeXZBuRsbL97h5+pfUuePx2Rxt4OF5xdPnn3C2qHBu5O23H/Hw3pzORoQciEJx77zin/3O9/jJT59y7/SM5aziMkUIgRAcWdRoK5h1Ff/k/mP2w46Hjx7x+P4csX3Gr33vMRue8HCpyf2emGTpuaeIkOpo4Mfimrg10nQ0MvmSER489OdBOYf7xJ2/cdx44i6ch5fuz5977EvX5VjNF0crefl63QUWQSYh4u1FzDkhcjl8soik7MvPkylGDqmFOB4KAFqq0m6823mQ8vi0X4ZMvHOKfQlH4RXrS7ALv8j6sojjVY97CUPxd9zovx6PLiBmefix2HBOxUOng0FHmIppKUViDKTgyWEkuBE/DAy7Lf1uy7jfMfZbxmFD6J8g9gnGZ4hwyXLVoNjRNZ5f+5V7OLfmg0+espw/gLCnqUCJAURAZsH5bMGDH3yL3/7+93FjoFMe/B5tBOMwMvQesqSqDbOl4V/+n/4pWgDuipDWaBP53tsPObUZZEQpSDmSEeT0xTz8VcWznFLx6vCFHP74Eb7C6O/m6F+WArzqsa/8mylO0cLn6iAHU5zSphAjImRiiIzOM4ye0Tnc6PCu5+rykrOzExprUVKSBcSUiCmRkShtMFqjpaDrOuqqQiqJUhpp1DEyEYgvsdrji5+wBbd7DCC8KmjJYgI3/83Wl9UW/jqD/pvUXH5Z6ysz9M+/0UPAmVMie0dOvhh1jMTgESkQ/YAfR/w44N1AdAPDfsfY7xl3W3Y31+x3G8b9lmG/Q2ZPlS6Qo6CxI9XM0ZhMDBtyHDlfGf7Lf/p9fvi9R1xeXHH94Ts8+/hDqrfuoZuKpmnRuSc7z9nJfdKsQoTAfruhqjTj2HN9tSNnifcDw/aCsrcC426NEhk3eub1gloGgoxkAilHIvIYOv511fG7Hv3LDoNXteY+/xkfDom761jEu1OAu5sW3D4+Tj09jujBlBIxjYQY6Puevu/Z7/ZcP3vOJx9+zHsffsyHnz7hxeUNu36gsoZ+t6E2ikVbY7Wka1u2uz3b/UDvInU356233+Zk3vDw4UO6bsZsNmOxWHB275zFcsl8PqOqa7TWSCmJMZZDJyZCDMfXffdQVFIilCIreSdoKtgFsiTzRezBX7f+Jsb69w2s9LXl6DJDCp44bHG7a4bNDc4NOD8S3EAYe9zY44ee1O8Z+h27zZpht2G7uWFzs2G33SJywiiBFnD/fMXMJpIAlT0xOJKEEDzJjWghWTWGTs2wYWCfDfvrHduuYn6+oG5qVEr46IhhjzANEcngoR+2uDEQ2pYUIUSPFhUyehg3GD8whsR275kvLWSHG7aM/Q7ZVoTj1uIlw32loU8e/fP33zXIu4W3n1e8u3vflz32rtEfH0uGXIw7hMA4jrhxoF9fsV7fcHNTvjabDe7mkmdPPmUzeJTzxP0Nzz97hrANOThqlVEnM2ZnJ5zNalolmLctnz6/4qMPPuT6+oZZJagqy4sXFzRNw6NHj7j/6CFn52ecnZ1xfn7OYrFAKcUwjOx2Wy4uLnjx4gUhBKSUNE2D9566rpnP5yyXy+OhcXp6ymw2o64bpG2QukCaD5/rL+J1X/U53V1f1un4fFfg74Jhf379rQz9WEo59rynIlTOpBhw+w3rF5+yfv4Ju+sX+HGPG3vGfk+/XeOGHjcOiHGEFHFjjyRB9NgUwGbC6JAhoZUi7NaElJFJkVOkrhsSJR/UAkRKpLEnuECrLVrUbNc39KuBxcmCMHrGmDBVRxbgvEPQ4LPAB884eobBM/Qjq2WDyAmVM2Hsyd4RgiAFEDkRk2NMe9ywx1ZLQk7ICQcrxG3aUozvc5+buPXot5tCkOQXjfulPJ9DxDr9X04FvsN9Qr70XPnOazjccAzTYyDGgv1///33GIae64sL1s+fcvniOdvNmtE5fvX73+fx+Zyz5jE3e8dmyMy7DjeM3PiMMpI3H5zw1v1TzpYd5ycn7PYjquro5pcMLnGz3vDo9BytFbOupmkbnj39jHff+xmL5ZJ+GBiHgfl8Tt00jKNjHHrmsw4pJV3X4X3g5uaGFxeXdG1bYMmLOW7fc3JywnKx4OzsnO985zucPn6Dxek55/fOSxSn9RQtqC80Gl7610vdi59/YH6ZMX9Z7v7Lrub/7T16TkCpnmc/krwjxUjvA8P6gusn77F78QFu85ww7PDDiOt73HZHcB5CxHvwwVM3lvmspWmWhDyy260ZdpndZoCQSCETQmTeZrTM1NJSy4hij84jaRdwW8eYJTe9A93hxhtcGIhpRKWGJBJCZ0LfMwyw2V3hgyMiiVkgpWa36fFLTZ6DTwFhysVTGNwwMDhDqBXSSPQYCDuP0xIl48vGljMyR+TBcA95OQpE8TbHyywOIf30z8MfkQruQGBfyv+TQMjbfFTIfJubClEO3pSRhJJWFCdOCongPc4N3Fxe8Lv/9/+e7377LdbX17z//vtcv/gEIyM/+K3f4NEbp8Rxi4yaKkl0VbPfZ07nK+J+zaJr+dXvvsW9ZcPJrKFqGpb3TklZY9sZ777zHu3piv/dP//nfPjxO5ycVLTzBX/wB39KcI5aa/oQ6DcbjBD0+wGXNctKsjKC1XJGN19SdTN++tMPGG4GGtWwbFpmlUZ1SxaLGddXF4SwZ7N5wcX/9K959PhNfuef/lPe+vZ36WZLmralqiq01ref5SE9QiDlXSOXSPFF00giv4RgPD78S4qod//9qvVNev6vIHQvmy66njjuCMOOMO4ZdjuuX3zGzdMPkGHDrBJk0xLblrGv6ZqWHEr+FVOk7/cMww6f91QSFvOak7OaMEauL264fH6JyANSGaw1GC2BzDj26Dgj58wQHJt+z6b3eBQhjbRtRTcrF1lIBUkQQ8JagxSR/a7HmIqUQGt9zNUre4+MIqaEBExVMbjE6Dz96FnOG4ydlcPHjaRYPLW8Y6wAyHQ0/sM1V0jEVKw8hoMCkLdGevDUWdwaernrjreXLxedPh+6F/zB1N2IkRwS0XuG3Z5+t2a3XfP8xQXvf/ABf/wnf8LJaomNGx6ez3nrjYf86m/8kKauCbJ8BhHPcvWIbnnG86trwmXi0YN7nJ2e8vjBGYuuImaBrhqUrqjake/96vdQSvPDH/6Q3f6KZl7z4uqGnDOnqxXf+da3kBRqz/3797m62XD94oa37z3idDVjMWtRVjPvWn79136F5BM31zc8uHfKctlyfu+czWZDUxkePHjAfr8nxUCKnv12w7DbopUmpUCKHbaqXsr1Dz+ndPezK+nhFyrwUnBoi76yyPo5T/75tOqXuf52hn4I2VMiuD3j9gK/fYHvNwxXL3DrK6q8p2kUtdXELBHS4PYNQ9+TfMAajQ97Mh2IM1L0JCKChJKSprJIApXNbNcbZM4oLdBaIDTE6HDjSNXOCAHqrIjGs77ZMZtZFu2SulE0TUMfQUnDbLagHyPjGAg+QZas13tOV3NSdigFzkV2uxEpErVQ2Nqye3ZBForl2T2MHZG6o98NOH9Flhkl9J0CWakiJz2h4+TthddZoSjhuzo8XoiplV1C/SiKEWdZSC2HdlfZnOXvy2nj3QHDH80+pQNFN5ZahvfEocfttmxvbthcPuHZ00/58NOnaOF4/uwzchr4L37wgF/59ps8fuMN2nlL0golW3IWLCtVQuZTxf++Mnz0ycdICbPa0s0XtLMOEPiU0bbmpJ7xL//V/xkpFDbDbDbjweP7tPMlP/jBhtP5nLceP+LJpx8za2oenJ1xc31DbRRWCRazBklitZgx+j1np6c8uHeCH3d0jeHb33qDqqqprYYMSmuiVzx6cA+lLUZJwjiQo0dkS4yRGONx+0opSSm9ovMhXpmP5yyO1f+/rnBaHp9fisDurr8p6u9vu74Sj05OJD8Q+ivc9mkx+PUN0jtqI7G1RRiNTKCUKR+AlOSpyCITpBQobehAiL702kOCFGjbBlLAKPD7Ea0Fxkik1aTKIkgIKamXc/q0RbpM1zZEP2IsQCIEjxCGmDL9fgRR4VxkGDyXlzc8f3rJ2emCqlZUlcaNniQFVQ3zrmVwA70LZGXZDY7TriV6zWfPnjKojNCgZVU47RPOXUqB0AKlC+9dTq2kciCoaYOVPryQkqzubqJiyCWfl8ff/Xz+frj99moUhN6Bgx9jxLmROPaM62vGm2v6zTWXTz/g8vlnGB/54VsrjL/Hm2884J/84CGL+YxZq3B+ADMnZYXzmcVqjrQKkDx+8wF1Jbm6umYx72i6GVXbEWJh8tm6QlcVddtQ1w2/9z/+W5bLJefn55zdf8jjx99hZjVPP/0Eo0Qx6OUMJTJdUxPcyPOnn3F2ssIqyb0HD+i6BT/78U85P11w72zJatEihCGMA8ZaNps1SsBitcBUDcN+wzgsCa6jaRsyEGM8evK7aMzDbYfP9VWFuVTi9i8cAndhz3dvO1yrzyMefxk9+K+m6p4SKThE7BFxS9hfQAwoAaZqELYmG4tMqWxqHZGxGCc5I0WFVKbkmCKT/QAhk4nEAiGnqhqs1iRjUXkEMoiMUuVDCyFg5h1ndUdX92ye35CzQ8QbEBnnRkw7R0bF06fP0aYjJkvXzYghl5w5Z5rWoo0kxkylLMuTFpm2bG5u6OYL5tUp7dkZ4PnpT97ng08c3kaUkRhZoybhCqUURmuMAmMsxhqUVCglQRuENlOBqDxWaU2WxXDFne9IBbIY+tHzSHHngLiTo985BGKKk6GXfDyNPb7fEYYNfncNfs/5ssFIgRSJhz98yLe+9QZVC1pHrIxshz2mTghlmS2W+JQwKTFbzrm8vOSzTz7i4cNHzBdLqqZFW4vMBdhi64rFyZKqrXnv3ff49NNP+e3f+WGprNsaY2akfsfFk09xQ8+sazBKljZrEKyW95Buy6xtOD874fTeKSGBFJFvvf2Y05M5lVX8+Z/9Jefn51ijUEDd1sy6lsEFPv3oY7719rdxQ0/0M9AFTh1jPHryw+d28O4HqK14BSAplcrmS7cdjPbzHRC4jRgOkdbnD4S/Dmb8Va6voBhXII4yBWSMWCQKTa4NZJDGopQum1EqRM5oJUELovcoKbBKHyvIKUeiT8TsEXJCzsVQaKACqqZh7Ld4P5KdQBHRlUeYERUcVTNHreZEYHHfovI5vt9htcUYRTufY23FdrsnuB4hI6d1zz/59pI3HlS8fb9jsbBUlWLVNGgXGdMevbBsx5HoAj/90cf86Ec/4p13PmXnFLLSKJHL4SXEBAhRGG1orGU266irmqqqqOoKrSXWlkqwmQxeaYWoFFrpImqBRElNVhKUvI0ShCi1BlWX55HqmBbIycMfNlWacnRyIIZMTIpMJI3PmakBckLKTNNYqtMVXZPRQoEQuORwMWKEpw8JWzVoqWnqDt8HPn7vE3TVslidUllNToEUIKFAKmbdjFrX9OuRf/v//XdUtaVdrKiaJdoYrG0Y3Ja+37Ld7jk5PeeTJ09RWvDmsuPxSQdeMm/rcsBpxYc/+xnjdoOYLfjow8/40Y/f4YOf/YR/8S/+D+QgMCpxtlyxD5IP3v8AN/bkMOIpPAkmeLKkdCgkqaj/ypLqCPIUmksEtwZ63OpTDSWnfAszzolMnAz4Tk9ESHIqHSHyqxWJDuvvh0cXIJRCmQpQgEbresoxwVhbwpcYQEhiKjmSUpIcMjF6rK1vP4go0NqUvArwIiOVKDosEwpH2RqUKeGpj2g9gXLGkSwtxtScPThBBs12vUanBi0VVksSjraTVKZi3PeMvee3fu0BP/j2OfNlzXzVFsSWTFhziXMjzzcj//aPfsb/8kfvsu0V2Tv60SNNhTAWPdTIFNBTyOy9p+97gvdYo+jaFmsrpBQYY+jahtlUBW6aBltVWKuRJqOURkmDlgaYvLuSJfyXCiFliYRMg9LlYNBao5RES4WSt4fCsbUnIilrsh+h35LjFoNDKw1KcHZ+QiSgKkMYU0kVbIWiIqJLjUAZtLKMvefTjz5jc73nn/5X/6yo+riB6EeiGxC2pputEFkQfeL//bv/mo/e/4Tf+se/ibY1xjZYa0sUlmDwmZQNJEn2nlmlefPeCW89PMf1W0JKpBj5oz/6Y378l3+JiYqby0ve/fgTnly84Le+/x20zBidWMwWDPsdnzzb8od/+Mf89q//KsQA2hBTKu3XmApmXwiSlJOhH4qft9ESOR0Pz4MBlxx92vbH7sqE2s+fIy6hS40GWQDCB0bj583n74NHP9aBhEJ3S0SzJPdXSKnIUhz7l4dw6WCpIXiUyBhjGGPAe398rFKqMNa0xg+uGH+acnoi4zgetd6g9DZjiOA8SpWqfA4RpQ0iOSoloLaIDDEFpMxIMkJHsg0YKdBSEz3MlxJtRzKOGEeutyM/fu+C/8fv/oj3nwic7lCV5MTCfDHH2CI6kRGIlMg+4JwjaIEVmRAUWoGWGSsLOKXf7ljf3CC0RUpZvLoxWKOoLFhboc0hldEYoTBKHSOaQ+ieFC9p3mmjsUofDV0pPaUEkiwhhwTjjpYNCxvxzpFVprY1dVWRpEFqCUYi64Z6dQ+ZlmyHGiMkKmsIgpvNDR++/wG//uu/Tts27LYbYowMw4Ag4weHsS0xRj744AN+9KMfce/eOY8ePsIYc3zPKSX2TvDjdz5ltxuZmR3350vq1ZzT5ZzoR7qmYrMb+E9/8h/58bvvUNc1i+Up7azhZDWjbQ1vvfEYScaPA8TM0Gf+6s//lO3lC+btbxLciKk7YkjkcSAdo0d5LGZKIUjitq6ShYQsp9vvVNXVF9ufAiBRDuCDXUypliiNodtWpyyEplchGr/unP0rydGz0Mh6iV3ex62fIpSaiA9lHTyM92EqfkAIASVuiyCfx3HfVj5Lfi2MKfjrCR55eJzW5cLlmBj3u7KBKeGsxKOUQhtNFqUolpMHItG7I0gnB09jFTmObDaBIGqeX2z4d3/4Af/+jz5h8Avmq3P0ImMaz4oWawyCjNFqilgSYz8yiEjfjwgNqqpopxRGG13ILzRsx0ScWncpJVLw7N3A5YtNKRgJBdIgjaGSFqvscRMeK/gqlfc4fXZaa7RU6Mn45WHjCUjZkfsdnfD8+nfvkWUmxoA1FcaUyMhYQ5Zg6xlJaZTWVNmw3UWUkihgv1uzWV+zWlbcv7dgGIZjDhpiRORElsWInRv55JNPePjwId/57ndZrpbHPLbc77i+umA57/j2228wrq+Q2dPVin63QZ+0rK8uGEOG5PmNX/0B/dCDFCQ8D+4tkQhEDly9eMrp2RKlLZcvnjGrFN956yFv3D+fiDiZYRxRcRIEleqYXonJmKWUZCkn9R8JuTiru7iInF5NrCmGO6UDh70sIB90BQ+PkZIsxbE+cNjzn6cyfx3rKwjdRWGlYTGzM3QzR/Q1PvgpR8zH4lTK4L0jpeLdc4qkVML3z1dCAVIqXCglVeGiT0UU5xxQPFrZ4JrgAxARzhWvphSQyVrhgkdXFmRB7JFTAZDEhJEKpTLaZ/ZD5Fmf+Z//6C/4Tz95zosbS1U/5Oy8YbG0aJ2pbCkKWq0mAYyA1ZLRBQZhIAzF22bQWlArQVOXPDPEiFaK2goighhLmB9CQAhFpZb0PtCHyGYc6TdbclboY5W+VOeLwU8MMSGLISqNPuTwdzaNFJlGj5xZyeq0odMQ3EhwnlGMyF6y3W5ZVSuMqUE1ZAEiOCo9cNJprncj200gR09tPWdvnWCsI8mm4NGDL/j0GOiWBdOw2+1IKXH//n3eeOMNlqsFdV0fI7G+72mN55//znd486zh9//9/8Le75hZS+h39LstbVXh/I5vvfUG+23g048+4e3vf5u6sQg3ImOmbhvefvMRTWPY7Ue62nK+nPFr33mLxw/vkSvDGBNhGNDSoXWJjtTdA3FqWd6G8QrxOcBMSolIKuInd7gBJVrNx1qJksWxaGlR2t6mVVojlCRrdVQcPjz/XRDP17X+dhDYO8dbArSuse2CYW1QAnII5cOgVButKbmPj4GcJeSIlAIfIj54yHk6cUVpmd3tC8eEd47gSuh+kG+u6xo4oL6K9xE5kUMsGGmjQCtGPyCNJmeQCIyUEDN+CAXYNySebwP/nz/+Gf/hxxc4ec78wZL5DJYzaGRmps6p9JJUb0ihYOs1iugHVI5YCbWRmK6Z4LUgiRgFXdccjVqpwvBKIpOkJBmN9xGpKqQ2iBDIWiFNYNt7eh+J3pc887AZpZo+n5I3KqUwUiEPLO7p4lRG8t0HDcuuYtlZZPb4WA7bcRxLWeUqU7cVUmu0BG0Fye3JwVPLhtN5h4uGFBLjANZEgt9g23OkaMnJc+/effbbHfPFkpQV292OzWbN6ck5y9WSbtaipJouZiL6gUb1VNXIjQ08fuOM0Q/k5Fi2DcGPBDSVNQWu68ph8qO//BE/+OGvcNq1LJuOLCRPPvuMqtYsFyc8fvyQt956m8V8hjKGm2FgN2TyGNHy1jm8ZOhTKJ+ZWmKU0D2mojgcY6QfevbDns1mw2azZbffs9lu2Wy33FxvybngPqrKYq0lJclysUQIyWKxxGjD6vSE5fmK07MzTk5OmC8WdF1X9vvkmOAgYHInNfgK1lcSugsShoSkQtl7jLYBnxFSkYIjBk9ldPHwU3EtxEhKk6gEEL3HKEFwoWipExHZIUWpknofUCjCFPaXVMAzDAN1TSnwTDmQn0IjpQ3Be+LgyDljq4oswYcRciSFSO8ELko2+8y//YvP+L2fblDNfb51smQ1r1AGFvM5ZsJKW+MRUSOspNISJSAGx367ZrfdkDS4FJAqTWGxJEeHpKa2in0Y0cYSUsb7iEBhTU2MPbVMWC2RfULHTKOhbjRbGXA+4SKElEkp4yK3+SICKSMeUdplJLSgUEObmrmStCrTVYrkB8ih/JYSpT0ZPdcvnlMZSYwJMUpSiqSc2e97cq5o6oZ+c0M7m7O895iUAqO/wtiGrmtwQ6SbnSOioDKamzFwfnbO4uSUMYxYLEZZrBSM2yvWz95B7p8gU0b6kU4ZrBAoVVMLGMeR3X5LCIG6d6z7TBJgkQxXW9ZjIDbFOSCha++RhWS26kBJkha4mNgNI7t+hxAbpNFHD3owdKGnAucdAEvOmRgKWSrESAyBfhi42V5z+eI5F09f8OzFFRfbLVs30u9GyAXYc36y4vzslKdPn3Jyeo41DZfPGz54/2NyjrzxcMHjN9/k7W9/l+99/wc8ePiI0/unNF0H0gKKNNGjvspRx39LQy/eY/IhAMiJapizmDxa6UvmVHDXOQZIESmKvkiaAAwxBnyIGCXo+6H002PEj0N5w6lgxmMIOO/RUxHKjY5JIQFrTaHBxghSklNkGAeklBity+CGOEIcyTnjfGLMNVdD5n/6jx/x5+9fYLr7PLj3gFWtaCtJ2xWt90N4pZVCRoUUGUUqxJyhR0vJrGmJPuCHoVTAhaCpqymUK96srRt670rKoTSjCwigsgYfS92gsdNlyRmfM41RaCUxIeNiIsRUsEQpkXI51LTUaAFWleKilhQNvK6hsYa2slitydGR04SqzZHoRtq6hRzZXF9TN+EYkg59z26/Rzcd+03CKhj2I3pnENGRwoBulpws75Nmc5TpgEjf96zOljx86w2EMjRdh6wVFsF4fcW7f/kf2V59zLwBiSFn6NoZN9s1i/mMZdfincP7wn33zrFQkbcfntM1FSk4rIiI2GPampOzU5pZg0+O6/UVTbdAeLi+3rHZOza7kZQFUhfQkrUF12CNRWh5NHQBU2ieSfFl+q73nuAcfhwhJk6XS1zO2KYmmgK5Xc5bTtqWX3njTU7bEmneu/+QnAS7y2dcX12xWj7m3tkplVEEN7DfbmhmNUprrFRTyvnVr6+cpnrIfzKlJJ8yxBAQsYA4cgoIIsRADh5JBKFKLzNE/OgnZlUgBk+KnjCGIlEjJbW1hSGXEkoItJQMuz1h9FSVneoBmtH3VFoSRkeYMPVKKXIYSh4dM2PUbJPk9//yQ/7iY0998gYn5/eZ1YZFJaiNQmuJFpkCrc8QPcGVE1zmAhQiBqKfIhctSbXFe0fbVNipvXgo40gJoraEDFJojNI4V3rQKElKBWhklSRpSUiJKEVJN6xEhYgnkDXEmEsKBFgrMVJilcQahRYZoyS1EeToqG1LCgEhIYyRJCK6sSQR6bcROWtxJFSMVFWF9x63XWMyiDxStZbGKpCJsb+CONKmHqMC7iZgZucIXTgLSkpOlrag42yLMoYsI+vnz7n69GPG9QtM2iNjTUiJEDJ9PyKlpu8ds3pGxtK0HfNFCa+VyBADs7YmuIGb60syke7+Cls3CGXIoyRLxW7nuLm64mazYz96pDY454HSKdK68CWsrYqXn3AKAjHJYpVrMylkFbRhCoz7PRfPXqCzYrU84fnNGrff8ubZfXabDTZDZwwmJ+4tFsQYOZs1jMPAvWVFaxa88ehBOcgnNSVBPBaYY4wIWQQ7Dj39r2p9DYZeqplIVcLAlBGxcJ6VKb4/eEf0I1pm3OiQSpecPsfJ43uSdwRfACphdOSYSl/dTx5MSZIPVFpjasVutyM6h54AKDknQhIkH0hTrSAAbtgTncNHgdMVP/7sinefjZycPub8/n2UgtqIAoXVGmt0AQQRcc4RY0TnMhWmJPele6CMJCuNIJJjIc+0lT22cKwxhBgByRgjyUdydChpqLSCGHERhNW4kCaDz1gpwGicj4TMsRinzK24QkqJurZUWqPIWCURuURHtcpYLaisJrqePnpSgnZeU1cSrSXGSGqrsUaR8ogPEVtZupnFe1/osykgM2hlwGp8TgzrC/ywo55tIW6JpkZQkMvazpDCYVUguMz65hlPPvqMiydPEGFHbeVEMlKMg0MKjR8dF5eXXDy/oWmKR1wsFuScePvNR/gQubheoyXcf/CISMSsGrSpiFnj0sDzFzdcPL/h2WcviGSUNWQhCt5gQlEqpaiqcghLUzoMWqkCWqIUOLOIB+QL5Ezwns3NNfvNhlrX6JVkv9lwfXXFo9UJy+WMympspdnvN0hpub6+5v79ByxXSx4+uk/f75m3TWmlalX2UPQMQ8+cFWQKsEekUif4CtdXbuiHCntMmRwTbhxRQuJTIvqE0pN3SwEEBfYYAyKX/naIHknCjQVwEl3hTOcQp1zU4scR3TTTgRFKL1qpAoNViqHv0VoTJjpmDAEhBCEEvM+MXiDsnA+e9bzzbKQ6/RZnXUslPJVVVG2NrixGSPTEzgvOo5Wka2tEKFp2IkeEzIQcjmCeICCnQFPZEj7XVamqUzwsgNQKIQMxJKRUOBcLG09IfEygC5YgKFlS0CSRWRAyGFVyb6ESakonck4Yo1m0HdENkCJKCCqjqJSgMsUj7rdbVMrUzQxSQktFbQ1SUTymSGAlWWa8CMzP5uQMLhQt/egHpMyYSiNtTdahpF/DFT7clG5HVaOUpdEB7TPhpi/59rP36S9uECFydu8cqRKEwM3NmhASUmqMqRCockCjEULw5NkFwXt2/YgQkbauCG7AJWiaijPVUDdLLi43/OQnH/Puex9Tm4roA6N3SOuQWmGNBj157SAKkpGESIoYNEkrTLydv5elQE2ttRADu+2aq4tLhn4gZMdycUIKiRgCz65e8N3vfJsYHUMYCbJjfbPh4mLD7MkLfuu3f53znNjvNjx6+ICUM9JUNJWl3+0QTUMIAV3BV1d+e3l9RYY+oX6EJ0lHFhVCSpQs7KzkA0IUPHqeYJra2FKtlpLketwkTaSlJPmIEpI0QRcL5Fsyjo6QSm/cDbtSuY4RaRtQmpgF6+0OITL7vgfHpFyaMEqVn30k54p3n/S887wvSC0V0cLR2Ia2qTHWooxFJoEWCmNKRVSb8p5yDORKFYy/dyRrCa6IalRWQbKlCm4VGY+xmpRyARIlyCQqIwgIUgpURmKUps4C5yO98ySZSVpglGB0EUUiZEFGURtgwhBIKYtWG5nOgIsQpwqwnYpOQmSMUWSrCc6xCyMMsOg0VgsSgZwT1rRkkTBVhbKGKAS2qemUIQwONwpGH5CxRykBqkJLiTGWFBNaG5Rq0LZBSwsx4Pya9foGnz31vOL0wQl1s8CHyLNPP+Oz59cEn0lZstutyTlRtx3KaHL0dI1BthZSkezqd477907RCrp5SzYdew9/+hc/5ic/fY++d1S2wkweWukSmocQMbJElEqpUtbJoJFoITATBkErVXAgWqOUBiSX1xs+/vgpu92AVJb9vufJi+eFQDPVZRpbQZIk71hfXbPdRULwPPnsCbNZR9c27Hee3X5kvpjTNg0iei4vnlLN56QQiwKvTAjUXWbyV7L+lsi4u3K9kiwcWSSyrIBCu5RKkaNEKo2WmZQjTO22CAUVJ0XJR71DJMgTAyqFxNCPkDLReSpjiBQvfeh9Vtbio0cIsBpSLPBamQTDuCuVfiBlTcyJiOH9pzveuQyEasG8MswqgWotymiElBhtSCGipMUoA1OrL6eEn3JYVdlyvMUAMZJDhesN2+2WFEs+2DQVQheCjJSaGApUUotETB5TzgqCT+Rc2oxWSzJ6CjE1o/cYZRhDJKTCT09J3iHDqFugBhFVKfrkkFN3QwgI3jOMA5BwsRCCjIMULN5lVmcLkAXhZ7VFIEsP2FiEsWQpMbMaO5focSw47lz4NnXTYqqWjEEIhdQ1WluQJVILydPNW7qzBUpawLLbOD759FMuLne4wFRw8yBgPp8hTTWJhTqMLHukqWsEgvl8TlMblssObRS26Xjnnfd578OP2Pc9WhtiCihT0pLZfF4Kr+OINqULI6WirgsU94hMtBZjJryCVqANUmqurze89/5HXF5eFVqxMQjjWW/XeD+iyFTKlMlCqeTzbj8gcuZkVVPXirHfsJw3PHnyCc4P/Mav/3oprmpJ8APBOdShVZoKWeurXl+BR7/t+YFECouSNUEU7rnURSIYUSJDhSgbnoJpH1wpYoUYIUYUhfEzjq4wypA4NyBSZrvtsdYQfCyeColAYmVCZY+SmiBLz90ohWhrolD0ATYenl5f8+T5mm1oCGaO0raQS1RGW3vssR5ZRkLgosdqg1ICpadQLgm0tlRWkWKpN5iUqC1UlSmEm5yL2IEXpATGlC6B97fIvkIlzaVGoTSIRIiutAqFKgShcSDGglxLkwqOD7EgsSjY+VuwAYXhpxU553Jxcyp7Z/q51pJ21mCI+OCY6XKQGatKJFMZlK2w3RwxcQqUaRHKoqSgykVoI0ZHSiNCC9AKbRuUaRDSIKQkxcKFt3Vd0pMk2Kx7Li+ecX295eryhufPLsrLzpKQRaGBxkyKAzF4alOEIquqZjGfAYnZvKOZtQQhmc8WE9T2fbbbLVJKqqqClLHaYJRGpMx8Poduhhv3VFNxtJom45qqwtjS+z6i5rQ6IuTW6xKRjKNDTzTitrI458hGUy/mNN2cvu/pGjvVkARdo6lqjZSZtpVI5dAqcPHsE56frxAPHtK0De18jjbmWHv56k28rK/I0AEEIisQFVq1ZFPjQijY86l86cc7qLhcWjhaa1IsIVXORVIqjI79vuToSihyLHh3rQ3ORcbRIWUJp72PpBio66oIFStbYJhZsd5FbrYDz2/2XPWB9R6GWCGrioygkgKjFdpKmrrBTCd8jJHFYsEweLQx1E2FMQo14ctr06CVIPgdOYKqNBZJ9hpjJN22pu+HUmGXqhx0HKa2FBQbCVIs4d0hvVCqCGS4kLBC4WIqwhxpLLxzWTagUbJ0JWIgx3xs/eVcCpa1NXjvkeTiKZQGIZnPGmIfqFVi1rZIFcg54sOIFQ1KS1Rdo6sG3cxAt5imQ9gFSF2ilzCihCYNYipsjljRTdh6i7H1ROoKCK1IIbC+3vPRJ8959933qKqGpm4BxW4/8OTJU9ppXHVhehXIyAEUFd1I29QIIlVtyBPIyM7myKrm408/xTlPVVXkLKjruhSAU6ZSuqAfhaTpGnp9CxkuHtxMvAJNyrLsNVGGUmhVJvVCiSi89+SQqCtLZStEVkQPViu8c3x48Zzv/8p30NqQgOB6JJ7FckGlBTIH3nh4zjiMKFFAUyFFbNPQtO2tXoGSRynwr3J9RTn6oQ+hEKrGVjOSrsiin2aSKYQyhQDiHD5G/DgiJ4mjQwtDGU2YqJ5aW/wY2Y/jpHcmS+inIzFkhBRYY0vuLhsuveRyvWM3jux9Yj9Gri9GQgIhNT5BxqB1RstC3TQIUtII3VJNyqJSSowxNG1LGxJN22GrAmPMxFLoGj2EESMcQjtyGhh2I1IYtFZUlSkFMmtxoaQ44+gmHTJZ2ikUz15VFSEUaeOcE0KWDe58AKmpjEamiFAaoQw+lWiIZNj3+0LyIZewNAViTtPhCUpOFGJZXn9X14x+S2sERoGymqYxKC2QMqOtJipBkpqIRqkaVEvMhhwFxAw+MGy3uGHH4DLrfmD79Ck7f8Hs5CHNvKW2GiMkw82Wy6fPyDFzs97z5LMrlsuIPLWkFGm7Gdpec3WzZtcPnJyclJaryEgB87YGLSaY7chMW0xdMV+taJcnbPqe7W7HbrdDCIG1pZKeXSgOJRWcxiEhb9u2bHp9u+2FVERkSfFiiYJSTGiR2W73XF1dstlsGUfP6aKlMgrJpL+Qi3S5J7PZbHj+/DmNtawWc3SuMVrh+sD2ZsfHH37KfNbynW8/opktqGuLtoZ6tmC2WJTXdGDQfQ31uK8IGXeHfqcsup5j21mpnKeCwkKqYujJkFNkjBEx5SJaW2KI+An84if54WEoYJk8qRqO44DUgm7egdCkJLi+2fDuk2s+Ww9cbweGJBGmxtYtMQeUFhiVi0pszrR1jULQdTOUsVTdgkdvvEXTNbRNQ0qJqq5RUqJMhVCqqN9ICCES/YgYd1QGpHCIPDK6DZubDRnLMAw456irGqk1prakCLYqslUhJFIIZYCBgDiFuKVYJ/AhYI1BKs1+dOXA0JqYMzH4Kd8vXY0UM8FHSI4YymCMGBw5RgQZOY1UijGz2e55c7VEhwqZQznoTFUKUurAGoyFuSULOqv0kieWYPDEYU8ad7j9hugDOVu2O8/v/cmP+fOffYqyM5ZnS9568xFv3H/ATFdsL9doIUurS1o2mz1mysG3+z1Cabb7nrDZ0I+OrmuptKSpqnIQGkvb1MznhvlygW1n1G2L84FdP7Lb7hEIurZFytIfzwhcSnjv6GxHCIGh76kai55w5ge2JFqjbMEN+JhAakQssO2nT5/yzjvvsN1uEEgqPafSkpgyTVW6KV3bcLXpUUIw7HtkSlwGz9lqQWNblFZcXtzgvOONhw85WXTU84561uKloJ3N6WZzlDZMzBiK2/tq11dg6J+D60lJbuZQP8BUI9rtidnjc0YIg5YNqExVNUQ/gEjEkJHSIkRidI5+KC0xJRMpBrQSyJJwklTAScXeaT78dMt7H17xYr1F1RVCdDRVIc9UOaOMIKVIrQ3z+RxjNLqyrE5W1HVN27RoY5jNO6KUKJ0xorS+CvZ4IPtI8o6YfGld5UQSDqFtUa1REmNn1LUr/Vid0WqOUoaUBaKqjrJOOWfGcWS7kaUAJ/MkcRVRWkDWiFyqOrVWpDHhYkJojUyZqjKMoyMD/ZjxUSJ1DVLSO1cQiRh8jGgtCGHA2gqiYTumMjDy/JQ83BTyCwIlakiWFA0pCLIvYheJQMo9furfj2NPjqEAcazFaM3mxQ1/8dOP+b2//IhnFwON9lxd7dHUNLLFnhUix67f4UPi8ua6YMV3e+bzGfveE7OkamboGNG2RipDXRsKa9Gh0DjnMO0KWbfopiWEhAuBqxeXuD5gdUWOI13XlGKcgpxHBBJTi6JJmBW2ZDAkkYmydCa0yhgtMLZhP3iSKpLQ3vWM3pNyoqktMkOrM4ta4SJsh3IdyJmayElbcbaYHzsgVoBJAULEqsx8tmC5aAl9TzAN67TBLmqs7ZC2I0l7lBf7OtbfGgJ7OHnuThIVUqMX9xmHHWncQixeMIRIiIUoYIxFJI8LIzGEEs7GTE55Arkkgo+T9xJESpi1H2Zc3ux5/+P3uFw79mPGVIbZrC4XOQZCiFSVwmpFVVnm8zknJyfUdU1IkZPTU7QxU95foIhZ26KoYgwKScyZRCZHXybL5FDEL3JEqkJ5jaHkyl3TcHp+TkawRJJzqbT7ELFti7WWq6sruq4jpcR1vcG5UlC8uLik7/eFyTZGIBautMgYK0sPXZtJ/igQJ6y8JNDWinEION9TaVUUaTCk5HHjQM4BgkTVhoTm8nrPg++cUXcGkTIuRISa3msq0FoxHUZWGOzEhpMCuq6dhBsc0Q1sdzs++ewpP3v3fZ49v8TnGiML/Hi73XF1fc291YJu1iElbDa7Y4X7IHFV1ZaYI11X47zH+5H9boNvK5q6oj1Z4EOgbWqGwbFYlfyZLFjfrHn65CkXz66wxjBvO4wx5e+LhKKZ8Oul05NiCbeTAh8clagJSkwppaapKmxVM/qE846QM6uTE+q6YewHovM8X+9Juma5mDNTA5JE1xqaaoY1p7z51lsYYwghsGwamrohS4g5gxS0ywVJC/pcsVze5/Txt2mWZyUtm2oH8HeYj/6FJQTUc5rztxiyw6cR4XZlwEEuJ7UbdyQ/kEIo7TMf2G827DdbdusNwfmCRcbgptbSxdWGJ5eSi6sbBu9p53OauUaphCQhZcQ0Fmss2mjm84aT1Ql2YhQJIQq2PEWstKx3G5wo1NhqvihFQwmgMFWFG8tUGWsUxALoGf1IVdXHTRSCZxyZCjqlwh5iLqG0KCAerTV1XeNcqagvV0t22z1d101Ves/Tp88YlS895ZTQphBYrCwh3RBDkcKqZkXoYlKCTbXEB1OqzbmAb8ZBoHJiv/ds9xt85xGpQSfP6bLhe98+QYaRSgiyFIQUMXUFWmOqCm0bTNUilSptUa2IKUyY70C/2/H8+QX9EHl+uWazH4gCiJ4UHM1lxflqNiH2OvbbDUrJou1m7RGVBiWdMlKw2+1wgMq3+PJhGLl/dlJkqeqGnAuH4urymsvL6xK254iWhqY2CKDgjgTKHsY7ldalUbr00VMqUVnw+CGjlMFT8va2W0wcBMHm5hKlFPfunbO5vibkzGaM9M+vCRkqmZk1NXWl6epZAcFowRgGmrphTIlhu2OxWjJbzIlkTDdjde8hVTunmq1olmfIqkNI/bUrzXxtI5kEAt3MMavHxNCTwohgg8KhQsmRQioV9XEY6Xc7ht0e14/kmNBCE73g8mrN3ic2+5Hnl2tCLsouTdswm3cTOcNhrWKxmBf537rGWoNPAef3WAuVsTjvqKxGTYQOJTLeDYXVFn0RsEgeU2m0yOxdT20NpFJ0Gd1QoKZVNdFkA0oUyG/IgCytOaEEwTuCD3RG0/c91lr6vifGiBRqCsP3pBRYLmdlTgOCmBLDMLDf7Whbw/pmi0CyXNRTwa6wq5ZtCW9BI2UzpQaQK4WrLN4rXFvx4uqS6/2G6COVslztEzdj5LStyMFPYbgELQtQZspfrbUIZYhp6nP7sUhG+8IdiDGx2QVu1j0hQyLhc8bkYrTX19dst1senq/IOfPixQtiTOx2u2MrbDHvEGS0FMy7hu7+Pfq+ZxwG6spyspzRzWbUTYPWluATm/WOq6s1H334WRGh1JK2sczaunQhciqtUFNC4RgLKlKrkhZJJSDB2O+wdUXyRdaq35ZdW7cdbW3puo5+t+Phw0e8ePqMi/EFYz+wDVtSGLFS0FQGbTXNrBRxR5/puo6rm/6I1NynyKO2ZXmyZHX+gHZ5TjtbUDUzbFWj5OeGR3xN6ysz9M8L2YvsQUjM7ISc3iCHkZwSOmfC1KPOueSuIueCsJIKLwQpBMKYcB72vS8naRRU7ZKZAq0VXdMeOdfKWJYnC87Oz4oRhliMVtYMw0g/9KxvPFIK2m5FVRUjyVP+X1lNFPmoI7+Ydez3PaSI1XWBOuZIXRkqW8LD7WaPFKKMdvYCn4oXEbJcYFs1hNiTcpHMchOfer/fY60tFeCUGV1PFUpeWroNhkeP7jGOI5vNlsVsTQyFK3AgPhRZriJXVHJ8Qc6GmBzD0GMkJKvIjaZt76O3GzZXjsubnoubnrNtxaKd0dU1qJIWSSkLu0vJyZsOIDJZqEL7VQJrLS6HY5swZUU/BDKqjIoWJYVr25b79+8jhMA5N01IMTi3L0Y1HXg5eZraUtcNdV1GJ+VJ3rtril77QWVot92R2HOz3RWkoNQkEl3XspjNJsRiYZ/VtaWtqykdKJ+/khIjJP044sd+okKbCaRVUJshFGGTxeqEk9UKJQQKwf3799lutgQ3IqcZ8b3PRK3oZIdypX+/vvFsNjc0dUM3L1FhPZsjtEbXTdHozxC8A3ZoLYt8V644TO75utbX5NEL71wgkaKhqu8RuxvwPT4FcFtwUHSOJIKAEkWnRkmJyKWHvB09vRsx2tLOmkIOsZqDJl/wnq6b0c47pFH4cURMskgpJlw/Mp/NqYxhs90jRSGoVJWh7weygKab0XSzQnhxjqquEUrigkcZXabCakM9X+DHkSA1IsTCMFKaHDwJMfX1i/iFFJmcHctlx263p65aNsOefj8WdlQOWF3GNzVVC6kYbCZitEEgqKsaLTXLriV4XxB3KQGFCBRLvxClanI+KJ9ofDsdKiERI9QeomjpUAx7x/vvfICVmbOqwy7AGoE2GWRRM42+R+s8iVAWKKbQFpRG5DLbLuVIFpl3nqy56SNQQRYoZQrwptJcXF9SGVngvXdkwrquKynUODKObnIQpYuw2+1omobFomK2mDP0e9qqZj2MPHtxiZIKYzTr9TUpepSWzJb3qK2dFGCKKpHRFQgYhv0Ejmkhg/MeFxxd1yBkEZaIbkRrCMkTBo/0mQqF7ToWsxnGaO49vM+zJ5/hh4GUIcWMELJ0RyhRBTFQN3VJx2yFVOXA00IWQlYCIyXRj1xs1mhbUzcFcyAFR/HIr2t9bfPR8zSjWmQBqsF2S3x/TR525AlWWVQLJSF5+rEnpBJ+amOQQSCjoDPmiGJSWhFjaXOV8UlVgaPaklsewBAxFp316D3BO7pujvOZOPWtjdH4aKi7GfPVaZGrmirdpq7ox5FEnjDvZpKuEvhpeFDynpgzbVUzDD22qksvfJLNqms7iVhm6qpGCMlquSLF4pGtsfT7nrpuqGxp6QUfESLhnWeXdoSJcdfUBiESXVdz1K/XCj+4YygvxDRqWGisNlQmHCW31JhZxQoTI4Ms4olPPrrg6dmK5fwBWoKUCVNXCGGQE4swxTInXulpgkyehk9oi5CaTe/4yYdPcEmhtaHrZqzmLbUtYXxMibppQCr6oSAFh2GgnwhH6TBDfXAgFVc3a7TWrE5bTGVxIbDtB262O9brNSkX3f3VogUSbWM4O13R2BqtJCGX3FtJCSLTD3vENJNdG81+vyeTWawWSCkLss0ntDHl2npHToHeZ2TKNN7TnSwxixmPHz/io3fe5erymkTRRGjblrYyKJlJYaTrOpbztrQNN/tJMswWodIMcopaldaIRpKEIoQ4HaZfFIv8qtfX5NEPAnvTJpES3Z1i+y3BOXQoY5OzSSQDMrbICnT2gKdWGtNJToQuIVWMR1UQ50u4r6QqPdMcubnZ0s465vMZUDTocpbMVwtG54g5UTU1MeViuCGijGVWtcyXK65vbjCmUErvivXZyhJDOAr+l4JXxhhbijqigHaEEGQS+TgyqYRy+/0eo2v6YUdd1SwWM7bbLVoLstWMrsfYGW4ciCnQtqXI1/d9Cf+nvJ4UjgcYFHSetNWxZXfQC/feHxVUcs5T4SuglUORC2NNWJwLvPPuBzSd5rvff4zSkjGAUoUFKIQkxIw1GmksaIvSBi0ycdRk2fHBp2s+e35JREyQV0+/W7PfjCzmc04WBdo5n82QXcFUfPLJJzjnjkzCLARSKbb9gBCChw9PCilJS7Z9qVOs1xuGoSdnSVvX1FbSNjUny/ZY0IPb1NFaO3VTiiNQShVcQ11PEk+lThBjxFYVTNFFSYEUrnc4PzJGj6wUpqkLHiEGtBRoVYA5q9WC1XJRosBJYPP6+rocAG2L0Zq2bYvUmYAQY3FQVfHeRpXajfce+0UD+srX12PomVs+rQCUROgZ1fJxEVnIjuQGhMvgIeY5JmkQPcpG6pxIQRBCCaf9RGIpuJtJcFImEJGmqXFpLK0kUbii2pSLh1ZIrVHWokRBSCUhGZ1HmRINIAviTIg0UVuLSMVRiCClI0sMCmGG7ApSLqXSsgueGBPaVC8pkuRcclsmQQEhM1WtGYcRYw2IzDjuC1R2mgwyjmPhgE9yWNEPGH2r066UKq2iCQF3OIRSShMrKx9vLwdDpq4VUrRYLblZb1GioMA+++QZSM/b333MYrUCWSbEIjRaWJSpkNoiqxqjNX57wzh4Nr3kT3/yGftxAtikTPQFR1B0+QqJ5PmzZzSqsMOc88xmMwBubsqQRakN/ejYbrZUVYXUl+XAiqVTcX19zfPnzyeGXhG/LCOmMpeXI/NZw7xbIuKtkUspS7uxshNnomgMWns7e+1AYlFKEXPGezBW4Q80apEZ+x1PPvkYVVvcfiB5h1GCShXuvut3XEfPycnp9Joci8XieKAcvg6HcKFIe2oostfaHF9f93UB3O+sr63qzkGmUJSfwSKbFXa+J4Ydtt0iRo8IEFXBqGtbTUW7QHIJOYRjSyWlab5aXU2CkEW0L4TAbN6V9sbkkXNKVJVhN45UTQkFowhYbZDKMIQBayqy0PiYMbYmuC1a347Msday321h8oxh4rQDDMMwhbylxVa8qDwCYg6rqH1ODLLgpoq2JGqJUmBszTAMtwcCHA38YOzG2um55HSohAku/LI89mFDHSKSgxwUZJSINLXCyDL2qO8HREiIceD66ROMGNHf/haz5QlJW7Q2CGWLkWtLEJJhHMjOkSP82V99wE8+uCAJjbYWhESLyMm8RdcVVdNg7NT7zwkxQXDPz8959uzZlN7UeBS7fsPgA9JaeucZh4Esiy6gG0e2/cjZ6SnztsONAy9evEAQOFl2hRLcMuX743EmwDDuWCzmCCHYbrfMZqXVZ6Y08IBdL1FSQMjD4M6KcfRE79ntd+wvHS4FrDJ41xfoa/TkqMlA1RUJrnEsgiTjOGKMOZKihBTHCCNNh8EwjsyqmqpuGMaiZvw1UdBfWl+LoU+TpgqIRhRVuRLWGur5KcmtEeMNchiQIeNE8QxCCtwQiaMvVD1RvKCtNEzeFSEm4xFoLakqw3ZfsM5pCnFzTkhtQCZiKqAbYxXKWGIWBdBjbAGLpFQmzWiN98WYD6CHw2SUg0EZY9huN0hRCDY5BaSQGKNJ4yS4+FKlWROCo2mqY24aYyhRgQByorKm3Dfx5Y0pUUWKRSNPiaKFJwDv3JTPBypTF7quLO0iH/zECdfH95ByJnuHlkyeXrBYFG60DhHSgK0hjQO76yu0Mihziqk12tYoU4w4xUSOiRQS7777Ib/3h3/KPpRuhzEVSipaC3VdM1su0Lao3YzjwNAPVLMZdV1zc3ODD+EI87y6vKYfhiIXnmG92ZbOzL6nqstwjAcPH2KtZbvZoFJkuTxBiQI2GgfHfr+fcv6I8yPWWs7Ozo6pznKxpGnKoEetp4JpLm0w5xzGlLkAWhuCLzMEnB9ww8DoRvrgwdZorWiaGlOVEWN1XUZsITjCaUv9xwCwXC6pm5brmxtm81kpNLZdiQKrCqUVNluatuUrJ5+/Yn2NHh0OR5W485PQHWZ+D+92KJ/ISWK0IA5bcoqlVYNCiFhGFOVpplWW0yTWQv4IIR1ZYcvFjPV6g3OuCFogqWwD/bqohCAhgrY1QkpsTRFuUIXDLYTAoQgxTrrjquimG0HwwzEsFkIUocK5JuWAEOBTQusav3dHuWrnHOM40jQNOQW8C2hFEabMAZkFRFHG/KZc/p0z1orjvDSRAopEDoksRBFpnOoUTVUhjULX5fJJKWlDwcuTpwMhZ3RlqeoKMclMHzz+MAwoC1a3KGtoZi2Oiu2YqL3CBFXUcnPAWk0tBT4InlwH/sOfvcPH19dQS2bJYI2hbmqsMazO7yEJGAGVhKZpUVrQdDXRKZ4+f8bgA2OkyCTvdoyudEZSTiwWC+azDqLHOYfMiTCO+L6fJKXPaZsaP/YFeegz2ijatmK+qEk5Yq2ibZaQJT4EjC7qw03dkHJJyw6HsJhagtUkGZXzgNIQ44jImVlVs5qf0DuPqmYYRs4Wc4SAq6srqqomZ46yV/P5/FiDSErx6fNnCDJVbakrg4gBrRV6UhDqZh11W3+9Jjitr63q/nlg/hEiKyRVuyC7M8ZhT/YOS+E3Z23AVuTgJvaVRNgCJ83ToPoY0xGAorVmGAaGoWexWHC92RJjpG46pFLMFjMyulTzRWmV2apijLc96ZcmkMaE1uLYJ0aUnO7uVI2qqpCyFK5yZtKRK5FGTmm6zRfMuy5qtUWSup4m1RR2lJjSkZxKpfhQ7CuhfKaua2KMNNa+FCGUfC8eQRYHD5Kn/5TW1KajoPYC2hia6TVsd1vcMGDqitoUOSVlDUJrqrouIg1KlQs4FRoB+mFDv3f86Kfv8qOffgi5pqtrLMWbLZdzuq6jaWtEClhV/nbXdSwXC/p+jx8OQJvCVpzN59Rti1TyqNF/6GVn71guFscq/eAc81l7jFQW8xZiYD6boaRGqZIiySzK7+eINoWRlrKfvHZEoRjH8aVBIcZafLgdC3ZysqKuKnY3PT4mtK1Jmz337p0jRQnF33//fVLKk0cX2KrBVpZxGBFS0DQ165srUk7MZnNubtbknGkXK4QorcR21jE7OUPw9fbPD+tr9uhfskSFbk7wzRrGPdIXDDSVBa+JWgN2QmGVQpcQehLcLxJKzrlj4cm5cQJd1Iyu5F4hBNrZjBBLncA0FUXXIB2LWsdpJvJ2Btohzyu5VtkQh7wr50IHjcEVkUQyZDFFF4IUS3W1sMIyu91AXRlS0dSALImxyGU4V3L5Q7Rw2HwxRpqmYbPZTBTW2yk2+33pCzdtQyk66qNnKkIZssBjRZkOGr3HeUe/GY6vXU8HA8mRcuG0Bwc6VOzHAa0b6qbIWsdU+PLODXz6/AV/8qOfsRslVtfoqqYy5bM5WZQim5EULX03gCzqv8M4MGtb1CSSIZUkjUXPDSEQqXQZjDEMw4A1GqsE6/X6WMF+6623jgIfUhbtgKayKFmmzGw2W87OTyaGYWCvdohBTLoFgrbtCtYg3aZVIQSapmEYRra7bVGuaQo+XklVIkmpuF7vqGpNrWqsrnj27Pl02Et2u910QCr6fkddF1SmMZrnL16wXK4Ki1ApZssTVqf38DmjTEW7WJWOxlcsAvll65s19KkqntGoaoWdbxn7a/SoSV4TJ0kkaww+3Y5dEkIRw6Q5N4XrVVXhnKPrOsKNL0SMqsGH4oWb2axws6uakGSZbc3Uz5yM48AqO+a06XZmdqnIhqPQ/yEkk1KSgwAmtZkARghymsgsU9GubDLJkEprZxyKx9jveqxW9Pv9Mfc/KJMeKvzAsdIfJy71AUxS8tGMUPn4Og85YhIS532ZWBsTQisqWSOtxjk/RSol1ipdT40yhmbWYSqL0OUgTSnhQxF+FFKx3Tl+7w/+gp+8/4ykW6xSWJXompqTk1NOVivWmzUxOHZ9DzlxulqiTXkvwzAUNd8QioceA4PzE2ilFKkOh61zjm7esd/vj5/NMAycniwY90WBZb/fk1OgthX73cjoerquo5s1eB8ZLjYvtWXXNz1d1x7ltwDatp2m3u5vK/BTVT6lQlzKZBbLjpAECMk4Fhmxk5OTY4fDB48PI8vVnNOTU8Zx4OZmjRaJs9Wc2fIU2y649+gNsjSYqkFXdZkIPCkofRPrGzN0IW7D95RFoaw2C0TdoHeaeJBxMgaZLTmMCBNRsoRjRityLsCGEALGmGPhq2mbKezWCFEE+ZumYRscIZWe+ehCETzM6SVPfqhS322tHS54DOG4MYCpEh6L0OQUepcCnELKxOhHhFDTBhpZrZa4sUcIiffjBGxJuDuV9UM1XU3Y6EOacPAala3o97tJgUWWyq61NE1zfG0hTO04a4s0EuWztnVFcI4wDMjJiKRSWGOIfl/IOkqCFISUsOKA8DtELxrnAz/60Xv85GdPybpwrJsq8/jBkvPzR1RVzQcffMCL58+x1tLNClYg5cTJyQmrxYzdZsve7Ur42rRUTakpbHc7ZCrX6nDoHqbvyMlbSyl58eJFmUorYDPsGbUghppUpzK1Zgys11ustVxfrdntRryLaGMYh4GUM/fvndF0knZSc3n69CkpJdrZjGqiEo/jWA6ivmDVq6pGCkmKAWNqwm5gtVoVqvF2e3y9upLUtQVRuifzRcu9kzmnqwXdckmzPMfWLWjLbL6kqtuSGiKPae7Xvb7x0D0fvgRIO8M2ZyT9EUpLpMxICSiBsgYfAzFFYk5s12tSTHRNxzgMk9fu6IeeyjZklYlCM1udMfpA7xJV3bHbj5MKq8OIaUTuVIkHjkqyYkIt5Twh3KREKFWEKymoulgwJygpiJNia0IQE2Qhp7ZQZAweoRXKGBhGQsgICqovBjDVbYitjaJuSijpR3eLy7a6DKoQAtsU4oWLjqZrSovIDfTTwQSlZqCbplTLp1SAzKSYUxFlQGkLlNxSL2aMfigevSmz1pUpKVVlCr588J4/++n7/Ps//SlBNJyeL3n7u7/Kb/+j32C1aNlcvOC9994vWvdaMY4D88UJi+UJq+WqYOO9Q2pB3baozYaUi+DIYtYUzvcYceMIFFKNd57tfijhs1JYKwkh8/GnL6ZpsUXGOgTFvt8Ro2ffD/iLa5IyPHlxw/VNj1KWtoHtZovRinYeGJOgdz2fPr0phJuHD5HK0e/3KKUm7MMUTamKYYwgi9T3+nqD0hU++qORN03DvfunGFsO6RQ8866diqYaXbfYti0qS0SWixld11E3LV+LjMzPWd+wob9coCvaXS2DKAoy2miiBBd9GXNcVWThCySxaxj2e9wwYnQpsKWcadoWYxtCLIMDbNUQZSAmgZVm6o1PbDnvmc9a+r4vF0JrvHc476fCTck5xUS6kaqQLIL3pTo+RR0p+IJ5zoWSqo0kEqZx0YV7vJgvi169KAYnhGQcRrQ2QAGylEECFiFgHIeiXisF4zgcC29KSRrblHywqaiainEcERTN+AMoI8ZIRJBiLEVMSp7tYyLLhMiqDKBEEHPGVC2zxYqYEspM4ojGIqsZIif6YeDf/MGf8u/+448ZnGa2XPGP/sk/47/+P/4LFienfPzRRwybzZ1qPkd11aqqaNoWW1lIHp8C2/2ObtYVnLmQ9H2ZpNNvXWHdkfEuEGIik6i6pkB4p/nz19c7lFLM24ZhKMXaWdcwuj0hBkwGXbdFhHJ0tK2l1gqUolvMEFoxuMiLywtubm6Yz+estyPbzZbddo2tKlarVZF8NpLNbkNlLUydFqMUtrJF1KQqdY5D+jj2jmbSZpcorK6wXUs3XxJSwogCiCrqwLk4mqPL+2YM/pdSjCvttgzREfod6YjuimUu9wRqyIAwIGKRX6qrmmFbcNOmrpBS0LQtKRZGWwqpABkqy34/UOd2ysVLFbsfil6dkEX9pUj4S0rRvHzgB+ho6cPe5sF3QSm3YBSOhb0wsaQO6UDXddxcX1M1tqjBpkCMpQCltEJkcywmHqrRh3QkTrllETBU7Pe7Y6FwGAakmnTtmrbwglIqIpdCE6ex9Ec0VvBH4JKPEaM1ddOSlEHWc5qqECvqui7DH6Pk4sUN//bf/Ql//Ofv0a3epp4J/rf/1T/nX/03/1eUtnz62Wd89tnHPHm/qK8ecPkxRoZ+R1dXRTjSV1hdZsBZXTwmKeHCiJYCWVliV6KQbT8y+kDXVAQ3HguihyJkiA7nEyfLDpEzSgmWqwX7Xh55/nVds1wucaE4AClgvpjRNPVU2ynFztlshpSSi4sLNusdKWVOTizb/gZrDE1TUYb0ljQqUfZQ6AeapqFt22PhVGtNO5tjjKWyB6kqyXy5LMg/V1KRzW6Psg12v0fbmpnSZdLvN7S+MUOfzKKErIDIRVFU+D1MeG0hSkEmuWI8MiZiPoSnxQDarsWHONFcS6FGq4osCxoppYi1FVoHDrK+ylhGXy6KkApd6YLY0gZSIqTh2HY5FOTKAMOCaDq0sJRSuHE8GiNwNEAl1YS/t8ffV1qRY8C5sXg8NUVskwLuQW3l8Jx3K/4HCGVK4RiKe+9LW9Fo0AqXynu0UxSSs8RIcycaKAdUzKmISEzPoaTENi1imkqiVEkzMoKPPn7Gf////Nf89L3nPH7r15gvHvG93/we//K//VcsTlZcX17ixj3bzTXPnj9nvV6zWq3Ybrel07BZUxvNm4/voyeMeFdX1A/bkhNXlv1+X0QdN7viKeuGuh65uF4zunFKr9Lxs4SJlWY0QmROTpbURrNYzOhmzUvtx+Vyia1bpFYM+y3RgTaCxbyj74tWXyEpUcgyomjz+Ys1bddRVxKfHFXTgiqfSUjFE3dtdWzpFrCNwdgKHzMxe1CKquuoqqrIQ0/ch363Z7ZY3YFSfwOY18+tbzx0vxWHTmTfl698OxFaSTmJ8JdQPnmPm7x9zplxGJETlrueNmqcxBWVLmKO1lp2u/1E45RopQkTeqwYesM4jKA0Ume0rQkh0jSlxXMwrDF4BPkYnhZYo0RmjmSSg0dWSnLQDzhAVStbEUdfJrwohRsdPgQkBaxzKADdxV4fDP0upLWu6+NhcPismKiNWuvCBZ9SAcEtLkBOdYdh7Iu2nSndBqEUKYwoEsiMEpY0BC6vNvzB7/8Vf/WzT6A+oT05481vP+L/8t/+N5w9fEg6UHezp2sq6qpiq4pe/Ww2K9fEWO6fn7CYtSxnDUolamvIWWOs5f75Kd57dtst65sNm+2Oza5H6z37vic4h/O3fW3vPcYaZrMWYzWzect83jFrarquYbk6Obbncs6cn58TgfVmjSAgG828awk+vCTzfOigiCqQbTHc6/0G0W+5d37K4DwubJGyOKCqsgyjO0YP8/m8qAaFyN4nmsoSs2S3H/GRQr0OHmSZ7dZ2LVLI6XnV105L/fz6Rg09HzAz0z+y96QYOJToMkw5owY3HkcqF92ysqljKoSOMCmqFJ24jBQKZTQxF6+prcEHT2OLAEHOZcrqvh85m58QU+l5S2XKiFMKEYXpFA6+CEVUE2b7YHhieo1yMvLyVopSa05xquArQigij9qYMuZHFkBNjIEUA0LZMhN+EloUU5HwgKsv3j5gjMZawzCOOO+p6wpbVUTJkazR9z3OF107kBPks54KbAbdVhymuRzgp947TJYYETHZs9vsuXp+wXsffEo3P0XNT3n01kP+y//6n3H+8B5Ijfd7+n5PTpH9bkPbthy45Yv5gn2/Z940nC4WnK1WSDzROfbjSFaGhVbUVVG0Wcw7Hj96RAiJy5s1H3/6hPV2x26/JwdxbFP2fU9VVZyerJjNG05XK5bzjtVizsMHD6ib7ojtj5N66zAOSCVYLWfk4JBk9rs9zpUUyU4gmaZt8dqzdT279QYhJN1sRpaZq/WmRCBNU8g4UuGDp64sdhLS2O52XN+sGaNgtTrh5PSEummmQ0cRYiCnosu33WxpusVxUOfRGL6hNP2bM/RcvPjkayAbhJmT1bzIeOVCeU5KkHwESYE/jm4aCCgw1uBkJOSEGxOjj9hWE3CEMJYWjKnxOaCsKtVwWeaVxSBg8pghZeq2Zb1eo/UE8tiPRBcxViFynOCq6YiZPnhwpTXRJ7Jk4sd71EGSOwsgTWSUqRYRK7SRiBSRMhRoqlLEccAohY6BsC/AC2XLlJcy86u0/IxWhOBZbzfUbYPpmtLjDpkUIvttyd/bqqYoNRfl1Sgz2lbIDHbqChhbo2yFkAqXJY3VdCKwfvYZ+82O68stLzY9ylp++P1f4bd+67d59Pb3kdISB0fYjww3G/YX18R9z83lFdubNVJKHj56iMiZ7zx+yFv37/H02VNePH0G01SdwQgePLjHajGjrSxVpxBGoJuGR4sZ1WKOlxB+9BOePd+ShoGm7SBn5rOW1bLh9GTB+b1zFvMFbdtweu+crKqyvVIx8uAD1jlWq4K8DEOPdwNu8IQ0UjU1u93uNpJSknsnK9qqZj96YoYXV2uSH+iahrqeETzs/cBs3iCUZbMbgIGcErZqWTUz5ssFi9US09T4SSHp+qYHClApx8zm6hrdzI/CKd/k+ub66NP3W/13EFqgVGGBSVHy98ztMPo8iSdEV4o4okC5J5pfRT86fJhCvKmgpW3xsGVGe6kGl7FFAh9B61Lcmc/nAAzDiNWGcTpZvXcodVAqvaU19n1fACeIMk9bQPBl3rgQRe3mEHUoLcg5FK+USvogRJo8tCK4UjUvRlwQWkarMu1Taow1d0LwTN8PNHWN0poQIkyhf85l3NAh5A/Rk8UE2c2pRABNhxaloJSRxbMrTWcsMgdePPmM9YtnKFXx5OnT0qOvO95++y2++73vFhUUoO97bq6vS2jtXMHUe89+v6eua85Oz3j08BGr2Yw//LMf8Sd/+qfs+x4XPJlMpQRvPH7Et958g/vnpzx+9ADbWJqZoGoqzs/O+M3fqJh1c37y43dY31wXHbhasJi1LJYz7t0/Z7la0rUtXTejrixJ2aniX0LhEAK5aSe5plR0Af3IbLHAZ8mTJ0/Z7XbHUF9JmFeWebNk2w9cXN0gjcRRBCm2ux1+dHR1S4gjiKIcM5/PqdqW+XwxKRfJoq2XIjFncipqOoe8vHDTy/gnIQQxRfR/vlX3W3oLRHJ2pDxO0WThmx+KU3ALXClcdMHY9yQhUDlTt7NSMQ8RbUqlfj86mq6ASuqmKyF+8Fhdke6E+Yfc2NpSGNITm0xKyehGlOKYH47jSF3Xx42Uc8YaSwgHpNxUsRcH3jpImQmxUF2FzGgl2N7sitZ6LpmCmQZDaC0wRgKxVHlVxhh5fN7gA1VVsd3vkNZQVZYMuH44cqyHoRBvpJLIXEgyVpUN5cYRn0FXFdYWGLAQIKLj+sUTnnzw3jTH3RJiwduf3rvHgwcPODs7o64L3JYM2+2G3WZD3+/ZbLdAQZgNw8Bnn33Gw4cP+Xd/+Vds1xtiFKxO7yHbmourC/x6yyefPme72fNeXfE7v/2bfPs7b1DXaVKClZydnDBrW95+/IjL58+4fPEMN+xp64q6a1nMujIDXgpETuVrggIfro8xpb6DhEZkUqWpG8vN9TVXN7tjalS6Eh6tBSKWgmZzsuBkMWfTD1yttwRX6KyNqSgV+4StSjfkUHXfbrelpVbX+BTISlLVNUaX4Z+H+oFSijqEIvzZ99i6QdvJHHL+2vvqvwRDh4IR96TYgyiTRw8Y46wV0eeJzlnCVpXFNKi+FOH6fY+pWuqqJmQmMcYKHcqmL8W1kq+OzpHygG0WbHcDyta46QCxE8JMiMnrT5XwEAJVZY6wSOBWnliWiaNx6okWhFOBlhZYZDwW1LzzGCPo9zus1UgE47gj5TL9VSpJ29ZoLemHAVScXk+awEOZnCI+lL/XNg1wi4/f78tIpiNOfxoTLJQqxiwlUqnjlM84dSQQkv3mgueffoDMgdp27J3D2IrT04b79+9z//599CT1fKBiNk3Ls48/YL1eH+WUuq5DSslmsyktOgn/xT/+HfLNnrcev4FddPz//vD3+bO/+ik5BgYXub5+QYx/Vg45rRFCUzUCU5kicnHvnNXJkgeP7rO+uiDHgJLFoAVlWo4XYCuLNN1LxccCFEpUoiYZRXCSPnqkVkfoarm+pYU77DcE74uAhlCEHBCi4CXcMKCloqorUkwThJtj1V5rTQyBkApIarZcHEk6JDfN37tFX9bdjBaKAMpUaC176D8jZNznU5IYPfv9Gh8KNjpPvWk5Vd2DGIqYsTYFtz2JFYgQCKmn73vmpoSVKSX6oWiw7UeHUBGli9yvEaIQTVKi6zo2+wFbN3jvj0MNUwiMUyutIKRuCTM5Fx36Q3W9CAlMWm2yqMykXLjrh6W0JiWPUhI/DghRpqzsN3tidCgpQOTJ+xc1XG0UWRe9szAJH15dX2JUQ0qSqm2OOWjKieQLdrxt29ui4EEnTudSZIyRlCi8dkqLMITA6Adunj8jDnvsBFaKEUJMx2jqwK0WUk7YhIrz83Oy+xa79RW79RrvAicnJ+x2uzJNtOtYthV6t6XaDoj3P+HaDejPXnBzvWG5WmK0xJqK7W7kR3/5VygSy9NzlienLE/OaGYzkrVoQBqLbdoyeGHYIYlHaS+RY/HoUhzVdw7V9DJaCoTMpKgYvSs6gCnRNM3RI19dXeGT4GK9L9Eh+6JjMKVNq9UJyQfW6w2VLt2GQ+R2UBHSSuNcOKr/NnVRLjLalrnx06EPxVkoWfgI3jlMFZFafiPB+zfs0afqeg7gR4Trka6HKcdJKUNMxHEghxGjEpUR+FwomAWZZmm1ZL8fGYY9xtYYZUhCFA+qNdEHxjygdSEOSODm+kUR6JcCUsDIiujA1BqpMl6rouEmBDlFoneklI8X6uDZpZTEcEA4Taq16DLsMYw0TQU5QC5TXkNyBcbpenIY0SkeR+QWMorFjR4pNVqowk2XxfvmnIm5INequsZ5VyDBMRUo7jTD/JDqGa3L97pBaktGFQKFqnBuxA8DUo545xi2N0WJZzZDmZq835fe9Ok9uvkJVd1ibVVGdedcqMOTnrvSBmVKEcz7EpGFUIz+h48f8/TPf0RlMk3MuNGj+4G5KpNh14PDKIlImY8+fMZpd0pNw84XU9NVBjUvc+uMIceKbAzCaqLvyf2e4Ab8MOKNwNYd0iqQBrQhCYlOHhULUzEISd3MQCjcWMQ5pBDsNqWqfnG14fpmwPuAVIqqbph1LW1VkVNm7YvGwRBiwRxUDUlIdv1QcA2VRamKuq1JMdLvdiUat2VUuJzktZS2VLP5FNbro6BJ/gbCdvilIOMy5EgODpVKT9lRRAJjzvhhhOCRTLxiJYiy8NAFavKEB6XVkv9ICoJKT4MUhJJIqQi+9LAraxhGR7/fYNuO4Ebapi04a+FRKqGMQurJG+SEQKKUJMGRVaV1wZ/H6IkxAGoKbw0xO6SRSEmRYk6JnAovOseCihM5IXOZbV7VFq0O+aJAoiAmfByxVUXwgZgStjZIpaZ6QwEPFdljc4S/3gJ8Cv8dCuddGj3JQhlapQl+ILiR/fqS4IZC6GgLlVdIwXIxRyze5uzeI+quQ+qC8BGidBgEeWpJapBFned2ImzJP3/2058yT+Bj4ur5C7rVglnX8UN9nwsHW59Yb7bUOmOy5N0PPkUlwYP7K0wl6VtLowvnH9I0iDKDUhhVpJv8UKa5KgmyWRR99Koj5mlyby6a/SmWwmVVN2hjcY3HDWOpMwwDu+0OkkBLTVal9kKK0yQfjYslehhdT8yC+aJjDAkfBmqrp8KtRJp6YvuBztPUoRBo2oZEZnCOrmoQ0zBLbcyxSCe/AXUZ+CVBYA8ouZLLlFAxp6nSnss01eCLuLI2hpwF4xgKtVIk4rQBaqtJweGFQJt6YqoVLnhMAWUqgi+ghaqqcT6U3FoIvB+RUuP9eJsrMfG6J/TSAb12WM45xB1s+YFRBiCRNHVFHAfC6JAioRAlrJvQbVEITF1TNxW2tngfJwWS2yksPkzQXylQUhJiQKGORl7mgFOAP5PizMGrGq3JooCOlC16b4KMSJFxHHBjmWfuhj1SSaqmtPRkEmgXmC0sJ9/+Fs18RTPrCrNNUED9UlC1DcuTFcPwkM12zebykn6/Z7Va4ZzjnXfe4Yff+haN0dy7f48P3/2Qn378EbswoquKHzx6A7H9hE8GB7VikJkn2xv8B56b/RkBCEHwoJohYkJKVQqfqsydkyKRhaZqitBlzol+e0OMgXZJQf8dBUButfPgVnNA1mVASG8KI3AeBYNzDKNnH4rAZWUV0RZ0Ztd1xCRAKkY3QI5YJaEqxBWmvaCUKlJas9mENNRUTU3Kgv3gCCHS9z31MFC3s5LqfYPEll+KoadYvKaUhSeec57kcA8FrslL+aLJVtc1SpZKOTlhJYw+EGNGG1FURidx/e12T9POQBlkVVRGQvAkDuKNA7Y5SEGJMiV19ChZ1FlkjqRQhBuYprcc1lGVhltllyMu3RiyC0QfEKnM9x6HAZFDUT8RhZGnKk1dm6KnNjjUVB3v+7Ggr/qeRkqMOSDlMsreimQ454ossy75X1FlCcfOhK1siY5CIGWBHxy77Y7tes2srbFKlPTFaExdY6sa5yLdYo6aWVbfehtlGqqmDMzIABJSSCQBVdswW85pu47lcnn05GdnZ4U2W1c8eXrBYtkyCrje7unDiN2P/Nb3vk9oW8L6hkFVvMiOIXo+u7lhcJ5hiChZI+ynnN47o53PMU1VxD6yghSompZAZIiB6EdyvyFPY5eEEGhbtNIPVN/DoZxzRuVEEJ66rpkvFsQQkKbChUAIjmEcqCqNkBODbeL47wbHMAyMbiAogZl1pJgwk9IsE87/8FlooylzZDIIVa6lLvz1vu+p+562m6Mrvqnu2i/Lo99qpx8nSFK8epxaH0fvmst3W+mpXVQ45qSAH9wkWghSF/0uhSC4Mj/NOz/J6k6TY4QgRl+8gyoTPYogg0OagvlOvsgvRV9mkx9O68NGCjEi70gtw3QA+ID3A7J0q/HjgMyFQZZiAfCIytI0FUO/wfn/P3v/9axZlp53Yr9lt/3M8SczK8tXOzQAAiSHQ85IMRETipBudSFd61r/mTShC0ZwRM0wCGLIGRKGANHoBqpduaxKe9zntl1GF2t/X2Y1ugFMm6rqJldEd7qqynO+vZd73+f5Pf10RQg0zY4syycprZoWkaTTD5iDcWa32x0MM/vj+v5KIURSu0kpEFO++eh6+n7E9T1VkTGvCoY+ceKN0Qid2HAupMkxny9ZHC3RWYUy6qWlOEY8ERcDUiuqWc3y+Ihhs0UIwWq1oq7rtBBnlk4J/vR732W9WrPre7rokbOM3Gouy5ymrHka4ardMcYRYRTPtg2Du+L8csOuWx24+ZVJbPUYU+yTMun648YBPw7gOto+2ZalUsyWCrAg1EGivA9skFISJnCJUpJxGBnHgaK0HMUF223qVszqkqIoGL1P15OQIqqcGzFlhrXJuKJ1+tELfWAF7AujxtqkP+g65ssyMQDCSxLsMAzkFb+ByrhXhmCK+xEJhghpZ1RaE4YUjSu1SS5el4QfUqRjmTU6raBAcI5h9KjKEGKa1JnNXoYdTnJPpRXj4Bh9JBAY+g6ZZaA9SnK4/49DT/Qj1miiUhPCShzcavuWXHTx4Krat2mGsaOwiuTjcAliK2QKb5zkA1mW44LDDWMqbDmPEkmfHkOCGFR1Uk6N44i2Jslqo8A5lXaDPdF0SpCp6zrdn6VM30vw6YoSPVEo8jxjUdfJVz/2tOOQQgjK8pAQYjOD8yKp57Kcaj5HyJcTPZmNDLKAPgbcOHB8esr26prV6m464pbc3d2BklxcXrK5aRiNxhjLIALUOUEGZpnhfD7n2bMrxs2OLjrsco40GfPze3z3hz/mKPepRag1usgRRqeMeGUQIiJEJPeO6EYg5ecpLRm7hrHLUdmUaUbaVHxIph7vPVZbZCbom44sz9k0TSLPasViPkcIQVEUFGWB6EdU12OMpiwKhMhRSqTd3NgD2KJ3PXU9O1yl8iI9n35aXPq+Q2pDaTOI6UT7RQQrvjq+2IkeBYmwmhGkAWWJSuOlBy0Jo0TZCj8GvEi8d0GAODnAgkd6h5jinqSYwJESvHBTrnlasQUSQnoBQKJtTt/1KDKUF+B6UALhe+LY07vusIMnXXtK5ISXrPU4KfaCk3gX0/1bpL/D6v19LYVLKmMSZQKQmUZlGrzDb7fQjog+kkmNGBLNdugGorFoKWibJu0I3ZCY6FbjXfLEZ0WFFwrfp94wWuJkROpIiAIpNMZq9KH9B1qVxJAMNUJBpjVRaoRQKDGgjELZGju/ICsWWFtOG83+ZRSkikNASYOSGik1XnhMpghxoO8iYey5/vQx/+D8gvti5CKDv84Mz5SlNBYVUqz0rMg4P6p45huaXcPmtuX8wRHn9y747l98xtPbFvIZWTGnqCqklWSiRiIIURFlhiwEykdUCKn4mlmiCojYJrmzTMhvOeGcCWBRuOBACarZHKEMNitYr1bJL9APLBYLtDF0bqDf7VK4pJAoacEHijynKHKKvEBLA1ZS5Ol5F3nCQXtiqtNMhVElBYSRfrvB5XB6cg4+EJ1HaJlqIb/i8QVO9FdFsAKEIgiN0BlKWrQaiEonV5WWuD7tokprlFSpZdYPqOgIPsUPJdSThZiQus6LlIZhS4zN2I4OWxT4mBxDmc3o2w5jDW4caaZ7FUT6oXulD5sWiz3aCV5O9jC1tkY3MAxjYsxLjdJpt1FKJu+0m2KQoiN6hQhyCoFMx3wiSdWnBc2uASHQWrHbbWjbjqVZJqeblUgCzgcWsxolUhpnKkorYkzcMT+F+CHSjrG/tytlCCGZh5z3xJBaPlFopEzqPKEUtphR1Eu0Sgz9V/ebtKuLz+kcIDKfzxi6lq6Z4ceR5XKRdOJC0I+O06MTzncJytiPAUZPcI7CZhxXM062O7oxgvMUecZf//B9tkODDYIPP/6Y87Nj7r1xSTFUSNERlUGb1FERQlPOarx0RO9x44gVhnHTIgqJlEk4pJQgRS57/JiETQkYmZBSRkm0lPRVeVBl9n1qYxpjknZDJGSZ8EmWrZU+2IhTu9URgk+f7zAkT/pUWd9f+9SEDDNFmTaU6WryRYhl4MuiwEqJzkq8LQlD6vMq1RO0Ig5JESaVOHDZREhYXj8OuC7hlbuuT+uFSNlffd+T5cll1Ox2LLOCPE/4ZGUyBCn1ZOyHg9CkbSeLbEwV3q7rDioz4CCY2cthxeSi66cUkaSnZ7r7pftXDA4RPdZoRPSAQVmdGG5T6F7Py/7zvnWXMsAlITqMkcTocK7HaIUfxSQAkrghtR9tZpHIdGxXBj+ds70fDxX6JCJJ2vt2WhhjFEhhyMsaoSXRR7ywaFtjyyVK/81XIinu05Vku9nQt7tpIZEHEMPNVUI9nV2eUx+f0C6PEHFLIWARHCuhcaNjCB7hPVYoZspQC4mclcyqkh998inzWcFc5uy2a55dv+D69ob5ccI6JxAFGJO+73FUjNYQXYqF2m4aul2DqTsWZwJCwNiIzhLz3QufirCTUEkqKPIMeZL89Ps4LCEF/XZ3iFfag0H3Cs3ZLB3TD3FPxmCyDGHU1DPXuCm5Za++3GsxxPRstNYv1a+/6vnGl2RqEclHiilmuG6NlBYlDQ6BkBGhJr66n8QrzhN9WgXHManQ0gc9JhzyMCBsCtQT2oJM5FFZlEk1RbKkKpmC7/quPaze+2LbXnK7DwDc68j34MADRNLoQ1xSxKdrQIQYPVKmHV2EiBABLSRmctPtK+Rj3zE6d/A1d106SWijyXJz8GGH6FIe2NCRa33Y1WOAcjZDmJRDF4JHCkOWFRAcgZeKPjkJc9p2i/cpH16ZjKIo0dom9ZgtkTJH5wvy2cnnJnraxdNTE0JgrE3s+WaLGx1Nk7Tj5+dnuGGgaxokgu04sHjtNbrhE5zyMPRkecVNs0FEhwiCwXvyzDKrCi7uP+DT1TWlkhQi+f2V0bRuJMoEbmCqJUiZTDlpIRxpmw6JoCoqBHLyRwaau1tc3mOLkcIHXEjRUFLGg9pxHAdkjGSZRqma9TogRCAvcjyCpmlSTjwkHLTNyG1K3dlsNqktGyNeCLS16ZQ1GWxkfPkZ7t+pxC4ME8fPTJvUb9yOfohwmH4lkbZAmBIhEkPdhymlRETEZHBhCqmP3h8+xEQfMeRZQdsOB8CCcw5lk184TP5zD1MuW8rZ0kId6KnWWp4/f858PjsEJryEBMbPGWwOIQ+vdgvEFJckBSF6pJh07kokUuxECVW5QUpF37VpVxv6w4TeF/ikFIxDj5pig4RICwsxYlTKT/cuMPgkl1UorFTEEDG5RQhDlhsILwkqAF3X0vUN1qTPJVMWLTUEgdSTUElmCF0iTcle/vnqk4KXrcUDlnn6jDabDYvZnNcfPmS72bDeNvSjJ784p12vUKczxK3Dzio2Q0NZaJRMGe5Yxfnygk4K5DhybAtOZwt80BSLkgdvPmR5csy8qrFlDoTJjJKiuqSIzG2FMgabZ+i8QFU1brOm2axpdw3SbJjNl3TOkc9mh/fHB09i94EPkdH1RHwSMmmLC+JwdZvP51hj2KzWbIfxoGjb25a1Tie2YRwZupbcF2ibYSYs2v7UpkzGrCxZLBZkeY44EIZ/g5RxeyrKKz8QCUgj8UYQVOoDRw84EGmG4oYRSQpJ8MOY7qFSMLokMcyKDCcNLkpym9N7j5aRqCQuBLK8ILrIOHqsNVMLKmWiSWBW5gTXQ1ZgMzO19QTOj9hJq7w3tOwn/egdCChyS2YkSgSskQQfUSKllLhxIHqHVpFuvUmngGEgjsN0pwuHlgwk+k1tSqRUSJW6Ed57jLSESUvd7HbYvGRoB8p8gRAWrQuMLrF5DXQM/Yjb3/8QSJMxy0tU9KiYgjCEyhAkq61HgLIok6N09lOfnY8R590hamoYPVleUszm+BBpu4G333iT9e0dd+u/IlpBnGvKd+5zkgf0zQ1Rl3zy4QdUs5qzk3NWP/gxcz2nOFry0aef8drlguVyxuXlBSavyJcVFw8vqJcVRD/VGyA6R9d0yBgwUpIbCVaAjogQyZXCCUGQkl3fMvqBzqRct9365gD43O+0Sij8mGTFRgh8CBMJKKG49zZg5z0R6Ib+cJS31pAVeTpdDgM6y9GywGQ5WulpwVQEDwJFnhWcXZwzO1oi9AQj+ZXMtr85vrgdfe+2T/Gqk4Z68p8rCTqFIAptiWPPMLYoIQhCTHLStLu5cUjtN6XxCGyWIWVOFwQupiOUjzEFNjqPcJ7MFvgwMgwjw2T7DDElnJZlgZt62gfOdvriGMeRLMsPL0Uayd9M9EQfiV5ADIzBYa1EEfFjh51aXkO7ITpHhsTFSDeM5FmOQB5yutN9TTC0PbnNidNnlOcFkvRnznsymx28194nprzRKY9uGHoiiWYrlD6cTrIsJwaB9D24PrXdNFimcEgpscWcoqzT5/8znl36b2VEN3LbtlR5Tn52joyRftewWa2ZVTWnxwu0gYGBWBnmb76GOTsiz3Ky44LS5uTa8E5lyOoZ16s199+6TzGrU4iEUsyPlilbLpMILRh8gHHANS5563c71qs149CTlRnzsxPioBmbntWzK/r1it12g84sp5cXKY2WwPb2JqGpTCqSzWYztu1A8BFpMqrZHGs1be9QKk4hD0ksUxTFpLzUmOlD0tam3xMKqVXCnynLMHhUnoRQwUdQk3hHKqLYb3o/sev9iseXonUXiDThVY5WBU5mSJ12lKAUQcSDKwkm0YlS9CEmxayIKJl4NdumQ+UaW9QJJTS1lZRSGL3fwZPltG07BufRNkNM7q5h9FiTMtb3R9NXj65JyJPY78F5nHcE16VetFGIOCAgSTJ96l8bpVCCBP5XCUi5vl2xXa+xShOjOPiiy7IEUnGuH0bmizmbzRaTW0w2GV4UKJOhpm6FzTN0JlA6MLgdwQ0onRI6pUiMMyFSoSf4RDhReJxLAEyhNVoZvFAIXWDyBSor/5aXLslKg3O4tqG9uyGOPbdXn2G0plSaF08ecRXh5HTOJx9/xI1zLGazVCCMkavtivXY0xOIjefs8ozBe+b5EXlZkFcVymiUNdiqRAuQ3hHGAXd7R7e7RRKYVTN8N7C9u2Oz2SLyghd3WwY/sLq75fbFFaEfKIuCk9NT7tn80BIEQdO0OOenoMPkeENJyrpitlyAUNB0YBK3bnRj4s0pOD8/4+5udUB9paJtPOhBJIKyqlDGJp/FVPPZgzr36S7B+y8oce3l+BLca5DeKAnCYrMF3tziZUOUk1mCQBTTnVCIA9gvz3OC0TgfGXxIO7c2DM5B0yB1Rp4lrXqMEalSAqsbx9SGA4bRUQiFlIooRlK8kcbqRJfZK5uUUsl+7AI+jLhhRIRIHB1ubMlNhpZJcKNlQESfLKkxoK1O32rwhHFMstgYyY1BS0U/uAMyuKoq1uuEYzI6Y7drcM4xyxcYY9g1PdFHrEypKtpmqX8eQzJVpCYeNivIbA4kMq73MeGHdQqEkGrypguJUkmAEoRB50tUtkQXMzA/g3kSI4SIArrNij//3/4tzx8/wsSR4+MjXn/jDU4XC27vbrl78YLjepYwXVLx4UcfIpRi13eE4JkvFrz9tXewWcbQtWQ2Q1k9afMVnog0AhEjwgfa1R3PP/mYrrnj9OSEnYdnz6748KNH3K23bIdkDTUKylnObDlnOV+yXCwpi4Kj4xO2my1ucLRNT9v0+Amw0XcjASiriqwsMJkFqamNgV2D6FJuesxSyMU4hgOoM4TAdrtFCHDWUlQ1ymRTlygczEVap4jl9Na/tNK+Ohu+iPElTHRx+A5FVCBztK2RegMyJZ6OIanJtDaTlDMlcNoswzuDEoLYj0SfRCLGZLipOpwq5In8EkPER4fWGd6lCvqmHfAx9ayDkIQIPgqUTH73VwsthEgIjqFPMcQxBNrdjjB2aGmSrVKE5JeOIXHhRAQ/Jg5dP7JZrdFC4vohLRRTMW+1Wh0ACMAUnOgZ3XiIYzow6kiiCj2pBV0IVKZOJyA0xlZktsKajBAcRoMULmWXK4ULA24YiN5PueMJF61UgczmSDtH2uJA+flpjy36wNXTp/zz//f/i+//+Z+wLDNeOztB9h3bmxc8fvRhcojtOk6OT1AqtbzuP3jAs+fPuX72HG0Mv/Xtb7M8PsYTyK1GC4FQkqhSbUTEZMIR3jN2Lc71LI8XjKVldJ7r57c8enzNx0/u2HXpc8dBuax5+423uHjjEpuVaGXJjaHvR5qmw7vIOAbadpg6KFAUnqwuUFPunBeRzBpEiMwkhDDSdY6iyBEi0ncdu93uwOGXUlJV5WQPFodCpfMepU1q4U3Fv5Q2W0yoMf3F9dWm8eX00SEd3QUgLSqrkDpPIgepkNpOBNiUfy3JGLqeMHp8nAgfQk/RuOkO50JisO052zGCzfK0SCBeggKMSYWuAHle0riRyMSsm9ol+xU34aFTq2rsUzzzbrdhMS8oiwylUuVXSKZ6g0wvr4yp5z/0hHGk91PTx3mcH9m0XTJnOJf060xoZiXBO1LQYaLglHVJN3qKssBFgdGWLC8xpgA0SmUU+Qwlc8ZxwPupCzGZZcZxxPkeTVoQlUmJLF5KlM1RtkJPn7+Hn3qkjN7zve98h3/7P/1/2T1/Qa4VdW6pi5z5cpHcgF2HJOCHnvXtHaeX59xcX9EMPd45jmcz3vv611lWNd1uSz8OuF0DUxyW0KnImud5WugEhLFHSti1DTdXt1zfrLi523K37mhGCMJyfFxx//iId996jYuHZ3gdQKVTy+3dhna9pW8btpttYsk36cR0cnKCsYnldDhnCsngHSDQJuWYKy2ZzRPKehjSAtH3PZvNlqLIsTajKAuszenGVGTVSqONIjh/QHwZa5nNF9gsZal/kc41+MIn+vQaCV55oxQxP0HZVQJIqIzRzBFkhLjFj1uC6AnSMQbP2PnEfZNgbAoaVFmOE4amG9A+FfWQBo95WZjKLIP3lEWqiIZxTL1rZVAhTpbS8QCVVELQeQijI/oBqzyr7XMyGZnN76G1TLpnF5BofEh9eoQgxhEfPSEO5Ba6dmS72ZFlJcoYKmq0zpDCsN7sEnY4CFwcMNYgjWLwI1pYkBGbKZSREDXaFiiTcM9STZBIERA4pBwJDAgMWmcEL5AiYnGgNNiSWNQ4ZZNv29QpPSSvEFNYwU8bbrfmz/7wD/h3//Mf8M7De3z7d/4BR4ucvK4p5jOePn3K3WbLbCuwRYEpFQjPxfkx73//+1xdvWCzWfHWm/e5fTay2W64vb1ls2mQQiKJFJlhVhbUdYXO7JRum6ydz5484ePPbtg1qbWlETw8P2I2q5jNDJf3Ljg6PQKT0NNi13O38zx69JzVekuIA1qndq3NMypTs1guEUqibUlezJEmWUohYo3BSomfzSlnRxRlzXq3xcUu+Q4C2LwgIvBIgsjwaJROkM7gPW70GKuRJkWL6bJAFRXYFLaYKlW/gTbVwzclPv+7xIhShrycs70rUKZAm5YY4tQ2m7K+XcC5QJI1pX93cI4QPUUxJyvm6NzR9iPSKpzz5LmaKDGpGFLPamJMRRgfQ0IrSzEtsJJ9zJKf+vFKCpq2QeLwjJg842g5R0/yx+iT1x0CRiq8G+naHqM8MbjkDzd6cuUlsY81GWiBMpIiy3Ei4IMjiiR00camuGelGVySYWptEUoTfcpAF1IjtCUvytQmkypRaXzE+/T6CRwxyuTUk0k9Z/MctAWVfAbSZKnIN7nlftZr12xuefr4Y5QWfPzpp9hC8Hu/8w1OszneBbwbKeYzFAJpDEFqvvf+93nn3XcxWcH1zR1vvvk61zd3bHe75AQbxwNAM7gBN450bcd2t2M2rynLpCbr2pbdbodUnvOLBYvFgqquJ5NJRDGiVSS6nl23Y73d4Jxks+759PFTNpstZak5vzimqqqD0lEqiTaGsigpijJpGXRy9KVbW0RJRZYVk9NSURQl67v1gQhsjMF4nzwEUwF4j7TKMo3NLGNIBBmpDMbaCeX9sovzRU32L+/o/soQQiJthcwXyH5FNrb0/TDx3iXBM927DX7CQyNSaN7gRsZdS21qsqIC7elGj81y+n44EFz3FtAQEqXVFhY3eBQyQfbFS0XZ/sgbwogUnmHoyTLN0dkFxhpMnlJJh77DakNwI12zw40dRoMSIgUqioDvk1U0FWQGhnGg6x2L3BJEwGQKowzlrEztxL1nTGpMlnaNKFUKplAGqe2Usb2P+00hkgASnVBYMaa7a/QICcFLjDaYrCTqnCA1mAKRVai8SmpC4FXP5KvZ7f32lrG5QcaBm9WK//idO1AC+XWLUI6TowV3dzdIW1JWNdYYXi9qNm1LUJbTew/Zto7r26corRmH6blYTTMMxIlwEwBGT00SSe0lpvfu3eP8gaBezJnP5tPzHLm9uWHsGoI3DONAiILVasPz2x1X11t2m47CGo6OjpjPasqyPkzQsizJsoyyKl8GVbrEDghRJkrNpIY0NmdmDGOb1Iz7cMUsy5jP55T14kCW3YuplM0QSpNlhqKeJ8mxModO0hc9vvSJLqZdHV2gimPc7gYp75ItdXJJCZlSUCJJwimVZvQRFR1FVqLKmtEHcAGbVwSVghL2BpUDHZQEjxmdQyozqdniJCUNB9+yECJVrocB8ORlTjVL2vBsShpptltMViZqi/OM3qNl+nOtPHk2x0hY9QNdm/LaqqqibQeskATh0ZnFVhnKprqBCwJjM0LwFPWcMHnAhVAYZdG2xOQV0uQg8wRjkICaxEchKQyjCITgcD6lzZRlTVHNiFITkAkcmVXIrEbnZQqFY39bfbnD7F/0q0cfYvsNmR9ZlBWNG/l3/9ufcvVszdfevcdFV/Ps8ROeXG2oyhlSJhLOj3/8AQ8e3KcqS5599pTT0xOUtEkkhWK1bmj7jr5tsVpQFzl1aSlzRZ5lB5S0c458VpFnqYVorcXt0jP2KG5XO27vtuy6kfWm4WrTMDqY5TV5WZGqJzGl3NhU/DST6SSEQNu2oFJMdhgHrNHImBBWGjm9c4k2s1gsDrlrUiZP+34XTz36CUgSIkppimrGfHnM/OgEY7Mv+mp+GF/6RD9IY6XFlEtGWyV5LAKihChxo6fv3ZQtniqkQiVVnDKWaApGNEJpXEj2xOD8RDf1B/nm/siGCy9FL1MfdBiHz0lepZQMfiC3htnREmltEu8oxeAG8rLEDz1SW/p+SP5kUjXW9S1SOOoio65r3OgPQqGiyJHBH7QCOkv4J+8CWmUU9YzZJMQIMVAVOVIbXJBIY4lC4Vwy4RhlUVoghMf7FD+spAHhCTIipSZEiTYZ2qZj+yg0QhuEydGv7OYpve1lw2ffffDec/PoY06URN2/z8ZprpqGx9dXfPfjj1k3z/mvf/c9ThZLbq4Grm/WNF1DjJHl8pggLT/84JMEkLDJA9+3A84NrJqWXdtitcIqQzsGiigPu/leqOS95+ZmjY8aoma37dntdjStY9MNvLha8/GjZ2wbhzI5XpoEzpCGfhhpW0/wJftYqn1vexxHmiGFI6IkNs8p6mrqdYcDvCLFbGnm8xmE9O/t8dZSaVarO/K8OEx4ay1DjBibY7OCvCgxWY4tCoR4SQv6IsdXYKIDIhKFQBY1opgzygxHBB3BG4QpyOeWGATRTdxsmayCHjGFDaYCh5IKESVCCpquI8800afeZhzTdUDJOPU6xUSoCYxdM7WXJP043Wt1QbU8Jq/rRGOVTCqxZBYJPqBNZHmcYUVgbDZ43+MlRBxOGkypseNAv2sZB4fJZyipcGOfgA7KEIhELSnrGeIQmghFXiCnu3qKsRKImHr8WqfcdTe4KaXVoLOAURKCZOw9kvS5aJtPe5pCmRJpS4KeIXWd6KnpISQP//5XrwA36ssHzB68Rn91R7tu0L3j/PyYCjBy4Op2R6V7FrmkaToyFZHacnp2xtXNLd3oODuakxc5eWZRMrK6u6PILCfLGq0UrYNKR06KQLU4wxQFbdfT7TqGLrA8PcHqjHbbsd1uGcaBu/WWp3cN682OfgBrcur5AqEkbdcwjg290ARqPOlu3Hc9QsoEjhSCcTo1KaXIx5HcZoQokTpLhVjhQY9EDUEmw5TJDHbM6UeHCikcQ2mFNhqTWaSS5MpQ5BllmfDSe8Gl/AK85z9tfPkTPUnPiKjEectLhMkTS470wkgb8IzJCyw0IkYGF1OxKssRZg9CVAihSBl2EYQkInDDAN5hdPKTexGwKkXyuHFERTA6mWqEUggfEEpzenJJUdYp/NAIIo5xHAgR+r5DSYnJMoJ3k2ZdpAWoNCglSH3xJsEh/ICPEgmp968t1uQIVLoOAEz6fCWSIUcgcC6x2ZVJv9baJNyyEmmy83KXQgyASwuQStceazKitulerjKELVH5LKnh9JTCcngW4nO/3iOsTt58lzfGlvvbFdfPX/DRo2dcrxu2o2O7djx6estJXZNphc4sZ/WMLC9omy3r2xuMEiyPjzBZOqkMzrHebjDljHffeI3XXnvIs51nWD3H7p4wCkk+WxLZcn11x9AOVB76XU/btvR9jw+e69sNNzcNIUbm9Ww6jit654h+JLiBaAVSW7ox8Ozpi8kApaaCWYYTERcS7ScGaLMdRSkwusANPcr3RCMJOiC0oJ5XdENP24/kRZ2CN6QEpcjLCmMtMaagUGMMRivKssTm2cHE8p/xjr7/QWBMqmiPUjPGSeEmkr1wGHrarscHkNqmwk4QWGVgustrZQmDRxCIIRW2knlkhCiRZl9lf/l3ji4JdHwELVMYYVHPqGczQGKtSbbICULoppxygkNL8KQTiZ9Q0YokdczzHBcHNn3CSwcvwVicT713bQ3KmtTmUYoxpCP5/roRQiAqPVXH086sbT4dxe3hCApTaoh3CNTBlKOtRdgML3OELsCUoAqkTUfJz0X3/pSXb19YqhfHSFuSFZ4s37KYzzk5u+T67orvr2+5Wm1oRwlCM7qI7rcYuWVWZDy8d5aSX+494OOPP+HHH37Izc0tIcJ8Ifj3f/wnnP7wh7z7+/8t89N7CBvwxtD2LbbKqU8WrO8ELzYNu3bg+fPnh2vF7e0tSqdW3GxWTVlwW0af9BD7nrxzjtVqTZHnxJC4AFL1aN1hpky7XbND6w4iVOPIsc2RBIauQ2iPkTlCWrSWzGZzBIpx9JR1TVQpTjuK9L+irFDaMFseMZsvX0quvyC0808bX42J/spIu9Mk0xRqev/8dC/V5HWJ8xEXBSov8AGULQhCo6RGyBRN5GMK9yO+fGH3/myhE09bmEQHiSHlrA9dT1lUzIqarCjRMtFthqFPAYZ7/HMwSCHwMdJ1QzqGj+nEoVRCRQshJ2ChxGjN2LS4USCxoDRlXSGNYgyp8i952eLbt2lCumcgTYbNyzTZTYYy+SSqecmxS757g4gKj8dog9QadIZSM2RWga0R+QydzxHSHj7zn7XD7O/I1fKErD5is9tispyiKKjqmiqP+L4jBGgGweAiQSi8iwgjQCreee9reOf4oz/9cz598pRtO3Cz2qYiVT7j3uk5t88f8z/9z/+Kd7/2Dd67nNOv1vTDDfP5jG3bcdPseP7iMf2Q4B578ZOSkugHqqpIGfTa0nY7hJRYnWHUJHwaR5wfGV3KajvOC0KE1XrHUpe0bc/zF1eUpUWISDmr0kbhHWPXEsVI1BFUhpQ2tf60pe8d/ehSwMT0Gdq8YLZYUs8XaJthiyqFXXyJkxy+ihN92qVihBhE2qFE8jCH4HFoysUMpGZwycEltMVoC0hikAglET5OCrEU+qCLksxoHGOa6AGIMR1xEUQJZZ2RFzV5NUNPTq6xSzZSa2xCQ/uRtm2Scg+PkWlxSvFRKWQxRj/JJCPC98gJdEgkVedtgj4ESO20GBjGAYI6tAL3i4qUkijUBG4skNNLEyNT0os++OvlhLRGqHRS0IqoLEJXSFOniZ7NiKpAIP/OI+R+51Q2582vfZPPwsgmRvKsJPhAZnJEiCghuV23PH12wyAEJsvp2pZtN/Kd73/IX/3VX3F1dc2D199k3TpUdUQTFT/65ClLfUxVFPSbluerljAO1MLRtA0/+ugpu65lvd2gI+TGMK/rVHUf3bT4emyW+PzjOCIlWD0pLCdj0V6tFmIi6yI1NsuIQjEOkR/84EPWmxsevHbOYlknhaWE1W5Dt9uQhQxbpcVdqfRO2boAWnrnycuaqq4PZB9bVMyWxyhtUkdDJKw5n6uCfLHjS5/o8RVlfwwxpX9O92Apk+IMIZN2PCR/tVAWm5dYqdMHqRQxpthlH1PYgPOOuqrIrEFOq7OSoKKeOOWe6D1aG6JUBCWp6hnapiq3kCrpwwEEeJ/EOWnXTqQaqwV+6GjbJsX1kHr0QiqyTDMOPUNIeCoXU8CCDxKbZTB5nAc3IoInhIgb0+RKPLJsIteAUAqTFZO4xSKlxmidNP/E5KolIoQiKgUxIs3LHR1VIrPUNxe6SIx7foas/dUHwuTBF5r50Rmr5TGZiMgYubu9IQwbysxweTxnUWTcO6oJtiCrF/zlX72PR/PZixue3+4IEQYf2LQtImh2XY+KAz/8YMe3376HMYamG3FVxY9/9D536xVoSecGsrLg/KjkeFZOUcSpgLheb2jHAR9C8hqIRMEdhw6bFWijyPKcIrfpz7WhrGuEUgzjSDcMNNuWp89e4OOAD8lklGeGvmvYblYMbYOLI7bJ0HlPZh3CZtOJKiS7sFTkRUJEIwTWZlOajQKh2AeWfJnjS5/okEQxAhDOI70nhIEgBTIric4Rg0xAVaXQukTpAiHTyoxUyUQyHdXDOBKdQ/qerKoTv1yA92pCSycyqgsdxHEijUhstUDmBVGIaaEYcdETZUyONC2JY8D7cVKSQdcnLXuy2QsGDypErJaEIaCETnfj0BF7hzSgMoXXaadsmxYRA2WeTCxtGIkBbJSoIDHYFDSY5RhTo9QMpTJQJLgCHhk8MjoknqAzgswQU+FN6gxMTl/UyHyGViWgeEnS/8nn8HKS7yvuUkrEBNdcXLzBj65uMThEVnB7fYMLoEQkzy1HZ5fY5ZzjszNmyxl/9Ef/kW51TS6gw04Fyo7gNOcn9zk+P8etn7IaFEXs2V0/504ZXOioK83yKPnD61nJycwyrwusyejagb4fGa2mjRJ84Ob2Bjc6EAItNHF06MzgvOP6bpsCDpVn6PuEBCfhxu52DbEQxJgRtUGGyLBe08odd9tVukb5QLPpyO2aaExy+SmDtIaoBG5IuCmbF+RFlYIapXpZexJfpNj1p4+vxEQXQqTJ5xxd2x6CEaY/BNLxuCzzVJySicnW9y0my9MHHSZiawzpf1MRzlpzEMM450AkHTlMcT2CQ9IKIRXJok+QCzkVobVU+LFHCUFm9NRzH1PDSgqM1VhtcUNPt9uy2WzItERbk9DLeQl5Rj8OGJOwQ8GPyekWYOxGtEzhEZGkytsXG6W2GJOTF9Wk4SeltwqVFkcpEHGauiEgRUiFSVNBPkdmJZnNJ5vuPrnzZ792++P6qyqvmEJrODo948Hrb/Hxj96nMDlKG3bbHX3bJoJv2xLy5BR8843XcYNjt9nQtR3jbmT0UFQLdL7gG9/6Nk+urrnbddypET00FBno/paiUNT1jJOTJTbTzGYVdZ3jxpEsK4hRpvv2xEzfbDZ0XTq6V1WFG8b0nIaBGJPz8fb2FmvKg2NwbxcdhoF+GOkHhw8AkvVqQ+sET59cJ/OL1mzWHTHeoozFFDWKlJqbFhD1UoMhEp5L8OVU13/W+EpM9P1LpacPrpk+oH0SyZ6WEmNASY+IIyGJ4HHDFKU7AR0P/t/t7mWm+VRUSmQWj3cDQoJzPVZLiiKbJu3eRhqmQloyhfgxoZt9cAku4QYS8yqiSGkrQz9OHvGRvu8JI1jvISqkiKjc4sceMfokNVcCoTVd17Ft21Qn2B+5lUkimsmqq5RFTPCHKCIId8isQwiiSEdEKUQS1dgSigWyOEGYAi0AUpHzJ03QP3lU/8lJPv0V6UelOD6/pGka2s0dWZZxcXHO1TPPpmmJ48h2taGuak5OMt54eJ//5p/+Y4xR/Nn3PmC72XJ++YDLh2+ijMAoyGxGCCN1mfP6vTNOj4/xfUaWG5QCYxRWpNw9lKEfPUEomn6k6Xr6vqdpmsO7Y62l6Xe0bYcPmqoqUAratiXLMqoq5anPZsmMc3V1w/VqQzeMrFcbYhB048Bq69isWxZzQQyKthtwfpV868UMTIHJklIuypex1d57dIxfuDvt7xpfiYm+p42+qq/eC//3ksJDFToMCBEYhwHnA2PrybLqlSgjpgIcBzbYq4o351OqioiBrmuYnRxhbSrYeTcgRRLCKCGmuy8QEvtdEkAE/NBhdJrAcUpWTe2w5HnPjEyTGoijBw3RKITR9E1LYTTSWjSR6Dxj54g+7cZxGIlKIYoRK0mtvqxKbUMp0iQXfgpoUJO4Rk5hBQVR5cljns3BlAQSVnhP8vpp4/Of+8uF8XPPCFLyy2zBgzff5Ufvf4+PP/mEB6cL5rMZLsCmG2mbnjzLsVqT5xmX50f8k3/82zRdy19+78esXzynLiseP3/KdrOhzgwnF/f5P/z+t1GuJ4w9vczJMoNUYDMDeAbnWe1abm9W1NWcXdMzukjf9wf+OiSHWmplJlpwWZZ4P3L/3n3m8yOyLEcpxdOnT9ntGoYhfc0+ws31Cik0eaEJyvPo04H3v/9XvPP2O7z+xuusNzes1yuK2RJTzimziqqqCFOy0P5z+1n1jy9zfCUmOgAR+q7n7u6OEBOhIwh32GH2uOQY3CR1TaKZ0YekSdb7QL2kXV6t1yibH4wqiR7qGN3I6HoUkbLKMVYRY+J+pSw0MR3/maKggOjx0ack0r5j7DvKfIYihRNIQTKekK4YhIhVScMv/cjoe6TRFFVJ41qCG5FaEINECcXQDokiowMyl8SQ8E/VrKasZgihcc5PmnaHkCMiTD5fKZA6XQ+iPSLIpHzDJNqMJCQp8d9jh/kcQku8esh/SQYSUlHMF7z25jv823++IrRrzk+W1LOagY5ubGm2O26U4ORkQVVa3nhwzn/12+/idy0/+ugZj37wXWyhOS/g3Tdf59vf+ha982gPs3qOzjMg1UP60aeAQiTDmApfHz/6DOcCEXnAOu0z1sZxJDc5knQauHfvHotFTZ7nlMU8Vey9T0d5a5BCobVFRoGUmn4YKTPL6dmMd959yNXVFc9fPKKoJPfuP6Drh8OmEUJAaUWRV8mEZC0+eHQISVb7FRpfjYkep9B4FbAaBu+JccDRM4wDQ+vwfWAcHEMcEgZp8gVf363YNJ6mD8xns0OqZT/0vNulKmqc7k7ee0QccENLtJKoBU2vsJkhk5JIIIQUZTwOfcqujp5xaBi6dirGJRMMwafdVAh0ViCFxvtA3yWQRBARcMSxQciA1ClPXNu9WceirMQVgqgSXip6j1GOymoWsxlVOQPv6ds1JnMolU3afEUQAi0NQlWIbI4sllCeoFSRerYiFdF+5gT/nCDu71oExN/452eLJdXigs9++B2OqgxhM3RZM5MG4VMW/doKjk+PQAjefnhJaXPefvMpHz36DKEkR/M5X3v3PS7Pj9lt1wQX0KVFu+ROa9ctRVkwDD1D30NUeJdanURHAGxuMVpTlwUiQF7PWJ6cTu7FDiFTEmtRL1kcHXF7e4f3ntfeeovd0DM7fcDpzZrddk1uFJvNlro8Qg4DDx/c5+vvvs3Qtfz1X/8V+rWHzJdHaJORZ2XiJzgwRY1UGpvlyUn4FTu2w1dmoqfgPCWSnlxEweAEd5uBu5sN189WrG525HlFNBHnA7u2o3eR65tbnlyvGbzAWJN87N4TvOMvv/cj7LTai0mbnmvJrDCcnR9RlIrXXr9keTTHKDMV2NJEVzhcP+LGnhA6hHNokZjoo0ypJx5gOuKPo2fsx3QaMZoQPW6cbI/OE3wS0UQRCUhCkEiZQh8xmm7dAwGrNMYmY4TEpPabCUhlUUIjpUkFxSSyJqtPkPUFLpshZb63A6Xxs963n5i0r97Tf+qk3x/pD/t7xGQZv/fP/jvWV48TGz4E8iynKnLGdoO2BqklTdcjQmB1t+Pi4oz7rz3gW+uvsdnuqLIZVVkjZaQskgBl06zJpWLoU4zxXiV4t22JUtPs2vSViGQHPlkusdpQZDlGa4os5+zBA3yINE3DBx9+wOXFJQFJMww8efoUrTXvvfceR2enfPLJM45vVrTbNVVuKKqKz55eMbody/mc1x/c43g5Y15VPHv2nPe++dsYbdHKIJBsdy164Vgs0mT/smyof9f4pU30nyzq/O8bKWZ4t15xt2pY3418+uELHj+9oe1HinrJxTd/i2o2Z3N3w+3tihfXj3j02RPabmDTwRg8w12bRDIyJa+sO0/w2ySKmdRmEklpDS82jnunJdKDGj3yJMlXjBQJSDGJLQQRIwWO5O/u2w5ZKoJLWCqlUvBC8ElKm+cGH3qUNnjngJAmvRuwymAyhQ9q8scnemjXdTRNi8p08p1LDVLTjw5RlZiiSNVeNNopur6n8T1rP3AkjyhmBi9zmBR2L9FIf7+d5e/85/7GfT0JjN749jf5xtN/gtpcsbp6Qbe94fjshHw2I8qUpV7NZjz+9DE//uAp918TfOu3v0kfRrwcadfb5DAMjr7vgEBdlXQT7qnrB5o2edJ3bcvowqHeEkJACkmpFRdnF+RVyex4iY+Rt954kw9//AEffvAjdtstT7xjHE7ZbDc0TZMchf2AVZqj5Zy22RGcBSlSHeBuzTjsuFtv8TGSFe+QlXOevrjm60CR5ylhRYlDGMXeHfn3/ky/4PFL3dFfTTX5+36je8ielMl88pd/+X2++5/+jKF1XJw/4OT+CccXF0hraYaBx9cNt3cNt23keufZ7Hp67+jHIYkVjAIfUwsmCqTK0EKndpSQRA/DIPF3HfgRHTyFMQQxYrWiyAxysq8Gn4Q2zreJbRZCSlNRGkkkjFMYnzYMzqNU2sn1ZDrBCEIURBdTW4zkfNNK4qcCnzUmoYe8x8U0wYu6RhqLMhlZUU/JrLDdbbl+csWjjz7myadPGEdJMb/g9W//Pt/8Z/9Hjh6+QZRfjHEiAsXyiG/9o3/C8x9+Fykl2w8+ZBxa5kdHDG5Aq2TsWC6PqWZHvP/9H3N674J7rz3E3l3xyepjHj95hLUZs9mctu1wGay2CcDY9EmjEOJA23aEuNf0+yTDLSustJyfnmHKnPnJEUEJnj97wnZ1S7NZJYpP3/LpJx9TzRKhZjGbsVmtsTZFQn3zG1878P7bruPx48eMw452t0VmFZ2X2GrO7oNHXD19xsn5fZCCIXiyuj601/ZRXl/F8Us/uu8D5/fFkZ8cr1Z4Y0zZ1UhJ2+z4k//wR/zxH/85t+uOPMuYA8YN3N5ep966EPSuZb274/Gzzxh9j9BMEL5EbA3OHzTmwRiEA+0j1gqEiAlCECNDiGyHwJObLbbIMbljuahwJAUdwUMQBD8QfI+IASPkRHpViCnOR0vBMLQJjoFHiISxGoep/RU12hQEoXBDj5Qq5a+7VGwCRVEU+LDGB6jqOUW9QKjkPfftSHOz4ZNPPuX993/Io08+ZbduCE3gZHlM233MD77/V3z2yV/zf/5//D+pzx5+YbuJEpJ7D98mExE/9ix3d9xc3yB1smt2XUcQyYP/+psPuLp7znf/6n3mJ6ecn7+JCZq7xRVN06F1zt3thg0t3eDpuo7VZsdyucQP48SzS0XZ2WxGWZacnp6x2bWo3HJ5eQkCnr94wY9+8D6b1YqqzBmHEas1wXvubm45PT7h+ZNnZFnG/fv3E8QxRIzVzJZLzusZ9994CxFGmt0WqyUiOKrZAjFGbl5c4cYUlCGtYQye7BWk81d1/GITPR6wEUDaRbIsYxzHl8mkWh9cUiEEur4nsza1sUJSKoVB8O/+8A/5s//4Z2x2HdXilKPjGUPoWG1uub1+ShhG8swiM0Pb3CEZgAGrJaMMjDHlXoWYjCxKKfyoJ176SN+1ycetAkFFpNXcBYkPGeZ6zdHMM6+LlGUuU886BbckAEZ0gX4caNuWKgMXX55EcI6oI0rL1KMXknEYJj16Aj+EEAhxxAWBDqk/HqOnG0aKskDK5H7LywJrLEIobu/WfPrBp/zo/e9zfXvH3W5HNzqi0MSihDiirKfzN/zZX/xrTv/9t/hv/y//d7TN07TYX6fEy/v1L2sIQPhI2/Q8vbohGM29N19jvdnw5MljXnv4kNENrDYb5rM5y5OSt959gydPr/mX/79/zW//zu9xcZwRCPR9z26XYBLrzYbIdBQ2hrbrDtl0KXjBYq1hPp+hraHKMk4uzxnHgXazZfviijiObDYriInZboyh7wes1ly9eJHoNUXBer3mrDijmpWYrKKsa3ReEMWAkZbZYkG73dA3W9bbW46XS3KbcXt7S3F2SYiQGfu5DL2v6vgFd/Q0qQ6c9r2Dx6Si1dB3NF1DXmQpBhmNMpZhHNGkvvHdi894/0cf8K/+4A+4fvYcheCo6GjXASugHUa6riXPcvqhx68aXBvIZYlXil3bET1TCGMkuJc9bZG6ZDgfJldcuhcPWtD0SSzTBonIPPWnPdq2vPl6RpFn+EHjx5aAI0rNqAz96Bi0oRxFyudTARdGlJZEN6LklI4ZQYzdxEFL2nkTs4R7dh6n5MR2S355m2XkhcXQUeca13o+/PBj/uDf/yeuXtyQG40xlszOUTJp/x0Zznl8lBipcGvPB//hf+X3//F/TfHgXUxMOKQoPeJXkAsSSd2RtmvQUqKlJsvmXJyf8tmjR9zdvOD45BKlLeubNcoGFouaYYx89t0f8G//l/+Vk6MMJT1aKeLoaZuGuq5ou45h6NHaoFV6brNZOUEaBcfHC4QIXF6cYcoFm9sVty+eMjRbRPToGDhbHrPdNSgU23XD8dEJeZ2DVNTzBWU9J8SUbW+kxXUDTdyQjXGi68LzZ9cM/cjm+ROyOBAYeP3h6wyzGpEXVPURAnVQXn6Vxy/p6D4dU6eKbyS50PKiYBygbRqUkdisRAmFMRqCY+ha/vSP/5h//j/+S54+fUphc3DpiHVc1wif7k1lWeJDwiZuNmt22yYlmsRkIOgmVpdSiqZpDrHGzg+HxUep5AwbvUc6kQQzIhXCgvPYbk5W5pycLalnGqE9KkRkTC206Dy9G7AyYpRGKcl2t8Zaydj2mMwSgkJEMTnPUuqJnLDFYjI79KFjGEaiEgeumLWKo+NThui5HS1/+L/8Gf/hz99n20WqzBILhcoMAU1WFDgf8CMHa6rREmsUm5srnn/2Ma/fewvEpNbiVyfeMEazXC6hP+PWtXSbW87uvYYPsNnsaNoWpSxKKprthryo0VqymNf88EcfouUZbbPheHGEUYo8q4lRUuQlZVEl1JbSrNYrzs/ODjtmURQT780zn9XgR1ZaUcxqZEyLQdb14Id0JC9qTGbIZhWX9x9Q1nOKeobSlt1uw6rZ0HQDNi9YqPQOP396w6bpccNIoRSZtKxevMCrz7j4+gKjBEpM77TV/DQNwldp/EITPW3kAT+m9Aud2ZdOnZRqkCagFjTNlk1zQ1XXoFK76+OPP+Ff/8Ef8qMf/jDpk0/PybTGZDkxBOxE65zNZrRtS9MkFlk/JJrL0PfJwrk3X0xXB6XSPTn49NWM45i+0jDFOKmkiVekKu7V7RrfRJyU2DKjrktmpUZFS4gpn9sPjmG35Xh5BAju7u6wdhKyTBeYved9v6gorRF5mY6mqzWuHwjeE10APdFaQ6RpRroh8PFtyx9+79/zg0+e47HUZYW0GVEpgjCovGIYx2mBSFyr3Jrp+3AMzZYnn3zAG7/3zxAqm57Rr+alEyQuZVSK2eIohS0oRbMWHF88YHY08OijR2yunnF6esLx4phd2zF2LfNZTlVqrLHcdYGxiNjcJFqtsQzBUdcVRZGirGckNLO19iB5resaIeDp40cYYzg9P+f05JgQA+urFzz97FNG13OynDN0Ldu2ZbPy5HnGzc0tx6fnmCyjnFW8ee8N2n5gs+tecgLnM+rFEU+fPOP2+RM2Y8OwuiVqxWx1RSYceW5RWTFx+/7+BegvY/ziO7qISJ1YXL4dQaSEFCFSCgZCINEUZUnv13Sbu+m4mvP+X7/PJ48+xUjN0fEcfKAoc85PT1Ei0u62h+OalJK7u7vk9R5HIJktRpdca+OYwhfqOqVqNE2L8JF+AhU4l+gwXshJSpvilbWEcexo+4BXkaLKOD894u2Hc3JTITKIg2J18xQpLSEI7m5vyPNkjIgIiiIDKQ6S270/PAJD1xFjpCgKdqNLWnqp8M5PMU8R76AfPI9ebHj/k2s2vqQuapAClCEaS1CW1oWU1Ckl2SQPllKmRdIaZBzZ3b4g+gGh9nv5r+7lkwJQClXN8G5k9EmpKHVGs15zfHpGt2vom4bM1BzN51RVzXy9wmjIiznj2CePfxfQUiF0ZCSw6zp0lhGRzI6OmZcZfduQ5zl931OWJXVV0Y8jyAmAWVYslkdc3n/I8vScp589QkXH6voZs8WcrYs8+ugD7r32Oh/88PvUiyX1rKJvdpxf3ud4VoEyZNbih5EXNyv6rqNpGvz2lqMiJ7iRu+ePaVdXLC/fRmSWr/ipHfg5J/rne+YRISNZbmmaXaKn9i1Zvi9MTNVS5xn7ljIzrFYbvvfdv+Rf/I//gpubWxZVzayqKbOcqigpshzvBoqi4ObmhmEYUhi9TUctYyzd4JBKUlgLbkCqFEPctu1Lrrl4WQTck1sCkc65dMQzKqWdqkAbep6vVmQfKy6PjziazTg+rRFSYOcl5QBiHNi2O+p6lmyrMe0QSV6blHL7xEyEQITIKMQh7liIlCSCFymZ1HcEH9ltPcPg0DpHmBIfNAGV0kRDxCMRJiMKmWyu44i1SQPe9z1WpfRUqwTt5pYwdCkznF/djp5668kLH6WknB8RZWqn7VY3DP1IUZScnZ7S7jZsVxuKqmS2nDObXZJlCmUL6rrg0cef0DctIkaaZs0QI85HsjwV5SICq6AsUjJPcqUl440PAxJNNwoePX7C89sVVb1gfnzJty7vI13Hk08+oNmuEasN81nNcj6jqmpu71bsxoFcCF6MnryaUc8XrNZr2r5ntU1GmLZtOapKYnCMY0+7ueXpJx9y/vY/SEpjvprH9VfHz72jH4IOBocQSZue2bTqeuGweTy83KAQyhJQNE2KLv7OX3yX7/z5dxhHR640nbHM6hl5VbJqdgQ34seRfgxsmjUvbu4OaCVhNF3Toa2l6Tr6oU8gycmd1rYto3OE6Z76qhZaC1JCZkg74zAOqeKNxKFoh8CTF3dc3Z5P5JCciCOrZnTbO0yVo3xHiI6izFCaSQyjplw3UrZZBCEVejI8+DAkTlvTMYypHTh0LmGMrrZcrXqiyDiaWYYwIBgYhEYIgxUaxoDNTeLaaQjCJxkwitLk4HuCAKXyJLj5FY+X6jsBMeGSy9mCLCvRWcUwBNa3d9SLOctlze1qSze0aDdwtFhSjhW5rajLGjf0DF2SuV5dXRNG2Ky2ZDrHWkO368BHhgHOL05RWY8pCzbdjrZrGYbAZjdwfbNhvWlROlmav/WN93h4/5LTB29Tlhmb1TWzjz7ms0ePOJrPOcpSa+366WPurl5QljN8iBydnJHPj5hnGd5o1Ltvsbm7ZlytcdsdVZmxubtlt7lhVi/R4qshMP3bxs/9FR6CEZSm2W4oigytNUOc0Eh9hzbJAw2gdEa9POb2+RP++v0f8Ed/9CeM/UjfD7R5x2KxJArBpmlYrVb4MUUV780obnQIKRiHMLWqkh49TmX/tm0TG8wldJDRGmly5HRf3qdaChFTgAGCru/o/Ih3SWJKEIw+WS6f395RFpax61guSrLccnJ8H+8a1k8+xRpDxNP2LdqoCe+UThGeyBgimS0Q04Rn4tlpk7HZbWh3HXFwDCNEnbPud2yGkWVZEEZP75NRJsty5mV5SHqVViMkjH7HOEaiTImzKkSUssxmx4jPvXi/ynLcVHoVYvprNNJqZkcXKARju2X1/LMUzoBiuB0ZxnEq0imCCxRZybyeE6sEe5zNPbpJSbm31zeHk1zbjWjTYcuC2fKIbhwRRpEVZTL67ALttuf6yTXaBtrS8pd9y8cffkSMkffee4fj45IHb7zBvK54+sknZCJQnSzYNi1tO3B7/Qxrcq7GEb1uyHJLXlisVQQBnRsJu5ZjV9Os19xcP2N2+TpfFSX53zZ+7q/Qez8lU2jKqqTb7ZBFhrEW5x3DkNpLSqrkPgoJ9fMf//RP+R/+h/8Pjz97ytHRktvbFU3T0DTN4fjjnJvcbN1kR00IpxBTMQ1eqvD2vfnRpYJbURSUZQoK8NM9fg8TTHd9gRJgrKUqE4hgu93S9j1WCeSEiF7vGh59+ilv3L/kaFmmfry1EAeOj49pdptEmAkvKaxiimEO3qOVnThQ8pDzHkkiodF51psWvGMYI61TOFGwursmzyVH9YKuHYhRUNnUTcjz/LCQaaUgqkmqq+j7ltwKrE3FS23MT3liv6qx77NM8z2CtprZ0ZI33/0aH0ZHv12TWYEfAtEHbpoXlEXOtm+Qx/pQV3HOobVhqD1ZZthut4kVICVuSKin6+trnBuo65K2XSUqrwts1juCdyyWM3zoqKqSuq4nDDd8+OFHvLgqyLXi7dcf8vXf/l1uXzzndnWN0gJrFWWZJWYJqWMTO8fQ7wjBURpNfX7GTgfGoSMLjmePHnH/7d9C1/nf7Rf4ksfPfUdP5JHkB86tIS9y2t2GLLeIMSBFJAwDQYAggxh5+tln/PEf/zFXV1cImdDO8/mctt2xWq3YbDZ/I//MucTcRqS/V8nkAtsPIQQ2s4fs6f3XFkKcKvTDwRihlEImLCJGSDJjkVlOmWX0Y4eKmmyywF7fbbGUZHnK2aqrgr7v0UIwTkkifsI/KzlhqoJPKTEhxSUgkvDGT1DAGCNCSobB07QDfnRs2sCLbeTJXc+mi+z6Hecq4/xogR8HhB8IwR6iorTWiavn48SxF5RlTpWT4qW+pMrQ4eWWABFlLcXyiNP7r/Pks0/IVMvJxSXr2xvG0eE6R4yw3W4PGfGz2QwhJGUpKMqMcTyaFkvB6B1RgFJ6YhdIiKm7cnV1nRj2mSYvMkLIOD45OsQkARNnTmPyiqv1DoegOD6jcx3B92RZKvoWRY3QFlnMIQSiH2m3K9Z3m+R9l5Hddo3KC3j+jKHZUdTHX8pn/r9n/EJ39HTvFex2O8oiJ8tz+rZJlXgViWFM7PUpoufu7pbHjx8fomWVkhRFQVFkL4tYJJaXj4HBO5RW5LlFSpXoMcYmu+lkO1VKMbrEXA8hqazclG6JMgc88P747gaX4PpKk2lLlmcoJdhsbxn7gNl73T3kZYkbRzYbB7HlaFHQ+Q72gY1RYm1K6GBS0KXCX0pZiUi6IQU9uCkr2zsHUdB2jt22pXGa53cDj28aNv1AlRm4u0XEgfunRwilDx2FFCWcUll9ahxMqCSVHGB5xfJoiZJfrBd6P8njxJyKQEAgs4L5xT1EnrO9eUbMDFld0q7X9Lstoh8O0Ua73Y4sy9BaJU95bqmqYpp8BcIkoGXwEzTUQ9t0tF3DyfEZIQbm84ld4D3zxSIl3k7vlJSKiCYvK5bHc4ahQ2vL2eU5188+I89TnLbWGfOjE1ZjpNAa37W0t1fErk8Sa+kJwaNEpFuvGNuWX+316Jczfq6Jvm8hpbRLSZ7nk3rNkpcVXbNl6DuKooQQUtHLOTbr9eGD9z6kl5SAteZzHK+qqogC1ptNon6aNGFjCIneGl/2x4fpaD86N/3cTeaCjOg5pKg6l34/0woiuNERs0BdVjRtgxsdRmfUVU2MI84HtruW5y+ueO3BGSFE+n4gyzS6yBn9iFVJ4iqmnUYITUCgRfIkj8OQ8tuVxPXD1Bacct1Gx2rXsfMZ2yGy7j27IPAuIGRA3N1gtee1+2+SCYuQAucGRhcwJmNoWoSUZJkmyyxC9mSFZb484tV+z5f1+kUhEEqTVzOyomC2rKiWC5rVHZvra+5evKD0SQ1XVhWr9ZphGBmGBPswJmG2MmuJIdC3PfVsxm7oyVSW4q6ReA9FXlJUBc4Nh1wAY+xhkdjXaYSwaKup53N0pmm7BotnsVywWW+wmQYiu2bD7OicoWlYrW6xRlNXJZkISHqichgpkJmdJGJf7UkOv2AVwVpL3+7QSmJtxna3o6pqsnqB263omoYiq3FBEvMZJ/dfp64XxPAiEULCmDhmxMOCcTC79AHRQzUv8YOj2aTI4ZExKd9iONy9XUgebzf6dEcOIsWu+PHlNzrpkYfgEtU0Wmol6YYUayzsEoh0Q8/xrMQIwWrTcjSruLttqLIjsmqByQWxDYztDqMyQvRYaxgdjJ406UQinUglsUYm5Z0RODzCDSwqSV1ZPnoiuO7grnM4EVAiZ/CCrQehRl7stsyvPuH+yQloQ4dBZnP6USJlh1ByCnEIoCWmUOSzGUFkSBEQ0aO+4EKROrz0E2tepagtIwTFRcWwvOCmukJVJzS3z9HFjrwoKLYd7a4FGSeMlybpmhTjAC4amlYQguH6Zo3RhrKqsHWJEII8y+j6DiUVtqwwWcJl17PkLhNEMqvT0V8qhLEoYRmCITtWSYTlB6JUVMsFWknaTY80iigzyiLDDw2FsBTHBaOwtHmOLIov9PP9ecfP/RbslUB5UTC0KaAwL0qapqGqa2bzJbvVhq7vENoydhEt5SG2VojkxfbeIWQgzzO8T66lvu9xvWMcfMoT73rariPGQO97QgxT4UZPxRhHFAk4oZTCWDsxt/1BvHIIOpCRGAOjSz3a5CNWaA+ub6nKjLvb67RbL0t2uy29lWh1kl5AIspYyrJmHNrJtCPwwaGUScWkYUhRv0XB6Bz4mOD/QuDHkRh6zk4rLm93iOuRTMCRsWwHRzt4tNCclzUXM8tZrZnnkWxmWfeCbT8whmR3DTJJX6UUaCUo50tsOSftMImEGxG/dEPL3/ZO/NQRE8BSWUVhci7zisXRKZubBc36Dj+MeAe79Yau6xhc+vykBG1M8iu4QIwBW+QUU8aatpbaZodr3GxKrMnLKiWYTrJoay0heEJ0hCgIJDlyYQuMXWI5QQpYP3uU6hx+QOmS46MjZnU98QIa7LzExpHQ73AYlqcXFGX1t3/vX5HxC0306SfkZZmO0X2PtTbt5GVBPV+y2+3Y7VJMzmeffHwIt98X3BaLOeBeaX+lIlzb9cQo2HWJmY2SU4stcbz3x/FDTtm0w2utD4UrmLhe0zUjpZ4EMmuwxkwKugYibNsOP/SYkJFreHDvknmpKayZVHobXG8ICAqtsVkBISYiDgFjZMrXmsZewbUXlUgfU6oJAj8OFMbz3sM5b10aiBld77hpd1ytWtrWUZmMRWY4m2neeesSO5+zaiNXq8Czq5bbbUeW5/gw0jQthRBk8xPs/CQ9lrif6F+Fg6WYevvp61FGUS0MNjOU1Yx2u0lFuDxjs14ziHh4tvsFO4yecjajKArG0SUJcIhYoQ41G2NScOasrlJohnMMY89mdYvznmI2Y3F0jDIZJi8Se91k5FZSFjUCze7qU4auA5nqS0VRkGUp/jq4FNxItcB7yemDN1Mc9a/B+IXPdULKJCdVClUU0ySTNE1K2LBVCQJunz/n0Qcf0LYtevoAu3ZIofI2SVx3u92haIZMYpxuSs4UCKKAGFL1fN8uMybB8kcfDzz4V3fxVzsESqWMtSzPDrTOEFLLh+CJbsCPgiwvkMHjXaBvR3wmEMEz9C1VXqCVRVjAR4LrCT4gplTTfbhi13W4cUz9/ImLHkaPTLYvCiWZHReMY4I3dp2mtoqzas5q29NtG0xoqMozmqHBdZKHb36NS+as/sNf4tc7fN+nHchI8sxycu8NZDmf+lxT+5G9kuFLHuIlOjotPgpT1IlgKzX9riH0PdE5Bjemndlmh+adGkbmswVGa0IWCCGxBVw/kGUZXdcdJMFdu6PvOm5vb1ORL884PTunrGcU1YysrFLST1YQpjQgW804QzKOnub2OXEYUJP1dN9KDiEmz0FWslyecfn6O0j91bWmvjp+7mIccGCJxenne1OJc56mcWyalrLOKaqSPs8Y2nYKYqhSbrfqEEKhdDxM2mEYAPAh4GKgHROkUUiBP3hixYF1lk4B8gDd3O/we5XcwTprU+qmyXRihU98r7ZpKfKc0+Mj8DVVplhWGW4ccCKishwloGt3NNtAEAMhr5BEYmACPabi2+ATYaRpmoMfXwlBbjOa1RpCwI8O7TVaCqyF2aIAWfDi+QpudggvOFvWhLkkEyPz4wonJWVVUSyWuN7SOofRht57tFHkuWZ5tOTo/B5R7E8VaYr/IoCvX9aIAv7GuSJClAZpC6TuyfKCYbOiaxtWtyuESITXPM/TETwvGLcNY4xkWXaQGSupDl6IYRjY7XZs13eJ1jsmwdXQdzx8+Drz5TFFVR+irZSxZFIQSJFZ9fmb3I+Cj//6P+Gau6R5mFq8iYsXKeYnLC8fcnL/TcrFyWEB+6qPX0KlRoBUSf0VSdlkRlIUgaYb6DcNQnmaq48xJKJK7wfGOJJlCqsyUKl41fQ72j7dx0NMpwUfQlJYkoQsxuYv7+TBT24xTfThwOzah93tC3v73nJKcpGAml6kkSxLedYvrp+TK0PoFMYPLGrNzBqyMKJwKQElePwwgLIEOTHmbI6SlsF7lPKgBP2wwdp8imBODrcI+BhpupYYHCnaUaGEwvmQCNMmGVMqG6mqEptLqrMlJ/ffYHZ8STsaVldb+sExjA5hC5a1oVYN9WzO/PwhQulpSpk0EX7xB/xLGD/t8hARBJSWyDxHlSUiM2y7HZ988gkhRvIso6prjDYonYQ1wzCkOOrRIbVi2+ywJrVJ+76na1tWqzuqqmJ+tERnlqwsOb68IK8X5GWZAhjV3sab6idaANIyv3yDt7TlyQd/xYvnL6jrCj8MCCkp6yVnD95geXpBXs4QSv8t399Xa/xiE306hx3ooIKUZErE2J5K5GxXO1abZ3z6wV8TxwYJKKMp6hzlItJBN8IwOLp+ZPQh2VhjROARUhzu3FJK+nESy0iFMtl0P5++mWmihxAOSrI9SHAfLdz3qeUVVdr9qyrHuZHFYo4RmtAkkEKmBYvScLasOT8/ppxZskITgmNoG2wuEVqAFASpMbpAKAhjn2KDwlQIlJKAwxQZw9XI4EYIgXa9RXewiFDXx9y7vGQ7BEzXMa9qqllOdTxjdu8e9cklozP0veSjH3/GZtXgfMSPjjg6cu04uXiAnR0jJnHO3o/+ld1vBOkZC1DWIvMCkRnKWcUnjz9Fa0NRFCy9I7MWLSVD1yRF5Hx+aJsOux032y1SSpbLJZlJFf7NesXx+Skn5xecXF6wPL9AZwWDC9R1+TeERYdfaUt9dp+3ypLq2bPDVTLPc2Q+o6jnqYp/+PfFV5Hu/DfGL8Gm+orhfvoxKcA0ymjmR5YmNChtMBoWlWXXabwDYyxFYWivt4e8azmFAHZdl1ZarQ9CmP0dfN9zV0odpLB7CeW+oLePHtY6OaqstdPVIp/QQh1Floo3RZ7ccrdXL8iB6v6Ss9MZy6Oc5XFNUJF+7BLcUQqMsgTnUFKjREzJmoJDBX//WSglGd2Yfi4VRVlObT5o2wYrIWpFzESCHpxcsr25Q+QV5fEZi3tnVCcXDF6z2Q58//uf8P5ffwBeklkoFzWz3HF0vOD8ra9hqsXnXuCvSiX4p34V6b5HJD3j/TMty4qqqNLzHEZWt3cIIVjOZvRTfedV/sC+m/LixYtp4a6oFzWVkEitqWYLjk8vWSxPEaZiGIZJkPOyiPaTyGupDKY+5sxWwCSbnhJ79zTh/b/36zJ+RU1WkQwOSoOA2fEJX/vWt9luN9SfPEFFcB4wAmTETBptOfSEoWeYCm373nfbtof7/36H3rfX9oW3EMJB8ri/mx8ca9OPSSH3UtUXQ2S1WqGkQOHIreZiuWBRl2gJq+0Gk2ushjKb9OV+IMoOmyfqyNAmVJYtclyUjH2binHNLqGeCGghkUpiphPHzc0t4zig6gInFTEvGVVG63tkcURxfEF+egGzJV2saTvPX73/AX/9vR8xDoHMWopcIEKPlrA4PeXkjfcQvyYV4JdjP2HklJlXUBYFJ4sld6tVgohImdJVXLpv72sgQgjyPD/Udo6OjoDp2VrD2b17HJ/d4/TiPrP5KcqUMO3Me/GU+hmTNsZIlAqTl5/rWkh+HQ7pP338aib6VHwJcQ8nMNiipC4sy1KTSYmPmizPiSTfeT+MuOBZljm7tjkc1Xe73YGZ3ff9ZKRJ1dV9aN52uz30rvdtuz2C91UabZZlKG3SISREvBvRWrOY18wLQ50VLPMCQkuVFazbgRe3HVWm6Y1nJzyZAjMX6djsJcqmcEYRU6EwyyxlZiAGxr4FMZlqvCfEQNM0SOW4OD/m7OElxfIYWSx59Nkdt9uR4/kxZnaOqM7pVEF0ipu7G773/o+5vr7GyBTZ1LUjla5ZzOY8fPMtytN7eJFYdL8W45UvU0SwmcVYwzgOaKnItKGwCSMmlUJpOUmNORTZ9gt+0zRpJ69rrLXUJzOyIqOc1cyPTsiKGYiX1YpX27s/a/ysK8+v0y7+6vglgCc+P8R0b4+v3tuFIAiJknB+vGQxa+lXKdCwLEu26w1/8Z2/oO17js9OKesaNzr6Pk3eYRhYr9YMYyKrGqMPk30+n+PGcVrxA1IIkBIpBCbP0511WjQS1ippx73zuH5Ik9J7ul2HCYHN0HG2yNlstzSDp3eO9e2Gy+MFMjP02w3S9SyPCpSOSGnIskUy6ShLcANuClgUIkErfUiylRBSHHM1s5zfO6JaziCvaAbNo8crXjy7JXjD8gxqmeOxrNcb/tN3/pJnL56DHyjzgiyTWJNzclRzcXnK5RtvQF58JSrsf98Rf+InAjBa0zTN4YRXluXUpSnRVlOUOUqlU97oUmqtGx1VVVIUObNZfeifhxgxWUGWl0j9+df878I+TQqMV7/KaXw1Sps/z/iFTC0/baQ7cyT6nrBftseG3XaH6xyLouDhgzO8vqXvOzSKGBzGSJoucHV1w4XJGQZP8AHnIm0zMo6eiEogxaPl4Y613W4xWiH8iIwx9fMn0ITWGZnNDuaGSBKSOCJjEAhhYHDkhefieEaRZwgEPbBbDUTnmZWpMDj6wIu7uwSHRJEbR1ValBQ4REoxjp44jsShR4YeHUZidKAlIkaKTHFal6hSUx4dEfMF0Rxz9fSO9XVDiSKXCoKj26zoVoFPP3nM80ePyIUkr2bIGBFOUGQKLXuKyxOyh28crgW/TiOyr2d4RIxkJsNmFSaztH3L6EckEo/HaoOQEW1kyr4TCjeCLgwhpOTWvm9J7C9JGAVKlQiZTW3ggBAvc9H+7mShr2wZ8+cavxB44qeNGNNeLqRKIYWAkhnVbE5eWprNyOXZnNbBk2c3bFapMFJVFdumw02tcmMtY5966saY1MsUSUu+L97M53N2u10CLsaUbY5KklBjNHlWHCSQ+z5rPw6To00kIYuSFFnOrJ6lux+pej96yHTGMIxoDbumQwpP3/WE1iOlYbYsKVBJ0CGT3zzJcwdECAQ/TjLc5DqLEopZRTarEFPE8XrT8Pizp2Qm4+LiARf372PLmvV6w48++ogf/+DHNJttiogSgbzIKaykzmG5nPPaO98gm1/gpiLmr8+eM92N97um0uhqxtHZRaIEAz6kJNWkknOEkNySfZ9yA0IMWGXR+uWJLcZIWRQsFktmiyXKGpDi5d+z/9v/tiP4r+nx/G8bP/dE3xfBfvpIJQw/rYoxOBAqJYyKAU3kuM7x4xmfPrkiLyx1XaeJHgRCSoQXB1WSUuoQlDirC6q64uTk5HB8j26kb3cHyAC8fJBpZ59eHO/ZhzsMLhXs9mKcrh/J8pK2bdhtdlhr0TK57Abn0EpgdCT4iFaS3mu6XpH3EE1PlDuEAD+0+L5F+AE5RS75CFFJVGaxdYkqaqKu6J3go48/48MPP+Z4dkY0hqu7NdcfPeLx8xc8efaCsevIlCTLDdaC1R4lHbNZxWtvvs3R5btEvSCgvjBN+y9rfL5fIxGmYH58RlGWxKurQ2U9xpiekxB0kyR6X5izk69hHMfDyU0ZA0qiM4OcuiAx/voW0n4Z4xdSxv20489eWhqjOqyhQmi0zcnymuAjzd01cbQczU/o/CnD2PLNb34DqT+gHRwBgdEaPe3GTdNgTLKy1rOC4+Mjjo6OkFNFdn13i1LqADBIRJaCzGYpD23SS1trGdv+IKgJwZNXJSFEXtzcTYSUlE3uR5cQU0QyI+idZ7ZYIKLDSklUkm7QDH1E5SNjs0EIcG1DGHbEoUVLUEWNLXN2fUdW5uiqIKgcnc0JIzTtiNEZMUR+8NHHPH9xxc3tir5PJpk6z1AKrBYYFcizwKzOuHjjIe/9g39CffYWUeQomM4Wvy6v8yuX8yhBaoiK+uiMqqoOp7D9O9a0Df3QHZjueZ4TQmCz2ZBl6XrWtm2612vF8uSYrCgI01/x61pE+2WNX+hi97d9eEFMBbkYpwzxHKlKgjOoAEOzYhN6lsf3GYec0/NzsqLm+fUt26bDO4eaquX79lqe52S54vLykqqquLm5oes6ttvN4Ti/b7cISDE8lsOCsO/FB59yzkVMu/yuGZEyHlp2cX9lEJLcaITImNsKHyKL+YK+61A2x9hZqiG0DbbIE366b9F4YnBIqdFS4X1i3NmyINyt8NKgdYFRinp+RAzP+fSzx1z3KdwhOCiKCmszFkWG8D2ZUSznOVUhqRcFl+99neM3vk6wcwQCFd105Pz1uKtPW8X0/2lHDwLyasZ8Pk95dFObdL+TG6MPEzrRaF7+WVVVE8SkYDafU81qtDUpWkS8BF795zrdf2V99H3PceJ7EGQBtmAg0IZI7+H27o5sVJwcnWOk4hvvvsP911pu1mu6biSzJQIOTqYE7fdk1tC1LUPb0G03ZEpTVDOGcUyppCEy+pFMSfIsp6wrNtsN692Wrnc4HwlDTz0rmVUGK1LBLwbFdtvjQgp0lEqRZ4Ht6Gi952icse48F7MaHzW3m5ZNM3AkKoR0GBGRQzdxxjzRgrcNxiYGeYySqDN0MWcIkhgEjI66sFDnxCwlzVRFPVXooaoDpSmYZQXz3JCXnrfefZOLr/9TVHGaTCEA4ouzo/7yhvgbv1LGUh6dIdQP0HHEKAUiIDNLDJGxHyYQxXRHt9lUn0m1EG0k0ZTo8gikfqWk9uv22fxyx69s+f88d0MiTZHcbUQ+vbrj+rZluwvYTUT0cH7vEl0WPLg85+LeGUh7mOj7dA6lFPiRu5trbla3KDdwuZwhpcFkBZvdlhfXVynBsyypbYXSmma34/r2lrbvcC4SvcdI0HjcmNxx7QAuRpp+pBsH5OR3Xg8jIgbu2p7bZqC0hvx1jbYZN+s1dWnZuY7jWWRmPaHdoRgRWiXduU7LndUW50GoHJ3lKG1oVhuWlWU8qViUhrobMeacsip5+uRpIqRkktpqdHQYo7l47SFf+71/yuLBt0BlqQovBF8VVfvff7zcBl7+TjKp5LMjtMkJrkOLQJSS4CPjMMCkimsmp6NRSY8BEIgoo8nnJ+h8/hOfyX+Z6L/08epHun+UUkoihk8+u+HJ8xVPnt0xjIKyHFLMkBK8e7ykLAq2XUNZGrxP/vbMGoy2NLsdty+ec/XsKQwd88JQLEuEyljveuzRktmsYtzvyFFzc3PL1e01/dDTtm2i0sZIniVs1a7pCMHTOMHgPf04EuXkbUYipUKKSOciftvCTPLR1RWD0sQguXrRocVIHq5Z6p6LI8XpscUajdBJ5qu1RSmTOG8h+dfHbkO3vWHsblnOFFs8xWxBUVYE7+FsQdf3FBTUmUBmW+qLioe/+49YvvVfoWw20Xn2H/qv44u8V6W98ltKUc7m6CzDj4bRu4O8GTiIZEIIxBCo69Q711mG0BmYgvny6EArEr+WJ51f/vjCLnQxRm7uGj58dM2L25YgM1rX0212OD8SteR2u+ad997j9Tdep85TAqucwIpaZhgZqfV9TpZzwtjjxp6x79hsO0DSuxGpIBOG9XbLarWhH/rpTj6p5IJHkWiyQihQAhci3ehxMSCtxRYFymiiFwQfEUqQZYbMaJwIrIfI+OIOGSUiCIZ24NSOzM6LFN5nI3mVoQqLMTlCGgQK7x3eRVbXd0nvvl6RqUjfNhQ6MIqRXDmavqU0ER3huJpTV5riZMnpO+/x4Jv/EDm79/lJ/ps0hKCaLUBqru/WSGKylUqBkokks2cLxhCIexKvyVB5SXV0zsnp+efkrf9lfEETfW8X3WwHulEzO7qXVEvrDXd3d9xtV+g8o+17/uOf/Ak/+sEPePdr73Jx7/JgdsiyjLqakx9X9F3LZnVLt9uyXd+h+0CRpXzy0Ozo3Ej0jhgifbdPcUlRRviRTCtE9IwuWWHbwUOAMivJ64r1dsPYjwRhENoSAvSbFkng4f17CAVX1+v0d0uoioLLec7yLGNxUjKvJNJKglRIoVAqTfhxvSYE2Kx27LYdwg0Y4TBIvBsJccfYeAxgC0V+dERVGVRmOXvwOg/f+6eURw/x093zN/VFrmYLTFYw+EBpbSLBiJdGlmEYDq01azRCSgKSi8sHvP7u18mK8tf0hPOrG19oibYfHGV9jM1yPvnsMx4/ew4CqrKg6TvmuqZrWsZ+wI2e65sVJ8cnh1CGLC8wZcBqhTAFphRkMRkzN9wRu4Ag0DcNfdvgvEjFOecPIYsSyLMkhR1GP4lzco4XC05PTxFK8r3vv0/wgVH6BLDMM2xWsL674Xaz48HRCbfXn4BrqBcFDy7mPLifcXKek5WACgwuYoocY3OQOrn3bMbQ3/H8yRVDG1DCY4QjUwElDbmREJOO++TkDGsz7NJQH5/z8J1/SH3xTaKuQXxluDG/klHUNa+98SZXjz+mb3aYwxE/tW73rkWtNVIb6vmS09fe4PWvfZNicZwWBvg79ez/OY0vbKJLIVMUrrW8/d577MaRVddxc3vL0DnmVcVI6qe2TYfJGsLzZzRtw9HREWVbYYxOscrGoJXCeZ9egQnO2I89XTey3fV0Q6BzEWkMOibRSxh9QiTnRcpGixKlLSYr+N3f+T1OlzOePnnEn+5WrLqRGD1SKeLxObJe8Ppb79Dv1tx0LVpHXj+uuX9U8N7DE959fclyFtE24ElBDllRIkyBl5JeC1RRQNAE75EGvI9s257ZbEZuDAUjJpfYWU62mJNVR1Qn97n3zjep7r8DeUkQir9LvPnrPoSRvPbue3zvu3/BZr1G0RGUxEWNUQo9sflMtWT27rd5+xvf4PT0DJNlCYDyysLwX0YaX8hEF0KAhKPlkqquePvtt3lxd8fddks/Oh59+jHNrsPcM8wyS7vbsmt2OEKSrfY9R0dHeOfIjcEYc7AbKqWIrmN0I6vVGudS/NEwDkhhkCIlqCgBRklym9xs4+gpioLNrsXkJV/72jcoDGzWN2ilkTISo0IpM01OgRaadnB4seW9t+7xj9495ayGo0XJvFIIMdAPia8ulUEIhdQanRdJ8urdtCiNgGC32xJiZL1ZY4+PKOoSNJiiJl+ccn7vbU7e+l3Ks4dJUIJB8Zu8l6chhODywQN+63d+l+/0HZvrF0gXkNLTS8/8qOTi/j3e+ca3eOPrv8dsPke8otRM5sn/MslfHV/oHX0+n1PXNVlmKYqCvCgoq4qjk0uOl3O261twI7bI6fqOKF/KYK+vr1FSsgmB+WzGZrM58OHmpcWo9GD3IosYAs51U/U9YJQgjBFrNH4KgUjQP0dVVZyfXyLDQFXO0Dqp0byHk+MLbDVHAo8/eoQKDccXOd98+wG/9fULKt1A7LBSpvAFYqq0m4LMlkQlQBqiSJyyoq5QEvq2Q8t0FC2LjDLPEEaRzypmZ5ecPHyPi7d+B3P8JqiJHT75AAR7/+9v6BCQFQXf/v1/SLPb8eGPfsiw2jKrZ9hZydd/99u89s5byYKaLV6hvaTxn7Mw5meNL2xH34ckXr14kRJTsoyiKDhaHnF6+Saub3jcdTx98YTXH5wn4usEiNj7zMsiRfSsVqsUxjDlqonTJbMymVx8WNONjjwvoEuAQK1SAkpmNYLI6Bzd6JAuVeJvb2758IOPeePBBScn51xc3KN7eoW2KdPtdnXD2DZkdFwcl3z94YLf/8ZrXB4pmtUNQgbGsSX4gC2m+CiVgdAEEZBCAxkhOqJMFfzV9TUgODpaJqCGH4lSUy6XHN97nfM3voU9ekjUWbL6wt90Tf4mjle+0dnJCb/9j/8Jp/dfQw0pZml5dkJ1tEBYPTHbXgFg7iESX8KX/VUfX9iOHmJks97w+PFj/uIvvsPJ5SUA8/mCxdkbXL94ysM33kTLER8dxurD7rynw6zWa/T039sjeJ1zvHgx0FUFQmq6vqfreqw1RO8IbsAYndxfYX+sTyGJQjpMVjCOI//6X/8b/sk//F1mdc47b7/Luoer2+esHt/QjyNHdcEbF8d8/fVT/pt/9JB7JwXN7RPGdkAqjVACNbkgU+tLJbskSbEldUn0A3frFU+efEocPWVREvxI1zhikZOVx9x7+Dpnb76LPboAVU7vriPpOOUkF/3N7gzHKaBSSM3J5X3K+RF2QofJyZ0IE/dgb/x/ZfwmfzY/7/jCinHee55cX/Fifce/+Tf/iv/+v/8/8eblAz569ITdZs18VnNcW9q7a1a3zzFaoJUieE+RZYnyaiOj68iyjNu7hnboadqOflQM44jNc/K8wGSJwS1Von2GpFI50GVc302BDzD2HYXSPLt9yr/8N89ZzGe4ENms79hu1hgDr5/OePPekrfPZ/z+b73JWw/OkK5naHaIEBJ0QipidCgC0Q0IG0AElMpSsIOWNNdr2qsbzBDQaHIPftsgCsv56+/w1u/8M87f+Trm9AHYBQGJICD3voFpxN/wif5SQJ2eWTVLxTchxctlbmLO/Zfx9xtf2EQ3xvDu197ltTfu8f3vfId/9S//BV/75u/z4N5DBhXxY8/Vsxt224a+dXgdKQpYr1doSNw1CVIGwCOVoB1aghRsmhaAoyxLqareJV6bD8nOaCz91GoZxoHR+0Nu2dB3RKk4XiRu95Nna8Z+RLjA/aOK+2dLHl4sePNizm+9c5+H90/JDdw9e8puu8UoyLSikBpJWlQiaZJHQIjEKlPKsbl5zHDznMtFSVnMaHY9Ics4ffcdvvGP/ymnb/1DzOIETAFi31SS8BOK7d/kSQ77iT79XIif+g0L8Zu/4P0yxxc20YUQvPXmQ/5v/9f/f3tntlvZcZ3hb9W8hzNw6KbYg6XAChwgRm5y4SB5hjxpniHJtZErA0EQwLYSGLCTltQDhzPsvWvIRR2y2bFkIYjEZqv3d3E4gjg84M+qWrXW//89/7C95De/+R2//Jdf0qy+4NHZEU1wvHr5NaVEhnFPKTWUoaTMi91A3zT0fYe2wn67rRlmMVOKUFJk2u8oqacNC+I4oCjEcaINDiWKONXMtRhr8GJOmZhSTUnd7xlMZr1ccNz0qFxwxvBo5TnqLD95dsbnnz3haBnQCi5ff8Vuc4F3mqYNmKY7BApotNUo49DWoq1nEodRFsYtErd8+vwTrCno0BKlwx095/izv+DkJz/FL0/ABkR0/SM+eO+9ffzx8450v+OX/lhek++DexW6MZa/+du/o+x3/NM//jO/+rff8vL173n91W8RJazXRxhjiTnCBNYqcoyUlInDQM6J1SqgEBRCGxquNzsa77GSGXYbds5gDjkNUhJGFClNGAFtDCm7uqJLOuz8qqB2ux1WCu16xdnxmrOTI5YOPjnpOT87ptGFtN+w3Y28+ur39B60SDU3cI4SU51916CdZ0qgvMH5Hi01iOLo9BGnqyOMFaJ1+PVT3NGn2MUTXLMGd9O2+WO/QJu5b35wod/MDJdSKGLw3Zq//sUvMGXi88/O+dW//ju/+8MLjA90ixVfv7pAGbkNX1A3lVSlmMaRzdWEDw15msgxQil4Y+kbQ8qZq4vXNE1DSZlF2zCMIyVHnNWkWEdcdzfVeqmFn3GKRCmUMtKGkd2+mg6G42P8YsXl9Z5xN2DyjjRt0GbEmtp+mUomA01oIEeMUxTRNb1GO5QYrLGkkmk++ZS2O8GFJYQWaZdgehQB0NwePd9hviya+f9zry2wWQRRlsXxY37+V39Jb0dOe/j1H57x5csLvr64wjlD0wR2OaG1Qoti0S/QQCmZHCMpRpQoVIE+NDRekeLIcrFgt9tCzpgbi2dbc86maaI23lm887RNZDuMpFIoRUgI2ynz6nKDTqm6wxrDZowsXeTJ2qDKANMOH+pEW87VjFIZQy51SiqXunNxTYOxGqPrPzrle5qjJ/jVM1A9RTTx9jpIkFx9R+u0FdQT6Czyme+HexW6JoMSiu9Znn/Ok5f/Sdj8B9dXLV/+9ys21yMpKerIRibliUYZQp4QySRVwGoSCe8MKmd2mx1jAmsygUjXeXZTAePYX11hlGbVdYgIl1dXRDG4Rc/SCa83G16Pie0EKoLXhuAcojVX+z3dds8kEZaZNAnKFqx3eCv40NVRUW2RXM8KxQRcY8gKsICZ8HbDICvM4hy3+BR0D9ThjLchy6X+E/qjV2wW+cz3w/2d0Q9vayVaEBdYPjpn+/Wa4/aShS+M+w0vXm/Y7AZyyYQgB19ujVJgrMIHQ0mJ9WKF04brqyv2w8B28wZLonGOtg1shkS7WjCNI0YdstdoGKaaxa4XPX2AcL3nOir2g0KTsUazXK94/PgUPQ3sr69Ri4acYJKCMQptAkpbQNVWV12tpUMbEFdjlhCLNg1FNyi/wC1OapJK+YYZ7G8U9Czyme+PezYYe9vogNKY9RmlP2XdXXJ25GmDYhwnhqlgjK6Zad7ROEchkUrCU/DBEVSi9ZqFa7m6LlxhaXSit0JYNLRjZnd9hQ7+YPgIp+sjUk68efUVrdd8cnTK6uUV//Vmx7ZpMUqwJBbBYMpIawut0Xhb6LtAYwWvDUVgyoUQHMYFRBu8bzDWorwiKYuyC0T3ZLPGr58ifkkpmjuF9JmZe+P+hH5wDrrrI6e7E7pPPie+fsHJSc/p8YKjVztgQElm2WjOjxcEqYMgYxw4XS1RJRG85njdMQ0bdFaYyWLKwMIV4njN2eqUCxlp25btdstisSDGyGY/sOgdvbfklPnz509YLbZ8vZnQFNZ9gGmPLzuePjrmZOlYLhXejGhVsMZivaXpeqwPoCwuNDgfQApTKijjEd1h/BFmcY40pxTVcDcWaGbmPnkPK/pB7KIQ17F6+lPkzRecl8CfXRRevNyi4kTXtPzs2WOWRrFuGrbba4pu6UOg5MjxuiN4QXdddV3dXXGyOKINjqFoQtAcr8+r37fOeK+IStGtH/Hm8oKgII/VUebR2tH5DTkOHPWG4+VjVJ5ovKPvHOtlnV83ShF8gzmYIfimq5V1axGtydSgR9ssUXaJ7U+R/gx0RxFTZ6nn1XzmPfB+vIFvu50srjulf/JzTqYv+NmnE/uLK34NdI3m2emaRpvqxKI8/bLHaoU1GmNAq4LRhmUXkOMFiy6gBILWhFZwQTGNita2KFG8fHVN0B2T9xgKsUSUhi4IR22Ht/VnWFNQeEQVukVAe433Nd9LG1sn4oylaA8mkJVBlQlvFTQtJfRIfw7Lp2A6wNzpUJ/bNmfun3sUutzdtx/eFUQc4fRz1ruRz4YtLkcWrqGUyMlxneG2ymM19F2Dohbq+r4np3zwcE/0rQEppDTStB7nNMEbNtsRNym8b9ht3uBVpFm1NdDhEOFjjGHY7zFGaDuD1gXnDb7xdH2HEmGaIhMGbRsw9a7cuAZjAkoZLBklmWg6VHOKXj0Fs7ozUfW2lXNe1Gfum/tf0eXtO0KNyimuYXn+nDheYoxwcrTi4vKSi+tXCIrWW4IzOKsRZfDe0bUt41gzuI4eLYjDHqVgmvaE4BAFxkBvAinVMaezp6fsNju8NShl2O/3xBzpw5K283hvD/30tSagTcMYM00ItKE9xC4bilYYbTDIoftOwC7IusEtTlGrc9D920mz76ywz8z8sDyIWI8iDt2u6c+eoUi0ztEvHf21JadIiSNGgTWapu1uc7KtqsGDzipKsKQ8EXA1cVNR/eC8sN3uUWI47jv2jWPYbhEpGD3hnSbFDa4JGFtjnUoBrT3Gd9jQ1eq/c7cpMFEKKReQKvgkimx67NFz1PLxO9v1ubNt5iHwIISuEZCW5vgn6KIZzQu0fYULDSmOkCZyHCklo51GlMYYXWfTZUcaBkR7DKmu6E1Aa2G72yEoXDDEKYPyNL0mlYz3Dts2DMO+bv91vWtvmh6KwlqPbXts09xmgGmt60w0iaIsCU9yHeI7bP8I3Z0eGmI01Y15FvnMw+C9C10QSq6TX6Ja/PFzjGmxNtAMF6RpRBGZ9lumaUSsZpoioiwpRbRReNtR4kScBhADokAU3ps6JBMLm6stoenY7a/wy2U1/S+FdK2xzrLqWkoR2maJKItWhmItxoU6F19uvOChZCi2AbdG9Y8x7THKexBHKerOSh5B7J9+AWZm7oH3LnQAMTVbHEwd8lg3WNcyXX0J4xUqblBO4dJAjBnrfe1RTxrlBK0VOY2UwaCyJ45DDV0MhlLAe6EohbUOTKpttsaQUuZx31FywUpAGYMOHu0MSiuUcljrKQhahJwLIgblz7D9EfSnSHNEOoj5bsGtFh9nkc88DN6/0G8r0XIbkStKo9sV2lni9oK4e0PWrl5hjRsE2A9DPV+nTBYYS8EGRZ6qub811UgixogoRbtYorXCes2wUweftlzvx42lJI1xFpSgva3urSiUGKYsjMVQXMA1S3R/jjQ9KAtiUYeC22yDMPNQef9C/zaUA2cwpkE1C+KwIQ7XmOE1kiONGZGSyWliKqBsQ0kT2IiSQkmRVEZ88BxcyLCmtqp63wI1Fy3GCWsdGaEUAaXRrvayC6XmP2uP8Ut0f4xuVhSzIBZBVRfr2Vl45sHzcIUO1JEuj/IG6zp0u0btOtJuA2agxD3EEVVyLZiliZImJCcSI8E5tNHVRLA+VDtmF6pRpNIo48k5oZ0lF0UWQ9aerCxaLBiLbpfYZo34BUkMGXnHbXkeJ5156Dwood8ka5Sb5rECSKEUBRiUMUjv0WGkDBvycE0Zd6i4RyQj2pLjCDnWvnI1UeRwhs/54CdYr8bqgEk9i2sLyYBWFiUOxKNtC26JCkvEuBqgIBopBSsHV1aqG8yNceMs9ZmHyoMS+g1361k3Y603/t1FNGIdYgKqWUEcKeMA054c95S0p+QRSQO6DKQ0EQ+BB6XkaiMsUi2ZtQWlKUqRtEbZgLId2vWIbavdsnLvPLe6kud3RF3efdYzMw+OByn0/90qC3/s+CliQBvQoZo85IwuE7rsKeUg9mGPSZGcJ1IayTkeOnEVIgqlLEo70AbnW5T1iG5AHLn2vX3L85k93WY+LB6m0OHdZZ1vXy/LzfFYK6CmowgNQgYn1Re9JHSOUBKlJEDVzHLRdYsvCnShCrimf9w1WP6uGfJ5LZ956Dxcof8fKLcBBwXQHHrtuJMDcPOddz6Ub8wAuP36IQdpFvHMj4EPX+hyMzpSbu2bb8jv7PVvHg5bgPL284WDMSM3cQkFkbuV9FnuMx82H7zQpUCVaT58pi7jdSMuB4u2Gykf4kiRw+V3/V4B0kH5hXw4Dxx2BzKfx2c+fD54oVfejS26y9sT/p+2cXK3q7aeF/CZHx0fvtC/pS1t1urMzFvmfenMzEfALPSZmY+AWegzMx8Bs9BnZj4CZqHPzHwEzEKfmfkImIU+M/MRMAt9ZuYjwGw2m/f9HGZmZn5ANpsN/wO+4LdlGNk1aAAAAABJRU5ErkJggg==\" id=\"image7a64fba29a\" transform=\"scale(1 -1) translate(0 -138.96)\" x=\"40\" y=\"-10.455579\" width=\"180\" height=\"138.96\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m75da7b547b\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m75da7b547b\" x=\"39.711765\" y=\"149.537454\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(36.530515 164.135892) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m75da7b547b\" x=\"101.476471\" y=\"149.537454\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(91.932721 164.135892) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m75da7b547b\" x=\"163.241176\" y=\"149.537454\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(150.516176 164.135892) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m9ccb66fea6\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9ccb66fea6\" x=\"39.65\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.2875 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m9ccb66fea6\" x=\"39.65\" y=\"35.705101\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(13.5625 39.50432) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m9ccb66fea6\" x=\"39.65\" y=\"60.410983\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(13.5625 64.210202) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m9ccb66fea6\" x=\"39.65\" y=\"85.116866\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(13.5625 88.916085) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m9ccb66fea6\" x=\"39.65\" y=\"109.822748\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(13.5625 113.621967) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m9ccb66fea6\" x=\"39.65\" y=\"134.528631\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(7.2 138.327849) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 39.65 149.537454 \n", "L 39.65 10.937454 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 219.**********.537454 \n", "L 219.508824 10.937454 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 39.65 149.537454 \n", "L 219.**********.537454 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 39.65 10.937454 \n", "L 219.508824 10.937454 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p8fa03e93bc\">\n", "   <rect x=\"39.65\" y=\"10.937454\" width=\"179.858824\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.set_figsize()\n", "print('input image shape:', img.permute(1, 2, 0).shape)\n", "d2l.plt.imshow(img.permute(1, 2, 0));\n", "print('output image shape:', out_img.shape)\n", "d2l.plt.imshow(out_img);"]}, {"cell_type": "markdown", "id": "e85f4c76", "metadata": {"origin_pos": 27}, "source": ["In a fully convolutional network, we [**initialize the transposed convolutional layer with upsampling of bilinear interpolation. For the $1\\times 1$ convolutional layer, we use Xavier initialization.**]\n"]}, {"cell_type": "code", "execution_count": 10, "id": "1ae40200", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:28.372559Z", "iopub.status.busy": "2023-08-18T19:34:28.371880Z", "iopub.status.idle": "2023-08-18T19:34:28.381788Z", "shell.execute_reply": "2023-08-18T19:34:28.380642Z"}, "origin_pos": 29, "tab": ["pytorch"]}, "outputs": [], "source": ["W = bilinear_kernel(num_classes, num_classes, 64)\n", "net.transpose_conv.weight.data.copy_(W);"]}, {"cell_type": "markdown", "id": "fbc76cde", "metadata": {"origin_pos": 30}, "source": ["## [**Reading the Dataset**]\n", "\n", "We read\n", "the semantic segmentation dataset\n", "as introduced in :numref:`sec_semantic_segmentation`. \n", "The output image shape of random cropping is\n", "specified as $320\\times 480$: both the height and width are divisible by $32$.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "0bdc2a20", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:28.386035Z", "iopub.status.busy": "2023-08-18T19:34:28.385440Z", "iopub.status.idle": "2023-08-18T19:35:21.373422Z", "shell.execute_reply": "2023-08-18T19:35:21.369676Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["read 1114 examples\n", "read 1078 examples\n"]}], "source": ["batch_size, crop_size = 32, (320, 480)\n", "train_iter, test_iter = d2l.load_data_voc(batch_size, crop_size)"]}, {"cell_type": "markdown", "id": "6654107c", "metadata": {"origin_pos": 32}, "source": ["## [**Training**]\n", "\n", "\n", "Now we can train our constructed\n", "fully convolutional network. \n", "The loss function and accuracy calculation here\n", "are not essentially different from those in image classification of earlier chapters. \n", "Because we use the output channel of the\n", "transposed convolutional layer to\n", "predict the class for each pixel,\n", "the channel dimension is specified in the loss calculation.\n", "In addition, the accuracy is calculated\n", "based on correctness\n", "of the predicted class for all the pixels.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "b65f6226", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:21.378517Z", "iopub.status.busy": "2023-08-18T19:35:21.377599Z", "iopub.status.idle": "2023-08-18T19:36:21.659017Z", "shell.execute_reply": "2023-08-18T19:36:21.657836Z"}, "origin_pos": 34, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.420, train acc 0.869, test acc 0.849\n", "32.0 examples/sec on [device(type='mps')]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"187.155469pt\" viewBox=\"0 0 235.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-27T22:43:40.309096</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 235.**********.155469 \n", "L 235.784375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mfa31b76487\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mfa31b76487\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(26.921875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 78.928125 149.599219 \n", "L 78.928125 10.999219 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mfa31b76487\" x=\"78.928125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 127.753125 149.599219 \n", "L 127.753125 10.999219 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mfa31b76487\" x=\"127.753125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(124.571875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 176.578125 149.599219 \n", "L 176.578125 10.999219 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mfa31b76487\" x=\"176.578125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(173.396875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mfa31b76487\" x=\"225.403125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(222.221875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 177.875781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m49581baa9a\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m49581baa9a\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 153.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 121.879219 \n", "L 225.403125 121.879219 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m49581baa9a\" x=\"30.103125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 125.678438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 94.159219 \n", "L 225.403125 94.159219 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m49581baa9a\" x=\"30.103125\" y=\"94.159219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 97.958438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 66.439219 \n", "L 225.403125 66.439219 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m49581baa9a\" x=\"30.103125\" y=\"66.439219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 70.238437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 38.719219 \n", "L 225.403125 38.719219 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m49581baa9a\" x=\"30.103125\" y=\"38.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.518438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m49581baa9a\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 30.818686 -1 \n", "L 38.719301 60.021267 \n", "L 47.335478 55.570935 \n", "L 55.951654 59.412143 \n", "L 64.567831 58.80658 \n", "L 73.184007 61.869247 \n", "L 78.928125 62.978629 \n", "L 87.544301 75.189162 \n", "L 96.160478 76.631073 \n", "L 104.776654 78.233088 \n", "L 113.392831 79.080116 \n", "L 122.009007 79.208397 \n", "L 127.753125 79.325724 \n", "L 136.369301 89.134382 \n", "L 144.985478 86.532393 \n", "L 153.601654 85.664363 \n", "L 162.217831 85.893968 \n", "L 170.834007 86.693065 \n", "L 176.578125 86.2736 \n", "L 185.194301 94.089966 \n", "L 193.810478 91.158469 \n", "L 202.426654 93.320773 \n", "L 211.042831 92.88704 \n", "L 219.659007 92.561768 \n", "L 225.403125 91.387539 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M -1 56.151384 \n", "L 7.126654 52.39898 \n", "L 15.742831 48.86796 \n", "L 24.359007 47.344458 \n", "L 30.103125 46.099288 \n", "L 38.719301 35.47292 \n", "L 47.335478 36.564222 \n", "L 55.951654 35.328659 \n", "L 64.567831 35.604786 \n", "L 73.184007 34.989127 \n", "L 78.928125 34.853698 \n", "L 87.544301 32.374248 \n", "L 96.160478 32.24694 \n", "L 104.776654 32.061905 \n", "L 113.392831 31.954144 \n", "L 122.009007 31.881042 \n", "L 127.753125 31.910251 \n", "L 136.369301 29.373499 \n", "L 144.985478 30.198415 \n", "L 153.601654 30.276095 \n", "L 162.217831 30.319338 \n", "L 170.834007 30.096672 \n", "L 176.578125 30.292704 \n", "L 185.194301 27.788368 \n", "L 193.810478 29.013247 \n", "L 202.426654 28.208485 \n", "L 211.042831 28.575416 \n", "L 219.659007 28.685053 \n", "L 225.403125 29.106022 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 30.103125 36.285726 \n", "L 78.928125 33.569569 \n", "L 127.753125 32.548555 \n", "L 176.578125 31.816298 \n", "L 225.403125 31.908906 \n", "\" clip-path=\"url(#pb338a6f57a)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 37.103125 144.599219 \n", "L 114.871875 144.599219 \n", "Q 116.871875 144.599219 116.871875 142.599219 \n", "L 116.871875 99.564844 \n", "Q 116.871875 97.564844 114.871875 97.564844 \n", "L 37.103125 97.564844 \n", "Q 35.103125 97.564844 35.103125 99.564844 \n", "L 35.103125 142.599219 \n", "Q 35.103125 144.599219 37.103125 144.599219 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 39.103125 105.663281 \n", "L 49.103125 105.663281 \n", "L 59.103125 105.663281 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(67.103125 109.163281) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 39.103125 120.341406 \n", "L 49.103125 120.341406 \n", "L 59.103125 120.341406 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(67.103125 123.841406) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 39.103125 135.019531 \n", "L 49.103125 135.019531 \n", "L 59.103125 135.019531 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(67.103125 138.519531) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb338a6f57a\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def loss(inputs, targets):\n", "    return F.cross_entropy(inputs, targets, reduction='none').mean(1).mean(1)\n", "\n", "num_epochs, lr, wd, devices = 5, 0.001, 1e-3, d2l.try_all_gpus()\n", "trainer = torch.optim.SGD(net.parameters(), lr=lr, weight_decay=wd)\n", "d2l.train_ch13(net, train_iter, test_iter, loss, trainer, num_epochs, devices)"]}, {"cell_type": "markdown", "id": "9c0c7f12", "metadata": {"origin_pos": 35}, "source": ["## [**Prediction**]\n", "\n", "\n", "When predicting, we need to standardize the input image\n", "in each channel and transform the image into the four-dimensional input format required by the CNN.\n"]}, {"cell_type": "code", "execution_count": 14, "id": "e7f1ceba", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:21.663792Z", "iopub.status.busy": "2023-08-18T19:36:21.662705Z", "iopub.status.idle": "2023-08-18T19:36:21.669589Z", "shell.execute_reply": "2023-08-18T19:36:21.668481Z"}, "origin_pos": 37, "tab": ["pytorch"]}, "outputs": [], "source": ["def predict(img):\n", "    X = test_iter.dataset.normalize_image(img).unsqueeze(0)\n", "    pred = net(X.to(devices[0])).argmax(dim=1)\n", "    return pred.reshape(pred.shape[1], pred.shape[2])"]}, {"cell_type": "markdown", "id": "27b9364c", "metadata": {"origin_pos": 38}, "source": ["To [**visualize the predicted class**] of each pixel, we map the predicted class back to its label color in the dataset.\n"]}, {"cell_type": "code", "execution_count": 15, "id": "88b09f25", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:21.673578Z", "iopub.status.busy": "2023-08-18T19:36:21.672677Z", "iopub.status.idle": "2023-08-18T19:36:21.678207Z", "shell.execute_reply": "2023-08-18T19:36:21.677171Z"}, "origin_pos": 40, "tab": ["pytorch"]}, "outputs": [], "source": ["def label2image(pred):\n", "    colormap = torch.tensor(d2l.VOC_COLORMAP, device=devices[0])\n", "    X = pred.long()\n", "    return colormap[X, :]"]}, {"cell_type": "markdown", "id": "db007d32", "metadata": {"origin_pos": 41}, "source": ["Images in the test dataset vary in size and shape.\n", "Since the model uses a transposed convolutional layer with stride of 32,\n", "when the height or width of an input image is indivisible by 32,\n", "the output height or width of the\n", "transposed convolutional layer will deviate from the shape of the input image.\n", "In order to address this issue,\n", "we can crop multiple rectangular areas with height and width that are integer multiples of 32 in the image,\n", "and perform forward propagation\n", "on the pixels in these areas separately.\n", "Note that\n", "the union of these rectangular areas needs to completely cover the input image.\n", "When a pixel is covered by multiple rectangular areas,\n", "the average of the transposed convolution outputs\n", "in separate areas for this same pixel\n", "can be input to\n", "the softmax operation\n", "to predict the class.\n", "\n", "\n", "For simplicity, we only read a few larger test images,\n", "and crop a $320\\times480$ area for prediction starting from the upper-left corner of an image.\n", "For these test images, we\n", "print their cropped areas,\n", "prediction results,\n", "and ground-truth row by row.\n"]}, {"cell_type": "code", "execution_count": 16, "id": "8f780e21", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:21.682056Z", "iopub.status.busy": "2023-08-18T19:36:21.681351Z", "iopub.status.idle": "2023-08-18T19:36:53.274281Z", "shell.execute_reply": "2023-08-18T19:36:53.273369Z"}, "origin_pos": 43, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"460.8pt\" height=\"313.900358pt\" viewBox=\"0 0 460.8 313.900358\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-27T22:48:24.325095</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 313.900358 \n", "L 460.8 313.900358 \n", "L 460.8 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 7.2 71.895652 \n", "L 104.243478 71.895652 \n", "L 104.243478 7.2 \n", "L 7.2 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p08c2615a20)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image02a243a99c\" transform=\"scale(1 -1) translate(0 -64.8)\" x=\"7.2\" y=\"-7.095652\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 7.2 71.895652 \n", "L 7.2 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 104.243478 71.895652 \n", "L 104.243478 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 7.2 71.895652 \n", "L 104.243478 71.895652 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 7.2 7.2 \n", "L 104.243478 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 123.652174 71.895652 \n", "L 220.695652 71.895652 \n", "L 220.695652 7.2 \n", "L 123.652174 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p6c1939d206)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image9a17d7fdce\" transform=\"scale(1 -1) translate(0 -64.8)\" x=\"123.652174\" y=\"-7.095652\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 123.652174 71.895652 \n", "L 123.652174 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 220.695652 71.895652 \n", "L 220.695652 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 123.652174 71.895652 \n", "L 220.695652 71.895652 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 123.652174 7.2 \n", "L 220.695652 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 240.104348 71.895652 \n", "L 337.147826 71.895652 \n", "L 337.147826 7.2 \n", "L 240.104348 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pc2becc8ba2)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageac18017e38\" transform=\"scale(1 -1) translate(0 -64.8)\" x=\"240.104348\" y=\"-7.095652\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 240.104348 71.895652 \n", "L 240.104348 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 337.147826 71.895652 \n", "L 337.147826 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 240.104348 71.895652 \n", "L 337.147826 71.895652 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 240.104348 7.2 \n", "L 337.147826 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 356.556522 71.895652 \n", "L 453.6 71.895652 \n", "L 453.6 7.2 \n", "L 356.556522 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pd9b12f6172)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image5a3d66cda0\" transform=\"scale(1 -1) translate(0 -64.8)\" x=\"356.556522\" y=\"-7.095652\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 356.556522 71.895652 \n", "L 356.556522 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 453.6 71.895652 \n", "L 453.6 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 356.556522 71.895652 \n", "L 453.6 71.895652 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 356.556522 7.2 \n", "L 453.6 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 7.2 189.298005 \n", "L 104.243478 189.298005 \n", "L 104.243478 124.602353 \n", "L 7.2 124.602353 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p58d81d9d21)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAIcAAABaCAYAAACSR0X7AAAEhklEQVR4nO3c224bVRiG4deJEyi7lgBtFQRCiJ2ExAEgqATiArgi7g04RMBREQdsCpRCaBJI4qSA4zZNPRx8M5pR4h/vPWvc75GiJlVi2enbNWtm1nILyDAbYKXuJ2DpchwWchwWchwWchwWchwWchwWchwWchwWchwWchwWchwWchwWchwWchwWchwWchwWchwWchwWchwWas/rgVuVPx9ncIUZ0AUezOtJ2FRmGscKcBF4DXgj/3oFeIY4jgNgDzgEbgMnwD9AD7gP9Gf5BG0sLabcmlAE8TIK4nngAuXIMaqs8kSKMDpoZLkPbKFQeigmgHvA3TM/a7MzURxt4DngVeAV4FkmC2ISfRQLKJQuCmQb+AONQHdQODadseJYR0FcA64AaywmiFEUL+IERfMnsAscATvoUHWMR5hxjBTHKjpkXAM286+bIgNOURg/A9+iWE7qfFINMTSOq8BHwOtofpHKSDGJDJ0ZHaBIvkMji0eTwcI41oB3gA/RqWiToxikOI2+BXwD/EY5lzEZGMcG8DE6A1n2q2QZmuRuAz+gs6JTNG/5N/+eB8Sn1GuVzy+ieVnxuEf5Y0Ezw2tdguwK5QWPx4AP0AtdttFimCIU0NlOL/+8QxnKAbpuAwrjKuXv6QnK32OW/0wR1k7++WH+GPdQkMdzeB2z0voEsnXOh/CwhTGK6hA76e8nq/z5N/Aj8BlpjiztR+t+Bg0yi/8w1dsKl9AolOrtg2WfUiQtA26S7i0Cx1GjPpoAp8px1KiHLvWnynHUaB9da0mV46hJhu4upzoZBcdRq5TnG+A4atNHd4pT5jhqkKFVbzt1P5Eh5raG1M7L0OXyL4DraJFSyhzHAhRrSq4DX6N7K03gOOasj5Yvfk65DrYpHMecZOjO61f5R+qHkEEcxxxkaJT4FI0aTV1p5jgmVP0HP6l87KErn1+S9tXPUTiOIfqUC5TvoFPQYv/Mdv49XXTNos9ybYlwHLliFViXcuPUPvAX2ubQQ/OGJk0op/VQx1GcYu4BN9Dain20KivFlVmL1u6c+YtV4EmW89JpdXTYQivOb6PRwTGc11o/M5luA28C76MlbE1eS1q8sGMUwy109nCEAmnqWcSihPtWnkI73N6ieftWMnTmsIuuSv6KFvM6hvEM3fG2AbxL+pFk6NCwi+YPP6H5Q8rrJVI38kbqp4H3gLfRxp0UIilGiA7wPVrm7yBmZ6xd9i3gBRTIJtoAdYHF7aEt3oejiyaSvwC/oxtZp//zczaZid+8ZZ3yfTouAy+i9+nYYLYjS3Fh6QCdat5EZxc9PIeYt6nf2af6QKtoIrsJvIRGlcto22AbjTTF91bjKUaEu+gwcYiuOG6hLYXF+2v4cLFYM4sjsoYOO4+gHV6r6NBUvY7SQaeXxRusnOIQUjD3OKy5lvFCqM2I47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47CQ47DQf5aoEu+OEYNfAAAAAElFTkSuQmCC\" id=\"image1f1b0a140e\" transform=\"scale(1 -1) translate(0 -64.8)\" x=\"7.2\" y=\"-124.498005\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 7.2 189.298005 \n", "L 7.2 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 104.243478 189.298005 \n", "L 104.243478 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 7.2 189.298005 \n", "L 104.243478 189.298005 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 7.2 124.602353 \n", "L 104.243478 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 123.652174 189.298005 \n", "L 220.695652 189.298005 \n", "L 220.695652 124.602353 \n", "L 123.652174 124.602353 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pc6656864ba)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image5e65f41585\" transform=\"scale(1 -1) translate(0 -64.8)\" x=\"123.652174\" y=\"-124.498005\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 123.652174 189.298005 \n", "L 123.652174 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 220.695652 189.298005 \n", "L 220.695652 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 123.652174 189.298005 \n", "L 220.695652 189.298005 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 123.652174 124.602353 \n", "L 220.695652 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 240.104348 189.298005 \n", "L 337.147826 189.298005 \n", "L 337.147826 124.602353 \n", "L 240.104348 124.602353 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p7efdb02b85)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAIcAAABaCAYAAACSR0X7AAADTklEQVR4nO3b34tUdRyH8WdnHTfL3LbWTTZNIjMLKqGgm/7d/oXuLG9M74wuIhAhUoqISqRy09XjxWcOnl33LZt7zsys87xgGHZYzg7Mw5nvr10CGqQ9jGb9BjS/jEORcSgyDkXGocg4FBnH3Box64/nyEz/+sJaAo4BLwOvTV5bBs5MnkfAJvAQ+Bm4CfwK3J/6u3QRbCqWgBPAO8AH1Ic/Blb2+L2uhorkD+B74AfgDtP42IxjcK8C54ALwGnqbrE7gP1qgH+An4BrwC/Ao4O/xcA4BnEEOAVcpMJYnbz+vFHs1gAPgBvAVeB2T9fdyTh6MwbeBM5TY4fTVCR9BbGXBvgP+Ir6yumXcRzIEnAS+Ah4D1inBpRDBrGXLeBL4FavV3W28tyOA58BnwMvMf0gusbUoLVfxvG/LAOvAB8Dn1LT0FlG0dqiBqr9Mo5natcj1qkxxHnqa+QgM44h3AP+7f2qxvGUFWCNWod4nxpkHmc2Y4n9+g3Y7v2qxgHUmKGN4V1qsWrM/MbQ1QB/M8S8YoHjWKGCuEAFsUYtWx+GIHbrd5bSWrA4jgJv8WIE0XoI3B3kygsSx4iadn5CrVwe9iC6tqm9lv4tSBxrwBfUPseLEgXUOOM2Q8xUYKFWSDeBD6kp6TpPpqOHNZYHwHfA19RUtn8LFEerXcjaAN6ePDZ4sso577E0wJ/AJeBH3JUd1IiK5RRwlto0W2U+1za2qQ22yww1CO0yjqcsU7OaVeqraGPyOMnOwznTjKahToJ9Q50KG+5u0WUc+9I91vcGdXdpp8VjKqQR/W/Rt4d7vgWuM9TYIjGOA2nPe7YrqpuTn1+fPE5QM6Rldu7cdgNqOs9b1LrFfeB3Kowr1Bhj+oxjUGPqbtLu10AFdKzzO/eo434Af1GHdx5NnmfLOBT5fyuKjEORcSgyDkXGocg4FBmHIuNQZByKjEORcSgyDkXGocg4FBmHIuNQZByKjEORcSgyDkXGocg4FBmHIuNQZByKjEORcSgyDkXGocg4FBmHIuNQZByKjEORcSgyDkXGocg4FBmHIuNQZByKjEORcSgyDkXGocg4FBmHIuNQZByKjEORcSgyDkXGocg4FBmHIuNQZByKjEORcSh6DLKxaFbXBzRdAAAAAElFTkSuQmCC\" id=\"image325aa1fc6e\" transform=\"scale(1 -1) translate(0 -64.8)\" x=\"240.104348\" y=\"-124.498005\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 240.104348 189.298005 \n", "L 240.104348 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 337.147826 189.298005 \n", "L 337.147826 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 240.104348 189.298005 \n", "L 337.147826 189.298005 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 240.104348 124.602353 \n", "L 337.147826 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 356.556522 189.298005 \n", "L 453.6 189.298005 \n", "L 453.6 124.602353 \n", "L 356.556522 124.602353 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p42e0cdb359)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageaca52c207c\" transform=\"scale(1 -1) translate(0 -64.8)\" x=\"356.556522\" y=\"-124.498005\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 356.556522 189.298005 \n", "L 356.556522 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 453.6 189.298005 \n", "L 453.6 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 356.556522 189.298005 \n", "L 453.6 189.298005 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 356.556522 124.602353 \n", "L 453.6 124.602353 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 7.2 306.700358 \n", "L 104.243478 306.700358 \n", "L 104.243478 242.004706 \n", "L 7.2 242.004706 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p9a32b56b87)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image1f5dea1503\" transform=\"scale(1 -1) translate(0 -64.8)\" x=\"7.2\" y=\"-241.900358\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 7.2 306.700358 \n", "L 7.2 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 104.243478 306.700358 \n", "L 104.243478 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_45\">\n", "    <path d=\"M 7.2 306.700358 \n", "L 104.243478 306.700358 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_46\">\n", "    <path d=\"M 7.2 242.004706 \n", "L 104.243478 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_10\">\n", "   <g id=\"patch_47\">\n", "    <path d=\"M 123.652174 306.700358 \n", "L 220.695652 306.700358 \n", "L 220.695652 242.004706 \n", "L 123.652174 242.004706 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p9c9a5a54c7)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image382bffb801\" transform=\"scale(1 -1) translate(0 -64.8)\" x=\"123.652174\" y=\"-241.900358\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_48\">\n", "    <path d=\"M 123.652174 306.700358 \n", "L 123.652174 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_49\">\n", "    <path d=\"M 220.695652 306.700358 \n", "L 220.695652 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_50\">\n", "    <path d=\"M 123.652174 306.700358 \n", "L 220.695652 306.700358 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_51\">\n", "    <path d=\"M 123.652174 242.004706 \n", "L 220.695652 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_11\">\n", "   <g id=\"patch_52\">\n", "    <path d=\"M 240.104348 306.700358 \n", "L 337.147826 306.700358 \n", "L 337.147826 242.004706 \n", "L 240.104348 242.004706 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p389395a2a8)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image6d90abc5ef\" transform=\"scale(1 -1) translate(0 -64.8)\" x=\"240.104348\" y=\"-241.900358\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_53\">\n", "    <path d=\"M 240.104348 306.700358 \n", "L 240.104348 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_54\">\n", "    <path d=\"M 337.147826 306.700358 \n", "L 337.147826 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_55\">\n", "    <path d=\"M 240.104348 306.700358 \n", "L 337.147826 306.700358 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_56\">\n", "    <path d=\"M 240.104348 242.004706 \n", "L 337.147826 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_12\">\n", "   <g id=\"patch_57\">\n", "    <path d=\"M 356.556522 306.700358 \n", "L 453.6 306.700358 \n", "L 453.6 242.004706 \n", "L 356.556522 242.004706 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p187cca365a)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image2477402fa6\" transform=\"scale(1 -1) translate(0 -64.8)\" x=\"356.556522\" y=\"-241.900358\" width=\"97.2\" height=\"64.8\"/>\n", "   </g>\n", "   <g id=\"patch_58\">\n", "    <path d=\"M 356.556522 306.700358 \n", "L 356.556522 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_59\">\n", "    <path d=\"M 453.6 306.700358 \n", "L 453.6 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_60\">\n", "    <path d=\"M 356.556522 306.700358 \n", "L 453.6 306.700358 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_61\">\n", "    <path d=\"M 356.556522 242.004706 \n", "L 453.6 242.004706 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p08c2615a20\">\n", "   <rect x=\"7.2\" y=\"7.2\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6c1939d206\">\n", "   <rect x=\"123.652174\" y=\"7.2\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pc2becc8ba2\">\n", "   <rect x=\"240.104348\" y=\"7.2\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pd9b12f6172\">\n", "   <rect x=\"356.556522\" y=\"7.2\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p58d81d9d21\">\n", "   <rect x=\"7.2\" y=\"124.602353\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pc6656864ba\">\n", "   <rect x=\"123.652174\" y=\"124.602353\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p7efdb02b85\">\n", "   <rect x=\"240.104348\" y=\"124.602353\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p42e0cdb359\">\n", "   <rect x=\"356.556522\" y=\"124.602353\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p9a32b56b87\">\n", "   <rect x=\"7.2\" y=\"242.004706\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p9c9a5a54c7\">\n", "   <rect x=\"123.652174\" y=\"242.004706\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p389395a2a8\">\n", "   <rect x=\"240.104348\" y=\"242.004706\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p187cca365a\">\n", "   <rect x=\"356.556522\" y=\"242.004706\" width=\"97.043478\" height=\"64.695652\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 800x600 with 12 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["voc_dir = d2l.download_extract('voc2012', 'VOCdevkit/VOC2012')\n", "test_images, test_labels = d2l.read_voc_images(voc_dir, False)\n", "n, imgs = 4, []\n", "for i in range(n):\n", "    crop_rect = (0, 0, 320, 480)\n", "    X = torchvision.transforms.functional.crop(test_images[i], *crop_rect)\n", "    pred = label2image(predict(X))\n", "    imgs += [X.permute(1,2,0), pred.cpu(),\n", "             torchvision.transforms.functional.crop(\n", "                 test_labels[i], *crop_rect).permute(1,2,0)]\n", "d2l.show_images(imgs[::3] + imgs[1::3] + imgs[2::3], 3, n, scale=2);"]}, {"cell_type": "markdown", "id": "f4076170", "metadata": {"origin_pos": 44}, "source": ["## Summary\n", "\n", "* The fully convolutional network first uses a CNN to extract image features, then transforms the number of channels into the number of classes via a $1\\times 1$ convolutional layer, and finally transforms the height and width of the feature maps to those of the input image via the transposed convolution.\n", "* In a fully convolutional network, we can use upsampling of bilinear interpolation to initialize the transposed convolutional layer.\n", "\n", "\n", "## Exercises\n", "\n", "1. If we use Xavier initialization for the transposed convolutional layer in the experiment, how does the result change?\n", "1. Can you further improve the accuracy of the model by tuning the hyperparameters?\n", "1. Predict the classes of all pixels in test images.\n", "1. The original fully convolutional network paper also uses outputs of some intermediate CNN layers :cite:`Long.Shelhamer.Darrell.2015`. Try to implement this idea.\n"]}, {"cell_type": "markdown", "id": "a4ba1b83", "metadata": {"origin_pos": 46, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1582)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON> (aideep)", "language": "python", "name": "<PERSON><PERSON>"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
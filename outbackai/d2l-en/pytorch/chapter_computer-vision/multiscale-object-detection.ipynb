{"cells": [{"cell_type": "markdown", "id": "dc4a093a", "metadata": {"origin_pos": 0}, "source": ["# Multiscale Object Detection\n", ":label:`sec_multiscale-object-detection`\n", "\n", "\n", "In :numref:`sec_anchor`,\n", "we generated multiple anchor boxes centered on each pixel of an input image. \n", "Essentially these anchor boxes \n", "represent samples of\n", "different regions of the image.\n", "However, \n", "we may end up with too many anchor boxes to compute\n", "if they are generated for *every* pixel.\n", "Think of a $561 \\times 728$ input image.\n", "If five anchor boxes \n", "with varying shapes\n", "are generated for each pixel as their center,\n", "over two million anchor boxes ($561 \\times 728 \\times 5$) need to be labeled and predicted on the image.\n", "\n", "## Multiscale Anchor Boxes\n", ":label:`subsec_multiscale-anchor-boxes`\n", "\n", "You may realize that\n", "it is not difficult to reduce anchor boxes on an image.\n", "For instance, we can just \n", "uniformly sample a small portion of pixels\n", "from the input image\n", "to generate anchor boxes centered on them.\n", "In addition, \n", "at different scales\n", "we can generate different numbers of anchor boxes\n", "of different sizes.\n", "Intuitively,\n", "smaller objects are more likely\n", "to appear on an image than larger ones.\n", "As an example,\n", "$1 \\times 1$, $1 \\times 2$, and $2 \\times 2$ objects \n", "can appear on a $2 \\times 2$ image\n", "in 4, 2, and 1 possible ways, respectively.\n", "Therefore, when using smaller anchor boxes to detect smaller objects, we can sample more regions,\n", "while for larger objects we can sample fewer regions.\n", "\n", "To demonstrate how to generate anchor boxes\n", "at multiple scales, let's read an image.\n", "Its height and width are 561 and 728 pixels, respectively.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "aa342d62", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:37.388787Z", "iopub.status.busy": "2023-08-18T19:43:37.388422Z", "iopub.status.idle": "2023-08-18T19:43:40.309649Z", "shell.execute_reply": "2023-08-18T19:43:40.308383Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(561, 728)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["%matplotlib inline\n", "import torch\n", "from d2l import torch as d2l\n", "\n", "img = d2l.plt.imread('../img/catdog.jpg')\n", "h, w = img.shape[:2]\n", "h, w"]}, {"cell_type": "markdown", "id": "4155e3fe", "metadata": {"origin_pos": 3}, "source": ["Recall that in :numref:`sec_conv_layer`\n", "we call a two-dimensional array output of \n", "a convolutional layer a feature map.\n", "By defining the feature map shape,\n", "we can determine centers of uniformly sampled anchor boxes  on any image.\n", "\n", "\n", "The `display_anchors` function is defined below.\n", "[**We generate anchor boxes (`anchors`) on the feature map (`fmap`) with each unit (pixel) as the anchor box center.**]\n", "Since the $(x, y)$-axis coordinate values\n", "in the anchor boxes (`anchors`) have been divided by the width and height of the feature map (`fmap`),\n", "these values are between 0 and 1,\n", "which indicate the relative positions of\n", "anchor boxes in the feature map.\n", "\n", "Since centers of the anchor boxes (`anchors`)\n", "are spread over all units on the feature map (`fmap`),\n", "these centers must be *uniformly* distributed\n", "on any input image\n", "in terms of their relative spatial positions.\n", "More concretely,\n", "given the width and height of the feature map `fmap_w` and `fmap_h`, respectively,\n", "the following function will *uniformly* sample\n", "pixels in `fmap_h` rows and `fmap_w` columns\n", "on any input image.\n", "Centered on these uniformly sampled pixels,\n", "anchor boxes of scale `s` (assuming the length of the list `s` is 1) and different aspect ratios (`ratios`)\n", "will be generated.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "a23f0931", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:40.315151Z", "iopub.status.busy": "2023-08-18T19:43:40.314382Z", "iopub.status.idle": "2023-08-18T19:43:40.320222Z", "shell.execute_reply": "2023-08-18T19:43:40.319395Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [], "source": ["def display_anchors(fmap_w, fmap_h, s):\n", "    d2l.set_figsize()\n", "    # Values on the first two dimensions do not affect the output\n", "    fmap = torch.zeros((1, 10, fmap_h, fmap_w))\n", "    anchors = d2l.multibox_prior(fmap, sizes=s, ratios=[1, 2, 0.5])\n", "    bbox_scale = torch.tensor((w, h, w, h))\n", "    d2l.show_bboxes(d2l.plt.imshow(img).axes,\n", "                    anchors[0] * bbox_scale)"]}, {"cell_type": "markdown", "id": "f653ee9d", "metadata": {"origin_pos": 6}, "source": ["First, let's [**consider\n", "detection of small objects**].\n", "In order to make it easier to distinguish when displayed, the anchor boxes with different centers here do not overlap:\n", "the anchor box scale is set to 0.15\n", "and the height and width of the feature map are set to 4. We can see\n", "that the centers of the anchor boxes in 4 rows and 4 columns on the image are uniformly distributed.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f2f5c6bf", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:40.323884Z", "iopub.status.busy": "2023-08-18T19:43:40.323202Z", "iopub.status.idle": "2023-08-18T19:43:40.724999Z", "shell.execute_reply": "2023-08-18T19:43:40.724110Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"220.346324pt\" height=\"173.353814pt\" viewBox=\"0 0 220.**********.353814\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:43:40.635662</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 173.353814 \n", "L 220.**********.353814 \n", "L 220.346324 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 149.475689 \n", "L 213.**********.475689 \n", "L 213.146324 10.875689 \n", "L 33.2875 10.875689 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p142190c3c1)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageda838d0b7c\" transform=\"scale(1 -1) translate(0 -138.96)\" x=\"33.2875\" y=\"-10.515689\" width=\"180\" height=\"138.96\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.40397 17.929219 \n", "L 69.382796 17.929219 \n", "L 69.382796 38.719222 \n", "L 42.40397 38.719222 \n", "L 42.40397 17.929219 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 36.816473 20.973844 \n", "L 74.970292 20.973844 \n", "L 74.970292 35.674594 \n", "L 36.816473 35.674594 \n", "L 36.816473 20.973844 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 46.354927 13.623469 \n", "L 65.431837 13.623469 \n", "L 65.431837 43.024969 \n", "L 46.354927 43.024969 \n", "L 46.354927 13.623469 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 87.368679 17.929219 \n", "L 114.347502 17.929219 \n", "L 114.347502 38.719222 \n", "L 87.368679 38.719222 \n", "L 87.368679 17.929219 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 81.781178 20.973844 \n", "L 119.934998 20.973844 \n", "L 119.934998 35.674594 \n", "L 81.781178 35.674594 \n", "L 81.781178 20.973844 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 91.319637 13.623469 \n", "L 110.396543 13.623469 \n", "L 110.396543 43.024969 \n", "L 91.319637 43.024969 \n", "L 91.319637 13.623469 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 132.333381 17.929219 \n", "L 159.312207 17.929219 \n", "L 159.312207 38.719222 \n", "L 132.333381 38.719222 \n", "L 132.333381 17.929219 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 126.745892 20.973844 \n", "L 164.899704 20.973844 \n", "L 164.899704 35.674594 \n", "L 126.745892 35.674594 \n", "L 126.745892 20.973844 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 136.284339 13.623469 \n", "L 155.361249 13.623469 \n", "L 155.361249 43.024969 \n", "L 136.284339 43.024969 \n", "L 136.284339 13.623469 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 177.298094 17.929219 \n", "L 204.276906 17.929219 \n", "L 204.276906 38.719222 \n", "L 177.298094 38.719222 \n", "L 177.298094 17.929219 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 171.71059 20.973844 \n", "L 209.86441 20.973844 \n", "L 209.86441 35.674594 \n", "L 171.71059 35.674594 \n", "L 171.71059 20.973844 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 181.249045 13.623469 \n", "L 200.325955 13.623469 \n", "L 200.325955 43.024969 \n", "L 181.249045 43.024969 \n", "L 181.249045 13.623469 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 42.40397 52.57922 \n", "L 69.382796 52.57922 \n", "L 69.382796 73.369218 \n", "L 42.40397 73.369218 \n", "L 42.40397 52.57922 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 36.816473 55.623846 \n", "L 74.970292 55.623846 \n", "L 74.970292 70.324592 \n", "L 36.816473 70.324592 \n", "L 36.816473 55.623846 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 46.354927 48.273469 \n", "L 65.431837 48.273469 \n", "L 65.431837 77.674965 \n", "L 46.354927 77.674965 \n", "L 46.354927 48.273469 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 87.368679 52.57922 \n", "L 114.347502 52.57922 \n", "L 114.347502 73.369218 \n", "L 87.368679 73.369218 \n", "L 87.368679 52.57922 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 81.781178 55.623846 \n", "L 119.934998 55.623846 \n", "L 119.934998 70.324592 \n", "L 81.781178 70.324592 \n", "L 81.781178 55.623846 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 91.319637 48.273469 \n", "L 110.396543 48.273469 \n", "L 110.396543 77.674965 \n", "L 91.319637 77.674965 \n", "L 91.319637 48.273469 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 132.333381 52.57922 \n", "L 159.312207 52.57922 \n", "L 159.312207 73.369218 \n", "L 132.333381 73.369218 \n", "L 132.333381 52.57922 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 126.745892 55.623846 \n", "L 164.899704 55.623846 \n", "L 164.899704 70.324592 \n", "L 126.745892 70.324592 \n", "L 126.745892 55.623846 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 136.284339 48.273469 \n", "L 155.361249 48.273469 \n", "L 155.361249 77.674965 \n", "L 136.284339 77.674965 \n", "L 136.284339 48.273469 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 177.298094 52.57922 \n", "L 204.276906 52.57922 \n", "L 204.276906 73.369218 \n", "L 177.298094 73.369218 \n", "L 177.298094 52.57922 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 171.71059 55.623846 \n", "L 209.86441 55.623846 \n", "L 209.86441 70.324592 \n", "L 171.71059 70.324592 \n", "L 171.71059 55.623846 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 181.249045 48.273469 \n", "L 200.325955 48.273469 \n", "L 200.325955 77.674965 \n", "L 181.249045 77.674965 \n", "L 181.249045 48.273469 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 42.40397 87.229223 \n", "L 69.382796 87.229223 \n", "L 69.382796 108.019214 \n", "L 42.40397 108.019214 \n", "L 42.40397 87.229223 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 36.816473 90.273849 \n", "L 74.970292 90.273849 \n", "L 74.970292 104.974588 \n", "L 36.816473 104.974588 \n", "L 36.816473 90.273849 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 46.354927 82.923472 \n", "L 65.431837 82.923472 \n", "L 65.431837 112.324965 \n", "L 46.354927 112.324965 \n", "L 46.354927 82.923472 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 87.368679 87.229223 \n", "L 114.347502 87.229223 \n", "L 114.347502 108.019214 \n", "L 87.368679 108.019214 \n", "L 87.368679 87.229223 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 81.781178 90.273849 \n", "L 119.934998 90.273849 \n", "L 119.934998 104.974588 \n", "L 81.781178 104.974588 \n", "L 81.781178 90.273849 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 91.319637 82.923472 \n", "L 110.396543 82.923472 \n", "L 110.396543 112.324965 \n", "L 91.319637 112.324965 \n", "L 91.319637 82.923472 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 132.333381 87.229223 \n", "L 159.312207 87.229223 \n", "L 159.312207 108.019214 \n", "L 132.333381 108.019214 \n", "L 132.333381 87.229223 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 126.745892 90.273849 \n", "L 164.899704 90.273849 \n", "L 164.899704 104.974588 \n", "L 126.745892 104.974588 \n", "L 126.745892 90.273849 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 136.284339 82.923472 \n", "L 155.361249 82.923472 \n", "L 155.361249 112.324965 \n", "L 136.284339 112.324965 \n", "L 136.284339 82.923472 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 177.298094 87.229223 \n", "L 204.276906 87.229223 \n", "L 204.276906 108.019214 \n", "L 177.298094 108.019214 \n", "L 177.298094 87.229223 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 171.71059 90.273849 \n", "L 209.86441 90.273849 \n", "L 209.86441 104.974588 \n", "L 171.71059 104.974588 \n", "L 171.71059 90.273849 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 181.249045 82.923472 \n", "L 200.325955 82.923472 \n", "L 200.325955 112.324965 \n", "L 181.249045 112.324965 \n", "L 181.249045 82.923472 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 42.40397 121.879223 \n", "L 69.382796 121.879223 \n", "L 69.382796 142.669222 \n", "L 42.40397 142.669222 \n", "L 42.40397 121.879223 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 36.816473 124.923849 \n", "L 74.970292 124.923849 \n", "L 74.970292 139.624596 \n", "L 36.816473 139.624596 \n", "L 36.816473 124.923849 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 46.354927 117.573472 \n", "L 65.431837 117.573472 \n", "L 65.431837 146.974958 \n", "L 46.354927 146.974958 \n", "L 46.354927 117.573472 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 87.368679 121.879223 \n", "L 114.347502 121.879223 \n", "L 114.347502 142.669222 \n", "L 87.368679 142.669222 \n", "L 87.368679 121.879223 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 81.781178 124.923849 \n", "L 119.934998 124.923849 \n", "L 119.934998 139.624596 \n", "L 81.781178 139.624596 \n", "L 81.781178 124.923849 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 91.319637 117.573472 \n", "L 110.396543 117.573472 \n", "L 110.396543 146.974958 \n", "L 91.319637 146.974958 \n", "L 91.319637 117.573472 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_45\">\n", "    <path d=\"M 132.333381 121.879223 \n", "L 159.312207 121.879223 \n", "L 159.312207 142.669222 \n", "L 132.333381 142.669222 \n", "L 132.333381 121.879223 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_46\">\n", "    <path d=\"M 126.745892 124.923849 \n", "L 164.899704 124.923849 \n", "L 164.899704 139.624596 \n", "L 126.745892 139.624596 \n", "L 126.745892 124.923849 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_47\">\n", "    <path d=\"M 136.284339 117.573472 \n", "L 155.361249 117.573472 \n", "L 155.361249 146.974958 \n", "L 136.284339 146.974958 \n", "L 136.284339 117.573472 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_48\">\n", "    <path d=\"M 177.298094 121.879223 \n", "L 204.276906 121.879223 \n", "L 204.276906 142.669222 \n", "L 177.298094 142.669222 \n", "L 177.298094 121.879223 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_49\">\n", "    <path d=\"M 171.71059 124.923849 \n", "L 209.86441 124.923849 \n", "L 209.86441 139.624596 \n", "L 171.71059 139.624596 \n", "L 171.71059 124.923849 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_50\">\n", "    <path d=\"M 181.249045 117.573472 \n", "L 200.325955 117.573472 \n", "L 200.325955 146.974958 \n", "L 181.249045 146.974958 \n", "L 181.249045 117.573472 \n", "z\n", "\" clip-path=\"url(#p142190c3c1)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m5a5af7bc8e\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5a5af7bc8e\" x=\"33.411029\" y=\"149.475689\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(30.229779 164.074127) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m5a5af7bc8e\" x=\"82.822794\" y=\"149.475689\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(73.279044 164.074127) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m5a5af7bc8e\" x=\"132.234559\" y=\"149.475689\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(122.690809 164.074127) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m5a5af7bc8e\" x=\"181.646324\" y=\"149.475689\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(172.102574 164.074127) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"mca36cbcd3e\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mca36cbcd3e\" x=\"33.2875\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mca36cbcd3e\" x=\"33.2875\" y=\"35.705101\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 39.50432) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mca36cbcd3e\" x=\"33.2875\" y=\"60.410983\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(7.2 64.210202) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mca36cbcd3e\" x=\"33.2875\" y=\"85.116866\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(7.2 88.916085) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mca36cbcd3e\" x=\"33.2875\" y=\"109.822748\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(7.2 113.621967) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mca36cbcd3e\" x=\"33.2875\" y=\"134.528631\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(7.2 138.327849) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_51\">\n", "    <path d=\"M 33.2875 149.475689 \n", "L 33.2875 10.875689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_52\">\n", "    <path d=\"M 213.**********.475689 \n", "L 213.146324 10.875689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_53\">\n", "    <path d=\"M 33.2875 149.475689 \n", "L 213.**********.475689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_54\">\n", "    <path d=\"M 33.2875 10.875689 \n", "L 213.146324 10.875689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p142190c3c1\">\n", "   <rect x=\"33.2875\" y=\"10.875689\" width=\"179.858824\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_anchors(fmap_w=4, fmap_h=4, s=[0.15])"]}, {"cell_type": "markdown", "id": "48e1e28a", "metadata": {"origin_pos": 8}, "source": ["We move on to [**reduce the height and width of the feature map by half and use larger anchor boxes to detect larger objects**]. When the scale is set to 0.4, \n", "some anchor boxes will overlap with each other.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "ac73661b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:40.732529Z", "iopub.status.busy": "2023-08-18T19:43:40.731744Z", "iopub.status.idle": "2023-08-18T19:43:41.016547Z", "shell.execute_reply": "2023-08-18T19:43:41.015632Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"220.346324pt\" height=\"173.353814pt\" viewBox=\"0 0 220.**********.353814\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:43:40.940473</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 173.353814 \n", "L 220.**********.353814 \n", "L 220.346324 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 149.475689 \n", "L 213.**********.475689 \n", "L 213.146324 10.875689 \n", "L 33.2875 10.875689 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pbf8dc04bf3)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image3110b16158\" transform=\"scale(1 -1) translate(0 -138.96)\" x=\"33.2875\" y=\"-10.515689\" width=\"180\" height=\"138.96\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 42.40397 17.929219 \n", "L 114.347502 17.929219 \n", "L 114.347502 73.369218 \n", "L 42.40397 73.369218 \n", "L 42.40397 17.929219 \n", "z\n", "\" clip-path=\"url(#pbf8dc04bf3)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 27.503975 26.048218 \n", "L 129.247498 26.048218 \n", "L 129.247498 65.25022 \n", "L 27.503975 65.25022 \n", "L 27.503975 26.048218 \n", "z\n", "\" clip-path=\"url(#pbf8dc04bf3)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.939856 6.447217 \n", "L 103.81162 6.447217 \n", "L 103.81162 84.851222 \n", "L 52.939856 84.851222 \n", "L 52.939856 6.447217 \n", "z\n", "\" clip-path=\"url(#pbf8dc04bf3)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 132.333381 17.929219 \n", "L 204.276906 17.929219 \n", "L 204.276906 73.369218 \n", "L 132.333381 73.369218 \n", "L 132.333381 17.929219 \n", "z\n", "\" clip-path=\"url(#pbf8dc04bf3)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 117.433384 26.048218 \n", "L 219.176917 26.048218 \n", "L 219.176917 65.25022 \n", "L 117.433384 65.25022 \n", "L 117.433384 26.048218 \n", "z\n", "\" clip-path=\"url(#pbf8dc04bf3)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 142.869262 6.447217 \n", "L 193.741032 6.447217 \n", "L 193.741032 84.851222 \n", "L 142.869262 84.851222 \n", "L 142.869262 6.447217 \n", "z\n", "\" clip-path=\"url(#pbf8dc04bf3)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 42.40397 87.229223 \n", "L 114.347502 87.229223 \n", "L 114.347502 142.669222 \n", "L 42.40397 142.669222 \n", "L 42.40397 87.229223 \n", "z\n", "\" clip-path=\"url(#pbf8dc04bf3)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 27.503975 95.348213 \n", "L 129.247498 95.348213 \n", "L 129.247498 134.550224 \n", "L 27.503975 134.550224 \n", "L 27.503975 95.348213 \n", "z\n", "\" clip-path=\"url(#pbf8dc04bf3)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 52.939856 75.747216 \n", "L 103.81162 75.747216 \n", "L 103.81162 154.151229 \n", "L 52.939856 154.151229 \n", "L 52.939856 75.747216 \n", "z\n", "\" clip-path=\"url(#pbf8dc04bf3)\" style=\"fill: none; stroke: #bf00bf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 132.333381 87.229223 \n", "L 204.276906 87.229223 \n", "L 204.276906 142.669222 \n", "L 132.333381 142.669222 \n", "L 132.333381 87.229223 \n", "z\n", "\" clip-path=\"url(#pbf8dc04bf3)\" style=\"fill: none; stroke: #00bfbf; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 117.433384 95.348213 \n", "L 219.176917 95.348213 \n", "L 219.176917 134.550224 \n", "L 117.433384 134.550224 \n", "L 117.433384 95.348213 \n", "z\n", "\" clip-path=\"url(#pbf8dc04bf3)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 142.869262 75.747216 \n", "L 193.741032 75.747216 \n", "L 193.741032 154.151229 \n", "L 142.869262 154.151229 \n", "L 142.869262 75.747216 \n", "z\n", "\" clip-path=\"url(#pbf8dc04bf3)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m85a9b7d1d3\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m85a9b7d1d3\" x=\"33.411029\" y=\"149.475689\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(30.229779 164.074127) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m85a9b7d1d3\" x=\"82.822794\" y=\"149.475689\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(73.279044 164.074127) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m85a9b7d1d3\" x=\"132.234559\" y=\"149.475689\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(122.690809 164.074127) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m85a9b7d1d3\" x=\"181.646324\" y=\"149.475689\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(172.102574 164.074127) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"md1549c5da5\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md1549c5da5\" x=\"33.2875\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#md1549c5da5\" x=\"33.2875\" y=\"35.705101\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 39.50432) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#md1549c5da5\" x=\"33.2875\" y=\"60.410983\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(7.2 64.210202) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#md1549c5da5\" x=\"33.2875\" y=\"85.116866\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(7.2 88.916085) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#md1549c5da5\" x=\"33.2875\" y=\"109.822748\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(7.2 113.621967) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#md1549c5da5\" x=\"33.2875\" y=\"134.528631\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(7.2 138.327849) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 33.2875 149.475689 \n", "L 33.2875 10.875689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 213.**********.475689 \n", "L 213.146324 10.875689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 33.2875 149.475689 \n", "L 213.**********.475689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 33.2875 10.875689 \n", "L 213.146324 10.875689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pbf8dc04bf3\">\n", "   <rect x=\"33.2875\" y=\"10.875689\" width=\"179.858824\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_anchors(fmap_w=2, fmap_h=2, s=[0.4])"]}, {"cell_type": "markdown", "id": "7debbaec", "metadata": {"origin_pos": 10}, "source": ["Finally, we [**further reduce the height and width of the feature map by half and increase the anchor box scale to 0.8**]. Now the center of the anchor box is the center of the image.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "d3c50b52", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:43:41.021696Z", "iopub.status.busy": "2023-08-18T19:43:41.021086Z", "iopub.status.idle": "2023-08-18T19:43:41.419436Z", "shell.execute_reply": "2023-08-18T19:43:41.418206Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"220.346324pt\" height=\"173.353814pt\" viewBox=\"0 0 220.**********.353814\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:43:41.339023</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 173.353814 \n", "L 220.**********.353814 \n", "L 220.346324 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 149.475689 \n", "L 213.**********.475689 \n", "L 213.146324 10.875689 \n", "L 33.2875 10.875689 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p1d03e2ae77)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagea4c6322229\" transform=\"scale(1 -1) translate(0 -138.96)\" x=\"33.2875\" y=\"-10.515689\" width=\"180\" height=\"138.96\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 51.396911 24.859218 \n", "L 195.283974 24.859218 \n", "L 195.283974 135.739217 \n", "L 51.396911 135.739217 \n", "L 51.396911 24.859218 \n", "z\n", "\" clip-path=\"url(#p1d03e2ae77)\" style=\"fill: none; stroke: #0000ff; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 21.596922 41.097218 \n", "L 221.346324 41.097218 \n", "M 221.346324 119.501222 \n", "L 21.596922 119.501222 \n", "L 21.596922 41.097218 \n", "\" clip-path=\"url(#p1d03e2ae77)\" style=\"fill: none; stroke: #008000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 72.468682 1.895216 \n", "L 174.212211 1.895216 \n", "L 174.212211 158.703225 \n", "L 72.468682 158.703225 \n", "L 72.468682 1.895216 \n", "z\n", "\" clip-path=\"url(#p1d03e2ae77)\" style=\"fill: none; stroke: #ff0000; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m100c7b4880\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m100c7b4880\" x=\"33.411029\" y=\"149.475689\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(30.229779 164.074127) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m100c7b4880\" x=\"82.822794\" y=\"149.475689\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(73.279044 164.074127) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m100c7b4880\" x=\"132.234559\" y=\"149.475689\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(122.690809 164.074127) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m100c7b4880\" x=\"181.646324\" y=\"149.475689\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(172.102574 164.074127) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"mbcefc5af8d\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mbcefc5af8d\" x=\"33.2875\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mbcefc5af8d\" x=\"33.2875\" y=\"35.705101\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 39.50432) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mbcefc5af8d\" x=\"33.2875\" y=\"60.410983\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(7.2 64.210202) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mbcefc5af8d\" x=\"33.2875\" y=\"85.116866\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(7.2 88.916085) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mbcefc5af8d\" x=\"33.2875\" y=\"109.822748\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(7.2 113.621967) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mbcefc5af8d\" x=\"33.2875\" y=\"134.528631\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(7.2 138.327849) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 33.2875 149.475689 \n", "L 33.2875 10.875689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 213.**********.475689 \n", "L 213.146324 10.875689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 33.2875 149.475689 \n", "L 213.**********.475689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 33.2875 10.875689 \n", "L 213.146324 10.875689 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p1d03e2ae77\">\n", "   <rect x=\"33.2875\" y=\"10.875689\" width=\"179.858824\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_anchors(fmap_w=1, fmap_h=1, s=[0.8])"]}, {"cell_type": "markdown", "id": "ab218969", "metadata": {"origin_pos": 12}, "source": ["## Multiscale Detection\n", "\n", "\n", "Since we have generated multiscale anchor boxes,\n", "we will use them to detect objects of various sizes\n", "at different scales.\n", "In the following\n", "we introduce a CNN-based multiscale object detection\n", "method that we will implement\n", "in :numref:`sec_ssd`.\n", "\n", "At some scale,\n", "say that we have $c$ feature maps of shape $h \\times w$.\n", "Using the method in :numref:`subsec_multiscale-anchor-boxes`,\n", "we generate $hw$ sets of anchor boxes,\n", "where each set has $a$ anchor boxes with the same center.\n", "For example, \n", "at the first scale in the experiments in :numref:`subsec_multiscale-anchor-boxes`,\n", "given ten (number of channels) $4 \\times 4$ feature maps,\n", "we generated 16 sets of anchor boxes,\n", "where each set contains 3 anchor boxes with the same center.\n", "Next, each anchor box is labeled with\n", "the class and offset based on ground-truth bounding boxes. At the current scale, the object detection model needs to predict the classes and offsets of $hw$ sets of anchor boxes on the input image, where different sets have different centers.\n", "\n", "\n", "Assume that the $c$ feature maps here\n", "are the intermediate outputs obtained\n", "by the CNN forward propagation based on the input image. Since there are $hw$ different spatial positions on each feature map,\n", "the same spatial position can be \n", "thought of as having $c$ units.\n", "According to the\n", "definition of receptive field in :numref:`sec_conv_layer`,\n", "these $c$ units at the same spatial position\n", "of the feature maps\n", "have the same receptive field on the input image:\n", "they represent the input image information\n", "in the same receptive field.\n", "Therefore, we can transform the $c$ units\n", "of the feature maps at the same spatial position\n", "into the\n", "classes and offsets of the $a$ anchor boxes\n", "generated using this spatial position.\n", "In essence,\n", "we use the information of the input image in a certain receptive field\n", "to predict the classes and offsets of the anchor boxes\n", "that are\n", "close to that receptive field\n", "on the input image.\n", "\n", "\n", "When the feature maps at different layers\n", "have varying-size receptive fields on the input image, they can be used to detect objects of different sizes.\n", "For example, we can design a neural network where\n", "units of feature maps that are closer to the output layer\n", "have wider receptive fields,\n", "so they can detect larger objects from the input image.\n", "\n", "In a nutshell, we can leverage\n", "layerwise representations of images at multiple levels\n", "by deep neural networks\n", "for multiscale object detection.\n", "We will show how this works through a concrete example\n", "in :numref:`sec_ssd`.\n", "\n", "\n", "\n", "\n", "## Summary\n", "\n", "* At multiple scales, we can generate anchor boxes with different sizes to detect objects with different sizes.\n", "* By defining the shape of feature maps, we can determine centers of uniformly sampled anchor boxes on any image.\n", "* We use the information of the input image in a certain receptive field to predict the classes and offsets of the anchor boxes that are close to that receptive field on the input image.\n", "* Through deep learning, we can leverage its layerwise representations of images at multiple levels for multiscale object detection.\n", "\n", "\n", "## Exercises\n", "\n", "1. According to our discussions in :numref:`sec_alexnet`, deep neural networks learn hierarchical features with increasing levels of abstraction for images. In multiscale object detection, do feature maps at different scales correspond to different levels of abstraction? Why or why not?\n", "1. At the first scale (`fmap_w=4, fmap_h=4`) in the experiments in :numref:`subsec_multiscale-anchor-boxes`, generate uniformly distributed anchor boxes that may overlap.\n", "1. Given a feature map variable with shape $1 \\times c \\times h \\times w$, where $c$, $h$, and $w$ are the number of channels, height, and width of the feature maps, respectively. How can you transform this variable into the classes and offsets of anchor boxes? What is the shape of the output?\n"]}, {"cell_type": "markdown", "id": "546f146c", "metadata": {"origin_pos": 14, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1607)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
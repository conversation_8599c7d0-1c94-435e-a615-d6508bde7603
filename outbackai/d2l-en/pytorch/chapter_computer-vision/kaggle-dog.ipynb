{"cells": [{"cell_type": "markdown", "id": "89356571", "metadata": {"origin_pos": 0}, "source": ["# Dog Breed Identification (ImageNet Dogs) on Kaggle\n", "\n", "In this section, we will practice\n", "the dog breed identification problem on\n", "Kaggle. (**The web address of this competition is https://www.kaggle.com/c/dog-breed-identification**)\n", "\n", "In this competition,\n", "120 different breeds of dogs will be recognized.\n", "In fact,\n", "the dataset for this competition is\n", "a subset of the ImageNet dataset.\n", "Unlike the images in the CIFAR-10 dataset in :numref:`sec_kaggle_cifar10`,\n", "the images in the ImageNet dataset are both higher and wider in varying dimensions.\n", ":numref:`fig_kaggle_dog` shows the information on the competition's webpage. You need a Kaggle account\n", "to submit your results.\n", "\n", "\n", "![The dog breed identification competition website. The competition dataset can be obtained by clicking the \"Data\" tab.](../img/kaggle-dog.jpg)\n", ":width:`400px`\n", ":label:`fig_kaggle_dog`\n"]}, {"cell_type": "code", "execution_count": 1, "id": "522dbb60", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:07.445133Z", "iopub.status.busy": "2023-08-18T19:37:07.444735Z", "iopub.status.idle": "2023-08-18T19:37:10.912341Z", "shell.execute_reply": "2023-08-18T19:37:10.911410Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import os\n", "import torch\n", "import torchvision\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "3236bc52", "metadata": {"origin_pos": 3}, "source": ["## Obtaining and Organizing the Dataset\n", "\n", "The competition dataset is divided into a training set and a test set, which contain 10222 and 10357 JPEG images\n", "of three RGB (color) channels, respectively.\n", "Among the training dataset,\n", "there are 120 breeds of dogs\n", "such as Labradors, Poodles, Dachshunds, Samoyeds, Huskies, Chihuahuas, and Yorkshire Terriers.\n", "\n", "\n", "### Downloading the Dataset\n", "\n", "After logging into Kaggle,\n", "you can click on the \"Data\" tab on the\n", "competition webpage shown in :numref:`fig_kaggle_dog` and download the dataset by clicking the \"Download All\" button.\n", "After unzipping the downloaded file in `../data`, you will find the entire dataset in the following paths:\n", "\n", "* ../data/dog-breed-identification/labels.csv\n", "* ../data/dog-breed-identification/sample_submission.csv\n", "* ../data/dog-breed-identification/train\n", "* ../data/dog-breed-identification/test\n", "\n", "You may have noticed that the above structure is\n", "similar to that of the CIFAR-10 competition in :numref:`sec_kaggle_cifar10`, where folders `train/` and `test/` contain training and testing dog images, respectively, and `labels.csv` contains\n", "the labels for the training images.\n", "Similarly, to make it easier to get started, [**we provide a small sample of the dataset**] mentioned above: `train_valid_test_tiny.zip`.\n", "If you are going to use the full dataset for the Kaggle competition, you need to change the `demo` variable below to `False`.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "3410e2ed", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:10.916623Z", "iopub.status.busy": "2023-08-18T19:37:10.915891Z", "iopub.status.idle": "2023-08-18T19:37:12.042530Z", "shell.execute_reply": "2023-08-18T19:37:12.041410Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/kaggle_dog_tiny.zip from http://d2l-data.s3-accelerate.amazonaws.com/kaggle_dog_tiny.zip...\n"]}], "source": ["#@save\n", "d2l.DATA_HUB['dog_tiny'] = (d2l.DATA_URL + 'kaggle_dog_tiny.zip',\n", "                            '0cb91d09b814ecdc07b50f31f8dcad3e81d6a86d')\n", "\n", "# If you use the full dataset downloaded for the Kaggle competition, change\n", "# the variable below to `False`\n", "demo = True\n", "if demo:\n", "    data_dir = d2l.download_extract('dog_tiny')\n", "else:\n", "    data_dir = os.path.join('..', 'data', 'dog-breed-identification')"]}, {"cell_type": "markdown", "id": "dc030ecd", "metadata": {"origin_pos": 5}, "source": ["### [**Organizing the Dataset**]\n", "\n", "We can organize the dataset similarly to what we did in :numref:`sec_kaggle_cifar10`, namely splitting out\n", "a validation set from the original training set, and moving images into subfolders grouped by labels.\n", "\n", "The `reorg_dog_data` function below reads\n", "the training data labels, splits out the validation set, and organizes the training set.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "12a7827b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:12.048428Z", "iopub.status.busy": "2023-08-18T19:37:12.046704Z", "iopub.status.idle": "2023-08-18T19:37:12.439141Z", "shell.execute_reply": "2023-08-18T19:37:12.438146Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [], "source": ["def reorg_dog_data(data_dir, valid_ratio):\n", "    labels = d2l.read_csv_labels(os.path.join(data_dir, 'labels.csv'))\n", "    d2l.reorg_train_valid(data_dir, labels, valid_ratio)\n", "    d2l.reorg_test(data_dir)\n", "\n", "\n", "batch_size = 32 if demo else 128\n", "valid_ratio = 0.1\n", "reorg_dog_data(data_dir, valid_ratio)"]}, {"cell_type": "markdown", "id": "5749b6c7", "metadata": {"origin_pos": 7}, "source": ["## [**Image Augmentation**]\n", "\n", "Recall that this dog breed dataset\n", "is a subset of the ImageNet dataset,\n", "whose images\n", "are larger than those of the CIFAR-10 dataset\n", "in :numref:`sec_kaggle_cifar10`.\n", "The following\n", "lists a few image augmentation operations\n", "that might be useful for relatively larger images.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "da028e84", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:12.444078Z", "iopub.status.busy": "2023-08-18T19:37:12.443719Z", "iopub.status.idle": "2023-08-18T19:37:12.450590Z", "shell.execute_reply": "2023-08-18T19:37:12.449525Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["transform_train = torchvision.transforms.Compose([\n", "    # Randomly crop the image to obtain an image with an area of 0.08 to 1 of\n", "    # the original area and height-to-width ratio between 3/4 and 4/3. Then,\n", "    # scale the image to create a new 224 x 224 image\n", "    torchvision.transforms.RandomResizedCrop(224, scale=(0.08, 1.0),\n", "                                             ratio=(3.0/4.0, 4.0/3.0)),\n", "    torchvision.transforms.RandomHorizontalFlip(),\n", "    # Randomly change the brightness, contrast, and saturation\n", "    torchvision.transforms.ColorJitter(brightness=0.4,\n", "                                       contrast=0.4,\n", "                                       saturation=0.4),\n", "    # Add random noise\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    # Standardize each channel of the image\n", "    torchvision.transforms.Normalize([0.485, 0.456, 0.406],\n", "                                     [0.229, 0.224, 0.225])])"]}, {"cell_type": "markdown", "id": "ef89c6cb", "metadata": {"origin_pos": 10}, "source": ["During prediction,\n", "we only use image preprocessing operations\n", "without randomness.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "014f4992", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:12.454305Z", "iopub.status.busy": "2023-08-18T19:37:12.454005Z", "iopub.status.idle": "2023-08-18T19:37:12.459587Z", "shell.execute_reply": "2023-08-18T19:37:12.458771Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["transform_test = torchvision.transforms.Compose([\n", "    torchvision.<PERSON>.<PERSON><PERSON><PERSON>(256),\n", "    # Crop a 224 x 224 square area from the center of the image\n", "    torchvision.transforms.CenterCrop(224),\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    torchvision.transforms.Normalize([0.485, 0.456, 0.406],\n", "                                     [0.229, 0.224, 0.225])])"]}, {"cell_type": "markdown", "id": "9dcc3c05", "metadata": {"origin_pos": 13}, "source": ["## [**Reading the Dataset**]\n", "\n", "As in :numref:`sec_kaggle_cifar10`,\n", "we can read the organized dataset\n", "consisting of raw image files.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "3f91960b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:12.464061Z", "iopub.status.busy": "2023-08-18T19:37:12.463218Z", "iopub.status.idle": "2023-08-18T19:37:12.492441Z", "shell.execute_reply": "2023-08-18T19:37:12.491530Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["train_ds, train_valid_ds = [torchvision.datasets.ImageFolder(\n", "    os.path.join(data_dir, 'train_valid_test', folder),\n", "    transform=transform_train) for folder in ['train', 'train_valid']]\n", "\n", "valid_ds, test_ds = [torchvision.datasets.ImageFolder(\n", "    os.path.join(data_dir, 'train_valid_test', folder),\n", "    transform=transform_test) for folder in ['valid', 'test']]"]}, {"cell_type": "markdown", "id": "d3a71b4e", "metadata": {"origin_pos": 16}, "source": ["Below we create data iterator instances\n", "the same way\n", "as in :numref:`sec_kaggle_cifar10`.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "dc700919", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:12.495969Z", "iopub.status.busy": "2023-08-18T19:37:12.495419Z", "iopub.status.idle": "2023-08-18T19:37:12.505734Z", "shell.execute_reply": "2023-08-18T19:37:12.501006Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [], "source": ["train_iter, train_valid_iter = [torch.utils.data.DataLoader(\n", "    dataset, batch_size, shuffle=True, drop_last=True)\n", "    for dataset in (train_ds, train_valid_ds)]\n", "\n", "valid_iter = torch.utils.data.DataLoader(valid_ds, batch_size, shuffle=False,\n", "                                         drop_last=True)\n", "\n", "test_iter = torch.utils.data.DataLoader(test_ds, batch_size, shuffle=False,\n", "                                        drop_last=False)"]}, {"cell_type": "markdown", "id": "0701b62c", "metadata": {"origin_pos": 19}, "source": ["## [**Fine-Tuning a Pretrained Model**]\n", "\n", "Again,\n", "the dataset for this competition is a subset of the ImageNet dataset.\n", "Therefore, we can use the approach discussed in\n", ":numref:`sec_fine_tuning`\n", "to select a model pretrained on the\n", "full ImageNet dataset and use it to extract image features to be fed into a\n", "custom small-scale output network.\n", "High-level APIs of deep learning frameworks\n", "provide a wide range of models\n", "pretrained on the ImageNet dataset.\n", "Here, we choose\n", "a pretrained ResNet-34 model,\n", "where we simply reuse\n", "the input of this model's output layer\n", "(i.e., the extracted\n", "features).\n", "Then we can replace the original output layer with a small custom\n", "output network that can be trained,\n", "such as stacking two\n", "fully connected layers.\n", "Different from the experiment in\n", ":numref:`sec_fine_tuning`,\n", "the following does\n", "not retrain the pretrained model used for feature\n", "extraction. This reduces training time and\n", "memory for storing gradients.\n", "\n", "Recall that we\n", "standardized images using\n", "the means and standard deviations of the three RGB channels for the full ImageNet dataset.\n", "In fact,\n", "this is also consistent with the standardization operation\n", "by the pretrained model on ImageNet.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "61785088", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:12.511001Z", "iopub.status.busy": "2023-08-18T19:37:12.510321Z", "iopub.status.idle": "2023-08-18T19:37:12.519013Z", "shell.execute_reply": "2023-08-18T19:37:12.517929Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [], "source": ["def get_net(devices):\n", "    finetune_net = nn.Sequential()\n", "    finetune_net.features = torchvision.models.resnet34(pretrained=True)\n", "    # Define a new output network (there are 120 output categories)\n", "    finetune_net.output_new = nn.Sequential(nn.Linear(1000, 256),\n", "                                            nn.ReLU(),\n", "                                            nn.<PERSON><PERSON>(256, 120))\n", "    # Move the model to devices\n", "    finetune_net = finetune_net.to(devices[0])\n", "    # Freeze parameters of feature layers\n", "    for param in finetune_net.features.parameters():\n", "        param.requires_grad = False\n", "    return finetune_net"]}, {"cell_type": "markdown", "id": "f854ebef", "metadata": {"origin_pos": 22}, "source": ["Before [**calculating the loss**],\n", "we first obtain the input of the pretrained model's output layer, i.e., the extracted feature.\n", "Then we use this feature as input for our small custom output network to calculate the loss.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "88afcf21", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:12.525163Z", "iopub.status.busy": "2023-08-18T19:37:12.523035Z", "iopub.status.idle": "2023-08-18T19:37:12.531000Z", "shell.execute_reply": "2023-08-18T19:37:12.529963Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [], "source": ["loss = nn.CrossEntropyLoss(reduction='none')\n", "\n", "def evaluate_loss(data_iter, net, devices):\n", "    l_sum, n = 0.0, 0\n", "    for features, labels in data_iter:\n", "        features, labels = features.to(devices[0]), labels.to(devices[0])\n", "        outputs = net(features)\n", "        l = loss(outputs, labels)\n", "        l_sum += l.sum()\n", "        n += labels.numel()\n", "    return l_sum / n"]}, {"cell_type": "markdown", "id": "0b3d346f", "metadata": {"origin_pos": 25}, "source": ["## Defining [**the Training Function**]\n", "\n", "We will select the model and tune hyperparameters according to the model's performance on the validation set. The model training function `train` only\n", "iterates parameters of the small custom output network.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "a2fec7ed", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:12.535506Z", "iopub.status.busy": "2023-08-18T19:37:12.535169Z", "iopub.status.idle": "2023-08-18T19:37:12.548096Z", "shell.execute_reply": "2023-08-18T19:37:12.547177Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [], "source": ["def train(net, train_iter, valid_iter, num_epochs, lr, wd, devices, lr_period,\n", "          lr_decay):\n", "    # Only train the small custom output network\n", "    net = nn.DataParallel(net, device_ids=devices).to(devices[0])\n", "    trainer = torch.optim.SGD((param for param in net.parameters()\n", "                               if param.requires_grad), lr=lr,\n", "                              momentum=0.9, weight_decay=wd)\n", "    scheduler = torch.optim.lr_scheduler.Step<PERSON>(trainer, lr_period, lr_decay)\n", "    num_batches, timer = len(train_iter), d2l.Timer()\n", "    legend = ['train loss']\n", "    if valid_iter is not None:\n", "        legend.append('valid loss')\n", "    animator = d2l.Animator(xlabel='epoch', xlim=[1, num_epochs],\n", "                            legend=legend)\n", "    for epoch in range(num_epochs):\n", "        metric = d2l.Accumulator(2)\n", "        for i, (features, labels) in enumerate(train_iter):\n", "            timer.start()\n", "            features, labels = features.to(devices[0]), labels.to(devices[0])\n", "            trainer.zero_grad()\n", "            output = net(features)\n", "            l = loss(output, labels).sum()\n", "            l.backward()\n", "            trainer.step()\n", "            metric.add(l, labels.shape[0])\n", "            timer.stop()\n", "            if (i + 1) % (num_batches // 5) == 0 or i == num_batches - 1:\n", "                animator.add(epoch + (i + 1) / num_batches,\n", "                             (metric[0] / metric[1], None))\n", "        measures = f'train loss {metric[0] / metric[1]:.3f}'\n", "        if valid_iter is not None:\n", "            valid_loss = evaluate_loss(valid_iter, net, devices)\n", "            animator.add(epoch + 1, (None, valid_loss.detach().cpu()))\n", "        scheduler.step()\n", "    if valid_iter is not None:\n", "        measures += f', valid loss {valid_loss:.3f}'\n", "    print(measures + f'\\n{metric[1] * num_epochs / timer.sum():.1f}'\n", "          f' examples/sec on {str(devices)}')"]}, {"cell_type": "markdown", "id": "d77e6522", "metadata": {"origin_pos": 28}, "source": ["## [**Training and Validating the Model**]\n", "\n", "Now we can train and validate the model.\n", "The following hyperparameters are all tunable.\n", "For example, the number of epochs can be increased. Because `lr_period` and `lr_decay` are set to 2 and 0.9, respectively, the learning rate of the optimization algorithm will be multiplied by 0.9 after every 2 epochs.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "c06c3c5b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:37:12.552339Z", "iopub.status.busy": "2023-08-18T19:37:12.552051Z", "iopub.status.idle": "2023-08-18T19:39:35.191318Z", "shell.execute_reply": "2023-08-18T19:39:35.189837Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train loss 1.240, valid loss 1.545\n", "577.5 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"229.425pt\" height=\"185.508423pt\" viewBox=\"0 0 229.425 185.508423\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:39:35.140582</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 185.508423 \n", "L 229.425 185.508423 \n", "L 229.425 -0 \n", "L 0 -0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 20.5625 147.952173 \n", "L 215.8625 147.952173 \n", "L 215.8625 9.352173 \n", "L 20.5625 9.352173 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 42.2625 147.952173 \n", "L 42.2625 9.352173 \n", "\" clip-path=\"url(#paeeba9be02)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m533ec6d73d\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m533ec6d73d\" x=\"42.2625\" y=\"147.952173\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(39.08125 162.55061) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 85.6625 147.952173 \n", "L 85.6625 9.352173 \n", "\" clip-path=\"url(#paeeba9be02)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m533ec6d73d\" x=\"85.6625\" y=\"147.952173\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(82.48125 162.55061) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 129.0625 147.952173 \n", "L 129.0625 9.352173 \n", "\" clip-path=\"url(#paeeba9be02)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m533ec6d73d\" x=\"129.0625\" y=\"147.952173\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(125.88125 162.55061) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 172.4625 147.952173 \n", "L 172.4625 9.352173 \n", "\" clip-path=\"url(#paeeba9be02)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m533ec6d73d\" x=\"172.4625\" y=\"147.952173\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(169.28125 162.55061) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 215.8625 147.952173 \n", "L 215.8625 9.352173 \n", "\" clip-path=\"url(#paeeba9be02)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m533ec6d73d\" x=\"215.8625\" y=\"147.952173\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(209.5 162.55061) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(102.984375 176.228735) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 20.5625 140.24164 \n", "L 215.8625 140.24164 \n", "\" clip-path=\"url(#paeeba9be02)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mf7b0bd4025\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf7b0bd4025\" x=\"20.5625\" y=\"140.24164\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(7.2 144.040859) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 20.5625 107.931035 \n", "L 215.8625 107.931035 \n", "\" clip-path=\"url(#paeeba9be02)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mf7b0bd4025\" x=\"20.5625\" y=\"107.931035\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(7.2 111.730254) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 20.5625 75.62043 \n", "L 215.8625 75.62043 \n", "\" clip-path=\"url(#paeeba9be02)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mf7b0bd4025\" x=\"20.5625\" y=\"75.62043\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(7.2 79.419648) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 20.5625 43.309824 \n", "L 215.8625 43.309824 \n", "\" clip-path=\"url(#paeeba9be02)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mf7b0bd4025\" x=\"20.5625\" y=\"43.309824\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(7.2 47.109043) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 20.5625 10.999219 \n", "L 215.8625 10.999219 \n", "\" clip-path=\"url(#paeeba9be02)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mf7b0bd4025\" x=\"20.5625\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(7.2 14.798437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 2.881019 15.652173 \n", "L 6.899537 15.960757 \n", "L 10.918056 17.287027 \n", "L 14.936574 19.093154 \n", "L 18.955093 20.854639 \n", "L 20.5625 21.494152 \n", "L 24.581019 39.532891 \n", "L 28.599537 42.954629 \n", "L 32.618056 44.438919 \n", "L 36.636574 46.77079 \n", "L 40.655093 48.803834 \n", "L 42.2625 50.28708 \n", "L 46.281019 73.791358 \n", "L 50.299537 75.284037 \n", "L 54.318056 75.902591 \n", "L 58.336574 75.83296 \n", "L 62.355093 77.58725 \n", "L 63.9625 77.275433 \n", "L 67.981019 97.139882 \n", "L 71.999537 95.039977 \n", "L 76.018056 96.500923 \n", "L 80.036574 94.852441 \n", "L 84.055093 95.851825 \n", "L 85.6625 95.968662 \n", "L 89.681019 114.528264 \n", "L 93.699537 112.267334 \n", "L 97.718056 109.317171 \n", "L 101.736574 109.545384 \n", "L 105.755093 109.81734 \n", "L 107.3625 110.041389 \n", "L 111.381019 119.730497 \n", "L 115.399537 118.279108 \n", "L 119.418056 117.082201 \n", "L 123.436574 117.368667 \n", "L 127.455093 118.365508 \n", "L 129.0625 118.170507 \n", "L 133.081019 123.054115 \n", "L 137.099537 121.744614 \n", "L 141.118056 121.163563 \n", "L 145.136574 122.618372 \n", "L 149.155093 120.489822 \n", "L 150.7625 121.011951 \n", "L 154.781019 128.460251 \n", "L 158.799537 130.113222 \n", "L 162.818056 128.668455 \n", "L 166.836574 128.294685 \n", "L 170.855093 127.847429 \n", "L 172.4625 127.657119 \n", "L 176.481019 125.591587 \n", "L 180.499537 127.590871 \n", "L 184.518056 129.342213 \n", "L 188.536574 130.753103 \n", "L 192.555093 131.936941 \n", "L 194.1625 131.628226 \n", "L 198.181019 141.652173 \n", "L 202.199537 135.457161 \n", "L 206.218056 134.715413 \n", "L 210.236574 134.370943 \n", "L 214.255093 132.647401 \n", "L 215.8625 132.494207 \n", "\" clip-path=\"url(#paeeba9be02)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 20.5625 33.821933 \n", "L 42.2625 59.443904 \n", "L 63.9625 81.983047 \n", "L 85.6625 97.456386 \n", "L 107.3625 106.617358 \n", "L 129.0625 112.353019 \n", "L 150.7625 115.985599 \n", "L 172.4625 118.086715 \n", "L 194.1625 119.932657 \n", "L 215.8625 122.62165 \n", "\" clip-path=\"url(#paeeba9be02)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 20.5625 147.952173 \n", "L 20.5625 9.352173 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 215.8625 147.952173 \n", "L 215.8625 9.352173 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 20.5625 147.952173 \n", "L 215.8625 147.952173 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 20.5625 9.352173 \n", "L 215.8625 9.352173 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 130.417188 46.708423 \n", "L 208.8625 46.708423 \n", "Q 210.8625 46.708423 210.8625 44.708423 \n", "L 210.8625 16.352173 \n", "Q 210.8625 14.352173 208.8625 14.352173 \n", "L 130.417188 14.352173 \n", "Q 128.417188 14.352173 128.417188 16.352173 \n", "L 128.417188 44.708423 \n", "Q 128.417188 46.708423 130.417188 46.708423 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_23\">\n", "     <path d=\"M 132.417188 22.45061 \n", "L 142.417188 22.45061 \n", "L 152.417188 22.45061 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(160.417188 25.95061) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_24\">\n", "     <path d=\"M 132.417188 37.128735 \n", "L 142.417188 37.128735 \n", "L 152.417188 37.128735 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- valid loss -->\n", "     <g transform=\"translate(160.417188 40.628735) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"176.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"239.501953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"271.289062\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"299.072266\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"360.253906\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"412.353516\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"paeeba9be02\">\n", "   <rect x=\"20.5625\" y=\"9.352173\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["devices, num_epochs, lr, wd = d2l.try_all_gpus(), 10, 1e-4, 1e-4\n", "lr_period, lr_decay, net = 2, 0.9, get_net(devices)\n", "train(net, train_iter, valid_iter, num_epochs, lr, wd, devices, lr_period,\n", "      lr_decay)"]}, {"cell_type": "markdown", "id": "24685aa0", "metadata": {"origin_pos": 31}, "source": ["## [**Classifying the Testing Set**] and Submitting Results on Kaggle\n", "\n", "\n", "Similar to the final step in :numref:`sec_kaggle_cifar10`,\n", "in the end all the labeled data (including the validation set) are used for training the model and classifying the testing set.\n", "We will use the trained custom output network\n", "for classification.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "44a716b0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:35.196555Z", "iopub.status.busy": "2023-08-18T19:39:35.195796Z", "iopub.status.idle": "2023-08-18T19:41:31.649186Z", "shell.execute_reply": "2023-08-18T19:41:31.648347Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train loss 1.217\n", "742.7 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"229.425pt\" height=\"185.674373pt\" viewBox=\"0 0 229.425 185.674373\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:41:31.600621</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 185.674373 \n", "L 229.425 185.674373 \n", "L 229.425 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 20.5625 148.118123 \n", "L 215.8625 148.118123 \n", "L 215.8625 9.518123 \n", "L 20.5625 9.518123 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 42.2625 148.118123 \n", "L 42.2625 9.518123 \n", "\" clip-path=\"url(#p23a2ad9366)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m515c417b30\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m515c417b30\" x=\"42.2625\" y=\"148.118123\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(39.08125 162.716561) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 85.6625 148.118123 \n", "L 85.6625 9.518123 \n", "\" clip-path=\"url(#p23a2ad9366)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m515c417b30\" x=\"85.6625\" y=\"148.118123\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(82.48125 162.716561) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 129.0625 148.118123 \n", "L 129.0625 9.518123 \n", "\" clip-path=\"url(#p23a2ad9366)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m515c417b30\" x=\"129.0625\" y=\"148.118123\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(125.88125 162.716561) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 172.4625 148.118123 \n", "L 172.4625 9.518123 \n", "\" clip-path=\"url(#p23a2ad9366)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m515c417b30\" x=\"172.4625\" y=\"148.118123\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(169.28125 162.716561) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 215.8625 148.118123 \n", "L 215.8625 9.518123 \n", "\" clip-path=\"url(#p23a2ad9366)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m515c417b30\" x=\"215.8625\" y=\"148.118123\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(209.5 162.716561) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(102.984375 176.394686) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 20.5625 145.829675 \n", "L 215.8625 145.829675 \n", "\" clip-path=\"url(#p23a2ad9366)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m2a6b0b976b\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m2a6b0b976b\" x=\"20.5625\" y=\"145.829675\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(7.2 149.628893) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 20.5625 112.122061 \n", "L 215.8625 112.122061 \n", "\" clip-path=\"url(#p23a2ad9366)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m2a6b0b976b\" x=\"20.5625\" y=\"112.122061\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(7.2 115.921279) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 20.5625 78.414447 \n", "L 215.8625 78.414447 \n", "\" clip-path=\"url(#p23a2ad9366)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m2a6b0b976b\" x=\"20.5625\" y=\"78.414447\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(7.2 82.213665) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 20.5625 44.706833 \n", "L 215.8625 44.706833 \n", "\" clip-path=\"url(#p23a2ad9366)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m2a6b0b976b\" x=\"20.5625\" y=\"44.706833\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(7.2 48.506051) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 20.5625 10.999219 \n", "L 215.8625 10.999219 \n", "\" clip-path=\"url(#p23a2ad9366)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m2a6b0b976b\" x=\"20.5625\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(7.2 14.798438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 3.0625 15.818123 \n", "L 7.2625 17.481587 \n", "L 11.4625 19.326339 \n", "L 15.6625 21.393652 \n", "L 19.8625 24.024419 \n", "L 20.5625 24.246081 \n", "L 24.7625 45.707808 \n", "L 28.9625 50.504531 \n", "L 33.1625 52.564199 \n", "L 37.3625 54.639362 \n", "L 41.5625 56.31177 \n", "L 42.2625 56.598257 \n", "L 46.4625 79.090953 \n", "L 50.6625 82.205873 \n", "L 54.8625 83.134543 \n", "L 59.0625 84.610569 \n", "L 63.2625 86.10741 \n", "L 63.9625 86.254735 \n", "L 68.1625 102.399211 \n", "L 72.3625 103.141292 \n", "L 76.5625 100.500348 \n", "L 80.7625 102.871927 \n", "L 84.9625 103.894843 \n", "L 85.6625 103.70601 \n", "L 89.8625 117.105179 \n", "L 94.0625 116.571204 \n", "L 98.2625 117.844702 \n", "L 102.4625 117.751797 \n", "L 106.6625 118.250436 \n", "L 107.3625 118.158559 \n", "L 111.5625 127.52385 \n", "L 115.7625 125.592948 \n", "L 119.9625 123.823777 \n", "L 124.1625 123.846767 \n", "L 128.3625 125.507209 \n", "L 129.0625 125.315649 \n", "L 133.2625 130.890998 \n", "L 137.4625 130.081491 \n", "L 141.6625 129.791419 \n", "L 145.8625 130.967071 \n", "L 150.0625 129.91912 \n", "L 150.7625 130.074369 \n", "L 154.9625 136.222948 \n", "L 159.1625 134.548964 \n", "L 163.3625 135.53149 \n", "L 167.5625 135.040105 \n", "L 171.7625 133.786077 \n", "L 172.4625 133.681154 \n", "L 176.6625 136.710488 \n", "L 180.8625 136.778762 \n", "L 185.0625 136.566225 \n", "L 189.2625 135.922413 \n", "L 193.4625 136.144842 \n", "L 194.1625 136.034904 \n", "L 198.3625 140.709356 \n", "L 202.5625 141.818123 \n", "L 206.7625 139.993039 \n", "L 210.9625 138.843171 \n", "L 215.1625 138.264817 \n", "L 215.8625 138.518114 \n", "\" clip-path=\"url(#p23a2ad9366)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\"/>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 20.5625 148.118123 \n", "L 20.5625 9.518123 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 215.8625 148.118123 \n", "L 215.8625 9.518123 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 20.5625 148.118123 \n", "L 215.8625 148.118123 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 20.5625 9.518123 \n", "L 215.8625 9.518123 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 131.09375 32.196248 \n", "L 208.8625 32.196248 \n", "Q 210.8625 32.196248 210.8625 30.196248 \n", "L 210.8625 16.518123 \n", "Q 210.8625 14.518123 208.8625 14.518123 \n", "L 131.09375 14.518123 \n", "Q 129.09375 14.518123 129.09375 16.518123 \n", "L 129.09375 30.196248 \n", "Q 129.09375 32.196248 131.09375 32.196248 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_23\">\n", "     <path d=\"M 133.09375 22.616561 \n", "L 143.09375 22.616561 \n", "L 153.09375 22.616561 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(161.09375 26.116561) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p23a2ad9366\">\n", "   <rect x=\"20.5625\" y=\"9.518123\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["net = get_net(devices)\n", "train(net, train_valid_iter, None, num_epochs, lr, wd, devices, lr_period,\n", "      lr_decay)\n", "\n", "preds = []\n", "for data, label in test_iter:\n", "    output = torch.nn.functional.softmax(net(data.to(devices[0])), dim=1)\n", "    preds.extend(output.cpu().detach().numpy())\n", "ids = sorted(os.listdir(\n", "    os.path.join(data_dir, 'train_valid_test', 'test', 'unknown')))\n", "with open('submission.csv', 'w') as f:\n", "    f.write('id,' + ','.join(train_valid_ds.classes) + '\\n')\n", "    for i, output in zip(ids, preds):\n", "        f.write(i.split('.')[0] + ',' + ','.join(\n", "            [str(num) for num in output]) + '\\n')"]}, {"cell_type": "markdown", "id": "2707c202", "metadata": {"origin_pos": 34}, "source": ["The above code\n", "will generate a `submission.csv` file\n", "to be submitted\n", "to Kaggle in the same way described in :numref:`sec_kaggle_house`.\n", "\n", "\n", "## Summary\n", "\n", "\n", "* Images in the ImageNet dataset are larger (with varying dimensions) than CIFAR-10 images. We may modify image augmentation operations for tasks on a different dataset.\n", "* To classify a subset of the ImageNet dataset, we can leverage pre-trained models on the full ImageNet dataset to extract features and only train a custom small-scale output network. This will lead to less computational time and memory cost.\n", "\n", "\n", "## Exercises\n", "\n", "1. When using the full Kaggle competition dataset, what results can you achieve when you increase `batch_size` (batch size) and `num_epochs` (number of epochs) while setting some other hyperparameters as `lr = 0.01`, `lr_period = 10`, and `lr_decay = 0.1`?\n", "1. Do you get better results if you use a deeper pretrained model? How do you tune hyperparameters? Can you further improve the results?\n"]}, {"cell_type": "markdown", "id": "20bcfde3", "metadata": {"origin_pos": 36, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1481)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "20485ca9", "metadata": {"origin_pos": 0}, "source": ["# Fine-Tuning\n", ":label:`sec_fine_tuning`\n", "\n", "In earlier chapters, we discussed how to train models on the Fashion-MNIST training dataset with only 60000 images. We also described ImageNet, the most widely used large-scale image dataset in academia, which has more than 10 million images and 1000 objects. However, the size of the dataset that we usually encounter is between those of the two datasets.\n", "\n", "\n", "Suppose that we want to recognize different types of chairs from images, and then recommend purchase links to users. \n", "One possible method is to first identify\n", "100 common chairs,\n", "take 1000 images of different angles for each chair, \n", "and then train a classification model on the collected image dataset.\n", "Although this chair dataset may be larger than the Fashion-MNIST dataset,\n", "the number of examples is still less than one-tenth of \n", "that in ImageNet.\n", "This may lead to overfitting of complicated models \n", "that are suitable for ImageNet on this chair dataset.\n", "Besides, due to the limited amount of training examples,\n", "the accuracy of the trained model\n", "may not meet practical requirements.\n", "\n", "\n", "In order to address the above problems,\n", "an obvious solution is to collect more data.\n", "However, collecting and labeling data can take a lot of time and money.\n", "For example, in order to collect the ImageNet dataset, researchers have spent millions of dollars from research funding.\n", "Although the current data collection cost has been significantly reduced, this cost still cannot be ignored.\n", "\n", "\n", "Another solution is to apply *transfer learning* to transfer the knowledge learned from the *source dataset* to the *target dataset*.\n", "For example, although most of the images in the ImageNet dataset have nothing to do with chairs, the model trained on this dataset may extract more general image features, which can help identify edges, textures, shapes, and object composition.\n", "These similar features may\n", "also be effective for recognizing chairs.\n", "\n", "\n", "## Steps\n", "\n", "\n", "In this section, we will introduce a common technique in transfer learning: *fine-tuning*. As shown in :numref:`fig_finetune`, fine-tuning consists of the following four steps:\n", "\n", "\n", "1. Pretrain a neural network model, i.e., the *source model*, on a source dataset (e.g., the ImageNet dataset).\n", "1. Create a new neural network model, i.e., the *target model*. This copies all model designs and their parameters on the source model except the output layer. We assume that these model parameters contain the knowledge learned from the source dataset and this knowledge will also be applicable to the target dataset. We also assume that the output layer of the source model is closely related to the labels of the source dataset; thus it is not used in the target model.\n", "1. Add an output layer to the target model, whose number of outputs is the number of categories in the target dataset. Then randomly initialize the model parameters of this layer.\n", "1. Train the target model on the target dataset, such as a chair dataset. The output layer will be trained from scratch, while the parameters of all the other layers are fine-tuned based on the parameters of the source model.\n", "\n", "![Fine tuning.](../img/finetune.svg)\n", ":label:`fig_finetune`\n", "\n", "When target datasets are much smaller than source datasets, fine-tuning helps to improve models' generalization ability.\n", "\n", "\n", "## Hot Dog Recognition\n", "\n", "Let's demonstrate fine-tuning via a concrete case:\n", "hot dog recognition. \n", "We will fine-tune a ResNet model on a small dataset,\n", "which was pretrained on the ImageNet dataset.\n", "This small dataset consists of\n", "thousands of images with and without hot dogs.\n", "We will use the fine-tuned model to recognize \n", "hot dogs from images.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "fb0f7671", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:15.209278Z", "iopub.status.busy": "2023-08-18T19:34:15.208876Z", "iopub.status.idle": "2023-08-18T19:34:22.000442Z", "shell.execute_reply": "2023-08-18T19:34:21.999419Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import os\n", "import torch\n", "import torchvision\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "30199ff3", "metadata": {"origin_pos": 3}, "source": ["### Reading the Dataset\n", "\n", "[**The hot dog dataset we use was taken from online images**].\n", "This dataset consists of\n", "1400 positive-class images containing hot dogs,\n", "and as many negative-class images containing other foods.\n", "1000 images of both classes are used for training and the rest are for testing.\n", "\n", "\n", "After unzipping the downloaded dataset,\n", "we obtain two folders `hotdog/train` and `hotdog/test`. Both folders have `hotdog` and `not-hotdog` subfolders, either of which contains images of\n", "the corresponding class.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "19660135", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:22.006042Z", "iopub.status.busy": "2023-08-18T19:34:22.005365Z", "iopub.status.idle": "2023-08-18T19:34:33.206979Z", "shell.execute_reply": "2023-08-18T19:34:33.205042Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/hotdog.zip from http://d2l-data.s3-accelerate.amazonaws.com/hotdog.zip...\n"]}], "source": ["#@save\n", "d2l.DATA_HUB['hotdog'] = (d2l.DATA_URL + 'hotdog.zip',\n", "                         'fba480ffa8aa7e0febbb511d181409f899b9baa5')\n", "\n", "data_dir = d2l.download_extract('hotdog')"]}, {"cell_type": "markdown", "id": "a95289be", "metadata": {"origin_pos": 5}, "source": ["We create two instances to read all the image files in the training and testing datasets, respectively.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "dfacc35e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:33.212440Z", "iopub.status.busy": "2023-08-18T19:34:33.211622Z", "iopub.status.idle": "2023-08-18T19:34:33.228377Z", "shell.execute_reply": "2023-08-18T19:34:33.227421Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["train_imgs = torchvision.datasets.ImageFolder(os.path.join(data_dir, 'train'))\n", "test_imgs = torchvision.datasets.ImageFolder(os.path.join(data_dir, 'test'))"]}, {"cell_type": "markdown", "id": "e85f0b8a", "metadata": {"origin_pos": 8}, "source": ["The first 8 positive examples and the last 8 negative images are shown below. As you can see, [**the images vary in size and aspect ratio**].\n"]}, {"cell_type": "code", "execution_count": 4, "id": "d6a1a61c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:33.232122Z", "iopub.status.busy": "2023-08-18T19:34:33.231258Z", "iopub.status.idle": "2023-08-18T19:34:34.208991Z", "shell.execute_reply": "2023-08-18T19:34:34.207678Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1120x280 with 16 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["hotdogs = [train_imgs[i][0] for i in range(8)]\n", "not_hotdogs = [train_imgs[-i - 1][0] for i in range(8)]\n", "d2l.show_images(hotdogs + not_hotdogs, 2, 8, scale=1.4);"]}, {"cell_type": "markdown", "id": "935d44a0", "metadata": {"origin_pos": 10}, "source": ["During training, we first crop a random area of random size and random aspect ratio from the image,\n", "and then scale this area\n", "to a $224 \\times 224$ input image. \n", "During testing, we scale both the height and width of an image to 256 pixels, and then crop a central $224 \\times 224$ area as input.\n", "In addition, \n", "for the three RGB (red, green, and blue) color channels\n", "we *standardize* their values channel by channel.\n", "<PERSON><PERSON><PERSON>y,\n", "the mean value of a channel is subtracted from each value of that channel and then the result is divided by the standard deviation of that channel.\n", "\n", "[~~Data augmentations~~]\n"]}, {"cell_type": "code", "execution_count": 5, "id": "31e4523c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:34.216241Z", "iopub.status.busy": "2023-08-18T19:34:34.215791Z", "iopub.status.idle": "2023-08-18T19:34:34.224989Z", "shell.execute_reply": "2023-08-18T19:34:34.223829Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["# Specify the means and standard deviations of the three RGB channels to\n", "# standardize each channel\n", "normalize = torchvision.transforms.Normalize(\n", "    [0.485, 0.456, 0.406], [0.229, 0.224, 0.225])\n", "\n", "train_augs = torchvision.transforms.Compose([\n", "    torchvision.transforms.RandomResizedCrop(224),\n", "    torchvision.transforms.RandomHorizontalFlip(),\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    normalize])\n", "\n", "test_augs = torchvision.transforms.Compose([\n", "    torchvision.transforms.Resize([256, 256]),\n", "    torchvision.transforms.CenterCrop(224),\n", "    torchvision.transforms.<PERSON><PERSON><PERSON><PERSON>(),\n", "    normalize])"]}, {"cell_type": "markdown", "id": "17705236", "metadata": {"origin_pos": 13}, "source": ["### [**Defining and Initializing the Model**]\n", "\n", "We use ResNet-18, which was pretrained on the ImageNet dataset, as the source model. Here, we specify `pretrained=True` to automatically download the pretrained model parameters. \n", "If this model is used for the first time,\n", "Internet connection is required for download.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "e9046843", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:34.229238Z", "iopub.status.busy": "2023-08-18T19:34:34.228807Z", "iopub.status.idle": "2023-08-18T19:34:34.679551Z", "shell.execute_reply": "2023-08-18T19:34:34.674452Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["pretrained_net = torchvision.models.resnet18(pretrained=True)"]}, {"cell_type": "markdown", "id": "cd306132", "metadata": {"origin_pos": 17, "tab": ["pytorch"]}, "source": ["The pretrained source model instance contains a number of feature layers and an output layer `fc`.\n", "The main purpose of this division is to facilitate the fine-tuning of model parameters of all layers but the output layer. The member variable `fc` of source model is given below.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "df7b4156", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:34.684734Z", "iopub.status.busy": "2023-08-18T19:34:34.684186Z", "iopub.status.idle": "2023-08-18T19:34:34.691680Z", "shell.execute_reply": "2023-08-18T19:34:34.690534Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["Linear(in_features=512, out_features=1000, bias=True)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pretrained_net.fc"]}, {"cell_type": "markdown", "id": "974d8999", "metadata": {"origin_pos": 20}, "source": ["As a fully connected layer, it transforms ResNet's final global average pooling outputs into 1000 class outputs of the ImageNet dataset.\n", "We then construct a new neural network as the target model. It is defined in the same way as the pretrained source model except that\n", "its number of outputs in the final layer\n", "is set to\n", "the number of classes in the target dataset (rather than 1000).\n", "\n", "In the code below, the model parameters before the output layer of the target model instance `finetune_net` are initialized to model parameters of the corresponding layers from the source model.\n", "Since these model parameters were obtained via pretraining on ImageNet, \n", "they are effective.\n", "Therefore, we can only use \n", "a small learning rate to *fine-tune* such pretrained parameters.\n", "In contrast, model parameters in the output layer are randomly initialized and generally require a larger learning rate to be learned from scratch.\n", "Letting the base learning rate be $\\eta$, a learning rate of $10\\eta$ will be used to iterate the model parameters in the output layer.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "448bbec1", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:34.696427Z", "iopub.status.busy": "2023-08-18T19:34:34.694972Z", "iopub.status.idle": "2023-08-18T19:34:35.162252Z", "shell.execute_reply": "2023-08-18T19:34:35.161212Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["finetune_net = torchvision.models.resnet18(pretrained=True)\n", "finetune_net.fc = nn.Linear(finetune_net.fc.in_features, 2)\n", "nn.init.xavier_uniform_(finetune_net.fc.weight);"]}, {"cell_type": "markdown", "id": "2162cbf1", "metadata": {"origin_pos": 23}, "source": ["### [**Fine-Tuning the Model**]\n", "\n", "First, we define a training function `train_fine_tuning` that uses fine-tuning so it can be called multiple times.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "cb2c0df2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:35.166579Z", "iopub.status.busy": "2023-08-18T19:34:35.166026Z", "iopub.status.idle": "2023-08-18T19:34:35.176137Z", "shell.execute_reply": "2023-08-18T19:34:35.175112Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [], "source": ["# If `param_group=True`, the model parameters in the output layer will be\n", "# updated using a learning rate ten times greater\n", "def train_fine_tuning(net, learning_rate, batch_size=128, num_epochs=5,\n", "                      param_group=True):\n", "    train_iter = torch.utils.data.DataLoader(torchvision.datasets.ImageFolder(\n", "        os.path.join(data_dir, 'train'), transform=train_augs),\n", "        batch_size=batch_size, shuffle=True)\n", "    test_iter = torch.utils.data.DataLoader(torchvision.datasets.ImageFolder(\n", "        os.path.join(data_dir, 'test'), transform=test_augs),\n", "        batch_size=batch_size)\n", "    devices = d2l.try_all_gpus()\n", "    loss = nn.CrossEntropyLoss(reduction=\"none\")\n", "    if param_group:\n", "        params_1x = [param for name, param in net.named_parameters()\n", "             if name not in [\"fc.weight\", \"fc.bias\"]]\n", "        trainer = torch.optim.SGD([{'params': params_1x},\n", "                                   {'params': net.fc.parameters(),\n", "                                    'lr': learning_rate * 10}],\n", "                                lr=learning_rate, weight_decay=0.001)\n", "    else:\n", "        trainer = torch.optim.SGD(net.parameters(), lr=learning_rate,\n", "                                  weight_decay=0.001)\n", "    d2l.train_ch13(net, train_iter, test_iter, loss, trainer, num_epochs,\n", "                   devices)"]}, {"cell_type": "markdown", "id": "6910639b", "metadata": {"origin_pos": 26}, "source": ["We [**set the base learning rate to a small value**]\n", "in order to *fine-tune* the model parameters obtained via pretraining. Based on the previous settings, we will train the output layer parameters of the target model from scratch using a learning rate ten times greater.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "2c3b7118", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:35.180052Z", "iopub.status.busy": "2023-08-18T19:34:35.179043Z", "iopub.status.idle": "2023-08-18T19:36:58.138993Z", "shell.execute_reply": "2023-08-18T19:36:58.138042Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.242, train acc 0.909, test acc 0.940\n", "1062.4 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"187.155469pt\" viewBox=\"0 0 235.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:36:58.069991</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 235.**********.155469 \n", "L 235.784375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m2aca8ff002\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m2aca8ff002\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(26.921875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 78.928125 149.599219 \n", "L 78.928125 10.999219 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m2aca8ff002\" x=\"78.928125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 127.753125 149.599219 \n", "L 127.753125 10.999219 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m2aca8ff002\" x=\"127.753125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(124.571875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 176.578125 149.599219 \n", "L 176.578125 10.999219 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m2aca8ff002\" x=\"176.578125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(173.396875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m2aca8ff002\" x=\"225.403125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(222.221875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 177.875781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mf2f63990da\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf2f63990da\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 153.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 121.879219 \n", "L 225.403125 121.879219 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mf2f63990da\" x=\"30.103125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 125.678438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 94.159219 \n", "L 225.403125 94.159219 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mf2f63990da\" x=\"30.103125\" y=\"94.159219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 97.958438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 66.439219 \n", "L 225.403125 66.439219 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mf2f63990da\" x=\"30.103125\" y=\"66.439219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 70.238437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 38.719219 \n", "L 225.403125 38.719219 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mf2f63990da\" x=\"30.103125\" y=\"38.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.518438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#mf2f63990da\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 36.802446 -1 \n", "L 39.257812 86.217713 \n", "L 48.4125 80.383259 \n", "L 57.567188 87.967688 \n", "L 66.721875 88.102055 \n", "L 75.876563 87.959883 \n", "L 78.928125 88.642305 \n", "L 88.082813 115.047338 \n", "L 97.2375 111.788791 \n", "L 106.392188 117.44407 \n", "L 115.546875 119.008809 \n", "L 124.701563 116.070568 \n", "L 127.753125 116.772194 \n", "L 136.907813 121.429716 \n", "L 146.0625 123.745935 \n", "L 155.217187 125.320678 \n", "L 164.371875 123.272356 \n", "L 173.526563 119.212628 \n", "L 176.578125 118.783952 \n", "L 185.732812 125.270575 \n", "L 194.8875 121.169694 \n", "L 204.042188 122.437683 \n", "L 213.196875 123.149919 \n", "L 222.351562 115.687615 \n", "L 225.403125 116.059681 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M -1 81.633899 \n", "L -0.4125 81.923438 \n", "L 8.742188 69.832031 \n", "L 17.896875 66.493359 \n", "L 27.051562 58.715156 \n", "L 30.103125 57.499519 \n", "L 39.257812 30.850781 \n", "L 48.4125 31.392188 \n", "L 57.567188 29.166406 \n", "L 66.721875 30.128906 \n", "L 75.876563 29.984531 \n", "L 78.928125 29.779519 \n", "L 88.082813 24.353906 \n", "L 97.2375 23.992969 \n", "L 106.392188 22.308594 \n", "L 115.546875 22.458984 \n", "L 124.701563 23.343281 \n", "L 127.753125 23.057419 \n", "L 136.907813 22.188281 \n", "L 146.0625 20.203125 \n", "L 155.217187 19.902344 \n", "L 164.371875 20.293359 \n", "L 173.526563 21.827344 \n", "L 176.578125 21.948619 \n", "L 185.732812 20.744531 \n", "L 194.8875 21.646875 \n", "L 204.042188 21.105469 \n", "L 213.196875 20.744531 \n", "L 222.351562 23.632031 \n", "L 225.403125 23.542519 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 30.103125 34.561219 \n", "L 78.928125 22.606969 \n", "L 127.753125 21.740719 \n", "L 176.578125 20.181469 \n", "L 225.403125 19.315219 \n", "\" clip-path=\"url(#p89af5321df)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 103.816406 \n", "L 218.403125 103.816406 \n", "Q 220.403125 103.816406 220.403125 101.816406 \n", "L 220.403125 58.782031 \n", "Q 220.403125 56.782031 218.403125 56.782031 \n", "L 140.634375 56.782031 \n", "Q 138.634375 56.782031 138.634375 58.782031 \n", "L 138.634375 101.816406 \n", "Q 138.634375 103.816406 140.634375 103.816406 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 142.634375 64.880469 \n", "L 152.634375 64.880469 \n", "L 162.634375 64.880469 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 68.380469) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 142.634375 79.558594 \n", "L 152.634375 79.558594 \n", "L 162.634375 79.558594 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 83.058594) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 142.634375 94.236719 \n", "L 152.634375 94.236719 \n", "L 162.634375 94.236719 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(170.634375 97.736719) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p89af5321df\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["train_fine_tuning(finetune_net, 5e-5)"]}, {"cell_type": "markdown", "id": "b5f8cdee", "metadata": {"origin_pos": 29}, "source": ["[**For comparison,**] we define an identical model, but (**initialize all of its model parameters to random values**). Since the entire model needs to be trained from scratch, we can use a larger learning rate.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "896bc6e6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:58.143441Z", "iopub.status.busy": "2023-08-18T19:36:58.142671Z", "iopub.status.idle": "2023-08-18T19:39:18.298264Z", "shell.execute_reply": "2023-08-18T19:39:18.296594Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.352, train acc 0.846, test acc 0.850\n", "1525.4 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"187.155469pt\" viewBox=\"0 0 235.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:39:18.226931</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 235.**********.155469 \n", "L 235.784375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m851f8f92eb\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m851f8f92eb\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(26.921875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 78.928125 149.599219 \n", "L 78.928125 10.999219 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m851f8f92eb\" x=\"78.928125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 127.753125 149.599219 \n", "L 127.753125 10.999219 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m851f8f92eb\" x=\"127.753125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(124.571875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 176.578125 149.599219 \n", "L 176.578125 10.999219 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m851f8f92eb\" x=\"176.578125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(173.396875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m851f8f92eb\" x=\"225.403125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(222.221875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 177.875781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m242c38545d\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m242c38545d\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 153.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 121.879219 \n", "L 225.403125 121.879219 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m242c38545d\" x=\"30.103125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 125.678438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 94.159219 \n", "L 225.403125 94.159219 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m242c38545d\" x=\"30.103125\" y=\"94.159219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 97.958438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 66.439219 \n", "L 225.403125 66.439219 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m242c38545d\" x=\"30.103125\" y=\"66.439219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 70.238437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 38.719219 \n", "L 225.403125 38.719219 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m242c38545d\" x=\"30.103125\" y=\"38.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.518438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m242c38545d\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 35.990321 -1 \n", "L 39.257812 71.809868 \n", "L 48.4125 81.376737 \n", "L 57.567188 85.142986 \n", "L 66.721875 86.364732 \n", "L 75.876563 88.168687 \n", "L 78.928125 88.635861 \n", "L 88.082813 98.12 \n", "L 97.2375 98.552192 \n", "L 106.392188 96.526246 \n", "L 115.546875 97.050645 \n", "L 124.701563 96.495527 \n", "L 127.753125 96.693512 \n", "L 136.907813 101.492737 \n", "L 146.0625 98.479332 \n", "L 155.217187 99.42788 \n", "L 164.371875 100.472277 \n", "L 173.526563 99.409932 \n", "L 176.578125 98.706044 \n", "L 185.732812 98.726441 \n", "L 194.8875 97.340011 \n", "L 204.042188 99.381302 \n", "L 213.196875 100.383851 \n", "L 222.351562 101.152481 \n", "L 225.403125 100.825531 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M -1 73.457824 \n", "L -0.4125 73.260938 \n", "L 8.742188 69.832031 \n", "L 17.896875 62.883984 \n", "L 27.051562 64.273594 \n", "L 30.103125 63.320719 \n", "L 39.257812 39.513281 \n", "L 48.4125 37.167188 \n", "L 57.567188 37.467969 \n", "L 66.721875 37.167188 \n", "L 75.876563 36.192656 \n", "L 78.928125 36.363019 \n", "L 88.082813 33.377344 \n", "L 97.2375 33.016406 \n", "L 106.392188 34.460156 \n", "L 115.546875 34.911328 \n", "L 124.701563 34.604531 \n", "L 127.753125 34.491919 \n", "L 136.907813 31.933594 \n", "L 146.0625 33.196875 \n", "L 155.217187 33.136719 \n", "L 164.371875 32.475 \n", "L 173.526563 33.160781 \n", "L 176.578125 33.591019 \n", "L 185.732812 31.572656 \n", "L 194.8875 33.196875 \n", "L 204.042188 32.535156 \n", "L 213.196875 32.294531 \n", "L 222.351562 31.933594 \n", "L 225.403125 32.343619 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 30.103125 43.396969 \n", "L 78.928125 36.813469 \n", "L 127.753125 38.026219 \n", "L 176.578125 31.269469 \n", "L 225.403125 31.789219 \n", "\" clip-path=\"url(#p7493c0593a)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 37.103125 144.599219 \n", "L 114.871875 144.599219 \n", "Q 116.871875 144.599219 116.871875 142.599219 \n", "L 116.871875 99.564844 \n", "Q 116.871875 97.564844 114.871875 97.564844 \n", "L 37.103125 97.564844 \n", "Q 35.103125 97.564844 35.103125 99.564844 \n", "L 35.103125 142.599219 \n", "Q 35.103125 144.599219 37.103125 144.599219 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 39.103125 105.663281 \n", "L 49.103125 105.663281 \n", "L 59.103125 105.663281 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(67.103125 109.163281) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 39.103125 120.341406 \n", "L 49.103125 120.341406 \n", "L 59.103125 120.341406 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(67.103125 123.841406) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 39.103125 135.019531 \n", "L 49.103125 135.019531 \n", "L 59.103125 135.019531 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(67.103125 138.519531) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p7493c0593a\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["scratch_net = torchvision.models.resnet18()\n", "scratch_net.fc = nn.Linear(scratch_net.fc.in_features, 2)\n", "train_fine_tuning(scratch_net, 5e-4, param_group=False)"]}, {"cell_type": "markdown", "id": "9a25c8b3", "metadata": {"origin_pos": 32}, "source": ["As we can see, the fine-tuned model tends to perform better for the same epoch\n", "because its initial parameter values are more effective.\n", "\n", "\n", "## Summary\n", "\n", "* Transfer learning transfers knowledge learned from the source dataset to the target dataset. Fine-tuning is a common technique for transfer learning.\n", "* The target model copies all model designs with their parameters from the source model except the output layer, and fine-tunes these parameters based on the target dataset. In contrast, the output layer of the target model needs to be trained from scratch.\n", "* Generally, fine-tuning parameters uses a smaller learning rate, while training the output layer from scratch can use a larger learning rate.\n", "\n", "\n", "## Exercises\n", "\n", "1. Keep increasing the learning rate of `finetune_net`. How does the accuracy of the model change?\n", "2. Further adjust hyperparameters of `finetune_net` and `scratch_net` in the comparative experiment. Do they still differ in accuracy?\n", "3. Set the parameters before the output layer of `finetune_net` to those of the source model and do *not* update them during training. How does the accuracy of the model change? You can use the following code.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "3cb185ed", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:18.305867Z", "iopub.status.busy": "2023-08-18T19:39:18.304631Z", "iopub.status.idle": "2023-08-18T19:39:18.311192Z", "shell.execute_reply": "2023-08-18T19:39:18.309227Z"}, "origin_pos": 34, "tab": ["pytorch"]}, "outputs": [], "source": ["for param in finetune_net.parameters():\n", "    param.requires_grad = False"]}, {"cell_type": "markdown", "id": "16d1cf0d", "metadata": {"origin_pos": 35}, "source": ["4. In fact, there is a \"hotdog\" class in the `ImageNet` dataset. Its corresponding weight parameter in the output layer can be obtained via the following code. How can we leverage this weight parameter?\n"]}, {"cell_type": "code", "execution_count": 13, "id": "13dc297e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:18.315239Z", "iopub.status.busy": "2023-08-18T19:39:18.314614Z", "iopub.status.idle": "2023-08-18T19:39:18.328069Z", "shell.execute_reply": "2023-08-18T19:39:18.325873Z"}, "origin_pos": 37, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.Size([1, 512])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["weight = pretrained_net.fc.weight\n", "hotdog_w = torch.split(weight.data, 1, dim=0)[934]\n", "hotdog_w.shape"]}, {"cell_type": "markdown", "id": "9f50ccdc", "metadata": {"origin_pos": 39, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1439)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
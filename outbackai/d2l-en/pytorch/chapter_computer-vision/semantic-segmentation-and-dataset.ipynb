{"cells": [{"cell_type": "markdown", "id": "d8efb606", "metadata": {"origin_pos": 0}, "source": ["# Semantic Segmentation and the Dataset\n", ":label:`sec_semantic_segmentation`\n", "\n", "When discussing object detection tasks\n", "in :numref:`sec_bbox`--:numref:`sec_rcnn`,\n", "rectangular bounding boxes\n", "are used to label and predict objects in images.\n", "This section will discuss the problem of *semantic segmentation*,\n", "which focuses on how to divide an image into regions belonging to different semantic classes.\n", "Different from object detection,\n", "semantic segmentation\n", "recognizes and understands\n", "what are in images in pixel level:\n", "its labeling and prediction of semantic regions are\n", "in pixel level.\n", ":numref:`fig_segmentation` shows the labels\n", "of the dog, cat, and background of the image in semantic segmentation.\n", "Compared with in object detection,\n", "the pixel-level borders labeled\n", "in semantic segmentation are obviously more fine-grained.\n", "\n", "\n", "![Labels of the dog, cat, and background of the image in semantic segmentation.](../img/segmentation.svg)\n", ":label:`fig_segmentation`\n", "\n", "\n", "## Image Segmentation and Instance Segmentation\n", "\n", "There are also two important tasks\n", "in the field of computer vision that are similar to semantic segmentation,\n", "namely image segmentation and instance segmentation.\n", "We will briefly\n", "distinguish them from semantic segmentation as follows.\n", "\n", "* *Image segmentation* divides an image into several constituent regions. The methods for this type of problem usually make use of the correlation between pixels in the image. It does not need label information about image pixels during training, and it cannot guarantee that the segmented regions will have the semantics that we hope to obtain during prediction. Taking the image in :numref:`fig_segmentation` as input, image segmentation may divide the dog into two regions: one covers the mouth and eyes which are mainly black, and the other covers the rest of the body which is mainly yellow.\n", "* *Instance segmentation* is also called *simultaneous detection and segmentation*. It studies how to recognize the pixel-level regions of each object instance in an image. Different from semantic segmentation, instance segmentation needs to distinguish not only semantics, but also different object instances. For example, if there are two dogs in the image, instance segmentation needs to distinguish which of the two dogs a pixel belongs to.\n", "\n", "\n", "\n", "## The Pascal VOC2012 Semantic Segmentation Dataset\n", "\n", "[**On of the most important semantic segmentation dataset\n", "is [Pascal VOC2012](http://host.robots.ox.ac.uk/pascal/VOC/voc2012/).**]\n", "In the following,\n", "we will take a look at this dataset.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "b3ff2e8e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:11.871245Z", "iopub.status.busy": "2023-08-18T19:28:11.870620Z", "iopub.status.idle": "2023-08-18T19:28:14.849010Z", "shell.execute_reply": "2023-08-18T19:28:14.848095Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import os\n", "import torch\n", "import torchvision\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "28b90a1b", "metadata": {"origin_pos": 3}, "source": ["The tar file of the dataset is about 2 GB,\n", "so it may take a while to download the file.\n", "The extracted dataset is located at `../data/VOCdevkit/VOC2012`.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "b7119d6e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:28:14.854717Z", "iopub.status.busy": "2023-08-18T19:28:14.853304Z", "iopub.status.idle": "2023-08-18T19:29:34.095855Z", "shell.execute_reply": "2023-08-18T19:29:34.094939Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/VOCtrainval_11-May-2012.tar from http://d2l-data.s3-accelerate.amazonaws.com/VOCtrainval_11-May-2012.tar...\n"]}], "source": ["#@save\n", "d2l.DATA_HUB['voc2012'] = (d2l.DATA_URL + 'VOCtrainval_11-May-2012.tar',\n", "                           '4e443f8a2eca6b1dac8a6c57641b67dd40621a49')\n", "\n", "voc_dir = d2l.download_extract('voc2012', 'VOCdevkit/VOC2012')"]}, {"cell_type": "markdown", "id": "de67a2bb", "metadata": {"origin_pos": 5}, "source": ["After entering the path `../data/VOCdevkit/VOC2012`,\n", "we can see the different components of the dataset.\n", "The `ImageSets/Segmentation` path contains text files\n", "that specify training and test samples,\n", "while the `JPEGImages` and `SegmentationClass` paths\n", "store the input image and label for each example, respectively.\n", "The label here is also in the image format,\n", "with the same size\n", "as its labeled input image.\n", "Besides,\n", "pixels with the same color in any label image belong to the same semantic class.\n", "The following defines the `read_voc_images` function to [**read all the input images and labels into the memory**].\n"]}, {"cell_type": "code", "execution_count": 3, "id": "77291c79", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:34.099720Z", "iopub.status.busy": "2023-08-18T19:29:34.099086Z", "iopub.status.idle": "2023-08-18T19:29:39.482362Z", "shell.execute_reply": "2023-08-18T19:29:39.481494Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def read_voc_images(voc_dir, is_train=True):\n", "    \"\"\"Read all VOC feature and label images.\"\"\"\n", "    txt_fname = os.path.join(voc_dir, 'ImageSets', 'Segmentation',\n", "                             'train.txt' if is_train else 'val.txt')\n", "    mode = torchvision.io.image.ImageReadMode.RGB\n", "    with open(txt_fname, 'r') as f:\n", "        images = f.read().split()\n", "    features, labels = [], []\n", "    for i, fname in enumerate(images):\n", "        features.append(torchvision.io.read_image(os.path.join(\n", "            voc_dir, 'JPEGImages', f'{fname}.jpg')))\n", "        labels.append(torchvision.io.read_image(os.path.join(\n", "            voc_dir, 'SegmentationClass' ,f'{fname}.png'), mode))\n", "    return features, labels\n", "\n", "train_features, train_labels = read_voc_images(voc_dir, True)"]}, {"cell_type": "markdown", "id": "833be1f4", "metadata": {"origin_pos": 8}, "source": ["We [**draw the first five input images and their labels**].\n", "In the label images, white and black represent borders and  background, respectively, while the other colors correspond to different classes.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "9bcf86cd", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:39.486452Z", "iopub.status.busy": "2023-08-18T19:29:39.485859Z", "iopub.status.idle": "2023-08-18T19:29:40.082103Z", "shell.execute_reply": "2023-08-18T19:29:40.080866Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"data": {"image/png": "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******************************************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", "text/plain": ["<Figure size 750x300 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["n = 5\n", "imgs = train_features[:n] + train_labels[:n]\n", "imgs = [img.permute(1,2,0) for img in imgs]\n", "d2l.show_images(imgs, 2, n);"]}, {"cell_type": "markdown", "id": "c7b57be3", "metadata": {"origin_pos": 11}, "source": ["Next, we [**enumerate\n", "the RGB color values and class names**]\n", "for all the labels in this dataset.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "a058500c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:40.086085Z", "iopub.status.busy": "2023-08-18T19:29:40.085486Z", "iopub.status.idle": "2023-08-18T19:29:40.092850Z", "shell.execute_reply": "2023-08-18T19:29:40.091770Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "VOC_COLORMAP = [[0, 0, 0], [128, 0, 0], [0, 128, 0], [128, 128, 0],\n", "                [0, 0, 128], [128, 0, 128], [0, 128, 128], [128, 128, 128],\n", "                [64, 0, 0], [192, 0, 0], [64, 128, 0], [192, 128, 0],\n", "                [64, 0, 128], [192, 0, 128], [64, 128, 128], [192, 128, 128],\n", "                [0, 64, 0], [128, 64, 0], [0, 192, 0], [128, 192, 0],\n", "                [0, 64, 128]]\n", "\n", "#@save\n", "VOC_CLASSES = ['background', 'aeroplane', 'bicycle', 'bird', 'boat',\n", "               'bottle', 'bus', 'car', 'cat', 'chair', 'cow',\n", "               'diningtable', 'dog', 'horse', 'motorbike', 'person',\n", "               'potted plant', 'sheep', 'sofa', 'train', 'tv/monitor']"]}, {"cell_type": "markdown", "id": "583b3007", "metadata": {"origin_pos": 13}, "source": ["With the two constants defined above,\n", "we can conveniently\n", "[**find the class index for each pixel in a label**].\n", "We define the `voc_colormap2label` function\n", "to build the mapping from the above RGB color values\n", "to class indices,\n", "and the `voc_label_indices` function\n", "to map any RGB values to their class indices in this Pascal VOC2012 dataset.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "0397eb81", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:40.097285Z", "iopub.status.busy": "2023-08-18T19:29:40.096259Z", "iopub.status.idle": "2023-08-18T19:29:40.105202Z", "shell.execute_reply": "2023-08-18T19:29:40.104186Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def voc_colormap2label():\n", "    \"\"\"Build the mapping from RGB to class indices for VOC labels.\"\"\"\n", "    colormap2label = torch.zeros(256 ** 3, dtype=torch.long)\n", "    for i, colormap in enumerate(VOC_COLORMAP):\n", "        colormap2label[\n", "            (colormap[0] * 256 + colormap[1]) * 256 + colormap[2]] = i\n", "    return colormap2label\n", "\n", "#@save\n", "def voc_label_indices(colormap, colormap2label):\n", "    \"\"\"Map any RGB values in VOC labels to their class indices.\"\"\"\n", "    colormap = colormap.permute(1, 2, 0).numpy().astype('int32')\n", "    idx = ((colormap[:, :, 0] * 256 + colormap[:, :, 1]) * 256\n", "           + colormap[:, :, 2])\n", "    return colormap2label[idx]"]}, {"cell_type": "markdown", "id": "ca29f584", "metadata": {"origin_pos": 16}, "source": ["[**For example**], in the first example image,\n", "the class index for the front part of the airplane is 1,\n", "while the background index is 0.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "e4d5ecc0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:40.110288Z", "iopub.status.busy": "2023-08-18T19:29:40.109584Z", "iopub.status.idle": "2023-08-18T19:29:40.159624Z", "shell.execute_reply": "2023-08-18T19:29:40.158538Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(tensor([[0, 0, 0, 0, 0, 0, 0, 0, 0, 1],\n", "         [0, 0, 0, 0, 0, 0, 0, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 0, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 1, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 1, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 0, 1, 1, 1, 1],\n", "         [0, 0, 0, 0, 0, 0, 0, 0, 1, 1]]),\n", " 'aeroplane')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["y = voc_label_indices(train_labels[0], voc_colormap2label())\n", "y[105:115, 130:140], VOC_CLASSES[1]"]}, {"cell_type": "markdown", "id": "9f82d9f1", "metadata": {"origin_pos": 18}, "source": ["### Data Preprocessing\n", "\n", "In previous experiments\n", "such as in :numref:`sec_alexnet`--:numref:`sec_googlenet`,\n", "images are rescaled\n", "to fit the model's required input shape.\n", "However, in semantic segmentation,\n", "doing so\n", "requires rescaling the predicted pixel classes\n", "back to the original shape of the input image.\n", "Such rescaling may be inaccurate,\n", "especially for segmented regions with different classes. To avoid this issue,\n", "we crop the image to a *fixed* shape instead of rescaling. Specifically, [**using random cropping from image augmentation, we crop the same area of\n", "the input image and the label**].\n"]}, {"cell_type": "code", "execution_count": 8, "id": "fda65a21", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:40.163447Z", "iopub.status.busy": "2023-08-18T19:29:40.162849Z", "iopub.status.idle": "2023-08-18T19:29:40.168029Z", "shell.execute_reply": "2023-08-18T19:29:40.167016Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def voc_rand_crop(feature, label, height, width):\n", "    \"\"\"Randomly crop both feature and label images.\"\"\"\n", "    rect = torchvision.transforms.RandomCrop.get_params(\n", "        feature, (height, width))\n", "    feature = torchvision.transforms.functional.crop(feature, *rect)\n", "    label = torchvision.transforms.functional.crop(label, *rect)\n", "    return feature, label"]}, {"cell_type": "code", "execution_count": 9, "id": "9bdbfc5b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:40.171880Z", "iopub.status.busy": "2023-08-18T19:29:40.171066Z", "iopub.status.idle": "2023-08-18T19:29:40.647767Z", "shell.execute_reply": "2023-08-18T19:29:40.646592Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 750x300 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["imgs = []\n", "for _ in range(n):\n", "    imgs += voc_rand_crop(train_features[0], train_labels[0], 200, 300)\n", "\n", "imgs = [img.permute(1, 2, 0) for img in imgs]\n", "d2l.show_images(imgs[::2] + imgs[1::2], 2, n);"]}, {"cell_type": "markdown", "id": "635e7cf9", "metadata": {"origin_pos": 23}, "source": ["### [**Custom Semantic Segmentation Dataset Class**]\n", "\n", "We define a custom semantic segmentation dataset class `VOCSegDataset` by inheriting the `Dataset` class provided by high-level APIs.\n", "By implementing the `__getitem__` function,\n", "we can arbitrarily access the input image indexed as `idx` in the dataset and the class index of each pixel in this image.\n", "Since some images in the dataset\n", "have a smaller size\n", "than the output size of random cropping,\n", "these examples are filtered out\n", "by a custom `filter` function.\n", "In addition, we also\n", "define the `normalize_image` function to\n", "standardize the values of the three RGB channels of input images.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "cf5783a4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:40.651770Z", "iopub.status.busy": "2023-08-18T19:29:40.651180Z", "iopub.status.idle": "2023-08-18T19:29:40.659950Z", "shell.execute_reply": "2023-08-18T19:29:40.658907Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class VOCSegDataset(torch.utils.data.Dataset):\n", "    \"\"\"A customized dataset to load the VOC dataset.\"\"\"\n", "\n", "    def __init__(self, is_train, crop_size, voc_dir):\n", "        self.transform = torchvision.transforms.Normalize(\n", "            mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])\n", "        self.crop_size = crop_size\n", "        features, labels = read_voc_images(voc_dir, is_train=is_train)\n", "        self.features = [self.normalize_image(feature)\n", "                         for feature in self.filter(features)]\n", "        self.labels = self.filter(labels)\n", "        self.colormap2label = voc_colormap2label()\n", "        print('read ' + str(len(self.features)) + ' examples')\n", "\n", "    def normalize_image(self, img):\n", "        return self.transform(img.float() / 255)\n", "\n", "    def filter(self, imgs):\n", "        return [img for img in imgs if (\n", "            img.shape[1] >= self.crop_size[0] and\n", "            img.shape[2] >= self.crop_size[1])]\n", "\n", "    def __getitem__(self, idx):\n", "        feature, label = voc_rand_crop(self.features[idx], self.labels[idx],\n", "                                       *self.crop_size)\n", "        return (feature, voc_label_indices(label, self.colormap2label))\n", "\n", "    def __len__(self):\n", "        return len(self.features)"]}, {"cell_type": "markdown", "id": "ab3dad12", "metadata": {"origin_pos": 26}, "source": ["### [**Reading the Dataset**]\n", "\n", "We use the custom `VOCSegDatase`t class to\n", "create instances of the training set and test set, respectively.\n", "Suppose that\n", "we specify that the output shape of randomly cropped images is $320\\times 480$.\n", "Below we can view the number of examples\n", "that are retained in the training set and test set.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "1f4edd04", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:40.663670Z", "iopub.status.busy": "2023-08-18T19:29:40.662945Z", "iopub.status.idle": "2023-08-18T19:29:53.646250Z", "shell.execute_reply": "2023-08-18T19:29:53.644966Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["read 1114 examples\n"]}, {"name": "stdout", "output_type": "stream", "text": ["read 1078 examples\n"]}], "source": ["crop_size = (320, 480)\n", "voc_train = VOCSegDataset(True, crop_size, voc_dir)\n", "voc_test = VOCSegDataset(False, crop_size, voc_dir)"]}, {"cell_type": "markdown", "id": "e7e1d5cc", "metadata": {"origin_pos": 28}, "source": ["Setting the batch size to 64,\n", "we define the data iterator for the training set.\n", "Let's print the shape of the first minibatch.\n", "Different from in image classification or object detection, labels here are three-dimensional tensors.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "a288fbec", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:53.652635Z", "iopub.status.busy": "2023-08-18T19:29:53.651744Z", "iopub.status.idle": "2023-08-18T19:29:55.162566Z", "shell.execute_reply": "2023-08-18T19:29:55.161082Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([64, 3, 320, 480])\n", "torch.<PERSON><PERSON>([64, 320, 480])\n"]}], "source": ["batch_size = 64\n", "train_iter = torch.utils.data.DataLoader(voc_train, batch_size, shuffle=True,\n", "                                    drop_last=True,\n", "                                    num_workers=d2l.get_dataloader_workers())\n", "for X, Y in train_iter:\n", "    print(X.shape)\n", "    print(Y.shape)\n", "    break"]}, {"cell_type": "markdown", "id": "c083c65b", "metadata": {"origin_pos": 31}, "source": ["### [**Putting It All Together**]\n", "\n", "Finally, we define the following `load_data_voc` function\n", "to download and read the Pascal VOC2012 semantic segmentation dataset.\n", "It returns data iterators for both the training and test datasets.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "59659ab3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:55.168292Z", "iopub.status.busy": "2023-08-18T19:29:55.167653Z", "iopub.status.idle": "2023-08-18T19:29:55.174558Z", "shell.execute_reply": "2023-08-18T19:29:55.173725Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def load_data_voc(batch_size, crop_size):\n", "    \"\"\"Load the VOC semantic segmentation dataset.\"\"\"\n", "    voc_dir = d2l.download_extract('voc2012', os.path.join(\n", "        'VOCdevkit', 'VOC2012'))\n", "    num_workers = d2l.get_dataloader_workers()\n", "    train_iter = torch.utils.data.DataLoader(\n", "        VOCSegDataset(True, crop_size, voc_dir), batch_size,\n", "        shuffle=True, drop_last=True, num_workers=num_workers)\n", "    test_iter = torch.utils.data.DataLoader(\n", "        VOCSegDataset(False, crop_size, voc_dir), batch_size,\n", "        drop_last=True, num_workers=num_workers)\n", "    return train_iter, test_iter"]}, {"cell_type": "markdown", "id": "7ad0f20c", "metadata": {"origin_pos": 34}, "source": ["## Summary\n", "\n", "* Semantic segmentation recognizes and understands what are in an image in pixel level by dividing the image into regions belonging to different semantic classes.\n", "* One of the most important semantic segmentation dataset is Pascal VOC2012.\n", "* In semantic segmentation, since the input image and  label correspond one-to-one on the pixel, the input image is randomly cropped to a fixed shape rather than rescaled.\n", "\n", "\n", "## Exercises\n", "\n", "1. How can semantic segmentation be applied in autonomous vehicles and medical image diagnostics? Can you think of other applications?\n", "1. Recall the descriptions of data augmentation in :numref:`sec_image_augmentation`. Which of the image augmentation methods used in image classification would be infeasible to be applied in semantic segmentation?\n"]}, {"cell_type": "markdown", "id": "b6a9c6f8", "metadata": {"origin_pos": 36, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1480)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
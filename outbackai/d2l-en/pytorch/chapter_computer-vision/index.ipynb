{"cells": [{"cell_type": "markdown", "id": "95c6ad63", "metadata": {"origin_pos": 0}, "source": ["# Computer Vision\n", ":label:`chap_cv`\n", "\n", "Whether it is medical diagnosis, self-driving vehicles, camera monitoring, or smart filters, many applications in the field of computer vision are closely related to our current and future lives. \n", "In recent years, deep learning has been\n", "the transformative power for advancing the performance of computer vision systems.\n", "It can be said that the most advanced computer vision applications are almost inseparable from deep learning.\n", "In view of this, this chapter will focus on the field of computer vision, and investigate methods and applications that have recently been influential in academia and industry.\n", "\n", "\n", "In :numref:`chap_cnn` and :numref:`chap_modern_cnn`, we studied various convolutional neural networks that are\n", "commonly used in computer vision, and applied them\n", "to simple image classification tasks. \n", "At the beginning of this chapter, we will describe\n", "two methods that \n", "may improve model generalization, namely *image augmentation* and *fine-tuning*,\n", "and apply them to image classification. \n", "Since deep neural networks can effectively represent images in multiple levels, \n", "such layerwise representations have been successfully \n", "used in various computer vision tasks such as *object detection*, *semantic segmentation*, and *style transfer*. \n", "Following the key idea of leveraging layerwise representations in computer vision,\n", "we will begin with major components and techniques for object detection. Next, we will show how to use *fully convolutional networks* for semantic segmentation of images. Then we will explain how to use style transfer techniques to generate images like the cover of this book.\n", "In the end, we conclude this chapter\n", "by applying the materials of this chapter and several previous chapters on two popular computer vision benchmark datasets.\n", "\n", ":begin_tab:toc\n", " - [image-augmentation](image-augmentation.ipynb)\n", " - [fine-tuning](fine-tuning.ipynb)\n", " - [bounding-box](bounding-box.ipynb)\n", " - [anchor](anchor.ipynb)\n", " - [multiscale-object-detection](multiscale-object-detection.ipynb)\n", " - [object-detection-dataset](object-detection-dataset.ipynb)\n", " - [ssd](ssd.ipynb)\n", " - [rcnn](rcnn.ipynb)\n", " - [semantic-segmentation-and-dataset](semantic-segmentation-and-dataset.ipynb)\n", " - [transposed-conv](transposed-conv.ipynb)\n", " - [fcn](fcn.ipynb)\n", " - [neural-style](neural-style.ipynb)\n", " - [kaggle-cifar10](kaggle-cifar10.ipynb)\n", " - [kaggle-dog](kaggle-dog.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
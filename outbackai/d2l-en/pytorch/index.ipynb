{"cells": [{"cell_type": "markdown", "id": "2793515b", "metadata": {"origin_pos": 0}, "source": ["Dive into Deep Learning\n", "========================\n", "\n", "```eval_rst\n", ".. raw:: html\n", "   :file: frontpage.html\n", "```\n", "\n", ":begin_tab:toc\n", " - [chapter_preface/index](chapter_preface/index.ipynb)\n", " - [chapter_installation/index](chapter_installation/index.ipynb)\n", " - [chapter_notation/index](chapter_notation/index.ipynb)\n", ":end_tab:\n", "\n", ":begin_tab:toc\n", " - [chapter_introduction/index](chapter_introduction/index.ipynb)\n", " - [chapter_preliminaries/index](chapter_preliminaries/index.ipynb)\n", " - [chapter_linear-regression/index](chapter_linear-regression/index.ipynb)\n", " - [chapter_linear-classification/index](chapter_linear-classification/index.ipynb)\n", " - [chapter_multilayer-perceptrons/index](chapter_multilayer-perceptrons/index.ipynb)\n", " - [chapter_builders-guide/index](chapter_builders-guide/index.ipynb)\n", " - [chapter_convolutional-neural-networks/index](chapter_convolutional-neural-networks/index.ipynb)\n", " - [chapter_convolutional-modern/index](chapter_convolutional-modern/index.ipynb)\n", " - [chapter_recurrent-neural-networks/index](chapter_recurrent-neural-networks/index.ipynb)\n", " - [chapter_recurrent-modern/index](chapter_recurrent-modern/index.ipynb)\n", " - [chapter_attention-mechanisms-and-transformers/index](chapter_attention-mechanisms-and-transformers/index.ipynb)\n", " - [chapter_optimization/index](chapter_optimization/index.ipynb)\n", " - [chapter_computational-performance/index](chapter_computational-performance/index.ipynb)\n", " - [chapter_computer-vision/index](chapter_computer-vision/index.ipynb)\n", " - [chapter_natural-language-processing-pretraining/index](chapter_natural-language-processing-pretraining/index.ipynb)\n", " - [chapter_natural-language-processing-applications/index](chapter_natural-language-processing-applications/index.ipynb)\n", " - [chapter_reinforcement-learning/index](chapter_reinforcement-learning/index.ipynb)\n", " - [chapter_gaussian-processes/index](chapter_gaussian-processes/index.ipynb)\n", " - [chapter_hyperparameter-optimization/index](chapter_hyperparameter-optimization/index.ipynb)\n", " - [chapter_generative-adversarial-networks/index](chapter_generative-adversarial-networks/index.ipynb)\n", " - [chapter_recommender-systems/index](chapter_recommender-systems/index.ipynb)\n", " - [chapter_appendix-mathematics-for-deep-learning/index](chapter_appendix-mathematics-for-deep-learning/index.ipynb)\n", " - [chapter_appendix-tools-for-deep-learning/index](chapter_appendix-tools-for-deep-learning/index.ipynb)\n", ":end_tab:\n", "\n", ":begin_tab:toc\n", " - [chapter_references/zreferences](chapter_references/zreferences.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
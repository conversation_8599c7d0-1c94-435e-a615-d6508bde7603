{"cells": [{"cell_type": "markdown", "id": "741b627f", "metadata": {"origin_pos": 0}, "source": ["# Language Models\n", ":label:`sec_language-model`\n", "\n", "In :numref:`sec_text-sequence`, we saw how to map text sequences into tokens, where these tokens can be viewed as a sequence of discrete observations such as words or characters. Assume that the tokens in a text sequence of length $T$ are in turn $x_1, x_2, \\ldots, x_T$.\n", "The goal of *language models*\n", "is to estimate the joint probability of the whole sequence:\n", "\n", "$$P(x_1, x_2, \\ldots, x_T),$$\n", "\n", "where statistical tools\n", "in :numref:`sec_sequence`\n", "can be applied.\n", "\n", "Language models are incredibly useful. For instance, an ideal language model should generate natural text on its own, simply by drawing one token at a time $x_t \\sim P(x_t \\mid x_{t-1}, \\ldots, x_1)$.\n", "Quite unlike the monkey using a typewriter, all text emerging from such a model would pass as natural language, e.g., English text. Furthermore, it would be sufficient for generating a meaningful dialog, simply by conditioning the text on previous dialog fragments.\n", "Clearly we are still very far from designing such a system, since it would need to *understand* the text rather than just generate grammatically sensible content.\n", "\n", "Nonetheless, language models are of great service even in their limited form.\n", "For instance, the phrases \"to recognize speech\" and \"to wreck a nice beach\" sound very similar.\n", "This can cause ambiguity in speech recognition,\n", "which is easily resolved through a language model that rejects the second translation as outlandish.\n", "Likewise, in a document summarization algorithm\n", "it is worthwhile knowing that \"dog bites man\" is much more frequent than \"man bites dog\", or that \"I want to eat grandma\" is a rather disturbing statement, whereas \"I want to eat, grandma\" is much more benign.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "8b60dead", "metadata": {"attributes": {"classes": [], "id": "", "n": "3"}, "execution": {"iopub.execute_input": "2023-08-18T19:28:41.375771Z", "iopub.status.busy": "2023-08-18T19:28:41.375138Z", "iopub.status.idle": "2023-08-18T19:28:44.315880Z", "shell.execute_reply": "2023-08-18T19:28:44.314922Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "54c6ae2a", "metadata": {"origin_pos": 6}, "source": ["## Learning Language Models\n", "\n", "The obvious question is how we should model a document, or even a sequence of tokens. \n", "Suppose that we tokenize text data at the word level.\n", "Let's start by applying basic probability rules:\n", "\n", "$$P(x_1, x_2, \\ldots, x_T) = \\prod_{t=1}^T P(x_t  \\mid  x_1, \\ldots, x_{t-1}).$$\n", "\n", "For example, \n", "the probability of a text sequence containing four words would be given as:\n", "\n", "$$\\begin{aligned}&P(\\textrm{deep}, \\textrm{learning}, \\textrm{is}, \\textrm{fun}) \\\\\n", "=&P(\\textrm{deep}) P(\\textrm{learning}  \\mid  \\textrm{deep}) P(\\textrm{is}  \\mid  \\textrm{deep}, \\textrm{learning}) P(\\textrm{fun}  \\mid  \\textrm{deep}, \\textrm{learning}, \\textrm{is}).\\end{aligned}$$\n", "\n", "### Markov Models and $n$-grams\n", ":label:`subsec_markov-models-and-n-grams`\n", "\n", "Among those sequence model analyses in :numref:`sec_sequence`,\n", "let's apply Markov models to language modeling.\n", "A distribution over sequences satisfies the Markov property of first order if $P(x_{t+1} \\mid x_t, \\ldots, x_1) = P(x_{t+1} \\mid x_t)$. Higher orders correspond to longer dependencies. This leads to a number of approximations that we could apply to model a sequence:\n", "\n", "$$\n", "\\begin{aligned}\n", "P(x_1, x_2, x_3, x_4) &=  P(x_1) P(x_2) P(x_3) P(x_4),\\\\\n", "P(x_1, x_2, x_3, x_4) &=  P(x_1) P(x_2  \\mid  x_1) P(x_3  \\mid  x_2) P(x_4  \\mid  x_3),\\\\\n", "P(x_1, x_2, x_3, x_4) &=  P(x_1) P(x_2  \\mid  x_1) P(x_3  \\mid  x_1, x_2) P(x_4  \\mid  x_2, x_3).\n", "\\end{aligned}\n", "$$\n", "\n", "The probability formulae that involve one, two, and three variables are typically referred to as *unigram*, *bigram*, and *trigram* models, respectively. \n", "In order to compute the language model, we need to calculate the\n", "probability of words and the conditional probability of a word given\n", "the previous few words.\n", "Note that\n", "such probabilities are\n", "language model parameters.\n", "\n", "\n", "\n", "### Word Frequency\n", "\n", "Here, we\n", "assume that the training dataset is a large text corpus, such as all\n", "Wikipedia entries, [<PERSON>](https://en.wikipedia.org/wiki/<PERSON>_<PERSON>),\n", "and all text posted on the\n", "web.\n", "The probability of words can be calculated from the relative word\n", "frequency of a given word in the training dataset.\n", "For example, the estimate $\\hat{P}(\\textrm{deep})$ can be calculated as the\n", "probability of any sentence starting with the word \"deep\". A\n", "slightly less accurate approach would be to count all occurrences of\n", "the word \"deep\" and divide it by the total number of words in\n", "the corpus.\n", "This works fairly well, particularly for frequent\n", "words. Moving on, we could attempt to estimate\n", "\n", "$$\\hat{P}(\\textrm{learning} \\mid \\textrm{deep}) = \\frac{n(\\textrm{deep, learning})}{n(\\textrm{deep})},$$\n", "\n", "where $n(x)$ and $n(x, x')$ are the number of occurrences of singletons\n", "and consecutive word pairs, respectively.\n", "Unfortunately, \n", "estimating the\n", "probability of a word pair is somewhat more difficult, since the\n", "occurrences of \"deep learning\" are a lot less frequent. \n", "In particular, for some unusual word combinations it may be tricky to\n", "find enough occurrences to get accurate estimates.\n", "As suggested by the empirical results in :numref:`subsec_natural-lang-stat`,\n", "things take a turn for the worse for three-word combinations and beyond.\n", "There will be many plausible three-word combinations that we likely will not see in our dataset.\n", "Unless we provide some solution to assign such word combinations a nonzero count, we will not be able to use them in a language model. If the dataset is small or if the words are very rare, we might not find even a single one of them.\n", "\n", "### <PERSON><PERSON>moothing\n", "\n", "A common strategy is to perform some form of *Laplace smoothing*.\n", "The solution is to\n", "add a small constant to all counts. \n", "Denote by $n$ the total number of words in\n", "the training set\n", "and $m$ the number of unique words.\n", "This solution helps with singletons, e.g., via\n", "\n", "$$\\begin{aligned}\n", "\t\\hat{P}(x) & = \\frac{n(x) + \\epsilon_1/m}{n + \\epsilon_1}, \\\\\n", "\t\\hat{P}(x' \\mid x) & = \\frac{n(x, x') + \\epsilon_2 \\hat{P}(x')}{n(x) + \\epsilon_2}, \\\\\n", "\t\\hat{P}(x'' \\mid x,x') & = \\frac{n(x, x',x'') + \\epsilon_3 \\hat{P}(x'')}{n(x, x') + \\epsilon_3}.\n", "\\end{aligned}$$\n", "\n", "Here $\\epsilon_1,\\epsilon_2$, and $\\epsilon_3$ are hyperparameters.\n", "Take $\\epsilon_1$ as an example:\n", "when $\\epsilon_1 = 0$, no smoothing is applied;\n", "when $\\epsilon_1$ approaches positive infinity,\n", "$\\hat{P}(x)$ approaches the uniform probability $1/m$. \n", "The above is a rather primitive variant of what\n", "other techniques can accomplish :cite:`<PERSON>.Gasthaus.Archambeau.ea.2011`.\n", "\n", "\n", "Unfortunately, models like this get unwieldy rather quickly\n", "for the following reasons. \n", "First, \n", "as discussed in :numref:`subsec_natural-lang-stat`,\n", "many $n$-grams occur very rarely, \n", "making Laplace smoothing rather unsuitable for language modeling.\n", "Second, we need to store all counts.\n", "Third, this entirely ignores the meaning of the words. For\n", "instance, \"cat\" and \"feline\" should occur in related contexts.\n", "It is quite difficult to adjust such models to additional contexts,\n", "whereas, deep learning based language models are well suited to\n", "take this into account.\n", "Last, long word\n", "sequences are almost certain to be novel, hence a model that simply\n", "counts the frequency of previously seen word sequences is bound to perform poorly there.\n", "Therefore, we focus on using neural networks for language modeling\n", "in the rest of the chapter.\n", "\n", "\n", "## Perplexity\n", ":label:`subsec_perplexity`\n", "\n", "Next, let's discuss about how to measure the quality of the language model, which we will then use to evaluate our models in the subsequent sections.\n", "One way is to check how surprising the text is.\n", "A good language model is able to predict, with high accuracy, the tokens that come next.\n", "Consider the following continuations of the phrase \"It is raining\", as proposed by different language models:\n", "\n", "1. \"It is raining outside\"\n", "1. \"It is raining banana tree\"\n", "1. \"It is raining piouw;kcj pwe<PERSON>iut\"\n", "\n", "In terms of quality, Example 1 is clearly the best. The words are sensible and logically coherent.\n", "While it might not quite accurately reflect which word follows semantically (\"in San Francisco\" and \"in winter\" would have been perfectly reasonable extensions), the model is able to capture which kind of word follows.\n", "Example 2 is considerably worse by producing a nonsensical extension. Nonetheless, at least the model has learned how to spell words and some degree of correlation between words. Last, Example 3 indicates a poorly trained model that does not fit data properly.\n", "\n", "We might measure the quality of the model by computing  the likelihood of the sequence.\n", "Unfortunately this is a number that is hard to understand and difficult to compare.\n", "After all, shorter sequences are much more likely to occur than the longer ones,\n", "hence evaluating the model on <PERSON><PERSON><PERSON><PERSON>'s magnum opus\n", "*War and Peace* will inevitably produce a much smaller likelihood than, say, on <PERSON><PERSON><PERSON><PERSON><PERSON>'s novella *The Little Prince*. What is missing is the equivalent of an average.\n", "\n", "Information theory comes handy here.\n", "We defined entropy, surprisal, and cross-entropy\n", "when we introduced the softmax regression\n", "(:numref:`subsec_info_theory_basics`).\n", "If we want to compress text, we can ask about\n", "predicting the next token given the current set of tokens.\n", "A better language model should allow us to predict the next token more accurately.\n", "Thus, it should allow us to spend fewer bits in compressing the sequence.\n", "So we can measure it by the cross-entropy loss averaged\n", "over all the $n$ tokens of a sequence:\n", "\n", "$$\\frac{1}{n} \\sum_{t=1}^n -\\log P(x_t \\mid x_{t-1}, \\ldots, x_1),$$\n", ":eqlabel:`eq_avg_ce_for_lm`\n", "\n", "where $P$ is given by a language model and $x_t$ is the actual token observed at time step $t$ from the sequence.\n", "This makes the performance on documents of different lengths comparable. For historical reasons, scientists in natural language processing prefer to use a quantity called *perplexity*. In a nutshell, it is the exponential of :eqref:`eq_avg_ce_for_lm`:\n", "\n", "$$\\exp\\left(-\\frac{1}{n} \\sum_{t=1}^n \\log P(x_t \\mid x_{t-1}, \\ldots, x_1)\\right).$$\n", "\n", "Perplexity can be best understood as the reciprocal of the geometric mean of the number of real choices that we have when deciding which token to pick next. Let's look at a number of cases:\n", "\n", "* In the best case scenario, the model always perfectly estimates the probability of the target token as 1. In this case the perplexity of the model is 1.\n", "* In the worst case scenario, the model always predicts the probability of the target token as 0. In this situation, the perplexity is positive infinity.\n", "* At the baseline, the model predicts a uniform distribution over all the available tokens of the vocabulary. In this case, the perplexity equals the number of unique tokens of the vocabulary. In fact, if we were to store the sequence without any compression, this would be the best we could do for encoding it. Hence, this provides a nontrivial upper bound that any useful model must beat.\n", "\n", "## Partitioning Sequences\n", ":label:`subsec_partitioning-seqs`\n", "\n", "We will design language models using neural networks\n", "and use perplexity to evaluate \n", "how good the model is at \n", "predicting the next token given the current set of tokens\n", "in text sequences.\n", "Before introducing the model,\n", "let's assume that it\n", "processes a minibatch of sequences with predefined length\n", "at a time.\n", "Now the question is how to [**read minibatches of input sequences and target sequences at random**].\n", "\n", "\n", "Suppose that the dataset takes the form of a sequence of $T$ token indices in `corpus`.\n", "We will\n", "partition it\n", "into subsequences, where each subsequence has $n$ tokens (time steps).\n", "To iterate over \n", "(almost) all the tokens of the entire dataset \n", "for each epoch\n", "and obtain all possible length-$n$ subsequences,\n", "we can introduce randomness.\n", "More concretely,\n", "at the beginning of each epoch,\n", "discard the first $d$ tokens,\n", "where $d\\in [0,n)$ is uniformly sampled at random.\n", "The rest of the sequence\n", "is then partitioned\n", "into $m=\\lfloor (T-d)/n \\rfloor$ subsequences.\n", "Denote by $\\mathbf x_t = [x_t, \\ldots, x_{t+n-1}]$ the length-$n$ subsequence starting from token $x_t$ at time step $t$. \n", "The resulting $m$ partitioned subsequences\n", "are \n", "$\\mathbf x_d, \\mathbf x_{d+n}, \\ldots, \\mathbf x_{d+n(m-1)}.$\n", "Each subsequence will be used as an input sequence into the language model.\n", "\n", "\n", "For language modeling,\n", "the goal is to predict the next token based on the tokens we have seen so far; hence the targets (labels) are the original sequence, shifted by one token.\n", "The target sequence for any input sequence $\\mathbf x_t$\n", "is $\\mathbf x_{t+1}$ with length $n$.\n", "\n", "![Obtaining five pairs of input sequences and target sequences from partitioned length-5 subsequences.](../img/lang-model-data.svg) \n", ":label:`fig_lang_model_data`\n", "\n", ":numref:`fig_lang_model_data` shows an example of obtaining five pairs of input sequences and target sequences with $n=5$ and $d=2$.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "a09a2d6b", "metadata": {"attributes": {"classes": [], "id": "", "n": "5"}, "execution": {"iopub.execute_input": "2023-08-18T19:28:44.321194Z", "iopub.status.busy": "2023-08-18T19:28:44.320794Z", "iopub.status.idle": "2023-08-18T19:28:44.326575Z", "shell.execute_reply": "2023-08-18T19:28:44.325777Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(d2l.TimeMachine)  #@save\n", "def __init__(self, batch_size, num_steps, num_train=10000, num_val=5000):\n", "    super(d2l.<PERSON><PERSON><PERSON>, self).__init__()\n", "    self.save_hyperparameters()\n", "    corpus, self.vocab = self.build(self._download())\n", "    array = torch.tensor([corpus[i:i+num_steps+1]\n", "                        for i in range(len(corpus)-num_steps)])\n", "    self.X, self.Y = array[:,:-1], array[:,1:]"]}, {"cell_type": "markdown", "id": "5d5393db", "metadata": {"origin_pos": 8}, "source": ["To train language models,\n", "we will randomly sample \n", "pairs of input sequences and target sequences\n", "in minibatches.\n", "The following data loader randomly generates a minibatch from the dataset each time.\n", "The argument `batch_size` specifies the number of subsequence examples in each minibatch\n", "and `num_steps` is the subsequence length in tokens.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "06c824e8", "metadata": {"attributes": {"classes": [], "id": "", "n": "6"}, "execution": {"iopub.execute_input": "2023-08-18T19:28:44.329887Z", "iopub.status.busy": "2023-08-18T19:28:44.329335Z", "iopub.status.idle": "2023-08-18T19:28:44.334517Z", "shell.execute_reply": "2023-08-18T19:28:44.333506Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(d2l.TimeMachine)  #@save\n", "def get_dataloader(self, train):\n", "    idx = slice(0, self.num_train) if train else slice(\n", "        self.num_train, self.num_train + self.num_val)\n", "    return self.get_tensorloader([self.X, self.Y], train, idx)"]}, {"cell_type": "markdown", "id": "d6608ee2", "metadata": {"origin_pos": 10}, "source": ["As we can see in the following, \n", "a minibatch of target sequences\n", "can be obtained \n", "by shifting the input sequences\n", "by one token.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "7cbf8e11", "metadata": {"attributes": {"classes": [], "id": "", "n": "7"}, "execution": {"iopub.execute_input": "2023-08-18T19:28:44.338600Z", "iopub.status.busy": "2023-08-18T19:28:44.338043Z", "iopub.status.idle": "2023-08-18T19:28:45.209913Z", "shell.execute_reply": "2023-08-18T19:28:45.208778Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/timemachine.txt from http://d2l-data.s3-accelerate.amazonaws.com/timemachine.txt...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["X: tensor([[10,  4,  2, 21, 10, 16, 15,  0, 20,  2],\n", "        [21,  9,  6, 19,  0, 24,  2, 26,  0, 16]]) \n", "Y: tensor([[ 4,  2, 21, 10, 16, 15,  0, 20,  2, 10],\n", "        [ 9,  6, 19,  0, 24,  2, 26,  0, 16,  9]])\n"]}], "source": ["data = d2l.TimeMachine(batch_size=2, num_steps=10)\n", "for X, Y in data.train_dataloader():\n", "    print('X:', X, '\\nY:', Y)\n", "    break"]}, {"cell_type": "markdown", "id": "7f4c8710", "metadata": {"origin_pos": 12}, "source": ["## Summary and Discussion\n", "\n", "Language models estimate the joint probability of a text sequence. For long sequences, $n$-grams provide a convenient model by truncating the dependence. However, there is a lot of structure but not enough frequency to deal efficiently with infrequent word combinations via Laplace smoothing. Thus, we will focus on neural language modeling in subsequent sections.\n", "To train language models, we can randomly sample pairs of input sequences and target sequences in minibatches. After training, we will use perplexity to measure the language model quality.\n", "\n", "Language models can be scaled up with increased data size, model size, and amount in training compute. Large language models can perform desired tasks by predicting output text given input text instructions. As we will discuss later (e.g., :numref:`sec_large-pretraining-transformers`),\n", "at the present moment\n", "large language models form the basis of state-of-the-art systems across diverse tasks.\n", "\n", "\n", "## Exercises\n", "\n", "1. Suppose there are 100,000 words in the training dataset. How much word frequency and multi-word adjacent frequency does a four-gram need to store?\n", "1. How would you model a dialogue?\n", "1. What other methods can you think of for reading long sequence data?\n", "1. Consider our method for discarding a uniformly random number of the first few tokens at the beginning of each epoch.\n", "    1. Does it really lead to a perfectly uniform distribution over the sequences on the document?\n", "    1. What would you have to do to make things even more uniform? \n", "1. If we want a sequence example to be a complete sentence, what kind of problem does this introduce in minibatch sampling? How can we fix it?\n"]}, {"cell_type": "markdown", "id": "59140a53", "metadata": {"origin_pos": 14, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/118)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": [], "kernelspec": {"name": "<PERSON><PERSON>", "language": "python", "display_name": "<PERSON> (aideep)"}}, "nbformat": 4, "nbformat_minor": 5}
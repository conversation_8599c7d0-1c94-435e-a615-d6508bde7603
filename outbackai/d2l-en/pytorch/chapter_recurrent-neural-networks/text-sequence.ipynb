{"cells": [{"cell_type": "markdown", "id": "bb24ed67", "metadata": {"origin_pos": 0}, "source": ["# Converting Raw Text into Sequence Data\n", ":label:`sec_text-sequence`\n", "\n", "Throughout this book,\n", "we will often work with text data\n", "represented as sequences\n", "of words, characters, or word pieces.\n", "To get going, we will need some basic\n", "tools for converting raw text\n", "into sequences of the appropriate form.\n", "Typical preprocessing pipelines\n", "execute the following steps:\n", "\n", "1. Load text as strings into memory.\n", "1. Split the strings into tokens (e.g., words or characters).\n", "1. Build a vocabulary dictionary to associate each vocabulary element with a numerical index.\n", "1. Convert the text into sequences of numerical indices.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "117b083a", "metadata": {"attributes": {"classes": [], "id": "", "n": "3"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:23.122018Z", "iopub.status.busy": "2023-08-18T19:29:23.121504Z", "iopub.status.idle": "2023-08-18T19:29:25.901709Z", "shell.execute_reply": "2023-08-18T19:29:25.900650Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import collections\n", "import random\n", "import re\n", "import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "08135c83", "metadata": {"origin_pos": 6}, "source": ["## Reading the Dataset\n", "\n", "Here, we will work with <PERSON><PERSON> <PERSON><PERSON>'\n", "[The Time Machine](http://www.gutenberg.org/ebooks/35),\n", "a book containing just over 30,000 words.\n", "While real applications will typically\n", "involve significantly larger datasets,\n", "this is sufficient to demonstrate\n", "the preprocessing pipeline.\n", "The following `_download` method\n", "(**reads the raw text into a string**).\n"]}, {"cell_type": "code", "execution_count": 8, "id": "dd560200", "metadata": {"attributes": {"classes": [], "id": "", "n": "5"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:25.906885Z", "iopub.status.busy": "2023-08-18T19:29:25.905685Z", "iopub.status.idle": "2023-08-18T19:29:25.919860Z", "shell.execute_reply": "2023-08-18T19:29:25.918500Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'The Time Machine, by <PERSON><PERSON> <PERSON><PERSON> [1898]\\n\\n\\n\\n\\nI\\n\\n\\nThe Time Tra'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["class TimeMachine(d2l.DataModule):  #@save\n", "    \"\"\"The Time Machine dataset.\"\"\"\n", "\n", "    def _download(self):\n", "        fname = d2l.download(d2l.DATA_URL + 'timemachine.txt', self.root,\n", "                             '090b5e7e70c295757f55df93cb0a180b9691891a')\n", "        with open(fname) as f:\n", "            return f.read()\n", "\n", "\n", "data = TimeMachine()\n", "raw_text = data._download()\n", "raw_text[:60]"]}, {"cell_type": "markdown", "id": "e20b206c", "metadata": {"origin_pos": 8}, "source": ["For simplicity, we ignore punctuation and capitalization when preprocessing the raw text.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "c4d26c1b", "metadata": {"attributes": {"classes": [], "id": "", "n": "6"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:25.925597Z", "iopub.status.busy": "2023-08-18T19:29:25.924754Z", "iopub.status.idle": "2023-08-18T19:29:25.959402Z", "shell.execute_reply": "2023-08-18T19:29:25.958183Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'the time machine by h g wells i the time traveller for so it'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["@d2l.add_to_class(TimeMachine)  #@save\n", "def _preprocess(self, text):\n", "    return re.sub('[^A-Za-z]+', ' ', text).lower()\n", "\n", "\n", "text = data._preprocess(raw_text)\n", "text[:60]"]}, {"cell_type": "markdown", "id": "e0d6cc11", "metadata": {"origin_pos": 10}, "source": ["## Tokenization\n", "\n", "*Tokens* are the atomic (indivisible) units of text.\n", "Each time step corresponds to 1 token,\n", "but what precisely constitutes a token is a design choice.\n", "For example, we could represent the sentence\n", "\"Baby needs a new pair of shoes\"\n", "as a sequence of 7 words,\n", "where the set of all words comprise\n", "a large vocabulary (typically tens\n", "or hundreds of thousands of words).\n", "Or we would represent the same sentence\n", "as a much longer sequence of 30 characters,\n", "using a much smaller vocabulary\n", "(there are only 256 distinct ASCII characters).\n", "Below, we tokenize our preprocessed text\n", "into a sequence of characters.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "dc35eee3", "metadata": {"attributes": {"classes": [], "id": "", "n": "7"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:25.963957Z", "iopub.status.busy": "2023-08-18T19:29:25.962999Z", "iopub.status.idle": "2023-08-18T19:29:25.975230Z", "shell.execute_reply": "2023-08-18T19:29:25.974102Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'t,h,e, ,t,i,m,e, ,m,a,c,h,i,n,e, ,b,y, ,h, ,g, ,w,e,l,l,s, '"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["@d2l.add_to_class(TimeMachine)  #@save\n", "def _tokenize(self, text):\n", "    return list(text)\n", "\n", "\n", "tokens = data._tokenize(text)\n", "','.join(tokens[:30])"]}, {"cell_type": "markdown", "id": "cc014b66", "metadata": {"origin_pos": 12}, "source": ["## Vocabulary\n", "\n", "These tokens are still strings.\n", "However, the inputs to our models\n", "must ultimately consist\n", "of numerical inputs.\n", "[**Next, we introduce a class\n", "for constructing *vocabularies*,\n", "i.e., objects that associate\n", "each distinct token value\n", "with a unique index.**]\n", "First, we determine the set of unique tokens in our training *corpus*.\n", "We then assign a numerical index to each unique token.\n", "Rare vocabulary elements are often dropped for convenience.\n", "Whenever we encounter a token at training or test time\n", "that had not been previously seen or was dropped from the vocabulary,\n", "we represent it by a special \"&lt;unk&gt;\" token,\n", "signifying that this is an *unknown* value.\n"]}, {"cell_type": "code", "id": "820d7cff", "metadata": {"attributes": {"classes": [], "id": "", "n": "8"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:25.979690Z", "iopub.status.busy": "2023-08-18T19:29:25.978764Z", "iopub.status.idle": "2023-08-18T19:29:25.995117Z", "shell.execute_reply": "2023-08-18T19:29:25.994009Z"}, "origin_pos": 13, "tab": ["pytorch"], "ExecuteTime": {"end_time": "2025-03-31T13:41:24.370742Z", "start_time": "2025-03-31T13:41:24.357758Z"}}, "source": ["class Vocab:  #@save\n", "    \"\"\"Vocabulary for text.\"\"\"\n", "\n", "    def __init__(self, tokens=[], min_freq=0, reserved_tokens=[]):\n", "        # Flatten a 2D list if needed\n", "        if tokens and isinstance(tokens[0], list):\n", "            tokens = [token for line in tokens for token in line]\n", "        # Count token frequencies\n", "        counter = collections.Counter(tokens)\n", "        self.token_freqs = sorted(counter.items(), key=lambda x: x[1],\n", "                                  reverse=True)\n", "        # The list of unique tokens\n", "        self.idx_to_token = list(sorted(set(['<unk>'] + reserved_tokens + [\n", "            token for token, freq in self.token_freqs if freq >= min_freq])))\n", "        self.token_to_idx = {token: idx\n", "                             for idx, token in enumerate(self.idx_to_token)}\n", "\n", "    def __len__(self):\n", "        return len(self.idx_to_token)\n", "\n", "    def __getitem__(self, tokens):\n", "        if not isinstance(tokens, (list, tuple)):\n", "            return self.token_to_idx.get(tokens, self.unk)\n", "        return [self.__getitem__(token) for token in tokens]\n", "\n", "    def to_tokens(self, indices):\n", "        if hasattr(indices, '__len__') and len(indices) > 1:\n", "            return [self.idx_to_token[int(index)] for index in indices]\n", "        return self.idx_to_token[indices]\n", "\n", "    @property\n", "    def unk(self):  # Index for the unknown token\n", "        return self.token_to_idx['<unk>']"], "outputs": [], "execution_count": 1}, {"cell_type": "markdown", "id": "6aa8b5db", "metadata": {"origin_pos": 14}, "source": ["We now [**construct a vocabulary**] for our dataset,\n", "converting the sequence of strings\n", "into a list of numerical indices.\n", "Note that we have not lost any information\n", "and can easily convert our dataset\n", "back to its original (string) representation.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "da288532", "metadata": {"attributes": {"classes": [], "id": "", "n": "9"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:25.999437Z", "iopub.status.busy": "2023-08-18T19:29:25.998566Z", "iopub.status.idle": "2023-08-18T19:29:26.019939Z", "shell.execute_reply": "2023-08-18T19:29:26.019020Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["indices: [21, 9, 6, 0, 21, 10, 14, 6, 0, 14]\n", "words: ['t', 'h', 'e', ' ', 't', 'i', 'm', 'e', ' ', 'm']\n"]}], "source": ["vocab = Vocab(tokens)\n", "indices = vocab[tokens[:10]]\n", "print('indices:', indices)\n", "print('words:', vocab.to_tokens(indices))"]}, {"cell_type": "markdown", "id": "2be48414", "metadata": {"origin_pos": 16}, "source": ["## Putting It All Together\n", "\n", "Using the above classes and methods,\n", "we [**package everything into the following\n", "`build` method of the `TimeMachine` class**],\n", "which returns `corpus`, a list of token indices, and `vocab`,\n", "the vocabulary of *The Time Machine* corpus.\n", "The modifications we did here are:\n", "(i) we tokenize text into characters, not words,\n", "to simplify the training in later sections;\n", "(ii) `corpus` is a single list, not a list of token lists,\n", "since each text line in *The Time Machine* dataset\n", "is not necessarily a sentence or paragraph.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "b19fc5b8", "metadata": {"attributes": {"classes": [], "id": "", "n": "10"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:26.023503Z", "iopub.status.busy": "2023-08-18T19:29:26.022928Z", "iopub.status.idle": "2023-08-18T19:29:26.127518Z", "shell.execute_reply": "2023-08-18T19:29:26.126623Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(173428, 28)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["@d2l.add_to_class(TimeMachine)  #@save\n", "def build(self, raw_text, vocab=None):\n", "    tokens = self._tokenize(self._preprocess(raw_text))\n", "    if vocab is None: vocab = Vocab(tokens)\n", "    corpus = [vocab[token] for token in tokens]\n", "    return corpus, vocab\n", "\n", "\n", "corpus, vocab = data.build(raw_text)\n", "len(corpus), len(vocab)"]}, {"cell_type": "markdown", "id": "a9ca0946", "metadata": {"origin_pos": 18}, "source": ["## Exploratory Language Statistics\n", ":label:`subsec_natural-lang-stat`\n", "\n", "Using the real corpus and the `Vocab` class defined over words,\n", "we can inspect basic statistics concerning word use in our corpus.\n", "Below, we construct a vocabulary from words used in *The Time Machine*\n", "and print the ten most frequently occurring of them.\n"]}, {"cell_type": "code", "execution_count": 14, "id": "b5985d60", "metadata": {"attributes": {"classes": [], "id": "", "n": "11"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:26.131046Z", "iopub.status.busy": "2023-08-18T19:29:26.130467Z", "iopub.status.idle": "2023-08-18T19:29:26.147044Z", "shell.execute_reply": "2023-08-18T19:29:26.146169Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["[('the', 2261),\n", " ('i', 1267),\n", " ('and', 1245),\n", " ('of', 1155),\n", " ('a', 816),\n", " ('to', 695),\n", " ('was', 552),\n", " ('in', 541),\n", " ('that', 443),\n", " ('my', 440)]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["words = text.split()\n", "vocab = Vocab(words)\n", "vocab.token_freqs[:10]"]}, {"cell_type": "markdown", "id": "921d6c1b", "metadata": {"origin_pos": 20}, "source": ["Note that (**the ten most frequent words**)\n", "are not all that descriptive.\n", "You might even imagine that\n", "we might see a very similar list\n", "if we had chosen any book at random.\n", "Articles like \"the\" and \"a\",\n", "pronouns like \"i\" and \"my\",\n", "and prepositions like \"of\", \"to\", and \"in\"\n", "occur often because they serve common syntactic roles.\n", "Such words that are common but not particularly descriptive\n", "are often called (***stop words***) and,\n", "in previous generations of text classifiers\n", "based on so-called bag-of-words representations,\n", "they were most often filtered out.\n", "However, they carry meaning and\n", "it is not necessary to filter them out\n", "when working with modern RNN- and\n", "Transformer-based neural models.\n", "If you look further down the list,\n", "you will notice\n", "that word frequency decays quickly.\n", "The $10^{\\textrm{th}}$ most frequent word\n", "is less than $1/5$ as common as the most popular.\n", "Word frequency tends to follow a power law distribution\n", "(specifically the Zipfian) as we go down the ranks.\n", "To get a better idea, we [**plot the figure of the word frequency**].\n"]}, {"cell_type": "code", "execution_count": 15, "id": "1017fe5b", "metadata": {"attributes": {"classes": [], "id": "", "n": "12"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:26.150706Z", "iopub.status.busy": "2023-08-18T19:29:26.149958Z", "iopub.status.idle": "2023-08-18T19:29:27.117808Z", "shell.execute_reply": "2023-08-18T19:29:27.116986Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"247.978125pt\" height=\"183.35625pt\" viewBox=\"0 0 247.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-31T21:29:03.918928</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 247.**********.35625 \n", "L 247.978125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 45.**********.8 \n", "L 240.**********.8 \n", "L 240.778125 7.2 \n", "L 45.478125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 54.355398 145.8 \n", "L 54.355398 7.2 \n", "\" clip-path=\"url(#peccb14804d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m9a8e9d78fa\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9a8e9d78fa\" x=\"54.355398\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- $\\mathdefault{10^{0}}$ -->\n", "      <g transform=\"translate(45.555398 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 102.85613 145.8 \n", "L 102.85613 7.2 \n", "\" clip-path=\"url(#peccb14804d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m9a8e9d78fa\" x=\"102.85613\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(94.05613 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 151.356861 145.8 \n", "L 151.356861 7.2 \n", "\" clip-path=\"url(#peccb14804d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m9a8e9d78fa\" x=\"151.356861\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(142.556861 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 199.857593 145.8 \n", "L 199.857593 7.2 \n", "\" clip-path=\"url(#peccb14804d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m9a8e9d78fa\" x=\"199.857593\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- $\\mathdefault{10^{3}}$ -->\n", "      <g transform=\"translate(191.057593 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <defs>\n", "       <path id=\"m70d3b00ec0\" d=\"M 0 0 \n", "L 0 2 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"46.842539\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"49.655191\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"52.136126\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"68.955573\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"77.496128\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"83.555748\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"88.255954\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"92.096303\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"95.343271\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"98.155923\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"100.636858\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"117.456305\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_17\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"125.99686\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_18\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"132.05648\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_19\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"136.756686\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_20\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"140.597035\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_21\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"143.844003\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_22\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"146.656655\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_23\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"149.13759\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_24\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"165.957036\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_25\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"174.497591\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_26\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"180.557211\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_27\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"185.257418\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_28\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"189.097766\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_29\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"192.344735\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_30\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"195.157387\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_31\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"197.638321\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_32\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"214.457768\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_33\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"222.998323\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_34\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"229.057943\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_35\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"233.75815\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_36\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#m70d3b00ec0\" x=\"237.598498\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- token: x -->\n", "     <g transform=\"translate(122.916406 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-3a\" d=\"M 750 794 \n", "L 1409 794 \n", "L 1409 0 \n", "L 750 0 \n", "L 750 794 \n", "z\n", "M 750 3309 \n", "L 1409 3309 \n", "L 1409 2516 \n", "L 750 2516 \n", "L 750 3309 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"100.390625\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"154.675781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"216.199219\"/>\n", "      <use xlink:href=\"#DejaVuSans-3a\" x=\"279.578125\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"313.269531\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"345.056641\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_41\">\n", "      <path d=\"M 45.478125 139.5 \n", "L 240.778125 139.5 \n", "\" clip-path=\"url(#peccb14804d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_42\">\n", "      <defs>\n", "       <path id=\"m76b3ab81eb\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m76b3ab81eb\" x=\"45.478125\" y=\"139.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- $\\mathdefault{10^{0}}$ -->\n", "      <g transform=\"translate(20.878125 143.299219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_43\">\n", "      <path d=\"M 45.478125 101.936282 \n", "L 240.778125 101.936282 \n", "\" clip-path=\"url(#peccb14804d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#m76b3ab81eb\" x=\"45.478125\" y=\"101.936282\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(20.878125 105.735501) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_45\">\n", "      <path d=\"M 45.478125 64.372564 \n", "L 240.778125 64.372564 \n", "\" clip-path=\"url(#peccb14804d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#m76b3ab81eb\" x=\"45.478125\" y=\"64.372564\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(20.878125 68.171783) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_47\">\n", "      <path d=\"M 45.478125 26.808846 \n", "L 240.778125 26.808846 \n", "\" clip-path=\"url(#peccb14804d)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#m76b3ab81eb\" x=\"45.478125\" y=\"26.808846\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- $\\mathdefault{10^{3}}$ -->\n", "      <g transform=\"translate(20.878125 30.608065) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_49\">\n", "      <defs>\n", "       <path id=\"mfa6cc112fa\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"145.318694\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"143.1403\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_51\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"141.218821\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"128.192194\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_53\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"121.577552\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"116.884388\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_55\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"113.244088\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"110.269746\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_57\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"107.754976\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"105.576583\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_59\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"103.655104\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_60\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"90.628476\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_61\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"84.013834\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_62\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"79.32067\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_63\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"75.68037\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_64\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"72.706028\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_65\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"70.191258\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_66\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"68.012865\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_67\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"66.091386\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_68\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"53.064758\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_69\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"46.450116\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_70\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"41.756953\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_71\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"38.116652\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_72\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"35.14231\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_29\">\n", "     <g id=\"line2d_73\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"32.62754\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_30\">\n", "     <g id=\"line2d_74\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"30.449147\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_31\">\n", "     <g id=\"line2d_75\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"28.527668\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_32\">\n", "     <g id=\"line2d_76\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"15.501041\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_33\">\n", "     <g id=\"line2d_77\">\n", "      <g>\n", "       <use xlink:href=\"#mfa6cc112fa\" x=\"45.478125\" y=\"8.886398\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- frequency: n(x) -->\n", "     <g transform=\"translate(14.798438 114.517188) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-71\" d=\"M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "M 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 -1331 \n", "L 2906 -1331 \n", "L 2906 525 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-66\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"35.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"74.068359\"/>\n", "      <use xlink:href=\"#DejaVuSans-71\" x=\"135.591797\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"199.068359\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"262.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"323.970703\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"387.349609\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"442.330078\"/>\n", "      <use xlink:href=\"#DejaVuSans-3a\" x=\"494.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"527.951172\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"559.738281\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"623.117188\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"662.130859\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"721.310547\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M -1 22.937391 \n", "L 54.355398 22.948175 \n", "L 68.955573 23.233932 \n", "L 77.496128 24.458034 \n", "L 83.555748 30.126092 \n", "L 88.255954 32.744485 \n", "L 92.096303 36.502574 \n", "L 95.343271 36.830949 \n", "L 98.155923 40.091237 \n", "L 102.85613 40.3137 \n", "L 104.863705 43.749961 \n", "L 106.696478 47.517487 \n", "L 108.382465 48.168938 \n", "L 111.396684 51.43591 \n", "L 112.756098 51.809238 \n", "L 114.033071 52.741704 \n", "L 115.237033 53.064758 \n", "L 116.375883 56.910265 \n", "L 117.456305 57.541843 \n", "L 118.484001 59.236831 \n", "L 119.46388 59.236831 \n", "L 120.400194 59.598035 \n", "L 121.296653 60.218404 \n", "L 122.156511 60.473311 \n", "L 122.982641 60.995394 \n", "L 123.777588 61.128568 \n", "L 125.282771 61.128568 \n", "L 126.687531 61.672409 \n", "L 127.356273 62.235006 \n", "L 128.004435 62.235006 \n", "L 128.633246 62.378741 \n", "L 129.243828 62.378741 \n", "L 129.837208 62.523752 \n", "L 130.414329 63.421982 \n", "L 130.976058 63.576615 \n", "L 131.523195 63.89035 \n", "L 132.05648 65.381982 \n", "L 133.084176 65.732828 \n", "L 133.579813 65.732828 \n", "L 134.064055 65.911122 \n", "L 134.537414 66.273664 \n", "L 136.331144 67.023852 \n", "L 137.173801 67.023852 \n", "L 137.582816 67.216916 \n", "L 137.984039 67.216916 \n", "L 138.377763 67.610036 \n", "L 139.143796 68.636395 \n", "L 139.516613 69.065728 \n", "L 139.882946 69.284707 \n", "L 140.597035 69.284707 \n", "L 141.956448 70.191258 \n", "L 142.60461 70.191258 \n", "L 142.921362 70.425992 \n", "L 143.233421 71.151165 \n", "L 143.540924 72.171104 \n", "L 144.437383 72.980215 \n", "L 144.72792 72.980215 \n", "L 145.014504 73.542812 \n", "L 145.576233 73.542812 \n", "L 145.851578 74.125507 \n", "L 146.123371 74.42485 \n", "L 146.3917 74.42485 \n", "L 147.684351 76.009951 \n", "L 148.90224 76.009951 \n", "L 149.13759 76.346328 \n", "L 149.370339 76.346328 \n", "L 149.600544 76.689788 \n", "L 149.828261 76.689788 \n", "L 150.053543 77.040634 \n", "L 150.715281 77.040634 \n", "L 150.93132 77.399192 \n", "L 151.145165 77.399192 \n", "L 151.566451 78.140852 \n", "L 151.979476 78.140852 \n", "L 152.182991 78.524722 \n", "L 152.384558 78.524722 \n", "L 152.781996 79.32067 \n", "L 153.364437 79.32067 \n", "L 153.743971 80.157455 \n", "L 153.931205 80.157455 \n", "L 154.116788 80.592513 \n", "L 154.843191 80.592513 \n", "L 155.020944 81.039492 \n", "L 155.887881 81.039492 \n", "L 156.057068 81.499064 \n", "L 156.720543 81.499064 \n", "L 156.883197 81.971958 \n", "L 157.678144 81.971958 \n", "L 157.833596 82.458971 \n", "L 158.141099 82.458971 \n", "L 158.293184 82.960971 \n", "L 158.890773 82.960971 \n", "L 159.037558 83.47891 \n", "L 159.328095 83.47891 \n", "L 159.471874 84.013834 \n", "L 159.756522 84.013834 \n", "L 159.897416 84.566894 \n", "L 160.037374 84.566894 \n", "L 160.176408 85.139364 \n", "L 160.858138 85.139364 \n", "L 160.991875 85.732655 \n", "L 161.518493 85.732655 \n", "L 161.648115 86.34834 \n", "L 162.409533 86.34834 \n", "L 162.533803 86.988176 \n", "L 162.902271 86.988176 \n", "L 163.023676 87.654134 \n", "L 163.502415 87.654134 \n", "L 163.620418 88.34844 \n", "L 164.200719 88.34844 \n", "L 164.314886 89.073613 \n", "L 165.423752 89.073613 \n", "L 165.638689 89.832527 \n", "L 166.374151 89.832527 \n", "L 166.579651 90.628476 \n", "L 167.675405 90.628476 \n", "L 167.86865 91.46526 \n", "L 168.716963 91.46526 \n", "L 168.900926 92.347298 \n", "L 169.173892 92.347298 \n", "L 169.353924 93.279764 \n", "L 169.972187 93.279764 \n", "L 170.145551 94.268777 \n", "L 171.483372 94.268777 \n", "L 171.64478 95.32164 \n", "L 172.433771 95.32164 \n", "L 172.588084 96.44717 \n", "L 173.490948 96.44717 \n", "L 173.637733 97.656146 \n", "L 175.256101 97.656146 \n", "L 175.391124 98.96194 \n", "L 176.37712 98.96194 \n", "L 176.505167 100.381419 \n", "L 178.33794 100.381419 \n", "L 178.454636 101.936282 \n", "L 180.451629 101.936282 \n", "L 180.609805 103.655104 \n", "L 182.42068 103.655104 \n", "L 182.564787 105.576583 \n", "L 185.257418 105.576583 \n", "L 185.383422 107.754976 \n", "L 188.528445 107.754976 \n", "L 188.672225 110.269746 \n", "L 191.67211 110.269746 \n", "L 191.796014 113.244088 \n", "L 195.754423 113.244088 \n", "L 195.882004 116.884388 \n", "L 200.724191 116.884388 \n", "L 200.84513 121.577552 \n", "L 207.214001 121.577552 \n", "L 207.332503 128.192194 \n", "L 216.282641 128.192194 \n", "L 216.398216 139.5 \n", "L 231.900852 139.5 \n", "L 231.900852 139.5 \n", "\" clip-path=\"url(#peccb14804d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 45.**********.8 \n", "L 45.478125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 240.**********.8 \n", "L 240.778125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 45.**********.8 \n", "L 240.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 45.478125 7.2 \n", "L 240.778125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"peccb14804d\">\n", "   <rect x=\"45.478125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["freqs = [freq for token, freq in vocab.token_freqs]\n", "d2l.plot(freqs, xlabel='token: x', ylabel='frequency: n(x)',\n", "         xscale='log', yscale='log')"]}, {"cell_type": "markdown", "id": "02725699", "metadata": {"origin_pos": 22}, "source": ["After dealing with the first few words as exceptions,\n", "all the remaining words roughly follow a straight line on a log--log plot.\n", "This phenomenon is captured by *<PERSON><PERSON><PERSON>'s law*,\n", "which states that the frequency $n_i$\n", "of the $i^\\textrm{th}$ most frequent word is:\n", "\n", "$$n_i \\propto \\frac{1}{i^\\alpha},$$\n", ":eqlabel:`eq_zipf_law`\n", "\n", "which is equivalent to\n", "\n", "$$\\log n_i = -\\alpha \\log i + c,$$\n", "\n", "where $\\alpha$ is the exponent that characterizes\n", "the distribution and $c$ is a constant.\n", "This should already give us pause for thought if we want\n", "to model words by counting statistics.\n", "After all, we will significantly overestimate the frequency of the tail, also known as the infrequent words. But [**what about the other word combinations, such as two consecutive words (bigrams), three consecutive words (trigrams)**], and beyond?\n", "Let's see whether the bigram frequency behaves in the same manner as the single word (unigram) frequency.\n"]}, {"cell_type": "code", "execution_count": 16, "id": "eb992e88", "metadata": {"attributes": {"classes": [], "id": "", "n": "13"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:27.121634Z", "iopub.status.busy": "2023-08-18T19:29:27.121053Z", "iopub.status.idle": "2023-08-18T19:29:27.157009Z", "shell.execute_reply": "2023-08-18T19:29:27.156222Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["[('of--the', 309),\n", " ('in--the', 169),\n", " ('i--had', 130),\n", " ('i--was', 112),\n", " ('and--the', 109),\n", " ('the--time', 102),\n", " ('it--was', 99),\n", " ('to--the', 85),\n", " ('as--i', 78),\n", " ('of--a', 73)]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["bigram_tokens = ['--'.join(pair) for pair in zip(words[:-1], words[1:])]\n", "bigram_vocab = Vocab(bigram_tokens)\n", "bigram_vocab.token_freqs[:10]"]}, {"cell_type": "markdown", "id": "f44467c1", "metadata": {"origin_pos": 24}, "source": ["One thing is notable here. Out of the ten most frequent word pairs, nine are composed of both stop words and only one is relevant to the actual book---\"the time\". Furthermore, let's see whether the trigram frequency behaves in the same manner.\n"]}, {"cell_type": "code", "execution_count": 17, "id": "8a284366", "metadata": {"attributes": {"classes": [], "id": "", "n": "14"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:27.160412Z", "iopub.status.busy": "2023-08-18T19:29:27.159846Z", "iopub.status.idle": "2023-08-18T19:29:27.203866Z", "shell.execute_reply": "2023-08-18T19:29:27.203004Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["[('the--time--traveller', 59),\n", " ('the--time--machine', 30),\n", " ('the--medical--man', 24),\n", " ('it--seemed--to', 16),\n", " ('it--was--a', 15),\n", " ('here--and--there', 15),\n", " ('seemed--to--me', 14),\n", " ('i--did--not', 14),\n", " ('i--saw--the', 13),\n", " ('i--began--to', 13)]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["trigram_tokens = ['--'.join(triple) for triple in zip(\n", "    words[:-2], words[1:-1], words[2:])]\n", "trigram_vocab = Vocab(trigram_tokens)\n", "trigram_vocab.token_freqs[:10]"]}, {"cell_type": "markdown", "id": "7940c8db", "metadata": {"origin_pos": 26}, "source": ["Now, let's [**visualize the token frequency**] among these three models: unigrams, bigrams, and trigrams.\n"]}, {"cell_type": "code", "execution_count": 18, "id": "ad96dfc1", "metadata": {"attributes": {"classes": [], "id": "", "n": "15"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:27.207781Z", "iopub.status.busy": "2023-08-18T19:29:27.206873Z", "iopub.status.idle": "2023-08-18T19:29:28.094411Z", "shell.execute_reply": "2023-08-18T19:29:28.093574Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"247.978125pt\" height=\"183.35625pt\" viewBox=\"0 0 247.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-31T21:29:04.279676</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 247.**********.35625 \n", "L 247.978125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 45.**********.8 \n", "L 240.**********.8 \n", "L 240.778125 7.2 \n", "L 45.478125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 54.355398 145.8 \n", "L 54.355398 7.2 \n", "\" clip-path=\"url(#p02c75b8cfd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m6c37343b21\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m6c37343b21\" x=\"54.355398\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- $\\mathdefault{10^{0}}$ -->\n", "      <g transform=\"translate(45.555398 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 94.026857 145.8 \n", "L 94.026857 7.2 \n", "\" clip-path=\"url(#p02c75b8cfd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m6c37343b21\" x=\"94.026857\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(85.226857 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 133.698316 145.8 \n", "L 133.698316 7.2 \n", "\" clip-path=\"url(#p02c75b8cfd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m6c37343b21\" x=\"133.698316\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(124.898316 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 173.369775 145.8 \n", "L 173.369775 7.2 \n", "\" clip-path=\"url(#p02c75b8cfd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m6c37343b21\" x=\"173.369775\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- $\\mathdefault{10^{3}}$ -->\n", "      <g transform=\"translate(164.569775 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 213.041234 145.8 \n", "L 213.041234 7.2 \n", "\" clip-path=\"url(#p02c75b8cfd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m6c37343b21\" x=\"213.041234\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- $\\mathdefault{10^{4}}$ -->\n", "      <g transform=\"translate(204.241234 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <defs>\n", "       <path id=\"m762a570fd0\" d=\"M 0 0 \n", "L 0 2 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"45.554334\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"48.210211\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"50.510836\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"52.540131\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"66.297697\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"73.283494\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"78.239996\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"82.084558\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"85.225793\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"87.88167\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"90.182295\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_17\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"92.21159\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_18\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"105.969156\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_19\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"112.954953\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_20\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"117.911455\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_21\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"121.756017\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_22\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"124.897252\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_23\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"127.553129\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_24\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"129.853754\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_25\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"131.883049\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_26\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"145.640615\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_27\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"152.626412\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_28\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"157.582914\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_29\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"161.427476\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_30\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"164.568711\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_31\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"167.224588\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_32\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"169.525213\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_33\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"171.554508\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_34\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"185.312074\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_35\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"192.297871\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_36\">\n", "     <g id=\"line2d_41\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"197.254373\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_37\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"201.098935\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_38\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"204.24017\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_39\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"206.896047\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_40\">\n", "     <g id=\"line2d_45\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"209.196672\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_41\">\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"211.225968\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_42\">\n", "     <g id=\"line2d_47\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"224.983533\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_43\">\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"231.96933\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_44\">\n", "     <g id=\"line2d_49\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"236.925832\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_45\">\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use xlink:href=\"#m762a570fd0\" x=\"240.770394\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- token: x -->\n", "     <g transform=\"translate(122.916406 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-3a\" d=\"M 750 794 \n", "L 1409 794 \n", "L 1409 0 \n", "L 750 0 \n", "L 750 794 \n", "z\n", "M 750 3309 \n", "L 1409 3309 \n", "L 1409 2516 \n", "L 750 2516 \n", "L 750 3309 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"100.390625\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"154.675781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"216.199219\"/>\n", "      <use xlink:href=\"#DejaVuSans-3a\" x=\"279.578125\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"313.269531\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"345.056641\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_51\">\n", "      <path d=\"M 45.478125 139.5 \n", "L 240.778125 139.5 \n", "\" clip-path=\"url(#p02c75b8cfd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_52\">\n", "      <defs>\n", "       <path id=\"m4eeffb8167\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m4eeffb8167\" x=\"45.478125\" y=\"139.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- $\\mathdefault{10^{0}}$ -->\n", "      <g transform=\"translate(20.878125 143.299219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_53\">\n", "      <path d=\"M 45.478125 101.936282 \n", "L 240.778125 101.936282 \n", "\" clip-path=\"url(#p02c75b8cfd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#m4eeffb8167\" x=\"45.478125\" y=\"101.936282\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(20.878125 105.735501) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_55\">\n", "      <path d=\"M 45.478125 64.372564 \n", "L 240.778125 64.372564 \n", "\" clip-path=\"url(#p02c75b8cfd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use xlink:href=\"#m4eeffb8167\" x=\"45.478125\" y=\"64.372564\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(20.878125 68.171783) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_57\">\n", "      <path d=\"M 45.478125 26.808846 \n", "L 240.778125 26.808846 \n", "\" clip-path=\"url(#p02c75b8cfd)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use xlink:href=\"#m4eeffb8167\" x=\"45.478125\" y=\"26.808846\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- $\\mathdefault{10^{3}}$ -->\n", "      <g transform=\"translate(20.878125 30.608065) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_59\">\n", "      <defs>\n", "       <path id=\"m17dfe394db\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"145.318694\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_60\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"143.1403\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_61\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"141.218821\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_62\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"128.192194\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_63\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"121.577552\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_64\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"116.884388\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_65\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"113.244088\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_66\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"110.269746\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_67\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"107.754976\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_68\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"105.576583\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_69\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"103.655104\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_70\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"90.628476\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_71\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"84.013834\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_72\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"79.32067\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_73\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"75.68037\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_74\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"72.706028\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_75\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"70.191258\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_76\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"68.012865\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_77\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"66.091386\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_78\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"53.064758\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_79\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"46.450116\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_80\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"41.756953\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_81\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"38.116652\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_82\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"35.14231\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_29\">\n", "     <g id=\"line2d_83\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"32.62754\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_30\">\n", "     <g id=\"line2d_84\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"30.449147\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_31\">\n", "     <g id=\"line2d_85\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"28.527668\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_32\">\n", "     <g id=\"line2d_86\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"15.501041\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_33\">\n", "     <g id=\"line2d_87\">\n", "      <g>\n", "       <use xlink:href=\"#m17dfe394db\" x=\"45.478125\" y=\"8.886398\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- frequency: n(x) -->\n", "     <g transform=\"translate(14.798438 114.517188) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-71\" d=\"M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "M 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 -1331 \n", "L 2906 -1331 \n", "L 2906 525 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-66\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"35.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"74.068359\"/>\n", "      <use xlink:href=\"#DejaVuSans-71\" x=\"135.591797\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"199.068359\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"262.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"323.970703\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"387.349609\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"442.330078\"/>\n", "      <use xlink:href=\"#DejaVuSans-3a\" x=\"494.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"527.951172\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"559.738281\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"623.117188\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"662.130859\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"721.310547\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M -1 22.934991 \n", "L 54.355398 22.948175 \n", "L 66.297697 23.233932 \n", "L 73.283494 24.458034 \n", "L 78.239996 30.126092 \n", "L 82.084558 32.744485 \n", "L 85.225793 36.502574 \n", "L 87.88167 36.830949 \n", "L 90.182295 40.091237 \n", "L 94.026857 40.3137 \n", "L 95.668965 43.749961 \n", "L 97.168092 47.517487 \n", "L 98.547156 48.168938 \n", "L 101.012654 51.43591 \n", "L 102.124594 51.809238 \n", "L 103.169102 52.741704 \n", "L 104.15389 53.064758 \n", "L 105.085419 56.910265 \n", "L 105.969156 57.541843 \n", "L 106.809766 59.236831 \n", "L 107.611264 59.236831 \n", "L 108.377128 59.598035 \n", "L 109.110391 60.218404 \n", "L 109.813718 60.473311 \n", "L 110.489455 60.995394 \n", "L 111.139687 61.128568 \n", "L 112.37086 61.128568 \n", "L 113.519892 61.672409 \n", "L 114.066893 62.235006 \n", "L 114.597061 62.235006 \n", "L 115.111401 62.378741 \n", "L 115.61083 62.378741 \n", "L 116.096189 62.523752 \n", "L 116.568248 63.421982 \n", "L 117.027718 63.576615 \n", "L 117.475252 63.89035 \n", "L 117.911455 65.381982 \n", "L 118.752065 65.732828 \n", "L 119.157475 65.732828 \n", "L 119.553563 65.911122 \n", "L 119.94075 66.273664 \n", "L 121.407942 67.023852 \n", "L 122.097198 67.023852 \n", "L 122.431754 67.216916 \n", "L 122.759937 67.216916 \n", "L 123.081986 67.610036 \n", "L 123.708567 68.636395 \n", "L 124.013515 69.065728 \n", "L 124.313159 69.284707 \n", "L 124.897252 69.284707 \n", "L 126.009193 70.191258 \n", "L 126.53936 70.191258 \n", "L 126.798449 70.425992 \n", "L 127.553129 72.436374 \n", "L 128.038488 72.980215 \n", "L 128.276134 72.980215 \n", "L 128.510547 73.542812 \n", "L 128.970017 73.542812 \n", "L 129.417551 74.42485 \n", "L 129.637033 74.42485 \n", "L 130.898262 76.009951 \n", "L 131.690544 76.009951 \n", "L 131.883049 76.346328 \n", "L 132.073428 76.346328 \n", "L 132.261726 76.689788 \n", "L 132.447988 76.689788 \n", "L 132.632259 77.040634 \n", "L 133.173531 77.040634 \n", "L 133.350241 77.399192 \n", "L 133.525158 77.399192 \n", "L 133.869751 78.140852 \n", "L 134.207587 78.140852 \n", "L 134.374053 78.524722 \n", "L 134.538926 78.524722 \n", "L 134.864013 79.32067 \n", "L 135.340424 79.32067 \n", "L 135.650867 80.157455 \n", "L 135.804015 80.157455 \n", "L 135.955814 80.592513 \n", "L 136.54998 80.592513 \n", "L 136.695374 81.039492 \n", "L 137.40449 81.039492 \n", "L 137.542877 81.499064 \n", "L 138.085571 81.499064 \n", "L 138.218615 81.971958 \n", "L 138.868847 81.971958 \n", "L 138.995999 82.458971 \n", "L 139.247523 82.458971 \n", "L 139.371922 82.960971 \n", "L 139.860723 82.960971 \n", "L 139.980787 83.47891 \n", "L 140.218433 83.47891 \n", "L 140.336039 84.013834 \n", "L 140.568868 84.013834 \n", "L 140.684113 84.566894 \n", "L 140.798592 84.566894 \n", "L 141.025294 85.139364 \n", "L 141.469941 85.139364 \n", "L 141.688034 85.732655 \n", "L 142.010082 85.732655 \n", "L 142.221485 86.34834 \n", "L 142.738914 86.34834 \n", "L 142.941611 86.988176 \n", "L 143.141952 86.988176 \n", "L 143.33999 87.654134 \n", "L 143.632843 87.654134 \n", "L 143.825349 88.34844 \n", "L 144.204025 88.34844 \n", "L 144.390288 89.073613 \n", "L 145.204412 89.073613 \n", "L 145.380221 89.832527 \n", "L 145.981796 89.832527 \n", "L 146.149886 90.628476 \n", "L 147.046164 90.628476 \n", "L 147.204231 91.46526 \n", "L 147.898113 91.46526 \n", "L 148.048587 92.347298 \n", "L 148.271861 92.347298 \n", "L 148.419119 93.279764 \n", "L 148.924831 93.279764 \n", "L 149.066635 94.268777 \n", "L 150.160914 94.268777 \n", "L 150.292938 95.32164 \n", "L 150.938298 95.32164 \n", "L 151.064519 96.44717 \n", "L 151.803022 96.44717 \n", "L 151.923086 97.656146 \n", "L 153.246839 97.656146 \n", "L 153.41224 98.96194 \n", "L 154.163784 98.96194 \n", "L 154.320651 100.381419 \n", "L 155.767648 100.381419 \n", "L 155.910629 101.936282 \n", "L 157.496553 101.936282 \n", "L 157.625933 103.655104 \n", "L 159.107149 103.655104 \n", "L 159.225022 105.576583 \n", "L 161.427476 105.576583 \n", "L 161.56476 107.754976 \n", "L 164.103032 107.754976 \n", "L 164.220637 110.269746 \n", "L 166.674411 110.269746 \n", "L 166.801003 113.244088 \n", "L 170.013563 113.244088 \n", "L 170.138714 116.884388 \n", "L 174.078613 116.884388 \n", "L 174.193969 121.577552 \n", "L 179.386992 121.577552 \n", "L 179.508069 128.192194 \n", "L 186.804739 128.192194 \n", "L 186.922828 139.5 \n", "L 199.579747 139.5 \n", "L 199.579747 139.5 \n", "\" clip-path=\"url(#p02c75b8cfd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M -1 55.798556 \n", "L 54.355398 55.812292 \n", "L 73.283494 62.523752 \n", "L 78.239996 62.966686 \n", "L 82.084558 64.04951 \n", "L 85.225793 64.536523 \n", "L 87.88167 67.023852 \n", "L 90.182295 68.425892 \n", "L 92.21159 69.506666 \n", "L 94.026857 70.664152 \n", "L 95.668965 70.905841 \n", "L 97.168092 72.171104 \n", "L 98.547156 72.436374 \n", "L 99.823969 72.436374 \n", "L 101.012654 72.706028 \n", "L 102.124594 75.357316 \n", "L 103.169102 75.357316 \n", "L 104.15389 76.009951 \n", "L 105.085419 76.009951 \n", "L 105.969156 76.689788 \n", "L 106.809766 76.689788 \n", "L 108.377128 77.399192 \n", "L 109.813718 77.399192 \n", "L 110.489455 77.765807 \n", "L 111.139687 77.765807 \n", "L 111.766268 78.917842 \n", "L 112.37086 79.32067 \n", "L 112.954953 79.32067 \n", "L 113.519892 79.733698 \n", "L 115.111401 79.733698 \n", "L 115.61083 80.592513 \n", "L 116.096189 81.039492 \n", "L 117.027718 81.039492 \n", "L 117.475252 81.971958 \n", "L 118.336886 81.971958 \n", "L 118.752065 82.960971 \n", "L 119.157475 82.960971 \n", "L 119.94075 84.013834 \n", "L 121.052691 84.013834 \n", "L 121.756017 85.139364 \n", "L 122.431754 85.139364 \n", "L 122.759937 85.732655 \n", "L 123.708567 85.732655 \n", "L 124.013515 86.34834 \n", "L 124.607681 86.34834 \n", "L 124.897252 86.988176 \n", "L 125.462191 86.988176 \n", "L 125.737863 87.654134 \n", "L 126.53936 87.654134 \n", "L 126.798449 88.34844 \n", "L 127.553129 88.34844 \n", "L 127.797518 89.073613 \n", "L 129.637033 89.073613 \n", "L 129.853754 89.832527 \n", "L 130.898262 89.832527 \n", "L 131.099774 90.628476 \n", "L 131.495862 90.628476 \n", "L 131.690544 91.46526 \n", "L 133.173531 91.46526 \n", "L 133.350241 92.347298 \n", "L 134.538926 92.347298 \n", "L 134.702236 93.279764 \n", "L 135.18308 93.279764 \n", "L 135.340424 94.268777 \n", "L 137.542877 94.268777 \n", "L 137.680162 95.32164 \n", "L 139.980787 95.32164 \n", "L 140.10002 96.44717 \n", "L 141.796053 96.44717 \n", "L 142.010082 97.656146 \n", "L 143.535777 97.656146 \n", "L 143.729364 98.96194 \n", "L 145.292541 98.96194 \n", "L 145.467457 100.381419 \n", "L 147.438644 100.381419 \n", "L 147.593166 101.936282 \n", "L 150.489097 101.936282 \n", "L 150.61864 103.655104 \n", "L 152.854615 103.655104 \n", "L 152.967594 105.576583 \n", "L 156.099442 105.576583 \n", "L 156.239707 107.754976 \n", "L 159.458377 107.754976 \n", "L 159.57388 110.269746 \n", "L 163.225504 110.269746 \n", "L 163.349233 113.244088 \n", "L 167.82918 113.244088 \n", "L 167.947593 116.884388 \n", "L 174.128146 116.884388 \n", "L 174.243171 121.577552 \n", "L 182.205262 121.577552 \n", "L 182.318375 128.192194 \n", "L 195.477351 128.192194 \n", "L 195.591578 139.5 \n", "L 225.183092 139.5 \n", "L 225.183092 139.5 \n", "\" clip-path=\"url(#p02c75b8cfd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M -1 83.998438 \n", "L 54.355398 84.013834 \n", "L 66.297697 87.654134 \n", "L 73.283494 94.268777 \n", "L 78.239996 95.32164 \n", "L 82.084558 95.32164 \n", "L 85.225793 96.44717 \n", "L 87.88167 96.44717 \n", "L 90.182295 97.656146 \n", "L 92.21159 97.656146 \n", "L 94.026857 98.96194 \n", "L 98.547156 98.96194 \n", "L 99.823969 100.381419 \n", "L 105.969156 100.381419 \n", "L 106.809766 101.936282 \n", "L 109.813718 101.936282 \n", "L 110.489455 103.655104 \n", "L 114.066893 103.655104 \n", "L 114.597061 105.576583 \n", "L 121.052691 105.576583 \n", "L 121.407942 107.754976 \n", "L 126.276316 107.754976 \n", "L 126.53936 110.269746 \n", "L 132.632259 110.269746 \n", "L 132.814579 113.244088 \n", "L 140.568868 113.244088 \n", "L 140.684113 116.884388 \n", "L 148.271861 116.884388 \n", "L 148.419119 121.577552 \n", "L 160.652212 121.577552 \n", "L 160.795788 128.192194 \n", "L 181.787567 128.192194 \n", "L 181.903446 139.5 \n", "L 231.900852 139.5 \n", "L 231.900852 139.5 \n", "\" clip-path=\"url(#p02c75b8cfd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 45.**********.8 \n", "L 45.478125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 240.**********.8 \n", "L 240.778125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 45.**********.8 \n", "L 240.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 45.478125 7.2 \n", "L 240.778125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 159.996875 59.234375 \n", "L 233.778125 59.234375 \n", "Q 235.778125 59.234375 235.778125 57.234375 \n", "L 235.778125 14.2 \n", "Q 235.778125 12.2 233.778125 12.2 \n", "L 159.996875 12.2 \n", "Q 157.996875 12.2 157.996875 14.2 \n", "L 157.996875 57.234375 \n", "Q 157.996875 59.234375 159.996875 59.234375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_91\">\n", "     <path d=\"M 161.996875 20.298438 \n", "L 171.996875 20.298438 \n", "L 181.996875 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- unigram -->\n", "     <g transform=\"translate(189.996875 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-75\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"63.378906\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"126.757812\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"154.541016\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"218.017578\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"259.130859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"320.410156\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_92\">\n", "     <path d=\"M 161.996875 34.976563 \n", "L 171.996875 34.976563 \n", "L 181.996875 34.976563 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- bigram -->\n", "     <g transform=\"translate(189.996875 38.476563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-62\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"91.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"154.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"195.849609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"257.128906\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_93\">\n", "     <path d=\"M 161.996875 49.654688 \n", "L 171.996875 49.654688 \n", "L 181.996875 49.654688 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- trigram -->\n", "     <g transform=\"translate(189.996875 53.154688) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"108.105469\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"171.582031\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"212.695312\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"273.974609\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p02c75b8cfd\">\n", "   <rect x=\"45.478125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["bigram_freqs = [freq for token, freq in bigram_vocab.token_freqs]\n", "trigram_freqs = [freq for token, freq in trigram_vocab.token_freqs]\n", "d2l.plot([freqs, bigram_freqs, trigram_freqs], xlabel='token: x',\n", "         ylabel='frequency: n(x)', xscale='log', yscale='log',\n", "         legend=['unigram', 'bigram', 'trigram'])"]}, {"cell_type": "markdown", "id": "3999dc95", "metadata": {"origin_pos": 28}, "source": ["This figure is quite exciting.\n", "First, beyond unigram words, sequences of words\n", "also appear to be following <PERSON><PERSON><PERSON>'s law,\n", "albeit with a smaller exponent\n", "$\\alpha$ in :eqref:`eq_zipf_law`,\n", "depending on the sequence length.\n", "Second, the number of distinct $n$-grams is not that large.\n", "This gives us hope that there is quite a lot of structure in language.\n", "Third, many $n$-grams occur very rarely.\n", "This makes certain methods unsuitable for language modeling\n", "and motivates the use of deep learning models.\n", "We will discuss this in the next section.\n", "\n", "\n", "## Summary\n", "\n", "Text is among the most common forms of sequence data encountered in deep learning.\n", "Common choices for what constitutes a token are characters, words, and word pieces.\n", "To preprocess text, we usually (i) split text into tokens; (ii) build a vocabulary to map token strings to numerical indices; and (iii) convert text data into token indices for models to manipulate.\n", "In practice, the frequency of words tends to follow <PERSON><PERSON><PERSON>'s law. This is true not just for individual words (unigrams), but also for $n$-grams.\n", "\n", "\n", "## Exercises\n", "\n", "1. In the experiment of this section, tokenize text into words and vary the `min_freq` argument value of the `Vocab` instance. Qualitatively characterize how changes in `min_freq` impact the size of the resulting vocabulary.\n", "1. Estimate the exponent of Zipfian distribution for unigrams, bigrams, and trigrams in this corpus.\n", "1. Find some other sources of data (download a standard machine learning dataset, pick another public domain book,\n", "   scrape a website, etc). For each, tokenize the data at both the word and character levels. How do the vocabulary sizes compare with *The Time Machine* corpus at equivalent values of `min_freq`. Estimate the exponent of the Zipfian distribution corresponding to the unigram and bigram distributions for these corpora. How do they compare with the values that you observed for *The Time Machine* corpus?\n"]}, {"cell_type": "markdown", "id": "635a4711", "metadata": {"origin_pos": 30, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/118)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON> (aideep)", "language": "python", "name": "<PERSON><PERSON>"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
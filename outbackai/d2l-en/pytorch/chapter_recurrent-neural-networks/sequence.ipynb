{"cells": [{"cell_type": "markdown", "id": "6e18af3b", "metadata": {"origin_pos": 0}, "source": ["# Working with Sequences\n", ":label:`sec_sequence`\n", "\n", "Up until now, we have focused on models whose inputs\n", "consisted of a single feature vector $\\mathbf{x} \\in \\mathbb{R}^d$.\n", "The main change of perspective when developing models\n", "capable of processing sequences is that we now\n", "focus on inputs that consist of an ordered list\n", "of feature vectors $\\mathbf{x}_1, \\dots, \\mathbf{x}_T$,\n", "where each feature vector $\\mathbf{x}_t$ is\n", "indexed by a time step $t \\in \\mathbb{Z}^+$\n", "lying in $\\mathbb{R}^d$.\n", "\n", "Some datasets consist of a single massive sequence.\n", "Consider, for example, the extremely long streams\n", "of sensor readings that might be available to climate scientists.\n", "In such cases, we might create training datasets\n", "by randomly sampling subsequences of some predetermined length.\n", "More often, our data arrives as a collection of sequences.\n", "Consider the following examples:\n", "(i) a collection of documents,\n", "each represented as its own sequence of words,\n", "and each having its own length $T_i$;\n", "(ii) sequence representation of\n", "patient stays in the hospital,\n", "where each stay consists of a number of events\n", "and the sequence length depends roughly\n", "on the length of the stay.\n", "\n", "\n", "Previously, when dealing with individual inputs,\n", "we assumed that they were sampled independently\n", "from the same underlying distribution $P(X)$.\n", "While we still assume that entire sequences\n", "(e.g., entire documents or patient trajectories)\n", "are sampled independently,\n", "we cannot assume that the data arriving\n", "at each time step are independent of each other.\n", "For example, the words that likely to appear later in a document\n", "depend heavily on words occurring earlier in the document.\n", "The medicine a patient is likely to receive\n", "on the 10th day of a hospital visit\n", "depends heavily on what transpired\n", "in the previous nine days.\n", "\n", "This should come as no surprise.\n", "If we did not believe that the elements in a sequence were related,\n", "we would not have bothered to model them as a sequence in the first place.\n", "Consider the usefulness of the auto-fill features\n", "that are popular on search tools and modern email clients.\n", "They are useful precisely because it is often possible\n", "to predict (imperfectly, but better than random guessing)\n", "what the likely continuations of a sequence might be,\n", "given some initial prefix.\n", "For most sequence models,\n", "we do not require independence,\n", "or even stationarity, of our sequences.\n", "Instead, we require only that\n", "the sequences themselves are sampled\n", "from some fixed underlying distribution\n", "over entire sequences.\n", "\n", "This flexible approach allows for such phenomena\n", "as (i) documents looking significantly different\n", "at the beginning than at the end;\n", "or (ii) patient status evolving either\n", "towards recovery or towards death\n", "over the course of a hospital stay;\n", "or (iii) customer taste evolving in predictable ways\n", "over the course of continued interaction with a recommender system.\n", "\n", "\n", "We sometimes wish to predict a fixed target $y$\n", "given sequentially structured input\n", "(e.g., sentiment classification based on a movie review).\n", "At other times, we wish to predict a sequentially structured target\n", "($y_1, \\ldots, y_T$)\n", "given a fixed input (e.g., image captioning).\n", "Still other times, our goal is to predict sequentially structured targets\n", "based on sequentially structured inputs\n", "(e.g., machine translation or video captioning).\n", "Such sequence-to-sequence tasks take two forms:\n", "(i) *aligned*: where the input at each time step\n", "aligns with a corresponding target (e.g., part of speech tagging);\n", "(ii) *unaligned*: where the input and target\n", "do not necessarily exhibit a step-for-step correspondence\n", "(e.g., machine translation).\n", "\n", "Before we worry about handling targets of any kind,\n", "we can tackle the most straightforward problem:\n", "unsupervised density modeling (also called *sequence modeling*).\n", "Here, given a collection of sequences,\n", "our goal is to estimate the probability mass function\n", "that tells us how likely we are to see any given sequence,\n", "i.e., $p(\\mathbf{x}_1, \\ldots, \\mathbf{x}_T)$.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "620cb95e", "metadata": {"attributes": {"classes": [], "id": "", "n": "8"}, "execution": {"iopub.execute_input": "2023-08-18T19:31:26.652882Z", "iopub.status.busy": "2023-08-18T19:31:26.652060Z", "iopub.status.idle": "2023-08-18T19:31:29.809430Z", "shell.execute_reply": "2023-08-18T19:31:29.808240Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "1e47a591", "metadata": {"origin_pos": 6}, "source": ["## Autoregressive Models\n", "\n", "\n", "Before introducing specialized neural networks\n", "designed to handle sequentially structured data,\n", "let's take a look at some actual sequence data\n", "and build up some basic intuitions and statistical tools.\n", "In particular, we will focus on stock price data\n", "from the FTSE 100 index (:numref:`fig_ftse100`).\n", "At each *time step* $t \\in \\mathbb{Z}^+$, we observe\n", "the price, $x_t$, of the index at that time.\n", "\n", "\n", "![FTSE 100 index over about 30 years.](../img/ftse100.png)\n", ":width:`400px`\n", ":label:`fig_ftse100`\n", "\n", "\n", "Now suppose that a trader would like to make short-term trades,\n", "strategically getting into or out of the index,\n", "depending on whether they believe\n", "that it will rise or decline\n", "in the subsequent time step.\n", "Absent any other features\n", "(news, financial reporting data, etc.),\n", "the only available signal for predicting\n", "the subsequent value is the history of prices to date.\n", "The trader is thus interested in knowing\n", "the probability distribution\n", "\n", "$$P(x_t \\mid x_{t-1}, \\ldots, x_1)$$\n", "\n", "over prices that the index might take\n", "in the subsequent time step.\n", "While estimating the entire distribution\n", "over a continuously valued random variable\n", "can be difficult, the trader would be happy\n", "to focus on a few key statistics of the distribution,\n", "particularly the expected value and the variance.\n", "One simple strategy for estimating the conditional expectation\n", "\n", "$$\\mathbb{E}[(x_t \\mid x_{t-1}, \\ldots, x_1)],$$\n", "\n", "would be to apply a linear regression model\n", "(recall :numref:`sec_linear_regression`).\n", "Such models that regress the value of a signal\n", "on the previous values of that same signal\n", "are naturally called *autoregressive models*.\n", "There is just one major problem: the number of inputs,\n", "$x_{t-1}, \\ldots, x_1$ varies, depending on $t$.\n", "In other words, the number of inputs increases\n", "with the amount of data that we encounter.\n", "Thus if we want to treat our historical data\n", "as a training set, we are left with the problem\n", "that each example has a different number of features.\n", "Much of what follows in this chapter\n", "will revolve around techniques\n", "for overcoming these challenges\n", "when engaging in such *autoregressive* modeling problems\n", "where the object of interest is\n", "$P(x_t \\mid x_{t-1}, \\ldots, x_1)$\n", "or some statistic(s) of this distribution.\n", "\n", "A few strategies recur frequently.\n", "First of all,\n", "we might believe that although long sequences\n", "$x_{t-1}, \\ldots, x_1$ are available,\n", "it may not be necessary\n", "to look back so far in the history\n", "when predicting the near future.\n", "In this case we might content ourselves\n", "to condition on some window of length $\\tau$\n", "and only use $x_{t-1}, \\ldots, x_{t-\\tau}$ observations.\n", "The immediate benefit is that now the number of arguments\n", "is always the same, at least for $t > \\tau$.\n", "This allows us to train any linear model or deep network\n", "that requires fixed-length vectors as inputs.\n", "Second, we might develop models that maintain\n", "some summary $h_t$ of the past observations\n", "(see :numref:`fig_sequence-model`)\n", "and at the same time update $h_t$\n", "in addition to the prediction $\\hat{x}_t$.\n", "This leads to models that estimate not only $x_t$\n", "with $\\hat{x}_t = P(x_t \\mid h_{t})$\n", "but also updates of the form\n", "$h_t = g(h_{t-1}, x_{t-1})$.\n", "Since $h_t$ is never observed,\n", "these models are also called\n", "*latent autoregressive models*.\n", "\n", "![A latent autoregressive model.](../img/sequence-model.svg)\n", ":label:`fig_sequence-model`\n", "\n", "To construct training data from historical data, one\n", "typically creates examples by sampling windows randomly.\n", "In general, we do not expect time to stand still.\n", "However, we often assume that while\n", "the specific values of $x_t$ might change,\n", "the dynamics according to which each subsequent\n", "observation is generated given the previous observations do not.\n", "Statisticians call dynamics that do not change *stationary*.\n", "\n", "\n", "\n", "## Sequence Models\n", "\n", "Sometimes, especially when working with language,\n", "we wish to estimate the joint probability\n", "of an entire sequence.\n", "This is a common task when working with sequences\n", "composed of discrete *tokens*, such as words.\n", "Generally, these estimated functions are called *sequence models*\n", "and for natural language data, they are called *language models*.\n", "The field of sequence modeling has been driven so much by natural language processing,\n", "that we often describe sequence models as \"language models\",\n", "even when dealing with non-language data.\n", "Language models prove useful for all sorts of reasons.\n", "Sometimes we want to evaluate the likelihood of sentences.\n", "For example, we might wish to compare\n", "the naturalness of two candidate outputs\n", "generated by a machine translation system\n", "or by a speech recognition system.\n", "But language modeling gives us not only\n", "the capacity to *evaluate* likelihood,\n", "but the ability to *sample* sequences,\n", "and even to optimize for the most likely sequences.\n", "\n", "While language modeling might not, at first glance, look\n", "like an autoregressive problem,\n", "we can reduce language modeling to autoregressive prediction\n", "by decomposing the joint density  of a sequence $p(x_1, \\ldots, x_T)$\n", "into the product of conditional densities\n", "in a left-to-right fashion\n", "by applying the chain rule of probability:\n", "\n", "$$P(x_1, \\ldots, x_T) = P(x_1) \\prod_{t=2}^T P(x_t \\mid x_{t-1}, \\ldots, x_1).$$\n", "\n", "Note that if we are working with discrete signals such as words,\n", "then the autoregressive model must be a probabilistic classifier,\n", "outputting a full probability distribution\n", "over the vocabulary for whatever word will come next,\n", "given the leftwards context.\n", "\n", "\n", "\n", "### Markov Models\n", ":label:`subsec_markov-models`\n", "\n", "\n", "Now suppose that we wish to employ the strategy mentioned above,\n", "where we condition only on the $\\tau$ previous time steps,\n", "i.e., $x_{t-1}, \\ldots, x_{t-\\tau}$, rather than\n", "the entire sequence history $x_{t-1}, \\ldots, x_1$.\n", "Whenever we can throw away the history\n", "beyond the previous $\\tau$ steps\n", "without any loss in predictive power,\n", "we say that the sequence satisfies a *Markov condition*,\n", "i.e., *that the future is conditionally independent of the past,\n", "given the recent history*.\n", "When $\\tau = 1$, we say that the data is characterized\n", "by a *first-order Markov model*,\n", "and when $\\tau = k$, we say that the data is characterized\n", "by a $k^{\\textrm{th}}$-order Markov model.\n", "For when the first-order Markov condition holds ($\\tau = 1$)\n", "the factorization of our joint probability becomes a product\n", "of probabilities of each word given the previous *word*:\n", "\n", "$$P(x_1, \\ldots, x_T) = P(x_1) \\prod_{t=2}^T P(x_t \\mid x_{t-1}).$$\n", "\n", "We often find it useful to work with models that proceed\n", "as though a Markov condition were satisfied,\n", "even when we know that this is only *approximately* true.\n", "With real text documents we continue to gain information\n", "as we include more and more leftwards context.\n", "But these gains diminish rapidly.\n", "Thus, sometimes we compromise, obviating computational and statistical difficulties\n", "by training models whose validity depends\n", "on a $k^{\\textrm{th}}$-order Markov condition.\n", "Even today's massive RNN- and Transformer-based language models\n", "seldom incorporate more than thousands of words of context.\n", "\n", "\n", "With discrete data, a true Markov model\n", "simply counts the number of times\n", "that each word has occurred in each context, producing\n", "the relative frequency estimate of $P(x_t \\mid x_{t-1})$.\n", "Whenever the data assumes only discrete values\n", "(as in language),\n", "the most likely sequence of words can be computed efficiently\n", "using dynamic programming.\n", "\n", "\n", "### The Order of Decoding\n", "\n", "You may be wondering why we represented\n", "the factorization of a text sequence $P(x_1, \\ldots, x_T)$\n", "as a left-to-right chain of conditional probabilities.\n", "Why not right-to-left or some other, seemingly random order?\n", "In principle, there is nothing wrong with unfolding\n", "$P(x_1, \\ldots, x_T)$ in reverse order.\n", "The result is a valid factorization:\n", "\n", "$$P(x_1, \\ldots, x_T) = P(x_T) \\prod_{t=T-1}^1 P(x_t \\mid x_{t+1}, \\ldots, x_T).$$\n", "\n", "\n", "However, there are many reasons why factorizing text\n", "in the same direction in which we read it\n", "(left-to-right for most languages,\n", "but right-to-left for Arabic and Hebrew)\n", "is preferred for the task of language modeling.\n", "First, this is just a more natural direction for us to think about.\n", "After all we all read text every day,\n", "and this process is guided by our ability\n", "to anticipate which words and phrases\n", "are likely to come next.\n", "Just think of how many times you have completed\n", "someone else's sentence.\n", "Thus, even if we had no other reason to prefer such in-order decodings,\n", "they would be useful if only because we have better intuitions\n", "for what should be likely when predicting in this order.\n", "\n", "Second, by factorizing in order,\n", "we can assign probabilities to arbitrarily long sequences\n", "using the same language model.\n", "To convert a probability over steps $1$ through $t$\n", "into one that extends to word $t+1$ we simply\n", "multiply by the conditional probability\n", "of the additional token given the previous ones:\n", "$P(x_{t+1}, \\ldots, x_1) = P(x_{t}, \\ldots, x_1) \\cdot P(x_{t+1} \\mid x_{t}, \\ldots, x_1)$.\n", "\n", "Third, we have stronger predictive models\n", "for predicting adjacent words than\n", "words at arbitrary other locations.\n", "While all orders of factorization are valid,\n", "they do not necessarily all represent equally easy\n", "predictive modeling problems.\n", "This is true not only for language,\n", "but for other kinds of data as well,\n", "e.g., when the data is causally structured.\n", "For example, we believe that future events cannot influence the past.\n", "Hence, if we change $x_t$, we may be able to influence\n", "what happens for $x_{t+1}$ going forward but not the converse.\n", "That is, if we change $x_t$, the distribution over past events will not change.\n", "In some contexts, this makes it easier to predict $P(x_{t+1} \\mid x_t)$\n", "than to predict $P(x_t \\mid x_{t+1})$.\n", "For instance, in some cases, we can find $x_{t+1} = f(x_t) + \\epsilon$\n", "for some additive noise $\\epsilon$,\n", "whereas the converse is not true :cite:`Hoyer.Janzing.Mooij.ea.2009`.\n", "This is great news, since it is typically the forward direction\n", "that we are interested in estimating.\n", "The book by :citet:<PERSON><PERSON>.Janzing.Scholkopf.2017` contains more on this topic.\n", "We barely scratch the surface of it.\n", "\n", "\n", "## Training\n", "\n", "Before we focus our attention on text data,\n", "let's first try this out with some\n", "continuous-valued synthetic data.\n", "\n", "(**Here, our 1000 synthetic data will follow\n", "the trigonometric `sin` function,\n", "applied to 0.01 times the time step.\n", "To make the problem a little more interesting,\n", "we corrupt each sample with additive noise.**)\n", "From this sequence we extract training examples,\n", "each consisting of features and a label.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a8c96aa0", "metadata": {"attributes": {"classes": [], "id": "", "n": "10"}, "execution": {"iopub.execute_input": "2023-08-18T19:31:29.814983Z", "iopub.status.busy": "2023-08-18T19:31:29.814065Z", "iopub.status.idle": "2023-08-18T19:31:29.820359Z", "shell.execute_reply": "2023-08-18T19:31:29.819346Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["class Data(d2l.DataModule):\n", "    def __init__(self, batch_size=16, T=1000, num_train=600, tau=4):\n", "        self.save_hyperparameters()\n", "        self.time = torch.arange(1, T + 1, dtype=torch.float32)\n", "        self.x = torch.sin(0.01 * self.time) + torch.randn(T) * 0.2"]}, {"cell_type": "code", "execution_count": 5, "id": "fde5ee01", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:29.827700Z", "iopub.status.busy": "2023-08-18T19:31:29.827239Z", "iopub.status.idle": "2023-08-18T19:31:30.107268Z", "shell.execute_reply": "2023-08-18T19:31:30.103579Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"406.885938pt\" height=\"211.07625pt\" viewBox=\"0 0 406.**********.07625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-31T21:25:16.906452</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 211.07625 \n", "L 406.**********.07625 \n", "L 406.885938 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "L 386.960938 7.2 \n", "L 52.160938 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 118.852829 173.52 \n", "L 118.852829 7.2 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m70378e215a\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m70378e215a\" x=\"118.852829\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(109.309079 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 185.879856 173.52 \n", "L 185.879856 7.2 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m70378e215a\" x=\"185.879856\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(176.336106 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 252.906883 173.52 \n", "L 252.906883 7.2 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m70378e215a\" x=\"252.906883\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(243.363133 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 319.93391 173.52 \n", "L 319.93391 7.2 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m70378e215a\" x=\"319.93391\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(310.39016 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m70378e215a\" x=\"386.960938\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(374.235937 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- time -->\n", "     <g transform=\"translate(208.264844 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 52.**********.096992 \n", "L 386.**********.096992 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mef92e632c0\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mef92e632c0\" x=\"52.160938\" y=\"173.096992\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1.5 -->\n", "      <g transform=\"translate(20.878125 176.896211) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 52.160938 148.389413 \n", "L 386.960938 148.389413 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mef92e632c0\" x=\"52.160938\" y=\"148.389413\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 152.188632) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 52.160938 123.681834 \n", "L 386.960938 123.681834 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mef92e632c0\" x=\"52.160938\" y=\"123.681834\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 127.481053) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 52.160938 98.974256 \n", "L 386.960938 98.974256 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mef92e632c0\" x=\"52.160938\" y=\"98.974256\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257812 102.773474) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 52.160938 74.266677 \n", "L 386.960938 74.266677 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mef92e632c0\" x=\"52.160938\" y=\"74.266677\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257812 78.065896) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 52.160938 49.559098 \n", "L 386.960938 49.559098 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#mef92e632c0\" x=\"52.160938\" y=\"49.559098\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257812 53.358317) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 52.160938 24.851519 \n", "L 386.960938 24.851519 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#mef92e632c0\" x=\"52.160938\" y=\"24.851519\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(29.257812 28.650738) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- x -->\n", "     <g transform=\"translate(14.798438 93.319375) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 52.160938 105.583812 \n", "L 52.496073 94.804074 \n", "L 52.831208 106.394654 \n", "L 53.166343 98.533636 \n", "L 53.501478 99.549721 \n", "L 54.171748 79.068618 \n", "L 54.506883 99.920647 \n", "L 54.842019 95.035606 \n", "L 55.177154 98.789676 \n", "L 55.512289 93.822276 \n", "L 55.847424 101.676877 \n", "L 56.517694 81.208426 \n", "L 56.852829 86.165799 \n", "L 57.187965 109.543262 \n", "L 57.5231 89.92117 \n", "L 57.858235 86.604717 \n", "L 58.19337 94.724822 \n", "L 58.528505 82.074924 \n", "L 58.86364 105.363935 \n", "L 59.198775 99.388709 \n", "L 59.53391 81.93069 \n", "L 59.869046 100.313657 \n", "L 60.204181 92.121941 \n", "L 60.539316 102.767995 \n", "L 60.874451 106.265236 \n", "L 61.209586 96.560301 \n", "L 61.544721 97.494985 \n", "L 62.214992 75.034718 \n", "L 62.550127 86.593731 \n", "L 62.885262 85.386404 \n", "L 63.220397 98.140538 \n", "L 63.555532 75.859384 \n", "L 63.890667 94.965102 \n", "L 64.225802 84.797775 \n", "L 64.560938 81.806644 \n", "L 64.896073 91.848399 \n", "L 65.231208 91.358788 \n", "L 65.566343 70.110635 \n", "L 65.901478 76.458818 \n", "L 66.236613 93.452876 \n", "L 66.571748 71.88023 \n", "L 66.906883 79.626615 \n", "L 67.242019 67.419327 \n", "L 67.577154 62.075048 \n", "L 67.912289 84.079484 \n", "L 68.247424 72.231003 \n", "L 68.582559 80.915615 \n", "L 68.917694 67.045939 \n", "L 69.252829 87.755265 \n", "L 69.9231 65.940314 \n", "L 70.258235 80.192526 \n", "L 70.59337 59.50564 \n", "L 70.928505 81.102487 \n", "L 71.26364 52.188008 \n", "L 71.93391 73.323141 \n", "L 72.269046 76.308726 \n", "L 72.604181 81.038516 \n", "L 73.274451 65.564575 \n", "L 73.609586 57.991132 \n", "L 73.944721 112.313584 \n", "L 74.614992 68.374467 \n", "L 74.950127 61.388841 \n", "L 75.285262 65.959824 \n", "L 75.620397 65.005058 \n", "L 75.955532 72.166467 \n", "L 76.290667 57.958651 \n", "L 76.625802 63.008389 \n", "L 76.960938 76.374998 \n", "L 77.296073 66.530996 \n", "L 77.631208 63.226385 \n", "L 77.966343 68.582346 \n", "L 78.301478 59.413597 \n", "L 78.636613 66.166916 \n", "L 78.971748 53.913025 \n", "L 79.306883 67.242021 \n", "L 79.642019 39.439015 \n", "L 79.977154 68.315027 \n", "L 80.312289 65.136916 \n", "L 80.647424 57.880139 \n", "L 80.982559 61.111584 \n", "L 81.317694 59.076319 \n", "L 81.652829 62.706173 \n", "L 81.987965 59.02403 \n", "L 82.3231 63.347428 \n", "L 82.658235 49.77206 \n", "L 83.328505 66.078896 \n", "L 83.66364 67.053417 \n", "L 83.998775 54.254131 \n", "L 84.33391 67.380595 \n", "L 84.669046 73.172159 \n", "L 85.339316 56.222336 \n", "L 86.009586 63.940167 \n", "L 86.679856 44.935546 \n", "L 87.014992 44.896119 \n", "L 87.350127 55.099012 \n", "L 88.020397 48.188156 \n", "L 88.355532 60.28769 \n", "L 88.690667 59.885549 \n", "L 89.025802 57.891805 \n", "L 89.360938 58.906554 \n", "L 89.696073 55.89522 \n", "L 90.031208 67.127711 \n", "L 90.366343 54.938789 \n", "L 90.701478 62.812216 \n", "L 91.036613 41.272639 \n", "L 91.371748 53.238779 \n", "L 91.706883 31.426681 \n", "L 92.042019 51.987207 \n", "L 92.377154 46.146029 \n", "L 92.712289 33.067984 \n", "L 93.047424 54.824862 \n", "L 93.382559 69.219474 \n", "L 93.717694 65.21653 \n", "L 94.052829 53.771791 \n", "L 94.387965 58.44279 \n", "L 94.7231 53.124104 \n", "L 95.058235 51.101999 \n", "L 95.39337 57.907107 \n", "L 95.728505 47.25855 \n", "L 96.06364 59.425028 \n", "L 96.398775 60.026017 \n", "L 96.73391 63.507827 \n", "L 97.069046 57.368978 \n", "L 97.404181 58.305881 \n", "L 97.739316 38.720809 \n", "L 98.074451 36.028991 \n", "L 98.409586 50.000212 \n", "L 98.744721 39.574142 \n", "L 99.079856 52.544089 \n", "L 99.414992 55.685223 \n", "L 99.750127 48.834248 \n", "L 100.085262 39.236096 \n", "L 100.420397 57.103744 \n", "L 100.755532 58.567439 \n", "L 101.425802 14.76 \n", "L 101.760938 66.237461 \n", "L 102.096073 29.964569 \n", "L 102.431208 51.11356 \n", "L 102.766343 56.15828 \n", "L 103.101478 72.76307 \n", "L 103.436613 40.501481 \n", "L 103.771748 51.921629 \n", "L 104.106883 48.328987 \n", "L 104.442019 49.016513 \n", "L 104.777154 60.799274 \n", "L 105.112289 58.030403 \n", "L 106.117694 42.317027 \n", "L 106.452829 54.472186 \n", "L 106.787965 58.074186 \n", "L 107.1231 51.633115 \n", "L 107.458235 42.115033 \n", "L 107.79337 41.430417 \n", "L 108.128505 41.407714 \n", "L 108.798775 47.907439 \n", "L 109.13391 45.81646 \n", "L 109.469046 73.686326 \n", "L 109.804181 59.305743 \n", "L 110.139316 56.885501 \n", "L 110.474451 61.501489 \n", "L 110.809586 45.571216 \n", "L 111.144721 56.499905 \n", "L 111.479856 63.087213 \n", "L 111.814992 48.359236 \n", "L 112.150127 45.328082 \n", "L 112.485262 48.72792 \n", "L 112.820397 54.885781 \n", "L 113.155532 58.784027 \n", "L 113.490667 50.062954 \n", "L 113.825802 55.07212 \n", "L 114.160938 54.513027 \n", "L 114.496073 43.352007 \n", "L 114.831208 49.780932 \n", "L 115.166343 36.918818 \n", "L 115.501478 50.251697 \n", "L 115.836613 41.922677 \n", "L 116.171748 43.890828 \n", "L 116.506883 50.693441 \n", "L 116.842019 34.339508 \n", "L 117.177154 53.457546 \n", "L 117.512289 50.42011 \n", "L 117.847424 48.747112 \n", "L 118.182559 60.237654 \n", "L 118.517694 51.074195 \n", "L 118.852829 30.74924 \n", "L 119.187965 56.002051 \n", "L 119.5231 50.952813 \n", "L 119.858235 61.139108 \n", "L 120.19337 45.530228 \n", "L 120.528505 53.368584 \n", "L 120.86364 57.824683 \n", "L 121.198775 66.24359 \n", "L 121.53391 58.33934 \n", "L 121.869046 60.374566 \n", "L 122.204181 45.18969 \n", "L 122.874451 72.876623 \n", "L 123.209586 62.95418 \n", "L 123.544721 64.476746 \n", "L 123.879856 35.960028 \n", "L 124.214992 48.808258 \n", "L 124.550127 66.615358 \n", "L 124.885262 72.925902 \n", "L 125.220397 61.225393 \n", "L 125.555532 62.629417 \n", "L 125.890667 46.755191 \n", "L 126.225802 68.233166 \n", "L 126.560938 53.459264 \n", "L 126.896073 66.603229 \n", "L 127.566343 62.624949 \n", "L 127.901478 76.390488 \n", "L 128.236613 47.62248 \n", "L 128.571748 62.140629 \n", "L 128.906883 58.309748 \n", "L 129.242019 63.833438 \n", "L 129.577154 57.808534 \n", "L 130.247424 76.234156 \n", "L 130.582559 73.943375 \n", "L 130.917694 45.091562 \n", "L 131.252829 53.894166 \n", "L 131.587965 57.817441 \n", "L 131.9231 72.449553 \n", "L 132.258235 69.321045 \n", "L 132.59337 71.077053 \n", "L 132.928505 84.438497 \n", "L 133.598775 50.093079 \n", "L 133.93391 66.383204 \n", "L 134.269046 61.580614 \n", "L 134.604181 62.370872 \n", "L 134.939316 67.192851 \n", "L 135.274451 62.727766 \n", "L 135.609586 80.110275 \n", "L 135.944721 61.966316 \n", "L 136.279856 68.656643 \n", "L 136.614992 63.841379 \n", "L 136.950127 78.626438 \n", "L 137.285262 69.368595 \n", "L 137.620397 85.511268 \n", "L 137.955532 62.305152 \n", "L 138.290667 73.775253 \n", "L 138.625802 90.446416 \n", "L 138.960938 69.18466 \n", "L 139.296073 87.92926 \n", "L 139.631208 71.289093 \n", "L 139.966343 72.629547 \n", "L 140.301478 80.585135 \n", "L 140.636613 70.010804 \n", "L 140.971748 80.579282 \n", "L 141.306883 72.895944 \n", "L 141.642019 84.272421 \n", "L 141.977154 72.209949 \n", "L 142.312289 70.484918 \n", "L 142.647424 88.364588 \n", "L 142.982559 80.747462 \n", "L 143.317694 98.86758 \n", "L 143.652829 62.952775 \n", "L 143.987965 78.430715 \n", "L 144.3231 74.930941 \n", "L 144.658235 87.79387 \n", "L 144.99337 79.56642 \n", "L 145.328505 65.611946 \n", "L 145.66364 70.958375 \n", "L 145.998775 82.716283 \n", "L 146.33391 89.922572 \n", "L 146.669046 76.000462 \n", "L 147.004181 74.16727 \n", "L 147.339316 83.129049 \n", "L 147.674451 95.526622 \n", "L 148.009586 66.909898 \n", "L 148.344721 100.058904 \n", "L 148.679856 94.632688 \n", "L 149.014992 66.081874 \n", "L 149.350127 66.782861 \n", "L 149.685262 88.541889 \n", "L 150.020397 84.981139 \n", "L 150.355532 70.222963 \n", "L 150.690667 86.977045 \n", "L 151.025802 84.171514 \n", "L 151.360938 87.002279 \n", "L 151.696073 86.154412 \n", "L 152.031208 98.468142 \n", "L 152.366343 103.402409 \n", "L 152.701478 97.656728 \n", "L 153.036613 106.171115 \n", "L 153.371748 100.474446 \n", "L 153.706883 92.141414 \n", "L 154.377154 84.953674 \n", "L 154.712289 109.405408 \n", "L 155.047424 97.979508 \n", "L 155.382559 79.607575 \n", "L 155.717694 98.746384 \n", "L 156.052829 102.835397 \n", "L 156.387965 100.245776 \n", "L 156.7231 99.764812 \n", "L 157.058235 82.157295 \n", "L 157.39337 111.781375 \n", "L 157.728505 109.922582 \n", "L 158.06364 97.303333 \n", "L 158.398775 105.293208 \n", "L 158.73391 92.85851 \n", "L 159.069046 91.590997 \n", "L 159.404181 89.31134 \n", "L 159.739316 95.058881 \n", "L 160.074451 109.000386 \n", "L 160.409586 92.103002 \n", "L 160.744721 96.510845 \n", "L 161.079856 124.340909 \n", "L 161.414992 106.068056 \n", "L 161.750127 101.7894 \n", "L 162.085262 91.485304 \n", "L 162.420397 117.342794 \n", "L 162.755532 103.517954 \n", "L 163.090667 106.160482 \n", "L 163.425802 114.561178 \n", "L 163.760938 98.037159 \n", "L 164.096073 115.739653 \n", "L 164.431208 123.06978 \n", "L 164.766343 108.836396 \n", "L 165.101478 105.377028 \n", "L 165.436613 120.989805 \n", "L 165.771748 115.526147 \n", "L 166.106883 99.794337 \n", "L 166.442019 127.140079 \n", "L 166.777154 124.301632 \n", "L 167.112289 109.581128 \n", "L 167.447424 107.549332 \n", "L 167.782559 112.016373 \n", "L 168.117694 118.791959 \n", "L 168.452829 91.207164 \n", "L 168.787965 91.921319 \n", "L 169.1231 116.337438 \n", "L 169.458235 112.364182 \n", "L 169.79337 132.702275 \n", "L 170.128505 115.845078 \n", "L 170.46364 114.863516 \n", "L 170.798775 114.650208 \n", "L 171.13391 102.193754 \n", "L 171.469046 135.085264 \n", "L 171.804181 131.640636 \n", "L 172.139316 116.268365 \n", "L 172.474451 120.522816 \n", "L 172.809586 138.537477 \n", "L 173.144721 123.317135 \n", "L 173.479856 146.912697 \n", "L 173.814992 144.439061 \n", "L 174.150127 124.261113 \n", "L 174.485262 120.044256 \n", "L 174.820397 106.506621 \n", "L 175.155532 125.52362 \n", "L 175.490667 119.716966 \n", "L 175.825802 118.074201 \n", "L 176.160938 125.035345 \n", "L 176.496073 109.289929 \n", "L 176.831208 111.273012 \n", "L 177.166343 127.822958 \n", "L 177.501478 128.303575 \n", "L 177.836613 124.952256 \n", "L 178.171748 130.252249 \n", "L 178.506883 115.431269 \n", "L 179.177154 124.34576 \n", "L 179.512289 143.162731 \n", "L 179.847424 129.612941 \n", "L 180.182559 136.109638 \n", "L 180.517694 133.848832 \n", "L 180.852829 130.374264 \n", "L 181.187965 131.487052 \n", "L 181.5231 138.316055 \n", "L 182.19337 128.524781 \n", "L 182.528505 113.506767 \n", "L 182.86364 123.098845 \n", "L 183.198775 136.159318 \n", "L 183.53391 135.179351 \n", "L 183.869046 119.461779 \n", "L 184.204181 151.033822 \n", "L 184.874451 121.277934 \n", "L 185.209586 132.160909 \n", "L 185.544721 131.666534 \n", "L 185.879856 135.797417 \n", "L 186.214992 131.565558 \n", "L 186.885262 130.617566 \n", "L 187.220397 150.515914 \n", "L 187.555532 133.534201 \n", "L 187.890667 135.299722 \n", "L 188.225802 152.54041 \n", "L 188.560938 133.371384 \n", "L 188.896073 146.042201 \n", "L 189.231208 139.097136 \n", "L 189.566343 139.426817 \n", "L 189.901478 143.234224 \n", "L 190.236613 160.360881 \n", "L 190.571748 132.51274 \n", "L 190.906883 140.453397 \n", "L 191.242019 159.014987 \n", "L 191.577154 147.713391 \n", "L 191.912289 146.170852 \n", "L 192.247424 128.799885 \n", "L 192.582559 138.901584 \n", "L 192.917694 133.768131 \n", "L 193.252829 154.187727 \n", "L 193.587965 153.783245 \n", "L 193.9231 139.563927 \n", "L 194.258235 138.461082 \n", "L 194.59337 132.97395 \n", "L 194.928505 132.022895 \n", "L 195.26364 146.887173 \n", "L 195.598775 152.167855 \n", "L 195.93391 138.892786 \n", "L 196.269046 147.07064 \n", "L 196.604181 126.448388 \n", "L 196.939316 135.563024 \n", "L 197.274451 149.714454 \n", "L 197.609586 140.281983 \n", "L 197.944721 140.695837 \n", "L 198.279856 163.704416 \n", "L 198.614992 133.666734 \n", "L 198.950127 160.205295 \n", "L 199.285262 144.456533 \n", "L 199.955532 151.893624 \n", "L 200.290667 125.609292 \n", "L 200.625802 149.837076 \n", "L 200.960938 137.357908 \n", "L 201.296073 142.481843 \n", "L 201.631208 145.578988 \n", "L 201.966343 146.195723 \n", "L 202.301478 152.308409 \n", "L 202.636613 134.008038 \n", "L 202.971748 146.188183 \n", "L 203.306883 146.888286 \n", "L 203.642019 148.312951 \n", "L 203.977154 148.199543 \n", "L 204.647424 153.682231 \n", "L 204.982559 147.158377 \n", "L 205.317694 150.584011 \n", "L 205.652829 144.846395 \n", "L 205.987965 131.311101 \n", "L 206.3231 140.868521 \n", "L 206.658235 143.408847 \n", "L 206.99337 159.660731 \n", "L 207.328505 156.36308 \n", "L 207.66364 144.604518 \n", "L 208.33391 155.695034 \n", "L 208.669046 148.993727 \n", "L 209.004181 153.324262 \n", "L 209.339316 154.662309 \n", "L 209.674451 138.145389 \n", "L 210.344721 156.694711 \n", "L 210.679856 142.625913 \n", "L 211.350127 163.258174 \n", "L 211.685262 148.126324 \n", "L 212.020397 157.488966 \n", "L 212.355532 138.987515 \n", "L 212.690667 138.152758 \n", "L 213.025802 148.557806 \n", "L 213.360938 148.78209 \n", "L 213.696073 143.521248 \n", "L 214.366343 165.96 \n", "L 215.036613 129.736584 \n", "L 215.371748 146.894374 \n", "L 215.706883 140.281146 \n", "L 216.042019 151.783908 \n", "L 216.377154 145.225977 \n", "L 216.712289 155.037886 \n", "L 217.047424 143.835051 \n", "L 217.382559 139.470479 \n", "L 217.717694 144.479666 \n", "L 218.052829 155.043953 \n", "L 218.387965 147.034654 \n", "L 218.7231 162.383934 \n", "L 219.058235 154.769721 \n", "L 219.39337 149.71152 \n", "L 219.728505 149.644443 \n", "L 220.06364 159.87777 \n", "L 220.398775 139.463334 \n", "L 220.73391 142.852442 \n", "L 221.069046 142.146907 \n", "L 221.404181 131.419653 \n", "L 221.739316 142.594586 \n", "L 222.074451 124.487653 \n", "L 222.409586 157.055666 \n", "L 222.744721 140.330148 \n", "L 223.079856 143.959269 \n", "L 223.414992 155.862137 \n", "L 223.750127 152.510402 \n", "L 224.085262 146.785831 \n", "L 224.420397 135.963436 \n", "L 224.755532 140.189321 \n", "L 225.090667 133.128932 \n", "L 225.425802 149.03855 \n", "L 225.760938 154.746794 \n", "L 226.096073 127.443123 \n", "L 226.431208 132.994403 \n", "L 226.766343 145.019185 \n", "L 227.101478 127.898124 \n", "L 227.436613 148.649825 \n", "L 227.771748 147.270849 \n", "L 228.106883 137.102986 \n", "L 228.442019 132.00848 \n", "L 228.777154 147.847261 \n", "L 229.112289 152.723783 \n", "L 229.447424 134.059332 \n", "L 229.782559 135.717061 \n", "L 230.117694 143.770633 \n", "L 230.452829 142.919817 \n", "L 230.787965 130.67384 \n", "L 231.1231 144.821774 \n", "L 231.79337 130.406901 \n", "L 232.128505 140.121657 \n", "L 232.46364 140.019621 \n", "L 232.798775 134.661746 \n", "L 233.13391 150.397557 \n", "L 233.469046 119.753255 \n", "L 233.804181 121.982982 \n", "L 234.139316 120.764061 \n", "L 234.474451 113.893976 \n", "L 234.809586 131.283503 \n", "L 235.144721 139.401899 \n", "L 235.479856 134.794382 \n", "L 235.814992 122.126381 \n", "L 236.150127 136.09082 \n", "L 236.485262 138.741467 \n", "L 236.820397 123.507801 \n", "L 237.155532 135.389271 \n", "L 237.490667 134.643635 \n", "L 237.825802 120.351425 \n", "L 238.160938 123.895415 \n", "L 238.496073 115.101882 \n", "L 238.831208 142.119957 \n", "L 239.166343 126.445419 \n", "L 239.501478 144.452604 \n", "L 240.171748 106.555685 \n", "L 240.506883 128.407618 \n", "L 240.842019 107.911829 \n", "L 241.177154 125.070009 \n", "L 241.512289 119.451207 \n", "L 241.847424 131.80737 \n", "L 242.182559 120.378116 \n", "L 242.517694 129.777237 \n", "L 243.187965 128.530457 \n", "L 243.5231 123.47079 \n", "L 243.858235 134.493827 \n", "L 244.19337 131.036163 \n", "L 244.528505 136.151769 \n", "L 244.86364 137.154919 \n", "L 245.198775 146.374619 \n", "L 245.53391 111.587794 \n", "L 245.869046 125.615716 \n", "L 246.204181 121.700977 \n", "L 246.539316 135.926079 \n", "L 246.874451 136.77093 \n", "L 247.209586 128.228277 \n", "L 247.544721 133.269162 \n", "L 247.879856 104.965384 \n", "L 248.214992 138.52303 \n", "L 248.885262 108.020454 \n", "L 249.220397 125.392495 \n", "L 249.555532 110.962805 \n", "L 249.890667 129.681279 \n", "L 250.225802 117.089621 \n", "L 250.560938 116.705333 \n", "L 250.896073 113.377745 \n", "L 251.231208 104.590828 \n", "L 251.566343 134.075702 \n", "L 251.901478 132.804956 \n", "L 252.236613 110.761928 \n", "L 252.571748 119.49309 \n", "L 252.906883 114.438924 \n", "L 253.242019 112.579072 \n", "L 253.577154 98.839299 \n", "L 253.912289 122.084341 \n", "L 254.247424 99.392272 \n", "L 254.582559 110.842232 \n", "L 254.917694 95.90922 \n", "L 255.252829 120.260589 \n", "L 255.587965 94.446097 \n", "L 255.9231 96.986956 \n", "L 256.258235 105.726656 \n", "L 256.59337 98.333678 \n", "L 256.928505 102.675546 \n", "L 257.26364 97.857437 \n", "L 257.598775 121.758378 \n", "L 257.93391 94.820189 \n", "L 258.269046 93.561105 \n", "L 258.939316 106.137579 \n", "L 259.274451 88.688915 \n", "L 259.609586 112.011292 \n", "L 259.944721 109.60938 \n", "L 260.279856 108.034489 \n", "L 260.614992 91.545789 \n", "L 260.950127 109.686891 \n", "L 261.285262 100.488363 \n", "L 261.955532 87.487764 \n", "L 262.290667 109.772459 \n", "L 262.625802 111.934721 \n", "L 262.960938 104.912966 \n", "L 263.296073 80.416518 \n", "L 263.631208 101.060939 \n", "L 263.966343 104.840612 \n", "L 264.301478 85.812226 \n", "L 264.636613 91.053375 \n", "L 264.971748 100.107304 \n", "L 265.306883 92.331848 \n", "L 265.642019 105.79277 \n", "L 265.977154 95.386252 \n", "L 266.312289 78.286852 \n", "L 266.647424 98.239681 \n", "L 266.982559 88.435769 \n", "L 267.317694 84.349952 \n", "L 267.652829 98.421239 \n", "L 267.987965 90.590158 \n", "L 268.658235 80.841742 \n", "L 268.99337 95.678522 \n", "L 269.328505 78.21545 \n", "L 269.66364 83.660019 \n", "L 270.33391 101.930725 \n", "L 270.669046 80.112662 \n", "L 271.004181 80.476094 \n", "L 271.339316 69.243396 \n", "L 271.674451 92.737777 \n", "L 272.009586 91.409695 \n", "L 272.679856 75.155911 \n", "L 273.014992 77.319668 \n", "L 273.350127 74.821305 \n", "L 273.685262 94.969223 \n", "L 274.020397 100.146038 \n", "L 274.355532 88.006744 \n", "L 275.025802 86.620372 \n", "L 275.360937 76.530508 \n", "L 275.696073 79.737919 \n", "L 276.031208 78.953741 \n", "L 276.366343 73.306082 \n", "L 276.701478 79.702807 \n", "L 277.036613 79.634722 \n", "L 277.371748 71.29236 \n", "L 277.706883 78.349285 \n", "L 278.377154 86.065984 \n", "L 278.712289 66.257793 \n", "L 279.047424 73.018722 \n", "L 279.382559 64.349164 \n", "L 279.717694 77.137937 \n", "L 280.052829 69.351924 \n", "L 280.387965 74.057826 \n", "L 280.7231 66.947001 \n", "L 281.058235 54.618309 \n", "L 281.39337 67.320845 \n", "L 281.728505 66.752886 \n", "L 282.06364 59.3763 \n", "L 282.398775 77.925778 \n", "L 282.73391 60.083996 \n", "L 283.069046 75.191751 \n", "L 283.404181 74.872443 \n", "L 283.739316 64.276811 \n", "L 284.074451 69.358413 \n", "L 284.409586 63.111763 \n", "L 284.744721 61.943955 \n", "L 285.079856 83.083319 \n", "L 285.414992 56.955106 \n", "L 286.085262 65.064295 \n", "L 286.420397 77.062039 \n", "L 286.755532 83.320045 \n", "L 287.760938 56.288506 \n", "L 288.096073 64.946495 \n", "L 288.431208 48.916506 \n", "L 288.766343 69.816298 \n", "L 289.101478 50.967752 \n", "L 289.436613 61.623504 \n", "L 289.771748 60.494558 \n", "L 290.442019 73.136095 \n", "L 290.777154 72.859955 \n", "L 291.112289 63.623834 \n", "L 291.447424 71.371637 \n", "L 291.782559 64.634839 \n", "L 292.117694 77.855923 \n", "L 292.452829 49.196434 \n", "L 293.1231 64.420216 \n", "L 293.458235 49.923346 \n", "L 293.79337 59.540666 \n", "L 294.128505 45.643973 \n", "L 294.46364 67.500793 \n", "L 294.798775 66.924878 \n", "L 295.469046 50.744419 \n", "L 295.804181 53.349333 \n", "L 296.139316 44.687339 \n", "L 296.474451 45.545409 \n", "L 297.144721 60.936178 \n", "L 297.479856 38.175243 \n", "L 297.814992 58.346509 \n", "L 298.150127 37.239911 \n", "L 298.485262 53.885382 \n", "L 298.820397 36.948178 \n", "L 299.155532 54.736489 \n", "L 299.490667 48.033259 \n", "L 299.825802 55.538544 \n", "L 300.160938 70.201461 \n", "L 300.496073 71.066211 \n", "L 300.831208 66.452691 \n", "L 301.166343 52.888159 \n", "L 301.501478 65.937745 \n", "L 301.836613 42.905665 \n", "L 302.171748 38.765967 \n", "L 302.506883 53.717499 \n", "L 302.842019 60.945518 \n", "L 303.177154 63.942909 \n", "L 303.512289 65.913263 \n", "L 303.847424 44.216704 \n", "L 304.182559 59.431149 \n", "L 304.517694 41.992241 \n", "L 304.852829 61.208599 \n", "L 305.187965 59.352863 \n", "L 305.5231 45.73687 \n", "L 305.858235 58.347248 \n", "L 306.19337 60.275507 \n", "L 306.86364 47.140888 \n", "L 307.198775 56.22301 \n", "L 307.53391 54.422881 \n", "L 307.869046 51.068092 \n", "L 308.204181 60.625839 \n", "L 308.539316 49.956871 \n", "L 308.874451 45.788938 \n", "L 309.209586 50.000895 \n", "L 309.544721 32.822311 \n", "L 310.214992 54.061799 \n", "L 310.550127 53.243162 \n", "L 310.885262 56.232041 \n", "L 311.220397 54.49534 \n", "L 311.555532 65.694622 \n", "L 311.890667 34.534097 \n", "L 312.225802 68.342127 \n", "L 312.560938 42.588661 \n", "L 312.896073 36.422688 \n", "L 313.231208 48.230311 \n", "L 313.566343 33.353739 \n", "L 313.901478 59.449469 \n", "L 314.236613 38.387905 \n", "L 314.571748 56.794993 \n", "L 314.906883 52.668799 \n", "L 315.242019 64.37979 \n", "L 315.577154 40.384774 \n", "L 315.912289 53.597181 \n", "L 316.247424 46.700702 \n", "L 316.917694 70.150253 \n", "L 317.252829 58.792612 \n", "L 317.587965 33.039255 \n", "L 317.9231 49.098041 \n", "L 318.258235 31.417539 \n", "L 318.59337 41.441485 \n", "L 318.928505 42.578741 \n", "L 319.26364 46.547236 \n", "L 319.598775 46.142725 \n", "L 319.93391 44.216327 \n", "L 320.269046 41.37608 \n", "L 320.604181 42.422236 \n", "L 320.939316 51.21709 \n", "L 321.274451 36.675518 \n", "L 321.609586 43.193546 \n", "L 321.944721 24.842495 \n", "L 322.279856 51.731337 \n", "L 322.614992 29.872956 \n", "L 323.285262 49.327409 \n", "L 323.620397 37.253524 \n", "L 323.955532 55.186442 \n", "L 324.290667 37.944362 \n", "L 324.625802 32.659308 \n", "L 324.960938 62.671474 \n", "L 325.296073 61.085883 \n", "L 325.631208 49.064582 \n", "L 325.966343 48.924093 \n", "L 326.301478 41.57484 \n", "L 326.636613 56.992082 \n", "L 326.971748 47.249307 \n", "L 327.306883 51.112167 \n", "L 327.642019 65.738031 \n", "L 327.977154 32.373619 \n", "L 328.312289 42.747199 \n", "L 328.647424 44.313895 \n", "L 328.982559 57.803435 \n", "L 329.317694 60.252321 \n", "L 329.652829 46.168667 \n", "L 329.987965 50.442574 \n", "L 330.3231 60.789216 \n", "L 330.658235 66.019898 \n", "L 330.99337 51.196522 \n", "L 331.328505 48.80196 \n", "L 331.66364 58.524015 \n", "L 331.998775 77.382669 \n", "L 332.33391 53.021546 \n", "L 332.669046 53.046897 \n", "L 333.004181 49.509533 \n", "L 333.339316 54.584464 \n", "L 333.674451 67.829031 \n", "L 334.009586 53.103 \n", "L 334.344721 61.348642 \n", "L 334.679856 48.388371 \n", "L 335.014992 68.323433 \n", "L 335.350127 62.178221 \n", "L 335.685262 60.60298 \n", "L 336.020397 47.153088 \n", "L 336.690667 75.128776 \n", "L 337.025802 60.846433 \n", "L 337.360937 54.606142 \n", "L 337.696073 54.722684 \n", "L 338.366343 78.235954 \n", "L 338.701478 48.348361 \n", "L 339.036613 75.827576 \n", "L 339.706883 54.680666 \n", "L 340.042019 61.073058 \n", "L 340.377154 48.886416 \n", "L 340.712289 56.936085 \n", "L 341.047424 54.918048 \n", "L 341.382559 66.087011 \n", "L 341.717694 67.771137 \n", "L 342.052829 65.658804 \n", "L 342.387965 69.923062 \n", "L 342.7231 77.942339 \n", "L 343.058235 65.0413 \n", "L 343.39337 74.352614 \n", "L 343.728505 69.748613 \n", "L 344.398775 74.708388 \n", "L 344.73391 80.626607 \n", "L 345.069046 70.636823 \n", "L 345.404181 74.660922 \n", "L 345.739316 68.91861 \n", "L 346.074451 61.197671 \n", "L 346.409586 58.080695 \n", "L 346.744721 82.559794 \n", "L 347.079856 80.173878 \n", "L 347.414992 62.226599 \n", "L 347.750127 57.66272 \n", "L 348.085262 71.80269 \n", "L 348.420397 64.414443 \n", "L 348.755532 73.288869 \n", "L 349.090667 67.535731 \n", "L 349.425802 71.767858 \n", "L 349.760938 60.335051 \n", "L 350.096073 79.528182 \n", "L 350.431208 78.278965 \n", "L 350.766343 61.148887 \n", "L 351.101478 76.369013 \n", "L 351.436613 80.278112 \n", "L 352.106883 95.500219 \n", "L 352.442019 90.263349 \n", "L 352.777154 71.979073 \n", "L 353.112289 80.261463 \n", "L 353.447424 76.203639 \n", "L 353.782559 75.734586 \n", "L 354.452829 93.281876 \n", "L 354.787965 77.113362 \n", "L 355.1231 79.119968 \n", "L 355.458235 79.911432 \n", "L 355.79337 83.147953 \n", "L 356.128505 81.63008 \n", "L 356.46364 85.909144 \n", "L 356.798775 104.663987 \n", "L 357.13391 83.643712 \n", "L 357.469046 86.127062 \n", "L 358.139316 78.331698 \n", "L 358.474451 91.451595 \n", "L 358.809586 81.595619 \n", "L 359.144721 77.807569 \n", "L 359.479856 102.793966 \n", "L 359.814992 94.130027 \n", "L 360.485262 89.690917 \n", "L 360.820397 99.40689 \n", "L 361.155532 103.452228 \n", "L 361.825802 92.188519 \n", "L 362.160938 100.919378 \n", "L 362.496073 116.533894 \n", "L 362.831208 100.857607 \n", "L 363.166343 65.554909 \n", "L 363.501478 87.185537 \n", "L 363.836613 82.845513 \n", "L 364.171748 93.621014 \n", "L 364.506883 91.451973 \n", "L 365.177154 78.730569 \n", "L 365.512289 105.136044 \n", "L 365.847424 101.709871 \n", "L 366.182559 94.861749 \n", "L 366.517694 111.546447 \n", "L 366.852829 100.967713 \n", "L 367.187965 99.380449 \n", "L 367.5231 92.065612 \n", "L 367.858235 117.240448 \n", "L 368.19337 100.104055 \n", "L 368.528505 98.844075 \n", "L 368.86364 110.904707 \n", "L 369.198775 87.598449 \n", "L 369.53391 99.117831 \n", "L 369.869046 101.765372 \n", "L 370.204181 97.135244 \n", "L 370.539316 112.811674 \n", "L 370.874451 115.201565 \n", "L 371.209586 96.582702 \n", "L 371.544721 110.064962 \n", "L 371.879856 109.495089 \n", "L 372.214992 109.533082 \n", "L 372.550127 114.553997 \n", "L 372.885262 101.578653 \n", "L 373.220397 106.177504 \n", "L 373.555532 106.553451 \n", "L 373.890667 110.410071 \n", "L 374.225802 121.577902 \n", "L 374.560938 111.893228 \n", "L 374.896073 106.844392 \n", "L 375.231208 93.993039 \n", "L 375.566343 111.168828 \n", "L 375.901478 107.357956 \n", "L 376.236613 116.024352 \n", "L 376.571748 109.570673 \n", "L 376.906883 129.176501 \n", "L 377.242019 120.971758 \n", "L 377.577154 118.93349 \n", "L 377.912289 118.657991 \n", "L 378.247424 112.727944 \n", "L 378.917694 116.28109 \n", "L 379.252829 132.048331 \n", "L 379.587965 121.843832 \n", "L 379.9231 119.037252 \n", "L 380.258235 117.822355 \n", "L 380.59337 135.758723 \n", "L 380.928505 111.553024 \n", "L 381.26364 109.077646 \n", "L 381.598775 115.702357 \n", "L 381.93391 112.345026 \n", "L 382.269046 126.040904 \n", "L 382.604181 128.749882 \n", "L 382.939316 129.870961 \n", "L 383.274451 134.600762 \n", "L 383.609586 104.934888 \n", "L 383.944721 140.617087 \n", "L 384.279856 120.10799 \n", "L 384.614992 106.950486 \n", "L 384.950127 138.321702 \n", "L 385.285262 127.813686 \n", "L 385.620397 122.497055 \n", "L 385.955532 115.075764 \n", "L 386.290667 122.758373 \n", "L 386.625802 120.168072 \n", "L 386.960938 127.09093 \n", "L 386.960938 127.09093 \n", "\" clip-path=\"url(#p1f35269b88)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.52 \n", "L 52.160938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 7.2 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p1f35269b88\">\n", "   <rect x=\"52.160938\" y=\"7.2\" width=\"334.8\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = Data()\n", "d2l.plot(data.time, data.x, 'time', 'x', xlim=[1, 1000], figsize=(6, 3))"]}, {"cell_type": "markdown", "id": "a74b512c", "metadata": {"origin_pos": 9}, "source": ["To begin, we try a model that acts as if\n", "the data satisfied a $\\tau^{\\textrm{th}}$-order Markov condition,\n", "and thus predicts $x_t$ using only the past $\\tau$ observations.\n", "[**Thus for each time step we have an example\n", "with label $y  = x_t$ and features\n", "$\\mathbf{x}_t = [x_{t-\\tau}, \\ldots, x_{t-1}]$.**]\n", "The astute reader might have noticed that\n", "this results in $1000-\\tau$ examples,\n", "since we lack sufficient history for $y_1, \\ldots, y_\\tau$.\n", "While we could pad the first $\\tau$ sequences with zeros,\n", "to keep things simple, we drop them for now.\n", "The resulting dataset contains $T - \\tau$ examples,\n", "where each input to the model has sequence length $\\tau$.\n", "We (**create a data iterator on the first 600 examples**),\n", "covering a period of the sin function.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "c15ae39a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:30.112040Z", "iopub.status.busy": "2023-08-18T19:31:30.111069Z", "iopub.status.idle": "2023-08-18T19:31:30.119552Z", "shell.execute_reply": "2023-08-18T19:31:30.118224Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(Data)\n", "def get_dataloader(self, train):\n", "    features = [self.x[i : self.T-self.tau+i] for i in range(self.tau)]\n", "    self.features = torch.stack(features, 1)\n", "    self.labels = self.x[self.tau:].reshape((-1, 1))\n", "    i = slice(0, self.num_train) if train else slice(self.num_train, None)\n", "    return self.get_tensorloader([self.features, self.labels], train, i)"]}, {"cell_type": "markdown", "id": "717e15ba", "metadata": {"origin_pos": 11}, "source": ["In this example our model will be a standard linear regression.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "33cdb63b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:30.123181Z", "iopub.status.busy": "2023-08-18T19:31:30.122830Z", "iopub.status.idle": "2023-08-18T19:31:33.416925Z", "shell.execute_reply": "2023-08-18T19:31:33.416098Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"242.146875pt\" height=\"183.35625pt\" viewBox=\"0 0 242.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-31T21:25:17.746056</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 242.**********.35625 \n", "L 242.146875 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 36.**********.8 \n", "L 231.**********.8 \n", "L 231.765625 7.2 \n", "L 36.465625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"md6390feecd\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md6390feecd\" x=\"36.465625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(33.284375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#md6390feecd\" x=\"75.525625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(72.344375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#md6390feecd\" x=\"114.585625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(111.404375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#md6390feecd\" x=\"153.645625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(150.464375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#md6390feecd\" x=\"192.705625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(189.524375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#md6390feecd\" x=\"231.765625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(228.584375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(118.8875 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m86b1f27d02\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m86b1f27d02\" x=\"36.465625\" y=\"136.907365\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.05 -->\n", "      <g transform=\"translate(7.2 140.706583) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m86b1f27d02\" x=\"36.465625\" y=\"98.604906\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.10 -->\n", "      <g transform=\"translate(7.2 102.404125) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m86b1f27d02\" x=\"36.465625\" y=\"60.302447\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.15 -->\n", "      <g transform=\"translate(7.2 64.101666) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m86b1f27d02\" x=\"36.465625\" y=\"21.999989\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.20 -->\n", "      <g transform=\"translate(7.2 25.799208) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 45.716678 13.5 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 75.525625 95.859141 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "L 84.776678 113.906509 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 75.525625 95.859141 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "L 84.776678 113.906509 \n", "L 104.306678 122.059511 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 75.525625 95.859141 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "L 84.776678 113.906509 \n", "L 104.306678 122.059511 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 75.525625 95.859141 \n", "L 114.585625 130.91191 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "L 84.776678 113.906509 \n", "L 104.306678 122.059511 \n", "L 123.836678 131.099031 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 75.525625 95.859141 \n", "L 114.585625 130.91191 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "L 84.776678 113.906509 \n", "L 104.306678 122.059511 \n", "L 123.836678 131.099031 \n", "L 143.366678 134.053876 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 75.525625 95.859141 \n", "L 114.585625 130.91191 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "L 84.776678 113.906509 \n", "L 104.306678 122.059511 \n", "L 123.836678 131.099031 \n", "L 143.366678 134.053876 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 75.525625 95.859141 \n", "L 114.585625 130.91191 \n", "L 153.645625 137.405171 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "L 84.776678 113.906509 \n", "L 104.306678 122.059511 \n", "L 123.836678 131.099031 \n", "L 143.366678 134.053876 \n", "L 162.896678 135.335749 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 75.525625 95.859141 \n", "L 114.585625 130.91191 \n", "L 153.645625 137.405171 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "L 84.776678 113.906509 \n", "L 104.306678 122.059511 \n", "L 123.836678 131.099031 \n", "L 143.366678 134.053876 \n", "L 162.896678 135.335749 \n", "L 182.426678 134.196061 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 75.525625 95.859141 \n", "L 114.585625 130.91191 \n", "L 153.645625 137.405171 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "L 84.776678 113.906509 \n", "L 104.306678 122.059511 \n", "L 123.836678 131.099031 \n", "L 143.366678 134.053876 \n", "L 162.896678 135.335749 \n", "L 182.426678 134.196061 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 75.525625 95.859141 \n", "L 114.585625 130.91191 \n", "L 153.645625 137.405171 \n", "L 192.705625 139.029849 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "L 84.776678 113.906509 \n", "L 104.306678 122.059511 \n", "L 123.836678 131.099031 \n", "L 143.366678 134.053876 \n", "L 162.896678 135.335749 \n", "L 182.426678 134.196061 \n", "L 201.956678 134.39101 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 75.525625 95.859141 \n", "L 114.585625 130.91191 \n", "L 153.645625 137.405171 \n", "L 192.705625 139.029849 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "L 84.776678 113.906509 \n", "L 104.306678 122.059511 \n", "L 123.836678 131.099031 \n", "L 143.366678 134.053876 \n", "L 162.896678 135.335749 \n", "L 182.426678 134.196061 \n", "L 201.956678 134.39101 \n", "L 221.486678 136.031934 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 75.525625 95.859141 \n", "L 114.585625 130.91191 \n", "L 153.645625 137.405171 \n", "L 192.705625 139.029849 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 45.716678 13.5 \n", "L 65.246678 78.763006 \n", "L 84.776678 113.906509 \n", "L 104.306678 122.059511 \n", "L 123.836678 131.099031 \n", "L 143.366678 134.053876 \n", "L 162.896678 135.335749 \n", "L 182.426678 134.196061 \n", "L 201.956678 134.39101 \n", "L 221.486678 136.031934 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 75.525625 95.859141 \n", "L 114.585625 130.91191 \n", "L 153.645625 137.405171 \n", "L 192.705625 139.029849 \n", "L 231.765625 139.5 \n", "\" clip-path=\"url(#p6505f27918)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 36.**********.8 \n", "L 36.465625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 231.**********.8 \n", "L 231.765625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 36.**********.8 \n", "L 231.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 36.465625 7.2 \n", "L 231.765625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 145.175 45.1125 \n", "L 224.765625 45.1125 \n", "Q 226.765625 45.1125 226.765625 43.1125 \n", "L 226.765625 14.2 \n", "Q 226.765625 12.2 224.765625 12.2 \n", "L 145.175 12.2 \n", "Q 143.175 12.2 143.175 14.2 \n", "L 143.175 43.1125 \n", "Q 143.175 45.1125 145.175 45.1125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_39\">\n", "     <path d=\"M 147.175 20.298438 \n", "L 157.175 20.298438 \n", "L 167.175 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(175.175 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_40\">\n", "     <path d=\"M 147.175 35.254688 \n", "L 157.175 35.254688 \n", "L 167.175 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(175.175 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p6505f27918\">\n", "   <rect x=\"36.465625\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = d2l.LinearRegression(lr=0.01)\n", "trainer = d2l.Trainer(max_epochs=5)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "ce3f7c6c", "metadata": {"origin_pos": 13}, "source": ["## Prediction\n", "\n", "[**To evaluate our model, we first check\n", "how well it performs at one-step-ahead prediction**].\n"]}, {"cell_type": "code", "execution_count": 14, "id": "28304131", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:33.422971Z", "iopub.status.busy": "2023-08-18T19:31:33.422165Z", "iopub.status.idle": "2023-08-18T19:31:33.722775Z", "shell.execute_reply": "2023-08-18T19:31:33.721919Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"394.160937pt\" height=\"211.07625pt\" viewBox=\"0 0 394.**********.07625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-31T21:25:43.210574</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 211.07625 \n", "L 394.**********.07625 \n", "L 394.160937 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "L 386.960938 7.2 \n", "L 52.160938 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 65.849654 173.52 \n", "L 65.849654 7.2 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m63d7da9619\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m63d7da9619\" x=\"65.849654\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(62.668404 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 127.028274 173.52 \n", "L 127.028274 7.2 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m63d7da9619\" x=\"127.028274\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(117.484524 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 188.206895 173.52 \n", "L 188.206895 7.2 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m63d7da9619\" x=\"188.206895\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(178.663145 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 249.385515 173.52 \n", "L 249.385515 7.2 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m63d7da9619\" x=\"249.385515\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(239.841765 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 310.564135 173.52 \n", "L 310.564135 7.2 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m63d7da9619\" x=\"310.564135\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(301.020385 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 371.742756 173.52 \n", "L 371.742756 7.2 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m63d7da9619\" x=\"371.742756\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(359.017756 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- time -->\n", "     <g transform=\"translate(208.264844 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 52.**********.096992 \n", "L 386.**********.096992 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"m7e83e4d697\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7e83e4d697\" x=\"52.160938\" y=\"173.096992\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1.5 -->\n", "      <g transform=\"translate(20.878125 176.896211) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 52.160938 148.389413 \n", "L 386.960938 148.389413 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m7e83e4d697\" x=\"52.160938\" y=\"148.389413\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 152.188632) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 52.160938 123.681834 \n", "L 386.960938 123.681834 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m7e83e4d697\" x=\"52.160938\" y=\"123.681834\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 127.481053) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 52.160938 98.974256 \n", "L 386.960938 98.974256 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m7e83e4d697\" x=\"52.160938\" y=\"98.974256\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257812 102.773474) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 52.160938 74.266677 \n", "L 386.960938 74.266677 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m7e83e4d697\" x=\"52.160938\" y=\"74.266677\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257812 78.065896) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 52.160938 49.559098 \n", "L 386.960938 49.559098 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m7e83e4d697\" x=\"52.160938\" y=\"49.559098\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257812 53.358317) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_25\">\n", "      <path d=\"M 52.160938 24.851519 \n", "L 386.960938 24.851519 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m7e83e4d697\" x=\"52.160938\" y=\"24.851519\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(29.257812 28.650738) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- x -->\n", "     <g transform=\"translate(14.798438 93.319375) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 67.379119 99.549721 \n", "L 67.990906 79.068618 \n", "L 68.296799 99.920647 \n", "L 68.602692 95.035606 \n", "L 68.908585 98.789676 \n", "L 69.214478 93.822276 \n", "L 69.520371 101.676877 \n", "L 70.132157 81.208426 \n", "L 70.43805 86.165799 \n", "L 70.743943 109.543262 \n", "L 71.049837 89.92117 \n", "L 71.35573 86.604717 \n", "L 71.661623 94.724822 \n", "L 71.967516 82.074924 \n", "L 72.273409 105.363935 \n", "L 72.579302 99.388709 \n", "L 72.885195 81.93069 \n", "L 73.191088 100.313657 \n", "L 73.496981 92.121941 \n", "L 73.802874 102.767995 \n", "L 74.108768 106.265236 \n", "L 74.414661 96.560301 \n", "L 74.720554 97.494985 \n", "L 75.33234 75.034718 \n", "L 75.638233 86.593731 \n", "L 75.944126 85.386404 \n", "L 76.250019 98.140538 \n", "L 76.555912 75.859384 \n", "L 76.861805 94.965102 \n", "L 77.167699 84.797775 \n", "L 77.473592 81.806644 \n", "L 77.779485 91.848399 \n", "L 78.085378 91.358788 \n", "L 78.391271 70.110635 \n", "L 78.697164 76.458818 \n", "L 79.003057 93.452876 \n", "L 79.30895 71.88023 \n", "L 79.614843 79.626615 \n", "L 79.920736 67.419327 \n", "L 80.22663 62.075048 \n", "L 80.532523 84.079484 \n", "L 80.838416 72.231003 \n", "L 81.144309 80.915615 \n", "L 81.450202 67.045939 \n", "L 81.756095 87.755265 \n", "L 82.367881 65.940314 \n", "L 82.673774 80.192526 \n", "L 82.979668 59.50564 \n", "L 83.285561 81.102487 \n", "L 83.591454 52.188008 \n", "L 84.20324 73.323141 \n", "L 84.509133 76.308726 \n", "L 84.815026 81.038516 \n", "L 85.426812 65.564575 \n", "L 85.732705 57.991132 \n", "L 86.038599 112.313584 \n", "L 86.650385 68.374467 \n", "L 86.956278 61.388841 \n", "L 87.262171 65.959824 \n", "L 87.568064 65.005058 \n", "L 87.873957 72.166467 \n", "L 88.17985 57.958651 \n", "L 88.485743 63.008389 \n", "L 88.791636 76.374998 \n", "L 89.09753 66.530996 \n", "L 89.403423 63.226385 \n", "L 89.709316 68.582346 \n", "L 90.015209 59.413597 \n", "L 90.321102 66.166916 \n", "L 90.626995 53.913025 \n", "L 90.932888 67.242021 \n", "L 91.238781 39.439015 \n", "L 91.544674 68.315027 \n", "L 91.850567 65.136916 \n", "L 92.156461 57.880139 \n", "L 92.462354 61.111584 \n", "L 92.768247 59.076319 \n", "L 93.07414 62.706173 \n", "L 93.380033 59.02403 \n", "L 93.685926 63.347428 \n", "L 93.991819 49.77206 \n", "L 94.603605 66.078896 \n", "L 94.909498 67.053417 \n", "L 95.215392 54.254131 \n", "L 95.521285 67.380595 \n", "L 95.827178 73.172159 \n", "L 96.438964 56.222336 \n", "L 97.05075 63.940167 \n", "L 97.662536 44.935546 \n", "L 97.96843 44.896119 \n", "L 98.274323 55.099012 \n", "L 98.886109 48.188156 \n", "L 99.192002 60.28769 \n", "L 99.497895 59.885549 \n", "L 99.803788 57.891805 \n", "L 100.109681 58.906554 \n", "L 100.415574 55.89522 \n", "L 100.721467 67.127711 \n", "L 101.027361 54.938789 \n", "L 101.333254 62.812216 \n", "L 101.639147 41.272639 \n", "L 101.94504 53.238779 \n", "L 102.250933 31.426681 \n", "L 102.556826 51.987207 \n", "L 102.862719 46.146029 \n", "L 103.168612 33.067984 \n", "L 103.780398 69.219474 \n", "L 104.086292 65.21653 \n", "L 104.392185 53.771791 \n", "L 104.698078 58.44279 \n", "L 105.003971 53.124104 \n", "L 105.309864 51.101999 \n", "L 105.615757 57.907107 \n", "L 105.92165 47.25855 \n", "L 106.227543 59.425028 \n", "L 106.533436 60.026017 \n", "L 106.839329 63.507827 \n", "L 107.145223 57.368978 \n", "L 107.451116 58.305881 \n", "L 107.757009 38.720809 \n", "L 108.062902 36.028991 \n", "L 108.368795 50.000212 \n", "L 108.674688 39.574142 \n", "L 108.980581 52.544089 \n", "L 109.286474 55.685223 \n", "L 109.592367 48.834248 \n", "L 109.89826 39.236096 \n", "L 110.204154 57.103744 \n", "L 110.510047 58.567439 \n", "L 111.121833 14.76 \n", "L 111.427726 66.237461 \n", "L 111.733619 29.964569 \n", "L 112.039512 51.11356 \n", "L 112.345405 56.15828 \n", "L 112.651298 72.76307 \n", "L 112.957191 40.501481 \n", "L 113.263085 51.921629 \n", "L 113.568978 48.328987 \n", "L 113.874871 49.016513 \n", "L 114.180764 60.799274 \n", "L 114.486657 58.030403 \n", "L 115.404336 42.317027 \n", "L 115.710229 54.472186 \n", "L 116.016123 58.074186 \n", "L 116.322016 51.633115 \n", "L 116.627909 42.115033 \n", "L 116.933802 41.430417 \n", "L 117.239695 41.407714 \n", "L 117.851481 47.907439 \n", "L 118.157374 45.81646 \n", "L 118.463267 73.686326 \n", "L 118.76916 59.305743 \n", "L 119.075054 56.885501 \n", "L 119.380947 61.501489 \n", "L 119.68684 45.571216 \n", "L 119.992733 56.499905 \n", "L 120.298626 63.087213 \n", "L 120.604519 48.359236 \n", "L 120.910412 45.328082 \n", "L 121.216305 48.72792 \n", "L 121.522198 54.885781 \n", "L 121.828091 58.784027 \n", "L 122.133985 50.062954 \n", "L 122.439878 55.07212 \n", "L 122.745771 54.513027 \n", "L 123.051664 43.352007 \n", "L 123.357557 49.780932 \n", "L 123.66345 36.918818 \n", "L 123.969343 50.251697 \n", "L 124.275236 41.922677 \n", "L 124.581129 43.890828 \n", "L 124.887022 50.693441 \n", "L 125.192916 34.339508 \n", "L 125.498809 53.457546 \n", "L 125.804702 50.42011 \n", "L 126.110595 48.747112 \n", "L 126.416488 60.237654 \n", "L 126.722381 51.074195 \n", "L 127.028274 30.74924 \n", "L 127.334167 56.002051 \n", "L 127.64006 50.952813 \n", "L 127.945953 61.139108 \n", "L 128.251847 45.530228 \n", "L 128.55774 53.368584 \n", "L 128.863633 57.824683 \n", "L 129.169526 66.24359 \n", "L 129.475419 58.33934 \n", "L 129.781312 60.374566 \n", "L 130.087205 45.18969 \n", "L 130.698991 72.876623 \n", "L 131.004885 62.95418 \n", "L 131.310778 64.476746 \n", "L 131.616671 35.960028 \n", "L 131.922564 48.808258 \n", "L 132.228457 66.615358 \n", "L 132.53435 72.925902 \n", "L 132.840243 61.225393 \n", "L 133.146136 62.629417 \n", "L 133.452029 46.755191 \n", "L 133.757922 68.233166 \n", "L 134.063816 53.459264 \n", "L 134.369709 66.603229 \n", "L 134.981495 62.624949 \n", "L 135.287388 76.390488 \n", "L 135.593281 47.62248 \n", "L 135.899174 62.140629 \n", "L 136.205067 58.309748 \n", "L 136.51096 63.833438 \n", "L 136.816853 57.808534 \n", "L 137.42864 76.234156 \n", "L 137.734533 73.943375 \n", "L 138.040426 45.091562 \n", "L 138.346319 53.894166 \n", "L 138.652212 57.817441 \n", "L 138.958105 72.449553 \n", "L 139.263998 69.321045 \n", "L 139.569891 71.077053 \n", "L 139.875784 84.438497 \n", "L 140.487571 50.093079 \n", "L 140.793464 66.383204 \n", "L 141.099357 61.580614 \n", "L 141.40525 62.370872 \n", "L 141.711143 67.192851 \n", "L 142.017036 62.727766 \n", "L 142.322929 80.110275 \n", "L 142.628822 61.966316 \n", "L 142.934715 68.656643 \n", "L 143.240609 63.841379 \n", "L 143.546502 78.626438 \n", "L 143.852395 69.368595 \n", "L 144.158288 85.511268 \n", "L 144.464181 62.305152 \n", "L 144.770074 73.775253 \n", "L 145.075967 90.446416 \n", "L 145.38186 69.18466 \n", "L 145.687753 87.92926 \n", "L 145.993646 71.289093 \n", "L 146.29954 72.629547 \n", "L 146.605433 80.585135 \n", "L 146.911326 70.010804 \n", "L 147.217219 80.579282 \n", "L 147.523112 72.895944 \n", "L 147.829005 84.272421 \n", "L 148.134898 72.209949 \n", "L 148.440791 70.484918 \n", "L 148.746684 88.364588 \n", "L 149.052578 80.747462 \n", "L 149.358471 98.86758 \n", "L 149.664364 62.952775 \n", "L 149.970257 78.430715 \n", "L 150.27615 74.930941 \n", "L 150.582043 87.79387 \n", "L 150.887936 79.56642 \n", "L 151.193829 65.611946 \n", "L 151.499722 70.958375 \n", "L 151.805615 82.716283 \n", "L 152.111509 89.922572 \n", "L 152.417402 76.000462 \n", "L 152.723295 74.16727 \n", "L 153.029188 83.129049 \n", "L 153.335081 95.526622 \n", "L 153.640974 66.909898 \n", "L 153.946867 100.058904 \n", "L 154.25276 94.632688 \n", "L 154.558653 66.081874 \n", "L 154.864546 66.782861 \n", "L 155.17044 88.541889 \n", "L 155.476333 84.981139 \n", "L 155.782226 70.222963 \n", "L 156.088119 86.977045 \n", "L 156.394012 84.171514 \n", "L 156.699905 87.002279 \n", "L 157.005798 86.154412 \n", "L 157.311691 98.468142 \n", "L 157.617584 103.402409 \n", "L 157.923477 97.656728 \n", "L 158.229371 106.171115 \n", "L 158.535264 100.474446 \n", "L 158.841157 92.141414 \n", "L 159.452943 84.953674 \n", "L 159.758836 109.405408 \n", "L 160.064729 97.979508 \n", "L 160.370622 79.607575 \n", "L 160.676515 98.746384 \n", "L 160.982408 102.835397 \n", "L 161.288302 100.245776 \n", "L 161.594195 99.764812 \n", "L 161.900088 82.157295 \n", "L 162.205981 111.781375 \n", "L 162.511874 109.922582 \n", "L 162.817767 97.303333 \n", "L 163.12366 105.293208 \n", "L 163.429553 92.85851 \n", "L 163.735446 91.590997 \n", "L 164.04134 89.31134 \n", "L 164.347233 95.058881 \n", "L 164.653126 109.000386 \n", "L 164.959019 92.103002 \n", "L 165.264912 96.510845 \n", "L 165.570805 124.340909 \n", "L 165.876698 106.068056 \n", "L 166.182591 101.7894 \n", "L 166.488484 91.485304 \n", "L 166.794377 117.342794 \n", "L 167.100271 103.517954 \n", "L 167.406164 106.160482 \n", "L 167.712057 114.561178 \n", "L 168.01795 98.037159 \n", "L 168.323843 115.739653 \n", "L 168.629736 123.06978 \n", "L 168.935629 108.836396 \n", "L 169.241522 105.377028 \n", "L 169.547415 120.989805 \n", "L 169.853308 115.526147 \n", "L 170.159202 99.794337 \n", "L 170.465095 127.140079 \n", "L 170.770988 124.301632 \n", "L 171.076881 109.581128 \n", "L 171.382774 107.549332 \n", "L 171.688667 112.016373 \n", "L 171.99456 118.791959 \n", "L 172.300453 91.207164 \n", "L 172.606346 91.921319 \n", "L 172.912239 116.337438 \n", "L 173.218133 112.364182 \n", "L 173.524026 132.702275 \n", "L 173.829919 115.845078 \n", "L 174.135812 114.863516 \n", "L 174.441705 114.650208 \n", "L 174.747598 102.193754 \n", "L 175.053491 135.085264 \n", "L 175.359384 131.640636 \n", "L 175.665277 116.268365 \n", "L 175.97117 120.522816 \n", "L 176.277064 138.537477 \n", "L 176.582957 123.317135 \n", "L 176.88885 146.912697 \n", "L 177.194743 144.439061 \n", "L 177.500636 124.261113 \n", "L 177.806529 120.044256 \n", "L 178.112422 106.506621 \n", "L 178.418315 125.52362 \n", "L 178.724208 119.716966 \n", "L 179.030102 118.074201 \n", "L 179.335995 125.035345 \n", "L 179.641888 109.289929 \n", "L 179.947781 111.273012 \n", "L 180.253674 127.822958 \n", "L 180.559567 128.303575 \n", "L 180.86546 124.952256 \n", "L 181.171353 130.252249 \n", "L 181.477246 115.431269 \n", "L 182.089033 124.34576 \n", "L 182.394926 143.162731 \n", "L 182.700819 129.612941 \n", "L 183.006712 136.109638 \n", "L 183.312605 133.848832 \n", "L 183.618498 130.374264 \n", "L 183.924391 131.487052 \n", "L 184.230284 138.316055 \n", "L 184.84207 128.524781 \n", "L 185.147964 113.506767 \n", "L 185.75975 136.159318 \n", "L 186.065643 135.179351 \n", "L 186.371536 119.461779 \n", "L 186.677429 151.033822 \n", "L 187.289215 121.277934 \n", "L 187.595108 132.160909 \n", "L 187.901001 131.666534 \n", "L 188.206895 135.797417 \n", "L 188.512788 131.565558 \n", "L 189.124574 130.617566 \n", "L 189.430467 150.515914 \n", "L 189.73636 133.534201 \n", "L 190.042253 135.299722 \n", "L 190.348146 152.54041 \n", "L 190.654039 133.371384 \n", "L 190.959932 146.042201 \n", "L 191.265826 139.097136 \n", "L 191.571719 139.426817 \n", "L 191.877612 143.234224 \n", "L 192.183505 160.360881 \n", "L 192.489398 132.51274 \n", "L 192.795291 140.453397 \n", "L 193.101184 159.014987 \n", "L 193.407077 147.713391 \n", "L 193.71297 146.170852 \n", "L 194.018863 128.799885 \n", "L 194.324757 138.901584 \n", "L 194.63065 133.768131 \n", "L 194.936543 154.187727 \n", "L 195.242436 153.783245 \n", "L 195.548329 139.563927 \n", "L 195.854222 138.461082 \n", "L 196.160115 132.97395 \n", "L 196.466008 132.022895 \n", "L 196.771901 146.887173 \n", "L 197.077795 152.167855 \n", "L 197.383688 138.892786 \n", "L 197.689581 147.07064 \n", "L 197.995474 126.448388 \n", "L 198.301367 135.563024 \n", "L 198.60726 149.714454 \n", "L 198.913153 140.281983 \n", "L 199.219046 140.695837 \n", "L 199.524939 163.704416 \n", "L 199.830832 133.666734 \n", "L 200.136726 160.205295 \n", "L 200.442619 144.456533 \n", "L 201.054405 151.893624 \n", "L 201.360298 125.609292 \n", "L 201.666191 149.837076 \n", "L 201.972084 137.357908 \n", "L 202.277977 142.481843 \n", "L 202.58387 145.578988 \n", "L 202.889763 146.195723 \n", "L 203.195657 152.308409 \n", "L 203.50155 134.008038 \n", "L 203.807443 146.188183 \n", "L 204.113336 146.888286 \n", "L 204.419229 148.312951 \n", "L 204.725122 148.199543 \n", "L 205.336908 153.682231 \n", "L 205.642801 147.158377 \n", "L 205.948694 150.584011 \n", "L 206.254588 144.846395 \n", "L 206.560481 131.311101 \n", "L 206.866374 140.868521 \n", "L 207.172267 143.408847 \n", "L 207.47816 159.660731 \n", "L 207.784053 156.36308 \n", "L 208.089946 144.604518 \n", "L 208.701732 155.695034 \n", "L 209.007625 148.993727 \n", "L 209.313519 153.324262 \n", "L 209.619412 154.662309 \n", "L 209.925305 138.145389 \n", "L 210.537091 156.694711 \n", "L 210.842984 142.625913 \n", "L 211.45477 163.258174 \n", "L 211.760663 148.126324 \n", "L 212.066557 157.488966 \n", "L 212.37245 138.987515 \n", "L 212.678343 138.152758 \n", "L 212.984236 148.557806 \n", "L 213.290129 148.78209 \n", "L 213.596022 143.521248 \n", "L 214.207808 165.96 \n", "L 214.819594 129.736584 \n", "L 215.125488 146.894374 \n", "L 215.431381 140.281146 \n", "L 215.737274 151.783908 \n", "L 216.043167 145.225977 \n", "L 216.34906 155.037886 \n", "L 216.654953 143.835051 \n", "L 216.960846 139.470479 \n", "L 217.266739 144.479666 \n", "L 217.572632 155.043953 \n", "L 217.878525 147.034654 \n", "L 218.184419 162.383934 \n", "L 218.796205 149.71152 \n", "L 219.102098 149.644443 \n", "L 219.407991 159.87777 \n", "L 219.713884 139.463334 \n", "L 220.019777 142.852442 \n", "L 220.32567 142.146907 \n", "L 220.631563 131.419653 \n", "L 220.937456 142.594586 \n", "L 221.24335 124.487653 \n", "L 221.549243 157.055666 \n", "L 221.855136 140.330148 \n", "L 222.161029 143.959269 \n", "L 222.466922 155.862137 \n", "L 222.772815 152.510402 \n", "L 223.078708 146.785831 \n", "L 223.384601 135.963436 \n", "L 223.690494 140.189321 \n", "L 223.996387 133.128932 \n", "L 224.302281 149.03855 \n", "L 224.608174 154.746794 \n", "L 224.914067 127.443123 \n", "L 225.21996 132.994403 \n", "L 225.525853 145.019185 \n", "L 225.831746 127.898124 \n", "L 226.137639 148.649825 \n", "L 226.443532 147.270849 \n", "L 226.749425 137.102986 \n", "L 227.055318 132.00848 \n", "L 227.361212 147.847261 \n", "L 227.667105 152.723783 \n", "L 227.972998 134.059332 \n", "L 228.278891 135.717061 \n", "L 228.584784 143.770633 \n", "L 228.890677 142.919817 \n", "L 229.19657 130.67384 \n", "L 229.502463 144.821774 \n", "L 230.11425 130.406901 \n", "L 230.420143 140.121657 \n", "L 230.726036 140.019621 \n", "L 231.031929 134.661746 \n", "L 231.337822 150.397557 \n", "L 231.643715 119.753255 \n", "L 231.949608 121.982982 \n", "L 232.255501 120.764061 \n", "L 232.561394 113.893976 \n", "L 232.867287 131.283503 \n", "L 233.173181 139.401899 \n", "L 233.479074 134.794382 \n", "L 233.784967 122.126381 \n", "L 234.09086 136.09082 \n", "L 234.396753 138.741467 \n", "L 234.702646 123.507801 \n", "L 235.008539 135.389271 \n", "L 235.314432 134.643635 \n", "L 235.620325 120.351425 \n", "L 235.926218 123.895415 \n", "L 236.232112 115.101882 \n", "L 236.538005 142.119957 \n", "L 236.843898 126.445419 \n", "L 237.149791 144.452604 \n", "L 237.761577 106.555685 \n", "L 238.06747 128.407618 \n", "L 238.373363 107.911829 \n", "L 238.679256 125.070009 \n", "L 238.985149 119.451207 \n", "L 239.291043 131.80737 \n", "L 239.596936 120.378116 \n", "L 239.902829 129.777237 \n", "L 240.514615 128.530457 \n", "L 240.820508 123.47079 \n", "L 241.126401 134.493827 \n", "L 241.432294 131.036163 \n", "L 241.738187 136.151769 \n", "L 242.04408 137.154919 \n", "L 242.349974 146.374619 \n", "L 242.655867 111.587794 \n", "L 242.96176 125.615716 \n", "L 243.267653 121.700977 \n", "L 243.573546 135.926079 \n", "L 243.879439 136.77093 \n", "L 244.185332 128.228277 \n", "L 244.491225 133.269162 \n", "L 244.797118 104.965384 \n", "L 245.103012 138.52303 \n", "L 245.714798 108.020454 \n", "L 246.020691 125.392495 \n", "L 246.326584 110.962805 \n", "L 246.632477 129.681279 \n", "L 246.93837 117.089621 \n", "L 247.244263 116.705333 \n", "L 247.550156 113.377745 \n", "L 247.856049 104.590828 \n", "L 248.161943 134.075702 \n", "L 248.467836 132.804956 \n", "L 248.773729 110.761928 \n", "L 249.079622 119.49309 \n", "L 249.385515 114.438924 \n", "L 249.691408 112.579072 \n", "L 249.997301 98.839299 \n", "L 250.303194 122.084341 \n", "L 250.609087 99.392272 \n", "L 250.91498 110.842232 \n", "L 251.220874 95.90922 \n", "L 251.526767 120.260589 \n", "L 251.83266 94.446097 \n", "L 252.138553 96.986956 \n", "L 252.444446 105.726656 \n", "L 252.750339 98.333678 \n", "L 253.056232 102.675546 \n", "L 253.362125 97.857437 \n", "L 253.668018 121.758378 \n", "L 253.973911 94.820189 \n", "L 254.279805 93.561105 \n", "L 254.891591 106.137579 \n", "L 255.197484 88.688915 \n", "L 255.503377 112.011292 \n", "L 256.115163 108.034489 \n", "L 256.421056 91.545789 \n", "L 256.726949 109.686891 \n", "L 257.032842 100.488363 \n", "L 257.644629 87.487764 \n", "L 257.950522 109.772459 \n", "L 258.256415 111.934721 \n", "L 258.562308 104.912966 \n", "L 258.868201 80.416518 \n", "L 259.174094 101.060939 \n", "L 259.479987 104.840612 \n", "L 259.78588 85.812226 \n", "L 260.091773 91.053375 \n", "L 260.397667 100.107304 \n", "L 260.70356 92.331848 \n", "L 261.009453 105.79277 \n", "L 261.315346 95.386252 \n", "L 261.621239 78.286852 \n", "L 261.927132 98.239681 \n", "L 262.233025 88.435769 \n", "L 262.538918 84.349952 \n", "L 262.844811 98.421239 \n", "L 263.456598 85.364269 \n", "L 263.762491 80.841742 \n", "L 264.068384 95.678522 \n", "L 264.374277 78.21545 \n", "L 264.68017 83.660019 \n", "L 265.291956 101.930725 \n", "L 265.597849 80.112662 \n", "L 265.903742 80.476094 \n", "L 266.209636 69.243396 \n", "L 266.515529 92.737777 \n", "L 266.821422 91.409695 \n", "L 267.433208 75.155911 \n", "L 267.739101 77.319668 \n", "L 268.044994 74.821305 \n", "L 268.350887 94.969223 \n", "L 268.65678 100.146038 \n", "L 268.962673 88.006744 \n", "L 269.57446 86.620372 \n", "L 269.880353 76.530508 \n", "L 270.186246 79.737919 \n", "L 270.492139 78.953741 \n", "L 270.798032 73.306082 \n", "L 271.103925 79.702807 \n", "L 271.409818 79.634722 \n", "L 271.715711 71.29236 \n", "L 272.021604 78.349285 \n", "L 272.633391 86.065984 \n", "L 272.939284 66.257793 \n", "L 273.245177 73.018722 \n", "L 273.55107 64.349164 \n", "L 273.856963 77.137937 \n", "L 274.162856 69.351924 \n", "L 274.468749 74.057826 \n", "L 274.774642 66.947001 \n", "L 275.080535 54.618309 \n", "L 275.386429 67.320845 \n", "L 275.692322 66.752886 \n", "L 275.998215 59.3763 \n", "L 276.304108 77.925778 \n", "L 276.610001 60.083996 \n", "L 276.915894 75.191751 \n", "L 277.221787 74.872443 \n", "L 277.52768 64.276811 \n", "L 277.833573 69.358413 \n", "L 278.139467 63.111763 \n", "L 278.44536 61.943955 \n", "L 278.751253 83.083319 \n", "L 279.057146 56.955106 \n", "L 279.668932 65.064295 \n", "L 279.974825 77.062039 \n", "L 280.280718 83.320045 \n", "L 281.198398 56.288506 \n", "L 281.504291 64.946495 \n", "L 281.810184 48.916506 \n", "L 282.116077 69.816298 \n", "L 282.42197 50.967752 \n", "L 282.727863 61.623504 \n", "L 283.033756 60.494558 \n", "L 283.645542 73.136095 \n", "L 283.951435 72.859955 \n", "L 284.257329 63.623834 \n", "L 284.563222 71.371637 \n", "L 284.869115 64.634839 \n", "L 285.175008 77.855923 \n", "L 285.480901 49.196434 \n", "L 286.092687 64.420216 \n", "L 286.39858 49.923346 \n", "L 286.704473 59.540666 \n", "L 287.010366 45.643973 \n", "L 287.31626 67.500793 \n", "L 287.622153 66.924878 \n", "L 288.233939 50.744419 \n", "L 288.539832 53.349333 \n", "L 288.845725 44.687339 \n", "L 289.151618 45.545409 \n", "L 289.763404 60.936178 \n", "L 290.069297 38.175243 \n", "L 290.375191 58.346509 \n", "L 290.681084 37.239911 \n", "L 290.986977 53.885382 \n", "L 291.29287 36.948178 \n", "L 291.598763 54.736489 \n", "L 291.904656 48.033259 \n", "L 292.210549 55.538544 \n", "L 292.516442 70.201461 \n", "L 292.822335 71.066211 \n", "L 293.128229 66.452691 \n", "L 293.434122 52.888159 \n", "L 293.740015 65.937745 \n", "L 294.045908 42.905665 \n", "L 294.351801 38.765967 \n", "L 294.657694 53.717499 \n", "L 294.963587 60.945518 \n", "L 295.575373 65.913263 \n", "L 295.881266 44.216704 \n", "L 296.18716 59.431149 \n", "L 296.493053 41.992241 \n", "L 296.798946 61.208599 \n", "L 297.104839 59.352863 \n", "L 297.410732 45.73687 \n", "L 297.716625 58.347248 \n", "L 298.022518 60.275507 \n", "L 298.634304 47.140888 \n", "L 298.940197 56.22301 \n", "L 299.246091 54.422881 \n", "L 299.551984 51.068092 \n", "L 299.857877 60.625839 \n", "L 300.16377 49.956871 \n", "L 300.469663 45.788938 \n", "L 300.775556 50.000895 \n", "L 301.081449 32.822311 \n", "L 301.693235 54.061799 \n", "L 301.999128 53.243162 \n", "L 302.305022 56.232041 \n", "L 302.610915 54.49534 \n", "L 302.916808 65.694622 \n", "L 303.222701 34.534097 \n", "L 303.528594 68.342127 \n", "L 303.834487 42.588661 \n", "L 304.14038 36.422688 \n", "L 304.446273 48.230311 \n", "L 304.752166 33.353739 \n", "L 305.058059 59.449469 \n", "L 305.363953 38.387905 \n", "L 305.669846 56.794993 \n", "L 305.975739 52.668799 \n", "L 306.281632 64.37979 \n", "L 306.587525 40.384774 \n", "L 306.893418 53.597181 \n", "L 307.199311 46.700702 \n", "L 307.811097 70.150253 \n", "L 308.11699 58.792612 \n", "L 308.422884 33.039255 \n", "L 308.728777 49.098041 \n", "L 309.03467 31.417539 \n", "L 309.340563 41.441485 \n", "L 309.646456 42.578741 \n", "L 309.952349 46.547236 \n", "L 310.258242 46.142725 \n", "L 310.564135 44.216327 \n", "L 310.870028 41.37608 \n", "L 311.175922 42.422236 \n", "L 311.481815 51.21709 \n", "L 311.787708 36.675518 \n", "L 312.093601 43.193546 \n", "L 312.399494 24.842495 \n", "L 312.705387 51.731337 \n", "L 313.01128 29.872956 \n", "L 313.623066 49.327409 \n", "L 313.928959 37.253524 \n", "L 314.234853 55.186442 \n", "L 314.540746 37.944362 \n", "L 314.846639 32.659308 \n", "L 315.152532 62.671474 \n", "L 315.458425 61.085883 \n", "L 315.764318 49.064582 \n", "L 316.070211 48.924093 \n", "L 316.376104 41.57484 \n", "L 316.681997 56.992082 \n", "L 316.98789 47.249307 \n", "L 317.293784 51.112167 \n", "L 317.599677 65.738031 \n", "L 317.90557 32.373619 \n", "L 318.211463 42.747199 \n", "L 318.517356 44.313895 \n", "L 318.823249 57.803435 \n", "L 319.129142 60.252321 \n", "L 319.435035 46.168667 \n", "L 319.740928 50.442574 \n", "L 320.046821 60.789216 \n", "L 320.352715 66.019898 \n", "L 320.658608 51.196522 \n", "L 320.964501 48.80196 \n", "L 321.270394 58.524015 \n", "L 321.576287 77.382669 \n", "L 321.88218 53.021546 \n", "L 322.188073 53.046897 \n", "L 322.493966 49.509533 \n", "L 322.799859 54.584464 \n", "L 323.105752 67.829031 \n", "L 323.411646 53.103 \n", "L 323.717539 61.348642 \n", "L 324.023432 48.388371 \n", "L 324.329325 68.323433 \n", "L 324.635218 62.178221 \n", "L 324.941111 60.60298 \n", "L 325.247004 47.153088 \n", "L 325.85879 75.128776 \n", "L 326.164684 60.846433 \n", "L 326.470577 54.606142 \n", "L 326.77647 54.722684 \n", "L 327.388256 78.235954 \n", "L 327.694149 48.348361 \n", "L 328.000042 75.827576 \n", "L 328.611828 54.680666 \n", "L 328.917721 61.073058 \n", "L 329.223615 48.886416 \n", "L 329.529508 56.936085 \n", "L 329.835401 54.918048 \n", "L 330.141294 66.087011 \n", "L 330.447187 67.771137 \n", "L 330.75308 65.658804 \n", "L 331.058973 69.923062 \n", "L 331.364866 77.942339 \n", "L 331.670759 65.0413 \n", "L 331.976652 74.352614 \n", "L 332.282546 69.748613 \n", "L 332.894332 74.708388 \n", "L 333.200225 80.626607 \n", "L 333.506118 70.636823 \n", "L 333.812011 74.660922 \n", "L 334.72969 58.080695 \n", "L 335.035583 82.559794 \n", "L 335.341477 80.173878 \n", "L 335.64737 62.226599 \n", "L 335.953263 57.66272 \n", "L 336.259156 71.80269 \n", "L 336.565049 64.414443 \n", "L 336.870942 73.288869 \n", "L 337.176835 67.535731 \n", "L 337.482728 71.767858 \n", "L 337.788621 60.335051 \n", "L 338.094514 79.528182 \n", "L 338.400408 78.278965 \n", "L 338.706301 61.148887 \n", "L 339.012194 76.369013 \n", "L 339.318087 80.278112 \n", "L 339.929873 95.500219 \n", "L 340.235766 90.263349 \n", "L 340.541659 71.979073 \n", "L 340.847552 80.261463 \n", "L 341.153445 76.203639 \n", "L 341.459339 75.734586 \n", "L 342.071125 93.281876 \n", "L 342.377018 77.113362 \n", "L 342.682911 79.119968 \n", "L 342.988804 79.911432 \n", "L 343.294697 83.147953 \n", "L 343.60059 81.63008 \n", "L 343.906483 85.909144 \n", "L 344.212377 104.663987 \n", "L 344.51827 83.643712 \n", "L 344.824163 86.127062 \n", "L 345.435949 78.331698 \n", "L 345.741842 91.451595 \n", "L 346.047735 81.595619 \n", "L 346.353628 77.807569 \n", "L 346.659521 102.793966 \n", "L 346.965414 94.130027 \n", "L 347.577201 89.690917 \n", "L 347.883094 99.40689 \n", "L 348.188987 103.452228 \n", "L 348.800773 92.188519 \n", "L 349.106666 100.919378 \n", "L 349.412559 116.533894 \n", "L 349.718452 100.857607 \n", "L 350.024345 65.554909 \n", "L 350.330239 87.185537 \n", "L 350.636132 82.845513 \n", "L 350.942025 93.621014 \n", "L 351.247918 91.451973 \n", "L 351.859704 78.730569 \n", "L 352.165597 105.136044 \n", "L 352.47149 101.709871 \n", "L 352.777383 94.861749 \n", "L 353.083276 111.546447 \n", "L 353.38917 100.967713 \n", "L 353.695063 99.380449 \n", "L 354.000956 92.065612 \n", "L 354.306849 117.240448 \n", "L 354.612742 100.104055 \n", "L 354.918635 98.844075 \n", "L 355.224528 110.904707 \n", "L 355.530421 87.598449 \n", "L 355.836314 99.117831 \n", "L 356.142207 101.765372 \n", "L 356.448101 97.135244 \n", "L 356.753994 112.811674 \n", "L 357.059887 115.201565 \n", "L 357.36578 96.582702 \n", "L 357.671673 110.064962 \n", "L 357.977566 109.495089 \n", "L 358.283459 109.533082 \n", "L 358.589352 114.553997 \n", "L 358.895245 101.578653 \n", "L 359.201139 106.177504 \n", "L 359.507032 106.553451 \n", "L 359.812925 110.410071 \n", "L 360.118818 121.577902 \n", "L 360.424711 111.893228 \n", "L 360.730604 106.844392 \n", "L 361.036497 93.993039 \n", "L 361.34239 111.168828 \n", "L 361.648283 107.357956 \n", "L 361.954176 116.024352 \n", "L 362.26007 109.570673 \n", "L 362.565963 129.176501 \n", "L 362.871856 120.971758 \n", "L 363.177749 118.93349 \n", "L 363.483642 118.657991 \n", "L 363.789535 112.727944 \n", "L 364.401321 116.28109 \n", "L 364.707214 132.048331 \n", "L 365.013107 121.843832 \n", "L 365.319001 119.037252 \n", "L 365.624894 117.822355 \n", "L 365.930787 135.758723 \n", "L 366.23668 111.553024 \n", "L 366.542573 109.077646 \n", "L 366.848466 115.702357 \n", "L 367.154359 112.345026 \n", "L 367.460252 126.040904 \n", "L 367.766145 128.749882 \n", "L 368.072038 129.870961 \n", "L 368.377932 134.600762 \n", "L 368.683825 104.934888 \n", "L 368.989718 140.617087 \n", "L 369.601504 106.950486 \n", "L 369.907397 138.321702 \n", "L 370.21329 127.813686 \n", "L 370.519183 122.497055 \n", "L 370.825076 115.075764 \n", "L 371.130969 122.758373 \n", "L 371.436863 120.168072 \n", "L 371.742756 127.09093 \n", "L 371.742756 127.09093 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 67.379119 100.055846 \n", "L 67.685012 100.983206 \n", "L 67.990906 99.02955 \n", "L 68.296799 94.475472 \n", "L 68.602692 95.801778 \n", "L 68.908585 87.028507 \n", "L 69.520371 96.992177 \n", "L 69.826264 98.755429 \n", "L 70.132157 95.390449 \n", "L 70.43805 94.170898 \n", "L 70.743943 94.186439 \n", "L 71.049837 92.743561 \n", "L 71.35573 86.040907 \n", "L 71.661623 95.871227 \n", "L 71.967516 98.709226 \n", "L 72.273409 87.304773 \n", "L 72.579302 95.281221 \n", "L 72.885195 91.669105 \n", "L 73.191088 91.608861 \n", "L 73.496981 102.297929 \n", "L 73.802874 91.397986 \n", "L 74.414661 99.208671 \n", "L 74.720554 97.864462 \n", "L 75.026447 103.195878 \n", "L 75.638233 91.974502 \n", "L 75.944126 91.336265 \n", "L 76.250019 82.773029 \n", "L 76.555912 86.286028 \n", "L 76.861805 84.064307 \n", "L 77.167699 93.504267 \n", "L 77.473592 86.804769 \n", "L 77.779485 85.542794 \n", "L 78.085378 90.962999 \n", "L 78.391271 86.103256 \n", "L 78.697164 83.394868 \n", "L 79.003057 88.457595 \n", "L 79.30895 84.581826 \n", "L 79.614843 73.927856 \n", "L 79.920736 84.683894 \n", "L 80.532523 73.3538 \n", "L 80.838416 77.126677 \n", "L 81.144309 67.678797 \n", "L 81.450202 76.442045 \n", "L 81.756095 76.023219 \n", "L 82.061988 80.576701 \n", "L 82.367881 75.104842 \n", "L 82.673774 75.698085 \n", "L 82.979668 82.017404 \n", "L 83.285561 68.719665 \n", "L 83.591454 76.432334 \n", "L 83.897347 66.021273 \n", "L 84.20324 69.685933 \n", "L 84.509133 69.104513 \n", "L 84.815026 63.489151 \n", "L 85.120919 72.401437 \n", "L 85.426812 75.089294 \n", "L 85.732705 76.309595 \n", "L 86.038599 72.625206 \n", "L 86.344492 81.053648 \n", "L 86.650385 69.46057 \n", "L 86.956278 82.399259 \n", "L 87.262171 90.54945 \n", "L 87.873957 65.984491 \n", "L 88.17985 67.124878 \n", "L 88.485743 64.665568 \n", "L 88.791636 68.416447 \n", "L 89.09753 68.951136 \n", "L 89.403423 63.30105 \n", "L 89.709316 69.325687 \n", "L 90.015209 71.659978 \n", "L 90.321102 64.584123 \n", "L 90.626995 67.243715 \n", "L 90.932888 62.452751 \n", "L 91.238781 65.302446 \n", "L 91.544674 55.795033 \n", "L 91.850567 64.138903 \n", "L 92.156461 57.298333 \n", "L 92.462354 56.675031 \n", "L 92.768247 66.422047 \n", "L 93.07414 62.023845 \n", "L 93.380033 61.658687 \n", "L 93.685926 61.049867 \n", "L 93.991819 62.845554 \n", "L 94.297712 59.201987 \n", "L 94.603605 61.484604 \n", "L 95.215392 58.356023 \n", "L 95.521285 61.018157 \n", "L 95.827178 67.999271 \n", "L 96.133071 64.916305 \n", "L 96.438964 63.341108 \n", "L 96.744857 67.843028 \n", "L 97.05075 67.951358 \n", "L 97.662536 58.377653 \n", "L 97.96843 59.034631 \n", "L 98.274323 56.369657 \n", "L 98.886109 48.143675 \n", "L 99.192002 51.149561 \n", "L 99.497895 56.495051 \n", "L 99.803788 53.818051 \n", "L 100.109681 56.719373 \n", "L 100.415574 61.062915 \n", "L 100.721467 59.387139 \n", "L 101.027361 61.982009 \n", "L 101.333254 58.001194 \n", "L 101.639147 63.292397 \n", "L 101.94504 57.01348 \n", "L 102.250933 58.929295 \n", "L 102.556826 47.845386 \n", "L 102.862719 50.260114 \n", "L 103.168612 44.569336 \n", "L 103.474505 41.403608 \n", "L 103.780398 52.037017 \n", "L 104.086292 48.639452 \n", "L 104.392185 51.209393 \n", "L 104.698078 61.331512 \n", "L 105.003971 66.101894 \n", "L 105.309864 58.977149 \n", "L 105.615757 56.256791 \n", "L 105.92165 57.635364 \n", "L 106.227543 52.236916 \n", "L 106.533436 57.294033 \n", "L 106.839329 55.73007 \n", "L 107.145223 57.513016 \n", "L 107.451116 60.390574 \n", "L 107.757009 62.191402 \n", "L 108.062902 56.036008 \n", "L 108.368795 53.654156 \n", "L 108.674688 50.17413 \n", "L 108.980581 39.561854 \n", "L 109.286474 47.304751 \n", "L 109.592367 48.976839 \n", "L 109.89826 48.469057 \n", "L 110.510047 54.901477 \n", "L 110.81594 49.160454 \n", "L 111.121833 46.340082 \n", "L 111.427726 48.207855 \n", "L 111.733619 52.905725 \n", "L 112.039512 27.431925 \n", "L 112.345405 45.708712 \n", "L 112.651298 51.135733 \n", "L 112.957191 50.729295 \n", "L 113.263085 51.634959 \n", "L 113.568978 62.788491 \n", "L 114.180764 48.64142 \n", "L 114.486657 54.297629 \n", "L 114.79255 52.531009 \n", "L 115.098443 55.861365 \n", "L 115.404336 57.259319 \n", "L 115.710229 53.322775 \n", "L 116.016123 52.097317 \n", "L 116.322016 49.245769 \n", "L 116.627909 50.865232 \n", "L 116.933802 54.05597 \n", "L 117.239695 52.746421 \n", "L 117.545588 46.890549 \n", "L 117.851481 44.210866 \n", "L 118.157374 44.715839 \n", "L 118.463267 45.482502 \n", "L 118.76916 55.024988 \n", "L 119.075054 51.434682 \n", "L 119.380947 60.709493 \n", "L 119.68684 66.194479 \n", "L 119.992733 56.132893 \n", "L 120.298626 59.930183 \n", "L 120.604519 57.148867 \n", "L 120.910412 51.901126 \n", "L 121.216305 57.506165 \n", "L 121.522198 55.118946 \n", "L 121.828091 50.396618 \n", "L 122.439878 52.852414 \n", "L 122.745771 57.812484 \n", "L 123.051664 55.701346 \n", "L 123.357557 51.683348 \n", "L 123.66345 54.932156 \n", "L 123.969343 47.166572 \n", "L 124.275236 49.201913 \n", "L 124.581129 44.36369 \n", "L 124.887022 45.471286 \n", "L 125.192916 48.713122 \n", "L 125.498809 42.306194 \n", "L 125.804702 50.573953 \n", "L 126.110595 45.876492 \n", "L 126.416488 46.991693 \n", "L 126.722381 55.482629 \n", "L 127.028274 51.371336 \n", "L 127.334167 49.953592 \n", "L 127.64006 57.098796 \n", "L 127.945953 44.773933 \n", "L 128.251847 49.888936 \n", "L 128.55774 52.723916 \n", "L 128.863633 56.915747 \n", "L 129.169526 55.668117 \n", "L 129.475419 55.331322 \n", "L 129.781312 57.678192 \n", "L 130.087205 62.986461 \n", "L 130.393098 59.013318 \n", "L 130.698991 61.296509 \n", "L 131.310778 57.363497 \n", "L 131.616671 67.809436 \n", "L 131.922564 60.680249 \n", "L 132.228457 61.244759 \n", "L 132.53435 55.495052 \n", "L 132.840243 51.93827 \n", "L 133.452029 69.152125 \n", "L 133.757922 62.830919 \n", "L 134.063816 64.886845 \n", "L 134.369709 55.453075 \n", "L 134.675602 61.503224 \n", "L 134.981495 62.731554 \n", "L 135.287388 62.147778 \n", "L 135.593281 69.393033 \n", "L 135.899174 60.481133 \n", "L 136.205067 69.014332 \n", "L 136.51096 61.865682 \n", "L 136.816853 58.761061 \n", "L 137.122747 60.800229 \n", "L 137.42864 63.996786 \n", "L 137.734533 65.927398 \n", "L 138.040426 66.736109 \n", "L 138.346319 66.022021 \n", "L 138.652212 70.729293 \n", "L 138.958105 59.96799 \n", "L 139.263998 56.974725 \n", "L 139.569891 60.654209 \n", "L 140.181678 75.387697 \n", "L 140.487571 70.430278 \n", "L 140.793464 71.813025 \n", "L 141.099357 74.192827 \n", "L 141.40525 60.379229 \n", "L 141.711143 60.837275 \n", "L 142.017036 65.943289 \n", "L 142.322929 63.391892 \n", "L 142.628822 69.991115 \n", "L 142.934715 65.224856 \n", "L 143.240609 72.063329 \n", "L 143.546502 69.98743 \n", "L 143.852395 70.016607 \n", "L 144.158288 68.070646 \n", "L 144.464181 76.191122 \n", "L 144.770074 71.79158 \n", "L 145.075967 77.743248 \n", "L 145.38186 78.823887 \n", "L 145.687753 69.507723 \n", "L 145.993646 84.759244 \n", "L 146.29954 78.149076 \n", "L 146.605433 78.254165 \n", "L 146.911326 80.537899 \n", "L 147.217219 72.473319 \n", "L 147.523112 78.728444 \n", "L 147.829005 75.469925 \n", "L 148.134898 78.747889 \n", "L 148.440791 76.36517 \n", "L 148.746684 77.608307 \n", "L 149.052578 81.623292 \n", "L 149.358471 74.706808 \n", "L 149.664364 85.578873 \n", "L 149.970257 79.599755 \n", "L 150.27615 87.969435 \n", "L 150.582043 79.714917 \n", "L 150.887936 76.366034 \n", "L 151.193829 78.261426 \n", "L 151.499722 78.360682 \n", "L 151.805615 81.183026 \n", "L 152.111509 76.02511 \n", "L 152.417402 74.972946 \n", "L 152.723295 77.672183 \n", "L 153.029188 84.105433 \n", "L 153.335081 83.665132 \n", "L 153.640974 81.255746 \n", "L 153.946867 76.580372 \n", "L 154.25276 93.082895 \n", "L 154.558653 85.008542 \n", "L 154.864546 80.249496 \n", "L 155.17044 90.093914 \n", "L 155.476333 82.984193 \n", "L 155.782226 72.254089 \n", "L 156.088119 76.958505 \n", "L 156.394012 87.594765 \n", "L 156.699905 79.955042 \n", "L 157.005798 81.890264 \n", "L 157.617584 89.71124 \n", "L 157.923477 91.586014 \n", "L 158.229371 94.449297 \n", "L 158.535264 102.838166 \n", "L 158.841157 100.875287 \n", "L 159.14705 99.977692 \n", "L 159.452943 99.882555 \n", "L 159.758836 93.859205 \n", "L 160.064729 95.859767 \n", "L 160.370622 90.181646 \n", "L 160.676515 93.5537 \n", "L 160.982408 102.783681 \n", "L 161.288302 92.791899 \n", "L 161.594195 92.938208 \n", "L 161.900088 101.05836 \n", "L 162.205981 96.975368 \n", "L 162.511874 103.604149 \n", "L 162.817767 96.138892 \n", "L 163.12366 97.971532 \n", "L 163.429553 109.731516 \n", "L 163.735446 101.061974 \n", "L 164.04134 99.418867 \n", "L 164.653126 93.588447 \n", "L 164.959019 95.842033 \n", "L 165.264912 92.802331 \n", "L 165.876698 106.919113 \n", "L 166.182591 97.843668 \n", "L 166.488484 108.913851 \n", "L 166.794377 109.079732 \n", "L 167.100271 107.787854 \n", "L 167.406164 98.701531 \n", "L 168.01795 111.58837 \n", "L 168.323843 103.46609 \n", "L 168.629736 112.211016 \n", "L 168.935629 110.683024 \n", "L 169.241522 107.916858 \n", "L 169.547415 116.055514 \n", "L 169.853308 117.260876 \n", "L 170.159202 109.49546 \n", "L 170.465095 110.178679 \n", "L 170.770988 120.679707 \n", "L 171.076881 111.944538 \n", "L 171.382774 113.033325 \n", "L 171.688667 121.057824 \n", "L 172.606346 105.311714 \n", "L 172.912239 109.761459 \n", "L 173.218133 107.961677 \n", "L 173.524026 97.454149 \n", "L 173.829919 112.205703 \n", "L 174.135812 114.783101 \n", "L 174.441705 120.931251 \n", "L 174.747598 121.598194 \n", "L 175.053491 112.135516 \n", "L 175.359384 120.241991 \n", "L 175.665277 114.396149 \n", "L 175.97117 118.564088 \n", "L 176.277064 129.916753 \n", "L 176.582957 127.507903 \n", "L 176.88885 119.742506 \n", "L 177.194743 134.201493 \n", "L 177.500636 133.970331 \n", "L 177.806529 132.410786 \n", "L 178.112422 138.729379 \n", "L 178.418315 126.76073 \n", "L 178.724208 123.082723 \n", "L 179.030102 114.892135 \n", "L 179.335995 116.974226 \n", "L 179.641888 123.215071 \n", "L 179.947781 116.477133 \n", "L 180.253674 119.130385 \n", "L 180.559567 119.847853 \n", "L 180.86546 115.141017 \n", "L 181.477246 128.577672 \n", "L 181.783139 123.634253 \n", "L 182.089033 125.683312 \n", "L 182.394926 123.056468 \n", "L 182.700819 124.374857 \n", "L 183.006712 124.036808 \n", "L 183.312605 134.475077 \n", "L 183.618498 135.298423 \n", "L 183.924391 132.145087 \n", "L 184.230284 133.861282 \n", "L 184.536177 133.508261 \n", "L 184.84207 131.431611 \n", "L 185.147964 133.160931 \n", "L 185.453857 129.865129 \n", "L 185.75975 128.846104 \n", "L 186.065643 124.748896 \n", "L 186.371536 122.819444 \n", "L 186.677429 127.073782 \n", "L 186.983322 139.469549 \n", "L 187.289215 128.452511 \n", "L 187.595108 131.914113 \n", "L 187.901001 139.088255 \n", "L 188.206895 128.160704 \n", "L 188.512788 129.146591 \n", "L 188.818681 131.648771 \n", "L 189.124574 132.925127 \n", "L 189.430467 132.660394 \n", "L 189.73636 136.105497 \n", "L 190.042253 131.289765 \n", "L 190.348146 139.221189 \n", "L 190.654039 144.166323 \n", "L 190.959932 133.883902 \n", "L 191.265826 144.38871 \n", "L 191.571719 141.296443 \n", "L 191.877612 139.522483 \n", "L 192.489398 144.383874 \n", "L 192.795291 138.685414 \n", "L 193.101184 148.686478 \n", "L 193.407077 148.857227 \n", "L 193.71297 139.137911 \n", "L 194.018863 148.600308 \n", "L 194.63065 144.495798 \n", "L 194.936543 136.005808 \n", "L 195.242436 139.04882 \n", "L 195.548329 140.42372 \n", "L 195.854222 142.706466 \n", "L 196.160115 149.471096 \n", "L 196.771901 136.938772 \n", "L 197.077795 138.306721 \n", "L 197.383688 137.314469 \n", "L 197.689581 139.167748 \n", "L 197.995474 148.529978 \n", "L 198.301367 139.996252 \n", "L 198.60726 140.909468 \n", "L 198.913153 139.551147 \n", "L 199.219046 133.302874 \n", "L 199.830832 149.288294 \n", "L 200.136726 138.30271 \n", "L 200.442619 154.144955 \n", "L 200.748512 146.61371 \n", "L 201.054405 147.355517 \n", "L 201.360298 151.451034 \n", "L 201.666191 140.786878 \n", "L 201.972084 149.847343 \n", "L 202.277977 137.664006 \n", "L 202.58387 139.044802 \n", "L 202.889763 143.548949 \n", "L 203.195657 141.27543 \n", "L 203.50155 145.807697 \n", "L 203.807443 142.380826 \n", "L 204.113336 148.139472 \n", "L 204.419229 143.461336 \n", "L 204.725122 142.047828 \n", "L 205.031015 146.529166 \n", "L 205.642801 149.165083 \n", "L 205.948694 148.506392 \n", "L 206.254588 151.413905 \n", "L 206.560481 148.373281 \n", "L 206.866374 143.937891 \n", "L 207.172267 145.490015 \n", "L 207.47816 138.96604 \n", "L 208.089946 145.397807 \n", "L 208.395839 149.429572 \n", "L 208.701732 155.188188 \n", "L 209.313519 147.151782 \n", "L 209.619412 152.354445 \n", "L 209.925305 152.283909 \n", "L 210.231198 147.325783 \n", "L 210.537091 151.832313 \n", "L 210.842984 148.367762 \n", "L 211.148877 142.469293 \n", "L 211.45477 151.910182 \n", "L 211.760663 152.436656 \n", "L 212.066557 147.431893 \n", "L 212.37245 157.431584 \n", "L 212.678343 150.540007 \n", "L 212.984236 148.681765 \n", "L 213.290129 147.626512 \n", "L 213.596022 140.855523 \n", "L 213.901915 143.149283 \n", "L 214.207808 149.858176 \n", "L 214.513701 150.697213 \n", "L 214.819594 148.901242 \n", "L 215.431381 154.138893 \n", "L 215.737274 139.120917 \n", "L 216.34906 143.490427 \n", "L 216.654953 148.096764 \n", "L 216.960846 146.695685 \n", "L 217.572632 147.550109 \n", "L 217.878525 144.679663 \n", "L 218.184419 142.937457 \n", "L 218.490312 152.679048 \n", "L 218.796205 151.296243 \n", "L 219.102098 153.049523 \n", "L 219.407991 155.53348 \n", "L 219.713884 153.593905 \n", "L 220.019777 146.49553 \n", "L 220.32567 151.323818 \n", "L 220.631563 146.966621 \n", "L 220.937456 138.36481 \n", "L 221.24335 142.211991 \n", "L 221.549243 133.185342 \n", "L 221.855136 142.147517 \n", "L 222.161029 134.720901 \n", "L 222.772815 149.834395 \n", "L 223.078708 144.44588 \n", "L 223.384601 148.764241 \n", "L 223.690494 148.89402 \n", "L 223.996387 146.69861 \n", "L 224.302281 138.762388 \n", "L 224.608174 140.715418 \n", "L 224.914067 140.905459 \n", "L 225.21996 137.408294 \n", "L 225.525853 146.692637 \n", "L 225.831746 141.412405 \n", "L 226.137639 129.513537 \n", "L 226.443532 141.434472 \n", "L 226.749425 138.688383 \n", "L 227.055318 137.959329 \n", "L 227.361212 143.42276 \n", "L 227.667105 143.193543 \n", "L 227.972998 138.888863 \n", "L 228.278891 138.279306 \n", "L 228.584784 146.177765 \n", "L 228.890677 142.876509 \n", "L 229.19657 136.727763 \n", "L 229.502463 137.208639 \n", "L 229.808356 143.402381 \n", "L 230.11425 136.352113 \n", "L 230.420143 135.807885 \n", "L 230.726036 140.274631 \n", "L 231.031929 134.974592 \n", "L 231.337822 135.013974 \n", "L 231.643715 142.445 \n", "L 231.949608 132.412082 \n", "L 232.255501 137.230565 \n", "L 232.561394 130.794071 \n", "L 232.867287 119.176968 \n", "L 233.173181 124.005024 \n", "L 233.479074 122.978987 \n", "L 233.784967 125.938712 \n", "L 234.396753 136.595848 \n", "L 234.702646 130.800892 \n", "L 235.008539 127.731687 \n", "L 235.314432 136.747011 \n", "L 235.926218 127.156665 \n", "L 236.232112 132.007564 \n", "L 236.538005 124.052978 \n", "L 236.843898 127.404544 \n", "L 237.149791 121.139499 \n", "L 237.455684 133.016384 \n", "L 237.761577 131.543854 \n", "L 238.06747 128.110046 \n", "L 238.373363 132.941654 \n", "L 238.679256 113.762936 \n", "L 238.985149 119.944389 \n", "L 239.291043 118.27823 \n", "L 239.596936 120.829478 \n", "L 239.902829 121.687403 \n", "L 240.208722 126.882642 \n", "L 240.514615 126.658352 \n", "L 240.820508 126.071901 \n", "L 241.126401 127.815709 \n", "L 241.432294 130.22534 \n", "L 241.738187 127.147717 \n", "L 242.349974 133.661524 \n", "L 242.655867 136.759809 \n", "L 242.96176 129.92442 \n", "L 243.267653 137.554114 \n", "L 243.573546 126.519801 \n", "L 243.879439 123.36835 \n", "L 244.185332 126.928702 \n", "L 244.491225 128.745891 \n", "L 244.797118 135.373483 \n", "L 245.103012 125.121895 \n", "L 245.408905 132.830339 \n", "L 245.714798 119.573864 \n", "L 246.020691 118.757138 \n", "L 246.326584 128.947425 \n", "L 246.632477 114.067016 \n", "L 246.93837 120.485411 \n", "L 247.244263 117.749625 \n", "L 247.550156 119.765646 \n", "L 247.856049 120.677847 \n", "L 248.161943 113.891069 \n", "L 248.467836 120.13109 \n", "L 248.773729 115.164524 \n", "L 249.079622 117.595339 \n", "L 249.385515 129.76815 \n", "L 249.691408 119.631053 \n", "L 249.997301 114.788419 \n", "L 250.303194 112.395458 \n", "L 250.609087 115.979852 \n", "L 250.91498 104.167658 \n", "L 251.220874 111.236459 \n", "L 251.526767 106.856378 \n", "L 251.83266 109.614064 \n", "L 252.138553 101.187657 \n", "L 252.444446 105.981405 \n", "L 252.750339 106.943991 \n", "L 253.056232 96.945786 \n", "L 253.362125 102.298005 \n", "L 253.668018 101.287283 \n", "L 253.973911 106.527254 \n", "L 254.279805 99.1628 \n", "L 254.585698 106.345096 \n", "L 254.891591 106.413282 \n", "L 255.197484 97.811738 \n", "L 255.503377 95.677646 \n", "L 255.80927 106.312274 \n", "L 256.115163 100.755746 \n", "L 256.726949 106.099881 \n", "L 257.032842 109.418381 \n", "L 257.338736 100.163954 \n", "L 257.644629 99.820054 \n", "L 257.950522 100.857552 \n", "L 258.256415 101.205835 \n", "L 258.562308 96.898373 \n", "L 258.868201 101.020402 \n", "L 259.174094 103.349921 \n", "L 259.479987 106.884263 \n", "L 259.78588 96.023437 \n", "L 260.091773 90.371657 \n", "L 260.397667 100.456847 \n", "L 260.70356 96.87409 \n", "L 261.009453 90.167809 \n", "L 261.315346 98.931113 \n", "L 261.927132 94.434807 \n", "L 262.233025 100.416525 \n", "L 262.538918 87.682255 \n", "L 262.844811 88.266643 \n", "L 263.150705 95.163056 \n", "L 263.456598 88.105359 \n", "L 263.762491 90.702445 \n", "L 264.068384 91.512035 \n", "L 264.374277 90.640871 \n", "L 264.68017 82.532909 \n", "L 264.986063 88.063086 \n", "L 265.291956 88.622033 \n", "L 265.597849 87.230035 \n", "L 265.903742 86.433666 \n", "L 266.209636 93.325497 \n", "L 266.515529 85.808677 \n", "L 266.821422 84.433469 \n", "L 267.127315 79.845048 \n", "L 267.433208 82.392199 \n", "L 267.739101 88.391827 \n", "L 268.044994 84.936496 \n", "L 268.350887 78.406606 \n", "L 268.65678 82.053496 \n", "L 268.962673 83.092923 \n", "L 269.268567 86.699851 \n", "L 269.57446 95.565627 \n", "L 269.880353 92.613681 \n", "L 270.186246 85.500295 \n", "L 270.492139 85.874122 \n", "L 271.103925 77.85192 \n", "L 271.409818 80.350093 \n", "L 272.021604 76.191014 \n", "L 272.327498 80.257558 \n", "L 272.633391 78.025911 \n", "L 272.939284 78.765258 \n", "L 273.245177 77.56326 \n", "L 273.55107 82.213314 \n", "L 273.856963 73.800616 \n", "L 274.162856 72.781204 \n", "L 274.468749 69.789224 \n", "L 274.774642 72.856535 \n", "L 275.080535 72.51866 \n", "L 275.386429 68.445024 \n", "L 275.692322 70.72754 \n", "L 275.998215 63.364983 \n", "L 276.304108 61.975206 \n", "L 276.610001 71.007477 \n", "L 276.915894 63.328566 \n", "L 277.221787 71.757658 \n", "L 277.52768 71.318152 \n", "L 277.833573 68.084519 \n", "L 278.139467 74.600318 \n", "L 278.44536 68.84231 \n", "L 278.751253 66.798534 \n", "L 279.057146 71.654487 \n", "L 279.363039 62.198067 \n", "L 279.668932 71.05329 \n", "L 279.974825 69.479709 \n", "L 280.280718 65.080645 \n", "L 281.198398 77.010728 \n", "L 281.504291 73.627731 \n", "L 281.810184 68.841721 \n", "L 282.116077 58.369495 \n", "L 282.42197 64.437196 \n", "L 282.727863 56.431201 \n", "L 283.033756 61.588228 \n", "L 283.339649 61.402266 \n", "L 283.645542 60.719343 \n", "L 283.951435 65.372879 \n", "L 284.563222 69.809895 \n", "L 284.869115 73.641981 \n", "L 285.175008 68.274189 \n", "L 285.480901 71.421161 \n", "L 285.786794 64.130453 \n", "L 286.092687 68.836157 \n", "L 286.39858 64.574497 \n", "L 286.704473 53.599608 \n", "L 287.010366 61.695545 \n", "L 287.31626 55.310607 \n", "L 287.622153 59.588166 \n", "L 287.928046 57.423738 \n", "L 288.233939 58.844849 \n", "L 288.539832 64.139934 \n", "L 288.845725 61.698665 \n", "L 289.151618 53.558537 \n", "L 289.457511 51.883392 \n", "L 290.069297 50.735403 \n", "L 290.375191 47.748454 \n", "L 290.681084 58.68098 \n", "L 290.986977 47.487764 \n", "L 291.29287 51.572104 \n", "L 291.598763 46.176956 \n", "L 291.904656 49.776605 \n", "L 292.210549 47.357985 \n", "L 292.516442 50.147174 \n", "L 292.822335 57.555491 \n", "L 293.128229 58.205508 \n", "L 293.434122 65.187043 \n", "L 294.045908 69.148602 \n", "L 294.351801 56.405659 \n", "L 294.657694 55.663254 \n", "L 294.963587 55.366581 \n", "L 295.26948 47.54183 \n", "L 295.575373 52.493664 \n", "L 295.881266 60.943229 \n", "L 296.18716 59.007062 \n", "L 296.493053 64.826468 \n", "L 296.798946 52.731265 \n", "L 297.104839 55.953134 \n", "L 297.716625 51.77876 \n", "L 298.022518 61.100536 \n", "L 298.328411 55.728267 \n", "L 298.634304 54.286413 \n", "L 298.940197 57.521086 \n", "L 299.246091 58.431268 \n", "L 299.551984 53.114449 \n", "L 299.857877 53.085643 \n", "L 300.16377 58.052213 \n", "L 300.469663 53.35829 \n", "L 300.775556 54.788027 \n", "L 301.081449 55.189953 \n", "L 301.387342 45.44311 \n", "L 301.693235 48.471637 \n", "L 302.305022 43.907275 \n", "L 302.610915 52.402723 \n", "L 303.222701 58.984303 \n", "L 303.528594 51.292141 \n", "L 303.834487 63.788109 \n", "L 304.14038 49.096197 \n", "L 304.446273 49.564031 \n", "L 304.752166 54.685452 \n", "L 305.058059 39.469193 \n", "L 305.363953 48.581063 \n", "L 305.669846 41.515497 \n", "L 305.975739 51.049536 \n", "L 306.281632 51.028671 \n", "L 306.587525 53.681701 \n", "L 306.893418 52.309667 \n", "L 307.199311 58.827176 \n", "L 307.505204 51.986324 \n", "L 307.811097 51.472769 \n", "L 308.11699 56.628997 \n", "L 308.422884 55.390957 \n", "L 308.728777 57.375646 \n", "L 309.03467 61.694005 \n", "L 309.340563 43.342553 \n", "L 309.646456 43.102776 \n", "L 309.952349 42.255516 \n", "L 310.258242 40.894429 \n", "L 310.564135 44.700635 \n", "L 310.870028 46.106815 \n", "L 311.175922 46.617884 \n", "L 311.481815 46.030824 \n", "L 311.787708 46.541157 \n", "L 312.093601 42.157824 \n", "L 312.399494 47.605573 \n", "L 312.705387 40.412236 \n", "L 313.01128 44.818385 \n", "L 313.317173 34.380996 \n", "L 313.623066 41.025693 \n", "L 313.928959 44.331491 \n", "L 314.234853 37.604354 \n", "L 314.540746 49.332793 \n", "L 314.846639 43.28295 \n", "L 315.152532 44.586124 \n", "L 315.458425 52.069793 \n", "L 315.764318 43.487542 \n", "L 316.070211 49.922006 \n", "L 316.376104 59.825072 \n", "L 316.681997 52.827752 \n", "L 316.98789 52.583304 \n", "L 317.293784 47.164704 \n", "L 317.90557 56.919694 \n", "L 318.211463 46.318257 \n", "L 318.517356 56.045902 \n", "L 318.823249 48.867377 \n", "L 319.129142 44.611384 \n", "L 319.435035 49.388384 \n", "L 319.740928 51.406345 \n", "L 320.046821 58.215337 \n", "L 320.658608 54.373448 \n", "L 320.964501 55.941801 \n", "L 321.270394 60.994199 \n", "L 321.88218 58.443989 \n", "L 322.188073 54.923132 \n", "L 322.493966 65.589988 \n", "L 322.799859 62.037774 \n", "L 323.105752 54.866139 \n", "L 323.411646 56.918828 \n", "L 323.717539 53.747748 \n", "L 324.023432 62.732947 \n", "L 324.329325 58.402206 \n", "L 324.635218 61.580838 \n", "L 324.941111 57.868304 \n", "L 325.247004 60.518769 \n", "L 325.85879 62.790423 \n", "L 326.470577 57.608384 \n", "L 326.77647 66.23681 \n", "L 327.082363 65.548235 \n", "L 327.388256 61.233832 \n", "L 327.694149 62.055717 \n", "L 328.000042 58.810276 \n", "L 328.305935 74.591747 \n", "L 328.611828 64.065551 \n", "L 328.917721 61.815263 \n", "L 329.223615 68.452325 \n", "L 329.529508 57.63709 \n", "L 329.835401 59.093544 \n", "L 330.141294 56.149756 \n", "L 330.447187 57.828654 \n", "L 331.058973 63.224592 \n", "L 331.364866 68.870388 \n", "L 331.670759 70.700988 \n", "L 331.976652 68.216778 \n", "L 332.282546 75.205074 \n", "L 332.588439 71.88668 \n", "L 332.894332 71.634082 \n", "L 333.200225 73.70232 \n", "L 333.506118 74.639719 \n", "L 333.812011 73.835851 \n", "L 334.117904 77.943331 \n", "L 335.035583 69.272821 \n", "L 335.341477 70.660919 \n", "L 335.64737 66.046123 \n", "L 335.953263 69.620766 \n", "L 336.259156 76.180393 \n", "L 336.565049 72.213437 \n", "L 336.870942 62.245117 \n", "L 337.176835 68.328625 \n", "L 337.482728 68.94144 \n", "L 337.788621 70.8414 \n", "L 338.094514 68.796613 \n", "L 338.400408 73.389353 \n", "L 338.706301 70.118051 \n", "L 339.012194 68.991965 \n", "L 339.318087 79.224972 \n", "L 339.62398 73.23025 \n", "L 339.929873 75.031344 \n", "L 340.235766 83.666144 \n", "L 340.541659 86.643853 \n", "L 340.847552 87.494664 \n", "L 341.153445 90.287092 \n", "L 341.459339 80.44749 \n", "L 341.765232 77.100189 \n", "L 342.071125 80.990479 \n", "L 342.377018 81.329996 \n", "L 342.682911 80.691632 \n", "L 342.988804 87.644815 \n", "L 343.60059 80.349928 \n", "L 343.906483 80.943092 \n", "L 344.212377 83.554343 \n", "L 344.51827 88.915459 \n", "L 344.824163 84.499846 \n", "L 345.130056 93.842723 \n", "L 345.435949 91.324107 \n", "L 345.741842 84.0339 \n", "L 346.047735 86.687458 \n", "L 346.353628 81.257104 \n", "L 346.659521 84.062246 \n", "L 346.965414 91.36141 \n", "L 347.271308 84.128423 \n", "L 347.883094 96.624351 \n", "L 348.188987 95.240315 \n", "L 348.49488 94.619074 \n", "L 348.800773 96.007368 \n", "L 349.106666 99.58615 \n", "L 349.412559 101.031302 \n", "L 349.718452 100.915564 \n", "L 350.024345 98.241572 \n", "L 350.330239 98.19577 \n", "L 350.636132 103.458097 \n", "L 350.942025 83.387978 \n", "L 351.247918 82.04987 \n", "L 351.553811 87.345339 \n", "L 351.859704 88.164225 \n", "L 352.165597 89.622769 \n", "L 352.47149 93.106476 \n", "L 352.777383 87.474414 \n", "L 353.083276 93.671685 \n", "L 353.38917 105.923119 \n", "L 353.695063 99.325487 \n", "L 354.000956 102.898459 \n", "L 354.306849 102.842658 \n", "L 354.612742 105.065494 \n", "L 354.918635 97.201976 \n", "L 355.530421 109.355724 \n", "L 355.836314 96.834861 \n", "L 356.142207 104.046204 \n", "L 356.753994 95.073611 \n", "L 357.059887 104.157033 \n", "L 357.36578 103.863388 \n", "L 357.671673 103.378858 \n", "L 357.977566 113.322438 \n", "L 358.283459 106.890366 \n", "L 358.589352 105.508302 \n", "L 358.895245 111.29199 \n", "L 359.201139 107.741887 \n", "L 359.507032 110.906414 \n", "L 359.812925 107.81887 \n", "L 360.118818 106.021074 \n", "L 360.424711 110.62423 \n", "L 360.730604 109.66633 \n", "L 361.036497 113.991895 \n", "L 361.34239 110.940941 \n", "L 361.648283 110.13409 \n", "L 361.954176 102.436023 \n", "L 362.871856 116.583386 \n", "L 363.177749 114.953434 \n", "L 363.789535 123.319696 \n", "L 364.095428 118.158069 \n", "L 364.401321 117.809407 \n", "L 364.707214 115.936228 \n", "L 365.013107 118.558204 \n", "L 365.319001 117.146698 \n", "L 365.624894 123.095026 \n", "L 365.930787 124.437019 \n", "L 366.23668 124.434468 \n", "L 366.542573 116.672461 \n", "L 366.848466 122.526946 \n", "L 367.154359 121.340124 \n", "L 367.460252 111.053467 \n", "L 367.766145 116.240621 \n", "L 368.072038 117.909817 \n", "L 368.377932 122.199268 \n", "L 368.683825 129.218844 \n", "L 368.989718 122.928875 \n", "L 369.295611 134.447358 \n", "L 369.601504 119.403137 \n", "L 369.907397 119.276238 \n", "L 370.21329 132.109574 \n", "L 370.519183 117.101599 \n", "L 371.130969 128.163614 \n", "L 371.436863 124.496625 \n", "L 371.742756 119.123963 \n", "L 371.742756 119.123963 \n", "\" clip-path=\"url(#p4999c0e02c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.52 \n", "L 52.160938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 7.2 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 59.160938 168.52 \n", "L 153.885938 168.52 \n", "Q 155.885938 168.52 155.885938 166.52 \n", "L 155.885938 138.16375 \n", "Q 155.885938 136.16375 153.885938 136.16375 \n", "L 59.160938 136.16375 \n", "Q 57.160938 136.16375 57.160938 138.16375 \n", "L 57.160938 166.52 \n", "Q 57.160938 168.52 59.160938 168.52 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_29\">\n", "     <path d=\"M 61.160938 144.262188 \n", "L 71.160938 144.262188 \n", "L 81.160938 144.262188 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- labels -->\n", "     <g transform=\"translate(89.160938 147.762188) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6c\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"89.0625\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"152.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"214.0625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"241.845703\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 61.160938 158.940313 \n", "L 71.160938 158.940313 \n", "L 81.160938 158.940313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- 1-step preds -->\n", "     <g transform=\"translate(89.160938 162.440313) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"99.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"151.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"191.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"252.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"316.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"347.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"411.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"450.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"511.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"575.142578\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p4999c0e02c\">\n", "   <rect x=\"52.160938\" y=\"7.2\" width=\"334.8\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["onestep_preds = model(data.features).detach().numpy()\n", "d2l.plot(data.time[data.tau:], [data.labels, onestep_preds], 'time', 'x',\n", "         legend=['labels', '1-step preds'], figsize=(6, 3))"]}, {"cell_type": "markdown", "id": "543d4a35", "metadata": {"origin_pos": 16}, "source": ["These predictions look good,\n", "even near the end at $t=1000$.\n", "\n", "But what if we only observed sequence data\n", "up until time step 604 (`n_train + tau`)\n", "and wished to make predictions several steps\n", "into the future?\n", "Unfortunately, we cannot directly compute\n", "the one-step-ahead prediction for time step 609,\n", "because we do not know the corresponding inputs,\n", "having seen only up to $x_{604}$.\n", "We can address this problem by plugging in\n", "our earlier predictions as inputs to our model\n", "for making subsequent predictions,\n", "projecting forward, one step at a time,\n", "until reaching the desired time step:\n", "\n", "$$\\begin{aligned}\n", "\\hat{x}_{605} &= f(x_{601}, x_{602}, x_{603}, x_{604}), \\\\\n", "\\hat{x}_{606} &= f(x_{602}, x_{603}, x_{604}, \\hat{x}_{605}), \\\\\n", "\\hat{x}_{607} &= f(x_{603}, x_{604}, \\hat{x}_{605}, \\hat{x}_{606}),\\\\\n", "\\hat{x}_{608} &= f(x_{604}, \\hat{x}_{605}, \\hat{x}_{606}, \\hat{x}_{607}),\\\\\n", "\\hat{x}_{609} &= f(\\hat{x}_{605}, \\hat{x}_{606}, \\hat{x}_{607}, \\hat{x}_{608}),\\\\\n", "&\\vdots\\end{aligned}$$\n", "\n", "Generally, for an observed sequence $x_1, \\ldots, x_t$,\n", "its predicted output $\\hat{x}_{t+k}$ at time step $t+k$\n", "is called the $k$*-step-ahead prediction*.\n", "Since we have observed up to $x_{604}$,\n", "its $k$-step-ahead prediction is $\\hat{x}_{604+k}$.\n", "In other words, we will have to\n", "keep on using our own predictions\n", "to make multistep-ahead predictions.\n", "Let's see how well this goes.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "9d74def4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:33.726831Z", "iopub.status.busy": "2023-08-18T19:31:33.726546Z", "iopub.status.idle": "2023-08-18T19:31:33.756835Z", "shell.execute_reply": "2023-08-18T19:31:33.755987Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["multistep_preds = torch.zeros(data.T)\n", "multistep_preds[:] = data.x\n", "for i in range(data.num_train + data.tau, data.T):\n", "    multistep_preds[i] = model(\n", "        multistep_preds[i - data.tau:i].reshape((1, -1)))\n", "multistep_preds = multistep_preds.detach().numpy()"]}, {"cell_type": "code", "execution_count": 10, "id": "55da8029", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:33.759927Z", "iopub.status.busy": "2023-08-18T19:31:33.759653Z", "iopub.status.idle": "2023-08-18T19:31:34.022817Z", "shell.execute_reply": "2023-08-18T19:31:34.021647Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"394.160937pt\" height=\"211.07625pt\" viewBox=\"0 0 394.**********.07625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-31T21:25:17.942168</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 211.07625 \n", "L 394.**********.07625 \n", "L 394.160937 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "L 386.960938 7.2 \n", "L 52.160938 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 65.849654 173.52 \n", "L 65.849654 7.2 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m86509dbc68\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m86509dbc68\" x=\"65.849654\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(62.668404 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 127.028274 173.52 \n", "L 127.028274 7.2 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m86509dbc68\" x=\"127.028274\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(117.484524 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 188.206895 173.52 \n", "L 188.206895 7.2 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m86509dbc68\" x=\"188.206895\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(178.663145 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 249.385515 173.52 \n", "L 249.385515 7.2 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m86509dbc68\" x=\"249.385515\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(239.841765 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 310.564135 173.52 \n", "L 310.564135 7.2 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m86509dbc68\" x=\"310.564135\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(301.020385 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 371.742756 173.52 \n", "L 371.742756 7.2 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m86509dbc68\" x=\"371.742756\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(359.017756 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- time -->\n", "     <g transform=\"translate(208.264844 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 52.160938 155.443232 \n", "L 386.960938 155.443232 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path id=\"m42d04120c3\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m42d04120c3\" x=\"52.160938\" y=\"155.443232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 159.242451) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 52.160938 126.706342 \n", "L 386.960938 126.706342 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m42d04120c3\" x=\"52.160938\" y=\"126.706342\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 130.505561) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 52.160938 97.969452 \n", "L 386.960938 97.969452 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m42d04120c3\" x=\"52.160938\" y=\"97.969452\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257812 101.768671) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 52.160938 69.232562 \n", "L 386.960938 69.232562 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m42d04120c3\" x=\"52.160938\" y=\"69.232562\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257812 73.03178) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 52.160938 40.495672 \n", "L 386.960938 40.495672 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m42d04120c3\" x=\"52.160938\" y=\"40.495672\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257812 44.29489) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 52.160938 11.758782 \n", "L 386.960938 11.758782 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m42d04120c3\" x=\"52.160938\" y=\"11.758782\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(29.257812 15.558) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- x -->\n", "     <g transform=\"translate(14.798438 93.319375) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 67.379119 99.227428 \n", "L 67.685012 100.306022 \n", "L 67.990906 98.033764 \n", "L 68.296799 92.737007 \n", "L 68.602692 94.279607 \n", "L 68.908585 84.075591 \n", "L 69.520371 95.664135 \n", "L 69.826264 97.714939 \n", "L 70.132157 93.801198 \n", "L 70.43805 92.382763 \n", "L 70.743943 92.400838 \n", "L 71.049837 90.722656 \n", "L 71.35573 82.926933 \n", "L 71.661623 94.360382 \n", "L 71.967516 97.661201 \n", "L 72.273409 84.39691 \n", "L 72.579302 93.674157 \n", "L 72.885195 89.472977 \n", "L 73.191088 89.402909 \n", "L 73.496981 101.83515 \n", "L 73.802874 89.157645 \n", "L 74.414661 98.242095 \n", "L 74.720554 96.678673 \n", "L 75.026447 102.879536 \n", "L 75.638233 89.828178 \n", "L 75.944126 89.085858 \n", "L 76.250019 79.126129 \n", "L 76.555912 83.212028 \n", "L 76.861805 80.627989 \n", "L 77.167699 91.607417 \n", "L 77.473592 83.815366 \n", "L 77.779485 82.347588 \n", "L 78.085378 88.651719 \n", "L 78.391271 82.99945 \n", "L 78.697164 79.849378 \n", "L 79.003057 85.737734 \n", "L 79.30895 81.229906 \n", "L 79.614843 68.838486 \n", "L 79.920736 81.348618 \n", "L 80.532523 68.170813 \n", "L 80.838416 72.55897 \n", "L 81.144309 61.57033 \n", "L 81.450202 71.762689 \n", "L 81.756095 71.275561 \n", "L 82.061988 76.571624 \n", "L 82.367881 70.207414 \n", "L 82.673774 70.897404 \n", "L 82.979668 78.247277 \n", "L 83.285561 62.780943 \n", "L 83.591454 71.751395 \n", "L 83.897347 59.642498 \n", "L 84.20324 63.90479 \n", "L 84.509133 63.228553 \n", "L 84.815026 56.697437 \n", "L 85.120919 67.063139 \n", "L 85.426812 70.189332 \n", "L 85.732705 71.608639 \n", "L 86.038599 67.3234 \n", "L 86.344492 77.126352 \n", "L 86.650385 63.642676 \n", "L 86.956278 78.691406 \n", "L 87.262171 88.170729 \n", "L 87.873957 59.599718 \n", "L 88.17985 60.926078 \n", "L 88.485743 58.065705 \n", "L 88.791636 62.428277 \n", "L 89.09753 63.050162 \n", "L 89.403423 56.478661 \n", "L 89.709316 63.485796 \n", "L 90.015209 66.200762 \n", "L 90.321102 57.970977 \n", "L 90.626995 61.064295 \n", "L 90.932888 55.492021 \n", "L 91.238781 58.806445 \n", "L 91.544674 47.748562 \n", "L 91.850567 57.453151 \n", "L 92.156461 49.497021 \n", "L 92.462354 48.77207 \n", "L 92.768247 60.10863 \n", "L 93.07414 54.99317 \n", "L 93.380033 54.568461 \n", "L 93.685926 53.860355 \n", "L 93.991819 55.948883 \n", "L 94.297712 51.711122 \n", "L 94.603605 54.365988 \n", "L 95.215392 50.727199 \n", "L 95.521285 53.823473 \n", "L 95.827178 61.943067 \n", "L 96.133071 58.357331 \n", "L 96.438964 56.525251 \n", "L 96.744857 61.761344 \n", "L 97.05075 61.887341 \n", "L 97.662536 50.752357 \n", "L 97.96843 51.516474 \n", "L 98.274323 48.416897 \n", "L 98.886109 38.849422 \n", "L 99.192002 42.345507 \n", "L 99.497895 48.562739 \n", "L 99.803788 45.449175 \n", "L 100.109681 48.823644 \n", "L 100.415574 53.87553 \n", "L 100.721467 51.926469 \n", "L 101.027361 54.944511 \n", "L 101.333254 50.314504 \n", "L 101.639147 56.468597 \n", "L 101.94504 49.165715 \n", "L 102.250933 51.393961 \n", "L 102.556826 38.502487 \n", "L 102.862719 41.31101 \n", "L 103.168612 34.692179 \n", "L 103.474505 31.010184 \n", "L 103.780398 43.377689 \n", "L 104.086292 39.42605 \n", "L 104.392185 42.415097 \n", "L 104.698078 54.187931 \n", "L 105.003971 59.736266 \n", "L 105.309864 51.449618 \n", "L 105.615757 48.285624 \n", "L 105.92165 49.889014 \n", "L 106.227543 43.610188 \n", "L 106.533436 49.49202 \n", "L 106.839329 47.673005 \n", "L 107.145223 49.746714 \n", "L 107.451116 53.093545 \n", "L 107.757009 55.188051 \n", "L 108.062902 48.028836 \n", "L 108.368795 45.258552 \n", "L 108.674688 41.211003 \n", "L 108.980581 28.868078 \n", "L 109.286474 37.873686 \n", "L 109.592367 39.818458 \n", "L 109.89826 39.227866 \n", "L 110.510047 46.709286 \n", "L 110.81594 40.032016 \n", "L 111.121833 36.751699 \n", "L 111.427726 38.924068 \n", "L 111.733619 44.388066 \n", "L 112.039512 14.76 \n", "L 112.345405 36.017365 \n", "L 112.651298 42.329424 \n", "L 112.957191 41.856704 \n", "L 113.263085 42.910063 \n", "L 113.568978 55.882513 \n", "L 114.180764 39.428339 \n", "L 114.486657 46.006962 \n", "L 114.79255 43.952242 \n", "L 115.098443 47.825713 \n", "L 115.404336 49.451644 \n", "L 115.710229 44.873129 \n", "L 116.016123 43.447824 \n", "L 116.322016 40.131245 \n", "L 116.627909 42.01481 \n", "L 116.933802 45.725893 \n", "L 117.239695 44.202784 \n", "L 117.545588 37.391936 \n", "L 117.851481 34.27525 \n", "L 118.157374 34.862574 \n", "L 118.463267 35.754264 \n", "L 118.76916 46.852939 \n", "L 119.075054 42.677126 \n", "L 119.380947 53.464473 \n", "L 119.68684 59.84395 \n", "L 119.992733 48.141522 \n", "L 120.298626 52.558073 \n", "L 120.604519 49.323181 \n", "L 120.910412 43.219638 \n", "L 121.216305 49.738746 \n", "L 121.522198 46.962219 \n", "L 121.828091 41.469774 \n", "L 122.439878 44.326061 \n", "L 122.745771 50.095019 \n", "L 123.051664 47.639598 \n", "L 123.357557 42.966344 \n", "L 123.66345 46.744968 \n", "L 123.969343 37.712972 \n", "L 124.275236 40.080237 \n", "L 124.581129 34.452997 \n", "L 124.887022 35.741219 \n", "L 125.192916 39.511734 \n", "L 125.498809 32.059964 \n", "L 125.804702 41.676029 \n", "L 126.110595 36.212507 \n", "L 126.416488 37.509574 \n", "L 126.722381 47.385212 \n", "L 127.028274 42.603449 \n", "L 127.334167 40.9545 \n", "L 127.64006 49.264944 \n", "L 127.945953 34.930142 \n", "L 128.251847 40.879299 \n", "L 128.55774 44.176608 \n", "L 128.863633 49.052043 \n", "L 129.169526 47.600949 \n", "L 129.475419 47.20923 \n", "L 129.781312 49.938828 \n", "L 130.087205 56.112769 \n", "L 130.393098 51.491686 \n", "L 130.698991 54.14722 \n", "L 131.310778 49.572812 \n", "L 131.616671 61.722274 \n", "L 131.922564 53.430459 \n", "L 132.228457 54.08703 \n", "L 132.53435 47.399661 \n", "L 132.840243 43.262839 \n", "L 133.452029 63.283929 \n", "L 133.757922 55.931861 \n", "L 134.063816 58.323067 \n", "L 134.369709 47.350838 \n", "L 134.675602 54.387646 \n", "L 134.981495 55.816291 \n", "L 135.287388 55.137313 \n", "L 135.593281 63.564124 \n", "L 135.899174 53.198872 \n", "L 136.205067 63.123664 \n", "L 136.51096 54.809213 \n", "L 136.816853 51.198291 \n", "L 137.122747 53.570006 \n", "L 137.42864 57.287857 \n", "L 137.734533 59.533314 \n", "L 138.040426 60.47391 \n", "L 138.346319 59.643368 \n", "L 138.652212 65.118301 \n", "L 138.958105 52.602045 \n", "L 139.263998 49.120639 \n", "L 139.569891 53.400173 \n", "L 140.181678 70.536398 \n", "L 140.487571 64.770524 \n", "L 140.793464 66.378769 \n", "L 141.099357 69.146669 \n", "L 141.40525 53.080349 \n", "L 141.711143 53.613094 \n", "L 142.017036 59.551796 \n", "L 142.322929 56.584317 \n", "L 142.628822 64.259741 \n", "L 142.934715 58.716201 \n", "L 143.240609 66.669892 \n", "L 143.546502 64.255455 \n", "L 143.852395 64.28939 \n", "L 144.158288 62.026082 \n", "L 144.464181 71.470845 \n", "L 144.770074 66.353826 \n", "L 145.075967 73.276092 \n", "L 145.38186 74.532962 \n", "L 145.687753 63.697518 \n", "L 145.993646 81.436256 \n", "L 146.29954 73.748102 \n", "L 146.605433 73.87033 \n", "L 146.911326 76.526494 \n", "L 147.217219 67.146743 \n", "L 147.523112 74.421954 \n", "L 147.829005 70.632036 \n", "L 148.134898 74.44457 \n", "L 148.440791 71.673277 \n", "L 148.746684 73.119145 \n", "L 149.052578 77.788894 \n", "L 149.358471 69.74447 \n", "L 149.664364 82.389551 \n", "L 149.970257 75.435358 \n", "L 150.27615 85.169966 \n", "L 150.582043 75.569301 \n", "L 150.887936 71.674282 \n", "L 151.193829 73.878774 \n", "L 151.499722 73.994217 \n", "L 151.805615 77.27683 \n", "L 152.111509 71.27776 \n", "L 152.417402 70.05401 \n", "L 152.723295 73.193438 \n", "L 153.029188 80.675822 \n", "L 153.335081 80.163717 \n", "L 153.640974 77.361408 \n", "L 153.946867 71.923574 \n", "L 154.25276 91.117328 \n", "L 154.558653 81.72621 \n", "L 154.864546 76.191059 \n", "L 155.17044 87.640905 \n", "L 155.476333 79.37173 \n", "L 155.782226 66.891761 \n", "L 156.088119 72.363373 \n", "L 156.394012 84.734195 \n", "L 156.699905 75.848585 \n", "L 157.005798 78.099403 \n", "L 157.617584 87.195824 \n", "L 157.923477 89.376336 \n", "L 158.229371 92.706563 \n", "L 158.535264 102.463488 \n", "L 158.841157 100.180503 \n", "L 159.14705 99.136528 \n", "L 159.452943 99.025876 \n", "L 159.758836 92.020239 \n", "L 160.064729 94.347053 \n", "L 160.370622 87.742944 \n", "L 160.676515 91.664912 \n", "L 160.982408 102.400118 \n", "L 161.288302 90.778877 \n", "L 161.594195 90.949046 \n", "L 161.900088 100.393431 \n", "L 162.205981 95.644585 \n", "L 162.511874 103.354388 \n", "L 162.817767 94.671697 \n", "L 163.12366 96.803204 \n", "L 163.429553 110.481006 \n", "L 163.735446 100.397635 \n", "L 164.04134 98.486571 \n", "L 164.653126 91.705326 \n", "L 164.959019 94.326426 \n", "L 165.264912 90.79101 \n", "L 165.876698 107.209956 \n", "L 166.182591 96.654488 \n", "L 166.488484 109.529996 \n", "L 166.794377 109.722929 \n", "L 167.100271 108.220372 \n", "L 167.406164 97.652251 \n", "L 168.01795 112.640676 \n", "L 168.323843 103.193814 \n", "L 168.629736 113.364863 \n", "L 168.935629 111.587686 \n", "L 169.241522 108.370413 \n", "L 169.547415 117.836322 \n", "L 169.853308 119.238253 \n", "L 170.159202 110.206454 \n", "L 170.465095 111.001093 \n", "L 170.770988 123.214628 \n", "L 171.076881 113.054928 \n", "L 171.382774 114.321273 \n", "L 171.688667 123.654408 \n", "L 172.606346 105.340423 \n", "L 172.912239 110.515832 \n", "L 173.218133 108.422542 \n", "L 173.524026 96.201447 \n", "L 173.829919 113.358683 \n", "L 174.135812 116.356404 \n", "L 174.441705 123.507193 \n", "L 174.747598 124.282901 \n", "L 175.053491 113.27705 \n", "L 175.359384 122.705529 \n", "L 175.665277 115.906347 \n", "L 175.97117 120.753994 \n", "L 176.277064 133.958051 \n", "L 176.582957 131.156366 \n", "L 176.88885 122.124587 \n", "L 177.194743 138.941546 \n", "L 177.500636 138.672686 \n", "L 177.806529 136.85881 \n", "L 178.112422 144.20784 \n", "L 178.418315 130.287343 \n", "L 178.724208 126.009528 \n", "L 179.030102 116.483218 \n", "L 179.335995 118.904857 \n", "L 179.641888 126.163458 \n", "L 179.947781 118.326698 \n", "L 180.253674 121.412642 \n", "L 180.559567 122.247115 \n", "L 180.86546 116.772688 \n", "L 181.477246 132.400592 \n", "L 181.783139 126.651001 \n", "L 182.089033 129.034221 \n", "L 182.394926 125.978991 \n", "L 182.700819 127.512383 \n", "L 183.006712 127.119204 \n", "L 183.312605 139.259746 \n", "L 183.618498 140.217364 \n", "L 183.924391 136.549781 \n", "L 184.230284 138.545853 \n", "L 184.536177 138.135262 \n", "L 184.84207 135.719952 \n", "L 185.147964 137.731289 \n", "L 185.453857 133.898008 \n", "L 185.75975 132.7128 \n", "L 186.065643 127.94742 \n", "L 186.371536 125.703313 \n", "L 186.677429 130.651448 \n", "L 186.983322 145.068716 \n", "L 187.289215 132.25502 \n", "L 187.595108 136.281141 \n", "L 187.901001 144.625242 \n", "L 188.206895 131.915625 \n", "L 188.512788 133.062291 \n", "L 188.818681 135.972526 \n", "L 189.124574 137.457031 \n", "L 189.430467 137.149125 \n", "L 189.73636 141.156055 \n", "L 190.042253 135.554974 \n", "L 190.348146 144.779854 \n", "L 190.654039 150.53144 \n", "L 190.959932 138.572163 \n", "L 191.265826 150.790094 \n", "L 191.571719 147.19354 \n", "L 191.877612 145.130283 \n", "L 192.489398 150.784469 \n", "L 192.795291 144.156705 \n", "L 193.101184 155.788742 \n", "L 193.407077 155.987336 \n", "L 193.71297 144.682995 \n", "L 194.018863 155.688519 \n", "L 194.63065 150.914646 \n", "L 194.936543 141.040108 \n", "L 195.242436 144.579375 \n", "L 195.548329 146.178494 \n", "L 195.854222 148.83351 \n", "L 196.160115 156.701315 \n", "L 196.771901 142.125221 \n", "L 197.077795 143.716255 \n", "L 197.383688 142.562186 \n", "L 197.689581 144.717698 \n", "L 197.995474 155.60672 \n", "L 198.301367 145.681314 \n", "L 198.60726 146.743457 \n", "L 198.913153 145.163622 \n", "L 199.219046 137.896381 \n", "L 199.830832 156.488702 \n", "L 200.136726 143.711589 \n", "L 200.442619 162.137387 \n", "L 200.748512 153.377947 \n", "L 201.054405 154.240727 \n", "L 201.360298 159.004142 \n", "L 201.666191 146.600876 \n", "L 201.972084 157.138921 \n", "L 202.277977 142.968725 \n", "L 202.58387 144.574702 \n", "L 202.889763 149.813385 \n", "L 203.195657 147.169101 \n", "L 203.50155 152.440489 \n", "L 203.807443 148.454764 \n", "L 204.113336 155.15253 \n", "L 204.419229 149.711484 \n", "L 204.725122 148.067461 \n", "L 205.031015 153.279615 \n", "L 205.642801 156.345398 \n", "L 205.948694 155.579287 \n", "L 206.254588 158.960957 \n", "L 206.560481 155.424469 \n", "L 206.866374 150.265756 \n", "L 207.172267 152.070999 \n", "L 207.47816 144.483095 \n", "L 208.089946 151.963754 \n", "L 208.395839 156.653019 \n", "L 208.701732 163.350751 \n", "L 209.313519 154.003768 \n", "L 209.619412 160.054881 \n", "L 209.925305 159.972842 \n", "L 210.231198 154.206145 \n", "L 210.537091 159.447599 \n", "L 210.842984 155.418049 \n", "L 211.148877 148.557659 \n", "L 211.45477 159.538168 \n", "L 211.760663 160.150499 \n", "L 212.066557 154.329559 \n", "L 212.37245 165.96 \n", "L 212.678343 157.944544 \n", "L 212.984236 155.78326 \n", "L 213.290129 154.555916 \n", "L 213.596022 146.680715 \n", "L 213.901915 149.348541 \n", "L 214.207808 157.151521 \n", "L 214.513701 158.127388 \n", "L 214.819594 156.03853 \n", "L 215.431381 162.130337 \n", "L 215.737274 144.663229 \n", "L 216.34906 149.74532 \n", "L 216.654953 155.102857 \n", "L 216.960846 153.473291 \n", "L 217.572632 154.467053 \n", "L 217.878525 151.128496 \n", "L 218.184419 149.102171 \n", "L 218.490312 160.432421 \n", "L 218.796205 158.824107 \n", "L 219.102098 160.863313 \n", "L 219.407991 163.752353 \n", "L 219.713884 161.496472 \n", "L 220.019777 153.240494 \n", "L 220.32567 158.856179 \n", "L 220.631563 153.788411 \n", "L 220.937456 143.783817 \n", "L 221.24335 148.258396 \n", "L 221.549243 137.759681 \n", "L 221.855136 148.183407 \n", "L 222.161029 139.545659 \n", "L 222.772815 157.123862 \n", "L 223.078708 150.856587 \n", "L 223.384601 155.879187 \n", "L 223.690494 156.03013 \n", "L 223.996387 153.476693 \n", "L 224.302281 144.246232 \n", "L 224.608174 146.517762 \n", "L 224.914067 146.738795 \n", "L 225.21996 142.671312 \n", "L 225.525853 153.469745 \n", "L 225.831746 147.328413 \n", "L 226.137639 133.489079 \n", "L 226.443532 147.354079 \n", "L 226.749425 144.160158 \n", "L 227.055318 143.31221 \n", "L 227.361212 149.666618 \n", "L 227.667105 149.400019 \n", "L 227.972998 144.393332 \n", "L 228.278891 143.684368 \n", "L 228.584784 152.870908 \n", "L 228.890677 149.031283 \n", "L 229.19657 141.8798 \n", "L 229.502463 142.439098 \n", "L 229.808356 149.642915 \n", "L 230.11425 141.442889 \n", "L 230.420143 140.809908 \n", "L 230.726036 146.005091 \n", "L 231.031929 139.840721 \n", "L 231.337822 139.886526 \n", "L 231.643715 148.529404 \n", "L 231.949608 136.860318 \n", "L 232.255501 142.464598 \n", "L 232.561394 134.978441 \n", "L 232.867287 121.466821 \n", "L 233.173181 127.082237 \n", "L 233.479074 125.888875 \n", "L 233.784967 129.331271 \n", "L 234.396753 141.726373 \n", "L 234.702646 134.986375 \n", "L 235.008539 131.416644 \n", "L 235.314432 141.902187 \n", "L 235.926218 130.747847 \n", "L 236.232112 136.389831 \n", "L 236.538005 127.138011 \n", "L 236.843898 131.036151 \n", "L 237.149791 123.749402 \n", "L 237.455684 137.563169 \n", "L 237.761577 135.850499 \n", "L 238.06747 131.856707 \n", "L 238.373363 137.476252 \n", "L 238.679256 115.16987 \n", "L 238.985149 122.359394 \n", "L 239.291043 120.421517 \n", "L 239.596936 123.388823 \n", "L 239.902829 124.386659 \n", "L 240.208722 130.429137 \n", "L 240.514615 130.168269 \n", "L 240.820508 129.486181 \n", "L 241.126401 131.514369 \n", "L 241.432294 134.316962 \n", "L 241.738187 130.73744 \n", "L 242.349974 138.313518 \n", "L 242.655867 141.917072 \n", "L 242.96176 133.966968 \n", "L 243.267653 142.840912 \n", "L 243.573546 130.007124 \n", "L 243.879439 126.341734 \n", "L 244.185332 130.482708 \n", "L 244.491225 132.596244 \n", "L 244.797118 140.304665 \n", "L 245.103012 128.381247 \n", "L 245.408905 137.346785 \n", "L 245.714798 121.928444 \n", "L 246.020691 120.978527 \n", "L 246.326584 132.830645 \n", "L 246.632477 115.52354 \n", "L 246.93837 122.988646 \n", "L 247.244263 119.806708 \n", "L 247.550156 122.151501 \n", "L 247.856049 123.212464 \n", "L 248.161943 115.318898 \n", "L 248.467836 122.576542 \n", "L 248.773729 116.800029 \n", "L 249.079622 119.627261 \n", "L 249.385515 133.785214 \n", "L 249.691408 121.994959 \n", "L 249.997301 116.362589 \n", "L 250.303194 113.579384 \n", "L 250.609087 117.74832 \n", "L 250.91498 104.009794 \n", "L 251.220874 112.231375 \n", "L 251.526767 107.13699 \n", "L 251.83266 110.3444 \n", "L 252.138553 100.543815 \n", "L 252.444446 106.119326 \n", "L 252.750339 107.238891 \n", "L 253.056232 95.610179 \n", "L 253.362125 101.835239 \n", "L 253.668018 100.659688 \n", "L 253.973911 106.754193 \n", "L 254.279805 98.188744 \n", "L 254.585698 106.542329 \n", "L 254.891591 106.621634 \n", "L 255.197484 96.617351 \n", "L 255.503377 94.135231 \n", "L 255.80927 106.504154 \n", "L 256.115163 100.041468 \n", "L 256.726949 106.257124 \n", "L 257.032842 110.116805 \n", "L 257.338736 99.353166 \n", "L 257.644629 98.953183 \n", "L 257.950522 100.159876 \n", "L 258.256415 100.564957 \n", "L 258.562308 95.555035 \n", "L 258.868201 100.349284 \n", "L 259.174094 103.058701 \n", "L 259.479987 107.169423 \n", "L 259.78588 94.537414 \n", "L 260.091773 87.963942 \n", "L 260.397667 99.693824 \n", "L 260.70356 95.526791 \n", "L 261.009453 87.72685 \n", "L 261.315346 97.919274 \n", "L 261.927132 92.68971 \n", "L 262.233025 99.646926 \n", "L 262.538918 84.835953 \n", "L 262.844811 85.515642 \n", "L 263.150705 93.536721 \n", "L 263.456598 85.328056 \n", "L 263.762491 88.348674 \n", "L 264.068384 89.290292 \n", "L 264.374277 88.277059 \n", "L 264.68017 78.84685 \n", "L 264.986063 85.278889 \n", "L 265.291956 85.928989 \n", "L 265.597849 84.309984 \n", "L 265.903742 83.383743 \n", "L 266.209636 91.399494 \n", "L 266.515529 82.656831 \n", "L 266.821422 81.057355 \n", "L 267.127315 75.720654 \n", "L 267.433208 78.683194 \n", "L 267.739101 85.661241 \n", "L 268.044994 81.642415 \n", "L 268.350887 74.047631 \n", "L 268.65678 78.289255 \n", "L 268.962673 79.498191 \n", "L 269.268567 83.693337 \n", "L 269.57446 94.004944 \n", "L 269.880353 90.571595 \n", "L 270.186246 82.298158 \n", "L 270.492139 82.732949 \n", "L 271.103925 73.402486 \n", "L 271.409818 76.308061 \n", "L 272.021604 71.47072 \n", "L 272.327498 76.200436 \n", "L 272.633391 73.604851 \n", "L 272.939284 74.464772 \n", "L 273.245177 73.066752 \n", "L 273.55107 78.475136 \n", "L 273.856963 68.690495 \n", "L 274.162856 67.504839 \n", "L 274.468749 64.024926 \n", "L 274.774642 67.592454 \n", "L 275.080535 67.199478 \n", "L 275.386429 62.461513 \n", "L 275.692322 65.116263 \n", "L 275.998215 56.55302 \n", "L 276.304108 54.936598 \n", "L 276.610001 65.441852 \n", "L 276.915894 56.510664 \n", "L 277.221787 66.314372 \n", "L 277.52768 65.803192 \n", "L 277.833573 62.042217 \n", "L 278.139467 69.620613 \n", "L 278.44536 62.92359 \n", "L 278.751253 60.546514 \n", "L 279.057146 66.194377 \n", "L 279.363039 55.195804 \n", "L 279.668932 65.495136 \n", "L 279.974825 63.664936 \n", "L 280.280718 58.548472 \n", "L 281.198398 72.424112 \n", "L 281.504291 68.489417 \n", "L 281.810184 62.922904 \n", "L 282.116077 50.742868 \n", "L 282.42197 57.800089 \n", "L 282.727863 48.488477 \n", "L 283.033756 54.486512 \n", "L 283.339649 54.270223 \n", "L 283.645542 53.475929 \n", "L 283.951435 58.888363 \n", "L 284.563222 64.048968 \n", "L 284.869115 68.50599 \n", "L 285.175008 62.262819 \n", "L 285.480901 65.922999 \n", "L 285.786794 57.443323 \n", "L 286.092687 62.916433 \n", "L 286.39858 57.959782 \n", "L 286.704473 45.195108 \n", "L 287.010366 54.611331 \n", "L 287.31626 47.185137 \n", "L 287.622153 52.16028 \n", "L 287.928046 49.642878 \n", "L 288.233939 51.295742 \n", "L 288.539832 57.45435 \n", "L 288.845725 54.614958 \n", "L 289.151618 45.14734 \n", "L 289.457511 43.199011 \n", "L 290.069297 41.863809 \n", "L 290.375191 38.389747 \n", "L 290.681084 51.10515 \n", "L 290.986977 38.086545 \n", "L 291.29287 42.836959 \n", "L 291.598763 36.56197 \n", "L 291.904656 40.74865 \n", "L 292.210549 37.935602 \n", "L 292.516442 41.179651 \n", "L 292.822335 49.796116 \n", "L 293.128229 50.552138 \n", "L 293.434122 58.672222 \n", "L 294.045908 63.279832 \n", "L 294.351801 48.458769 \n", "L 294.657694 47.595293 \n", "L 294.963587 47.250239 \n", "L 295.26948 38.149427 \n", "L 295.575373 43.908807 \n", "L 295.881266 53.736327 \n", "L 296.18716 51.484409 \n", "L 296.493053 58.252844 \n", "L 296.798946 44.185155 \n", "L 297.104839 47.932447 \n", "L 297.716625 43.077317 \n", "L 298.022518 53.919287 \n", "L 298.328411 47.670909 \n", "L 298.634304 45.993917 \n", "L 298.940197 49.7561 \n", "L 299.246091 50.814715 \n", "L 299.551984 44.630829 \n", "L 299.857877 44.597326 \n", "L 300.16377 50.373844 \n", "L 300.469663 44.914436 \n", "L 300.775556 46.577335 \n", "L 301.081449 47.044806 \n", "L 301.387342 35.708449 \n", "L 301.693235 39.230867 \n", "L 302.305022 33.922149 \n", "L 302.610915 43.803035 \n", "L 303.222701 51.457939 \n", "L 303.528594 42.511339 \n", "L 303.834487 57.04515 \n", "L 304.14038 39.957281 \n", "L 304.446273 40.50141 \n", "L 304.752166 46.458031 \n", "L 305.058059 28.760305 \n", "L 305.363953 39.358139 \n", "L 305.669846 31.14032 \n", "L 305.975739 42.229171 \n", "L 306.281632 42.204903 \n", "L 306.587525 45.290589 \n", "L 306.893418 43.694803 \n", "L 307.199311 51.275188 \n", "L 307.505204 43.318729 \n", "L 307.811097 42.721424 \n", "L 308.11699 48.71853 \n", "L 308.422884 47.27859 \n", "L 308.728777 49.586943 \n", "L 309.03467 54.609539 \n", "L 309.340563 33.265332 \n", "L 309.646456 32.986453 \n", "L 309.952349 32.001021 \n", "L 310.258242 30.417969 \n", "L 310.564135 34.84489 \n", "L 310.870028 36.48039 \n", "L 311.175922 37.074805 \n", "L 311.481815 36.392007 \n", "L 311.787708 36.985565 \n", "L 312.093601 31.887398 \n", "L 312.399494 38.223566 \n", "L 312.705387 29.85714 \n", "L 313.01128 34.981843 \n", "L 313.317173 22.842326 \n", "L 313.623066 30.570639 \n", "L 313.928959 34.415547 \n", "L 314.234853 26.591348 \n", "L 314.540746 40.232461 \n", "L 314.846639 33.19601 \n", "L 315.152532 34.711706 \n", "L 315.458425 43.41581 \n", "L 315.764318 33.433966 \n", "L 316.070211 40.917763 \n", "L 316.376104 52.43582 \n", "L 316.681997 44.297378 \n", "L 316.98789 44.013065 \n", "L 317.293784 37.7108 \n", "L 317.90557 49.056633 \n", "L 318.211463 36.726314 \n", "L 318.517356 48.040343 \n", "L 318.823249 39.691145 \n", "L 319.129142 34.741085 \n", "L 319.435035 40.297118 \n", "L 319.740928 42.644167 \n", "L 320.046821 50.56357 \n", "L 320.658608 46.095146 \n", "L 320.964501 47.919265 \n", "L 321.270394 53.795609 \n", "L 321.88218 50.829511 \n", "L 322.188073 46.734472 \n", "L 322.493966 59.140879 \n", "L 322.799859 55.00937 \n", "L 323.105752 46.668184 \n", "L 323.411646 49.055626 \n", "L 323.717539 45.367407 \n", "L 324.023432 55.817912 \n", "L 324.329325 50.780914 \n", "L 324.635218 54.477916 \n", "L 324.941111 50.159943 \n", "L 325.247004 53.242645 \n", "L 325.85879 55.884761 \n", "L 326.470577 49.857635 \n", "L 326.77647 59.893184 \n", "L 327.082363 59.092316 \n", "L 327.388256 54.074321 \n", "L 327.694149 55.030239 \n", "L 328.000042 51.255531 \n", "L 328.305935 69.610645 \n", "L 328.611828 57.367837 \n", "L 328.917721 54.750572 \n", "L 329.223615 62.470006 \n", "L 329.529508 49.891022 \n", "L 329.835401 51.584995 \n", "L 330.141294 48.161134 \n", "L 330.447187 50.113826 \n", "L 331.058973 56.389734 \n", "L 331.364866 62.956247 \n", "L 331.670759 65.08538 \n", "L 331.976652 62.196045 \n", "L 332.282546 70.323993 \n", "L 332.588439 66.464435 \n", "L 332.894332 66.170643 \n", "L 333.200225 68.576169 \n", "L 333.506118 69.666439 \n", "L 333.812011 68.731477 \n", "L 334.117904 73.508805 \n", "L 335.035583 63.424308 \n", "L 335.341477 65.038777 \n", "L 335.64737 59.671401 \n", "L 335.953263 63.828996 \n", "L 336.259156 71.458367 \n", "L 336.565049 66.844479 \n", "L 336.870942 55.250526 \n", "L 337.176835 62.326133 \n", "L 337.482728 63.038885 \n", "L 337.788621 65.24869 \n", "L 338.094514 62.87044 \n", "L 338.400408 68.212164 \n", "L 338.706301 64.407378 \n", "L 339.012194 63.09765 \n", "L 339.318087 74.999456 \n", "L 339.62398 68.027115 \n", "L 339.929873 70.121931 \n", "L 340.235766 80.164893 \n", "L 340.541659 83.628208 \n", "L 340.847552 84.617769 \n", "L 341.153445 87.865586 \n", "L 341.459339 76.421342 \n", "L 341.765232 72.528163 \n", "L 342.071125 77.052881 \n", "L 342.377018 77.447767 \n", "L 342.682911 76.705299 \n", "L 342.988804 84.792407 \n", "L 343.60059 76.307869 \n", "L 343.906483 76.997767 \n", "L 344.212377 80.034861 \n", "L 344.51827 86.270267 \n", "L 344.824163 81.134556 \n", "L 345.130056 92.001069 \n", "L 345.435949 89.071717 \n", "L 345.741842 80.592623 \n", "L 346.047735 83.678924 \n", "L 346.353628 77.362988 \n", "L 346.659521 80.625592 \n", "L 346.965414 89.115103 \n", "L 347.271308 80.702561 \n", "L 347.883094 95.236325 \n", "L 348.188987 93.626581 \n", "L 348.49488 92.904028 \n", "L 348.800773 94.518725 \n", "L 349.106666 98.681134 \n", "L 349.412559 100.361961 \n", "L 349.718452 100.227348 \n", "L 350.024345 97.117282 \n", "L 350.330239 97.064011 \n", "L 350.636132 103.184517 \n", "L 350.942025 79.841365 \n", "L 351.247918 78.285038 \n", "L 351.553811 84.444092 \n", "L 351.859704 85.396522 \n", "L 352.165597 87.092926 \n", "L 352.47149 91.144755 \n", "L 352.777383 84.594217 \n", "L 353.083276 91.802138 \n", "L 353.38917 106.051535 \n", "L 353.695063 98.377962 \n", "L 354.000956 102.533614 \n", "L 354.306849 102.468713 \n", "L 354.612742 105.054049 \n", "L 354.918635 95.908148 \n", "L 355.530421 110.04393 \n", "L 355.836314 95.481165 \n", "L 356.142207 103.868533 \n", "L 356.753994 93.43269 \n", "L 357.059887 103.997437 \n", "L 357.36578 103.655903 \n", "L 357.671673 103.092357 \n", "L 357.977566 114.657535 \n", "L 358.283459 107.176521 \n", "L 358.589352 105.56907 \n", "L 358.895245 112.295962 \n", "L 359.201139 108.166908 \n", "L 359.507032 111.847506 \n", "L 359.812925 108.256445 \n", "L 360.118818 106.165465 \n", "L 360.424711 111.519303 \n", "L 360.730604 110.40519 \n", "L 361.036497 115.436167 \n", "L 361.34239 111.887664 \n", "L 361.648283 110.949232 \n", "L 361.954176 101.995764 \n", "L 362.871856 118.450278 \n", "L 363.177749 116.554514 \n", "L 363.789535 126.285146 \n", "L 364.095428 120.281761 \n", "L 364.401321 119.876239 \n", "L 364.707214 117.697582 \n", "L 365.013107 120.747149 \n", "L 365.319001 119.105456 \n", "L 365.624894 126.023837 \n", "L 365.930787 127.584682 \n", "L 366.23668 127.581715 \n", "L 366.542573 118.55388 \n", "L 366.848466 125.363115 \n", "L 367.154359 123.982746 \n", "L 367.460252 112.018541 \n", "L 367.766145 118.051615 \n", "L 368.072038 119.993023 \n", "L 368.377932 124.981998 \n", "L 368.683825 133.146327 \n", "L 368.989718 125.83059 \n", "L 369.295611 139.227507 \n", "L 369.601504 121.729874 \n", "L 369.907397 121.582281 \n", "L 370.21329 136.508477 \n", "L 370.519183 119.053002 \n", "L 371.130969 131.91901 \n", "L 371.436863 127.654008 \n", "L 371.742756 121.405172 \n", "L 371.742756 121.405172 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 250.91498 104.009794 \n", "L 251.220874 110.233079 \n", "L 251.526767 111.250889 \n", "L 251.83266 104.369815 \n", "L 252.444446 110.131287 \n", "L 253.056232 106.869782 \n", "L 253.362125 108.512773 \n", "L 253.668018 109.535733 \n", "L 253.973911 108.597773 \n", "L 254.279805 108.333522 \n", "L 254.585698 109.231323 \n", "L 254.891591 109.465712 \n", "L 255.197484 109.093921 \n", "L 255.503377 109.244775 \n", "L 255.80927 109.687726 \n", "L 256.115163 109.742013 \n", "L 256.421056 109.681057 \n", "L 257.338736 110.167222 \n", "L 257.950522 110.411216 \n", "L 259.174094 110.891534 \n", "L 264.068384 112.662335 \n", "L 269.880353 114.530359 \n", "L 275.692322 116.164617 \n", "L 282.116077 117.733801 \n", "L 288.845725 119.146808 \n", "L 296.18716 120.457958 \n", "L 304.14038 121.649274 \n", "L 313.01128 122.744417 \n", "L 322.799859 123.719592 \n", "L 333.812011 124.5835 \n", "L 346.353628 125.334284 \n", "L 360.730604 125.966258 \n", "L 371.742756 126.326945 \n", "L 371.742756 126.326945 \n", "\" clip-path=\"url(#p4bdd0b6985)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.52 \n", "L 52.160938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 7.2 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 59.160938 168.52 \n", "L 169.470313 168.52 \n", "Q 171.470313 168.52 171.470313 166.52 \n", "L 171.470313 138.16375 \n", "Q 171.470313 136.16375 169.470313 136.16375 \n", "L 59.160938 136.16375 \n", "Q 57.160938 136.16375 57.160938 138.16375 \n", "L 57.160938 166.52 \n", "Q 57.160938 168.52 59.160938 168.52 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 61.160938 144.262188 \n", "L 71.160938 144.262188 \n", "L 81.160938 144.262188 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- 1-step preds -->\n", "     <g transform=\"translate(89.160938 147.762188) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"99.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"151.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"191.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"252.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"316.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"347.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"411.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"450.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"511.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"575.142578\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 61.160938 158.940313 \n", "L 71.160938 158.940313 \n", "L 81.160938 158.940313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- multistep preds -->\n", "     <g transform=\"translate(89.160938 162.440313) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6d\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"97.412109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"160.791016\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"188.574219\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"227.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"255.566406\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"307.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"346.875\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"408.398438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"471.875\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"503.662109\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"567.138672\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"606.001953\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"667.525391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.001953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p4bdd0b6985\">\n", "   <rect x=\"52.160938\" y=\"7.2\" width=\"334.8\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.plot([data.time[data.tau:], data.time[data.num_train+data.tau:]],\n", "         [onestep_preds, multistep_preds[data.num_train+data.tau:]], 'time',\n", "         'x', legend=['1-step preds', 'multistep preds'], figsize=(6, 3))"]}, {"cell_type": "markdown", "id": "192710ff", "metadata": {"origin_pos": 21}, "source": ["Unfortunately, in this case we fail spectacularly.\n", "The predictions decay to a constant\n", "pretty quickly after a few steps.\n", "Why did the algorithm perform so much worse\n", "when predicting further into the future?\n", "Ultimately, this is down to the fact\n", "that errors build up.\n", "Let's say that after step 1 we have some error $\\epsilon_1 = \\bar\\epsilon$.\n", "Now the *input* for step 2 is perturbed by $\\epsilon_1$,\n", "hence we suffer some error in the order of\n", "$\\epsilon_2 = \\bar\\epsilon + c \\epsilon_1$\n", "for some constant $c$, and so on.\n", "The predictions can diverge rapidly\n", "from the true observations.\n", "You may already be familiar\n", "with this common phenomenon.\n", "For instance, weather forecasts for the next 24 hours\n", "tend to be pretty accurate but beyond that,\n", "accuracy declines rapidly.\n", "We will discuss methods for improving this\n", "throughout this chapter and beyond.\n", "\n", "Let's [**take a closer look at the difficulties in $k$-step-ahead predictions**]\n", "by computing predictions on the entire sequence for $k = 1, 4, 16, 64$.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "461d4735", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:34.028725Z", "iopub.status.busy": "2023-08-18T19:31:34.028433Z", "iopub.status.idle": "2023-08-18T19:31:34.034986Z", "shell.execute_reply": "2023-08-18T19:31:34.033780Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["def k_step_pred(k):\n", "    features = []\n", "    for i in range(data.tau):\n", "        features.append(data.x[i : i+data.T-data.tau-k+1])\n", "    # The (i+tau)-th element stores the (i+1)-step-ahead predictions\n", "    for i in range(k):\n", "        preds = model(torch.stack(features[i : i+data.tau], 1))\n", "        features.append(preds.reshape(-1))\n", "    return features[data.tau:]"]}, {"cell_type": "code", "execution_count": 12, "id": "26a8e67f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:31:34.039244Z", "iopub.status.busy": "2023-08-18T19:31:34.038971Z", "iopub.status.idle": "2023-08-18T19:31:34.384683Z", "shell.execute_reply": "2023-08-18T19:31:34.383554Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"394.160938pt\" height=\"211.07625pt\" viewBox=\"0 0 394.**********.07625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-03-31T21:25:18.090037</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 211.07625 \n", "L 394.**********.07625 \n", "L 394.160938 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "L 386.960938 7.2 \n", "L 52.160938 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 110.486415 173.52 \n", "L 110.486415 7.2 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m75d8756811\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m75d8756811\" x=\"110.486415\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(100.942665 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 175.800501 173.52 \n", "L 175.800501 7.2 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m75d8756811\" x=\"175.800501\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(166.256751 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 241.114586 173.52 \n", "L 241.114586 7.2 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m75d8756811\" x=\"241.114586\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(231.570836 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 306.428671 173.52 \n", "L 306.428671 7.2 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m75d8756811\" x=\"306.428671\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(296.884921 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 371.742756 173.52 \n", "L 371.742756 7.2 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m75d8756811\" x=\"371.742756\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1000 -->\n", "      <g transform=\"translate(359.017756 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- time -->\n", "     <g transform=\"translate(208.264844 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"66.992188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"164.404297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 52.160938 155.443232 \n", "L 386.960938 155.443232 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"meaac405a97\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#meaac405a97\" x=\"52.160938\" y=\"155.443232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 159.242451) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 52.160938 126.706342 \n", "L 386.960938 126.706342 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#meaac405a97\" x=\"52.160938\" y=\"126.706342\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 130.505561) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 52.160938 97.969452 \n", "L 386.960938 97.969452 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#meaac405a97\" x=\"52.160938\" y=\"97.969452\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257812 101.768671) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 52.160938 69.232562 \n", "L 386.960938 69.232562 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#meaac405a97\" x=\"52.160938\" y=\"69.232562\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257812 73.03178) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 52.160938 40.495672 \n", "L 386.960938 40.495672 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#meaac405a97\" x=\"52.160938\" y=\"40.495672\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257812 44.29489) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 52.160938 11.758782 \n", "L 386.960938 11.758782 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#meaac405a97\" x=\"52.160938\" y=\"11.758782\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(29.257812 15.558) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- x -->\n", "     <g transform=\"translate(14.798438 93.319375) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 67.379119 99.227428 \n", "L 67.70569 100.306022 \n", "L 68.03226 98.033764 \n", "L 68.358831 92.737007 \n", "L 68.685401 94.279607 \n", "L 69.011971 84.075591 \n", "L 69.665112 95.664135 \n", "L 69.991683 97.714939 \n", "L 70.318253 93.801198 \n", "L 70.644824 92.382763 \n", "L 70.971394 92.400838 \n", "L 71.297964 90.722656 \n", "L 71.624535 82.926933 \n", "L 71.951105 94.360382 \n", "L 72.277676 97.661201 \n", "L 72.604246 84.39691 \n", "L 72.930817 93.674157 \n", "L 73.257387 89.472977 \n", "L 73.583957 89.402909 \n", "L 73.910528 101.83515 \n", "L 74.237098 89.157645 \n", "L 74.890239 98.242095 \n", "L 75.21681 96.678673 \n", "L 75.54338 102.879536 \n", "L 76.196521 89.828178 \n", "L 76.523091 89.085858 \n", "L 76.849662 79.126129 \n", "L 77.176232 83.212028 \n", "L 77.502803 80.627989 \n", "L 77.829373 91.607417 \n", "L 78.155943 83.815366 \n", "L 78.482514 82.347588 \n", "L 78.809084 88.651719 \n", "L 79.135655 82.99945 \n", "L 79.462225 79.849378 \n", "L 79.788795 85.737734 \n", "L 80.115366 81.229906 \n", "L 80.441936 68.838486 \n", "L 80.768507 81.348618 \n", "L 81.421648 68.170813 \n", "L 81.748218 72.55897 \n", "L 82.074788 61.57033 \n", "L 82.401359 71.762689 \n", "L 82.727929 71.275561 \n", "L 83.0545 76.571624 \n", "L 83.38107 70.207414 \n", "L 83.707641 70.897404 \n", "L 84.034211 78.247277 \n", "L 84.360781 62.780943 \n", "L 84.687352 71.751395 \n", "L 85.013922 59.642498 \n", "L 85.340493 63.90479 \n", "L 85.667063 63.228553 \n", "L 85.993634 56.697437 \n", "L 86.320204 67.063139 \n", "L 86.646774 70.189332 \n", "L 86.973345 71.608639 \n", "L 87.299915 67.3234 \n", "L 87.626486 77.126352 \n", "L 87.953056 63.642676 \n", "L 88.279627 78.691406 \n", "L 88.606197 88.170729 \n", "L 89.259338 59.599718 \n", "L 89.585908 60.926078 \n", "L 89.912479 58.065705 \n", "L 90.239049 62.428277 \n", "L 90.56562 63.050162 \n", "L 90.89219 56.478661 \n", "L 91.21876 63.485796 \n", "L 91.545331 66.200762 \n", "L 91.871901 57.970977 \n", "L 92.198472 61.064295 \n", "L 92.525042 55.492021 \n", "L 92.851612 58.806445 \n", "L 93.178183 47.748562 \n", "L 93.504753 57.453151 \n", "L 93.831324 49.497021 \n", "L 94.157894 48.77207 \n", "L 94.484465 60.10863 \n", "L 94.811035 54.99317 \n", "L 95.137605 54.568461 \n", "L 95.464176 53.860355 \n", "L 95.790746 55.948883 \n", "L 96.117317 51.711122 \n", "L 96.443887 54.365988 \n", "L 97.097028 50.727199 \n", "L 97.423598 53.823473 \n", "L 97.750169 61.943067 \n", "L 98.076739 58.357331 \n", "L 98.40331 56.525251 \n", "L 98.72988 61.761344 \n", "L 99.056451 61.887341 \n", "L 99.709591 50.752357 \n", "L 100.036162 51.516474 \n", "L 100.362732 48.416897 \n", "L 101.015873 38.849422 \n", "L 101.342444 42.345507 \n", "L 101.669014 48.562739 \n", "L 101.995584 45.449175 \n", "L 102.322155 48.823644 \n", "L 102.648725 53.87553 \n", "L 102.975296 51.926469 \n", "L 103.301866 54.944511 \n", "L 103.628437 50.314504 \n", "L 103.955007 56.468597 \n", "L 104.281577 49.165715 \n", "L 104.608148 51.393961 \n", "L 104.934718 38.502487 \n", "L 105.261289 41.31101 \n", "L 105.587859 34.692179 \n", "L 105.91443 31.010184 \n", "L 106.241 43.377689 \n", "L 106.56757 39.42605 \n", "L 106.894141 42.415097 \n", "L 107.220711 54.187931 \n", "L 107.547282 59.736266 \n", "L 107.873852 51.449618 \n", "L 108.200422 48.285624 \n", "L 108.526993 49.889014 \n", "L 108.853563 43.610188 \n", "L 109.180134 49.49202 \n", "L 109.506704 47.673005 \n", "L 109.833275 49.746714 \n", "L 110.159845 53.093545 \n", "L 110.486415 55.188051 \n", "L 110.812986 48.028836 \n", "L 111.139556 45.258552 \n", "L 111.466127 41.211003 \n", "L 111.792697 28.868078 \n", "L 112.119268 37.873686 \n", "L 112.445838 39.818458 \n", "L 112.772408 39.227866 \n", "L 113.425549 46.709286 \n", "L 113.75212 40.032016 \n", "L 114.07869 36.751699 \n", "L 114.405261 38.924068 \n", "L 114.731831 44.388066 \n", "L 115.058401 14.76 \n", "L 115.384972 36.017365 \n", "L 115.711542 42.329424 \n", "L 116.038113 41.856704 \n", "L 116.364683 42.910063 \n", "L 116.691254 55.882513 \n", "L 117.344394 39.428339 \n", "L 117.670965 46.006962 \n", "L 117.997535 43.952242 \n", "L 118.324106 47.825713 \n", "L 118.650676 49.451644 \n", "L 118.977247 44.873129 \n", "L 119.303817 43.447824 \n", "L 119.630387 40.131245 \n", "L 119.956958 42.01481 \n", "L 120.283528 45.725893 \n", "L 120.610099 44.202784 \n", "L 120.936669 37.391936 \n", "L 121.263239 34.27525 \n", "L 121.58981 34.862574 \n", "L 121.91638 35.754264 \n", "L 122.242951 46.852939 \n", "L 122.569521 42.677126 \n", "L 122.896092 53.464473 \n", "L 123.222662 59.84395 \n", "L 123.549232 48.141522 \n", "L 123.875803 52.558073 \n", "L 124.202373 49.323181 \n", "L 124.528944 43.219638 \n", "L 124.855514 49.738746 \n", "L 125.182085 46.962219 \n", "L 125.508655 41.469774 \n", "L 126.161796 44.326061 \n", "L 126.488366 50.095019 \n", "L 126.814937 47.639598 \n", "L 127.141507 42.966344 \n", "L 127.468078 46.744968 \n", "L 127.794648 37.712972 \n", "L 128.121218 40.080237 \n", "L 128.447789 34.452997 \n", "L 128.774359 35.741219 \n", "L 129.10093 39.511734 \n", "L 129.4275 32.059964 \n", "L 129.754071 41.676029 \n", "L 130.080641 36.212507 \n", "L 130.407211 37.509574 \n", "L 130.733782 47.385212 \n", "L 131.060352 42.603449 \n", "L 131.386923 40.9545 \n", "L 131.713493 49.264944 \n", "L 132.040064 34.930142 \n", "L 132.366634 40.879299 \n", "L 132.693204 44.176608 \n", "L 133.019775 49.052043 \n", "L 133.346345 47.600949 \n", "L 133.672916 47.20923 \n", "L 133.999486 49.938828 \n", "L 134.326057 56.112769 \n", "L 134.652627 51.491686 \n", "L 134.979197 54.14722 \n", "L 135.632338 49.572812 \n", "L 135.958909 61.722274 \n", "L 136.285479 53.430459 \n", "L 136.612049 54.08703 \n", "L 136.93862 47.399661 \n", "L 137.26519 43.262839 \n", "L 137.918331 63.283929 \n", "L 138.244902 55.931861 \n", "L 138.571472 58.323067 \n", "L 138.898042 47.350838 \n", "L 139.224613 54.387646 \n", "L 139.551183 55.816291 \n", "L 139.877754 55.137313 \n", "L 140.204324 63.564124 \n", "L 140.530895 53.198872 \n", "L 140.857465 63.123664 \n", "L 141.184035 54.809213 \n", "L 141.510606 51.198291 \n", "L 141.837176 53.570006 \n", "L 142.163747 57.287857 \n", "L 142.490317 59.533314 \n", "L 142.816888 60.47391 \n", "L 143.143458 59.643368 \n", "L 143.470028 65.118301 \n", "L 143.796599 52.602045 \n", "L 144.123169 49.120639 \n", "L 144.44974 53.400173 \n", "L 145.102881 70.536398 \n", "L 145.429451 64.770524 \n", "L 145.756021 66.378769 \n", "L 146.082592 69.146669 \n", "L 146.409162 53.080349 \n", "L 146.735733 53.613094 \n", "L 147.062303 59.551796 \n", "L 147.388874 56.584317 \n", "L 147.715444 64.259741 \n", "L 148.042014 58.716201 \n", "L 148.368585 66.669892 \n", "L 148.695155 64.255455 \n", "L 149.021726 64.28939 \n", "L 149.348296 62.026082 \n", "L 149.674866 71.470845 \n", "L 150.001437 66.353826 \n", "L 150.328007 73.276092 \n", "L 150.654578 74.532962 \n", "L 150.981148 63.697518 \n", "L 151.307719 81.436256 \n", "L 151.634289 73.748102 \n", "L 151.960859 73.87033 \n", "L 152.28743 76.526494 \n", "L 152.614 67.146743 \n", "L 152.940571 74.421954 \n", "L 153.267141 70.632036 \n", "L 153.593712 74.44457 \n", "L 153.920282 71.673277 \n", "L 154.246852 73.119145 \n", "L 154.573423 77.788894 \n", "L 154.899993 69.74447 \n", "L 155.226564 82.389551 \n", "L 155.553134 75.435358 \n", "L 155.879705 85.169966 \n", "L 156.206275 75.569301 \n", "L 156.532845 71.674282 \n", "L 156.859416 73.878774 \n", "L 157.185986 73.994217 \n", "L 157.512557 77.27683 \n", "L 157.839127 71.27776 \n", "L 158.165698 70.05401 \n", "L 158.492268 73.193438 \n", "L 158.818838 80.675822 \n", "L 159.145409 80.163717 \n", "L 159.471979 77.361408 \n", "L 159.79855 71.923574 \n", "L 160.12512 91.117328 \n", "L 160.451691 81.72621 \n", "L 160.778261 76.191059 \n", "L 161.104831 87.640905 \n", "L 161.431402 79.37173 \n", "L 161.757972 66.891761 \n", "L 162.084543 72.363373 \n", "L 162.411113 84.734195 \n", "L 162.737684 75.848585 \n", "L 163.064254 78.099403 \n", "L 163.717395 87.195824 \n", "L 164.043965 89.376336 \n", "L 164.370536 92.706563 \n", "L 164.697106 102.463488 \n", "L 165.023676 100.180503 \n", "L 165.350247 99.136528 \n", "L 165.676817 99.025876 \n", "L 166.003388 92.020239 \n", "L 166.329958 94.347053 \n", "L 166.656529 87.742944 \n", "L 166.983099 91.664912 \n", "L 167.309669 102.400118 \n", "L 167.63624 90.778877 \n", "L 167.96281 90.949046 \n", "L 168.289381 100.393431 \n", "L 168.615951 95.644585 \n", "L 168.942522 103.354388 \n", "L 169.269092 94.671697 \n", "L 169.595662 96.803204 \n", "L 169.922233 110.481006 \n", "L 170.248803 100.397635 \n", "L 170.575374 98.486571 \n", "L 171.228515 91.705326 \n", "L 171.555085 94.326426 \n", "L 171.881655 90.79101 \n", "L 172.208226 100.685478 \n", "L 172.534796 107.209956 \n", "L 172.861367 96.654488 \n", "L 173.187937 109.529996 \n", "L 173.514508 109.722929 \n", "L 173.841078 108.220372 \n", "L 174.167648 97.652251 \n", "L 174.820789 112.640676 \n", "L 175.14736 103.193814 \n", "L 175.47393 113.364863 \n", "L 175.800501 111.587686 \n", "L 176.127071 108.370413 \n", "L 176.453641 117.836322 \n", "L 176.780212 119.238253 \n", "L 177.106782 110.206454 \n", "L 177.433353 111.001093 \n", "L 177.759923 123.214628 \n", "L 178.086493 113.054928 \n", "L 178.413064 114.321273 \n", "L 178.739634 123.654408 \n", "L 179.719346 105.340423 \n", "L 180.045916 110.515832 \n", "L 180.372486 108.422542 \n", "L 180.699057 96.201447 \n", "L 181.025627 113.358683 \n", "L 181.352198 116.356404 \n", "L 181.678768 123.507193 \n", "L 182.005339 124.282901 \n", "L 182.331909 113.27705 \n", "L 182.658479 122.705529 \n", "L 182.98505 115.906347 \n", "L 183.31162 120.753994 \n", "L 183.638191 133.958051 \n", "L 183.964761 131.156366 \n", "L 184.291332 122.124587 \n", "L 184.617902 138.941546 \n", "L 184.944472 138.672686 \n", "L 185.271043 136.85881 \n", "L 185.597613 144.20784 \n", "L 185.924184 130.287343 \n", "L 186.250754 126.009528 \n", "L 186.577325 116.483218 \n", "L 186.903895 118.904857 \n", "L 187.230465 126.163458 \n", "L 187.557036 118.326698 \n", "L 187.883606 121.412642 \n", "L 188.210177 122.247115 \n", "L 188.536747 116.772688 \n", "L 189.189888 132.400592 \n", "L 189.516458 126.651001 \n", "L 189.843029 129.034221 \n", "L 190.169599 125.978991 \n", "L 190.49617 127.512383 \n", "L 190.82274 127.119204 \n", "L 191.149311 139.259746 \n", "L 191.475881 140.217364 \n", "L 191.802451 136.549781 \n", "L 192.129022 138.545853 \n", "L 192.455592 138.135262 \n", "L 192.782163 135.719952 \n", "L 193.108733 137.731289 \n", "L 193.435303 133.898008 \n", "L 193.761874 132.7128 \n", "L 194.088444 127.94742 \n", "L 194.415015 125.703313 \n", "L 194.741585 130.651448 \n", "L 195.068156 145.068716 \n", "L 195.394726 132.25502 \n", "L 195.721296 136.281141 \n", "L 196.047867 144.625242 \n", "L 196.374437 131.915625 \n", "L 196.701008 133.062291 \n", "L 197.027578 135.972526 \n", "L 197.354149 137.457031 \n", "L 197.680719 137.149125 \n", "L 198.007289 141.156055 \n", "L 198.33386 135.554974 \n", "L 198.66043 144.779854 \n", "L 198.987001 150.53144 \n", "L 199.313571 138.572163 \n", "L 199.640142 150.790094 \n", "L 199.966712 147.19354 \n", "L 200.293282 145.130283 \n", "L 200.946423 150.784469 \n", "L 201.272994 144.156705 \n", "L 201.599564 155.788742 \n", "L 201.926135 155.987336 \n", "L 202.252705 144.682995 \n", "L 202.579275 155.688519 \n", "L 203.232416 150.914646 \n", "L 203.558987 141.040108 \n", "L 203.885557 144.579375 \n", "L 204.212128 146.178494 \n", "L 204.538698 148.83351 \n", "L 204.865268 156.701315 \n", "L 205.518409 142.125221 \n", "L 205.84498 143.716255 \n", "L 206.17155 142.562186 \n", "L 206.49812 144.717698 \n", "L 206.824691 155.60672 \n", "L 207.151261 145.681314 \n", "L 207.477832 146.743457 \n", "L 207.804402 145.163622 \n", "L 208.130973 137.896381 \n", "L 208.784113 156.488702 \n", "L 209.110684 143.711589 \n", "L 209.437254 162.137387 \n", "L 209.763825 153.377947 \n", "L 210.090395 154.240727 \n", "L 210.416966 159.004142 \n", "L 210.743536 146.600876 \n", "L 211.070106 157.138921 \n", "L 211.396677 142.968725 \n", "L 211.723247 144.574702 \n", "L 212.049818 149.813385 \n", "L 212.376388 147.169101 \n", "L 212.702959 152.440489 \n", "L 213.029529 148.454764 \n", "L 213.356099 155.15253 \n", "L 213.68267 149.711484 \n", "L 214.00924 148.067461 \n", "L 214.335811 153.279615 \n", "L 214.988952 156.345398 \n", "L 215.315522 155.579287 \n", "L 215.642092 158.960957 \n", "L 215.968663 155.424469 \n", "L 216.295233 150.265756 \n", "L 216.621804 152.070999 \n", "L 216.948374 144.483095 \n", "L 217.601515 151.963754 \n", "L 217.928085 156.653019 \n", "L 218.254656 163.350751 \n", "L 218.907797 154.003768 \n", "L 219.234367 160.054881 \n", "L 219.560938 159.972842 \n", "L 219.887508 154.206145 \n", "L 220.214078 159.447599 \n", "L 220.540649 155.418049 \n", "L 220.867219 148.557659 \n", "L 221.19379 159.538168 \n", "L 221.52036 160.150499 \n", "L 221.84693 154.329559 \n", "L 222.173501 165.96 \n", "L 222.500071 157.944544 \n", "L 222.826642 155.78326 \n", "L 223.153212 154.555916 \n", "L 223.479783 146.680715 \n", "L 223.806353 149.348541 \n", "L 224.132923 157.151521 \n", "L 224.459494 158.127388 \n", "L 224.786064 156.03853 \n", "L 225.112635 159.739702 \n", "L 225.439205 162.130337 \n", "L 225.765776 144.663229 \n", "L 226.418916 149.74532 \n", "L 226.745487 155.102857 \n", "L 227.072057 153.473291 \n", "L 227.725198 154.467053 \n", "L 228.051769 151.128496 \n", "L 228.378339 149.102171 \n", "L 228.704909 160.432421 \n", "L 229.03148 158.824107 \n", "L 229.35805 160.863313 \n", "L 229.684621 163.752353 \n", "L 230.011191 161.496472 \n", "L 230.337762 153.240494 \n", "L 230.664332 158.856179 \n", "L 230.990902 153.788411 \n", "L 231.317473 143.783817 \n", "L 231.644043 148.258396 \n", "L 231.970614 137.759681 \n", "L 232.297184 148.183407 \n", "L 232.623755 139.545659 \n", "L 233.276895 157.123862 \n", "L 233.603466 150.856587 \n", "L 233.930036 155.879187 \n", "L 234.256607 156.03013 \n", "L 234.583177 153.476693 \n", "L 234.909747 144.246232 \n", "L 235.236318 146.517762 \n", "L 235.562888 146.738795 \n", "L 235.889459 142.671312 \n", "L 236.216029 153.469745 \n", "L 236.5426 147.328413 \n", "L 236.86917 133.489079 \n", "L 237.19574 147.354079 \n", "L 237.522311 144.160158 \n", "L 237.848881 143.31221 \n", "L 238.175452 149.666618 \n", "L 238.502022 149.400019 \n", "L 238.828593 144.393332 \n", "L 239.155163 143.684368 \n", "L 239.481733 152.870908 \n", "L 239.808304 149.031283 \n", "L 240.134874 141.8798 \n", "L 240.461445 142.439098 \n", "L 240.788015 149.642915 \n", "L 241.114586 141.442889 \n", "L 241.441156 140.809908 \n", "L 241.767726 146.005091 \n", "L 242.094297 139.840721 \n", "L 242.420867 139.886526 \n", "L 242.747438 148.529404 \n", "L 243.074008 136.860318 \n", "L 243.400579 142.464598 \n", "L 243.727149 134.978441 \n", "L 244.053719 121.466821 \n", "L 244.38029 127.082237 \n", "L 244.70686 125.888875 \n", "L 245.033431 129.331271 \n", "L 245.686572 141.726373 \n", "L 246.013142 134.986375 \n", "L 246.339712 131.416644 \n", "L 246.666283 141.902187 \n", "L 247.319424 130.747847 \n", "L 247.645994 136.389831 \n", "L 247.972564 127.138011 \n", "L 248.299135 131.036151 \n", "L 248.625705 123.749402 \n", "L 248.952276 137.563169 \n", "L 249.278846 135.850499 \n", "L 249.605417 131.856707 \n", "L 249.931987 137.476252 \n", "L 250.258557 115.16987 \n", "L 250.585128 122.359394 \n", "L 250.911698 120.421517 \n", "L 251.238269 123.388823 \n", "L 251.564839 124.386659 \n", "L 251.89141 130.429137 \n", "L 252.21798 130.168269 \n", "L 252.54455 129.486181 \n", "L 252.871121 131.514369 \n", "L 253.197691 134.316962 \n", "L 253.524262 130.73744 \n", "L 254.177403 138.313518 \n", "L 254.503973 141.917072 \n", "L 254.830543 133.966968 \n", "L 255.157114 142.840912 \n", "L 255.483684 130.007124 \n", "L 255.810255 126.341734 \n", "L 256.136825 130.482708 \n", "L 256.463396 132.596244 \n", "L 256.789966 140.304665 \n", "L 257.116536 128.381247 \n", "L 257.443107 137.346785 \n", "L 257.769677 121.928444 \n", "L 258.096248 120.978527 \n", "L 258.422818 132.830645 \n", "L 258.749389 115.52354 \n", "L 259.075959 122.988646 \n", "L 259.402529 119.806708 \n", "L 259.7291 122.151501 \n", "L 260.05567 123.212464 \n", "L 260.382241 115.318898 \n", "L 260.708811 122.576542 \n", "L 261.035382 116.800029 \n", "L 261.361952 119.627261 \n", "L 261.688522 133.785214 \n", "L 262.015093 121.994959 \n", "L 262.341663 116.362589 \n", "L 262.668234 113.579384 \n", "L 262.994804 117.74832 \n", "L 263.321374 104.009794 \n", "L 263.647945 112.231375 \n", "L 263.974515 107.13699 \n", "L 264.301086 110.3444 \n", "L 264.627656 100.543815 \n", "L 264.954227 106.119326 \n", "L 265.280797 107.238891 \n", "L 265.607367 95.610179 \n", "L 265.933938 101.835239 \n", "L 266.260508 100.659688 \n", "L 266.587079 106.754193 \n", "L 266.913649 98.188744 \n", "L 267.24022 106.542329 \n", "L 267.56679 106.621634 \n", "L 267.89336 96.617351 \n", "L 268.219931 94.135231 \n", "L 268.546501 106.504154 \n", "L 268.873072 100.041468 \n", "L 269.526213 106.257124 \n", "L 269.852783 110.116805 \n", "L 270.179353 99.353166 \n", "L 270.505924 98.953183 \n", "L 270.832494 100.159876 \n", "L 271.159065 100.564957 \n", "L 271.485635 95.555035 \n", "L 271.812206 100.349284 \n", "L 272.138776 103.058701 \n", "L 272.465346 107.169423 \n", "L 272.791917 94.537414 \n", "L 273.118487 87.963942 \n", "L 273.445058 99.693824 \n", "L 273.771628 95.526791 \n", "L 274.098199 87.72685 \n", "L 274.424769 97.919274 \n", "L 275.07791 92.68971 \n", "L 275.40448 99.646926 \n", "L 275.731051 84.835953 \n", "L 276.057621 85.515642 \n", "L 276.384191 93.536721 \n", "L 276.710762 85.328056 \n", "L 277.037332 88.348674 \n", "L 277.363903 89.290292 \n", "L 277.690473 88.277059 \n", "L 278.017044 78.84685 \n", "L 278.343614 85.278889 \n", "L 278.670184 85.928989 \n", "L 278.996755 84.309984 \n", "L 279.323325 83.383743 \n", "L 279.649896 91.399494 \n", "L 279.976466 82.656831 \n", "L 280.303037 81.057355 \n", "L 280.629607 75.720654 \n", "L 280.956177 78.683194 \n", "L 281.282748 85.661241 \n", "L 281.609318 81.642415 \n", "L 281.935889 74.047631 \n", "L 282.262459 78.289255 \n", "L 282.58903 79.498191 \n", "L 282.9156 83.693337 \n", "L 283.24217 94.004944 \n", "L 283.568741 90.571595 \n", "L 283.895311 82.298158 \n", "L 284.221882 82.732949 \n", "L 284.875023 73.402486 \n", "L 285.201593 76.308061 \n", "L 285.854734 71.47072 \n", "L 286.181304 76.200436 \n", "L 286.507875 73.604851 \n", "L 286.834445 74.464772 \n", "L 287.161016 73.066752 \n", "L 287.487586 78.475136 \n", "L 287.814156 68.690495 \n", "L 288.140727 67.504839 \n", "L 288.467297 64.024926 \n", "L 288.793868 67.592454 \n", "L 289.120438 67.199478 \n", "L 289.447009 62.461513 \n", "L 289.773579 65.116263 \n", "L 290.100149 56.55302 \n", "L 290.42672 54.936598 \n", "L 290.75329 65.441852 \n", "L 291.079861 56.510664 \n", "L 291.406431 66.314372 \n", "L 291.733001 65.803192 \n", "L 292.059572 62.042217 \n", "L 292.386142 69.620613 \n", "L 292.712713 62.92359 \n", "L 293.039283 60.546514 \n", "L 293.365854 66.194377 \n", "L 293.692424 55.195804 \n", "L 294.018994 65.495136 \n", "L 294.345565 63.664936 \n", "L 294.672135 58.548472 \n", "L 295.325276 67.656635 \n", "L 295.651847 72.424112 \n", "L 295.978417 68.489417 \n", "L 296.304987 62.922904 \n", "L 296.631558 50.742868 \n", "L 296.958128 57.800089 \n", "L 297.284699 48.488477 \n", "L 297.611269 54.486512 \n", "L 297.93784 54.270223 \n", "L 298.26441 53.475929 \n", "L 298.59098 58.888363 \n", "L 299.244121 64.048968 \n", "L 299.570692 68.50599 \n", "L 299.897262 62.262819 \n", "L 300.223833 65.922999 \n", "L 300.550403 57.443323 \n", "L 300.876973 62.916433 \n", "L 301.203544 57.959782 \n", "L 301.530114 45.195108 \n", "L 301.856685 54.611331 \n", "L 302.183255 47.185137 \n", "L 302.509826 52.16028 \n", "L 302.836396 49.642878 \n", "L 303.162966 51.295742 \n", "L 303.489537 57.45435 \n", "L 303.816107 54.614958 \n", "L 304.142678 45.14734 \n", "L 304.469248 43.199011 \n", "L 305.122389 41.863809 \n", "L 305.448959 38.389747 \n", "L 305.77553 51.10515 \n", "L 306.1021 38.086545 \n", "L 306.428671 42.836959 \n", "L 306.755241 36.56197 \n", "L 307.081811 40.74865 \n", "L 307.408382 37.935602 \n", "L 307.734952 41.179651 \n", "L 308.061523 49.796116 \n", "L 308.388093 50.552138 \n", "L 308.714664 58.672222 \n", "L 309.367804 63.279832 \n", "L 309.694375 48.458769 \n", "L 310.020945 47.595293 \n", "L 310.347516 47.250239 \n", "L 310.674086 38.149427 \n", "L 311.000657 43.908807 \n", "L 311.327227 53.736327 \n", "L 311.653797 51.484409 \n", "L 311.980368 58.252844 \n", "L 312.306938 44.185155 \n", "L 312.633509 47.932447 \n", "L 313.28665 43.077317 \n", "L 313.61322 53.919287 \n", "L 313.93979 47.670909 \n", "L 314.266361 45.993917 \n", "L 314.592931 49.7561 \n", "L 314.919502 50.814715 \n", "L 315.246072 44.630829 \n", "L 315.572643 44.597326 \n", "L 315.899213 50.373844 \n", "L 316.225783 44.914436 \n", "L 316.552354 46.577335 \n", "L 316.878924 47.044806 \n", "L 317.205495 35.708449 \n", "L 317.532065 39.230867 \n", "L 318.185206 33.922149 \n", "L 318.511776 43.803035 \n", "L 319.164917 51.457939 \n", "L 319.491488 42.511339 \n", "L 319.818058 57.04515 \n", "L 320.144628 39.957281 \n", "L 320.471199 40.50141 \n", "L 320.797769 46.458031 \n", "L 321.12434 28.760305 \n", "L 321.45091 39.358139 \n", "L 321.777481 31.14032 \n", "L 322.104051 42.229171 \n", "L 322.430621 42.204903 \n", "L 322.757192 45.290589 \n", "L 323.083762 43.694803 \n", "L 323.410333 51.275188 \n", "L 323.736903 43.318729 \n", "L 324.063474 42.721424 \n", "L 324.390044 48.71853 \n", "L 324.716614 47.27859 \n", "L 325.043185 49.586943 \n", "L 325.369755 54.609539 \n", "L 325.696326 33.265332 \n", "L 326.022896 32.986453 \n", "L 326.349467 32.001021 \n", "L 326.676037 30.417969 \n", "L 327.002607 34.84489 \n", "L 327.329178 36.48039 \n", "L 327.655748 37.074805 \n", "L 327.982319 36.392007 \n", "L 328.308889 36.985565 \n", "L 328.63546 31.887398 \n", "L 328.96203 38.223566 \n", "L 329.2886 29.85714 \n", "L 329.615171 34.981843 \n", "L 329.941741 22.842326 \n", "L 330.268312 30.570639 \n", "L 330.594882 34.415547 \n", "L 330.921453 26.591348 \n", "L 331.248023 40.232461 \n", "L 331.574593 33.19601 \n", "L 331.901164 34.711706 \n", "L 332.227734 43.41581 \n", "L 332.554305 33.433966 \n", "L 332.880875 40.917763 \n", "L 333.207445 52.43582 \n", "L 333.534016 44.297378 \n", "L 333.860586 44.013065 \n", "L 334.187157 37.7108 \n", "L 334.840298 49.056633 \n", "L 335.166868 36.726314 \n", "L 335.493438 48.040343 \n", "L 335.820009 39.691145 \n", "L 336.146579 34.741085 \n", "L 336.47315 40.297118 \n", "L 336.79972 42.644167 \n", "L 337.126291 50.56357 \n", "L 337.779431 46.095146 \n", "L 338.106002 47.919265 \n", "L 338.432572 53.795609 \n", "L 339.085713 50.829511 \n", "L 339.412284 46.734472 \n", "L 339.738854 59.140879 \n", "L 340.065424 55.00937 \n", "L 340.391995 46.668184 \n", "L 340.718565 49.055626 \n", "L 341.045136 45.367407 \n", "L 341.371706 55.817912 \n", "L 341.698277 50.780914 \n", "L 342.024847 54.477916 \n", "L 342.351417 50.159943 \n", "L 342.677988 53.242645 \n", "L 343.331129 55.884761 \n", "L 343.98427 49.857635 \n", "L 344.31084 59.893184 \n", "L 344.63741 59.092316 \n", "L 344.963981 54.074321 \n", "L 345.290551 55.030239 \n", "L 345.617122 51.255531 \n", "L 345.943692 69.610645 \n", "L 346.270263 57.367837 \n", "L 346.596833 54.750572 \n", "L 346.923403 62.470006 \n", "L 347.249974 49.891022 \n", "L 347.576544 51.584995 \n", "L 347.903115 48.161134 \n", "L 348.229685 50.113826 \n", "L 348.882826 56.389734 \n", "L 349.209396 62.956247 \n", "L 349.535967 65.08538 \n", "L 349.862537 62.196045 \n", "L 350.189108 70.323993 \n", "L 350.515678 66.464435 \n", "L 350.842248 66.170643 \n", "L 351.168819 68.576169 \n", "L 351.495389 69.666439 \n", "L 351.82196 68.731477 \n", "L 352.14853 73.508805 \n", "L 353.128241 63.424308 \n", "L 353.454812 65.038777 \n", "L 353.781382 59.671401 \n", "L 354.107953 63.828996 \n", "L 354.434523 71.458367 \n", "L 354.761094 66.844479 \n", "L 355.087664 55.250526 \n", "L 355.414234 62.326133 \n", "L 355.740805 63.038885 \n", "L 356.067375 65.24869 \n", "L 356.393946 62.87044 \n", "L 356.720516 68.212164 \n", "L 357.047087 64.407378 \n", "L 357.373657 63.09765 \n", "L 357.700227 74.999456 \n", "L 358.026798 68.027115 \n", "L 358.353368 70.121931 \n", "L 358.679939 80.164893 \n", "L 359.006509 83.628208 \n", "L 359.33308 84.617769 \n", "L 359.65965 87.865586 \n", "L 359.98622 76.421342 \n", "L 360.312791 72.528163 \n", "L 360.639361 77.052881 \n", "L 360.965932 77.447767 \n", "L 361.292502 76.705299 \n", "L 361.619072 84.792407 \n", "L 362.272213 76.307869 \n", "L 362.598784 76.997767 \n", "L 362.925354 80.034861 \n", "L 363.251925 86.270267 \n", "L 363.578495 81.134556 \n", "L 363.905065 92.001069 \n", "L 364.231636 89.071717 \n", "L 364.558206 80.592623 \n", "L 364.884777 83.678924 \n", "L 365.211347 77.362988 \n", "L 365.537918 80.625592 \n", "L 365.864488 89.115103 \n", "L 366.191058 80.702561 \n", "L 366.517629 89.532933 \n", "L 366.844199 95.236325 \n", "L 367.17077 93.626581 \n", "L 367.49734 92.904028 \n", "L 367.823911 94.518725 \n", "L 368.150481 98.681134 \n", "L 368.477051 100.361961 \n", "L 368.803622 100.227348 \n", "L 369.130192 97.117282 \n", "L 369.456763 97.064011 \n", "L 369.783333 103.184517 \n", "L 370.109904 79.841365 \n", "L 370.436474 78.285038 \n", "L 370.763044 84.444092 \n", "L 371.089615 85.396522 \n", "L 371.416185 87.092926 \n", "L 371.742756 91.144755 \n", "L 371.742756 91.144755 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 67.379119 99.880809 \n", "L 67.70569 100.086716 \n", "L 68.358831 84.901037 \n", "L 68.685401 94.889426 \n", "L 69.011971 90.865866 \n", "L 69.338542 95.356747 \n", "L 69.665112 95.040068 \n", "L 69.991683 99.363876 \n", "L 70.644824 86.339736 \n", "L 70.971394 87.370195 \n", "L 71.297964 99.254899 \n", "L 71.624535 89.200801 \n", "L 71.951105 89.975225 \n", "L 72.277676 94.564643 \n", "L 72.604246 83.768324 \n", "L 72.930817 98.04371 \n", "L 73.257387 96.037939 \n", "L 73.583957 86.740801 \n", "L 73.910528 98.727512 \n", "L 74.237098 91.474172 \n", "L 74.890239 102.963482 \n", "L 75.21681 98.035217 \n", "L 75.54338 99.769216 \n", "L 76.196521 81.494968 \n", "L 76.523091 85.376868 \n", "L 76.849662 82.058621 \n", "L 77.176232 90.414166 \n", "L 77.502803 79.478862 \n", "L 77.829373 91.100731 \n", "L 78.155943 84.717783 \n", "L 78.482514 81.725877 \n", "L 78.809084 88.771248 \n", "L 79.135655 87.540828 \n", "L 79.462225 75.457431 \n", "L 79.788795 78.262033 \n", "L 80.115366 85.618243 \n", "L 80.441936 71.940069 \n", "L 80.768507 78.465535 \n", "L 81.421648 63.243537 \n", "L 81.748218 75.385464 \n", "L 82.074788 67.674935 \n", "L 82.401359 75.179177 \n", "L 82.727929 68.635314 \n", "L 83.0545 79.860011 \n", "L 83.707641 67.590707 \n", "L 84.034211 76.313866 \n", "L 84.360781 61.130069 \n", "L 84.687352 73.502087 \n", "L 85.013922 56.057341 \n", "L 85.340493 59.633262 \n", "L 85.667063 65.76343 \n", "L 85.993634 66.463264 \n", "L 86.320204 73.664236 \n", "L 86.646774 70.902841 \n", "L 86.973345 67.045267 \n", "L 87.299915 60.357326 \n", "L 87.626486 91.254593 \n", "L 87.953056 80.408004 \n", "L 88.279627 74.198589 \n", "L 88.606197 71.089799 \n", "L 89.259338 60.749792 \n", "L 89.585908 64.976806 \n", "L 89.912479 57.394729 \n", "L 90.239049 60.041147 \n", "L 90.56562 67.535279 \n", "L 90.89219 61.724449 \n", "L 91.21876 61.801692 \n", "L 91.545331 64.970369 \n", "L 91.871901 57.597339 \n", "L 92.198472 61.246748 \n", "L 92.525042 53.35506 \n", "L 92.851612 60.133956 \n", "L 93.178183 42.801863 \n", "L 93.504753 58.282617 \n", "L 93.831324 56.610688 \n", "L 94.157894 52.858151 \n", "L 94.484465 57.959573 \n", "L 94.811035 55.13647 \n", "L 95.137605 56.666458 \n", "L 95.464176 54.903324 \n", "L 95.790746 57.574798 \n", "L 96.117317 49.338613 \n", "L 96.443887 52.459639 \n", "L 96.770458 57.174189 \n", "L 97.097028 58.231587 \n", "L 97.423598 53.158274 \n", "L 97.750169 61.505937 \n", "L 98.076739 64.510482 \n", "L 98.72988 57.235348 \n", "L 99.056451 58.030046 \n", "L 99.383021 57.815381 \n", "L 100.036162 45.575505 \n", "L 100.362732 42.953755 \n", "L 100.689303 46.084232 \n", "L 101.015873 43.763557 \n", "L 101.342444 43.184181 \n", "L 101.669014 51.38048 \n", "L 101.995584 51.455195 \n", "L 102.322155 51.994627 \n", "L 102.648725 54.235435 \n", "L 102.975296 51.989841 \n", "L 103.301866 58.63653 \n", "L 103.628437 51.890897 \n", "L 103.955007 57.074021 \n", "L 104.281577 43.753927 \n", "L 104.608148 48.205294 \n", "L 104.934718 32.852364 \n", "L 105.261289 42.059101 \n", "L 105.587859 38.586068 \n", "L 105.91443 30.09239 \n", "L 106.241 44.38426 \n", "L 106.56757 52.838604 \n", "L 106.894141 54.368866 \n", "L 107.220711 52.887687 \n", "L 107.547282 55.889615 \n", "L 107.873852 50.200541 \n", "L 108.200422 47.323676 \n", "L 108.526993 51.088631 \n", "L 108.853563 43.936843 \n", "L 109.180134 51.334007 \n", "L 109.506704 52.265902 \n", "L 109.833275 55.269881 \n", "L 110.159845 53.8717 \n", "L 110.486415 54.460894 \n", "L 110.812986 41.450186 \n", "L 111.139556 36.411992 \n", "L 111.466127 41.232756 \n", "L 111.792697 32.73789 \n", "L 112.119268 41.987535 \n", "L 112.445838 45.828945 \n", "L 112.772408 42.775563 \n", "L 113.098979 38.471238 \n", "L 113.425549 47.754814 \n", "L 113.75212 48.000174 \n", "L 114.405261 22.315267 \n", "L 114.731831 48.00622 \n", "L 115.058401 23.809306 \n", "L 115.384972 39.655059 \n", "L 115.711542 46.6349 \n", "L 116.038113 56.257337 \n", "L 116.364683 42.232785 \n", "L 116.691254 49.432063 \n", "L 117.017824 44.88181 \n", "L 117.344394 41.966361 \n", "L 117.670965 50.670407 \n", "L 117.997535 49.991491 \n", "L 118.324106 48.64781 \n", "L 118.977247 40.349209 \n", "L 119.303817 45.442704 \n", "L 119.630387 47.513679 \n", "L 119.956958 45.686162 \n", "L 120.283528 41.446938 \n", "L 120.936669 36.065359 \n", "L 121.263239 36.64037 \n", "L 121.58981 38.88546 \n", "L 121.91638 38.631225 \n", "L 122.242951 57.261534 \n", "L 122.569521 51.767061 \n", "L 122.896092 53.553435 \n", "L 123.222662 57.782279 \n", "L 123.549232 45.458705 \n", "L 123.875803 50.88844 \n", "L 124.202373 54.077256 \n", "L 124.528944 45.169829 \n", "L 124.855514 44.386614 \n", "L 125.182085 44.295477 \n", "L 125.508655 45.704376 \n", "L 125.835225 49.171418 \n", "L 126.161796 45.883362 \n", "L 126.488366 49.701961 \n", "L 126.814937 48.888308 \n", "L 127.141507 41.30819 \n", "L 127.468078 44.649759 \n", "L 127.794648 34.989989 \n", "L 128.121218 41.310493 \n", "L 128.447789 36.20793 \n", "L 128.774359 36.972218 \n", "L 129.10093 42.037735 \n", "L 129.4275 31.561915 \n", "L 129.754071 43.28561 \n", "L 130.080641 41.668163 \n", "L 130.407211 41.385235 \n", "L 130.733782 50.937263 \n", "L 131.060352 45.76967 \n", "L 131.386923 33.651272 \n", "L 131.713493 47.127506 \n", "L 132.040064 41.685874 \n", "L 132.366634 49.276057 \n", "L 132.693204 43.683581 \n", "L 133.019775 47.870158 \n", "L 133.346345 50.255633 \n", "L 133.672916 55.252523 \n", "L 133.999486 53.44013 \n", "L 134.326057 56.093859 \n", "L 134.652627 46.465739 \n", "L 135.305768 60.699807 \n", "L 135.632338 56.654873 \n", "L 135.958909 61.200248 \n", "L 136.285479 43.090797 \n", "L 136.612049 46.307047 \n", "L 136.93862 53.902453 \n", "L 137.26519 58.212287 \n", "L 137.591761 57.153672 \n", "L 137.918331 60.62919 \n", "L 138.244902 49.392229 \n", "L 138.571472 59.429453 \n", "L 138.898042 49.875969 \n", "L 139.224613 58.05676 \n", "L 139.551183 58.752085 \n", "L 139.877754 57.663099 \n", "L 140.204324 67.850985 \n", "L 140.530895 50.611989 \n", "L 140.857465 58.610049 \n", "L 141.184035 54.546688 \n", "L 141.510606 55.851412 \n", "L 141.837176 54.287369 \n", "L 142.490317 66.623857 \n", "L 142.816888 67.228042 \n", "L 143.143458 51.848205 \n", "L 143.470028 54.719919 \n", "L 143.796599 52.276281 \n", "L 144.123169 59.237655 \n", "L 144.44974 61.317393 \n", "L 144.77631 65.555366 \n", "L 145.102881 76.007445 \n", "L 145.756021 56.397102 \n", "L 146.082592 63.274474 \n", "L 146.409162 56.184078 \n", "L 146.735733 56.577013 \n", "L 147.062303 61.399548 \n", "L 147.388874 58.568629 \n", "L 147.715444 70.142341 \n", "L 148.042014 60.718731 \n", "L 148.368585 65.365051 \n", "L 148.695155 62.345057 \n", "L 149.021726 69.566511 \n", "L 149.348296 65.695156 \n", "L 149.674866 76.938255 \n", "L 150.001437 64.772463 \n", "L 150.328007 70.798126 \n", "L 150.654578 80.877909 \n", "L 150.981148 68.057641 \n", "L 151.307719 82.380845 \n", "L 151.634289 72.656507 \n", "L 151.960859 71.678263 \n", "L 152.28743 76.615186 \n", "L 152.614 68.517583 \n", "L 152.940571 75.67137 \n", "L 153.267141 71.319708 \n", "L 153.593712 77.976574 \n", "L 153.920282 71.93021 \n", "L 154.246852 70.299869 \n", "L 154.573423 80.890763 \n", "L 154.899993 76.081886 \n", "L 155.226564 89.902295 \n", "L 155.553134 70.717817 \n", "L 155.879705 78.307877 \n", "L 156.206275 73.859362 \n", "L 156.532845 78.929605 \n", "L 156.859416 77.129632 \n", "L 157.185986 69.169008 \n", "L 157.512557 71.195859 \n", "L 158.165698 80.235567 \n", "L 158.492268 75.482365 \n", "L 158.818838 75.821063 \n", "L 159.145409 79.684949 \n", "L 159.471979 86.151578 \n", "L 159.79855 71.011213 \n", "L 160.12512 92.310683 \n", "L 160.451691 89.449164 \n", "L 160.778261 72.562211 \n", "L 161.104831 73.539074 \n", "L 161.431402 80.966126 \n", "L 161.757972 77.089397 \n", "L 162.084543 71.651933 \n", "L 162.411113 83.183883 \n", "L 162.737684 80.070248 \n", "L 163.390824 84.41695 \n", "L 163.717395 92.261597 \n", "L 164.043965 97.278185 \n", "L 164.370536 96.559511 \n", "L 164.697106 104.182503 \n", "L 165.023676 101.304476 \n", "L 165.350247 96.030853 \n", "L 165.676817 92.828687 \n", "L 166.003388 87.571046 \n", "L 166.329958 100.628563 \n", "L 166.656529 95.229123 \n", "L 166.983099 86.339298 \n", "L 167.309669 97.795608 \n", "L 167.96281 97.144888 \n", "L 168.289381 100.369421 \n", "L 168.615951 89.234672 \n", "L 168.942522 105.316894 \n", "L 169.269092 104.395816 \n", "L 169.595662 99.174661 \n", "L 169.922233 106.994411 \n", "L 170.248803 97.312168 \n", "L 170.901944 91.664374 \n", "L 171.228515 92.793819 \n", "L 171.555085 101.760458 \n", "L 171.881655 93.574746 \n", "L 172.208226 97.877658 \n", "L 172.534796 115.049265 \n", "L 172.861367 105.182322 \n", "L 173.187937 106.31222 \n", "L 173.514508 100.022675 \n", "L 173.841078 111.393649 \n", "L 174.167648 103.205888 \n", "L 174.494219 106.578329 \n", "L 174.820789 113.659948 \n", "L 175.14736 102.538318 \n", "L 175.47393 113.762729 \n", "L 175.800501 118.786102 \n", "L 176.127071 111.536113 \n", "L 176.453641 111.772636 \n", "L 176.780212 119.620064 \n", "L 177.106782 115.199742 \n", "L 177.433353 107.015211 \n", "L 177.759923 123.808225 \n", "L 178.086493 121.544962 \n", "L 178.413064 114.707489 \n", "L 178.739634 115.316211 \n", "L 179.066205 114.530377 \n", "L 179.392775 116.669564 \n", "L 179.719346 100.538506 \n", "L 180.045916 99.48552 \n", "L 180.372486 110.745683 \n", "L 180.699057 107.10627 \n", "L 181.025627 124.493482 \n", "L 181.352198 119.496677 \n", "L 181.678768 120.087422 \n", "L 182.005339 119.733836 \n", "L 182.331909 108.888203 \n", "L 182.658479 128.026163 \n", "L 182.98505 127.579934 \n", "L 183.31162 121.870205 \n", "L 183.638191 127.163269 \n", "L 183.964761 135.590752 \n", "L 184.291332 126.613118 \n", "L 184.617902 143.919755 \n", "L 184.944472 145.254136 \n", "L 185.271043 134.327475 \n", "L 185.597613 132.313401 \n", "L 185.924184 118.757355 \n", "L 186.250754 125.062691 \n", "L 186.577325 120.467107 \n", "L 186.903895 120.275406 \n", "L 187.230465 126.382484 \n", "L 187.557036 116.002323 \n", "L 187.883606 116.347774 \n", "L 188.210177 125.136586 \n", "L 188.536747 125.458985 \n", "L 188.863318 126.980686 \n", "L 189.189888 132.660373 \n", "L 189.516458 123.319826 \n", "L 190.169599 126.078583 \n", "L 190.49617 137.150625 \n", "L 190.82274 132.548045 \n", "L 191.149311 139.379591 \n", "L 191.475881 139.10004 \n", "L 191.802451 135.718599 \n", "L 192.129022 136.56803 \n", "L 192.455592 140.011561 \n", "L 193.108733 135.226613 \n", "L 193.435303 125.154208 \n", "L 193.761874 127.570431 \n", "L 194.088444 133.364708 \n", "L 194.415015 133.929169 \n", "L 194.741585 127.911087 \n", "L 195.068156 147.87766 \n", "L 195.394726 137.400767 \n", "L 195.721296 131.191161 \n", "L 196.047867 138.098665 \n", "L 196.374437 133.977108 \n", "L 196.701008 136.777649 \n", "L 197.027578 136.255873 \n", "L 197.354149 136.109503 \n", "L 197.680719 135.597487 \n", "L 198.007289 147.434264 \n", "L 198.33386 139.022725 \n", "L 198.66043 141.827644 \n", "L 198.987001 152.848884 \n", "L 199.313571 140.506918 \n", "L 199.640142 149.795955 \n", "L 199.966712 145.966446 \n", "L 200.293282 144.728071 \n", "L 200.619853 147.84294 \n", "L 200.946423 158.197601 \n", "L 201.272994 143.482101 \n", "L 201.599564 148.941403 \n", "L 201.926135 159.007095 \n", "L 202.252705 151.333716 \n", "L 202.579275 153.835059 \n", "L 202.905846 143.299312 \n", "L 203.232416 145.378907 \n", "L 203.558987 139.728492 \n", "L 203.885557 151.295906 \n", "L 204.212128 154.271816 \n", "L 204.538698 148.388422 \n", "L 204.865268 149.029625 \n", "L 205.191839 142.512675 \n", "L 205.518409 138.709811 \n", "L 205.84498 146.790098 \n", "L 206.17155 151.008284 \n", "L 206.49812 146.028129 \n", "L 206.824691 152.940176 \n", "L 207.151261 139.078262 \n", "L 207.477832 141.719107 \n", "L 207.804402 149.130121 \n", "L 208.130973 143.463551 \n", "L 208.457543 146.766293 \n", "L 208.784113 161.863399 \n", "L 209.110684 144.282685 \n", "L 209.437254 161.925493 \n", "L 209.763825 153.049422 \n", "L 210.090395 154.192953 \n", "L 210.416966 157.880627 \n", "L 210.743536 139.88257 \n", "L 211.070106 153.225352 \n", "L 211.396677 143.708708 \n", "L 211.723247 145.961126 \n", "L 212.049818 149.992142 \n", "L 212.376388 149.807174 \n", "L 212.702959 155.210794 \n", "L 213.029529 144.962978 \n", "L 213.356099 151.664786 \n", "L 213.68267 151.03582 \n", "L 214.00924 151.523651 \n", "L 214.988952 158.160494 \n", "L 215.315522 154.891255 \n", "L 215.642092 157.207079 \n", "L 215.968663 153.126589 \n", "L 216.295233 143.402913 \n", "L 216.621804 147.151842 \n", "L 216.948374 146.407653 \n", "L 217.274945 156.816713 \n", "L 217.601515 158.844593 \n", "L 217.928085 154.554443 \n", "L 218.254656 158.127847 \n", "L 218.581226 159.824084 \n", "L 218.907797 155.452372 \n", "L 219.234367 159.35094 \n", "L 219.560938 160.416169 \n", "L 219.887508 149.858447 \n", "L 220.540649 158.643086 \n", "L 220.867219 150.085708 \n", "L 221.52036 164.721896 \n", "L 221.84693 156.205424 \n", "L 222.173501 163.908332 \n", "L 222.500071 152.007877 \n", "L 222.826642 148.524912 \n", "L 223.153212 152.874313 \n", "L 223.806353 149.847772 \n", "L 224.459494 165.644568 \n", "L 224.786064 157.944729 \n", "L 225.112635 147.289854 \n", "L 225.439205 154.140666 \n", "L 225.765776 145.608011 \n", "L 226.092346 152.338554 \n", "L 226.418916 151.077761 \n", "L 226.745487 157.701052 \n", "L 227.072057 152.372057 \n", "L 227.398628 149.041711 \n", "L 227.725198 151.016074 \n", "L 228.051769 155.737644 \n", "L 228.378339 152.296716 \n", "L 228.704909 163.928551 \n", "L 229.35805 158.718972 \n", "L 229.684621 158.965794 \n", "L 230.011191 163.256578 \n", "L 230.337762 150.757249 \n", "L 230.664332 152.294575 \n", "L 230.990902 149.874356 \n", "L 231.317473 140.363871 \n", "L 231.644043 146.496121 \n", "L 231.970614 134.171859 \n", "L 232.297184 153.073186 \n", "L 232.623755 144.827033 \n", "L 232.950325 148.536173 \n", "L 233.276895 158.472404 \n", "L 233.603466 155.923611 \n", "L 233.930036 154.781629 \n", "L 234.256607 148.3871 \n", "L 234.583177 148.011776 \n", "L 234.909747 141.004447 \n", "L 235.236318 149.332978 \n", "L 235.562888 154.299406 \n", "L 235.889459 139.672519 \n", "L 236.5426 147.23088 \n", "L 236.86917 134.621147 \n", "L 237.19574 148.892247 \n", "L 237.522311 149.223746 \n", "L 237.848881 143.946407 \n", "L 238.175452 142.428988 \n", "L 238.502022 149.623755 \n", "L 238.828593 152.142658 \n", "L 239.155163 143.209744 \n", "L 239.808304 147.77457 \n", "L 240.134874 145.621316 \n", "L 240.461445 139.572461 \n", "L 240.788015 148.117737 \n", "L 241.441156 138.02578 \n", "L 241.767726 144.06259 \n", "L 242.094297 142.702459 \n", "L 242.420867 140.167095 \n", "L 242.747438 151.026646 \n", "L 243.074008 132.467075 \n", "L 243.400579 132.189781 \n", "L 243.727149 128.145696 \n", "L 244.053719 119.162388 \n", "L 244.38029 129.463561 \n", "L 244.70686 135.324338 \n", "L 245.033431 135.785325 \n", "L 245.360001 131.507011 \n", "L 245.686572 139.147386 \n", "L 246.013142 139.394791 \n", "L 246.339712 130.778874 \n", "L 246.666283 139.132012 \n", "L 246.992853 137.598351 \n", "L 247.319424 128.321243 \n", "L 247.645994 130.481499 \n", "L 247.972564 122.390237 \n", "L 248.299135 136.879919 \n", "L 248.625705 129.212173 \n", "L 248.952276 142.601089 \n", "L 249.278846 134.068691 \n", "L 249.605417 120.619042 \n", "L 249.931987 131.210023 \n", "L 250.258557 114.203049 \n", "L 250.585128 123.784675 \n", "L 250.911698 121.852778 \n", "L 251.238269 129.121042 \n", "L 251.564839 125.065327 \n", "L 251.89141 131.124356 \n", "L 252.21798 131.646622 \n", "L 252.54455 131.20893 \n", "L 252.871121 129.283074 \n", "L 253.197691 135.426486 \n", "L 253.524262 133.547871 \n", "L 253.850832 137.733941 \n", "L 254.177403 140.108639 \n", "L 254.503973 146.57036 \n", "L 254.830543 126.623825 \n", "L 255.157114 133.049996 \n", "L 255.483684 126.952331 \n", "L 256.136825 136.569009 \n", "L 256.463396 133.429886 \n", "L 256.789966 137.975059 \n", "L 257.116536 119.045225 \n", "L 257.443107 136.393942 \n", "L 258.096248 116.559408 \n", "L 258.422818 127.936194 \n", "L 258.749389 115.565844 \n", "L 259.075959 126.75203 \n", "L 259.402529 121.067223 \n", "L 259.7291 120.599104 \n", "L 260.05567 118.925524 \n", "L 260.382241 110.813753 \n", "L 260.708811 127.73493 \n", "L 261.035382 128.462512 \n", "L 261.361952 118.696807 \n", "L 261.688522 125.865737 \n", "L 262.015093 119.207049 \n", "L 262.341663 115.5796 \n", "L 262.668234 106.982898 \n", "L 262.994804 118.835887 \n", "L 263.321374 104.369815 \n", "L 263.647945 111.139879 \n", "L 263.974515 102.326875 \n", "L 264.301086 114.549903 \n", "L 264.627656 100.126995 \n", "L 264.954227 100.848944 \n", "L 265.280797 105.518693 \n", "L 265.607367 98.321276 \n", "L 265.933938 102.267681 \n", "L 266.260508 99.691598 \n", "L 266.587079 113.969171 \n", "L 266.913649 99.640606 \n", "L 267.24022 99.323265 \n", "L 267.56679 102.389216 \n", "L 267.89336 102.063758 \n", "L 268.219931 92.888851 \n", "L 268.546501 107.691661 \n", "L 268.873072 106.536869 \n", "L 269.199642 107.056253 \n", "L 269.526213 99.600539 \n", "L 269.852783 108.328118 \n", "L 270.179353 101.296861 \n", "L 270.832494 93.109896 \n", "L 271.159065 103.751034 \n", "L 271.485635 105.655912 \n", "L 271.812206 104.733659 \n", "L 272.138776 92.213163 \n", "L 272.465346 101.150779 \n", "L 272.791917 100.25221 \n", "L 273.118487 88.799891 \n", "L 273.771628 97.002384 \n", "L 274.098199 91.220113 \n", "L 274.424769 101.417568 \n", "L 274.751339 96.434899 \n", "L 275.07791 85.625907 \n", "L 275.40448 96.21208 \n", "L 275.731051 87.498367 \n", "L 276.057621 84.851314 \n", "L 276.384191 94.528565 \n", "L 276.710762 88.984688 \n", "L 277.363903 83.920338 \n", "L 277.690473 90.499396 \n", "L 278.017044 79.565046 \n", "L 278.343614 82.992723 \n", "L 278.996755 93.545213 \n", "L 279.323325 83.420577 \n", "L 279.649896 84.151034 \n", "L 279.976466 74.388635 \n", "L 280.303037 84.488035 \n", "L 280.629607 84.510395 \n", "L 280.956177 81.17276 \n", "L 281.609318 77.528372 \n", "L 281.935889 73.36128 \n", "L 282.262459 85.213054 \n", "L 282.58903 90.962584 \n", "L 282.9156 87.49151 \n", "L 283.24217 89.692895 \n", "L 283.568741 87.609479 \n", "L 283.895311 79.015902 \n", "L 284.221882 79.509238 \n", "L 284.548452 77.323279 \n", "L 284.875023 72.649751 \n", "L 285.201593 76.350644 \n", "L 285.528163 75.889902 \n", "L 285.854734 70.933597 \n", "L 286.181304 75.35787 \n", "L 286.507875 77.049181 \n", "L 286.834445 80.081476 \n", "L 287.161016 69.854841 \n", "L 287.487586 72.982894 \n", "L 287.814156 65.032429 \n", "L 288.140727 70.233114 \n", "L 288.467297 66.289265 \n", "L 288.793868 69.428736 \n", "L 289.120438 66.008265 \n", "L 289.447009 56.927624 \n", "L 289.773579 62.767713 \n", "L 290.100149 60.470021 \n", "L 290.42672 56.246186 \n", "L 290.75329 69.021538 \n", "L 291.079861 58.50402 \n", "L 291.406431 68.315936 \n", "L 291.733001 69.492293 \n", "L 292.059572 62.846737 \n", "L 292.386142 67.063087 \n", "L 292.712713 61.592347 \n", "L 293.039283 59.356524 \n", "L 293.365854 72.241836 \n", "L 293.692424 57.10474 \n", "L 294.018994 60.511188 \n", "L 294.345565 61.659004 \n", "L 294.672135 66.475949 \n", "L 294.998706 73.448848 \n", "L 295.325276 71.09751 \n", "L 295.651847 66.801603 \n", "L 295.978417 59.760435 \n", "L 296.304987 60.817813 \n", "L 296.631558 48.6561 \n", "L 296.958128 60.38738 \n", "L 297.284699 49.291719 \n", "L 297.611269 55.22238 \n", "L 297.93784 55.484042 \n", "L 298.26441 59.03237 \n", "L 298.59098 64.898556 \n", "L 298.917551 66.671875 \n", "L 299.244121 62.981073 \n", "L 299.570692 67.534094 \n", "L 299.897262 62.371671 \n", "L 300.223833 69.980563 \n", "L 300.550403 53.34036 \n", "L 301.203544 58.267612 \n", "L 301.530114 46.936112 \n", "L 301.856685 53.918876 \n", "L 302.183255 44.68971 \n", "L 302.509826 56.403515 \n", "L 302.836396 57.643267 \n", "L 303.489537 51.742673 \n", "L 303.816107 50.682795 \n", "L 304.142678 42.598324 \n", "L 304.469248 41.203624 \n", "L 304.795818 44.413084 \n", "L 305.122389 49.495069 \n", "L 305.448959 37.601 \n", "L 305.77553 50.289352 \n", "L 306.1021 36.301116 \n", "L 306.428671 44.535581 \n", "L 306.755241 34.940515 \n", "L 307.081811 44.007186 \n", "L 307.408382 41.339532 \n", "L 307.734952 46.071564 \n", "L 308.061523 57.891 \n", "L 308.388093 60.751416 \n", "L 308.714664 62.026915 \n", "L 309.041234 55.305245 \n", "L 309.367804 61.087984 \n", "L 309.694375 44.683694 \n", "L 310.020945 39.491276 \n", "L 310.347516 46.000583 \n", "L 310.674086 48.037603 \n", "L 311.327227 58.639839 \n", "L 311.653797 46.825212 \n", "L 311.980368 54.580499 \n", "L 312.306938 41.458456 \n", "L 312.633509 50.992177 \n", "L 312.960079 51.268083 \n", "L 313.28665 43.37859 \n", "L 313.61322 52.350976 \n", "L 313.93979 52.215085 \n", "L 314.592931 46.088761 \n", "L 314.919502 50.094903 \n", "L 315.572643 45.971622 \n", "L 315.899213 52.718355 \n", "L 316.225783 46.206934 \n", "L 316.552354 43.531829 \n", "L 316.878924 45.089034 \n", "L 317.205495 32.164845 \n", "L 317.858636 42.316642 \n", "L 318.185206 42.54526 \n", "L 318.511776 48.131172 \n", "L 318.838347 48.902668 \n", "L 319.164917 56.271355 \n", "L 319.491488 38.007679 \n", "L 319.818058 57.452949 \n", "L 320.144628 41.036895 \n", "L 320.471199 35.503934 \n", "L 320.797769 42.524733 \n", "L 321.12434 29.243549 \n", "L 321.45091 45.29396 \n", "L 321.777481 34.191348 \n", "L 322.104051 45.985356 \n", "L 322.430621 45.730768 \n", "L 322.757192 52.937936 \n", "L 323.083762 41.291829 \n", "L 323.410333 48.266858 \n", "L 323.736903 42.699773 \n", "L 324.063474 47.584214 \n", "L 324.390044 57.688795 \n", "L 324.716614 53.139504 \n", "L 325.043185 39.494955 \n", "L 325.369755 46.137683 \n", "L 325.696326 30.039662 \n", "L 326.022896 33.207863 \n", "L 326.349467 34.289481 \n", "L 327.002607 38.206205 \n", "L 327.329178 37.89461 \n", "L 327.655748 36.390595 \n", "L 327.982319 36.243927 \n", "L 328.308889 41.109726 \n", "L 328.63546 32.739069 \n", "L 328.96203 36.920305 \n", "L 329.2886 24.60061 \n", "L 329.615171 38.322727 \n", "L 329.941741 25.282606 \n", "L 330.594882 38.591117 \n", "L 330.921453 30.662873 \n", "L 331.248023 43.964732 \n", "L 331.574593 34.305691 \n", "L 331.901164 30.336657 \n", "L 332.227734 48.214979 \n", "L 332.554305 47.282098 \n", "L 332.880875 44.521176 \n", "L 333.207445 47.356703 \n", "L 333.534016 40.024905 \n", "L 333.860586 46.946543 \n", "L 334.187157 41.255695 \n", "L 334.513727 44.336856 \n", "L 334.840298 54.681842 \n", "L 335.166868 34.533418 \n", "L 335.493438 40.343248 \n", "L 335.820009 38.409034 \n", "L 336.47315 49.197163 \n", "L 336.79972 43.466103 \n", "L 337.779431 55.015475 \n", "L 338.106002 48.982494 \n", "L 338.432572 48.230785 \n", "L 338.759143 52.012447 \n", "L 339.085713 62.436701 \n", "L 339.412284 50.901738 \n", "L 339.738854 53.058096 \n", "L 340.065424 48.995656 \n", "L 340.391995 47.886255 \n", "L 340.718565 56.279566 \n", "L 341.045136 49.052605 \n", "L 341.371706 55.834509 \n", "L 341.698277 47.844732 \n", "L 342.024847 58.147483 \n", "L 342.351417 55.558873 \n", "L 342.677988 55.640422 \n", "L 343.004558 48.837772 \n", "L 343.657699 62.597928 \n", "L 343.98427 55.976254 \n", "L 344.31084 55.13608 \n", "L 344.63741 53.706451 \n", "L 344.963981 57.716605 \n", "L 345.290551 65.729214 \n", "L 345.617122 50.564101 \n", "L 345.943692 68.406679 \n", "L 346.596833 53.526424 \n", "L 346.923403 58.408004 \n", "L 347.249974 47.756366 \n", "L 347.576544 51.157522 \n", "L 347.903115 49.519439 \n", "L 348.229685 56.019668 \n", "L 348.556255 59.42018 \n", "L 348.882826 60.181998 \n", "L 349.535967 70.249463 \n", "L 349.862537 63.576799 \n", "L 350.189108 70.108917 \n", "L 350.515678 67.105282 \n", "L 350.842248 68.031434 \n", "L 351.168819 70.380339 \n", "L 351.495389 74.242454 \n", "L 351.82196 69.522693 \n", "L 352.14853 72.32345 \n", "L 352.475101 68.153112 \n", "L 352.801671 61.696636 \n", "L 353.128241 58.219919 \n", "L 353.454812 70.960263 \n", "L 353.781382 70.752518 \n", "L 354.107953 63.364998 \n", "L 354.434523 61.576459 \n", "L 354.761094 66.056721 \n", "L 355.087664 59.408933 \n", "L 355.414234 66.177385 \n", "L 355.740805 64.454362 \n", "L 356.067375 66.984135 \n", "L 356.393946 60.527792 \n", "L 356.720516 71.193152 \n", "L 357.047087 71.288243 \n", "L 357.373657 62.193569 \n", "L 357.700227 72.490417 \n", "L 358.026798 73.353984 \n", "L 358.353368 79.179048 \n", "L 358.679939 88.057866 \n", "L 359.006509 87.858199 \n", "L 359.33308 78.248757 \n", "L 359.65965 81.291022 \n", "L 359.98622 75.319947 \n", "L 360.312791 73.218089 \n", "L 360.965932 85.380732 \n", "L 361.292502 77.953459 \n", "L 361.619072 80.240262 \n", "L 361.945643 79.032284 \n", "L 362.598784 78.940031 \n", "L 362.925354 82.236199 \n", "L 363.251925 94.948299 \n", "L 363.578495 84.589572 \n", "L 363.905065 87.853144 \n", "L 364.231636 84.300675 \n", "L 364.558206 78.628758 \n", "L 364.884777 86.080811 \n", "L 365.211347 80.118223 \n", "L 365.537918 78.464631 \n", "L 365.864488 94.029994 \n", "L 366.191058 89.416103 \n", "L 366.517629 91.17562 \n", "L 366.844199 91.78977 \n", "L 367.49734 98.902251 \n", "L 368.150481 95.421939 \n", "L 368.477051 99.765312 \n", "L 368.803622 108.833108 \n", "L 369.130192 101.742578 \n", "L 369.456763 81.722659 \n", "L 369.783333 90.326602 \n", "L 370.109904 80.863136 \n", "L 370.436474 85.811626 \n", "L 370.763044 88.416034 \n", "L 371.416185 81.884546 \n", "L 371.742756 96.24032 \n", "L 371.742756 96.24032 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 67.379119 102.838341 \n", "L 67.70569 102.921594 \n", "L 68.03226 98.337604 \n", "L 68.358831 91.366871 \n", "L 68.685401 93.569994 \n", "L 69.011971 93.163249 \n", "L 69.338542 97.831527 \n", "L 69.665112 98.473277 \n", "L 69.991683 100.414096 \n", "L 70.318253 97.69753 \n", "L 70.644824 92.90142 \n", "L 70.971394 90.29618 \n", "L 71.297964 95.895484 \n", "L 71.624535 94.84814 \n", "L 71.951105 95.717407 \n", "L 72.277676 95.557646 \n", "L 72.604246 89.495088 \n", "L 72.930817 96.465486 \n", "L 73.257387 97.902848 \n", "L 73.583957 94.753919 \n", "L 73.910528 98.424527 \n", "L 74.237098 94.563573 \n", "L 74.890239 103.252455 \n", "L 75.21681 102.464702 \n", "L 75.54338 102.712032 \n", "L 75.86995 96.924015 \n", "L 76.196521 88.842641 \n", "L 76.849662 85.007434 \n", "L 77.176232 90.893187 \n", "L 77.502803 87.3365 \n", "L 77.829373 91.877839 \n", "L 78.155943 88.692114 \n", "L 78.482514 87.246769 \n", "L 78.809084 89.895821 \n", "L 79.135655 90.100675 \n", "L 79.462225 84.644273 \n", "L 79.788795 82.701497 \n", "L 80.115366 84.562837 \n", "L 80.441936 79.996827 \n", "L 80.768507 82.798541 \n", "L 81.421648 70.862293 \n", "L 81.748218 75.138287 \n", "L 82.074788 73.381804 \n", "L 82.401359 78.823938 \n", "L 82.727929 75.745744 \n", "L 83.0545 80.469101 \n", "L 83.38107 78.720647 \n", "L 83.707641 75.936622 \n", "L 84.034211 78.062655 \n", "L 84.360781 69.937609 \n", "L 84.687352 75.069415 \n", "L 85.013922 66.377748 \n", "L 85.340493 65.699376 \n", "L 85.667063 67.258589 \n", "L 85.993634 70.047624 \n", "L 86.320204 76.959638 \n", "L 86.646774 77.24398 \n", "L 86.973345 74.295727 \n", "L 87.299915 68.045394 \n", "L 87.626486 82.770273 \n", "L 87.953056 85.063004 \n", "L 88.279627 85.632435 \n", "L 89.259338 66.043988 \n", "L 89.585908 68.706952 \n", "L 89.912479 65.759199 \n", "L 90.239049 65.778873 \n", "L 90.56562 68.969584 \n", "L 90.89219 68.312685 \n", "L 91.21876 68.953295 \n", "L 91.545331 69.137207 \n", "L 91.871901 64.950935 \n", "L 92.198472 66.067845 \n", "L 92.525042 61.563777 \n", "L 92.851612 63.860115 \n", "L 93.178183 54.754861 \n", "L 93.504753 60.077504 \n", "L 93.831324 60.491165 \n", "L 94.157894 61.24596 \n", "L 94.484465 63.581188 \n", "L 94.811035 61.244025 \n", "L 95.137605 61.944013 \n", "L 95.464176 61.420353 \n", "L 95.790746 62.764421 \n", "L 96.117317 58.436164 \n", "L 96.443887 58.257417 \n", "L 96.770458 60.245449 \n", "L 97.097028 63.089825 \n", "L 97.423598 62.18657 \n", "L 98.076739 67.220772 \n", "L 98.40331 67.716746 \n", "L 99.709591 59.408324 \n", "L 100.362732 50.570822 \n", "L 100.689303 50.484333 \n", "L 101.015873 50.652943 \n", "L 101.342444 51.332079 \n", "L 101.669014 55.250992 \n", "L 102.648725 60.251043 \n", "L 102.975296 58.81729 \n", "L 103.301866 62.12044 \n", "L 103.628437 59.902598 \n", "L 103.955007 62.308532 \n", "L 104.281577 54.692307 \n", "L 104.608148 54.006632 \n", "L 104.934718 44.311242 \n", "L 105.261289 46.530985 \n", "L 105.587859 45.308875 \n", "L 105.91443 41.788156 \n", "L 106.894141 60.370484 \n", "L 107.220711 62.110396 \n", "L 107.547282 61.702254 \n", "L 107.873852 57.279769 \n", "L 108.200422 54.91852 \n", "L 108.526993 56.028216 \n", "L 108.853563 52.644949 \n", "L 109.180134 55.914753 \n", "L 109.506704 57.246478 \n", "L 109.833275 60.575975 \n", "L 110.159845 61.060842 \n", "L 110.486415 60.862535 \n", "L 111.139556 46.203995 \n", "L 111.466127 45.29693 \n", "L 111.792697 41.722108 \n", "L 112.119268 47.225677 \n", "L 112.445838 50.715209 \n", "L 112.772408 51.207852 \n", "L 113.098979 48.591238 \n", "L 113.425549 51.128718 \n", "L 113.75212 52.680857 \n", "L 114.07869 47.923013 \n", "L 114.405261 37.418793 \n", "L 114.731831 44.541726 \n", "L 115.058401 35.964698 \n", "L 115.384972 45.834906 \n", "L 115.711542 50.066044 \n", "L 116.038113 58.300122 \n", "L 116.364683 55.141664 \n", "L 116.691254 56.240476 \n", "L 117.017824 51.227557 \n", "L 117.344394 49.335424 \n", "L 117.670965 54.334191 \n", "L 117.997535 56.065772 \n", "L 118.324106 56.745901 \n", "L 118.650676 53.807667 \n", "L 118.977247 48.898188 \n", "L 119.303817 49.792379 \n", "L 119.630387 52.299741 \n", "L 119.956958 53.7389 \n", "L 120.283528 51.273557 \n", "L 120.936669 43.607595 \n", "L 121.263239 43.394694 \n", "L 121.58981 45.281713 \n", "L 121.91638 46.258374 \n", "L 122.242951 57.113369 \n", "L 122.569521 58.903087 \n", "L 122.896092 61.730163 \n", "L 123.222662 62.585418 \n", "L 123.549232 55.390055 \n", "L 123.875803 56.19538 \n", "L 124.202373 57.623772 \n", "L 124.528944 54.842435 \n", "L 124.855514 53.251867 \n", "L 125.182085 50.533352 \n", "L 125.508655 50.808717 \n", "L 125.835225 54.331803 \n", "L 126.161796 54.334595 \n", "L 126.488366 55.813705 \n", "L 126.814937 55.022336 \n", "L 127.141507 51.037701 \n", "L 127.468078 51.018644 \n", "L 127.794648 44.860906 \n", "L 128.121218 46.643095 \n", "L 128.447789 44.379574 \n", "L 128.774359 44.885869 \n", "L 129.10093 47.363239 \n", "L 129.4275 42.606156 \n", "L 129.754071 47.46515 \n", "L 130.080641 47.69371 \n", "L 130.407211 49.365371 \n", "L 130.733782 54.846053 \n", "L 131.060352 53.610881 \n", "L 131.386923 46.83553 \n", "L 131.713493 49.780478 \n", "L 132.040064 47.6026 \n", "L 132.366634 54.205868 \n", "L 132.693204 53.198803 \n", "L 133.346345 54.852158 \n", "L 133.672916 59.096345 \n", "L 134.326057 62.317815 \n", "L 134.652627 56.301909 \n", "L 134.979197 57.789038 \n", "L 135.305768 62.193901 \n", "L 135.632338 63.96956 \n", "L 135.958909 67.31527 \n", "L 136.285479 56.185041 \n", "L 136.612049 52.579408 \n", "L 136.93862 54.675083 \n", "L 137.26519 61.224714 \n", "L 137.591761 65.260345 \n", "L 137.918331 66.909328 \n", "L 138.244902 59.149152 \n", "L 138.571472 61.647474 \n", "L 138.898042 57.634449 \n", "L 139.224613 62.368667 \n", "L 139.551183 63.726074 \n", "L 139.877754 64.158652 \n", "L 140.204324 69.855478 \n", "L 140.530895 62.21329 \n", "L 140.857465 63.648537 \n", "L 141.184035 59.92542 \n", "L 141.510606 61.050548 \n", "L 141.837176 61.198014 \n", "L 142.163747 64.045741 \n", "L 142.816888 72.09008 \n", "L 143.143458 64.817966 \n", "L 143.796599 56.596697 \n", "L 145.102881 77.13702 \n", "L 145.429451 74.35716 \n", "L 145.756021 67.66015 \n", "L 146.082592 66.21167 \n", "L 146.409162 61.625693 \n", "L 146.735733 62.86663 \n", "L 147.062303 65.696471 \n", "L 147.388874 64.887789 \n", "L 147.715444 71.470895 \n", "L 148.042014 68.792842 \n", "L 148.368585 70.737736 \n", "L 148.695155 68.063876 \n", "L 149.021726 71.568914 \n", "L 149.348296 71.59705 \n", "L 149.674866 78.48717 \n", "L 150.001437 73.683673 \n", "L 150.328007 75.066793 \n", "L 150.654578 79.839818 \n", "L 150.981148 76.45082 \n", "L 151.307719 83.919595 \n", "L 151.634289 79.108144 \n", "L 151.960859 77.604791 \n", "L 152.28743 78.778686 \n", "L 152.614 75.113281 \n", "L 152.940571 78.595842 \n", "L 153.267141 76.692572 \n", "L 153.593712 80.319406 \n", "L 154.246852 76.55884 \n", "L 154.573423 80.803311 \n", "L 154.899993 80.535837 \n", "L 155.226564 89.835759 \n", "L 155.553134 81.809865 \n", "L 155.879705 82.142655 \n", "L 156.206275 77.404804 \n", "L 156.532845 80.570391 \n", "L 156.859416 82.029336 \n", "L 157.185986 77.933006 \n", "L 157.512557 75.751581 \n", "L 157.839127 76.423144 \n", "L 158.165698 81.643181 \n", "L 158.492268 82.421192 \n", "L 158.818838 81.754681 \n", "L 159.145409 81.664542 \n", "L 159.471979 86.06974 \n", "L 159.79855 81.118791 \n", "L 160.12512 90.394041 \n", "L 160.451691 90.742274 \n", "L 160.778261 84.275534 \n", "L 161.104831 80.175148 \n", "L 161.431402 79.627436 \n", "L 161.757972 80.264335 \n", "L 162.084543 80.099223 \n", "L 162.411113 84.333208 \n", "L 162.737684 83.075135 \n", "L 163.064254 86.001513 \n", "L 163.390824 87.916465 \n", "L 164.043965 97.662372 \n", "L 164.370536 100.131913 \n", "L 164.697106 104.764635 \n", "L 165.023676 103.975138 \n", "L 165.676817 97.1597 \n", "L 166.003388 91.87603 \n", "L 166.329958 97.685694 \n", "L 166.656529 98.375378 \n", "L 166.983099 95.054262 \n", "L 167.309669 97.417723 \n", "L 167.63624 97.208842 \n", "L 168.289381 102.991924 \n", "L 168.615951 96.331031 \n", "L 168.942522 102.046792 \n", "L 169.269092 104.073241 \n", "L 169.595662 104.811595 \n", "L 169.922233 107.955711 \n", "L 170.248803 101.776243 \n", "L 170.901944 95.114667 \n", "L 171.228515 94.577219 \n", "L 171.555085 100.304293 \n", "L 171.881655 99.04775 \n", "L 172.208226 100.919975 \n", "L 172.534796 109.359321 \n", "L 172.861367 108.829455 \n", "L 173.187937 110.850868 \n", "L 173.514508 104.746103 \n", "L 173.841078 107.80663 \n", "L 174.167648 106.030858 \n", "L 174.820789 112.576884 \n", "L 175.14736 107.611445 \n", "L 175.800501 116.234399 \n", "L 176.453641 115.114127 \n", "L 176.780212 116.94521 \n", "L 177.106782 116.102381 \n", "L 177.433353 112.837143 \n", "L 177.759923 119.299769 \n", "L 178.086493 120.228279 \n", "L 178.413064 119.579233 \n", "L 178.739634 117.975035 \n", "L 179.066205 114.626437 \n", "L 179.392775 115.676643 \n", "L 179.719346 108.386819 \n", "L 180.045916 104.02276 \n", "L 180.372486 106.358489 \n", "L 180.699057 107.663871 \n", "L 181.025627 120.83705 \n", "L 181.352198 122.172801 \n", "L 181.678768 122.552004 \n", "L 182.005339 120.326103 \n", "L 182.331909 113.208053 \n", "L 182.658479 121.723574 \n", "L 182.98505 125.472967 \n", "L 183.638191 127.492685 \n", "L 183.964761 130.405517 \n", "L 184.291332 128.620341 \n", "L 184.617902 138.79384 \n", "L 184.944472 142.208424 \n", "L 185.597613 134.505584 \n", "L 185.924184 122.619007 \n", "L 186.250754 122.129723 \n", "L 186.577325 120.614708 \n", "L 186.903895 121.938277 \n", "L 187.230465 125.009097 \n", "L 187.557036 119.85875 \n", "L 187.883606 118.223988 \n", "L 189.189888 131.346773 \n", "L 189.516458 126.483657 \n", "L 189.843029 125.278265 \n", "L 190.169599 124.673452 \n", "L 190.49617 131.85696 \n", "L 190.82274 133.628676 \n", "L 191.149311 138.294009 \n", "L 191.475881 137.956509 \n", "L 191.802451 136.074423 \n", "L 192.129022 135.839437 \n", "L 192.455592 137.334589 \n", "L 192.782163 137.168963 \n", "L 193.108733 136.166506 \n", "L 193.435303 129.043395 \n", "L 193.761874 126.635846 \n", "L 194.088444 129.023653 \n", "L 194.415015 132.641049 \n", "L 194.741585 131.596877 \n", "L 195.068156 140.484726 \n", "L 195.721296 135.383877 \n", "L 196.047867 135.61529 \n", "L 196.374437 132.518642 \n", "L 196.701008 135.26 \n", "L 197.027578 136.126648 \n", "L 197.354149 135.917625 \n", "L 197.680719 135.022194 \n", "L 198.007289 141.401767 \n", "L 198.33386 140.183819 \n", "L 198.66043 141.886028 \n", "L 198.987001 146.593757 \n", "L 199.313571 142.275294 \n", "L 199.640142 146.926448 \n", "L 199.966712 144.717814 \n", "L 200.293282 143.95059 \n", "L 200.619853 145.213849 \n", "L 200.946423 151.546514 \n", "L 201.272994 146.961675 \n", "L 201.599564 147.912157 \n", "L 201.926135 151.314929 \n", "L 202.252705 150.417645 \n", "L 202.579275 153.145314 \n", "L 202.905846 145.85323 \n", "L 203.232416 143.088104 \n", "L 203.558987 138.828378 \n", "L 204.212128 150.395111 \n", "L 204.538698 150.078031 \n", "L 204.865268 148.672995 \n", "L 205.191839 142.426148 \n", "L 205.518409 138.441657 \n", "L 205.84498 141.917544 \n", "L 206.17155 146.648617 \n", "L 206.49812 147.169954 \n", "L 206.824691 150.184704 \n", "L 207.151261 141.873935 \n", "L 207.477832 140.328525 \n", "L 207.804402 143.217729 \n", "L 208.130973 143.164597 \n", "L 208.457543 146.024265 \n", "L 208.784113 153.777185 \n", "L 209.110684 147.655383 \n", "L 209.437254 155.940541 \n", "L 209.763825 151.894326 \n", "L 210.090395 152.64214 \n", "L 210.416966 154.136363 \n", "L 210.743536 144.478419 \n", "L 211.070106 147.942642 \n", "L 211.396677 142.83603 \n", "L 212.376388 147.626957 \n", "L 212.702959 151.54685 \n", "L 213.029529 147.081146 \n", "L 213.356099 148.553784 \n", "L 213.68267 147.721047 \n", "L 214.00924 149.299214 \n", "L 214.662381 152.95133 \n", "L 214.988952 154.800984 \n", "L 215.315522 153.885944 \n", "L 215.642092 154.562206 \n", "L 215.968663 151.924784 \n", "L 216.295233 145.608902 \n", "L 216.948374 143.338927 \n", "L 217.601515 155.662093 \n", "L 217.928085 155.381168 \n", "L 218.254656 155.438158 \n", "L 218.581226 155.246524 \n", "L 218.907797 154.172847 \n", "L 219.234367 156.381053 \n", "L 219.560938 156.963004 \n", "L 219.887508 151.612972 \n", "L 220.214078 151.701208 \n", "L 220.540649 153.065221 \n", "L 220.867219 150.679526 \n", "L 221.52036 158.321974 \n", "L 221.84693 156.434055 \n", "L 222.173501 160.192361 \n", "L 222.500071 153.14219 \n", "L 222.826642 148.614649 \n", "L 223.153212 148.332998 \n", "L 223.479783 148.356556 \n", "L 223.806353 149.265519 \n", "L 224.459494 159.050252 \n", "L 224.786064 158.323591 \n", "L 225.112635 151.514466 \n", "L 225.439205 149.581283 \n", "L 225.765776 143.937247 \n", "L 226.092346 148.473256 \n", "L 226.418916 149.555683 \n", "L 226.745487 153.699161 \n", "L 227.072057 151.914404 \n", "L 227.398628 149.270332 \n", "L 227.725198 148.20018 \n", "L 228.051769 150.594665 \n", "L 228.378339 151.202347 \n", "L 228.704909 158.292013 \n", "L 229.03148 158.703988 \n", "L 229.35805 158.062477 \n", "L 229.684621 156.714641 \n", "L 230.011191 157.977883 \n", "L 230.337762 152.514217 \n", "L 230.664332 151.303231 \n", "L 230.990902 147.674779 \n", "L 231.317473 141.780198 \n", "L 231.644043 143.331815 \n", "L 231.970614 136.595343 \n", "L 232.297184 145.361771 \n", "L 232.623755 144.201424 \n", "L 232.950325 147.74403 \n", "L 233.276895 152.783471 \n", "L 233.603466 153.368656 \n", "L 233.930036 154.207012 \n", "L 234.909747 140.986883 \n", "L 235.562888 149.417517 \n", "L 235.889459 144.429911 \n", "L 236.5426 142.169851 \n", "L 236.86917 136.898353 \n", "L 237.19574 144.076707 \n", "L 237.522311 145.82133 \n", "L 237.848881 145.189945 \n", "L 238.175452 143.157749 \n", "L 238.502022 144.592523 \n", "L 238.828593 147.651759 \n", "L 239.155163 145.825372 \n", "L 239.481733 145.113004 \n", "L 239.808304 143.968832 \n", "L 240.134874 143.663465 \n", "L 240.461445 141.455112 \n", "L 240.788015 144.283942 \n", "L 241.441156 139.412878 \n", "L 241.767726 140.990634 \n", "L 242.094297 140.59229 \n", "L 242.420867 140.428305 \n", "L 242.747438 146.035354 \n", "L 243.074008 137.494179 \n", "L 243.400579 134.033224 \n", "L 244.053719 121.510192 \n", "L 245.033431 135.645248 \n", "L 245.360001 134.46475 \n", "L 245.686572 136.091346 \n", "L 246.013142 136.526335 \n", "L 246.339712 133.827912 \n", "L 246.666283 136.833008 \n", "L 246.992853 135.777035 \n", "L 247.319424 131.624238 \n", "L 247.645994 130.633641 \n", "L 247.972564 124.318463 \n", "L 248.299135 130.883865 \n", "L 248.625705 130.212074 \n", "L 248.952276 138.961912 \n", "L 249.278846 136.065342 \n", "L 249.605417 127.173745 \n", "L 249.931987 127.424681 \n", "L 250.258557 117.83366 \n", "L 250.585128 122.237026 \n", "L 250.911698 121.921207 \n", "L 251.238269 126.908366 \n", "L 251.564839 126.72048 \n", "L 251.89141 129.831324 \n", "L 252.54455 131.20115 \n", "L 252.871121 130.308521 \n", "L 253.197691 132.743549 \n", "L 253.524262 132.848095 \n", "L 254.503973 142.92352 \n", "L 254.830543 133.666338 \n", "L 255.157114 132.476362 \n", "L 255.483684 126.321723 \n", "L 255.810255 129.626648 \n", "L 256.136825 134.410781 \n", "L 256.463396 134.927501 \n", "L 256.789966 136.713783 \n", "L 257.116536 125.518984 \n", "L 257.443107 130.624882 \n", "L 258.096248 122.387281 \n", "L 258.422818 125.076657 \n", "L 258.749389 118.159359 \n", "L 259.075959 124.058541 \n", "L 259.402529 122.685459 \n", "L 259.7291 122.516378 \n", "L 260.05567 120.273414 \n", "L 260.382241 114.425173 \n", "L 260.708811 121.872944 \n", "L 261.035382 126.123751 \n", "L 261.361952 124.756016 \n", "L 261.688522 125.887785 \n", "L 262.015093 120.052581 \n", "L 262.341663 117.503422 \n", "L 262.668234 111.837125 \n", "L 262.994804 115.523872 \n", "L 263.321374 109.244775 \n", "L 263.647945 112.104876 \n", "L 263.974515 106.530497 \n", "L 264.301086 111.594286 \n", "L 264.627656 105.941322 \n", "L 264.954227 104.915458 \n", "L 265.280797 104.910905 \n", "L 265.607367 101.68868 \n", "L 265.933938 104.113373 \n", "L 266.260508 102.629474 \n", "L 266.587079 110.242434 \n", "L 266.913649 105.894055 \n", "L 267.56679 102.629358 \n", "L 267.89336 102.408765 \n", "L 268.219931 99.292738 \n", "L 268.546501 105.6718 \n", "L 268.873072 106.682696 \n", "L 269.199642 109.564641 \n", "L 269.526213 105.682357 \n", "L 269.852783 107.251165 \n", "L 270.179353 103.826244 \n", "L 270.505924 102.115287 \n", "L 270.832494 97.938723 \n", "L 271.812206 108.252389 \n", "L 272.138776 101.187262 \n", "L 272.465346 100.718385 \n", "L 272.791917 99.691868 \n", "L 273.118487 96.150914 \n", "L 273.445058 96.990558 \n", "L 273.771628 96.881631 \n", "L 274.098199 95.264831 \n", "L 274.424769 101.345652 \n", "L 274.751339 100.120743 \n", "L 275.07791 93.911202 \n", "L 275.40448 95.690401 \n", "L 275.731051 91.148583 \n", "L 276.057621 90.335202 \n", "L 276.384191 94.376801 \n", "L 276.710762 92.706831 \n", "L 277.037332 92.127372 \n", "L 277.363903 88.890119 \n", "L 277.690473 90.598778 \n", "L 278.017044 86.136728 \n", "L 278.343614 87.095808 \n", "L 278.670184 88.530383 \n", "L 278.996755 93.461736 \n", "L 279.649896 89.598475 \n", "L 279.976466 81.018031 \n", "L 280.629607 86.298397 \n", "L 280.956177 87.489262 \n", "L 281.282748 85.409147 \n", "L 281.935889 78.154718 \n", "L 282.58903 91.147245 \n", "L 282.9156 93.300846 \n", "L 283.24217 94.063908 \n", "L 283.568741 90.992652 \n", "L 283.895311 85.325355 \n", "L 284.221882 83.613325 \n", "L 284.875023 78.556662 \n", "L 285.201593 79.638257 \n", "L 285.528163 79.595463 \n", "L 285.854734 77.556497 \n", "L 286.507875 79.707733 \n", "L 286.834445 82.916909 \n", "L 287.161016 78.754716 \n", "L 287.487586 77.697157 \n", "L 287.814156 71.359374 \n", "L 288.140727 72.94424 \n", "L 288.467297 71.938519 \n", "L 288.793868 73.993331 \n", "L 289.120438 72.212402 \n", "L 289.447009 66.162483 \n", "L 289.773579 66.26718 \n", "L 290.42672 63.983687 \n", "L 290.75329 70.366281 \n", "L 291.079861 66.565456 \n", "L 291.406431 71.564166 \n", "L 291.733001 72.925026 \n", "L 292.059572 70.757317 \n", "L 292.386142 71.828663 \n", "L 292.712713 67.908448 \n", "L 293.039283 65.846013 \n", "L 293.365854 72.166193 \n", "L 293.692424 66.974344 \n", "L 294.018994 67.506599 \n", "L 294.345565 65.640666 \n", "L 294.672135 68.616274 \n", "L 294.998706 75.591736 \n", "L 295.325276 77.528992 \n", "L 295.651847 74.871615 \n", "L 295.978417 67.783571 \n", "L 296.304987 64.958746 \n", "L 296.631558 57.950686 \n", "L 296.958128 62.74758 \n", "L 297.284699 58.049032 \n", "L 297.611269 60.653488 \n", "L 297.93784 60.616385 \n", "L 298.26441 63.325699 \n", "L 298.59098 68.277869 \n", "L 298.917551 71.410636 \n", "L 299.244121 70.468057 \n", "L 299.570692 71.556565 \n", "L 299.897262 68.525445 \n", "L 300.223833 72.467038 \n", "L 300.550403 64.679904 \n", "L 301.203544 61.344751 \n", "L 301.530114 56.269289 \n", "L 301.856685 59.182001 \n", "L 302.183255 53.684239 \n", "L 302.509826 58.714982 \n", "L 302.836396 61.598058 \n", "L 303.162966 62.772516 \n", "L 303.489537 60.530286 \n", "L 303.816107 57.088957 \n", "L 304.142678 51.255154 \n", "L 304.469248 48.92164 \n", "L 304.795818 49.607233 \n", "L 305.122389 53.520675 \n", "L 305.448959 49.481171 \n", "L 305.77553 54.205631 \n", "L 306.1021 46.622259 \n", "L 306.428671 49.703119 \n", "L 306.755241 44.760629 \n", "L 307.081811 48.579457 \n", "L 307.408382 48.30972 \n", "L 307.734952 51.91661 \n", "L 308.388093 64.963744 \n", "L 308.714664 68.57483 \n", "L 309.041234 64.698741 \n", "L 309.367804 64.78004 \n", "L 309.694375 55.430828 \n", "L 310.020945 49.769872 \n", "L 310.347516 49.534132 \n", "L 310.674086 51.738967 \n", "L 311.327227 63.748122 \n", "L 311.653797 58.202555 \n", "L 311.980368 59.080285 \n", "L 312.306938 50.961962 \n", "L 312.633509 54.694404 \n", "L 312.960079 56.215468 \n", "L 313.28665 53.746306 \n", "L 313.61322 56.89955 \n", "L 313.93979 56.83562 \n", "L 314.266361 56.921064 \n", "L 314.592931 54.995427 \n", "L 314.919502 55.062044 \n", "L 315.246072 53.917365 \n", "L 315.572643 53.73074 \n", "L 315.899213 57.061798 \n", "L 316.552354 52.415804 \n", "L 316.878924 51.220356 \n", "L 317.205495 43.489371 \n", "L 317.532065 43.608893 \n", "L 318.185206 49.03548 \n", "L 318.511776 54.236312 \n", "L 318.838347 55.5522 \n", "L 319.164917 59.874651 \n", "L 319.491488 51.396523 \n", "L 319.818058 58.477683 \n", "L 320.144628 50.770383 \n", "L 320.471199 47.018733 \n", "L 320.797769 47.182204 \n", "L 321.12434 39.646207 \n", "L 321.45091 47.691497 \n", "L 321.777481 44.272463 \n", "L 322.104051 50.703732 \n", "L 322.430621 51.668041 \n", "L 322.757192 57.073364 \n", "L 323.083762 52.749563 \n", "L 323.410333 54.033106 \n", "L 323.736903 49.902114 \n", "L 324.063474 52.49913 \n", "L 324.390044 59.726058 \n", "L 324.716614 60.915103 \n", "L 325.043185 53.592464 \n", "L 325.369755 51.33942 \n", "L 325.696326 40.428247 \n", "L 326.022896 40.453371 \n", "L 326.349467 40.741353 \n", "L 327.002607 45.387094 \n", "L 327.329178 45.833724 \n", "L 327.982319 43.79742 \n", "L 328.308889 46.207537 \n", "L 328.63546 43.107864 \n", "L 328.96203 44.294671 \n", "L 329.2886 36.505788 \n", "L 329.615171 41.352276 \n", "L 329.941741 36.174619 \n", "L 330.594882 42.895583 \n", "L 330.921453 40.767717 \n", "L 331.248023 48.016781 \n", "L 331.901164 41.201261 \n", "L 332.227734 48.382623 \n", "L 332.880875 54.537493 \n", "L 333.207445 55.018928 \n", "L 333.534016 48.79158 \n", "L 333.860586 50.818624 \n", "L 334.187157 49.210471 \n", "L 334.513727 51.36018 \n", "L 334.840298 57.050302 \n", "L 335.166868 48.476573 \n", "L 335.493438 48.206997 \n", "L 335.820009 44.221331 \n", "L 336.146579 47.854643 \n", "L 336.47315 54.047487 \n", "L 336.79972 53.509216 \n", "L 337.126291 53.935192 \n", "L 337.452861 55.229115 \n", "L 337.779431 59.057415 \n", "L 338.106002 58.466495 \n", "L 338.432572 56.629029 \n", "L 338.759143 56.153898 \n", "L 339.085713 62.817708 \n", "L 339.412284 61.203598 \n", "L 339.738854 61.462725 \n", "L 340.065424 56.02245 \n", "L 340.391995 53.660293 \n", "L 340.718565 58.790097 \n", "L 341.045136 57.78491 \n", "L 341.371706 61.322285 \n", "L 341.698277 56.481388 \n", "L 342.024847 60.647778 \n", "L 342.351417 61.092222 \n", "L 342.677988 62.684472 \n", "L 343.004558 58.411841 \n", "L 343.331129 58.985068 \n", "L 343.657699 63.658883 \n", "L 343.98427 64.133884 \n", "L 344.31084 63.860122 \n", "L 344.63741 60.164291 \n", "L 344.963981 60.811759 \n", "L 345.290551 67.277132 \n", "L 345.617122 62.736402 \n", "L 345.943692 69.827632 \n", "L 346.596833 62.957956 \n", "L 346.923403 63.324767 \n", "L 347.249974 56.447508 \n", "L 347.576544 56.789568 \n", "L 347.903115 55.633824 \n", "L 348.556255 63.745872 \n", "L 349.209396 69.198048 \n", "L 349.535967 72.844503 \n", "L 349.862537 71.125404 \n", "L 350.189108 73.967457 \n", "L 350.515678 72.222936 \n", "L 350.842248 72.847987 \n", "L 351.168819 74.348592 \n", "L 351.495389 77.195106 \n", "L 351.82196 76.084544 \n", "L 352.14853 76.90637 \n", "L 352.475101 73.908049 \n", "L 353.128241 65.238704 \n", "L 353.781382 73.923766 \n", "L 354.107953 73.139338 \n", "L 354.434523 69.524947 \n", "L 354.761094 68.199376 \n", "L 355.087664 65.473471 \n", "L 355.414234 70.068495 \n", "L 355.740805 70.144102 \n", "L 356.067375 71.644215 \n", "L 356.393946 68.207074 \n", "L 356.720516 72.605282 \n", "L 357.047087 74.509969 \n", "L 357.373657 71.730688 \n", "L 357.700227 75.089219 \n", "L 358.026798 75.526952 \n", "L 358.353368 81.278237 \n", "L 358.679939 89.098256 \n", "L 359.006509 91.793229 \n", "L 359.33308 86.910735 \n", "L 359.65965 84.751374 \n", "L 359.98622 79.66133 \n", "L 360.312791 78.257088 \n", "L 360.639361 81.701812 \n", "L 360.965932 86.345605 \n", "L 361.292502 85.122994 \n", "L 361.619072 85.147783 \n", "L 361.945643 82.534874 \n", "L 362.272213 82.275648 \n", "L 362.598784 83.048348 \n", "L 362.925354 85.207188 \n", "L 363.251925 93.119295 \n", "L 363.578495 91.30597 \n", "L 363.905065 92.549341 \n", "L 364.558206 83.853838 \n", "L 364.884777 86.857229 \n", "L 365.211347 85.109647 \n", "L 365.537918 84.340506 \n", "L 365.864488 91.47239 \n", "L 366.191058 92.383628 \n", "L 366.517629 95.700001 \n", "L 366.844199 95.326085 \n", "L 367.17077 96.487543 \n", "L 367.49734 99.550593 \n", "L 367.823911 100.837299 \n", "L 368.150481 99.809802 \n", "L 368.477051 100.511968 \n", "L 368.803622 106.137618 \n", "L 369.130192 106.161613 \n", "L 369.456763 94.832191 \n", "L 369.783333 91.374682 \n", "L 370.109904 83.509987 \n", "L 370.763044 91.200054 \n", "L 371.089615 90.553458 \n", "L 371.416185 87.523048 \n", "L 371.742756 93.393075 \n", "L 371.742756 93.393075 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 67.379119 109.980058 \n", "L 67.70569 109.971785 \n", "L 68.03226 106.669045 \n", "L 68.358831 101.618286 \n", "L 68.685401 103.218338 \n", "L 69.011971 103.189809 \n", "L 69.338542 106.423411 \n", "L 69.665112 106.82256 \n", "L 69.991683 108.188063 \n", "L 70.318253 106.298505 \n", "L 70.644824 102.755085 \n", "L 70.971394 100.844913 \n", "L 71.297964 105.031229 \n", "L 71.624535 104.454565 \n", "L 71.951105 104.763675 \n", "L 72.277676 104.625304 \n", "L 72.604246 100.455085 \n", "L 72.930817 105.347789 \n", "L 73.257387 106.573337 \n", "L 73.583957 104.185032 \n", "L 73.910528 106.615826 \n", "L 74.237098 104.113281 \n", "L 74.890239 110.302467 \n", "L 75.21681 109.734121 \n", "L 75.54338 109.76316 \n", "L 75.86995 105.653258 \n", "L 76.196521 99.822744 \n", "L 76.849662 97.270516 \n", "L 77.176232 101.484702 \n", "L 77.502803 98.952908 \n", "L 77.829373 101.99223 \n", "L 78.155943 99.904773 \n", "L 78.482514 98.797574 \n", "L 78.809084 100.641468 \n", "L 79.135655 100.933586 \n", "L 79.462225 96.956367 \n", "L 79.788795 95.365268 \n", "L 80.115366 96.892062 \n", "L 80.441936 93.800138 \n", "L 80.768507 95.521099 \n", "L 81.421648 87.029482 \n", "L 81.748218 90.106898 \n", "L 82.074788 89.095142 \n", "L 82.401359 92.811916 \n", "L 82.727929 90.609336 \n", "L 83.0545 93.923665 \n", "L 83.38107 92.846632 \n", "L 83.707641 90.704109 \n", "L 84.034211 92.133216 \n", "L 84.360781 86.52028 \n", "L 84.687352 90.032768 \n", "L 85.013922 83.983446 \n", "L 85.340493 83.278379 \n", "L 85.667063 84.536389 \n", "L 85.993634 86.714358 \n", "L 86.320204 91.570765 \n", "L 86.646774 91.7217 \n", "L 86.973345 89.493288 \n", "L 87.299915 84.990766 \n", "L 87.626486 95.623926 \n", "L 87.953056 97.718045 \n", "L 88.279627 97.644827 \n", "L 88.932767 86.228424 \n", "L 89.259338 83.699173 \n", "L 89.585908 85.616855 \n", "L 89.912479 83.524632 \n", "L 90.239049 83.402348 \n", "L 90.56562 85.784697 \n", "L 90.89219 85.447878 \n", "L 91.21876 85.724149 \n", "L 91.545331 85.824374 \n", "L 91.871901 82.928669 \n", "L 92.198472 83.654195 \n", "L 92.525042 80.491394 \n", "L 92.851612 82.068958 \n", "L 93.178183 75.653933 \n", "L 93.504753 79.278417 \n", "L 94.157894 80.363605 \n", "L 94.484465 81.85022 \n", "L 94.811035 80.258467 \n", "L 95.137605 80.768275 \n", "L 95.464176 80.410808 \n", "L 95.790746 81.340128 \n", "L 96.117317 78.260534 \n", "L 96.443887 78.034657 \n", "L 96.770458 79.57869 \n", "L 97.097028 81.712669 \n", "L 97.423598 80.966525 \n", "L 98.076739 84.610566 \n", "L 98.40331 84.990047 \n", "L 99.056451 81.785361 \n", "L 99.383021 81.062327 \n", "L 99.709591 78.992003 \n", "L 100.362732 72.514662 \n", "L 100.689303 72.570304 \n", "L 101.015873 72.808057 \n", "L 101.342444 73.204075 \n", "L 101.669014 75.965158 \n", "L 102.648725 79.537666 \n", "L 102.975296 78.529272 \n", "L 103.301866 80.889077 \n", "L 103.628437 79.382836 \n", "L 103.955007 80.973843 \n", "L 104.281577 75.567352 \n", "L 104.608148 74.949547 \n", "L 104.934718 68.157689 \n", "L 105.261289 69.653243 \n", "L 105.587859 68.988569 \n", "L 105.91443 66.405047 \n", "L 106.56757 75.204994 \n", "L 106.894141 79.889013 \n", "L 107.220711 80.890961 \n", "L 107.547282 80.46648 \n", "L 107.873852 77.417938 \n", "L 108.200422 75.719453 \n", "L 108.526993 76.513161 \n", "L 108.853563 74.169623 \n", "L 109.180134 76.41618 \n", "L 109.506704 77.486647 \n", "L 109.833275 79.863457 \n", "L 110.159845 80.160865 \n", "L 110.486415 79.950869 \n", "L 111.139556 69.339113 \n", "L 111.466127 68.796466 \n", "L 111.792697 66.448995 \n", "L 112.445838 72.84291 \n", "L 112.772408 73.189964 \n", "L 113.098979 71.167596 \n", "L 113.75212 74.29522 \n", "L 114.07869 70.818151 \n", "L 114.405261 62.993607 \n", "L 114.731831 68.141564 \n", "L 115.058401 62.585226 \n", "L 115.384972 69.207742 \n", "L 115.711542 72.338351 \n", "L 116.038113 78.357516 \n", "L 116.364683 76.039815 \n", "L 116.691254 76.476225 \n", "L 117.017824 73.069876 \n", "L 117.344394 71.794171 \n", "L 117.670965 75.35145 \n", "L 117.997535 76.689982 \n", "L 118.324106 77.077032 \n", "L 118.650676 74.879926 \n", "L 118.977247 71.359059 \n", "L 119.303817 72.052966 \n", "L 119.630387 73.998986 \n", "L 119.956958 74.996684 \n", "L 120.283528 73.087341 \n", "L 120.936669 67.632761 \n", "L 121.263239 67.538972 \n", "L 121.58981 68.922909 \n", "L 121.91638 69.621203 \n", "L 122.242951 77.371862 \n", "L 122.896092 80.600309 \n", "L 123.222662 81.136281 \n", "L 123.549232 76.092264 \n", "L 123.875803 76.549217 \n", "L 124.202373 77.729434 \n", "L 125.182085 72.529859 \n", "L 125.508655 72.855527 \n", "L 125.835225 75.423585 \n", "L 126.161796 75.389948 \n", "L 126.488366 76.334244 \n", "L 126.814937 75.834805 \n", "L 127.141507 72.982416 \n", "L 127.468078 72.870261 \n", "L 127.794648 68.563868 \n", "L 128.121218 69.781406 \n", "L 128.447789 68.291689 \n", "L 128.774359 68.584699 \n", "L 129.10093 70.358551 \n", "L 129.4275 67.017512 \n", "L 129.754071 70.346236 \n", "L 130.080641 70.707889 \n", "L 130.407211 71.854531 \n", "L 130.733782 75.694702 \n", "L 131.060352 74.910982 \n", "L 131.386923 69.91098 \n", "L 131.713493 71.890684 \n", "L 132.040064 70.681232 \n", "L 132.366634 75.341838 \n", "L 132.693204 74.573546 \n", "L 133.346345 75.718379 \n", "L 133.672916 78.83608 \n", "L 134.326057 80.997761 \n", "L 134.652627 76.690698 \n", "L 134.979197 77.69183 \n", "L 135.305768 81.057267 \n", "L 135.632338 82.385939 \n", "L 135.958909 84.555328 \n", "L 136.285479 76.564204 \n", "L 136.612049 73.831006 \n", "L 136.93862 75.606677 \n", "L 137.26519 80.510191 \n", "L 137.591761 83.258784 \n", "L 137.918331 84.222221 \n", "L 138.244902 78.694715 \n", "L 138.571472 80.444496 \n", "L 138.898042 77.799751 \n", "L 139.224613 81.052481 \n", "L 139.551183 82.091313 \n", "L 139.877754 82.382828 \n", "L 140.204324 86.408639 \n", "L 140.530895 81.028887 \n", "L 140.857465 81.787735 \n", "L 141.184035 79.321826 \n", "L 141.510606 80.172906 \n", "L 141.837176 80.257626 \n", "L 142.163747 82.250195 \n", "L 142.816888 88.135566 \n", "L 143.143458 82.776507 \n", "L 143.796599 76.905437 \n", "L 145.102881 91.631642 \n", "L 145.429451 89.726878 \n", "L 145.756021 84.697067 \n", "L 146.082592 83.584221 \n", "L 146.409162 80.608971 \n", "L 146.735733 81.457247 \n", "L 147.062303 83.435872 \n", "L 147.388874 82.908427 \n", "L 147.715444 87.575177 \n", "L 148.042014 85.766418 \n", "L 148.368585 86.953482 \n", "L 148.695155 85.097755 \n", "L 149.021726 87.645391 \n", "L 149.348296 87.756945 \n", "L 149.674866 92.577717 \n", "L 150.001437 89.207689 \n", "L 150.328007 89.993533 \n", "L 150.654578 93.565045 \n", "L 150.981148 91.293865 \n", "L 151.307719 96.345234 \n", "L 151.634289 93.057034 \n", "L 151.960859 91.862137 \n", "L 152.28743 92.719779 \n", "L 152.614 90.223355 \n", "L 152.940571 92.596215 \n", "L 153.267141 91.323058 \n", "L 153.593712 93.869565 \n", "L 154.246852 91.119543 \n", "L 154.573423 94.17622 \n", "L 154.899993 94.182606 \n", "L 155.226564 100.708096 \n", "L 155.553134 95.005203 \n", "L 155.879705 94.92944 \n", "L 156.206275 91.763579 \n", "L 156.532845 94.118592 \n", "L 156.859416 95.186769 \n", "L 157.185986 92.120396 \n", "L 157.512557 90.441834 \n", "L 157.839127 91.101726 \n", "L 158.165698 94.976432 \n", "L 158.492268 95.472807 \n", "L 158.818838 94.785529 \n", "L 159.145409 94.761468 \n", "L 159.471979 98.080348 \n", "L 159.79855 94.551948 \n", "L 160.12512 100.897213 \n", "L 160.451691 101.488017 \n", "L 161.104831 93.452863 \n", "L 161.431402 93.288266 \n", "L 161.757972 94.048913 \n", "L 162.084543 93.755378 \n", "L 162.411113 96.601454 \n", "L 162.737684 95.92751 \n", "L 163.064254 97.984654 \n", "L 163.390824 99.302686 \n", "L 164.043965 106.371918 \n", "L 164.370536 108.101475 \n", "L 164.697106 111.290358 \n", "L 165.023676 110.789195 \n", "L 165.676817 105.761546 \n", "L 166.003388 102.027424 \n", "L 166.329958 106.254752 \n", "L 166.656529 106.956354 \n", "L 166.983099 104.34777 \n", "L 167.309669 105.862923 \n", "L 167.63624 106.028798 \n", "L 168.289381 110.035021 \n", "L 168.615951 105.236608 \n", "L 168.942522 109.248678 \n", "L 169.269092 111.022539 \n", "L 169.595662 111.441936 \n", "L 169.922233 113.461646 \n", "L 170.248803 109.167251 \n", "L 170.901944 104.344161 \n", "L 171.228515 104.022912 \n", "L 171.555085 108.201269 \n", "L 171.881655 107.363677 \n", "L 172.208226 108.471201 \n", "L 172.534796 114.602344 \n", "L 172.861367 114.471799 \n", "L 173.187937 115.591201 \n", "L 173.514508 111.1372 \n", "L 173.841078 113.404171 \n", "L 174.167648 112.391828 \n", "L 174.820789 116.853931 \n", "L 175.14736 113.400655 \n", "L 175.800501 119.599845 \n", "L 176.453641 118.572644 \n", "L 176.780212 119.920661 \n", "L 177.106782 119.522695 \n", "L 177.433353 117.053206 \n", "L 177.759923 121.543567 \n", "L 178.086493 122.528997 \n", "L 178.413064 121.949029 \n", "L 178.739634 120.560589 \n", "L 179.066205 118.255587 \n", "L 179.392775 119.138576 \n", "L 179.719346 113.923109 \n", "L 180.045916 110.532301 \n", "L 180.699057 113.617341 \n", "L 181.025627 122.893405 \n", "L 181.352198 123.873906 \n", "L 181.678768 123.920837 \n", "L 182.005339 122.311016 \n", "L 182.331909 117.295355 \n", "L 182.658479 123.343292 \n", "L 182.98505 126.341023 \n", "L 183.638191 127.352382 \n", "L 183.964761 129.598657 \n", "L 184.291332 128.493241 \n", "L 184.617902 135.56151 \n", "L 184.944472 138.171153 \n", "L 185.597613 132.292514 \n", "L 185.924184 123.869134 \n", "L 186.250754 123.57304 \n", "L 186.577325 122.724747 \n", "L 186.903895 123.587361 \n", "L 187.230465 125.714355 \n", "L 187.557036 122.101536 \n", "L 187.883606 120.784897 \n", "L 189.189888 130.245022 \n", "L 189.516458 126.803193 \n", "L 189.843029 125.82245 \n", "L 190.169599 125.486484 \n", "L 190.49617 130.736477 \n", "L 190.82274 132.078213 \n", "L 191.149311 135.190198 \n", "L 191.475881 134.973683 \n", "L 191.802451 133.63959 \n", "L 192.129022 133.425686 \n", "L 192.455592 134.544583 \n", "L 192.782163 134.482869 \n", "L 193.108733 133.677496 \n", "L 193.435303 128.517035 \n", "L 193.761874 126.748629 \n", "L 194.088444 128.658658 \n", "L 194.415015 131.366988 \n", "L 194.741585 130.466097 \n", "L 195.068156 136.684644 \n", "L 195.721296 133.096911 \n", "L 196.047867 133.122425 \n", "L 196.374437 131.150991 \n", "L 196.701008 133.114385 \n", "L 197.027578 133.705374 \n", "L 197.354149 133.50212 \n", "L 197.680719 132.859681 \n", "L 198.007289 137.471895 \n", "L 198.33386 136.732905 \n", "L 198.66043 137.712766 \n", "L 198.987001 141.109736 \n", "L 199.313571 138.203903 \n", "L 199.640142 141.295149 \n", "L 199.966712 139.82101 \n", "L 200.293282 139.248232 \n", "L 200.619853 140.13279 \n", "L 200.946423 144.748834 \n", "L 201.272994 141.537226 \n", "L 201.599564 141.899583 \n", "L 201.926135 144.488443 \n", "L 202.252705 144.071891 \n", "L 202.579275 145.787367 \n", "L 202.905846 140.503821 \n", "L 203.232416 138.476949 \n", "L 203.558987 135.607109 \n", "L 204.212128 144.032441 \n", "L 204.538698 143.693053 \n", "L 204.865268 142.474492 \n", "L 205.191839 138.074329 \n", "L 205.518409 135.262682 \n", "L 205.84498 137.799139 \n", "L 206.17155 141.33661 \n", "L 206.49812 141.650987 \n", "L 206.824691 143.609996 \n", "L 207.151261 137.747736 \n", "L 207.477832 136.522005 \n", "L 207.804402 138.751896 \n", "L 208.130973 138.863741 \n", "L 208.457543 140.709521 \n", "L 208.784113 146.269097 \n", "L 209.110684 142.070252 \n", "L 209.437254 147.671635 \n", "L 209.763825 145.007855 \n", "L 210.090395 145.43517 \n", "L 210.416966 146.491469 \n", "L 210.743536 139.629914 \n", "L 211.070106 141.901844 \n", "L 211.396677 138.549231 \n", "L 211.723247 139.726704 \n", "L 212.049818 141.34281 \n", "L 212.376388 141.935361 \n", "L 212.702959 144.70739 \n", "L 213.029529 141.514695 \n", "L 213.356099 142.408239 \n", "L 213.68267 141.97834 \n", "L 214.988952 147.02765 \n", "L 215.315522 146.368596 \n", "L 215.642092 146.777677 \n", "L 215.968663 144.932812 \n", "L 216.295233 140.368252 \n", "L 216.948374 138.853803 \n", "L 217.601515 147.778825 \n", "L 217.928085 147.430458 \n", "L 218.581226 147.316252 \n", "L 218.907797 146.618854 \n", "L 219.234367 148.092421 \n", "L 219.560938 148.549348 \n", "L 219.887508 144.720658 \n", "L 220.214078 144.641496 \n", "L 220.540649 145.787812 \n", "L 220.867219 144.164878 \n", "L 221.52036 149.560438 \n", "L 221.84693 148.283767 \n", "L 222.173501 150.757146 \n", "L 222.826642 142.438556 \n", "L 223.153212 142.301953 \n", "L 223.479783 142.499482 \n", "L 223.806353 143.088851 \n", "L 224.132923 146.07539 \n", "L 224.459494 150.138231 \n", "L 224.786064 149.639887 \n", "L 225.112635 144.494808 \n", "L 225.439205 143.024896 \n", "L 225.765776 139.30812 \n", "L 226.092346 142.52301 \n", "L 226.418916 143.322943 \n", "L 226.745487 146.219198 \n", "L 227.072057 144.978651 \n", "L 227.398628 142.975165 \n", "L 227.725198 142.196393 \n", "L 228.051769 144.045054 \n", "L 228.378339 144.542658 \n", "L 228.704909 149.487155 \n", "L 229.03148 149.877733 \n", "L 229.35805 149.308559 \n", "L 229.684621 148.269386 \n", "L 230.011191 149.255324 \n", "L 230.337762 145.423759 \n", "L 230.664332 144.349887 \n", "L 230.990902 141.842621 \n", "L 231.317473 137.694285 \n", "L 231.644043 138.724641 \n", "L 231.970614 134.031248 \n", "L 232.297184 140.2014 \n", "L 232.623755 139.639564 \n", "L 232.950325 141.960427 \n", "L 233.276895 145.533471 \n", "L 233.930036 146.583467 \n", "L 234.909747 137.115515 \n", "L 235.236318 139.724313 \n", "L 235.562888 143.292591 \n", "L 235.889459 139.676884 \n", "L 236.216029 138.44265 \n", "L 236.5426 137.930638 \n", "L 236.86917 134.339051 \n", "L 237.19574 139.27437 \n", "L 237.522311 140.71196 \n", "L 237.848881 140.206943 \n", "L 238.175452 138.568781 \n", "L 238.502022 139.643699 \n", "L 238.828593 142.04051 \n", "L 239.155163 140.681458 \n", "L 239.808304 139.208867 \n", "L 240.134874 139.138486 \n", "L 240.461445 137.48098 \n", "L 240.788015 139.393804 \n", "L 241.441156 135.99481 \n", "L 241.767726 137.058957 \n", "L 242.420867 136.778909 \n", "L 242.747438 140.719966 \n", "L 243.074008 134.719115 \n", "L 243.400579 131.953414 \n", "L 244.053719 123.234168 \n", "L 245.033431 133.490699 \n", "L 245.360001 132.456513 \n", "L 245.686572 133.527217 \n", "L 246.013142 134.044128 \n", "L 246.339712 132.099719 \n", "L 246.666283 134.061894 \n", "L 246.992853 133.468682 \n", "L 247.319424 130.485109 \n", "L 247.645994 129.615525 \n", "L 247.972564 125.19926 \n", "L 248.299135 129.920283 \n", "L 248.625705 129.654078 \n", "L 248.952276 135.709874 \n", "L 249.278846 133.696234 \n", "L 249.605417 127.146757 \n", "L 249.931987 127.227265 \n", "L 250.258557 120.712353 \n", "L 250.585128 123.704721 \n", "L 250.911698 123.603483 \n", "L 251.238269 127.154126 \n", "L 251.564839 127.039563 \n", "L 251.89141 129.14979 \n", "L 252.54455 130.20079 \n", "L 252.871121 129.505077 \n", "L 253.197691 131.230419 \n", "L 253.524262 131.415897 \n", "L 254.503973 138.577424 \n", "L 254.830543 131.945789 \n", "L 255.157114 130.79321 \n", "L 255.483684 126.642756 \n", "L 255.810255 129.098532 \n", "L 256.136825 132.577663 \n", "L 256.463396 132.880944 \n", "L 256.789966 134.021505 \n", "L 257.116536 126.058493 \n", "L 257.443107 129.55858 \n", "L 258.096248 123.849279 \n", "L 258.422818 125.603191 \n", "L 258.749389 120.934117 \n", "L 259.075959 125.046463 \n", "L 259.402529 124.180007 \n", "L 259.7291 123.931931 \n", "L 260.05567 122.294204 \n", "L 260.382241 118.142472 \n", "L 260.708811 123.464468 \n", "L 261.035382 126.793389 \n", "L 261.361952 125.652523 \n", "L 261.688522 126.160178 \n", "L 262.015093 122.178631 \n", "L 262.341663 120.380294 \n", "L 262.668234 116.277897 \n", "L 262.994804 118.880052 \n", "L 263.321374 114.62193 \n", "L 263.647945 116.450825 \n", "L 263.974515 112.537936 \n", "L 264.301086 116.133755 \n", "L 264.627656 112.264555 \n", "L 264.954227 111.278242 \n", "L 265.280797 111.326316 \n", "L 265.607367 109.198385 \n", "L 265.933938 110.819329 \n", "L 266.260508 109.780697 \n", "L 266.587079 115.235974 \n", "L 266.913649 112.274242 \n", "L 267.56679 109.652176 \n", "L 267.89336 109.706201 \n", "L 268.219931 107.456193 \n", "L 268.546501 111.850454 \n", "L 268.873072 112.819352 \n", "L 269.199642 114.818211 \n", "L 269.526213 111.898443 \n", "L 269.852783 112.942501 \n", "L 270.179353 110.719979 \n", "L 270.505924 109.401358 \n", "L 270.832494 106.317803 \n", "L 271.812206 113.912058 \n", "L 272.138776 108.643038 \n", "L 272.791917 107.783943 \n", "L 273.118487 105.264027 \n", "L 273.445058 105.598162 \n", "L 273.771628 105.661419 \n", "L 274.098199 104.632512 \n", "L 274.424769 108.862061 \n", "L 274.751339 108.070392 \n", "L 275.07791 103.490835 \n", "L 275.40448 104.650637 \n", "L 275.731051 101.69195 \n", "L 276.057621 101.005961 \n", "L 276.384191 103.836572 \n", "L 276.710762 102.812493 \n", "L 277.037332 102.272581 \n", "L 277.363903 99.882625 \n", "L 277.690473 101.161907 \n", "L 278.017044 98.111925 \n", "L 278.343614 98.612232 \n", "L 278.670184 99.712364 \n", "L 278.996755 103.373324 \n", "L 279.649896 100.320405 \n", "L 279.976466 94.241123 \n", "L 280.629607 98.315522 \n", "L 280.956177 99.068583 \n", "L 281.282748 97.384476 \n", "L 281.935889 92.310227 \n", "L 282.58903 101.796965 \n", "L 282.9156 103.257397 \n", "L 283.24217 103.575165 \n", "L 283.568741 101.414432 \n", "L 283.895311 97.410261 \n", "L 284.548452 94.540409 \n", "L 284.875023 92.610134 \n", "L 285.201593 93.337517 \n", "L 285.528163 93.391577 \n", "L 285.854734 91.914285 \n", "L 286.507875 93.473115 \n", "L 286.834445 95.806003 \n", "L 287.161016 92.772259 \n", "L 287.487586 91.83324 \n", "L 287.814156 87.432581 \n", "L 288.140727 88.583355 \n", "L 288.467297 87.964122 \n", "L 288.793868 89.353651 \n", "L 289.120438 88.083194 \n", "L 289.447009 83.699435 \n", "L 289.773579 83.714707 \n", "L 290.42672 82.262031 \n", "L 290.75329 86.733391 \n", "L 291.079861 84.175769 \n", "L 291.406431 87.569407 \n", "L 291.733001 88.661265 \n", "L 292.059572 87.100962 \n", "L 292.386142 87.716779 \n", "L 292.712713 85.005122 \n", "L 293.039283 83.507715 \n", "L 293.365854 88.057361 \n", "L 293.692424 84.507254 \n", "L 294.018994 84.581684 \n", "L 294.345565 83.324037 \n", "L 294.672135 85.608974 \n", "L 294.998706 90.652812 \n", "L 295.325276 91.984074 \n", "L 295.651847 89.898612 \n", "L 295.978417 84.767339 \n", "L 296.304987 82.807372 \n", "L 296.631558 77.933388 \n", "L 296.958128 81.262431 \n", "L 297.284699 78.080274 \n", "L 297.611269 79.7797 \n", "L 297.93784 79.830425 \n", "L 298.26441 81.807378 \n", "L 298.59098 85.361777 \n", "L 298.917551 87.607612 \n", "L 299.244121 86.849781 \n", "L 299.570692 87.542309 \n", "L 299.897262 85.477997 \n", "L 300.223833 88.268439 \n", "L 300.550403 82.757372 \n", "L 300.876973 81.110475 \n", "L 301.203544 80.28679 \n", "L 301.530114 76.803788 \n", "L 301.856685 78.707147 \n", "L 302.183255 74.870574 \n", "L 302.509826 78.414681 \n", "L 302.836396 80.67013 \n", "L 303.162966 81.442765 \n", "L 303.489537 79.65736 \n", "L 303.816107 77.184012 \n", "L 304.142678 73.108241 \n", "L 304.469248 71.404066 \n", "L 304.795818 71.946331 \n", "L 305.122389 74.849617 \n", "L 305.448959 71.949239 \n", "L 305.77553 75.099282 \n", "L 306.1021 69.884715 \n", "L 306.428671 71.955502 \n", "L 306.755241 68.538079 \n", "L 307.081811 71.181262 \n", "L 307.408382 71.123174 \n", "L 307.734952 73.649523 \n", "L 308.388093 83.107089 \n", "L 308.714664 85.566666 \n", "L 309.041234 82.659111 \n", "L 309.367804 82.654629 \n", "L 309.694375 76.136683 \n", "L 310.020945 71.904649 \n", "L 310.347516 71.788783 \n", "L 310.674086 73.633035 \n", "L 311.327227 82.122546 \n", "L 311.653797 78.08626 \n", "L 311.980368 78.54408 \n", "L 312.306938 72.942557 \n", "L 312.633509 75.533424 \n", "L 312.960079 76.783967 \n", "L 313.28665 74.966658 \n", "L 313.61322 77.046276 \n", "L 314.266361 77.253235 \n", "L 314.592931 75.745875 \n", "L 314.919502 75.771695 \n", "L 315.246072 75.091607 \n", "L 315.572643 74.927173 \n", "L 315.899213 77.260085 \n", "L 316.552354 73.899928 \n", "L 316.878924 73.030198 \n", "L 317.205495 67.588885 \n", "L 317.532065 67.557327 \n", "L 318.185206 71.716067 \n", "L 318.511776 75.317344 \n", "L 318.838347 76.229054 \n", "L 319.164917 79.310002 \n", "L 319.491488 73.282328 \n", "L 319.818058 78.103001 \n", "L 320.144628 72.93025 \n", "L 320.471199 70.013838 \n", "L 320.797769 70.070616 \n", "L 321.12434 64.919823 \n", "L 321.45091 70.562177 \n", "L 321.777481 68.308228 \n", "L 322.104051 72.702798 \n", "L 322.430621 73.5096 \n", "L 322.757192 77.365542 \n", "L 323.083762 74.255663 \n", "L 323.410333 74.965199 \n", "L 323.736903 72.176932 \n", "L 324.063474 74.047533 \n", "L 324.390044 79.286589 \n", "L 324.716614 80.188925 \n", "L 325.043185 74.7015 \n", "L 325.369755 72.917299 \n", "L 325.696326 65.417447 \n", "L 326.022896 65.358751 \n", "L 326.349467 65.659021 \n", "L 327.002607 69.001419 \n", "L 327.329178 69.286474 \n", "L 327.982319 67.78627 \n", "L 328.308889 69.556368 \n", "L 328.63546 67.394778 \n", "L 328.96203 68.086893 \n", "L 329.2886 62.577323 \n", "L 329.615171 65.980897 \n", "L 329.941741 62.521693 \n", "L 330.594882 67.197611 \n", "L 330.921453 65.778434 \n", "L 331.248023 70.790307 \n", "L 331.901164 65.873919 \n", "L 332.227734 71.01618 \n", "L 332.880875 75.590756 \n", "L 333.207445 75.695817 \n", "L 333.534016 71.307901 \n", "L 333.860586 72.787197 \n", "L 334.187157 71.782879 \n", "L 334.513727 73.202379 \n", "L 334.840298 77.296267 \n", "L 335.166868 71.257761 \n", "L 335.493438 70.723238 \n", "L 335.820009 68.06748 \n", "L 336.146579 70.812001 \n", "L 336.47315 75.283097 \n", "L 336.79972 74.811802 \n", "L 337.126291 74.931221 \n", "L 337.452861 75.970958 \n", "L 337.779431 78.843719 \n", "L 338.106002 78.358561 \n", "L 338.432572 76.844934 \n", "L 338.759143 76.557841 \n", "L 339.085713 81.514689 \n", "L 339.412284 80.426079 \n", "L 339.738854 80.268502 \n", "L 340.065424 76.41346 \n", "L 340.391995 74.831863 \n", "L 340.718565 78.577436 \n", "L 341.045136 77.927787 \n", "L 341.371706 80.255442 \n", "L 341.698277 76.852488 \n", "L 342.024847 79.791886 \n", "L 342.351417 80.282837 \n", "L 342.677988 81.326875 \n", "L 343.004558 78.171298 \n", "L 343.331129 78.541055 \n", "L 343.657699 82.097965 \n", "L 343.98427 82.505568 \n", "L 344.31084 82.040201 \n", "L 344.63741 79.361727 \n", "L 344.963981 79.952524 \n", "L 345.290551 84.713899 \n", "L 345.617122 81.445569 \n", "L 345.943692 86.200433 \n", "L 346.596833 81.481971 \n", "L 346.923403 81.612269 \n", "L 347.249974 76.840244 \n", "L 347.576544 77.013842 \n", "L 347.903115 76.281515 \n", "L 348.556255 82.157317 \n", "L 349.209396 85.934942 \n", "L 349.535967 88.589431 \n", "L 349.862537 87.389398 \n", "L 350.189108 89.26916 \n", "L 350.515678 88.117271 \n", "L 350.842248 88.547623 \n", "L 351.168819 89.622083 \n", "L 351.495389 91.69147 \n", "L 351.82196 90.901063 \n", "L 352.14853 91.373337 \n", "L 352.475101 89.277559 \n", "L 353.128241 83.001664 \n", "L 353.781382 89.537697 \n", "L 354.107953 88.810149 \n", "L 354.434523 85.968581 \n", "L 354.761094 85.120757 \n", "L 355.087664 83.388019 \n", "L 355.414234 86.573816 \n", "L 355.740805 86.655165 \n", "L 356.067375 87.678361 \n", "L 356.393946 85.222724 \n", "L 356.720516 88.314357 \n", "L 357.047087 89.863126 \n", "L 357.373657 87.79241 \n", "L 357.700227 89.992546 \n", "L 358.026798 90.530574 \n", "L 359.006509 102.157322 \n", "L 359.33308 98.517265 \n", "L 359.65965 96.837834 \n", "L 359.98622 93.386486 \n", "L 360.312791 92.399308 \n", "L 360.639361 94.852815 \n", "L 360.965932 98.282995 \n", "L 361.292502 97.390667 \n", "L 361.619072 97.193318 \n", "L 361.945643 95.392935 \n", "L 362.272213 95.291249 \n", "L 362.598784 95.851198 \n", "L 362.925354 97.366969 \n", "L 363.251925 103.075643 \n", "L 363.578495 101.874931 \n", "L 363.905065 102.479046 \n", "L 364.558206 96.353872 \n", "L 364.884777 98.504486 \n", "L 365.211347 97.382711 \n", "L 365.537918 96.696447 \n", "L 365.864488 101.790654 \n", "L 366.191058 102.69255 \n", "L 366.517629 104.872668 \n", "L 366.844199 104.503524 \n", "L 367.17077 105.390009 \n", "L 367.49734 107.683994 \n", "L 367.823911 108.582264 \n", "L 368.150481 107.721223 \n", "L 368.477051 108.207749 \n", "L 368.803622 112.380333 \n", "L 369.130192 112.471902 \n", "L 369.456763 104.06683 \n", "L 369.783333 101.364973 \n", "L 370.109904 96.181757 \n", "L 370.763044 101.714925 \n", "L 371.089615 101.187759 \n", "L 371.416185 98.916615 \n", "L 371.742756 103.157207 \n", "L 371.742756 103.157207 \n", "\" clip-path=\"url(#p8404ed6792)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.52 \n", "L 52.160938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 386.**********.52 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.52 \n", "L 386.**********.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 7.2 \n", "L 386.960938 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 169.017188 73.9125 \n", "L 270.104688 73.9125 \n", "Q 272.104688 73.9125 272.104688 71.9125 \n", "L 272.104688 14.2 \n", "Q 272.104688 12.2 270.104688 12.2 \n", "L 169.017188 12.2 \n", "Q 167.017188 12.2 167.017188 14.2 \n", "L 167.017188 71.9125 \n", "Q 167.017188 73.9125 169.017188 73.9125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 171.017188 20.298438 \n", "L 181.017188 20.298438 \n", "L 191.017188 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- 1-step preds -->\n", "     <g transform=\"translate(199.017188 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"99.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"151.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"191.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"252.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"316.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"347.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"411.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"450.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"511.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"575.142578\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 171.017188 34.976562 \n", "L 181.017188 34.976562 \n", "L 191.017188 34.976562 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- 4-step preds -->\n", "     <g transform=\"translate(199.017188 38.476562) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-34\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"99.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"151.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"191.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"252.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"316.015625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"347.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"411.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"450.142578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"511.666016\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"575.142578\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_29\">\n", "     <path d=\"M 171.017188 49.654687 \n", "L 181.017188 49.654687 \n", "L 191.017188 49.654687 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- 16-step preds -->\n", "     <g transform=\"translate(199.017188 53.154687) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"127.246094\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"163.330078\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"215.429688\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"254.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"316.162109\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"379.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"411.425781\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"474.902344\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"513.765625\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"575.289062\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"638.765625\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 171.017188 64.332813 \n", "L 181.017188 64.332813 \n", "L 191.017188 64.332813 \n", "\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- 64-step preds -->\n", "     <g transform=\"translate(199.017188 67.832813) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-36\"/>\n", "      <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"127.246094\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"163.330078\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"215.429688\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"254.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"316.162109\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"379.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"411.425781\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"474.902344\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"513.765625\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"575.289062\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"638.765625\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p8404ed6792\">\n", "   <rect x=\"52.160938\" y=\"7.2\" width=\"334.8\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["steps = (1, 4, 16, 64)\n", "preds = k_step_pred(steps[-1])\n", "d2l.plot(data.time[data.tau+steps[-1]-1:],\n", "         [preds[k - 1].detach().numpy() for k in steps], 'time', 'x',\n", "         legend=[f'{k}-step preds' for k in steps], figsize=(6, 3))"]}, {"cell_type": "markdown", "id": "4ca5526c", "metadata": {"origin_pos": 25}, "source": ["This clearly illustrates how the quality of the prediction changes\n", "as we try to predict further into the future.\n", "While the 4-step-ahead predictions still look good,\n", "anything beyond that is almost useless.\n", "\n", "## Summary\n", "\n", "There is quite a difference in difficulty\n", "between interpolation and extrapolation.\n", "Consequently, if you have a sequence, always respect\n", "the temporal order of the data when training,\n", "i.e., never train on future data.\n", "Given this kind of data,\n", "sequence models require specialized statistical tools for estimation.\n", "Two popular choices are autoregressive models\n", "and latent-variable autoregressive models.\n", "For causal models (e.g., time going forward),\n", "estimating the forward direction is typically\n", "a lot easier than the reverse direction.\n", "For an observed sequence up to time step $t$,\n", "its predicted output at time step $t+k$\n", "is the $k$*-step-ahead prediction*.\n", "As we predict further in time by increasing $k$,\n", "the errors accumulate and the quality of the prediction degrades,\n", "often dramatically.\n", "\n", "## Exercises\n", "\n", "1. Improve the model in the experiment of this section.\n", "    1. Incorporate more than the past four observations? How many do you really need?\n", "    1. How many past observations would you need if there was no noise? Hint: you can write $\\sin$ and $\\cos$ as a differential equation.\n", "    1. Can you incorporate older observations while keeping the total number of features constant? Does this improve accuracy? Why?\n", "    1. Change the neural network architecture and evaluate the performance. You may train the new model with more epochs. What do you observe?\n", "1. An investor wants to find a good security to buy.\n", "   They look at past returns to decide which one is likely to do well.\n", "   What could possibly go wrong with this strategy?\n", "1. Does causality also apply to text? To which extent?\n", "1. Give an example for when a latent autoregressive model\n", "   might be needed to capture the dynamic of the data.\n"]}, {"cell_type": "markdown", "id": "e599b913", "metadata": {"origin_pos": 27, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/114)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
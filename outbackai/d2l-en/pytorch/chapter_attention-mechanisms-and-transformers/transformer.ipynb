{"cells": [{"cell_type": "markdown", "id": "0463f227", "metadata": {"origin_pos": 1}, "source": ["# The Transformer Architecture\n", ":label:`sec_transformer`\n", "\n", "\n", "We have compared CNNs, RNNs, and self-attention in\n", ":numref:`subsec_cnn-rnn-self-attention`.\n", "Notably, self-attention\n", "enjoys both parallel computation and\n", "the shortest maximum path length.\n", "Therefore,\n", "it is appealing to design deep architectures\n", "by using self-attention.\n", "Unlike earlier self-attention models\n", "that still rely on RNNs for input representations :cite:`Cheng.Dong.Lapata.2016,Lin<PERSON>.Santos.ea.2017,Paulus.Xiong.Socher.2017`,\n", "the Transformer model\n", "is solely based on attention mechanisms\n", "without any convolutional or recurrent layer :cite:`Vaswani.Shazeer.Parmar.ea.2017`.\n", "Though originally proposed\n", "for sequence-to-sequence learning on text data,\n", "Transformers have been\n", "pervasive in a wide range of\n", "modern deep learning applications,\n", "such as in areas to do with language, vision, speech, and reinforcement learning.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "ee18893c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:06.687415Z", "iopub.status.busy": "2023-08-18T19:50:06.687094Z", "iopub.status.idle": "2023-08-18T19:50:09.889628Z", "shell.execute_reply": "2023-08-18T19:50:09.888444Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import math\n", "import pandas as pd\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "23e72a17", "metadata": {"origin_pos": 6}, "source": ["## Model\n", "\n", "As an instance of the encoder--decoder\n", "architecture,\n", "the overall architecture of\n", "the Transformer\n", "is presented in :numref:`fig_transformer`.\n", "As we can see,\n", "the Transformer is composed of an encoder and a decoder.\n", "In contrast to\n", "Bahdanau attention\n", "for sequence-to-sequence learning\n", "in :numref:`fig_s2s_attention_details`,\n", "the input (source) and output (target)\n", "sequence embeddings\n", "are added with positional encoding\n", "before being fed into\n", "the encoder and the decoder\n", "that stack modules based on self-attention.\n", "\n", "![The Transformer architecture.](../img/transformer.svg)\n", ":width:`320px`\n", ":label:`fig_transformer`\n", "\n", "\n", "Now we provide an overview of the\n", "Transformer architecture in :numref:`fig_transformer`.\n", "At a high level,\n", "the Transformer encoder is a stack of multiple identical layers,\n", "where each layer\n", "has two sublayers (either is denoted as $\\textrm{sublayer}$).\n", "The first\n", "is a multi-head self-attention pooling\n", "and the second is a positionwise feed-forward network.\n", "Specifically,\n", "in the encoder self-attention,\n", "queries, keys, and values are all from the\n", "outputs of the previous encoder layer.\n", "Inspired by the ResNet design of :numref:`sec_resnet`,\n", "a residual connection is employed\n", "around both sublayers.\n", "In the Transformer,\n", "for any input $\\mathbf{x} \\in \\mathbb{R}^d$ at any position of the sequence,\n", "we require that $\\textrm{sublayer}(\\mathbf{x}) \\in \\mathbb{R}^d$ so that\n", "the residual connection $\\mathbf{x} + \\textrm{sublayer}(\\mathbf{x}) \\in \\mathbb{R}^d$ is feasible.\n", "This addition from the residual connection is immediately\n", "followed by layer normalization :cite:`<PERSON>.Ki<PERSON>.Hinton.2016`.\n", "As a result, the Transformer encoder outputs a $d$-dimensional vector representation\n", "for each position of the input sequence.\n", "\n", "The Transformer decoder is also a stack of multiple identical layers\n", "with residual connections and layer normalizations.\n", "As well as the two sublayers described in\n", "the encoder, the decoder inserts\n", "a third sublayer, known as\n", "the encoder--decoder attention,\n", "between these two.\n", "In the encoder--decoder attention,\n", "queries are from the\n", "outputs of the decoder's self-attention sublayer,\n", "and the keys and values are\n", "from the Transformer encoder outputs.\n", "In the decoder self-attention,\n", "queries, keys, and values are all from the\n", "outputs of the previous decoder layer.\n", "However, each position in the decoder is\n", "allowed only to attend to all positions in the decoder\n", "up to that position.\n", "This *masked* attention\n", "preserves the autoregressive property,\n", "ensuring that the prediction only depends\n", "on those output tokens that have been generated.\n", "\n", "\n", "We have already described and implemented\n", "multi-head attention based on scaled dot products\n", "in :numref:`sec_multihead-attention`\n", "and positional encoding in :numref:`subsec_positional-encoding`.\n", "In the following, we will implement\n", "the rest of the Transformer model.\n", "\n", "## [**Positionwise Feed-Forward Networks**]\n", ":label:`subsec_positionwise-ffn`\n", "\n", "The positionwise feed-forward network transforms\n", "the representation at all the sequence positions\n", "using the same MLP.\n", "This is why we call it *positionwise*.\n", "In the implementation below,\n", "the input `X` with shape\n", "(batch size, number of time steps or sequence length in tokens,\n", "number of hidden units or feature dimension)\n", "will be transformed by a two-layer MLP into\n", "an output tensor of shape\n", "(batch size, number of time steps, `ffn_num_outputs`).\n"]}, {"cell_type": "code", "execution_count": 2, "id": "623f67ee", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:09.894092Z", "iopub.status.busy": "2023-08-18T19:50:09.893416Z", "iopub.status.idle": "2023-08-18T19:50:09.899737Z", "shell.execute_reply": "2023-08-18T19:50:09.898347Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["class PositionWiseFFN(nn.Module):  #@save\n", "    \"\"\"The positionwise feed-forward network.\"\"\"\n", "    def __init__(self, ffn_num_hiddens, ffn_num_outputs):\n", "        super().__init__()\n", "        self.dense1 = nn.LazyLinear(ffn_num_hiddens)\n", "        self.relu = nn.ReLU()\n", "        self.dense2 = nn.LazyLinear(ffn_num_outputs)\n", "\n", "    def forward(self, X):\n", "        return self.dense2(self.relu(self.dense1(X)))"]}, {"cell_type": "markdown", "id": "cfc40d5b", "metadata": {"origin_pos": 11}, "source": ["The following example\n", "shows that [**the innermost dimension\n", "of a tensor changes**] to\n", "the number of outputs in\n", "the positionwise feed-forward network.\n", "Since the same M<PERSON> transforms\n", "at all the positions,\n", "when the inputs at all these positions are the same,\n", "their outputs are also identical.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c462f39f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:09.906345Z", "iopub.status.busy": "2023-08-18T19:50:09.905327Z", "iopub.status.idle": "2023-08-18T19:50:09.920436Z", "shell.execute_reply": "2023-08-18T19:50:09.919542Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.1123,  0.5570,  0.0585, -0.0411, -0.3834, -0.0831, -0.1272,  0.2395],\n", "        [ 0.1123,  0.5570,  0.0585, -0.0411, -0.3834, -0.0831, -0.1272,  0.2395],\n", "        [ 0.1123,  0.5570,  0.0585, -0.0411, -0.3834, -0.0831, -0.1272,  0.2395]],\n", "       grad_fn=<SelectBackward0>)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["ffn = PositionWiseFFN(4, 8)\n", "ffn.eval()\n", "ffn(torch.ones((2, 3, 4)))[0]"]}, {"cell_type": "markdown", "id": "04e047d8", "metadata": {"origin_pos": 16}, "source": ["## Residual Connection and Layer Normalization\n", "\n", "Now let's focus on the \"add & norm\" component in :numref:`fig_transformer`.\n", "As we described at the beginning of this section,\n", "this is a residual connection immediately\n", "followed by layer normalization.\n", "Both are key to effective deep architectures.\n", "\n", "In :numref:`sec_batch_norm`,\n", "we explained how batch normalization\n", "recenters and rescales across the examples within\n", "a minibatch.\n", "As discussed in :numref:`subsec_layer-normalization-in-bn`,\n", "layer normalization is the same as batch normalization\n", "except that the former\n", "normalizes across the feature dimension,\n", "thus enjoying benefits of scale independence and batch size independence.\n", "Despite its pervasive applications\n", "in computer vision,\n", "batch normalization\n", "is usually empirically\n", "less effective than layer normalization\n", "in natural language processing\n", "tasks, where the inputs are often\n", "variable-length sequences.\n", "\n", "The following code snippet\n", "[**compares the normalization across different dimensions\n", "by layer normalization and batch normalization**].\n"]}, {"cell_type": "code", "execution_count": 4, "id": "81c95717", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:09.926398Z", "iopub.status.busy": "2023-08-18T19:50:09.924518Z", "iopub.status.idle": "2023-08-18T19:50:09.937855Z", "shell.execute_reply": "2023-08-18T19:50:09.936657Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["layer norm: tensor([[-1.0000,  1.0000],\n", "        [-1.0000,  1.0000]], grad_fn=<NativeLayerNormBackward0>) \n", "batch norm: tensor([[-1.0000, -1.0000],\n", "        [ 1.0000,  1.0000]], grad_fn=<NativeBatchNormBackward0>)\n"]}], "source": ["ln = nn.<PERSON>erNorm(2)\n", "bn = nn.LazyBatchNorm1d()\n", "X = torch.tensor([[1, 2], [2, 3]], dtype=torch.float32)\n", "# Compute mean and variance from X in the training mode\n", "print('layer norm:', ln(X), '\\nbatch norm:', bn(X))"]}, {"cell_type": "markdown", "id": "e802e7de", "metadata": {"origin_pos": 21}, "source": ["Now we can implement the `AddNorm` class\n", "[**using a residual connection followed by layer normalization**].\n", "Dropout is also applied for regularization.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "331f12e2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:09.941811Z", "iopub.status.busy": "2023-08-18T19:50:09.941163Z", "iopub.status.idle": "2023-08-18T19:50:09.948019Z", "shell.execute_reply": "2023-08-18T19:50:09.946884Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [], "source": ["class AddNorm(nn.Module):  #@save\n", "    \"\"\"The residual connection followed by layer normalization.\"\"\"\n", "    def __init__(self, norm_shape, dropout):\n", "        super().__init__()\n", "        self.dropout = nn.Dropout(dropout)\n", "        self.ln = nn.LayerNorm(norm_shape)\n", "\n", "    def forward(self, X, Y):\n", "        return self.ln(self.dropout(Y) + X)"]}, {"cell_type": "markdown", "id": "f034f8c8", "metadata": {"origin_pos": 26}, "source": ["The residual connection requires that\n", "the two inputs are of the same shape\n", "so that [**the output tensor also has the same shape after the addition operation**].\n"]}, {"cell_type": "code", "execution_count": 6, "id": "d3835d18", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:09.951769Z", "iopub.status.busy": "2023-08-18T19:50:09.951126Z", "iopub.status.idle": "2023-08-18T19:50:09.957096Z", "shell.execute_reply": "2023-08-18T19:50:09.956115Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [], "source": ["add_norm = AddNorm(4, 0.5)\n", "shape = (2, 3, 4)\n", "d2l.check_shape(add_norm(torch.ones(shape), torch.ones(shape)), shape)"]}, {"cell_type": "markdown", "id": "5d9098c5", "metadata": {"origin_pos": 31}, "source": ["## Encoder\n", ":label:`subsec_transformer-encoder`\n", "\n", "With all the essential components to assemble\n", "the Transformer encoder,\n", "let's start by\n", "implementing [**a single layer within the encoder**].\n", "The following `TransformerEncoderBlock` class\n", "contains two sublayers: multi-head self-attention and positionwise feed-forward networks,\n", "where a residual connection followed by layer normalization is employed\n", "around both sublayers.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "2c7cce60", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:09.960576Z", "iopub.status.busy": "2023-08-18T19:50:09.960031Z", "iopub.status.idle": "2023-08-18T19:50:09.965982Z", "shell.execute_reply": "2023-08-18T19:50:09.965069Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [], "source": ["class TransformerEncoderBlock(nn.Module):  #@save\n", "    \"\"\"The Transformer encoder block.\"\"\"\n", "    def __init__(self, num_hiddens, ffn_num_hiddens, num_heads, dropout,\n", "                 use_bias=False):\n", "        super().__init__()\n", "        self.attention = d2l.MultiHeadAttention(num_hiddens, num_heads,\n", "                                                dropout, use_bias)\n", "        self.addnorm1 = AddNorm(num_hiddens, dropout)\n", "        self.ffn = PositionWiseFFN(ffn_num_hiddens, num_hiddens)\n", "        self.addnorm2 = AddNorm(num_hiddens, dropout)\n", "\n", "    def forward(self, X, valid_lens):\n", "        Y = self.addnorm1(X, self.attention(X, X, X, valid_lens))\n", "        return self.addnorm2(Y, self.ffn(Y))"]}, {"cell_type": "markdown", "id": "015540bd", "metadata": {"origin_pos": 36}, "source": ["As we can see,\n", "[**no layer in the Transformer encoder\n", "changes the shape of its input.**]\n"]}, {"cell_type": "code", "execution_count": 8, "id": "9aefd8d7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:09.969308Z", "iopub.status.busy": "2023-08-18T19:50:09.968775Z", "iopub.status.idle": "2023-08-18T19:50:09.982374Z", "shell.execute_reply": "2023-08-18T19:50:09.981506Z"}, "origin_pos": 38, "tab": ["pytorch"]}, "outputs": [], "source": ["X = torch.ones((2, 100, 24))\n", "valid_lens = torch.tensor([3, 2])\n", "encoder_blk = TransformerEncoderBlock(24, 48, 8, 0.5)\n", "encoder_blk.eval()\n", "d2l.check_shape(encoder_blk(X, valid_lens), X.shape)"]}, {"cell_type": "markdown", "id": "21112538", "metadata": {"origin_pos": 41}, "source": ["In the following [**Transformer encoder**] implementation,\n", "we stack `num_blks` instances of the above `TransformerEncoderBlock` classes.\n", "Since we use the fixed positional encoding\n", "whose values are always between $-1$ and $1$,\n", "we multiply values of the learnable input embeddings\n", "by the square root of the embedding dimension\n", "to rescale before summing up the input embedding and the positional encoding.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "bdcabb11", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:09.986032Z", "iopub.status.busy": "2023-08-18T19:50:09.985758Z", "iopub.status.idle": "2023-08-18T19:50:09.993370Z", "shell.execute_reply": "2023-08-18T19:50:09.992550Z"}, "origin_pos": 43, "tab": ["pytorch"]}, "outputs": [], "source": ["class TransformerEncoder(d2l.Encoder):  #@save\n", "    \"\"\"The Transformer encoder.\"\"\"\n", "    def __init__(self, vocab_size, num_hiddens, ffn_num_hiddens,\n", "                 num_heads, num_blks, dropout, use_bias=False):\n", "        super().__init__()\n", "        self.num_hiddens = num_hiddens\n", "        self.embedding = nn.Embedding(vocab_size, num_hiddens)\n", "        self.pos_encoding = d2l.PositionalEncoding(num_hiddens, dropout)\n", "        self.blks = nn.Sequential()\n", "        for i in range(num_blks):\n", "            self.blks.add_module(\"block\"+str(i), TransformerEncoderBlock(\n", "                num_hiddens, ffn_num_hiddens, num_heads, dropout, use_bias))\n", "\n", "    def forward(self, X, valid_lens):\n", "        # Since positional encoding values are between -1 and 1, the embedding\n", "        # values are multiplied by the square root of the embedding dimension\n", "        # to rescale before they are summed up\n", "        X = self.pos_encoding(self.embedding(X) * math.sqrt(self.num_hiddens))\n", "        self.attention_weights = [None] * len(self.blks)\n", "        for i, blk in enumerate(self.blks):\n", "            X = blk(X, valid_lens)\n", "            self.attention_weights[\n", "                i] = blk.attention.attention.attention_weights\n", "        return X"]}, {"cell_type": "markdown", "id": "94344475", "metadata": {"origin_pos": 46}, "source": ["Below we specify hyperparameters to [**create a two-layer Transformer encoder**].\n", "The shape of the Transformer encoder output\n", "is (batch size, number of time steps, `num_hiddens`).\n"]}, {"cell_type": "code", "execution_count": 10, "id": "e09106b4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:09.996457Z", "iopub.status.busy": "2023-08-18T19:50:09.996186Z", "iopub.status.idle": "2023-08-18T19:50:10.014181Z", "shell.execute_reply": "2023-08-18T19:50:10.013041Z"}, "origin_pos": 48, "tab": ["pytorch"]}, "outputs": [], "source": ["encoder = TransformerEncoder(200, 24, 48, 8, 2, 0.5)\n", "d2l.check_shape(encoder(torch.ones((2, 100), dtype=torch.long), valid_lens),\n", "                (2, 100, 24))"]}, {"cell_type": "markdown", "id": "012a99d0", "metadata": {"origin_pos": 51}, "source": ["## Decoder\n", "\n", "As shown in :numref:`fig_transformer`,\n", "[**the Transformer decoder\n", "is composed of multiple identical layers**].\n", "Each layer is implemented in the following\n", "`TransformerDecoderBlock` class,\n", "which contains three sublayers:\n", "decoder self-attention,\n", "encoder--decoder attention,\n", "and positionwise feed-forward networks.\n", "These sublayers employ\n", "a residual connection around them\n", "followed by layer normalization.\n", "\n", "\n", "As we described earlier in this section,\n", "in the masked multi-head decoder self-attention\n", "(the first sublayer),\n", "queries, keys, and values\n", "all come from the outputs of the previous decoder layer.\n", "When training sequence-to-sequence models,\n", "tokens at all the positions (time steps)\n", "of the output sequence\n", "are known.\n", "However,\n", "during prediction\n", "the output sequence is generated token by token;\n", "thus,\n", "at any decoder time step\n", "only the generated tokens\n", "can be used in the decoder self-attention.\n", "To preserve autoregression in the decoder,\n", "its masked self-attention\n", "specifies  `dec_valid_lens` so that\n", "any query\n", "only attends to\n", "all positions in the decoder\n", "up to the query position.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "23664727", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:10.017719Z", "iopub.status.busy": "2023-08-18T19:50:10.017425Z", "iopub.status.idle": "2023-08-18T19:50:10.027060Z", "shell.execute_reply": "2023-08-18T19:50:10.026020Z"}, "origin_pos": 53, "tab": ["pytorch"]}, "outputs": [], "source": ["class TransformerDecoderBlock(nn.Module):\n", "    # The i-th block in the Transformer decoder\n", "    def __init__(self, num_hiddens, ffn_num_hiddens, num_heads, dropout, i):\n", "        super().__init__()\n", "        self.i = i\n", "        self.attention1 = d2l.MultiHeadAttention(num_hiddens, num_heads,\n", "                                                 dropout)\n", "        self.addnorm1 = AddNorm(num_hiddens, dropout)\n", "        self.attention2 = d2l.MultiHeadAttention(num_hiddens, num_heads,\n", "                                                 dropout)\n", "        self.addnorm2 = AddNorm(num_hiddens, dropout)\n", "        self.ffn = PositionWiseFFN(ffn_num_hiddens, num_hiddens)\n", "        self.addnorm3 = AddNorm(num_hiddens, dropout)\n", "\n", "    def forward(self, X, state):\n", "        enc_outputs, enc_valid_lens = state[0], state[1]\n", "        # During training, all the tokens of any output sequence are processed\n", "        # at the same time, so state[2][self.i] is None as initialized. When\n", "        # decoding any output sequence token by token during prediction,\n", "        # state[2][self.i] contains representations of the decoded output at\n", "        # the i-th block up to the current time step\n", "        if state[2][self.i] is None:\n", "            key_values = X\n", "        else:\n", "            key_values = torch.cat((state[2][self.i], X), dim=1)\n", "        state[2][self.i] = key_values\n", "        if self.training:\n", "            batch_size, num_steps, _ = X.shape\n", "            # Shape of dec_valid_lens: (batch_size, num_steps), where every\n", "            # row is [1, 2, ..., num_steps]\n", "            dec_valid_lens = torch.arange(\n", "                1, num_steps + 1, device=X.device).repeat(batch_size, 1)\n", "        else:\n", "            dec_valid_lens = None\n", "        # Self-attention\n", "        X2 = self.attention1(X, key_values, key_values, dec_valid_lens)\n", "        Y = self.addnorm1(X, X2)\n", "        # Encoder-decoder attention. Shape of enc_outputs:\n", "        # (batch_size, num_steps, num_hiddens)\n", "        Y2 = self.attention2(Y, enc_outputs, enc_outputs, enc_valid_lens)\n", "        Z = self.addnorm2(Y, Y2)\n", "        return self.addnorm3(Z, self.ffn(Z)), state"]}, {"cell_type": "markdown", "id": "a9481394", "metadata": {"origin_pos": 56}, "source": ["To facilitate scaled dot product operations\n", "in the encoder--decoder attention\n", "and addition operations in the residual connections,\n", "[**the feature dimension (`num_hiddens`) of the decoder is\n", "the same as that of the encoder.**]\n"]}, {"cell_type": "code", "execution_count": 12, "id": "1f487464", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:10.030357Z", "iopub.status.busy": "2023-08-18T19:50:10.030070Z", "iopub.status.idle": "2023-08-18T19:50:10.048172Z", "shell.execute_reply": "2023-08-18T19:50:10.046972Z"}, "origin_pos": 58, "tab": ["pytorch"]}, "outputs": [], "source": ["decoder_blk = TransformerDecoderBlock(24, 48, 8, 0.5, 0)\n", "X = torch.ones((2, 100, 24))\n", "state = [encoder_blk(X, valid_lens), valid_lens, [None]]\n", "d2l.check_shape(decoder_blk(X, state)[0], X.shape)"]}, {"cell_type": "markdown", "id": "5cb8bf72", "metadata": {"origin_pos": 61}, "source": ["Now we [**construct the entire Transformer decoder**]\n", "composed of `num_blks` instances of `TransformerDecoderBlock`.\n", "In the end,\n", "a fully connected layer computes the prediction\n", "for all the `vocab_size` possible output tokens.\n", "Both of the decoder self-attention weights\n", "and the encoder--decoder attention weights\n", "are stored for later visualization.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "38ebb1e7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:10.051657Z", "iopub.status.busy": "2023-08-18T19:50:10.051318Z", "iopub.status.idle": "2023-08-18T19:50:10.061485Z", "shell.execute_reply": "2023-08-18T19:50:10.060579Z"}, "origin_pos": 63, "tab": ["pytorch"]}, "outputs": [], "source": ["class TransformerDecoder(d2l.AttentionDecoder):\n", "    def __init__(self, vocab_size, num_hiddens, ffn_num_hiddens, num_heads,\n", "                 num_blks, dropout):\n", "        super().__init__()\n", "        self.num_hiddens = num_hiddens\n", "        self.num_blks = num_blks\n", "        self.embedding = nn.Embedding(vocab_size, num_hiddens)\n", "        self.pos_encoding = d2l.PositionalEncoding(num_hiddens, dropout)\n", "        self.blks = nn.Sequential()\n", "        for i in range(num_blks):\n", "            self.blks.add_module(\"block\"+str(i), TransformerDecoderBlock(\n", "                num_hiddens, ffn_num_hiddens, num_heads, dropout, i))\n", "        self.dense = nn.LazyLinear(vocab_size)\n", "\n", "    def init_state(self, enc_outputs, enc_valid_lens):\n", "        return [enc_outputs, enc_valid_lens, [None] * self.num_blks]\n", "\n", "    def forward(self, X, state):\n", "        X = self.pos_encoding(self.embedding(X) * math.sqrt(self.num_hiddens))\n", "        self._attention_weights = [[None] * len(self.blks) for _ in range (2)]\n", "        for i, blk in enumerate(self.blks):\n", "            X, state = blk(X, state)\n", "            # Decoder self-attention weights\n", "            self._attention_weights[0][\n", "                i] = blk.attention1.attention.attention_weights\n", "            # Encoder-decoder attention weights\n", "            self._attention_weights[1][\n", "                i] = blk.attention2.attention.attention_weights\n", "        return self.dense(X), state\n", "\n", "    @property\n", "    def attention_weights(self):\n", "        return self._attention_weights"]}, {"cell_type": "markdown", "id": "f91f85ae", "metadata": {"origin_pos": 66}, "source": ["## [**Training**]\n", "\n", "Let's instantiate an encoder--decoder model\n", "by following the Transformer architecture.\n", "Here we specify that\n", "both the Transformer encoder and the Transformer decoder\n", "have two layers using 4-head attention.\n", "As in :numref:`sec_seq2seq_training`,\n", "we train the Transformer model\n", "for sequence-to-sequence learning on the English--French machine translation dataset.\n"]}, {"cell_type": "code", "execution_count": 14, "id": "74f2da96", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:10.065769Z", "iopub.status.busy": "2023-08-18T19:50:10.064767Z", "iopub.status.idle": "2023-08-18T19:50:42.759965Z", "shell.execute_reply": "2023-08-18T19:50:42.758647Z"}, "origin_pos": 67, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"229.425pt\" height=\"183.35625pt\" viewBox=\"0 0 229.425 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-20T08:43:40.810923</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 229.425 183.35625 \n", "L 229.425 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 20.5625 145.8 \n", "L 215.8625 145.8 \n", "L 215.8625 7.2 \n", "L 20.5625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m1aec3144d4\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1aec3144d4\" x=\"20.5625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(17.38125 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m1aec3144d4\" x=\"53.1125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(49.93125 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m1aec3144d4\" x=\"85.6625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(79.3 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m1aec3144d4\" x=\"118.2125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(111.85 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m1aec3144d4\" x=\"150.7625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(144.4 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m1aec3144d4\" x=\"183.3125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 25 -->\n", "      <g transform=\"translate(176.95 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m1aec3144d4\" x=\"215.8625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(209.5 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(102.984375 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_8\">\n", "      <defs>\n", "       <path id=\"m4b245c5660\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m4b245c5660\" x=\"20.5625\" y=\"145.058321\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(7.2 148.85754) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m4b245c5660\" x=\"20.5625\" y=\"115.195575\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(7.2 118.994794) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m4b245c5660\" x=\"20.5625\" y=\"85.332829\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(7.2 89.132048) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m4b245c5660\" x=\"20.5625\" y=\"55.470083\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(7.2 59.269302) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m4b245c5660\" x=\"20.5625\" y=\"25.607337\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(7.2 29.406556) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 21.37625 13.5 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 27.0725 77.489008 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 27.0725 77.489008 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 27.0725 77.489008 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_127\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_128\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_129\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_130\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_131\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_132\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_133\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_134\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_135\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_136\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_137\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_138\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_139\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_140\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_141\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_142\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_143\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_144\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_145\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_146\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_147\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_148\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_149\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_150\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_151\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_152\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_153\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_154\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_155\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_156\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_157\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_158\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_159\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_160\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_161\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_162\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_163\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_164\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_165\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_166\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_167\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "L 190.63625 138.537688 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_168\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_169\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "L 190.63625 138.537688 \n", "L 193.89125 138.695938 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_170\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_171\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "L 190.63625 138.537688 \n", "L 193.89125 138.695938 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_172\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "L 196.3325 80.279205 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_173\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "L 190.63625 138.537688 \n", "L 193.89125 138.695938 \n", "L 197.14625 139.336668 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_174\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "L 196.3325 80.279205 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_175\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "L 190.63625 138.537688 \n", "L 193.89125 138.695938 \n", "L 197.14625 139.336668 \n", "L 200.40125 138.495404 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_176\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "L 196.3325 80.279205 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_177\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "L 190.63625 138.537688 \n", "L 193.89125 138.695938 \n", "L 197.14625 139.336668 \n", "L 200.40125 138.495404 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_178\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "L 196.3325 80.279205 \n", "L 202.8425 79.677125 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_179\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "L 190.63625 138.537688 \n", "L 193.89125 138.695938 \n", "L 197.14625 139.336668 \n", "L 200.40125 138.495404 \n", "L 203.65625 138.82811 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_180\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "L 196.3325 80.279205 \n", "L 202.8425 79.677125 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_181\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "L 190.63625 138.537688 \n", "L 193.89125 138.695938 \n", "L 197.14625 139.336668 \n", "L 200.40125 138.495404 \n", "L 203.65625 138.82811 \n", "L 206.91125 139.26519 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_182\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "L 196.3325 80.279205 \n", "L 202.8425 79.677125 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_183\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "L 190.63625 138.537688 \n", "L 193.89125 138.695938 \n", "L 197.14625 139.336668 \n", "L 200.40125 138.495404 \n", "L 203.65625 138.82811 \n", "L 206.91125 139.26519 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_184\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "L 196.3325 80.279205 \n", "L 202.8425 79.677125 \n", "L 209.3525 77.293704 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_185\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "L 190.63625 138.537688 \n", "L 193.89125 138.695938 \n", "L 197.14625 139.336668 \n", "L 200.40125 138.495404 \n", "L 203.65625 138.82811 \n", "L 206.91125 139.26519 \n", "L 210.16625 139.5 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_186\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "L 196.3325 80.279205 \n", "L 202.8425 79.677125 \n", "L 209.3525 77.293704 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_187\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "L 190.63625 138.537688 \n", "L 193.89125 138.695938 \n", "L 197.14625 139.336668 \n", "L 200.40125 138.495404 \n", "L 203.65625 138.82811 \n", "L 206.91125 139.26519 \n", "L 210.16625 139.5 \n", "L 213.42125 139.317062 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_188\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "L 196.3325 80.279205 \n", "L 202.8425 79.677125 \n", "L 209.3525 77.293704 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_189\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 72.946469 \n", "L 27.88625 89.862825 \n", "L 31.14125 98.350914 \n", "L 34.39625 107.166185 \n", "L 37.65125 107.938681 \n", "L 40.90625 111.018789 \n", "L 44.16125 114.452242 \n", "L 47.41625 116.189731 \n", "L 50.67125 116.083899 \n", "L 53.92625 118.574539 \n", "L 57.18125 119.266334 \n", "L 60.43625 120.032849 \n", "L 63.69125 122.071428 \n", "L 66.94625 121.316795 \n", "L 70.20125 124.088605 \n", "L 73.45625 124.861227 \n", "L 76.71125 124.006927 \n", "L 79.96625 125.235276 \n", "L 83.22125 126.863911 \n", "L 86.47625 127.105169 \n", "L 89.73125 127.724731 \n", "L 92.98625 128.676729 \n", "L 96.24125 128.650869 \n", "L 99.49625 129.72733 \n", "L 102.75125 130.321571 \n", "L 106.00625 130.64519 \n", "L 109.26125 131.366171 \n", "L 112.51625 131.187097 \n", "L 115.77125 133.220775 \n", "L 119.02625 132.976933 \n", "L 122.28125 133.411363 \n", "L 125.53625 132.949278 \n", "L 128.79125 134.419028 \n", "L 132.04625 135.032608 \n", "L 135.30125 134.462048 \n", "L 138.55625 135.194104 \n", "L 141.81125 135.202135 \n", "L 145.06625 135.467182 \n", "L 148.32125 136.771018 \n", "L 151.57625 136.192879 \n", "L 154.83125 136.65872 \n", "L 158.08625 137.251249 \n", "L 161.34125 136.97061 \n", "L 164.59625 137.660115 \n", "L 167.85125 137.382518 \n", "L 171.10625 137.514347 \n", "L 174.36125 138.150042 \n", "L 177.61625 138.130096 \n", "L 180.87125 137.926251 \n", "L 184.12625 138.488876 \n", "L 187.38125 138.549433 \n", "L 190.63625 138.537688 \n", "L 193.89125 138.695938 \n", "L 197.14625 139.336668 \n", "L 200.40125 138.495404 \n", "L 203.65625 138.82811 \n", "L 206.91125 139.26519 \n", "L 210.16625 139.5 \n", "L 213.42125 139.317062 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_190\">\n", "    <path d=\"M 27.0725 77.489008 \n", "L 33.5825 77.60713 \n", "L 40.0925 81.462584 \n", "L 46.6025 80.852912 \n", "L 53.1125 78.051682 \n", "L 59.6225 79.640124 \n", "L 66.1325 69.407908 \n", "L 72.6425 76.867728 \n", "L 79.1525 74.983464 \n", "L 85.6625 78.873862 \n", "L 92.1725 80.590393 \n", "L 98.6825 83.162896 \n", "L 105.1925 84.465651 \n", "L 111.7025 86.942672 \n", "L 118.2125 90.905083 \n", "L 124.7225 80.544848 \n", "L 131.2325 86.350024 \n", "L 137.7425 87.109868 \n", "L 144.2525 84.422922 \n", "L 150.7625 85.182442 \n", "L 157.2725 88.54004 \n", "L 163.7825 85.834758 \n", "L 170.2925 85.230305 \n", "L 176.8025 78.694197 \n", "L 183.3125 81.173187 \n", "L 189.8225 78.262204 \n", "L 196.3325 80.279205 \n", "L 202.8425 79.677125 \n", "L 209.3525 77.293704 \n", "L 215.8625 82.446885 \n", "\" clip-path=\"url(#p4d9432a56e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 20.5625 145.8 \n", "L 20.5625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 215.8625 145.8 \n", "L 215.8625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 20.5625 145.8 \n", "L 215.8625 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 20.5625 7.2 \n", "L 215.8625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 129.271875 45.1125 \n", "L 208.8625 45.1125 \n", "Q 210.8625 45.1125 210.8625 43.1125 \n", "L 210.8625 14.2 \n", "Q 210.8625 12.2 208.8625 12.2 \n", "L 129.271875 12.2 \n", "Q 127.271875 12.2 127.271875 14.2 \n", "L 127.271875 43.1125 \n", "Q 127.271875 45.1125 129.271875 45.1125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_191\">\n", "     <path d=\"M 131.271875 20.298438 \n", "L 141.271875 20.298438 \n", "L 151.271875 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(159.271875 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_192\">\n", "     <path d=\"M 131.271875 35.254688 \n", "L 141.271875 35.254688 \n", "L 151.271875 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(159.271875 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p4d9432a56e\">\n", "   <rect x=\"20.5625\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = d2l.MTFraEng(batch_size=128)\n", "num_hiddens, num_blks, dropout = 256, 2, 0.2\n", "ffn_num_hiddens, num_heads = 64, 4\n", "encoder = TransformerEncoder(\n", "    len(data.src_vocab), num_hiddens, ffn_num_hiddens, num_heads,\n", "    num_blks, dropout)\n", "decoder = TransformerDecoder(\n", "    len(data.tgt_vocab), num_hiddens, ffn_num_hiddens, num_heads,\n", "    num_blks, dropout)\n", "model = d2l.Seq2Seq(encoder, decoder, tgt_pad=data.tgt_vocab['<pad>'],\n", "                    lr=0.001)\n", "trainer = d2l.Trainer(max_epochs=30, gradient_clip_val=1, num_gpus=1)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "ba393cdc", "metadata": {"origin_pos": 68}, "source": ["After training,\n", "we use the Transformer model\n", "to [**translate a few English sentences**] into French and compute their BLEU scores.\n"]}, {"cell_type": "code", "execution_count": 15, "id": "06e5e238", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:42.765441Z", "iopub.status.busy": "2023-08-18T19:50:42.764512Z", "iopub.status.idle": "2023-08-18T19:50:42.852261Z", "shell.execute_reply": "2023-08-18T19:50:42.850805Z"}, "origin_pos": 69, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["go . => ['va', '!'], bleu,1.000\n", "i lost . => [\"j'ai\", 'perdu', '.'], bleu,1.000\n", "he's calm . => ['<unk>', '!'], bleu,0.000\n", "i'm home . => ['je', 'suis', 'chez', 'moi', '.'], bleu,1.000\n"]}], "source": ["engs = ['go .', 'i lost .', 'he\\'s calm .', 'i\\'m home .']\n", "fras = ['va !', 'j\\'ai perdu .', 'il est calme .', 'je suis chez moi .']\n", "preds, _ = model.predict_step(\n", "    data.build(engs, fras), d2l.try_gpu(), data.num_steps)\n", "for en, fr, p in zip(engs, fras, preds):\n", "    translation = []\n", "    for token in data.tgt_vocab.to_tokens(p):\n", "        if token == '<eos>':\n", "            break\n", "        translation.append(token)\n", "    print(f'{en} => {translation}, bleu,'\n", "          f'{d2l.bleu(\" \".join(translation), fr, k=2):.3f}')"]}, {"cell_type": "markdown", "id": "8ed88841", "metadata": {"origin_pos": 70}, "source": ["Let's [**visualize the Transformer attention weights**] when translating the final English sentence into French.\n", "The shape of the encoder self-attention weights\n", "is (number of encoder layers, number of attention heads, `num_steps` or number of queries, `num_steps` or number of key-value pairs).\n"]}, {"cell_type": "code", "execution_count": 16, "id": "e948e78a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:42.856734Z", "iopub.status.busy": "2023-08-18T19:50:42.856107Z", "iopub.status.idle": "2023-08-18T19:50:42.926580Z", "shell.execute_reply": "2023-08-18T19:50:42.925245Z"}, "origin_pos": 71, "tab": ["pytorch"]}, "outputs": [], "source": ["_, dec_attention_weights = model.predict_step(\n", "    data.build([engs[-1]], [fras[-1]]), d2l.try_gpu(), data.num_steps, True)\n", "enc_attention_weights = torch.cat(model.encoder.attention_weights, 0)\n", "shape = (num_blks, num_heads, -1, data.num_steps)\n", "enc_attention_weights = enc_attention_weights.reshape(shape)\n", "d2l.check_shape(enc_attention_weights,\n", "                (num_blks, num_heads, data.num_steps, data.num_steps))"]}, {"cell_type": "markdown", "id": "d068fa43", "metadata": {"origin_pos": 73}, "source": ["In the encoder self-attention,\n", "both queries and keys come from the same input sequence.\n", "Since padding tokens do not carry meaning,\n", "with specified valid length of the input sequence\n", "no query attends to positions of padding tokens.\n", "In the following,\n", "two layers of multi-head attention weights\n", "are presented row by row.\n", "Each head independently attends\n", "based on a separate representation subspace of queries, keys, and values.\n"]}, {"cell_type": "code", "execution_count": 17, "id": "520c51a5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:42.931273Z", "iopub.status.busy": "2023-08-18T19:50:42.930241Z", "iopub.status.idle": "2023-08-18T19:50:44.499507Z", "shell.execute_reply": "2023-08-18T19:50:44.498627Z"}, "origin_pos": 75, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"402.17495pt\" height=\"233.64481pt\" viewBox=\"0 0 402.17495 233.64481\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-20T08:43:41.739054</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 233.64481 \n", "L 402.17495 233.64481 \n", "L 402.17495 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "L 34.240625 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p68ca82c1ba)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABHUlEQVR4nO3dwQ2CQBBAUTHebMECLckCLcALZ2xhSdj8KO+dCZCfuWxIhmVbP9vlYM/7Y/ja1/o++vE/41q/wJmJHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPihZcYK9z1G173/46p3kx8SPyR+SPyQ+CHxQ+KHxA+JHxI/dJtxU3+IG2PyQ+KHxA+JHxI/JH5I/JD4IfFDu064Z/7YPYPJD4kfEj8kfkj8kPgh8UPih8QPiR/6AlZDFIcTsr8vAAAAAElFTkSuQmCC\" id=\"imagee8a15604a0\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"34.240625\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m1227f17e34\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"38.014538\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"75.753668\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"m3cae57cddc\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"34.240625\" y=\"26.092038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 29.891257) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"34.240625\" y=\"63.831168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 67.630387) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 95.477874) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-51\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 3406 84 \n", "L 4238 -825 \n", "L 3475 -825 \n", "L 2784 -78 \n", "Q 2681 -84 2626 -87 \n", "Q 2572 -91 2522 -91 \n", "Q 1538 -91 948 567 \n", "Q 359 1225 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1516 4351 937 \n", "Q 4025 359 3406 84 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 34.240625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.240625 22.318125 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-48\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2753 \n", "L 3553 2753 \n", "L 3553 4666 \n", "L 4184 4666 \n", "L 4184 0 \n", "L 3553 0 \n", "L 3553 2222 \n", "L 1259 2222 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "L 115.757147 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p27085bcb2d)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABHElEQVR4nO3csQ3CMBBA0ZgWRmA/GIkBGYAmdVghKcyTyH+1C+vrKsu6sa2fbYGe1/uuc6/1Pfkmv3fRFziz4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPjcdy273g7h+XzElNPlR8qPhQ8aHiQ8WHig8VHyo+NI6scD/zuvUZmnyo+FDxoeJDxYeKDxUfKj5UfKj40KHnhRnO/GTR5EPFh4oPFR8qPlR8qPhQ8aHiQ30Rh5p8qPhQ8aHiQ8WHig8VHyo+VHyo+NAXj2YdKfeaeuUAAAAASUVORK5CYII=\" id=\"imagecc81e318c6\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"115.757147\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"119.53106\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"157.27019\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"115.757147\" y=\"26.092038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"115.757147\" y=\"63.831168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 115.757147 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 115.757147 22.318125 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "L 197.273668 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p53bd3f8fc8)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABGElEQVR4nO3dwQnCMBiA0Va86QjupyM5oAN46bmukELgIf3eOZDwkWP4s+7bd18me90ew2vf22f29n/jog9wZsWHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VH7oeWTw6uO7MQ+uO6OZDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfGh9LvfhP9B7+j1XNx8qPlR8qPhQ8aHiQ8WHig8VHyo+9AO8lg6FsQ5kyQAAAABJRU5ErkJggg==\" id=\"image33713cb98a\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"197.273668\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"201.047582\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"238.786712\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"197.273668\" y=\"26.092038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"197.273668\" y=\"63.831168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 197.273668 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 197.273668 22.318125 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "L 278.79019 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pa4dfd2d2b4)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABIElEQVR4nO3csRHCMBAAQZkUSqA/KIkCKYDEMbQgB2YD3cYONDefvMaj7bt/vgN6Xu9T373298kn+b+LPsDKig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+tB154G7lx+jO0ORDxYeKDxUfKj5UfKj4UPGh4kOHNtxZs5vwGGtvw00+VHyo+FDxoeJDxYeKDxUfKj5UfGh7jNv09cLKVwFnaPKh4kPFh4oPFR8qPlR8qPhQ8aF+EYeafKj4UPGh4kPFh4oPFR8qPlR8qPjQD3HHICmmrc+4AAAAAElFTkSuQmCC\" id=\"imaged306d43cca\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"278.79019\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"282.564103\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"320.303234\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"278.79019\" y=\"26.092038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"278.79019\" y=\"63.831168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 278.79019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 278.79019 22.318125 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 102.17106 196.08856 \n", "L 102.17106 128.158125 \n", "L 34.240625 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p7e4941147d)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABqklEQVR4nO3aMUoDQRhH8YwuCIIQLC2srSzsrGxyCz2Adt7B2trK0kLv4DGCoiAYQxohIFpJmPUEwj/C8tj4fvXHMDy+ZrIpdTJuB6H7g1E0N3p9SI8ctPNZNFe2d+Iz+2KNvsB/ZnyQ8UHGBxkfZHyQ8UHGBxkfZHxQMyiFvUEb/7qxctx8kPFBxgcZH2R8kPFBxgcZH2R8kPFBxgcZH2R8kPFBxgcZH2R8kPFBxgcZH9QsM9zJp276Az7IzQcZH2R8kPFBxgcZH2R8kPFBxgct98Lt5onbxaG94OaDjA8yPsj4IOODjA8yPsj4IOODjA8q9eM9/tGgnYyjuXp3nd9gOIzGmtOL/MyecPNBxgcZH2R8kPFBxgcZH2R8kPFBpX7O4xfu2dZuNHf19fbnC/2mrOBfyd18kPFBxgcZH2R8kPFBxgcZH2R8kPFBZXF7mX9Af3mK5tZPzuML1Gl45v5RfGZfuPkg44OMDzI+yPgg44OMDzI+yPigUmfP8Qv3Zu8wmjuePuY3WHxHY2VjMz+zJ9x8kPFBxgcZH2R8kPFBxgcZH2R8kPFBPwTCMy9YDX5tAAAAAElFTkSuQmCC\" id=\"imagefc0d53928d\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"34.240625\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_9\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"38.014538\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(34.833288 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"75.753668\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(72.572418 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(35.142561 224.365122) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4b\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2694 \n", "L 3353 4666 \n", "L 4166 4666 \n", "L 1850 2491 \n", "L 4331 0 \n", "L 3500 0 \n", "L 1259 2247 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"34.240625\" y=\"131.932038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 135.731257) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"34.240625\" y=\"169.671168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 173.470387) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 201.317874) rotate(-90) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 34.240625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 102.17106 196.08856 \n", "L 102.17106 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 102.17106 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 34.240625 128.158125 \n", "L 102.17106 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 122.158125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 183.687582 196.08856 \n", "L 183.687582 128.158125 \n", "L 115.757147 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pb8a5c3a8ff)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABpElEQVR4nO3boUoEURhH8XuXEWSDIIiaFGybxWqwa/AFxCYsWC2y1RdYVLBbzNtM+wY+gkmM9pXxFf4jyGGc88sfl8vhKzszW1fLl7aEpseX0dzDx1t6ZCnjjWisNmv5mT0xoi8wZMYHGR9kfJDxQcYHGR9kfJDxQcYHNV2Ga6l/dY9BcvNBxgcZH2R8kPFBxgcZH2R8kPFBTan5r9a2pO/a43fyHWf/FzcfZHyQ8UHGBxkfZHyQ8UHGBxkfZHyQ8UHGBxkfZHyQ8UHGBxkfZHyQ8UHGB3X6RDznp+QJNx9kfJDxQcYHGR9kfJDxQcYHGR9kfFCzOJvGw/PZeTa4Pu5wheE+inDzQcYHGR9kfJDxQcYHGR9kfJDxQXV1fxP/Bfz2+jGau/t6//WFhsTNBxkfZHyQ8UHGBxkfZHyQ8UHGBxkf1JS9g3j4arITzX2/Psdnjg5Porm6uRuf2RduPsj4IOODjA8yPsj4IOODjA8yPqh+Hk3iF+hbT/Norl0u8gucXkRzo+39+My+cPNBxgcZH2R8kPFBxgcZH2R8kPFBxgf9AF3/ID2pOP1GAAAAAElFTkSuQmCC\" id=\"imageb5ae9e89b2\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"115.757147\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_11\">\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"119.53106\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(116.34981 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"157.27019\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(154.08894 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(116.659083 224.365122) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_12\">\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"115.757147\" y=\"131.932038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"115.757147\" y=\"169.671168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 115.757147 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 183.687582 196.08856 \n", "L 183.687582 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 183.687582 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 115.757147 128.158125 \n", "L 183.687582 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 122.158125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 265.204103 196.08856 \n", "L 265.204103 128.158125 \n", "L 197.273668 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p4bc8f6fb8d)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABhUlEQVR4nO3dMUoDQQBG4RnZGKIxBMEqvXgAL2LlAXKuHCBH8Aw5gmnshBRRMAhidr3Cv4U8Nnlf/TMMj6kCS2r7uetKaLV4iHbL9216ZClf+2hWb27zMwfigr7AOTM+yPgg44OMDzI+yPgg44OMDzI+qOkzbv/rFmfKlw8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOODjA9qSpd/V343Cj9Y//nOb9Ae8+2J8eWDjA8yPsj4IOODjA8yPsj4IOODmjoax+OX/SHaPY2v8hv02Z4YXz7I+CDjg4wPMj7I+CDjg4wPMj7I+KCmO/7G48dp9lNEd/jIb3A5iWZ9fgYZCl8+yPgg44OMDzI+yPgg44OMDzI+qLa7t/g/0MtkFs3Wi/v4yOfXTbSr1/P4zKHw5YOMDzI+yPgg44OMDzI+yPgg44OMD/oDBGQg7AnJydcAAAAASUVORK5CYII=\" id=\"imageef607210a4\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"197.273668\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_13\">\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"201.047582\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(197.866332 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"238.786712\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(235.605462 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(198.175605 224.365122) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_14\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"197.273668\" y=\"131.932038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"197.273668\" y=\"169.671168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 197.273668 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 265.204103 196.08856 \n", "L 265.204103 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 265.204103 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 197.273668 128.158125 \n", "L 265.204103 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_22\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 122.158125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 346.720625 196.08856 \n", "L 346.720625 128.158125 \n", "L 278.79019 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p7166cf768e)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABq0lEQVR4nO3aoUpEURRG4XP0imabgsEwT6DNKNhNavAFjBazr2CyGXwBjTaTySImQTEoVkEcMczM9RX+EYbF5a4vbzaHxS7D3Dq6uWhL6Hr/JJrbfXtKV5YyHkVjtVnId3bEHP2APjM+yPgg44OMDzI+yPgg44OMDzI+qJlmuJY6q3f0kpcPMj7I+CDjg4wPMj7I+CDjg4wPako7od/QW14+yPgg44OMDzI+yPgg44OMDzI+yPigptS8f1viT/kV8PJBxgcZH2R8kPFBxgcZH2R8kPFBU30iPhO1v5+de/kg44OMDzI+yPgg44OMDzI+yPgg44Pq6Ow4/1d8cSlburMXr2wf76K5+e2DeGdXePkg44OMDzI+yPgg44OMDzI+yPigOn59iH/hHq1vRXPnw/d/P6hPvHyQ8UHGBxkfZHyQ8UHGBxkfZHyQ8UFNe3sVD59urkVz7e8wf8HPVzRWl1fynR3h5YOMDzI+yPgg44OMDzI+yPgg44Pq5Psz/gP9cnUQzR0+38cPaD9eorm5wUa8syu8fJDxQcYHGR9kfJDxQcYHGR9kfJDxQX+yAC2UOqHxUQAAAABJRU5ErkJggg==\" id=\"imageb4f6ebcb97\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"278.79019\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_15\">\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"282.564103\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(279.382853 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m1227f17e34\" x=\"320.303234\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(317.121984 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(279.692126 224.365122) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_16\">\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"278.79019\" y=\"131.932038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m3cae57cddc\" x=\"278.79019\" y=\"169.671168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 278.79019 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 346.720625 196.08856 \n", "L 346.720625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 346.720625 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 278.79019 128.158125 \n", "L 346.720625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_26\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 122.158125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 366.250625 167.415342 \n", "L 372.071825 167.415342 \n", "L 372.071825 50.991342 \n", "L 366.250625 50.991342 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAgAAACiCAYAAAB8iIwDAAAA7klEQVR4nN2XSw4DIQxDUyn3P2s33ZWkN8iLZGCgsx1kP8cwn1d+3mnF5ZZR3Te3LAU6CkELjCyCFrACxoQFqc9BV5gQExkOiBmDFsgKBJkbGBiSLIYakxnYgg6OHpMnKcc8YMvxqOUutkzy+S13xbmYADkh5g2QqCC/9fKEmFd0QZA58BGEXehtygx3xFw/h7/oAp7E5oEMFBMVBluU980D60YFYgg5Jk9S7mIHpNwFODRSgEBDARjNk34noatOTLRYr4CTHHoXBNnogixkSIxZfxx0Rv2VIVGhwSBb1AJzFGQLUsC6dUjck+u7+AE+P2IIsbjxbAAAAABJRU5ErkJggg==\" id=\"image2e2712b9e4\" transform=\"scale(1 -1) translate(0 -116.64)\" x=\"366.48\" y=\"-50.4\" width=\"5.76\" height=\"116.64\"/>\n", "   <g id=\"matplotlib.axis_17\"/>\n", "   <g id=\"matplotlib.axis_18\">\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_33\">\n", "      <defs>\n", "       <path id=\"mc4c9197732\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc4c9197732\" x=\"372.071825\" y=\"167.415342\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(379.071825 171.214561) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#mc4c9197732\" x=\"372.071825\" y=\"141.145341\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(379.071825 144.94456) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#mc4c9197732\" x=\"372.071825\" y=\"114.87534\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(379.071825 118.674559) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#mc4c9197732\" x=\"372.071825\" y=\"88.605339\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(379.071825 92.404558) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#mc4c9197732\" x=\"372.071825\" y=\"62.335338\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_31\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(379.071825 66.134557) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 366.250625 167.415342 \n", "L 369.161225 167.415342 \n", "L 372.071825 167.415342 \n", "L 372.071825 50.991342 \n", "L 369.161225 50.991342 \n", "L 366.250625 50.991342 \n", "L 366.250625 167.415342 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p68ca82c1ba\">\n", "   <rect x=\"34.240625\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p27085bcb2d\">\n", "   <rect x=\"115.757147\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p53bd3f8fc8\">\n", "   <rect x=\"197.273668\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pa4dfd2d2b4\">\n", "   <rect x=\"278.79019\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p7e4941147d\">\n", "   <rect x=\"34.240625\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pb8a5c3a8ff\">\n", "   <rect x=\"115.757147\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p4bc8f6fb8d\">\n", "   <rect x=\"197.273668\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p7166cf768e\">\n", "   <rect x=\"278.79019\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 700x350 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.show_heatmaps(\n", "    enc_attention_weights.cpu(), xlabel='Key positions',\n", "    ylabel='Query positions', titles=['Head %d' % i for i in range(1, 5)],\n", "    figsize=(7, 3.5))"]}, {"cell_type": "markdown", "id": "89466194", "metadata": {"origin_pos": 76}, "source": ["[**To visualize the decoder self-attention weights and the encoder--decoder attention weights,\n", "we need more data manipulations.**]\n", "For example,\n", "we fill the masked attention weights with zero.\n", "Note that\n", "the decoder self-attention weights\n", "and the encoder--decoder attention weights\n", "both have the same queries:\n", "the beginning-of-sequence token followed by\n", "the output tokens and possibly\n", "end-of-sequence tokens.\n"]}, {"cell_type": "code", "execution_count": 18, "id": "41d25d85", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:44.505810Z", "iopub.status.busy": "2023-08-18T19:50:44.505240Z", "iopub.status.idle": "2023-08-18T19:50:44.519144Z", "shell.execute_reply": "2023-08-18T19:50:44.517957Z"}, "origin_pos": 78, "tab": ["pytorch"]}, "outputs": [], "source": ["dec_attention_weights_2d = [head[0].tolist()\n", "                            for step in dec_attention_weights\n", "                            for attn in step for blk in attn for head in blk]\n", "dec_attention_weights_filled = torch.tensor(\n", "    pd.DataFrame(dec_attention_weights_2d).fillna(0.0).values)\n", "shape = (-1, 2, num_blks, num_heads, data.num_steps)\n", "dec_attention_weights = dec_attention_weights_filled.reshape(shape)\n", "dec_self_attention_weights, dec_inter_attention_weights = \\\n", "    dec_attention_weights.permute(1, 2, 3, 0, 4)"]}, {"cell_type": "code", "execution_count": 19, "id": "2ddd5159", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:44.523889Z", "iopub.status.busy": "2023-08-18T19:50:44.523032Z", "iopub.status.idle": "2023-08-18T19:50:44.528387Z", "shell.execute_reply": "2023-08-18T19:50:44.527176Z"}, "origin_pos": 81, "tab": ["pytorch"]}, "outputs": [], "source": ["d2l.check_shape(dec_self_attention_weights,\n", "                (num_blks, num_heads, data.num_steps, data.num_steps))\n", "d2l.check_shape(dec_inter_attention_weights,\n", "                (num_blks, num_heads, data.num_steps, data.num_steps))"]}, {"cell_type": "markdown", "id": "544463d1", "metadata": {"origin_pos": 82}, "source": ["Because of the autoregressive property of the decoder self-attention,\n", "no query attends to key--value pairs after the query position.\n"]}, {"cell_type": "code", "execution_count": 20, "id": "8430c053", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:44.533271Z", "iopub.status.busy": "2023-08-18T19:50:44.532389Z", "iopub.status.idle": "2023-08-18T19:50:45.954406Z", "shell.execute_reply": "2023-08-18T19:50:45.953261Z"}, "origin_pos": 83, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"402.17495pt\" height=\"233.64481pt\" viewBox=\"0 0 402.17495 233.64481\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-20T08:43:42.157875</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 233.64481 \n", "L 402.17495 233.64481 \n", "L 402.17495 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "L 34.240625 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pca6246c906)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABHklEQVR4nO3csQ3CQBAAQZsUSqA/KIkCKYDEsekA2cFrhL0TW/JrdcnrpZvX5bNOB/O83jd991reg0/y24X+/eSKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+ND+m2+YHdP3gfDRNPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig/NesHdvyyjG6HJh4oPFR8qPlR8qPhQ8aHiQ8WHdt1wz3wbHaHJh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj7UCneoyYeKDxUfKj5UfKj4UPGh4kPFh4oPfQGzaR0pJ0WalwAAAABJRU5ErkJggg==\" id=\"image800603a756\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"34.240625\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m9c381af5ee\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"38.014538\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"75.753668\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"m601a9d1f28\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"34.240625\" y=\"26.092038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 29.891257) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"34.240625\" y=\"63.831168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 67.630387) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 95.477874) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-51\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 3406 84 \n", "L 4238 -825 \n", "L 3475 -825 \n", "L 2784 -78 \n", "Q 2681 -84 2626 -87 \n", "Q 2572 -91 2522 -91 \n", "Q 1538 -91 948 567 \n", "Q 359 1225 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1516 4351 937 \n", "Q 4025 359 3406 84 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 34.240625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.240625 22.318125 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-48\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2753 \n", "L 3553 2753 \n", "L 3553 4666 \n", "L 4184 4666 \n", "L 4184 0 \n", "L 3553 0 \n", "L 3553 2222 \n", "L 1259 2222 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "L 115.757147 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p64b6c50073)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABGUlEQVR4nO3buw0CMRBAQR8plEB/UBIFUgDJxUcLm6ARujexA+tpI3+2x7oda+i1v6dLM3DRGziz4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8aDv2z/h9/r94Xu+jdfq/QZMPFR8qPlR8qPhQ8aHiQ8WHig9t/UB3mnyo+FDxoeJDxYeKDxUfKj5UfKj40E8u0KcX2Gud+8iiyYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+1Pt8qMmHig8VHyo+VHyo+FDxoeJDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPvQFdwIaKfRVlW8AAAAASUVORK5CYII=\" id=\"image6bd423f4f7\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"115.757147\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"119.53106\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"157.27019\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"115.757147\" y=\"26.092038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"115.757147\" y=\"63.831168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 115.757147 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 115.757147 22.318125 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "L 197.273668 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p3c596bb68b)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABIUlEQVR4nO3bsQ3CQBAAQZsUSqAxKoACKIYCKYDEEYFdAo9ktAEzsfXB6pK37ud1ea0TH73vl93PPOx+IsPED4kfEj8kfkj8kPgh8UPih8QPzX4vjLkdz0PfPZbn8JkmPyR+SPyQ+CHxQ+KHxA+JHxI/9NUN9xe3vH9m8kPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFD4ofED4kfEj8kfkj8kPgh8UPih8QPiR8SPyR+SPzQfJ1Ow/v59u73ZfJD4ofED4kfEj8kfkj8kPgh8UNeoIdMfkj8kPgh8UPih8QPiR8SPyR+SPyQ+CHxQ+KHxA+JHxI/JH5I/JD4IfFDVsRDJj8kfkj8kPgh8UPih8QPiR8SPyR+aAPM+SLTxW0PdQAAAABJRU5ErkJggg==\" id=\"imageb300a2dcb0\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"197.273668\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"201.047582\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"238.786712\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"197.273668\" y=\"26.092038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"197.273668\" y=\"63.831168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 197.273668 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 197.273668 22.318125 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "L 278.79019 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p2371c7e257)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABLUlEQVR4nO3bsQ2CQBiAUTB2OoL76UgO6AA21LjCUZAX5Hs1IZcvf8MdN6/Ld50GvW6Poefey2f0lad20Qs4s+JDxYeKDxUfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig/NWw7Q/83oDwHTtM9PAU0+VHyo+FDxoeJDxYeKDxUfKj5UfOiqF7CHo9wjaPKh4kPFh4oPFR8qPlR8qPhQ8aFNB+hH+XI8iiYfKj5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+NAuN9D1ze6jaPKh4kPFh4oPFR8qPlR8qPhQ8aHiQ/Nzug9vL5x5K2APTT5UfKj4UPGh4kPFh4oPFR8qPlR8qPhQ8aHiQ8WHig8VHyo+VHyo+FDxoR9XmSMrmc7UfAAAAABJRU5ErkJggg==\" id=\"imagef66ae230ac\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"278.79019\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"282.564103\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"320.303234\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"278.79019\" y=\"26.092038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"278.79019\" y=\"63.831168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 278.79019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 278.79019 22.318125 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 102.17106 196.08856 \n", "L 102.17106 128.158125 \n", "L 34.240625 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p9e7d60a145)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABxklEQVR4nO3cMUoDURRG4bwYK20FLQSxsRF34AYsBAsrsRAEySKEuArtrdyBhbsQQbAJxkYQTJGIqJm4A/kDCYc456svL4/DbWYGUsbD/rhRU+Ofr3h2dH469d9vTv1ExYwPMj7I+CDjg4wPMj7I+CDjg4wPKrV+vfA5iGc7q9vR3EW/G5/p5oOMDzI+yPgg44OMDzI+yPgg44NKNXiPn3BLKVO/wLiqst9u5ntSde+judvdw/jMvd5jPJty80HGBxkfZHyQ8UHGBxkfZHyQ8UHGB/3LD+jtpfVo7nLYm/FN/ubmg4wPMj7I+CDjg4wPMj7I+CDjg0rVf82fcKtRNHa9sRMfefz8kA2WfE9KazGeJbn5IOODjA8yPsj4IOODjA8yPsj4IOODyvfFSfx6obl/lB26thlfoLq7ieYWDtrxmfPCzQcZH2R8kPFBxgcZH2R8kPFBxgdN9AG9s7KVzb095ReYk4/ds+Dmg4wPMj7I+CDjg4wPMj7I+CDjg4wPak0y/JH+GV2NXxlMws0HGR9kfJDxQcYHGR9kfJDxQcYHlbPGcvwB/Wr4Msu71I6bDzI+yPgg44OMDzI+yPgg44OMDzI+6BdlQUSgsDwECwAAAABJRU5ErkJggg==\" id=\"image3550bdfbe2\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"34.240625\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_9\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"38.014538\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(34.833288 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"75.753668\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(72.572418 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(35.142561 224.365122) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4b\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2694 \n", "L 3353 4666 \n", "L 4166 4666 \n", "L 1850 2491 \n", "L 4331 0 \n", "L 3500 0 \n", "L 1259 2247 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"34.240625\" y=\"131.932038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 135.731257) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"34.240625\" y=\"169.671168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 173.470387) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 201.317874) rotate(-90) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 34.240625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 102.17106 196.08856 \n", "L 102.17106 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 102.17106 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 34.240625 128.158125 \n", "L 102.17106 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 122.158125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 183.687582 196.08856 \n", "L 183.687582 128.158125 \n", "L 115.757147 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p4953e6d1b5)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAAB60lEQVR4nO3ZQWpTURhH8dzk1RZR6qwDKQ6riEgnXUEnAUeuwFknrqDQDkqW0EBxEy5GUBAHQgNCQVMwIRqT97qCwj8h4SA5v/HHx+VwJ+++0oxvmxaomU2juVI9yHeOhtHc/PI83lmO38SzqfbKNypmfJDxQcYHGR9kfJDxQcYHGR9kfFBFH6BV16vfOfsXjf399C1eufP6RzTX6b6Ld3rzQcYHGR9kfJDxQcYHGR9kfJDxQQX/gT6fRXOlk3+MN9NJNNfbexHvPBt+j2dT3nyQ8UHGBxkfZHyQ8UHGBxkfZHyQ8UFV0+SvC6WUaK6Z/ln2PPfvXOCc/acvo7l1PBkswpsPMj7I+CDjg4wPMj7I+CDjg4wPKvVouPIf6J9fHcazzz9cRHPto268s1Rb8SzJmw8yPsj4IOODjA8yPsj4IOODjA8yPqjUPwf588JklM092YtXftw/iObeDr7GO/8X3nyQ8UHGBxkfZHyQ8UHGBxkfZHxQOa124y/c3s2XbOn2w6UPtEm8+SDjg4wPMj7I+CDjg4wPMj7I+CDjg0r9+1f8vPD+8bNorj++XvpAm8SbDzI+yPgg44OMDzI+yPgg44OMDyonrUfxF+7VeLDOs2wcbz7I+CDjg4wPMj7I+CDjg4wPMj7I+KA7X4dM7WnJ8H8AAAAASUVORK5CYII=\" id=\"imagec677646754\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"115.757147\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_11\">\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"119.53106\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(116.34981 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"157.27019\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(154.08894 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(116.659083 224.365122) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_12\">\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"115.757147\" y=\"131.932038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"115.757147\" y=\"169.671168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 115.757147 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 183.687582 196.08856 \n", "L 183.687582 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 183.687582 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 115.757147 128.158125 \n", "L 183.687582 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 122.158125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 265.204103 196.08856 \n", "L 265.204103 128.158125 \n", "L 197.273668 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pf03e82c1be)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABy0lEQVR4nO3aPUoDURRA4ffiQPAvIElnYSNBbN2BZBE2gp3YWbiNlIKt+1CxTGlqEUGtBBGJoklwdAs3knAYc7768jIcbjPzksv3158UlHOOjlbC5cZ2eHb35mrqv1+b+okKMz7I+CDjg4wPMj7I+CDjg4wPMj6ooB+A9DgcxYfL79BYbq6Hj3TzQcYHGR9kfJDxQcYHGR9kfJDxQcV/uxSfRG/wFZ49mODNNcrNBxkfZHyQ8UHGBxkfZHyQ8UHGBxkflM8brfD/8/fv+8HJ8JEpFfXY3EL8rr8qn0zcfJDxQcYHGR9kfJDxQcYHGR9kfFAuBy/x19HxMDR20d4JH9npX8cG60vhM/PianiW5OaDjA8yPsj4IOODjA8yPsj4IOODjA8qxsd74eHaVjs017mLXrSnlEafobGqfDKYhJsPMj7I+CDjg4wPMj7I+CDjg4wPyqfLzfAF+tFtLzbYaMUfoCJ/554FNx9kfJDxQcYHGR9kfJDxQcYHGR9kfFAu357DnxdO1jZDc93Bw58faJ64+SDjg4wPMj7I+CDjg4wPMj7I+KB8mFbCb7hnH0+zfJa54+aDjA8yPsj4IOODjA8yPsj4IOODjA/6BbkVMjiiRDP+AAAAAElFTkSuQmCC\" id=\"imagef02fa57e5c\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"197.273668\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_13\">\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"201.047582\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(197.866332 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"238.786712\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(235.605462 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(198.175605 224.365122) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_14\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"197.273668\" y=\"131.932038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"197.273668\" y=\"169.671168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 197.273668 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 265.204103 196.08856 \n", "L 265.204103 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 265.204103 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 197.273668 128.158125 \n", "L 265.204103 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_22\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 122.158125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 346.720625 196.08856 \n", "L 346.720625 128.158125 \n", "L 278.79019 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p697c14e520)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABx0lEQVR4nO3cvUrDcBRA8eYDRegkCoJCN32COunsLPgCTi7i4uLWzRdwcXQugru46hu4iNilCA7WYo04SOMr3ErkEHN+8+WfcLhLQtOkLMZlq6EG3W54tnN5Ufn108pPVJjxQcYHGR9kfJDxQcYHGR9kfJDxQTl9A6SHl4/wbOerCM2lG5vhM918kPFBxgcZH2R8kPFBxgcZH2R8UKOfcK9eJ+HZnRmeXKPcfJDxQcYHGR9kfJDxQcYHGR9kfJDxQTO9Xigno9jg/EL80DQLjSX5XPjI/up6aO68GIbP/AtuPsj4IOODjA8yPsj4IOODjA8yPiiZjp7DX6BP7+9ig8On8A2Ug8fQXHZ4Gj4zyerxuwA3H2R8kPFBxgcZH2R8kPFBxgcZH2R8UP59sh8f7p2F5srltfgdLK2ExuryymAWbj7I+CDjg4wPMj7I+CDjg4wPMj4oT7e2Kz/08/goPNvuX1d+/bpw80HGBxkfZHyQ8UHGBxkfZHyQ8UHGB+Wt93F4+G1vNzS3eHP7y9tpFjcfZHyQ8UHGBxkfZHyQ8UHGBxkflBy02uEv0On/JPtv3HyQ8UHGBxkfZHyQ8UHGBxkfZHyQ8UE/Fzky8EuvBuQAAAAASUVORK5CYII=\" id=\"image428f90d083\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"278.79019\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_15\">\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"282.564103\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(279.382853 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m9c381af5ee\" x=\"320.303234\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(317.121984 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(279.692126 224.365122) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_16\">\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"278.79019\" y=\"131.932038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m601a9d1f28\" x=\"278.79019\" y=\"169.671168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 278.79019 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 346.720625 196.08856 \n", "L 346.720625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 346.720625 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 278.79019 128.158125 \n", "L 346.720625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_26\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 122.158125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 366.250625 167.415342 \n", "L 372.071825 167.415342 \n", "L 372.071825 50.991342 \n", "L 366.250625 50.991342 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAgAAACiCAYAAAB8iIwDAAAA7klEQVR4nN2XSw4DIQxDUyn3P2s33ZWkN8iLZGCgsx1kP8cwn1d+3mnF5ZZR3Te3LAU6CkELjCyCFrACxoQFqc9BV5gQExkOiBmDFsgKBJkbGBiSLIYakxnYgg6OHpMnKcc8YMvxqOUutkzy+S13xbmYADkh5g2QqCC/9fKEmFd0QZA58BGEXehtygx3xFw/h7/oAp7E5oEMFBMVBluU980D60YFYgg5Jk9S7mIHpNwFODRSgEBDARjNk34noatOTLRYr4CTHHoXBNnogixkSIxZfxx0Rv2VIVGhwSBb1AJzFGQLUsC6dUjck+u7+AE+P2IIsbjxbAAAAABJRU5ErkJggg==\" id=\"image7724676b5d\" transform=\"scale(1 -1) translate(0 -116.64)\" x=\"366.48\" y=\"-50.4\" width=\"5.76\" height=\"116.64\"/>\n", "   <g id=\"matplotlib.axis_17\"/>\n", "   <g id=\"matplotlib.axis_18\">\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_33\">\n", "      <defs>\n", "       <path id=\"med937df9e0\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#med937df9e0\" x=\"372.071825\" y=\"167.415342\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(379.071825 171.214561) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#med937df9e0\" x=\"372.071825\" y=\"144.130542\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(379.071825 147.929761) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#med937df9e0\" x=\"372.071825\" y=\"120.845742\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(379.071825 124.644961) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#med937df9e0\" x=\"372.071825\" y=\"97.560942\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(379.071825 101.360161) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#med937df9e0\" x=\"372.071825\" y=\"74.276142\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_31\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(379.071825 78.075361) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#med937df9e0\" x=\"372.071825\" y=\"50.991342\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_32\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(379.071825 54.790561) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 366.250625 167.415342 \n", "L 369.161225 167.415342 \n", "L 372.071825 167.415342 \n", "L 372.071825 50.991342 \n", "L 369.161225 50.991342 \n", "L 366.250625 50.991342 \n", "L 366.250625 167.415342 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pca6246c906\">\n", "   <rect x=\"34.240625\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p64b6c50073\">\n", "   <rect x=\"115.757147\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p3c596bb68b\">\n", "   <rect x=\"197.273668\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p2371c7e257\">\n", "   <rect x=\"278.79019\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p9e7d60a145\">\n", "   <rect x=\"34.240625\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p4953e6d1b5\">\n", "   <rect x=\"115.757147\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pf03e82c1be\">\n", "   <rect x=\"197.273668\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p697c14e520\">\n", "   <rect x=\"278.79019\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 700x350 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.show_heatmaps(\n", "    dec_self_attention_weights[:, :, :, :],\n", "    xlabel='Key positions', ylabel='Query positions',\n", "    titles=['Head %d' % i for i in range(1, 5)], figsize=(7, 3.5))"]}, {"cell_type": "markdown", "id": "34030fb3", "metadata": {"origin_pos": 84}, "source": ["Similar to the case in the encoder self-attention,\n", "via the specified valid length of the input sequence,\n", "[**no query from the output sequence\n", "attends to those padding tokens from the input sequence.**]\n"]}, {"cell_type": "code", "execution_count": 21, "id": "1c0b1dfe", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:50:45.958174Z", "iopub.status.busy": "2023-08-18T19:50:45.957587Z", "iopub.status.idle": "2023-08-18T19:50:47.397366Z", "shell.execute_reply": "2023-08-18T19:50:47.396481Z"}, "origin_pos": 85, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"402.17495pt\" height=\"233.64481pt\" viewBox=\"0 0 402.17495 233.64481\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-20T08:43:42.522811</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 233.64481 \n", "L 402.17495 233.64481 \n", "L 402.17495 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "L 34.240625 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p81be2bf3a7)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABwElEQVR4nO3cMS8DcRiAcf/rmSQGNiGRiMFg9AkkvobVZJNYDCbMEpHYDSajaGKyWCzE2MmChAo1yOn5Cm+bq0evz29+0/v3ybvctbnU/Xwrx6r28RoePVxYCc1tvrT6Pc2/ldEHGGXGBxkfZHyQ8UHGBxkfZHyQ8UHGBxkfZHyQ8UHGBxkfZHyQ8UHGBxkfZHxQnlKq/lMnp8OjD1/f1V9/SLj5IOODjA8yPsj4IOODjA8yPsj4IOODUnF6EP5/ftFshubGt3bjB5hbis1l9duT+n2jIWJ8kPFBxgcZH2R8kPFBxgcZH5R+bi/Dd7hXa+uhudXWXfwAjTw8WzduPsj4IOODjA8yPsj4IOODjA8yPsj4oFR22tW/4K4HGxOzobnjzuOAT/L33HyQ8UHGBxkfZHyQ8UHGBxkfZHxQ6r4/h+9wt6cWQ3P77fjr1lPWCM/WjZsPMj7I+CDjg4wPMj7I+CDjg4wPMj4oP59fDg/v3V+E5np5ZFCWsacbA3kRH8zNBxkfZHyQ8UHGBxkfZHyQ8UHGB6XiZCf8A/rT0Vlobub6pu8DjRI3H2R8kPFBxgcZH2R8kPFBxgcZH2R80C/QATUPj3evYwAAAABJRU5ErkJggg==\" id=\"imageb64e4ab861\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"34.240625\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"ma7299f47ef\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"38.014538\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"75.753668\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"md2cd496a94\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"34.240625\" y=\"26.092038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 29.891257) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"34.240625\" y=\"63.831168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 67.630387) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 95.477874) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-51\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 3406 84 \n", "L 4238 -825 \n", "L 3475 -825 \n", "L 2784 -78 \n", "Q 2681 -84 2626 -87 \n", "Q 2572 -91 2522 -91 \n", "Q 1538 -91 948 567 \n", "Q 359 1225 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1516 4351 937 \n", "Q 4025 359 3406 84 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 34.240625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 102.17106 90.24856 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 34.240625 90.24856 \n", "L 102.17106 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.240625 22.318125 \n", "L 102.17106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-48\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2753 \n", "L 3553 2753 \n", "L 3553 4666 \n", "L 4184 4666 \n", "L 4184 0 \n", "L 3553 0 \n", "L 3553 2222 \n", "L 1259 2222 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "L 115.757147 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p6f7ec9a9b5)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABr0lEQVR4nO3cPUoDURRA4blxiIIiowGJpbgRO21tXImlWGjtXqx0BQEXkA1ooxCCv5044xZuZOTwkvPVl5eXM6+ZzJBoX1+6KimGG6m5rm2zS1aXuwepueu3x/SapRjQG1hlxgcZH2R8kPFBxgcZH2R8kPFBxgcZH2R8kPFBxgcZH2R8kPFBxgcZH2R8UJ19KL6IGOSv6ez7p/fPL4UnH2R8kPFBxgcZH2R8kPFBxgcZH2R8UN116dfzq4jofQNR9b9mKTz5IOODjA8yPsj4IOODjA8yPsj4oLgaNulb3IvnaW5wfTO/gX+4ay6FJx9kfJDxQcYHGR9kfJDxQcYHGR9kfFB9ut+kh9uHu9Tc2tFZes3sn+Et8s5/KZbvGxXE+CDjg4wPMj7I+CDjg4wPMj4o2vdZ+gH6+c5hau7m8+nPG1olnnyQ8UHGBxkfZHyQ8UHGBxkfZHyQ8UFxPxqnf144nk5yg1tNfgcf89RYNHv5NQvhyQcZH2R8kPFBxgcZH2R8kPFBxgfVt/Ov9PDJ9qj/HSzhnWuWJx9kfJDxQcYHGR9kfJDxQcYHGR9kfNAvyU8q9/+7a7gAAAAASUVORK5CYII=\" id=\"imageaf517eb118\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"115.757147\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"119.53106\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"157.27019\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"115.757147\" y=\"26.092038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"115.757147\" y=\"63.831168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 115.757147 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 183.687582 90.24856 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 115.757147 90.24856 \n", "L 183.687582 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 115.757147 22.318125 \n", "L 183.687582 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "L 197.273668 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pba796452a3)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABwElEQVR4nO3bQSvDcRzH8f1mMySlSBIXuTk7OMjZTa5OLpIn4AF4EB4GcZRyc+Do4kBJSWkro6i1eQqflN5te7/O33779d738u+/lc7Nea8SKnNL2dzEVHpkpb27E81NnV7FZ/aLKn2BYWZ8kPFBxgcZH2R8kPFBxgcZH2R8kPFBxgcZH2R8kPFBxgcZH2R8kPFBxgfVepdn8XD18DgczL/TxsJ0PDto3HyQ8UHGBxkfZHyQ8UHGBxkfZHyQ8UGl23qNf5/fa71Fc9drW/EFNh9uo7nSmIjP7BduPsj4IOODjA8yPsj4IOODjA8yPqhWRsfz6dnsH+gXzc/4yM36WP75A8bNBxkfZHyQ8UHGBxkfZHyQ8UHGBxkfVLrP9/kL9HYzO3R+Ob7A4/pGNLd8l71o7yduPsj4IOODjA8yPsj4IOODjA8yPqhWGanHw2VmMRv8eI/P/P7pxLODxs0HGR9kfJDxQcYHGR9kfJDxQcYHGR9U9iuT8Qv0k6+X/7zL0HHzQcYHGR9kfJDxQcYHGR9kfJDxQeVpdSV+wl082ovmRrYP/nyhYeLmg4wPMj7I+CDjg4wPMj7I+CDjg4wP+gVjQC7uMZVHTQAAAABJRU5ErkJggg==\" id=\"image5db5fe225e\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"197.273668\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"201.047582\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"238.786712\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"197.273668\" y=\"26.092038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"197.273668\" y=\"63.831168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 197.273668 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 265.204103 90.24856 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 197.273668 90.24856 \n", "L 265.204103 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 197.273668 22.318125 \n", "L 265.204103 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "L 278.79019 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p39e1507df7)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABtUlEQVR4nO3bO0pDQRxG8ZnkIkoeWNjqHoI7cAN27sFX7zoEH9twAy5AEGsrwUetkdxOct3CZ5pjkvOr/0yGwzR3mNT5x3NXUr1+NPa0fxAvOXm8j+bqeCdec1n06A2sM+ODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPsj4IOODjA8yPqgpzUY+vTWKxh6mbbzkZLCd//6K8eSDjA8yPsj4IOODjA8yPsj4IOODjA+qPzcX8fv83tF5tujmIN7A92H2ln98l73jXyaefJDxQcYHGR9kfJDxQcYHGR9kfFCdzz7zf6Cni9Yaz54OdqO5q/Zt0e38W558kPFBxgcZH2R8kPFBxgcZH2R8kPFBzV+uArouu4k4Dq8MSinlZgWvDVKefJDxQcYHGR9kfJDxQcYHGR9kfFCTfrWWUsrJcC+au569LrqfteLJBxkfZHyQ8UHGBxkfZHyQ8UHGBxkfVM/qKL5fuJy+ZIv2m4U3tE48+SDjg4wPMj7I+CDjg4wPMj7I+KDatV/xF278RHyYPxG/bd/j2VXjyQcZH2R8kPFBxgcZH2R8kPFBxgcZH/QLipYySGzz+zUAAAAASUVORK5CYII=\" id=\"image7a4f2370e8\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"278.79019\" y=\"-21.84856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"282.564103\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"320.303234\" y=\"90.24856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"278.79019\" y=\"26.092038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"278.79019\" y=\"63.831168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 278.79019 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 346.720625 90.24856 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 278.79019 90.24856 \n", "L 346.720625 90.24856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 278.79019 22.318125 \n", "L 346.720625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 102.17106 196.08856 \n", "L 102.17106 128.158125 \n", "L 34.240625 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pfc74c5549b)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABtElEQVR4nO3dP0oDQRxH8RldFP+AF7CzEwQbL+BBbO1E0dLSE2jjYWxsLaytrAQLUQJhIRDjeoXvFvLM7vvUP3YnL9NM2CW1ayddCXVdODr9Si9Z7vaOormzj9f4mstihV7AmBkfZHyQ8UHGBxkfZHyQ8UHGBxkf1PSaTn9eUMSdDzI+yPgg44OMDzI+yPgg44OMD+p3wq01m1vJv9PvER+a3fkg44OMDzI+yPgg44OMDzI+yPgg44Oa+Jn7UkrpfrKx9/xZ+oOt9fz+A+POBxkfZHyQ8UHGBxkfZHyQ8UHGBzVlPsunw9Pw9OIyvuTx7Xl+/4Fx54OMDzI+yPgg44OMDzI+yPgg44OMD2rq2kY83LWTaG4xm+crWO33isCQuPNBxgcZH2R8kPFBxgcZH2R8kPFBTTdr4+Gb3cNo7vozf0S89nhbfWjG+8n/AeODjA8yPsj4IOODjA8yPsj4IOODap9/iFs8P0RzLydX8QL2nx6jubq5E19zWbjzQcYHGR9kfJDxQcYHGR9kfJDxQfW0bMcn3Pv27S/XMjrufJDxQcYHGR9kfJDxQcYHGR9kfJDxQb9rfzlsVBkI8gAAAABJRU5ErkJggg==\" id=\"image129e58be9f\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"34.240625\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_9\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"38.014538\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(34.833288 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"75.753668\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(72.572418 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(35.142561 224.365122) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4b\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2694 \n", "L 3353 4666 \n", "L 4166 4666 \n", "L 1850 2491 \n", "L 4331 0 \n", "L 3500 0 \n", "L 1259 2247 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"34.240625\" y=\"131.932038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 135.731257) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"34.240625\" y=\"169.671168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(20.878125 173.470387) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 201.317874) rotate(-90) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 34.240625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 102.17106 196.08856 \n", "L 102.17106 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 34.240625 196.08856 \n", "L 102.17106 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 34.240625 128.158125 \n", "L 102.17106 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <!-- Head 1 -->\n", "    <g transform=\"translate(46.791467 122.158125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 183.687582 196.08856 \n", "L 183.687582 128.158125 \n", "L 115.757147 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pe87785274c)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABvUlEQVR4nO3dQUpCURhH8XdDcCIR1SQsJ0ELaDsuIGoXrcKgcfOGjaJJo4YtoQiCsEBfUOR7beGvIAf1/MYfl8vhm6hPLc30s61SzSwam11fxkd+3z1Gc9u39/GZq2KLvsAmMz7I+CDjg4wPMj7I+CDjg4wPMj6os4xDS7cbzzY/f8u4wkpw80HGBxkfZHyQ8UHGBxkfZHyQ8UGdqm3y6Tb8rH1wHB/58V5HczvxiavDzQcZH2R8kPFBxgcZH2R8kPFBxgcZH1Ta+it+Pr8N31447x3FF7iqX+PZdePmg4wPMj7I+CDjg4wPMj7I+CDjg8o830AvpURz6Svhqqqqi94gmhvVL/GZq8LNBxkfZHyQ8UHGBxkfZHyQ8UHGBxkfVJrJOH4v4KZ/Es0Nnx/yC+wfxrPrxs0HGR9kfJDxQcYHGR9kfJDxQcYHzfUba0+T32huuHuw0GU2jZsPMj7I+CDjg4wPMj7I+CDjg4wPMj6onFW9+AP00TR7Rj59jn/Tufkg44OMDzI+yPgg44OMDzI+yPigzmkv/0OxavyWze31F7vNhnHzQcYHGR9kfJDxQcYHGR9kfJDxQcYH/QPfMzrOF2AH0wAAAABJRU5ErkJggg==\" id=\"imagef2e80e9711\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"115.757147\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_11\">\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"119.53106\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(116.34981 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"157.27019\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(154.08894 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(116.659083 224.365122) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_12\">\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"115.757147\" y=\"131.932038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"115.757147\" y=\"169.671168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 115.757147 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 183.687582 196.08856 \n", "L 183.687582 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 115.757147 196.08856 \n", "L 183.687582 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 115.757147 128.158125 \n", "L 183.687582 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- Head 2 -->\n", "    <g transform=\"translate(128.307989 122.158125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 265.204103 196.08856 \n", "L 265.204103 128.158125 \n", "L 197.273668 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p9763d54d85)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABoklEQVR4nO3aMUoDQRxG8RnZQpBUqS21sbD0StpaCV7AxlZv4xmsLTSYIoQ0CSQgKjte4VsQHtm8X/1ndnhMM7tb+82qlVD/+pINfrylS5Y2n0Vz3e1jvOa+OKI3cMiMDzI+yPgg44OMDzI+yPgg44OMD+roDZRa6R1gPPkg44OMDzI+yPgg44OMDzI+yPigrnxt8+nVIpv7+Y6XrBeX+fNHxpMPMj7I+CDjg4wPMj7I+CDjg4wPMj6ott06/j+/tWy0DvgofnNyGs097+bxmvvCkw8yPsj4IOODjA8yPsj4IOODjA/qWt/n0/1vNHY/PYuXfNp+5s8fGU8+yPgg44OMDzI+yPgg44OMDzI+yPigrpT4+3lpi/do7mpyHK855GP72HjyQcYHGR9kfJDxQcYHGR9kfJDxQbVfL+Mr7t30PJp72MzyDXjDFcH4IOODjA8yPsj4IOODjA8yPsj4oHpdJvHrhfRf+kN+ZTCEJx9kfJDxQcYHGR9kfJDxQcYHGR/UtQG/iHtz/V+efJDxQcYHGR9kfJDxQcYHGR9kfJDxQX8RIzWI5NuqkAAAAABJRU5ErkJggg==\" id=\"image08f9bf0098\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"197.273668\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_13\">\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"201.047582\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(197.866332 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"238.786712\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(235.605462 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(198.175605 224.365122) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_14\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"197.273668\" y=\"131.932038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"197.273668\" y=\"169.671168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 197.273668 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 265.204103 196.08856 \n", "L 265.204103 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 197.273668 196.08856 \n", "L 265.204103 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 197.273668 128.158125 \n", "L 265.204103 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_22\">\n", "    <!-- Head 3 -->\n", "    <g transform=\"translate(209.824511 122.158125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 346.720625 196.08856 \n", "L 346.720625 128.158125 \n", "L 278.79019 128.158125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p7803fdbc1f)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAF8AAABfCAYAAACOTBv1AAABx0lEQVR4nO3bPUoDURhG4bnJaCFosBML8QfB2kXYaukOxFIE12CTUtBlCDa6DGsrEQ0WBhUrdcYtvJHAYfQ89ceXy+E2SWZKMx61VaqeicZKrx+vPJ5fieaG7/fxzq7o0Qf4z4wPMj7I+CDjg4wPMj7I+CDjg4wPqukDNPQBQN58kPFBxgcZH2R8kPFBxgcZH2R8UF01XxOMz07/AGXqKzvDmw8yPsj4IOODjA8yPsj4IOODjA8yPqg8bm/Fz+cvXd9kS+cW4gN8X15Ec/3dg3hnV3jzQcYHGR9kfJDxQcYHGR9kfJDxQaV5f8nfQG+zB7oPF1bjlecfD/HsX+PNBxkfZHyQ8UHGBxkfZHyQ8UHGBxkfVH8e7cfDvY21aG6Snwza8SiaK4tL8c6u8OaDjA8yPsj4IOODjA8yPsj4IOODSvP6HP+BfjhYj+Ym+obbZh9fyt97Vd2bDzI+yPgg44OMDzI+yPgg44OMDzI+qH7b24mHz66G0Vz6k0FVVVX7dBfNleXNeGdXePNBxgcZH2R8kPFBxgcZH2R8kPFB5aQ/iL+Ono5us6Vzg18f6D/x5oOMDzI+yPgg44OMDzI+yPgg44OMD/oBi0A5DO4OlUUAAAAASUVORK5CYII=\" id=\"image5956fd7e1e\" transform=\"scale(1 -1) translate(0 -68.4)\" x=\"278.79019\" y=\"-127.68856\" width=\"68.4\" height=\"68.4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_15\">\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"282.564103\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(279.382853 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#ma7299f47ef\" x=\"320.303234\" y=\"196.08856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(317.121984 210.686997) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(279.692126 224.365122) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_16\">\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"278.79019\" y=\"131.932038\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#md2cd496a94\" x=\"278.79019\" y=\"169.671168\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 278.79019 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 346.720625 196.08856 \n", "L 346.720625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 278.79019 196.08856 \n", "L 346.720625 196.08856 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 278.79019 128.158125 \n", "L 346.720625 128.158125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_26\">\n", "    <!-- Head 4 -->\n", "    <g transform=\"translate(291.341033 122.158125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-48\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"261.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"293.261719\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_9\">\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 366.250625 167.415342 \n", "L 372.071825 167.415342 \n", "L 372.071825 50.991342 \n", "L 366.250625 50.991342 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAgAAACiCAYAAAB8iIwDAAAA7klEQVR4nN2XSw4DIQxDUyn3P2s33ZWkN8iLZGCgsx1kP8cwn1d+3mnF5ZZR3Te3LAU6CkELjCyCFrACxoQFqc9BV5gQExkOiBmDFsgKBJkbGBiSLIYakxnYgg6OHpMnKcc8YMvxqOUutkzy+S13xbmYADkh5g2QqCC/9fKEmFd0QZA58BGEXehtygx3xFw/h7/oAp7E5oEMFBMVBluU980D60YFYgg5Jk9S7mIHpNwFODRSgEBDARjNk34noatOTLRYr4CTHHoXBNnogixkSIxZfxx0Rv2VIVGhwSBb1AJzFGQLUsC6dUjck+u7+AE+P2IIsbjxbAAAAABJRU5ErkJggg==\" id=\"image308f8a399e\" transform=\"scale(1 -1) translate(0 -116.64)\" x=\"366.48\" y=\"-50.4\" width=\"5.76\" height=\"116.64\"/>\n", "   <g id=\"matplotlib.axis_17\"/>\n", "   <g id=\"matplotlib.axis_18\">\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_33\">\n", "      <defs>\n", "       <path id=\"m49b94a5ba2\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m49b94a5ba2\" x=\"372.071825\" y=\"167.415342\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(379.071825 171.214561) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m49b94a5ba2\" x=\"372.071825\" y=\"143.488542\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(379.071825 147.28776) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m49b94a5ba2\" x=\"372.071825\" y=\"119.561741\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(379.071825 123.36096) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m49b94a5ba2\" x=\"372.071825\" y=\"95.63494\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(379.071825 99.434159) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m49b94a5ba2\" x=\"372.071825\" y=\"71.70814\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_31\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(379.071825 75.507358) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 366.250625 167.415342 \n", "L 369.161225 167.415342 \n", "L 372.071825 167.415342 \n", "L 372.071825 50.991342 \n", "L 369.161225 50.991342 \n", "L 366.250625 50.991342 \n", "L 366.250625 167.415342 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p81be2bf3a7\">\n", "   <rect x=\"34.240625\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6f7ec9a9b5\">\n", "   <rect x=\"115.757147\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pba796452a3\">\n", "   <rect x=\"197.273668\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p39e1507df7\">\n", "   <rect x=\"278.79019\" y=\"22.318125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pfc74c5549b\">\n", "   <rect x=\"34.240625\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pe87785274c\">\n", "   <rect x=\"115.757147\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p9763d54d85\">\n", "   <rect x=\"197.273668\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p7803fdbc1f\">\n", "   <rect x=\"278.79019\" y=\"128.158125\" width=\"67.930435\" height=\"67.930435\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 700x350 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.show_heatmaps(\n", "    dec_inter_attention_weights, xlabel='Key positions',\n", "    ylabel='Query positions', titles=['Head %d' % i for i in range(1, 5)],\n", "    figsize=(7, 3.5))"]}, {"cell_type": "markdown", "id": "a25ea19e", "metadata": {"origin_pos": 86}, "source": ["Although the Transformer architecture\n", "was originally proposed for sequence-to-sequence learning,\n", "as we will discover later in the book,\n", "either the Transformer encoder\n", "or the Transformer decoder\n", "is often individually used\n", "for different deep learning tasks.\n", "\n", "## Summary\n", "\n", "The Transformer is an instance of the encoder--decoder architecture,\n", "though either the encoder or the decoder can be used individually in practice.\n", "In the Transformer architecture, multi-head self-attention is used\n", "for representing the input sequence and the output sequence,\n", "though the decoder has to preserve the autoregressive property via a masked version.\n", "Both the residual connections and the layer normalization in the Transformer\n", "are important for training a very deep model.\n", "The positionwise feed-forward network in the Transformer model\n", "transforms the representation at all the sequence positions using the same MLP.\n", "\n", "\n", "## Exercises\n", "\n", "1. Train a deeper Transformer in the experiments. How does it affect the training speed and the translation performance?\n", "1. Is it a good idea to replace scaled dot product attention with additive attention in the Transformer? Why?\n", "1. For language modeling, should we use the Transformer encoder, decoder, or both? How would you design this method?\n", "1. What challenges can Transformers face if input sequences are very long? Why?\n", "1. How would you improve the computational and memory efficiency of Transformers? Hint: you may refer to the survey paper by :citet:`Tay.Dehghani.Bahri.ea.2020`.\n"]}, {"cell_type": "markdown", "id": "97dcc07a", "metadata": {"origin_pos": 88, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1066)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "c6ab6874", "metadata": {"origin_pos": 0}, "source": ["# Large-Scale Pretraining with Transformers\n", ":label:`sec_large-pretraining-transformers`\n", "\n", "So far in our image classification and machine translation experiments,\n", "models have been trained on datasets with input--output examples\n", "*from scratch* to perform specific tasks.\n", "For example, a Transformer was trained\n", "with English--French pairs (:numref:`sec_transformer`)\n", "so that this model can translate input English text into French.\n", "As a result, each model becomes a *specific expert*\n", "that is sensitive to even a slight shift in data distribution\n", "(:numref:`sec_environment-and-distribution-shift`).\n", "For better generalized models, or even more competent *generalists*\n", "that can perform multiple tasks with or without adaptation,\n", "*pretraining* models on large data has been increasingly common.\n", "\n", "Given larger data for pretraining, the Transformer architecture\n", "performs better with an increased model size and training compute,\n", "demonstrating superior *scaling* behavior.\n", "Specifically, performance of Transformer-based language models\n", "scales as a power law with the amount of model parameters,\n", "training tokens, and training compute :cite:`kaplan2020scaling`.\n", "The scalability of Transformers is also evidenced\n", "by the significantly boosted performance\n", "from larger vision Transformers trained on larger data\n", "(discussed in :numref:`sec_vision-transformer`).\n", "More recent success stories include <PERSON><PERSON>, a *generalist* model\n", "that can play Atari, caption images, chat, and act as a robot :cite:`reed2022generalist`. Gato is a single  Transformer that scales well when pretrained on diverse modalities,\n", "including text, images, joint torques, and button presses.\n", "Notably, all such multimodal data is serialized into a flat sequence of tokens,\n", "which can be processed akin to text tokens (:numref:`sec_transformer`)\n", "or image patches (:numref:`sec_vision-transformer`) by Transformers.\n", "\n", "Prior to the compelling success of pretraining Transformers for multimodal data,\n", "Transformers were extensively pretrained  with a wealth of text.\n", "Originally proposed for machine translation,\n", "the Transformer architecture in :numref:`fig_transformer`\n", "consists of an encoder for representing input sequences\n", "and a decoder for generating target sequences.\n", "Primarily, Transformers can be used in three different modes:\n", "*encoder-only*, *encoder--decoder*, and *decoder-only*.\n", "To conclude this chapter, we will review these three modes\n", "and explain the scalability in pretraining Transformers.\n", "\n", "## Encoder-Only\n", "\n", "When only the Transformer encoder is used,\n", "a sequence of input tokens is converted\n", "into the same number of representations\n", "that can be further projected into output\n", "(e.g., classification). A Transformer encoder\n", "consists of  self-attention layers,\n", "where all input tokens attend to each other.\n", "For example, vision Transformers depicted in :numref:`fig_vit`\n", "are encoder-only, converting a sequence of input image patches into\n", "the representation of a special “&lt;cls&gt;” token.\n", "Since this representation depends on all input tokens,\n", "it is further projected into classification labels.\n", "This design was inspired by an earlier encoder-only Transformer\n", "pretrained on text: BERT (Bidirectional Encoder Representations from Transformers) :cite:`<PERSON>.Chang.Lee.ea.2018`.\n", "\n", "\n", "### Pretraining BERT\n", "\n", "![Left: Pretraining BERT with masked language modeling. Prediction of the masked \"love\" token depends on all input tokens before and after \"love\". Right: Attention pattern in the Transformer encoder. Each token along the vertical axis attends to all input tokens along the horizontal axis.](../img/bert-encoder-only.svg)\n", ":label:`fig_bert-encoder-only`\n", "\n", "BERT is pretrained on text sequences using *masked language modeling*:\n", "input text with randomly masked tokens is fed\n", "into a Transformer encoder to predict the masked tokens.\n", "As illustrated in :numref:`fig_bert-encoder-only`,\n", "an original text sequence \"I\", \"love\", \"this\", \"red\", \"car\"\n", "is prepended with the “&lt;cls&gt;” token, and the “&lt;mask&gt;” token\n", "randomly replaces \"love\"; then the cross-entropy loss between the masked token \"love\"\n", "and its prediction is to be minimized during pretraining.\n", "Note that there is no constraint in the attention pattern of Transformer encoders\n", "(right of :numref:`fig_bert-encoder-only`)\n", "so all tokens can attend to each other.\n", "Thus, prediction of \"love\" depends on input tokens before and after it in the sequence.\n", "This is why BERT is a \"bidirectional encoder\".\n", "Without need for manual labeling, large-scale text data\n", "from books and Wikipedia can be used for pretraining BERT.\n", "\n", "\n", "### Fine-Tuning BERT\n", "\n", "The pretrained BERT can be *fine-tuned* to downstream encoding tasks involving single text or text pairs. During fine-tuning, additional layers can be added to BERT with randomized parameters: these parameters and those pretrained BERT parameters will be *updated* to fit training data of downstream tasks.\n", "\n", "![Fine-tuning BERT for sentiment analysis.](../img/bert-finetune-classification.svg)\n", ":label:`fig_bert-finetune-classification`\n", "\n", ":numref:`fig_bert-finetune-classification` illustrates\n", "fine-tuning of BERT for sentiment analysis.\n", "The Transformer encoder is a pretrained BERT,\n", "which takes a text sequence as input\n", "and feeds the “&lt;cls&gt;” representation\n", "(global representation of the input)\n", "into an additional fully connected layer\n", "to predict the sentiment.\n", "During fine-tuning, the cross-entropy loss\n", "between the prediction and the label\n", "on sentiment analysis data\n", "is minimized via gradient-based algorithms,\n", "where the additional layer is trained from scratch\n", "while pretrained parameters of BERT are updated.\n", "BERT does more than sentiment analysis.\n", "The general language representations learned\n", "by the 350-million-parameter BERT\n", "from 250 billion training tokens\n", "advanced the state of the art for natural language tasks\n", "such as single text classification,\n", "text pair classification or regression,\n", "text tagging, and question answering.\n", "\n", "You may note that these downstream tasks include text pair understanding.\n", "BERT pretraining has another loss for predicting\n", "whether one sentence immediately follows the other.\n", "However, this loss was later found to be less useful when pretraining RoBERTa,\n", "a BERT variant of the same size, on 2000 billion tokens :cite:`Liu.Ott.Goyal.ea.2019`.\n", "Other derivatives of BERT improved model architectures or pretraining objectives,\n", "such as ALBERT (enforcing parameter sharing) :cite:`lan2019albert`,\n", "SpanBERT (representing and predicting spans of text) :cite:`joshi2020spanbert`,\n", "DistilBERT (lightweight via knowledge distillation) :cite:`sanh2019<PERSON><PERSON><PERSON>`,\n", "and ELECTRA (replaced token detection) :cite:`clark2019electra`.\n", "Moreover, BERT inspired Transformer pretraining in computer vision,\n", "such as with vision Transformers :cite:`Dosovi<PERSON>i<PERSON>.Beyer.Kolesnikov.ea.2021`,\n", "Swin Transformers :cite:`liu2021swin`,\n", "and MAE (masked autoencoders) :cite:`he2022masked`.\n", "\n", "## Encoder--Decoder\n", "\n", "Since a Transformer encoder converts a sequence of input tokens\n", "into the same number of output representations,\n", "the encoder-only mode cannot generate a sequence of arbitrary length as in machine translation.\n", "As originally proposed for machine translation,\n", "the Transformer architecture can be outfitted with a decoder\n", "that autoregressively predicts the target sequence\n", "of arbitrary length, token by token,\n", "conditional on both encoder output and decoder output:\n", "(i) for conditioning on encoder output, encoder--decoder cross-attention\n", "(multi-head attention of decoder in :numref:`fig_transformer`)\n", "allows target tokens to attend to *all* input tokens;\n", "(ii) conditioning on decoder output is achieved\n", "by a so-called *causal* attention\n", "(this name is common in the literature but is misleading\n", "as it has little connection to the proper study of causality)\n", "pattern (masked multi-head attention of decoder in :numref:`fig_transformer`),\n", "where any target token can only attend to *past* and *present* tokens in the target sequence.\n", "\n", "To pretrain encoder--decoder Transformers beyond human-labeled machine translation data,\n", "BART :cite:`lewi<PERSON>2019<PERSON>t` and T5 :cite:`raffel2020exploring`\n", "are two concurrently proposed encoder--decoder Transformers\n", "pretrained on large-scale text corpora.\n", "Both attempt to reconstruct original text in their pretraining objectives,\n", "while the former emphasizes noising input\n", "(e.g., masking, deletion, permutation, and rotation)\n", "and the latter highlights multitask unification\n", "with comprehensive ablation studies.\n", "\n", "\n", "### Pretraining T5\n", "\n", "\n", "As an example of the pretrained Transformer encoder--decoder,\n", "T5 (Text-to-Text Transfer Transformer)\n", "unifies many tasks as the same text-to-text problem:\n", "for any task, the input of the encoder is a task description\n", "(e.g., \"Summarize\", \":\") followed by task input\n", "(e.g., a sequence of tokens from an article),\n", "and the decoder predicts the task output\n", "(e.g., a sequence of tokens summarizing the input article).\n", "To perform as text-to-text, T<PERSON> is trained\n", "to generate some target text conditional on input text.\n", "\n", "\n", "![Left: Pretraining T5 by predicting consecutive spans. The original sentence is \"I\", \"love\", \"this\", \"red\", \"car\", where \"love\" is replaced by a special “&lt;X&gt;” token, and consecutive \"red\", \"car\" are replaced by a special “&lt;Y&gt;” token. The target sequence ends with a special “&lt;Z&gt;” token. Right: Attention pattern in the Transformer encoder--decoder. In the encoder self-attention (lower square), all input tokens attend to each other; In the encoder--decoder cross-attention (upper rectangle), each target token attends to all input tokens; In the decoder self-attention (upper triangle), each target token  attends to present and past target tokens only (causal).](../img/t5-encoder-decoder.svg)\n", ":label:`fig_t5-encoder-decoder`\n", "\n", "To obtain input and output from any original text,\n", "T5 is pretrained to predict consecutive spans.\n", "Specifically, tokens from text are randomly replaced\n", "by special tokens where each consecutive span\n", "is replaced by the same special token.\n", "Consider the example in :numref:`fig_t5-encoder-decoder`,\n", "where the original text is \"I\", \"love\", \"this\", \"red\", \"car\".\n", "Tokens \"love\", \"red\", \"car\" are randomly replaced by special tokens.\n", "Since \"red\" and \"car\" are a consecutive span,\n", "they are replaced by the same special token.\n", "As a result, the input sequence is \"I\", \"&lt;X&gt;\", \"this\", \"&lt;Y&gt;\",\n", "and the target sequence is\n", "\"&lt;X&gt;\", \"love\", \"&lt;Y&gt;\", \"red\", \"car\", \"&lt;Z&gt;\",\n", "where \"&lt;Z&gt;\" is another special token marking the end.\n", "As shown in :numref:`fig_t5-encoder-decoder`,\n", "the decoder has a causal attention pattern to prevent itself\n", "from attending to future tokens during sequence prediction.\n", "\n", "In T5, predicting consecutive span is also referred to\n", "as reconstructing corrupted text.\n", "With this objective, T5 is pretrained\n", "with 1000 billion tokens from the C4\n", "(Colossal Clean Crawled Corpus) data,\n", "which consists of clean English text\n", "from the web :cite:`raffel2020exploring`.\n", "\n", "### Fine-Tuning T5\n", "\n", "Similar to BERT, T5 needs to be fine-tuned (updating T5 parameters)\n", "on task-specific training data to perform this task.\n", "Major differences from BERT fine-tuning include:\n", "(i) T5 input includes task descriptions;\n", "(ii) T5 can generate sequences\n", "with arbitrary length\n", "with its Transformer decoder;\n", "(iii) No additional layers are required.\n", "\n", "![Fine-tuning T5 for text summarization. Both the task description and article tokens are fed into the Transformer encoder for predicting the summary.](../img/t5-finetune-summarization.svg)\n", ":label:`fig_t5-finetune-summarization`\n", "\n", ":numref:`fig_t5-finetune-summarization`\n", "explains fine-tuning T5\n", "using text summarization as an example.\n", "In this downstream task,\n", "the task description tokens \"Summarize\", \":\"\n", "followed by the article tokens are input to the encoder.\n", "\n", "After fine-tuning, the 11-billion-parameter T5 (T5-11B)\n", "achieved state-of-the-art results on multiple encoding (e.g., classification)\n", "and generation (e.g., summarization) benchmarks.\n", "Since released, T5 has been extensively used in later research.\n", "For example, switch Transformers are designed based on T5\n", "to activate a subset of the parameters\n", "for better computational efficiency :cite:`fedus2022switch`.\n", "In a text-to-image model called Imagen,\n", "text is input to a frozen T5 encoder (T5-XXL)\n", "with 4.6 billion parameters :cite:`saharia2022photorealistic`.\n", "The photorealistic text-to-image examples in :numref:`fig_imagen`\n", "suggest that the T5 encoder alone may effectively\n", "represent text even without fine-tuning.\n", "\n", "![Text-to-image examples by the Imagen model, whose text encoder is from T5 (figures taken from :citet:`saharia2022photorealistic`).](../img/imagen.png)\n", ":width:`700px`\n", ":label:`fig_imagen`\n", "\n", "\n", "## Decoder-Only\n", "\n", "\n", "We have reviewed encoder-only and encoder--decoder Transformers.\n", "Alternatively, decoder-only Transformers\n", "remove the entire encoder and the decoder sublayer\n", "with the encoder--decoder cross-attention\n", "from the original encoder--decoder architecture\n", "depicted in :numref:`fig_transformer`.\n", "Nowadays, decoder-only Transformers have been the *de facto* architecture\n", "in large-scale language modeling (:numref:`sec_language-model`),\n", "which leverages the world's abundant unlabeled text corpora via self-supervised learning.\n", "\n", "\n", "\n", "### GPT and GPT-2\n", "\n", "Using language modeling as the training objective,\n", "the GPT (generative pre-training) model\n", "chooses a Transformer decoder\n", "as its backbone :cite:`<PERSON><PERSON>.Narasimhan.Salimans.ea.2018`.\n", "\n", "![Left: Pretraining GPT with language modeling. The target sequence is the input sequence shifted by one token. Both “&lt;bos&gt;” and “&lt;eos&gt;” are special tokens marking the beginning and end of sequences, respectively. Right: Attention pattern in the Transformer decoder. Each token along the vertical axis attends to only its past tokens along the horizontal axis (causal).](../img/gpt-decoder-only.svg)\n", ":label:`fig_gpt-decoder-only`\n", "\n", "Following the autoregressive language model training\n", "as described in :numref:`subsec_partitioning-seqs`,\n", ":numref:`fig_gpt-decoder-only` illustrates\n", "GPT pretraining with a Transformer encoder,\n", "where the target sequence is the input sequence shifted by one token.\n", "Note that the attention pattern in the Transformer decoder\n", "enforces that each token can only attend to its past tokens\n", "(future tokens cannot be attended to because they have not yet been chosen).\n", "\n", "\n", "GPT has 100 million parameters and needs to be\n", "fine-tuned for individual downstream tasks.\n", "A much larger Transformer-decoder language model,\n", "GPT-2, was introduced one year later :cite:`Radford.Wu.Child.ea.2019`.\n", "Compared with the original Transformer decoder in GPT, pre-normalization\n", "(discussed in :numref:`subsec_vit-encoder`)\n", "and improved initialization and weight-scaling were adopted in GPT-2.\n", "Pretrained on 40 GB of text, the 1.5-billion-parameter\n", "GPT-2 obtained the state-of-the-art results on language modeling benchmarks\n", "and promising results on multiple other tasks\n", "*without updating the parameters or architecture*.\n", "\n", "\n", "### GPT-3 and Beyond\n", "\n", "GPT-2 demonstrated potential of using the same language model\n", "for multiple tasks without updating the model.\n", "This is more computationally efficient than fine-tuning,\n", "which requires model updates via gradient computation.\n", "\n", "\n", "![Zero-shot, one-shot, few-shot in-context learning with language models (Transformer decoders). No parameter update is needed.](../img/gpt-3-xshot.svg)\n", ":label:`fig_gpt-3-xshot`\n", "\n", "Before explaining the more computationally efficient use\n", "of language models without parameter update,\n", "recall :numref:`sec_rnn-scratch` that a language model\n", "can be trained to generate a text sequence\n", "conditional on some prefix text sequence.\n", "Thus, a pretrained language model may generate the task output\n", "as a sequence *without parameter update*,\n", "conditional on an input sequence with the task description,\n", "task-specific input--output examples, and a prompt (task input).\n", "This learning paradigm is called *in-context learning* :cite:`brown2020language`,\n", "which can be further categorized\n", "into *zero-shot*, *one-shot*, and *few-shot*,\n", "when there is no, one, and a few task-specific input--output examples (:numref:`fig_gpt-3-xshot`).\n", "\n", "\n", "![Aggregate performance of GPT-3 for all 42 accuracy-denominated benchmarks (caption adapted and figure taken from :citet:`brown2020language`).](../img/gpt3-xshot-scaling.png)\n", ":width:`400px`\n", ":label:`fig_gpt3-xshot-scaling`\n", "\n", "These three settings were tested in GPT-3 :cite:`brown2020language`,\n", "whose largest version uses data and model size\n", "about two orders of magnitude larger than those in GPT-2.\n", "GPT-3 uses the same Transformer decoder architecture\n", "as its direct predecessor GPT-2\n", "except that attention patterns\n", "(at the right in :numref:`fig_gpt-decoder-only`)\n", "are sparser at alternating layers.\n", "Pretrained with 300 billion tokens,\n", "GPT-3 performs better with larger model size,\n", "where few-shot performance increases most rapidly (:numref:`fig_gpt3-xshot-scaling`).\n", "\n", "The subsequent GPT-4 model did not fully disclose technical details in its report :cite:`openai2023gpt4`.\n", "By contrast with its predecessors, GPT-4\n", "is a large-scale, multimodal model that\n", "can take both text and images as input\n", "and generate text output.\n", "\n", "\n", "## Scalability\n", "\n", ":numref:`fig_gpt3-xshot-scaling` empirically demonstrates scalability\n", "of Transformers in the GPT-3 language model.\n", "For language modeling, more comprehensive empirical studies\n", "on the scalability of Transformers have led researchers to see promise\n", "in training larger Transformers with more data and compute :cite:`kaplan2020scaling`.\n", "\n", "![Transformer language model performance improves smoothly as we increase the model size, dataset size, and amount of compute used for training. For optimal performance all three factors must be scaled up in tandem. Empirical performance has a power-law relationship with each individual factor when not bottlenecked by the other two (caption adapted and figure taken from :citet:`kaplan2020scaling`).](../img/scaling-power-law.png)\n", ":width:`700px`\n", ":label:`fig_scaling-power-law3`\n", "\n", "As shown in :numref:`fig_scaling-power-law3`,\n", "*power-law scaling* can be observed in the performance\n", "with respect to the model size (number of parameters, excluding embedding layers),\n", "dataset size (number of training tokens),\n", "and amount of training compute (PetaFLOP/s-days, excluding embedding layers).\n", "In general, increasing all these three factors in tandem leads to better performance.\n", "However, *how* to increase them in tandem\n", "still remains a matter of debate :cite:`hoffmann2022training`.\n", "\n", "![Transformer language model training runs (figure taken from :citet:`kaplan2020scaling`).](../img/scaling-sample-conv.png)\n", ":width:`700px`\n", ":label:`fig_scaling-sample-conv`\n", "\n", "As well as increased performance, large models also enjoy better sample efficiency than small models. :numref:`fig_scaling-sample-conv` shows that large models need fewer training samples (tokens processed) to perform at the same level achieved by small models, and performance is scaled smoothly with compute.\n", "\n", "\n", "\n", "![GPT-3 performance (cross-entropy validation loss) follows a power-law trend with the amount of compute used for training. The power-law behavior observed in :citet:`kaplan2020scaling` continues for an additional two orders of magnitude with only small deviations from the predicted curve. Embedding parameters are excluded from compute and parameter counts (caption adapted and figure taken from :citet:`brown2020language`).](../img/scaling-gpt3.png)\n", ":width:`250px`\n", ":label:`fig_scaling-gpt3`\n", "\n", "\n", "The empirical scaling behaviors in :citet:`kaplan2020scaling` have been tested in subsequent large Transformer models. For example, GPT-3 supported this hypothesis with two more orders of magnitude in :numref:`fig_scaling-gpt3`.\n", "\n", "\n", "\n", "\n", "\n", "## Large Language Models\n", "\n", "The scalability of Transformers in the GPT series has inspired subsequent large language models. \n", "The GPT-2 Transformer decoder was used for training the 530-billion-parameter Megatron-Turing NLG :cite:`smith2022using` with 270 billion training tokens. Following the GPT-2 design, the 280-billion-parameter Gopher :cite:`rae2021scaling` pretrained with 300 billion tokens, performed competitively across diverse tasks. \n", "Inheriting the same architecture and using the same compute budget of Go<PERSON>, <PERSON><PERSON><PERSON> :cite:`hoffmann2022training` is a substantially smaller (70 billion parameters) model that trains for much longer (1.4 trillion training tokens), outperforming <PERSON><PERSON> on many tasks and with more emphasis on the number of tokens than on the number of parameters.\n", "To continue the scaling line of language modeling, \n", "PaLM (Pathway Language Model) :cite:`chowdhery2022palm`, a 540-billion-parameter Transformer decoder with modified designs pretrained on 780 billion tokens, outperformed average human performance on the BIG-Bench benchmark :cite:`srivastava2022beyond`. Its later version, PaLM 2 :cite:`anil2023palm`, scaled data and model roughly 1:1 and improved multilingual and reasoning capabilities. \n", "Other large language models, such as Minerva  :cite:`lewkowycz2022solving` that further trains a generalist (PaLM) and Galactica :cite:`taylor2022galactica` that is not trained on a general corpus, have shown promising quantitative and scientific reasoning capabilities.\n", "\n", "\n", "Open-sourced releases, such as OPT (Open Pretrained Transformers) :cite:`zhang2022opt`, BLOOM :cite:` scao2022bloom`, and FALCON :cite:`penedo2023refinedweb`,\n", "democratized research and use of large language models.\n", "Focusing on computational efficiency at inference time,\n", "the open-sourced Llama 1 :cite:`touvron2023llama` outperformed much larger models by training on more tokens than had been typically used. The updated Llama 2 :cite:`touvron2023llama2` further increased the pretraining corpus by 40%, leading to product models that may match the performance of competitive close-sourced models. \n", "\n", "\n", "\n", ":citet:`wei2022emergent` discussed emergent abilities of large language models that are present in larger models, but not in smaller models.\n", "However, simply increasing model size does not inherently make models follow human instructions better.\n", ":citet:`wei2021finetuned,sanh2021multitask` have found that fine-tuning large language models\n", "on a range of datasets described via *instructions*\n", "can improve zero-shot performance on held-out tasks.\n", "Using *reinforcement learning from human feedback*,\n", ":citet:`ouyang2022training` fine-tuned GPT-3\n", "to follow a diverse set of instructions.\n", "Following the resultant InstructGPT which\n", "aligns language models with human intent\n", "via fine-tuning :cite:`ouyang2022training`,\n", "[ChatGPT](https://chat.openai.com/)\n", "can generate human-like responses (e.g., code debugging and creative writing)\n", "based on conversations with humans\n", "and can perform many natural language processing\n", "tasks zero-shot :cite:`qin2023chatgpt`.\n", ":citet:`bai2022constitutional` replaced human inputs (e.g., human-labeled data) with model outputs\n", "to partially automate the instruction tuning process, which is also known as *reinforcement learning from AI feedback*.\n", "\n", "\n", "Large language models offer an exciting prospect\n", "of formulating text input to induce models to perform desired tasks via in-context learning,\n", "which is also known as *prompting*.\n", "Notably,\n", "*chain-of-thought prompting* :cite:`wei2022chain`,\n", "an in-context learning method\n", "with few-shot \"question, intermediate reasoning steps, answer\" demonstrations,\n", "elicits the complex reasoning capabilities of\n", "large language models\n", "in order to solve mathematical, commonsense, and symbolic reasoning tasks.\n", "Sampling multiple reasoning paths :cite:`wang2023self`, diversifying few-shot demonstrations :cite:`zhang2023automatic`, \n", "and reducing complex problems to sub-problems :cite:`zhou2023least`\n", "can all improve the reasoning accuracy. In fact, with simple prompts like \"Let's think step by step\" just before each answer,\n", "large language models can even perform *zero-shot*\n", "chain-of-thought reasoning with decent accuracy :cite:`kojima2022large`.\n", "Even for multimodal inputs consisting of both text and images,\n", "language models can perform multimodal chain-of-thought reasoning with higher accuracy than using text input only :cite:`zhang2023multicot`.\n", "\n", "\n", "\n", "\n", "## Summary and Discussion\n", "\n", "Transformers have been pretrained as encoder-only (e.g., BERT), encoder--decoder (e.g., T5), and decoder-only (e.g., GPT series). Pretrained models may be adapted to perform different tasks with model update (e.g., fine-tuning) or not (e.g., few-shot). Scalability of Transformers suggests that better performance benefits from larger models, more training data, and more training compute. Since Transformers were first designed and pretrained for text data, this section leans slightly towards natural language processing. Nonetheless, those models discussed above can be often found in more recent models across multiple modalities. For example,\n", "(i) Chinchilla :cite:`hoffmann2022training` was further extended to Flamingo :cite:`alayrac2022flamingo`, a visual language model for few-shot learning;\n", "(ii) GPT-2 :cite:`Radford.Wu.Child.ea.2019` and the vision Transformer encode text and images in CLIP (Contrastive Language-Image Pre-training) :cite:`radford2021learning`, whose image and text embeddings were later adopted in the DALL-E 2 text-to-image system :cite:`ramesh2022hierarchical`. Although there have been no systematic studies on Transformer scalability in multimodal pretraining yet, an all-Transformer text-to-image model called Parti :cite:`yu2022scaling` shows potential of scalability across modalities:\n", "a larger Parti is more capable of high-fidelity image generation and content-rich text understanding (:numref:`fig_parti`).\n", "\n", "\n", "![Image examples generated from the same text by the Parti model of increasing sizes (350M, 750M, 3B, 20B) (examples taken from :citet:`yu2022scaling`).](../img/parti.png)\n", ":width:`700px`\n", ":label:`fig_parti`\n", "\n", "\n", "\n", "\n", "## Exercises\n", "\n", "1. Is it possible to fine-tune T5 using a minibatch consisting of different tasks? Why or why not? How about for GPT-2?\n", "1. Given a powerful language model, what applications can you think of?\n", "1. Say that you are asked to fine-tune a language model to perform text classification by adding additional layers. Where will you add them? Why?\n", "1. Consider sequence-to-sequence problems (e.g., machine translation) where the input sequence is always available throughout the target sequence prediction. What could be limitations of modeling with decoder-only Transformers? Why?\n", "\n", "\n", "[Discussions](https://discuss.d2l.ai/t/9232)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "952d5ccf", "metadata": {"origin_pos": 0}, "source": ["# Attention Pooling by Similarity\n", "\n", ":label:`sec_attention-pooling`\n", "\n", "Now that we have introduced the primary components of the attention mechanism, let's use them in a rather classical setting, namely regression and classification via kernel density estimation :cite:`Nadaraya.1964,Watson.1964`. This detour simply provides additional background: it is entirely optional and can be skipped if needed. \n", "At their core, Nadaraya--Watson estimators rely on some similarity kernel $\\alpha(\\mathbf{q}, \\mathbf{k})$ relating queries $\\mathbf{q}$ to keys $\\mathbf{k}$. Some common kernels are\n", "\n", "$$\\begin{aligned}\n", "\\alpha(\\mathbf{q}, \\mathbf{k}) & = \\exp\\left(-\\frac{1}{2} \\|\\mathbf{q} - \\mathbf{k}\\|^2 \\right) && \\textrm{Gaussian;} \\\\\n", "\\alpha(\\mathbf{q}, \\mathbf{k}) & = 1 \\textrm{ if } \\|\\mathbf{q} - \\mathbf{k}\\| \\leq 1 && \\textrm{Boxcar;} \\\\\n", "\\alpha(\\mathbf{q}, \\mathbf{k}) & = \\mathop{\\mathrm{max}}\\left(0, 1 - \\|\\mathbf{q} - \\mathbf{k}\\|\\right) && \\textrm{Epanechikov.}\n", "\\end{aligned}\n", "$$\n", "\n", "There are many more choices that we could pick. See a [Wikipedia article](https://en.wikipedia.org/wiki/<PERSON><PERSON>_(statistics)) for a more extensive review and how the choice of kernels is related to kernel density estimation, sometimes also called *Parzen Windows* :cite:`parzen1957consistent`. All of the kernels are heuristic and can be tuned. For instance, we can adjust the width, not only on a global basis but even on a per-coordinate basis. Regardless, all of them lead to the following equation for regression and classification alike:\n", "\n", "$$f(\\mathbf{q}) = \\sum_i \\mathbf{v}_i \\frac{\\alpha(\\mathbf{q}, \\mathbf{k}_i)}{\\sum_j \\alpha(\\mathbf{q}, \\mathbf{k}_j)}.$$\n", "\n", "In the case of a (scalar) regression with observations $(\\mathbf{x}_i, y_i)$ for features and labels respectively, $\\mathbf{v}_i = y_i$ are scalars, $\\mathbf{k}_i = \\mathbf{x}_i$ are vectors, and the query $\\mathbf{q}$ denotes the new location where $f$ should be evaluated. In the case of (multiclass) classification, we use one-hot-encoding of $y_i$ to obtain $\\mathbf{v}_i$. One of the convenient properties of this estimator is that it requires no training. Even more so, if we suitably narrow the kernel with increasing amounts of data, the approach is consistent :cite:`mack1982weak`, i.e., it will converge to some statistically optimal solution. Let's start by inspecting some kernels.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "f0fc295b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:40.046262Z", "iopub.status.busy": "2023-08-18T19:42:40.045760Z", "iopub.status.idle": "2023-08-18T19:42:43.356438Z", "shell.execute_reply": "2023-08-18T19:42:43.355525Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import numpy as np\n", "import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l\n", "\n", "d2l.use_svg_display()"]}, {"cell_type": "markdown", "id": "3f207d4f", "metadata": {"origin_pos": 6}, "source": ["## [**Kernels and Data**]\n", "\n", "All the kernels $\\alpha(\\mathbf{k}, \\mathbf{q})$ defined in this section are *translation and rotation invariant*; that is, if we shift and rotate $\\mathbf{k}$ and $\\mathbf{q}$ in the same manner, the value of $\\alpha$ remains unchanged. For simplicity we thus pick scalar arguments $k, q \\in \\mathbb{R}$ and pick the key $k = 0$ as the origin. This yields:\n"]}, {"cell_type": "code", "execution_count": 8, "id": "a68a04dc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:43.360662Z", "iopub.status.busy": "2023-08-18T19:42:43.359961Z", "iopub.status.idle": "2023-08-18T19:42:43.367870Z", "shell.execute_reply": "2023-08-18T19:42:43.366629Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["# Define some kernels\n", "def gaussian(x):\n", "    return torch.exp(-x**2 / 2)\n", "\n", "def boxcar(x):\n", "    return torch.abs(x) < 1.0\n", "\n", "def constant(x):\n", "    return 1.0 + 0 * x\n", "\n", "def e<PERSON><PERSON><PERSON><PERSON>(x):\n", "    return torch.max(1 - torch.abs(x), torch.zeros_like(x))"]}, {"cell_type": "code", "execution_count": 9, "id": "3d70c7ee", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:43.371996Z", "iopub.status.busy": "2023-08-18T19:42:43.371175Z", "iopub.status.idle": "2023-08-18T19:42:44.008195Z", "shell.execute_reply": "2023-08-18T19:42:44.007251Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"706.903125pt\" height=\"211.07625pt\" viewBox=\"0 0 706.**********.07625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-13T18:45:46.546538</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 211.07625 \n", "L 706.**********.07625 \n", "L 706.903125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.52 \n", "L 175.**********.52 \n", "L 175.668342 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m0c0b661f9f\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m0c0b661f9f\" x=\"50.222992\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(42.851899 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m0c0b661f9f\" x=\"104.236059\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(101.054809 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m0c0b661f9f\" x=\"158.249126\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(155.067876 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- <PERSON><PERSON><PERSON> -->\n", "     <g transform=\"translate(79.947452 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-47\" d=\"M 3809 666 \n", "L 3809 1919 \n", "L 2778 1919 \n", "L 2778 2438 \n", "L 4434 2438 \n", "L 4434 434 \n", "Q 4069 175 3628 42 \n", "Q 3188 -91 2688 -91 \n", "Q 1594 -91 976 548 \n", "Q 359 1188 359 2328 \n", "Q 359 3472 976 4111 \n", "Q 1594 4750 2688 4750 \n", "Q 3144 4750 3555 4637 \n", "Q 3966 4525 4313 4306 \n", "L 4313 3634 \n", "Q 3963 3931 3569 4081 \n", "Q 3175 4231 2741 4231 \n", "Q 1884 4231 1454 3753 \n", "Q 1025 3275 1025 2328 \n", "Q 1025 1384 1454 906 \n", "Q 1884 428 2741 428 \n", "Q 3075 428 3337 486 \n", "Q 3600 544 3809 666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-47\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"77.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"138.769531\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"202.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"254.248047\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"306.347656\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"334.130859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"395.410156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"mb355e386b9\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"30.103125\" y=\"165.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 169.759219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"30.103125\" y=\"135.72\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 139.519219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"30.103125\" y=\"105.48\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 109.279219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"30.103125\" y=\"75.24\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 79.039219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"30.103125\" y=\"45\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 48.799219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"30.103125\" y=\"14.76\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 18.559219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_10\">\n", "    <path d=\"M 36.719726 159.316736 \n", "L 39.420377 157.472425 \n", "L 42.121034 155.223991 \n", "L 44.821684 152.515053 \n", "L 47.522342 149.290115 \n", "L 50.222996 145.497301 \n", "L 52.92365 141.091455 \n", "L 55.6243 136.037714 \n", "L 58.324951 130.315194 \n", "L 61.025605 123.920763 \n", "L 63.726259 116.872548 \n", "L 66.42691 109.21297 \n", "L 69.127567 101.010924 \n", "L 71.828221 92.363052 \n", "L 74.528875 83.393534 \n", "L 77.229527 74.252563 \n", "L 79.93018 65.113106 \n", "L 82.630834 56.166259 \n", "L 85.331486 47.615075 \n", "L 88.03214 39.667142 \n", "L 90.732792 32.52647 \n", "L 93.433446 26.384811 \n", "L 96.134099 21.413183 \n", "L 98.834753 17.753959 \n", "L 101.535406 15.514116 \n", "L 104.236059 14.76 \n", "L 106.936712 15.514116 \n", "L 109.637366 17.753959 \n", "L 112.338019 21.413183 \n", "L 115.038673 26.384811 \n", "L 117.739326 32.52647 \n", "L 120.43998 39.667142 \n", "L 123.140632 47.615075 \n", "L 125.841286 56.166268 \n", "L 128.541938 65.113106 \n", "L 131.242592 74.252563 \n", "L 133.943246 83.393552 \n", "L 136.6439 92.363061 \n", "L 139.344554 101.010933 \n", "L 142.045205 109.212961 \n", "L 144.745859 116.872548 \n", "L 147.446513 123.920763 \n", "L 150.147167 130.315194 \n", "L 152.847818 136.037714 \n", "L 155.548472 141.091462 \n", "L 158.249126 145.497305 \n", "L 160.949776 149.290115 \n", "L 163.650434 152.515053 \n", "L 166.351084 155.223991 \n", "L 169.051742 157.472425 \n", "\" clip-path=\"url(#pcef3823cad)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.52 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 175.**********.52 \n", "L 175.668342 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.52 \n", "L 175.**********.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 175.668342 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 204.781386 173.52 \n", "L 350.346603 173.52 \n", "L 350.346603 7.2 \n", "L 204.781386 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m0c0b661f9f\" x=\"224.901253\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(217.53016 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m0c0b661f9f\" x=\"278.91432\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(275.73307 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m0c0b661f9f\" x=\"332.927387\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(329.746137 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- Boxcar -->\n", "     <g transform=\"translate(260.489776 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-42\" d=\"M 1259 2228 \n", "L 1259 519 \n", "L 2272 519 \n", "Q 2781 519 3026 730 \n", "Q 3272 941 3272 1375 \n", "Q 3272 1813 3026 2020 \n", "Q 2781 2228 2272 2228 \n", "L 1259 2228 \n", "z\n", "M 1259 4147 \n", "L 1259 2741 \n", "L 2194 2741 \n", "Q 2656 2741 2882 2914 \n", "Q 3109 3088 3109 3444 \n", "Q 3109 3797 2882 3972 \n", "Q 2656 4147 2194 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2241 4666 \n", "Q 2963 4666 3353 4366 \n", "Q 3744 4066 3744 3513 \n", "Q 3744 3084 3544 2831 \n", "Q 3344 2578 2956 2516 \n", "Q 3422 2416 3680 2098 \n", "Q 3938 1781 3938 1306 \n", "Q 3938 681 3513 340 \n", "Q 3088 0 2303 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-42\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"68.603516\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"126.660156\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"184.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"239.070312\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"300.349609\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"204.781386\" y=\"165.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"204.781386\" y=\"135.72\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"204.781386\" y=\"105.48\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"204.781386\" y=\"75.24\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"204.781386\" y=\"45\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"204.781386\" y=\"14.76\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 211.397987 165.96 \n", "L 214.098637 165.96 \n", "L 216.799295 165.96 \n", "L 219.499945 165.96 \n", "L 222.200603 165.96 \n", "L 224.901257 165.96 \n", "L 227.601911 165.96 \n", "L 230.302561 165.96 \n", "L 233.003212 165.96 \n", "L 235.703866 165.96 \n", "L 238.40452 165.96 \n", "L 241.105171 165.96 \n", "L 243.805828 165.96 \n", "L 246.506482 165.96 \n", "L 249.207136 165.96 \n", "L 251.907788 14.76 \n", "L 254.608441 14.76 \n", "L 257.309095 14.76 \n", "L 260.009747 14.76 \n", "L 262.710401 14.76 \n", "L 265.411053 14.76 \n", "L 268.111706 14.76 \n", "L 270.81236 14.76 \n", "L 273.513014 14.76 \n", "L 276.213667 14.76 \n", "L 278.91432 14.76 \n", "L 281.614973 14.76 \n", "L 284.315627 14.76 \n", "L 287.01628 14.76 \n", "L 289.716933 14.76 \n", "L 292.417587 14.76 \n", "L 295.118241 14.76 \n", "L 297.818893 14.76 \n", "L 300.519547 14.76 \n", "L 303.220199 14.76 \n", "L 305.920853 165.96 \n", "L 308.621507 165.96 \n", "L 311.322161 165.96 \n", "L 314.022815 165.96 \n", "L 316.723466 165.96 \n", "L 319.42412 165.96 \n", "L 322.124774 165.96 \n", "L 324.825428 165.96 \n", "L 327.526079 165.96 \n", "L 330.226733 165.96 \n", "L 332.927387 165.96 \n", "L 335.628037 165.96 \n", "L 338.328695 165.96 \n", "L 341.029345 165.96 \n", "L 343.730002 165.96 \n", "\" clip-path=\"url(#p239f12a712)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 204.781386 173.52 \n", "L 204.781386 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 350.346603 173.52 \n", "L 350.346603 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 204.781386 173.52 \n", "L 350.346603 173.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 204.781386 7.2 \n", "L 350.346603 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 379.459647 173.52 \n", "L 525.024864 173.52 \n", "L 525.024864 7.2 \n", "L 379.459647 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m0c0b661f9f\" x=\"399.579514\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(392.20842 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m0c0b661f9f\" x=\"453.592581\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(450.411331 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m0c0b661f9f\" x=\"507.605647\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(504.424397 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- Constant -->\n", "     <g transform=\"translate(429.764912 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"194.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"246.484375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"285.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"346.972656\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"410.351562\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"379.459647\" y=\"165.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"379.459647\" y=\"135.72\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"379.459647\" y=\"105.48\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"379.459647\" y=\"75.24\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"379.459647\" y=\"45\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"379.459647\" y=\"14.76\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 386.076248 14.76 \n", "L 388.776898 14.76 \n", "L 391.477555 14.76 \n", "L 394.178206 14.76 \n", "L 396.878863 14.76 \n", "L 399.579517 14.76 \n", "L 402.280171 14.76 \n", "L 404.980822 14.76 \n", "L 407.681473 14.76 \n", "L 410.382127 14.76 \n", "L 413.082781 14.76 \n", "L 415.783432 14.76 \n", "L 418.484089 14.76 \n", "L 421.184743 14.76 \n", "L 423.885397 14.76 \n", "L 426.586049 14.76 \n", "L 429.286701 14.76 \n", "L 431.987355 14.76 \n", "L 434.688008 14.76 \n", "L 437.388662 14.76 \n", "L 440.089314 14.76 \n", "L 442.789967 14.76 \n", "L 445.49062 14.76 \n", "L 448.191274 14.76 \n", "L 450.891927 14.76 \n", "L 453.592581 14.76 \n", "L 456.293234 14.76 \n", "L 458.993888 14.76 \n", "L 461.694541 14.76 \n", "L 464.395194 14.76 \n", "L 467.095847 14.76 \n", "L 469.796501 14.76 \n", "L 472.497154 14.76 \n", "L 475.197808 14.76 \n", "L 477.89846 14.76 \n", "L 480.599114 14.76 \n", "L 483.299768 14.76 \n", "L 486.000422 14.76 \n", "L 488.701076 14.76 \n", "L 491.401727 14.76 \n", "L 494.102381 14.76 \n", "L 496.803035 14.76 \n", "L 499.503689 14.76 \n", "L 502.204339 14.76 \n", "L 504.904993 14.76 \n", "L 507.605647 14.76 \n", "L 510.306298 14.76 \n", "L 513.006955 14.76 \n", "L 515.707606 14.76 \n", "L 518.408263 14.76 \n", "\" clip-path=\"url(#p9dbaeb885d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 379.459647 173.52 \n", "L 379.459647 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 525.024864 173.52 \n", "L 525.024864 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 379.459647 173.52 \n", "L 525.024864 173.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 379.459647 7.2 \n", "L 525.024864 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 554.137908 173.52 \n", "L 699.703125 173.52 \n", "L 699.703125 7.2 \n", "L 554.137908 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m0c0b661f9f\" x=\"574.257775\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(566.886681 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m0c0b661f9f\" x=\"628.270842\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(625.089592 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#m0c0b661f9f\" x=\"682.283908\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(679.102658 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_22\">\n", "     <!-- <PERSON><PERSON><PERSON><PERSON>v -->\n", "     <g transform=\"translate(595.237704 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-45\" d=\"M 628 4666 \n", "L 3578 4666 \n", "L 3578 4134 \n", "L 1259 4134 \n", "L 1259 2753 \n", "L 3481 2753 \n", "L 3481 2222 \n", "L 1259 2222 \n", "L 1259 531 \n", "L 3634 531 \n", "L 3634 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-45\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"63.183594\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"126.660156\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"187.939453\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"251.318359\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"312.841797\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"367.822266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"431.201172\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"458.984375\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"513.269531\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"574.451172\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"554.137908\" y=\"165.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"554.137908\" y=\"135.72\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"554.137908\" y=\"105.48\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"554.137908\" y=\"75.24\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"554.137908\" y=\"45\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#mb355e386b9\" x=\"554.137908\" y=\"14.76\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 560.754508 165.96 \n", "L 563.455159 165.96 \n", "L 566.155816 165.96 \n", "L 568.856467 165.96 \n", "L 571.557124 165.96 \n", "L 574.257778 165.96 \n", "L 576.958432 165.96 \n", "L 579.659083 165.96 \n", "L 582.359734 165.96 \n", "L 585.060388 165.96 \n", "L 587.761042 165.96 \n", "L 590.461692 165.96 \n", "L 593.16235 165.96 \n", "L 595.863004 165.96 \n", "L 598.563658 165.96 \n", "L 601.26431 165.959991 \n", "L 603.964962 150.839996 \n", "L 606.665616 135.719993 \n", "L 609.366269 120.599998 \n", "L 612.066923 105.479995 \n", "L 614.767575 90.36 \n", "L 617.468228 75.239996 \n", "L 620.168881 60.120002 \n", "L 622.869535 44.999998 \n", "L 625.570188 29.880004 \n", "L 628.270842 14.76 \n", "L 630.971495 29.880004 \n", "L 633.672148 44.999998 \n", "L 636.372802 60.120002 \n", "L 639.073455 75.239996 \n", "L 641.774108 90.36 \n", "L 644.474762 105.480004 \n", "L 647.175415 120.599998 \n", "L 649.876069 135.720002 \n", "L 652.576721 150.839996 \n", "L 655.277375 165.96 \n", "L 657.978029 165.96 \n", "L 660.678683 165.96 \n", "L 663.379337 165.96 \n", "L 666.079988 165.96 \n", "L 668.780642 165.96 \n", "L 671.481296 165.96 \n", "L 674.18195 165.96 \n", "L 676.8826 165.96 \n", "L 679.583254 165.96 \n", "L 682.283908 165.96 \n", "L 684.984559 165.96 \n", "L 687.685216 165.96 \n", "L 690.385867 165.96 \n", "L 693.086524 165.96 \n", "\" clip-path=\"url(#p6a52490cd6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 554.137908 173.52 \n", "L 554.137908 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 699.703125 173.52 \n", "L 699.703125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 554.137908 173.52 \n", "L 699.703125 173.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 554.137908 7.2 \n", "L 699.703125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pcef3823cad\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"145.565217\" height=\"166.32\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p239f12a712\">\n", "   <rect x=\"204.781386\" y=\"7.2\" width=\"145.565217\" height=\"166.32\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p9dbaeb885d\">\n", "   <rect x=\"379.459647\" y=\"7.2\" width=\"145.565217\" height=\"166.32\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6a52490cd6\">\n", "   <rect x=\"554.137908\" y=\"7.2\" width=\"145.565217\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1200x300 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axes = d2l.plt.subplots(1, 4, sharey=True, figsize=(12, 3))\n", "\n", "kernels = (gaussian, boxcar, constant, epanechikov)\n", "names = ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>')\n", "x = torch.arange(-2.5, 2.5, 0.1)\n", "for kernel, name, ax in zip(kernels, names, axes):\n", "    ax.plot(x.detach().numpy(), kernel(x).detach().numpy())\n", "    ax.set_xlabel(name)\n", "\n", "d2l.plt.show()"]}, {"cell_type": "markdown", "id": "7fd6163d", "metadata": {"origin_pos": 9}, "source": ["Different kernels correspond to different notions of range and smoothness. For instance, the boxcar kernel only attends to observations within a distance of $1$ (or some otherwise defined hyperparameter) and does so indiscriminately. \n", "\n", "To see Na<PERSON><PERSON>--<PERSON> estimation in action, let's define some training data. In the following we use the dependency\n", "\n", "$$y_i = 2\\sin(x_i) + x_i + \\epsilon,$$\n", "\n", "where $\\epsilon$ is drawn from a normal distribution with zero mean and unit variance. We draw 40 training examples.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "9b5fddb2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:44.012599Z", "iopub.status.busy": "2023-08-18T19:42:44.012057Z", "iopub.status.idle": "2023-08-18T19:42:44.017824Z", "shell.execute_reply": "2023-08-18T19:42:44.016986Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["def f(x):\n", "    return 2 * torch.sin(x) + x\n", "\n", "n = 40\n", "x_train, _ = torch.sort(torch.rand(n) * 5)\n", "y_train = f(x_train) + torch.randn(n)\n", "x_val = torch.arange(0, 5, 0.1)\n", "y_val = f(x_val)"]}, {"cell_type": "markdown", "id": "6d262f53", "metadata": {"origin_pos": 11}, "source": ["## [**Attention Pooling via Na<PERSON><PERSON>--Watson Regression**]\n", "\n", "Now that we have data and kernels, all we need is a function that computes the kernel regression estimates. Note that we also want to obtain the relative kernel weights in order to perform some minor diagnostics. Hence we first compute the kernel between all training features (covariates) `x_train` and all validation features `x_val`. This yields a matrix, which we subsequently normalize. When multiplied with the training labels `y_train` we obtain the estimates.\n", "\n", "Recall attention pooling in :eqref:`eq_attention_pooling`. Let each validation feature be a query, and each training feature--label pair be a key--value pair. As a result, the  normalized relative kernel weights (`attention_w` below) are the *attention weights*.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "cb74933e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:44.021591Z", "iopub.status.busy": "2023-08-18T19:42:44.020986Z", "iopub.status.idle": "2023-08-18T19:42:44.026105Z", "shell.execute_reply": "2023-08-18T19:42:44.025184Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["def nadaraya_watson(x_train, y_train, x_val, kernel):\n", "    dists = x_train.reshape((-1, 1)) - x_val.reshape((1, -1))\n", "    # Each column/row corresponds to each query/key\n", "    k = kernel(dists).type(torch.float32)\n", "    # Normalization over keys for each query\n", "    attention_w = k / k.sum(0)\n", "    y_hat = y_train@attention_w\n", "    return y_hat, attention_w"]}, {"cell_type": "markdown", "id": "fffd3124", "metadata": {"origin_pos": 13}, "source": ["Let's have a look at the kind of estimates that the different kernels produce.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "4c1bf8b9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:44.030164Z", "iopub.status.busy": "2023-08-18T19:42:44.029419Z", "iopub.status.idle": "2023-08-18T19:42:44.038956Z", "shell.execute_reply": "2023-08-18T19:42:44.037815Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["def plot(x_train, y_train, x_val, y_val, kernels, names, attention=False):\n", "    fig, axes = d2l.plt.subplots(1, 4, sharey=True, figsize=(12, 3))\n", "    for kernel, name, ax in zip(kernels, names, axes):\n", "        y_hat, attention_w = nadar<PERSON>_watson(x_train, y_train, x_val, kernel)\n", "        if attention:\n", "            pcm = ax.imshow(attention_w.detach().numpy(), cmap='Reds')\n", "        else:\n", "            ax.plot(x_val, y_hat)\n", "            ax.plot(x_val, y_val, 'm--')\n", "            ax.plot(x_train, y_train, 'o', alpha=0.5);\n", "        ax.set_xlabel(name)\n", "        if not attention:\n", "            ax.legend(['y_hat', 'y'])\n", "    if attention:\n", "        fig.colorbar(pcm, ax=axes, shrink=0.7)"]}, {"cell_type": "code", "execution_count": 13, "id": "de5882da", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:44.042582Z", "iopub.status.busy": "2023-08-18T19:42:44.042080Z", "iopub.status.idle": "2023-08-18T19:42:44.864841Z", "shell.execute_reply": "2023-08-18T19:42:44.863524Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"705.742188pt\" height=\"211.07625pt\" viewBox=\"0 0 705.**********.07625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-13T18:45:46.747430</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 211.07625 \n", "L 705.**********.07625 \n", "L 705.742188 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 28.**********.52 \n", "L 174.**********.52 \n", "L 174.507405 7.2 \n", "L 28.942188 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m43f47c86c0\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m43f47c86c0\" x=\"35.558788\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(32.377538 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m43f47c86c0\" x=\"89.319886\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(86.138636 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m43f47c86c0\" x=\"143.080984\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(139.899734 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- <PERSON><PERSON><PERSON> -->\n", "     <g transform=\"translate(78.786515 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-47\" d=\"M 3809 666 \n", "L 3809 1919 \n", "L 2778 1919 \n", "L 2778 2438 \n", "L 4434 2438 \n", "L 4434 434 \n", "Q 4069 175 3628 42 \n", "Q 3188 -91 2688 -91 \n", "Q 1594 -91 976 548 \n", "Q 359 1188 359 2328 \n", "Q 359 3472 976 4111 \n", "Q 1594 4750 2688 4750 \n", "Q 3144 4750 3555 4637 \n", "Q 3966 4525 4313 4306 \n", "L 4313 3634 \n", "Q 3963 3931 3569 4081 \n", "Q 3175 4231 2741 4231 \n", "Q 1884 4231 1454 3753 \n", "Q 1025 3275 1025 2328 \n", "Q 1025 1384 1454 906 \n", "Q 1884 428 2741 428 \n", "Q 3075 428 3337 486 \n", "Q 3600 544 3809 666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-47\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"77.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"138.769531\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"202.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"254.248047\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"306.347656\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"334.130859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"395.410156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m210c6249c0\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"28.942188\" y=\"160.023978\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(7.2 163.823196) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"28.942188\" y=\"136.489429\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(15.579688 140.288647) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"28.942188\" y=\"112.954879\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(15.579688 116.754098) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"28.942188\" y=\"89.42033\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(15.579688 93.219549) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"28.942188\" y=\"65.885781\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(15.579688 69.685) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"28.942188\" y=\"42.351232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(15.579688 46.150451) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"28.942188\" y=\"18.816683\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(15.579688 22.615902) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 35.558788 88.075833 \n", "L 38.246843 86.488959 \n", "L 40.934898 84.904168 \n", "L 43.622953 83.325801 \n", "L 46.311008 81.758246 \n", "L 48.999063 80.205971 \n", "L 51.687118 78.673418 \n", "L 54.375172 77.16487 \n", "L 57.063228 75.684556 \n", "L 59.751283 74.236422 \n", "L 62.439337 72.824177 \n", "L 65.127393 71.45117 \n", "L 67.815448 70.120436 \n", "L 70.503504 68.834558 \n", "L 73.191559 67.59575 \n", "L 75.879612 66.40586 \n", "L 78.567667 65.266274 \n", "L 81.255723 64.178096 \n", "L 83.943778 63.142123 \n", "L 86.631831 62.159007 \n", "L 89.319886 61.229241 \n", "L 92.007939 60.353442 \n", "L 94.695997 59.532261 \n", "L 97.38405 58.766681 \n", "L 100.072108 58.05807 \n", "L 102.760161 57.408201 \n", "L 105.448219 56.819341 \n", "L 108.136272 56.294257 \n", "L 110.824324 55.836062 \n", "L 113.512376 55.448084 \n", "L 116.200435 55.133572 \n", "L 118.888487 54.895461 \n", "L 121.576546 54.735915 \n", "L 124.264599 54.656104 \n", "L 126.952657 54.655851 \n", "L 129.64071 54.733469 \n", "L 132.328762 54.885703 \n", "L 135.016814 55.10784 \n", "L 137.704873 55.393898 \n", "L 140.392925 55.73701 \n", "L 143.080984 56.129729 \n", "L 145.769036 56.564497 \n", "L 148.457089 57.033959 \n", "L 151.145154 57.531274 \n", "L 153.833206 58.050276 \n", "L 156.521259 58.585701 \n", "L 159.209311 59.133106 \n", "L 161.897376 59.688934 \n", "L 164.585428 60.250411 \n", "L 167.273481 60.815486 \n", "\" clip-path=\"url(#p0cd5f13e17)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 35.558788 136.489429 \n", "L 38.246843 129.436904 \n", "L 40.934898 122.431332 \n", "L 43.622953 115.519193 \n", "L 46.311008 108.746038 \n", "L 48.999063 102.156026 \n", "L 51.687118 95.791486 \n", "L 54.375172 89.692501 \n", "L 57.063228 83.896486 \n", "L 59.751283 78.437843 \n", "L 62.439337 73.3476 \n", "L 65.127393 68.653095 \n", "L 67.815448 64.377727 \n", "L 70.503504 60.540695 \n", "L 73.191559 57.156824 \n", "L 75.879612 54.236418 \n", "L 78.567667 51.785119 \n", "L 81.255723 49.803928 \n", "L 83.943778 48.289106 \n", "L 86.631831 47.232294 \n", "L 89.319886 46.620519 \n", "L 92.007939 46.436386 \n", "L 94.695997 46.658225 \n", "L 97.38405 47.260293 \n", "L 100.072108 48.21307 \n", "L 102.760161 49.483513 \n", "L 105.448219 51.035418 \n", "L 108.136272 52.829761 \n", "L 110.824324 54.825098 \n", "L 113.512376 56.977983 \n", "L 116.200435 59.243392 \n", "L 118.888487 61.575158 \n", "L 121.576546 63.92649 \n", "L 124.264599 66.250361 \n", "L 126.952657 68.500053 \n", "L 129.64071 70.629557 \n", "L 132.328762 72.594089 \n", "L 135.016814 74.350501 \n", "L 137.704873 75.857737 \n", "L 140.392925 77.077219 \n", "L 143.080984 77.973246 \n", "L 145.769036 78.513346 \n", "L 148.457089 78.668604 \n", "L 151.145154 78.413973 \n", "L 153.833206 77.728463 \n", "L 156.521259 76.595419 \n", "L 159.209311 75.002643 \n", "L 161.897376 72.942526 \n", "L 164.585428 70.412156 \n", "L 167.273481 67.413295 \n", "\" clip-path=\"url(#p0cd5f13e17)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <defs>\n", "     <path id=\"m2bbd52720c\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p0cd5f13e17)\">\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"39.410258\" y=\"165.96\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"40.082135\" y=\"131.741311\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"47.58544\" y=\"144.245018\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"47.677198\" y=\"126.530533\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"50.170511\" y=\"71.247381\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"50.391086\" y=\"52.975873\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"53.555667\" y=\"100.315599\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"56.435086\" y=\"88.056856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"61.528514\" y=\"82.561185\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"63.795402\" y=\"77.573569\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"64.743501\" y=\"39.173367\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"65.563693\" y=\"45.929936\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"66.972522\" y=\"83.762051\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"72.159444\" y=\"14.76\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"74.475769\" y=\"26.360659\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"76.002158\" y=\"75.252049\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"76.322961\" y=\"69.851335\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"76.82929\" y=\"76.762707\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"78.495094\" y=\"54.77645\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"79.014039\" y=\"28.813159\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"79.631025\" y=\"92.734646\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"88.579366\" y=\"58.711496\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"90.200734\" y=\"86.826696\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"90.678954\" y=\"31.790484\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"99.125692\" y=\"24.138427\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"101.969953\" y=\"103.375856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"102.643674\" y=\"15.568769\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"104.904751\" y=\"75.911378\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"107.567925\" y=\"62.085087\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"108.187536\" y=\"36.447934\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"112.228843\" y=\"39.733094\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"125.037676\" y=\"38.259749\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"136.378317\" y=\"35.985323\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"142.11124\" y=\"62.194626\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"144.393601\" y=\"69.89927\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"146.377887\" y=\"56.969112\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"153.434539\" y=\"58.879761\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"154.662123\" y=\"58.210348\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"159.580036\" y=\"43.846998\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"167.890804\" y=\"101.692038\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 28.**********.52 \n", "L 28.942188 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 174.**********.52 \n", "L 174.507405 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 28.**********.52 \n", "L 174.**********.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 28.942188 7.2 \n", "L 174.507405 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 108.202717 168.52 \n", "L 167.507405 168.52 \n", "Q 169.507405 168.52 169.507405 166.52 \n", "L 169.507405 137.885625 \n", "Q 169.507405 135.885625 167.507405 135.885625 \n", "L 108.202717 135.885625 \n", "Q 106.202717 135.885625 106.202717 137.885625 \n", "L 106.202717 166.52 \n", "Q 106.202717 168.52 108.202717 168.52 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_14\">\n", "     <path d=\"M 110.202717 143.984063 \n", "L 120.202717 143.984063 \n", "L 130.202717 143.984063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- y_hat -->\n", "     <g transform=\"translate(138.202717 147.484063) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"109.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"172.558594\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"233.837891\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_15\">\n", "     <path d=\"M 110.202717 158.940313 \n", "L 120.202717 158.940313 \n", "L 130.202717 158.940313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- y -->\n", "     <g transform=\"translate(138.202717 162.440313) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 203.620448 173.52 \n", "L 349.185666 173.52 \n", "L 349.185666 7.2 \n", "L 203.620448 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m43f47c86c0\" x=\"210.237049\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(207.055799 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m43f47c86c0\" x=\"263.998147\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(260.816897 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m43f47c86c0\" x=\"317.759245\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(314.577995 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Boxcar -->\n", "     <g transform=\"translate(259.328838 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-42\" d=\"M 1259 2228 \n", "L 1259 519 \n", "L 2272 519 \n", "Q 2781 519 3026 730 \n", "Q 3272 941 3272 1375 \n", "Q 3272 1813 3026 2020 \n", "Q 2781 2228 2272 2228 \n", "L 1259 2228 \n", "z\n", "M 1259 4147 \n", "L 1259 2741 \n", "L 2194 2741 \n", "Q 2656 2741 2882 2914 \n", "Q 3109 3088 3109 3444 \n", "Q 3109 3797 2882 3972 \n", "Q 2656 4147 2194 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2241 4666 \n", "Q 2963 4666 3353 4366 \n", "Q 3744 4066 3744 3513 \n", "Q 3744 3084 3544 2831 \n", "Q 3344 2578 2956 2516 \n", "Q 3422 2416 3680 2098 \n", "Q 3938 1781 3938 1306 \n", "Q 3938 681 3513 340 \n", "Q 3088 0 2303 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-42\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"68.603516\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"126.660156\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"184.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"239.070312\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"300.349609\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"203.620448\" y=\"160.023978\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"203.620448\" y=\"136.489429\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"203.620448\" y=\"112.954879\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"203.620448\" y=\"89.42033\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"203.620448\" y=\"65.885781\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"203.620448\" y=\"42.351232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"203.620448\" y=\"18.816683\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 210.237049 107.070417 \n", "L 212.925104 98.216423 \n", "L 215.613159 93.082515 \n", "L 218.301214 93.082515 \n", "L 220.989269 87.488045 \n", "L 223.677324 83.41289 \n", "L 226.365379 80.412413 \n", "L 229.053433 78.54208 \n", "L 231.741489 78.54208 \n", "L 234.429544 78.54208 \n", "L 237.117598 77.640689 \n", "L 239.805654 76.113018 \n", "L 242.493709 69.500497 \n", "L 245.181765 69.500497 \n", "L 247.86982 67.528238 \n", "L 250.557873 60.926894 \n", "L 253.245928 61.500301 \n", "L 255.933984 59.762551 \n", "L 258.622039 57.41669 \n", "L 261.310092 56.647838 \n", "L 263.998147 55.469956 \n", "L 266.686199 55.179606 \n", "L 269.374258 54.105567 \n", "L 272.06231 54.105567 \n", "L 274.750369 55.411107 \n", "L 277.438422 57.11996 \n", "L 280.12648 53.415136 \n", "L 282.814533 52.077176 \n", "L 285.502585 50.736185 \n", "L 288.190637 50.736185 \n", "L 290.878696 51.026446 \n", "L 293.566748 51.236317 \n", "L 296.254807 51.714055 \n", "L 298.942859 51.714055 \n", "L 301.630918 54.609161 \n", "L 304.31897 54.05233 \n", "L 307.007023 51.866429 \n", "L 309.695075 50.042623 \n", "L 312.383134 51.553146 \n", "L 315.071186 53.030643 \n", "L 317.759245 58.437462 \n", "L 320.447297 58.437462 \n", "L 323.13535 58.437462 \n", "L 325.823415 58.437462 \n", "L 328.511467 60.959685 \n", "L 331.199519 60.959685 \n", "L 333.887572 60.959685 \n", "L 336.575637 60.959685 \n", "L 339.263689 64.527452 \n", "L 341.951742 64.527452 \n", "\" clip-path=\"url(#p1513e37ae6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 210.237049 136.489429 \n", "L 212.925104 129.436904 \n", "L 215.613159 122.431332 \n", "L 218.301214 115.519193 \n", "L 220.989269 108.746038 \n", "L 223.677324 102.156026 \n", "L 226.365379 95.791486 \n", "L 229.053433 89.692501 \n", "L 231.741489 83.896486 \n", "L 234.429544 78.437843 \n", "L 237.117598 73.3476 \n", "L 239.805654 68.653095 \n", "L 242.493709 64.377727 \n", "L 245.181765 60.540695 \n", "L 247.86982 57.156824 \n", "L 250.557873 54.236418 \n", "L 253.245928 51.785119 \n", "L 255.933984 49.803928 \n", "L 258.622039 48.289106 \n", "L 261.310092 47.232294 \n", "L 263.998147 46.620519 \n", "L 266.686199 46.436386 \n", "L 269.374258 46.658225 \n", "L 272.06231 47.260293 \n", "L 274.750369 48.21307 \n", "L 277.438422 49.483513 \n", "L 280.12648 51.035418 \n", "L 282.814533 52.829761 \n", "L 285.502585 54.825098 \n", "L 288.190637 56.977983 \n", "L 290.878696 59.243392 \n", "L 293.566748 61.575158 \n", "L 296.254807 63.92649 \n", "L 298.942859 66.250361 \n", "L 301.630918 68.500053 \n", "L 304.31897 70.629557 \n", "L 307.007023 72.594089 \n", "L 309.695075 74.350501 \n", "L 312.383134 75.857737 \n", "L 315.071186 77.077219 \n", "L 317.759245 77.973246 \n", "L 320.447297 78.513346 \n", "L 323.13535 78.668604 \n", "L 325.823415 78.413973 \n", "L 328.511467 77.728463 \n", "L 331.199519 76.595419 \n", "L 333.887572 75.002643 \n", "L 336.575637 72.942526 \n", "L 339.263689 70.412156 \n", "L 341.951742 67.413295 \n", "\" clip-path=\"url(#p1513e37ae6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <g clip-path=\"url(#p1513e37ae6)\">\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"214.088519\" y=\"165.96\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"214.760396\" y=\"131.741311\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"222.263701\" y=\"144.245018\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"222.355459\" y=\"126.530533\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"224.848771\" y=\"71.247381\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"225.069347\" y=\"52.975873\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"228.233927\" y=\"100.315599\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"231.113347\" y=\"88.056856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"236.206775\" y=\"82.561185\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"238.473663\" y=\"77.573569\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"239.421762\" y=\"39.173367\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"240.241953\" y=\"45.929936\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"241.650782\" y=\"83.762051\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"246.837705\" y=\"14.76\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"249.15403\" y=\"26.360659\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"250.680419\" y=\"75.252049\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"251.001222\" y=\"69.851335\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"251.507551\" y=\"76.762707\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"253.173355\" y=\"54.77645\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"253.692299\" y=\"28.813159\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"254.309286\" y=\"92.734646\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"263.257627\" y=\"58.711496\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"264.878995\" y=\"86.826696\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"265.357215\" y=\"31.790484\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"273.803953\" y=\"24.138427\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"276.648214\" y=\"103.375856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"277.321935\" y=\"15.568769\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"279.583012\" y=\"75.911378\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"282.246185\" y=\"62.085087\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"282.865797\" y=\"36.447934\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"286.907104\" y=\"39.733094\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"299.715936\" y=\"38.259749\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"311.056578\" y=\"35.985323\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"316.7895\" y=\"62.194626\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"319.071861\" y=\"69.89927\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"321.056148\" y=\"56.969112\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"328.1128\" y=\"58.879761\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"329.340384\" y=\"58.210348\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"334.258296\" y=\"43.846998\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"342.569065\" y=\"101.692038\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 203.620448 173.52 \n", "L 203.620448 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 349.185666 173.52 \n", "L 349.185666 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 203.620448 173.52 \n", "L 349.185666 173.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 203.620448 7.2 \n", "L 349.185666 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_2\">\n", "    <g id=\"patch_13\">\n", "     <path d=\"M 282.880978 168.52 \n", "L 342.185666 168.52 \n", "Q 344.185666 168.52 344.185666 166.52 \n", "L 344.185666 137.885625 \n", "Q 344.185666 135.885625 342.185666 135.885625 \n", "L 282.880978 135.885625 \n", "Q 280.880978 135.885625 280.880978 137.885625 \n", "L 280.880978 166.52 \n", "Q 280.880978 168.52 282.880978 168.52 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_29\">\n", "     <path d=\"M 284.880978 143.984063 \n", "L 294.880978 143.984063 \n", "L 304.880978 143.984063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- y_hat -->\n", "     <g transform=\"translate(312.880978 147.484063) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"109.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"172.558594\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"233.837891\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 284.880978 158.940313 \n", "L 294.880978 158.940313 \n", "L 304.880978 158.940313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- y -->\n", "     <g transform=\"translate(312.880978 162.440313) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 378.298709 173.52 \n", "L 523.863927 173.52 \n", "L 523.863927 7.2 \n", "L 378.298709 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m43f47c86c0\" x=\"384.91531\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(381.73406 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m43f47c86c0\" x=\"438.676408\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(435.495158 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#m43f47c86c0\" x=\"492.437506\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(489.256256 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_23\">\n", "     <!-- Constant -->\n", "     <g transform=\"translate(428.603974 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"194.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"246.484375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"285.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"346.972656\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"410.351562\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"378.298709\" y=\"160.023978\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"378.298709\" y=\"136.489429\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"378.298709\" y=\"112.954879\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"378.298709\" y=\"89.42033\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"378.298709\" y=\"65.885781\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"378.298709\" y=\"42.351232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"378.298709\" y=\"18.816683\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 384.91531 67.747749 \n", "L 387.603365 67.747749 \n", "L 390.29142 67.747749 \n", "L 392.979475 67.747749 \n", "L 395.66753 67.747749 \n", "L 398.355585 67.747749 \n", "L 401.04364 67.747749 \n", "L 403.731694 67.747749 \n", "L 406.41975 67.747749 \n", "L 409.107805 67.747749 \n", "L 411.795859 67.747749 \n", "L 414.483915 67.747749 \n", "L 417.17197 67.747749 \n", "L 419.860026 67.747749 \n", "L 422.548081 67.747749 \n", "L 425.236133 67.747749 \n", "L 427.924189 67.747749 \n", "L 430.612245 67.747749 \n", "L 433.3003 67.747749 \n", "L 435.988352 67.747749 \n", "L 438.676408 67.747749 \n", "L 441.36446 67.747749 \n", "L 444.052519 67.747749 \n", "L 446.740571 67.747749 \n", "L 449.42863 67.747749 \n", "L 452.116682 67.747749 \n", "L 454.804741 67.747749 \n", "L 457.492793 67.747749 \n", "L 460.180846 67.747749 \n", "L 462.868898 67.747749 \n", "L 465.556957 67.747749 \n", "L 468.245009 67.747749 \n", "L 470.933068 67.747749 \n", "L 473.62112 67.747749 \n", "L 476.309179 67.747749 \n", "L 478.997231 67.747749 \n", "L 481.685284 67.747749 \n", "L 484.373336 67.747749 \n", "L 487.061395 67.747749 \n", "L 489.749447 67.747749 \n", "L 492.437506 67.747749 \n", "L 495.125558 67.747749 \n", "L 497.81361 67.747749 \n", "L 500.501676 67.747749 \n", "L 503.189728 67.747749 \n", "L 505.87778 67.747749 \n", "L 508.565833 67.747749 \n", "L 511.253898 67.747749 \n", "L 513.94195 67.747749 \n", "L 516.630002 67.747749 \n", "\" clip-path=\"url(#pa2a0942a0a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 384.91531 136.489429 \n", "L 387.603365 129.436904 \n", "L 390.29142 122.431332 \n", "L 392.979475 115.519193 \n", "L 395.66753 108.746038 \n", "L 398.355585 102.156026 \n", "L 401.04364 95.791486 \n", "L 403.731694 89.692501 \n", "L 406.41975 83.896486 \n", "L 409.107805 78.437843 \n", "L 411.795859 73.3476 \n", "L 414.483915 68.653095 \n", "L 417.17197 64.377727 \n", "L 419.860026 60.540695 \n", "L 422.548081 57.156824 \n", "L 425.236133 54.236418 \n", "L 427.924189 51.785119 \n", "L 430.612245 49.803928 \n", "L 433.3003 48.289106 \n", "L 435.988352 47.232294 \n", "L 438.676408 46.620519 \n", "L 441.36446 46.436386 \n", "L 444.052519 46.658225 \n", "L 446.740571 47.260293 \n", "L 449.42863 48.21307 \n", "L 452.116682 49.483513 \n", "L 454.804741 51.035418 \n", "L 457.492793 52.829761 \n", "L 460.180846 54.825098 \n", "L 462.868898 56.977983 \n", "L 465.556957 59.243392 \n", "L 468.245009 61.575158 \n", "L 470.933068 63.92649 \n", "L 473.62112 66.250361 \n", "L 476.309179 68.500053 \n", "L 478.997231 70.629557 \n", "L 481.685284 72.594089 \n", "L 484.373336 74.350501 \n", "L 487.061395 75.857737 \n", "L 489.749447 77.077219 \n", "L 492.437506 77.973246 \n", "L 495.125558 78.513346 \n", "L 497.81361 78.668604 \n", "L 500.501676 78.413973 \n", "L 503.189728 77.728463 \n", "L 505.87778 76.595419 \n", "L 508.565833 75.002643 \n", "L 511.253898 72.942526 \n", "L 513.94195 70.412156 \n", "L 516.630002 67.413295 \n", "\" clip-path=\"url(#pa2a0942a0a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <g clip-path=\"url(#pa2a0942a0a)\">\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"388.76678\" y=\"165.96\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"389.438657\" y=\"131.741311\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"396.941962\" y=\"144.245018\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"397.03372\" y=\"126.530533\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"399.527032\" y=\"71.247381\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"399.747608\" y=\"52.975873\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"402.912188\" y=\"100.315599\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"405.791608\" y=\"88.056856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"410.885036\" y=\"82.561185\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"413.151924\" y=\"77.573569\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"414.100023\" y=\"39.173367\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"414.920214\" y=\"45.929936\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"416.329043\" y=\"83.762051\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"421.515966\" y=\"14.76\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"423.832291\" y=\"26.360659\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"425.35868\" y=\"75.252049\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"425.679483\" y=\"69.851335\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"426.185812\" y=\"76.762707\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"427.851615\" y=\"54.77645\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"428.37056\" y=\"28.813159\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"428.987547\" y=\"92.734646\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"437.935888\" y=\"58.711496\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"439.557256\" y=\"86.826696\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"440.035476\" y=\"31.790484\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"448.482214\" y=\"24.138427\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"451.326475\" y=\"103.375856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"452.000196\" y=\"15.568769\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"454.261273\" y=\"75.911378\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"456.924446\" y=\"62.085087\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"457.544058\" y=\"36.447934\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"461.585365\" y=\"39.733094\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"474.394197\" y=\"38.259749\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"485.734839\" y=\"35.985323\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"491.467761\" y=\"62.194626\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"493.750122\" y=\"69.89927\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"495.734409\" y=\"56.969112\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"502.791061\" y=\"58.879761\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"504.018645\" y=\"58.210348\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"508.936557\" y=\"43.846998\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"517.247326\" y=\"101.692038\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 378.298709 173.52 \n", "L 378.298709 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 523.863927 173.52 \n", "L 523.863927 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 378.298709 173.52 \n", "L 523.863927 173.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 378.298709 7.2 \n", "L 523.863927 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_3\">\n", "    <g id=\"patch_19\">\n", "     <path d=\"M 457.559239 168.52 \n", "L 516.863927 168.52 \n", "Q 518.863927 168.52 518.863927 166.52 \n", "L 518.863927 137.885625 \n", "Q 518.863927 135.885625 516.863927 135.885625 \n", "L 457.559239 135.885625 \n", "Q 455.559239 135.885625 455.559239 137.885625 \n", "L 455.559239 166.52 \n", "Q 455.559239 168.52 457.559239 168.52 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_44\">\n", "     <path d=\"M 459.559239 143.984063 \n", "L 469.559239 143.984063 \n", "L 479.559239 143.984063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_24\">\n", "     <!-- y_hat -->\n", "     <g transform=\"translate(487.559239 147.484063) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"109.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"172.558594\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"233.837891\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_45\">\n", "     <path d=\"M 459.559239 158.940313 \n", "L 469.559239 158.940313 \n", "L 479.559239 158.940313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- y -->\n", "     <g transform=\"translate(487.559239 162.440313) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 552.97697 173.52 \n", "L 698.542188 173.52 \n", "L 698.542188 7.2 \n", "L 552.97697 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#m43f47c86c0\" x=\"559.593571\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_26\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(556.412321 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_47\">\n", "      <g>\n", "       <use xlink:href=\"#m43f47c86c0\" x=\"613.354669\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(610.173419 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#m43f47c86c0\" x=\"667.115767\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(663.934517 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_29\">\n", "     <!-- <PERSON><PERSON><PERSON><PERSON>v -->\n", "     <g transform=\"translate(594.076766 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-45\" d=\"M 628 4666 \n", "L 3578 4666 \n", "L 3578 4134 \n", "L 1259 4134 \n", "L 1259 2753 \n", "L 3481 2753 \n", "L 3481 2222 \n", "L 1259 2222 \n", "L 1259 531 \n", "L 3634 531 \n", "L 3634 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-45\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"63.183594\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"126.660156\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"187.939453\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"251.318359\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"312.841797\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"367.822266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"431.201172\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"458.984375\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"513.269531\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"574.451172\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_49\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"552.97697\" y=\"160.023978\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"552.97697\" y=\"136.489429\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_51\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"552.97697\" y=\"112.954879\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"552.97697\" y=\"89.42033\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_53\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"552.97697\" y=\"65.885781\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"552.97697\" y=\"42.351232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_55\">\n", "      <g>\n", "       <use xlink:href=\"#m210c6249c0\" x=\"552.97697\" y=\"18.816683\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 559.593571 119.748355 \n", "L 562.281626 116.955839 \n", "L 564.969681 111.529754 \n", "L 567.657736 106.114077 \n", "L 570.345791 101.492253 \n", "L 573.033845 95.982487 \n", "L 575.721901 90.38546 \n", "L 578.409955 85.178981 \n", "L 581.09801 80.385357 \n", "L 583.786066 75.965508 \n", "L 586.47412 71.819918 \n", "L 589.162175 67.717606 \n", "L 591.850231 64.793659 \n", "L 594.538286 62.437574 \n", "L 597.226342 60.258548 \n", "L 599.914394 59.310478 \n", "L 602.60245 58.597883 \n", "L 605.290505 58.069112 \n", "L 607.978561 57.492692 \n", "L 610.666613 57.007357 \n", "L 613.354669 56.477963 \n", "L 616.042721 55.957525 \n", "L 618.73078 55.38946 \n", "L 621.418832 54.965992 \n", "L 624.106891 54.439208 \n", "L 626.794943 53.495661 \n", "L 629.483002 52.826647 \n", "L 632.171054 51.765974 \n", "L 634.859107 50.815599 \n", "L 637.547159 49.710918 \n", "L 640.235218 48.636931 \n", "L 642.92327 48.228843 \n", "L 645.611329 48.240452 \n", "L 648.299381 48.298858 \n", "L 650.98744 48.546896 \n", "L 653.675492 49.334112 \n", "L 656.363545 50.460799 \n", "L 659.051597 51.86786 \n", "L 661.739656 53.223653 \n", "L 664.427708 54.590611 \n", "L 667.115767 56.188533 \n", "L 669.803819 57.421072 \n", "L 672.491871 58.419597 \n", "L 675.179937 59.457724 \n", "L 677.867989 60.25444 \n", "L 680.556041 61.089974 \n", "L 683.244093 62.09223 \n", "L 685.932159 64.077197 \n", "L 688.620211 66.446799 \n", "L 691.308263 69.117692 \n", "\" clip-path=\"url(#p7fcbdc8f8c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 559.593571 136.489429 \n", "L 562.281626 129.436904 \n", "L 564.969681 122.431332 \n", "L 567.657736 115.519193 \n", "L 570.345791 108.746038 \n", "L 573.033845 102.156026 \n", "L 575.721901 95.791486 \n", "L 578.409955 89.692501 \n", "L 581.09801 83.896486 \n", "L 583.786066 78.437843 \n", "L 586.47412 73.3476 \n", "L 589.162175 68.653095 \n", "L 591.850231 64.377727 \n", "L 594.538286 60.540695 \n", "L 597.226342 57.156824 \n", "L 599.914394 54.236418 \n", "L 602.60245 51.785119 \n", "L 605.290505 49.803928 \n", "L 607.978561 48.289106 \n", "L 610.666613 47.232294 \n", "L 613.354669 46.620519 \n", "L 616.042721 46.436386 \n", "L 618.73078 46.658225 \n", "L 621.418832 47.260293 \n", "L 624.106891 48.21307 \n", "L 626.794943 49.483513 \n", "L 629.483002 51.035418 \n", "L 632.171054 52.829761 \n", "L 634.859107 54.825098 \n", "L 637.547159 56.977983 \n", "L 640.235218 59.243392 \n", "L 642.92327 61.575158 \n", "L 645.611329 63.92649 \n", "L 648.299381 66.250361 \n", "L 650.98744 68.500053 \n", "L 653.675492 70.629557 \n", "L 656.363545 72.594089 \n", "L 659.051597 74.350501 \n", "L 661.739656 75.857737 \n", "L 664.427708 77.077219 \n", "L 667.115767 77.973246 \n", "L 669.803819 78.513346 \n", "L 672.491871 78.668604 \n", "L 675.179937 78.413973 \n", "L 677.867989 77.728463 \n", "L 680.556041 76.595419 \n", "L 683.244093 75.002643 \n", "L 685.932159 72.942526 \n", "L 688.620211 70.412156 \n", "L 691.308263 67.413295 \n", "\" clip-path=\"url(#p7fcbdc8f8c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <g clip-path=\"url(#p7fcbdc8f8c)\">\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"563.445041\" y=\"165.96\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"564.116918\" y=\"131.741311\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"571.620223\" y=\"144.245018\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"571.711981\" y=\"126.530533\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"574.205293\" y=\"71.247381\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"574.425869\" y=\"52.975873\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"577.590449\" y=\"100.315599\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"580.469869\" y=\"88.056856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"585.563296\" y=\"82.561185\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"587.830185\" y=\"77.573569\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"588.778284\" y=\"39.173367\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"589.598475\" y=\"45.929936\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"591.007304\" y=\"83.762051\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"596.194227\" y=\"14.76\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"598.510551\" y=\"26.360659\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"600.036941\" y=\"75.252049\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"600.357744\" y=\"69.851335\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"600.864073\" y=\"76.762707\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"602.529876\" y=\"54.77645\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"603.048821\" y=\"28.813159\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"603.665808\" y=\"92.734646\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"612.614149\" y=\"58.711496\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"614.235517\" y=\"86.826696\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"614.713736\" y=\"31.790484\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"623.160475\" y=\"24.138427\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"626.004735\" y=\"103.375856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"626.678457\" y=\"15.568769\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"628.939534\" y=\"75.911378\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"631.602707\" y=\"62.085087\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"632.222319\" y=\"36.447934\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"636.263626\" y=\"39.733094\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"649.072458\" y=\"38.259749\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"660.4131\" y=\"35.985323\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"666.146022\" y=\"62.194626\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"668.428383\" y=\"69.89927\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"670.41267\" y=\"56.969112\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"677.469322\" y=\"58.879761\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"678.696906\" y=\"58.210348\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"683.614818\" y=\"43.846998\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#m2bbd52720c\" x=\"691.925587\" y=\"101.692038\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 552.97697 173.52 \n", "L 552.97697 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 698.542188 173.52 \n", "L 698.542188 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 552.97697 173.52 \n", "L 698.542188 173.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 552.97697 7.2 \n", "L 698.542188 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_4\">\n", "    <g id=\"patch_25\">\n", "     <path d=\"M 632.2375 168.52 \n", "L 691.542188 168.52 \n", "Q 693.542188 168.52 693.542188 166.52 \n", "L 693.542188 137.885625 \n", "Q 693.542188 135.885625 691.542188 135.885625 \n", "L 632.2375 135.885625 \n", "Q 630.2375 135.885625 630.2375 137.885625 \n", "L 630.2375 166.52 \n", "Q 630.2375 168.52 632.2375 168.52 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_59\">\n", "     <path d=\"M 634.2375 143.984063 \n", "L 644.2375 143.984063 \n", "L 654.2375 143.984063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_30\">\n", "     <!-- y_hat -->\n", "     <g transform=\"translate(662.2375 147.484063) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"109.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"172.558594\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"233.837891\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_60\">\n", "     <path d=\"M 634.2375 158.940313 \n", "L 644.2375 158.940313 \n", "L 654.2375 158.940313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_31\">\n", "     <!-- y -->\n", "     <g transform=\"translate(662.2375 162.440313) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p0cd5f13e17\">\n", "   <rect x=\"28.942188\" y=\"7.2\" width=\"145.565217\" height=\"166.32\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p1513e37ae6\">\n", "   <rect x=\"203.620448\" y=\"7.2\" width=\"145.565217\" height=\"166.32\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pa2a0942a0a\">\n", "   <rect x=\"378.298709\" y=\"7.2\" width=\"145.565217\" height=\"166.32\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p7fcbdc8f8c\">\n", "   <rect x=\"552.97697\" y=\"7.2\" width=\"145.565217\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1200x300 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot(x_train, y_train, x_val, y_val, kernels, names)"]}, {"cell_type": "markdown", "id": "875d879e", "metadata": {"origin_pos": 16}, "source": ["The first thing that stands out is that all three nontrivial kernels (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>ech<PERSON><PERSON>) produce fairly workable estimates that are not too far from the true function. Only the constant kernel that leads to the trivial estimate $f(x) = \\frac{1}{n} \\sum_i y_i$ produces a rather unrealistic result. Let's inspect the attention weighting a bit more closely:\n"]}, {"cell_type": "code", "execution_count": 14, "id": "7b7e2f23", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:44.871160Z", "iopub.status.busy": "2023-08-18T19:42:44.870386Z", "iopub.status.idle": "2023-08-18T19:42:45.626544Z", "shell.execute_reply": "2023-08-18T19:42:45.625051Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"632.009325pt\" height=\"149.54912pt\" viewBox=\"0 0 632.**********.54912\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-13T18:45:46.924886</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 149.54912 \n", "L 632.**********.54912 \n", "L 632.009325 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 26.925 111.99287 \n", "L 143.**********.99287 \n", "L 143.377174 18.83113 \n", "L 26.925 18.83113 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p8ae9c3aaee)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image015062eb84\" transform=\"scale(1 -1) translate(0 -93.6)\" x=\"26.925\" y=\"-18.39287\" width=\"116.64\" height=\"93.6\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"me03e9d8453\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me03e9d8453\" x=\"28.089522\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(24.908272 126.591307) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#me03e9d8453\" x=\"74.670391\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(68.307891 126.591307) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#me03e9d8453\" x=\"121.251261\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(114.888761 126.591307) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- <PERSON><PERSON><PERSON> -->\n", "     <g transform=\"translate(62.212806 140.269432) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-47\" d=\"M 3809 666 \n", "L 3809 1919 \n", "L 2778 1919 \n", "L 2778 2438 \n", "L 4434 2438 \n", "L 4434 434 \n", "Q 4069 175 3628 42 \n", "Q 3188 -91 2688 -91 \n", "Q 1594 -91 976 548 \n", "Q 359 1188 359 2328 \n", "Q 359 3472 976 4111 \n", "Q 1594 4750 2688 4750 \n", "Q 3144 4750 3555 4637 \n", "Q 3966 4525 4313 4306 \n", "L 4313 3634 \n", "Q 3963 3931 3569 4081 \n", "Q 3175 4231 2741 4231 \n", "Q 1884 4231 1454 3753 \n", "Q 1025 3275 1025 2328 \n", "Q 1025 1384 1454 906 \n", "Q 1884 428 2741 428 \n", "Q 3075 428 3337 486 \n", "Q 3600 544 3809 666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-47\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"77.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"138.769531\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"202.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"254.248047\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"306.347656\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"334.130859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"395.410156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m9326af4fd0\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"26.925\" y=\"19.995652\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(13.5625 23.794871) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"26.925\" y=\"43.286087\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(7.2 47.085306) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"26.925\" y=\"66.576522\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(7.2 70.37574) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"26.925\" y=\"89.866957\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(7.2 93.666175) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 26.925 111.99287 \n", "L 26.925 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 143.**********.99287 \n", "L 143.377174 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 26.925 111.99287 \n", "L 143.**********.99287 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 26.925 18.83113 \n", "L 143.377174 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 166.667609 111.99287 \n", "L 283.119783 111.99287 \n", "L 283.119783 18.83113 \n", "L 166.667609 18.83113 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p34589e544c)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAKIAAACCCAYAAADFXhMtAAADa0lEQVR4nO3dL2wTYRjH8Rsty6AZJCAwiCYTiBmCnMAsOMwEApAQAgoDkiBA4VAsJIRgQcwgSAhmAkkwE2QhQSwkS2gyGPu/ZrjrurVsa6/3fu+970e9d7l2j/jlea53125oZ2VpJ1HpzdbHj/yahc3No/+d32sd9x878jtJA2AQhVANXYAG69vFS+l6cWkjXe8dq+eHh3OrqRM7ohAMohAMohA8R4xAY3KibftXYz1QJb2zIwrBIArB0VxQy9euhC4hU3ZEIRhEITia4WIbwd3YEYVgEIXgaIbZe3F6+EwtUCX5siMKwSAKwSAKwXNEgLJcovkfO6IQDKIQHM05cgQnyfTKQsf9dkQhGEQhOJp7tD39qLWx0fqa5vbcfLpe+b7Y9prY7pLc+Dl/8EGHZEcUgkEUQmlHc/P9q9bG1q5fPVj9my53lv/kWFG+shyrWbAjCsEgCsEgCqE054jNmRftO46H/fWrEC7/mAtdQld2RCEYRCFEPZr3jeMSuvD1S+gSDsWOKASDKIToRrPjuDjjeDc7ohAMohCiGM1tDzCU0NlPn0OX0Dc7ohAMohAMohAMohAMohAMohAKefmm7JdrkiRJRt99DF1CpuyIQjCIQsCN5uaHN62NtdXOB0X8mH8Md0l6YUcUgkEUgkEUgkEUgkEUgkEUQvDLN/u+Y3LiZJhCAortLkkv7IhCMIhCGOhobr59fvBB3iVRYkcUhEEUgkEUgkEUgkEUQqafmpuvn7bvqI1m+faFMPJyJnQJhWRHFIJBFIJBFIJBFIJBFIJBFIJBFIJBFIJBFIJBFIJBFIJBFELfDz1sP76drofq9X7frpCq95+FLqHw7IhCMIhCMIhC6OkccevB9XQ9VKtlVkyRVO8+CV1CVOyIQjCIQug6mtfvTHV9UeXUyECKoXH85seOKASDKASDKASDKASDKIS2T82NyYl0XRs7l3sxBJWbD0OXUEp2RCEYRCEYRCEYRCEYRCEYRCFUZ+vj6cb42OmApYRTmboXuoTSsyMKwSAKIfg/hcyL45fNjigEgyiEqEez47g47IhCMIhCMIhCiO4c0fPCYrIjCsEgCsEgCsEgCsEgCiGKT82Vq7dCl6A+2RGFYBCFYBCFYBCFYBCFYBCFgL584wMM5WFHFIJBFIJBFIJBFIJBFMI//C5t8kgBiFQAAAAASUVORK5CYII=\" id=\"image88fc842fa5\" transform=\"scale(1 -1) translate(0 -93.6)\" x=\"166.667609\" y=\"-18.39287\" width=\"116.64\" height=\"93.6\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#me03e9d8453\" x=\"167.83213\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(164.65088 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#me03e9d8453\" x=\"214.413\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(208.0505 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#me03e9d8453\" x=\"260.99387\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(254.63137 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- Boxcar -->\n", "     <g transform=\"translate(207.819477 140.269432) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-42\" d=\"M 1259 2228 \n", "L 1259 519 \n", "L 2272 519 \n", "Q 2781 519 3026 730 \n", "Q 3272 941 3272 1375 \n", "Q 3272 1813 3026 2020 \n", "Q 2781 2228 2272 2228 \n", "L 1259 2228 \n", "z\n", "M 1259 4147 \n", "L 1259 2741 \n", "L 2194 2741 \n", "Q 2656 2741 2882 2914 \n", "Q 3109 3088 3109 3444 \n", "Q 3109 3797 2882 3972 \n", "Q 2656 4147 2194 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2241 4666 \n", "Q 2963 4666 3353 4366 \n", "Q 3744 4066 3744 3513 \n", "Q 3744 3084 3544 2831 \n", "Q 3344 2578 2956 2516 \n", "Q 3422 2416 3680 2098 \n", "Q 3938 1781 3938 1306 \n", "Q 3938 681 3513 340 \n", "Q 3088 0 2303 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-42\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"68.603516\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"126.660156\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"184.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"239.070312\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"300.349609\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"166.667609\" y=\"19.995652\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"166.667609\" y=\"43.286087\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"166.667609\" y=\"66.576522\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"166.667609\" y=\"89.866957\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 166.667609 111.99287 \n", "L 166.667609 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 283.119783 111.99287 \n", "L 283.119783 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 166.667609 111.99287 \n", "L 283.119783 111.99287 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 166.667609 18.83113 \n", "L 283.119783 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 306.410217 111.99287 \n", "L 422.862391 111.99287 \n", "L 422.862391 18.83113 \n", "L 306.410217 18.83113 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pa8d2bd4a12)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAKIAAACCCAYAAADFXhMtAAABdklEQVR4nO3SoQHAIBDAwKf7D4tB0zGIuJsgIuuefQce+14HwIwRiTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSIIRSTAiCUYkwYgkGJEEI5JgRBKMSMIP414E5yIADlsAAAAASUVORK5CYII=\" id=\"image804b8f429c\" transform=\"scale(1 -1) translate(0 -93.6)\" x=\"306.410217\" y=\"-18.39287\" width=\"116.64\" height=\"93.6\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#me03e9d8453\" x=\"307.574739\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(304.393489 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#me03e9d8453\" x=\"354.155609\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(347.793109 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#me03e9d8453\" x=\"400.736478\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(394.373978 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Constant -->\n", "     <g transform=\"translate(342.158961 140.269432) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"194.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"246.484375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"285.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"346.972656\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"410.351562\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"306.410217\" y=\"19.995652\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"306.410217\" y=\"43.286087\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"306.410217\" y=\"66.576522\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"306.410217\" y=\"89.866957\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 306.410217 111.99287 \n", "L 306.410217 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 422.862391 111.99287 \n", "L 422.862391 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 306.410217 111.99287 \n", "L 422.862391 111.99287 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 306.410217 18.83113 \n", "L 422.862391 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 446.152826 111.99287 \n", "L 562.605 111.99287 \n", "L 562.605 18.83113 \n", "L 446.152826 18.83113 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p3511548438)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image40ca7d07e1\" transform=\"scale(1 -1) translate(0 -93.6)\" x=\"446.152826\" y=\"-18.39287\" width=\"116.64\" height=\"93.6\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#me03e9d8453\" x=\"447.317348\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(444.136098 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#me03e9d8453\" x=\"493.898217\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(487.535717 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#me03e9d8453\" x=\"540.479087\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(534.116587 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_20\">\n", "     <!-- <PERSON><PERSON><PERSON><PERSON>v -->\n", "     <g transform=\"translate(472.696101 140.269432) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-45\" d=\"M 628 4666 \n", "L 3578 4666 \n", "L 3578 4134 \n", "L 1259 4134 \n", "L 1259 2753 \n", "L 3481 2753 \n", "L 3481 2222 \n", "L 1259 2222 \n", "L 1259 531 \n", "L 3634 531 \n", "L 3634 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-45\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"63.183594\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"126.660156\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"187.939453\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"251.318359\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"312.841797\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"367.822266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"431.201172\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"458.984375\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"513.269531\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"574.451172\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"446.152826\" y=\"19.995652\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"446.152826\" y=\"43.286087\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"446.152826\" y=\"66.576522\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m9326af4fd0\" x=\"446.152826\" y=\"89.866957\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 446.152826 111.99287 \n", "L 446.152826 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 562.605 111.99287 \n", "L 562.605 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 446.152826 111.99287 \n", "L 562.605 111.99287 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 446.152826 18.83113 \n", "L 562.605 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 596.085 123.624 \n", "L 601.9062 123.624 \n", "L 601.9062 7.2 \n", "L 596.085 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAgAAACiCAYAAAB8iIwDAAAA7klEQVR4nN2XSw4DIQxDUyn3P2s33ZWkN8iLZGCgsx1kP8cwn1d+3mnF5ZZR3Te3LAU6CkELjCyCFrACxoQFqc9BV5gQExkOiBmDFsgKBJkbGBiSLIYakxnYgg6OHpMnKcc8YMvxqOUutkzy+S13xbmYADkh5g2QqCC/9fKEmFd0QZA58BGEXehtygx3xFw/h7/oAp7E5oEMFBMVBluU980D60YFYgg5Jk9S7mIHpNwFODRSgEBDARjNk34noatOTLRYr4CTHHoXBNnogixkSIxZfxx0Rv2VIVGhwSBb1AJzFGQLUsC6dUjck+u7+AE+P2IIsbjxbAAAAABJRU5ErkJggg==\" id=\"image23c141a7b6\" transform=\"scale(1 -1) translate(0 -116.64)\" x=\"596.16\" y=\"-6.48\" width=\"5.76\" height=\"116.64\"/>\n", "   <g id=\"matplotlib.axis_9\"/>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_29\">\n", "      <defs>\n", "       <path id=\"mc63025a383\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc63025a383\" x=\"601.9062\" y=\"123.624\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(608.9062 127.423219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#mc63025a383\" x=\"601.9062\" y=\"86.180922\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 0.1 -->\n", "      <g transform=\"translate(608.9062 89.980141) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#mc63025a383\" x=\"601.9062\" y=\"48.737844\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(608.9062 52.537062) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#mc63025a383\" x=\"601.9062\" y=\"11.294765\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 0.3 -->\n", "      <g transform=\"translate(608.9062 15.093984) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 596.085 123.624 \n", "L 598.9956 123.624 \n", "L 601.9062 123.624 \n", "L 601.9062 7.2 \n", "L 598.9956 7.2 \n", "L 596.085 7.2 \n", "L 596.085 123.624 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p8ae9c3aaee\">\n", "   <rect x=\"26.925\" y=\"18.83113\" width=\"116.452174\" height=\"93.161739\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p34589e544c\">\n", "   <rect x=\"166.667609\" y=\"18.83113\" width=\"116.452174\" height=\"93.161739\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pa8d2bd4a12\">\n", "   <rect x=\"306.410217\" y=\"18.83113\" width=\"116.452174\" height=\"93.161739\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p3511548438\">\n", "   <rect x=\"446.152826\" y=\"18.83113\" width=\"116.452174\" height=\"93.161739\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1200x300 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot(x_train, y_train, x_val, y_val, kernels, names, attention=True)"]}, {"cell_type": "markdown", "id": "7e9ddb54", "metadata": {"origin_pos": 18}, "source": ["The visualization clearly shows why the estimates for <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> are very similar: after all, they are derived from very similar attention weights, despite the different functional form of the kernel. This raises the question as to whether this is always the case. \n", "\n", "## [**Adapting Attention Pooling**]\n", "\n", "We could replace the Gaussian kernel with one of a different width. That is, we could use \n", "$\\alpha(\\mathbf{q}, \\mathbf{k}) = \\exp\\left(-\\frac{1}{2 \\sigma^2} \\|\\mathbf{q} - \\mathbf{k}\\|^2 \\right)$ where $\\sigma^2$ determines the width of the kernel. Let's see whether this affects the outcomes.\n"]}, {"cell_type": "code", "execution_count": 15, "id": "ea6039fc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:45.632492Z", "iopub.status.busy": "2023-08-18T19:42:45.631618Z", "iopub.status.idle": "2023-08-18T19:42:46.594208Z", "shell.execute_reply": "2023-08-18T19:42:46.592944Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"705.742188pt\" height=\"211.07625pt\" viewBox=\"0 0 705.**********.07625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-13T18:45:47.202493</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 211.07625 \n", "L 705.**********.07625 \n", "L 705.742188 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 28.**********.52 \n", "L 174.**********.52 \n", "L 174.507405 7.2 \n", "L 28.942188 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m8b757127a1\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8b757127a1\" x=\"35.558788\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(32.377538 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m8b757127a1\" x=\"89.319886\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(86.138636 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m8b757127a1\" x=\"143.080984\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(139.899734 188.118438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- Sigma 0.1 -->\n", "     <g transform=\"translate(76.512296 201.796563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"91.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"154.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"252.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"345.214844\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"408.837891\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"440.625\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m09862003d4\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"28.942188\" y=\"160.023978\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −1 -->\n", "      <g transform=\"translate(7.2 163.823196) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"28.942188\" y=\"136.489429\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(15.579688 140.288647) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"28.942188\" y=\"112.954879\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(15.579688 116.754098) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"28.942188\" y=\"89.42033\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(15.579688 93.219549) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"28.942188\" y=\"65.885781\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(15.579688 69.685) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"28.942188\" y=\"42.351232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(15.579688 46.150451) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"28.942188\" y=\"18.816683\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(15.579688 22.615902) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 35.558788 152.137779 \n", "L 38.246843 149.999438 \n", "L 40.934898 147.116999 \n", "L 43.622953 135.91155 \n", "L 46.311008 116.427626 \n", "L 48.999063 98.640775 \n", "L 51.687118 86.354675 \n", "L 54.375172 87.599122 \n", "L 57.063228 88.189687 \n", "L 59.751283 77.931836 \n", "L 62.439337 66.956554 \n", "L 65.127393 61.860391 \n", "L 67.815448 58.463941 \n", "L 70.503504 42.540999 \n", "L 73.191559 45.915268 \n", "L 75.879612 57.783699 \n", "L 78.567667 62.081967 \n", "L 81.255723 63.231659 \n", "L 83.943778 63.682952 \n", "L 86.631831 60.992195 \n", "L 89.319886 59.76433 \n", "L 92.007939 57.597305 \n", "L 94.695997 47.983774 \n", "L 97.38405 40.076918 \n", "L 100.072108 50.294852 \n", "L 102.760161 57.561417 \n", "L 105.448219 57.957525 \n", "L 108.136272 52.387322 \n", "L 110.824324 45.494735 \n", "L 113.512376 41.234337 \n", "L 116.200435 39.981171 \n", "L 118.888487 38.853053 \n", "L 121.576546 38.267751 \n", "L 124.264599 38.259727 \n", "L 126.952657 38.25351 \n", "L 129.64071 37.902885 \n", "L 132.328762 36.254722 \n", "L 135.016814 36.956633 \n", "L 137.704873 43.076149 \n", "L 140.392925 58.030317 \n", "L 143.080984 63.562623 \n", "L 145.769036 62.869795 \n", "L 148.457089 60.521359 \n", "L 151.145154 58.656575 \n", "L 153.833206 57.823072 \n", "L 156.521259 54.291878 \n", "L 159.209311 47.776776 \n", "L 161.897376 50.425135 \n", "L 164.585428 85.825158 \n", "L 167.273481 100.719417 \n", "\" clip-path=\"url(#p54a6426024)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 35.558788 136.489429 \n", "L 38.246843 129.436904 \n", "L 40.934898 122.431332 \n", "L 43.622953 115.519193 \n", "L 46.311008 108.746038 \n", "L 48.999063 102.156026 \n", "L 51.687118 95.791486 \n", "L 54.375172 89.692501 \n", "L 57.063228 83.896486 \n", "L 59.751283 78.437843 \n", "L 62.439337 73.3476 \n", "L 65.127393 68.653095 \n", "L 67.815448 64.377727 \n", "L 70.503504 60.540695 \n", "L 73.191559 57.156824 \n", "L 75.879612 54.236418 \n", "L 78.567667 51.785119 \n", "L 81.255723 49.803928 \n", "L 83.943778 48.289106 \n", "L 86.631831 47.232294 \n", "L 89.319886 46.620519 \n", "L 92.007939 46.436386 \n", "L 94.695997 46.658225 \n", "L 97.38405 47.260293 \n", "L 100.072108 48.21307 \n", "L 102.760161 49.483513 \n", "L 105.448219 51.035418 \n", "L 108.136272 52.829761 \n", "L 110.824324 54.825098 \n", "L 113.512376 56.977983 \n", "L 116.200435 59.243392 \n", "L 118.888487 61.575158 \n", "L 121.576546 63.92649 \n", "L 124.264599 66.250361 \n", "L 126.952657 68.500053 \n", "L 129.64071 70.629557 \n", "L 132.328762 72.594089 \n", "L 135.016814 74.350501 \n", "L 137.704873 75.857737 \n", "L 140.392925 77.077219 \n", "L 143.080984 77.973246 \n", "L 145.769036 78.513346 \n", "L 148.457089 78.668604 \n", "L 151.145154 78.413973 \n", "L 153.833206 77.728463 \n", "L 156.521259 76.595419 \n", "L 159.209311 75.002643 \n", "L 161.897376 72.942526 \n", "L 164.585428 70.412156 \n", "L 167.273481 67.413295 \n", "\" clip-path=\"url(#p54a6426024)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <defs>\n", "     <path id=\"mce0b2195fb\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p54a6426024)\">\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"39.410258\" y=\"165.96\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"40.082135\" y=\"131.741311\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"47.58544\" y=\"144.245018\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"47.677198\" y=\"126.530533\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"50.170511\" y=\"71.247381\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"50.391086\" y=\"52.975873\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"53.555667\" y=\"100.315599\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"56.435086\" y=\"88.056856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"61.528514\" y=\"82.561185\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"63.795402\" y=\"77.573569\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"64.743501\" y=\"39.173367\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"65.563693\" y=\"45.929936\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"66.972522\" y=\"83.762051\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"72.159444\" y=\"14.76\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"74.475769\" y=\"26.360659\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"76.002158\" y=\"75.252049\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"76.322961\" y=\"69.851335\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"76.82929\" y=\"76.762707\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"78.495094\" y=\"54.77645\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"79.014039\" y=\"28.813159\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"79.631025\" y=\"92.734646\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"88.579366\" y=\"58.711496\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"90.200734\" y=\"86.826696\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"90.678954\" y=\"31.790484\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"99.125692\" y=\"24.138427\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"101.969953\" y=\"103.375856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"102.643674\" y=\"15.568769\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"104.904751\" y=\"75.911378\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"107.567925\" y=\"62.085087\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"108.187536\" y=\"36.447934\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"112.228843\" y=\"39.733094\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"125.037676\" y=\"38.259749\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"136.378317\" y=\"35.985323\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"142.11124\" y=\"62.194626\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"144.393601\" y=\"69.89927\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"146.377887\" y=\"56.969112\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"153.434539\" y=\"58.879761\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"154.662123\" y=\"58.210348\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"159.580036\" y=\"43.846998\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"167.890804\" y=\"101.692038\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 28.**********.52 \n", "L 28.942188 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 174.**********.52 \n", "L 174.507405 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 28.**********.52 \n", "L 174.**********.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 28.942188 7.2 \n", "L 174.507405 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 108.202717 168.52 \n", "L 167.507405 168.52 \n", "Q 169.507405 168.52 169.507405 166.52 \n", "L 169.507405 137.885625 \n", "Q 169.507405 135.885625 167.507405 135.885625 \n", "L 108.202717 135.885625 \n", "Q 106.202717 135.885625 106.202717 137.885625 \n", "L 106.202717 166.52 \n", "Q 106.202717 168.52 108.202717 168.52 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_14\">\n", "     <path d=\"M 110.202717 143.984063 \n", "L 120.202717 143.984063 \n", "L 130.202717 143.984063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- y_hat -->\n", "     <g transform=\"translate(138.202717 147.484063) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"109.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"172.558594\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"233.837891\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_15\">\n", "     <path d=\"M 110.202717 158.940313 \n", "L 120.202717 158.940313 \n", "L 130.202717 158.940313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- y -->\n", "     <g transform=\"translate(138.202717 162.440313) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 203.620448 173.52 \n", "L 349.185666 173.52 \n", "L 349.185666 7.2 \n", "L 203.620448 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m8b757127a1\" x=\"210.237049\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(207.055799 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m8b757127a1\" x=\"263.998147\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(260.816897 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m8b757127a1\" x=\"317.759245\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(314.577995 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Sigma 0.2 -->\n", "     <g transform=\"translate(251.190557 201.796563) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"91.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"154.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"252.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"345.214844\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"408.837891\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"440.625\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"203.620448\" y=\"160.023978\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"203.620448\" y=\"136.489429\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"203.620448\" y=\"112.954879\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"203.620448\" y=\"89.42033\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"203.620448\" y=\"65.885781\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"203.620448\" y=\"42.351232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"203.620448\" y=\"18.816683\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 210.237049 145.778794 \n", "L 212.925104 140.821946 \n", "L 215.613159 132.621561 \n", "L 218.301214 121.751629 \n", "L 220.989269 110.727014 \n", "L 223.677324 101.624562 \n", "L 226.365379 94.55265 \n", "L 229.053433 88.312284 \n", "L 231.741489 81.517801 \n", "L 234.429544 74.169398 \n", "L 237.117598 67.534045 \n", "L 239.805654 62.026956 \n", "L 242.493709 57.54988 \n", "L 245.181765 54.905774 \n", "L 247.86982 54.828622 \n", "L 250.557873 56.458139 \n", "L 253.245928 58.462185 \n", "L 255.933984 60.065437 \n", "L 258.622039 60.780422 \n", "L 261.310092 60.22184 \n", "L 263.998147 58.500138 \n", "L 266.686199 56.023028 \n", "L 269.374258 53.533507 \n", "L 272.06231 52.463257 \n", "L 274.750369 53.04177 \n", "L 277.438422 53.861901 \n", "L 280.12648 53.830642 \n", "L 282.814533 52.676809 \n", "L 285.502585 50.614672 \n", "L 288.190637 47.986332 \n", "L 290.878696 44.959523 \n", "L 293.566748 41.756739 \n", "L 296.254807 39.441172 \n", "L 298.942859 38.51741 \n", "L 301.630918 38.447933 \n", "L 304.31897 39.441554 \n", "L 307.007023 42.402691 \n", "L 309.695075 47.13304 \n", "L 312.383134 52.255417 \n", "L 315.071186 56.546615 \n", "L 317.759245 59.328641 \n", "L 320.447297 60.448409 \n", "L 323.13535 60.086349 \n", "L 325.823415 58.705256 \n", "L 328.511467 57.0701 \n", "L 331.199519 56.157919 \n", "L 333.887572 57.470708 \n", "L 336.575637 63.06492 \n", "L 339.263689 73.258384 \n", "L 341.951742 84.396854 \n", "\" clip-path=\"url(#pea2e449fcd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 210.237049 136.489429 \n", "L 212.925104 129.436904 \n", "L 215.613159 122.431332 \n", "L 218.301214 115.519193 \n", "L 220.989269 108.746038 \n", "L 223.677324 102.156026 \n", "L 226.365379 95.791486 \n", "L 229.053433 89.692501 \n", "L 231.741489 83.896486 \n", "L 234.429544 78.437843 \n", "L 237.117598 73.3476 \n", "L 239.805654 68.653095 \n", "L 242.493709 64.377727 \n", "L 245.181765 60.540695 \n", "L 247.86982 57.156824 \n", "L 250.557873 54.236418 \n", "L 253.245928 51.785119 \n", "L 255.933984 49.803928 \n", "L 258.622039 48.289106 \n", "L 261.310092 47.232294 \n", "L 263.998147 46.620519 \n", "L 266.686199 46.436386 \n", "L 269.374258 46.658225 \n", "L 272.06231 47.260293 \n", "L 274.750369 48.21307 \n", "L 277.438422 49.483513 \n", "L 280.12648 51.035418 \n", "L 282.814533 52.829761 \n", "L 285.502585 54.825098 \n", "L 288.190637 56.977983 \n", "L 290.878696 59.243392 \n", "L 293.566748 61.575158 \n", "L 296.254807 63.92649 \n", "L 298.942859 66.250361 \n", "L 301.630918 68.500053 \n", "L 304.31897 70.629557 \n", "L 307.007023 72.594089 \n", "L 309.695075 74.350501 \n", "L 312.383134 75.857737 \n", "L 315.071186 77.077219 \n", "L 317.759245 77.973246 \n", "L 320.447297 78.513346 \n", "L 323.13535 78.668604 \n", "L 325.823415 78.413973 \n", "L 328.511467 77.728463 \n", "L 331.199519 76.595419 \n", "L 333.887572 75.002643 \n", "L 336.575637 72.942526 \n", "L 339.263689 70.412156 \n", "L 341.951742 67.413295 \n", "\" clip-path=\"url(#pea2e449fcd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <g clip-path=\"url(#pea2e449fcd)\">\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"214.088519\" y=\"165.96\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"214.760396\" y=\"131.741311\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"222.263701\" y=\"144.245018\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"222.355459\" y=\"126.530533\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"224.848771\" y=\"71.247381\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"225.069347\" y=\"52.975873\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"228.233927\" y=\"100.315599\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"231.113347\" y=\"88.056856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"236.206775\" y=\"82.561185\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"238.473663\" y=\"77.573569\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"239.421762\" y=\"39.173367\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"240.241953\" y=\"45.929936\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"241.650782\" y=\"83.762051\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"246.837705\" y=\"14.76\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"249.15403\" y=\"26.360659\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"250.680419\" y=\"75.252049\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"251.001222\" y=\"69.851335\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"251.507551\" y=\"76.762707\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"253.173355\" y=\"54.77645\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"253.692299\" y=\"28.813159\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"254.309286\" y=\"92.734646\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"263.257627\" y=\"58.711496\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"264.878995\" y=\"86.826696\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"265.357215\" y=\"31.790484\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"273.803953\" y=\"24.138427\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"276.648214\" y=\"103.375856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"277.321935\" y=\"15.568769\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"279.583012\" y=\"75.911378\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"282.246185\" y=\"62.085087\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"282.865797\" y=\"36.447934\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"286.907104\" y=\"39.733094\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"299.715936\" y=\"38.259749\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"311.056578\" y=\"35.985323\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"316.7895\" y=\"62.194626\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"319.071861\" y=\"69.89927\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"321.056148\" y=\"56.969112\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"328.1128\" y=\"58.879761\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"329.340384\" y=\"58.210348\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"334.258296\" y=\"43.846998\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"342.569065\" y=\"101.692038\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 203.620448 173.52 \n", "L 203.620448 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 349.185666 173.52 \n", "L 349.185666 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 203.620448 173.52 \n", "L 349.185666 173.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 203.620448 7.2 \n", "L 349.185666 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_2\">\n", "    <g id=\"patch_13\">\n", "     <path d=\"M 282.880978 168.52 \n", "L 342.185666 168.52 \n", "Q 344.185666 168.52 344.185666 166.52 \n", "L 344.185666 137.885625 \n", "Q 344.185666 135.885625 342.185666 135.885625 \n", "L 282.880978 135.885625 \n", "Q 280.880978 135.885625 280.880978 137.885625 \n", "L 280.880978 166.52 \n", "Q 280.880978 168.52 282.880978 168.52 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_29\">\n", "     <path d=\"M 284.880978 143.984063 \n", "L 294.880978 143.984063 \n", "L 304.880978 143.984063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- y_hat -->\n", "     <g transform=\"translate(312.880978 147.484063) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"109.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"172.558594\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"233.837891\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 284.880978 158.940313 \n", "L 294.880978 158.940313 \n", "L 304.880978 158.940313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- y -->\n", "     <g transform=\"translate(312.880978 162.440313) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 378.298709 173.52 \n", "L 523.863927 173.52 \n", "L 523.863927 7.2 \n", "L 378.298709 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m8b757127a1\" x=\"384.91531\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(381.73406 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m8b757127a1\" x=\"438.676408\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(435.495158 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#m8b757127a1\" x=\"492.437506\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(489.256256 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_23\">\n", "     <!-- Sigma 0.5 -->\n", "     <g transform=\"translate(425.868818 201.796563) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"91.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"154.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"252.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"345.214844\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"408.837891\"/>\n", "      <use xlink:href=\"#DejaVuSans-35\" x=\"440.625\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"378.298709\" y=\"160.023978\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"378.298709\" y=\"136.489429\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"378.298709\" y=\"112.954879\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"378.298709\" y=\"89.42033\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"378.298709\" y=\"65.885781\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"378.298709\" y=\"42.351232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"378.298709\" y=\"18.816683\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 384.91531 113.211538 \n", "L 387.603365 109.560034 \n", "L 390.29142 105.6835 \n", "L 392.979475 101.596655 \n", "L 395.66753 97.329475 \n", "L 398.355585 92.932032 \n", "L 401.04364 88.477546 \n", "L 403.731694 84.061587 \n", "L 406.41975 79.795285 \n", "L 409.107805 75.793142 \n", "L 411.795859 72.157323 \n", "L 414.483915 68.962939 \n", "L 417.17197 66.24843 \n", "L 419.860026 64.013355 \n", "L 422.548081 62.222973 \n", "L 425.236133 60.817113 \n", "L 427.924189 59.720305 \n", "L 430.612245 58.851515 \n", "L 433.3003 58.132512 \n", "L 435.988352 57.495049 \n", "L 438.676408 56.886494 \n", "L 441.36446 56.273181 \n", "L 444.052519 55.639753 \n", "L 446.740571 54.984711 \n", "L 449.42863 54.313312 \n", "L 452.116682 53.630725 \n", "L 454.804741 52.938661 \n", "L 457.492793 52.236171 \n", "L 460.180846 51.525113 \n", "L 462.868898 50.819341 \n", "L 465.556957 50.156146 \n", "L 468.245009 49.605744 \n", "L 470.933068 49.269675 \n", "L 473.62112 49.256646 \n", "L 476.309179 49.636516 \n", "L 478.997231 50.398017 \n", "L 481.685284 51.446227 \n", "L 484.373336 52.645016 \n", "L 487.061395 53.87103 \n", "L 489.749447 55.04508 \n", "L 492.437506 56.135912 \n", "L 495.125558 57.149008 \n", "L 497.81361 58.112761 \n", "L 500.501676 59.067373 \n", "L 503.189728 60.056919 \n", "L 505.87778 61.123842 \n", "L 508.565833 62.30448 \n", "L 511.253898 63.625854 \n", "L 513.94195 65.103261 \n", "L 516.630002 66.739074 \n", "\" clip-path=\"url(#p1c957ba24e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 384.91531 136.489429 \n", "L 387.603365 129.436904 \n", "L 390.29142 122.431332 \n", "L 392.979475 115.519193 \n", "L 395.66753 108.746038 \n", "L 398.355585 102.156026 \n", "L 401.04364 95.791486 \n", "L 403.731694 89.692501 \n", "L 406.41975 83.896486 \n", "L 409.107805 78.437843 \n", "L 411.795859 73.3476 \n", "L 414.483915 68.653095 \n", "L 417.17197 64.377727 \n", "L 419.860026 60.540695 \n", "L 422.548081 57.156824 \n", "L 425.236133 54.236418 \n", "L 427.924189 51.785119 \n", "L 430.612245 49.803928 \n", "L 433.3003 48.289106 \n", "L 435.988352 47.232294 \n", "L 438.676408 46.620519 \n", "L 441.36446 46.436386 \n", "L 444.052519 46.658225 \n", "L 446.740571 47.260293 \n", "L 449.42863 48.21307 \n", "L 452.116682 49.483513 \n", "L 454.804741 51.035418 \n", "L 457.492793 52.829761 \n", "L 460.180846 54.825098 \n", "L 462.868898 56.977983 \n", "L 465.556957 59.243392 \n", "L 468.245009 61.575158 \n", "L 470.933068 63.92649 \n", "L 473.62112 66.250361 \n", "L 476.309179 68.500053 \n", "L 478.997231 70.629557 \n", "L 481.685284 72.594089 \n", "L 484.373336 74.350501 \n", "L 487.061395 75.857737 \n", "L 489.749447 77.077219 \n", "L 492.437506 77.973246 \n", "L 495.125558 78.513346 \n", "L 497.81361 78.668604 \n", "L 500.501676 78.413973 \n", "L 503.189728 77.728463 \n", "L 505.87778 76.595419 \n", "L 508.565833 75.002643 \n", "L 511.253898 72.942526 \n", "L 513.94195 70.412156 \n", "L 516.630002 67.413295 \n", "\" clip-path=\"url(#p1c957ba24e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <g clip-path=\"url(#p1c957ba24e)\">\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"388.76678\" y=\"165.96\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"389.438657\" y=\"131.741311\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"396.941962\" y=\"144.245018\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"397.03372\" y=\"126.530533\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"399.527032\" y=\"71.247381\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"399.747608\" y=\"52.975873\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"402.912188\" y=\"100.315599\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"405.791608\" y=\"88.056856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"410.885036\" y=\"82.561185\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"413.151924\" y=\"77.573569\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"414.100023\" y=\"39.173367\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"414.920214\" y=\"45.929936\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"416.329043\" y=\"83.762051\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"421.515966\" y=\"14.76\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"423.832291\" y=\"26.360659\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"425.35868\" y=\"75.252049\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"425.679483\" y=\"69.851335\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"426.185812\" y=\"76.762707\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"427.851615\" y=\"54.77645\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"428.37056\" y=\"28.813159\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"428.987547\" y=\"92.734646\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"437.935888\" y=\"58.711496\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"439.557256\" y=\"86.826696\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"440.035476\" y=\"31.790484\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"448.482214\" y=\"24.138427\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"451.326475\" y=\"103.375856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"452.000196\" y=\"15.568769\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"454.261273\" y=\"75.911378\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"456.924446\" y=\"62.085087\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"457.544058\" y=\"36.447934\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"461.585365\" y=\"39.733094\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"474.394197\" y=\"38.259749\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"485.734839\" y=\"35.985323\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"491.467761\" y=\"62.194626\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"493.750122\" y=\"69.89927\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"495.734409\" y=\"56.969112\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"502.791061\" y=\"58.879761\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"504.018645\" y=\"58.210348\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"508.936557\" y=\"43.846998\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"517.247326\" y=\"101.692038\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 378.298709 173.52 \n", "L 378.298709 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 523.863927 173.52 \n", "L 523.863927 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 378.298709 173.52 \n", "L 523.863927 173.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 378.298709 7.2 \n", "L 523.863927 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_3\">\n", "    <g id=\"patch_19\">\n", "     <path d=\"M 457.559239 168.52 \n", "L 516.863927 168.52 \n", "Q 518.863927 168.52 518.863927 166.52 \n", "L 518.863927 137.885625 \n", "Q 518.863927 135.885625 516.863927 135.885625 \n", "L 457.559239 135.885625 \n", "Q 455.559239 135.885625 455.559239 137.885625 \n", "L 455.559239 166.52 \n", "Q 455.559239 168.52 457.559239 168.52 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_44\">\n", "     <path d=\"M 459.559239 143.984063 \n", "L 469.559239 143.984063 \n", "L 479.559239 143.984063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_24\">\n", "     <!-- y_hat -->\n", "     <g transform=\"translate(487.559239 147.484063) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"109.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"172.558594\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"233.837891\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_45\">\n", "     <path d=\"M 459.559239 158.940313 \n", "L 469.559239 158.940313 \n", "L 479.559239 158.940313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- y -->\n", "     <g transform=\"translate(487.559239 162.440313) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 552.97697 173.52 \n", "L 698.542188 173.52 \n", "L 698.542188 7.2 \n", "L 552.97697 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#m8b757127a1\" x=\"559.593571\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_26\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(556.412321 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_47\">\n", "      <g>\n", "       <use xlink:href=\"#m8b757127a1\" x=\"613.354669\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(610.173419 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#m8b757127a1\" x=\"667.115767\" y=\"173.52\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(663.934517 188.118438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_29\">\n", "     <!-- Sigma 1 -->\n", "     <g transform=\"translate(605.317391 201.796563) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"91.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"154.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"252.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"345.214844\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_49\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"552.97697\" y=\"160.023978\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"552.97697\" y=\"136.489429\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_51\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"552.97697\" y=\"112.954879\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"552.97697\" y=\"89.42033\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_53\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"552.97697\" y=\"65.885781\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"552.97697\" y=\"42.351232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_55\">\n", "      <g>\n", "       <use xlink:href=\"#m09862003d4\" x=\"552.97697\" y=\"18.816683\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 559.593571 88.075833 \n", "L 562.281626 86.488959 \n", "L 564.969681 84.904168 \n", "L 567.657736 83.325801 \n", "L 570.345791 81.758246 \n", "L 573.033845 80.205971 \n", "L 575.721901 78.673418 \n", "L 578.409955 77.16487 \n", "L 581.09801 75.684556 \n", "L 583.786066 74.236422 \n", "L 586.47412 72.824177 \n", "L 589.162175 71.45117 \n", "L 591.850231 70.120436 \n", "L 594.538286 68.834558 \n", "L 597.226342 67.59575 \n", "L 599.914394 66.40586 \n", "L 602.60245 65.266274 \n", "L 605.290505 64.178096 \n", "L 607.978561 63.142123 \n", "L 610.666613 62.159007 \n", "L 613.354669 61.229241 \n", "L 616.042721 60.353442 \n", "L 618.73078 59.532261 \n", "L 621.418832 58.766681 \n", "L 624.106891 58.05807 \n", "L 626.794943 57.408201 \n", "L 629.483002 56.819341 \n", "L 632.171054 56.294257 \n", "L 634.859107 55.836062 \n", "L 637.547159 55.448084 \n", "L 640.235218 55.133572 \n", "L 642.92327 54.895461 \n", "L 645.611329 54.735915 \n", "L 648.299381 54.656104 \n", "L 650.98744 54.655851 \n", "L 653.675492 54.733469 \n", "L 656.363545 54.885703 \n", "L 659.051597 55.10784 \n", "L 661.739656 55.393898 \n", "L 664.427708 55.73701 \n", "L 667.115767 56.129729 \n", "L 669.803819 56.564497 \n", "L 672.491871 57.033959 \n", "L 675.179937 57.531274 \n", "L 677.867989 58.050276 \n", "L 680.556041 58.585701 \n", "L 683.244093 59.133106 \n", "L 685.932159 59.688934 \n", "L 688.620211 60.250411 \n", "L 691.308263 60.815486 \n", "\" clip-path=\"url(#p500b82dfeb)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 559.593571 136.489429 \n", "L 562.281626 129.436904 \n", "L 564.969681 122.431332 \n", "L 567.657736 115.519193 \n", "L 570.345791 108.746038 \n", "L 573.033845 102.156026 \n", "L 575.721901 95.791486 \n", "L 578.409955 89.692501 \n", "L 581.09801 83.896486 \n", "L 583.786066 78.437843 \n", "L 586.47412 73.3476 \n", "L 589.162175 68.653095 \n", "L 591.850231 64.377727 \n", "L 594.538286 60.540695 \n", "L 597.226342 57.156824 \n", "L 599.914394 54.236418 \n", "L 602.60245 51.785119 \n", "L 605.290505 49.803928 \n", "L 607.978561 48.289106 \n", "L 610.666613 47.232294 \n", "L 613.354669 46.620519 \n", "L 616.042721 46.436386 \n", "L 618.73078 46.658225 \n", "L 621.418832 47.260293 \n", "L 624.106891 48.21307 \n", "L 626.794943 49.483513 \n", "L 629.483002 51.035418 \n", "L 632.171054 52.829761 \n", "L 634.859107 54.825098 \n", "L 637.547159 56.977983 \n", "L 640.235218 59.243392 \n", "L 642.92327 61.575158 \n", "L 645.611329 63.92649 \n", "L 648.299381 66.250361 \n", "L 650.98744 68.500053 \n", "L 653.675492 70.629557 \n", "L 656.363545 72.594089 \n", "L 659.051597 74.350501 \n", "L 661.739656 75.857737 \n", "L 664.427708 77.077219 \n", "L 667.115767 77.973246 \n", "L 669.803819 78.513346 \n", "L 672.491871 78.668604 \n", "L 675.179937 78.413973 \n", "L 677.867989 77.728463 \n", "L 680.556041 76.595419 \n", "L 683.244093 75.002643 \n", "L 685.932159 72.942526 \n", "L 688.620211 70.412156 \n", "L 691.308263 67.413295 \n", "\" clip-path=\"url(#p500b82dfeb)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <g clip-path=\"url(#p500b82dfeb)\">\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"563.445041\" y=\"165.96\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"564.116918\" y=\"131.741311\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"571.620223\" y=\"144.245018\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"571.711981\" y=\"126.530533\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"574.205293\" y=\"71.247381\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"574.425869\" y=\"52.975873\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"577.590449\" y=\"100.315599\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"580.469869\" y=\"88.056856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"585.563296\" y=\"82.561185\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"587.830185\" y=\"77.573569\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"588.778284\" y=\"39.173367\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"589.598475\" y=\"45.929936\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"591.007304\" y=\"83.762051\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"596.194227\" y=\"14.76\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"598.510551\" y=\"26.360659\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"600.036941\" y=\"75.252049\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"600.357744\" y=\"69.851335\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"600.864073\" y=\"76.762707\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"602.529876\" y=\"54.77645\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"603.048821\" y=\"28.813159\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"603.665808\" y=\"92.734646\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"612.614149\" y=\"58.711496\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"614.235517\" y=\"86.826696\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"614.713736\" y=\"31.790484\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"623.160475\" y=\"24.138427\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"626.004735\" y=\"103.375856\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"626.678457\" y=\"15.568769\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"628.939534\" y=\"75.911378\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"631.602707\" y=\"62.085087\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"632.222319\" y=\"36.447934\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"636.263626\" y=\"39.733094\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"649.072458\" y=\"38.259749\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"660.4131\" y=\"35.985323\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"666.146022\" y=\"62.194626\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"668.428383\" y=\"69.89927\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"670.41267\" y=\"56.969112\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"677.469322\" y=\"58.879761\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"678.696906\" y=\"58.210348\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"683.614818\" y=\"43.846998\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "     <use xlink:href=\"#mce0b2195fb\" x=\"691.925587\" y=\"101.692038\" style=\"fill: #ff7f0e; fill-opacity: 0.5; stroke: #ff7f0e; stroke-opacity: 0.5\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 552.97697 173.52 \n", "L 552.97697 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 698.542188 173.52 \n", "L 698.542188 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 552.97697 173.52 \n", "L 698.542188 173.52 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 552.97697 7.2 \n", "L 698.542188 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_4\">\n", "    <g id=\"patch_25\">\n", "     <path d=\"M 632.2375 168.52 \n", "L 691.542188 168.52 \n", "Q 693.542188 168.52 693.542188 166.52 \n", "L 693.542188 137.885625 \n", "Q 693.542188 135.885625 691.542188 135.885625 \n", "L 632.2375 135.885625 \n", "Q 630.2375 135.885625 630.2375 137.885625 \n", "L 630.2375 166.52 \n", "Q 630.2375 168.52 632.2375 168.52 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_59\">\n", "     <path d=\"M 634.2375 143.984063 \n", "L 644.2375 143.984063 \n", "L 654.2375 143.984063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_30\">\n", "     <!-- y_hat -->\n", "     <g transform=\"translate(662.2375 147.484063) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"109.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"172.558594\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"233.837891\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_60\">\n", "     <path d=\"M 634.2375 158.940313 \n", "L 644.2375 158.940313 \n", "L 654.2375 158.940313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_31\">\n", "     <!-- y -->\n", "     <g transform=\"translate(662.2375 162.440313) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p54a6426024\">\n", "   <rect x=\"28.942188\" y=\"7.2\" width=\"145.565217\" height=\"166.32\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pea2e449fcd\">\n", "   <rect x=\"203.620448\" y=\"7.2\" width=\"145.565217\" height=\"166.32\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p1c957ba24e\">\n", "   <rect x=\"378.298709\" y=\"7.2\" width=\"145.565217\" height=\"166.32\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p500b82dfeb\">\n", "   <rect x=\"552.97697\" y=\"7.2\" width=\"145.565217\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1200x300 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sigmas = (0.1, 0.2, 0.5, 1)\n", "names = ['Sigma ' + str(sigma) for sigma in sigmas]\n", "\n", "def gaussian_with_width(sigma):\n", "    return (lambda x: torch.exp(-x**2 / (2*sigma**2)))\n", "\n", "kernels = [gaussian_with_width(sigma) for sigma in sigmas]\n", "plot(x_train, y_train, x_val, y_val, kernels, names)"]}, {"cell_type": "markdown", "id": "d7415656", "metadata": {"origin_pos": 20}, "source": ["Clearly, the narrower the kernel, the less smooth the estimate. At the same time, it adapts better to the local variations. Let's look at the corresponding attention weights.\n"]}, {"cell_type": "code", "execution_count": 16, "id": "9bee3050", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:42:46.600739Z", "iopub.status.busy": "2023-08-18T19:42:46.599687Z", "iopub.status.idle": "2023-08-18T19:42:47.319301Z", "shell.execute_reply": "2023-08-18T19:42:47.318277Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"638.371825pt\" height=\"149.54912pt\" viewBox=\"0 0 638.**********.54912\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-13T18:45:47.384694</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 149.54912 \n", "L 638.**********.54912 \n", "L 638.371825 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 26.925 111.99287 \n", "L 143.**********.99287 \n", "L 143.377174 18.83113 \n", "L 26.925 18.83113 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p4dc3aafbb0)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagecdfd3d815d\" transform=\"scale(1 -1) translate(0 -93.6)\" x=\"26.925\" y=\"-18.39287\" width=\"116.64\" height=\"93.6\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"ma3a7e72af4\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma3a7e72af4\" x=\"28.089522\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(24.908272 126.591307) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#ma3a7e72af4\" x=\"74.670391\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(68.307891 126.591307) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#ma3a7e72af4\" x=\"121.251261\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(114.888761 126.591307) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- Sigma 0.1 -->\n", "     <g transform=\"translate(59.938587 140.269432) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"91.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"154.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"252.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"345.214844\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"408.837891\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"440.625\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"m4c9c1ea3ea\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"26.925\" y=\"19.995652\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(13.5625 23.794871) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"26.925\" y=\"43.286087\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(7.2 47.085306) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"26.925\" y=\"66.576522\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(7.2 70.37574) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"26.925\" y=\"89.866957\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(7.2 93.666175) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 26.925 111.99287 \n", "L 26.925 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 143.**********.99287 \n", "L 143.377174 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 26.925 111.99287 \n", "L 143.**********.99287 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 26.925 18.83113 \n", "L 143.377174 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 166.667609 111.99287 \n", "L 283.119783 111.99287 \n", "L 283.119783 18.83113 \n", "L 166.667609 18.83113 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pa0a3c65240)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image9f761cac13\" transform=\"scale(1 -1) translate(0 -93.6)\" x=\"166.667609\" y=\"-18.39287\" width=\"116.64\" height=\"93.6\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#ma3a7e72af4\" x=\"167.83213\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(164.65088 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#ma3a7e72af4\" x=\"214.413\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(208.0505 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#ma3a7e72af4\" x=\"260.99387\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(254.63137 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- Sigma 0.2 -->\n", "     <g transform=\"translate(199.681196 140.269432) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"91.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"154.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"252.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"345.214844\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"408.837891\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"440.625\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"166.667609\" y=\"19.995652\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"166.667609\" y=\"43.286087\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"166.667609\" y=\"66.576522\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"166.667609\" y=\"89.866957\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 166.667609 111.99287 \n", "L 166.667609 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 283.119783 111.99287 \n", "L 283.119783 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 166.667609 111.99287 \n", "L 283.119783 111.99287 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 166.667609 18.83113 \n", "L 283.119783 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 306.410217 111.99287 \n", "L 422.862391 111.99287 \n", "L 422.862391 18.83113 \n", "L 306.410217 18.83113 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p9f1adb9541)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAKIAAACCCAYAAADFXhMtAAALUUlEQVR4nO2dW3MbxxGFzwJLkCDFq+52Ervy//9AHvInkqpUqpJYlmPZoimKpMQbRAKbB1WhTw/YkxVNiU3qfE+zCywASaM5M91nepru5LCDuHN0Hf2zdTP/4oyuZ1NrTy+sfUltALg4t487eWftwz17z5tf/DOvXtr7fnwxb0/+afd//ceue+Tvvx3P2385OJm3BxAiAeqIIgWNpDkXTnIBL7tOci+tffnBP3Nh193k1O4fH9j9wzf+mbe/WfvNa3vfrrWx62X2w6v9eXvyi332/r7J/P7hxD1zdGlThfdTa2tEFClQRxQpUEcUKdAc8Zbw4ZdaKIbCLzwXvKC5F88DAXRHNnfrDmnux/PAPZr7Aej2ac64b89P9w/t6yncAgCXR2fz9odT+22XF/ZnmM1892oaaw+HNg5qRBQpUEcUKWhv+wcIAAhkGijCN5wlsfBN9+EcTHdOEnpmmQwcv7f3nNB9ADgleT8zye0m9D0XU0S0Q9PcQWPjW8NaDGCwNJy3h6sjux9+shBfEHVEkQJJ8w2zkBlxLwYyywaEMktybhLaHdiqt9t7Ze/59Sf/Na/JnPCbPTM7OLKvoRUvAMwm9BtopdvwynZt2T2z9GRj3h5urNkLG3Yfm5vumWZz2y62rK0RUaRAHVGkQNL8OSllOvIJsoHhwpsEulOS5vdmLMBb8wl2+3tw7Nm1C0gf2sp4dlZMAei3Nq2tbJuVpXm7XV9xjww21+2CJXhry57f3PHfs/3Q2hv2Po2IIgXqiCIF6ogiBZoj3gC9DQx8PeV5YWBkBdC9MwMC9tmkSuGb197AMN21Zy72bY45PS3mhcRgZPPCQTAvHGxtuGewbeGXZofmfttBGwA2bM7YrG3ZZ4e/TIgviDqiSIGk+RPoJcHR9k3ASTAmZkxwGZNdnyXBj/+y1178YB/1wrInvF8EAE6PSerpZ66smPyOtlbdM6NnFn4ZPntsLzy2dvPkuf9tD59Ye5vet07Zk7UiszKiEFAr04NIhjqiSMFXK82hzLI3cFb476bBFk7KhnTk/+ve+u2X2PvZ2lwl4SdrX/7wMz+B85dm4T84sO+ZTm06MB77f8b1x2ZAGP/Z5LP5/jtr//F7/9u++ZO9tvPM7m8+svvLXs6xZNKKoa20MRhe3QYA8iryvgGNiCIF6ogiBfdiF98nr2aBuCAR2e67c2+n7969tYsj3vVGEvyGVsAH9H4AOKBKC+/Mtr9gQCAGY5O/ZnvLXnj61O6TrAIAnpPMPvrG2ryaHdVklqTeSau3/bO0llsCPhWNiCIF6ogiBeqIIgV3JnwTh1sAH3KJKmYVWQ43L6Ttk6e05ZINB4CfFx7Qa0c2F+xoyyYm3uSKAW2zXH8wbw83aH41HvtnaP9H85AyHk+/tTbNAwGg2aH545jMq0FW4+NvC0IuNzgPrKERUaRAHVGk4M5Ic5UoMxLdBwqvIEs7yXkZ8nH7TNjcQO3adtJB8P9+SFK4tOReakYkocskrRRuaZYKmXXhl6szGShl9jPKbh80IooUqCOKFKSW5mtZ8INCRYvHOZBRgbdsHls1BHAbcEWMcGaW/u4DrY551d4Wf70swfzaslVQaNZLOz5Z7R/Sanjb2mxMAApzwhJVZ4iMCUBoRvicK2VGI6JIgTqiSEE6ae7lE1yooBDIceQfBNBREBtnLLlkdDgtagie0zMTqknIv4fld1jIHwe0V2gFvEZyvCDN5AfcooA2GRjqPsHAwFBZNX8pOWY0IooUqCOKFKgjihSkmyOG1LIk0fui48MAlw3pooxJLbMSEWVPAB+y4XlclD0BvDmBwi9Ny6GY4js5FIMom3K7mZQSjYgiBeqIIgW3Ls2Lp3EG0hoVMCqvOYPC2ZOyuNFJkEE5tkOzOXvy8fMoBOQKW9Jf44gyGaUZYUxhljXzI+IBVUPgfSUAGle0iEI7I/ItLvna1i6DwuGbIHvy8VKmByHUEUUObnQ7aVVmIzt/7RBEt+WT5beoMz2hjMeJSWt3ckj3KXsC+KwJZ0xY5suVNkvwCknjmI52WDVrvpNSAFgl2z8/01bkPJJZlyUpV823myW5DhoRRQrUEUUKbn3VvHgIYp+qDRU5nwW2/ZqHMaIMFA+DnW6DwOhQev6GgZxGdv6rru8pGhFFCtQRRQrUEUUKfvccsXoaZ2RmjTImQDHfuzqb0pWZFa7gxdkPbl8Wz0S/m+drpbGVKyVw+GbVsiTNCoVluI2ifrTbPxJV30KRDem3NfSuhGwYjYgiBeqIIgVOmnsXOupdDDPaP8IZk0qW5IyOgDi3Nib+0Gt3zdmYacU/SHKKLcpskJw2Y3oP4PaWNJHpgH2CQ1+1IQz5DAL/IHAnsyTXQSOiSIE6okjB9VbNYfbjGlmS3odrVyz87vlgNVwaA1w2JJDTtiat0Wq2Z5Ykktl7LL81NCKKFKgjihS09YB0D6610rZ2VzMwRIHvvgYGZywoJG8QyGTNtBB5ACPJ7W1giOX4Pq+UGY2IIgXqiCIF6ogiBT58U6umEO0zqWZWgv0n06uPHCuvXQHMy0pta/7dbu5H/8/KoplLZECgalrNMmVMRr7qQhNlTaJ55cJekp71rL9CNCKKFKgjihTcbGZl4QiJHiGb61R6iLInJU0g00BcUJOf6VlnOix0VJVcyTGjEVGkQB1RpCCW5lqW5FrPBM/XsiQ1c0REKcHz+z19fmzb72tauMFV79eSSSnRiChSoI4oUtD2PkKCr0OfYFkeONgqwEHsIqDdK4hdq9LA0ubKAxclgfnatekUqAU/YnDYYmiGiIsjKYjt0YgoUqCOKFKgjihS0MbZi1oFBt4aWjlmjK/PqR41bQ3tyq2hPGfk58vTRRmeo/G8kNtl1YWV1Stfc9UY2qI2NRsnopM+q8eMXf3//msN2TAaEUUK1BFFClo+AiI85RMo6lmbZHYspVyNAUDHx0Pw8RL8naXkRhkUlsW2qDMd+QldQSR/gmfjqjMEp3kOy4O/o6MiKltI+TslwSEaEUUK1BFFCtowS1JKM0moq0FIq16XFQGAix4r4FrVhmg1WmY8opUyZ0lqMtsnS/LxBjX/v8xKivujEVGkQB1RpKANd+EVkunONHb1DYM24HfxRcdOlLb/aBdeE8gngCYqqBRJe+2ze1dqkIHhJtGIKFKgjihSoI4oUtD2KowJFDWwKbPiwjLe9OBDNmySrYRsmEGwtbOsTe2yLtZu+lZdCItufvpxZArZXA+NiCIF6ogiBS1nPzoOv7B/EHCmhe48MDMsZFZImqO9LbU61WxGIDODMzYA3vTg/ISjq9tA5aTRyp6TIGQjOf79aEQUKVBHFCloZ6/+Y1css6WF/5yuo8MWy22eLG2jq80I7nBFAKDTntzJT6vrdn/ZewtDCebV9KAwPfQ5+ekeHLZ4V9CIKFKgjihS0GL3v3bFtv9JsQKuSfD804oVMEswS6E797iw8PeR46Vyd11QUrhmeughx5LiL4dGRJECdUSRAnVEkYIWL/89v6jWtua5E4dFRsGh2YAPn3CYhg7tblY34ODDuaN5Yfk9qrpw59GIKFKgjihS0Do5rvkEOdxRFmjqQ1h/uufpTIja4j6gEVGkQB1RpKDF69d25epPFyvTFcuGdGPyBrpPK4wFK1cXRGq4ONK4qFsY+gkrxZH6rJSjYy+glXIGNCKKFKgjihT44y1qEnWdlXJERSbF14l6hEiBOqJIgTqiSEG7/9e/zS8e/GFr3l7+dse/89mzebNZI/Pqk+fW3nnqHmk2H9nF+rbd5+2gC0dIsMmV95zIwHCf0YgoUqCOKFLQTqdchIlCOYOijw6DGtauXWQ8XEGkqOhmz8O5/ZuC+5Lju4pGRJECdUSRgv8Bh9MVLLMRX68AAAAASUVORK5CYII=\" id=\"image694159b114\" transform=\"scale(1 -1) translate(0 -93.6)\" x=\"306.410217\" y=\"-18.39287\" width=\"116.64\" height=\"93.6\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#ma3a7e72af4\" x=\"307.574739\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(304.393489 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#ma3a7e72af4\" x=\"354.155609\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(347.793109 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#ma3a7e72af4\" x=\"400.736478\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(394.373978 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Sigma 0.5 -->\n", "     <g transform=\"translate(339.423804 140.269432) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"91.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"154.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"252.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"345.214844\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"408.837891\"/>\n", "      <use xlink:href=\"#DejaVuSans-35\" x=\"440.625\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"306.410217\" y=\"19.995652\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"306.410217\" y=\"43.286087\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"306.410217\" y=\"66.576522\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"306.410217\" y=\"89.866957\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 306.410217 111.99287 \n", "L 306.410217 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 422.862391 111.99287 \n", "L 422.862391 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 306.410217 111.99287 \n", "L 422.862391 111.99287 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 306.410217 18.83113 \n", "L 422.862391 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 446.152826 111.99287 \n", "L 562.605 111.99287 \n", "L 562.605 18.83113 \n", "L 446.152826 18.83113 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p8dbc878cbe)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image7b0d811e98\" transform=\"scale(1 -1) translate(0 -93.6)\" x=\"446.152826\" y=\"-18.39287\" width=\"116.64\" height=\"93.6\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#ma3a7e72af4\" x=\"447.317348\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(444.136098 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#ma3a7e72af4\" x=\"493.898217\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(487.535717 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#ma3a7e72af4\" x=\"540.479087\" y=\"111.99287\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(534.116587 126.591307) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_20\">\n", "     <!-- Sigma 1 -->\n", "     <g transform=\"translate(483.936726 140.269432) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-53\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"91.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"154.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"252.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"313.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"345.214844\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"446.152826\" y=\"19.995652\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"446.152826\" y=\"43.286087\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"446.152826\" y=\"66.576522\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m4c9c1ea3ea\" x=\"446.152826\" y=\"89.866957\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 446.152826 111.99287 \n", "L 446.152826 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 562.605 111.99287 \n", "L 562.605 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 446.152826 111.99287 \n", "L 562.605 111.99287 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 446.152826 18.83113 \n", "L 562.605 18.83113 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 596.085 123.624 \n", "L 601.9062 123.624 \n", "L 601.9062 7.2 \n", "L 596.085 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAgAAACiCAYAAAB8iIwDAAAA7klEQVR4nN2XSw4DIQxDUyn3P2s33ZWkN8iLZGCgsx1kP8cwn1d+3mnF5ZZR3Te3LAU6CkELjCyCFrACxoQFqc9BV5gQExkOiBmDFsgKBJkbGBiSLIYakxnYgg6OHpMnKcc8YMvxqOUutkzy+S13xbmYADkh5g2QqCC/9fKEmFd0QZA58BGEXehtygx3xFw/h7/oAp7E5oEMFBMVBluU980D60YFYgg5Jk9S7mIHpNwFODRSgEBDARjNk34noatOTLRYr4CTHHoXBNnogixkSIxZfxx0Rv2VIVGhwSBb1AJzFGQLUsC6dUjck+u7+AE+P2IIsbjxbAAAAABJRU5ErkJggg==\" id=\"image2ac88b6edd\" transform=\"scale(1 -1) translate(0 -116.64)\" x=\"596.16\" y=\"-6.48\" width=\"5.76\" height=\"116.64\"/>\n", "   <g id=\"matplotlib.axis_9\"/>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_29\">\n", "      <defs>\n", "       <path id=\"m9973745574\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9973745574\" x=\"601.9062\" y=\"81.630991\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 0.05 -->\n", "      <g transform=\"translate(608.9062 85.430209) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m9973745574\" x=\"601.9062\" y=\"39.637629\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 0.10 -->\n", "      <g transform=\"translate(608.9062 43.436847) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 596.085 123.624 \n", "L 598.9956 123.624 \n", "L 601.9062 123.624 \n", "L 601.9062 7.2 \n", "L 598.9956 7.2 \n", "L 596.085 7.2 \n", "L 596.085 123.624 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p4dc3aafbb0\">\n", "   <rect x=\"26.925\" y=\"18.83113\" width=\"116.452174\" height=\"93.161739\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pa0a3c65240\">\n", "   <rect x=\"166.667609\" y=\"18.83113\" width=\"116.452174\" height=\"93.161739\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p9f1adb9541\">\n", "   <rect x=\"306.410217\" y=\"18.83113\" width=\"116.452174\" height=\"93.161739\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p8dbc878cbe\">\n", "   <rect x=\"446.152826\" y=\"18.83113\" width=\"116.452174\" height=\"93.161739\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1200x300 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot(x_train, y_train, x_val, y_val, kernels, names, attention=True)"]}, {"cell_type": "markdown", "id": "6deff9de", "metadata": {"origin_pos": 22}, "source": ["As we would expect, the narrower the kernel, the narrower the range of large attention weights. It is also clear that picking the same width might not be ideal. In fact, :citet:`Silverman86` proposed a heuristic that depends on the local density. Many more such \"tricks\" have been proposed. For instance, :citet:`norelli2022asif` used a similar nearest-neighbor interpolation technique for designing cross-modal image and text representations. \n", "\n", "The astute reader might wonder why we are providing this deep dive for a method that is over half a century old. First, it is one of the earliest precursors of modern attention mechanisms. Second, it is great for visualization. Third, and just as importantly, it demonstrates the limits of hand-crafted attention mechanisms. A much better strategy is to *learn* the mechanism, by learning the representations for queries and keys. This is what we will embark on in the following sections.\n", "\n", "\n", "## Summary\n", "\n", "Nadaraya--Watson kernel regression is an early precursor of the current attention mechanisms. \n", "It can be used directly with little to no training or tuning, either for classification or regression. \n", "The attention weight is assigned according to the similarity (or distance) between query and key, and according to how many similar observations are available. \n", "\n", "## Exercises\n", "\n", "1. Parzen windows density estimates are given by $\\hat{p}(\\mathbf{x}) = \\frac{1}{n} \\sum_i k(\\mathbf{x}, \\mathbf{x}_i)$. Prove that for binary classification the function $\\hat{p}(\\mathbf{x}, y=1) - \\hat{p}(\\mathbf{x}, y=-1)$, as obtained by Parzen windows is equivalent to Nadaraya--Watson classification. \n", "1. Implement stochastic gradient descent to learn a good value for kernel widths in Nadaraya--Watson regression. \n", "    1. What happens if you just use the above estimates to minimize $(f(\\mathbf{x_i}) - y_i)^2$ directly? Hint: $y_i$ is part of the terms used to compute $f$.\n", "    1. Remove $(\\mathbf{x}_i, y_i)$ from the estimate for $f(\\mathbf{x}_i)$ and optimize over the kernel widths. Do you still observe overfitting?\n", "1. Assume that all $\\mathbf{x}$ lie on the unit sphere, i.e., all satisfy $\\|\\mathbf{x}\\| = 1$. Can you simplify the $\\|\\mathbf{x} - \\mathbf{x}_i\\|^2$ term in the exponential? Hint: we will later see that this is very closely related to dot product attention. \n", "1. Recall that :citet:`mack1982weak` proved that <PERSON><PERSON><PERSON>--Watson estimation is consistent. How quickly should you reduce the scale for the attention mechanism as you get more data? Provide some intuition for your answer. Does it depend on the dimensionality of the data? How?\n"]}, {"cell_type": "markdown", "id": "288e3042", "metadata": {"origin_pos": 24, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1599)\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "e4433f58", "metadata": {"origin_pos": 1}, "source": ["# The Bahdanau Attention Mechanism\n", ":label:`sec_seq2seq_attention`\n", "\n", "When we encountered machine translation in :numref:`sec_seq2seq`,\n", "we designed an encoder--decoder architecture for sequence-to-sequence learning\n", "based on two RNNs :cite:`Sutskever.Vinyals.Le.2014`.\n", "Specifically, the RNN encoder transforms a variable-length sequence\n", "into a *fixed-shape* context variable.\n", "Then, the RNN decoder generates the output (target) sequence token by token\n", "based on the generated tokens and the context variable.\n", "\n", "Recall :numref:`fig_seq2seq_details` which we repeat (:numref:`fig_s2s_attention_state`) with some additional detail. Conventionally, in an RNN all relevant information about a source sequence is translated into some internal *fixed-dimensional* state representation by the encoder. It is this very state that is used by the decoder as the complete and exclusive source of information for generating the translated sequence. In other words, the sequence-to-sequence mechanism treats the intermediate state as a sufficient statistic of whatever string might have served as input.\n", "\n", "![Sequence-to-sequence model. The state, as generated by the encoder, is the only piece of information shared between the encoder and the decoder.](../img/seq2seq-state.svg)\n", ":label:`fig_s2s_attention_state`\n", "\n", "While this is quite reasonable for short sequences, it is clear that it is infeasible for long ones, such as a book chapter or even just a very long sentence. After all, before too long there will simply not be enough \"space\" in the intermediate representation to store all that is important in the source sequence. Consequently the decoder will fail to translate long and complex sentences. One of the first to encounter this was :citet:`Graves.2013` who tried to design an RNN to generate handwritten text. Since the source text has arbitrary length they designed a differentiable attention model\n", "to align text characters with the much longer pen trace,\n", "where the alignment moves only in one direction. This, in turn, draws on decoding algorithms in speech recognition, e.g., hidden Markov models :cite:`rabiner1993fundamentals`.\n", "\n", "Inspired by the idea of learning to align,\n", ":citet:`Bahdanau.Cho.Bengio.2014` proposed a differentiable attention model\n", "*without* the unidirectional alignment limitation.\n", "When predicting a token,\n", "if not all the input tokens are relevant,\n", "the model aligns (or attends)\n", "only to parts of the input sequence\n", "that are deemed relevant to the current prediction. This is then used to update the current state before generating the next token. While quite innocuous in its description, this *Bahdanau attention mechanism* has arguably turned into one of the most influential ideas of the past decade in deep learning, giving rise to Transformers :cite:`Vaswani.Shazeer.Parmar.ea.2017` and many related new architectures.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "64405f0b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:13.059684Z", "iopub.status.busy": "2023-08-18T19:46:13.058589Z", "iopub.status.idle": "2023-08-18T19:46:16.109138Z", "shell.execute_reply": "2023-08-18T19:46:16.108090Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "2196d7ca", "metadata": {"origin_pos": 6}, "source": ["## Model\n", "\n", "We follow the notation introduced by the sequence-to-sequence architecture of :numref:`sec_seq2seq`, in particular :eqref:`eq_seq2seq_s_t`.\n", "The key idea is that instead of keeping the state,\n", "i.e., the context variable $\\mathbf{c}$ summarizing the source sentence, as fixed, we dynamically update it, as a function of both the original text (encoder hidden states $\\mathbf{h}_{t}$) and the text that was already generated (decoder hidden states $\\mathbf{s}_{t'-1}$). This yields $\\mathbf{c}_{t'}$, which is updated after any decoding time step $t'$. Suppose that the input sequence is of length $T$. In this case the context variable is the output of attention pooling:\n", "\n", "$$\\mathbf{c}_{t'} = \\sum_{t=1}^{T} \\alpha(\\mathbf{s}_{t' - 1}, \\mathbf{h}_{t}) \\mathbf{h}_{t}.$$\n", "\n", "We used $\\mathbf{s}_{t' - 1}$ as the query, and\n", "$\\mathbf{h}_{t}$ as both the key and the value. Note that $\\mathbf{c}_{t'}$ is then used to generate the state $\\mathbf{s}_{t'}$ and to generate a new token: see :eqref:`eq_seq2seq_s_t`. In particular, the attention weight $\\alpha$ is computed as in :eqref:`eq_attn-scoring-alpha`\n", "using the additive attention scoring function\n", "defined by :eqref:`eq_additive-attn`.\n", "This RNN encoder--decoder architecture\n", "using attention is depicted in :numref:`fig_s2s_attention_details`. Note that later this model was modified so as to include the already generated tokens in the decoder as further context (i.e., the attention sum does not stop at $T$ but rather it proceeds up to $t'-1$). For instance, see :citet:`chan2015listen` for a description of this strategy, as applied to speech recognition.\n", "\n", "![Layers in an RNN encoder--decoder model with the <PERSON>hdan<PERSON> attention mechanism.](../img/seq2seq-details-attention.svg)\n", ":label:`fig_s2s_attention_details`\n", "\n", "## Defining the Decoder with Attention\n", "\n", "To implement the RNN encoder--decoder with attention,\n", "we only need to redefine the decoder (omitting the generated symbols from the attention function simplifies the design). Let's begin with [**the base interface for decoders with attention**] by defining the quite unsurprisingly named `AttentionDecoder` class.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "7392fc80", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:16.115524Z", "iopub.status.busy": "2023-08-18T19:46:16.114277Z", "iopub.status.idle": "2023-08-18T19:46:16.121848Z", "shell.execute_reply": "2023-08-18T19:46:16.120397Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["class AttentionDecoder(d2l.Decoder):  #@save\n", "    \"\"\"The base attention-based decoder interface.\"\"\"\n", "    def __init__(self):\n", "        super().__init__()\n", "\n", "    @property\n", "    def attention_weights(self):\n", "        raise NotImplementedError"]}, {"cell_type": "markdown", "id": "33cd556d", "metadata": {"origin_pos": 8}, "source": ["We need to [**implement the RNN decoder**]\n", "in the `Seq2SeqAttentionDecoder` class.\n", "The state of the decoder is initialized with\n", "(i) the hidden states of the last layer of the encoder at all time steps, used as keys and values for attention;\n", "(ii) the hidden state of the encoder at all layers at the final time step, which serves to initialize the hidden state of the decoder;\n", "and (iii) the valid length of the encoder, to exclude the padding tokens in attention pooling.\n", "At each decoding time step, the hidden state of the final layer of the decoder, obtained at the previous time step, is used as the query of the attention mechanism.\n", "Both the output of the attention mechanism and the input embedding are concatenated to serve as the input of the RNN decoder.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f0a3f536", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:16.128312Z", "iopub.status.busy": "2023-08-18T19:46:16.125898Z", "iopub.status.idle": "2023-08-18T19:46:16.142962Z", "shell.execute_reply": "2023-08-18T19:46:16.141775Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["class Seq2SeqAttentionDecoder(AttentionDecoder):\n", "    def __init__(self, vocab_size, embed_size, num_hiddens, num_layers,\n", "                 dropout=0):\n", "        super().__init__()\n", "        self.attention = d2l.AdditiveAttention(num_hiddens, dropout)\n", "        self.embedding = nn.Embedding(vocab_size, embed_size)\n", "        self.rnn = nn.GRU(\n", "            embed_size + num_hiddens, num_hiddens, num_layers,\n", "            dropout=dropout)\n", "        self.dense = nn.LazyLinear(vocab_size)\n", "        self.apply(d2l.init_seq2seq)\n", "\n", "    def init_state(self, enc_outputs, enc_valid_lens):\n", "        # Shape of outputs: (num_steps, batch_size, num_hiddens).\n", "        # Shape of hidden_state: (num_layers, batch_size, num_hiddens)\n", "        outputs, hidden_state = enc_outputs\n", "        return (outputs.permute(1, 0, 2), hidden_state, enc_valid_lens)\n", "\n", "    def forward(self, X, state):\n", "        # Shape of enc_outputs: (batch_size, num_steps, num_hiddens).\n", "        # Shape of hidden_state: (num_layers, batch_size, num_hiddens)\n", "        enc_outputs, hidden_state, enc_valid_lens = state\n", "        # Shape of the output X: (num_steps, batch_size, embed_size)\n", "        X = self.embedding(X).permute(1, 0, 2)\n", "        outputs, self._attention_weights = [], []\n", "        for x in X:\n", "            # Shape of query: (batch_size, 1, num_hiddens)\n", "            query = torch.unsqueeze(hidden_state[-1], dim=1)\n", "            # Shape of context: (batch_size, 1, num_hiddens)\n", "            context = self.attention(\n", "                query, enc_outputs, enc_outputs, enc_valid_lens)\n", "            # Concatenate on the feature dimension\n", "            x = torch.cat((context, torch.unsqueeze(x, dim=1)), dim=-1)\n", "            # Reshape x as (1, batch_size, embed_size + num_hiddens)\n", "            out, hidden_state = self.rnn(x.permute(1, 0, 2), hidden_state)\n", "            outputs.append(out)\n", "            self._attention_weights.append(self.attention.attention_weights)\n", "        # After fully connected layer transformation, shape of outputs:\n", "        # (num_steps, batch_size, vocab_size)\n", "        outputs = self.dense(torch.cat(outputs, dim=0))\n", "        return outputs.permute(1, 0, 2), [enc_outputs, hidden_state,\n", "                                          enc_valid_lens]\n", "\n", "    @property\n", "    def attention_weights(self):\n", "        return self._attention_weights"]}, {"cell_type": "markdown", "id": "af402335", "metadata": {"origin_pos": 13}, "source": ["In the following, we [**test the implemented\n", "decoder**] with attention\n", "using a minibatch of four sequences, each of which are seven time steps long.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "e7d6e370", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:16.149119Z", "iopub.status.busy": "2023-08-18T19:46:16.148126Z", "iopub.status.idle": "2023-08-18T19:46:16.201990Z", "shell.execute_reply": "2023-08-18T19:46:16.200456Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["vocab_size, embed_size, num_hiddens, num_layers = 10, 8, 16, 2\n", "batch_size, num_steps = 4, 7\n", "encoder = d2l.Seq2SeqEncoder(vocab_size, embed_size, num_hiddens, num_layers)\n", "decoder = Seq2SeqAttentionDecoder(vocab_size, embed_size, num_hiddens,\n", "                                  num_layers)\n", "X = torch.zeros((batch_size, num_steps), dtype=torch.long)\n", "state = decoder.init_state(encoder(X), None)\n", "output, state = decoder(X, state)\n", "d2l.check_shape(output, (batch_size, num_steps, vocab_size))\n", "d2l.check_shape(state[0], (batch_size, num_steps, num_hiddens))\n", "d2l.check_shape(state[1][0], (batch_size, num_hiddens))"]}, {"cell_type": "markdown", "id": "c6c52079", "metadata": {"origin_pos": 15}, "source": ["## [**Training**]\n", "\n", "Now that we specified the new decoder we can proceed analogously to :numref:`sec_seq2seq_training`:\n", "specify the hyperparameters, instantiate\n", "a regular encoder and a decoder with attention,\n", "and train this model for machine translation.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "a73f9cc6", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:16.207768Z", "iopub.status.busy": "2023-08-18T19:46:16.207253Z", "iopub.status.idle": "2023-08-18T19:46:52.077164Z", "shell.execute_reply": "2023-08-18T19:46:52.076268Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"229.425pt\" height=\"183.35625pt\" viewBox=\"0 0 229.425 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:46:51.953577</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 229.425 183.35625 \n", "L 229.425 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 20.5625 145.8 \n", "L 215.8625 145.8 \n", "L 215.8625 7.2 \n", "L 20.5625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m02bd3650cc\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m02bd3650cc\" x=\"20.5625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(17.38125 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m02bd3650cc\" x=\"53.1125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(49.93125 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m02bd3650cc\" x=\"85.6625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(79.3 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m02bd3650cc\" x=\"118.2125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(111.85 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m02bd3650cc\" x=\"150.7625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(144.4 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m02bd3650cc\" x=\"183.3125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 25 -->\n", "      <g transform=\"translate(176.95 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m02bd3650cc\" x=\"215.8625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(209.5 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(102.984375 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_8\">\n", "      <defs>\n", "       <path id=\"mccd43e75b8\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mccd43e75b8\" x=\"20.5625\" y=\"142.409122\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(7.2 146.208341) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mccd43e75b8\" x=\"20.5625\" y=\"113.158256\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(7.2 116.957474) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mccd43e75b8\" x=\"20.5625\" y=\"83.907389\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(7.2 87.706608) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#mccd43e75b8\" x=\"20.5625\" y=\"54.656522\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(7.2 58.455741) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mccd43e75b8\" x=\"20.5625\" y=\"25.405656\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(7.2 29.204874) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 21.37625 13.5 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 27.0725 85.647459 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 27.0725 85.647459 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 27.0725 85.647459 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_127\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_128\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_129\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_130\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_131\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_132\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_133\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_134\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_135\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_136\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_137\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_138\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_139\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_140\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_141\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_142\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_143\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_144\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_145\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_146\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_147\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_148\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_149\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_150\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_151\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_152\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_153\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_154\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_155\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_156\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_157\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_158\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_159\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_160\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_161\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_162\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_163\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_164\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_165\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_166\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_167\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "L 190.63625 139.438294 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_168\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_169\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "L 190.63625 139.438294 \n", "L 193.89125 139.046501 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_170\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_171\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "L 190.63625 139.438294 \n", "L 193.89125 139.046501 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_172\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "L 196.3325 87.847449 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_173\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "L 190.63625 139.438294 \n", "L 193.89125 139.046501 \n", "L 197.14625 139.486349 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_174\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "L 196.3325 87.847449 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_175\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "L 190.63625 139.438294 \n", "L 193.89125 139.046501 \n", "L 197.14625 139.486349 \n", "L 200.40125 139.167098 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_176\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "L 196.3325 87.847449 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_177\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "L 190.63625 139.438294 \n", "L 193.89125 139.046501 \n", "L 197.14625 139.486349 \n", "L 200.40125 139.167098 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_178\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "L 196.3325 87.847449 \n", "L 202.8425 86.588901 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_179\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "L 190.63625 139.438294 \n", "L 193.89125 139.046501 \n", "L 197.14625 139.486349 \n", "L 200.40125 139.167098 \n", "L 203.65625 139.298126 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_180\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "L 196.3325 87.847449 \n", "L 202.8425 86.588901 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_181\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "L 190.63625 139.438294 \n", "L 193.89125 139.046501 \n", "L 197.14625 139.486349 \n", "L 200.40125 139.167098 \n", "L 203.65625 139.298126 \n", "L 206.91125 139.5 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_182\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "L 196.3325 87.847449 \n", "L 202.8425 86.588901 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_183\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "L 190.63625 139.438294 \n", "L 193.89125 139.046501 \n", "L 197.14625 139.486349 \n", "L 200.40125 139.167098 \n", "L 203.65625 139.298126 \n", "L 206.91125 139.5 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_184\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "L 196.3325 87.847449 \n", "L 202.8425 86.588901 \n", "L 209.3525 86.050424 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_185\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "L 190.63625 139.438294 \n", "L 193.89125 139.046501 \n", "L 197.14625 139.486349 \n", "L 200.40125 139.167098 \n", "L 203.65625 139.298126 \n", "L 206.91125 139.5 \n", "L 210.16625 139.351648 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_186\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "L 196.3325 87.847449 \n", "L 202.8425 86.588901 \n", "L 209.3525 86.050424 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_187\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "L 190.63625 139.438294 \n", "L 193.89125 139.046501 \n", "L 197.14625 139.486349 \n", "L 200.40125 139.167098 \n", "L 203.65625 139.298126 \n", "L 206.91125 139.5 \n", "L 210.16625 139.351648 \n", "L 213.42125 139.307996 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_188\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "L 196.3325 87.847449 \n", "L 202.8425 86.588901 \n", "L 209.3525 86.050424 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_189\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 84.495131 \n", "L 27.88625 99.148943 \n", "L 31.14125 106.598298 \n", "L 34.39625 109.600274 \n", "L 37.65125 109.682575 \n", "L 40.90625 111.858474 \n", "L 44.16125 114.620823 \n", "L 47.41625 115.740442 \n", "L 50.67125 117.359805 \n", "L 53.92625 120.336679 \n", "L 57.18125 118.143253 \n", "L 60.43625 121.420098 \n", "L 63.69125 122.185113 \n", "L 66.94625 123.535594 \n", "L 70.20125 124.711712 \n", "L 73.45625 124.78016 \n", "L 76.71125 128.072713 \n", "L 79.96625 128.073673 \n", "L 83.22125 128.451201 \n", "L 86.47625 129.736287 \n", "L 89.73125 130.349185 \n", "L 92.98625 131.90653 \n", "L 96.24125 131.155638 \n", "L 99.49625 132.726827 \n", "L 102.75125 132.802553 \n", "L 106.00625 134.168081 \n", "L 109.26125 133.720748 \n", "L 112.51625 135.23981 \n", "L 115.77125 134.673286 \n", "L 119.02625 135.579251 \n", "L 122.28125 135.965727 \n", "L 125.53625 136.405406 \n", "L 128.79125 136.575463 \n", "L 132.04625 137.540734 \n", "L 135.30125 136.69541 \n", "L 138.55625 137.408629 \n", "L 141.81125 137.534347 \n", "L 145.06625 138.086678 \n", "L 148.32125 137.963843 \n", "L 151.57625 138.649736 \n", "L 154.83125 137.91509 \n", "L 158.08625 138.56803 \n", "L 161.34125 138.453253 \n", "L 164.59625 138.630995 \n", "L 167.85125 138.655819 \n", "L 171.10625 138.94877 \n", "L 174.36125 138.793776 \n", "L 177.61625 139.163013 \n", "L 180.87125 138.875793 \n", "L 184.12625 139.219269 \n", "L 187.38125 139.046849 \n", "L 190.63625 139.438294 \n", "L 193.89125 139.046501 \n", "L 197.14625 139.486349 \n", "L 200.40125 139.167098 \n", "L 203.65625 139.298126 \n", "L 206.91125 139.5 \n", "L 210.16625 139.351648 \n", "L 213.42125 139.307996 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_190\">\n", "    <path d=\"M 27.0725 85.647459 \n", "L 33.5825 90.134094 \n", "L 40.0925 92.99896 \n", "L 46.6025 96.216022 \n", "L 53.1125 98.813224 \n", "L 59.6225 100.888227 \n", "L 66.1325 101.463487 \n", "L 72.6425 100.558066 \n", "L 79.1525 101.360677 \n", "L 85.6625 101.657188 \n", "L 92.1725 100.905205 \n", "L 98.6825 99.684704 \n", "L 105.1925 99.093925 \n", "L 111.7025 97.012689 \n", "L 118.2125 97.409614 \n", "L 124.7225 94.620625 \n", "L 131.2325 96.657192 \n", "L 137.7425 94.336919 \n", "L 144.2525 94.59038 \n", "L 150.7625 92.544607 \n", "L 157.2725 91.764707 \n", "L 163.7825 89.53457 \n", "L 170.2925 89.447369 \n", "L 176.8025 87.362992 \n", "L 183.3125 87.581105 \n", "L 189.8225 87.01269 \n", "L 196.3325 87.847449 \n", "L 202.8425 86.588901 \n", "L 209.3525 86.050424 \n", "L 215.8625 88.263365 \n", "\" clip-path=\"url(#pb6851bdab6)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 20.5625 145.8 \n", "L 20.5625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 215.8625 145.8 \n", "L 215.8625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 20.5625 145.8 \n", "L 215.8625 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 20.5625 7.2 \n", "L 215.8625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 129.271875 45.1125 \n", "L 208.8625 45.1125 \n", "Q 210.8625 45.1125 210.8625 43.1125 \n", "L 210.8625 14.2 \n", "Q 210.8625 12.2 208.8625 12.2 \n", "L 129.271875 12.2 \n", "Q 127.271875 12.2 127.271875 14.2 \n", "L 127.271875 43.1125 \n", "Q 127.271875 45.1125 129.271875 45.1125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_191\">\n", "     <path d=\"M 131.271875 20.298438 \n", "L 141.271875 20.298438 \n", "L 151.271875 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(159.271875 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_192\">\n", "     <path d=\"M 131.271875 35.254688 \n", "L 141.271875 35.254688 \n", "L 151.271875 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(159.271875 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb6851bdab6\">\n", "   <rect x=\"20.5625\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = d2l.MTFraEng(batch_size=128)\n", "embed_size, num_hiddens, num_layers, dropout = 256, 256, 2, 0.2\n", "encoder = d2l.Seq2SeqEncoder(\n", "    len(data.src_vocab), embed_size, num_hiddens, num_layers, dropout)\n", "decoder = Seq2SeqAttentionDecoder(\n", "    len(data.tgt_vocab), embed_size, num_hiddens, num_layers, dropout)\n", "model = d2l.Seq2Seq(encoder, decoder, tgt_pad=data.tgt_vocab['<pad>'],\n", "                    lr=0.005)\n", "trainer = d2l.Trainer(max_epochs=30, gradient_clip_val=1, num_gpus=1)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "62af9112", "metadata": {"origin_pos": 17}, "source": ["After the model is trained,\n", "we use it to [**translate a few English sentences**]\n", "into French and compute their BLEU scores.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "a22c2296", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:52.082122Z", "iopub.status.busy": "2023-08-18T19:46:52.081526Z", "iopub.status.idle": "2023-08-18T19:46:52.108612Z", "shell.execute_reply": "2023-08-18T19:46:52.107700Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["go . => ['va', '!'], bleu,1.000\n", "i lost . => [\"j'ai\", 'perdu', '.'], bleu,1.000\n", "he's calm . => ['il', 'court', '.'], bleu,0.000\n", "i'm home . => ['je', 'suis', 'chez', 'moi', '.'], bleu,1.000\n"]}], "source": ["engs = ['go .', 'i lost .', 'he\\'s calm .', 'i\\'m home .']\n", "fras = ['va !', 'j\\'ai perdu .', 'il est calme .', 'je suis chez moi .']\n", "preds, _ = model.predict_step(\n", "    data.build(engs, fras), d2l.try_gpu(), data.num_steps)\n", "for en, fr, p in zip(engs, fras, preds):\n", "    translation = []\n", "    for token in data.tgt_vocab.to_tokens(p):\n", "        if token == '<eos>':\n", "            break\n", "        translation.append(token)\n", "    print(f'{en} => {translation}, bleu,'\n", "          f'{d2l.bleu(\" \".join(translation), fr, k=2):.3f}')"]}, {"cell_type": "markdown", "id": "f0015d7f", "metadata": {"origin_pos": 19}, "source": ["Let's [**visualize the attention weights**]\n", "when translating the last English sentence.\n", "We see that each query assigns non-uniform weights\n", "over key--value pairs.\n", "It shows that at each decoding step,\n", "different parts of the input sequences\n", "are selectively aggregated in the attention pooling.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "5b39b45c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:52.111885Z", "iopub.status.busy": "2023-08-18T19:46:52.111597Z", "iopub.status.idle": "2023-08-18T19:46:52.130667Z", "shell.execute_reply": "2023-08-18T19:46:52.129810Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [], "source": ["_, dec_attention_weights = model.predict_step(\n", "    data.build([engs[-1]], [fras[-1]]), d2l.try_gpu(), data.num_steps, True)\n", "attention_weights = torch.cat(\n", "    [step[0][0][0] for step in dec_attention_weights], 0)\n", "attention_weights = attention_weights.reshape((1, 1, -1, data.num_steps))"]}, {"cell_type": "code", "execution_count": 8, "id": "e9c665a8", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:46:52.134058Z", "iopub.status.busy": "2023-08-18T19:46:52.133495Z", "iopub.status.idle": "2023-08-18T19:46:52.369882Z", "shell.execute_reply": "2023-08-18T19:46:52.368741Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"143.43925pt\" height=\"183.35625pt\" viewBox=\"0 0 143.43925 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:46:52.318433</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 143.43925 183.35625 \n", "L 143.43925 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 34.**********.8 \n", "L 95.**********.8 \n", "L 95.840625 7.2 \n", "L 34.240625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pf6344916d1)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAFYAAADBCAYAAABVNUcMAAACv0lEQVR4nO3crYqUYRyG8XvGl5WVEZMfaQ1aZGHThDWZLGazbcEkaPIEPACLaDaYrAY/MCjYDMKCwhpEw+KmAWGEYeb1JO4rCNfvAO5w8W8PPJPj+bUxZScny/ZkkuTy7vn65uzFq/pmkkyRVRmWYliIYSGGhRgWYliIYSGGhRgWYliIYSGGhRgWYliIYSGGhRgWYliIYSEDMTqO9ffJJMl0ONUf3az7m/FiMYaFGBZiWIhhIYaFGBZiWIhhIYaFGBZiWIhhIYaFGBZiWIhhIYaFGBZiWIhhIZPV4/v9J9Wjo/pkkiy/H9c3ty6eq28mXizGsBDDQgwLMSzEsBDDQgwLMSzEsBDDQgwLMSzEsBDDQgwLMSzEsBDDQgwLmWx+fa0/Jq6fPWpPJknePnlT39zfvVDfTLxYjGEhhoUYFmJYiGEhhoUYFmJYiGEhhoUYFmJYiGEhhoUYFmJYiGEhhoUYFmJYyLD5+a0++vHpu/pmklzfu1TfnN2+Wd9MvFiMYSGGhRgWYliIYSGGhRgWYliIYSGGhRgWYliIYSGGhRgWYliIYSGGhRgWMowvn9dHP//5W99MkvnsdH1zeudhfTPxYjGGhRgWYliIYSGGhRgWYliIYSGGhRgWYliIYSGGhRgWYliIYSGGhRgWYliIYSGT9eGH+se8i4O77ckkyWq1qW+everHvP8Vw0IMCzEsxLAQw0IMCzEsxLAQw0IMCzEsxLAQw0IMCzEsxLAQw0IMCzEsZBi/fKqPvj78Xd9Mklv7O/XNrXsP6puJF4sxLMSwEMNCDAsxLMSwEMNCDAsxLMSwEMNCDAsxLMSwEMNCDAsxLMSwEMNCDAsZsn2mPvp+saxvJsn8x6K+eWXvRn0z8WIxhoUYFmJYiGEhhoUYFmJYiGEhhoUYFmJYiGEhhoUYFmJYiGEhhoUYFmJYyD/C9j3s594P2gAAAABJRU5ErkJggg==\" id=\"image93707d387e\" transform=\"scale(1 -1) translate(0 -138.96)\" x=\"34.240625\" y=\"-6.84\" width=\"61.92\" height=\"138.96\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m7b98490c04\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7b98490c04\" x=\"41.940625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(38.759375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m7b98490c04\" x=\"72.740625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(69.559375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- Key positions -->\n", "     <g transform=\"translate(31.977344 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4b\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2694 \n", "L 3353 4666 \n", "L 4166 4666 \n", "L 1850 2491 \n", "L 4331 0 \n", "L 3500 0 \n", "L 1259 2247 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4b\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"60.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"122.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"181.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"213.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"276.542969\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"337.724609\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"389.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"417.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"456.816406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"484.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"545.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"609.160156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"me12e79ec55\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me12e79ec55\" x=\"34.240625\" y=\"14.9\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(20.878125 18.699219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#me12e79ec55\" x=\"34.240625\" y=\"45.7\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(20.878125 49.499219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#me12e79ec55\" x=\"34.240625\" y=\"76.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(20.878125 80.299219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#me12e79ec55\" x=\"34.240625\" y=\"107.3\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(20.878125 111.099219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#me12e79ec55\" x=\"34.240625\" y=\"138.1\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(20.878125 141.899219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- Query positions -->\n", "     <g transform=\"translate(14.798437 115.694531) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-51\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 3406 84 \n", "L 4238 -825 \n", "L 3475 -825 \n", "L 2784 -78 \n", "Q 2681 -84 2626 -87 \n", "Q 2572 -91 2522 -91 \n", "Q 1538 -91 948 567 \n", "Q 359 1225 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1516 4351 937 \n", "Q 4025 359 3406 84 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-51\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"142.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"203.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"244.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"303.90625\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"335.693359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"399.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"460.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"512.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"540.234375\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"579.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"607.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"668.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"731.787109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 34.**********.8 \n", "L 34.240625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 95.**********.8 \n", "L 95.840625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 34.**********.8 \n", "L 95.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 34.240625 7.2 \n", "L 95.840625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 102.815625 118.08 \n", "L 106.973625 118.08 \n", "L 106.973625 34.92 \n", "L 102.815625 34.92 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAYAAABzCAYAAAC7I3M6AAAAx0lEQVR4nL2VSQ7EMAgEicT/vzqXOSUs+QBlqSPLObrVFGCIr/7/2obPrXI6N7cuVagRsXQAvNmhZ4UF6unK8HjUrLAlqTosCa6HQoEKbD2UnlXENgYLchNTnkQMpY/PhynRu0sFMhz3gxhyqMV+yPCNBXJ3cc+xQGSAUMRAhw7vhL9on4DLTSxKt+jxQiEwlMxIGZ5xgEF1oFD0pKIw35+ZJzrmc/MyhBODHBvh6EDGPLkrB8yCeZDj2RcKHfcBBrbkxq1V4S8NNhBOPsFR5gAAAABJRU5ErkJggg==\" id=\"imageb2f72f687f\" transform=\"scale(1 -1) translate(0 -82.8)\" x=\"102.96\" y=\"-34.56\" width=\"4.32\" height=\"82.8\"/>\n", "   <g id=\"matplotlib.axis_3\"/>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_8\">\n", "      <defs>\n", "       <path id=\"m743def79cf\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m743def79cf\" x=\"106.973625\" y=\"102.281991\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.20 -->\n", "      <g transform=\"translate(113.973625 106.08121) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m743def79cf\" x=\"106.973625\" y=\"77.054904\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.25 -->\n", "      <g transform=\"translate(113.973625 80.854123) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m743def79cf\" x=\"106.973625\" y=\"51.827816\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.30 -->\n", "      <g transform=\"translate(113.973625 55.627035) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 102.815625 118.08 \n", "L 104.894625 118.08 \n", "L 106.973625 118.08 \n", "L 106.973625 34.92 \n", "L 104.894625 34.92 \n", "L 102.815625 34.92 \n", "L 102.815625 118.08 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf6344916d1\">\n", "   <rect x=\"34.240625\" y=\"7.2\" width=\"61.6\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 250x250 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plus one to include the end-of-sequence token\n", "d2l.show_heatmaps(\n", "    attention_weights[:, :, :, :len(engs[-1].split()) + 1].cpu(),\n", "    xlabel='Key positions', ylabel='Query positions')"]}, {"cell_type": "markdown", "id": "ebbb775a", "metadata": {"origin_pos": 25}, "source": ["## Summary\n", "\n", "When predicting a token, if not all the input tokens are relevant, the RNN encoder--decoder with the <PERSON><PERSON><PERSON><PERSON> attention mechanism selectively aggregates different parts of the input sequence. This is achieved by treating the state (context variable) as an output of additive attention pooling.\n", "In the RNN encoder--decoder, the <PERSON>hdanau attention mechanism treats the decoder hidden state at the previous time step as the query, and the encoder hidden states at all the time steps as both the keys and values.\n", "\n", "\n", "## Exercises\n", "\n", "1. Replace GRU with LSTM in the experiment.\n", "1. Modify the experiment to replace the additive attention scoring function with the scaled dot-product. How does it influence the training efficiency?\n"]}, {"cell_type": "markdown", "id": "3f7db1a8", "metadata": {"origin_pos": 27, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1065)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
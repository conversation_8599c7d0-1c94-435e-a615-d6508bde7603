{"cells": [{"cell_type": "markdown", "id": "f3bb03f2", "metadata": {"origin_pos": 1}, "source": ["# Self-Attention and Positional Encoding\n", ":label:`sec_self-attention-and-positional-encoding`\n", "\n", "In deep learning, we often use CNNs or RNNs to encode sequences.\n", "Now with attention mechanisms in mind, \n", "imagine feeding a sequence of tokens \n", "into an attention mechanism\n", "such that at every step,\n", "each token has its own query, keys, and values.\n", "Here, when computing the value of a token's representation at the next layer,\n", "the token can attend (via its query vector) to any other's token \n", "(matching based on their key vectors).\n", "Using the full set of query-key compatibility scores,\n", "we can compute, for each token, a representation\n", "by building the appropriate weighted sum\n", "over the other tokens. \n", "Because every token is attending to each other token\n", "(unlike the case where decoder steps attend to encoder steps),\n", "such architectures are typically described as *self-attention* models :cite:`Lin.Feng.Santos.ea.2017,Vaswani.Shazeer.Parmar.ea.2017`, \n", "and elsewhere described as *intra-attention* model :cite:`<PERSON><PERSON>Dong.Lapata.2016,<PERSON><PERSON><PERSON><PERSON>Tackstrom.Das.ea.2016,Paulus.Xiong.Socher.2017`.\n", "In this section, we will discuss sequence encoding using self-attention,\n", "including using additional information for the sequence order.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "b2969e34", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:32.804452Z", "iopub.status.busy": "2023-08-18T19:30:32.803811Z", "iopub.status.idle": "2023-08-18T19:30:35.929844Z", "shell.execute_reply": "2023-08-18T19:30:35.926598Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import math\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "5bb88dd8", "metadata": {"origin_pos": 6}, "source": ["## [**Self-Attention**]\n", "\n", "Given a sequence of input tokens\n", "$\\mathbf{x}_1, \\ldots, \\mathbf{x}_n$ where any $\\mathbf{x}_i \\in \\mathbb{R}^d$ ($1 \\leq i \\leq n$),\n", "its self-attention outputs\n", "a sequence of the same length\n", "$\\mathbf{y}_1, \\ldots, \\mathbf{y}_n$,\n", "where\n", "\n", "$$\\mathbf{y}_i = f(\\mathbf{x}_i, (\\mathbf{x}_1, \\mathbf{x}_1), \\ldots, (\\mathbf{x}_n, \\mathbf{x}_n)) \\in \\mathbb{R}^d$$\n", "\n", "according to the definition of attention pooling in\n", ":eqref:`eq_attention_pooling`.\n", "Using multi-head attention,\n", "the following code snippet\n", "computes the self-attention of a tensor\n", "with shape (batch size, number of time steps or sequence length in tokens, $d$).\n", "The output tensor has the same shape.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "13743b61", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:35.935527Z", "iopub.status.busy": "2023-08-18T19:30:35.934433Z", "iopub.status.idle": "2023-08-18T19:30:35.974177Z", "shell.execute_reply": "2023-08-18T19:30:35.973091Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["num_hiddens, num_heads = 100, 5\n", "attention = d2l.MultiHeadAttention(num_hiddens, num_heads, 0.5)\n", "batch_size, num_queries, valid_lens = 2, 4, torch.tensor([3, 2])\n", "X = torch.ones((batch_size, num_queries, num_hiddens))\n", "d2l.check_shape(attention(X, X, X, valid_lens),\n", "                (batch_size, num_queries, num_hiddens))"]}, {"cell_type": "markdown", "id": "41ae9a49", "metadata": {"origin_pos": 14}, "source": ["## Comparing CNNs, RNNs, and Self-Attention\n", ":label:`subsec_cnn-rnn-self-attention`\n", "\n", "Let's\n", "compare architectures for mapping\n", "a sequence of $n$ tokens\n", "to another one of equal length,\n", "where each input or output token is represented by\n", "a $d$-dimensional vector.\n", "Specifically,\n", "we will consider CNNs, RNNs, and self-attention.\n", "We will compare their\n", "computational complexity, \n", "sequential operations,\n", "and maximum path lengths.\n", "Note that sequential operations prevent parallel computation,\n", "while a shorter path between\n", "any combination of sequence positions\n", "makes it easier to learn long-range dependencies \n", "within the sequence :cite:`Hochreiter.Bengio.Frasconi.ea.2001`.\n", "\n", "\n", "![Comparing CNN (padding tokens are omitted), RNN, and self-attention architectures.](../img/cnn-rnn-self-attention.svg)\n", ":label:`fig_cnn-rnn-self-attention`\n", "\n", "\n", "\n", "Let's regard any text sequence as a \"one-dimensional image\". Similarly, one-dimensional CNNs can process local features such as $n$-grams in text.\n", "Given a sequence of length $n$,\n", "consider a convolutional layer whose kernel size is $k$,\n", "and whose numbers of input and output channels are both $d$.\n", "The computational complexity of the convolutional layer is $\\mathcal{O}(knd^2)$.\n", "As :numref:`fig_cnn-rnn-self-attention` shows,\n", "CNNs are hierarchical,\n", "so there are $\\mathcal{O}(1)$ sequential operations\n", "and the maximum path length is $\\mathcal{O}(n/k)$.\n", "For example, $\\mathbf{x}_1$ and $\\mathbf{x}_5$\n", "are within the receptive field of a two-layer CNN\n", "with kernel size 3 in :numref:`fig_cnn-rnn-self-attention`.\n", "\n", "When updating the hidden state of RNNs,\n", "multiplication of the $d \\times d$ weight matrix\n", "and the $d$-dimensional hidden state has \n", "a computational complexity of $\\mathcal{O}(d^2)$.\n", "Since the sequence length is $n$,\n", "the computational complexity of the recurrent layer\n", "is $\\mathcal{O}(nd^2)$.\n", "According to :numref:`fig_cnn-rnn-self-attention`,\n", "there are $\\mathcal{O}(n)$ sequential operations\n", "that cannot be parallelized\n", "and the maximum path length is also $\\mathcal{O}(n)$.\n", "\n", "In self-attention,\n", "the queries, keys, and values \n", "are all $n \\times d$ matrices.\n", "Consider the scaled dot product attention in\n", ":eqref:`eq_softmax_QK_V`,\n", "where an $n \\times d$ matrix is multiplied by\n", "a $d \\times n$ matrix,\n", "then the output $n \\times n$ matrix is multiplied\n", "by an $n \\times d$ matrix.\n", "As a result,\n", "the self-attention\n", "has a $\\mathcal{O}(n^2d)$ computational complexity.\n", "As we can see from :numref:`fig_cnn-rnn-self-attention`,\n", "each token is directly connected\n", "to any other token via self-attention.\n", "Therefore,\n", "computation can be parallel with $\\mathcal{O}(1)$ sequential operations\n", "and the maximum path length is also $\\mathcal{O}(1)$.\n", "\n", "All in all,\n", "both CNNs and self-attention enjoy parallel computation\n", "and self-attention has the shortest maximum path length.\n", "However, the quadratic computational complexity with respect to the sequence length\n", "makes self-attention prohibitively slow for very long sequences.\n", "\n", "\n", "\n", "\n", "\n", "## [**Positional Encoding**]\n", ":label:`subsec_positional-encoding`\n", "\n", "\n", "Unlike RNNs, which recurrently process\n", "tokens of a sequence one-by-one,\n", "self-attention ditches\n", "sequential operations in favor of \n", "parallel computation.\n", "Note that self-attention by itself\n", "does not preserve the order of the sequence. \n", "What do we do if it really matters \n", "that the model knows in which order\n", "the input sequence arrived?\n", "\n", "The dominant approach for preserving \n", "information about the order of tokens\n", "is to represent this to the model \n", "as an additional input associated \n", "with each token. \n", "These inputs are called *positional encodings*,\n", "and they can either be learned or fixed *a priori*.\n", "We now describe a simple scheme for fixed positional encodings\n", "based on sine and cosine functions :cite:`Vaswani.Shazeer.Parmar.ea.2017`.\n", "\n", "Suppose that the input representation \n", "$\\mathbf{X} \\in \\mathbb{R}^{n \\times d}$ \n", "contains the $d$-dimensional embeddings \n", "for $n$ tokens of a sequence.\n", "The positional encoding outputs\n", "$\\mathbf{X} + \\mathbf{P}$\n", "using a positional embedding matrix \n", "$\\mathbf{P} \\in \\mathbb{R}^{n \\times d}$ of the same shape,\n", "whose element on the $i^\\textrm{th}$ row \n", "and the $(2j)^\\textrm{th}$\n", "or the $(2j + 1)^\\textrm{th}$ column is\n", "\n", "$$\\begin{aligned} p_{i, 2j} &= \\sin\\left(\\frac{i}{10000^{2j/d}}\\right),\\\\p_{i, 2j+1} &= \\cos\\left(\\frac{i}{10000^{2j/d}}\\right).\\end{aligned}$$\n", ":eqlabel:`eq_positional-encoding-def`\n", "\n", "At first glance,\n", "this trigonometric function\n", "design looks weird.\n", "Before we give explanations of this design,\n", "let's first implement it in the following `PositionalEncoding` class.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "3eb1b5ef", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:35.979909Z", "iopub.status.busy": "2023-08-18T19:30:35.978770Z", "iopub.status.idle": "2023-08-18T19:30:35.987465Z", "shell.execute_reply": "2023-08-18T19:30:35.986155Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["class PositionalEncoding(nn.Module):  #@save\n", "    \"\"\"Positional encoding.\"\"\"\n", "    def __init__(self, num_hiddens, dropout, max_len=1000):\n", "        super().__init__()\n", "        self.dropout = nn.Dropout(dropout)\n", "        # Create a long enough P\n", "        self.P = torch.zeros((1, max_len, num_hiddens))\n", "        X = torch.arange(max_len, dtype=torch.float32).reshape(\n", "            -1, 1) / torch.pow(10000, torch.arange(\n", "            0, num_hiddens, 2, dtype=torch.float32) / num_hiddens)\n", "        self.P[:, :, 0::2] = torch.sin(X)\n", "        self.P[:, :, 1::2] = torch.cos(X)\n", "\n", "    def forward(self, X):\n", "        X = X + self.P[:, :X.shape[1], :].to(X.device)\n", "        return self.dropout(X)"]}, {"cell_type": "markdown", "id": "17ad8db2", "metadata": {"origin_pos": 19}, "source": ["In the positional embedding matrix $\\mathbf{P}$,\n", "[**rows correspond to positions within a sequence\n", "and columns represent different positional encoding dimensions**].\n", "In the example below,\n", "we can see that\n", "the $6^{\\textrm{th}}$ and the $7^{\\textrm{th}}$\n", "columns of the positional embedding matrix \n", "have a higher frequency than \n", "the $8^{\\textrm{th}}$ and the $9^{\\textrm{th}}$\n", "columns.\n", "The offset between \n", "the $6^{\\textrm{th}}$ and the $7^{\\textrm{th}}$ (same for the $8^{\\textrm{th}}$ and the $9^{\\textrm{th}}$) columns\n", "is due to the alternation of sine and cosine functions.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "51320f4e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:35.991251Z", "iopub.status.busy": "2023-08-18T19:30:35.990632Z", "iopub.status.idle": "2023-08-18T19:30:36.368109Z", "shell.execute_reply": "2023-08-18T19:30:36.366973Z"}, "origin_pos": 21, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"380.482812pt\" height=\"183.35625pt\" viewBox=\"0 0 380.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:30:36.288792</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 183.35625 \n", "L 380.**********.35625 \n", "L 380.482812 0 \n", "L -0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 38.**********.8 \n", "L 373.**********.8 \n", "L 373.282813 7.2 \n", "L 38.482813 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 53.700994 145.8 \n", "L 53.700994 7.2 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m0188bf0a8d\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m0188bf0a8d\" x=\"53.700994\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(50.519744 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 105.288051 145.8 \n", "L 105.288051 7.2 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m0188bf0a8d\" x=\"105.288051\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(98.925551 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 156.875108 145.8 \n", "L 156.875108 7.2 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m0188bf0a8d\" x=\"156.875108\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(150.512608 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 208.462165 145.8 \n", "L 208.462165 7.2 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m0188bf0a8d\" x=\"208.462165\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(202.099665 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 260.049222 145.8 \n", "L 260.049222 7.2 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m0188bf0a8d\" x=\"260.049222\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(253.686722 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 311.636279 145.8 \n", "L 311.636279 7.2 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m0188bf0a8d\" x=\"311.636279\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(305.273779 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 363.223336 145.8 \n", "L 363.223336 7.2 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m0188bf0a8d\" x=\"363.223336\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(356.860836 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- Row (position) -->\n", "     <g transform=\"translate(170.189844 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-52\" d=\"M 2841 2188 \n", "Q 3044 2119 3236 1894 \n", "Q 3428 1669 3622 1275 \n", "L 4263 0 \n", "L 3584 0 \n", "L 2988 1197 \n", "Q 2756 1666 2539 1819 \n", "Q 2322 1972 1947 1972 \n", "L 1259 1972 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "L 2053 4666 \n", "Q 2853 4666 3247 4331 \n", "Q 3641 3997 3641 3322 \n", "Q 3641 2881 3436 2590 \n", "Q 3231 2300 2841 2188 \n", "z\n", "M 1259 4147 \n", "L 1259 2491 \n", "L 2053 2491 \n", "Q 2509 2491 2742 2702 \n", "Q 2975 2913 2975 3322 \n", "Q 2975 3731 2742 3939 \n", "Q 2509 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-52\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"64.982422\"/>\n", "      <use xlink:href=\"#DejaVuSans-77\" x=\"126.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"207.951172\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"239.738281\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"278.751953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"342.228516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"403.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"455.509766\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"483.292969\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"522.501953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"550.285156\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"611.466797\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"674.845703\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 38.482813 139.5 \n", "L 373.282813 139.5 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <defs>\n", "       <path id=\"md84152c1e4\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md84152c1e4\" x=\"38.482813\" y=\"139.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(7.2 143.299219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 38.482813 108 \n", "L 373.282813 108 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#md84152c1e4\" x=\"38.482813\" y=\"108\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(7.2 111.799219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 38.482813 76.5 \n", "L 373.282813 76.5 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#md84152c1e4\" x=\"38.482813\" y=\"76.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(15.579688 80.299219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 38.482813 45 \n", "L 373.282813 45 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#md84152c1e4\" x=\"38.482813\" y=\"45\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(15.579688 48.799219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 38.482813 13.5 \n", "L 373.282813 13.5 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#md84152c1e4\" x=\"38.482813\" y=\"13.5\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(15.579688 17.299219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 53.700994 76.5 \n", "L 58.8597 65.355792 \n", "L 64.018406 54.563068 \n", "L 69.177111 44.462222 \n", "L 74.335817 35.371837 \n", "L 79.494523 27.578612 \n", "L 84.653229 21.328343 \n", "L 89.811934 16.818165 \n", "L 94.97064 14.190325 \n", "L 100.129346 13.527701 \n", "L 105.288051 14.851191 \n", "L 110.446757 18.119061 \n", "L 115.605463 23.22824 \n", "L 120.764168 30.017578 \n", "L 125.922874 38.272962 \n", "L 131.08158 47.733995 \n", "L 136.240286 58.102294 \n", "L 141.398991 69.050862 \n", "L 146.557697 80.234358 \n", "L 151.716403 91.300088 \n", "L 156.875108 101.899017 \n", "L 162.033814 111.696885 \n", "L 167.19252 120.384648 \n", "L 172.351225 127.688311 \n", "L 177.509931 133.377536 \n", "L 182.668637 137.272851 \n", "L 187.827343 139.251424 \n", "L 192.986048 139.250846 \n", "L 198.144754 137.271135 \n", "L 203.30346 133.37473 \n", "L 208.462165 127.684526 \n", "L 213.620871 120.379992 \n", "L 218.779577 111.691504 \n", "L 223.938282 101.893063 \n", "L 229.096988 91.293765 \n", "L 234.255694 80.227879 \n", "L 239.4144 69.044417 \n", "L 244.573105 58.096071 \n", "L 249.731811 47.728208 \n", "L 254.890517 38.267791 \n", "L 260.049222 30.0132 \n", "L 265.207928 23.224759 \n", "L 270.366634 18.116616 \n", "L 275.525339 14.849854 \n", "L 280.684045 13.52751 \n", "L 285.842751 14.191283 \n", "L 291.001457 16.820238 \n", "L 296.160162 21.331467 \n", "L 301.318868 27.582724 \n", "L 306.477574 35.376772 \n", "L 311.636279 44.467828 \n", "L 316.794985 54.569164 \n", "L 321.953691 65.362187 \n", "L 327.112396 76.50649 \n", "L 332.271102 87.650589 \n", "L 337.429808 98.443004 \n", "L 342.588514 108.543399 \n", "L 347.747219 117.633104 \n", "L 352.905925 125.425496 \n", "L 358.064631 131.6748 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 53.700994 13.5 \n", "L 58.8597 14.493496 \n", "L 64.018406 17.442644 \n", "L 69.177111 22.254439 \n", "L 74.335817 28.77711 \n", "L 79.494523 36.804942 \n", "L 84.653229 46.084737 \n", "L 89.811934 56.323816 \n", "L 94.97064 67.199234 \n", "L 100.129346 78.368 \n", "L 105.288051 89.477851 \n", "L 110.446757 100.178386 \n", "L 115.605463 110.132123 \n", "L 120.764168 119.025106 \n", "L 125.922874 126.576875 \n", "L 131.08158 132.549237 \n", "L 136.240286 136.753833 \n", "L 141.398991 139.058056 \n", "L 146.557697 139.389225 \n", "L 151.716403 137.736898 \n", "L 156.875108 134.153187 \n", "L 162.033814 128.751119 \n", "L 167.19252 121.701079 \n", "L 172.351225 113.225425 \n", "L 177.509931 103.591439 \n", "L 182.668637 93.103029 \n", "L 187.827343 82.090967 \n", "L 192.986048 70.902569 \n", "L 198.144754 59.890681 \n", "L 203.30346 49.402673 \n", "L 208.462165 39.769302 \n", "L 213.620871 31.2944 \n", "L 218.779577 24.245258 \n", "L 223.938282 18.844191 \n", "L 229.096988 15.261578 \n", "L 234.255694 13.610392 \n", "L 239.4144 13.94271 \n", "L 244.573105 16.248067 \n", "L 249.731811 20.453733 \n", "L 254.890517 26.427072 \n", "L 260.049222 33.979686 \n", "L 265.207928 42.87339 \n", "L 270.366634 52.827643 \n", "L 275.525339 63.528508 \n", "L 280.684045 74.638487 \n", "L 285.842751 85.807178 \n", "L 291.001457 96.682325 \n", "L 296.160162 106.920933 \n", "L 301.318868 116.200123 \n", "L 306.477574 124.227145 \n", "L 311.636279 130.748869 \n", "L 316.794985 135.55962 \n", "L 321.953691 138.507653 \n", "L 327.112396 139.5 \n", "L 332.271102 138.505359 \n", "L 337.429808 135.555099 \n", "L 342.588514 130.742238 \n", "L 347.747219 124.218628 \n", "L 352.905925 116.19 \n", "L 358.064631 106.909564 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 53.700994 76.5 \n", "L 58.8597 70.210494 \n", "L 64.018406 63.983832 \n", "L 69.177111 57.882226 \n", "L 74.335817 51.966643 \n", "L 79.494523 46.29619 \n", "L 84.653229 40.927523 \n", "L 89.811934 35.914287 \n", "L 94.97064 31.306567 \n", "L 100.129346 27.150408 \n", "L 105.288051 23.48733 \n", "L 110.446757 20.353934 \n", "L 115.605463 17.781538 \n", "L 120.764168 15.795834 \n", "L 125.922874 14.416667 \n", "L 131.08158 13.657815 \n", "L 136.240286 13.526864 \n", "L 141.398991 14.025116 \n", "L 146.557697 15.147599 \n", "L 151.716403 16.883094 \n", "L 156.875108 19.214263 \n", "L 162.033814 22.117806 \n", "L 167.19252 25.56473 \n", "L 172.351225 29.520569 \n", "L 177.509931 33.945826 \n", "L 182.668637 38.796257 \n", "L 187.827343 44.023409 \n", "L 192.986048 49.57507 \n", "L 198.144754 55.395743 \n", "L 203.30346 61.427298 \n", "L 208.462165 67.60944 \n", "L 213.620871 73.880412 \n", "L 218.779577 80.177574 \n", "L 223.938282 86.437976 \n", "L 229.096988 92.599096 \n", "L 234.255694 98.599343 \n", "L 239.4144 104.378782 \n", "L 244.573105 109.879679 \n", "L 249.731811 115.047043 \n", "L 254.890517 119.829274 \n", "L 260.049222 124.178557 \n", "L 265.207928 128.051455 \n", "L 270.366634 131.409266 \n", "L 275.525339 134.218458 \n", "L 280.684045 136.450932 \n", "L 285.842751 138.084398 \n", "L 291.001457 139.102531 \n", "L 296.160162 139.495163 \n", "L 301.318868 139.258367 \n", "L 306.477574 138.394512 \n", "L 311.636279 136.91223 \n", "L 316.794985 134.826329 \n", "L 321.953691 132.157649 \n", "L 327.112396 128.932843 \n", "L 332.271102 125.18416 \n", "L 337.429808 120.949039 \n", "L 342.588514 116.269803 \n", "L 347.747219 111.193199 \n", "L 352.905925 105.769927 \n", "L 358.064631 100.054224 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 53.700994 13.5 \n", "L 58.8597 13.814737 \n", "L 64.018406 14.755804 \n", "L 69.177111 16.3138 \n", "L 74.335817 18.473158 \n", "L 79.494523 21.212299 \n", "L 84.653229 24.503856 \n", "L 89.811934 28.314941 \n", "L 94.97064 32.607477 \n", "L 100.129346 37.338571 \n", "L 105.288051 42.460953 \n", "L 110.446757 47.923445 \n", "L 115.605463 53.671465 \n", "L 120.764168 59.647571 \n", "L 125.922874 65.792069 \n", "L 131.08158 72.043557 \n", "L 136.240286 78.339571 \n", "L 141.398991 84.617206 \n", "L 146.557697 90.813729 \n", "L 151.716403 96.867241 \n", "L 156.875108 102.717251 \n", "L 162.033814 108.3053 \n", "L 167.19252 113.575573 \n", "L 172.351225 118.475387 \n", "L 177.509931 122.955809 \n", "L 182.668637 126.972046 \n", "L 187.827343 130.483989 \n", "L 192.986048 133.456546 \n", "L 198.144754 135.860008 \n", "L 203.30346 137.670365 \n", "L 208.462165 138.869527 \n", "L 213.620871 139.445514 \n", "L 218.779577 139.392571 \n", "L 223.938282 138.711228 \n", "L 229.096988 137.408286 \n", "L 234.255694 135.496771 \n", "L 239.4144 132.995782 \n", "L 244.573105 129.9303 \n", "L 249.731811 126.330969 \n", "L 254.890517 122.233731 \n", "L 260.049222 117.679547 \n", "L 265.207928 112.713914 \n", "L 270.366634 107.386442 \n", "L 275.525339 101.750337 \n", "L 280.684045 95.861965 \n", "L 285.842751 89.780136 \n", "L 291.001457 83.565615 \n", "L 296.160162 77.280498 \n", "L 301.318868 70.987552 \n", "L 306.477574 64.749714 \n", "L 311.636279 58.629281 \n", "L 316.794985 52.687407 \n", "L 321.953691 46.983461 \n", "L 327.112396 41.574405 \n", "L 332.271102 36.514343 \n", "L 337.429808 31.853804 \n", "L 342.588514 27.639354 \n", "L 347.747219 23.913101 \n", "L 352.905925 20.712264 \n", "L 358.064631 18.068855 \n", "\" clip-path=\"url(#pd4450e5e9a)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 38.**********.8 \n", "L 38.482813 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 373.**********.8 \n", "L 373.282813 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 38.**********.8 \n", "L 373.282812 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 38.482813 7.2 \n", "L 373.282812 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 45.482813 140.8 \n", "L 102.903125 140.8 \n", "Q 104.903125 140.8 104.903125 138.8 \n", "L 104.903125 81.0875 \n", "Q 104.903125 79.0875 102.903125 79.0875 \n", "L 45.482813 79.0875 \n", "Q 43.482813 79.0875 43.482813 81.0875 \n", "L 43.482813 138.8 \n", "Q 43.482813 140.8 45.482813 140.8 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_29\">\n", "     <path d=\"M 47.482813 87.185938 \n", "L 57.482813 87.185938 \n", "L 67.482812 87.185938 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- Col 6 -->\n", "     <g transform=\"translate(75.482812 90.685938) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"190.576172\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 47.482813 101.864063 \n", "L 57.482813 101.864063 \n", "L 67.482812 101.864063 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- Col 7 -->\n", "     <g transform=\"translate(75.482812 105.364063) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-37\" x=\"190.576172\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_31\">\n", "     <path d=\"M 47.482813 116.542188 \n", "L 57.482813 116.542188 \n", "L 67.482812 116.542188 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Col 8 -->\n", "     <g transform=\"translate(75.482812 120.042188) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-38\" x=\"190.576172\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_32\">\n", "     <path d=\"M 47.482813 131.220313 \n", "L 57.482813 131.220313 \n", "L 67.482812 131.220313 \n", "\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Col 9 -->\n", "     <g transform=\"translate(75.482812 134.720313) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-39\" x=\"190.576172\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pd4450e5e9a\">\n", "   <rect x=\"38.482813\" y=\"7.2\" width=\"334.8\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["encoding_dim, num_steps = 32, 60\n", "pos_encoding = PositionalEncoding(encoding_dim, 0)\n", "X = pos_encoding(torch.zeros((1, num_steps, encoding_dim)))\n", "P = pos_encoding.P[:, :X.shape[1], :]\n", "d2l.plot(torch.arange(num_steps), P[0, :, 6:10].T, xlabel='Row (position)',\n", "         figsize=(6, 2.5), legend=[\"Col %d\" % d for d in torch.arange(6, 10)])"]}, {"cell_type": "markdown", "id": "811eb6d1", "metadata": {"origin_pos": 24}, "source": ["### Absolute Positional Information\n", "\n", "To see how the monotonically decreased frequency\n", "along the encoding dimension relates to absolute positional information,\n", "let's print out [**the binary representations**] of $0, 1, \\ldots, 7$.\n", "As we can see, the lowest bit, the second-lowest bit, \n", "and the third-lowest bit alternate on every number, \n", "every two numbers, and every four numbers, respectively.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "6f42d89b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:36.373921Z", "iopub.status.busy": "2023-08-18T19:30:36.373258Z", "iopub.status.idle": "2023-08-18T19:30:36.380089Z", "shell.execute_reply": "2023-08-18T19:30:36.378862Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 in binary is 000\n", "1 in binary is 001\n", "2 in binary is 010\n", "3 in binary is 011\n", "4 in binary is 100\n", "5 in binary is 101\n", "6 in binary is 110\n", "7 in binary is 111\n"]}], "source": ["for i in range(8):\n", "    print(f'{i} in binary is {i:>03b}')"]}, {"cell_type": "markdown", "id": "b617c79b", "metadata": {"origin_pos": 26}, "source": ["In binary representations, a higher bit \n", "has a lower frequency than a lower bit.\n", "Similarly, as demonstrated in the heat map below,\n", "[**the positional encoding decreases\n", "frequencies along the encoding dimension**]\n", "by using trigonometric functions.\n", "Since the outputs are float numbers,\n", "such continuous representations\n", "are more space-efficient\n", "than binary representations.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "c5f60f9f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:30:36.384358Z", "iopub.status.busy": "2023-08-18T19:30:36.383531Z", "iopub.status.idle": "2023-08-18T19:30:36.858217Z", "shell.execute_reply": "2023-08-18T19:30:36.857049Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"213.775737pt\" height=\"268.467469pt\" viewBox=\"0 0 213.**********.467469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:30:36.784791</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 268.467469 \n", "L 213.**********.467469 \n", "L 213.775737 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 40.**********.911219 \n", "L 158.**********.911219 \n", "L 158.875125 9.151219 \n", "L 40.603125 9.151219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p3ad5fb84a1)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAKUAAAE0CAYAAABNZvOIAAAUX0lEQVR4nO2de5DW1X3Gz+7+9n7fZbkJyLIiYFhZEQVEcMFoQqo20JmKsSkDaLikljgTZdqESVukrWnMCDOCKYhSI87QKWDA6ZC2XIQICKJWFLnKgiKwu7D397IX+kf/43lec15e1v3u7vP588nJ+b2TefYMT77f8z1JxbPfvOqu4f0Xvn+t5EY+uAS0X674CWixeHbxi6Cd2/0CaJ+dbwTtciQC2qPzV9Dv/NM/Pwna/ImloBVN+ilor7z8NGjzfvpvoA2oqAAtEsbf6Jxzl0+eAq3i/rtA+3DTNtC+Pe/PQfvv3/wWtBk/mUO/vfnXa0F7bMl80N58/jeg/fBvF4D2+j++DNrsny2k316/fDVoc36+CLRXn1sFWjLdUYguRKYU5pAphTlkSmGOoPmjvSAu2TYCV6ZlgvTazjN005WzKlDMKwGpJdIGWn5WKmjtVyGLOZeaQb/dFO0ALSkpCReSPVvb8b/ryH+3o8PzG845d5XsKb4WnZTCHDKlMIdMKcwhUwpzBKXTHwHxrbWbQcsfPQ60T/d9TDftP38CaOkDh4LWGMagk5MRgBZpbcePpGfTbzdHyVoGCSDRdhKokvDv9moHrosZdAi+a5M9t4zj090CnZTCHDKlMIdMKcwhUwpzBK/OuxvEygPvg/adqbeCtvFfdtFNi3LSQBtU2g+0xhAGnZK8dNCawin4kRhBpzHiWUEhFZ1IAhWd5OQYf9/kOz0tmNxodFIKc8iUwhwypTCHTCnMIVMKcwRjbi4AcfZCLD0+Vj4AtI2kR9I55zLTMC2X39oHtPPNIdBGBbmgZaTi306QlUW/HYpioqewMmObX5mRpu+UWOk7jt5LH76h/swk13X/F4FOSmEOmVKYQ6YU5pAphTmCI+fqQfzFA8NBSyHNfQPuxEkPzjnXQfoNJ5UVgHa2HoNOKgkMaQFq6ZlYjnTOuZBvPyWB9lMmY2hLtJ+ScqMDUQy6Q4lTJ6Uwh0wpzCFTCnPIlMIcwbzXDoG4e0klaBmkSjNz6jC6aTOZfDG2bwFoG45cAC2ZBCoWdDKy+YSMEPn2VTZhg/VTtnXGhIxOCEU9HJ2UwhwypTCHTCnMIVMKcwTHt24Bce00nBH+gzGDQHusfCDdtKYxClr/Agwm52qaQGPVoDRS5cmKFXRIRYdsSemUCRm0UuP3e3zjUHIXtpl1BjophTlkSmEOmVKYQ6YU5gjSbxsP4tKVO0G77e8fBu2eYcV00/2fXwZtfGkRaOcvNYPG5o6npOA/5LOzcQqHc86FyR0dWtEh0IoOa13T1ItORSelMIdMKcwhUwpzyJTCHMGKxVNAXLDoRdDW7B8D2tQRfBjBgS/rQKska6ursaLDwkYWaZvLzeVBJ0Jb1+hSwLeio9a1zkUnpTCHTCnMIVMKc8iUwhzBn92OLWlrHp0B2q7dx0C78P3RdNN9J7Gi01GJ/+BvuNwAWnMEW89y0vHBp/wcPozgi68aQaOv4BKirKKT4juMwOsTsdd200CU3Am/USelMIdMKcwhUwpzyJTCHEFtE96neXnWHaDdtXELaH+ouo9ueuxEDWhNpNISuoIT35rIy7YDyP2egixe0TlNWtfYvR8Ga5tLfBhB54+D7gZ5KC50UgpzyJTCHDKlMIdMKcwhUwpzBEu2HQXxtccxfbMy2No9VXTTC1U44o9NzXCNmNLrW1rpntdSSJ5vds65CClT+k7IaKUXx8iEDPK/BRth+P+Lr3+Weawtezo6KYU5ZEphDplSmEOmFOYI3lq7GcS9dw8GLX3EWNAO7PmM73rxNEhnLuM0DBfCfsqvWvDBJ+cKQSnJTqWfbiVlxvYEyowslHRKP+U3RLfo0ezqHyDEtciUwhwypTCHTCnMEbj0bBDnrd4HWmXlSNC2r1rPdyUVj/1kaoZrDYN0+goGHdYPWZyNl8mcc641ihWhNhZ0yD/4WUUnmVR0Ep2QIb4enZTCHDKlMIdMKcwhUwpzBLMXPATi+uWrQZv35FLQtq/L4btGMawcIFMzWAg4XYPhh1VkijJjVXQw6NCKDhnx51vRcX65KSZ8z+tvcetp6KQU5pAphTlkSmEOmVKYI/jFA8NB3LrjftDGDsb2sYLycXTTupPHQWNTM9jDSWfJy7YsqOSn8aDT1oqta21s8gWBjQJMIhdl2tvwHlBnvGLrS3cJRL4/UyelMIdMKcwhUwpzyJTCHEFmKoaNtT+eBFoxufw/feqtdNMtoQhoF6u+woXpWSBd8HzZNi+OoMMrOqR1jXyHta61XcVvxBM2fOeEd5P8csPRSSnMIVMKc8iUwhwypTBHsPYgTk778T2loDWGsCVs3p34MJRzzh3+7BJox/5rBy7MKgCpllR02OuyLKA5F6ui49e61samrtEusziGEXTTOzpdGbJ0UgpzyJTCHDKlMIdMKcwhUwpzBEtX7ATxT4b3A429TDZyYC7d9PbhfUA79ha+Lub6DwOJPbfM+hzTU2P8PUXxd/pOyGDpm5UZWY9kzJnnhF5aPfRGJ6Uwh0wpzCFTCnPIlMIcgTv3CYgLN34I2rD+eaCtGlZON51cVgDav6fg6L68Qtyz4St8GIoHHV5mZOMF6cUxcmnNO+h0JHpxzPdxJ1bjJA9LxZGcusODUTophTlkSmEOmVKYQ6YU5gjGzZoJ4oENm0A7dsc9oNV+dwTd9PaSfBTzsUpUUIwVoYYTn4IWacVgUZjNX7F1bVjRafXsp2xt9eunZM/iJtx/2E37LjsDnZTCHDKlMIdMKcwhUwpzBC/PqgBx3B4MG3UfHwLt8Ll76ablAzDoZA0aClrfvjgz/Sx52TYUxaDTLz/G31M7XnBjky9YRafdc0KG64hjQgYJML6hSBMyhDCCTCnMIVMKc8iUwhzBsL74iu3Sp6aBtuyZFaCt3X+Wbrr+L8aCNmQYVnRu6oPfPtSKYwSbyNSLICVGCiAVnXA7BqXE7ugkVtHxHQXYW9FJKcwhUwpzyJTCHDKlMEew9yQ+urRgwlDQlpVWgLZ71zG6acacu0BjAwoG5Kf/8V/onKuPYJUmiHXZhFR0wuQxJt87Ouxxp7iGEZC1N5ru8riTLzophTlkSmEOmVKYQ6YU5gieWL0fxPee+y5o4yePBO3AG/9BN23veBy0yWXk3g6DDC2oj2J4SYkVLMiggBANOvgd/9Y13C+uqWverWs9K8D4opNSmEOmFOaQKYU5ZEphjuDSnu0gLt9xC2hPTL4ZtAMb+D/Em8LYalbeB4PO5TC2mbl0bGerbcH9Eg86iVR0EnvcqbcGGF90UgpzyJTCHDKlMIdMKcwhUwpzBMX3fBvENaveBu3pDX8FWsYIvCDmnHPVDXj5q39BBmgpDSSFZheCVNOEZcaYCZak7zB7Mpmk7w7fMmMCc8yd85873ltDuk5KYQ6ZUphDphTmkCmFOYI1i3CW+cwfHQStP7nkNbUSeyydc+70lSbQphSXgBYhM8ZTCvCCWU0zBp2YkKDTGPEtM5JJHAH2XfLHnfx+Xky+gQtmzjmX1A3e0NVJKcwhUwpzyJTCHDKlMEdQeSsGkOlz8cEnFkqeGD+YbvpO1RXQHhzVH7TsdAwbuYX44FNdE1aIrsZ6DInozdHrr+gkpXm+JMsqP84lXP3xoadVfnRSCnPIlMIcMqUwh0wpzBF8Xt0C4q8euQ20U5eaQasYVEA3/dWOk6C1kRCRmYZho5C8bFvXghfMyEOyMWkmj0OxSRyJta75/x7fpcndoPrSGeikFOaQKYU5ZEphDplSmCNYtPFDELctnAjaS/uqQFv+Pd66duJ4NWhsakZOBoaNfv3wZdumJgw67XEknSZa0SGjAMkkDTriL56pF7EqTyImOimFOWRKYQ6ZUphDphTmCA5s2ATi76bg2L9NO0+BtmRqGd20puo8aNWNGFYKstNAG1SMowAPH70IGqsQxSIUxZDlyN2bdvLaLQ86CVZ0PBd3ZUtaV760q5NSmEOmFOaQKYU5ZEphjsANHAHiUyv2gNZy5jhoR89j5cc551wNVn9OX8YBBcP7Y/WmtCQTtD2HMCS1tvtXSkKkdS0pxXfqWoKv2BLiWOpFT5uhrpNSmEOmFOaQKYU5ZEphDplSmCNY+tQ0EJc9swJXkr7AVw6d47uGMWn/4Ww9aPeP6AtaWRHORg83h0FrjVVmJEm0JUJeLEsofcfTT9n5EzJ6GjophTlkSmEOmVKYQ6YU5ggWTBgK4hvTHwbt9Aefgfb7XSdi7Ip9kgdP1YIWJf2YN+VkgRZuwaATZQ82OUdH/IVI0GFPJrOLYykpN35Chi/dIRB1xk/USSnMIVMKc8iUwhwypTBHwPoSX503HrRFG1JB++Tt7XzXApxvfuL4JdBCZI56cRaGpEgIZ56zGezOOeeS8O8sTPopk0mAaY3gI1K+FZ14Llp1ZX7pBtlJJ6Wwh0wpzCFTCnPIlMIcwXIyn/yXD40CbXblUNCe3Ryim+YMxgkbtVVfgtbsOR7QhRpBCreSOebO0VnmUTIhg7WuuXbfoENa3OJIEHQtDU/eW/YodFIKc8iUwhwypTCHTCnMEax5aSuIfzlmIGgPlOF9mqTSMXTToWX9QDty9APQGkIYLG4qwgkZLoIPUIVjVXRI61o4TFrSAhZ0Epl5zn/ONzHzvKcFIp2UwhwypTCHTCnMIVMKcwQu1ADinHUHQdv3cxxaMPE+rPw451xpP3yJ9ggZUFDdgi1pt5DHnVwUg05jFEOSc865FGyxi5A7Oqx1zbuiQ+gurWvdAZ2UwhwypTCHTCnMIVMKcwTT58wA8T9feg20qz/DoPPkvUPoplEyvewNMqDgyyZsfUsNyN9JKwai+jiCTpTc0aGtax1sXVc+7tQ7E5FOSmEOmVKYQ6YU5pAphTmCF/70WyDu3DUBtLO1WFWZOKSYblrdgMHEFWI73MkanKaWwioopNJSF44RdEigamV3dFjrGgk6vq1r9HfHoJfmF290UgpzyJTCHDKlMIdMKcwhUwpzBCW5mFZXLp4C2tZjF0FbPHkY3ZSVx3IHY0nyTA0megpJxdXN8aRvXJuWgev803ccDzaRtb74pvQk17PivE5KYQ6ZUphDphTmkCmFOYItR86DOKP8JtAmLPsf0OaTh6Gcc64gC3sah5IJG1/WNIPWxl6nJWW96mYsHTrnvINOZja+lsv7Kf3+buOJGsk9LJjcaHRSCnPIlMIcMqUwh0wpzBH89Yp3QLz31zNBO/XeR6hdvJtuOnpwPmh3DC8Bbdehc6DFfLTpGi43Rfl/kIYBhgWdgF1QS6CfMp5LXl3ZTxnPJI+uQielMIdMKcwhUwpzyJTCHEHoyD4Qn9k6EldWnwHpt/+L1SDnnHtuII4CnDwMw8+W7Z+C1kKmWbBkUN/Cg06Qio87tZOHoBILOhjG4rk45osmZAhhBJlSmEOmFOaQKYU5grLvPQLitlc24coibGfbTF7Adc65Z+8rA21UcR5oDVdw3nozmU/OxvvVNZEpHM65lFScfBEhs9VpSxprXfMMMJ0xCtB/vxu6Xaft6YtOSmEOmVKYQ6YU5pAphTmCdXOx/ey+/YdBu3lcBWhV775LN61uqAStb146LqyvAakhRIIOuXfT2MgrOqlp5HGn+kbcklV0SKWGtnqR1rWYLWF0LV96vet6GjophTlkSmEOmVKYQ6YU5ghuH4ItZXMXPQxaSTYGiOe3b6WbnriMweLBvv1xYUsdSBebcQ66S8WQ1BSjosNa11wr7unbuhawx50IXVnR6WnopBTmkCmFOWRKYQ6ZUphDphTmCD4+Ww/i0vtvAe0SeUXs+YEj6KZ7q3DPh0bji2MugjPPzzXic8suPRuklmaevlmZ0bVhSTI1lZUZr/8lMXrBTFwXOimFOWRKYQ6ZUphDphTmCOa+ehDEd/5mKmhDSzBslE8qp5u+f7IWNDrLnJT1TpDnllnQCbeQdc65vEKczsGeZg7Y08re/ZRkXRxzzHvro02+6KQU5pAphTlkSmEOmVKYIzj59lsg/uu0UtCemoQv1s6dOpRu+g/r8eJZPbsQRv7Ff448+JSS5R90SvoXokgqOimsTzKBik5X9lN2hznm8aCTUphDphTmkCmFOWRKYY4g41sTQPy7lTtAm3nbANCmleLLtM459/TZL0CrJq1vLhUfYjpPgk56Jl4cayFTL5xzLi2NVGpIRSeVjQIklZqArrv+QBQPPW3Eny86KYU5ZEphDplSmEOmFOYIVi6eAuKPFr0I2q7Pq0H7wdghfNdafJ2WTc1wmTgH/dIlDDoZWRiIWi7wh6UyMsiEDNIil0pHAZKxfd4VHf8Eoes8X49OSmEOmVKYQ6YU5pAphTmCGeX4aNOaWTNAW7f7DGizKgbzXaM4UGDPGRxQ4HL7gFRXi+sKinFcIRtk4Jxz6emeQcezopPiGWDial3rpXdvfNFJKcwhUwpzyJTCHDKlMEdQTR5JWv1oBWjjFrwO2pnH7+S7khnlh0/iQ06ZhRhgmuuw8jPo5hL8Bplj7pxzGax1zbeiQ+iMio7vUt+7N92hHS0edFIKc8iUwhwypTCHTCnMETzzu09AfP2HY3HlF7huy9ELfNeiQSCdOnEJtLwibF0LfXEGtNzcUfiNGEEn07ei49m65vu4k2/lR/xxdFIKc8iUwhwypTCHTCnMIVMKcwRvr9sM4p4J5EJYThFIb+48TTfNH4J9lnVn8TLZ6HsrQLsYagAtLzsNP9JORgs65zJZmZGk6jTWT0nojFGAvbV86ItOSmEOmVKYQ6YU5pAphTkCNqVi3qp3QSupGAfa5+99QDcd+52JoB3+6ABoA/tOAu0IuXRWkEOCDikdOudcZhopMxK8+yk900Y8c8d7a4DxRSelMIdMKcwhUwpzyJTCHMHchQ+BuG7ZKtDmLl2E6/b+nm5aUfYwaIdJgBlUjI82sQBTkEWCTgxoRYdAJ2QQOqOiI74enZTCHDKlMIdMKcwhUwpz/B+jRbc8POeIMQAAAABJRU5ErkJggg==\" id=\"imagee8bbd59c91\" transform=\"scale(1 -1) translate(0 -221.76)\" x=\"40.603125\" y=\"-9.151219\" width=\"118.8\" height=\"221.76\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m235d391949\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m235d391949\" x=\"42.451125\" y=\"230.911219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(39.**********.509656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m235d391949\" x=\"116.371125\" y=\"230.911219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(110.008625 245.509656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- Column (encoding dimension) -->\n", "     <g transform=\"translate(24.650844 259.187781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"222.167969\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"319.580078\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"382.958984\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"414.746094\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"453.759766\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"515.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"578.662109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"633.642578\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"694.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"758.300781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"786.083984\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"849.462891\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"912.939453\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"944.726562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"1008.203125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"1035.986328\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"1133.398438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"1194.921875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1258.300781\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"1310.400391\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"1338.183594\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"1399.365234\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"1462.744141\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"m052667d0ba\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m052667d0ba\" x=\"40.603125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(27.240625 14.798437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m052667d0ba\" x=\"40.603125\" y=\"47.959219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(20.878125 51.758437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m052667d0ba\" x=\"40.603125\" y=\"84.919219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(20.878125 88.718437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m052667d0ba\" x=\"40.603125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(20.878125 125.678437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m052667d0ba\" x=\"40.603125\" y=\"158.839219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(20.878125 162.638437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m052667d0ba\" x=\"40.603125\" y=\"195.799219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(20.878125 199.598437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- Row (position) -->\n", "     <g transform=\"translate(14.798437 155.724187) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-52\" d=\"M 2841 2188 \n", "Q 3044 2119 3236 1894 \n", "Q 3428 1669 3622 1275 \n", "L 4263 0 \n", "L 3584 0 \n", "L 2988 1197 \n", "Q 2756 1666 2539 1819 \n", "Q 2322 1972 1947 1972 \n", "L 1259 1972 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "L 2053 4666 \n", "Q 2853 4666 3247 4331 \n", "Q 3641 3997 3641 3322 \n", "Q 3641 2881 3436 2590 \n", "Q 3231 2300 2841 2188 \n", "z\n", "M 1259 4147 \n", "L 1259 2491 \n", "L 2053 2491 \n", "Q 2509 2491 2742 2702 \n", "Q 2975 2913 2975 3322 \n", "Q 2975 3731 2742 3939 \n", "Q 2509 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-52\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"64.982422\"/>\n", "      <use xlink:href=\"#DejaVuSans-77\" x=\"126.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"207.951172\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"239.738281\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"278.751953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"342.228516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"403.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"455.509766\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"483.292969\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"522.501953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"550.285156\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"611.466797\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"674.845703\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 40.**********.911219 \n", "L 40.603125 9.151219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 158.**********.911219 \n", "L 158.875125 9.151219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 40.**********.911219 \n", "L 158.**********.911219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 40.603125 9.151219 \n", "L 158.875125 9.151219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 168.640125 186.559219 \n", "L 175.292925 186.559219 \n", "L 175.292925 53.503219 \n", "L 168.640125 53.503219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAAkAAAC5CAYAAAD6WxVlAAAA+ElEQVR4nO2YMQ4DMQgEHcn/f26KKOA8gSlGyJxyNWLwrgHrXu/POav49lllzNogZu0kQTfiapVwEMGhwqEE5HR1onYcVNzCocKRBM24sS317A4WcVNbqhkXKJNmsIdjmbRLN1YnNDOzTgQvnYdLMMXEtWHqBIKCnA7VFKConVojfJEtogQgEwryWsqTgBnsPSF7bWGZ6kRw9CAcs0VrTraDWeGtBjPFvaXYazDL5D0ephrMdrBmMMSNLfw+XGY9f80OBrtMHNFeSyGd2I8OZjCyhZxO1KkVd6Pif4NNHFMcBZEfZxkajgQxXHPhvbiYWngzzrsqAPcDIkusk8oTmTgAAAAASUVORK5CYII=\" id=\"imagec9609faf97\" transform=\"scale(1 -1) translate(0 -133.2)\" x=\"168.48\" y=\"-52.56\" width=\"6.48\" height=\"133.2\"/>\n", "   <g id=\"matplotlib.axis_3\"/>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_9\">\n", "      <defs>\n", "       <path id=\"m0442565b41\" d=\"M 0 0 \n", "L 3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m0442565b41\" x=\"175.292925\" y=\"186.559219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(182.292925 190.358437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m0442565b41\" x=\"175.292925\" y=\"153.295219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(182.292925 157.094437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m0442565b41\" x=\"175.292925\" y=\"120.031219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(182.292925 123.830437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m0442565b41\" x=\"175.292925\" y=\"86.767219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(182.292925 90.566437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m0442565b41\" x=\"175.292925\" y=\"53.503219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(182.292925 57.302437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\"/>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 168.640125 186.559219 \n", "L 171.966525 186.559219 \n", "L 175.292925 186.559219 \n", "L 175.292925 53.503219 \n", "L 171.966525 53.503219 \n", "L 168.640125 53.503219 \n", "L 168.640125 186.559219 \n", "z\n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p3ad5fb84a1\">\n", "   <rect x=\"40.603125\" y=\"9.151219\" width=\"118.272\" height=\"221.76\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["P = P[0, :, :].unsqueeze(0).unsqueeze(0)\n", "d2l.show_heatmaps(P, xlabel='Column (encoding dimension)',\n", "                  ylabel='Row (position)', figsize=(3.5, 4), cmap='Blues')"]}, {"cell_type": "markdown", "id": "6cb8a898", "metadata": {"origin_pos": 31}, "source": ["### Relative Positional Information\n", "\n", "Besides capturing absolute positional information,\n", "the above positional encoding\n", "also allows\n", "a model to easily learn to attend by relative positions.\n", "This is because\n", "for any fixed position offset $\\delta$,\n", "the positional encoding at position $i + \\delta$\n", "can be represented by a linear projection\n", "of that at position $i$.\n", "\n", "\n", "This projection can be explained\n", "mathematically.\n", "Denoting\n", "$\\omega_j = 1/10000^{2j/d}$,\n", "any pair of $(p_{i, 2j}, p_{i, 2j+1})$ \n", "in :eqref:`eq_positional-encoding-def`\n", "can \n", "be linearly projected to $(p_{i+\\delta, 2j}, p_{i+\\delta, 2j+1})$\n", "for any fixed offset $\\delta$:\n", "\n", "$$\\begin{aligned}\n", "\\begin{bmatrix} \\cos(\\delta \\omega_j) & \\sin(\\delta \\omega_j) \\\\  -\\sin(\\delta \\omega_j) & \\cos(\\delta \\omega_j) \\\\ \\end{bmatrix}\n", "\\begin{bmatrix} p_{i, 2j} \\\\  p_{i, 2j+1} \\\\ \\end{bmatrix}\n", "=&\\begin{bmatrix} \\cos(\\delta \\omega_j) \\sin(i \\omega_j) + \\sin(\\delta \\omega_j) \\cos(i \\omega_j) \\\\  -\\sin(\\delta \\omega_j) \\sin(i \\omega_j) + \\cos(\\delta \\omega_j) \\cos(i \\omega_j) \\\\ \\end{bmatrix}\\\\\n", "=&\\begin{bmatrix} \\sin\\left((i+\\delta) \\omega_j\\right) \\\\  \\cos\\left((i+\\delta) \\omega_j\\right) \\\\ \\end{bmatrix}\\\\\n", "=& \n", "\\begin{bmatrix} p_{i+\\delta, 2j} \\\\  p_{i+\\delta, 2j+1} \\\\ \\end{bmatrix},\n", "\\end{aligned}$$\n", "\n", "where the $2\\times 2$ projection matrix does not depend on any position index $i$.\n", "\n", "## Summary\n", "\n", "In self-attention, the queries, keys, and values all come from the same place.\n", "Both CNNs and self-attention enjoy parallel computation\n", "and self-attention has the shortest maximum path length.\n", "However, the quadratic computational complexity\n", "with respect to the sequence length\n", "makes self-attention prohibitively slow\n", "for very long sequences.\n", "To use the sequence order information, \n", "we can inject absolute or relative positional information \n", "by adding positional encoding to the input representations.\n", "\n", "## Exercises\n", "\n", "1. Suppose that we design a deep architecture to represent a sequence by stacking self-attention layers with positional encoding. What could the possible issues be?\n", "1. Can you design a learnable positional encoding method?\n", "1. Can we assign different learned embeddings according to different offsets between queries and keys that are compared in self-attention? Hint: you may refer to relative position embeddings :cite:`shaw2018self,huang2018music`.\n"]}, {"cell_type": "markdown", "id": "2cee2bcf", "metadata": {"origin_pos": 33, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1652)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
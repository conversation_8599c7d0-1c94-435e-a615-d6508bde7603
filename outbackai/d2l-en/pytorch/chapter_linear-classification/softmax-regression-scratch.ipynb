{"cells": [{"cell_type": "markdown", "id": "0337f854", "metadata": {"origin_pos": 1}, "source": ["# Softmax Regression Implementation from Scratch\n", ":label:`sec_softmax_scratch`\n", "\n", "Because softmax regression is so fundamental,\n", "we believe that you ought to know\n", "how to implement it yourself.\n", "Here, we limit ourselves to defining the\n", "softmax-specific aspects of the model\n", "and reuse the other components\n", "from our linear regression section,\n", "including the training loop.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "27f90605", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:38.020091Z", "iopub.status.busy": "2023-08-18T19:40:38.019758Z", "iopub.status.idle": "2023-08-18T19:40:41.731894Z", "shell.execute_reply": "2023-08-18T19:40:41.728859Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "7f7c0f8d", "metadata": {"origin_pos": 6}, "source": ["## The Softmax\n", "\n", "Let's begin with the most important part:\n", "the mapping from scalars to probabilities.\n", "For a refresher, recall the operation of the sum operator\n", "along specific dimensions in a tensor,\n", "as discussed in :numref:`subsec_lin-alg-reduction`\n", "and :numref:`subsec_lin-alg-non-reduction`.\n", "[**Given a matrix `X` we can sum over all elements (by default) or only\n", "over elements in the same axis.**]\n", "The `axis` variable lets us compute row and column sums:\n"]}, {"cell_type": "code", "execution_count": 2, "id": "4721b51f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:41.736669Z", "iopub.status.busy": "2023-08-18T19:40:41.735590Z", "iopub.status.idle": "2023-08-18T19:40:41.768196Z", "shell.execute_reply": "2023-08-18T19:40:41.766964Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(tensor([[5., 7., 9.]]),\n", " tensor([[ 6.],\n", "         [15.]]))"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.tensor([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])\n", "X.sum(0, keepdims=True), X.sum(1, keepdims=True)"]}, {"cell_type": "markdown", "id": "e183bcdc", "metadata": {"origin_pos": 8}, "source": ["Computing the softmax requires three steps:\n", "(i) exponentiation of each term;\n", "(ii) a sum over each row to compute the normalization constant for each example;\n", "(iii) division of each row by its normalization constant,\n", "ensuring that the result sums to 1:\n", "\n", "(**\n", "$$\\mathrm{softmax}(\\mathbf{X})_{ij} = \\frac{\\exp(\\mathbf{X}_{ij})}{\\sum_k \\exp(\\mathbf{X}_{ik})}.$$\n", "**)\n", "\n", "The (logarithm of the) denominator\n", "is called the (log) *partition function*.\n", "It was introduced in [statistical physics](https://en.wikipedia.org/wiki/Partition_function_(statistical_mechanics))\n", "to sum over all possible states in a thermodynamic ensemble.\n", "The implementation is straightforward:\n"]}, {"cell_type": "code", "execution_count": 3, "id": "d2f22e34", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:41.772527Z", "iopub.status.busy": "2023-08-18T19:40:41.771945Z", "iopub.status.idle": "2023-08-18T19:40:41.777757Z", "shell.execute_reply": "2023-08-18T19:40:41.776558Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["def softmax(X):\n", "    X_exp = torch.exp(X)\n", "    partition = X_exp.sum(1, keepdims=True)\n", "    return X_exp / partition  # The broadcasting mechanism is applied here"]}, {"cell_type": "markdown", "id": "12b4d33d", "metadata": {"origin_pos": 10}, "source": ["For any input `X`, [**we turn each element\n", "into a nonnegative number.\n", "Each row sums up to 1,**]\n", "as is required for a probability. Caution: the code above is *not* robust against very large or very small arguments. While it is sufficient to illustrate what is happening, you should *not* use this code verbatim for any serious purpose. Deep learning frameworks have such protections built in and we will be using the built-in softmax going forward.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "90ec733c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:41.782237Z", "iopub.status.busy": "2023-08-18T19:40:41.781359Z", "iopub.status.idle": "2023-08-18T19:40:41.793051Z", "shell.execute_reply": "2023-08-18T19:40:41.792163Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(tensor([[0.2511, 0.1417, 0.1158, 0.2529, 0.2385],\n", "         [0.2004, 0.1419, 0.1957, 0.2504, 0.2117]]),\n", " tensor([1., 1.]))"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["X = torch.rand((2, 5))\n", "X_prob = softmax(X)\n", "X_prob, X_prob.sum(1)"]}, {"cell_type": "markdown", "id": "23d983d7", "metadata": {"origin_pos": 14}, "source": ["## The Model\n", "\n", "We now have everything that we need\n", "to implement [**the softmax regression model.**]\n", "As in our linear regression example,\n", "each instance will be represented\n", "by a fixed-length vector.\n", "Since the raw data here consists\n", "of $28 \\times 28$ pixel images,\n", "[**we flatten each image,\n", "treating them as vectors of length 784.**]\n", "In later chapters, we will introduce\n", "convolutional neural networks,\n", "which exploit the spatial structure\n", "in a more satisfying way.\n", "\n", "\n", "In softmax regression,\n", "the number of outputs from our network\n", "should be equal to the number of classes.\n", "(**Since our dataset has 10 classes,\n", "our network has an output dimension of 10.**)\n", "Consequently, our weights constitute a $784 \\times 10$ matrix\n", "plus a $1 \\times 10$ row vector for the biases.\n", "As with linear regression,\n", "we initialize the weights `W`\n", "with Gaussian noise.\n", "The biases are initialized as zeros.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b88679dc", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:41.796788Z", "iopub.status.busy": "2023-08-18T19:40:41.796307Z", "iopub.status.idle": "2023-08-18T19:40:41.803043Z", "shell.execute_reply": "2023-08-18T19:40:41.802032Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["class SoftmaxRegressionScratch(d2l.Classifier):\n", "    def __init__(self, num_inputs, num_outputs, lr, sigma=0.01):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        self.W = torch.normal(0, sigma, size=(num_inputs, num_outputs),\n", "                              requires_grad=True)\n", "        self.b = torch.zeros(num_outputs, requires_grad=True)\n", "\n", "    def parameters(self):\n", "        return [self.W, self.b]"]}, {"cell_type": "markdown", "id": "cffef3c8", "metadata": {"origin_pos": 19}, "source": ["The code below defines how the network\n", "maps each input to an output.\n", "Note that we flatten each $28 \\times 28$ pixel image in the batch\n", "into a vector using `reshape`\n", "before passing the data through our model.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "6525b147", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:41.810424Z", "iopub.status.busy": "2023-08-18T19:40:41.807390Z", "iopub.status.idle": "2023-08-18T19:40:41.815642Z", "shell.execute_reply": "2023-08-18T19:40:41.814520Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(SoftmaxRegressionScratch)\n", "def forward(self, X):\n", "    X = X.reshape((-1, self.W.shape[0]))\n", "    return softmax(torch.matmul(X, self.W) + self.b)"]}, {"cell_type": "markdown", "id": "358c8926", "metadata": {"origin_pos": 21}, "source": ["## The Cross-Entropy Loss\n", "\n", "Next we need to implement the cross-entropy loss function\n", "(introduced in :numref:`subsec_softmax-regression-loss-func`).\n", "This may be the most common loss function\n", "in all of deep learning.\n", "At the moment, applications of deep learning\n", "easily cast as classification problems\n", "far outnumber those better treated as regression problems.\n", "\n", "Recall that cross-entropy takes the negative log-likelihood\n", "of the predicted probability assigned to the true label.\n", "For efficiency we avoid Python for-loops and use indexing instead.\n", "In particular, the one-hot encoding in $\\mathbf{y}$\n", "allows us to select the matching terms in $\\hat{\\mathbf{y}}$.\n", "\n", "To see this in action we [**create sample data `y_hat`\n", "with 2 examples of predicted probabilities over 3 classes and their corresponding labels `y`.**]\n", "The correct labels are $0$ and $2$ respectively (i.e., the first and third class).\n", "[**Using `y` as the indices of the probabilities in `y_hat`,**]\n", "we can pick out terms efficiently.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "be06d72f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:41.820273Z", "iopub.status.busy": "2023-08-18T19:40:41.819514Z", "iopub.status.idle": "2023-08-18T19:40:41.829451Z", "shell.execute_reply": "2023-08-18T19:40:41.828524Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([0.1000, 0.5000])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["y = torch.tensor([0, 2])\n", "y_hat = torch.tensor([[0.1, 0.3, 0.6], [0.3, 0.2, 0.5]])\n", "y_hat[[0, 1], y]"]}, {"cell_type": "markdown", "id": "100327e0", "metadata": {"origin_pos": 24, "tab": ["pytorch"]}, "source": ["Now we can (**implement the cross-entropy loss function**) by averaging over the logarithms of the selected probabilities.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "6f5696bf", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:41.833839Z", "iopub.status.busy": "2023-08-18T19:40:41.832979Z", "iopub.status.idle": "2023-08-18T19:40:41.846258Z", "shell.execute_reply": "2023-08-18T19:40:41.845315Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor(1.4979)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["def cross_entropy(y_hat, y):\n", "    return -torch.log(y_hat[list(range(len(y_hat))), y]).mean()\n", "\n", "cross_entropy(y_hat, y)"]}, {"cell_type": "code", "execution_count": 9, "id": "0d97074e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:41.850484Z", "iopub.status.busy": "2023-08-18T19:40:41.849744Z", "iopub.status.idle": "2023-08-18T19:40:41.854383Z", "shell.execute_reply": "2023-08-18T19:40:41.853500Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(SoftmaxRegressionScratch)\n", "def loss(self, y_hat, y):\n", "    return cross_entropy(y_hat, y)"]}, {"cell_type": "markdown", "id": "133145ec", "metadata": {"origin_pos": 30}, "source": ["## Training\n", "\n", "We reuse the `fit` method defined in :numref:`sec_linear_scratch` to [**train the model with 10 epochs.**]\n", "Note that the number of epochs (`max_epochs`),\n", "the minibatch size (`batch_size`),\n", "and learning rate (`lr`)\n", "are adjustable hyperparameters.\n", "That means that while these values are not\n", "learned during our primary training loop,\n", "they still influence the performance\n", "of our model, both vis-à-vis training\n", "and generalization performance.\n", "In practice you will want to choose these values\n", "based on the *validation* split of the data\n", "and then, ultimately, to evaluate your final model\n", "on the *test* split.\n", "As discussed in :numref:`subsec_generalization-model-selection`,\n", "we will regard the test data of Fashion-MNIST\n", "as the validation set, thus\n", "reporting validation loss and validation accuracy\n", "on this split.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "0fed2d06", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:41.860751Z", "iopub.status.busy": "2023-08-18T19:40:41.859826Z", "iopub.status.idle": "2023-08-18T19:41:38.026253Z", "shell.execute_reply": "2023-08-18T19:41:38.024988Z"}, "origin_pos": 31, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:41:37.881004</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mf77de1a268\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf77de1a268\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mf77de1a268\" x=\"69.163125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(65.981875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mf77de1a268\" x=\"108.223125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(105.041875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mf77de1a268\" x=\"147.283125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(144.101875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mf77de1a268\" x=\"186.343125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(183.161875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mf77de1a268\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"mb8d5d3317a\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb8d5d3317a\" x=\"30.103125\" y=\"125.158933\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 128.958151) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mb8d5d3317a\" x=\"30.103125\" y=\"98.954173\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 102.753392) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mb8d5d3317a\" x=\"30.103125\" y=\"72.749413\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.7 -->\n", "      <g transform=\"translate(7.2 76.548632) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mb8d5d3317a\" x=\"30.103125\" y=\"46.544654\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 50.343873) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#mb8d5d3317a\" x=\"30.103125\" y=\"20.339894\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.9 -->\n", "      <g transform=\"translate(7.2 24.139113) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 34.923295 13.5 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 49.633125 91.949892 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_16\"/>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 49.633125 91.949892 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 49.633125 48.719854 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 49.633125 91.949892 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 49.633125 48.719854 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 49.633125 91.949892 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 49.633125 48.719854 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 49.633125 48.719854 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "L 171.051551 135.87695 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "L 171.051551 135.87695 \n", "L 180.774997 135.686521 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "L 171.051551 135.87695 \n", "L 180.774997 135.686521 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "L 186.343125 127.280713 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "L 171.051551 135.87695 \n", "L 180.774997 135.686521 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "L 186.343125 127.280713 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "L 186.343125 38.816297 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "L 171.051551 135.87695 \n", "L 180.774997 135.686521 \n", "L 190.498444 138.382681 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "L 186.343125 127.280713 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "L 186.343125 38.816297 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "L 171.051551 135.87695 \n", "L 180.774997 135.686521 \n", "L 190.498444 138.382681 \n", "L 200.221891 136.84526 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "L 186.343125 127.280713 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "L 186.343125 38.816297 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "L 171.051551 135.87695 \n", "L 180.774997 135.686521 \n", "L 190.498444 138.382681 \n", "L 200.221891 136.84526 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "L 186.343125 127.280713 \n", "L 205.873125 131.250747 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "L 186.343125 38.816297 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "L 171.051551 135.87695 \n", "L 180.774997 135.686521 \n", "L 190.498444 138.382681 \n", "L 200.221891 136.84526 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "L 186.343125 127.280713 \n", "L 205.873125 131.250747 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "L 186.343125 38.816297 \n", "L 205.873125 37.383224 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "L 171.051551 135.87695 \n", "L 180.774997 135.686521 \n", "L 190.498444 138.382681 \n", "L 200.221891 136.84526 \n", "L 209.945338 138.719157 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "L 186.343125 127.280713 \n", "L 205.873125 131.250747 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "L 186.343125 38.816297 \n", "L 205.873125 37.383224 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "L 171.051551 135.87695 \n", "L 180.774997 135.686521 \n", "L 190.498444 138.382681 \n", "L 200.221891 136.84526 \n", "L 209.945338 138.719157 \n", "L 219.668785 139.5 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "L 186.343125 127.280713 \n", "L 205.873125 131.250747 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "L 186.343125 38.816297 \n", "L 205.873125 37.383224 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "L 171.051551 135.87695 \n", "L 180.774997 135.686521 \n", "L 190.498444 138.382681 \n", "L 200.221891 136.84526 \n", "L 209.945338 138.719157 \n", "L 219.668785 139.5 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "L 186.343125 127.280713 \n", "L 205.873125 131.250747 \n", "L 225.403125 130.008583 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "L 186.343125 38.816297 \n", "L 205.873125 37.383224 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 86.486977 \n", "L 54.370189 102.950789 \n", "L 64.093636 110.705927 \n", "L 73.817082 116.708816 \n", "L 83.540529 119.75998 \n", "L 93.263976 123.524795 \n", "L 102.987423 125.935604 \n", "L 112.71087 128.822425 \n", "L 122.434316 129.285365 \n", "L 132.157763 129.243378 \n", "L 141.88121 134.490411 \n", "L 151.604657 132.68381 \n", "L 161.328104 135.956955 \n", "L 171.051551 135.87695 \n", "L 180.774997 135.686521 \n", "L 190.498444 138.382681 \n", "L 200.221891 136.84526 \n", "L 209.945338 138.719157 \n", "L 219.668785 139.5 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 49.633125 91.949892 \n", "L 69.163125 108.628539 \n", "L 88.693125 114.325522 \n", "L 108.223125 119.456837 \n", "L 127.753125 122.227429 \n", "L 147.283125 116.578995 \n", "L 166.813125 122.814852 \n", "L 186.343125 127.280713 \n", "L 205.873125 131.250747 \n", "L 225.403125 130.008583 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_127\">\n", "    <path d=\"M 49.633125 48.719854 \n", "L 69.163125 44.522998 \n", "L 88.693125 43.089925 \n", "L 108.223125 41.11945 \n", "L 127.753125 39.942283 \n", "L 147.283125 43.141106 \n", "L 166.813125 41.938348 \n", "L 186.343125 38.816297 \n", "L 205.873125 37.383224 \n", "L 225.403125 38.662753 \n", "\" clip-path=\"url(#pae64a41c4e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 100.434375 \n", "L 218.403125 100.434375 \n", "Q 220.403125 100.434375 220.403125 98.434375 \n", "L 220.403125 54.565625 \n", "Q 220.403125 52.565625 218.403125 52.565625 \n", "L 138.8125 52.565625 \n", "Q 136.8125 52.565625 136.8125 54.565625 \n", "L 136.8125 98.434375 \n", "Q 136.8125 100.434375 138.8125 100.434375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_128\">\n", "     <path d=\"M 140.8125 60.664063 \n", "L 150.8125 60.664063 \n", "L 160.8125 60.664063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 64.164063) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_129\">\n", "     <path d=\"M 140.8125 75.620313 \n", "L 150.8125 75.620313 \n", "L 160.8125 75.620313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 79.120313) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_130\">\n", "     <path d=\"M 140.8125 90.576563 \n", "L 150.8125 90.576563 \n", "L 160.8125 90.576563 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 94.076563) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pae64a41c4e\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = d2l.FashionMNIST(batch_size=256)\n", "model = SoftmaxRegressionScratch(num_inputs=784, num_outputs=10, lr=0.1)\n", "trainer = d2l.Trainer(max_epochs=10)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "c01a1d3e", "metadata": {"origin_pos": 32}, "source": ["## Prediction\n", "\n", "Now that training is complete,\n", "our model is ready to [**classify some images.**]\n"]}, {"cell_type": "code", "execution_count": 11, "id": "ea0b6dd0", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:38.030970Z", "iopub.status.busy": "2023-08-18T19:41:38.030251Z", "iopub.status.idle": "2023-08-18T19:41:38.207822Z", "shell.execute_reply": "2023-08-18T19:41:38.206713Z"}, "origin_pos": 33, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([256])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["X, y = next(iter(data.val_dataloader()))\n", "preds = model(X).argmax(axis=1)\n", "preds.shape"]}, {"cell_type": "markdown", "id": "7a160103", "metadata": {"origin_pos": 34}, "source": ["We are more interested in the images we label *incorrectly*. We visualize them by\n", "comparing their actual labels\n", "(first line of text output)\n", "with the predictions from the model\n", "(second line of text output).\n"]}, {"cell_type": "code", "execution_count": 12, "id": "41ef8291", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:41:38.211804Z", "iopub.status.busy": "2023-08-18T19:41:38.211212Z", "iopub.status.idle": "2023-08-18T19:41:38.569431Z", "shell.execute_reply": "2023-08-18T19:41:38.568219Z"}, "origin_pos": 35, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"684pt\" height=\"114.189543pt\" viewBox=\"0 0 684 114.189543\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:41:38.474094</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 114.189543 \n", "L 684 114.189543 \n", "L 684 -0 \n", "L 0 -0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 7.2 106.989543 \n", "L 78.**********.989543 \n", "L 78.434043 35.7555 \n", "L 7.2 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pf094324a2d)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAAFUUlEQVR4nO2cTW8bVRSGZzzjr9jGdtKGNCGhokQiTVlUgCLYgliCQIh/wF9gV6Fu+AFsoIsukLpCQqKiCBCLLlAF5UOiVLSkITSF0oakTXDs2I49H+zuue8NE0ol0rN4n9U5Pndmbv3mnnvmzNT+C/5rqUdUkHvQEyACxVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVAExVBEGE48DB9cPXHY2PlN1GowFouTT/BMQxnrl2MI1RpdYyepD7F8IGPjBK8XxeLncvhKcLW0Y+zUOWdvkAe/XBhmxjqdkszbuUbOFz+OcW5pqyDj+nj9gvW9Na/h91T94GsvC64MRVAMRVAMRfjHzp6ARPnV0+8b+621BRi8MagYu5bvQ+xQoWXsX3sHILb4l+xL4yNtiIW+s/dYRKn8rfRjzPU7UfiP4zzP87pDHAvHDUPwO9uyZwShk9/Lsi8drHQg1ij0jO1+Fz1rruVgCLHj1d+M/e6plyHGlaEIiqEI/9nP34Q0NT9629gXPjwOg7tzsmzDYgQxu2SNu5gK9pLcLidzBSyJ83nxkwTLx2gYyFzycWbM8zwvsecT43lsgq5TvlpuUsFrFOryXTRrXYi1tsvGLhcHEHv18CVjz5VuQYwrQxEUQxEUQxHhHytYhp6cPWvsL+aOQqxckxJuZ6UGseId0TXFlO31D0m+TZ02Smqn8z7uNbD3OHuGnfv99RKEigOn5YLVNBBJte6V17AdYv874gKWy2kofrtUxanV5Dyb41janguPGfv6KH73XBmKoBiKCB/9GJfm8y9JSglXCxCLboqfNDHddI9ICRe0MN2ELdE8KWIKiSvZd+DBthwX9PC4xMoag7rTbcVq0ktzcmxSwLFDK6UkIf5t5jDD4PWLYkdlJ72F4hdWMb35UxK7/N6TeL3sy5H9hmIogmIoIhzUsA493ZowdjS5A7HZqTVjzzVWIXZta9zY1Twed6tTF3txHGL1n2R/cRu4fiL5tT+Ge0ZodSCKK3jcThPH2m2NwHkqZ/v2/uF5nhdXZULu0zx7DyvdwdhDN+Q8I7exoxu+Le2mpLsMMa4MRVAMRVAMRYTFTWyFX+4+Yuyc8+RrtS0tkOsXpyEWWC0It87vPiYfPPPUEsRqC7K/nP92HmLFCdkY0qvYcihuWrm+6uwnPXC93kHJ4cNRpxXelJyeW6pA7MAFOe/olzchFv2O/r2SfVfFlaEKiqGIsHxxKTMYd/BWfuScdEenvl/HwX+KH7e2IOQHUj63n3gcYt+90TB2cXIbj7skaXHuRZzn4mezxnbbIY35u+C3bzSNXV3GVs30J5Km4is/ellEmRHP80PnyWZgt3sxLaZR9pm4MhRBMRRBMRThuz8yvPXpEWPnT43B4PJH3/yvkwnOT4K/+MOMsXPO0zu7VeG+oF1ax7FTZ36W4+5uZF7fzf2pk+/vmfT+freZK0MRFEMRoftB43XpzCbtZTdsyNXwhYS018sYiexV2m2cngE/XbCewjljK5PylsH0O3jLHy/+gv4e89lVlsIE9vdn4rkyFEExFEExFLErYSbtPd74uo9x/4X6Gfz/bu2Z54zdP4p70uQrV4z9rwWob5W6zj6w1x6233BlKIJiKGLXHTh5cHBlKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKIJiKOJv/fRtiyaMmuMAAAAASUVORK5CYII=\" id=\"image677659bff9\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"7.2\" y=\"-35.709543\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 7.2 106.989543 \n", "L 7.2 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 78.**********.989543 \n", "L 78.434043 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 7.2 106.989543 \n", "L 78.**********.989543 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 7.2 35.7555 \n", "L 78.434043 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_1\">\n", "    <!-- sneaker -->\n", "    <g transform=\"translate(19.104834 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"177.001953\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"238.28125\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"292.566406\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"354.089844\"/>\n", "    </g>\n", "    <!-- sandal -->\n", "    <g transform=\"translate(23.059209 29.7555) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"113.378906\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"176.757812\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"240.234375\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"301.513672\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 92.680851 106.989543 \n", "L 163.914894 106.989543 \n", "L 163.914894 35.7555 \n", "L 92.680851 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pdf9b403f9c)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imaged3718e4d78\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"92.680851\" y=\"-35.709543\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 92.680851 106.989543 \n", "L 92.680851 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 163.914894 106.989543 \n", "L 163.914894 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 92.680851 106.989543 \n", "L 163.914894 106.989543 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 92.680851 35.7555 \n", "L 163.914894 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_2\">\n", "    <!-- coat -->\n", "    <g transform=\"translate(115.298497 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-63\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"54.980469\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"116.162109\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"177.441406\"/>\n", "    </g>\n", "    <!-- shirt -->\n", "    <g transform=\"translate(114.884122 29.7555) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"143.261719\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"184.375\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 178.161702 106.989543 \n", "L 249.395745 106.989543 \n", "L 249.395745 35.7555 \n", "L 178.161702 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p50fb9725fc)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageb0e2f870f1\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"178.161702\" y=\"-35.709543\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 178.161702 106.989543 \n", "L 178.161702 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 249.395745 106.989543 \n", "L 249.395745 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 178.161702 106.989543 \n", "L 249.395745 106.989543 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 178.161702 35.7555 \n", "L 249.395745 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_3\">\n", "    <!-- pullover -->\n", "    <g transform=\"translate(189.452473 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-70\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"126.855469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"182.421875\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"243.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.783203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"364.306641\"/>\n", "    </g>\n", "    <!-- t-shirt -->\n", "    <g transform=\"translate(195.848098 29.7555) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"75.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"127.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"190.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"218.554688\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"259.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 263.642553 106.989543 \n", "L 334.876596 106.989543 \n", "L 334.876596 35.7555 \n", "L 263.642553 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p4b2c2d7645)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image2b30930a84\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"263.642553\" y=\"-35.709543\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 263.642553 106.989543 \n", "L 263.642553 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 334.876596 106.989543 \n", "L 334.876596 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 263.642553 106.989543 \n", "L 334.876596 106.989543 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 263.642553 35.7555 \n", "L 334.876596 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- sandal -->\n", "    <g transform=\"translate(279.501762 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"113.378906\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"176.757812\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"240.234375\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"301.513672\"/>\n", "    </g>\n", "    <!-- sneaker -->\n", "    <g transform=\"translate(275.547387 29.7555) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"177.001953\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"238.28125\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"292.566406\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"354.089844\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 349.123404 106.989543 \n", "L 420.357447 106.989543 \n", "L 420.357447 35.7555 \n", "L 349.123404 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#paeeb849d6b)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageec7b9b7b6d\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"349.123404\" y=\"-35.709543\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 349.123404 106.989543 \n", "L 349.123404 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 420.357447 106.989543 \n", "L 420.357447 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 349.123404 106.989543 \n", "L 420.357447 106.989543 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 349.123404 35.7555 \n", "L 420.357447 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- ankle boot -->\n", "    <g transform=\"translate(353.017301 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"61.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"182.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"210.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"271.875\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"303.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"367.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"428.320312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"489.501953\"/>\n", "    </g>\n", "    <!-- sneaker -->\n", "    <g transform=\"translate(361.028238 29.7555) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"177.001953\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"238.28125\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"292.566406\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"354.089844\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 434.604255 106.989543 \n", "L 505.838298 106.989543 \n", "L 505.838298 35.7555 \n", "L 434.604255 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p42bb12c9ab)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "iVBORw0KGgoAAAANSUhEUgAAAGMAAABjCAYAAACPO76VAAAJ6klEQVR4nO1dS48cVxWuV1c/Z/o5j8STMTN+gbGd4BArCRAGKQYlskQWWXiHIIDYZIHYseUPoGyIxBKBnEg8hIQUJSCD4ihBsS1wTOzxeJix55Fh3D39flXXg12d812rh6nWyLqD7rc6V6e67q0+dc8595xzb+kv6q8GmqTQLSukA9cdep1x6vPQ9jM2XvDRjT12qGM7eLR/jfFIe1PYFUoYEkEJQyJY//sSdvETM9QwUL/2jkyGtP3328Dz2+0RhqZpRj5PjWwG77m6RvRN7M88cRTa3ki9CxDsyeD80yGdWK3itdUG9f3gwZ67UDNDIihhSIRIaqrxzKGQziw3gLf5QjykD38w3A2NBKaaqs9MAWv87kpIW3OH8Xetzmj97ebKCrz66/T8/psF4DmZiZAee0upqQMJJQyJoIQhESLZDC9G7l3tZBZ4+oDRpgm88mvPhXSsg7rXcKkda/vAu3eB+nt8fht4y2fpnhfPXwHeb648D+2pD2ZpbNiF1s/pQ3n52z0a2637wNspj4X0uOA7m4PRwihqZkgEJQyJEElNcZVieDgVS/+iOX7uwzrw3v49W736uJJ1jnVDWt+OA6/0MV1r/HkCeInjxPvDpa8BLy4EbXdeaYV07k9p4NWP03PEy/huBicTNOwnT2D/mWZIm72Yth9QM0MiKGFIBCUMiRDJZowtk570kqgnrdvk+v1x9TTw5t9cDun22VngOYtkJyqn0Z50pqlt9ZA38x7ZpcqZceB1J/HaWJz87tLlNeA54zSeeA1929ylqyG9+MZZ4E2lydbZ233g9afHtFGgZoZEUMKQCJHUlJ+gy90xQU2ZJNeL89eA985TCyEdmKhC7BYtXwc5fDfMPq3kU9sD4N29SKrpyG8xebWVw0RUr09j9YUklc7ddexCM0tFGucORhWaU6Re8wYuwc3+aOksNTMkghKGRFDCkAjRwiEtJ6TNmDn0uh0XQw7OGMk8+88y8DpHKUtm5B3gpa9ROEITAqGZNbI9/SKGUewaXtzsscc00TAkK+TOptd7wPNLVBAxdg/7j3+ZXFtnMg88qyMYnz1CzQyJoIQhEaKpqTZNzcEsrjJjOsn19SIme96ZoWRP9q9YyGDX2X02kzi4Pqkb3UfVU7xJq97uBLrZqTK6ltUyD+N2gRev07WGiytwZ4rUbayF/W+ulkJ6cgZVduE6udpCvmpXqJkhEZQwJIIShkSIZDMCFvIQwxrekcdC+u4Ao6j9POlbf3YSeF6C9O3hL20Ar3P98ZA2e1gY1z5E9sVN4liSgs3wS/TbB+dywEtv0bWt2RTw4lX6XT+LfWSW6a9z8HE1o06ZRWUzDiiUMCRCJDXFkdxCF3FjgVzUn6+fB16GrV7NnRbwnCKpm5U708Cbq5Ca0AeoeipfJPWW/gzdzlgDV/JGmVzU7L+Rx1f2mZsYHejNkfv61e9fBd6tH58K6Y0X0CUPuvjf7BVqZkgEJQyJoIQhEaJl+rLM9fPRaesXSPkulUvIO0m8iUuol7VjVJyWWcHhGA5FUXtT6HbOvku2p3Iao8S6g26wvcPeObEMlnmsgbCv47OvUDQ42cJnsq4tUmPhKeAFHWUzDjyUMCRCtKhtl5ImnTncElD8hOb/9gwWu/7wm38J6cs/FbZcjQ1PUllNisy6KVRTbooitfq3K8DTL2NyJ8a8aSeHj5x4QK6u38CIcv84qZtyB1VhwaLIrI2lxZoeZ8muzt63tKmZIRGUMCSCEoZEiGQzdBaN7GdR92cXqQ53/DVUor9aOhfSM94i8AL2OhjijmWDmGYPXelekYZ+prQJvC2vCG2epeNb4TQNM4ji9rdcjuxCq4dFDwWbbJbdEPxlTxWxHXgoYUgEJQyJEC3T55D/rovbbVlovJhCf/3OEmXsjByuTwJ2Ok9M0L08myhWh3QmSL+/v3IEeMe0GrRtZjP4vkRN0zTDYdUh+Rzw+gOyC+16Anh6mtY94j29ZlMbBWpmSAQlDIkQTU2xQ7wempoF2vfQcLBmNXeDdVPEulR+moCNSUAogBCRv0thjMYxzLT5adzWFeuQW+zZwqGQDN40jm0wIFVo1LBQLkhTn+KpD6MeNKlmhkRQwpAIShgSIVqmj4WDTQf1YutzZDMqTQxhZ1ihmFvCPXU8zBHo+G5wt9cYCEXJLBSev4l2IEgOPz5CF9S50Sb7UjuNIR7XIV5yC8fWm6HKNWPEU3REqJkhEZQwJMLIRWxWG5fg7UnK7lXWc8CbjrMtXwXMApp9Uj/iST18BW410F0uL9AKeJDF301cxfAv70N0lwMWGRbd9WCHIrVxYWuaF6ffif/FqFAzQyIoYUgEJQyJMLLNsLfxiAj3FOlXu4wZs2SFXF2uazUNj4sQI8Fw/3GMmj72IbmdXhLv6aXQLvE+rA66yF6W7iseWJldIhfZ6g53X0fdaixCzQyJoIQhEUZWU0YVE0iGQxFPsyt83iBDasuuodsZWMx9FVxEg51O46VwqPF1KnroH8KElZNHNRVrUp+BcGClH6P30a5htDd/h1RTP4uqV2duOD85QtOibR3jUDNDIihhSAQlDIkwss3wqzVox+vsvHHBC4RwhIU623CIJ24v5npZ6wnbiccp0+YlhHdKSOaZHWEfH7+PTX+BLoRjrDaNZ7cCbX6Mh6Ypm/F/ASUMiTC6mhL2HdgtPjlRxrEGcy3N4QUBvnCgmJ+m+4jbibvTpKYah/Expq/UoN2bpr0VXPU81L8xfGx2E9UkjyS4q/fFy0eCmhkSQQlDIihhSISRbYYIfnzFYF7YCuzyTBvahWAXPc3h5HF/RGqNKt5S94U6XOGUOB5mEW0WFDoI9bw6K0bjLvhD2KevI6uZIRGUMCRCNDXFPzYrTE1jdSukvS/gh275R3kN8ZxwxuMRVE1D91EsFtC75Oq6BVSLVkdYrdt0H1/YRsYjumJtls5UE7+HpmlaYp19TFfbH6iZIRGUMCSCEoZEiGYzdnHh+EfIE9U5ZDKXUYyg8uIBUS+nr1OYwf0PfsHSff7JkF69gPszDv0NQx6J9z8NaXF7sXOW7BvfUqZpePqbmOlLfLz3rxnvFWpmSAQlDImwbytwjvRHK9CuL8yHdKwt1DQxbzJ++Qbw1n/0dEh/5wdLwDuRuBTSv9z4OvBefuUTaA8CUjFv/O4C8OZ+dj2k+984A7xegX6XWcNiBa9a1fYbamZIBCUMiaCEIRH0F/VX9yfkuGsvFIIInkW9zMMh915CF3Xxe78I6W/dQl2//I+Zod0Fwit29Mx6SP/6+FvAe/btn4T0E++ha5u8T4Vy3qd3hva3X1AzQyIoYUiER6Om9oid7z4H7cl36RB1d2NTvHxfYKQp4uu327tcuQt0IUGmTkg4+FDCkAhKGBLhvwkbBgo7p0pXAAAAAElFTkSuQmCC\" id=\"image8c385747a8\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"434.604255\" y=\"-35.709543\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 434.604255 106.989543 \n", "L 434.604255 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 505.838298 106.989543 \n", "L 505.838298 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 434.604255 106.989543 \n", "L 505.838298 106.989543 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 434.604255 35.7555 \n", "L 505.838298 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- coat -->\n", "    <g transform=\"translate(457.221902 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-63\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"54.980469\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"116.162109\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"177.441406\"/>\n", "    </g>\n", "    <!-- pullover -->\n", "    <g transform=\"translate(445.895027 29.7555) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-70\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"126.855469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"182.421875\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"243.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.783203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"364.306641\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 520.**********.989543 \n", "L 591.**********.989543 \n", "L 591.319149 35.7555 \n", "L 520.085106 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p1e19bc1343)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagec370332855\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"520.085106\" y=\"-35.709543\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 520.**********.989543 \n", "L 520.085106 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 591.**********.989543 \n", "L 591.319149 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 520.**********.989543 \n", "L 591.**********.989543 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 520.085106 35.7555 \n", "L 591.319149 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- shirt -->\n", "    <g transform=\"translate(542.288378 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"143.261719\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"184.375\"/>\n", "    </g>\n", "    <!-- t-shirt -->\n", "    <g transform=\"translate(537.771503 29.7555) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"75.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"127.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"190.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"218.554688\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"259.667969\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 605.565957 106.989543 \n", "L 676.8 106.989543 \n", "L 676.8 35.7555 \n", "L 605.565957 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pcfa9b48a08)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image946b131028\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"605.565957\" y=\"-35.709543\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 605.565957 106.989543 \n", "L 605.565957 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 676.8 106.989543 \n", "L 676.8 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 605.565957 106.989543 \n", "L 676.8 106.989543 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 605.565957 35.7555 \n", "L 676.8 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_8\">\n", "    <!-- pullover -->\n", "    <g transform=\"translate(616.856729 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-70\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"126.855469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"182.421875\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"243.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.783203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"364.306641\"/>\n", "    </g>\n", "    <!-- shirt -->\n", "    <g transform=\"translate(627.769229 29.7555) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"143.261719\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"184.375\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf094324a2d\">\n", "   <rect x=\"7.2\" y=\"35.7555\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pdf9b403f9c\">\n", "   <rect x=\"92.680851\" y=\"35.7555\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p50fb9725fc\">\n", "   <rect x=\"178.161702\" y=\"35.7555\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p4b2c2d7645\">\n", "   <rect x=\"263.642553\" y=\"35.7555\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"paeeb849d6b\">\n", "   <rect x=\"349.123404\" y=\"35.7555\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p42bb12c9ab\">\n", "   <rect x=\"434.604255\" y=\"35.7555\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p1e19bc1343\">\n", "   <rect x=\"520.085106\" y=\"35.7555\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pcfa9b48a08\">\n", "   <rect x=\"605.565957\" y=\"35.7555\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1200x150 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["wrong = preds.type(y.dtype) != y\n", "X, y, preds = X[wrong], y[wrong], preds[wrong]\n", "labels = [a+'\\n'+b for a, b in zip(\n", "    data.text_labels(y), data.text_labels(preds))]\n", "data.visualize([X, y], labels=labels)"]}, {"cell_type": "markdown", "id": "806939a6", "metadata": {"origin_pos": 36}, "source": ["## Summary\n", "\n", "By now we are starting to get some experience\n", "with solving linear regression\n", "and classification problems.\n", "With it, we have reached what would arguably be\n", "the state of the art of 1960--1970s of statistical modeling.\n", "In the next section, we will show you how to leverage\n", "deep learning frameworks to implement this model\n", "much more efficiently.\n", "\n", "## Exercises\n", "\n", "1. In this section, we directly implemented the softmax function based on the mathematical definition of the softmax operation. As discussed in :numref:`sec_softmax` this can cause numerical instabilities.\n", "    1. Test whether `softmax` still works correctly if an input has a value of $100$.\n", "    1. Test whether `softmax` still works correctly if the largest of all inputs is smaller than $-100$?\n", "    1. Implement a fix by looking at the value relative to the largest entry in the argument.\n", "1. Implement a `cross_entropy` function that follows the definition of the cross-entropy loss function $\\sum_i y_i \\log \\hat{y}_i$.\n", "    1. Try it out in the code example of this section.\n", "    1. Why do you think it runs more slowly?\n", "    1. Should you use it? When would it make sense to?\n", "    1. What do you need to be careful of? Hint: consider the domain of the logarithm.\n", "1. Is it always a good idea to return the most likely label? For example, would you do this for medical diagnosis? How would you try to address this?\n", "1. Assume that we want to use softmax regression to predict the next word based on some features. What are some problems that might arise from a large vocabulary?\n", "1. Experiment with the hyperparameters of the code in this section. In particular:\n", "    1. Plot how the validation loss changes as you change the learning rate.\n", "    1. Do the validation and training loss change as you change the minibatch size? How large or small do you need to go before you see an effect?\n"]}, {"cell_type": "markdown", "id": "840785bb", "metadata": {"origin_pos": 38, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/51)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
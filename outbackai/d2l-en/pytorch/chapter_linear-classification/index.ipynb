{"cells": [{"cell_type": "markdown", "id": "f9f86ae9", "metadata": {"origin_pos": 0}, "source": ["# Linear Neural Networks for Classification\n", ":label:`chap_classification`\n", "\n", "Now that you have worked through all of the mechanics\n", "you are ready to apply the skills you have learned to broader kinds of tasks.\n", "Even as we pivot towards classification,\n", "most of the plumbing remains the same:\n", "loading the data, passing it through the model,\n", "generating output, calculating the loss,\n", "taking gradients with respect to weights,\n", "and updating the model.\n", "However, the precise form of the targets,\n", "the parametrization of the output layer,\n", "and the choice of loss function will adapt\n", "to suit the *classification* setting.\n", "\n", ":begin_tab:toc\n", " - [softmax-regression](softmax-regression.ipynb)\n", " - [image-classification-dataset](image-classification-dataset.ipynb)\n", " - [classification](classification.ipynb)\n", " - [softmax-regression-scratch](softmax-regression-scratch.ipynb)\n", " - [softmax-regression-concise](softmax-regression-concise.ipynb)\n", " - [generalization-classification](generalization-classification.ipynb)\n", " - [environment-and-distribution-shift](environment-and-distribution-shift.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
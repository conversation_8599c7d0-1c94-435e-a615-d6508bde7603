{"cells": [{"cell_type": "markdown", "id": "026a4a47", "metadata": {"origin_pos": 1}, "source": ["# Concise Implementation of Softmax Regression\n", ":label:`sec_softmax_concise`\n", "\n", "\n", "\n", "Just as high-level deep learning frameworks\n", "made it easier to implement linear regression\n", "(see :numref:`sec_linear_concise`),\n", "they are similarly convenient here.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d63e33cd", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:44:05.629460Z", "iopub.status.busy": "2023-08-18T19:44:05.628934Z", "iopub.status.idle": "2023-08-18T19:44:08.527389Z", "shell.execute_reply": "2023-08-18T19:44:08.526143Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "fe9e0fbb", "metadata": {"origin_pos": 6}, "source": ["## Defining the Model\n", "\n", "As in :numref:`sec_linear_concise`, \n", "we construct our fully connected layer \n", "using the built-in layer. \n", "The built-in `__call__` method then invokes `forward` \n", "whenever we need to apply the network to some input.\n"]}, {"cell_type": "markdown", "id": "9aa28e85", "metadata": {"origin_pos": 8, "tab": ["pytorch"]}, "source": ["We use a `Flatten` layer to convert the fourth-order tensor `X` to second order \n", "by keeping the dimensionality along the first axis unchanged.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "00d99921", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:44:08.531522Z", "iopub.status.busy": "2023-08-18T19:44:08.531066Z", "iopub.status.idle": "2023-08-18T19:44:08.536729Z", "shell.execute_reply": "2023-08-18T19:44:08.535876Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [], "source": ["class SoftmaxRegression(d2l.Classifier):  #@save\n", "    \"\"\"The softmax regression model.\"\"\"\n", "    def __init__(self, num_outputs, lr):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        self.net = nn.Sequential(nn.<PERSON>(),\n", "                                 nn.Lazy<PERSON>inear(num_outputs))\n", "\n", "    def forward(self, X):\n", "        return self.net(X)"]}, {"cell_type": "markdown", "id": "a179433e", "metadata": {"origin_pos": 14}, "source": ["## Softmax Revisited\n", ":label:`subsec_softmax-implementation-revisited`\n", "\n", "In :numref:`sec_softmax_scratch` we calculated our model's output\n", "and applied the cross-entropy loss. While this is perfectly\n", "reasonable mathematically, it is risky computationally, because of\n", "numerical underflow and overflow in the exponentiation.\n", "\n", "Recall that the softmax function computes probabilities via\n", "$\\hat y_j = \\frac{\\exp(o_j)}{\\sum_k \\exp(o_k)}$.\n", "If some of the $o_k$ are very large, i.e., very positive,\n", "then $\\exp(o_k)$ might be larger than the largest number\n", "we can have for certain data types. This is called *overflow*. Likewise,\n", "if every argument is a very large negative number, we will get *underflow*.\n", "For instance, single precision floating point numbers approximately\n", "cover the range of $10^{-38}$ to $10^{38}$. As such, if the largest term in $\\mathbf{o}$\n", "lies outside the interval $[-90, 90]$, the result will not be stable.\n", "A way round this problem is to subtract $\\bar{o} \\stackrel{\\textrm{def}}{=} \\max_k o_k$ from\n", "all entries:\n", "\n", "$$\n", "\\hat y_j = \\frac{\\exp o_j}{\\sum_k \\exp o_k} =\n", "\\frac{\\exp(o_j - \\bar{o}) \\exp \\bar{o}}{\\sum_k \\exp (o_k - \\bar{o}) \\exp \\bar{o}} =\n", "\\frac{\\exp(o_j - \\bar{o})}{\\sum_k \\exp (o_k - \\bar{o})}.\n", "$$\n", "\n", "By construction we know that $o_j - \\bar{o} \\leq 0$ for all $j$. As such, for a $q$-class\n", "classification problem, the denominator is contained in the interval $[1, q]$. Moreover, the\n", "numerator never exceeds $1$, thus preventing numerical overflow. Numerical underflow only\n", "occurs when $\\exp(o_j - \\bar{o})$ numerically evaluates as $0$. Nonetheless, a few steps down\n", "the road we might find ourselves in trouble when we want to compute $\\log \\hat{y}_j$ as $\\log 0$.\n", "In particular, in backpropagation,\n", "we might find ourselves faced with a screenful\n", "of the dreaded `NaN` (Not a Number) results.\n", "\n", "Fortunately, we are saved by the fact that\n", "even though we are computing exponential functions,\n", "we ultimately intend to take their log\n", "(when calculating the cross-entropy loss).\n", "By combining softmax and cross-entropy,\n", "we can escape the numerical stability issues altogether. We have:\n", "\n", "$$\n", "\\log \\hat{y}_j =\n", "\\log \\frac{\\exp(o_j - \\bar{o})}{\\sum_k \\exp (o_k - \\bar{o})} =\n", "o_j - \\bar{o} - \\log \\sum_k \\exp (o_k - \\bar{o}).\n", "$$\n", "\n", "This avoids both overflow and underflow.\n", "We will want to keep the conventional softmax function handy\n", "in case we ever want to evaluate the output probabilities by our model.\n", "But instead of passing softmax probabilities into our new loss function,\n", "we just\n", "[**pass the logits and compute the softmax and its log\n", "all at once inside the cross-entropy loss function,**]\n", "which does smart things like the [\"LogSumExp trick\"](https://en.wikipedia.org/wiki/LogSumExp).\n"]}, {"cell_type": "code", "execution_count": 3, "id": "5561ed28", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:44:08.542113Z", "iopub.status.busy": "2023-08-18T19:44:08.541216Z", "iopub.status.idle": "2023-08-18T19:44:08.546903Z", "shell.execute_reply": "2023-08-18T19:44:08.546113Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(d2l.Classifier)  #@save\n", "def loss(self, Y_hat, Y, averaged=True):\n", "    Y_hat = Y_hat.reshape((-1, Y_hat.shape[-1]))\n", "    Y = Y.reshape((-1,))\n", "    return F.cross_entropy(\n", "        Y_hat, Y, reduction='mean' if averaged else 'none')"]}, {"cell_type": "markdown", "id": "61ee6ad3", "metadata": {"origin_pos": 17}, "source": ["## Training\n", "\n", "Next we train our model. We use Fashion-MNIST images, flattened to 784-dimensional feature vectors.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "b88a9971", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:44:08.550300Z", "iopub.status.busy": "2023-08-18T19:44:08.549788Z", "iopub.status.idle": "2023-08-18T19:45:00.950604Z", "shell.execute_reply": "2023-08-18T19:45:00.949689Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"238.965625pt\" height=\"183.35625pt\" viewBox=\"0 0 238.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:00.822654</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 238.**********.35625 \n", "L 238.965625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mdcf7918d1d\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mdcf7918d1d\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mdcf7918d1d\" x=\"69.163125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(65.981875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mdcf7918d1d\" x=\"108.223125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(105.041875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mdcf7918d1d\" x=\"147.283125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(144.101875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mdcf7918d1d\" x=\"186.343125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(183.161875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mdcf7918d1d\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(219.040625 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m72af7f06fb\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m72af7f06fb\" x=\"30.103125\" y=\"125.669614\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 129.468832) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m72af7f06fb\" x=\"30.103125\" y=\"99.75624\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 103.555458) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m72af7f06fb\" x=\"30.103125\" y=\"73.842866\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.7 -->\n", "      <g transform=\"translate(7.2 77.642084) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m72af7f06fb\" x=\"30.103125\" y=\"47.929492\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 51.72871) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m72af7f06fb\" x=\"30.103125\" y=\"22.016118\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.9 -->\n", "      <g transform=\"translate(7.2 25.815336) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 34.923295 13.5 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 49.633125 91.606167 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_16\"/>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 49.633125 91.606167 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 49.633125 51.82662 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 49.633125 91.606167 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 49.633125 51.82662 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 49.633125 91.606167 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 49.633125 51.82662 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 49.633125 51.82662 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "L 171.051551 136.555619 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "L 171.051551 136.555619 \n", "L 180.774997 136.445276 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "L 171.051551 136.555619 \n", "L 180.774997 136.445276 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "L 186.343125 128.342521 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "L 171.051551 136.555619 \n", "L 180.774997 136.445276 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "L 186.343125 128.342521 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "L 186.343125 40.464213 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "L 171.051551 136.555619 \n", "L 180.774997 136.445276 \n", "L 190.498444 137.847277 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "L 186.343125 128.342521 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "L 186.343125 40.464213 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "L 171.051551 136.555619 \n", "L 180.774997 136.445276 \n", "L 190.498444 137.847277 \n", "L 200.221891 137.880131 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "L 186.343125 128.342521 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "L 186.343125 40.464213 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "L 171.051551 136.555619 \n", "L 180.774997 136.445276 \n", "L 190.498444 137.847277 \n", "L 200.221891 137.880131 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "L 186.343125 128.342521 \n", "L 205.873125 131.108597 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "L 186.343125 40.464213 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "L 171.051551 136.555619 \n", "L 180.774997 136.445276 \n", "L 190.498444 137.847277 \n", "L 200.221891 137.880131 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "L 186.343125 128.342521 \n", "L 205.873125 131.108597 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "L 186.343125 40.464213 \n", "L 205.873125 39.27483 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "L 171.051551 136.555619 \n", "L 180.774997 136.445276 \n", "L 190.498444 137.847277 \n", "L 200.221891 137.880131 \n", "L 209.945338 139.308996 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "L 186.343125 128.342521 \n", "L 205.873125 131.108597 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "L 186.343125 40.464213 \n", "L 205.873125 39.27483 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "L 171.051551 136.555619 \n", "L 180.774997 136.445276 \n", "L 190.498444 137.847277 \n", "L 200.221891 137.880131 \n", "L 209.945338 139.308996 \n", "L 219.668785 139.5 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "L 186.343125 128.342521 \n", "L 205.873125 131.108597 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "L 186.343125 40.464213 \n", "L 205.873125 39.27483 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "L 171.051551 136.555619 \n", "L 180.774997 136.445276 \n", "L 190.498444 137.847277 \n", "L 200.221891 137.880131 \n", "L 209.945338 139.308996 \n", "L 219.668785 139.5 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "L 186.343125 128.342521 \n", "L 205.873125 131.108597 \n", "L 225.403125 131.019117 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "L 186.343125 40.464213 \n", "L 205.873125 39.27483 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 34.923295 13.5 \n", "L 44.646742 87.665332 \n", "L 54.370189 102.878103 \n", "L 64.093636 111.872583 \n", "L 73.817082 115.504161 \n", "L 83.540529 122.512942 \n", "L 93.263976 124.183222 \n", "L 102.987423 126.453523 \n", "L 112.71087 127.323445 \n", "L 122.434316 130.875227 \n", "L 132.157763 130.85079 \n", "L 141.88121 133.557744 \n", "L 151.604657 134.179797 \n", "L 161.328104 135.632377 \n", "L 171.051551 136.555619 \n", "L 180.774997 136.445276 \n", "L 190.498444 137.847277 \n", "L 200.221891 137.880131 \n", "L 209.945338 139.308996 \n", "L 219.668785 139.5 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 49.633125 91.606167 \n", "L 69.163125 110.776801 \n", "L 88.693125 113.926611 \n", "L 108.223125 117.731746 \n", "L 127.753125 114.744947 \n", "L 147.283125 126.913654 \n", "L 166.813125 126.28416 \n", "L 186.343125 128.342521 \n", "L 205.873125 131.108597 \n", "L 225.403125 131.019117 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_127\">\n", "    <path d=\"M 49.633125 51.82662 \n", "L 69.163125 44.057669 \n", "L 88.693125 43.829915 \n", "L 108.223125 44.766238 \n", "L 127.753125 47.018475 \n", "L 147.283125 40.691967 \n", "L 166.813125 40.135235 \n", "L 186.343125 40.464213 \n", "L 205.873125 39.27483 \n", "L 225.403125 38.768709 \n", "\" clip-path=\"url(#p6e910fad84)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 100.434375 \n", "L 218.403125 100.434375 \n", "Q 220.403125 100.434375 220.403125 98.434375 \n", "L 220.403125 54.565625 \n", "Q 220.403125 52.565625 218.403125 52.565625 \n", "L 138.8125 52.565625 \n", "Q 136.8125 52.565625 136.8125 54.565625 \n", "L 136.8125 98.434375 \n", "Q 136.8125 100.434375 138.8125 100.434375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_128\">\n", "     <path d=\"M 140.8125 60.664063 \n", "L 150.8125 60.664063 \n", "L 160.8125 60.664063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 64.164063) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_129\">\n", "     <path d=\"M 140.8125 75.620313 \n", "L 150.8125 75.620313 \n", "L 160.8125 75.620313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 79.120313) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_130\">\n", "     <path d=\"M 140.8125 90.576563 \n", "L 150.8125 90.576563 \n", "L 160.8125 90.576563 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 94.076563) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p6e910fad84\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = d2l.FashionMNIST(batch_size=256)\n", "model = SoftmaxRegression(num_outputs=10, lr=0.1)\n", "trainer = d2l.Trainer(max_epochs=10)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "49386605", "metadata": {"origin_pos": 19}, "source": ["As before, this algorithm converges to a solution\n", "that is reasonably accurate,\n", "albeit this time with fewer lines of code than before.\n", "\n", "\n", "## Summary\n", "\n", "High-level APIs are very convenient at hiding from their user potentially dangerous aspects, such as numerical stability. Moreover, they allow users to design models concisely with very few lines of code. This is both a blessing and a curse. The obvious benefit is that it makes things highly accessible, even to engineers who never took a single class of statistics in their life (in fact, they are part of the target audience of the book). But hiding the sharp edges also comes with a price: a disincentive to add new and different components on your own, since there is little muscle memory for doing it. Moreover, it makes it more difficult to *fix* things whenever the protective padding of\n", "a framework fails to cover all the corner cases entirely. Again, this is due to lack of familiarity.\n", "\n", "As such, we strongly urge you to review *both* the bare bones and the elegant versions of many of the implementations that follow. While we emphasize ease of understanding, the implementations are nonetheless usually quite performant (convolutions are the big exception here). It is our intention to allow you to build on these when you invent something new that no framework can give you.\n", "\n", "\n", "## Exercises\n", "\n", "1. Deep learning uses many different number formats, including FP64 double precision (used extremely rarely),\n", "FP32 single precision, BFLOAT16 (good for compressed representations), FP16 (very unstable), TF32 (a new format from NVIDIA), and INT8. Compute the smallest and largest argument of the exponential function for which the result does not lead to numerical underflow or overflow.\n", "1. INT8 is a very limited format consisting of nonzero numbers from $1$ to $255$. How could you extend its dynamic range without using more bits? Do standard multiplication and addition still work?\n", "1. Increase the number of epochs for training. Why might the validation accuracy decrease after a while? How could we fix this?\n", "1. What happens as you increase the learning rate? Compare the loss curves for several learning rates. Which one works better? When?\n"]}, {"cell_type": "markdown", "id": "3884ebcb", "metadata": {"origin_pos": 21, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/53)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
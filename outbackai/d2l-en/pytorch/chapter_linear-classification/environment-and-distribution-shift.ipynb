{"cells": [{"cell_type": "markdown", "id": "9dd4d342", "metadata": {"origin_pos": 0}, "source": ["# Environment and Distribution Shift\n", ":label:`sec_environment-and-distribution-shift`\n", "\n", "In the previous sections, we worked through\n", "a number of hands-on applications of machine learning,\n", "fitting models to a variety of datasets.\n", "And yet, we never stopped to contemplate\n", "either where data came from in the first place\n", "or what we ultimately plan to do\n", "with the outputs from our models.\n", "Too often, machine learning developers\n", "in possession of data rush to develop models\n", "without pausing to consider these fundamental issues.\n", "\n", "Many failed machine learning deployments\n", "can be traced back to this failure.\n", "Sometimes models appear to perform marvelously\n", "as measured by test set accuracy\n", "but fail catastrophically in deployment\n", "when the distribution of data suddenly shifts.\n", "More insidiously, sometimes the very deployment of a model\n", "can be the catalyst that perturbs the data distribution.\n", "Say, for example, that we trained a model\n", "to predict who will repay rather than default on a loan,\n", "finding that an applicant's choice of footwear\n", "was associated with the risk of default\n", "(Oxfords indicate repayment, sneakers indicate default).\n", "We might be inclined \n", "thereafter to grant a loan\n", "to any applicant wearing Oxfords\n", "and to deny all applicants wearing sneakers.\n", "\n", "In this case, our ill-considered leap from\n", "pattern recognition to decision-making\n", "and our failure to critically consider the environment\n", "might have disastrous consequences.\n", "For starters, as soon as we began\n", "making decisions based on footwear,\n", "customers would catch on and change their behavior.\n", "Before long, all applicants would be wearing Oxfords,\n", "without any coincident improvement in credit-worthiness.\n", "Take a minute to digest this because similar issues abound\n", "in many applications of machine learning:\n", "by introducing our model-based decisions to the environment,\n", "we might break the model.\n", "\n", "While we cannot possibly give these topics\n", "a complete treatment in one section,\n", "we aim here to expose some common concerns,\n", "and to stimulate the critical thinking\n", "required to detect such situations early,\n", "mitigate damage, and use machine learning responsibly.\n", "Some of the solutions are simple\n", "(ask for the \"right\" data),\n", "some are technically difficult\n", "(implement a reinforcement learning system),\n", "and others require that we step outside the realm of\n", "statistical prediction altogether and\n", "grapple with difficult philosophical questions\n", "concerning the ethical application of algorithms.\n", "\n", "## Types of Distribution Shift\n", "\n", "To begin, we stick with the passive prediction setting\n", "considering the various ways that data distributions might shift\n", "and what might be done to salvage model performance.\n", "In one classic setup, we assume that our training data\n", "was sampled from some distribution $p_S(\\mathbf{x},y)$\n", "but that our test data will consist\n", "of unlabeled examples drawn from\n", "some different distribution $p_T(\\mathbf{x},y)$.\n", "Already, we must confront a sobering reality.\n", "Absent any assumptions on how $p_S$\n", "and $p_T$ relate to each other,\n", "learning a robust classifier is impossible.\n", "\n", "Consider a binary classification problem,\n", "where we wish to distinguish between dogs and cats.\n", "If the distribution can shift in arbitrary ways,\n", "then our setup permits the pathological case\n", "in which the distribution over inputs remains\n", "constant: $p_S(\\mathbf{x}) = p_T(\\mathbf{x})$,\n", "but the labels are all flipped:\n", "$p_S(y \\mid \\mathbf{x}) = 1 - p_T(y \\mid \\mathbf{x})$.\n", "In other words, if God can suddenly decide\n", "that in the future all \"cats\" are now dogs\n", "and what we previously called \"dogs\" are now cats---without\n", "any change in the distribution of inputs $p(\\mathbf{x})$,\n", "then we cannot possibly distinguish this setting\n", "from one in which the distribution did not change at all.\n", "\n", "Fortunately, under some restricted assumptions\n", "on the ways our data might change in the future,\n", "principled algorithms can detect shift\n", "and sometimes even adapt on the fly,\n", "improving on the accuracy of the original classifier.\n", "\n", "### Covariate Shift\n", "\n", "Among categories of distribution shift,\n", "covariate shift may be the most widely studied.\n", "Here, we assume that while the distribution of inputs\n", "may change over time, the labeling function,\n", "i.e., the conditional distribution\n", "$P(y \\mid \\mathbf{x})$ does not change.\n", "Statisticians call this *covariate shift*\n", "because the problem arises due to a\n", "shift in the distribution of the covariates (features).\n", "While we can sometimes reason about distribution shift\n", "without invoking causality, we note that covariate shift\n", "is the natural assumption to invoke in settings\n", "where we believe that $\\mathbf{x}$ causes $y$.\n", "\n", "Consider the challenge of distinguishing cats and dogs.\n", "Our training data might consist of images of the kind in :numref:`fig_cat-dog-train`.\n", "\n", "![Training data for distinguishing cats and dogs (illustrations: <PERSON><PERSON><PERSON> / 500px / Getty Images; ilkermetinkursova / iStock / Getty Images Plus; GlobalP / iStock / Getty Images Plus; Musthafa Aboobakuru / 500px / Getty Images).](../img/cat-dog-train.png)\n", ":label:`fig_cat-dog-train`\n", "\n", "\n", "At test time we are asked to classify the images in :numref:`fig_cat-dog-test`.\n", "\n", "![Test data for distinguishing cats and dogs (illustrations: SIBAS_minich / iStock / Getty Images Plus; Ghrzuzudu / iStock / Getty Images Plus; id-work / DigitalVision Vectors / Getty Images; Yime / iStock / Getty Images Plus).](../img/cat-dog-test.png)\n", ":label:`fig_cat-dog-test`\n", "\n", "The training set consists of photos,\n", "while the test set contains only cartoons.\n", "Training on a dataset with substantially different\n", "characteristics from the test set\n", "can spell trouble absent a coherent plan\n", "for how to adapt to the new domain.\n", "\n", "### Label Shift\n", "\n", "*Label shift* describes the converse problem.\n", "Here, we assume that the label marginal $P(y)$\n", "can change\n", "but the class-conditional distribution\n", "$P(\\mathbf{x} \\mid y)$ remains fixed across domains.\n", "Label shift is a reasonable assumption to make\n", "when we believe that $y$ causes $\\mathbf{x}$.\n", "For example, we may want to predict diagnoses\n", "given their symptoms (or other manifestations),\n", "even as the relative prevalence of diagnoses\n", "are changing over time.\n", "Label shift is the appropriate assumption here\n", "because diseases cause symptoms.\n", "In some degenerate cases the label shift\n", "and covariate shift assumptions can hold simultaneously.\n", "For example, when the label is deterministic,\n", "the covariate shift assumption will be satisfied,\n", "even when $y$ causes $\\mathbf{x}$.\n", "Interestingly, in these cases,\n", "it is often advantageous to work with methods\n", "that flow from the label shift assumption.\n", "That is because these methods tend\n", "to involve manipulating objects that look like labels (often low-dimensional),\n", "as opposed to objects that look like inputs,\n", "which tend to be high-dimensional in deep learning.\n", "\n", "### Concept Shift\n", "\n", "We may also encounter the related problem of *concept shift*,\n", "which arises when the very definitions of labels can change.\n", "This sounds weird---a *cat* is a *cat*, no?\n", "However, other categories are subject to changes in usage over time.\n", "Diagnostic criteria for mental illness,\n", "what passes for fashionable, and job titles,\n", "are all subject to considerable\n", "amounts of concept shift.\n", "It turns out that if we navigate around the United States,\n", "shifting the source of our data by geography,\n", "we will find considerable concept shift regarding\n", "the distribution of names for *soft drinks*\n", "as shown in :numref:`fig_popvssoda`.\n", "\n", "![Concept shift for soft drink names in the United States (CC-BY: <PERSON>, PopVsSoda.com).](../img/popvssoda.png)\n", ":width:`400px`\n", ":label:`fig_popvssoda`\n", "\n", "If we were to build a machine translation system,\n", "the distribution $P(y \\mid \\mathbf{x})$ might be different\n", "depending on our location.\n", "This problem can be tricky to spot.\n", "We might hope to exploit knowledge\n", "that shift only takes place gradually\n", "either in a temporal or geographic sense.\n", "\n", "## Examples of Distribution Shift\n", "\n", "Before delving into formalism and algorithms,\n", "we can discuss some concrete situations\n", "where covariate or concept shift might not be obvious.\n", "\n", "\n", "### Medical Diagnostics\n", "\n", "Imagine that you want to design an algorithm to detect cancer.\n", "You collect data from healthy and sick people\n", "and you train your algorithm.\n", "It works fine, giving you high accuracy\n", "and you conclude that you are ready\n", "for a successful career in medical diagnostics.\n", "*Not so fast.*\n", "\n", "The distributions that gave rise to the training data\n", "and those you will encounter in the wild might differ considerably.\n", "This happened to an unfortunate startup\n", "that some of we authors worked with years ago.\n", "They were developing a blood test for a disease\n", "that predominantly affects older men\n", "and hoped to study it using blood samples\n", "that they had collected from patients.\n", "However, it is considerably more difficult\n", "to obtain blood samples from healthy men\n", "than from sick patients already in the system.\n", "To compensate, the startup solicited\n", "blood donations from students on a university campus\n", "to serve as healthy controls in developing their test.\n", "Then they asked whether we could help them\n", "to build a classifier for detecting the disease.\n", "\n", "As we explained to them,\n", "it would indeed be easy to distinguish\n", "between the healthy and sick cohorts\n", "with near-perfect accuracy.\n", "However, that is because the test subjects\n", "differed in age, hormone levels,\n", "physical activity, diet, alcohol consumption,\n", "and many more factors unrelated to the disease.\n", "This was unlikely to be the case with real patients.\n", "Due to their sampling procedure,\n", "we could expect to encounter extreme covariate shift.\n", "Moreover, this case was unlikely to be\n", "correctable via conventional methods.\n", "In short, they wasted a significant sum of money.\n", "\n", "\n", "\n", "### Self-Driving Cars\n", "\n", "Say a company wanted to leverage machine learning\n", "for developing self-driving cars.\n", "One key component here is a roadside detector.\n", "Since real annotated data is expensive to get,\n", "they had the (smart and questionable) idea\n", "to use synthetic data from a game rendering engine\n", "as additional training data.\n", "This worked really well on \"test data\"\n", "drawn from the rendering engine.\n", "<PERSON><PERSON>, inside a real car it was a disaster.\n", "As it turned out, the roadside had been rendered\n", "with a very simplistic texture.\n", "More importantly, *all* the roadside had been rendered\n", "with the *same* texture and the roadside detector\n", "learned about this \"feature\" very quickly.\n", "\n", "A similar thing happened to the US Army\n", "when they first tried to detect tanks in the forest.\n", "They took aerial photographs of the forest without tanks,\n", "then drove the tanks into the forest\n", "and took another set of pictures.\n", "The classifier appeared to work *perfectly*.\n", "Unfortunately, it had merely learned\n", "how to distinguish trees with shadows\n", "from trees without shadows---the first set\n", "of pictures was taken in the early morning,\n", "the second set at noon.\n", "\n", "### Nonstationary Distributions\n", "\n", "A much more subtle situation arises\n", "when the distribution changes slowly\n", "(also known as *nonstationary distribution*)\n", "and the model is not updated adequately.\n", "Below are some typical cases.\n", "\n", "* We train a computational advertising model and then fail to update it frequently (e.g., we forget to incorporate that an obscure new device called an iPad was just launched).\n", "* We build a spam filter. It works well at detecting all spam that we have seen so far. But then the spammers wise up and craft new messages that look unlike anything we have seen before.\n", "* We build a product recommendation system. It works throughout the winter but then continues to recommend Santa hats long after Christmas.\n", "\n", "### More Anecdotes\n", "\n", "* We build a face detector. It works well on all benchmarks. Unfortunately it fails on test data---the offending examples are close-ups where the face fills the entire image (no such data was in the training set).\n", "* We build a web search engine for the US market and want to deploy it in the UK.\n", "* We train an image classifier by compiling a large dataset where each among a large set of classes is equally represented in the dataset, say 1000 categories, represented by 1000 images each. Then we deploy the system in the real world, where the actual label distribution of photographs is decidedly non-uniform.\n", "\n", "\n", "\n", "\n", "\n", "\n", "## Correction of Distribution Shift\n", "\n", "As we have discussed, there are many cases\n", "where training and test distributions\n", "$P(\\mathbf{x}, y)$ are different.\n", "In some cases, we get lucky and the models work\n", "despite covariate, label, or concept shift.\n", "In other cases, we can do better by employing\n", "principled strategies to cope with the shift.\n", "The remainder of this section grows considerably more technical.\n", "The impatient reader could continue on to the next section\n", "as this material is not prerequisite to subsequent concepts.\n", "\n", "### Empirical Risk and  Risk\n", ":label:`subsec_empirical-risk-and-risk`\n", "\n", "Let's first reflect on what exactly\n", "is happening during model training:\n", "we iterate over features and associated labels\n", "of training data\n", "$\\{(\\mathbf{x}_1, y_1), \\ldots, (\\mathbf{x}_n, y_n)\\}$\n", "and update the parameters of a model $f$ after every minibatch.\n", "For simplicity we do not consider regularization,\n", "so we largely minimize the loss on the training:\n", "\n", "$$\\mathop{\\mathrm{minimize}}_f \\frac{1}{n} \\sum_{i=1}^n l(f(\\mathbf{x}_i), y_i),$$\n", ":eqlabel:`eq_empirical-risk-min`\n", "\n", "where $l$ is the loss function\n", "measuring \"how bad\" the prediction $f(\\mathbf{x}_i)$ is given the associated label $y_i$.\n", "Statisticians call the term in :eqref:`eq_empirical-risk-min` *empirical risk*.\n", "The *empirical risk* is an average loss over the training data\n", "for approximating the *risk*,\n", "which is the\n", "expectation of the loss over the entire population of data drawn from their true distribution\n", "$p(\\mathbf{x},y)$:\n", "\n", "$$E_{p(\\mathbf{x}, y)} [l(f(\\mathbf{x}), y)] = \\int\\int l(f(\\mathbf{x}), y) p(\\mathbf{x}, y) \\;d\\mathbf{x}dy.$$\n", ":eqlabel:`eq_true-risk`\n", "\n", "However, in practice we typically cannot obtain the entire population of data.\n", "Thus, *empirical risk minimization*,\n", "which is minimizing the empirical risk in :eqref:`eq_empirical-risk-min`,\n", "is a practical strategy for machine learning,\n", "with the hope of approximately\n", "minimizing the risk.\n", "\n", "\n", "\n", "### Covariate Shift Correction\n", ":label:`subsec_covariate-shift-correction`\n", "\n", "Assume that we want to estimate\n", "some dependency $P(y \\mid \\mathbf{x})$\n", "for which we have labeled data $(\\mathbf{x}_i, y_i)$.\n", "Unfortunately, the observations $\\mathbf{x}_i$ are drawn\n", "from some *source distribution* $q(\\mathbf{x})$\n", "rather than the *target distribution* $p(\\mathbf{x})$.\n", "Fortunately,\n", "the dependency assumption means\n", "that the conditional distribution does not change: $p(y \\mid \\mathbf{x}) = q(y \\mid \\mathbf{x})$.\n", "If the source distribution $q(\\mathbf{x})$ is \"wrong\",\n", "we can correct for that by using the following simple identity in the risk:\n", "\n", "$$\n", "\\begin{aligned}\n", "\\int\\int l(f(\\mathbf{x}), y) p(y \\mid \\mathbf{x})p(\\mathbf{x}) \\;d\\mathbf{x}dy =\n", "\\int\\int l(f(\\mathbf{x}), y) q(y \\mid \\mathbf{x})q(\\mathbf{x})\\frac{p(\\mathbf{x})}{q(\\mathbf{x})} \\;d\\mathbf{x}dy.\n", "\\end{aligned}\n", "$$\n", "\n", "In other words, we need to reweigh each data example\n", "by the ratio of the\n", "probability\n", "that it would have been drawn from the correct distribution to that from the wrong one:\n", "\n", "$$\\beta_i \\stackrel{\\textrm{def}}{=} \\frac{p(\\mathbf{x}_i)}{q(\\mathbf{x}_i)}.$$\n", "\n", "Plugging in the weight $\\beta_i$ for\n", "each data example $(\\mathbf{x}_i, y_i)$\n", "we can train our model using\n", "*weighted empirical risk minimization*:\n", "\n", "$$\\mathop{\\mathrm{minimize}}_f \\frac{1}{n} \\sum_{i=1}^n \\beta_i l(f(\\mathbf{x}_i), y_i).$$\n", ":eqlabel:`eq_weighted-empirical-risk-min`\n", "\n", "\n", "\n", "Alas, we do not know that ratio,\n", "so before we can do anything useful we need to estimate it.\n", "Many methods are available,\n", "including some fancy operator-theoretic approaches\n", "that attempt to recalibrate the expectation operator directly\n", "using a minimum-norm or a maximum entropy principle.\n", "Note that for any such approach, we need samples\n", "drawn from both distributions---the \"true\" $p$, e.g.,\n", "by access to test data, and the one used\n", "for generating the training set $q$ (the latter is trivially available).\n", "Note however, that we only need features $\\mathbf{x} \\sim p(\\mathbf{x})$;\n", "we do not need to access labels $y \\sim p(y)$.\n", "\n", "In this case, there exists a very effective approach\n", "that will give almost as good results as the original: namely, logistic regression,\n", "which is a special case of softmax regression (see :numref:`sec_softmax`)\n", "for binary classification.\n", "This is all that is needed to compute estimated probability ratios.\n", "We learn a classifier to distinguish\n", "between data drawn from $p(\\mathbf{x})$\n", "and data drawn from $q(\\mathbf{x})$.\n", "If it is impossible to distinguish\n", "between the two distributions\n", "then it means that the associated instances\n", "are equally likely to come from\n", "either one of those two distributions.\n", "On the other hand, any instances\n", "that can be well discriminated\n", "should be significantly overweighted\n", "or underweighted accordingly.\n", "\n", "For simplicity's sake assume that we have\n", "an equal number of instances from both distributions\n", "$p(\\mathbf{x})$\n", "and $q(\\mathbf{x})$, respectively.\n", "Now denote by $z$ labels that are $1$\n", "for data drawn from $p$ and $-1$ for data drawn from $q$.\n", "Then the probability in a mixed dataset is given by\n", "\n", "$$P(z=1 \\mid \\mathbf{x}) = \\frac{p(\\mathbf{x})}{p(\\mathbf{x})+q(\\mathbf{x})} \\textrm{ and hence } \\frac{P(z=1 \\mid \\mathbf{x})}{P(z=-1 \\mid \\mathbf{x})} = \\frac{p(\\mathbf{x})}{q(\\mathbf{x})}.$$\n", "\n", "Thus, if we use a logistic regression approach,\n", "where $P(z=1 \\mid \\mathbf{x})=\\frac{1}{1+\\exp(-h(\\mathbf{x}))}$ ($h$ is a parametrized function),\n", "it follows that\n", "\n", "$$\n", "\\beta_i = \\frac{1/(1 + \\exp(-h(\\mathbf{x}_i)))}{\\exp(-h(\\mathbf{x}_i))/(1 + \\exp(-h(\\mathbf{x}_i)))} = \\exp(h(\\mathbf{x}_i)).\n", "$$\n", "\n", "As a result, we need to solve two problems:\n", "the first, to distinguish between\n", "data drawn from both distributions,\n", "and then a weighted empirical risk minimization problem\n", "in :eqref:`eq_weighted-empirical-risk-min`\n", "where we weigh terms by $\\beta_i$.\n", "\n", "Now we are ready to describe a correction algorithm.\n", "Suppose that we have a training set $\\{(\\mathbf{x}_1, y_1), \\ldots, (\\mathbf{x}_n, y_n)\\}$ and an unlabeled test set $\\{\\mathbf{u}_1, \\ldots, \\mathbf{u}_m\\}$.\n", "For covariate shift,\n", "we assume that $\\mathbf{x}_i$ for all $1 \\leq i \\leq n$ are drawn from some source distribution\n", "and $\\mathbf{u}_i$ for all $1 \\leq i \\leq m$\n", "are drawn from the target distribution.\n", "Here is a prototypical algorithm\n", "for correcting covariate shift:\n", "\n", "1. Create a binary-classification training set: $\\{(\\mathbf{x}_1, -1), \\ldots, (\\mathbf{x}_n, -1), (\\mathbf{u}_1, 1), \\ldots, (\\mathbf{u}_m, 1)\\}$.\n", "1. Train a binary classifier using logistic regression to get the function $h$.\n", "1. Weigh training data using $\\beta_i = \\exp(h(\\mathbf{x}_i))$ or better $\\beta_i = \\min(\\exp(h(\\mathbf{x}_i)), c)$ for some constant $c$.\n", "1. Use weights $\\beta_i$ for training on $\\{(\\mathbf{x}_1, y_1), \\ldots, (\\mathbf{x}_n, y_n)\\}$ in :eqref:`eq_weighted-empirical-risk-min`.\n", "\n", "Note that the above algorithm relies on a crucial assumption.\n", "For this scheme to work, we need that each data example\n", "in the target (e.g., test time) distribution\n", "had nonzero probability of occurring at training time.\n", "If we find a point where $p(\\mathbf{x}) > 0$ but $q(\\mathbf{x}) = 0$,\n", "then the corresponding importance weight should be infinity.\n", "\n", "\n", "\n", "\n", "\n", "\n", "### Label Shift Correction\n", "\n", "Assume that we are dealing with a\n", "classification task with $k$ categories.\n", "Using the same notation in :numref:`subsec_covariate-shift-correction`,\n", "$q$ and $p$ are the source distribution (e.g., training time) and target distribution (e.g., test time), respectively.\n", "Assume that the distribution of labels shifts over time:\n", "$q(y) \\neq p(y)$, but the class-conditional distribution\n", "stays the same: $q(\\mathbf{x} \\mid y)=p(\\mathbf{x} \\mid y)$.\n", "If the source distribution $q(y)$ is \"wrong\",\n", "we can correct for that\n", "according to\n", "the following identity in the risk\n", "as defined in\n", ":eqref:`eq_true-risk`:\n", "\n", "$$\n", "\\begin{aligned}\n", "\\int\\int l(f(\\mathbf{x}), y) p(\\mathbf{x} \\mid y)p(y) \\;d\\mathbf{x}dy =\n", "\\int\\int l(f(\\mathbf{x}), y) q(\\mathbf{x} \\mid y)q(y)\\frac{p(y)}{q(y)} \\;d\\mathbf{x}dy.\n", "\\end{aligned}\n", "$$\n", "\n", "\n", "\n", "Here, our importance weights will correspond to the\n", "label likelihood ratios:\n", "\n", "$$\\beta_i \\stackrel{\\textrm{def}}{=} \\frac{p(y_i)}{q(y_i)}.$$\n", "\n", "One nice thing about label shift is that\n", "if we have a reasonably good model\n", "on the source distribution,\n", "then we can get consistent estimates of these weights\n", "without ever having to deal with the ambient dimension.\n", "In deep learning, the inputs tend\n", "to be high-dimensional objects like images,\n", "while the labels are often simpler objects like categories.\n", "\n", "To estimate the target label distribution,\n", "we first take our reasonably good off-the-shelf classifier\n", "(typically trained on the training data)\n", "and compute its \"confusion\" matrix using the validation set\n", "(also from the training distribution).\n", "The *confusion matrix*, $\\mathbf{C}$, is simply a $k \\times k$ matrix,\n", "where each column corresponds to the label category (ground truth)\n", "and each row corresponds to our model's predicted category.\n", "Each cell's value $c_{ij}$ is the fraction of total predictions on the validation set\n", "where the true label was $j$ and our model predicted $i$.\n", "\n", "Now, we cannot calculate the confusion matrix\n", "on the target data directly\n", "because we do not get to see the labels for the examples\n", "that we see in the wild,\n", "unless we invest in a complex real-time annotation pipeline.\n", "What we can do, however, is average all of our model's predictions\n", "at test time together, yielding the mean model outputs $\\mu(\\hat{\\mathbf{y}}) \\in \\mathbb{R}^k$,\n", "where the $i^\\textrm{th}$ element $\\mu(\\hat{y}_i)$\n", "is the fraction of the total predictions on the test set\n", "where our model predicted $i$.\n", "\n", "It turns out that under some mild conditions---if\n", "our classifier was reasonably accurate in the first place,\n", "and if the target data contains only categories\n", "that we have seen before,\n", "and if the label shift assumption holds in the first place\n", "(the strongest assumption here)---we can estimate the test set label distribution\n", "by solving a simple linear system\n", "\n", "$$\\mathbf{C} p(\\mathbf{y}) = \\mu(\\hat{\\mathbf{y}}),$$\n", "\n", "because as an estimate $\\sum_{j=1}^k c_{ij} p(y_j) = \\mu(\\hat{y}_i)$ holds for all $1 \\leq i \\leq k$,\n", "where $p(y_j)$ is the $j^\\textrm{th}$ element of the $k$-dimensional label distribution vector $p(\\mathbf{y})$.\n", "If our classifier is sufficiently accurate to begin with,\n", "then the confusion matrix $\\mathbf{C}$ will be invertible,\n", "and we get a solution $p(\\mathbf{y}) = \\mathbf{C}^{-1} \\mu(\\hat{\\mathbf{y}})$.\n", "\n", "Because we observe the labels on the source data,\n", "it is easy to estimate the distribution $q(y)$.\n", "Then, for any training example $i$ with label $y_i$,\n", "we can take the ratio of our estimated $p(y_i)/q(y_i)$\n", "to calculate the weight $\\beta_i$,\n", "and plug this into weighted empirical risk minimization\n", "in :eqref:`eq_weighted-empirical-risk-min`.\n", "\n", "\n", "### Concept Shift Correction\n", "\n", "Concept shift is much harder to fix in a principled manner.\n", "For instance, in a situation where suddenly the problem changes\n", "from distinguishing cats from dogs to one of\n", "distinguishing white from black animals,\n", "it will be unreasonable to assume\n", "that we can do much better than just collecting new labels\n", "and training from scratch.\n", "Fortunately, in practice, such extreme shifts are rare.\n", "Instead, what usually happens is that the task keeps on changing slowly.\n", "To make things more concrete, here are some examples:\n", "\n", "* In computational advertising, new products are launched,\n", "old products become less popular. This means that the distribution over ads and their popularity changes gradually and any click-through rate predictor needs to change gradually with it.\n", "* Traffic camera lenses degrade gradually due to environmental wear, affecting image quality progressively.\n", "* News content changes gradually (i.e., most of the news remains unchanged but new stories appear).\n", "\n", "In such cases, we can use the same approach that we used for training networks to make them adapt to the change in the data. In other words, we use the existing network weights and simply perform a few update steps with the new data rather than training from scratch.\n", "\n", "\n", "## A Taxonomy of Learning Problems\n", "\n", "Armed with knowledge about how to deal with changes in distributions, we can now consider some other aspects of machine learning problem formulation.\n", "\n", "\n", "### Bat<PERSON> Learning\n", "\n", "In *batch learning*, we have access to training features and labels $\\{(\\mathbf{x}_1, y_1), \\ldots, (\\mathbf{x}_n, y_n)\\}$, which we use to train a model $f(\\mathbf{x})$. Later on, we deploy this model to score new data $(\\mathbf{x}, y)$ drawn from the same distribution. This is the default assumption for any of the problems that we discuss here. For instance, we might train a cat detector based on lots of pictures of cats and dogs. Once we have trained it, we ship it as part of a smart catdoor computer vision system that lets only cats in. This is then installed in a customer's home and is never updated again (barring extreme circumstances).\n", "\n", "\n", "### Online Learning\n", "\n", "Now imagine that the data $(\\mathbf{x}_i, y_i)$ arrives one sample at a time. More specifically, assume that we first observe $\\mathbf{x}_i$, then we need to come up with an estimate $f(\\mathbf{x}_i)$. Only once we have done this do we observe $y_i$ and so receive a reward or incur a loss, given our decision.\n", "Many real problems fall into this category. For example, we need to predict tomorrow's stock price, which allows us to trade based on that estimate and at the end of the day we find out whether our estimate made us a profit. In other words, in *online learning*, we have the following cycle where we are continuously improving our model given new observations:\n", "\n", "$$\\begin{aligned}&\\textrm{model } f_t \\longrightarrow \\textrm{data }  \\mathbf{x}_t \\longrightarrow \\textrm{estimate } f_t(\\mathbf{x}_t) \\longrightarrow\\\\ \\textrm{obs}&\\textrm{ervation } y_t \\longrightarrow \\textrm{loss } l(y_t, f_t(\\mathbf{x}_t)) \\longrightarrow \\textrm{model } f_{t+1}\\end{aligned}$$\n", "\n", "### Bandits\n", "\n", "*Bandits* are a special case of the problem above. While in most learning problems we have a continuously parametrized function $f$ where we want to learn its parameters (e.g., a deep network), in a *bandit* problem we only have a finite number of arms that we can pull, i.e., a finite number of actions that we can take. It is not very surprising that for this simpler problem stronger theoretical guarantees in terms of optimality can be obtained. We list it mainly since this problem is often (confusingly) treated as if it were a distinct learning setting.\n", "\n", "\n", "### Control\n", "\n", "In many cases the environment remembers what we did. Not necessarily in an adversarial manner but it will just remember and the response will depend on what happened before. For instance, a coffee boiler controller will observe different temperatures depending on whether it was heating the boiler previously. PID (proportional-integral-derivative) controller algorithms are a popular choice there.\n", "Likewise, a user's behavior on a news site will depend on what we showed them previously (e.g., they will read most news only once). Many such algorithms form a model of the environment in which they act so as to make their decisions appear less random.\n", "Recently,\n", "control theory (e.g., PID variants) has also been used\n", "to automatically tune hyperparameters\n", "to achieve better disentangling and reconstruction quality,\n", "and improve the diversity of generated text and the reconstruction quality of generated images :cite:`<PERSON>hao.Yao.Sun.ea.2020`.\n", "\n", "\n", "\n", "\n", "### Reinforcement Learning\n", "\n", "In the more general case of an environment with memory, we may encounter situations where the environment is trying to cooperate with us (cooperative games, in particular for non-zero-sum games), or others where the environment will try to win. Chess, Go, Backgammon, or StarCraft are some of the cases in *reinforcement learning*. Likewise, we might want to build a good controller for autonomous cars. Other cars are likely to respond to the autonomous car's driving style in nontrivial ways, e.g., trying to avoid it, trying to cause an accident, or trying to cooperate with it.\n", "\n", "### Considering the Environment\n", "\n", "One key distinction between the different situations above is that a strategy that might have worked throughout in the case of a stationary environment, might not work throughout in an environment that can adapt. For instance, an arbitrage opportunity discovered by a trader is likely to disappear once it is exploited. The speed and manner at which the environment changes determines to a large extent the type of algorithms that we can bring to bear. For instance, if we know that things may only change slowly, we can force any estimate to change only slowly, too. If we know that the environment might change instantaneously, but only very infrequently, we can make allowances for that. These types of knowledge are crucial for the aspiring data scientist in dealing with concept shift, i.e., when the problem that is being solved can change over time.\n", "\n", "\n", "\n", "\n", "## Fairness, Accountability, and Transparency in Machine Learning\n", "\n", "Finally, it is important to remember\n", "that when you deploy machine learning systems\n", "you are not merely optimizing a predictive model---you\n", "are typically providing a tool that will\n", "be used to (partially or fully) automate decisions.\n", "These technical systems can impact the lives\n", "of individuals who are subject to the resulting decisions.\n", "The leap from considering predictions to making decisions\n", "raises not only new technical questions,\n", "but also a slew of ethical questions\n", "that must be carefully considered.\n", "If we are deploying a medical diagnostic system,\n", "we need to know for which populations\n", "it may work and for which it may not.\n", "Overlooking foreseeable risks to the welfare of\n", "a subpopulation could cause us to administer inferior care.\n", "Moreover, once we contemplate decision-making systems,\n", "we must step back and reconsider how we evaluate our technology.\n", "Among other consequences of this change of scope,\n", "we will find that *accuracy* is seldom the right measure.\n", "For instance, when translating predictions into actions,\n", "we will often want to take into account\n", "the potential cost sensitivity of erring in various ways.\n", "If one way of misclassifying an image\n", "could be perceived as a racial sleight of hand,\n", "while misclassification to a different category\n", "would be harmless, then we might want to adjust\n", "our thresholds accordingly, accounting for societal values\n", "in designing the decision-making protocol.\n", "We also want to be careful about\n", "how prediction systems can lead to feedback loops.\n", "For example, consider predictive policing systems,\n", "which allocate patrol officers\n", "to areas with high forecasted crime.\n", "It is easy to see how a worrying pattern can emerge:\n", "\n", " 1. Neighborhoods with more crime get more patrols.\n", " 1. Consequently, more crimes are discovered in these neighborhoods, entering the training data available for future iterations.\n", " 1. Exposed to more positives, the model predicts yet more crime in these neighborhoods.\n", " 1. In the next iteration, the updated model targets the same neighborhood even more heavily leading to yet more crimes discovered, etc.\n", "\n", "Often, the various mechanisms by which\n", "a model's predictions become coupled to its training data\n", "are unaccounted for in the modeling process.\n", "This can lead to what researchers call *runaway feedback loops*.\n", "Additionally, we want to be careful about\n", "whether we are addressing the right problem in the first place.\n", "Predictive algorithms now play an outsize role\n", "in mediating the dissemination of information.\n", "Should the news that an individual encounters\n", "be determined by the set of Facebook pages they have *Liked*?\n", "These are just a few among the many pressing ethical dilemmas\n", "that you might encounter in a career in machine learning.\n", "\n", "\n", "## Summary\n", "\n", "In many cases training and test sets do not come from the same distribution. This is called distribution shift.\n", "The risk is the expectation of the loss over the entire population of data drawn from their true distribution. However, this entire population is usually unavailable. Empirical risk is an average loss over the training data to approximate the risk. In practice, we perform empirical risk minimization.\n", "\n", "Under the corresponding assumptions, covariate and label shift can be detected and corrected for at test time. Failure to account for this bias can become problematic at test time.\n", "In some cases, the environment may remember automated actions and respond in surprising ways. We must account for this possibility when building models and continue to monitor live systems, open to the possibility that our models and the environment will become entangled in unanticipated ways.\n", "\n", "## Exercises\n", "\n", "1. What could happen when we change the behavior of a search engine? What might the users do? What about the advertisers?\n", "1. Implement a covariate shift detector. Hint: build a classifier.\n", "1. Implement a covariate shift corrector.\n", "1. Besides distribution shift, what else could affect how the empirical risk approximates the risk?\n", "\n", "\n", "[Discussions](https://discuss.d2l.ai/t/105)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "e57838cd", "metadata": {"origin_pos": 1}, "source": ["# The Image Classification Dataset\n", ":label:`sec_fashion_mnist`\n", "\n", "(~~The MNIST dataset is one of the widely used dataset for image classification, while it is too simple as a benchmark dataset. We will use the similar, but more complex Fashion-MNIST dataset ~~)\n", "\n", "One widely used dataset for image classification is the  [MNIST dataset](https://en.wikipedia.org/wiki/MNIST_database) :cite:`LeCun.Bottou.Bengio.ea.1998` of handwritten digits. At the time of its release in the 1990s it posed a formidable challenge to most machine learning algorithms, consisting of 60,000 images of $28 \\times 28$ pixels resolution (plus a test dataset of 10,000 images). To put things into perspective, back in 1995, a Sun SPARCStation 5 with a whopping 64MB of RAM and a blistering 5 MFLOPs was considered state of the art equipment for machine learning at AT&T Bell Laboratories. Achieving high accuracy on digit recognition was a key component in automating letter sorting for the USPS in the 1990s. Deep networks such as LeNet-5 :cite:`LeCun.Jackel.Bottou.ea.1995`, support vector machines with invariances :cite:`Scholkopf.Burges.Vapnik.1996`, and tangent distance classifiers :cite:`Simard.LeCun.Denker.ea.1998` all could reach error rates below 1%. \n", "\n", "For over a decade, MNIST served as *the* point of reference for comparing machine learning algorithms. \n", "While it had a good run as a benchmark dataset,\n", "even simple models by today's standards achieve classification accuracy over 95%,\n", "making it unsuitable for distinguishing between strong models and weaker ones. Even more, the dataset allows for *very* high levels of accuracy, not typically seen in many classification problems. This skewed algorithmic development towards specific families of algorithms that can take advantage of clean datasets, such as active set methods and boundary-seeking active set algorithms.\n", "Today, MNIST serves as more of a sanity check than as a benchmark. ImageNet :cite:`Deng.Dong.Socher.ea.2009` poses a much \n", "more relevant challenge. Unfortunately, ImageNet is too large for many of the examples and illustrations in this book, as it would take too long to train to make the examples interactive. As a substitute we will focus our discussion in the coming sections on the qualitatively similar, but much smaller Fashion-MNIST\n", "dataset :cite:`Xiao.Rasul.Vollgraf.2017` which was released in 2017. It contains images of 10 categories of clothing at $28 \\times 28$ pixels resolution.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "270279ba", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:53.123984Z", "iopub.status.busy": "2023-08-18T19:35:53.123639Z", "iopub.status.idle": "2023-08-18T19:35:56.952902Z", "shell.execute_reply": "2023-08-18T19:35:56.951810Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["%matplotlib inline\n", "import time\n", "import torch\n", "import torchvision\n", "from torchvision import transforms\n", "from d2l import torch as d2l\n", "\n", "d2l.use_svg_display()"]}, {"cell_type": "markdown", "id": "1070bad5", "metadata": {"origin_pos": 6}, "source": ["## Loading the Dataset\n", "\n", "Since the Fashion-MNIST dataset is so useful, all major frameworks provide preprocessed versions of it. We can  [**download and read it into memory using built-in framework utilities.**]\n"]}, {"cell_type": "code", "execution_count": 2, "id": "b2e83e15", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:56.959608Z", "iopub.status.busy": "2023-08-18T19:35:56.958972Z", "iopub.status.idle": "2023-08-18T19:35:56.974091Z", "shell.execute_reply": "2023-08-18T19:35:56.969189Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["class FashionMNIST(d2l.DataModule):  #@save\n", "    \"\"\"The Fashion-MNIST dataset.\"\"\"\n", "    def __init__(self, batch_size=64, resize=(28, 28)):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        trans = transforms.Compose([transforms.Resize(resize),\n", "                                    transforms.ToTensor()])\n", "        self.train = torchvision.datasets.FashionMNIST(\n", "            root=self.root, train=True, transform=trans, download=True)\n", "        self.val = torchvision.datasets.FashionMNIST(\n", "            root=self.root, train=False, transform=trans, download=True)"]}, {"cell_type": "markdown", "id": "2ac7e84e", "metadata": {"origin_pos": 10}, "source": ["Fashion-MNIST consists of images from 10 categories, each represented\n", "by 6000 images in the training dataset and by 1000 in the test dataset.\n", "A *test dataset* is used for evaluating model performance (it must not be used for training).\n", "Consequently the training set and the test set\n", "contain 60,000 and 10,000 images, respectively.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "9702ba11", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:56.979230Z", "iopub.status.busy": "2023-08-18T19:35:56.978838Z", "iopub.status.idle": "2023-08-18T19:35:57.112651Z", "shell.execute_reply": "2023-08-18T19:35:57.111496Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["(60000, 10000)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["data = FashionMNIST(resize=(32, 32))\n", "len(data.train), len(data.val)"]}, {"cell_type": "markdown", "id": "a66cd0bb", "metadata": {"origin_pos": 13}, "source": ["The images are grayscale and upscaled to $32 \\times 32$ pixels in resolution above. This is similar to the original MNIST dataset which consisted of (binary) black and white images. Note, though, that most modern image data has three channels (red, green, blue) and that hyperspectral images can have in excess of 100 channels (the HyMap sensor has 126 channels).\n", "By convention we store an image as a $c \\times h \\times w$ tensor, where $c$ is the number of color channels, $h$ is the height and $w$ is the width.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "b31548fa", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:57.116730Z", "iopub.status.busy": "2023-08-18T19:35:57.116328Z", "iopub.status.idle": "2023-08-18T19:35:57.128533Z", "shell.execute_reply": "2023-08-18T19:35:57.127453Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([1, 32, 32])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data.train[0][0].shape"]}, {"cell_type": "markdown", "id": "b2e625e5", "metadata": {"origin_pos": 15}, "source": ["[~~Two utility functions to visualize the dataset~~]\n", "\n", "The categories of Fashion-MNIST have human-understandable names. \n", "The following convenience method converts between numeric labels and their names.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ca95ebc5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:57.137128Z", "iopub.status.busy": "2023-08-18T19:35:57.136465Z", "iopub.status.idle": "2023-08-18T19:35:57.142322Z", "shell.execute_reply": "2023-08-18T19:35:57.141204Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(FashionMNIST)  #@save\n", "def text_labels(self, indices):\n", "    \"\"\"Return text labels.\"\"\"\n", "    labels = ['t-shirt', 'trouser', 'pullover', 'dress', 'coat',\n", "              'sandal', 'shirt', 'sneaker', 'bag', 'ankle boot']\n", "    return [labels[int(i)] for i in indices]"]}, {"cell_type": "markdown", "id": "4a87f298", "metadata": {"origin_pos": 17}, "source": ["## Reading a Minibatch\n", "\n", "To make our life easier when reading from the training and test sets,\n", "we use the built-in data iterator rather than creating one from scratch.\n", "Recall that at each iteration, a data iterator\n", "[**reads a minibatch of data with size `batch_size`.**]\n", "We also randomly shuffle the examples for the training data iterator.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "8982acc7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:57.146600Z", "iopub.status.busy": "2023-08-18T19:35:57.145989Z", "iopub.status.idle": "2023-08-18T19:35:57.153720Z", "shell.execute_reply": "2023-08-18T19:35:57.150894Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(FashionMNIST)  #@save\n", "def get_dataloader(self, train):\n", "    data = self.train if train else self.val\n", "    return torch.utils.data.DataLoader(data, self.batch_size, shuffle=train,\n", "                                       num_workers=self.num_workers)"]}, {"cell_type": "markdown", "id": "f6058b32", "metadata": {"origin_pos": 21}, "source": ["To see how this works, let's load a minibatch of images by invoking the `train_dataloader` method. It contains 64 images.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "81f8afca", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:57.160131Z", "iopub.status.busy": "2023-08-18T19:35:57.159304Z", "iopub.status.idle": "2023-08-18T19:35:57.397652Z", "shell.execute_reply": "2023-08-18T19:35:57.396242Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([64, 1, 32, 32]) torch.float32 torch.<PERSON><PERSON>([64]) torch.int64\n"]}], "source": ["X, y = next(iter(data.train_dataloader()))\n", "print(X.shape, X.dtype, y.shape, y.dtype)"]}, {"cell_type": "markdown", "id": "51090d6f", "metadata": {"origin_pos": 23}, "source": ["Let's look at the time it takes to read the images. Even though it is a built-in loader, it is not blazingly fast. Nonetheless, this is sufficient since processing images with a deep network takes quite a bit longer. Hence it is good enough that training a network will not be I/O constrained.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "47e90ba5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:35:57.402928Z", "iopub.status.busy": "2023-08-18T19:35:57.402306Z", "iopub.status.idle": "2023-08-18T19:36:02.097749Z", "shell.execute_reply": "2023-08-18T19:36:02.096784Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'4.69 sec'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["tic = time.time()\n", "for X, y in data.train_dataloader():\n", "    continue\n", "f'{time.time() - tic:.2f} sec'"]}, {"cell_type": "markdown", "id": "2887996a", "metadata": {"origin_pos": 25}, "source": ["## Visualization\n", "\n", "We will often be using the Fashion-MNIST dataset. A convenience function `show_images` can be used to visualize the images and the associated labels. \n", "Skipping implementation details, we just show the interface below: we only need to know how to invoke `d2l.show_images` rather than how it works\n", "for such utility functions.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "06fb4e72", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:02.101877Z", "iopub.status.busy": "2023-08-18T19:36:02.101254Z", "iopub.status.idle": "2023-08-18T19:36:02.105863Z", "shell.execute_reply": "2023-08-18T19:36:02.105019Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [], "source": ["def show_images(imgs, num_rows, num_cols, titles=None, scale=1.5):  #@save\n", "    \"\"\"Plot a list of images.\"\"\"\n", "    raise NotImplementedError"]}, {"cell_type": "markdown", "id": "2e35cb97", "metadata": {"origin_pos": 27}, "source": ["Let's put it to good use. In general, it is a good idea to visualize and inspect data that you are training on. \n", "Humans are very good at spotting oddities and because of that, visualization serves as an additional safeguard against mistakes and errors in the design of experiments. Here are [**the images and their corresponding labels**] (in text)\n", "for the first few examples in the training dataset.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "b16ddca3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:36:02.109988Z", "iopub.status.busy": "2023-08-18T19:36:02.109439Z", "iopub.status.idle": "2023-08-18T19:36:02.819862Z", "shell.execute_reply": "2023-08-18T19:36:02.811142Z"}, "origin_pos": 28, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"684pt\" height=\"100.752168pt\" viewBox=\"0 0 684 100.752168\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:36:02.720056</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 100.752168 \n", "L 684 100.752168 \n", "L 684 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 7.2 93.552168 \n", "L 78.434043 93.552168 \n", "L 78.434043 22.318125 \n", "L 7.2 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p146ff0468f)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image60308847a8\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"7.2\" y=\"-22.272168\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 7.2 93.552168 \n", "L 7.2 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 78.434043 93.552168 \n", "L 78.434043 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 7.2 93.552168 \n", "L 78.434043 93.552168 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 7.2 22.318125 \n", "L 78.434043 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_1\">\n", "    <!-- ankle boot -->\n", "    <g transform=\"translate(11.093896 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"61.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-6b\" x=\"124.658203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"182.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"210.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"271.875\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"303.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"367.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"428.320312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"489.501953\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 92.680851 93.552168 \n", "L 163.914894 93.552168 \n", "L 163.914894 22.318125 \n", "L 92.680851 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p574a4dd0cd)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image75cdb7c454\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"92.680851\" y=\"-22.272168\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 92.680851 93.552168 \n", "L 92.680851 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 163.914894 93.552168 \n", "L 163.914894 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 92.680851 93.552168 \n", "L 163.914894 93.552168 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 92.680851 22.318125 \n", "L 163.914894 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_2\">\n", "    <!-- pullover -->\n", "    <g transform=\"translate(103.971622 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-70\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"126.855469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"182.421875\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"243.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.783203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"364.306641\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 178.161702 93.552168 \n", "L 249.395745 93.552168 \n", "L 249.395745 22.318125 \n", "L 178.161702 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pfe2618d79d)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imageeb08b50789\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"178.161702\" y=\"-22.272168\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 178.161702 93.552168 \n", "L 178.161702 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 249.395745 93.552168 \n", "L 249.395745 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 178.161702 93.552168 \n", "L 249.395745 93.552168 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 178.161702 22.318125 \n", "L 249.395745 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_3\">\n", "    <!-- trouser -->\n", "    <g transform=\"translate(192.337161 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 263.642553 93.552168 \n", "L 334.876596 93.552168 \n", "L 334.876596 22.318125 \n", "L 263.642553 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pc7d04c5026)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagea978749efe\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"263.642553\" y=\"-22.272168\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 263.642553 93.552168 \n", "L 263.642553 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 334.876596 93.552168 \n", "L 334.876596 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 263.642553 93.552168 \n", "L 334.876596 93.552168 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 263.642553 22.318125 \n", "L 334.876596 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_4\">\n", "    <!-- trouser -->\n", "    <g transform=\"translate(277.818012 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 349.123404 93.552168 \n", "L 420.357447 93.552168 \n", "L 420.357447 22.318125 \n", "L 349.123404 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p88e3b890ce)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imagec23657c2df\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"349.123404\" y=\"-22.272168\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 349.123404 93.552168 \n", "L 349.123404 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 420.357447 93.552168 \n", "L 420.357447 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 349.123404 93.552168 \n", "L 420.357447 93.552168 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 349.123404 22.318125 \n", "L 420.357447 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_5\">\n", "    <!-- shirt -->\n", "    <g transform=\"translate(371.326676 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"143.261719\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"184.375\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 434.604255 93.552168 \n", "L 505.838298 93.552168 \n", "L 505.838298 22.318125 \n", "L 434.604255 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p0f49cb0c91)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image6fa8d0df2f\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"434.604255\" y=\"-22.272168\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 434.604255 93.552168 \n", "L 434.604255 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 505.838298 93.552168 \n", "L 505.838298 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 434.604255 93.552168 \n", "L 505.838298 93.552168 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 434.604255 22.318125 \n", "L 505.838298 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- trouser -->\n", "    <g transform=\"translate(448.779714 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-74\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"78.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"139.253906\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"202.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"254.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"316.255859\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_7\">\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 520.085106 93.552168 \n", "L 591.319149 93.552168 \n", "L 591.319149 22.318125 \n", "L 520.085106 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p0a45b31650)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"imaged27f217159\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"520.085106\" y=\"-22.272168\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 520.085106 93.552168 \n", "L 520.085106 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 591.319149 93.552168 \n", "L 591.319149 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 520.085106 93.552168 \n", "L 591.319149 93.552168 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 520.085106 22.318125 \n", "L 591.319149 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- coat -->\n", "    <g transform=\"translate(542.702753 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-63\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"54.980469\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"116.162109\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"177.441406\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_8\">\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 605.565957 93.552168 \n", "L 676.8 93.552168 \n", "L 676.8 22.318125 \n", "L 605.565957 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p56ba50b430)\">\n", "    <image xlink:href=\"data:image/png;base64,\n", "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\" id=\"image67abf390db\" transform=\"scale(1 -1) translate(0 -71.28)\" x=\"605.565957\" y=\"-22.272168\" width=\"71.28\" height=\"71.28\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 605.565957 93.552168 \n", "L 605.565957 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 676.8 93.552168 \n", "L 676.8 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 605.565957 93.552168 \n", "L 676.8 93.552168 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 605.565957 22.318125 \n", "L 676.8 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_8\">\n", "    <!-- shirt -->\n", "    <g transform=\"translate(627.769229 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"115.478516\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"143.261719\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"184.375\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p146ff0468f\">\n", "   <rect x=\"7.2\" y=\"22.318125\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p574a4dd0cd\">\n", "   <rect x=\"92.680851\" y=\"22.318125\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pfe2618d79d\">\n", "   <rect x=\"178.161702\" y=\"22.318125\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pc7d04c5026\">\n", "   <rect x=\"263.642553\" y=\"22.318125\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p88e3b890ce\">\n", "   <rect x=\"349.123404\" y=\"22.318125\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p0f49cb0c91\">\n", "   <rect x=\"434.604255\" y=\"22.318125\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p0a45b31650\">\n", "   <rect x=\"520.085106\" y=\"22.318125\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p56ba50b430\">\n", "   <rect x=\"605.565957\" y=\"22.318125\" width=\"71.234043\" height=\"71.234043\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1200x150 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["@d2l.add_to_class(FashionMNIST)  #@save\n", "def visualize(self, batch, nrows=1, ncols=8, labels=[]):\n", "    X, y = batch\n", "    if not labels:\n", "        labels = self.text_labels(y)\n", "    d2l.show_images(X.squeeze(1), nrows, ncols, titles=labels)\n", "batch = next(iter(data.val_dataloader()))\n", "data.visualize(batch)"]}, {"cell_type": "markdown", "id": "437083bc", "metadata": {"origin_pos": 29}, "source": ["We are now ready to work with the Fashion-MNIST dataset in the sections that follow.\n", "\n", "## Summary\n", "\n", "We now have a slightly more realistic dataset to use for classification. Fashion-MNIST is an apparel classification dataset consisting of images representing 10 categories. We will use this dataset in subsequent sections and chapters to evaluate various network designs, from a simple linear model to advanced residual networks. As we commonly do with images, we read them as a tensor of shape (batch size, number of channels, height, width). For now, we only have one channel as the images are grayscale (the visualization above uses a false color palette for improved visibility). \n", "\n", "Lastly, data iterators are a key component for efficient performance. For instance, we might use GPUs for efficient image decompression, video transcoding, or other preprocessing. Whenever possible, you should rely on well-implemented data iterators that exploit high-performance computing to avoid slowing down your training loop.\n", "\n", "\n", "## Exercises\n", "\n", "1. Does reducing the `batch_size` (for instance, to 1) affect the reading performance?\n", "1. The data iterator performance is important. Do you think the current implementation is fast enough? Explore various options to improve it. Use a system profiler to find out where the bottlenecks are.\n", "1. Check out the framework's online API documentation. Which other datasets are available?\n"]}, {"cell_type": "markdown", "id": "7bff1562", "metadata": {"origin_pos": 31, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/49)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
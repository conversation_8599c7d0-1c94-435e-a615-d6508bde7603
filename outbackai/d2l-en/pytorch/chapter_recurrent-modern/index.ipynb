{"cells": [{"cell_type": "markdown", "id": "2b57309f", "metadata": {"origin_pos": 0}, "source": ["# Modern Recurrent Neural Networks\n", ":label:`chap_modern_rnn`\n", "\n", "The previous chapter introduced the key ideas \n", "behind recurrent neural networks (RNNs). \n", "However, just as with convolutional neural networks,\n", "there has been a tremendous amount of innovation\n", "in RNN architectures, culminating in several complex\n", "designs that have proven successful in practice. \n", "In particular, the most popular designs \n", "feature mechanisms for mitigating the notorious\n", "numerical instability faced by RNNs,\n", "as typified by vanishing and exploding gradients.\n", "Recall that in :numref:`chap_rnn` we dealt \n", "with exploding gradients by applying a blunt\n", "gradient clipping heuristic. \n", "Despite the efficacy of this hack,\n", "it leaves open the problem of vanishing gradients. \n", "\n", "In this chapter, we introduce the key ideas behind \n", "the most successful RNN architectures for sequences,\n", "which stem from two papers.\n", "The first, *Long Short-Term Memory* :cite:`Hochreiter.Schmidhuber.1997`,\n", "introduces the *memory cell*, a unit of computation that replaces \n", "traditional nodes in the hidden layer of a network.\n", "With these memory cells, networks are able \n", "to overcome difficulties with training \n", "encountered by earlier recurrent networks.\n", "Intuitively, the memory cell avoids \n", "the vanishing gradient problem\n", "by keeping values in each memory cell's internal state\n", "cascading along a recurrent edge with weight 1 \n", "across many successive time steps. \n", "A set of multiplicative gates help the network\n", "to determine not only the inputs to allow \n", "into the memory state, \n", "but when the content of the memory state \n", "should influence the model's output. \n", "\n", "The second paper, *Bidirectional Recurrent Neural Networks* :cite:`Schuster.Paliwal.1997`,\n", "introduces an architecture in which information \n", "from both the future (subsequent time steps) \n", "and the past (preceding time steps)\n", "are used to determine the output \n", "at any point in the sequence.\n", "This is in contrast to previous networks, \n", "in which only past input can affect the output.\n", "Bidirectional RNNs have become a mainstay \n", "for sequence labeling tasks in natural language processing,\n", "among a myriad of other tasks. \n", "Fortunately, the two innovations are not mutually exclusive, \n", "and have been successfully combined for phoneme classification\n", ":cite:<PERSON><PERSON>.Schmidhuber.2005` and handwriting recognition :cite:`graves2008novel`.\n", "\n", "\n", "The first sections in this chapter will explain the LSTM architecture,\n", "a lighter-weight version called the gated recurrent unit (GRU),\n", "the key ideas behind bidirectional RNNs \n", "and a brief explanation of how RNN layers \n", "are stacked together to form deep RNNs. \n", "Subsequently, we will explore the application of RNNs\n", "in sequence-to-sequence tasks, \n", "introducing machine translation\n", "along with key ideas such as *encoder--decoder* architectures and *beam search*.\n", "\n", ":begin_tab:toc\n", " - [lstm](lstm.ipynb)\n", " - [gru](gru.ipynb)\n", " - [deep-rnn](deep-rnn.ipynb)\n", " - [bi-rnn](bi-rnn.ipynb)\n", " - [machine-translation-and-dataset](machine-translation-and-dataset.ipynb)\n", " - [encoder-decoder](encoder-decoder.ipynb)\n", " - [seq2seq](seq2seq.ipynb)\n", " - [beam-search](beam-search.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
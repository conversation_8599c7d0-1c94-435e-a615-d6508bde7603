{"cells": [{"cell_type": "markdown", "id": "8d462bf4", "metadata": {"origin_pos": 1}, "source": ["#  Sequence-to-Sequence Learning for Machine Translation\n", ":label:`sec_seq2seq`\n", "\n", "In so-called sequence-to-sequence problems such as machine translation\n", "(as discussed in :numref:`sec_machine_translation`),\n", "where inputs and outputs each consist \n", "of variable-length unaligned sequences,\n", "we generally rely on encoder--decoder architectures\n", "(:numref:`sec_encoder-decoder`).\n", "In this section,\n", "we will demonstrate the application \n", "of an encoder--decoder architecture,\n", "where both the encoder and decoder \n", "are implemented as RNNs,\n", "to the task of machine translation\n", ":cite:`<PERSON><PERSON><PERSON>.Vinyals.Le.2014,<PERSON><PERSON>Merrienboer.Gulcehre.ea.2014`.\n", "\n", "Here, the encoder RNN will take a variable-length sequence as input \n", "and transform it into a fixed-shape hidden state.\n", "Later, in :numref:`chap_attention-and-transformers`,\n", "we will introduce attention mechanisms, \n", "which allow us to access encoded inputs\n", "without having to compress the entire input\n", "into a single fixed-length representation.\n", "\n", "Then to generate the output sequence, \n", "one token at a time,\n", "the decoder model, \n", "consisting of a separate RNN,\n", "will predict each successive target token\n", "given both the input sequence\n", "and the preceding tokens in the output.\n", "During training, the decoder will typically\n", "be conditioned upon the preceding tokens\n", "in the official \"ground truth\" label. \n", "However, at test time, we will want to condition\n", "each output of the decoder on the tokens already predicted. \n", "Note that if we ignore the encoder,\n", "the decoder in a sequence-to-sequence architecture \n", "behaves just like a normal language model.\n", ":numref:`fig_seq2seq` illustrates\n", "how to use two RNNs\n", "for sequence-to-sequence learning\n", "in machine translation.\n", "\n", "\n", "![Sequence-to-sequence learning with an RNN encoder and an RNN decoder.](../img/seq2seq.svg)\n", ":label:`fig_seq2seq`\n", "\n", "In :numref:`fig_seq2seq`,\n", "the special \"&lt;eos&gt;\" token\n", "marks the end of the sequence.\n", "Our model can stop making predictions\n", "once this token is generated.\n", "At the initial time step of the RNN decoder,\n", "there are two special design decisions to be aware of:\n", "First, we begin every input with a special \n", "beginning-of-sequence \"&lt;bos&gt;\" token.\n", "Second, we may feed\n", "the final hidden state of the encoder\n", "into the decoder\n", "at every single decoding time step :cite:`<PERSON><PERSON>Merrienboer.Gulcehre.ea.2014`.\n", "In some other designs,\n", "such as that of :citet:`Sutskever.Vinyals.Le.2014`,\n", "the final hidden state of the RNN encoder\n", "is used\n", "to initiate the hidden state of the decoder\n", "only at the first decoding step.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d30d0899", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:55:28.840265Z", "iopub.status.busy": "2023-08-18T19:55:28.839686Z", "iopub.status.idle": "2023-08-18T19:55:32.081579Z", "shell.execute_reply": "2023-08-18T19:55:32.080287Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import collections\n", "import math\n", "import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "50066a7a", "metadata": {"origin_pos": 6}, "source": ["## Teacher Forcing\n", "\n", "While running the encoder on the input sequence\n", "is relatively straightforward,\n", "handling the input and output \n", "of the decoder requires more care. \n", "The most common approach is sometimes called *teacher forcing*.\n", "Here, the original target sequence (token labels)\n", "is fed into the decoder as input.\n", "More concretely,\n", "the special beginning-of-sequence token\n", "and the original target sequence,\n", "excluding the final token,\n", "are concatenated as input to the decoder,\n", "while the decoder output (labels for training) is\n", "the original target sequence,\n", "shifted by one token:\n", "\"&lt;bos&gt;\", \"Ils\", \"regardent\", \".\" $\\rightarrow$\n", "\"Ils\", \"regardent\", \".\", \"&lt;eos&gt;\" (:numref:`fig_seq2seq`).\n", "\n", "Our implementation in\n", ":numref:`subsec_loading-seq-fixed-len`\n", "prepared training data for teacher forcing,\n", "where shifting tokens for self-supervised learning\n", "is similar to the training of language models in\n", ":numref:`sec_language-model`.\n", "An alternative approach is\n", "to feed the *predicted* token\n", "from the previous time step\n", "as the current input to the decoder.\n", "\n", "\n", "In the following, we explain the design \n", "depicted in :numref:`fig_seq2seq`\n", "in greater detail.\n", "We will train this model for machine translation\n", "on the English--French dataset as introduced in\n", ":numref:`sec_machine_translation`.\n", "\n", "## Encoder\n", "\n", "Recall that the encoder transforms an input sequence of variable length\n", "into a fixed-shape *context variable* $\\mathbf{c}$ (see :numref:`fig_seq2seq`).\n", "\n", "\n", "Consider a single sequence example (batch size 1).\n", "Suppose the input sequence is $x_1, \\ldots, x_T$, \n", "such that $x_t$ is the $t^{\\textrm{th}}$ token.\n", "At time step $t$, the RNN transforms\n", "the input feature vector $\\mathbf{x}_t$ for $x_t$\n", "and the hidden state $\\mathbf{h} _{t-1}$ \n", "from the previous time step \n", "into the current hidden state $\\mathbf{h}_t$.\n", "We can use a function $f$ to express \n", "the transformation of the RNN's recurrent layer:\n", "\n", "$$\\mathbf{h}_t = f(\\mathbf{x}_t, \\mathbf{h}_{t-1}). $$\n", "\n", "In general, the encoder transforms \n", "the hidden states at all time steps\n", "into a context variable through a customized function $q$:\n", "\n", "$$\\mathbf{c} =  q(\\mathbf{h}_1, \\ldots, \\mathbf{h}_T).$$\n", "\n", "For example, in :numref:`fig_seq2seq`,\n", "the context variable is just the hidden state $\\mathbf{h}_T$\n", "corresponding to the encoder RNN's representation\n", "after processing the final token of the input sequence.\n", "\n", "In this example, we have used a unidirectional RNN\n", "to design the encoder,\n", "where the hidden state only depends on the input subsequence \n", "at and before the time step of the hidden state.\n", "We can also construct encoders using bidirectional RNNs.\n", "In this case, a hidden state depends on the subsequence before and after the time step \n", "(including the input at the current time step), \n", "which encodes the information of the entire sequence.\n", "\n", "\n", "Now let's [**implement the RNN encoder**].\n", "Note that we use an *embedding layer*\n", "to obtain the feature vector for each token in the input sequence.\n", "The weight of an embedding layer is a matrix,\n", "where the number of rows corresponds to \n", "the size of the input vocabulary (`vocab_size`)\n", "and number of columns corresponds to \n", "the feature vector's dimension (`embed_size`).\n", "For any input token index $i$,\n", "the embedding layer fetches the $i^{\\textrm{th}}$ row \n", "(starting from 0) of the weight matrix\n", "to return its feature vector.\n", "Here we implement the encoder with a multilayer GRU.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "cd36a65e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:55:32.087883Z", "iopub.status.busy": "2023-08-18T19:55:32.086755Z", "iopub.status.idle": "2023-08-18T19:55:32.094916Z", "shell.execute_reply": "2023-08-18T19:55:32.093791Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["def init_seq2seq(module):  #@save\n", "    \"\"\"Initialize weights for sequence-to-sequence learning.\"\"\"\n", "    if type(module) == nn.Linear:\n", "         nn.init.xavier_uniform_(module.weight)\n", "    if type(module) == nn.GRU:\n", "        for param in module._flat_weights_names:\n", "            if \"weight\" in param:\n", "                nn.init.xavier_uniform_(module._parameters[param])"]}, {"cell_type": "code", "execution_count": 3, "id": "7b8df492", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:55:32.100040Z", "iopub.status.busy": "2023-08-18T19:55:32.099233Z", "iopub.status.idle": "2023-08-18T19:55:32.107817Z", "shell.execute_reply": "2023-08-18T19:55:32.106716Z"}, "origin_pos": 9, "tab": ["pytorch"]}, "outputs": [], "source": ["class Seq2SeqEncoder(d2l.Encoder):  #@save\n", "    \"\"\"The RNN encoder for sequence-to-sequence learning.\"\"\"\n", "    def __init__(self, vocab_size, embed_size, num_hiddens, num_layers,\n", "                 dropout=0):\n", "        super().__init__()\n", "        self.embedding = nn.Embedding(vocab_size, embed_size)\n", "        self.rnn = d2l.GRU(embed_size, num_hiddens, num_layers, dropout)\n", "        self.apply(init_seq2seq)\n", "\n", "    def forward(self, X, *args):\n", "        # X shape: (batch_size, num_steps)\n", "        embs = self.embedding(X.t().type(torch.int64))\n", "        # embs shape: (num_steps, batch_size, embed_size)\n", "        outputs, state = self.rnn(embs)\n", "        # outputs shape: (num_steps, batch_size, num_hiddens)\n", "        # state shape: (num_layers, batch_size, num_hiddens)\n", "        return outputs, state"]}, {"cell_type": "markdown", "id": "24c21c8e", "metadata": {"origin_pos": 12}, "source": ["Let's use a concrete example\n", "to [**illustrate the above encoder implementation.**]\n", "Below, we instantiate a two-layer GRU encoder\n", "whose number of hidden units is 16.\n", "Given a minibatch of sequence inputs `X`\n", "(batch size $=4$; number of time steps $=9$),\n", "the hidden states of the final layer\n", "at all the time steps\n", "(`enc_outputs` returned by the encoder's recurrent layers)\n", "are a tensor of shape\n", "(number of time steps, batch size, number of hidden units).\n"]}, {"cell_type": "code", "execution_count": 4, "id": "89d5cb2e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:55:32.112983Z", "iopub.status.busy": "2023-08-18T19:55:32.112158Z", "iopub.status.idle": "2023-08-18T19:55:32.150498Z", "shell.execute_reply": "2023-08-18T19:55:32.149041Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["vocab_size, embed_size, num_hiddens, num_layers = 10, 8, 16, 2\n", "batch_size, num_steps = 4, 9\n", "encoder = Seq2SeqEncoder(vocab_size, embed_size, num_hiddens, num_layers)\n", "X = torch.zeros((batch_size, num_steps))\n", "enc_outputs, enc_state = encoder(X)\n", "d2l.check_shape(enc_outputs, (num_steps, batch_size, num_hiddens))"]}, {"cell_type": "markdown", "id": "f827b6d3", "metadata": {"origin_pos": 14}, "source": ["Since we are using a GRU here,\n", "the shape of the multilayer hidden states\n", "at the final time step is\n", "(number of hidden layers, batch size, number of hidden units).\n"]}, {"cell_type": "code", "execution_count": 5, "id": "0e8eab95", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:55:32.155159Z", "iopub.status.busy": "2023-08-18T19:55:32.154233Z", "iopub.status.idle": "2023-08-18T19:55:32.160686Z", "shell.execute_reply": "2023-08-18T19:55:32.159216Z"}, "origin_pos": 15, "tab": ["pytorch"]}, "outputs": [], "source": ["d2l.check_shape(enc_state, (num_layers, batch_size, num_hiddens))"]}, {"cell_type": "markdown", "id": "8a35aecf", "metadata": {"origin_pos": 16}, "source": ["## [**Decoder**]\n", ":label:`sec_seq2seq_decoder`\n", "\n", "Given a target output sequence $y_1, y_2, \\ldots, y_{T'}$\n", "for each time step $t'$\n", "(we use $t^\\prime$ to differentiate from the input sequence time steps),\n", "the decoder assigns a predicted probability\n", "to each possible token occurring at step $y_{t'+1}$\n", "conditioned upon the previous tokens in the target\n", "$y_1, \\ldots, y_{t'}$ \n", "and the context variable \n", "$\\mathbf{c}$, i.e., $P(y_{t'+1} \\mid y_1, \\ldots, y_{t'}, \\mathbf{c})$.\n", "\n", "To predict the subsequent token $t^\\prime+1$ in the target sequence,\n", "the RNN decoder takes the previous step's target token $y_{t^\\prime}$,\n", "the hidden RNN state from the previous time step $\\mathbf{s}_{t^\\prime-1}$,\n", "and the context variable $\\mathbf{c}$ as its input,\n", "and transforms them into the hidden state \n", "$\\mathbf{s}_{t^\\prime}$ at the current time step.\n", "We can use a function $g$ to express \n", "the transformation of the decoder's hidden layer:\n", "\n", "$$\\mathbf{s}_{t^\\prime} = g(y_{t^\\prime-1}, \\mathbf{c}, \\mathbf{s}_{t^\\prime-1}).$$\n", ":eqlabel:`eq_seq2seq_s_t`\n", "\n", "After obtaining the hidden state of the decoder,\n", "we can use an output layer and the softmax operation \n", "to compute the predictive distribution\n", "$p(y_{t^{\\prime}+1} \\mid y_1, \\ldots, y_{t^\\prime}, \\mathbf{c})$ \n", "over the subsequent output token ${t^\\prime+1}$.\n", "\n", "Following :numref:`fig_seq2seq`,\n", "when implementing the decoder as follows,\n", "we directly use the hidden state at the final time step\n", "of the encoder\n", "to initialize the hidden state of the decoder.\n", "This requires that the RNN encoder and the RNN decoder \n", "have the same number of layers and hidden units.\n", "To further incorporate the encoded input sequence information,\n", "the context variable is concatenated\n", "with the decoder input at all the time steps.\n", "To predict the probability distribution of the output token,\n", "we use a fully connected layer\n", "to transform the hidden state \n", "at the final layer of the RNN decoder.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "1c318811", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:55:32.165108Z", "iopub.status.busy": "2023-08-18T19:55:32.164049Z", "iopub.status.idle": "2023-08-18T19:55:32.176473Z", "shell.execute_reply": "2023-08-18T19:55:32.175009Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [], "source": ["class Seq2SeqDecoder(d2l.Decoder):\n", "    \"\"\"The RNN decoder for sequence to sequence learning.\"\"\"\n", "    def __init__(self, vocab_size, embed_size, num_hiddens, num_layers,\n", "                 dropout=0):\n", "        super().__init__()\n", "        self.embedding = nn.Embedding(vocab_size, embed_size)\n", "        self.rnn = d2l.GRU(embed_size+num_hiddens, num_hiddens,\n", "                           num_layers, dropout)\n", "        self.dense = nn.LazyLinear(vocab_size)\n", "        self.apply(init_seq2seq)\n", "\n", "    def init_state(self, enc_all_outputs, *args):\n", "        return enc_all_outputs\n", "\n", "    def forward(self, X, state):\n", "        # X shape: (batch_size, num_steps)\n", "        # embs shape: (num_steps, batch_size, embed_size)\n", "        embs = self.embedding(X.t().type(torch.int32))\n", "        enc_output, hidden_state = state\n", "        # context shape: (batch_size, num_hiddens)\n", "        context = enc_output[-1]\n", "        # Broadcast context to (num_steps, batch_size, num_hiddens)\n", "        context = context.repeat(embs.shape[0], 1, 1)\n", "        # Concat at the feature dimension\n", "        embs_and_context = torch.cat((embs, context), -1)\n", "        outputs, hidden_state = self.rnn(embs_and_context, hidden_state)\n", "        outputs = self.dense(outputs).swapaxes(0, 1)\n", "        # outputs shape: (batch_size, num_steps, vocab_size)\n", "        # hidden_state shape: (num_layers, batch_size, num_hiddens)\n", "        return outputs, [enc_output, hidden_state]"]}, {"cell_type": "markdown", "id": "195041fa", "metadata": {"origin_pos": 21}, "source": ["To [**illustrate the implemented decoder**],\n", "below we instantiate it with the same hyperparameters from the aforementioned encoder.\n", "As we can see, the output shape of the decoder becomes (batch size, number of time steps, vocabulary size),\n", "where the final dimension of the tensor stores the predicted token distribution.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "a51f8b17", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:55:32.180880Z", "iopub.status.busy": "2023-08-18T19:55:32.179836Z", "iopub.status.idle": "2023-08-18T19:55:32.197802Z", "shell.execute_reply": "2023-08-18T19:55:32.196352Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["decoder = Seq2SeqDecoder(vocab_size, embed_size, num_hiddens, num_layers)\n", "state = decoder.init_state(encoder(X))\n", "dec_outputs, state = decoder(X, state)\n", "d2l.check_shape(dec_outputs, (batch_size, num_steps, vocab_size))\n", "d2l.check_shape(state[1], (num_layers, batch_size, num_hiddens))"]}, {"cell_type": "markdown", "id": "f87599b6", "metadata": {"origin_pos": 23}, "source": ["The layers in the above RNN encoder--decoder model \n", "are summarized in :numref:`fig_seq2seq_details`.\n", "\n", "![Layers in an RNN encoder--decoder model.](../img/seq2seq-details.svg)\n", ":label:`fig_seq2seq_details`\n", "\n", "\n", "\n", "## Encoder--Decoder for Sequence-to-Sequence Learning\n", "\n", "\n", "Putting it all together in code yields the following:\n"]}, {"cell_type": "code", "execution_count": 8, "id": "4e30d226", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:55:32.207516Z", "iopub.status.busy": "2023-08-18T19:55:32.206334Z", "iopub.status.idle": "2023-08-18T19:55:32.219468Z", "shell.execute_reply": "2023-08-18T19:55:32.218248Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [], "source": ["class Seq2Seq(d2l.EncoderDecoder):  #@save\n", "    \"\"\"The RNN encoder--decoder for sequence to sequence learning.\"\"\"\n", "    def __init__(self, encoder, decoder, tgt_pad, lr):\n", "        super().__init__(encoder, decoder)\n", "        self.save_hyperparameters()\n", "\n", "    def validation_step(self, batch):\n", "        Y_hat = self(*batch[:-1])\n", "        self.plot('loss', self.loss(Y_hat, batch[-1]), train=False)\n", "\n", "    def configure_optimizers(self):\n", "        # Adam optimizer is used here\n", "        return torch.optim.Adam(self.parameters(), lr=self.lr)"]}, {"cell_type": "markdown", "id": "81fe6c0d", "metadata": {"origin_pos": 26}, "source": ["## Loss Function with Mask<PERSON>\n", "\n", "At each time step, the decoder predicts \n", "a probability distribution for the output tokens.\n", "As with language modeling, \n", "we can apply softmax \n", "to obtain the distribution\n", "and calculate the cross-entropy loss for optimization.\n", "Recall from :numref:`sec_machine_translation`\n", "that the special padding tokens\n", "are appended to the end of sequences\n", "and so sequences of varying lengths\n", "can be efficiently loaded\n", "in minibatches of the same shape.\n", "However, prediction of padding tokens\n", "should be excluded from loss calculations.\n", "To this end, we can \n", "[**mask irrelevant entries with zero values**]\n", "so that multiplication \n", "of any irrelevant prediction\n", "with zero equates to zero.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "9baec321", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:55:32.224892Z", "iopub.status.busy": "2023-08-18T19:55:32.224198Z", "iopub.status.idle": "2023-08-18T19:55:32.232039Z", "shell.execute_reply": "2023-08-18T19:55:32.230833Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(Seq2Seq)\n", "def loss(self, Y_hat, Y):\n", "    l = super(Seq2Seq, self).loss(Y_hat, Y, averaged=False)\n", "    mask = (Y.reshape(-1) != self.tgt_pad).type(torch.float32)\n", "    return (l * mask).sum() / mask.sum()"]}, {"cell_type": "markdown", "id": "876a6584", "metadata": {"origin_pos": 29}, "source": ["## [**Training**]\n", ":label:`sec_seq2seq_training`\n", "\n", "Now we can [**create and train an RNN encoder--decoder model**]\n", "for sequence-to-sequence learning on the machine translation dataset.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "2e0ee6e7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:55:32.246700Z", "iopub.status.busy": "2023-08-18T19:55:32.246014Z", "iopub.status.idle": "2023-08-18T19:56:06.984975Z", "shell.execute_reply": "2023-08-18T19:56:06.983766Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"229.425pt\" height=\"183.35625pt\" viewBox=\"0 0 229.425 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:56:06.857753</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 229.425 183.35625 \n", "L 229.425 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 20.5625 145.8 \n", "L 215.8625 145.8 \n", "L 215.8625 7.2 \n", "L 20.5625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mdec6c65a06\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mdec6c65a06\" x=\"20.5625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(17.38125 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mdec6c65a06\" x=\"53.1125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(49.93125 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mdec6c65a06\" x=\"85.6625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(79.3 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mdec6c65a06\" x=\"118.2125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 15 -->\n", "      <g transform=\"translate(111.85 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mdec6c65a06\" x=\"150.7625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(144.4 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mdec6c65a06\" x=\"183.3125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 25 -->\n", "      <g transform=\"translate(176.95 160.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mdec6c65a06\" x=\"215.8625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(209.5 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(102.984375 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_8\">\n", "      <defs>\n", "       <path id=\"mbabbd31e37\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mbabbd31e37\" x=\"20.5625\" y=\"145.220468\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(7.2 149.019687) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mbabbd31e37\" x=\"20.5625\" y=\"117.355527\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(7.2 121.154745) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mbabbd31e37\" x=\"20.5625\" y=\"89.490585\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(7.2 93.289804) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#mbabbd31e37\" x=\"20.5625\" y=\"61.625644\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(7.2 65.424862) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mbabbd31e37\" x=\"20.5625\" y=\"33.760702\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(7.2 37.559921) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 21.37625 13.5 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 27.0725 45.129469 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 27.0725 45.129469 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 27.0725 45.129469 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_104\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_105\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_106\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_107\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_108\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_109\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_110\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_111\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_112\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_113\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_118\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_119\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_120\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_121\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_122\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_123\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_124\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_125\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_126\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_127\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_128\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_129\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_130\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_131\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_132\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_133\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_134\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_135\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_136\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_137\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_138\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_139\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_140\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_141\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_142\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_143\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_144\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_145\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_146\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_147\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_148\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_149\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_150\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_151\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_152\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_153\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_154\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_155\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_156\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_157\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_158\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_159\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_160\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_161\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_162\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_163\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_164\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_165\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_166\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_167\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "L 190.63625 138.965788 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_168\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_169\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "L 190.63625 138.965788 \n", "L 193.89125 138.372818 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_170\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_171\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "L 190.63625 138.965788 \n", "L 193.89125 138.372818 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_172\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "L 196.3325 51.000726 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_173\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "L 190.63625 138.965788 \n", "L 193.89125 138.372818 \n", "L 197.14625 139.142612 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_174\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "L 196.3325 51.000726 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_175\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "L 190.63625 138.965788 \n", "L 193.89125 138.372818 \n", "L 197.14625 139.142612 \n", "L 200.40125 138.115176 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_176\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "L 196.3325 51.000726 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_177\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "L 190.63625 138.965788 \n", "L 193.89125 138.372818 \n", "L 197.14625 139.142612 \n", "L 200.40125 138.115176 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_178\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "L 196.3325 51.000726 \n", "L 202.8425 52.2929 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_179\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "L 190.63625 138.965788 \n", "L 193.89125 138.372818 \n", "L 197.14625 139.142612 \n", "L 200.40125 138.115176 \n", "L 203.65625 139.46647 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_180\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "L 196.3325 51.000726 \n", "L 202.8425 52.2929 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_181\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "L 190.63625 138.965788 \n", "L 193.89125 138.372818 \n", "L 197.14625 139.142612 \n", "L 200.40125 138.115176 \n", "L 203.65625 139.46647 \n", "L 206.91125 138.404599 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_182\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "L 196.3325 51.000726 \n", "L 202.8425 52.2929 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_183\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "L 190.63625 138.965788 \n", "L 193.89125 138.372818 \n", "L 197.14625 139.142612 \n", "L 200.40125 138.115176 \n", "L 203.65625 139.46647 \n", "L 206.91125 138.404599 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_184\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "L 196.3325 51.000726 \n", "L 202.8425 52.2929 \n", "L 209.3525 51.369537 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_185\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "L 190.63625 138.965788 \n", "L 193.89125 138.372818 \n", "L 197.14625 139.142612 \n", "L 200.40125 138.115176 \n", "L 203.65625 139.46647 \n", "L 206.91125 138.404599 \n", "L 210.16625 139.5 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_186\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "L 196.3325 51.000726 \n", "L 202.8425 52.2929 \n", "L 209.3525 51.369537 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_187\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "L 190.63625 138.965788 \n", "L 193.89125 138.372818 \n", "L 197.14625 139.142612 \n", "L 200.40125 138.115176 \n", "L 203.65625 139.46647 \n", "L 206.91125 138.404599 \n", "L 210.16625 139.5 \n", "L 213.42125 138.650295 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_188\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "L 196.3325 51.000726 \n", "L 202.8425 52.2929 \n", "L 209.3525 51.369537 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_189\">\n", "    <path d=\"M 21.37625 13.5 \n", "L 24.63125 51.68169 \n", "L 27.88625 57.092465 \n", "L 31.14125 67.826944 \n", "L 34.39625 73.55514 \n", "L 37.65125 79.515805 \n", "L 40.90625 83.867058 \n", "L 44.16125 88.296718 \n", "L 47.41625 93.997609 \n", "L 50.67125 92.924557 \n", "L 53.92625 98.72294 \n", "L 57.18125 100.671705 \n", "L 60.43625 105.243304 \n", "L 63.69125 105.338881 \n", "L 66.94625 109.05422 \n", "L 70.20125 110.619081 \n", "L 73.45625 114.545402 \n", "L 76.71125 113.834021 \n", "L 79.96625 118.728127 \n", "L 83.22125 116.848176 \n", "L 86.47625 120.169704 \n", "L 89.73125 121.775716 \n", "L 92.98625 124.331481 \n", "L 96.24125 123.648379 \n", "L 99.49625 126.154341 \n", "L 102.75125 126.49777 \n", "L 106.00625 128.648153 \n", "L 109.26125 128.817579 \n", "L 112.51625 130.688467 \n", "L 115.77125 130.430662 \n", "L 119.02625 132.214034 \n", "L 122.28125 131.640599 \n", "L 125.53625 133.242291 \n", "L 128.79125 133.545366 \n", "L 132.04625 134.424666 \n", "L 135.30125 134.477714 \n", "L 138.55625 135.434887 \n", "L 141.81125 135.326084 \n", "L 145.06625 136.417062 \n", "L 148.32125 136.068069 \n", "L 151.57625 136.754555 \n", "L 154.83125 136.614932 \n", "L 158.08625 137.262726 \n", "L 161.34125 137.189512 \n", "L 164.59625 138.260315 \n", "L 167.85125 137.034527 \n", "L 171.10625 138.39471 \n", "L 174.36125 137.465242 \n", "L 177.61625 138.491284 \n", "L 180.87125 138.104051 \n", "L 184.12625 138.538744 \n", "L 187.38125 138.496865 \n", "L 190.63625 138.965788 \n", "L 193.89125 138.372818 \n", "L 197.14625 139.142612 \n", "L 200.40125 138.115176 \n", "L 203.65625 139.46647 \n", "L 206.91125 138.404599 \n", "L 210.16625 139.5 \n", "L 213.42125 138.650295 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_190\">\n", "    <path d=\"M 27.0725 45.129469 \n", "L 33.5825 62.163091 \n", "L 40.0925 66.228317 \n", "L 46.6025 70.013905 \n", "L 53.1125 70.958806 \n", "L 59.6225 74.276058 \n", "L 66.1325 74.439944 \n", "L 72.6425 74.01714 \n", "L 79.1525 73.268382 \n", "L 85.6625 72.417895 \n", "L 92.1725 69.803418 \n", "L 98.6825 71.318081 \n", "L 105.1925 65.835177 \n", "L 111.7025 64.600625 \n", "L 118.2125 63.589767 \n", "L 124.7225 62.487727 \n", "L 131.2325 61.408059 \n", "L 137.7425 60.538654 \n", "L 144.2525 62.19801 \n", "L 150.7625 54.899339 \n", "L 157.2725 57.071169 \n", "L 163.7825 59.514154 \n", "L 170.2925 55.2216 \n", "L 176.8025 57.307067 \n", "L 183.3125 54.236698 \n", "L 189.8225 57.057486 \n", "L 196.3325 51.000726 \n", "L 202.8425 52.2929 \n", "L 209.3525 51.369537 \n", "L 215.8625 51.703178 \n", "\" clip-path=\"url(#pb04dbd0975)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 20.5625 145.8 \n", "L 20.5625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 215.8625 145.8 \n", "L 215.8625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 20.5625 145.8 \n", "L 215.8625 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 20.5625 7.2 \n", "L 215.8625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 129.271875 45.1125 \n", "L 208.8625 45.1125 \n", "Q 210.8625 45.1125 210.8625 43.1125 \n", "L 210.8625 14.2 \n", "Q 210.8625 12.2 208.8625 12.2 \n", "L 129.271875 12.2 \n", "Q 127.271875 12.2 127.271875 14.2 \n", "L 127.271875 43.1125 \n", "Q 127.271875 45.1125 129.271875 45.1125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_191\">\n", "     <path d=\"M 131.271875 20.298438 \n", "L 141.271875 20.298438 \n", "L 151.271875 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(159.271875 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_192\">\n", "     <path d=\"M 131.271875 35.254688 \n", "L 141.271875 35.254688 \n", "L 151.271875 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(159.271875 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb04dbd0975\">\n", "   <rect x=\"20.5625\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = d2l.MTFraEng(batch_size=128)\n", "embed_size, num_hiddens, num_layers, dropout = 256, 256, 2, 0.2\n", "encoder = Seq2SeqEncoder(\n", "    len(data.src_vocab), embed_size, num_hiddens, num_layers, dropout)\n", "decoder = Seq2SeqDecoder(\n", "    len(data.tgt_vocab), embed_size, num_hiddens, num_layers, dropout)\n", "model = Seq2Seq(encoder, decoder, tgt_pad=data.tgt_vocab['<pad>'],\n", "                lr=0.005)\n", "trainer = d2l.Trainer(max_epochs=30, gradient_clip_val=1, num_gpus=1)\n", "trainer.fit(model, data)"]}, {"cell_type": "markdown", "id": "3914d070", "metadata": {"origin_pos": 31}, "source": ["## [**Prediction**]\n", "\n", "To predict the output sequence\n", "at each step, \n", "the predicted token from the previous\n", "time step is fed into the decoder as an input.\n", "One simple strategy is to sample whichever token\n", "that has been assigned by the decoder the highest probability\n", "when predicting at each step.\n", "As in training, at the initial time step\n", "the beginning-of-sequence (\"&lt;bos&gt;\") token\n", "is fed into the decoder.\n", "This prediction process\n", "is illustrated in :numref:`fig_seq2seq_predict`.\n", "When the end-of-sequence (\"&lt;eos&gt;\") token is predicted,\n", "the prediction of the output sequence is complete.\n", "\n", "\n", "![Predicting the output sequence token by token using an RNN encoder--decoder.](../img/seq2seq-predict.svg)\n", ":label:`fig_seq2seq_predict`\n", "\n", "In the next section, we will introduce \n", "more sophisticated strategies \n", "based on beam search (:numref:`sec_beam-search`).\n"]}, {"cell_type": "code", "execution_count": 11, "id": "1c19a4a9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:56:06.989275Z", "iopub.status.busy": "2023-08-18T19:56:06.988604Z", "iopub.status.idle": "2023-08-18T19:56:06.996162Z", "shell.execute_reply": "2023-08-18T19:56:06.994959Z"}, "origin_pos": 32, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(d2l.EncoderDecoder)  #@save\n", "def predict_step(self, batch, device, num_steps,\n", "                 save_attention_weights=False):\n", "    batch = [a.to(device) for a in batch]\n", "    src, tgt, src_valid_len, _ = batch\n", "    enc_all_outputs = self.encoder(src, src_valid_len)\n", "    dec_state = self.decoder.init_state(enc_all_outputs, src_valid_len)\n", "    outputs, attention_weights = [tgt[:, (0)].unsqueeze(1), ], []\n", "    for _ in range(num_steps):\n", "        Y, dec_state = self.decoder(outputs[-1], dec_state)\n", "        outputs.append(<PERSON><PERSON>argmax(2))\n", "        # Save attention weights (to be covered later)\n", "        if save_attention_weights:\n", "            attention_weights.append(self.decoder.attention_weights)\n", "    return torch.cat(outputs[1:], 1), attention_weights"]}, {"cell_type": "markdown", "id": "cb8f0164", "metadata": {"origin_pos": 34}, "source": ["## Evaluation of Predicted Sequences\n", "\n", "We can evaluate a predicted sequence\n", "by comparing it with the\n", "target sequence (the ground truth).\n", "But what precisely is the appropriate measure \n", "for comparing similarity between two sequences?\n", "\n", "\n", "Bilingual Evaluation Understudy (BLEU),\n", "though originally proposed for evaluating\n", "machine translation results :cite:`Papineni.Roukos.Ward.ea.2002`,\n", "has been extensively used in measuring\n", "the quality of output sequences for different applications.\n", "In principle, for any $n$-gram (:numref:`subsec_markov-models-and-n-grams`) in the predicted sequence,\n", "BLEU evaluates whether this $n$-gram appears\n", "in the target sequence.\n", "\n", "Denote by $p_n$ the precision of an $n$-gram,\n", "defined as the ratio \n", "of the number of matched $n$-grams in\n", "the predicted and target sequences\n", "to the number of $n$-grams in the predicted sequence.\n", "To explain, given a target sequence $A$, $B$, $C$, $D$, $E$, $F$,\n", "and a predicted sequence $A$, $B$, $B$, $C$, $D$,\n", "we have $p_1 = 4/5$,  $p_2 = 3/4$, $p_3 = 1/3$, and $p_4 = 0$.\n", "Now let $\\textrm{len}_{\\textrm{label}}$ and $\\textrm{len}_{\\textrm{pred}}$\n", "be the numbers of tokens in the target sequence \n", "and the predicted sequence, respectively.\n", "Then, BLEU is defined as\n", "\n", "$$ \\exp\\left(\\min\\left(0, 1 - \\frac{\\textrm{len}_{\\textrm{label}}}{\\textrm{len}_{\\textrm{pred}}}\\right)\\right) \\prod_{n=1}^k p_n^{1/2^n},$$\n", ":eqlabel:`eq_bleu`\n", "\n", "where $k$ is the longest $n$-gram for matching.\n", "\n", "Based on the definition of BLEU in :eqref:`eq_bleu`,\n", "whenever the predicted sequence is the same as the target sequence, BLEU is 1.\n", "Moreover,\n", "since matching longer $n$-grams is more difficult,\n", "BLEU assigns a greater weight\n", "when a longer $n$-gram has high precision.\n", "Specifically, when $p_n$ is fixed,\n", "$p_n^{1/2^n}$ increases as $n$ grows (the original paper uses $p_n^{1/n}$).\n", "Furthermore,\n", "since\n", "predicting shorter sequences\n", "tends to yield a higher $p_n$ value,\n", "the coefficient before the multiplication term in :eqref:`eq_bleu`\n", "penalizes shorter predicted sequences.\n", "For example, when $k=2$,\n", "given the target sequence $A$, $B$, $C$, $D$, $E$, $F$ and the predicted sequence $A$, $B$,\n", "although $p_1 = p_2 = 1$, the penalty factor $\\exp(1-6/2) \\approx 0.14$ lowers the BLEU.\n", "\n", "We [**implement the BLEU measure**] as follows.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "904e4635", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:56:06.999996Z", "iopub.status.busy": "2023-08-18T19:56:06.999108Z", "iopub.status.idle": "2023-08-18T19:56:07.006270Z", "shell.execute_reply": "2023-08-18T19:56:07.005413Z"}, "origin_pos": 35, "tab": ["pytorch"]}, "outputs": [], "source": ["def bleu(pred_seq, label_seq, k):  #@save\n", "    \"\"\"Compute the BLEU.\"\"\"\n", "    pred_tokens, label_tokens = pred_seq.split(' '), label_seq.split(' ')\n", "    len_pred, len_label = len(pred_tokens), len(label_tokens)\n", "    score = math.exp(min(0, 1 - len_label / len_pred))\n", "    for n in range(1, min(k, len_pred) + 1):\n", "        num_matches, label_subs = 0, collections.defaultdict(int)\n", "        for i in range(len_label - n + 1):\n", "            label_subs[' '.join(label_tokens[i: i + n])] += 1\n", "        for i in range(len_pred - n + 1):\n", "            if label_subs[' '.join(pred_tokens[i: i + n])] > 0:\n", "                num_matches += 1\n", "                label_subs[' '.join(pred_tokens[i: i + n])] -= 1\n", "        score *= math.pow(num_matches / (len_pred - n + 1), math.pow(0.5, n))\n", "    return score"]}, {"cell_type": "markdown", "id": "99544f4a", "metadata": {"origin_pos": 36}, "source": ["In the end,\n", "we use the trained RNN encoder--decoder\n", "to [**translate a few English sentences into French**]\n", "and compute the BLEU of the results.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "d5373a91", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:56:07.009753Z", "iopub.status.busy": "2023-08-18T19:56:07.009192Z", "iopub.status.idle": "2023-08-18T19:56:07.028981Z", "shell.execute_reply": "2023-08-18T19:56:07.028094Z"}, "origin_pos": 37, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["go . => ['va', '!'], bleu,1.000\n", "i lost . => [\"j'ai\", 'perdu', '.'], bleu,1.000\n", "he's calm . => ['elle', 'court', '.'], bleu,0.000\n", "i'm home . => ['je', 'suis', 'chez', 'moi', '.'], bleu,1.000\n"]}], "source": ["engs = ['go .', 'i lost .', 'he\\'s calm .', 'i\\'m home .']\n", "fras = ['va !', 'j\\'ai perdu .', 'il est calme .', 'je suis chez moi .']\n", "preds, _ = model.predict_step(\n", "    data.build(engs, fras), d2l.try_gpu(), data.num_steps)\n", "for en, fr, p in zip(engs, fras, preds):\n", "    translation = []\n", "    for token in data.tgt_vocab.to_tokens(p):\n", "        if token == '<eos>':\n", "            break\n", "        translation.append(token)\n", "    print(f'{en} => {translation}, bleu,'\n", "          f'{bleu(\" \".join(translation), fr, k=2):.3f}')"]}, {"cell_type": "markdown", "id": "dd49dd46", "metadata": {"origin_pos": 38}, "source": ["## Summary\n", "\n", "Following the design of the encoder--decoder architecture, we can use two RNNs to design a model for sequence-to-sequence learning.\n", "In encoder--decoder training, the teacher forcing approach feeds original output sequences (in contrast to predictions) into the decoder.\n", "When implementing the encoder and the decoder, we can use multilayer RNNs.\n", "We can use masks to filter out irrelevant computations, such as when calculating the loss.\n", "For evaluating output sequences,\n", "BLEU is a popular measure that matches $n$-grams between the predicted sequence and the target sequence.\n", "\n", "\n", "## Exercises\n", "\n", "1. Can you adjust the hyperparameters to improve the translation results?\n", "1. Rerun the experiment without using masks in the loss calculation. What results do you observe? Why?\n", "1. If the encoder and the decoder differ in the number of layers or the number of hidden units, how can we initialize the hidden state of the decoder?\n", "1. In training, replace teacher forcing with feeding the prediction at the previous time step into the decoder. How does this influence the performance?\n", "1. Rerun the experiment by replacing GRU with LSTM.\n", "1. Are there any other ways to design the output layer of the decoder?\n"]}, {"cell_type": "markdown", "id": "994069ec", "metadata": {"origin_pos": 40, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1062)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "05af2ce8", "metadata": {"origin_pos": 0}, "source": ["# Bidirectional Recurrent Neural Networks\n", ":label:`sec_bi_rnn`\n", "\n", "So far, our working example of a sequence learning task has been language modeling,\n", "where we aim to predict the next token given all previous tokens in a sequence. \n", "In this scenario, we wish only to condition upon the leftward context,\n", "and thus the unidirectional chaining of a standard RNN seems appropriate. \n", "However, there are many other sequence learning tasks contexts \n", "where it is perfectly fine to condition the prediction at every time step\n", "on both the leftward and the rightward context. \n", "Consider, for example, part of speech detection. \n", "Why shouldn't we take the context in both directions into account\n", "when assessing the part of speech associated with a given word?\n", "\n", "Another common task---often useful as a pretraining exercise\n", "prior to fine-tuning a model on an actual task of interest---is\n", "to mask out random tokens in a text document and then to train \n", "a sequence model to predict the values of the missing tokens.\n", "Note that depending on what comes after the blank,\n", "the likely value of the missing token changes dramatically:\n", "\n", "* I am `___`.\n", "* I am `___` hungry.\n", "* I am `___` hungry, and I can eat half a pig.\n", "\n", "In the first sentence \"happy\" seems to be a likely candidate.\n", "The words \"not\" and \"very\" seem plausible in the second sentence, \n", "but \"not\" seems incompatible with the third sentences. \n", "\n", "\n", "Fortunately, a simple technique transforms any unidirectional RNN \n", "into a bidirectional RNN :cite:`Schuster.Paliwal.1997`.\n", "We simply implement two unidirectional RNN layers\n", "chained together in opposite directions \n", "and acting on the same input (:numref:`fig_birnn`).\n", "For the first RNN layer,\n", "the first input is $\\mathbf{x}_1$\n", "and the last input is $\\mathbf{x}_T$,\n", "but for the second RNN layer, \n", "the first input is $\\mathbf{x}_T$\n", "and the last input is $\\mathbf{x}_1$.\n", "To produce the output of this bidirectional RNN layer,\n", "we simply concatenate together the corresponding outputs\n", "of the two underlying unidirectional RNN layers. \n", "\n", "\n", "![Architecture of a bidirectional RNN.](../img/birnn.svg)\n", ":label:`fig_birnn`\n", "\n", "\n", "Formally for any time step $t$,\n", "we consider a minibatch input $\\mathbf{X}_t \\in \\mathbb{R}^{n \\times d}$ \n", "(number of examples $=n$; number of inputs in each example $=d$) \n", "and let the hidden layer activation function be $\\phi$.\n", "In the bidirectional architecture,\n", "the forward and backward hidden states for this time step \n", "are $\\overrightarrow{\\mathbf{H}}_t  \\in \\mathbb{R}^{n \\times h}$ \n", "and $\\overleftarrow{\\mathbf{H}}_t  \\in \\mathbb{R}^{n \\times h}$, respectively,\n", "where $h$ is the number of hidden units.\n", "The forward and backward hidden state updates are as follows:\n", "\n", "\n", "$$\n", "\\begin{aligned}\n", "\\overrightarrow{\\mathbf{H}}_t &= \\phi(\\mathbf{X}_t \\mathbf{W}_{\\textrm{xh}}^{(f)} + \\overrightarrow{\\mathbf{H}}_{t-1} \\mathbf{W}_{\\textrm{hh}}^{(f)}  + \\mathbf{b}_\\textrm{h}^{(f)}),\\\\\n", "\\overleftarrow{\\mathbf{H}}_t &= \\phi(\\mathbf{X}_t \\mathbf{W}_{\\textrm{xh}}^{(b)} + \\overleftarrow{\\mathbf{H}}_{t+1} \\mathbf{W}_{\\textrm{hh}}^{(b)}  + \\mathbf{b}_\\textrm{h}^{(b)}),\n", "\\end{aligned}\n", "$$\n", "\n", "where the weights $\\mathbf{W}_{\\textrm{xh}}^{(f)} \\in \\mathbb{R}^{d \\times h}, \\mathbf{W}_{\\textrm{hh}}^{(f)} \\in \\mathbb{R}^{h \\times h}, \\mathbf{W}_{\\textrm{xh}}^{(b)} \\in \\mathbb{R}^{d \\times h}, \\textrm{ and } \\mathbf{W}_{\\textrm{hh}}^{(b)} \\in \\mathbb{R}^{h \\times h}$, and the biases $\\mathbf{b}_\\textrm{h}^{(f)} \\in \\mathbb{R}^{1 \\times h}$ and $\\mathbf{b}_\\textrm{h}^{(b)} \\in \\mathbb{R}^{1 \\times h}$ are all the model parameters.\n", "\n", "Next, we concatenate the forward and backward hidden states\n", "$\\overrightarrow{\\mathbf{H}}_t$ and $\\overleftarrow{\\mathbf{H}}_t$\n", "to obtain the hidden state $\\mathbf{H}_t \\in \\mathbb{R}^{n \\times 2h}$ for feeding into the output layer.\n", "In deep bidirectional RNNs with multiple hidden layers,\n", "such information is passed on as *input* to the next bidirectional layer. \n", "Last, the output layer computes the output \n", "$\\mathbf{O}_t \\in \\mathbb{R}^{n \\times q}$ (number of outputs $=q$):\n", "\n", "$$\\mathbf{O}_t = \\mathbf{H}_t \\mathbf{W}_{\\textrm{hq}} + \\mathbf{b}_\\textrm{q}.$$\n", "\n", "Here, the weight matrix $\\mathbf{W}_{\\textrm{hq}} \\in \\mathbb{R}^{2h \\times q}$ \n", "and the bias $\\mathbf{b}_\\textrm{q} \\in \\mathbb{R}^{1 \\times q}$ \n", "are the model parameters of the output layer. \n", "While technically, the two directions can have different numbers of hidden units,\n", "this design choice is seldom made in practice. \n", "We now demonstrate a simple implementation of a bidirectional RNN.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "ee07527f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:47.754935Z", "iopub.status.busy": "2023-08-18T19:39:47.754598Z", "iopub.status.idle": "2023-08-18T19:39:51.281601Z", "shell.execute_reply": "2023-08-18T19:39:51.280325Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "3f2aafd5", "metadata": {"origin_pos": 6}, "source": ["## Implementation from Scratch\n", "\n", "To implement a bidirectional RNN from scratch, we can\n", "include two unidirectional `RNNScratch` instances\n", "with separate learnable parameters.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "3282034f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:51.287307Z", "iopub.status.busy": "2023-08-18T19:39:51.286196Z", "iopub.status.idle": "2023-08-18T19:39:51.293977Z", "shell.execute_reply": "2023-08-18T19:39:51.293017Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["class BiRNNScratch(d2l.Module):\n", "    def __init__(self, num_inputs, num_hiddens, sigma=0.01):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        self.f_rnn = d2l.RNNScratch(num_inputs, num_hiddens, sigma)\n", "        self.b_rnn = d2l.RNNScratch(num_inputs, num_hiddens, sigma)\n", "        self.num_hiddens *= 2  # The output dimension will be doubled"]}, {"cell_type": "markdown", "id": "01d59627", "metadata": {"origin_pos": 9}, "source": ["States of forward and backward RNNs\n", "are updated separately,\n", "while outputs of these two RNNs are concatenated.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "bf749cb8", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:51.298781Z", "iopub.status.busy": "2023-08-18T19:39:51.297847Z", "iopub.status.idle": "2023-08-18T19:39:51.305149Z", "shell.execute_reply": "2023-08-18T19:39:51.304220Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(BiRNNScratch)\n", "def forward(self, inputs, Hs=None):\n", "    f_H, b_H = Hs if Hs is not None else (None, None)\n", "    f_outputs, f_H = self.f_rnn(inputs, f_H)\n", "    b_outputs, b_H = self.b_rnn(reversed(inputs), b_H)\n", "    outputs = [torch.cat((f, b), -1) for f, b in zip(\n", "        f_outputs, reversed(b_outputs))]\n", "    return outputs, (f_H, b_H)"]}, {"cell_type": "markdown", "id": "ecf0384a", "metadata": {"origin_pos": 11}, "source": ["## Concise Implementation\n"]}, {"cell_type": "markdown", "id": "73c382cc", "metadata": {"origin_pos": 12, "tab": ["pytorch"]}, "source": ["Using the high-level APIs,\n", "we can implement bidirectional RNNs more concisely.\n", "Here we take a GRU model as an example.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "e70cf71c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:51.309028Z", "iopub.status.busy": "2023-08-18T19:39:51.308375Z", "iopub.status.idle": "2023-08-18T19:39:51.313930Z", "shell.execute_reply": "2023-08-18T19:39:51.312960Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["class BiGRU(d2l.RNN):\n", "    def __init__(self, num_inputs, num_hiddens):\n", "        d2l.<PERSON><PERSON><PERSON>.__init__(self)\n", "        self.save_hyperparameters()\n", "        self.rnn = nn.GRU(num_inputs, num_hiddens, bidirectional=True)\n", "        self.num_hiddens *= 2"]}, {"cell_type": "markdown", "id": "9b98765b", "metadata": {"origin_pos": 15}, "source": ["## Summary\n", "\n", "In bidirectional RNNs, the hidden state for each time step is simultaneously determined by the data prior to and after the current time step. Bidirectional RNNs are mostly useful for sequence encoding and the estimation of observations given bidirectional context. Bidirectional RNNs are very costly to train due to long gradient chains.\n", "\n", "## Exercises\n", "\n", "1. If the different directions use a different number of hidden units, how will the shape of $\\mathbf{H}_t$ change?\n", "1. Design a bidirectional RNN with multiple hidden layers.\n", "1. Polysemy is common in natural languages. For example, the word \"bank\" has different meanings in contexts “i went to the bank to deposit cash” and “i went to the bank to sit down”. How can we design a neural network model such that given a context sequence and a word, a vector representation of the word in the correct context will be returned? What type of neural architectures is preferred for handling polysemy?\n"]}, {"cell_type": "markdown", "id": "c5aab116", "metadata": {"origin_pos": 17, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1059)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "71e739d3", "metadata": {"origin_pos": 1}, "source": ["# Machine Translation and the Dataset\n", ":label:`sec_machine_translation`\n", "\n", "Among the major breakthroughs that prompted \n", "widespread interest in modern RNNs\n", "was a major advance in the applied field of \n", "statistical  *machine translation*.\n", "Here, the model is presented with a sentence in one language\n", "and must predict the corresponding sentence in another. \n", "Note that here the sentences may be of different lengths,\n", "and that corresponding words in the two sentences \n", "may not occur in the same order, \n", "owing to differences \n", "in the two language's grammatical structure. \n", "\n", "\n", "Many problems have this flavor of mapping \n", "between two such \"unaligned\" sequences.\n", "Examples include mapping \n", "from dialog prompts to replies\n", "or from questions to answers.\n", "Broadly, such problems are called \n", "*sequence-to-sequence* (seq2seq) problems \n", "and they are our focus for \n", "both the remainder of this chapter\n", "and much of :numref:`chap_attention-and-transformers`.\n", "\n", "In this section, we introduce the machine translation problem\n", "and an example dataset that we will use in the subsequent examples.\n", "For decades, statistical formulations of translation between languages\n", "had been popular :cite:`<PERSON><PERSON>Cocke.Della-Pietra.ea.1988,Brown.Cocke.Della-Pietra.ea.1990`,\n", "even before researchers got neural network approaches working\n", "(methods were often lumped together under the term *neural machine translation*).\n", "\n", "\n", "First we will need some new code to process our data.\n", "Unlike the language modeling that we saw in :numref:`sec_language-model`,\n", "here each example consists of two separate text sequences,\n", "one in the source language and another (the translation) in the target language.\n", "The following code snippets will show how \n", "to load the preprocessed data into minibatches for training.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "1af4809f", "metadata": {"attributes": {"classes": [], "id": "", "n": "3"}, "execution": {"iopub.execute_input": "2023-08-18T19:25:35.594366Z", "iopub.status.busy": "2023-08-18T19:25:35.593736Z", "iopub.status.idle": "2023-08-18T19:25:38.332100Z", "shell.execute_reply": "2023-08-18T19:25:38.330551Z"}, "origin_pos": 3, "tab": ["pytorch"]}, "outputs": [], "source": ["import os\n", "import torch\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "8c65b7f8", "metadata": {"origin_pos": 6}, "source": ["## [**Downloading and Preprocessing the Dataset**]\n", "\n", "To begin, we download an English--French dataset\n", "that consists of [bilingual sentence pairs from the Tatoeba Project](http://www.manythings.org/anki/).\n", "Each line in the dataset is a tab-delimited pair \n", "consisting of an English text sequence (the *source*) \n", "and the translated French text sequence (the *target*).\n", "Note that each text sequence\n", "can be just one sentence,\n", "or a paragraph of multiple sentences.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "01ae73e6", "metadata": {"attributes": {"classes": [], "id": "", "n": "5"}, "execution": {"iopub.execute_input": "2023-08-18T19:25:38.337163Z", "iopub.status.busy": "2023-08-18T19:25:38.336114Z", "iopub.status.idle": "2023-08-18T19:25:38.343873Z", "shell.execute_reply": "2023-08-18T19:25:38.342658Z"}, "origin_pos": 7, "tab": ["pytorch"]}, "outputs": [], "source": ["class MTFraEng(d2l.DataModule):  #@save\n", "    \"\"\"The English-French dataset.\"\"\"\n", "    def _download(self):\n", "        d2l.extract(d2l.download(\n", "            d2l.DATA_URL+'fra-eng.zip', self.root,\n", "            '94646ad1522d915e7b0f9296181140edcf86a4f5'))\n", "        with open(self.root + '/fra-eng/fra.txt', encoding='utf-8') as f:\n", "            return f.read()"]}, {"cell_type": "code", "execution_count": 3, "id": "0c27fc69", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:38.347854Z", "iopub.status.busy": "2023-08-18T19:25:38.347146Z", "iopub.status.idle": "2023-08-18T19:25:38.705705Z", "shell.execute_reply": "2023-08-18T19:25:38.704813Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/fra-eng.zip from http://d2l-data.s3-accelerate.amazonaws.com/fra-eng.zip...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Go.\tVa !\n", "<PERSON><PERSON> !\n", "Run!\tCours !\n", "Run!\tCourez !\n", "Who?\tQui ?\n", "Wow!\tÇa alors !\n", "\n"]}], "source": ["data = MTFraEng()\n", "raw_text = data._download()\n", "print(raw_text[:75])"]}, {"cell_type": "markdown", "id": "00f26cb9", "metadata": {"origin_pos": 9}, "source": ["After downloading the dataset,\n", "we [**proceed with several preprocessing steps**]\n", "for the raw text data.\n", "For instance, we replace non-breaking space with space,\n", "convert uppercase letters to lowercase ones,\n", "and insert space between words and punctuation marks.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6b6ad92e", "metadata": {"attributes": {"classes": [], "id": "", "n": "6"}, "execution": {"iopub.execute_input": "2023-08-18T19:25:38.709698Z", "iopub.status.busy": "2023-08-18T19:25:38.709111Z", "iopub.status.idle": "2023-08-18T19:25:38.714600Z", "shell.execute_reply": "2023-08-18T19:25:38.713784Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(MTFraEng)  #@save\n", "def _preprocess(self, text):\n", "    # Replace non-breaking space with space\n", "    text = text.replace('\\u202f', ' ').replace('\\xa0', ' ')\n", "    # Insert space between words and punctuation marks\n", "    no_space = lambda char, prev_char: char in ',.!?' and prev_char != ' '\n", "    out = [' ' + char if i > 0 and no_space(char, text[i - 1]) else char\n", "           for i, char in enumerate(text.lower())]\n", "    return ''.join(out)"]}, {"cell_type": "code", "execution_count": 5, "id": "4a3c36d4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:38.717999Z", "iopub.status.busy": "2023-08-18T19:25:38.717445Z", "iopub.status.idle": "2023-08-18T19:25:41.820750Z", "shell.execute_reply": "2023-08-18T19:25:41.819869Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["go .\tva !\n", "hi .\tsalut !\n", "run !\tcours !\n", "run !\tcourez !\n", "who ?\tqui ?\n", "wow !\tça alors !\n"]}], "source": ["text = data._preprocess(raw_text)\n", "print(text[:80])"]}, {"cell_type": "markdown", "id": "a0beee60", "metadata": {"origin_pos": 12}, "source": ["## [**Tokenization**]\n", "\n", "Unlike the character-level tokenization\n", "in :numref:`sec_language-model`,\n", "for machine translation\n", "we prefer word-level tokenization here\n", "(today's state-of-the-art models use \n", "more complex tokenization techniques).\n", "The following `_tokenize` method\n", "tokenizes the first `max_examples` text sequence pairs,\n", "where each token is either a word or a punctuation mark.\n", "We append the special “&lt;eos&gt;” token\n", "to the end of every sequence to indicate the\n", "end of the sequence.\n", "When a model is predicting\n", "by generating a sequence token after token,\n", "the generation of the “&lt;eos&gt;” token\n", "can suggest that the output sequence is complete.\n", "In the end, the method below returns\n", "two lists of token lists: `src` and `tgt`.\n", "Specifically, `src[i]` is a list of tokens from the\n", "$i^\\textrm{th}$ text sequence in the source language (English here) \n", "and `tgt[i]` is that in the target language (French here).\n"]}, {"cell_type": "code", "execution_count": 6, "id": "c1fdeb00", "metadata": {"attributes": {"classes": [], "id": "", "n": "7"}, "execution": {"iopub.execute_input": "2023-08-18T19:25:41.824359Z", "iopub.status.busy": "2023-08-18T19:25:41.823783Z", "iopub.status.idle": "2023-08-18T19:25:41.829538Z", "shell.execute_reply": "2023-08-18T19:25:41.828715Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(MTFraEng)  #@save\n", "def _tokenize(self, text, max_examples=None):\n", "    src, tgt = [], []\n", "    for i, line in enumerate(text.split('\\n')):\n", "        if max_examples and i > max_examples: break\n", "        parts = line.split('\\t')\n", "        if len(parts) == 2:\n", "            # Skip empty tokens\n", "            src.append([t for t in f'{parts[0]} <eos>'.split(' ') if t])\n", "            tgt.append([t for t in f'{parts[1]} <eos>'.split(' ') if t])\n", "    return src, tgt"]}, {"cell_type": "code", "execution_count": 7, "id": "3bedb46e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:41.833010Z", "iopub.status.busy": "2023-08-18T19:25:41.832457Z", "iopub.status.idle": "2023-08-18T19:25:43.340916Z", "shell.execute_reply": "2023-08-18T19:25:43.340019Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["([['go', '.', '<eos>'],\n", "  ['hi', '.', '<eos>'],\n", "  ['run', '!', '<eos>'],\n", "  ['run', '!', '<eos>'],\n", "  ['who', '?', '<eos>'],\n", "  ['wow', '!', '<eos>']],\n", " [['va', '!', '<eos>'],\n", "  ['salut', '!', '<eos>'],\n", "  ['cours', '!', '<eos>'],\n", "  ['courez', '!', '<eos>'],\n", "  ['qui', '?', '<eos>'],\n", "  ['ça', 'alors', '!', '<eos>']])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["src, tgt = data._tokenize(text)\n", "src[:6], tgt[:6]"]}, {"cell_type": "markdown", "id": "3c8ca2e4", "metadata": {"origin_pos": 15}, "source": ["Let's [**plot the histogram of the number of tokens per text sequence.**]\n", "In this simple English--French dataset,\n", "most of the text sequences have fewer than 20 tokens.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "016131fa", "metadata": {"attributes": {"classes": [], "id": "", "n": "8"}, "execution": {"iopub.execute_input": "2023-08-18T19:25:43.344999Z", "iopub.status.busy": "2023-08-18T19:25:43.344240Z", "iopub.status.idle": "2023-08-18T19:25:43.350038Z", "shell.execute_reply": "2023-08-18T19:25:43.349161Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def show_list_len_pair_hist(legend, xlabel, ylabel, xlist, ylist):\n", "    \"\"\"Plot the histogram for list length pairs.\"\"\"\n", "    d2l.set_figsize()\n", "    _, _, patches = d2l.plt.hist(\n", "        [[len(l) for l in xlist], [len(l) for l in ylist]])\n", "    d2l.plt.xlabel(xlabel)\n", "    d2l.plt.ylabel(ylabel)\n", "    for patch in patches[1].patches:\n", "        patch.set_hatch('/')\n", "    d2l.plt.legend(legend)"]}, {"cell_type": "code", "execution_count": 9, "id": "b8fa90b4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:43.353348Z", "iopub.status.busy": "2023-08-18T19:25:43.352783Z", "iopub.status.idle": "2023-08-18T19:25:43.722959Z", "shell.execute_reply": "2023-08-18T19:25:43.722055Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"271.085198pt\" height=\"183.35625pt\" viewBox=\"0 0 271.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:25:43.667026</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 271.**********.35625 \n", "L 271.085198 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 66.**********.8 \n", "L 261.**********.8 \n", "L 261.353125 7.2 \n", "L 66.053125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 74.930398 145.8 \n", "L 82.177151 145.8 \n", "L 82.177151 13.8 \n", "L 74.930398 13.8 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 93.047281 145.8 \n", "L 100.294034 145.8 \n", "L 100.294034 70.342894 \n", "L 93.047281 70.342894 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 111.164164 145.8 \n", "L 118.410917 145.8 \n", "L 118.410917 141.170363 \n", "L 111.164164 141.170363 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 129.281047 145.8 \n", "L 136.5278 145.8 \n", "L 136.5278 145.34327 \n", "L 129.281047 145.34327 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 147.39793 145.8 \n", "L 154.644683 145.8 \n", "L 154.644683 145.744022 \n", "L 147.39793 145.744022 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 165.514813 145.8 \n", "L 172.761567 145.8 \n", "L 172.761567 145.783461 \n", "L 165.514813 145.783461 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 183.631696 145.8 \n", "L 190.87845 145.8 \n", "L 190.87845 145.792367 \n", "L 183.631696 145.792367 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 201.74858 145.8 \n", "L 208.995333 145.8 \n", "L 208.995333 145.798728 \n", "L 201.74858 145.798728 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 219.865463 145.8 \n", "L 227.112216 145.8 \n", "L 227.112216 145.797456 \n", "L 219.865463 145.797456 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 237.982346 145.8 \n", "L 245.229099 145.8 \n", "L 245.229099 145.8 \n", "L 237.982346 145.8 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 82.177151 145.8 \n", "L 89.423904 145.8 \n", "L 89.423904 26.776724 \n", "L 82.177151 26.776724 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: url(#h2f7cb2050f)\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 100.294034 145.8 \n", "L 107.540787 145.8 \n", "L 107.540787 60.494579 \n", "L 100.294034 60.494579 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: url(#h2f7cb2050f)\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 118.410917 145.8 \n", "L 125.65767 145.8 \n", "L 125.65767 138.604279 \n", "L 118.410917 138.604279 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: url(#h2f7cb2050f)\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 136.5278 145.8 \n", "L 143.774554 145.8 \n", "L 143.774554 144.858551 \n", "L 136.5278 144.858551 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: url(#h2f7cb2050f)\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 154.644683 145.8 \n", "L 161.891437 145.8 \n", "L 161.891437 145.691861 \n", "L 154.644683 145.691861 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: url(#h2f7cb2050f)\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 172.761567 145.8 \n", "L 180.00832 145.8 \n", "L 180.00832 145.758016 \n", "L 172.761567 145.758016 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: url(#h2f7cb2050f)\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 190.87845 145.8 \n", "L 198.125203 145.8 \n", "L 198.125203 145.797456 \n", "L 190.87845 145.797456 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: url(#h2f7cb2050f)\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 208.995333 145.8 \n", "L 216.242086 145.8 \n", "L 216.242086 145.797456 \n", "L 208.995333 145.797456 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: url(#h2f7cb2050f)\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 227.112216 145.8 \n", "L 234.358969 145.8 \n", "L 234.358969 145.797456 \n", "L 227.112216 145.797456 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: url(#h2f7cb2050f)\"/>\n", "   </g>\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 245.229099 145.8 \n", "L 252.475852 145.8 \n", "L 252.475852 145.796183 \n", "L 245.229099 145.796183 \n", "z\n", "\" clip-path=\"url(#pfd861e95f1)\" style=\"fill: url(#h2f7cb2050f)\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"md988411c33\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md988411c33\" x=\"128.11639\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(121.75389 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#md988411c33\" x=\"192.819544\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(186.457044 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#md988411c33\" x=\"257.522698\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(251.160198 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_4\">\n", "     <!-- # tokens per sequence -->\n", "     <g transform=\"translate(105.760937 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-23\" d=\"M 3272 2816 \n", "L 2363 2816 \n", "L 2100 1772 \n", "L 3016 1772 \n", "L 3272 2816 \n", "z\n", "M 2803 4594 \n", "L 2478 3297 \n", "L 3391 3297 \n", "L 3719 4594 \n", "L 4219 4594 \n", "L 3897 3297 \n", "L 4872 3297 \n", "L 4872 2816 \n", "L 3775 2816 \n", "L 3519 1772 \n", "L 4513 1772 \n", "L 4513 1294 \n", "L 3397 1294 \n", "L 3072 0 \n", "L 2572 0 \n", "L 2894 1294 \n", "L 1978 1294 \n", "L 1656 0 \n", "L 1153 0 \n", "L 1478 1294 \n", "L 494 1294 \n", "L 494 1772 \n", "L 1594 1772 \n", "L 1856 2816 \n", "L 850 2816 \n", "L 850 3297 \n", "L 1978 3297 \n", "L 2297 4594 \n", "L 2803 4594 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-71\" d=\"M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "M 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 -1331 \n", "L 2906 -1331 \n", "L 2906 525 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-23\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"83.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"115.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"154.785156\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"215.966797\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"270.251953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"331.775391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"395.154297\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"447.253906\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"479.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"542.517578\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"604.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"645.154297\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"676.941406\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"729.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-71\" x=\"790.564453\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"854.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"917.419922\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"978.943359\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"1042.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"1097.302734\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path id=\"mfae13e0441\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mfae13e0441\" x=\"66.053125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(52.690625 149.599219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mfae13e0441\" x=\"66.053125\" y=\"120.355443\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 20000 -->\n", "      <g transform=\"translate(27.240625 124.154662) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"254.492188\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mfae13e0441\" x=\"66.053125\" y=\"94.910886\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 40000 -->\n", "      <g transform=\"translate(27.240625 98.710105) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"254.492188\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mfae13e0441\" x=\"66.053125\" y=\"69.466329\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 60000 -->\n", "      <g transform=\"translate(27.240625 73.265548) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"254.492188\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mfae13e0441\" x=\"66.053125\" y=\"44.021772\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 80000 -->\n", "      <g transform=\"translate(27.240625 47.820991) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"254.492188\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mfae13e0441\" x=\"66.053125\" y=\"18.577216\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 100000 -->\n", "      <g transform=\"translate(20.878125 22.376434) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"190.869141\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"254.492188\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"318.115234\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- count -->\n", "     <g transform=\"translate(14.798438 90.60625) rotate(-90) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-63\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"54.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"116.162109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"179.541016\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"242.919922\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 66.**********.8 \n", "L 66.053125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 261.**********.8 \n", "L 261.353125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 66.**********.8 \n", "L 261.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 66.053125 7.2 \n", "L 261.353125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_27\">\n", "     <path d=\"M 189.15 44.55625 \n", "L 254.353125 44.55625 \n", "Q 256.353125 44.55625 256.353125 42.55625 \n", "L 256.353125 14.2 \n", "Q 256.353125 12.2 254.353125 12.2 \n", "L 189.15 12.2 \n", "Q 187.15 12.2 187.15 14.2 \n", "L 187.15 42.55625 \n", "Q 187.15 44.55625 189.15 44.55625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"patch_28\">\n", "     <path d=\"M 191.15 23.798438 \n", "L 211.15 23.798438 \n", "L 211.15 16.798438 \n", "L 191.15 16.798438 \n", "z\n", "\" style=\"fill: #1f77b4\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- source -->\n", "     <g transform=\"translate(219.15 23.798438) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-73\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"52.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"113.28125\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"176.660156\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"215.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"270.503906\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"patch_29\">\n", "     <path d=\"M 191.15 38.476562 \n", "L 211.15 38.476562 \n", "L 211.15 31.476562 \n", "L 191.15 31.476562 \n", "z\n", "\" style=\"fill: url(#h2f7cb2050f)\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- target -->\n", "     <g transform=\"translate(219.15 38.476562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"100.488281\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"139.851562\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"203.328125\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"264.851562\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pfd861e95f1\">\n", "   <rect x=\"66.053125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", " <defs>\n", "  <pattern id=\"h2f7cb2050f\" patternUnits=\"userSpaceOnUse\" x=\"0\" y=\"0\" width=\"72\" height=\"72\">\n", "   <rect x=\"0\" y=\"0\" width=\"73\" height=\"73\" fill=\"#ff7f0e\"/>\n", "   <path d=\"M -36 36 \n", "L 36 -36 \n", "M -24 48 \n", "L 48 -24 \n", "M -12 60 \n", "L 60 -12 \n", "M 0 72 \n", "L 72 0 \n", "M 12 84 \n", "L 84 12 \n", "M 24 96 \n", "L 96 24 \n", "M 36 108 \n", "L 108 36 \n", "\" style=\"fill: #000000; stroke: #000000; stroke-width: 1.0; stroke-linecap: butt; stroke-linejoin: miter\"/>\n", "  </pattern>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["show_list_len_pair_hist(['source', 'target'], '# tokens per sequence',\n", "                        'count', src, tgt);"]}, {"cell_type": "markdown", "id": "470e58cd", "metadata": {"origin_pos": 18}, "source": ["## Loading Sequences of Fixed Length\n", ":label:`subsec_loading-seq-fixed-len`\n", "\n", "Recall that in language modeling\n", "[**each example sequence**],\n", "either a segment of one sentence\n", "or a span over multiple sentences,\n", "(**had a fixed length.**)\n", "This was specified by the `num_steps`\n", "(number of time steps or tokens) argument from :numref:`sec_language-model`.\n", "In machine translation, each example is\n", "a pair of source and target text sequences,\n", "where the two text sequences may have different lengths.\n", "\n", "For computational efficiency,\n", "we can still process a minibatch of text sequences\n", "at one time by *truncation* and *padding*.\n", "Suppose that every sequence in the same minibatch\n", "should have the same length `num_steps`.\n", "If a text sequence has fewer than `num_steps` tokens,\n", "we will keep appending the special \"&lt;pad&gt;\" token\n", "to its end until its length reaches `num_steps`.\n", "Otherwise, we will truncate the text sequence\n", "by only taking its first `num_steps` tokens\n", "and discarding the remaining.\n", "In this way, every text sequence\n", "will have the same length\n", "to be loaded in minibatches of the same shape.\n", "Furthermore, we also record length of the source sequence excluding padding tokens.\n", "This information will be needed by some models that we will cover later.\n", "\n", "\n", "Since the machine translation dataset\n", "consists of pairs of languages,\n", "we can build two vocabularies for\n", "both the source language and\n", "the target language separately.\n", "With word-level tokenization,\n", "the vocabulary size will be significantly larger\n", "than that using character-level tokenization.\n", "To alleviate this,\n", "here we treat infrequent tokens\n", "that appear less than twice\n", "as the same unknown (\"&lt;unk&gt;\") token.\n", "As we will explain later (:numref:`fig_seq2seq`),\n", "when training with target sequences,\n", "the decoder output (label tokens)\n", "can be the same decoder input (target tokens),\n", "shifted by one token;\n", "and the special beginning-of-sequence \"&lt;bos&gt;\" token\n", "will be used as the first input token\n", "for predicting the target sequence (:numref:`fig_seq2seq_predict`).\n"]}, {"cell_type": "code", "execution_count": 10, "id": "499a8c37", "metadata": {"attributes": {"classes": [], "id": "", "n": "9"}, "execution": {"iopub.execute_input": "2023-08-18T19:25:43.726907Z", "iopub.status.busy": "2023-08-18T19:25:43.726170Z", "iopub.status.idle": "2023-08-18T19:25:43.731267Z", "shell.execute_reply": "2023-08-18T19:25:43.730455Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(MTFraEng)  #@save\n", "def __init__(self, batch_size, num_steps=9, num_train=512, num_val=128):\n", "    super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "    self.save_hyperparameters()\n", "    self.arrays, self.src_vocab, self.tgt_vocab = self._build_arrays(\n", "        self._download())"]}, {"cell_type": "code", "execution_count": 11, "id": "642915b2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:43.734774Z", "iopub.status.busy": "2023-08-18T19:25:43.734205Z", "iopub.status.idle": "2023-08-18T19:25:43.741936Z", "shell.execute_reply": "2023-08-18T19:25:43.741036Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(MTFraEng)  #@save\n", "def _build_arrays(self, raw_text, src_vocab=None, tgt_vocab=None):\n", "    def _build_array(sentences, vocab, is_tgt=False):\n", "        pad_or_trim = lambda seq, t: (\n", "            seq[:t] if len(seq) > t else seq + ['<pad>'] * (t - len(seq)))\n", "        sentences = [pad_or_trim(s, self.num_steps) for s in sentences]\n", "        if is_tgt:\n", "            sentences = [['<bos>'] + s for s in sentences]\n", "        if vocab is None:\n", "            vocab = d2l.Vocab(sentences, min_freq=2)\n", "        array = torch.tensor([vocab[s] for s in sentences])\n", "        valid_len = (array != vocab['<pad>']).type(torch.int32).sum(1)\n", "        return array, vocab, valid_len\n", "    src, tgt = self._tokenize(self._preprocess(raw_text),\n", "                              self.num_train + self.num_val)\n", "    src_array, src_vocab, src_valid_len = _build_array(src, src_vocab)\n", "    tgt_array, tgt_vocab, _ = _build_array(tgt, tgt_vocab, True)\n", "    return ((src_array, tgt_array[:,:-1], src_valid_len, tgt_array[:,1:]),\n", "            src_vocab, tgt_vocab)"]}, {"cell_type": "markdown", "id": "1db3140e", "metadata": {"origin_pos": 21}, "source": ["## [**Reading the Dataset**]\n", "\n", "Finally, we define the `get_dataloader` method\n", "to return the data iterator.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "5eb64a4b", "metadata": {"attributes": {"classes": [], "id": "", "n": "10"}, "execution": {"iopub.execute_input": "2023-08-18T19:25:43.745419Z", "iopub.status.busy": "2023-08-18T19:25:43.744866Z", "iopub.status.idle": "2023-08-18T19:25:43.749246Z", "shell.execute_reply": "2023-08-18T19:25:43.748444Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(MTFraEng)  #@save\n", "def get_dataloader(self, train):\n", "    idx = slice(0, self.num_train) if train else slice(self.num_train, None)\n", "    return self.get_tensorloader(self.arrays, train, idx)"]}, {"cell_type": "markdown", "id": "f7f295c6", "metadata": {"origin_pos": 23}, "source": ["Let's [**read the first minibatch from the English--French dataset.**]\n"]}, {"cell_type": "code", "execution_count": 13, "id": "ef39df99", "metadata": {"attributes": {"classes": [], "id": "", "n": "11"}, "execution": {"iopub.execute_input": "2023-08-18T19:25:43.752740Z", "iopub.status.busy": "2023-08-18T19:25:43.752195Z", "iopub.status.idle": "2023-08-18T19:25:47.133736Z", "shell.execute_reply": "2023-08-18T19:25:47.132842Z"}, "origin_pos": 24, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["source: tensor([[117, 182,   0,   3,   4,   4,   4,   4,   4],\n", "        [ 62,  72,   2,   3,   4,   4,   4,   4,   4],\n", "        [ 57, 124,   0,   3,   4,   4,   4,   4,   4]], dtype=torch.int32)\n", "decoder input: tensor([[  3,  37, 100,  58, 160,   0,   4,   5,   5],\n", "        [  3,   6,   2,   4,   5,   5,   5,   5,   5],\n", "        [  3, 180,   0,   4,   5,   5,   5,   5,   5]], dtype=torch.int32)\n", "source len excluding pad: tensor([4, 4, 4], dtype=torch.int32)\n", "label: tensor([[ 37, 100,  58, 160,   0,   4,   5,   5,   5],\n", "        [  6,   2,   4,   5,   5,   5,   5,   5,   5],\n", "        [180,   0,   4,   5,   5,   5,   5,   5,   5]], dtype=torch.int32)\n"]}], "source": ["data = MTFraEng(batch_size=3)\n", "src, tgt, src_valid_len, label = next(iter(data.train_dataloader()))\n", "print('source:', src.type(torch.int32))\n", "print('decoder input:', tgt.type(torch.int32))\n", "print('source len excluding pad:', src_valid_len.type(torch.int32))\n", "print('label:', label.type(torch.int32))"]}, {"cell_type": "markdown", "id": "1d0001a8", "metadata": {"origin_pos": 25}, "source": ["We show a pair of source and target sequences\n", "processed by the above `_build_arrays` method\n", "(in the string format).\n"]}, {"cell_type": "code", "execution_count": 14, "id": "e21903b8", "metadata": {"attributes": {"classes": [], "id": "", "n": "12"}, "execution": {"iopub.execute_input": "2023-08-18T19:25:47.137535Z", "iopub.status.busy": "2023-08-18T19:25:47.136938Z", "iopub.status.idle": "2023-08-18T19:25:47.141831Z", "shell.execute_reply": "2023-08-18T19:25:47.141037Z"}, "origin_pos": 26, "tab": ["pytorch"]}, "outputs": [], "source": ["@d2l.add_to_class(MTFraEng)  #@save\n", "def build(self, src_sentences, tgt_sentences):\n", "    raw_text = '\\n'.join([src + '\\t' + tgt for src, tgt in zip(\n", "        src_sentences, tgt_sentences)])\n", "    arrays, _, _ = self._build_arrays(\n", "        raw_text, self.src_vocab, self.tgt_vocab)\n", "    return arrays"]}, {"cell_type": "code", "execution_count": 15, "id": "102aced4", "metadata": {"attributes": {"classes": [], "id": "", "n": "13"}, "execution": {"iopub.execute_input": "2023-08-18T19:25:47.145332Z", "iopub.status.busy": "2023-08-18T19:25:47.144692Z", "iopub.status.idle": "2023-08-18T19:25:47.150415Z", "shell.execute_reply": "2023-08-18T19:25:47.149609Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["source: ['hi', '.', '<eos>', '<pad>', '<pad>', '<pad>', '<pad>', '<pad>', '<pad>']\n", "target: ['<bos>', 'salut', '.', '<eos>', '<pad>', '<pad>', '<pad>', '<pad>', '<pad>']\n"]}], "source": ["src, tgt, _,  _ = data.build(['hi .'], ['salut .'])\n", "print('source:', data.src_vocab.to_tokens(src[0].type(torch.int32)))\n", "print('target:', data.tgt_vocab.to_tokens(tgt[0].type(torch.int32)))"]}, {"cell_type": "markdown", "id": "e036b732", "metadata": {"origin_pos": 28}, "source": ["## Summary\n", "\n", "In natural language processing, *machine translation* refers to the task of automatically mapping from a sequence representing a string of text in a *source* language to a string representing a plausible translation in a *target* language. Using word-level tokenization, the vocabulary size will be significantly larger than that using character-level tokenization, but the sequence lengths will be much shorter. To mitigate the large vocabulary size, we can treat infrequent tokens as some \"unknown\" token. We can truncate and pad text sequences so that all of them will have the same length to be loaded in minibatches. Modern implementations often bucket sequences with similar lengths to avoid wasting excessive computation on padding. \n", "\n", "\n", "## Exercises\n", "\n", "1. Try different values of the `max_examples` argument in the `_tokenize` method. How does this affect the vocabulary sizes of the source language and the target language?\n", "1. Text in some languages such as Chinese and Japanese does not have word boundary indicators (e.g., space). Is word-level tokenization still a good idea for such cases? Why or why not?\n"]}, {"cell_type": "markdown", "id": "36e2ff84", "metadata": {"origin_pos": 30, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1060)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
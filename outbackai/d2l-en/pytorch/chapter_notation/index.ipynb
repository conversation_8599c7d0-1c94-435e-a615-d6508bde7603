{"cells": [{"cell_type": "markdown", "id": "1480fdf6", "metadata": {"origin_pos": 0}, "source": ["# Notation\n", ":label:`chap_notation`\n", "\n", "Throughout this book, we adhere \n", "to the following notational conventions.\n", "Note that some of these symbols are placeholders,\n", "while others refer to specific objects.\n", "As a general rule of thumb, \n", "the indefinite article \"a\" often indicates\n", "that the symbol is a placeholder\n", "and that similarly formatted symbols\n", "can denote other objects of the same type.\n", "For example, \"$x$: a scalar\" means \n", "that lowercased letters generally\n", "represent scalar values,\n", "but \"$\\mathbb{Z}$: the set of integers\"\n", "refers specifically to the symbol $\\mathbb{Z}$.\n", "\n", "\n", "\n", "## Numerical Objects\n", "\n", "* $x$: a scalar\n", "* $\\mathbf{x}$: a vector\n", "* $\\mathbf{X}$: a matrix\n", "* $\\mathsf{X}$: a general tensor\n", "* $\\mathbf{I}$: the identity matrix (of some given dimension), i.e., a square matrix with $1$ on all diagonal entries and $0$ on all off-diagonals\n", "* $x_i$, $[\\mathbf{x}]_i$: the $i^\\textrm{th}$ element of vector $\\mathbf{x}$\n", "* $x_{ij}$, $x_{i,j}$,$[\\mathbf{X}]_{ij}$, $[\\mathbf{X}]_{i,j}$: the element of matrix $\\mathbf{X}$ at row $i$ and column $j$.\n", "\n", "\n", "\n", "## Set Theory\n", "\n", "\n", "* $\\mathcal{X}$: a set\n", "* $\\mathbb{Z}$: the set of integers\n", "* $\\mathbb{Z}^+$: the set of positive integers\n", "* $\\mathbb{R}$: the set of real numbers\n", "* $\\mathbb{R}^n$: the set of $n$-dimensional vectors of real numbers\n", "* $\\mathbb{R}^{a\\times b}$: The set of matrices of real numbers with $a$ rows and $b$ columns\n", "* $|\\mathcal{X}|$: cardinality (number of elements) of set $\\mathcal{X}$\n", "* $\\mathcal{A}\\cup\\mathcal{B}$: union of sets $\\mathcal{A}$ and $\\mathcal{B}$\n", "* $\\mathcal{A}\\cap\\mathcal{B}$: intersection of sets $\\mathcal{A}$ and $\\mathcal{B}$\n", "* $\\mathcal{A}\\setminus\\mathcal{B}$: set subtraction of $\\mathcal{B}$ from $\\mathcal{A}$ (contains only those elements of $\\mathcal{A}$ that do not belong to $\\mathcal{B}$)\n", "\n", "\n", "\n", "## Functions and Operators\n", "\n", "\n", "* $f(\\cdot)$: a function\n", "* $\\log(\\cdot)$: the natural logarithm (base $e$)\n", "* $\\log_2(\\cdot)$: logarithm to base $2$\n", "* $\\exp(\\cdot)$: the exponential function\n", "* $\\mathbf{1}(\\cdot)$: the indicator function; evaluates to $1$ if the boolean argument is true, and $0$ otherwise\n", "* $\\mathbf{1}_{\\mathcal{X}}(z)$: the set-membership indicator function; evaluates to $1$ if the element $z$ belongs to the set $\\mathcal{X}$ and $0$ otherwise\n", "* $\\mathbf{(\\cdot)}^\\top$: transpose of a vector or a matrix\n", "* $\\mathbf{X}^{-1}$: inverse of matrix $\\mathbf{X}$\n", "* $\\odot$: Hadamard (elementwise) product\n", "* $[\\cdot, \\cdot]$: concatenation\n", "* $\\|\\cdot\\|_p$: $\\ell_p$ norm\n", "* $\\|\\cdot\\|$: $\\ell_2$ norm\n", "* $\\langle \\mathbf{x}, \\mathbf{y} \\rangle$: inner (dot) product of vectors $\\mathbf{x}$ and $\\mathbf{y}$\n", "* $\\sum$: summation over a collection of elements\n", "* $\\prod$: product over a collection of elements\n", "* $\\stackrel{\\textrm{def}}{=}$: an equality asserted as a definition of the symbol on the left-hand side\n", "\n", "\n", "\n", "## Calculus\n", "\n", "* $\\frac{dy}{dx}$: derivative of $y$ with respect to $x$\n", "* $\\frac{\\partial y}{\\partial x}$: partial derivative of $y$ with respect to $x$\n", "* $\\nabla_{\\mathbf{x}} y$: gradient of $y$ with respect to $\\mathbf{x}$\n", "* $\\int_a^b f(x) \\;dx$: definite integral of $f$ from $a$ to $b$ with respect to $x$\n", "* $\\int f(x) \\;dx$: indefinite integral of $f$ with respect to $x$\n", "\n", "\n", "\n", "## Probability and Information Theory\n", "\n", "* $X$: a random variable\n", "* $P$: a probability distribution\n", "* $X \\sim P$: the random variable $X$ follows distribution $P$\n", "* $P(X=x)$: the probability assigned to the event where random variable $X$ takes value $x$\n", "* $P(X \\mid Y)$: the conditional probability distribution of $X$ given $Y$\n", "* $p(\\cdot)$: a probability density function (PDF) associated with distribution $P$\n", "* ${E}[X]$: expectation of a random variable $X$\n", "* $X \\perp Y$: random variables $X$ and $Y$ are independent\n", "* $X \\perp Y \\mid Z$: random variables  $X$  and  $Y$ are conditionally independent given $Z$\n", "* $\\sigma_X$: standard deviation of random variable $X$\n", "* $\\textrm{Var}(X)$: variance of random variable $X$, equal to $\\sigma^2_X$\n", "* $\\textrm{Cov}(X, Y)$: covariance of random variables $X$ and $Y$\n", "* $\\rho(X, Y)$: the Pearson correlation coefficient between $X$ and $Y$, equals $\\frac{\\textrm{Cov}(X, Y)}{\\sigma_X \\sigma_Y}$\n", "* $H(X)$: entropy of random variable $X$\n", "* $D_{\\textrm{KL}}(P\\|Q)$: the KL-divergence (or relative entropy) from distribution $Q$ to distribution $P$\n", "\n", "\n", "\n", "[Discussions](https://discuss.d2l.ai/t/25)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "cebe44be", "metadata": {"origin_pos": 0}, "source": ["# Natural Language Inference: Using Attention\n", ":label:`sec_natural-language-inference-attention`\n", "\n", "We introduced the natural language inference task and the SNLI dataset in :numref:`sec_natural-language-inference-and-dataset`. In view of many models that are based on complex and deep architectures, :citet:`Parikh.Tackstrom.Das.ea.2016` proposed to address natural language inference with attention mechanisms and called it a \"decomposable attention model\".\n", "This results in a model without recurrent or convolutional layers, achieving the best result at the time on the SNLI dataset with much fewer parameters.\n", "In this section, we will describe and implement this attention-based method (with MLPs) for natural language inference, as depicted in :numref:`fig_nlp-map-nli-attention`.\n", "\n", "![This section feeds pretrained GloVe to an architecture based on attention and MLPs for natural language inference.](../img/nlp-map-nli-attention.svg)\n", ":label:`fig_nlp-map-nli-attention`\n", "\n", "\n", "## The Model\n", "\n", "Simpler than preserving the order of tokens in premises and hypotheses,\n", "we can just align tokens in one text sequence to every token in the other, and vice versa,\n", "then compare and aggregate such information to predict the logical relationships\n", "between premises and hypotheses.\n", "Similar to alignment of tokens between source and target sentences in machine translation,\n", "the alignment of tokens between premises and hypotheses\n", "can be neatly accomplished by attention mechanisms.\n", "\n", "![Natural language inference using attention mechanisms.](../img/nli-attention.svg)\n", ":label:`fig_nli_attention`\n", "\n", ":numref:`fig_nli_attention` depicts the natural language inference method using attention mechanisms.\n", "At a high level, it consists of three jointly trained steps: attending, comparing, and aggregating.\n", "We will illustrate them step by step in the following.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d0098871", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:47.909062Z", "iopub.status.busy": "2023-08-18T19:25:47.908527Z", "iopub.status.idle": "2023-08-18T19:25:50.712203Z", "shell.execute_reply": "2023-08-18T19:25:50.711230Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "7a5ef160", "metadata": {"origin_pos": 3}, "source": ["### Attending\n", "\n", "The first step is to align tokens in one text sequence to each token in the other sequence.\n", "Suppose that the premise is \"i do need sleep\" and the hypothesis is \"i am tired\".\n", "Due to semantical similarity,\n", "we may wish to align \"i\" in the hypothesis with \"i\" in the premise,\n", "and align \"tired\" in the hypothesis with \"sleep\" in the premise.\n", "Likewise, we may wish to align \"i\" in the premise with \"i\" in the hypothesis,\n", "and align \"need\" and \"sleep\" in the premise with \"tired\" in the hypothesis.\n", "Note that such alignment is *soft* using weighted average,\n", "where ideally large weights are associated with the tokens to be aligned.\n", "For ease of demonstration, :numref:`fig_nli_attention` shows such alignment in a *hard* way.\n", "\n", "Now we describe the soft alignment using attention mechanisms in more detail.\n", "Denote by $\\mathbf{A} = (\\mathbf{a}_1, \\ldots, \\mathbf{a}_m)$\n", "and $\\mathbf{B} = (\\mathbf{b}_1, \\ldots, \\mathbf{b}_n)$ the premise and hypothesis,\n", "whose number of tokens are $m$ and $n$, respectively,\n", "where $\\mathbf{a}_i, \\mathbf{b}_j \\in \\mathbb{R}^{d}$ ($i = 1, \\ldots, m, j = 1, \\ldots, n$) is a $d$-dimensional word vector.\n", "For soft alignment, we compute the attention weights $e_{ij} \\in \\mathbb{R}$ as\n", "\n", "$$e_{ij} = f(\\mathbf{a}_i)^\\top f(\\mathbf{b}_j),$$\n", ":eqlabel:`eq_nli_e`\n", "\n", "where the function $f$ is an MLP defined in the following `mlp` function.\n", "The output dimension of $f$ is specified by the `num_hiddens` argument of `mlp`.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "a9b18985", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:50.716350Z", "iopub.status.busy": "2023-08-18T19:25:50.715653Z", "iopub.status.idle": "2023-08-18T19:25:50.721751Z", "shell.execute_reply": "2023-08-18T19:25:50.720908Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [], "source": ["def mlp(num_inputs, num_hiddens, flatten):\n", "    net = []\n", "    net.append(nn.Dropout(0.2))\n", "    net.append(nn.Linear(num_inputs, num_hiddens))\n", "    net.append(nn.ReLU())\n", "    if flatten:\n", "        net.append(nn.<PERSON><PERSON>(start_dim=1))\n", "    net.append(nn.Dropout(0.2))\n", "    net.append(nn.Linear(num_hiddens, num_hiddens))\n", "    net.append(nn.ReLU())\n", "    if flatten:\n", "        net.append(nn.<PERSON><PERSON>(start_dim=1))\n", "    return nn.Sequential(*net)"]}, {"cell_type": "markdown", "id": "017af3a7", "metadata": {"origin_pos": 6}, "source": ["It should be highlighted that, in :eqref:`eq_nli_e`\n", "$f$ takes inputs $\\mathbf{a}_i$ and $\\mathbf{b}_j$ separately rather than takes a pair of them together as input.\n", "This *decomposition* trick leads to only $m + n$ applications (linear complexity) of $f$ rather than $mn$ applications\n", "(quadratic complexity).\n", "\n", "\n", "Normalizing the attention weights in :eqref:`eq_nli_e`,\n", "we compute the weighted average of all the token vectors in the hypothesis\n", "to obtain representation of the hypothesis that is softly aligned with the token indexed by $i$ in the premise:\n", "\n", "$$\n", "\\boldsymbol{\\beta}_i = \\sum_{j=1}^{n}\\frac{\\exp(e_{ij})}{ \\sum_{k=1}^{n} \\exp(e_{ik})} \\mathbf{b}_j.\n", "$$\n", "\n", "Likewise, we compute soft alignment of premise tokens for each token indexed by $j$ in the hypothesis:\n", "\n", "$$\n", "\\boldsymbol{\\alpha}_j = \\sum_{i=1}^{m}\\frac{\\exp(e_{ij})}{ \\sum_{k=1}^{m} \\exp(e_{kj})} \\mathbf{a}_i.\n", "$$\n", "\n", "Below we define the `Attend` class to compute the soft alignment of hypotheses (`beta`) with input premises `A` and soft alignment of premises (`alpha`) with input hypotheses `B`.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f54e4f2a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:50.725214Z", "iopub.status.busy": "2023-08-18T19:25:50.724546Z", "iopub.status.idle": "2023-08-18T19:25:50.730934Z", "shell.execute_reply": "2023-08-18T19:25:50.730108Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["class Attend(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self, num_inputs, num_hiddens, **kwargs):\n", "        super(Attend, self).__init__(**kwargs)\n", "        self.f = mlp(num_inputs, num_hiddens, flatten=False)\n", "\n", "    def forward(self, A, B):\n", "        # Shape of `A`/`B`: (`batch_size`, no. of tokens in sequence A/B,\n", "        # `embed_size`)\n", "        # Shape of `f_A`/`f_B`: (`batch_size`, no. of tokens in sequence A/B,\n", "        # `num_hiddens`)\n", "        f_A = self.f(A)\n", "        f_B = self.f(B)\n", "        # Shape of `e`: (`batch_size`, no. of tokens in sequence A,\n", "        # no. of tokens in sequence B)\n", "        e = torch.bmm(f_A, f_B.permute(0, 2, 1))\n", "        # Shape of `beta`: (`batch_size`, no. of tokens in sequence A,\n", "        # `embed_size`), where sequence B is softly aligned with each token\n", "        # (axis 1 of `beta`) in sequence A\n", "        beta = torch.bmm(<PERSON><PERSON>softmax(e, dim=-1), B)\n", "        # Shape of `alpha`: (`batch_size`, no. of tokens in sequence B,\n", "        # `embed_size`), where sequence A is softly aligned with each token\n", "        # (axis 1 of `alpha`) in sequence B\n", "        alpha = torch.bmm(<PERSON><PERSON>softmax(e.permute(0, 2, 1), dim=-1), A)\n", "        return beta, alpha"]}, {"cell_type": "markdown", "id": "2576c727", "metadata": {"origin_pos": 9}, "source": ["### Comparing\n", "\n", "In the next step, we compare a token in one sequence with the other sequence that is softly aligned with that token.\n", "Note that in soft alignment, all the tokens from one sequence, though with probably different attention weights, will be compared with a token in the other sequence.\n", "For easy of demonstration, :numref:`fig_nli_attention` pairs tokens with aligned tokens in a *hard* way.\n", "For example, suppose that the attending step determines that \"need\" and \"sleep\" in the premise are both aligned with \"tired\" in the hypothesis, the pair \"tired--need sleep\" will be compared.\n", "\n", "In the comparing step, we feed the concatenation (operator $[\\cdot, \\cdot]$) of tokens from one sequence and aligned tokens from the other sequence into a function $g$ (an MLP):\n", "\n", "$$\\mathbf{v}_{A,i} = g([\\mathbf{a}_i, \\boldsymbol{\\beta}_i]), i = 1, \\ldots, m\\\\ \\mathbf{v}_{B,j} = g([\\mathbf{b}_j, \\boldsymbol{\\alpha}_j]), j = 1, \\ldots, n.$$\n", "\n", ":eqlabel:`eq_nli_v_ab`\n", "\n", "\n", "In :eqref:`eq_nli_v_ab`, $\\mathbf{v}_{A,i}$ is the comparison between token $i$ in the premise and all the hypothesis tokens that are softly aligned with token $i$;\n", "while $\\mathbf{v}_{B,j}$ is the comparison between token $j$ in the hypothesis and all the premise tokens that are softly aligned with token $j$.\n", "The following `Compare` class defines such as comparing step.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "3ac23b52", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:50.734346Z", "iopub.status.busy": "2023-08-18T19:25:50.733684Z", "iopub.status.idle": "2023-08-18T19:25:50.739966Z", "shell.execute_reply": "2023-08-18T19:25:50.738723Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [], "source": ["class Compare(nn.Module):\n", "    def __init__(self, num_inputs, num_hiddens, **kwargs):\n", "        super(<PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.g = mlp(num_inputs, num_hiddens, flatten=False)\n", "\n", "    def forward(self, A, B, beta, alpha):\n", "        V_A = self.g(torch.cat([A, beta], dim=2))\n", "        V_B = self.g(torch.cat([B, alpha], dim=2))\n", "        return V_A, V_B"]}, {"cell_type": "markdown", "id": "64f65f11", "metadata": {"origin_pos": 12}, "source": ["### Aggregating\n", "\n", "With two sets of comparison vectors $\\mathbf{v}_{A,i}$ ($i = 1, \\ldots, m$) and $\\mathbf{v}_{B,j}$ ($j = 1, \\ldots, n$) on hand,\n", "in the last step we will aggregate such information to infer the logical relationship.\n", "We begin by summing up both sets:\n", "\n", "$$\n", "\\mathbf{v}_A = \\sum_{i=1}^{m} \\mathbf{v}_{A,i}, \\quad \\mathbf{v}_B = \\sum_{j=1}^{n}\\mathbf{v}_{B,j}.\n", "$$\n", "\n", "Next we feed the concatenation of both summarization results into function $h$ (an MLP) to obtain the classification result of the logical relationship:\n", "\n", "$$\n", "\\hat{\\mathbf{y}} = h([\\mathbf{v}_A, \\mathbf{v}_B]).\n", "$$\n", "\n", "The aggregation step is defined in the following `Aggregate` class.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "4eb1ef3b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:50.743698Z", "iopub.status.busy": "2023-08-18T19:25:50.742792Z", "iopub.status.idle": "2023-08-18T19:25:50.749599Z", "shell.execute_reply": "2023-08-18T19:25:50.748393Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["class Aggregate(nn.Module):\n", "    def __init__(self, num_inputs, num_hiddens, num_outputs, **kwargs):\n", "        super(Aggregate, self).__init__(**kwargs)\n", "        self.h = mlp(num_inputs, num_hiddens, flatten=True)\n", "        self.linear = nn.Linear(num_hiddens, num_outputs)\n", "\n", "    def forward(self, V_A, V_B):\n", "        # Sum up both sets of comparison vectors\n", "        V_A = V_A.sum(dim=1)\n", "        V_B = V_B.sum(dim=1)\n", "        # Feed the concatenation of both summarization results into an MLP\n", "        Y_hat = self.linear(self.h(torch.cat([V_A, V_B], dim=1)))\n", "        return Y_hat"]}, {"cell_type": "markdown", "id": "8a42ab92", "metadata": {"origin_pos": 15}, "source": ["### Putting It All Together\n", "\n", "By putting the attending, comparing, and aggregating steps together,\n", "we define the decomposable attention model to jointly train these three steps.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "f6c0d1c2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:50.753461Z", "iopub.status.busy": "2023-08-18T19:25:50.752655Z", "iopub.status.idle": "2023-08-18T19:25:50.760147Z", "shell.execute_reply": "2023-08-18T19:25:50.758904Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["class DecomposableAttention(nn.Module):\n", "    def __init__(self, vocab, embed_size, num_hiddens, num_inputs_attend=100,\n", "                 num_inputs_compare=200, num_inputs_agg=400, **kwargs):\n", "        super(Decomposable<PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.embedding = nn.Embedding(len(vocab), embed_size)\n", "        self.attend = Attend(num_inputs_attend, num_hiddens)\n", "        self.compare = Compare(num_inputs_compare, num_hiddens)\n", "        # There are 3 possible outputs: entailment, contradiction, and neutral\n", "        self.aggregate = Aggregate(num_inputs_agg, num_hiddens, num_outputs=3)\n", "\n", "    def forward(self, X):\n", "        premises, hypotheses = X\n", "        A = self.embedding(premises)\n", "        B = self.embedding(hypotheses)\n", "        beta, alpha = self.attend(A, B)\n", "        V_A, V_B = self.compare(A, B, beta, alpha)\n", "        Y_hat = self.aggregate(V_A, V_B)\n", "        return Y_hat"]}, {"cell_type": "markdown", "id": "b3f7aa33", "metadata": {"origin_pos": 18}, "source": ["## Training and Evaluating the Model\n", "\n", "Now we will train and evaluate the defined decomposable attention model on the SNLI dataset.\n", "We begin by reading the dataset.\n", "\n", "\n", "### Reading the dataset\n", "\n", "We download and read the SNLI dataset using the function defined in :numref:`sec_natural-language-inference-and-dataset`. The batch size and sequence length are set to $256$ and $50$, respectively.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "17a2d777", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:25:50.763482Z", "iopub.status.busy": "2023-08-18T19:25:50.762885Z", "iopub.status.idle": "2023-08-18T19:26:35.991825Z", "shell.execute_reply": "2023-08-18T19:26:35.990856Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/snli_1.0.zip from https://nlp.stanford.edu/projects/snli/snli_1.0.zip...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["read 549367 examples\n"]}, {"name": "stdout", "output_type": "stream", "text": ["read 9824 examples\n"]}], "source": ["batch_size, num_steps = 256, 50\n", "train_iter, test_iter, vocab = d2l.load_data_snli(batch_size, num_steps)"]}, {"cell_type": "markdown", "id": "c4cc8a73", "metadata": {"origin_pos": 20}, "source": ["### Creating the Model\n", "\n", "We use the pretrained 100-dimensional GloVe embedding to represent the input tokens.\n", "Thus, we predefine the dimension of vectors $\\mathbf{a}_i$ and $\\mathbf{b}_j$ in :eqref:`eq_nli_e` as 100.\n", "The output dimension of functions $f$ in :eqref:`eq_nli_e` and $g$ in :eqref:`eq_nli_v_ab` is set to 200.\n", "Then we create a model instance, initialize its parameters,\n", "and load the GloVe embedding to initialize vectors of input tokens.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "b839037e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:35.995390Z", "iopub.status.busy": "2023-08-18T19:26:35.995048Z", "iopub.status.idle": "2023-08-18T19:27:00.097946Z", "shell.execute_reply": "2023-08-18T19:27:00.096859Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/glove.6B.100d.zip from http://d2l-data.s3-accelerate.amazonaws.com/glove.6B.100d.zip...\n"]}], "source": ["embed_size, num_hiddens, devices = 100, 200, d2l.try_all_gpus()\n", "net = DecomposableAttention(vocab, embed_size, num_hiddens)\n", "glove_embedding = d2l.To<PERSON>('glove.6b.100d')\n", "embeds = glove_embedding[vocab.idx_to_token]\n", "net.embedding.weight.data.copy_(embeds);"]}, {"cell_type": "markdown", "id": "08c5a3d0", "metadata": {"origin_pos": 23}, "source": ["### Training and Evaluating the Model\n", "\n", "In contrast to the `split_batch` function in :numref:`sec_multi_gpu` that takes single inputs such as text sequences (or images),\n", "we define a `split_batch_multi_inputs` function to take multiple inputs such as premises and hypotheses in minibatches.\n"]}, {"cell_type": "markdown", "id": "fd998bf4", "metadata": {"origin_pos": 25}, "source": ["Now we can train and evaluate the model on the SNLI dataset.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "4e974bf9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:27:00.102008Z", "iopub.status.busy": "2023-08-18T19:27:00.101405Z", "iopub.status.idle": "2023-08-18T19:29:16.319833Z", "shell.execute_reply": "2023-08-18T19:29:16.318802Z"}, "origin_pos": 27, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.496, train acc 0.805, test acc 0.828\n", "20383.2 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"240.554687pt\" height=\"187.155469pt\" viewBox=\"0 0 240.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:29:16.262784</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 240.**********.155469 \n", "L 240.554687 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mb0defda810\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb0defda810\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(22.151563 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 62.653125 149.599219 \n", "L 62.653125 10.999219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mb0defda810\" x=\"62.653125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(54.701563 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 95.203125 149.599219 \n", "L 95.203125 10.999219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mb0defda810\" x=\"95.203125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(87.251563 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 127.753125 149.599219 \n", "L 127.753125 10.999219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mb0defda810\" x=\"127.753125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(119.801563 164.197656) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 160.303125 149.599219 \n", "L 160.303125 10.999219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mb0defda810\" x=\"160.303125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 3.0 -->\n", "      <g transform=\"translate(152.351562 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 192.853125 149.599219 \n", "L 192.853125 10.999219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mb0defda810\" x=\"192.853125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 3.5 -->\n", "      <g transform=\"translate(184.901562 164.197656) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mb0defda810\" x=\"225.403125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 4.0 -->\n", "      <g transform=\"translate(217.451562 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_8\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 177.875781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <defs>\n", "       <path id=\"m57e7bcd396\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m57e7bcd396\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 153.398438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 121.879219 \n", "L 225.403125 121.879219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m57e7bcd396\" x=\"30.103125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 125.678438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 94.159219 \n", "L 225.403125 94.159219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m57e7bcd396\" x=\"30.103125\" y=\"94.159219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 97.958438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 66.439219 \n", "L 225.403125 66.439219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m57e7bcd396\" x=\"30.103125\" y=\"66.439219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 70.238437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 30.103125 38.719219 \n", "L 225.403125 38.719219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m57e7bcd396\" x=\"30.103125\" y=\"38.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.518438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_25\">\n", "      <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m57e7bcd396\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M -1 35.328355 \n", "L 4.044924 38.394835 \n", "L 17.058857 43.950759 \n", "L 30.072789 47.866217 \n", "L 30.103125 47.874937 \n", "L 43.117058 68.836038 \n", "L 56.130991 69.043402 \n", "L 69.144924 69.612485 \n", "L 82.158857 70.351808 \n", "L 95.172789 70.840877 \n", "L 95.203125 70.845295 \n", "L 108.217058 76.376262 \n", "L 121.230991 76.341545 \n", "L 134.244924 76.722247 \n", "L 147.258857 76.925013 \n", "L 160.272789 77.125513 \n", "L 160.303125 77.124747 \n", "L 173.317058 80.644205 \n", "L 186.330991 80.639765 \n", "L 199.344924 80.94797 \n", "L 212.358857 80.85561 \n", "L 225.372789 80.885048 \n", "L 225.403125 80.884294 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M -1 63.709486 \n", "L 4.044924 61.649099 \n", "L 17.058857 58.130589 \n", "L 30.072789 55.685805 \n", "L 30.103125 55.680094 \n", "L 43.117058 43.399038 \n", "L 56.130991 43.395883 \n", "L 69.144924 43.141166 \n", "L 82.158857 42.768029 \n", "L 95.172789 42.564339 \n", "L 95.203125 42.563266 \n", "L 108.217058 40.07488 \n", "L 121.230991 40.084345 \n", "L 134.244924 39.92512 \n", "L 147.258857 39.84393 \n", "L 160.272789 39.790421 \n", "L 160.303125 39.790595 \n", "L 173.317058 38.007692 \n", "L 186.330991 38.0689 \n", "L 199.344924 37.908413 \n", "L 212.358857 37.990024 \n", "L 225.372789 38.015769 \n", "L 225.403125 38.017751 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 30.103125 40.866503 \n", "L 95.203125 37.31121 \n", "L 160.303125 36.436495 \n", "L 225.403125 34.785823 \n", "\" clip-path=\"url(#pbf67311279)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 37.103125 144.599219 \n", "L 114.871875 144.599219 \n", "Q 116.871875 144.599219 116.871875 142.599219 \n", "L 116.871875 99.564844 \n", "Q 116.871875 97.564844 114.871875 97.564844 \n", "L 37.103125 97.564844 \n", "Q 35.103125 97.564844 35.103125 99.564844 \n", "L 35.103125 142.599219 \n", "Q 35.103125 144.599219 37.103125 144.599219 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 39.103125 105.663281 \n", "L 49.103125 105.663281 \n", "L 59.103125 105.663281 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(67.103125 109.163281) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_31\">\n", "     <path d=\"M 39.103125 120.341406 \n", "L 49.103125 120.341406 \n", "L 59.103125 120.341406 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(67.103125 123.841406) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_32\">\n", "     <path d=\"M 39.103125 135.019531 \n", "L 49.103125 135.019531 \n", "L 59.103125 135.019531 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(67.103125 138.519531) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pbf67311279\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lr, num_epochs = 0.001, 4\n", "trainer = torch.optim.Adam(net.parameters(), lr=lr)\n", "loss = nn.CrossEntropyLoss(reduction=\"none\")\n", "d2l.train_ch13(net, train_iter, test_iter, loss, trainer, num_epochs, devices)"]}, {"cell_type": "markdown", "id": "95205eb7", "metadata": {"origin_pos": 28}, "source": ["### Using the Model\n", "\n", "Finally, define the prediction function to output the logical relationship between a pair of premise and hypothesis.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "fd297baf", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:16.323274Z", "iopub.status.busy": "2023-08-18T19:29:16.322982Z", "iopub.status.idle": "2023-08-18T19:29:16.330300Z", "shell.execute_reply": "2023-08-18T19:29:16.329139Z"}, "origin_pos": 30, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def predict_snli(net, vocab, premise, hypothesis):\n", "    \"\"\"Predict the logical relationship between the premise and hypothesis.\"\"\"\n", "    net.eval()\n", "    premise = torch.tensor(vocab[premise], device=d2l.try_gpu())\n", "    hypothesis = torch.tensor(vocab[hypothesis], device=d2l.try_gpu())\n", "    label = torch.argmax(net([premise.reshape((1, -1)),\n", "                           hypothesis.reshape((1, -1))]), dim=1)\n", "    return 'entailment' if label == 0 else 'contradiction' if label == 1 \\\n", "            else 'neutral'"]}, {"cell_type": "markdown", "id": "08e8653b", "metadata": {"origin_pos": 31}, "source": ["We can use the trained model to obtain the natural language inference result for a sample pair of sentences.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "7f906422", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:16.333494Z", "iopub.status.busy": "2023-08-18T19:29:16.333220Z", "iopub.status.idle": "2023-08-18T19:29:16.352434Z", "shell.execute_reply": "2023-08-18T19:29:16.351611Z"}, "origin_pos": 32, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'contradiction'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["predict_snli(net, vocab, ['he', 'is', 'good', '.'], ['he', 'is', 'bad', '.'])"]}, {"cell_type": "markdown", "id": "6e1ed459", "metadata": {"origin_pos": 33}, "source": ["## Summary\n", "\n", "* The decomposable attention model consists of three steps for predicting the logical relationships between premises and hypotheses: attending, comparing, and aggregating.\n", "* With attention mechanisms, we can align tokens in one text sequence to every token in the other, and vice versa. Such alignment is soft using weighted average, where ideally large weights are associated with the tokens to be aligned.\n", "* The decomposition trick leads to a more desirable linear complexity than quadratic complexity when computing attention weights.\n", "* We can use pretrained word vectors as the input representation for downstream natural language processing task such as natural language inference.\n", "\n", "\n", "## Exercises\n", "\n", "1. Train the model with other combinations of hyperparameters. Can you get better accuracy on the test set?\n", "1. What are major drawbacks of the decomposable attention model for natural language inference?\n", "1. Suppose that we want to get the level of semantical similarity (e.g., a continuous value between 0 and 1) for any pair of sentences. How shall we collect and label the dataset? Can you design a model with attention mechanisms?\n"]}, {"cell_type": "markdown", "id": "71ef7112", "metadata": {"origin_pos": 35, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1530)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
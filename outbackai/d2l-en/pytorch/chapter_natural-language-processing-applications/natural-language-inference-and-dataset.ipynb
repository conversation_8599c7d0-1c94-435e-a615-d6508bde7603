{"cells": [{"cell_type": "markdown", "id": "68096c17", "metadata": {"origin_pos": 0}, "source": ["# Natural Language Inference and the Dataset\n", ":label:`sec_natural-language-inference-and-dataset`\n", "\n", "In :numref:`sec_sentiment`, we discussed the problem of sentiment analysis.\n", "This task aims to classify a single text sequence into predefined categories,\n", "such as a set of sentiment polarities.\n", "However, when there is a need to decide whether one sentence can be inferred form another, \n", "or eliminate redundancy by identifying sentences that are semantically equivalent,\n", "knowing how to classify one text sequence is insufficient.\n", "Instead, we need to be able to reason over pairs of text sequences.\n", "\n", "\n", "## Natural Language Inference\n", "\n", "*Natural language inference* studies whether a *hypothesis*\n", "can be inferred from a *premise*, where both are a text sequence.\n", "In other words, natural language inference determines the logical relationship between a pair of text sequences.\n", "Such relationships usually fall into three types:\n", "\n", "* *Entailment*: the hypothesis can be inferred from the premise.\n", "* *Contradiction*: the negation of the hypothesis can be inferred from the premise.\n", "* *Neutral*: all the other cases.\n", "\n", "Natural language inference is also known as the recognizing textual entailment task.\n", "For example, the following pair will be labeled as *entailment* because \"showing affection\" in the hypothesis can be inferred from \"hugging one another\" in the premise.\n", "\n", "> Premise: Two women are hugging each other.\n", "\n", "> Hypothesis: Two women are showing affection.\n", "\n", "The following is an example of *contradiction* as \"running the coding example\" indicates \"not sleeping\" rather than \"sleeping\".\n", "\n", "> Premise: A man is running the coding example from Dive into Deep Learning.\n", "\n", "> Hypothesis: The man is sleeping.\n", "\n", "The third example shows a *neutrality* relationship because neither \"famous\" nor \"not famous\" can be inferred from the fact that \"are performing for us\". \n", "\n", "> Premise: The musicians are performing for us.\n", "\n", "> Hypothesis: The musicians are famous.\n", "\n", "Natural language inference has been a central topic for understanding natural language.\n", "It enjoys wide applications ranging from\n", "information retrieval to open-domain question answering.\n", "To study this problem, we will begin by investigating a popular natural language inference benchmark dataset.\n", "\n", "\n", "## The Stanford Natural Language Inference (SNLI) Dataset\n", "\n", "[**Stanford Natural Language Inference (SNLI) Corpus**] is a collection of over 500000 labeled English sentence pairs :cite:`Bowman.Angeli.Potts.ea.2015`.\n", "We download and store the extracted SNLI dataset in the path `../data/snli_1.0`.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "2a77a4ae", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:21.484092Z", "iopub.status.busy": "2023-08-18T19:26:21.483477Z", "iopub.status.idle": "2023-08-18T19:26:29.377743Z", "shell.execute_reply": "2023-08-18T19:26:29.376828Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import os\n", "import re\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l\n", "\n", "#@save\n", "d2l.DATA_HUB['SNLI'] = (\n", "    'https://nlp.stanford.edu/projects/snli/snli_1.0.zip',\n", "    '9fcde07509c7e87ec61c640c1b2753d9041758e4')\n", "\n", "data_dir = d2l.download_extract('SNLI')"]}, {"cell_type": "markdown", "id": "98299766", "metadata": {"origin_pos": 3}, "source": ["### [**Reading the Dataset**]\n", "\n", "The original SNLI dataset contains much richer information than what we really need in our experiments. Thus, we define a function `read_snli` to only extract part of the dataset, then return lists of premises, hypotheses, and their labels.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "15051d2a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:29.381653Z", "iopub.status.busy": "2023-08-18T19:26:29.381256Z", "iopub.status.idle": "2023-08-18T19:26:29.388949Z", "shell.execute_reply": "2023-08-18T19:26:29.388184Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def read_snli(data_dir, is_train):\n", "    \"\"\"Read the SNLI dataset into premises, hypotheses, and labels.\"\"\"\n", "    def extract_text(s):\n", "        # Remove information that will not be used by us\n", "        s = re.sub('\\\\(', '', s)\n", "        s = re.sub('\\\\)', '', s)\n", "        # Substitute two or more consecutive whitespace with space\n", "        s = re.sub('\\\\s{2,}', ' ', s)\n", "        return s.strip()\n", "    label_set = {'entailment': 0, 'contradiction': 1, 'neutral': 2}\n", "    file_name = os.path.join(data_dir, 'snli_1.0_train.txt'\n", "                             if is_train else 'snli_1.0_test.txt')\n", "    with open(file_name, 'r') as f:\n", "        rows = [row.split('\\t') for row in f.readlines()[1:]]\n", "    premises = [extract_text(row[1]) for row in rows if row[0] in label_set]\n", "    hypotheses = [extract_text(row[2]) for row in rows if row[0] in label_set]\n", "    labels = [label_set[row[0]] for row in rows if row[0] in label_set]\n", "    return premises, hypotheses, labels"]}, {"cell_type": "markdown", "id": "c5708690", "metadata": {"origin_pos": 5}, "source": ["Now let's [**print the first 3 pairs**] of premise and hypothesis, as well as their labels (\"0\", \"1\", and \"2\" correspond to \"entailment\", \"contradiction\", and \"neutral\", respectively ).\n"]}, {"cell_type": "code", "execution_count": 3, "id": "2c2e177a", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:29.392028Z", "iopub.status.busy": "2023-08-18T19:26:29.391753Z", "iopub.status.idle": "2023-08-18T19:26:41.816256Z", "shell.execute_reply": "2023-08-18T19:26:41.815386Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["premise: A person on a horse jumps over a broken down airplane .\n", "hypothesis: A person is training his horse for a competition .\n", "label: 2\n", "premise: A person on a horse jumps over a broken down airplane .\n", "hypothesis: A person is at a diner , ordering an omelette .\n", "label: 1\n", "premise: A person on a horse jumps over a broken down airplane .\n", "hypothesis: A person is outdoors , on a horse .\n", "label: 0\n"]}], "source": ["train_data = read_snli(data_dir, is_train=True)\n", "for x0, x1, y in zip(train_data[0][:3], train_data[1][:3], train_data[2][:3]):\n", "    print('premise:', x0)\n", "    print('hypothesis:', x1)\n", "    print('label:', y)"]}, {"cell_type": "markdown", "id": "02260619", "metadata": {"origin_pos": 7}, "source": ["The training set has about 550000 pairs,\n", "and the testing set has about 10000 pairs.\n", "The following shows that \n", "the three [**labels \"entailment\", \"contradiction\", and \"neutral\" are balanced**] in \n", "both the training set and the testing set.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "3399eb6f", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:41.821385Z", "iopub.status.busy": "2023-08-18T19:26:41.820790Z", "iopub.status.idle": "2023-08-18T19:26:42.248143Z", "shell.execute_reply": "2023-08-18T19:26:42.247216Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[183416, 183187, 182764]\n", "[3368, 3237, 3219]\n"]}], "source": ["test_data = read_snli(data_dir, is_train=False)\n", "for data in [train_data, test_data]:\n", "    print([[row for row in data[2]].count(i) for i in range(3)])"]}, {"cell_type": "markdown", "id": "aa0daebd", "metadata": {"origin_pos": 9}, "source": ["### [**Defining a Class for Loading the Dataset**]\n", "\n", "Below we define a class for loading the SNLI dataset by inheriting from the `Dataset` class in Gluon. The argument `num_steps` in the class constructor specifies the length of a text sequence so that each minibatch of sequences will have the same shape. \n", "In other words,\n", "tokens after the first `num_steps` ones in longer sequence are trimmed, while special tokens “&lt;pad&gt;” will be appended to shorter sequences until their length becomes `num_steps`.\n", "By implementing the `__getitem__` function, we can arbitrarily access the premise, hypothesis, and label with the index `idx`.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "d3dd55aa", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:42.251762Z", "iopub.status.busy": "2023-08-18T19:26:42.251121Z", "iopub.status.idle": "2023-08-18T19:26:42.259106Z", "shell.execute_reply": "2023-08-18T19:26:42.258283Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "class SNLIDataset(torch.utils.data.Dataset):\n", "    \"\"\"A customized dataset to load the SNLI dataset.\"\"\"\n", "    def __init__(self, dataset, num_steps, vocab=None):\n", "        self.num_steps = num_steps\n", "        all_premise_tokens = d2l.tokenize(dataset[0])\n", "        all_hypothesis_tokens = d2l.tokenize(dataset[1])\n", "        if vocab is None:\n", "            self.vocab = d2l.Vocab(all_premise_tokens + all_hypothesis_tokens,\n", "                                   min_freq=5, reserved_tokens=['<pad>'])\n", "        else:\n", "            self.vocab = vocab\n", "        self.premises = self._pad(all_premise_tokens)\n", "        self.hypotheses = self._pad(all_hypothesis_tokens)\n", "        self.labels = torch.tensor(dataset[2])\n", "        print('read ' + str(len(self.premises)) + ' examples')\n", "\n", "    def _pad(self, lines):\n", "        return torch.tensor([d2l.truncate_pad(\n", "            self.vocab[line], self.num_steps, self.vocab['<pad>'])\n", "                         for line in lines])\n", "\n", "    def __getitem__(self, idx):\n", "        return (self.premises[idx], self.hypotheses[idx]), self.labels[idx]\n", "\n", "    def __len__(self):\n", "        return len(self.premises)"]}, {"cell_type": "markdown", "id": "a99d881e", "metadata": {"origin_pos": 12}, "source": ["### [**Putting It All Together**]\n", "\n", "Now we can invoke the `read_snli` function and the `SNLIDataset` class to download the SNLI dataset and return `DataLoader` instances for both training and testing sets, together with the vocabulary of the training set.\n", "It is noteworthy that we must use the vocabulary constructed from the training set\n", "as that of the testing set. \n", "As a result, any new token from the testing set will be unknown to the model trained on the training set.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "af25e511", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:42.262443Z", "iopub.status.busy": "2023-08-18T19:26:42.261877Z", "iopub.status.idle": "2023-08-18T19:26:42.267478Z", "shell.execute_reply": "2023-08-18T19:26:42.266590Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["#@save\n", "def load_data_snli(batch_size, num_steps=50):\n", "    \"\"\"Download the SNLI dataset and return data iterators and vocabulary.\"\"\"\n", "    num_workers = d2l.get_dataloader_workers()\n", "    data_dir = d2l.download_extract('SNLI')\n", "    train_data = read_snli(data_dir, True)\n", "    test_data = read_snli(data_dir, False)\n", "    train_set = SNLIDataset(train_data, num_steps)\n", "    test_set = SNLIDataset(test_data, num_steps, train_set.vocab)\n", "    train_iter = torch.utils.data.DataLoader(train_set, batch_size,\n", "                                             shuffle=True,\n", "                                             num_workers=num_workers)\n", "    test_iter = torch.utils.data.DataLoader(test_set, batch_size,\n", "                                            shuffle=False,\n", "                                            num_workers=num_workers)\n", "    return train_iter, test_iter, train_set.vocab"]}, {"cell_type": "markdown", "id": "52342d52", "metadata": {"origin_pos": 15}, "source": ["Here we set the batch size to 128 and sequence length to 50,\n", "and invoke the `load_data_snli` function to get the data iterators and vocabulary.\n", "Then we print the vocabulary size.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "16d10217", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:26:42.270825Z", "iopub.status.busy": "2023-08-18T19:26:42.270189Z", "iopub.status.idle": "2023-08-18T19:27:26.541090Z", "shell.execute_reply": "2023-08-18T19:27:26.540150Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["read 549367 examples\n"]}, {"name": "stdout", "output_type": "stream", "text": ["read 9824 examples\n"]}, {"data": {"text/plain": ["18678"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["train_iter, test_iter, vocab = load_data_snli(128, 50)\n", "len(vocab)"]}, {"cell_type": "markdown", "id": "57fba2f1", "metadata": {"origin_pos": 17}, "source": ["Now we print the shape of the first minibatch.\n", "Contrary to sentiment analysis,\n", "we have two inputs `X[0]` and `X[1]` representing pairs of premises and hypotheses.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "4f3cf1e7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:27:26.544797Z", "iopub.status.busy": "2023-08-18T19:27:26.544188Z", "iopub.status.idle": "2023-08-18T19:27:26.884432Z", "shell.execute_reply": "2023-08-18T19:27:26.883416Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([128, 50])\n", "torch.<PERSON><PERSON>([128, 50])\n", "<PERSON>.<PERSON><PERSON>([128])\n"]}], "source": ["for X, Y in train_iter:\n", "    print(X[0].shape)\n", "    print(X[1].shape)\n", "    print(Y.shape)\n", "    break"]}, {"cell_type": "markdown", "id": "85258a9e", "metadata": {"origin_pos": 19}, "source": ["## Summary\n", "\n", "* Natural language inference studies whether a hypothesis can be inferred from a premise, where both are a text sequence.\n", "* In natural language inference, relationships between premises and hypotheses include entailment, contradiction, and neutral.\n", "* Stanford Natural Language Inference (SNLI) Corpus is a popular benchmark dataset of natural language inference.\n", "\n", "\n", "## Exercises\n", "\n", "1. Machine translation has long been evaluated based on superficial $n$-gram matching between an output translation and a ground-truth translation. Can you design a measure for evaluating machine translation results by using natural language inference?\n", "1. How can we change hyperparameters to reduce the vocabulary size?\n"]}, {"cell_type": "markdown", "id": "2f98055e", "metadata": {"origin_pos": 21, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1388)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
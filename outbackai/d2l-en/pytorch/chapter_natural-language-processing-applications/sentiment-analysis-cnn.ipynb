{"cells": [{"cell_type": "markdown", "id": "81d18467", "metadata": {"origin_pos": 0}, "source": ["# Sentiment Analysis: Using Convolutional Neural Networks\n", ":label:`sec_sentiment_cnn` \n", "\n", "\n", "In :numref:`chap_cnn`,\n", "we investigated mechanisms\n", "for processing\n", "two-dimensional image data\n", "with two-dimensional CNNs,\n", "which were applied to\n", "local features such as adjacent pixels.\n", "Though originally\n", "designed for computer vision,\n", "CNNs are also widely used\n", "for natural language processing.\n", "Simply put,\n", "just think of any text sequence\n", "as a one-dimensional image.\n", "In this way,\n", "one-dimensional CNNs\n", "can process local features\n", "such as $n$-grams in text.\n", "\n", "In this section,\n", "we will use the *textCNN* model\n", "to demonstrate\n", "how to design a CNN architecture\n", "for representing single text :cite:`<PERSON>.2014`.\n", "Compared with\n", ":numref:`fig_nlp-map-sa-rnn`\n", "that uses an RNN architecture with GloVe pretraining\n", "for sentiment analysis,\n", "the only difference in :numref:`fig_nlp-map-sa-cnn`\n", "lies in\n", "the choice of the architecture.\n", "\n", "\n", "![This section feeds pretrained GloVe to a CNN-based architecture for sentiment analysis.](../img/nlp-map-sa-cnn.svg)\n", ":label:`fig_nlp-map-sa-cnn`\n"]}, {"cell_type": "code", "execution_count": 1, "id": "4b47a220", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:10.301477Z", "iopub.status.busy": "2023-08-18T19:32:10.300459Z", "iopub.status.idle": "2023-08-18T19:32:51.806728Z", "shell.execute_reply": "2023-08-18T19:32:51.805738Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from d2l import torch as d2l\n", "\n", "batch_size = 64\n", "train_iter, test_iter, vocab = d2l.load_data_imdb(batch_size)"]}, {"cell_type": "markdown", "id": "5030f206", "metadata": {"origin_pos": 3}, "source": ["## One-Dimensional Convolutions\n", "\n", "Before introducing the model,\n", "let's see how a one-dimensional convolution works.\n", "Bear in mind that it is just a special case\n", "of a two-dimensional convolution\n", "based on the cross-correlation operation.\n", "\n", "![One-dimensional cross-correlation operation. The shaded portions are the first output element as well as the input and kernel tensor elements used for the output computation: $0\\times1+1\\times2=2$.](../img/conv1d.svg)\n", ":label:`fig_conv1d`\n", "\n", "As shown in :numref:`fig_conv1d`,\n", "in the one-dimensional case,\n", "the convolution window\n", "slides from left to right\n", "across the input tensor.\n", "During sliding,\n", "the input subtensor (e.g., $0$ and $1$ in :numref:`fig_conv1d`) contained in the convolution window\n", "at a certain position\n", "and the kernel tensor (e.g., $1$ and $2$ in :numref:`fig_conv1d`) are multiplied elementwise.\n", "The sum of these multiplications\n", "gives the single scalar value (e.g., $0\\times1+1\\times2=2$ in :numref:`fig_conv1d`)\n", "at the corresponding position of the output tensor.\n", "\n", "We implement one-dimensional cross-correlation in the following `corr1d` function.\n", "Given an input tensor `X`\n", "and a kernel tensor `K`,\n", "it returns the output tensor `Y`.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "6ae6e72e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:51.811182Z", "iopub.status.busy": "2023-08-18T19:32:51.810342Z", "iopub.status.idle": "2023-08-18T19:32:51.815496Z", "shell.execute_reply": "2023-08-18T19:32:51.814589Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [], "source": ["def corr1d(X, K):\n", "    w = K.shape[0]\n", "    Y = torch.zeros((X.shape[0] - w + 1))\n", "    for i in range(Y.shape[0]):\n", "        Y[i] = (X[i: i + w] * K).sum()\n", "    return Y"]}, {"cell_type": "markdown", "id": "367d6f01", "metadata": {"origin_pos": 5}, "source": ["We can construct the input tensor `X` and the kernel tensor `K` from :numref:`fig_conv1d` to validate the output of the above one-dimensional cross-correlation implementation.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "558e391c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:51.818896Z", "iopub.status.busy": "2023-08-18T19:32:51.818354Z", "iopub.status.idle": "2023-08-18T19:32:51.827278Z", "shell.execute_reply": "2023-08-18T19:32:51.826469Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([ 2.,  5.,  8., 11., 14., 17.])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["X, K = torch.tensor([0, 1, 2, 3, 4, 5, 6]), torch.tensor([1, 2])\n", "corr1d(X, K)"]}, {"cell_type": "markdown", "id": "a61afb5e", "metadata": {"origin_pos": 7}, "source": ["For any\n", "one-dimensional input with multiple channels,\n", "the convolution kernel\n", "needs to have the same number of input channels.\n", "Then for each channel,\n", "perform a cross-correlation operation on the one-dimensional tensor of the input and the one-dimensional tensor of the convolution kernel,\n", "summing the results over all the channels\n", "to produce the one-dimensional output tensor.\n", ":numref:`fig_conv1d_channel` shows a one-dimensional cross-correlation operation with 3 input channels.\n", "\n", "![One-dimensional cross-correlation operation with 3 input channels. The shaded portions are the first output element as well as the input and kernel tensor elements used for the output computation: $0\\times1+1\\times2+1\\times3+2\\times4+2\\times(-1)+3\\times(-3)=2$.](../img/conv1d-channel.svg)\n", ":label:`fig_conv1d_channel`\n", "\n", "\n", "We can implement the one-dimensional cross-correlation operation for multiple input channels\n", "and validate the results in :numref:`fig_conv1d_channel`.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "4aeae6e3", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:51.830851Z", "iopub.status.busy": "2023-08-18T19:32:51.830143Z", "iopub.status.idle": "2023-08-18T19:32:51.838780Z", "shell.execute_reply": "2023-08-18T19:32:51.837994Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["tensor([ 2.,  8., 14., 20., 26., 32.])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["def corr1d_multi_in(X, K):\n", "    # First, iterate through the 0th dimension (channel dimension) of `X` and\n", "    # `K`. Then, add them together\n", "    return sum(corr1d(x, k) for x, k in zip(X, K))\n", "\n", "X = torch.tensor([[0, 1, 2, 3, 4, 5, 6],\n", "              [1, 2, 3, 4, 5, 6, 7],\n", "              [2, 3, 4, 5, 6, 7, 8]])\n", "K = torch.tensor([[1, 2], [3, 4], [-1, -3]])\n", "corr1d_multi_in(X, K)"]}, {"cell_type": "markdown", "id": "a229abd3", "metadata": {"origin_pos": 9}, "source": ["Note that\n", "multi-input-channel one-dimensional cross-correlations\n", "are equivalent\n", "to\n", "single-input-channel\n", "two-dimensional cross-correlations.\n", "To illustrate,\n", "an equivalent form of\n", "the multi-input-channel one-dimensional cross-correlation\n", "in :numref:`fig_conv1d_channel`\n", "is\n", "the\n", "single-input-channel\n", "two-dimensional cross-correlation\n", "in :numref:`fig_conv1d_2d`,\n", "where the height of the convolution kernel\n", "has to be the same as that of the input tensor.\n", "\n", "\n", "![Two-dimensional cross-correlation operation with a single input channel. The shaded portions are the first output element as well as the input and kernel tensor elements used for the output computation: $2\\times(-1)+3\\times(-3)+1\\times3+2\\times4+0\\times1+1\\times2=2$.](../img/conv1d-2d.svg)\n", ":label:`fig_conv1d_2d`\n", "\n", "Both the outputs in :numref:`fig_conv1d` and :numref:`fig_conv1d_channel` have only one channel.\n", "Same as two-dimensional convolutions with multiple output channels described in :numref:`subsec_multi-output-channels`,\n", "we can also specify multiple output channels\n", "for one-dimensional convolutions.\n", "\n", "## Max-Over-Time Pooling\n", "\n", "Similarly, we can use pooling\n", "to extract the highest value\n", "from sequence representations\n", "as the most important feature\n", "across time steps.\n", "The *max-over-time pooling* used in textCNN\n", "works like\n", "the one-dimensional global max-pooling\n", ":cite:`<PERSON><PERSON><PERSON><PERSON>Weston.Bottou.ea.2011`.\n", "For a multi-channel input\n", "where each channel stores values\n", "at different time steps,\n", "the output at each channel\n", "is the maximum value\n", "for that channel.\n", "Note that\n", "the max-over-time pooling\n", "allows different numbers of time steps\n", "at different channels.\n", "\n", "## The textCNN Model\n", "\n", "Using the one-dimensional convolution\n", "and max-over-time pooling,\n", "the textCNN model\n", "takes individual pretrained token representations\n", "as input,\n", "then obtains and transforms sequence representations\n", "for the downstream application.\n", "\n", "For a single text sequence\n", "with $n$ tokens represented by\n", "$d$-dimensional vectors,\n", "the width, height, and number of channels\n", "of the input tensor\n", "are $n$, $1$, and $d$, respectively.\n", "The textCNN model transforms the input\n", "into the output as follows:\n", "\n", "1. Define multiple one-dimensional convolution kernels and perform convolution operations separately on the inputs. Convolution kernels with different widths may capture local features among different numbers of adjacent tokens.\n", "1. Perform max-over-time pooling on all the output channels, and then concatenate all the scalar pooling outputs as a vector.\n", "1. Transform the concatenated vector into the output categories using the fully connected layer. Dropout can be used for reducing overfitting.\n", "\n", "![The model architecture of textCNN.](../img/textcnn.svg)\n", ":label:`fig_conv1d_textcnn`\n", "\n", ":numref:`fig_conv1d_textcnn`\n", "illustrates the model architecture of textCNN\n", "with a concrete example.\n", "The input is a sentence with 11 tokens,\n", "where\n", "each token is represented by a 6-dimensional vectors.\n", "So we have a 6-channel input with width 11.\n", "Define\n", "two one-dimensional convolution kernels\n", "of widths 2 and 4,\n", "with 4 and 5 output channels, respectively.\n", "They produce\n", "4 output channels with width $11-2+1=10$\n", "and 5 output channels with width $11-4+1=8$.\n", "Despite different widths of these 9 channels,\n", "the max-over-time pooling\n", "gives a concatenated 9-dimensional vector,\n", "which is finally transformed\n", "into a 2-dimensional output vector\n", "for binary sentiment predictions.\n", "\n", "\n", "\n", "### Defining the Model\n", "\n", "We implement the textCNN model in the following class.\n", "Compared with the bidirectional RNN model in\n", ":numref:`sec_sentiment_rnn`,\n", "besides\n", "replacing recurrent layers with convolutional layers,\n", "we also use two embedding layers:\n", "one with trainable weights and the other\n", "with fixed weights.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "5ebd40b7", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:51.842650Z", "iopub.status.busy": "2023-08-18T19:32:51.841971Z", "iopub.status.idle": "2023-08-18T19:32:51.850302Z", "shell.execute_reply": "2023-08-18T19:32:51.849474Z"}, "origin_pos": 11, "tab": ["pytorch"]}, "outputs": [], "source": ["class TextCNN(nn.Module):\n", "    def __init__(self, vocab_size, embed_size, kernel_sizes, num_channels,\n", "                 **kwargs):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__(**kwargs)\n", "        self.embedding = nn.Embedding(vocab_size, embed_size)\n", "        # The embedding layer not to be trained\n", "        self.constant_embedding = nn.Embedding(vocab_size, embed_size)\n", "        self.dropout = nn.Dropout(0.5)\n", "        self.decoder = nn.Linear(sum(num_channels), 2)\n", "        # The max-over-time pooling layer has no parameters, so this instance\n", "        # can be shared\n", "        self.pool = nn.AdaptiveAvgPool1d(1)\n", "        self.relu = nn.ReLU()\n", "        # Create multiple one-dimensional convolutional layers\n", "        self.convs = nn.ModuleList()\n", "        for c, k in zip(num_channels, kernel_sizes):\n", "            self.convs.append(nn.Conv1d(2 * embed_size, c, k))\n", "\n", "    def forward(self, inputs):\n", "        # Concatenate two embedding layer outputs with shape (batch size, no.\n", "        # of tokens, token vector dimension) along vectors\n", "        embeddings = torch.cat((\n", "            self.embedding(inputs), self.constant_embedding(inputs)), dim=2)\n", "        # Per the input format of one-dimensional convolutional layers,\n", "        # rearrange the tensor so that the second dimension stores channels\n", "        embeddings = embeddings.permute(0, 2, 1)\n", "        # For each one-dimensional convolutional layer, after max-over-time\n", "        # pooling, a tensor of shape (batch size, no. of channels, 1) is\n", "        # obtained. Remove the last dimension and concatenate along channels\n", "        encoding = torch.cat([\n", "            torch.squeeze(self.relu(self.pool(conv(embeddings))), dim=-1)\n", "            for conv in self.convs], dim=1)\n", "        outputs = self.decoder(self.dropout(encoding))\n", "        return outputs"]}, {"cell_type": "markdown", "id": "a71ee814", "metadata": {"origin_pos": 12}, "source": ["Let's create a textCNN instance.\n", "It has 3 convolutional layers with kernel widths of 3, 4, and 5, all with 100 output channels.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "f1e6dd5b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:51.853735Z", "iopub.status.busy": "2023-08-18T19:32:51.853163Z", "iopub.status.idle": "2023-08-18T19:32:51.962565Z", "shell.execute_reply": "2023-08-18T19:32:51.961652Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [], "source": ["embed_size, kernel_sizes, nums_channels = 100, [3, 4, 5], [100, 100, 100]\n", "devices = d2l.try_all_gpus()\n", "net = TextCNN(len(vocab), embed_size, kernel_sizes, nums_channels)\n", "\n", "def init_weights(module):\n", "    if type(module) in (nn.Linear, nn.Conv1d):\n", "        nn.init.xavier_uniform_(module.weight)\n", "\n", "net.apply(init_weights);"]}, {"cell_type": "markdown", "id": "582b40a8", "metadata": {"origin_pos": 15}, "source": ["### Loading Pretrained Word Vectors\n", "\n", "Same as :numref:`sec_sentiment_rnn`,\n", "we load pretrained 100-dimensional GloVe embeddings\n", "as the initialized token representations.\n", "These token representations (embedding weights)\n", "will be trained in `embedding`\n", "and fixed in `constant_embedding`.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "265fb564", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:32:51.966482Z", "iopub.status.busy": "2023-08-18T19:32:51.965761Z", "iopub.status.idle": "2023-08-18T19:33:16.119259Z", "shell.execute_reply": "2023-08-18T19:33:16.116011Z"}, "origin_pos": 17, "tab": ["pytorch"]}, "outputs": [], "source": ["glove_embedding = d2l.To<PERSON>('glove.6b.100d')\n", "embeds = glove_embedding[vocab.idx_to_token]\n", "net.embedding.weight.data.copy_(embeds)\n", "net.constant_embedding.weight.data.copy_(embeds)\n", "net.constant_embedding.weight.requires_grad = False"]}, {"cell_type": "markdown", "id": "464057d8", "metadata": {"origin_pos": 18}, "source": ["### Training and Evaluating the Model\n", "\n", "Now we can train the textCNN model for sentiment analysis.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "162fb536", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:33:16.127427Z", "iopub.status.busy": "2023-08-18T19:33:16.125743Z", "iopub.status.idle": "2023-08-18T19:34:10.261815Z", "shell.execute_reply": "2023-08-18T19:34:10.260553Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.066, train acc 0.979, test acc 0.868\n", "4354.2 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"187.155469pt\" viewBox=\"0 0 235.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:34:10.184045</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 235.**********.155469 \n", "L 235.784375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"mb3864b32c4\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb3864b32c4\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(26.921875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 78.928125 149.599219 \n", "L 78.928125 10.999219 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mb3864b32c4\" x=\"78.928125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 127.753125 149.599219 \n", "L 127.753125 10.999219 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mb3864b32c4\" x=\"127.753125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(124.571875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 176.578125 149.599219 \n", "L 176.578125 10.999219 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mb3864b32c4\" x=\"176.578125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(173.396875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mb3864b32c4\" x=\"225.403125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(222.221875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 177.875781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"ma14e7e4c8c\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma14e7e4c8c\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 153.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 121.879219 \n", "L 225.403125 121.879219 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#ma14e7e4c8c\" x=\"30.103125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 125.678438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 94.159219 \n", "L 225.403125 94.159219 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#ma14e7e4c8c\" x=\"30.103125\" y=\"94.159219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 97.958438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 66.439219 \n", "L 225.403125 66.439219 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#ma14e7e4c8c\" x=\"30.103125\" y=\"66.439219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 70.238437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 38.719219 \n", "L 225.403125 38.719219 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#ma14e7e4c8c\" x=\"30.103125\" y=\"38.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.518438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#ma14e7e4c8c\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M -1 61.16903 \n", "L 0.758176 62.059511 \n", "L 10.498202 67.523259 \n", "L 20.238227 72.041632 \n", "L 29.978253 76.57316 \n", "L 30.103125 76.574961 \n", "L 39.843151 104.594338 \n", "L 49.583176 106.805735 \n", "L 59.323202 108.278459 \n", "L 69.063227 109.020157 \n", "L 78.803253 109.649299 \n", "L 78.928125 109.681891 \n", "L 88.668151 123.692965 \n", "L 98.408176 124.122629 \n", "L 108.148202 124.219824 \n", "L 117.888227 124.460424 \n", "L 127.628253 124.681375 \n", "L 127.753125 124.699225 \n", "L 137.493151 135.570182 \n", "L 147.233176 134.965889 \n", "L 156.973202 134.376832 \n", "L 166.713227 134.26399 \n", "L 176.453253 134.187762 \n", "L 176.578125 134.164325 \n", "L 186.318151 140.78739 \n", "L 196.058176 141.015759 \n", "L 205.798202 140.637815 \n", "L 215.538227 140.489814 \n", "L 225.278253 140.404575 \n", "L 225.403125 140.389312 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M -1 60.858595 \n", "L 0.758176 59.573077 \n", "L 10.498202 53.802704 \n", "L 20.238227 50.278936 \n", "L 29.978253 47.03744 \n", "L 30.103125 47.024131 \n", "L 39.843151 29.351502 \n", "L 49.583176 28.032692 \n", "L 59.323202 27.463522 \n", "L 69.063227 27.255288 \n", "L 78.803253 27.119243 \n", "L 78.928125 27.098995 \n", "L 88.668151 20.411358 \n", "L 98.408176 20.439123 \n", "L 108.148202 20.559435 \n", "L 117.888227 20.397476 \n", "L 127.628253 20.3003 \n", "L 127.753125 20.290963 \n", "L 137.493151 16.135637 \n", "L 147.233176 16.302224 \n", "L 156.973202 16.339243 \n", "L 166.713227 16.350811 \n", "L 176.453253 16.42994 \n", "L 176.578125 16.432339 \n", "L 186.318151 13.442488 \n", "L 196.058176 13.525781 \n", "L 205.798202 13.636839 \n", "L 215.538227 13.824249 \n", "L 225.278253 13.903377 \n", "L 225.403125 13.909819 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 30.103125 39.146107 \n", "L 78.928125 29.299963 \n", "L 127.753125 27.370651 \n", "L 176.578125 27.697747 \n", "L 225.403125 29.244523 \n", "\" clip-path=\"url(#p49f7d93eec)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 140.634375 103.816406 \n", "L 218.403125 103.816406 \n", "Q 220.403125 103.816406 220.403125 101.816406 \n", "L 220.403125 58.782031 \n", "Q 220.403125 56.782031 218.403125 56.782031 \n", "L 140.634375 56.782031 \n", "Q 138.634375 56.782031 138.634375 58.782031 \n", "L 138.634375 101.816406 \n", "Q 138.634375 103.816406 140.634375 103.816406 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 142.634375 64.880469 \n", "L 152.634375 64.880469 \n", "L 162.634375 64.880469 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(170.634375 68.380469) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 142.634375 79.558594 \n", "L 152.634375 79.558594 \n", "L 162.634375 79.558594 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(170.634375 83.058594) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 142.634375 94.236719 \n", "L 152.634375 94.236719 \n", "L 162.634375 94.236719 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(170.634375 97.736719) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p49f7d93eec\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lr, num_epochs = 0.001, 5\n", "trainer = torch.optim.Adam(net.parameters(), lr=lr)\n", "loss = nn.CrossEntropyLoss(reduction=\"none\")\n", "d2l.train_ch13(net, train_iter, test_iter, loss, trainer, num_epochs, devices)"]}, {"cell_type": "markdown", "id": "68d5e6b4", "metadata": {"origin_pos": 21}, "source": ["Below we use the trained model to predict the sentiment for two simple sentences.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "2476e31b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:10.265904Z", "iopub.status.busy": "2023-08-18T19:34:10.265258Z", "iopub.status.idle": "2023-08-18T19:34:10.281545Z", "shell.execute_reply": "2023-08-18T19:34:10.280480Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'positive'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["d2l.predict_sentiment(net, vocab, 'this movie is so great')"]}, {"cell_type": "code", "execution_count": 10, "id": "2c259095", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:34:10.285703Z", "iopub.status.busy": "2023-08-18T19:34:10.285417Z", "iopub.status.idle": "2023-08-18T19:34:10.297499Z", "shell.execute_reply": "2023-08-18T19:34:10.295186Z"}, "origin_pos": 23, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["'negative'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["d2l.predict_sentiment(net, vocab, 'this movie is so bad')"]}, {"cell_type": "markdown", "id": "6a8ac677", "metadata": {"origin_pos": 24}, "source": ["## Summary\n", "\n", "* One-dimensional CNNs can process local features such as $n$-grams in text.\n", "* Multi-input-channel one-dimensional cross-correlations are equivalent to single-input-channel two-dimensional cross-correlations.\n", "* The max-over-time pooling allows different numbers of time steps at different channels.\n", "* The textCNN model transforms individual token representations into downstream application outputs using one-dimensional convolutional layers and max-over-time pooling layers.\n", "\n", "\n", "## Exercises\n", "\n", "1. <PERSON>ne hyperparameters and compare the two architectures for sentiment analysis in :numref:`sec_sentiment_rnn` and in this section, such as in classification accuracy and computational efficiency.\n", "1. Can you further improve the classification accuracy of the model by using the methods introduced in the exercises of :numref:`sec_sentiment_rnn`?\n", "1. Add positional encoding in the input representations. Does it improve the classification accuracy?\n"]}, {"cell_type": "markdown", "id": "a431a47b", "metadata": {"origin_pos": 26, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1425)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "f25f38c1", "metadata": {"origin_pos": 0}, "source": ["# Natural Language Inference: Fine-Tuning BERT\n", ":label:`sec_natural-language-inference-bert`\n", "\n", "In earlier sections of this chapter,\n", "we have designed an attention-based architecture\n", "(in :numref:`sec_natural-language-inference-attention`)\n", "for the natural language inference task\n", "on the SNLI dataset (as described in :numref:`sec_natural-language-inference-and-dataset`).\n", "Now we revisit this task by fine-tuning BERT.\n", "As discussed in :numref:`sec_finetuning-bert`,\n", "natural language inference is a sequence-level text pair classification problem,\n", "and fine-tuning BERT only requires an additional MLP-based architecture,\n", "as illustrated in :numref:`fig_nlp-map-nli-bert`.\n", "\n", "![This section feeds pretrained BERT to an MLP-based architecture for natural language inference.](../img/nlp-map-nli-bert.svg)\n", ":label:`fig_nlp-map-nli-bert`\n", "\n", "In this section,\n", "we will download a pretrained small version of BERT,\n", "then fine-tune it\n", "for natural language inference on the SNLI dataset.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "9f088de5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:22.440128Z", "iopub.status.busy": "2023-08-18T19:39:22.439451Z", "iopub.status.idle": "2023-08-18T19:39:25.853436Z", "shell.execute_reply": "2023-08-18T19:39:25.852139Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import json\n", "import multiprocessing\n", "import os\n", "import torch\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "caaf2d0d", "metadata": {"origin_pos": 3}, "source": ["## [**Loading Pretrained BERT**]\n", "\n", "We have explained how to pretrain BERT on the WikiText-2 dataset in\n", ":numref:`sec_bert-dataset` and :numref:`sec_bert-pretraining`\n", "(note that the original BERT model is pretrained on much bigger corpora).\n", "As discussed in :numref:`sec_bert-pretraining`,\n", "the original BERT model has hundreds of millions of parameters.\n", "In the following,\n", "we provide two versions of pretrained BERT:\n", "\"bert.base\" is about as big as the original BERT base model that requires a lot of computational resources to fine-tune,\n", "while \"bert.small\" is a small version to facilitate demonstration.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "fdd9ca6e", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:25.859081Z", "iopub.status.busy": "2023-08-18T19:39:25.858390Z", "iopub.status.idle": "2023-08-18T19:39:25.863351Z", "shell.execute_reply": "2023-08-18T19:39:25.862573Z"}, "origin_pos": 5, "tab": ["pytorch"]}, "outputs": [], "source": ["d2l.DATA_HUB['bert.base'] = (d2l.DATA_URL + 'bert.base.torch.zip',\n", "                             '225d66f04cae318b841a13d32af3acc165f253ac')\n", "d2l.DATA_HUB['bert.small'] = (d2l.DATA_URL + 'bert.small.torch.zip',\n", "                              'c72329e68a732bef0452e4b96a1c341c8910f81f')"]}, {"cell_type": "markdown", "id": "6e761acc", "metadata": {"origin_pos": 6}, "source": ["Either pretrained BERT model contains a \"vocab.json\" file that defines the vocabulary set\n", "and a \"pretrained.params\" file of the pretrained parameters.\n", "We implement the following `load_pretrained_model` function to [**load pretrained BERT parameters**].\n"]}, {"cell_type": "code", "execution_count": 3, "id": "9ca530a2", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:25.867663Z", "iopub.status.busy": "2023-08-18T19:39:25.867063Z", "iopub.status.idle": "2023-08-18T19:39:25.874533Z", "shell.execute_reply": "2023-08-18T19:39:25.873621Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["def load_pretrained_model(pretrained_model, num_hiddens, ffn_num_hiddens,\n", "                          num_heads, num_blks, dropout, max_len, devices):\n", "    data_dir = d2l.download_extract(pretrained_model)\n", "    # Define an empty vocabulary to load the predefined vocabulary\n", "    vocab = d2l.Vocab()\n", "    vocab.idx_to_token = json.load(open(os.path.join(data_dir, 'vocab.json')))\n", "    vocab.token_to_idx = {token: idx for idx, token in enumerate(\n", "        vocab.idx_to_token)}\n", "    bert = d2l.BERTModel(\n", "        len(vocab), num_hiddens, ffn_num_hiddens=ffn_num_hiddens, num_heads=4,\n", "        num_blks=2, dropout=0.2, max_len=max_len)\n", "    # Load pretrained BERT parameters\n", "    bert.load_state_dict(torch.load(os.path.join(data_dir,\n", "                                                 'pretrained.params')))\n", "    return bert, vocab"]}, {"cell_type": "markdown", "id": "527319d5", "metadata": {"origin_pos": 9}, "source": ["To facilitate demonstration on most of machines,\n", "we will load and fine-tune the small version (\"bert.small\") of the pretrained BERT in this section.\n", "In the exercise, we will show how to fine-tune the much larger \"bert.base\" to significantly improve the testing accuracy.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "b4d73006", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:25.878388Z", "iopub.status.busy": "2023-08-18T19:39:25.877761Z", "iopub.status.idle": "2023-08-18T19:39:29.552585Z", "shell.execute_reply": "2023-08-18T19:39:29.550325Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading ../data/bert.small.torch.zip from http://d2l-data.s3-accelerate.amazonaws.com/bert.small.torch.zip...\n"]}], "source": ["devices = d2l.try_all_gpus()\n", "bert, vocab = load_pretrained_model(\n", "    'bert.small', num_hiddens=256, ffn_num_hiddens=512, num_heads=4,\n", "    num_blks=2, dropout=0.1, max_len=512, devices=devices)"]}, {"cell_type": "markdown", "id": "9599a861", "metadata": {"origin_pos": 11}, "source": ["## [**The Dataset for Fine-Tuning BERT**]\n", "\n", "For the downstream task natural language inference on the SNLI dataset,\n", "we define a customized dataset class `SNLIBERTDataset`.\n", "In each example,\n", "the premise and hypothesis form a pair of text sequence\n", "and is packed into one BERT input sequence as depicted in :numref:`fig_bert-two-seqs`.\n", "Recall :numref:`subsec_bert_input_rep` that segment IDs\n", "are used to distinguish the premise and the hypothesis in a BERT input sequence.\n", "With the predefined maximum length of a BERT input sequence (`max_len`),\n", "the last token of the longer of the input text pair keeps getting removed until\n", "`max_len` is met.\n", "To accelerate generation of the SNLI dataset\n", "for fine-tuning BERT,\n", "we use 4 worker processes to generate training or testing examples in parallel.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "0ef1ad4c", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:29.556910Z", "iopub.status.busy": "2023-08-18T19:39:29.556532Z", "iopub.status.idle": "2023-08-18T19:39:29.575941Z", "shell.execute_reply": "2023-08-18T19:39:29.571719Z"}, "origin_pos": 13, "tab": ["pytorch"]}, "outputs": [], "source": ["class SNLIBERTDataset(torch.utils.data.Dataset):\n", "    def __init__(self, dataset, max_len, vocab=None):\n", "        all_premise_hypothesis_tokens = [[\n", "            p_tokens, h_tokens] for p_tokens, h_tokens in zip(\n", "            *[d2l.tokenize([s.lower() for s in sentences])\n", "              for sentences in dataset[:2]])]\n", "\n", "        self.labels = torch.tensor(dataset[2])\n", "        self.vocab = vocab\n", "        self.max_len = max_len\n", "        (self.all_token_ids, self.all_segments,\n", "         self.valid_lens) = self._preprocess(all_premise_hypothesis_tokens)\n", "        print('read ' + str(len(self.all_token_ids)) + ' examples')\n", "\n", "    def _preprocess(self, all_premise_hypothesis_tokens):\n", "        pool = multiprocessing.Pool(4)  # Use 4 worker processes\n", "        out = pool.map(self._mp_worker, all_premise_hypothesis_tokens)\n", "        all_token_ids = [\n", "            token_ids for token_ids, segments, valid_len in out]\n", "        all_segments = [segments for token_ids, segments, valid_len in out]\n", "        valid_lens = [valid_len for token_ids, segments, valid_len in out]\n", "        return (torch.tensor(all_token_ids, dtype=torch.long),\n", "                torch.tensor(all_segments, dtype=torch.long),\n", "                torch.tensor(valid_lens))\n", "\n", "    def _mp_worker(self, premise_hypothesis_tokens):\n", "        p_tokens, h_tokens = premise_hypothesis_tokens\n", "        self._truncate_pair_of_tokens(p_tokens, h_tokens)\n", "        tokens, segments = d2l.get_tokens_and_segments(p_tokens, h_tokens)\n", "        token_ids = self.vocab[tokens] + [self.vocab['<pad>']] \\\n", "                             * (self.max_len - len(tokens))\n", "        segments = segments + [0] * (self.max_len - len(segments))\n", "        valid_len = len(tokens)\n", "        return token_ids, segments, valid_len\n", "\n", "    def _truncate_pair_of_tokens(self, p_tokens, h_tokens):\n", "        # Reserve slots for '<CLS>', '<SEP>', and '<SEP>' tokens for the BERT\n", "        # input\n", "        while len(p_tokens) + len(h_tokens) > self.max_len - 3:\n", "            if len(p_tokens) > len(h_tokens):\n", "                p_tokens.pop()\n", "            else:\n", "                h_tokens.pop()\n", "\n", "    def __getitem__(self, idx):\n", "        return (self.all_token_ids[idx], self.all_segments[idx],\n", "                self.valid_lens[idx]), self.labels[idx]\n", "\n", "    def __len__(self):\n", "        return len(self.all_token_ids)"]}, {"cell_type": "markdown", "id": "972e4031", "metadata": {"origin_pos": 14}, "source": ["After downloading the SNLI dataset,\n", "we [**generate training and testing examples**]\n", "by instantiating the `SNLIBERTDataset` class.\n", "Such examples will be read in minibatches during training and testing\n", "of natural language inference.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "ba8fa6e9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:39:29.579600Z", "iopub.status.busy": "2023-08-18T19:39:29.579246Z", "iopub.status.idle": "2023-08-18T19:40:35.629014Z", "shell.execute_reply": "2023-08-18T19:40:35.626314Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["read 549367 examples\n"]}, {"name": "stdout", "output_type": "stream", "text": ["read 9824 examples\n"]}], "source": ["# Reduce `batch_size` if there is an out of memory error. In the original BERT\n", "# model, `max_len` = 512\n", "batch_size, max_len, num_workers = 512, 128, d2l.get_dataloader_workers()\n", "data_dir = d2l.download_extract('SNLI')\n", "train_set = SNLIBERTDataset(d2l.read_snli(data_dir, True), max_len, vocab)\n", "test_set = SNLIBERTDataset(d2l.read_snli(data_dir, False), max_len, vocab)\n", "train_iter = torch.utils.data.DataLoader(train_set, batch_size, shuffle=True,\n", "                                   num_workers=num_workers)\n", "test_iter = torch.utils.data.DataLoader(test_set, batch_size,\n", "                                  num_workers=num_workers)"]}, {"cell_type": "markdown", "id": "1072ac02", "metadata": {"origin_pos": 17}, "source": ["## Fine-Tuning BERT\n", "\n", "As :numref:`fig_bert-two-seqs` indicates,\n", "fine-tuning BERT for natural language inference\n", "requires only an extra MLP consisting of two fully connected layers\n", "(see `self.hidden` and `self.output` in the following `BERTClassifier` class).\n", "[**This MLP transforms the\n", "BERT representation of the special “&lt;cls&gt;” token**],\n", "which encodes the information of both the premise and the hypothesis,\n", "(**into three outputs of natural language inference**):\n", "entailment, contradiction, and neutral.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "a65b8d66", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:35.635313Z", "iopub.status.busy": "2023-08-18T19:40:35.634393Z", "iopub.status.idle": "2023-08-18T19:40:35.643634Z", "shell.execute_reply": "2023-08-18T19:40:35.642590Z"}, "origin_pos": 19, "tab": ["pytorch"]}, "outputs": [], "source": ["class BERTClassifier(nn.Module):\n", "    def __init__(self, bert):\n", "        super(BERTClassifier, self).__init__()\n", "        self.encoder = bert.encoder\n", "        self.hidden = bert.hidden\n", "        self.output = nn.Lazy<PERSON>inear(3)\n", "\n", "    def forward(self, inputs):\n", "        tokens_X, segments_X, valid_lens_x = inputs\n", "        encoded_X = self.encoder(tokens_X, segments_X, valid_lens_x)\n", "        return self.output(self.hidden(encoded_X[:, 0, :]))"]}, {"cell_type": "markdown", "id": "02a14374", "metadata": {"origin_pos": 20}, "source": ["In the following,\n", "the pretrained BERT model `bert` is fed into the `BERTClassifier` instance `net` for\n", "the downstream application.\n", "In common implementations of BERT fine-tuning,\n", "only the parameters of the output layer of the additional MLP (`net.output`) will be learned from scratch.\n", "All the parameters of the pretrained BERT encoder (`net.encoder`) and the hidden layer of the additional MLP (`net.hidden`) will be fine-tuned.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "b92a26a4", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:35.647266Z", "iopub.status.busy": "2023-08-18T19:40:35.646712Z", "iopub.status.idle": "2023-08-18T19:40:35.653291Z", "shell.execute_reply": "2023-08-18T19:40:35.652150Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [], "source": ["net = BERTClassifier(bert)"]}, {"cell_type": "markdown", "id": "1b303e5c", "metadata": {"origin_pos": 23}, "source": ["Recall that\n", "in :numref:`sec_bert`\n", "both the `MaskLM` class and the `NextSentencePred` class\n", "have parameters in their employed MLPs.\n", "These parameters are part of those in the pretrained BERT model\n", "`bert`, and thus part of parameters in `net`.\n", "However, such parameters are only for computing\n", "the masked language modeling loss\n", "and the next sentence prediction loss\n", "during pretraining.\n", "These two loss functions are irrelevant to fine-tuning downstream applications,\n", "thus the parameters of the employed MLPs in \n", "`MaskLM` and `NextSentencePred` are not updated (staled) when BERT is fine-tuned.\n", "\n", "To allow parameters with stale gradients,\n", "the flag `ignore_stale_grad=True` is set in the `step` function of `d2l.train_batch_ch13`.\n", "We use this function to train and evaluate the model `net` using the training set\n", "(`train_iter`) and the testing set (`test_iter`) of SNLI.\n", "Due to the limited computational resources, [**the training**] and testing accuracy\n", "can be further improved: we leave its discussions in the exercises.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "70669ab9", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:40:35.658377Z", "iopub.status.busy": "2023-08-18T19:40:35.657672Z", "iopub.status.idle": "2023-08-18T19:45:31.971648Z", "shell.execute_reply": "2023-08-18T19:45:31.970642Z"}, "origin_pos": 25, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loss 0.520, train acc 0.791, test acc 0.786\n", "10588.8 examples/sec on [device(type='cuda', index=0), device(type='cuda', index=1)]\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"187.155469pt\" viewBox=\"0 0 235.**********.155469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:45:31.911818</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 187.155469 \n", "L 235.**********.155469 \n", "L 235.784375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "L 225.403125 10.999219 \n", "L 30.103125 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m3ccc108fa8\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3ccc108fa8\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(26.921875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 78.928125 149.599219 \n", "L 78.928125 10.999219 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m3ccc108fa8\" x=\"78.928125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 127.753125 149.599219 \n", "L 127.753125 10.999219 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m3ccc108fa8\" x=\"127.753125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(124.571875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 176.578125 149.599219 \n", "L 176.578125 10.999219 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m3ccc108fa8\" x=\"176.578125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(173.396875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m3ccc108fa8\" x=\"225.403125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(222.221875 164.197656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 177.875781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"me98e39ed7c\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me98e39ed7c\" x=\"30.103125\" y=\"149.599219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(7.2 153.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 30.103125 121.879219 \n", "L 225.403125 121.879219 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#me98e39ed7c\" x=\"30.103125\" y=\"121.879219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 125.678438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 30.103125 94.159219 \n", "L 225.403125 94.159219 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#me98e39ed7c\" x=\"30.103125\" y=\"94.159219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 97.958438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 30.103125 66.439219 \n", "L 225.403125 66.439219 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#me98e39ed7c\" x=\"30.103125\" y=\"66.439219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 70.238437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 30.103125 38.719219 \n", "L 225.403125 38.719219 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#me98e39ed7c\" x=\"30.103125\" y=\"38.719219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 42.518438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#me98e39ed7c\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M -1 26.147652 \n", "L 0.753521 27.858582 \n", "L 10.491219 33.25736 \n", "L 20.228917 36.882676 \n", "L 29.966615 39.582609 \n", "L 30.103125 39.621611 \n", "L 39.840823 56.020699 \n", "L 49.578521 56.340137 \n", "L 59.316219 57.028425 \n", "L 69.053917 57.779339 \n", "L 78.791615 58.409606 \n", "L 78.928125 58.418011 \n", "L 88.665823 66.895114 \n", "L 98.403521 66.758577 \n", "L 108.141219 66.712417 \n", "L 117.878917 66.804867 \n", "L 127.616615 66.854498 \n", "L 127.753125 66.844186 \n", "L 137.490823 73.35417 \n", "L 147.228521 73.100502 \n", "L 156.966219 72.926417 \n", "L 166.703917 72.811912 \n", "L 176.441615 72.859341 \n", "L 176.578125 72.859622 \n", "L 186.315823 78.275571 \n", "L 196.053521 77.895357 \n", "L 205.791219 77.503884 \n", "L 215.528917 77.442551 \n", "L 225.266615 77.557256 \n", "L 225.403125 77.553091 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M -1 69.339258 \n", "L 0.753521 68.074569 \n", "L 10.491219 64.345866 \n", "L 20.228917 62.001142 \n", "L 29.966615 60.330942 \n", "L 30.103125 60.310127 \n", "L 39.840823 50.539585 \n", "L 49.578521 50.334027 \n", "L 59.316219 50.002817 \n", "L 69.053917 49.614261 \n", "L 78.791615 49.291314 \n", "L 78.928125 49.286047 \n", "L 88.665823 45.115402 \n", "L 98.403521 45.178651 \n", "L 108.141219 45.20395 \n", "L 117.878917 45.11983 \n", "L 127.616615 45.113884 \n", "L 127.753125 45.117454 \n", "L 137.490823 41.778417 \n", "L 147.228521 42.047855 \n", "L 156.966219 42.094237 \n", "L 166.703917 42.131976 \n", "L 176.441615 42.153101 \n", "L 176.578125 42.155817 \n", "L 186.315823 39.713989 \n", "L 196.053521 39.856931 \n", "L 205.791219 40.013787 \n", "L 215.528917 40.095061 \n", "L 225.266615 40.017582 \n", "L 225.403125 40.016647 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 30.103125 49.839386 \n", "L 78.928125 46.55215 \n", "L 127.753125 43.617623 \n", "L 176.578125 41.811759 \n", "L 225.403125 40.711312 \n", "\" clip-path=\"url(#peb3cdd5285)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.599219 \n", "L 30.103125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.599219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.599219 \n", "L 225.**********.599219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.999219 \n", "L 225.403125 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 37.103125 144.599219 \n", "L 114.871875 144.599219 \n", "Q 116.871875 144.599219 116.871875 142.599219 \n", "L 116.871875 99.564844 \n", "Q 116.871875 97.564844 114.871875 97.564844 \n", "L 37.103125 97.564844 \n", "Q 35.103125 97.564844 35.103125 99.564844 \n", "L 35.103125 142.599219 \n", "Q 35.103125 144.599219 37.103125 144.599219 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 39.103125 105.663281 \n", "L 49.103125 105.663281 \n", "L 59.103125 105.663281 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train loss -->\n", "     <g transform=\"translate(67.103125 109.163281) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"353.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"405.615234\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_27\">\n", "     <path d=\"M 39.103125 120.341406 \n", "L 49.103125 120.341406 \n", "L 59.103125 120.341406 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #bf00bf; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- train acc -->\n", "     <g transform=\"translate(67.103125 123.841406) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"264.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"325.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"380.810547\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 39.103125 135.019531 \n", "L 49.103125 135.019531 \n", "L 59.103125 135.019531 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- test acc -->\n", "     <g transform=\"translate(67.103125 138.519531) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"100.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"192.041016\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"223.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"285.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"340.087891\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"peb3cdd5285\">\n", "   <rect x=\"30.103125\" y=\"10.999219\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lr, num_epochs = 1e-4, 5\n", "trainer = torch.optim.Adam(net.parameters(), lr=lr)\n", "loss = nn.CrossEntropyLoss(reduction='none')\n", "net(next(iter(train_iter))[0])\n", "d2l.train_ch13(net, train_iter, test_iter, loss, trainer, num_epochs, devices)"]}, {"cell_type": "markdown", "id": "26196099", "metadata": {"origin_pos": 26}, "source": ["## Summary\n", "\n", "* We can fine-tune the pretrained BERT model for downstream applications, such as natural language inference on the SNLI dataset.\n", "* During fine-tuning, the BERT model becomes part of the model for the downstream application. Parameters that are only related to pretraining loss will not be updated during fine-tuning. \n", "\n", "\n", "\n", "## Exercises\n", "\n", "1. Fine-tune a much larger pretrained BERT model that is about as big as the original BERT base model if your computational resource allows. Set arguments in the `load_pretrained_model` function as: replacing 'bert.small' with 'bert.base', increasing values of `num_hiddens=256`, `ffn_num_hiddens=512`, `num_heads=4`, and `num_blks=2` to 768, 3072, 12, and 12, respectively. By increasing fine-tuning epochs (and possibly tuning other hyperparameters), can you get a testing accuracy higher than 0.86?\n", "1. How to truncate a pair of sequences according to their ratio of length? Compare this pair truncation method and the one used in the `SNLIBERTDataset` class. What are their pros and cons?\n"]}, {"cell_type": "markdown", "id": "e4f408d0", "metadata": {"origin_pos": 28, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/1526)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
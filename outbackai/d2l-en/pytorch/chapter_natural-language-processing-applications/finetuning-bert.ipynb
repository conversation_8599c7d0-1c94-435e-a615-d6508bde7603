{"cells": [{"cell_type": "markdown", "id": "ea82f372", "metadata": {"origin_pos": 0}, "source": ["# Fine-Tuning BERT for Sequence-Level and Token-Level Applications\n", ":label:`sec_finetuning-bert`\n", "\n", "\n", "In the previous sections of this chapter,\n", "we have designed different models for natural language processing applications,\n", "such as based on RNNs, CNNs, attention, and MLPs.\n", "These models are helpful when there is space or time constraint,\n", "however,\n", "crafting a specific model for every natural language processing task\n", "is practically infeasible.\n", "In :numref:`sec_bert`,\n", "we introduced a pretraining model, BERT,\n", "that requires minimal architecture changes\n", "for a wide range of natural language processing tasks.\n", "On the one hand,\n", "at the time of its proposal,\n", "BERT improved the state of the art on various natural language processing tasks.\n", "On the other hand,\n", "as noted in :numref:`sec_bert-pretraining`,\n", "the two versions of the original BERT model\n", "come with 110 million and 340 million parameters.\n", "Thus, when there are sufficient computational resources,\n", "we may consider\n", "fine-tuning BERT for downstream natural language processing applications.\n", "\n", "In the following,\n", "we generalize a subset of natural language processing applications\n", "as sequence-level and token-level.\n", "On the sequence level,\n", "we introduce how to transform the BERT representation of the text input\n", "to the output label\n", "in single text classification\n", "and text pair classification or regression.\n", "On the token level, we will briefly introduce new applications\n", "such as text tagging and question answering\n", "and shed light on how BERT can represent their inputs and get transformed into output labels.\n", "During fine-tuning,\n", "the \"minimal architecture changes\" required by BERT across different applications\n", "are the extra fully connected layers.\n", "During supervised learning of a downstream application,\n", "parameters of the extra layers are learned from scratch while\n", "all the parameters in the pretrained BERT model are fine-tuned.\n", "\n", "\n", "## Single Text Classification\n", "\n", "*Single text classification* takes a single text sequence as input and outputs its classification result.\n", "Besides sentiment analysis that we have studied in this chapter,\n", "the Corpus of Linguistic Acceptability (CoLA)\n", "is also a dataset for single text classification,\n", "judging whether a given sentence is grammatically acceptable or not :cite:`Warstadt.Singh.Bowman.2019`.\n", "For instance, \"I should study.\" is acceptable but \"I should studying.\" is not.\n", "\n", "![Fine-tuning BERT for single text classification applications, such as sentiment analysis and testing linguistic acceptability. Suppose that the input single text has six tokens.](../img/bert-one-seq.svg)\n", ":label:`fig_bert-one-seq`\n", "\n", ":numref:`sec_bert` describes the input representation of BERT.\n", "The BERT input sequence unambiguously represents both single text and text pairs,\n", "where the special classification token \n", "“&lt;cls&gt;” is used for sequence classification and \n", "the special classification token \n", "“&lt;sep&gt;” marks the end of single text or separates a pair of text.\n", "As shown in :numref:`fig_bert-one-seq`,\n", "in single text classification applications,\n", "the BERT representation of the special classification token \n", "“&lt;cls&gt;” encodes the information of the entire input text sequence.\n", "As the representation of the input single text,\n", "it will be fed into a small MLP consisting of fully connected (dense) layers\n", "to output the distribution of all the discrete label values.\n", "\n", "\n", "## Text Pair Classification or Regression\n", "\n", "We have also examined natural language inference in this chapter.\n", "It belongs to *text pair classification*,\n", "a type of application classifying a pair of text.\n", "\n", "Taking a pair of text as input but outputting a continuous value,\n", "*semantic textual similarity* is a popular *text pair regression* task.\n", "This task measures semantic similarity of sentences.\n", "For instance, in the Semantic Textual Similarity Benchmark dataset,\n", "the similarity score of a pair of sentences\n", "is an ordinal scale ranging from 0 (no meaning overlap) to 5 (meaning equivalence) :cite:`Cer.Diab.Agirre.ea.2017`.\n", "The goal is to predict these scores.\n", "Examples from the Semantic Textual Similarity Benchmark dataset include (sentence 1, sentence 2, similarity score):\n", "\n", "* \"A plane is taking off.\", \"An air plane is taking off.\", 5.000;\n", "* \"A woman is eating something.\", \"A woman is eating meat.\", 3.000;\n", "* \"A woman is dancing.\", \"A man is talking.\", 0.000.\n", "\n", "\n", "![Fine-tuning BERT for text pair classification or regression applications, such as natural language inference and semantic textual similarity. Suppose that the input text pair has two and three tokens.](../img/bert-two-seqs.svg)\n", ":label:`fig_bert-two-seqs`\n", "\n", "Comparing with single text classification in :numref:`fig_bert-one-seq`,\n", "fine-tuning BERT for text pair classification in :numref:`fig_bert-two-seqs` \n", "is different in the input representation.\n", "For text pair regression tasks such as semantic textual similarity,\n", "trivial changes can be applied such as outputting a continuous label value\n", "and using the mean squared loss: they are common for regression.\n", "\n", "\n", "## Text Tagging\n", "\n", "Now let's consider token-level tasks, such as *text tagging*,\n", "where each token is assigned a label.\n", "Among text tagging tasks,\n", "*part-of-speech tagging* assigns each word a part-of-speech tag (e.g., adjective and determiner)\n", "according to the role of the word in the sentence.\n", "For example,\n", "according to the Penn Treebank II tag set,\n", "the sentence \"<PERSON> 's car is new\"\n", "should be tagged as\n", "\"NNP (noun, proper singular) NNP POS (possessive ending) NN (noun, singular or mass) VB (verb, base form) JJ (adjective)\".\n", "\n", "![Fine-tuning BERT for text tagging applications, such as part-of-speech tagging. Suppose that the input single text has six tokens.](../img/bert-tagging.svg)\n", ":label:`fig_bert-tagging`\n", "\n", "Fine-tuning BERT for text tagging applications\n", "is illustrated in :numref:`fig_bert-tagging`.\n", "Comparing with :numref:`fig_bert-one-seq`,\n", "the only distinction lies in that\n", "in text tagging, the BERT representation of *every token* of the input text\n", "is fed into the same extra fully connected layers to output the label of the token,\n", "such as a part-of-speech tag.\n", "\n", "\n", "\n", "## Question Answering\n", "\n", "As another token-level application,\n", "*question answering* reflects capabilities of reading comprehension.\n", "For example,\n", "the Stanford Question Answering Dataset (SQuAD v1.1)\n", "consists of reading passages and questions,\n", "where the answer to every question\n", "is just a segment of text (text span) from the passage that the question is about :cite:`<PERSON><PERSON><PERSON>.Zhang.Lopyrev.ea.2016`.\n", "To explain,\n", "consider a passage\n", "\"Some experts report that a mask's efficacy is inconclusive. However, mask makers insist that their products, such as N95 respirator masks, can guard against the virus.\"\n", "and a question \"Who say that N95 respirator masks can guard against the virus?\".\n", "The answer should be the text span \"mask makers\" in the passage.\n", "Thus, the goal in SQuAD v1.1 is to predict the start and end of the text span in the passage given a pair of question and passage.\n", "\n", "![Fine-tuning BERT for question answering. Suppose that the input text pair has two and three tokens.](../img/bert-qa.svg)\n", ":label:`fig_bert-qa`\n", "\n", "To fine-tune BERT for question answering,\n", "the question and passage are packed as\n", "the first and second text sequence, respectively,\n", "in the input of BERT.\n", "To predict the position of the start of the text span,\n", "the same additional fully connected layer will transform\n", "the BERT representation of any token from the passage of position $i$\n", "into a scalar score $s_i$.\n", "Such scores of all the passage tokens\n", "are further transformed by the softmax operation\n", "into a probability distribution,\n", "so that each token position $i$ in the passage is assigned\n", "a probability $p_i$ of being the start of the text span.\n", "Predicting the end of the text span\n", "is the same as above, except that\n", "parameters in its additional fully connected layer\n", "are independent from those for predicting the start.\n", "When predicting the end,\n", "any passage token of position $i$\n", "is transformed by the same fully connected layer\n", "into a scalar score $e_i$.\n", ":numref:`fig_bert-qa`\n", "depicts fine-tuning BERT for question answering.\n", "\n", "For question answering,\n", "the supervised learning's training objective is as straightforward as\n", "maximizing the log-likelihoods of the ground-truth start and end positions.\n", "When predicting the span,\n", "we can compute the score $s_i + e_j$ for a valid span\n", "from position $i$ to position $j$ ($i \\leq j$),\n", "and output the span with the highest score.\n", "\n", "\n", "## Summary\n", "\n", "* BERT requires minimal architecture changes (extra fully connected layers) for sequence-level and token-level natural language processing applications, such as single text classification (e.g., sentiment analysis and testing linguistic acceptability), text pair classification or regression (e.g., natural language inference and semantic textual similarity), text tagging (e.g., part-of-speech tagging), and question answering.\n", "* During supervised learning of a downstream application, parameters of the extra layers are learned from scratch while all the parameters in the pretrained BERT model are fine-tuned.\n", "\n", "\n", "## Exercises\n", "\n", "1. Let's design a search engine algorithm for news articles. When the system receives an query (e.g., \"oil industry during the coronavirus outbreak\"), it should return a ranked list of news articles that are most relevant to the query. Suppose that we have a huge pool of news articles and a large number of queries. To simplify the problem, suppose that the most relevant article has been labeled for each query. How can we apply negative sampling (see :numref:`subsec_negative-sampling`) and BERT in the algorithm design?\n", "1. How can we leverage BERT in training language models?\n", "1. Can we leverage BERT in machine translation?\n", "\n", "[Discussions](https://discuss.d2l.ai/t/396)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "1fd3a016", "metadata": {"origin_pos": 1}, "source": ["# What Is Hyperparameter Optimization?\n", ":label:`sec_what_is_hpo`\n", "\n", "As we have seen in the previous chapters, deep neural networks come with a\n", "large number of parameters or weights that are learned during training. On\n", "top of these, every neural network has additional *hyperparameters* that need\n", "to be configured by the user. For example, to ensure that stochastic gradient\n", "descent converges to a local optimum of the training loss\n", "(see :numref:`chap_optimization`), we have to adjust the learning rate and batch\n", "size. To avoid overfitting on training datasets,\n", "we might have to set regularization parameters, such as weight decay\n", "(see :numref:`sec_weight_decay`) or dropout (see :numref:`sec_dropout`). We can\n", "define the capacity and inductive bias of the model by setting the number of\n", "layers and number of units or filters per layer (i.e., the effective number\n", "of weights).\n", "\n", "Unfortunately, we cannot simply adjust these hyperparameters by minimizing the\n", "training loss, because this would lead to overfitting on the training data. For\n", "example, setting regularization parameters, such as dropout or weight decay\n", "to zero leads to a small training loss, but might hurt the generalization\n", "performance.\n", "\n", "![Typical workflow in machine learning that consists of training the model multiple times with different hyperparameters.](../img/ml_workflow.svg)\n", ":label:`ml_workflow`\n", "\n", "Without a different form of automation, hyperparameters have to be set manually\n", "in a trial-and-error fashion, in what amounts to a time-consuming and difficult\n", "part of machine learning workflows. For example, consider training\n", "a ResNet (see :numref:`sec_resnet`) on CIFAR-10, which requires more than 2 hours\n", "on an Amazon Elastic Cloud Compute (EC2) `g4dn.xlarge` instance. Even just\n", "trying ten hyperparameter configurations in sequence, this would already take us\n", "roughly one day. To make matters worse, hyperparameters are usually not directly\n", "transferable across architectures and datasets\n", ":cite:`feurer-arxiv22,wistuba-ml18,bardenet-icml13a`, and need to be re-optimized\n", "for every new task. Also, for most hyperparameters, there are no rule-of-thumbs,\n", "and expert knowledge is required to find sensible values.\n", "\n", "*Hyperparameter optimization (HPO)* algorithms are designed to tackle this\n", "problem in a principled and automated fashion :cite:`feurer-automlbook18a`, by\n", "framing it as a global optimization problem. The default objective is the error\n", "on a hold-out validation dataset, but could in principle be any other business\n", "metric. It can be combined with or constrained by secondary objectives, such as\n", "training time, inference time, or model complexity. \n", "\n", "Recently, hyperparameter optimization has been extended to *neural architecture\n", "search (NAS)* :cite:`elsken-arxiv18a,wistuba-arxiv19`, where the goal is to find\n", "entirely new neural network architectures. Compared to classical HPO, NAS is even\n", "more expensive in terms of computation and requires additional efforts to remain\n", "feasible in practice. Both, HPO and NAS can be considered as sub-fields of \n", "AutoML :cite:`hutter-book19a`, which aims to automate the entire ML pipeline.\n", "\n", "In this section we will introduce HPO and show how we can automatically find\n", "the best hyperparameters of the logistic regression example introduced in\n", ":numref:`sec_softmax_concise`.\n", "\n", "##  The Optimization Problem\n", ":label:`sec_definition_hpo`\n", "\n", "We will start with a simple toy problem: searching for the learning rate of the\n", "multi-class logistic regression model `SoftmaxRegression` from\n", ":numref:`sec_softmax_concise` to minimize the validation error on the Fashion\n", "MNIST dataset. While other hyperparameters like batch size or number of epochs\n", "are also worth tuning, we focus on learning rate alone for simplicity.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "37611073", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:29:44.506669Z", "iopub.status.busy": "2023-08-18T19:29:44.506127Z", "iopub.status.idle": "2023-08-18T19:29:47.782553Z", "shell.execute_reply": "2023-08-18T19:29:47.781668Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [], "source": ["import numpy as np\n", "import torch\n", "from scipy import stats\n", "from torch import nn\n", "from d2l import torch as d2l"]}, {"cell_type": "markdown", "id": "b51422df", "metadata": {"origin_pos": 3}, "source": ["Before we can run HPO, we first need to define two ingredients: the objective\n", "function and the configuration space.\n", "\n", "### The Objective Function\n", "\n", "The performance of a learning algorithm can be seen as a function\n", "$f: \\mathcal{X} \\rightarrow \\mathbb{R}$ that maps from the hyperparameter space\n", "$\\mathbf{x} \\in \\mathcal{X}$ to the validation loss. For every evaluation of\n", "$f(\\mathbf{x})$, we have to train and validate our machine learning model, which\n", "can be time and compute intensive in the case of deep neural networks trained on\n", "large datasets. Given our criterion $f(\\mathbf{x})$ our goal is to find\n", "$\\mathbf{x}_{\\star} \\in \\mathrm{argmin}_{\\mathbf{x} \\in \\mathcal{X}} f(\\mathbf{x})$. \n", "\n", "There is no simple way to compute gradients of $f$ with respect to $\\mathbf{x}$,\n", "because it would require to propagate the gradient through the entire training\n", "process. While there is recent work :cite:`maclaurin-icml15,franceschi-icml17a`\n", "to drive HPO by approximate \"hypergradients\", none of the existing approaches\n", "are competitive with the state-of-the-art yet, and we will not discuss them\n", "here. Furthermore, the computational burden of evaluating $f$ requires HPO\n", "algorithms to approach the global optimum with as few samples as possible.\n", "\n", "The training of neural networks is stochastic (e.g., weights are randomly\n", "initialized, mini-batches are randomly sampled), so that our observations will\n", "be noisy: $y \\sim f(\\mathbf{x}) + \\epsilon$, where we usually assume that the\n", "$\\epsilon \\sim N(0, \\sigma)$ observation noise is Gaussian distributed.\n", "\n", "Faced with all these challenges, we usually try to identify a small set of well\n", "performing hyperparameter configurations quickly, instead of hitting the global\n", "optima exactly. However, due to large computational demands of most neural\n", "networks models, even this can take days or weeks of compute. We will explore\n", "in :numref:`sec_mf_hpo` how we can speed-up the optimization process by either\n", "distributing the search or using cheaper-to-evaluate approximations of the\n", "objective function.\n", "\n", "We begin with a method for computing the validation error of a model.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "8f5a6798", "metadata": {"attributes": {"classes": [], "id": "", "n": "8"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:47.786829Z", "iopub.status.busy": "2023-08-18T19:29:47.786035Z", "iopub.status.idle": "2023-08-18T19:29:47.791984Z", "shell.execute_reply": "2023-08-18T19:29:47.791090Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [], "source": ["class HPOTrainer(d2l.Trainer):  #@save\n", "    def validation_error(self):\n", "        self.model.eval()\n", "        accuracy = 0\n", "        val_batch_idx = 0\n", "        for batch in self.val_dataloader:\n", "            with torch.no_grad():\n", "                x, y = self.prepare_batch(batch)\n", "                y_hat = self.model(x)\n", "                accuracy += self.model.accuracy(y_hat, y)\n", "            val_batch_idx += 1\n", "        return 1 -  accuracy / val_batch_idx"]}, {"cell_type": "markdown", "id": "06625e65", "metadata": {"origin_pos": 5}, "source": ["We optimize validation error with respect to the hyperparameter configuration\n", "`config`, consisting of the `learning_rate`. For each evaluation, we train our\n", "model for `max_epochs` epochs, then compute and return its validation error:\n"]}, {"cell_type": "code", "execution_count": 3, "id": "7bb87209", "metadata": {"attributes": {"classes": [], "id": "", "n": "5"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:47.795434Z", "iopub.status.busy": "2023-08-18T19:29:47.794840Z", "iopub.status.idle": "2023-08-18T19:29:47.799807Z", "shell.execute_reply": "2023-08-18T19:29:47.798996Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [], "source": ["def hpo_objective_softmax_classification(config, max_epochs=8):\n", "    learning_rate = config[\"learning_rate\"]\n", "    trainer = d2l.HPOTrainer(max_epochs=max_epochs)\n", "    data = d2l.FashionMNIST(batch_size=16)\n", "    model = d2l.SoftmaxRegression(num_outputs=10, lr=learning_rate)\n", "    trainer.fit(model=model, data=data)\n", "    return trainer.validation_error().detach().numpy()"]}, {"cell_type": "markdown", "id": "45aec920", "metadata": {"origin_pos": 7}, "source": ["### The Configuration Space\n", ":label:`sec_intro_config_spaces`\n", "\n", "Along with the objective function $f(\\mathbf{x})$, we also need to define the\n", "feasible set $\\mathbf{x} \\in \\mathcal{X}$ to optimize over, known as\n", "*configuration space* or *search space*. For our logistic regression example,\n", "we will use:\n"]}, {"cell_type": "code", "execution_count": 4, "id": "c439a206", "metadata": {"attributes": {"classes": [], "id": "", "n": "6"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:47.803280Z", "iopub.status.busy": "2023-08-18T19:29:47.802540Z", "iopub.status.idle": "2023-08-18T19:29:47.807469Z", "shell.execute_reply": "2023-08-18T19:29:47.806663Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["config_space = {\"learning_rate\": stats.loguniform(1e-4, 1)}"]}, {"cell_type": "markdown", "id": "f1ba685a", "metadata": {"origin_pos": 9}, "source": ["Here we use the use the `loguniform` object from SciPy, which represents a\n", "uniform distribution between -4 and -1 in the logarithmic space. This object\n", "allows us to sample random variables from this distribution.\n", "\n", "Each hyperparameter has a data type, such as `float` for `learning_rate`, as\n", "well as a closed bounded range (i.e., lower and upper bounds). We usually assign\n", "a prior distribution (e.g, uniform or log-uniform) to each hyperparameter to\n", "sample from. Some positive parameters, such as `learning_rate`, are best\n", "represented on a logarithmic scale as optimal values can differ by several\n", "orders of magnitude, while others, such as momentum, come with linear scale.\n", "\n", "Below we show a simple example of a configuration space consisting of typical\n", "hyperparameters of a multi-layer perceptron including their type and standard\n", "ranges.\n", "\n", ": Example configuration space of multi-layer perceptron\n", ":label:`tab_example_configspace`\n", "\n", "| Name                | Type        |Hyperparameter Ranges           | log-scale |\n", "| :----:              | :----:      |:------------------------------:|:---------:|\n", "| learning rate       | float       |      $[10^{-6},10^{-1}]$       |    yes    |\n", "| batch size          | integer     |           $[8,256]$            |    yes    |\n", "| momentum            | float       |           $[0,0.99]$           |    no     |\n", "| activation function | categorical | $\\{\\textrm{tanh}, \\textrm{relu}\\}$ |     -     |\n", "| number of units     | integer     |          $[32, 1024]$          |    yes    |\n", "| number of layers    | integer     |            $[1, 6]$            |    no     |\n", "\n", "\n", "\n", "In general, the structure of the configuration space $\\mathcal{X}$ can be complex\n", "and it can be quite different from $\\mathbb{R}^d$. In practice, some\n", "hyperparameters may depend on the value of others. For example, assume we try\n", "to tune the number of layers for a multi-layer perceptron, and for each layer\n", "the number of units. The number of units of the $l\\textrm{-th}$ layer is\n", "relevant only if the network has at least $l+1$ layers. These advanced HPO\n", "problems are beyond the scope of this chapter. We refer the interested reader\n", "to :cite:`hutter-lion11a,jena<PERSON>-icml17a,baptista-icml18a`.\n", "\n", "The configuration space plays an important role for hyperparameter optimization,\n", "since no algorithms can find something that is not included in the configuration\n", "space. On the other hand, if the ranges are too large, the computation budget\n", "to find well performing configurations might become infeasible.\n", "\n", "## Random Search\n", ":label:`sec_rs`\n", "\n", "*Random search* is the first hyperparameter optimization algorithm we will\n", "consider. The main idea of random search is to independently sample from the\n", "configuration space until a predefined budget (e.g maximum\n", "number of iterations) is exhausted, and to return the best observed\n", "configuration. All evaluations can be executed independently in parallel (see\n", ":numref:`sec_rs_async`), but here we use a sequential loop for simplicity.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "07f883db", "metadata": {"attributes": {"classes": [], "id": "", "n": "7"}, "execution": {"iopub.execute_input": "2023-08-18T19:29:47.810993Z", "iopub.status.busy": "2023-08-18T19:29:47.810248Z", "iopub.status.idle": "2023-08-18T19:40:58.104742Z", "shell.execute_reply": "2023-08-18T19:40:58.103591Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    validation_error = 0.17070001363754272\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"242.146875pt\" height=\"183.35625pt\" viewBox=\"0 0 242.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:40:56.501558</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 242.**********.35625 \n", "L 242.146875 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 36.**********.8 \n", "L 231.**********.8 \n", "L 231.765625 7.2 \n", "L 36.465625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m90648c7d5a\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m90648c7d5a\" x=\"36.465625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(33.284375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m90648c7d5a\" x=\"85.290625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(82.109375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m90648c7d5a\" x=\"134.115625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(130.934375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m90648c7d5a\" x=\"182.940625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(179.759375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m90648c7d5a\" x=\"231.765625\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(228.584375 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(118.8875 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m7221eca9d0\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7221eca9d0\" x=\"36.465625\" y=\"130.24407\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.75 -->\n", "      <g transform=\"translate(7.2 134.043288) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m7221eca9d0\" x=\"36.465625\" y=\"103.305972\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.00 -->\n", "      <g transform=\"translate(7.2 107.105191) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m7221eca9d0\" x=\"36.465625\" y=\"76.367874\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 1.25 -->\n", "      <g transform=\"translate(7.2 80.167093) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m7221eca9d0\" x=\"36.465625\" y=\"49.429777\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 1.50 -->\n", "      <g transform=\"translate(7.2 53.228996) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m7221eca9d0\" x=\"36.465625\" y=\"22.491679\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.75 -->\n", "      <g transform=\"translate(7.2 26.290898) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 42.565495 13.5 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 60.878125 80.403226 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_15\"/>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 60.878125 80.403226 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 60.878125 139.5 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 60.878125 80.403226 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 60.878125 139.5 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 60.878125 80.403226 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 60.878125 139.5 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 60.878125 139.5 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "L 164.627995 128.677653 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "L 164.627995 128.677653 \n", "L 176.834245 130.511603 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "L 164.627995 128.677653 \n", "L 176.834245 130.511603 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "L 182.940625 129.45346 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "L 164.627995 128.677653 \n", "L 176.834245 130.511603 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "L 182.940625 129.45346 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "L 182.940625 129.532904 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "L 164.627995 128.677653 \n", "L 176.834245 130.511603 \n", "L 189.040495 131.238428 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "L 182.940625 129.45346 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "L 182.940625 129.532904 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "L 164.627995 128.677653 \n", "L 176.834245 130.511603 \n", "L 189.040495 131.238428 \n", "L 201.246745 133.894785 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "L 182.940625 129.45346 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "L 182.940625 129.532904 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "L 164.627995 128.677653 \n", "L 176.834245 130.511603 \n", "L 189.040495 131.238428 \n", "L 201.246745 133.894785 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "L 182.940625 129.45346 \n", "L 207.353125 131.99875 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "L 182.940625 129.532904 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "L 164.627995 128.677653 \n", "L 176.834245 130.511603 \n", "L 189.040495 131.238428 \n", "L 201.246745 133.894785 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "L 182.940625 129.45346 \n", "L 207.353125 131.99875 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "L 182.940625 129.532904 \n", "L 207.353125 129.058793 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "L 164.627995 128.677653 \n", "L 176.834245 130.511603 \n", "L 189.040495 131.238428 \n", "L 201.246745 133.894785 \n", "L 213.452995 134.976619 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "L 182.940625 129.45346 \n", "L 207.353125 131.99875 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "L 182.940625 129.532904 \n", "L 207.353125 129.058793 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "L 164.627995 128.677653 \n", "L 176.834245 130.511603 \n", "L 189.040495 131.238428 \n", "L 201.246745 133.894785 \n", "L 213.452995 134.976619 \n", "L 225.659245 134.916961 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "L 182.940625 129.45346 \n", "L 207.353125 131.99875 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "L 182.940625 129.532904 \n", "L 207.353125 129.058793 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "L 164.627995 128.677653 \n", "L 176.834245 130.511603 \n", "L 189.040495 131.238428 \n", "L 201.246745 133.894785 \n", "L 213.452995 134.976619 \n", "L 225.659245 134.916961 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "L 182.940625 129.45346 \n", "L 207.353125 131.99875 \n", "L 231.765625 134.109173 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "L 182.940625 129.532904 \n", "L 207.353125 129.058793 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 42.565495 13.5 \n", "L 54.771745 67.831789 \n", "L 66.977995 89.539279 \n", "L 79.184245 101.767094 \n", "L 91.390495 109.454173 \n", "L 103.596745 114.499333 \n", "L 115.802995 119.047733 \n", "L 128.009245 121.650388 \n", "L 140.215495 124.898406 \n", "L 152.421745 126.576104 \n", "L 164.627995 128.677653 \n", "L 176.834245 130.511603 \n", "L 189.040495 131.238428 \n", "L 201.246745 133.894785 \n", "L 213.452995 134.976619 \n", "L 225.659245 134.916961 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 60.878125 80.403226 \n", "L 85.290625 104.743575 \n", "L 109.703125 115.463296 \n", "L 134.115625 121.807173 \n", "L 158.528125 126.1715 \n", "L 182.940625 129.45346 \n", "L 207.353125 131.99875 \n", "L 231.765625 134.109173 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 60.878125 139.5 \n", "L 85.290625 136.526034 \n", "L 109.703125 133.821449 \n", "L 134.115625 131.989658 \n", "L 158.528125 130.24407 \n", "L 182.940625 129.532904 \n", "L 207.353125 129.058793 \n", "L 231.765625 128.282976 \n", "\" clip-path=\"url(#pe3b1994b94)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 36.**********.8 \n", "L 36.465625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 231.**********.8 \n", "L 231.765625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 36.**********.8 \n", "L 231.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 36.465625 7.2 \n", "L 231.765625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 145.175 60.06875 \n", "L 224.765625 60.06875 \n", "Q 226.765625 60.06875 226.765625 58.06875 \n", "L 226.765625 14.2 \n", "Q 226.765625 12.2 224.765625 12.2 \n", "L 145.175 12.2 \n", "Q 143.175 12.2 143.175 14.2 \n", "L 143.175 58.06875 \n", "Q 143.175 60.06875 145.175 60.06875 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_103\">\n", "     <path d=\"M 147.175 20.298438 \n", "L 157.175 20.298438 \n", "L 167.175 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(175.175 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_104\">\n", "     <path d=\"M 147.175 35.254688 \n", "L 157.175 35.254688 \n", "L 167.175 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(175.175 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_105\">\n", "     <path d=\"M 147.175 50.210938 \n", "L 157.175 50.210938 \n", "L 167.175 50.210938 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(175.175 53.710938) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pe3b1994b94\">\n", "   <rect x=\"36.465625\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"186.450726pt\" viewBox=\"0 0 235.**********.450726\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:40:56.857170</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 186.450726 \n", "L 235.**********.450726 \n", "L 235.784375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.894476 \n", "L 225.**********.894476 \n", "L 225.403125 10.294476 \n", "L 30.103125 10.294476 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m9348d909ea\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9348d909ea\" x=\"30.103125\" y=\"148.894476\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 163.492914) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m9348d909ea\" x=\"78.928125\" y=\"148.894476\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 163.492914) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m9348d909ea\" x=\"127.753125\" y=\"148.894476\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(124.571875 163.492914) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m9348d909ea\" x=\"176.578125\" y=\"148.894476\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(173.396875 163.492914) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m9348d909ea\" x=\"225.403125\" y=\"148.894476\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(222.221875 163.492914) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 177.171039) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"me69074106d\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me69074106d\" x=\"30.103125\" y=\"134.373212\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 138.172431) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#me69074106d\" x=\"30.103125\" y=\"109.698414\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 113.497632) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#me69074106d\" x=\"30.103125\" y=\"85.023615\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.7 -->\n", "      <g transform=\"translate(7.2 88.822834) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#me69074106d\" x=\"30.103125\" y=\"60.348816\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 64.148035) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#me69074106d\" x=\"30.103125\" y=\"35.674017\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.9 -->\n", "      <g transform=\"translate(7.2 39.473236) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#me69074106d\" x=\"30.103125\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 14.798438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 36.202995 16.594476 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 54.515625 94.347715 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_16\"/>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 54.515625 94.347715 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 54.515625 65.481174 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 54.515625 94.347715 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 54.515625 65.481174 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 54.515625 94.347715 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 54.515625 65.481174 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 54.515625 65.481174 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "L 158.265495 136.105143 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "L 158.265495 136.105143 \n", "L 170.471745 138.407728 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "L 158.265495 136.105143 \n", "L 170.471745 138.407728 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "L 176.578125 131.5787 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "L 158.265495 136.105143 \n", "L 170.471745 138.407728 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "L 176.578125 131.5787 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "L 176.578125 53.982718 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "L 158.265495 136.105143 \n", "L 170.471745 138.407728 \n", "L 182.677995 138.638717 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "L 176.578125 131.5787 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "L 176.578125 53.982718 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "L 158.265495 136.105143 \n", "L 170.471745 138.407728 \n", "L 182.677995 138.638717 \n", "L 194.884245 140.816513 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "L 176.578125 131.5787 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "L 176.578125 53.982718 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "L 158.265495 136.105143 \n", "L 170.471745 138.407728 \n", "L 182.677995 138.638717 \n", "L 194.884245 140.816513 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "L 176.578125 131.5787 \n", "L 200.990625 133.619132 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "L 176.578125 53.982718 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "L 158.265495 136.105143 \n", "L 170.471745 138.407728 \n", "L 182.677995 138.638717 \n", "L 194.884245 140.816513 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "L 176.578125 131.5787 \n", "L 200.990625 133.619132 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "L 176.578125 53.982718 \n", "L 200.990625 53.316499 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "L 158.265495 136.105143 \n", "L 170.471745 138.407728 \n", "L 182.677995 138.638717 \n", "L 194.884245 140.816513 \n", "L 207.090495 140.750422 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "L 176.578125 131.5787 \n", "L 200.990625 133.619132 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "L 176.578125 53.982718 \n", "L 200.990625 53.316499 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "L 158.265495 136.105143 \n", "L 170.471745 138.407728 \n", "L 182.677995 138.638717 \n", "L 194.884245 140.816513 \n", "L 207.090495 140.750422 \n", "L 219.296745 142.594476 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "L 176.578125 131.5787 \n", "L 200.990625 133.619132 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "L 176.578125 53.982718 \n", "L 200.990625 53.316499 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "L 158.265495 136.105143 \n", "L 170.471745 138.407728 \n", "L 182.677995 138.638717 \n", "L 194.884245 140.816513 \n", "L 207.090495 140.750422 \n", "L 219.296745 142.594476 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "L 176.578125 131.5787 \n", "L 200.990625 133.619132 \n", "L 225.403125 134.661096 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "L 176.578125 53.982718 \n", "L 200.990625 53.316499 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 36.202995 16.594476 \n", "L 48.409245 89.244421 \n", "L 60.615495 105.241994 \n", "L 72.821745 115.6744 \n", "L 85.027995 120.7052 \n", "L 97.234245 125.231276 \n", "L 109.440495 128.07831 \n", "L 121.646745 131.330137 \n", "L 133.852995 132.9719 \n", "L 146.059245 135.085985 \n", "L 158.265495 136.105143 \n", "L 170.471745 138.407728 \n", "L 182.677995 138.638717 \n", "L 194.884245 140.816513 \n", "L 207.090495 140.750422 \n", "L 219.296745 142.594476 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 54.515625 94.347715 \n", "L 78.928125 112.427302 \n", "L 103.340625 121.033843 \n", "L 127.753125 124.875713 \n", "L 152.165625 129.249602 \n", "L 176.578125 131.5787 \n", "L 200.990625 133.619132 \n", "L 225.403125 134.661096 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_103\">\n", "    <path d=\"M 54.515625 65.481174 \n", "L 78.928125 59.2878 \n", "L 103.340625 56.672271 \n", "L 127.753125 55.734629 \n", "L 152.165625 54.476214 \n", "L 176.578125 53.982718 \n", "L 200.990625 53.316499 \n", "L 225.403125 52.995726 \n", "\" clip-path=\"url(#p241c3a16e9)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.894476 \n", "L 30.103125 10.294476 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.894476 \n", "L 225.403125 10.294476 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.894476 \n", "L 225.**********.894476 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 10.294476 \n", "L 225.403125 10.294476 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 103.528851 \n", "L 218.403125 103.528851 \n", "Q 220.403125 103.528851 220.403125 101.528851 \n", "L 220.403125 57.660101 \n", "Q 220.403125 55.660101 218.403125 55.660101 \n", "L 138.8125 55.660101 \n", "Q 136.8125 55.660101 136.8125 57.660101 \n", "L 136.8125 101.528851 \n", "Q 136.8125 103.528851 138.8125 103.528851 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_104\">\n", "     <path d=\"M 140.8125 63.758539 \n", "L 150.8125 63.758539 \n", "L 160.8125 63.758539 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 67.258539) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_105\">\n", "     <path d=\"M 140.8125 78.714789 \n", "L 150.8125 78.714789 \n", "L 160.8125 78.714789 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 82.214789) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_106\">\n", "     <path d=\"M 140.8125 93.671039 \n", "L 150.8125 93.671039 \n", "L 160.8125 93.671039 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 97.171039) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p241c3a16e9\">\n", "   <rect x=\"30.103125\" y=\"10.294476\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"183.35625pt\" viewBox=\"0 0 235.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:40:57.260562</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 235.**********.35625 \n", "L 235.784375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m481952887c\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m481952887c\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m481952887c\" x=\"78.928125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m481952887c\" x=\"127.753125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(124.571875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m481952887c\" x=\"176.578125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(173.396875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m481952887c\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(222.221875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m527c1c8e75\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m527c1c8e75\" x=\"30.103125\" y=\"143.661808\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(7.2 147.461027) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m527c1c8e75\" x=\"30.103125\" y=\"113.767043\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 117.566261) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m527c1c8e75\" x=\"30.103125\" y=\"83.872277\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 87.671496) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m527c1c8e75\" x=\"30.103125\" y=\"53.977512\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.7 -->\n", "      <g transform=\"translate(7.2 57.776731) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m527c1c8e75\" x=\"30.103125\" y=\"24.082747\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 27.881966) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 36.202995 72.146253 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 54.515625 102.717722 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_15\"/>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 54.515625 102.717722 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 54.515625 18.6419 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 54.515625 102.717722 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 54.515625 18.6419 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 54.515625 102.717722 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 54.515625 18.6419 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 54.515625 18.6419 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "L 158.265495 134.759497 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "L 158.265495 134.759497 \n", "L 170.471745 135.258344 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "L 158.265495 134.759497 \n", "L 170.471745 135.258344 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "L 176.578125 120.554574 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "L 158.265495 134.759497 \n", "L 170.471745 135.258344 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "L 176.578125 120.554574 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "L 176.578125 13.5 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "L 158.265495 134.759497 \n", "L 170.471745 135.258344 \n", "L 182.677995 136.331024 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "L 176.578125 120.554574 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "L 176.578125 13.5 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "L 158.265495 134.759497 \n", "L 170.471745 135.258344 \n", "L 182.677995 136.331024 \n", "L 194.884245 136.468158 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "L 176.578125 120.554574 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "L 176.578125 13.5 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "L 158.265495 134.759497 \n", "L 170.471745 135.258344 \n", "L 182.677995 136.331024 \n", "L 194.884245 136.468158 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "L 176.578125 120.554574 \n", "L 200.990625 101.969457 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "L 176.578125 13.5 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "L 158.265495 134.759497 \n", "L 170.471745 135.258344 \n", "L 182.677995 136.331024 \n", "L 194.884245 136.468158 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "L 176.578125 120.554574 \n", "L 200.990625 101.969457 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "L 176.578125 13.5 \n", "L 200.990625 16.041055 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "L 158.265495 134.759497 \n", "L 170.471745 135.258344 \n", "L 182.677995 136.331024 \n", "L 194.884245 136.468158 \n", "L 207.090495 139.5 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "L 176.578125 120.554574 \n", "L 200.990625 101.969457 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "L 176.578125 13.5 \n", "L 200.990625 16.041055 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "L 158.265495 134.759497 \n", "L 170.471745 135.258344 \n", "L 182.677995 136.331024 \n", "L 194.884245 136.468158 \n", "L 207.090495 139.5 \n", "L 219.296745 135.548321 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "L 176.578125 120.554574 \n", "L 200.990625 101.969457 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "L 176.578125 13.5 \n", "L 200.990625 16.041055 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "L 158.265495 134.759497 \n", "L 170.471745 135.258344 \n", "L 182.677995 136.331024 \n", "L 194.884245 136.468158 \n", "L 207.090495 139.5 \n", "L 219.296745 135.548321 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "L 176.578125 120.554574 \n", "L 200.990625 101.969457 \n", "L 225.403125 119.904641 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "L 176.578125 13.5 \n", "L 200.990625 16.041055 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 36.202995 72.146253 \n", "L 48.409245 111.372012 \n", "L 60.615495 119.903811 \n", "L 72.821745 123.541726 \n", "L 85.027995 127.734211 \n", "L 97.234245 128.487428 \n", "L 109.440495 131.198384 \n", "L 121.646745 131.112232 \n", "L 133.852995 133.734255 \n", "L 146.059245 132.295774 \n", "L 158.265495 134.759497 \n", "L 170.471745 135.258344 \n", "L 182.677995 136.331024 \n", "L 194.884245 136.468158 \n", "L 207.090495 139.5 \n", "L 219.296745 135.548321 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 54.515625 102.717722 \n", "L 78.928125 100.329108 \n", "L 103.340625 114.565394 \n", "L 127.753125 118.269962 \n", "L 152.165625 122.58594 \n", "L 176.578125 120.554574 \n", "L 200.990625 101.969457 \n", "L 225.403125 119.904641 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_102\">\n", "    <path d=\"M 54.515625 18.6419 \n", "L 78.928125 21.033481 \n", "L 103.340625 17.236846 \n", "L 127.753125 15.413265 \n", "L 152.165625 13.679369 \n", "L 176.578125 13.5 \n", "L 200.990625 16.041055 \n", "L 225.403125 14.75558 \n", "\" clip-path=\"url(#p17744ebef4)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 100.434375 \n", "L 218.403125 100.434375 \n", "Q 220.403125 100.434375 220.403125 98.434375 \n", "L 220.403125 54.565625 \n", "Q 220.403125 52.565625 218.403125 52.565625 \n", "L 138.8125 52.565625 \n", "Q 136.8125 52.565625 136.8125 54.565625 \n", "L 136.8125 98.434375 \n", "Q 136.8125 100.434375 138.8125 100.434375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_103\">\n", "     <path d=\"M 140.8125 60.664063 \n", "L 150.8125 60.664063 \n", "L 160.8125 60.664063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 64.164063) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_104\">\n", "     <path d=\"M 140.8125 75.620313 \n", "L 150.8125 75.620313 \n", "L 160.8125 75.620313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 79.120313) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_105\">\n", "     <path d=\"M 140.8125 90.576563 \n", "L 150.8125 90.576563 \n", "L 160.8125 90.576563 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 94.076563) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p17744ebef4\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"183.35625pt\" viewBox=\"0 0 235.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:40:57.656031</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 235.**********.35625 \n", "L 235.784375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m044cb2cff3\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m044cb2cff3\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m044cb2cff3\" x=\"78.928125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m044cb2cff3\" x=\"127.753125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(124.571875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m044cb2cff3\" x=\"176.578125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(173.396875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m044cb2cff3\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(222.221875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"ma20249a8b5\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma20249a8b5\" x=\"30.103125\" y=\"124.934968\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(7.2 128.734187) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#ma20249a8b5\" x=\"30.103125\" y=\"92.303938\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(7.2 96.103157) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#ma20249a8b5\" x=\"30.103125\" y=\"59.672908\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.7 -->\n", "      <g transform=\"translate(7.2 63.472126) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#ma20249a8b5\" x=\"30.103125\" y=\"27.041878\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(7.2 30.841096) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_10\">\n", "    <path d=\"M 36.202995 70.142164 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 54.515625 101.305616 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_14\"/>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 54.515625 101.305616 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 54.515625 24.822967 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 54.515625 101.305616 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 54.515625 24.822967 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 54.515625 101.305616 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 54.515625 24.822967 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 54.515625 24.822967 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "L 158.265495 134.083776 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "L 158.265495 134.083776 \n", "L 170.471745 135.287078 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "L 158.265495 134.083776 \n", "L 170.471745 135.287078 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "L 176.578125 108.122257 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "L 158.265495 134.083776 \n", "L 170.471745 135.287078 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "L 176.578125 108.122257 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "L 176.578125 15.523124 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "L 158.265495 134.083776 \n", "L 170.471745 135.287078 \n", "L 182.677995 133.691581 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "L 176.578125 108.122257 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "L 176.578125 15.523124 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "L 158.265495 134.083776 \n", "L 170.471745 135.287078 \n", "L 182.677995 133.691581 \n", "L 194.884245 136.114682 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "L 176.578125 108.122257 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "L 176.578125 15.523124 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "L 158.265495 134.083776 \n", "L 170.471745 135.287078 \n", "L 182.677995 133.691581 \n", "L 194.884245 136.114682 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "L 176.578125 108.122257 \n", "L 200.990625 131.461423 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "L 176.578125 15.523124 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "L 158.265495 134.083776 \n", "L 170.471745 135.287078 \n", "L 182.677995 133.691581 \n", "L 194.884245 136.114682 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "L 176.578125 108.122257 \n", "L 200.990625 131.461423 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "L 176.578125 15.523124 \n", "L 200.990625 14.642086 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "L 158.265495 134.083776 \n", "L 170.471745 135.287078 \n", "L 182.677995 133.691581 \n", "L 194.884245 136.114682 \n", "L 207.090495 139.5 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "L 176.578125 108.122257 \n", "L 200.990625 131.461423 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "L 176.578125 15.523124 \n", "L 200.990625 14.642086 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "L 158.265495 134.083776 \n", "L 170.471745 135.287078 \n", "L 182.677995 133.691581 \n", "L 194.884245 136.114682 \n", "L 207.090495 139.5 \n", "L 219.296745 132.831505 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "L 176.578125 108.122257 \n", "L 200.990625 131.461423 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "L 176.578125 15.523124 \n", "L 200.990625 14.642086 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "L 158.265495 134.083776 \n", "L 170.471745 135.287078 \n", "L 182.677995 133.691581 \n", "L 194.884245 136.114682 \n", "L 207.090495 139.5 \n", "L 219.296745 132.831505 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "L 176.578125 108.122257 \n", "L 200.990625 131.461423 \n", "L 225.403125 124.710542 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "L 176.578125 15.523124 \n", "L 200.990625 14.642086 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 36.202995 70.142164 \n", "L 48.409245 110.33789 \n", "L 60.615495 119.977617 \n", "L 72.821745 120.597408 \n", "L 85.027995 131.605973 \n", "L 97.234245 121.096345 \n", "L 109.440495 129.024482 \n", "L 121.646745 130.373518 \n", "L 133.852995 135.204521 \n", "L 146.059245 129.489979 \n", "L 158.265495 134.083776 \n", "L 170.471745 135.287078 \n", "L 182.677995 133.691581 \n", "L 194.884245 136.114682 \n", "L 207.090495 139.5 \n", "L 219.296745 132.831505 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 54.515625 101.305616 \n", "L 78.928125 53.4084 \n", "L 103.340625 133.915238 \n", "L 127.753125 128.329853 \n", "L 152.165625 132.751095 \n", "L 176.578125 108.122257 \n", "L 200.990625 131.461423 \n", "L 225.403125 124.710542 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 54.515625 24.822967 \n", "L 78.928125 40.453231 \n", "L 103.340625 13.989465 \n", "L 127.753125 13.5 \n", "L 152.165625 14.185252 \n", "L 176.578125 15.523124 \n", "L 200.990625 14.642086 \n", "L 225.403125 16.077851 \n", "\" clip-path=\"url(#pbe5a1346a1)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 100.434375 \n", "L 218.403125 100.434375 \n", "Q 220.403125 100.434375 220.403125 98.434375 \n", "L 220.403125 54.565625 \n", "Q 220.403125 52.565625 218.403125 52.565625 \n", "L 138.8125 52.565625 \n", "Q 136.8125 52.565625 136.8125 54.565625 \n", "L 136.8125 98.434375 \n", "Q 136.8125 100.434375 138.8125 100.434375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_102\">\n", "     <path d=\"M 140.8125 60.664063 \n", "L 150.8125 60.664063 \n", "L 160.8125 60.664063 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 64.164063) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_103\">\n", "     <path d=\"M 140.8125 75.620313 \n", "L 150.8125 75.620313 \n", "L 160.8125 75.620313 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 79.120313) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_104\">\n", "     <path d=\"M 140.8125 90.576563 \n", "L 150.8125 90.576563 \n", "L 160.8125 90.576563 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 94.076563) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pbe5a1346a1\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"235.784375pt\" height=\"183.35625pt\" viewBox=\"0 0 235.**********.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:40:58.009492</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 235.**********.35625 \n", "L 235.784375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "L 225.403125 7.2 \n", "L 30.103125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m5c4f6a0849\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5c4f6a0849\" x=\"30.103125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(26.921875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m5c4f6a0849\" x=\"78.928125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(75.746875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m5c4f6a0849\" x=\"127.753125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(124.571875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m5c4f6a0849\" x=\"176.578125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(173.396875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m5c4f6a0849\" x=\"225.403125\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(222.221875 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- epoch -->\n", "     <g transform=\"translate(112.525 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"186.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"241.162109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m72811251fa\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m72811251fa\" x=\"30.103125\" y=\"116.890506\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 120.689725) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m72811251fa\" x=\"30.103125\" y=\"83.611887\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(7.2 87.411105) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m72811251fa\" x=\"30.103125\" y=\"50.333267\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(7.2 54.132486) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m72811251fa\" x=\"30.103125\" y=\"17.054648\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(7.2 20.853866) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_10\">\n", "    <path d=\"M 36.202995 72.60777 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 54.515625 13.5 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_14\"/>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 54.515625 13.5 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 54.515625 139.5 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 54.515625 13.5 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 54.515625 139.5 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 54.515625 13.5 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 54.515625 139.5 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 54.515625 139.5 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_44\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_46\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_49\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_50\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_51\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_52\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_53\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_54\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_55\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_56\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_57\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_58\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_61\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_62\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_63\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_64\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "L 158.265495 101.990944 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_67\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_68\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_69\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "L 158.265495 101.990944 \n", "L 170.471745 100.489696 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_70\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_71\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_72\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "L 158.265495 101.990944 \n", "L 170.471745 100.489696 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "L 176.578125 88.508069 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_74\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "L 158.265495 101.990944 \n", "L 170.471745 100.489696 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "L 176.578125 88.508069 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "L 176.578125 131.772705 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "L 158.265495 101.990944 \n", "L 170.471745 100.489696 \n", "L 182.677995 99.408924 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_79\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "L 176.578125 88.508069 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "L 176.578125 131.772705 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "L 158.265495 101.990944 \n", "L 170.471745 100.489696 \n", "L 182.677995 99.408924 \n", "L 194.884245 103.970614 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "L 176.578125 88.508069 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "L 176.578125 131.772705 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "L 158.265495 101.990944 \n", "L 170.471745 100.489696 \n", "L 182.677995 99.408924 \n", "L 194.884245 103.970614 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "L 176.578125 88.508069 \n", "L 200.990625 96.854355 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "L 176.578125 131.772705 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "L 158.265495 101.990944 \n", "L 170.471745 100.489696 \n", "L 182.677995 99.408924 \n", "L 194.884245 103.970614 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "L 176.578125 88.508069 \n", "L 200.990625 96.854355 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "L 176.578125 131.772705 \n", "L 200.990625 130.967362 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "L 158.265495 101.990944 \n", "L 170.471745 100.489696 \n", "L 182.677995 99.408924 \n", "L 194.884245 103.970614 \n", "L 207.090495 104.079112 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_91\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "L 176.578125 88.508069 \n", "L 200.990625 96.854355 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_92\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "L 176.578125 131.772705 \n", "L 200.990625 130.967362 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_93\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "L 158.265495 101.990944 \n", "L 170.471745 100.489696 \n", "L 182.677995 99.408924 \n", "L 194.884245 103.970614 \n", "L 207.090495 104.079112 \n", "L 219.296745 99.335941 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_94\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "L 176.578125 88.508069 \n", "L 200.990625 96.854355 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_95\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "L 176.578125 131.772705 \n", "L 200.990625 130.967362 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_96\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "L 158.265495 101.990944 \n", "L 170.471745 100.489696 \n", "L 182.677995 99.408924 \n", "L 194.884245 103.970614 \n", "L 207.090495 104.079112 \n", "L 219.296745 99.335941 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_97\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "L 176.578125 88.508069 \n", "L 200.990625 96.854355 \n", "L 225.403125 109.979585 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_98\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "L 176.578125 131.772705 \n", "L 200.990625 130.967362 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_99\">\n", "    <path d=\"M 36.202995 72.60777 \n", "L 48.409245 87.796643 \n", "L 60.615495 97.944444 \n", "L 72.821745 93.746148 \n", "L 85.027995 101.042108 \n", "L 97.234245 97.466458 \n", "L 109.440495 99.209611 \n", "L 121.646745 100.593358 \n", "L 133.852995 102.770365 \n", "L 146.059245 101.102942 \n", "L 158.265495 101.990944 \n", "L 170.471745 100.489696 \n", "L 182.677995 99.408924 \n", "L 194.884245 103.970614 \n", "L 207.090495 104.079112 \n", "L 219.296745 99.335941 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_100\">\n", "    <path d=\"M 54.515625 13.5 \n", "L 78.928125 97.510586 \n", "L 103.340625 109.762186 \n", "L 127.753125 114.159254 \n", "L 152.165625 93.450149 \n", "L 176.578125 88.508069 \n", "L 200.990625 96.854355 \n", "L 225.403125 109.979585 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_101\">\n", "    <path d=\"M 54.515625 139.5 \n", "L 78.928125 129.676152 \n", "L 103.340625 129.223562 \n", "L 127.753125 129.117071 \n", "L 152.165625 130.954051 \n", "L 176.578125 131.772705 \n", "L 200.990625 130.967362 \n", "L 225.403125 128.251827 \n", "\" clip-path=\"url(#p8083e5d7dd)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 30.**********.8 \n", "L 30.103125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 225.**********.8 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 30.**********.8 \n", "L 225.**********.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 30.103125 7.2 \n", "L 225.403125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 138.8125 60.06875 \n", "L 218.403125 60.06875 \n", "Q 220.403125 60.06875 220.403125 58.06875 \n", "L 220.403125 14.2 \n", "Q 220.403125 12.2 218.403125 12.2 \n", "L 138.8125 12.2 \n", "Q 136.8125 12.2 136.8125 14.2 \n", "L 136.8125 58.06875 \n", "Q 136.8125 60.06875 138.8125 60.06875 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_102\">\n", "     <path d=\"M 140.8125 20.298438 \n", "L 150.8125 20.298438 \n", "L 160.8125 20.298438 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- train_loss -->\n", "     <g transform=\"translate(168.8125 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"80.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"141.601562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"169.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"232.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"282.763672\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"310.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"371.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"423.828125\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_103\">\n", "     <path d=\"M 140.8125 35.254688 \n", "L 150.8125 35.254688 \n", "L 160.8125 35.254688 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- val_loss -->\n", "     <g transform=\"translate(168.8125 38.754688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"226.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"287.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"339.306641\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_104\">\n", "     <path d=\"M 140.8125 50.210938 \n", "L 150.8125 50.210938 \n", "L 160.8125 50.210938 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #2ca02c; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- val_acc -->\n", "     <g transform=\"translate(168.8125 53.710938) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"198.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"314.501953\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p8083e5d7dd\">\n", "   <rect x=\"30.103125\" y=\"7.2\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["errors, values = [], []\n", "num_iterations = 5\n", "\n", "for i in range(num_iterations):\n", "    learning_rate = config_space[\"learning_rate\"].rvs()\n", "    print(f\"Trial {i}: learning_rate = {learning_rate}\")\n", "    y = hpo_objective_softmax_classification({\"learning_rate\": learning_rate})\n", "    print(f\"    validation_error = {y}\")\n", "    values.append(learning_rate)\n", "    errors.append(y)"]}, {"cell_type": "markdown", "id": "126a63c5", "metadata": {"origin_pos": 11}, "source": ["The best learning rate is then simply the one with the lowest validation error.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "43170dca", "metadata": {"attributes": {"classes": [], "id": "", "n": "7"}, "execution": {"iopub.execute_input": "2023-08-18T19:40:58.110279Z", "iopub.status.busy": "2023-08-18T19:40:58.109413Z", "iopub.status.idle": "2023-08-18T19:40:58.115714Z", "shell.execute_reply": "2023-08-18T19:40:58.114693Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["optimal learning rate = 0.09844872561810249\n"]}], "source": ["best_idx = np.argmin(errors)\n", "print(f\"optimal learning rate = {values[best_idx]}\")"]}, {"cell_type": "markdown", "id": "267bcd29", "metadata": {"origin_pos": 13}, "source": ["Due to its simplicity and generality, random search is one of the most frequently\n", "used HPO algorithms. It does not require any sophisticated implementation and\n", "can be applied to any configuration space as long as we can define some\n", "probability distribution for each hyperparameter.\n", "\n", "Unfortunately random search also comes with a few shortcomings. First, it does\n", "not adapt the sampling distribution based on the previous observations it\n", "collected so far. Hence, it is equally likely to sample a poorly performing\n", "configuration than a better performing configuration. Second, the same amount\n", "of resources are spent for all configurations, even though some may show poor\n", "initial performance and are less likely to outperform previously seen\n", "configurations.\n", "\n", "In the next sections we will look at more sample efficient hyperparameter\n", "optimization algorithms that overcome the shortcomings of random search by\n", "using a model to guide the search. We will also look at algorithms that\n", "automatically stop the evaluation process of poorly performing configurations\n", "to speed up the optimization process.\n", "\n", "## Summary\n", "\n", "In this section we introduced hyperparameter optimization (HPO) and how we can\n", "phrase it as a global optimization by defining a configuration space and an\n", "objective function. We also implemented our first HPO algorithm, random search,\n", "and applied it on a simple softmax classification problem.\n", "\n", "While random search is very simple, it is the better alternative to grid\n", "search, which simply evaluates a fixed set of hyperparameters. Random search\n", "somewhat mitigates the curse of dimensionality :cite:`bellman-science66`, and\n", "can be far more efficient than grid search if the criterion most strongly\n", "depends on a small subset of the hyperparameters.\n", "\n", "## Exercises\n", "\n", "1. In this chapter, we optimize the validation error of a model after training on a disjoint training set. For simplicity, our code uses `Trainer.val_dataloader`, which maps to a loader around `FashionMNIST.val`.\n", "    1. Convince yourself (by looking at the code) that this means we use the original FashionMNIST training set (60000 examples) for training, and the original *test set* (10000 examples) for validation.\n", "    2. Why could this practice be problematic? Hint: Re-read :numref:`sec_generalization_basics`, especially about *model selection*.\n", "    3. What should we have done instead?\n", "2. We stated above that hyperparameter optimization by gradient descent is very hard to do. Consider a small problem, such as training a two-layer perceptron on the FashionMNIST dataset (:numref:`sec_mlp-implementation`) with a batch size of 256. We would like to tune the learning rate of SGD in order to minimize a validation metric after one epoch of training.\n", "    1. Why cannot we use validation *error* for this purpose? What metric on the validation set would you use?\n", "    2. Sketch (roughly) the computational graph of the validation metric after training for one epoch. You may assume that initial weights and hyperparameters (such as learning rate) are input nodes to this graph. Hint: Re-read about computational graphs in :numref:`sec_backprop`.\n", "    3. Give a rough estimate of the number of floating point values you need to store during a forward pass on this graph. Hint: FashionMNIST has 60000 cases. Assume the required memory is dominated by the activations after each layer, and look up the layer widths in :numref:`sec_mlp-implementation`.\n", "    5. Apart from the sheer amount of compute and storage required, what other issues would gradient-based hyperparameter optimization run into? Hint: Re-read about vanishing and exploding gradients in :numref:`sec_numerical_stability`.\n", "    6. *Advanced*: Read :cite:`maclaurin-icml15` for an elegant (yet still somewhat unpractical) approach to gradient-based HPO.\n", "3. Grid search is another HPO baseline, where we define an equi-spaced grid for each hyperparameter, then iterate over the (combinatorial) Cartesian product in order to suggest configurations.\n", "    1. We stated above that random search can be much more efficient than grid search for HPO on a sizable number of hyperparameters, if the criterion most strongly depends on a small subset of the hyperparameters. Why is this? Hint: Read :cite:`bergstra2011algorithms`.\n"]}, {"cell_type": "markdown", "id": "b1f1c594", "metadata": {"origin_pos": 14, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/12090)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}
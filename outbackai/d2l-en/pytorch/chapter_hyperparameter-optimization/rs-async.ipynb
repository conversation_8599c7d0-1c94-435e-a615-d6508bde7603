{"cells": [{"cell_type": "markdown", "id": "ba85ae7f", "metadata": {"origin_pos": 1}, "source": ["# Asynchronous Random Search\n", ":label:`sec_rs_async`\n", "\n", "As we have seen in the previous :numref:`sec_api_hpo`, we might have to wait\n", "hours or even days before random search returns a good hyperparameter\n", "configuration, because of the expensive evaluation of hyperparameter\n", "configurations. In practice, we have often access to a pool of resources such as\n", "multiple GPUs on the same machine or multiple machines with a single GPU. This\n", "begs the question: *How do we efficiently distribute random search?*\n", "\n", "In general, we distinguish between synchronous and asynchronous parallel\n", "hyperparameter optimization (see :numref:`distributed_scheduling`). In the\n", "synchronous setting, we wait for all concurrently running trials to finish,\n", "before we start the next batch. Consider configuration spaces that contain\n", "hyperparameters such as the number of filters or number of layers of a deep\n", "neural network. Hyperparameter configurations that contain a larger number of \n", "layers of filters will naturally take more time to finish, and all other trials\n", "in the same batch will have to wait at synchronisation points (grey area in\n", ":numref:`distributed_scheduling`) before we can continue the optimization\n", "process.\n", "\n", "In the asynchronous setting we immediately schedule a new trial as soon as resources\n", "become available. This will optimally exploit our resources, since we can avoid any\n", "synchronisation overhead. For random search, each new hyperparameter configuration\n", "is chosen independently of all others, and in particular without exploiting\n", "observations from any prior evaluation. This means we can trivially parallelize random\n", "search asynchronously. This is not straight-forward with more sophisticated methods\n", "that make decision based on previous observations (see :numref:`sec_sh_async`).\n", "While we need access to more resources than in the sequential setting, asynchronous\n", "random search exhibits a linear speed-up, in that a certain performance is reached\n", "$K$ times faster if $K$ trials can be run in parallel. \n", "\n", "\n", "![Distributing the hyperparameter optimization process either synchronously or asynchronously. Compared to the sequential setting, we can reduce the overall wall-clock time while keep the total compute constant. Synchronous scheduling might lead to idling workers in the case of stragglers.](../img/distributed_scheduling.svg)\n", ":label:`distributed_scheduling`\n", "\n", "In this notebook, we will look at asynchronous random search that, where trials are\n", "executed in multiple python processes on the same machine. Distributed job scheduling\n", "and execution is difficult to implement from scratch. We will use *Syne Tune*\n", ":cite:`salinas-automl22`, which provides us with a simple interface for asynchronous\n", "HPO. Syne Tune is designed to be run with different execution back-ends, and the\n", "interested reader is invited to study its simple APIs in order to learn more about\n", "distributed HPO.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "3bc1ea0b", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T19:45:36.479120Z", "iopub.status.busy": "2023-08-18T19:45:36.478396Z", "iopub.status.idle": "2023-08-18T19:45:39.891609Z", "shell.execute_reply": "2023-08-18T19:45:39.890405Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO:root:SageMakerBackend is not imported since dependencies are missing. You can install them with\n", "   pip install 'syne-tune[extra]'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["AWS dependencies are not imported since dependencies are missing. You can install them with\n", "   pip install 'syne-tune[aws]'\n", "or (for everything)\n", "   pip install 'syne-tune[extra]'\n", "AWS dependencies are not imported since dependencies are missing. You can install them with\n", "   pip install 'syne-tune[aws]'\n", "or (for everything)\n", "   pip install 'syne-tune[extra]'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:Ray Tune schedulers and searchers are not imported since dependencies are missing. You can install them with\n", "   pip install 'syne-tune[raytune]'\n", "or (for everything)\n", "   pip install 'syne-tune[extra]'\n"]}], "source": ["import logging\n", "from d2l import torch as d2l\n", "\n", "logging.basicConfig(level=logging.INFO)\n", "from syne_tune import StoppingCriterion, Tuner\n", "from syne_tune.backend.python_backend import PythonBackend\n", "from syne_tune.config_space import loguniform, randint\n", "from syne_tune.experiments import load_experiment\n", "from syne_tune.optimizer.baselines import RandomSearch"]}, {"cell_type": "markdown", "id": "4dad0d63", "metadata": {"origin_pos": 3}, "source": ["## Objective Function\n", "\n", "First, we have to define a new objective function such that it now returns the\n", "performance back to <PERSON><PERSON> Tu<PERSON> via the `report` callback.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "836d36b0", "metadata": {"attributes": {"classes": [], "id": "", "n": "34"}, "execution": {"iopub.execute_input": "2023-08-18T19:45:39.896715Z", "iopub.status.busy": "2023-08-18T19:45:39.896297Z", "iopub.status.idle": "2023-08-18T19:45:39.903868Z", "shell.execute_reply": "2023-08-18T19:45:39.902721Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [], "source": ["def hpo_objective_lenet_synetune(learning_rate, batch_size, max_epochs):\n", "    from syne_tune import Reporter\n", "    from d2l import torch as d2l\n", "\n", "    model = d2l.LeNet(lr=learning_rate, num_classes=10)\n", "    trainer = d2l.HPOTrainer(max_epochs=1, num_gpus=1)\n", "    data = d2l.FashionMNIST(batch_size=batch_size)\n", "    model.apply_init([next(iter(data.get_dataloader(True)))[0]], d2l.init_cnn)\n", "    report = Reporter()\n", "    for epoch in range(1, max_epochs + 1):\n", "        if epoch == 1:\n", "            # Initialize the state of Trainer\n", "            trainer.fit(model=model, data=data)\n", "        else:\n", "            trainer.fit_epoch()\n", "        validation_error = trainer.validation_error().cpu().detach().numpy()\n", "        report(epoch=epoch, validation_error=float(validation_error))"]}, {"cell_type": "markdown", "id": "be1414d2", "metadata": {"origin_pos": 5}, "source": ["Note that the `PythonBackend` of Syne Tune requires dependencies to be imported\n", "inside the function definition.\n", "\n", "## Asynchronous Scheduler\n", "\n", "First, we define the number of workers that evaluate trials concurrently. We\n", "also need to specify how long we want to run random search, by defining an\n", "upper limit on the total wall-clock time.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "8444f99c", "metadata": {"attributes": {"classes": [], "id": "", "n": "37"}, "execution": {"iopub.execute_input": "2023-08-18T19:45:39.908295Z", "iopub.status.busy": "2023-08-18T19:45:39.907985Z", "iopub.status.idle": "2023-08-18T19:45:39.912826Z", "shell.execute_reply": "2023-08-18T19:45:39.911751Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [], "source": ["n_workers = 2  # Needs to be <= the number of available GPUs\n", "\n", "max_wallclock_time = 12 * 60  # 12 minutes"]}, {"cell_type": "markdown", "id": "f29aad51", "metadata": {"origin_pos": 7}, "source": ["Next, we state which metric we want to optimize and whether we want to minimize or\n", "maximize this metric. Namely, `metric` needs to correspond to the argument name\n", "passed to the `report` callback.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "1fe5e4fc", "metadata": {"attributes": {"classes": [], "id": "", "n": "38"}, "execution": {"iopub.execute_input": "2023-08-18T19:45:39.917113Z", "iopub.status.busy": "2023-08-18T19:45:39.916831Z", "iopub.status.idle": "2023-08-18T19:45:39.921556Z", "shell.execute_reply": "2023-08-18T19:45:39.920481Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["mode = \"min\"\n", "metric = \"validation_error\""]}, {"cell_type": "markdown", "id": "3ce2c04b", "metadata": {"origin_pos": 9}, "source": ["We use the configuration space from our previous example. In Syne Tune, this\n", "dictionary can also be used to pass constant attributes to the training script.\n", "We make use of this feature in order to pass `max_epochs`. Moreover, we specify\n", "the first configuration to be evaluated in `initial_config`.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "51b63ea4", "metadata": {"attributes": {"classes": [], "id": "", "n": "39"}, "execution": {"iopub.execute_input": "2023-08-18T19:45:39.925689Z", "iopub.status.busy": "2023-08-18T19:45:39.925408Z", "iopub.status.idle": "2023-08-18T19:45:39.930817Z", "shell.execute_reply": "2023-08-18T19:45:39.929661Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [], "source": ["config_space = {\n", "    \"learning_rate\": loguniform(1e-2, 1),\n", "    \"batch_size\": randint(32, 256),\n", "    \"max_epochs\": 10,\n", "}\n", "initial_config = {\n", "    \"learning_rate\": 0.1,\n", "    \"batch_size\": 128,\n", "}"]}, {"cell_type": "markdown", "id": "863e6b0a", "metadata": {"origin_pos": 11}, "source": ["Next, we need to specify the back-end for job executions. Here we just consider\n", "the distribution on a local machine where parallel jobs are executed as\n", "sub-processes. However, for large scale HPO, we could run this also on a cluster\n", "or cloud environment, where each trial consumes a full instance.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "5afe2680", "metadata": {"attributes": {"classes": [], "id": "", "n": "40"}, "execution": {"iopub.execute_input": "2023-08-18T19:45:39.935131Z", "iopub.status.busy": "2023-08-18T19:45:39.934847Z", "iopub.status.idle": "2023-08-18T19:45:39.940103Z", "shell.execute_reply": "2023-08-18T19:45:39.938952Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [], "source": ["trial_backend = PythonBackend(\n", "    tune_function=hpo_objective_lenet_synetune,\n", "    config_space=config_space,\n", ")"]}, {"cell_type": "markdown", "id": "d335a513", "metadata": {"origin_pos": 13}, "source": ["We can now create the scheduler for asynchronous random search, which is similar\n", "in behaviour to our `BasicScheduler` from :numref:`sec_api_hpo`.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "3c626a35", "metadata": {"attributes": {"classes": [], "id": "", "n": "41"}, "execution": {"iopub.execute_input": "2023-08-18T19:45:39.944525Z", "iopub.status.busy": "2023-08-18T19:45:39.944248Z", "iopub.status.idle": "2023-08-18T19:45:39.952915Z", "shell.execute_reply": "2023-08-18T19:45:39.951830Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.optimizer.schedulers.fifo:max_resource_level = 10, as inferred from config_space\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.optimizer.schedulers.fifo:Master random_seed = 2737092907\n"]}], "source": ["scheduler = RandomSearch(\n", "    config_space,\n", "    metric=metric,\n", "    mode=mode,\n", "    points_to_evaluate=[initial_config],\n", ")"]}, {"cell_type": "markdown", "id": "0eea9b5f", "metadata": {"origin_pos": 15}, "source": ["Syne Tune also features a `Tuner`, where the main experiment loop and\n", "bookkeeping is centralized, and interactions between scheduler and back-end are\n", "mediated.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "f7261c66", "metadata": {"attributes": {"classes": [], "id": "", "n": "42"}, "execution": {"iopub.execute_input": "2023-08-18T19:45:39.957089Z", "iopub.status.busy": "2023-08-18T19:45:39.956804Z", "iopub.status.idle": "2023-08-18T19:45:39.961864Z", "shell.execute_reply": "2023-08-18T19:45:39.961044Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [], "source": ["stop_criterion = StoppingCriterion(max_wallclock_time=max_wallclock_time)\n", "\n", "tuner = Tuner(\n", "    trial_backend=trial_backend,\n", "    scheduler=scheduler,\n", "    stop_criterion=stop_criterion,\n", "    n_workers=n_workers,\n", "    print_update_interval=int(max_wallclock_time * 0.6),\n", ")"]}, {"cell_type": "markdown", "id": "3878fcff", "metadata": {"origin_pos": 17}, "source": ["Let us run our distributed HPO experiment. According to our stopping criterion,\n", "it will run for about 12 minutes.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "57befb31", "metadata": {"attributes": {"classes": [], "id": "", "n": "43"}, "execution": {"iopub.execute_input": "2023-08-18T19:45:39.966538Z", "iopub.status.busy": "2023-08-18T19:45:39.966233Z", "iopub.status.idle": "2023-08-18T19:57:42.878521Z", "shell.execute_reply": "2023-08-18T19:57:42.877375Z"}, "origin_pos": 18, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:results of trials will be saved on /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:Detected 4 GPUs\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.1 --batch_size 128 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/0/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 0) - scheduled config {'learning_rate': 0.1, 'batch_size': 128, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.1702844732454753 --batch_size 114 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/1/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 1) - scheduled config {'learning_rate': 0.1702844732454753, 'batch_size': 114, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 0 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 1 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.34019846567238493 --batch_size 221 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/2/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 2) - scheduled config {'learning_rate': 0.34019846567238493, 'batch_size': 221, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.014628124155727769 --batch_size 88 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/3/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 3) - scheduled config {'learning_rate': 0.014628124155727769, 'batch_size': 88, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 2 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.1114831485450576 --batch_size 142 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/4/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 4) - scheduled config {'learning_rate': 0.1114831485450576, 'batch_size': 142, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 3 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.014076038679980779 --batch_size 223 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/5/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 5) - scheduled config {'learning_rate': 0.014076038679980779, 'batch_size': 223, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 4 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.02558173674804846 --batch_size 62 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/6/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 6) - scheduled config {'learning_rate': 0.02558173674804846, 'batch_size': 62, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 5 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.026035979388614055 --batch_size 139 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/7/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 7) - scheduled config {'learning_rate': 0.026035979388614055, 'batch_size': 139, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 6 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.24202494130424274 --batch_size 231 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/8/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 8) - scheduled config {'learning_rate': 0.24202494130424274, 'batch_size': 231, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 7 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.10483132064775551 --batch_size 145 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/9/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 9) - scheduled config {'learning_rate': 0.10483132064775551, 'batch_size': 145, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 8 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.017898854850751864 --batch_size 51 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/10/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 10) - scheduled config {'learning_rate': 0.017898854850751864, 'batch_size': 51, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 9 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.9645419978270817 --batch_size 200 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/11/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 11) - scheduled config {'learning_rate': 0.9645419978270817, 'batch_size': 200, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 11 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.10559888854748693 --batch_size 40 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/12/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 12) - scheduled config {'learning_rate': 0.10559888854748693, 'batch_size': 40, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:tuning status (last metric is reported)\n", " trial_id     status  iter  learning_rate  batch_size  max_epochs  epoch  validation_error  worker-time\n", "        0  Completed    10       0.100000         128          10   10.0          0.277195    64.928907\n", "        1  Completed    10       0.170284         114          10   10.0          0.286225    65.434195\n", "        2  Completed    10       0.340198         221          10   10.0          0.218990    59.729758\n", "        3  Completed    10       0.014628          88          10   10.0          0.899920    81.001636\n", "        4  Completed    10       0.111483         142          10   10.0          0.268684    64.427400\n", "        5  Completed    10       0.014076         223          10   10.0          0.899922    61.264475\n", "        6  Completed    10       0.025582          62          10   10.0          0.399520    75.966186\n", "        7  Completed    10       0.026036         139          10   10.0          0.899988    62.261541\n", "        8  Completed    10       0.242025         231          10   10.0          0.257636    58.186485\n", "        9  Completed    10       0.104831         145          10   10.0          0.273898    59.771699\n", "       10 InProgress     8       0.017899          51          10    8.0          0.496118    66.999746\n", "       11  Completed    10       0.964542         200          10   10.0          0.181600    59.159662\n", "       12 InProgress     0       0.105599          40          10      -                 -            -\n", "2 trials running, 11 finished (11 until the end), 436.60s wallclock-time\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 10 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.5846051207380589 --batch_size 35 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/13/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 13) - scheduled config {'learning_rate': 0.5846051207380589, 'batch_size': 35, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 12 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.2468891379769198 --batch_size 146 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/14/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 14) - scheduled config {'learning_rate': 0.2468891379769198, 'batch_size': 146, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 13 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.12956867470224812 --batch_size 218 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/15/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 15) - scheduled config {'learning_rate': 0.12956867470224812, 'batch_size': 218, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 14 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.24900745354561854 --batch_size 103 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/16/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 16) - scheduled config {'learning_rate': 0.24900745354561854, 'batch_size': 103, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 15 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.03903577426988046 --batch_size 80 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/17/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 17) - scheduled config {'learning_rate': 0.03903577426988046, 'batch_size': 80, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 16 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.01846559300690354 --batch_size 183 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/tune_function --tune_function_hash 4d7d5b85e4537ad0c5d0a202623dcec5 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958/18/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 18) - scheduled config {'learning_rate': 0.01846559300690354, 'batch_size': 183, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.stopping_criterion:reaching max wallclock time (720), stopping there.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Stopping trials that may still be running.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Tuning finished, results of trials can be found on /home/<USER>/syne-tune/python-entrypoint-2023-08-18-19-45-39-958\n"]}, {"name": "stdout", "output_type": "stream", "text": ["--------------------\n", "Resource summary (last result is reported):\n", " trial_id     status  iter  learning_rate  batch_size  max_epochs  epoch  validation_error  worker-time\n", "        0  Completed    10       0.100000         128          10     10          0.277195    64.928907\n", "        1  Completed    10       0.170284         114          10     10          0.286225    65.434195\n", "        2  Completed    10       0.340198         221          10     10          0.218990    59.729758\n", "        3  Completed    10       0.014628          88          10     10          0.899920    81.001636\n", "        4  Completed    10       0.111483         142          10     10          0.268684    64.427400\n", "        5  Completed    10       0.014076         223          10     10          0.899922    61.264475\n", "        6  Completed    10       0.025582          62          10     10          0.399520    75.966186\n", "        7  Completed    10       0.026036         139          10     10          0.899988    62.261541\n", "        8  Completed    10       0.242025         231          10     10          0.257636    58.186485\n", "        9  Completed    10       0.104831         145          10     10          0.273898    59.771699\n", "       10  Completed    10       0.017899          51          10     10          0.405545    83.778503\n", "       11  Completed    10       0.964542         200          10     10          0.181600    59.159662\n", "       12  Completed    10       0.105599          40          10     10          0.182500    94.734384\n", "       13  Completed    10       0.584605          35          10     10          0.153846   110.965637\n", "       14  Completed    10       0.246889         146          10     10          0.215050    65.142847\n", "       15  Completed    10       0.129569         218          10     10          0.313873    61.310455\n", "       16  Completed    10       0.249007         103          10     10          0.196101    72.519127\n", "       17 InProgress     9       0.039036          80          10      9          0.369000    73.403000\n", "       18 InProgress     5       0.018466         183          10      5          0.900263    34.714568\n", "2 trials running, 17 finished (17 until the end), 722.84s wallclock-time\n", "\n", "validation_error: best 0.14451533555984497 for trial-id 13\n", "--------------------\n"]}], "source": ["tuner.run()"]}, {"cell_type": "markdown", "id": "2dd14e00", "metadata": {"origin_pos": 19}, "source": ["The logs of all evaluated hyperparameter configurations are stored for further\n", "analysis. At any time during the tuning job, we can easily get the results\n", "obtained so far and plot the incumbent trajectory.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "c6323d54", "metadata": {"attributes": {"classes": [], "id": "", "n": "46"}, "execution": {"iopub.execute_input": "2023-08-18T19:57:42.883230Z", "iopub.status.busy": "2023-08-18T19:57:42.882930Z", "iopub.status.idle": "2023-08-18T19:57:43.133156Z", "shell.execute_reply": "2023-08-18T19:57:43.132313Z"}, "origin_pos": 20, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:matplotlib.legend:No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"412.78125pt\" height=\"198.474375pt\" viewBox=\"0 0 412.78125 198.474375\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:57:43.081792</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 198.474375 \n", "L 412.78125 198.474375 \n", "L 412.78125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 108.**********.918125 \n", "L 304.**********.918125 \n", "L 304.040625 22.318125 \n", "L 108.740625 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"md7692efe04\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md7692efe04\" x=\"113.782395\" y=\"160.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(110.601145 175.516563) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#md7692efe04\" x=\"164.318496\" y=\"160.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(154.774746 175.516563) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#md7692efe04\" x=\"214.854596\" y=\"160.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(205.310846 175.516563) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#md7692efe04\" x=\"265.390697\" y=\"160.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(255.846947 175.516563) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- wallclock time -->\n", "     <g transform=\"translate(170.732031 189.194688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-77\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"81.787109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"143.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"170.849609\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"198.632812\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"253.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"281.396484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"342.578125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"397.558594\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"455.46875\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"487.255859\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"526.464844\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"554.248047\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"651.660156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"m8595c6d65e\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8595c6d65e\" x=\"108.740625\" y=\"145.369458\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(85.8375 149.168677) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m8595c6d65e\" x=\"108.740625\" y=\"112.031716\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(85.8375 115.830935) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m8595c6d65e\" x=\"108.740625\" y=\"78.693974\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(85.8375 82.493193) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m8595c6d65e\" x=\"108.740625\" y=\"45.356232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(85.8375 49.155451) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- validation_error -->\n", "     <g transform=\"translate(79.479687 130.837656) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"176.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"239.501953\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"300.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"339.990234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"367.773438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"428.955078\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"492.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"542.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"603.857422\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"643.220703\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"682.083984\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"743.265625\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_9\">\n", "    <path d=\"M 117.617898 28.618125 \n", "L 117.619236 28.673098 \n", "L 118.885346 28.699029 \n", "L 120.150348 29.623669 \n", "L 120.150359 75.929797 \n", "L 121.41516 75.929797 \n", "L 121.4159 116.764289 \n", "L 123.947062 116.764289 \n", "L 123.947074 124.971603 \n", "L 125.211576 124.971603 \n", "L 125.212559 127.573829 \n", "L 126.479912 127.573829 \n", "L 126.479919 133.426179 \n", "L 140.410686 133.426179 \n", "L 141.67569 133.775499 \n", "L 142.945587 133.775499 \n", "L 142.945594 136.251416 \n", "L 145.474488 136.251416 \n", "L 146.7449 138.334387 \n", "L 148.009362 142.204005 \n", "L 213.954859 142.204005 \n", "L 215.220274 146.319584 \n", "L 216.490771 148.603211 \n", "L 217.755174 148.603211 \n", "L 219.019589 149.853385 \n", "L 220.291179 151.436922 \n", "L 244.384673 151.436922 \n", "L 244.385747 151.803937 \n", "L 246.921822 151.803937 \n", "L 246.921832 154.00199 \n", "L 249.456488 154.00199 \n", "L 250.737154 154.618125 \n", "L 295.163352 154.618125 \n", "L 295.163352 154.618125 \n", "\" clip-path=\"url(#p3de49c2089)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 108.**********.918125 \n", "L 108.740625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 304.**********.918125 \n", "L 304.040625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 108.**********.918125 \n", "L 304.**********.918125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 108.740625 22.318125 \n", "L 304.040625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <!-- Best result over time python-entrypoint-2023-08-18-19-45-39-958 -->\n", "    <g transform=\"translate(7.2 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-42\" d=\"M 1259 2228 \n", "L 1259 519 \n", "L 2272 519 \n", "Q 2781 519 3026 730 \n", "Q 3272 941 3272 1375 \n", "Q 3272 1813 3026 2020 \n", "Q 2781 2228 2272 2228 \n", "L 1259 2228 \n", "z\n", "M 1259 4147 \n", "L 1259 2741 \n", "L 2194 2741 \n", "Q 2656 2741 2882 2914 \n", "Q 3109 3088 3109 3444 \n", "Q 3109 3797 2882 3972 \n", "Q 2656 4147 2194 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2241 4666 \n", "Q 2963 4666 3353 4366 \n", "Q 3744 4066 3744 3513 \n", "Q 3744 3084 3544 2831 \n", "Q 3344 2578 2956 2516 \n", "Q 3422 2416 3680 2098 \n", "Q 3938 1781 3938 1306 \n", "Q 3938 681 3513 340 \n", "Q 3088 0 2303 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-42\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"68.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"130.126953\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"182.226562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"221.435547\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"253.222656\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"292.085938\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"353.609375\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"405.708984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"469.087891\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"496.871094\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"536.080078\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"567.867188\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"629.048828\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"688.228516\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"749.751953\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"790.865234\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"822.652344\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"861.861328\"/>\n", "     <use xlink:href=\"#DejaVuSans-6d\" x=\"889.644531\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"987.056641\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"1048.580078\"/>\n", "     <use xlink:href=\"#DejaVuSans-70\" x=\"1080.367188\"/>\n", "     <use xlink:href=\"#DejaVuSans-79\" x=\"1143.84375\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"1203.023438\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"1242.232422\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"1305.611328\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"1366.792969\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"1430.171875\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"1466.255859\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"1527.779297\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"1591.158203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"1630.367188\"/>\n", "     <use xlink:href=\"#DejaVuSans-79\" x=\"1671.480469\"/>\n", "     <use xlink:href=\"#DejaVuSans-70\" x=\"1730.660156\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"1794.136719\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"1855.318359\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"1883.101562\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"1946.480469\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"1985.689453\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"2021.773438\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"2085.396484\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"2149.019531\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"2212.642578\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"2276.265625\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"2312.349609\"/>\n", "     <use xlink:href=\"#DejaVuSans-38\" x=\"2375.972656\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"2439.595703\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"2475.679688\"/>\n", "     <use xlink:href=\"#DejaVuSans-38\" x=\"2539.302734\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"2602.925781\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"2639.009766\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"2702.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"2766.255859\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"2802.339844\"/>\n", "     <use xlink:href=\"#DejaVuSans-35\" x=\"2865.962891\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"2929.585938\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"2965.669922\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"3029.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"3092.916016\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"3129\"/>\n", "     <use xlink:href=\"#DejaVuSans-35\" x=\"3192.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-38\" x=\"3256.246094\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 293.040625 35.318125 \n", "L 297.040625 35.318125 \n", "Q 299.040625 35.318125 299.040625 33.318125 \n", "L 299.040625 29.318125 \n", "Q 299.040625 27.318125 297.040625 27.318125 \n", "L 293.040625 27.318125 \n", "Q 291.040625 27.318125 291.040625 29.318125 \n", "L 291.040625 33.318125 \n", "Q 291.040625 35.318125 293.040625 35.318125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p3de49c2089\">\n", "   <rect x=\"108.740625\" y=\"22.318125\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.set_figsize()\n", "tuning_experiment = load_experiment(tuner.name)\n", "tuning_experiment.plot()"]}, {"cell_type": "markdown", "id": "8cf3f5f6", "metadata": {"origin_pos": 21}, "source": ["## Visualize the Asynchronous Optimization Process\n", "\n", "Below we visualize how the learning curves of every trial (each color in the plot represents a trial) evolve during the\n", "asynchronous optimization process. At any point in time, there are as many trials\n", "running concurrently as we have workers. Once a trial finishes, we immediately\n", "start the next trial, without waiting for the other trials to finish. Idle time\n", "of workers is reduced to a minimum with asynchronous scheduling.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "8c1d8876", "metadata": {"attributes": {"classes": [], "id": "", "n": "45"}, "execution": {"iopub.execute_input": "2023-08-18T19:57:43.138953Z", "iopub.status.busy": "2023-08-18T19:57:43.138373Z", "iopub.status.idle": "2023-08-18T19:57:43.445360Z", "shell.execute_reply": "2023-08-18T19:57:43.444209Z"}, "origin_pos": 22, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'objective function')"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"385.78125pt\" height=\"183.35625pt\" viewBox=\"0 0 385.78125 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T19:57:43.374578</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 385.78125 183.35625 \n", "L 385.78125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 43.78125 145.8 \n", "L 378.58125 145.8 \n", "L 378.58125 7.2 \n", "L 43.78125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"ma802e49f73\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma802e49f73\" x=\"52.424285\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(49.243035 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#ma802e49f73\" x=\"95.740943\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(86.197193 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#ma802e49f73\" x=\"139.0576\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(129.51385 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#ma802e49f73\" x=\"182.374258\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(172.830508 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#ma802e49f73\" x=\"225.690915\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(216.147165 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#ma802e49f73\" x=\"269.007573\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(259.463823 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#ma802e49f73\" x=\"312.32423\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(302.78048 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#ma802e49f73\" x=\"355.640888\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 700 -->\n", "      <g transform=\"translate(346.097138 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- wall-clock time -->\n", "     <g transform=\"translate(173.71875 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-77\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"81.787109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"143.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"170.849609\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"198.632812\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"234.716797\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"289.697266\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"317.480469\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"378.662109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"433.642578\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"491.552734\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"523.339844\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"562.548828\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"590.332031\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"687.744141\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <defs>\n", "       <path id=\"me6815fb48a\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me6815fb48a\" x=\"43.78125\" y=\"130.746973\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(20.878125 134.546192) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#me6815fb48a\" x=\"43.78125\" y=\"99.195817\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(20.878125 102.995036) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#me6815fb48a\" x=\"43.78125\" y=\"67.644661\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(20.878125 71.443879) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#me6815fb48a\" x=\"43.78125\" y=\"36.093504\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(20.878125 39.892723) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- objective function -->\n", "     <g transform=\"translate(14.798438 121.346875) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6a\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 -63 \n", "Q 1178 -731 923 -1031 \n", "Q 669 -1331 103 -1331 \n", "L -116 -1331 \n", "L -116 -844 \n", "L 38 -844 \n", "Q 366 -844 484 -692 \n", "Q 603 -541 603 -63 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6f\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"61.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-6a\" x=\"124.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"152.441406\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"213.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"268.945312\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"308.154297\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"335.9375\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"395.117188\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"456.640625\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"488.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"523.632812\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"587.011719\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"650.390625\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"705.371094\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"744.580078\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"772.363281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"833.544922\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 58.999432 20.2524 \n", "L 61.172188 20.143194 \n", "L 63.340775 21.204057 \n", "L 65.509023 25.13547 \n", "L 69.849427 92.421935 \n", "L 72.017166 99.1459 \n", "L 74.191457 108.319201 \n", "L 78.526571 113.670284 \n", "L 80.699372 116.618851 \n", "L 82.868107 118.568954 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m25d14d4d19\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #1f77b4\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"58.999432\" y=\"20.2524\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"61.172188\" y=\"20.143194\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"63.340775\" y=\"21.204057\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"65.509023\" y=\"25.13547\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"69.849427\" y=\"92.421935\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"72.017166\" y=\"99.1459\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"74.191457\" y=\"108.319201\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"78.526571\" y=\"113.670284\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"80.699372\" y=\"116.618851\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"82.868107\" y=\"118.568954\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 59.001726 20.304427 \n", "L 61.1722 20.328969 \n", "L 63.340794 65.028617 \n", "L 65.510294 103.674769 \n", "L 69.849448 111.442249 \n", "L 72.018851 113.90502 \n", "L 74.191469 119.443741 \n", "L 78.527593 115.068676 \n", "L 80.699385 110.002437 \n", "L 82.868128 117.144412 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m42a677b7b6\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"59.001726\" y=\"20.304427\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"61.1722\" y=\"20.328969\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"63.340794\" y=\"65.028617\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"65.510294\" y=\"103.674769\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"69.849448\" y=\"111.442249\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"72.018851\" y=\"113.90502\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"74.191469\" y=\"119.443741\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"78.527593\" y=\"115.068676\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"80.699385\" y=\"110.002437\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"82.868128\" y=\"117.144412\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 87.22107 33.594548 \n", "L 89.395747 86.500993 \n", "L 91.563281 106.677177 \n", "L 93.730767 108.120337 \n", "L 98.072771 117.959603 \n", "L 100.241362 119.77434 \n", "L 102.418339 122.117571 \n", "L 104.586113 121.947406 \n", "L 108.931423 124.088915 \n", "L 111.099085 127.751158 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"me9490f2265\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #2ca02c\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#me9490f2265\" x=\"87.22107\" y=\"33.594548\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"89.395747\" y=\"86.500993\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"91.563281\" y=\"106.677177\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"93.730767\" y=\"108.120337\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"98.072771\" y=\"117.959603\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"100.241362\" y=\"119.77434\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"102.418339\" y=\"122.117571\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"104.586113\" y=\"121.947406\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"108.931423\" y=\"124.088915\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"111.099085\" y=\"127.751158\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 89.395727 20.330501 \n", "L 91.56327 20.339491 \n", "L 95.905223 20.294563 \n", "L 98.072782 20.303543 \n", "L 102.418327 20.303543 \n", "L 106.753586 20.285574 \n", "L 108.931435 20.35746 \n", "L 113.276905 20.285574 \n", "L 117.62072 20.35746 \n", "L 119.790001 20.330501 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m4566fd4f32\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #d62728\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"89.395727\" y=\"20.330501\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"91.56327\" y=\"20.339491\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"95.905223\" y=\"20.294563\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"98.072782\" y=\"20.303543\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"102.418327\" y=\"20.303543\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"106.753586\" y=\"20.285574\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"108.931435\" y=\"20.35746\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"113.276905\" y=\"20.285574\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"117.62072\" y=\"20.35746\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"119.790001\" y=\"20.330501\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 117.620732 20.253773 \n", "L 119.788507 20.253764 \n", "L 121.975041 30.062 \n", "L 124.14262 77.062745 \n", "L 126.310162 96.286981 \n", "L 130.653875 103.417974 \n", "L 132.824705 111.739693 \n", "L 134.999613 115.213398 \n", "L 139.338846 118.411172 \n", "L 141.519526 119.911736 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #9467bd; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"mf97b5cfde1\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #9467bd\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"117.620732\" y=\"20.253773\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"119.788507\" y=\"20.253764\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"121.975041\" y=\"30.062\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"124.14262\" y=\"77.062745\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"126.310162\" y=\"96.286981\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"130.653875\" y=\"103.417974\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"132.824705\" y=\"111.739693\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"134.999613\" y=\"115.213398\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"139.338846\" y=\"118.411172\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"141.519526\" y=\"119.911736\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 124.142641 20.327295 \n", "L 128.48627 20.30973 \n", "L 130.65389 20.325452 \n", "L 132.822519 20.318513 \n", "L 134.999591 20.327295 \n", "L 139.336664 20.327295 \n", "L 141.519515 20.318513 \n", "L 143.695633 20.361663 \n", "L 145.863173 20.318513 \n", "L 148.039544 20.330219 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #8c564b; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"mcd59339176\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #8c564b\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#mcd59339176\" x=\"124.142641\" y=\"20.327295\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"128.48627\" y=\"20.30973\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"130.65389\" y=\"20.325452\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"132.822519\" y=\"20.318513\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"134.999591\" y=\"20.327295\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"139.336664\" y=\"20.327295\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"141.519515\" y=\"20.318513\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"143.695633\" y=\"20.361663\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"145.863173\" y=\"20.318513\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"148.039544\" y=\"20.330219\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 148.03953 20.28722 \n", "L 150.208059 20.28722 \n", "L 154.552493 20.28722 \n", "L 156.735665 30.637782 \n", "L 161.072509 20.28722 \n", "L 163.248759 39.492726 \n", "L 167.586032 66.317624 \n", "L 169.760132 88.66269 \n", "L 174.095256 96.838795 \n", "L 176.27174 99.271562 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #e377c2; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"md5da12d5e8\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #e377c2\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"148.03953\" y=\"20.28722\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"150.208059\" y=\"20.28722\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"154.552493\" y=\"20.28722\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"156.735665\" y=\"30.637782\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"161.072509\" y=\"20.28722\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"163.248759\" y=\"39.492726\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"167.586032\" y=\"66.317624\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"169.760132\" y=\"88.66269\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"174.095256\" y=\"96.838795\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"176.27174\" y=\"99.271562\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 156.735653 20.318795 \n", "L 158.904232 20.316867 \n", "L 161.073869 20.315908 \n", "L 163.248747 20.319754 \n", "L 167.584626 20.319754 \n", "L 169.76012 20.319754 \n", "L 171.92776 20.313021 \n", "L 174.096689 20.319754 \n", "L 176.271758 20.319754 \n", "L 180.627364 20.319754 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #7f7f7f; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"mb38b3164da\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #7f7f7f\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#mb38b3164da\" x=\"156.735653\" y=\"20.318795\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"158.904232\" y=\"20.316867\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"161.073869\" y=\"20.315908\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"163.248747\" y=\"20.319754\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"167.584626\" y=\"20.319754\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"169.76012\" y=\"20.319754\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"171.92776\" y=\"20.313021\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"174.096689\" y=\"20.319754\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"176.271758\" y=\"20.319754\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"180.627364\" y=\"20.319754\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 182.829113 20.177355 \n", "L 184.997308 32.556657 \n", "L 187.165742 61.62314 \n", "L 191.513735 92.355719 \n", "L 193.681299 106.880169 \n", "L 195.859719 112.174459 \n", "L 198.026834 115.759824 \n", "L 200.195502 118.529508 \n", "L 204.552069 121.576966 \n", "L 206.720878 121.654569 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #bcbd22; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m528480f9e8\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #bcbd22\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#m528480f9e8\" x=\"182.829113\" y=\"20.177355\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"184.997308\" y=\"32.556657\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"187.165742\" y=\"61.62314\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"191.513735\" y=\"92.355719\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"193.681299\" y=\"106.880169\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"195.859719\" y=\"112.174459\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"198.026834\" y=\"115.759824\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"200.195502\" y=\"118.529508\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"204.552069\" y=\"121.576966\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"206.720878\" y=\"121.654569\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 184.997328 20.319058 \n", "L 187.167829 20.318494 \n", "L 191.513747 68.00338 \n", "L 193.682831 93.958137 \n", "L 195.859731 99.919455 \n", "L 198.026846 107.178206 \n", "L 200.198167 112.954276 \n", "L 204.55208 115.414048 \n", "L 206.723467 118.270795 \n", "L 208.92122 119.08906 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #17becf; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m15b531c368\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #17becf\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#m15b531c368\" x=\"184.997328\" y=\"20.319058\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#m15b531c368\" x=\"187.167829\" y=\"20.318494\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#m15b531c368\" x=\"191.513747\" y=\"68.00338\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#m15b531c368\" x=\"193.682831\" y=\"93.958137\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#m15b531c368\" x=\"195.859731\" y=\"99.919455\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#m15b531c368\" x=\"198.026846\" y=\"107.178206\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#m15b531c368\" x=\"200.198167\" y=\"112.954276\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#m15b531c368\" x=\"204.55208\" y=\"115.414048\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#m15b531c368\" x=\"206.723467\" y=\"118.270795\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#m15b531c368\" x=\"208.92122\" y=\"119.08906\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 213.26686 20.244135 \n", "L 215.457422 20.244135 \n", "L 219.794963 20.244135 \n", "L 224.148496 20.244135 \n", "L 226.317792 20.244135 \n", "L 230.663335 24.687738 \n", "L 235.010771 30.293266 \n", "L 239.354393 84.032608 \n", "L 241.545405 94.175881 \n", "L 245.882512 98.321137 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"213.26686\" y=\"20.244135\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"215.457422\" y=\"20.244135\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"219.794963\" y=\"20.244135\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"224.148496\" y=\"20.244135\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"226.317792\" y=\"20.244135\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"230.663335\" y=\"24.687738\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"235.010771\" y=\"30.293266\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"239.354393\" y=\"84.032608\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"241.545405\" y=\"94.175881\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m25d14d4d19\" x=\"245.882512\" y=\"98.321137\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 213.27024 61.397525 \n", "L 215.457433 104.811918 \n", "L 219.792551 122.038821 \n", "L 221.980366 125.414819 \n", "L 224.148508 127.307902 \n", "L 226.316074 131.646182 \n", "L 228.495787 133.807428 \n", "L 232.830902 134.990605 \n", "L 235.010751 136.489279 \n", "L 237.17829 133.649674 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"213.27024\" y=\"61.397525\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"215.457433\" y=\"104.811918\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"219.792551\" y=\"122.038821\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"221.980366\" y=\"125.414819\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"224.148508\" y=\"127.307902\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"226.316074\" y=\"131.646182\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"228.495787\" y=\"133.807428\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"232.830902\" y=\"134.990605\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"235.010751\" y=\"136.489279\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m42a677b7b6\" x=\"237.17829\" y=\"133.649674\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 243.713978 20.31792 \n", "L 248.075033 106.326409 \n", "L 252.410018 115.38157 \n", "L 256.767668 120.508637 \n", "L 258.938368 123.300889 \n", "L 263.29116 125.746096 \n", "L 267.628903 128.995934 \n", "L 271.977032 130.242176 \n", "L 276.313904 131.614644 \n", "L 280.663301 133.507661 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#me9490f2265\" x=\"243.713978\" y=\"20.31792\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"248.075033\" y=\"106.326409\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"252.410018\" y=\"115.38157\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"256.767668\" y=\"120.508637\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"258.938368\" y=\"123.300889\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"263.29116\" y=\"125.746096\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"267.628903\" y=\"128.995934\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"271.977032\" y=\"130.242176\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"276.313904\" y=\"131.614644\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#me9490f2265\" x=\"280.663301\" y=\"133.507661\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 254.600226 25.846476 \n", "L 258.936218 115.444203 \n", "L 263.291148 126.034824 \n", "L 267.627068 128.493335 \n", "L 271.977043 131.33011 \n", "L 276.315745 136.836625 \n", "L 280.66332 138.916884 \n", "L 287.203871 139.5 \n", "L 291.540746 136.946894 \n", "L 295.888778 138.028039 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"254.600226\" y=\"25.846476\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"258.936218\" y=\"115.444203\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"263.291148\" y=\"126.034824\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"267.627068\" y=\"128.493335\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"271.977043\" y=\"131.33011\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"276.315745\" y=\"136.836625\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"280.66332\" y=\"138.916884\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"287.203871\" y=\"139.5\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"291.540746\" y=\"136.946894\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m4566fd4f32\" x=\"295.888778\" y=\"138.028039\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 285.008443 20.362989 \n", "L 289.371462 78.079865 \n", "L 291.538835 103.784351 \n", "L 293.720258 115.389055 \n", "L 295.88879 118.441402 \n", "L 300.233365 122.514659 \n", "L 302.41824 122.733476 \n", "L 304.585809 124.581283 \n", "L 308.937203 126.634498 \n", "L 311.104237 128.372695 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #9467bd; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"285.008443\" y=\"20.362989\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"289.371462\" y=\"78.079865\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"291.538835\" y=\"103.784351\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"293.720258\" y=\"115.389055\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"295.88879\" y=\"118.441402\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"300.233365\" y=\"122.514659\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"302.41824\" y=\"122.733476\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"304.585809\" y=\"124.581283\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"308.937203\" y=\"126.634498\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#mf97b5cfde1\" x=\"311.104237\" y=\"128.372695\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 300.23666 20.329514 \n", "L 302.418251 20.31792 \n", "L 306.754189 20.31792 \n", "L 308.937223 23.637275 \n", "L 311.104224 73.792521 \n", "L 313.283794 92.293838 \n", "L 315.4808 98.353784 \n", "L 319.816133 104.129093 \n", "L 322.014702 104.116671 \n", "L 324.18312 112.782926 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #8c564b; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#mcd59339176\" x=\"300.23666\" y=\"20.329514\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"302.418251\" y=\"20.31792\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"306.754189\" y=\"20.31792\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"308.937223\" y=\"23.637275\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"311.104224\" y=\"73.792521\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"313.283794\" y=\"92.293838\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"315.4808\" y=\"98.353784\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"319.816133\" y=\"104.129093\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"322.014702\" y=\"104.116671\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#mcd59339176\" x=\"324.18312\" y=\"112.782926\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 317.648366 20.334253 \n", "L 319.819463 100.215837 \n", "L 322.014714 115.358269 \n", "L 326.351644 117.046207 \n", "L 328.545123 123.203869 \n", "L 330.712663 125.985787 \n", "L 335.066868 127.282918 \n", "L 339.402002 126.735957 \n", "L 341.586907 131.174482 \n", "L 345.923288 131.362043 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #e377c2; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"317.648366\" y=\"20.334253\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"319.819463\" y=\"100.215837\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"322.014714\" y=\"115.358269\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"326.351644\" y=\"117.046207\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"328.545123\" y=\"123.203869\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"330.712663\" y=\"125.985787\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"335.066868\" y=\"127.282918\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"339.402002\" y=\"126.735957\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"341.586907\" y=\"131.174482\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#md5da12d5e8\" x=\"345.923288\" y=\"131.362043\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 332.880241 20.31793 \n", "L 335.066879 20.31792 \n", "L 339.4041 33.742946 \n", "L 341.586919 20.31793 \n", "L 345.926787 47.562361 \n", "L 350.303295 88.152908 \n", "L 352.474543 95.725201 \n", "L 356.827617 101.893441 \n", "L 361.165537 104.086243 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #7f7f7f; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#mb38b3164da\" x=\"332.880241\" y=\"20.31793\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"335.066879\" y=\"20.31792\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"339.4041\" y=\"33.742946\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"341.586919\" y=\"20.31793\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"345.926787\" y=\"47.562361\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"350.303295\" y=\"88.152908\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"352.474543\" y=\"95.725201\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"356.827617\" y=\"101.893441\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#mb38b3164da\" x=\"361.165537\" y=\"104.086243\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 352.471626 18.611214 \n", "L 354.66002 20.336924 \n", "L 356.827636 20.321249 \n", "L 361.162557 13.5 \n", "L 363.363068 20.276481 \n", "\" clip-path=\"url(#p2115b07e64)\" style=\"fill: none; stroke: #bcbd22; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#p2115b07e64)\">\n", "     <use xlink:href=\"#m528480f9e8\" x=\"352.471626\" y=\"18.611214\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"354.66002\" y=\"20.336924\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"356.827636\" y=\"20.321249\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"361.162557\" y=\"13.5\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#m528480f9e8\" x=\"363.363068\" y=\"20.276481\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 43.78125 145.8 \n", "L 43.78125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 378.58125 145.8 \n", "L 378.58125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 43.78125 145.8 \n", "L 378.58125 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 43.78125 7.2 \n", "L 378.58125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p2115b07e64\">\n", "   <rect x=\"43.78125\" y=\"7.2\" width=\"334.8\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.set_figsize([6, 2.5])\n", "results = tuning_experiment.results\n", "\n", "for trial_id in results.trial_id.unique():\n", "    df = results[results[\"trial_id\"] == trial_id]\n", "    d2l.plt.plot(\n", "        df[\"st_tuner_time\"],\n", "        df[\"validation_error\"],\n", "        marker=\"o\"\n", "    )\n", "\n", "d2l.plt.xlabel(\"wall-clock time\")\n", "d2l.plt.ylabel(\"objective function\")"]}, {"cell_type": "markdown", "id": "e8e40d99", "metadata": {"origin_pos": 23}, "source": ["## Summary\n", "\n", "We can reduce the waiting time for random search substantially by distribution\n", "trials across parallel resources. In general, we distinguish between synchronous\n", "scheduling and asynchronous scheduling. Synchronous scheduling means that we\n", "sample a new batch of hyperparameter configurations once the previous batch\n", "finished. If we have a stragglers - trials that takes more time to finish than\n", "other trials - our workers need to wait at synchronization points. Asynchronous\n", "scheduling evaluates a new hyperparameter configurations as soon as resources\n", "become available, and, hence, ensures that all workers are busy at any point in\n", "time. While random search is easy to distribute asynchronously and does not\n", "require any change of the actual algorithm, other methods require some additional\n", "modifications.\n", "\n", "## Exercises\n", "\n", "1. Consider the `DropoutMLP` model implemented in :numref:`sec_dropout`, and used in Exercise 1 of :numref:`sec_api_hpo`.\n", "    1. Implement an objective function `hpo_objective_dropoutmlp_synetune` to be used with Syne Tune. Make sure that your function reports the validation error after every epoch.\n", "    2. Using the setup of Exercise 1 in :numref:`sec_api_hpo`, compare random search to Bayesian optimization. If you use SageMaker, feel free to use Syne Tune's benchmarking facilities in order to run experiments in parallel. Hint: Bayesian optimization is provided as `syne_tune.optimizer.baselines.BayesianOptimization`.\n", "    3. For this exercise, you need to run on an instance with at least 4 CPU cores. For one of the methods used above (random search, Bayesian optimization), run experiments with `n_workers=1`, `n_workers=2`, `n_workers=4`, and compare results (incumbent trajectories). At least for random search, you should observe linear scaling with respect to the number of workers. Hint: For robust results, you may have to average over several repetitions each.\n", "2. *Advanced*. The goal of this exercise is to implement a new scheduler in Syne Tune.\n", "    1. Create a virtual environment containing both the [d2lbook](https://github.com/d2l-ai/d2l-en/blob/master/INFO.md#installation-for-developers) and [syne-tune](https://syne-tune.readthedocs.io/en/latest/getting_started.html) sources.\n", "    2. Implement the `LocalSearcher` from Exercise 2 in :numref:`sec_api_hpo` as a new searcher in Syne Tune. Hint: Read [this tutorial](https://syne-tune.readthedocs.io/en/latest/tutorials/developer/README.html). Alternatively, you may follow this [example](https://syne-tune.readthedocs.io/en/latest/examples.html#launch-hpo-experiment-with-home-made-scheduler).\n", "    3. Compare your new `LocalSearcher` with `RandomSearch` on the `DropoutMLP` benchmark.\n"]}, {"cell_type": "markdown", "id": "a15754f4", "metadata": {"origin_pos": 24, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/12093)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": ["\"syne-tune[gpsearchers]==0.3.2\""]}, "nbformat": 4, "nbformat_minor": 5}
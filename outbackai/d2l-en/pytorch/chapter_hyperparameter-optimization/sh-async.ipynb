{"cells": [{"cell_type": "markdown", "id": "e78b2825", "metadata": {"origin_pos": 1}, "source": ["# Asynchronous Successive Halving\n", "\n", ":label:`sec_sh_async`\n", "\n", "As we have seen in :numref:`sec_rs_async`, we can accelerate HPO by\n", "distributing the evaluation of hyperparameter configurations across either\n", "multiple instances or multiples CPUs / GPUs on a single instance. However,\n", "compared to random search, it is not straightforward to run\n", "successive halving (SH) asynchronously in a distributed setting. Before we can\n", "decide which configuration to run next, we first have to collect all\n", "observations at the current rung level. This requires to\n", "synchronize workers at each rung level. For example, for the lowest rung level\n", "$r_{\\mathrm{min}}$, we first have to evaluate all $N = \\eta^K$ configurations, before we\n", "can promote the $\\frac{1}{\\eta}$ of them to the next rung level.\n", "\n", "In any distributed system, synchronization typically implies idle time for workers.\n", "First, we often observe high variations in training time across hyperparameter\n", "configurations. For example, assuming the number of filters per layer is a\n", "hyperparameter, then networks with less filters finish training faster than\n", "networks with more filters, which implies idle worker time due to stragglers.\n", "Moreover, the number of slots in a rung level is not always a multiple of the number\n", "of workers, in which case some workers may even sit idle for a full batch.\n", "\n", "Figure :numref:`synchronous_sh` shows the scheduling of synchronous SH with $\\eta=2$\n", "for four different trials with two workers. We start with evaluating Trial-0 and\n", "Trial-1 for one epoch and immediately continue with the next two trials once they\n", "are finished. We first have to wait until Trial-2 finishes, which takes\n", "substantially more time than the other trials, before we can promote the best two\n", "trials, i.e., Trial-0 and Trial-3 to the next rung level. This causes idle time for\n", "Worker-1. Then, we continue with Rung 1. Also, here Trial-3 takes longer than Trial-0,\n", "which leads to an additional ideling time of Worker-0. Once, we reach Rung-2, only\n", "the best trial, Trial-0, remains which occupies only one worker. To avoid that\n", "Worker-1 idles during that time, most implementaitons of SH continue already with\n", "the next round, and start evaluating new trials (e.g Trial-4) on the first rung.\n", "\n", "![Synchronous successive halving with two workers.](../img/sync_sh.svg)\n", ":label:`synchronous_sh`\n", "\n", "Asynchronous successive halving (ASHA) :cite:`li-arxiv18` adapts SH to the asynchronous\n", "parallel scenario. The main idea of ASHA is to promote configurations to the next rung\n", "level as soon as we collected at least $\\eta$ observations on the current rung level.\n", "This decision rule may lead to suboptimal promotions: configurations can be promoted to the\n", "next rung level, which in hindsight do not compare favourably against most others\n", "at the same rung level. On the other hand, we get rid of all synchronization points\n", "this way. In practice, such suboptimal initial promotions have only a modest impact on\n", "performance, not only because the ranking of hyperparameter configurations is often\n", "fairly consistent across rung levels, but also because rungs grow over time and\n", "reflect the distribution of metric values at this level better and better. If a\n", "worker is free, but no configuration can be promoted, we start a new configuration\n", "with $r = r_{\\mathrm{min}}$, i.e the first rung level.\n", "\n", ":numref:`asha` shows the scheduling of the same configurations for ASHA. Once Trial-1\n", "finishes, we collect the results of two trials (i.e Trial-0 and Trial-1) and\n", "immediately promote the better of them (Trial-0) to the next rung level. After Trial-0\n", "finishes on rung 1, there are too few trials there in order to support a further\n", "promotion. Hence, we continue with rung 0 and evaluate Trial-3. Once Trial-3 finishes,\n", "Trial-2 is still pending. At this point we have 3 trials evaluated on rung 0 and one\n", "trial evaluated already on rung 1. Since Trial-3 performs worse than Trial-0 at rung 0,\n", "and $\\eta=2$, we cannot promote any new trial yet, and Worker-1 starts Trial-4 from\n", "scratch instead. However, once Trial-2 finishes and\n", "scores worse than Trial-3, the latter is promoted towards rung 1. Afterwards, we\n", "collected 2 evaluations on rung 1, which means we can now promote Trial-0 towards\n", "rung 2. At the same time, Worker-1 continues with evaluating new trials (i.e.,\n", "Trial-5) on rung 0.\n", "\n", "\n", "![Asynchronous successive halving (ASHA) with two workers.](../img/asha.svg)\n", ":label:`asha`\n"]}, {"cell_type": "code", "execution_count": 1, "id": "0b0d01f5", "metadata": {"execution": {"iopub.execute_input": "2023-08-18T20:01:48.151578Z", "iopub.status.busy": "2023-08-18T20:01:48.150780Z", "iopub.status.idle": "2023-08-18T20:01:51.996055Z", "shell.execute_reply": "2023-08-18T20:01:51.995043Z"}, "origin_pos": 2, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO:root:SageMakerBackend is not imported since dependencies are missing. You can install them with\n", "   pip install 'syne-tune[extra]'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["AWS dependencies are not imported since dependencies are missing. You can install them with\n", "   pip install 'syne-tune[aws]'\n", "or (for everything)\n", "   pip install 'syne-tune[extra]'\n", "AWS dependencies are not imported since dependencies are missing. You can install them with\n", "   pip install 'syne-tune[aws]'\n", "or (for everything)\n", "   pip install 'syne-tune[extra]'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:Ray Tune schedulers and searchers are not imported since dependencies are missing. You can install them with\n", "   pip install 'syne-tune[raytune]'\n", "or (for everything)\n", "   pip install 'syne-tune[extra]'\n"]}], "source": ["import logging\n", "from d2l import torch as d2l\n", "\n", "logging.basicConfig(level=logging.INFO)\n", "import matplotlib.pyplot as plt\n", "from syne_tune import StoppingCriterion, Tuner\n", "from syne_tune.backend.python_backend import PythonBackend\n", "from syne_tune.config_space import loguniform, randint\n", "from syne_tune.experiments import load_experiment\n", "from syne_tune.optimizer.baselines import ASHA"]}, {"cell_type": "markdown", "id": "fd0951d5", "metadata": {"origin_pos": 3}, "source": ["## Objective Function\n", "\n", "We will use *Syne Tune* with the same objective function as in\n", ":numref:`sec_rs_async`.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "d1e0718f", "metadata": {"attributes": {"classes": [], "id": "", "n": "54"}, "execution": {"iopub.execute_input": "2023-08-18T20:01:52.001567Z", "iopub.status.busy": "2023-08-18T20:01:52.000842Z", "iopub.status.idle": "2023-08-18T20:01:52.008420Z", "shell.execute_reply": "2023-08-18T20:01:52.007251Z"}, "origin_pos": 4, "tab": ["pytorch"]}, "outputs": [], "source": ["def hpo_objective_lenet_synetune(learning_rate, batch_size, max_epochs):\n", "    from syne_tune import Reporter\n", "    from d2l import torch as d2l\n", "\n", "    model = d2l.LeNet(lr=learning_rate, num_classes=10)\n", "    trainer = d2l.HPOTrainer(max_epochs=1, num_gpus=1)\n", "    data = d2l.FashionMNIST(batch_size=batch_size)\n", "    model.apply_init([next(iter(data.get_dataloader(True)))[0]], d2l.init_cnn)\n", "    report = Reporter()\n", "    for epoch in range(1, max_epochs + 1):\n", "        if epoch == 1:\n", "            # Initialize the state of Trainer\n", "            trainer.fit(model=model, data=data)\n", "        else:\n", "            trainer.fit_epoch()\n", "        validation_error = trainer.validation_error().cpu().detach().numpy()\n", "        report(epoch=epoch, validation_error=float(validation_error))"]}, {"cell_type": "markdown", "id": "f0b67805", "metadata": {"origin_pos": 5}, "source": ["We will also use the same configuration space as before:\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c6d2be2f", "metadata": {"attributes": {"classes": [], "id": "", "n": "55"}, "execution": {"iopub.execute_input": "2023-08-18T20:01:52.013552Z", "iopub.status.busy": "2023-08-18T20:01:52.012730Z", "iopub.status.idle": "2023-08-18T20:01:52.017747Z", "shell.execute_reply": "2023-08-18T20:01:52.016801Z"}, "origin_pos": 6, "tab": ["pytorch"]}, "outputs": [], "source": ["min_number_of_epochs = 2\n", "max_number_of_epochs = 10\n", "eta = 2\n", "\n", "config_space = {\n", "    \"learning_rate\": loguniform(1e-2, 1),\n", "    \"batch_size\": randint(32, 256),\n", "    \"max_epochs\": max_number_of_epochs,\n", "}\n", "initial_config = {\n", "    \"learning_rate\": 0.1,\n", "    \"batch_size\": 128,\n", "}"]}, {"cell_type": "markdown", "id": "86be45c9", "metadata": {"origin_pos": 7}, "source": ["## Asynchronous Scheduler\n", "\n", "First, we define the number of workers that evaluate trials concurrently. We\n", "also need to specify how long we want to run random search, by defining an\n", "upper limit on the total wall-clock time.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "13a7bfc6", "metadata": {"attributes": {"classes": [], "id": "", "n": "56"}, "execution": {"iopub.execute_input": "2023-08-18T20:01:52.022545Z", "iopub.status.busy": "2023-08-18T20:01:52.021981Z", "iopub.status.idle": "2023-08-18T20:01:52.025990Z", "shell.execute_reply": "2023-08-18T20:01:52.025119Z"}, "origin_pos": 8, "tab": ["pytorch"]}, "outputs": [], "source": ["n_workers = 2  # Needs to be <= the number of available GPUs\n", "max_wallclock_time = 12 * 60  # 12 minutes"]}, {"cell_type": "markdown", "id": "2c88cc08", "metadata": {"origin_pos": 9}, "source": ["The code for running ASHA is a simple variation of what we did for asynchronous\n", "random search.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "31550ee0", "metadata": {"attributes": {"classes": [], "id": "", "n": "56"}, "execution": {"iopub.execute_input": "2023-08-18T20:01:52.030885Z", "iopub.status.busy": "2023-08-18T20:01:52.030158Z", "iopub.status.idle": "2023-08-18T20:01:52.038204Z", "shell.execute_reply": "2023-08-18T20:01:52.037151Z"}, "origin_pos": 10, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.optimizer.schedulers.fifo:max_resource_level = 10, as inferred from config_space\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.optimizer.schedulers.fifo:Master random_seed = 3140976097\n"]}], "source": ["mode = \"min\"\n", "metric = \"validation_error\"\n", "resource_attr = \"epoch\"\n", "\n", "scheduler = ASHA(\n", "    config_space,\n", "    metric=metric,\n", "    mode=mode,\n", "    points_to_evaluate=[initial_config],\n", "    max_resource_attr=\"max_epochs\",\n", "    resource_attr=resource_attr,\n", "    grace_period=min_number_of_epochs,\n", "    reduction_factor=eta,\n", ")"]}, {"cell_type": "markdown", "id": "7397e21b", "metadata": {"origin_pos": 11}, "source": ["Here, `metric` and `resource_attr` specify the key names used with the `report`\n", "callback, and `max_resource_attr` denotes which input to the objective function\n", "corresponds to $r_{\\mathrm{max}}$. Moreover, `grace_period` provides $r_{\\mathrm{min}}$, and\n", "`reduction_factor` is $\\eta$. We can run Syne Tune as before (this will\n", "take about 12 minutes):\n"]}, {"cell_type": "code", "execution_count": 6, "id": "e359f0f1", "metadata": {"attributes": {"classes": [], "id": "", "n": "57"}, "execution": {"iopub.execute_input": "2023-08-18T20:01:52.042832Z", "iopub.status.busy": "2023-08-18T20:01:52.041889Z", "iopub.status.idle": "2023-08-18T20:13:55.813788Z", "shell.execute_reply": "2023-08-18T20:13:55.812742Z"}, "origin_pos": 12, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:results of trials will be saved on /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:Detected 4 GPUs\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.1 --batch_size 128 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/0/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 0) - scheduled config {'learning_rate': 0.1, 'batch_size': 128, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.44639554136672527 --batch_size 196 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/1/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 1) - scheduled config {'learning_rate': 0.44639554136672527, 'batch_size': 196, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.011548051321691994 --batch_size 254 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/2/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 2) - scheduled config {'learning_rate': 0.011548051321691994, 'batch_size': 254, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.14942487313193167 --batch_size 132 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/3/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 3) - scheduled config {'learning_rate': 0.14942487313193167, 'batch_size': 132, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 1 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.06317157191455719 --batch_size 242 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/4/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 4) - scheduled config {'learning_rate': 0.06317157191455719, 'batch_size': 242, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.48801815412811467 --batch_size 41 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/5/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 5) - scheduled config {'learning_rate': 0.48801815412811467, 'batch_size': 41, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.5904067586747807 --batch_size 244 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/6/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 6) - scheduled config {'learning_rate': 0.5904067586747807, 'batch_size': 244, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.08812857364095393 --batch_size 148 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/7/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 7) - scheduled config {'learning_rate': 0.08812857364095393, 'batch_size': 148, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.012271314788363914 --batch_size 235 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/8/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 8) - scheduled config {'learning_rate': 0.012271314788363914, 'batch_size': 235, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 5 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.08845692598296777 --batch_size 236 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/9/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 9) - scheduled config {'learning_rate': 0.08845692598296777, 'batch_size': 236, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.0825770880068151 --batch_size 75 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/10/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 10) - scheduled config {'learning_rate': 0.0825770880068151, 'batch_size': 75, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.20235201406823256 --batch_size 65 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/11/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 11) - scheduled config {'learning_rate': 0.20235201406823256, 'batch_size': 65, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.3359885631737537 --batch_size 58 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/12/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 12) - scheduled config {'learning_rate': 0.3359885631737537, 'batch_size': 58, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.7892434579795236 --batch_size 89 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/13/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 13) - scheduled config {'learning_rate': 0.7892434579795236, 'batch_size': 89, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.1233786579597858 --batch_size 176 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/14/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 14) - scheduled config {'learning_rate': 0.1233786579597858, 'batch_size': 176, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 13 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.13707981127012328 --batch_size 141 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/15/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 15) - scheduled config {'learning_rate': 0.13707981127012328, 'batch_size': 141, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.02913976299993913 --batch_size 116 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/16/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 16) - scheduled config {'learning_rate': 0.02913976299993913, 'batch_size': 116, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.033362897489792855 --batch_size 154 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/17/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 17) - scheduled config {'learning_rate': 0.033362897489792855, 'batch_size': 154, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.29442952580755816 --batch_size 210 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/18/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 18) - scheduled config {'learning_rate': 0.29442952580755816, 'batch_size': 210, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.10214259921521483 --batch_size 239 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/19/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 19) - scheduled config {'learning_rate': 0.10214259921521483, 'batch_size': 239, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:tuning status (last metric is reported)\n", " trial_id     status  iter  learning_rate  batch_size  max_epochs  epoch  validation_error  worker-time\n", "        0    Stopped     4       0.100000         128          10    4.0          0.430578    29.093798\n", "        1  Completed    10       0.446396         196          10   10.0          0.205652    72.747496\n", "        2    Stopped     2       0.011548         254          10    2.0          0.900570    13.729115\n", "        3    Stopped     8       0.149425         132          10    8.0          0.259171    58.980305\n", "        4    Stopped     4       0.063172         242          10    4.0          0.900579    27.773950\n", "        5  Completed    10       0.488018          41          10   10.0          0.140488   113.171314\n", "        6    Stopped    10       0.590407         244          10   10.0          0.193776    70.364757\n", "        7    Stopped     2       0.088129         148          10    2.0          0.899955    14.169738\n", "        8    Stopped     2       0.012271         235          10    2.0          0.899840    13.434274\n", "        9    Stopped     2       0.088457         236          10    2.0          0.899801    13.034437\n", "       10    Stopped     4       0.082577          75          10    4.0          0.385970    35.426524\n", "       11    Stopped     4       0.202352          65          10    4.0          0.543102    34.653495\n", "       12    Stopped    10       0.335989          58          10   10.0          0.149558    90.924182\n", "       13  Completed    10       0.789243          89          10   10.0          0.144887    77.365970\n", "       14    Stopped     2       0.123379         176          10    2.0          0.899987    12.422906\n", "       15    Stopped     2       0.137080         141          10    2.0          0.899983    13.395153\n", "       16    Stopped     4       0.029140         116          10    4.0          0.900532    27.834111\n", "       17    Stopped     2       0.033363         154          10    2.0          0.899996    13.407285\n", "       18 InProgress     1       0.294430         210          10    1.0          0.899878     6.126259\n", "       19 InProgress     0       0.102143         239          10      -                 -            -\n", "2 trials running, 18 finished (3 until the end), 437.07s wallclock-time\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.02846298236356246 --batch_size 115 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/20/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 20) - scheduled config {'learning_rate': 0.02846298236356246, 'batch_size': 115, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.037703019195187606 --batch_size 91 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/21/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 21) - scheduled config {'learning_rate': 0.037703019195187606, 'batch_size': 91, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.0741039859356903 --batch_size 192 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/22/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 22) - scheduled config {'learning_rate': 0.0741039859356903, 'batch_size': 192, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.3032613031191755 --batch_size 252 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/23/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 23) - scheduled config {'learning_rate': 0.3032613031191755, 'batch_size': 252, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.019823425532533637 --batch_size 252 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/24/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 24) - scheduled config {'learning_rate': 0.019823425532533637, 'batch_size': 252, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.8203370335228594 --batch_size 77 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/25/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 25) - scheduled config {'learning_rate': 0.8203370335228594, 'batch_size': 77, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.2960420911378594 --batch_size 104 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/26/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 26) - scheduled config {'learning_rate': 0.2960420911378594, 'batch_size': 104, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.2993874715754653 --batch_size 192 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/27/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 27) - scheduled config {'learning_rate': 0.2993874715754653, 'batch_size': 192, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.08056711961080017 --batch_size 36 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/28/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 28) - scheduled config {'learning_rate': 0.08056711961080017, 'batch_size': 36, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.26868380288030347 --batch_size 151 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/29/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 29) - scheduled config {'learning_rate': 0.26868380288030347, 'batch_size': 151, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Trial trial_id 29 completed.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:root:running subprocess with command: /usr/bin/python /home/<USER>/.local/lib/python3.8/site-packages/syne_tune/backend/python_backend/python_entrypoint.py --learning_rate 0.9197404791177789 --batch_size 66 --max_epochs 10 --tune_function_root /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/tune_function --tune_function_hash e03d187e043d2a17cae636d6af164015 --st_checkpoint_dir /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046/30/checkpoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:(trial 30) - scheduled config {'learning_rate': 0.9197404791177789, 'batch_size': 66, 'max_epochs': 10}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.stopping_criterion:reaching max wallclock time (720), stopping there.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Stopping trials that may still be running.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:syne_tune.tuner:Tuning finished, results of trials can be found on /home/<USER>/syne-tune/python-entrypoint-2023-08-18-20-01-52-046\n"]}, {"name": "stdout", "output_type": "stream", "text": ["--------------------\n", "Resource summary (last result is reported):\n", " trial_id     status  iter  learning_rate  batch_size  max_epochs  epoch  validation_error  worker-time\n", "        0    Stopped     4       0.100000         128          10      4          0.430578    29.093798\n", "        1  Completed    10       0.446396         196          10     10          0.205652    72.747496\n", "        2    Stopped     2       0.011548         254          10      2          0.900570    13.729115\n", "        3    Stopped     8       0.149425         132          10      8          0.259171    58.980305\n", "        4    Stopped     4       0.063172         242          10      4          0.900579    27.773950\n", "        5  Completed    10       0.488018          41          10     10          0.140488   113.171314\n", "        6    Stopped    10       0.590407         244          10     10          0.193776    70.364757\n", "        7    Stopped     2       0.088129         148          10      2          0.899955    14.169738\n", "        8    Stopped     2       0.012271         235          10      2          0.899840    13.434274\n", "        9    Stopped     2       0.088457         236          10      2          0.899801    13.034437\n", "       10    Stopped     4       0.082577          75          10      4          0.385970    35.426524\n", "       11    Stopped     4       0.202352          65          10      4          0.543102    34.653495\n", "       12    Stopped    10       0.335989          58          10     10          0.149558    90.924182\n", "       13  Completed    10       0.789243          89          10     10          0.144887    77.365970\n", "       14    Stopped     2       0.123379         176          10      2          0.899987    12.422906\n", "       15    Stopped     2       0.137080         141          10      2          0.899983    13.395153\n", "       16    Stopped     4       0.029140         116          10      4          0.900532    27.834111\n", "       17    Stopped     2       0.033363         154          10      2          0.899996    13.407285\n", "       18    Stopped     8       0.294430         210          10      8          0.241193    52.089688\n", "       19    Stopped     2       0.102143         239          10      2          0.900002    12.487762\n", "       20    Stopped     2       0.028463         115          10      2          0.899995    14.100359\n", "       21    Stopped     2       0.037703          91          10      2          0.900026    14.664848\n", "       22    Stopped     2       0.074104         192          10      2          0.901730    13.312770\n", "       23    Stopped     2       0.303261         252          10      2          0.900009    12.725821\n", "       24    Stopped     2       0.019823         252          10      2          0.899917    12.533380\n", "       25    Stopped    10       0.820337          77          10     10          0.196842    81.816103\n", "       26    Stopped    10       0.296042         104          10     10          0.198453    81.121330\n", "       27    Stopped     4       0.299387         192          10      4          0.336183    24.610689\n", "       28 InProgress     9       0.080567          36          10      9          0.203052   104.303746\n", "       29  Completed    10       0.268684         151          10     10          0.222814    68.217289\n", "       30 InProgress     1       0.919740          66          10      1          0.900037    10.070776\n", "2 trials running, 29 finished (4 until the end), 723.70s wallclock-time\n", "\n", "validation_error: best 0.1404876708984375 for trial-id 5\n", "--------------------\n"]}], "source": ["trial_backend = PythonBackend(\n", "    tune_function=hpo_objective_lenet_synetune,\n", "    config_space=config_space,\n", ")\n", "\n", "stop_criterion = StoppingCriterion(max_wallclock_time=max_wallclock_time)\n", "tuner = Tuner(\n", "    trial_backend=trial_backend,\n", "    scheduler=scheduler,\n", "    stop_criterion=stop_criterion,\n", "    n_workers=n_workers,\n", "    print_update_interval=int(max_wallclock_time * 0.6),\n", ")\n", "tuner.run()"]}, {"cell_type": "markdown", "id": "02c8a8c9", "metadata": {"origin_pos": 13}, "source": ["Note that we are running a variant of ASHA where underperforming trials are\n", "stopped early. This is different to our implementation in\n", ":numref:`sec_mf_hpo_sh`, where each training job is started with a fixed\n", "`max_epochs`. In the latter case, a well-performing trial which reaches the\n", "full 10 epochs, first needs to train 1, then 2, then 4, then 8 epochs, each\n", "time starting from scratch. This type of pause-and-resume scheduling can be\n", "implemented efficiently by checkpointing the training state after each epoch,\n", "but we avoid this extra complexity here. After the experiment has finished,\n", "we can retrieve and plot results.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "dcb9f8b5", "metadata": {"attributes": {"classes": [], "id": "", "n": "59"}, "execution": {"iopub.execute_input": "2023-08-18T20:13:55.817945Z", "iopub.status.busy": "2023-08-18T20:13:55.817301Z", "iopub.status.idle": "2023-08-18T20:13:56.081185Z", "shell.execute_reply": "2023-08-18T20:13:56.080250Z"}, "origin_pos": 14, "tab": ["pytorch"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:matplotlib.legend:No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"412.78125pt\" height=\"198.474375pt\" viewBox=\"0 0 412.78125 198.474375\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T20:13:56.030029</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 198.474375 \n", "L 412.78125 198.474375 \n", "L 412.78125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 108.**********.918125 \n", "L 304.**********.918125 \n", "L 304.040625 22.318125 \n", "L 108.740625 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m39d9d848d0\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m39d9d848d0\" x=\"113.767267\" y=\"160.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(110.586017 175.516563) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m39d9d848d0\" x=\"164.247068\" y=\"160.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(154.703318 175.516563) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m39d9d848d0\" x=\"214.726869\" y=\"160.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(205.183119 175.516563) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m39d9d848d0\" x=\"265.20667\" y=\"160.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(255.66292 175.516563) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- wallclock time -->\n", "     <g transform=\"translate(170.732031 189.194688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-77\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"81.787109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"143.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"170.849609\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"198.632812\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"253.613281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"281.396484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"342.578125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"397.558594\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"455.46875\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"487.255859\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"526.464844\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"554.248047\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"651.660156\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"m3976ef3f50\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3976ef3f50\" x=\"108.740625\" y=\"144.646339\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(85.8375 148.445558) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m3976ef3f50\" x=\"108.740625\" y=\"111.134674\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(85.8375 114.933893) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m3976ef3f50\" x=\"108.740625\" y=\"77.623009\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(85.8375 81.422228) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m3976ef3f50\" x=\"108.740625\" y=\"44.111345\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(85.8375 47.910563) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- validation_error -->\n", "     <g transform=\"translate(79.479687 130.837656) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-76\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"120.458984\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"148.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"176.025391\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"239.501953\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"300.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"339.990234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"367.773438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"428.955078\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"492.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"542.333984\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"603.857422\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"643.220703\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"682.083984\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"743.265625\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_9\">\n", "    <path d=\"M 117.617898 28.618125 \n", "L 118.885818 28.618125 \n", "L 118.885951 100.823387 \n", "L 121.412589 100.823387 \n", "L 122.677949 129.77456 \n", "L 123.948053 129.77456 \n", "L 126.475844 134.821659 \n", "L 127.742154 134.821659 \n", "L 130.275536 140.32914 \n", "L 132.808399 140.32914 \n", "L 134.072002 143.699349 \n", "L 153.081042 143.699349 \n", "L 153.081063 144.259914 \n", "L 154.343893 144.259914 \n", "L 155.615071 149.070595 \n", "L 159.413942 149.070595 \n", "L 161.941256 149.727367 \n", "L 167.00922 149.727367 \n", "L 167.010573 151.737311 \n", "L 168.279447 151.737311 \n", "L 170.811377 154.552938 \n", "L 295.163352 154.618125 \n", "L 295.163352 154.618125 \n", "\" clip-path=\"url(#pf27ce50949)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 108.**********.918125 \n", "L 108.740625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 304.**********.918125 \n", "L 304.040625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 108.**********.918125 \n", "L 304.**********.918125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 108.740625 22.318125 \n", "L 304.040625 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <!-- Best result over time python-entrypoint-2023-08-18-20-01-52-046 -->\n", "    <g transform=\"translate(7.2 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-42\" d=\"M 1259 2228 \n", "L 1259 519 \n", "L 2272 519 \n", "Q 2781 519 3026 730 \n", "Q 3272 941 3272 1375 \n", "Q 3272 1813 3026 2020 \n", "Q 2781 2228 2272 2228 \n", "L 1259 2228 \n", "z\n", "M 1259 4147 \n", "L 1259 2741 \n", "L 2194 2741 \n", "Q 2656 2741 2882 2914 \n", "Q 3109 3088 3109 3444 \n", "Q 3109 3797 2882 3972 \n", "Q 2656 4147 2194 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2241 4666 \n", "Q 2963 4666 3353 4366 \n", "Q 3744 4066 3744 3513 \n", "Q 3744 3084 3544 2831 \n", "Q 3344 2578 2956 2516 \n", "Q 3422 2416 3680 2098 \n", "Q 3938 1781 3938 1306 \n", "Q 3938 681 3513 340 \n", "Q 3088 0 2303 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-42\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"68.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"130.126953\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"182.226562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"221.435547\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"253.222656\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"292.085938\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"353.609375\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"405.708984\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"469.087891\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"496.871094\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"536.080078\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"567.867188\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"629.048828\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"688.228516\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"749.751953\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"790.865234\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"822.652344\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"861.861328\"/>\n", "     <use xlink:href=\"#DejaVuSans-6d\" x=\"889.644531\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"987.056641\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"1048.580078\"/>\n", "     <use xlink:href=\"#DejaVuSans-70\" x=\"1080.367188\"/>\n", "     <use xlink:href=\"#DejaVuSans-79\" x=\"1143.84375\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"1203.023438\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"1242.232422\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"1305.611328\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"1366.792969\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"1430.171875\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"1466.255859\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"1527.779297\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"1591.158203\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"1630.367188\"/>\n", "     <use xlink:href=\"#DejaVuSans-79\" x=\"1671.480469\"/>\n", "     <use xlink:href=\"#DejaVuSans-70\" x=\"1730.660156\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"1794.136719\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"1855.318359\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"1883.101562\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"1946.480469\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"1985.689453\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"2021.773438\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"2085.396484\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"2149.019531\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" x=\"2212.642578\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"2276.265625\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"2312.349609\"/>\n", "     <use xlink:href=\"#DejaVuSans-38\" x=\"2375.972656\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"2439.595703\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"2475.679688\"/>\n", "     <use xlink:href=\"#DejaVuSans-38\" x=\"2539.302734\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"2602.925781\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"2639.009766\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"2702.632812\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"2766.255859\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"2802.339844\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"2865.962891\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"2929.585938\"/>\n", "     <use xlink:href=\"#DejaVuSans-35\" x=\"2965.669922\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"3029.292969\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"3092.916016\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"3129\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"3192.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-36\" x=\"3256.246094\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 293.040625 35.318125 \n", "L 297.040625 35.318125 \n", "Q 299.040625 35.318125 299.040625 33.318125 \n", "L 299.040625 29.318125 \n", "Q 299.040625 27.318125 297.040625 27.318125 \n", "L 293.040625 27.318125 \n", "Q 291.040625 27.318125 291.040625 29.318125 \n", "L 291.040625 33.318125 \n", "Q 291.040625 35.318125 293.040625 35.318125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf27ce50949\">\n", "   <rect x=\"108.740625\" y=\"22.318125\" width=\"195.3\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 350x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.set_figsize()\n", "e = load_experiment(tuner.name)\n", "e.plot()"]}, {"cell_type": "markdown", "id": "2b340591", "metadata": {"origin_pos": 15}, "source": ["## Visualize the Optimization Process\n", "\n", "Once more, we visualize the learning curves of every trial (each color in the plot represents a trial). Compare this to\n", "asynchronous random search in :numref:`sec_rs_async`. As we have seen for\n", "successive halving in :numref:`sec_mf_hpo`, most of the trials are stopped\n", "at 1 or 2 epochs ($r_{\\mathrm{min}}$ or $\\eta * r_{\\mathrm{min}}$). However, trials do not stop\n", "at the same point, because they require different amount of time per epoch. If\n", "we ran standard successive halving instead of ASHA, we would need to synchronize\n", "our workers, before we can promote configurations to the next rung level.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "e9a808f3", "metadata": {"attributes": {"classes": [], "id": "", "n": "60"}, "execution": {"iopub.execute_input": "2023-08-18T20:13:56.084717Z", "iopub.status.busy": "2023-08-18T20:13:56.084407Z", "iopub.status.idle": "2023-08-18T20:13:56.430494Z", "shell.execute_reply": "2023-08-18T20:13:56.429601Z"}, "origin_pos": 16, "tab": ["pytorch"]}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'objective function')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"385.78125pt\" height=\"183.35625pt\" viewBox=\"0 0 385.78125 183.35625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2023-08-18T20:13:56.364620</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.7.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 183.35625 \n", "L 385.78125 183.35625 \n", "L 385.78125 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 43.78125 145.8 \n", "L 378.58125 145.8 \n", "L 378.58125 7.2 \n", "L 43.78125 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m49a3f1edd2\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m49a3f1edd2\" x=\"52.39835\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(49.2171 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m49a3f1edd2\" x=\"95.666751\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(86.123001 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m49a3f1edd2\" x=\"138.935152\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(129.391402 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m49a3f1edd2\" x=\"182.203553\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(172.659803 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m49a3f1edd2\" x=\"225.471954\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(215.928204 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m49a3f1edd2\" x=\"268.740355\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(259.196605 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m49a3f1edd2\" x=\"312.008755\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(302.465005 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m49a3f1edd2\" x=\"355.277156\" y=\"145.8\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 700 -->\n", "      <g transform=\"translate(345.733406 160.398438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- wall-clock time -->\n", "     <g transform=\"translate(173.71875 174.076563) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-77\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"81.787109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"143.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"170.849609\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"198.632812\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"234.716797\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"289.697266\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"317.480469\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"378.662109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"433.642578\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"491.552734\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"523.339844\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"562.548828\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"590.332031\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"687.744141\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <defs>\n", "       <path id=\"maf41a633f4\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#maf41a633f4\" x=\"43.78125\" y=\"129.649579\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(20.878125 133.448798) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#maf41a633f4\" x=\"43.78125\" y=\"96.545779\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(20.878125 100.344998) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#maf41a633f4\" x=\"43.78125\" y=\"63.441979\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(20.878125 67.241198) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#maf41a633f4\" x=\"43.78125\" y=\"30.338179\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(20.878125 34.137398) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- objective function -->\n", "     <g transform=\"translate(14.798438 121.346875) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6a\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 -63 \n", "Q 1178 -731 923 -1031 \n", "Q 669 -1331 103 -1331 \n", "L -116 -1331 \n", "L -116 -844 \n", "L 38 -844 \n", "Q 366 -844 484 -692 \n", "Q 603 -541 603 -63 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6f\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"61.181641\"/>\n", "      <use xlink:href=\"#DejaVuSans-6a\" x=\"124.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"152.441406\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"213.964844\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"268.945312\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"308.154297\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"335.9375\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"395.117188\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"456.640625\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"488.427734\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"523.632812\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"587.011719\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"650.390625\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"705.371094\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"744.580078\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"772.363281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"833.544922\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <path d=\"M 58.999432 15.033525 \n", "L 61.173237 86.359989 \n", "L 65.503356 81.601653 \n", "L 67.673648 114.958802 \n", "L 69.851126 111.824469 \n", "L 74.182916 119.944473 \n", "L 76.3553 117.427289 \n", "L 80.69824 125.384924 \n", "L 82.875314 108.073006 \n", "L 87.206467 128.714115 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"mb474e4bbe9\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #1f77b4\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"58.999432\" y=\"15.033525\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"61.173237\" y=\"86.359989\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"65.503356\" y=\"81.601653\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"67.673648\" y=\"114.958802\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"69.851126\" y=\"111.824469\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"74.182916\" y=\"119.944473\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"76.3553\" y=\"117.427289\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"80.69824\" y=\"125.384924\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"82.875314\" y=\"108.073006\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"87.206467\" y=\"128.714115\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 59.003019 13.717529 \n", "L 61.17301 13.717529 \n", "L 65.504616 71.138485 \n", "L 67.673805 91.484619 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m4584bba7c3\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m4584bba7c3\" x=\"59.003019\" y=\"13.717529\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4584bba7c3\" x=\"61.17301\" y=\"13.717529\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4584bba7c3\" x=\"65.504616\" y=\"71.138485\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4584bba7c3\" x=\"67.673805\" y=\"91.484619\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 74.184483 13.830639 \n", "L 76.355179 13.691996 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m5079e16571\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #2ca02c\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m5079e16571\" x=\"74.184483\" y=\"13.830639\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#m5079e16571\" x=\"76.355179\" y=\"13.691996\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 82.875285 13.775717 \n", "L 85.040292 22.254281 \n", "L 89.387529 91.359778 \n", "L 91.55279 107.603478 \n", "L 93.72029 109.873743 \n", "L 98.068286 116.84692 \n", "L 102.400272 118.722557 \n", "L 104.5883 119.855712 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m7f7c91c9b6\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #d62728\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"82.875285\" y=\"13.775717\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"85.040292\" y=\"22.254281\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"89.387529\" y=\"91.359778\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"91.55279\" y=\"107.603478\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"93.72029\" y=\"109.873743\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"98.068286\" y=\"116.84692\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"102.400272\" y=\"118.722557\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"104.5883\" y=\"119.855712\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 93.717899 13.724642 \n", "L 95.902077 13.827364 \n", "L 98.06832 13.861608 \n", "L 102.401917 13.690408 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #9467bd; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m052c68827a\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #9467bd\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m052c68827a\" x=\"93.717899\" y=\"13.724642\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#m052c68827a\" x=\"95.902077\" y=\"13.827364\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#m052c68827a\" x=\"98.06832\" y=\"13.861608\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#m052c68827a\" x=\"102.401917\" y=\"13.690408\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 108.932851 97.050709 \n", "L 115.447536 115.450854 \n", "L 119.793429 129.267857 \n", "L 124.137415 134.019988 \n", "L 128.469806 131.720115 \n", "L 134.980416 134.668766 \n", "L 139.339991 134.580705 \n", "L 143.67259 136.654248 \n", "L 150.188254 139.435607 \n", "L 154.529067 139.5 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #8c564b; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m5a7c3f43ab\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #8c564b\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"108.932851\" y=\"97.050709\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"115.447536\" y=\"115.450854\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"119.793429\" y=\"129.267857\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"124.137415\" y=\"134.019988\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"128.469806\" y=\"131.720115\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"134.980416\" y=\"134.668766\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"139.339991\" y=\"134.580705\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"143.67259\" y=\"136.654248\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"150.188254\" y=\"139.435607\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"154.529067\" y=\"139.5\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 111.116803 42.949532 \n", "L 113.282349 96.397422 \n", "L 115.449593 110.10464 \n", "L 119.793394 115.731651 \n", "L 121.958281 122.617536 \n", "L 124.137443 123.809618 \n", "L 128.467684 123.080533 \n", "L 130.649793 125.532269 \n", "L 134.982331 129.851956 \n", "L 137.162833 130.679777 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #e377c2; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"mb30248e5f4\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #e377c2\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"111.116803\" y=\"42.949532\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"113.282349\" y=\"96.397422\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"115.449593\" y=\"110.10464\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"119.793394\" y=\"115.731651\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"121.958281\" y=\"122.617536\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"124.137443\" y=\"123.809618\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"128.467684\" y=\"123.080533\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"130.649793\" y=\"125.532269\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"134.982331\" y=\"129.851956\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"137.162833\" y=\"130.679777\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 143.670271 13.80633 \n", "L 145.847803 13.793791 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #7f7f7f; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"m1ca6fa7843\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #7f7f7f\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m1ca6fa7843\" x=\"143.670271\" y=\"13.80633\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#m1ca6fa7843\" x=\"145.847803\" y=\"13.793791\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 152.364117 13.812743 \n", "L 154.52929 13.812743 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #bcbd22; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"mdad81591f7\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #bcbd22\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#mdad81591f7\" x=\"152.364117\" y=\"13.812743\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"154.52929\" y=\"13.812743\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 161.046851 13.791758 \n", "L 163.231436 13.819195 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #17becf; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <defs>\n", "     <path id=\"ma77a153795\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #17becf\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#ma77a153795\" x=\"161.046851\" y=\"13.791758\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#ma77a153795\" x=\"163.231436\" y=\"13.819195\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 161.049748 13.769797 \n", "L 163.231758 13.835671 \n", "L 167.576917 78.313967 \n", "L 171.926372 98.867991 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"161.049748\" y=\"13.769797\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"163.231758\" y=\"13.835671\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"167.576917\" y=\"78.313967\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"171.926372\" y=\"98.867991\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 169.761087 79.082949 \n", "L 174.100571 62.265046 \n", "L 176.284396 108.903698 \n", "L 180.614894 72.859655 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m4584bba7c3\" x=\"169.761087\" y=\"79.082949\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4584bba7c3\" x=\"174.100571\" y=\"62.265046\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4584bba7c3\" x=\"176.284396\" y=\"108.903698\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4584bba7c3\" x=\"180.614894\" y=\"72.859655\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 178.449358 88.365518 \n", "L 182.804352 115.333412 \n", "L 187.135148 123.366865 \n", "L 189.323933 125.676288 \n", "L 193.6564 129.222978 \n", "L 198.003263 133.749658 \n", "L 202.345676 133.627333 \n", "L 206.676364 133.990203 \n", "L 208.860177 137.084571 \n", "L 213.192859 137.998717 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m5079e16571\" x=\"178.449358\" y=\"88.365518\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#m5079e16571\" x=\"182.804352\" y=\"115.333412\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#m5079e16571\" x=\"187.135148\" y=\"123.366865\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#m5079e16571\" x=\"189.323933\" y=\"125.676288\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#m5079e16571\" x=\"193.6564\" y=\"129.222978\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#m5079e16571\" x=\"198.003263\" y=\"133.749658\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#m5079e16571\" x=\"202.345676\" y=\"133.627333\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#m5079e16571\" x=\"206.676364\" y=\"133.990203\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#m5079e16571\" x=\"208.860177\" y=\"137.084571\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#m5079e16571\" x=\"213.192859\" y=\"137.998717\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_26\">\n", "    <path d=\"M 187.138114 45.673097 \n", "L 189.323769 93.257564 \n", "L 193.654087 120.625789 \n", "L 195.838479 121.893068 \n", "L 200.168548 129.052361 \n", "L 202.345695 127.946031 \n", "L 206.67805 130.365377 \n", "L 208.860348 131.870261 \n", "L 213.191291 123.070885 \n", "L 217.546898 138.771882 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"187.138114\" y=\"45.673097\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"189.323769\" y=\"93.257564\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"193.654087\" y=\"120.625789\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"195.838479\" y=\"121.893068\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"200.168548\" y=\"129.052361\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"202.345695\" y=\"127.946031\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"206.67805\" y=\"130.365377\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"208.860348\" y=\"131.870261\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"213.191291\" y=\"123.070885\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"217.546898\" y=\"138.771882\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_27\">\n", "    <path d=\"M 219.723199 13.792143 \n", "L 221.913521 13.788483 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #9467bd; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m052c68827a\" x=\"219.723199\" y=\"13.792143\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#m052c68827a\" x=\"221.913521\" y=\"13.788483\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_28\">\n", "    <path d=\"M 224.089566 13.784882 \n", "L 226.254261 13.789075 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #8c564b; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"224.089566\" y=\"13.784882\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"226.254261\" y=\"13.789075\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <path d=\"M 228.446907 13.886874 \n", "L 230.612534 13.886874 \n", "L 232.779297 13.698261 \n", "L 237.135635 13.698261 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #e377c2; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"228.446907\" y=\"13.886874\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"230.612534\" y=\"13.886874\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"232.779297\" y=\"13.698261\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"237.135635\" y=\"13.698261\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_30\">\n", "    <path d=\"M 232.777152 13.786974 \n", "L 234.956308 13.786964 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #7f7f7f; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m1ca6fa7843\" x=\"232.777152\" y=\"13.786974\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#m1ca6fa7843\" x=\"234.956308\" y=\"13.786964\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_31\">\n", "    <path d=\"M 239.317636 13.806488 \n", "L 243.679362 53.040211 \n", "L 245.848777 86.838732 \n", "L 248.043313 108.808139 \n", "L 252.374293 114.499741 \n", "L 254.567159 112.039196 \n", "L 256.742955 116.534641 \n", "L 261.074041 122.831286 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #bcbd22; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#mdad81591f7\" x=\"239.317636\" y=\"13.806488\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"243.679362\" y=\"53.040211\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"245.848777\" y=\"86.838732\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"248.043313\" y=\"108.808139\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"252.374293\" y=\"114.499741\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"254.567159\" y=\"112.039196\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"256.742955\" y=\"116.534641\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"261.074041\" y=\"122.831286\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 241.514512 13.78285 \n", "L 245.844453 13.785967 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #17becf; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#ma77a153795\" x=\"241.514512\" y=\"13.78285\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#ma77a153795\" x=\"245.844453\" y=\"13.785967\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_33\">\n", "    <path d=\"M 252.376945 13.788542 \n", "L 254.567315 13.787033 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"252.376945\" y=\"13.788542\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"254.567315\" y=\"13.787033\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_34\">\n", "    <path d=\"M 261.076662 13.781991 \n", "L 263.269677 13.781991 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m4584bba7c3\" x=\"261.076662\" y=\"13.781991\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m4584bba7c3\" x=\"263.269677\" y=\"13.781991\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_35\">\n", "    <path d=\"M 267.61011 13.678924 \n", "L 269.804403 13.5 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m5079e16571\" x=\"267.61011\" y=\"13.678924\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "     <use xlink:href=\"#m5079e16571\" x=\"269.804403\" y=\"13.5\" style=\"fill: #2ca02c; stroke: #2ca02c\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 269.804159 13.731291 \n", "L 271.981812 13.784754 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"269.804159\" y=\"13.731291\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "     <use xlink:href=\"#m7f7c91c9b6\" x=\"271.981812\" y=\"13.784754\" style=\"fill: #d62728; stroke: #d62728\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 274.160845 13.769472 \n", "L 278.508589 13.800026 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #9467bd; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m052c68827a\" x=\"274.160845\" y=\"13.769472\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "     <use xlink:href=\"#m052c68827a\" x=\"278.508589\" y=\"13.800026\" style=\"fill: #9467bd; stroke: #9467bd\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <path d=\"M 278.508722 13.784547 \n", "L 280.687547 100.46662 \n", "L 285.052313 104.946003 \n", "L 287.217826 118.854718 \n", "L 291.574704 110.173592 \n", "L 295.908263 135.782673 \n", "L 298.100388 121.557407 \n", "L 302.434052 129.53725 \n", "L 306.795839 139.009556 \n", "L 308.963834 130.172285 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #8c564b; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"278.508722\" y=\"13.784547\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"280.687547\" y=\"100.46662\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"285.052313\" y=\"104.946003\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"287.217826\" y=\"118.854718\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"291.574704\" y=\"110.173592\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"295.908263\" y=\"135.782673\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"298.100388\" y=\"121.557407\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"302.434052\" y=\"129.53725\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"306.795839\" y=\"139.009556\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "     <use xlink:href=\"#m5a7c3f43ab\" x=\"308.963834\" y=\"130.172285\" style=\"fill: #8c564b; stroke: #8c564b\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <path d=\"M 285.052332 88.845721 \n", "L 287.219706 112.751509 \n", "L 291.574737 118.838657 \n", "L 295.906034 122.267826 \n", "L 298.100368 125.081751 \n", "L 302.430882 126.123609 \n", "L 304.630428 130.003937 \n", "L 308.961108 129.864525 \n", "L 313.322455 132.506126 \n", "L 315.489778 129.905556 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #e377c2; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"285.052332\" y=\"88.845721\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"287.219706\" y=\"112.751509\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"291.574737\" y=\"118.838657\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"295.906034\" y=\"122.267826\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"298.100368\" y=\"125.081751\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"302.430882\" y=\"126.123609\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"304.630428\" y=\"130.003937\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"308.961108\" y=\"129.864525\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"313.322455\" y=\"132.506126\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "     <use xlink:href=\"#mb30248e5f4\" x=\"315.489778\" y=\"129.905556\" style=\"fill: #e377c2; stroke: #e377c2\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_40\">\n", "    <path d=\"M 315.487337 13.678924 \n", "L 317.680569 14.036763 \n", "L 319.845329 87.345947 \n", "L 322.011438 107.108693 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #7f7f7f; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#m1ca6fa7843\" x=\"315.487337\" y=\"13.678924\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#m1ca6fa7843\" x=\"317.680569\" y=\"14.036763\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#m1ca6fa7843\" x=\"319.845329\" y=\"87.345947\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "     <use xlink:href=\"#m1ca6fa7843\" x=\"322.011438\" y=\"107.108693\" style=\"fill: #7f7f7f; stroke: #7f7f7f\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <path d=\"M 322.014592 13.777779 \n", "L 328.543691 92.227763 \n", "L 332.891601 112.419134 \n", "L 337.248252 119.741043 \n", "L 343.765835 122.540741 \n", "L 348.098291 124.690823 \n", "L 352.4481 125.550816 \n", "L 358.997391 126.150996 \n", "L 363.363068 129.144447 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #bcbd22; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#mdad81591f7\" x=\"322.014592\" y=\"13.777779\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"328.543691\" y=\"92.227763\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"332.891601\" y=\"112.419134\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"337.248252\" y=\"119.741043\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"343.765835\" y=\"122.540741\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"348.098291\" y=\"124.690823\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"352.4481\" y=\"125.550816\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"358.997391\" y=\"126.150996\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "     <use xlink:href=\"#mdad81591f7\" x=\"363.363068\" y=\"129.144447\" style=\"fill: #bcbd22; stroke: #bcbd22\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 328.540564 13.876357 \n", "L 330.726606 99.513396 \n", "L 332.89162 101.900096 \n", "L 335.056878 115.519233 \n", "L 339.413078 120.934536 \n", "L 341.578302 116.304533 \n", "L 343.765854 120.322004 \n", "L 348.096307 125.417304 \n", "L 350.28207 123.263019 \n", "L 354.614257 125.873444 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #17becf; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#ma77a153795\" x=\"328.540564\" y=\"13.876357\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#ma77a153795\" x=\"330.726606\" y=\"99.513396\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#ma77a153795\" x=\"332.89162\" y=\"101.900096\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#ma77a153795\" x=\"335.056878\" y=\"115.519233\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#ma77a153795\" x=\"339.413078\" y=\"120.934536\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#ma77a153795\" x=\"341.578302\" y=\"116.304533\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#ma77a153795\" x=\"343.765854\" y=\"120.322004\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#ma77a153795\" x=\"348.096307\" y=\"125.417304\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#ma77a153795\" x=\"350.28207\" y=\"123.263019\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "     <use xlink:href=\"#ma77a153795\" x=\"354.614257\" y=\"125.873444\" style=\"fill: #17becf; stroke: #17becf\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_43\">\n", "    <path d=\"M 361.163641 13.780077 \n", "\" clip-path=\"url(#pd29cdbbba4)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    <g clip-path=\"url(#pd29cdbbba4)\">\n", "     <use xlink:href=\"#mb474e4bbe9\" x=\"361.163641\" y=\"13.780077\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 43.78125 145.8 \n", "L 43.78125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 378.58125 145.8 \n", "L 378.58125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 43.78125 145.8 \n", "L 378.58125 145.8 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 43.78125 7.2 \n", "L 378.58125 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pd29cdbbba4\">\n", "   <rect x=\"43.78125\" y=\"7.2\" width=\"334.8\" height=\"138.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 600x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d2l.set_figsize([6, 2.5])\n", "results = e.results\n", "for trial_id in results.trial_id.unique():\n", "    df = results[results[\"trial_id\"] == trial_id]\n", "    d2l.plt.plot(\n", "        df[\"st_tuner_time\"],\n", "        df[\"validation_error\"],\n", "        marker=\"o\"\n", "    )\n", "d2l.plt.xlabel(\"wall-clock time\")\n", "d2l.plt.ylabel(\"objective function\")"]}, {"cell_type": "markdown", "id": "c13b3389", "metadata": {"origin_pos": 17}, "source": ["## Summary\n", "\n", "Compared to random search, successive halving is not quite as trivial to run in\n", "an asynchronous distributed setting. To avoid synchronisation points, we promote\n", "configurations as quickly as possible to the next rung level, even if this means\n", "promoting some wrong ones. In practice, this usually does not hurt much, and the\n", "gains of asynchronous versus synchronous scheduling are usually much higher\n", "than the loss of the suboptimal decision making.\n"]}, {"cell_type": "markdown", "id": "8f3eebc1", "metadata": {"origin_pos": 18, "tab": ["pytorch"]}, "source": ["[Discussions](https://discuss.d2l.ai/t/12101)\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": ["\"syne-tune[gpsearchers]==0.3.2\""]}, "nbformat": 4, "nbformat_minor": 5}
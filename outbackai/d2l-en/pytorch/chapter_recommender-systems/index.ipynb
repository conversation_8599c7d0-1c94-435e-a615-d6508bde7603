{"cells": [{"cell_type": "markdown", "id": "17e9d7e5", "metadata": {"origin_pos": 0}, "source": ["# Recommender Systems\n", ":label:`chap_recsys`\n", "\n", "\n", "**<PERSON><PERSON>** (*Amazon*), **<PERSON>** (*Amazon*), and **<PERSON>** (*Google*)\n", "\n", "Recommender systems are widely employed in industry and are ubiquitous in our daily lives. These systems are utilized in a number of areas such as online shopping sites (e.g., amazon.com), music/movie services site (e.g., Netflix and Spotify), mobile application stores (e.g., IOS app store and google play), online advertising, just to name a few. \n", "\n", "The major goal of recommender systems is to help users discover relevant items such as movies to watch, text to read or products to buy, so as to create a delightful user experience. Moreover, recommender systems are among the most powerful machine learning systems that online retailers implement in order to drive incremental revenue. Recommender systems are replacements of search engines by reducing the efforts in proactive searches and surprising users with offers they never searched for. Many companies managed to position themselves ahead of their competitors with the help of more effective recommender systems. As such, recommender systems are central to not only our everyday lives but also highly indispensable in some industries.\n", "\n", "\n", "In this chapter, we will cover the fundamentals and advancements of recommender systems, along with exploring some common fundamental techniques for building recommender systems with different data sources available and their implementations. Specifically, you will learn how to predict the rating a user might give to a prospective item, how to generate a recommendation list of items and how to predict the click-through rate from abundant features. These tasks are commonplace in real-world applications. By studying this chapter, you will get hands-on experience pertaining to solving real world recommendation problems with not only classical methods but the more advanced deep learning based models as well.\n", "\n", ":begin_tab:toc\n", " - [recsys-intro](recsys-intro.ipynb)\n", " - [movielens](movielens.ipynb)\n", " - [mf](mf.ipynb)\n", " - [autorec](autorec.ipynb)\n", " - [ranking](ranking.ipynb)\n", " - [neumf](neumf.ipynb)\n", " - [seqrec](seqrec.ipynb)\n", " - [ctr](ctr.ipynb)\n", " - [fm](fm.ipynb)\n", " - [deepfm](deepfm.ipynb)\n", ":end_tab:\n"]}], "metadata": {"language_info": {"name": "python"}, "required_libs": []}, "nbformat": 4, "nbformat_minor": 5}